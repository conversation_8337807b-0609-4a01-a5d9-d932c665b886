{"version": 3, "names": ["_definitions", "require", "isType", "nodeType", "targetType", "ALIAS_KEYS", "aliases", "FLIPPED_ALIAS_KEYS", "alias"], "sources": ["../../src/validators/isType.ts"], "sourcesContent": ["import { FLIPPED_ALIAS_KEYS, ALIAS_KEYS } from \"../definitions\";\nimport type * as t from \"..\";\n\nexport default function isType<T extends t.Node[\"type\"]>(\n  nodeType: string,\n  targetType: T,\n): nodeType is T;\n\nexport default function isType(\n  nodeType: string | null | undefined,\n  targetType: string,\n): boolean;\n\n/**\n * Test if a `nodeType` is a `targetType` or if `targetType` is an alias of `nodeType`.\n */\nexport default function isType(nodeType: string, targetType: string): boolean {\n  if (nodeType === targetType) return true;\n\n  // This is a fast-path. If the test above failed, but an alias key is found, then the\n  // targetType was a primary node type, so there's no need to check the aliases.\n  // @ts-expect-error targetType may not index ALIAS_KEYS\n  if (ALIAS_KEYS[targetType]) return false;\n\n  const aliases: Array<string> | undefined = FLIPPED_ALIAS_KEYS[targetType];\n  if (aliases) {\n    if (aliases[0] === nodeType) return true;\n\n    for (const alias of aliases) {\n      if (nodeType === alias) return true;\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAgBe,SAASC,MAAMA,CAACC,QAAgB,EAAEC,UAAkB,EAAW;EAC5E,IAAID,QAAQ,KAAKC,UAAU,EAAE,OAAO,IAAI;EAKxC,IAAIC,uBAAU,CAACD,UAAU,CAAC,EAAE,OAAO,KAAK;EAExC,MAAME,OAAkC,GAAGC,+BAAkB,CAACH,UAAU,CAAC;EACzE,IAAIE,OAAO,EAAE;IACX,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAKH,QAAQ,EAAE,OAAO,IAAI;IAExC,KAAK,MAAMK,KAAK,IAAIF,OAAO,EAAE;MAC3B,IAAIH,QAAQ,KAAKK,KAAK,EAAE,OAAO,IAAI;IACrC;EACF;EAEA,OAAO,KAAK;AACd"}