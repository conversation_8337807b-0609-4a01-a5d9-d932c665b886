import React, { useRef, useEffect, useState } from 'react';
import { StyleSheet, Dimensions, Platform } from 'react-native';
import { useApThemeState } from '@appmaker-xyz/uikit/src/theme/ThemeContext';
import { emitEvent, analytics } from '@appmaker-xyz/core';
import {
  Layout,
  AppImage,
  AppTouchable,
  AppmakerText,
} from '@appmaker-xyz/uikit';
import { Swiper } from '@appmaker-xyz/uikit/Views';
import { testProps } from '@appmaker-xyz/core';

let deviceRatio =
  Dimensions.get('window').height / Dimensions.get('window').width;
const isTab = deviceRatio < 1.6;

const ImageSwiper = ({
  clientId,
  attributes,
  onPress,
  onAction,
  innerBlocks,
}) => {
  const {
    imageList = [],
    autoplay,
    resizeMode = 'contain',
    showsButtons,
    hidePagination = false,
    customImageHeight = false,
    activeDotColor,
    centerPagination,
    __appmaker_analytics_parent_id,
    __appmaker_analytics_parent_name,
    auto_swipe_delay = 3,
  } = attributes;
  const [loading, setLoading] = useState(true);
  const autoSwipeDelay = Number(parseFloat(auto_swipe_delay).toFixed(1));
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  const swiperRef = useRef();
  const activeDotStyles = [styles.activeDotStyle];
  const dotContainerStyles = [styles.dotContainer];
  if (activeDotColor) {
    activeDotStyles.push({ backgroundColor: activeDotColor });
  }
  if (centerPagination) {
    dotContainerStyles.push({ justifyContent: 'center' });
  }
  useEffect(() => {
    if (swiperRef.current) {
      swiperRef.current.scrollTo(attributes.index, true);
    }
  }, [attributes.index]);
  useEffect(() => {
    // This function is called to fix a bug in ios that swiper auto play not working
    if (Platform.OS === 'ios' && autoplay) {
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    }
  }, [attributes.autoplay]);

  const ratio =
    innerBlocks[0]?.attributes.imageContainerStyle.width /
    innerBlocks[0]?.attributes.imageContainerStyle.height;

  const ImageItem = ({ item }) => {
    const uri =
      typeof item === 'string'
        ? item?.attributes
        : item?.attributes?.image?.url;
    const _clientId = item?.clientId || 'image-slider-no-client-id';
    const appmakerAction =
      typeof item === 'object' ? item.attributes.appmakerAction : {};
    let onPressHandle = onPress;
    if (appmakerAction && onAction) {
      onPressHandle = () => {
        analytics.track(
          'appmaker_block_click',
          {
            blockType: 'appmaker_slider',
            blockId: _clientId,
            blockLabel: item?.attributes?.__appmaker_analytics?.blockLabel,
            ...(item.id && {
              resourceId: item?.attributes?.__appmaker_analytics?.resourceId,
            }),
            ...(__appmaker_analytics_parent_id && {
              parentId: __appmaker_analytics_parent_id,
            }),
            ...(__appmaker_analytics_parent_name && {
              parentName: __appmaker_analytics_parent_name,
            }),
            resourceUrl: uri,
            resourceName: item?.attributes?.__appmaker_analytics?.resourceName,
          },
          {
            blockAttributes: attributes,
            blockClientId: clientId,
            blockAction: appmakerAction,
            __appmaker_analytics_parent_id,
            __appmaker_analytics_parent_name,
            resourceName: attributes?.__appmaker_analytics?.resourceName,
          },
        );
        emitEvent('bannerClick', {
          action: appmakerAction,
          bannerName: appmakerAction?.title,
        });
        onAction(appmakerAction);
      };
    }
    return (
      <AppTouchable
        onPress={onPressHandle}
        clientId={`appmaker_slider-item-${_clientId}`}>
        <AppImage
          uri={uri}
          style={styles.imageStyle}
          resizeMode={resizeMode}
          fastImage={true}
        />
      </AppTouchable>
    );
  };

  return imageList.length == 1 ? (
    <ImageItem item={imageList[0]} />
  ) : (
    <Swiper
      key={`swiper-${clientId}`}
      {...testProps(clientId)}
      showsPagination={hidePagination ? false : true}
      ref={swiperRef}
      containerStyle={
        customImageHeight
          ? { height: parseInt(customImageHeight) }
          : { aspectRatio: ratio }
      }
      removeClippedSubviews={false}
      autoplayTimeout={autoSwipeDelay}
      autoplay={autoplay}
      activeDotStyle={activeDotStyles}
      dotStyle={styles.dotStyle}
      showsButtons={showsButtons}
      paginationStyle={dotContainerStyles}>
      {innerBlocks.map((item, index) => {
        return <ImageItem key={index} item={item} />;
      })}
    </Swiper>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    imageStyle: {
      width: '100%',
      height: '100%',
    },
    activeDotStyle: {
      backgroundColor: color.primary,
      width: spacing.base,
      height: spacing.mini,
      borderRadius: spacing.nano,
      margin: spacing.nano / 2,
    },
    dotStyle: {
      backgroundColor: color.grey,
      width: spacing.mini,
      height: spacing.mini,
      borderRadius: spacing.nano,
      margin: spacing.nano / 2,
    },
    dotContainer: {
      justifyContent: 'flex-start',
      paddingLeft: spacing.base,
      bottom: spacing.md,
      position: 'absolute',
    },
  });

const ImageSwiperMemo = React.memo(ImageSwiper, (prev, next) => {
  return prev.clientId === next.clientId;
});
export default ImageSwiperMemo;
