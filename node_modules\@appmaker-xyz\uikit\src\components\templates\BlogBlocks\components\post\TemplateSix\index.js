import PropTypes from 'prop-types';
import React from 'react';
import {
  View,
  ImageBackground,
  Image,
  Text,
  TouchableOpacity,
} from 'react-native';
// import AppTouchable from '../../../common/AppTouchable';
import styles from './styles';
// import { shadeColor } from '../../../common/Utils';

// const userImage = require('../../../../image/user-white.png');

/**
 * Template six for Post list item
 * @param { title, data, onClick } props.
 */
const ListTemplateSix = (props) => {
  const item = props.data;
  const titleStyle = props.data.styles ? props.data.styles.titleStyle : {};
  const firstLetter = props.title.charAt(0);

  /** if data has no image url then create a placeholder using below url */
  const imageUrl =
    item.image ||
    `https://via.placeholder.com/200x100/000000/707070/?text=${firstLetter}`;
  return (
    <TouchableOpacity onPress={() => props.onClick(props.data.action)}>
      <View style={styles.rootContainer}>
        <View style={styles.container}>
          <ImageBackground
            style={styles.image}
            imageStyle={{ opacity: 0.5 }}
            source={{ uri: imageUrl }}>
            <View>
              <View style={styles.authorContainer}>
                {/* <Image style={styles.authorImage} source={userImage} /> */}
                <Text allowFontScaling={false} style={styles.authorText}>
                  {item.author}
                </Text>
              </View>
              <Text
                allowFontScaling={false}
                style={[styles.title, { zIndex: 2 }, titleStyle]}
                numberOfLines={2}>
                {props.title}
              </Text>
            </View>
          </ImageBackground>
        </View>
      </View>
    </TouchableOpacity>
  );
};
ListTemplateSix.propTypes = {
  data: PropTypes.any.isRequired,
  title: PropTypes.string,
  onClick: PropTypes.func,
};

ListTemplateSix.defaultProps = {};

export default ListTemplateSix;
