{"version": 3, "names": ["TokContext", "constructor", "token", "preserveSpace", "exports", "types", "brace", "j_oTag", "j_cTag", "j_expr", "template"], "sources": ["../../src/tokenizer/context.ts"], "sourcesContent": ["// The token context is used in JSX plugin to track\n// jsx tag / jsx text / normal JavaScript expression\n\nexport class TokContext {\n  constructor(token: string, preserveSpace?: boolean) {\n    this.token = token;\n    this.preserveSpace = !!preserveSpace;\n  }\n\n  token: string;\n  preserveSpace: boolean;\n}\n\nconst types: {\n  [key: string]: TokContext;\n} = {\n  brace: new TokContext(\"{\"), // normal JavaScript expression\n  j_oTag: new TokContext(\"<tag\"), // JSX opening tag\n  j_cTag: new TokContext(\"</tag\"), // JSX closing tag\n  j_expr: new TokContext(\"<tag>...</tag>\", true), // JSX expressions\n};\n\nif (!process.env.BABEL_8_BREAKING) {\n  types.template = new TokContext(\"`\", true);\n}\n\nexport { types };\n"], "mappings": ";;;;;;AAGO,MAAMA,UAAU,CAAC;EACtBC,WAAWA,CAACC,KAAa,EAAEC,aAAuB,EAAE;IAAA,KAKpDD,KAAK;IAAA,KACLC,aAAa;IALX,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,aAAa,GAAG,CAAC,CAACA,aAAa;EACtC;AAIF;AAACC,OAAA,CAAAJ,UAAA,GAAAA,UAAA;AAED,MAAMK,KAEL,GAAG;EACFC,KAAK,EAAE,IAAIN,UAAU,CAAC,GAAG,CAAC;EAC1BO,MAAM,EAAE,IAAIP,UAAU,CAAC,MAAM,CAAC;EAC9BQ,MAAM,EAAE,IAAIR,UAAU,CAAC,OAAO,CAAC;EAC/BS,MAAM,EAAE,IAAIT,UAAU,CAAC,gBAAgB,EAAE,IAAI;AAC/C,CAAC;AAACI,OAAA,CAAAC,KAAA,GAAAA,KAAA;AAEiC;EACjCA,KAAK,CAACK,QAAQ,GAAG,IAAIV,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC;AAC5C"}