import React from 'react';
import CollectionSuggestions from './CollectionSuggestions';
import { useSearchResult } from '@appmaker-xyz/shopify';

const CollectionSuggestionWrapper = (props) => {
  const { predictiveSearchResult: predictiveResults } = useSearchResult();
  return (
    <CollectionSuggestions
      attributes={{
        title: 'Collections',
        collections: predictiveResults?.collections,
      }}
    />
  );
};

export default CollectionSuggestionWrapper;
