import ICONS from '../../ToolBarIcons';
// import { colors as configColors } from '@appmaker-xyz/app-config/newConfig';

const pagesData = {
  toolBarItems: [ICONS.SEARCH, ICONS.WISHLIST, ICONS.CART],
  title: '',
  blocks: [
    {
      name: 'appmaker/shopify-variation-listner',
      clientId: 'de9b066c-ae0d-4f7c-a3dd-123',
      attributes: {},
    },
    {
      name: 'appmaker/shopify-product-image',
      attributes: {},
    },
    {
      name: 'appmaker/shopify-product-data',
      clientId: 'product-data',
      attributes: {},
    },
    {
      name: 'appmaker/slot',
      clientId: 'slot-after-product-data',
    },
    {
      name: 'appmaker/slot',
      clientId: 'slot-before-product-counter',
    },
    {
      name: 'appmaker/product-counter',
      attributes: {
        counter: true,
        title: 'Quantity',
        __appmakerStylesClassName: 'customProductDetailCounterBlock',
      },
    },
    {
      name: 'appmaker/slot',
      clientId: 'slot-before-variation',
      attributes: {},
    },
    {
      name: 'appmaker/shopify-product-variation',
      attributes: {},
    },
    {
      name: 'appmaker/slot',
      clientId: 'slot-before-description',
      attributes: {},
    },
    {
      name: 'appmaker/shopify-product-description',
      attributes: {},
    },
    {
      name: 'appmaker/slot-block',
      clientId: 'slot-block-after-description',
      attributes: {
        key: 'product_extra_fields',
      },
    },
    {
      name: 'appmaker/slot',
      clientId: 'slot-after-description',
      attributes: {},
    },
    {
      name: 'appmaker/shopify-related-product-scroller',
      clientId: 'widget-description',
      attributes: {
        __display: true,
        hideTitleBlock: false,
        title: 'You May Also Like',
      },
    },
  ],
  metaDataSource: {
    savedItem: {
      source: 'savedProducts',
      attributes: {
        transformKey: 'product-list-save-items',
        methodName: 'allItems',
        params: {},
      },
    },
  },
  // fixedFooter: {
  //   name: 'appmaker/shopify-product-pbp-buttons',
  //   clientId: 'product-detail-buttons',
  //   attributes: {},
  // },
  stickyFooter: {
    blocks: [
      {
        clientId: '143f1163-10ac-4b22-bf57-2ba78c94b540',
        name: 'appmaker/shopify-product-pbp-buttons',
        attributes: {
          fixedFooter: '1',
        },
        innerBlocks: [],
      },
    ],
  },
  attributes: {
    loadingLayout: 'product',
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      // paddingHorizontal: 16,
    },
    // headerShown: false,
  },
};
export default pagesData;
