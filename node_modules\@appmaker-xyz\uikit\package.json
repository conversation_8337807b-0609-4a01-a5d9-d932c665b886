{"name": "@appmaker-xyz/uikit", "version": "0.2.49-expo-v2-build-test-25-04-e5e5c0b.0", "description": "", "main": "src/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rollup --config rollup.config.js", "watch": "rollup --config rollup.config.js --watch"}, "author": "", "license": "UNLICENSED", "dependencies": {"@ptomasroos/react-native-multi-slider": "^2.2.2", "he": "^1.2.0", "html-entities": "^1.3.1", "react-native-image-zoom-viewer": "^3.0.1", "react-native-modal": "^11.5.6", "react-native-render-html": "^5.1.0", "react-native-simple-radio-button": "^2.7.4", "reanimated-bottom-sheet": "^1.0.0-alpha.20", "striptags": "^3.1.1", "unescape": "^1.0.1"}, "devDependencies": {"@rollup/plugin-babel": "^5.3.1", "rollup": "^2.79.0", "rollup-plugin-local-resolve": "^1.0.7", "rollup-plugin-uglify": "^6.0.4"}, "repository": "https://github.com/Appmaker-xyz/starter-app", "publishConfig": {"registry": "https://flash.appmaker.xyz"}}