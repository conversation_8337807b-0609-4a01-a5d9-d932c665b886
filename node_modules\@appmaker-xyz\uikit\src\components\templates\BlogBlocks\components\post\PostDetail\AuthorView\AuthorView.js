import PropTypes from 'prop-types';
import React from 'react';
import { View, Text, Image, Dimensions, TouchableOpacity } from 'react-native';
import styles from './Styles';

const windowWidth = Dimensions.get('window').width;

const AuthorView = (props) => {
  const { image, title, author, authorImage, category, onPress } = props;
  /**
   * Post author value is not given header section is not shown
   */

  if (author && author !== '') {
    return (
      <View style={{ backgroundColor: '#0048A50A', paddingBottom: 10 }}>
        {image && (
          <Image
            style={{ height: windowWidth * 0.54 }}
            source={{ uri: image }}
          />
        )}
        <View style={{ marginHorizontal: 16, marginTop: 16 }}>
          {title && (
            <Text allowFontScaling={false} style={styles.title}>
              {title}
            </Text>
          )}
          <TouchableOpacity onPress={onPress}>
            <View style={styles.authorpress}>
              <View>
                <Text allowFontScaling={false} style={styles.authortext}>
                  {author}
                </Text>
                <Text allowFontScaling={false} style={styles.categorytext}>
                  {category}
                </Text>
              </View>
              <Image style={styles.authorimage} source={{ uri: authorImage }} />
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return null;
};

AuthorView.propTypes = {
  /**
   * @param image
   * image
   */
  image: PropTypes.string,

  /**
   * @param title
   * title of post
   */
  title: PropTypes.string,

  /**
   * @param author
   * post author
   */
  author: PropTypes.string,

  /**
   * @param authorImage
   * image of post author
   */
  authorImage: PropTypes.url,

  /**
   * @param onPress
   *function
   */
  onPress: PropTypes.func,
  /**
   * @param category
   * category
   */
  category: PropTypes.string,

  /**
   * Navigation props
   */
  navigation: {
    navigate: PropTypes.func,
  },
};
export default AuthorView;
