import React, { useState } from 'react';
import { StyleSheet, KeyboardAvoidingView, Platform } from 'react-native';
import { Layout, ThemeText, AppTouchable, Button } from '@appmaker-xyz/ui';
import { useAddressForm } from '@appmaker-xyz/shopify';
import {
  log,
  usePageState,
  getExtensionAsBoolean,
  getExtensionConfig,
} from '@appmaker-xyz/core';
import { InputFormItem } from '../components/InputFormItem';
import { SelectFormItem } from '../components/SelectFormItem';
import RegionPicker from './RegionPicker';
const sampleAddress = {
  firstName: 'John',
  lastName: 'Doe',
  address1: '123 Fake Street',
  address2: 'Apt. 4',
  city: 'Fakecity',
  province: 'ON',
  country: 'CA',
  zip: 'K2P 1L4',
  phone: '************',
  name: '<PERSON>',
  provinceCode: 'ON',
  countryCode: 'CA',
  countryName: 'Canada',
  default: true,
};
const AddressForm = (props) => {
  const { currentAction } = props;
  const currentAddress =
    currentAction?.params?.pageData?.node?.node ||
    currentAction?.params?.pageData?.node ||
    {};
  const id = currentAddress?.id;
  const {
    control,
    isLoading,
    submitAddress,
    getShipsToCountries,
    provinceList,
    formState,
    setFocus,
  } = useAddressForm({
    defaultValues: currentAddress,
    id,
  });

  const RenderInputField = ({ name, ...rest }) => {
    const isUIDisabled = !!getExtensionConfig('address-customization', name, {})
      ?.ui_disabled;
  
    const isCustomSchemActive = getExtensionAsBoolean(
      'address-customization',
      'use_custom_schema',
      false,
    );

    const showField = isUIDisabled && isCustomSchemActive;

    if (showField) {
      return null;
    }
    return <InputFormItem name={name} {...rest} />;
  };
  return (
    <KeyboardAvoidingView keyboardVerticalOffset={40} behavior={'padding'}>
      <Layout style={styles.container}>
        <RegionPicker
          control={control}
          labels={{
            country: 'Country',
            province: 'Province',
          }}
          name={{
            country: 'country',
            province: 'province',
          }}
        />
        {/* <InputFormItem
          control={control}
          name="country"
          placeholder={'Country'}
          style={styles.input}
          options={getShipsToCountries()}
          returnKeyType="next"
        />
        <InputFormItem
          control={control}
          name="province"
          placeholder={'Province'}
          style={styles.input}
          options={provinceList}
          returnKeyType="next"
        /> */}
        <ThemeText size="lg" style={styles.sectionHeader}>
          Shipping Address
        </ThemeText>
        <RenderInputField
          control={control}
          name="firstName"
          label={'First Name'}
          style={styles.input}
          returnKeyType="next"
          onSubmitEditing={() => setFocus('lastName')}
        />
        <RenderInputField
          control={control}
          name="lastName"
          label={'Last Name'}
          style={styles.input}
          returnKeyType="next"
          onSubmitEditing={() => setFocus('address1')}
        />
        <RenderInputField
          control={control}
          name="address1"
          label={'Address'}
          style={styles.input}
          returnKeyType="next"
          onSubmitEditing={() => setFocus('address2')}
        />
        <RenderInputField
          control={control}
          name="address2"
          label={'Apartment, suite, etc. (optional)'}
          style={styles.input}
          returnKeyType="next"
          onSubmitEditing={() => setFocus('city')}
        />
        <RenderInputField
          control={control}
          name="city"
          label={'City'}
          style={styles.input}
          returnKeyType="next"
          onSubmitEditing={() => setFocus('zip')}
        />
        <RenderInputField
          control={control}
          name="zip"
          label={'Zip/Postal Code'}
          style={styles.input}
          returnKeyType="next"
          onSubmitEditing={() => setFocus('phone')}
        />
        <RenderInputField
          control={control}
          name="phone"
          label={'Phone'}
          style={styles.input}
          returnKeyType="done"
          keyboardType="numeric"
        />
        <Button
          title="Save Address"
          onPress={submitAddress}
          isLoading={isLoading}
          activityIndicatorColor="#FFFFFF"
          size="md"
          color="#1E232C"
        />
      </Layout>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {},
  input: {
    backgroundColor: '#E8ECF4',
    borderRadius: 6,
    overflow: 'hidden',
    position: 'relative',
  },
  eyeIcon: {
    position: 'absolute',
    right: 12,
    top: 10,
  },
  forgotButton: {
    alignSelf: 'flex-end',
    marginBottom: 16,
    marginTop: -6,
  },
  signUpButton: {
    marginTop: 32,
    alignItems: 'center',
  },
  separator: {
    height: 1,
    backgroundColor: '#E8ECF4',
    marginVertical: 8,
  },
  sectionHeader: {
    marginBottom: 8,
    marginTop: 10,
  },
});

export default AddressForm;
