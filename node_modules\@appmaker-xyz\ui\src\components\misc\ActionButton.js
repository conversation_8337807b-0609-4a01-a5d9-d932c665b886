import React, { useState } from 'react';
import Icon from 'react-native-vector-icons/Feather';
import { UiKitButton } from './uiKitMigrationBlocks';
import { usePageState } from '@appmaker-xyz/core';
import { color, font } from './uiKitMigrationBlocks/UiKitStyles';

const ActionButton = ({
  attributes,
  onAction,
  pageState,
  data,
  clientId,
  t,
}) => {
  const {
    appmakerAction,
    successMessage,
    showMessage,
    loadingRequired = true,
    loadingKey = false,
  } = attributes;
  const [loading, setLoadingIndicator] = useState(false);
  usePageState((state) => {
    if (!loadingRequired) {
      return;
    }
    const loadingStatus = state[loadingKey ? loadingKey : 'loadingButton'];
    if (typeof loadingStatus === 'boolean' && loading !== loadingStatus) {
      setLoadingIndicator(state.loadingButton);
    }
  });
  const handleAction = async () => {
    setLoadingIndicator(true);
    try {
      const actionResp = await onAction({
        action: appmakerAction.action,
        params: { ...appmakerAction.params, pageState, data, onAction },
        t,
      });
      if (showMessage) {
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: successMessage,
          },
          t,
        });
      }
    } catch (error) {
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: error.message || error.data.message,
        },
        t,
      });
    } finally {
      setLoadingIndicator(false);
    }
  };
  return (
    <UiKitButton
      clientId={clientId}
      small={attributes.small}
      loading={loading}
      baseSize={attributes.baseSize}
      wholeContainerStyle={attributes.wholeContainerStyle}
      fontColor={attributes.fontColor}
      onPress={appmakerAction && handleAction}
      link={attributes.link}
      accessoryRight={() => (
        <Icon
          name={attributes.iconName}
          size={attributes.iconSize ? attributes.iconSize : font.size.lg}
          color={attributes.iconColor ? attributes.iconColor : color.white}
        />
      )}
      outline={attributes.outline}
      status={attributes.status}
      __appmakerCustomStyles={attributes?.__appmakerCustomStyles}>
      {attributes.content}
    </UiKitButton>
  );
};

export default ActionButton;
