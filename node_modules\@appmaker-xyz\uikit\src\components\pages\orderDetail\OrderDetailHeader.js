import React from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import { Layout, AppmakerText, Button } from '../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { appmaker } from '@appmaker-xyz/core';

const OrderDetailHeader = ({ attributes, onPress, onAction, blockData }) => {
  const {
    orderId,
    numberOfItems,
    appmakerAction,
    canCancelOrder,
    topNotice,
    can_repeat_order = false,
    status,
    orderDate,
    paymentMethod,
    orderFulfillment,
    pageDispatch,
  } = attributes;

  let paymentStatus = paymentMethod;

  function cardStatus() {
    switch (status) {
      case 'SCHEDULED':
        return 'primary';
      case 'UNFULFILLED':
        return 'warning';
      case 'FULFILLED':
        return 'success';
      case 'ON_HOLD':
        return 'dark';
      case 'PARTIALLY_FULFILLED':
        return 'danger';
      default:
        return 'demiDark';
    }
  }
  let cardColor = cardStatus();
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing, cardColor });
  const orderAgain = () => {
    onAction({
      type: 'GET_REDIRECT_URL',
      params: { url: 'payment', params: orderId },
    }).then((url) => {
      onAction({
        type: 'OPEN_IN_WEB_VIEW',
        params: { title: 'Order Again', url: url },
      });
    });
  };
  let tracking = {};
  if (Array.isArray(orderFulfillment)) {
    tracking.url = orderFulfillment[0]?.trackingInfo[0]?.url;
    tracking.num = orderFulfillment[0]?.trackingInfo[0]?.number;
  }

  tracking.url = appmaker.applyFilters(
    'shopify-order-track-url',
    tracking.url,
    { order: blockData?.node || blockData },
  );
  const detailFooterItems = appmaker.applyFilters(
    'appmaker-order-detail-footer',
    [],
  );
  return (
    <Layout backgroundColor={color.white}>
      <Layout style={styles.detailHeader}>
        <AppmakerText status={cardColor} category="h1Heading">
          {status}
        </AppmakerText>
        <AppmakerText>
          Order Id: #{orderId} ({numberOfItems} Items)
        </AppmakerText>
        {!!orderDate && (
          <AppmakerText status="demiDark" category="bodySubText">
            Order placed on {orderDate}
          </AppmakerText>
        )}
        <AppmakerText status="demiDark">
          Payment status {paymentMethod}
        </AppmakerText>
        {/* <Layout style={styles.orderActions}>
          {(paymentStatus === 'PAID' || 'PENDING') &&
            (status === 'PARTIALLY_FULFILLED' || 'UNFULFILLED') && (
              <>
                <Layout flex={1}>
                  <Button small onPress={() => orderAgain()} status="dark">
                    Edit
                  </Button>
                </Layout>
                <Layout flex={1}>
                  <Button
                    onPress={() =>
                      onAction({
                        type: 'CANCEL_ORDER',
                        params: { id: orderId, reason: '' },
                      }).then((res) => {
                        onAction({ type: 'GO_BACK' });
                        onAction({
                          type: 'LIST_ORDER',
                          params: { replacePage: true },
                        });
                      })
                    }
                    small
                    status="dark">
                    Cancel
                  </Button>
                </Layout>
              </>
            )}
          <Layout flex={1}>
            <Button small onPress={() => orderAgain()} status="dark">
              Reorder
            </Button>
          </Layout>
        </Layout> */}
        <Layout style={styles.orderActions}>
          {/* {status === 'FULFILLED' && (
            <Layout flex={1}>
              <Button small onPress={() => orderAgain()} status="dark">
                Return/Exchange
              </Button>
            </Layout>
          )} */}
          {!!tracking.num ? (
            <Layout flex={2}>
              <Button
                small
                onPress={() => {
                  onAction({
                    type: 'TRACK_ORDER',
                    params: { url: tracking.url, orderId },
                  });
                }}
                status="dark">
                {`Track order - #${tracking.num}`}
              </Button>
            </Layout>
          ) : null}

          {/* {(status === 'cancelled' ||
            status === 'failed' ||
            status === 'refunded' ||
            status === 'completed') &&
            can_repeat_order && (
              <Layout flex={2}>
                <Button small onPress={() => orderAgain()} status="dark">
                  Order Again
                </Button>
              </Layout>
            )} */}
          <>
            {/* {canCancelOrder && (
              <Layout flex={1} marginLeft={spacing.small}>
                <Button
                  onPress={() =>
                    onAction({
                      type: 'CANCEL_ORDER',
                      params: { id: orderId, reason: '' },
                    }).then((res) => {
                      onAction({ type: 'GO_BACK' });
                      onAction({
                        type: 'LIST_ORDER',
                        params: { replacePage: true },
                      });
                    })
                  }
                  small
                  outline
                  status="dark">
                  Cancel Order
                </Button>
              </Layout>
            )} */}
          </>
        </Layout>
        {/* {topNotice &&
          topNotice.map((item, key) => (
            <Layout flex={1}>
              {item.button && (
                <Button
                  small
                  onPress={() => onAction(item.button.action)}
                  status="dark">
                  {item.button.text}
                </Button>
              )}
              <AppmakerText
                status="grey"
                category="bodySubText"
                style={styles.shippingMsg}>
                <Icon name="info" size={spacing.md} />{' '}
                {item.message}
              </AppmakerText>
            </Layout>
          ))} */}
        {detailFooterItems.map((Item, key) => (
          <Item
            key={`footer-items-${key}`}
            attributes={attributes}
            onAction={onAction}
          />
        ))}
      </Layout>
    </Layout>
  );
};

const allStyles = ({ spacing, color, cardColor }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.light,
    },
    detailHeader: {
      padding: spacing.base,
      backgroundColor: `${color[cardColor]}1A`,
    },
    subPadding: {
      paddingHorizontal: spacing.base,
      paddingBottom: spacing.base,
    },
    orderActions: {
      flexDirection: 'row',
      paddingBottom: spacing.nano,
      paddingTop: spacing.small,
    },
    shippingMsg: {
      textAlign: 'center',
      marginTop: spacing.small,
    },
  });

export default OrderDetailHeader;
