import React from 'react';
import { appmaker } from '@appmaker-xyz/core';
import NestedMenu from './NestedMenu';
import CollapsibleMenu from './CollapsibleMenu';

const DrawerMenuBlock = ({ attributes, ...props }) => {
  const menuType = appmaker.applyFilters('nav-menu-type', 'type-1');
  if (menuType === 'collapsible') {
    return <CollapsibleMenu attributes={attributes} {...props} />;
  }
  return <NestedMenu attributes={attributes} {...props} />;
};

export default DrawerMenuBlock;
