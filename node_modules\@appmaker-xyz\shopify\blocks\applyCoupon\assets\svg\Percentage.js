import * as React from 'react';
import Svg, { Path } from 'react-native-svg';
const PercentageIcon = (props) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    width={props.width || 30}
    height={props.height || 31}
    viewBox="0 0 30 31"
    {...props}>
    <Path
      fill={props.color || '#30A479'}
      d="M14.557.118c-.528.091-.782.26-1.5.991-.732.75-.891.846-1.482.9-.355.032-.573-.018-1.287-.309-.454-.186-.636-.236-.913-.255-.605-.04-1.128.137-1.555.532-.345.314-.509.619-.695 1.296-.2.704-.373 1.054-.655 1.304-.414.364-.74.469-1.614.514-.313.018-.654.064-.809.114-.832.259-1.44 1.068-1.445 1.922 0 .119.05.51.113.869.128.754.11.995-.104 1.454-.186.396-.473.66-1.11 1.014-.35.195-.636.395-.79.554a2.052 2.052 0 0 0-.568 1.746c.072.427.186.664.577 1.195.504.691.586.896.582 1.428 0 .545-.073.732-.537 1.404-.413.596-.504.787-.572 1.2-.064.414.004.828.204 1.241.214.441.546.737 1.205 1.087.654.35.904.572 1.113.99.219.441.241.75.119 1.469-.055.304-.096.659-.096.795 0 .655.387 1.355.927 1.7.405.255.587.3 1.414.364.864.064 1.137.14 1.468.404.41.328.541.555.778 1.369.081.272.209.618.286.763.182.337.564.691.945.869.255.118.36.14.75.154.505.018.623-.009 1.5-.382.673-.286 1.264-.268 1.823.06.105.058.418.35.7.65.31.331.596.595.746.677.682.39 1.536.359 2.19-.087.15-.095.46-.395.692-.659.645-.736 1-.913 1.732-.877.34.018.459.05.94.254.305.128.687.255.85.282.81.141 1.696-.29 2.132-1.045.069-.114.196-.468.287-.787.195-.7.322-.972.595-1.245.373-.373.941-.555 1.732-.56.65 0 1.123-.195 1.564-.631.345-.35.513-.668.595-1.141.05-.295.046-.386-.073-1.068-.109-.637-.118-.777-.077-1 .127-.678.45-1.068 1.25-1.514.36-.195.641-.39.805-.554.463-.46.686-1.128.586-1.76-.068-.418-.19-.672-.627-1.277-.414-.573-.555-.923-.555-1.373 0-.431.141-.786.527-1.336.537-.764.587-.896.587-1.555 0-.536 0-.55-.168-.886-.228-.454-.518-.74-1.069-1.04-.654-.355-.904-.533-1.081-.774-.391-.531-.45-.909-.291-1.886.09-.586.1-.695.05-.982a1.976 1.976 0 0 0-.537-1.082c-.436-.472-1-.682-1.822-.686-.723 0-1.15-.146-1.56-.527-.281-.264-.427-.55-.618-1.191-.204-.7-.34-.955-.672-1.273a2.03 2.03 0 0 0-1.114-.564c-.441-.077-.81-.004-1.477.282-.66.282-1.014.355-1.396.282-.568-.105-.782-.255-1.54-1.082-.165-.177-.383-.377-.487-.44a2.285 2.285 0 0 0-1.468-.3Zm-2.941 9.75c1.582.36 2.563 1.878 2.236 3.45-.214 1.023-1.055 1.923-2.09 2.232-.374.114-1.174.114-1.546 0-.782-.232-1.491-.827-1.832-1.527a2.91 2.91 0 0 1-.31-1.518c.15-1.782 1.819-3.023 3.542-2.637Zm7.645.064c.1.05.218.15.264.223.109.19.1.495-.023.677-.277.391-8.077 9.737-8.182 9.8-.159.1-.482.091-.654-.013a.645.645 0 0 1-.25-.741c.022-.06 1.218-1.528 2.663-3.264 1.441-1.736 3.26-3.932 4.037-4.873 1.654-2 1.69-2.027 2.145-1.809Zm.714 5.146c.85.318 1.473.936 1.8 1.8.109.277.118.363.118.963s-.009.687-.118.964a3.051 3.051 0 0 1-1.85 1.818c-.26.091-.387.105-.937.105-.572-.005-.668-.014-.94-.123a3.013 3.013 0 0 1-1.8-1.8c-.155-.41-.191-1.21-.082-1.645A2.98 2.98 0 0 1 18.284 15c.4-.109 1.3-.068 1.69.078Z"
    />
    <Path
      fill={props.color || '#1D984F'}
      d="M10.573 11.423c-.423.15-.778.509-.882.886-.177.668.127 1.368.723 1.65.668.318 1.463.037 1.818-.636.168-.323.173-.855.013-1.182-.304-.618-1.05-.941-1.672-.718ZM18.51 16.553c-.286.105-.69.518-.786.805-.182.532-.064 1.05.327 1.44.296.296.564.405.978.405.522 0 .977-.3 1.213-.795.237-.491.16-1.05-.195-1.455a1.285 1.285 0 0 0-.991-.459 1.358 1.358 0 0 0-.546.06Z"
    />
  </Svg>
);
export default PercentageIcon;
