{"version": 3, "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread2", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "defineProperty", "getOwnPropertyDescriptors", "defineProperties"], "sources": ["../../src/helpers/objectSpread2.js"], "sourcesContent": ["/* @minVersion 7.5.0 */\n\nimport defineProperty from \"defineProperty\";\n\n// This function is different to \"Reflect.ownKeys\". The enumerableOnly\n// filters on symbol properties only. Returned string properties are always\n// enumerable. It is good to use in objectSpread.\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\n\nexport default function _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(\n          target,\n          key,\n          Object.getOwnPropertyDescriptor(source, key)\n        );\n      });\n    }\n  }\n  return target;\n}\n"], "mappings": ";;;;;;AAEA;AAMA,SAASA,OAAO,CAACC,MAAM,EAAEC,cAAc,EAAE;EACvC,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAC9B,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAChC,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAClD,IAAIC,cAAc,EAAE;MAClBI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QACtC,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;MAChE,CAAC,CAAC;IACJ;IACAP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAChC;EACA,OAAOH,IAAI;AACb;AAEe,SAASU,cAAc,CAACC,MAAM,EAAE;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IACrD,IAAIA,CAAC,GAAG,CAAC,EAAE;MACTf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QACnDC,eAAc,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAC1C,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIhB,MAAM,CAACkB,yBAAyB,EAAE;MAC3ClB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC;IAC3E,CAAC,MAAM;MACLlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAC7ChB,MAAM,CAACiB,cAAc,CACnBP,MAAM,EACNM,GAAG,EACHhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAC7C;MACH,CAAC,CAAC;IACJ;EACF;EACA,OAAON,MAAM;AACf"}