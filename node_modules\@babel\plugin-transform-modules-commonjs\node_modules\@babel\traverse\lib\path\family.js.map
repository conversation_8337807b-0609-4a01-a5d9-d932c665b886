{"version": 3, "names": ["_index", "require", "_t", "getBindingIdentifiers", "_getBindingIdentifiers", "getOuterBindingIdentifiers", "_getOuterBindingIdentifiers", "isDeclaration", "numericLiteral", "unaryExpression", "NORMAL_COMPLETION", "BREAK_COMPLETION", "NormalCompletion", "path", "type", "BreakCompletion", "getOpposite", "key", "getSibling", "addCompletionRecords", "records", "context", "push", "_getCompletionRecords", "completionRecordForSwitch", "cases", "lastNormalCompletions", "i", "length", "casePath", "caseCompletions", "normalCompletions", "breakCompletions", "c", "normalCompletionToBreak", "completions", "for<PERSON>ach", "replaceBreakStatementInBreakCompletion", "reachable", "isBreakStatement", "label", "replaceWith", "remove", "getStatementListCompletion", "paths", "canHaveBreak", "newContext", "Object", "assign", "inCaseClause", "isBlockStatement", "shouldPopulateBreak", "statementCompletions", "every", "some", "pathCompletions", "isVariableDeclaration", "isIfStatement", "get", "isDoExpression", "isFor", "<PERSON><PERSON><PERSON><PERSON>", "isLabeledStatement", "isProgram", "isFunction", "isTryStatement", "isCatchClause", "isSwitchStatement", "isSwitchCase", "getCompletionRecords", "map", "r", "NodePath", "parentPath", "parent", "container", "<PERSON><PERSON><PERSON>", "setContext", "getPrevSibling", "getNextSibling", "getAllNextSiblings", "_key", "sibling", "siblings", "node", "getAllPrevSiblings", "parts", "split", "_get<PERSON>ey", "_get<PERSON>attern", "Array", "isArray", "_", "part", "duplicates", "getBindingIdentifierPaths", "outerOnly", "search", "ids", "create", "id", "shift", "keys", "isIdentifier", "_ids", "name", "isExportDeclaration", "declaration", "isFunctionDeclaration", "isFunctionExpression", "child", "getOuterBindingIdentifierPaths"], "sources": ["../../src/path/family.ts"], "sourcesContent": ["// This file contains methods responsible for dealing with/retrieving children or siblings.\n\nimport type TraversalContext from \"../context\";\nimport NodePath from \"./index\";\nimport {\n  getBindingIdentifiers as _getBindingIdentifiers,\n  getOuterBindingIdentifiers as _getOuterBindingIdentifiers,\n  isDeclaration,\n  numericLiteral,\n  unaryExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nconst NORMAL_COMPLETION = 0 as const;\nconst BREAK_COMPLETION = 1 as const;\n\ntype Completion = {\n  path: NodePath;\n  type: 0 | 1;\n};\n\ntype CompletionContext = {\n  // whether the current context allows `break` statement. When it allows, we have\n  // to search all the statements for potential `break`\n  canHaveBreak: boolean;\n  // whether the statement is an immediate descendant of a switch case clause\n  inCaseClause: boolean;\n  // whether the `break` statement record should be populated to upper level\n  // when a `break` statement is an immediate descendant of a block statement, e.g.\n  // `{ break }`, it can influence the control flow in the upper levels.\n  shouldPopulateBreak: boolean;\n};\n\nfunction NormalCompletion(path: NodePath) {\n  return { type: NORMAL_COMPLETION, path };\n}\n\nfunction BreakCompletion(path: NodePath) {\n  return { type: BREAK_COMPLETION, path };\n}\n\nexport function getOpposite(this: NodePath): NodePath | null {\n  if (this.key === \"left\") {\n    return this.getSibling(\"right\");\n  } else if (this.key === \"right\") {\n    return this.getSibling(\"left\");\n  }\n  return null;\n}\n\nfunction addCompletionRecords(\n  path: NodePath | null | undefined,\n  records: Completion[],\n  context: CompletionContext,\n): Completion[] {\n  if (path) {\n    records.push(..._getCompletionRecords(path, context));\n  }\n  return records;\n}\n\nfunction completionRecordForSwitch(\n  cases: NodePath<t.SwitchCase>[],\n  records: Completion[],\n  context: CompletionContext,\n): Completion[] {\n  // https://tc39.es/ecma262/#sec-runtime-semantics-caseblockevaluation\n  let lastNormalCompletions: Completion[] = [];\n  for (let i = 0; i < cases.length; i++) {\n    const casePath = cases[i];\n    const caseCompletions = _getCompletionRecords(casePath, context);\n    const normalCompletions = [];\n    const breakCompletions = [];\n    for (const c of caseCompletions) {\n      if (c.type === NORMAL_COMPLETION) {\n        normalCompletions.push(c);\n      }\n      if (c.type === BREAK_COMPLETION) {\n        breakCompletions.push(c);\n      }\n    }\n    if (normalCompletions.length) {\n      lastNormalCompletions = normalCompletions;\n    }\n    records.push(...breakCompletions);\n  }\n  records.push(...lastNormalCompletions);\n  return records;\n}\n\nfunction normalCompletionToBreak(completions: Completion[]) {\n  completions.forEach(c => {\n    c.type = BREAK_COMPLETION;\n  });\n}\n\n/**\n * Determine how we should handle the break statement for break completions\n *\n * @param {Completion[]} completions\n * @param {boolean} reachable Whether the break statement is reachable after\n   we mark the normal completions _before_ the given break completions as the final\n   completions. For example,\n   `{ 0 }; break;` is transformed to `{ return 0 }; break;`, the `break` here is unreachable\n   and thus can be removed without consequences. We may in the future reserve them instead since\n   we do not consistently remove unreachable statements _after_ break\n   `{ var x = 0 }; break;` is transformed to `{ var x = 0 }; return void 0;`, the `break` is reachable\n   because we can not wrap variable declaration under a return statement\n */\nfunction replaceBreakStatementInBreakCompletion(\n  completions: Completion[],\n  reachable: boolean,\n) {\n  completions.forEach(c => {\n    if (c.path.isBreakStatement({ label: null })) {\n      if (reachable) {\n        c.path.replaceWith(unaryExpression(\"void\", numericLiteral(0)));\n      } else {\n        c.path.remove();\n      }\n    }\n  });\n}\n\nfunction getStatementListCompletion(\n  paths: NodePath[],\n  context: CompletionContext,\n): Completion[] {\n  const completions = [];\n  if (context.canHaveBreak) {\n    let lastNormalCompletions = [];\n    for (let i = 0; i < paths.length; i++) {\n      const path = paths[i];\n      const newContext = { ...context, inCaseClause: false };\n      if (\n        path.isBlockStatement() &&\n        (context.inCaseClause || // case test: { break }\n          context.shouldPopulateBreak) // case test: { { break } }\n      ) {\n        newContext.shouldPopulateBreak = true;\n      } else {\n        newContext.shouldPopulateBreak = false;\n      }\n      const statementCompletions = _getCompletionRecords(path, newContext);\n      if (\n        statementCompletions.length > 0 &&\n        // we can stop search `paths` when we have seen a `path` that is\n        // effectively a `break` statement. Examples are\n        // - `break`\n        // - `if (true) { 1; break } else { 2; break }`\n        // - `{ break }```\n        // In other words, the paths after this `path` are unreachable\n        statementCompletions.every(c => c.type === BREAK_COMPLETION)\n      ) {\n        if (\n          lastNormalCompletions.length > 0 &&\n          statementCompletions.every(c =>\n            c.path.isBreakStatement({ label: null }),\n          )\n        ) {\n          // when a break completion has a path as BreakStatement, it must be `{ break }`\n          // whose completion value we can not determine, otherwise it would have been\n          // replaced by `replaceBreakStatementInBreakCompletion`\n          // When we have seen normal completions from the last statement\n          // it is safe to stop populating break and mark normal completions as break\n          normalCompletionToBreak(lastNormalCompletions);\n          completions.push(...lastNormalCompletions);\n          // Declarations have empty completion record, however they can not be nested\n          // directly in return statement, i.e. `return (var a = 1)` is invalid.\n          if (lastNormalCompletions.some(c => c.path.isDeclaration())) {\n            completions.push(...statementCompletions);\n            replaceBreakStatementInBreakCompletion(\n              statementCompletions,\n              /* reachable */ true,\n            );\n          }\n          replaceBreakStatementInBreakCompletion(\n            statementCompletions,\n            /* reachable */ false,\n          );\n        } else {\n          completions.push(...statementCompletions);\n          if (!context.shouldPopulateBreak) {\n            replaceBreakStatementInBreakCompletion(\n              statementCompletions,\n              /* reachable */ true,\n            );\n          }\n        }\n        break;\n      }\n      if (i === paths.length - 1) {\n        completions.push(...statementCompletions);\n      } else {\n        lastNormalCompletions = [];\n        for (let i = 0; i < statementCompletions.length; i++) {\n          const c = statementCompletions[i];\n          if (c.type === BREAK_COMPLETION) {\n            completions.push(c);\n          }\n          if (c.type === NORMAL_COMPLETION) {\n            lastNormalCompletions.push(c);\n          }\n        }\n      }\n    }\n  } else if (paths.length) {\n    // When we are in a context where `break` must not exist, we can skip linear\n    // search on statement lists and assume that the last\n    // non-variable-declaration statement determines the completion.\n    for (let i = paths.length - 1; i >= 0; i--) {\n      const pathCompletions = _getCompletionRecords(paths[i], context);\n      if (\n        pathCompletions.length > 1 ||\n        (pathCompletions.length === 1 &&\n          !pathCompletions[0].path.isVariableDeclaration())\n      ) {\n        completions.push(...pathCompletions);\n        break;\n      }\n    }\n  }\n  return completions;\n}\n\nfunction _getCompletionRecords(\n  path: NodePath,\n  context: CompletionContext,\n): Completion[] {\n  let records: Completion[] = [];\n  if (path.isIfStatement()) {\n    records = addCompletionRecords(path.get(\"consequent\"), records, context);\n    records = addCompletionRecords(path.get(\"alternate\"), records, context);\n  } else if (\n    path.isDoExpression() ||\n    path.isFor() ||\n    path.isWhile() ||\n    path.isLabeledStatement()\n  ) {\n    // @ts-expect-error(flow->ts): todo\n    return addCompletionRecords(path.get(\"body\"), records, context);\n  } else if (path.isProgram() || path.isBlockStatement()) {\n    // @ts-expect-error(flow->ts): todo\n    return getStatementListCompletion(path.get(\"body\"), context);\n  } else if (path.isFunction()) {\n    return _getCompletionRecords(path.get(\"body\"), context);\n  } else if (path.isTryStatement()) {\n    records = addCompletionRecords(path.get(\"block\"), records, context);\n    records = addCompletionRecords(path.get(\"handler\"), records, context);\n  } else if (path.isCatchClause()) {\n    return addCompletionRecords(path.get(\"body\"), records, context);\n  } else if (path.isSwitchStatement()) {\n    return completionRecordForSwitch(path.get(\"cases\"), records, context);\n  } else if (path.isSwitchCase()) {\n    return getStatementListCompletion(path.get(\"consequent\"), {\n      canHaveBreak: true,\n      shouldPopulateBreak: false,\n      inCaseClause: true,\n    });\n  } else if (path.isBreakStatement()) {\n    records.push(BreakCompletion(path));\n  } else {\n    records.push(NormalCompletion(path));\n  }\n\n  return records;\n}\n\n/**\n * Retrieve the completion records of a given path.\n * Note: to ensure proper support on `break` statement, this method\n * will manipulate the AST around the break statement. Do not call the method\n * twice for the same path.\n *\n * @export\n * @param {NodePath} this\n * @returns {NodePath[]} Completion records\n */\nexport function getCompletionRecords(this: NodePath): NodePath[] {\n  const records = _getCompletionRecords(this, {\n    canHaveBreak: false,\n    shouldPopulateBreak: false,\n    inCaseClause: false,\n  });\n  return records.map(r => r.path);\n}\n\nexport function getSibling(this: NodePath, key: string | number): NodePath {\n  return NodePath.get({\n    parentPath: this.parentPath,\n    parent: this.parent,\n    container: this.container,\n    listKey: this.listKey,\n    key: key,\n  }).setContext(this.context);\n}\n\nexport function getPrevSibling(this: NodePath): NodePath {\n  // @ts-expect-error todo(flow->ts) this.key could be a string\n  return this.getSibling(this.key - 1);\n}\n\nexport function getNextSibling(this: NodePath): NodePath {\n  // @ts-expect-error todo(flow->ts) this.key could be a string\n  return this.getSibling(this.key + 1);\n}\n\nexport function getAllNextSiblings(this: NodePath): NodePath[] {\n  // @ts-expect-error todo(flow->ts) this.key could be a string\n  let _key: number = this.key;\n  let sibling = this.getSibling(++_key);\n  const siblings = [];\n  while (sibling.node) {\n    siblings.push(sibling);\n    sibling = this.getSibling(++_key);\n  }\n  return siblings;\n}\n\nexport function getAllPrevSiblings(this: NodePath): NodePath[] {\n  // @ts-expect-error todo(flow->ts) this.key could be a string\n  let _key: number = this.key;\n  let sibling = this.getSibling(--_key);\n  const siblings = [];\n  while (sibling.node) {\n    siblings.push(sibling);\n    sibling = this.getSibling(--_key);\n  }\n  return siblings;\n}\n\n// convert \"1\" to 1 (string index to number index)\ntype MaybeToIndex<T extends string> = T extends `${bigint}` ? number : T;\n\ntype Pattern<Obj extends string, Prop extends string> = `${Obj}.${Prop}`;\n\n// split \"body.body.1\" to [\"body\", \"body\", 1]\ntype Split<P extends string> = P extends Pattern<infer O, infer U>\n  ? [MaybeToIndex<O>, ...Split<U>]\n  : [MaybeToIndex<P>];\n\n// get all K with Node[K] is t.Node | t.Node[]\ntype NodeKeyOf<Node extends t.Node | t.Node[]> = keyof Pick<\n  Node,\n  {\n    [Key in keyof Node]-?: Node[Key] extends t.Node | t.Node[] ? Key : never;\n  }[keyof Node]\n>;\n\n// traverse the Node with tuple path [\"body\", \"body\", 1]\n// Path should be created with Split\ntype Trav<\n  Node extends t.Node | t.Node[],\n  Path extends unknown[],\n> = Path extends [infer K, ...infer R]\n  ? K extends NodeKeyOf<Node>\n    ? R extends []\n      ? Node[K]\n      : // @ts-expect-error ignore since TS is not smart enough\n        Trav<Node[K], R>\n    : never\n  : never;\n\ntype ToNodePath<T> = T extends Array<t.Node | null | undefined>\n  ? Array<NodePath<T[number]>>\n  : T extends t.Node | null | undefined\n  ? NodePath<T>\n  : never;\n\nfunction get<T extends t.Node, K extends keyof T>(\n  this: NodePath<T>,\n  key: K,\n  context?: boolean | TraversalContext,\n): T[K] extends Array<t.Node | null | undefined>\n  ? Array<NodePath<T[K][number]>>\n  : T[K] extends t.Node | null | undefined\n  ? NodePath<T[K]>\n  : never;\n\nfunction get<T extends t.Node, K extends string>(\n  this: NodePath<T>,\n  key: K,\n  context?: boolean | TraversalContext,\n): ToNodePath<Trav<T, Split<K>>>;\n\nfunction get<T extends t.Node>(\n  this: NodePath<T>,\n  key: string,\n  context?: true | TraversalContext,\n): NodePath | NodePath[];\n\nfunction get<T extends t.Node>(\n  this: NodePath<T>,\n  key: string,\n  context: true | TraversalContext = true,\n): NodePath | NodePath[] {\n  if (context === true) context = this.context;\n  const parts = key.split(\".\");\n  if (parts.length === 1) {\n    // \"foo\"\n    // @ts-expect-error key may not index T\n    return this._getKey(key, context);\n  } else {\n    // \"foo.bar\"\n    return this._getPattern(parts, context);\n  }\n}\n\nexport { get };\n\nexport function _getKey<T extends t.Node>(\n  this: NodePath<T>,\n  key: keyof T & string,\n  context?: TraversalContext,\n): NodePath | NodePath[] {\n  const node = this.node;\n  const container = node[key];\n\n  if (Array.isArray(container)) {\n    // requested a container so give them all the paths\n    return container.map((_, i) => {\n      return NodePath.get({\n        listKey: key,\n        parentPath: this,\n        parent: node,\n        container: container,\n        key: i,\n      }).setContext(context);\n    });\n  } else {\n    return NodePath.get({\n      parentPath: this,\n      parent: node,\n      container: node,\n      key: key,\n    }).setContext(context);\n  }\n}\n\nexport function _getPattern(\n  this: NodePath,\n  parts: string[],\n  context?: TraversalContext,\n): NodePath | NodePath[] {\n  let path: NodePath | NodePath[] = this;\n  for (const part of parts) {\n    if (part === \".\") {\n      // @ts-expect-error todo(flow-ts): Can path be an array here?\n      path = path.parentPath;\n    } else {\n      if (Array.isArray(path)) {\n        // @ts-expect-error part may not index path\n        path = path[part];\n      } else {\n        path = path.get(part, context);\n      }\n    }\n  }\n  return path;\n}\n\nfunction getBindingIdentifiers(\n  duplicates: true,\n): Record<string, t.Identifier[]>;\nfunction getBindingIdentifiers(\n  duplicates?: false,\n): Record<string, t.Identifier>;\nfunction getBindingIdentifiers(\n  duplicates: boolean,\n): Record<string, t.Identifier[] | t.Identifier>;\n\nfunction getBindingIdentifiers(\n  this: NodePath,\n  duplicates?: boolean,\n): Record<string, t.Identifier[] | t.Identifier> {\n  return _getBindingIdentifiers(this.node, duplicates);\n}\n\nexport { getBindingIdentifiers };\n\nfunction getOuterBindingIdentifiers(\n  duplicates: true,\n): Record<string, t.Identifier[]>;\nfunction getOuterBindingIdentifiers(\n  duplicates?: false,\n): Record<string, t.Identifier>;\nfunction getOuterBindingIdentifiers(\n  duplicates: boolean,\n): Record<string, t.Identifier[] | t.Identifier>;\n\nfunction getOuterBindingIdentifiers(\n  this: NodePath,\n  duplicates?: boolean,\n): Record<string, t.Identifier[] | t.Identifier> {\n  return _getOuterBindingIdentifiers(this.node, duplicates);\n}\n\nexport { getOuterBindingIdentifiers };\n\nfunction getBindingIdentifierPaths(\n  duplicates: true,\n  outerOnly?: boolean,\n): Record<string, NodePath<t.Identifier>[]>;\nfunction getBindingIdentifierPaths(\n  duplicates: false,\n  outerOnly?: boolean,\n): Record<string, NodePath<t.Identifier>>;\nfunction getBindingIdentifierPaths(\n  duplicates?: boolean,\n  outerOnly?: boolean,\n): Record<string, NodePath<t.Identifier> | NodePath<t.Identifier>[]>;\n\n// original source - https://github.com/babel/babel/blob/main/packages/babel-types/src/retrievers/getBindingIdentifiers.js\n// path.getBindingIdentifiers returns nodes where the following re-implementation returns paths\nfunction getBindingIdentifierPaths(\n  this: NodePath,\n  duplicates: boolean = false,\n  outerOnly: boolean = false,\n): Record<string, NodePath<t.Identifier> | NodePath<t.Identifier>[]> {\n  const path = this;\n  const search = [path];\n  const ids = Object.create(null);\n\n  while (search.length) {\n    const id = search.shift();\n    if (!id) continue;\n    if (!id.node) continue;\n\n    const keys =\n      // @ts-expect-error _getBindingIdentifiers.keys do not cover all node types\n      _getBindingIdentifiers.keys[id.node.type];\n\n    if (id.isIdentifier()) {\n      if (duplicates) {\n        const _ids = (ids[id.node.name] = ids[id.node.name] || []);\n        _ids.push(id);\n      } else {\n        ids[id.node.name] = id;\n      }\n      continue;\n    }\n\n    if (id.isExportDeclaration()) {\n      const declaration = id.get(\"declaration\");\n      if (isDeclaration(declaration)) {\n        search.push(declaration);\n      }\n      continue;\n    }\n\n    if (outerOnly) {\n      if (id.isFunctionDeclaration()) {\n        search.push(id.get(\"id\"));\n        continue;\n      }\n      if (id.isFunctionExpression()) {\n        continue;\n      }\n    }\n\n    if (keys) {\n      for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const child = id.get(key);\n        if (Array.isArray(child)) {\n          search.push(...child);\n        } else if (child.node) {\n          search.push(child);\n        }\n      }\n    }\n  }\n\n  return ids;\n}\n\nexport { getBindingIdentifierPaths };\n\nfunction getOuterBindingIdentifierPaths(\n  duplicates: true,\n): Record<string, NodePath<t.Identifier>[]>;\nfunction getOuterBindingIdentifierPaths(\n  duplicates?: false,\n): Record<string, NodePath<t.Identifier>>;\nfunction getOuterBindingIdentifierPaths(\n  duplicates?: boolean,\n): Record<string, NodePath<t.Identifier> | NodePath<t.Identifier>[]>;\n\nfunction getOuterBindingIdentifierPaths(\n  this: NodePath,\n  duplicates: boolean = false,\n) {\n  return this.getBindingIdentifierPaths(duplicates, true);\n}\n\nexport { getOuterBindingIdentifierPaths };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,EAAA,GAAAD,OAAA;AAMsB;EALpBE,qBAAqB,EAAIC,sBAAsB;EAC/CC,0BAA0B,EAAIC,2BAA2B;EACzDC,aAAa;EACbC,cAAc;EACdC;AAAe,IAAAP,EAAA;AAIjB,MAAMQ,iBAAiB,GAAG,CAAU;AACpC,MAAMC,gBAAgB,GAAG,CAAU;AAmBnC,SAASC,gBAAgBA,CAACC,IAAc,EAAE;EACxC,OAAO;IAAEC,IAAI,EAAEJ,iBAAiB;IAAEG;EAAK,CAAC;AAC1C;AAEA,SAASE,eAAeA,CAACF,IAAc,EAAE;EACvC,OAAO;IAAEC,IAAI,EAAEH,gBAAgB;IAAEE;EAAK,CAAC;AACzC;AAEO,SAASG,WAAWA,CAAA,EAAkC;EAC3D,IAAI,IAAI,CAACC,GAAG,KAAK,MAAM,EAAE;IACvB,OAAO,IAAI,CAACC,UAAU,CAAC,OAAO,CAAC;EACjC,CAAC,MAAM,IAAI,IAAI,CAACD,GAAG,KAAK,OAAO,EAAE;IAC/B,OAAO,IAAI,CAACC,UAAU,CAAC,MAAM,CAAC;EAChC;EACA,OAAO,IAAI;AACb;AAEA,SAASC,oBAAoBA,CAC3BN,IAAiC,EACjCO,OAAqB,EACrBC,OAA0B,EACZ;EACd,IAAIR,IAAI,EAAE;IACRO,OAAO,CAACE,IAAI,CAAC,GAAGC,qBAAqB,CAACV,IAAI,EAAEQ,OAAO,CAAC,CAAC;EACvD;EACA,OAAOD,OAAO;AAChB;AAEA,SAASI,yBAAyBA,CAChCC,KAA+B,EAC/BL,OAAqB,EACrBC,OAA0B,EACZ;EAEd,IAAIK,qBAAmC,GAAG,EAAE;EAC5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAME,QAAQ,GAAGJ,KAAK,CAACE,CAAC,CAAC;IACzB,MAAMG,eAAe,GAAGP,qBAAqB,CAACM,QAAQ,EAAER,OAAO,CAAC;IAChE,MAAMU,iBAAiB,GAAG,EAAE;IAC5B,MAAMC,gBAAgB,GAAG,EAAE;IAC3B,KAAK,MAAMC,CAAC,IAAIH,eAAe,EAAE;MAC/B,IAAIG,CAAC,CAACnB,IAAI,KAAKJ,iBAAiB,EAAE;QAChCqB,iBAAiB,CAACT,IAAI,CAACW,CAAC,CAAC;MAC3B;MACA,IAAIA,CAAC,CAACnB,IAAI,KAAKH,gBAAgB,EAAE;QAC/BqB,gBAAgB,CAACV,IAAI,CAACW,CAAC,CAAC;MAC1B;IACF;IACA,IAAIF,iBAAiB,CAACH,MAAM,EAAE;MAC5BF,qBAAqB,GAAGK,iBAAiB;IAC3C;IACAX,OAAO,CAACE,IAAI,CAAC,GAAGU,gBAAgB,CAAC;EACnC;EACAZ,OAAO,CAACE,IAAI,CAAC,GAAGI,qBAAqB,CAAC;EACtC,OAAON,OAAO;AAChB;AAEA,SAASc,uBAAuBA,CAACC,WAAyB,EAAE;EAC1DA,WAAW,CAACC,OAAO,CAACH,CAAC,IAAI;IACvBA,CAAC,CAACnB,IAAI,GAAGH,gBAAgB;EAC3B,CAAC,CAAC;AACJ;AAeA,SAAS0B,sCAAsCA,CAC7CF,WAAyB,EACzBG,SAAkB,EAClB;EACAH,WAAW,CAACC,OAAO,CAACH,CAAC,IAAI;IACvB,IAAIA,CAAC,CAACpB,IAAI,CAAC0B,gBAAgB,CAAC;MAAEC,KAAK,EAAE;IAAK,CAAC,CAAC,EAAE;MAC5C,IAAIF,SAAS,EAAE;QACbL,CAAC,CAACpB,IAAI,CAAC4B,WAAW,CAAChC,eAAe,CAAC,MAAM,EAAED,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAChE,CAAC,MAAM;QACLyB,CAAC,CAACpB,IAAI,CAAC6B,MAAM,EAAE;MACjB;IACF;EACF,CAAC,CAAC;AACJ;AAEA,SAASC,0BAA0BA,CACjCC,KAAiB,EACjBvB,OAA0B,EACZ;EACd,MAAMc,WAAW,GAAG,EAAE;EACtB,IAAId,OAAO,CAACwB,YAAY,EAAE;IACxB,IAAInB,qBAAqB,GAAG,EAAE;IAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,KAAK,CAAChB,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,MAAMd,IAAI,GAAG+B,KAAK,CAACjB,CAAC,CAAC;MACrB,MAAMmB,UAAU,GAAAC,MAAA,CAAAC,MAAA,KAAQ3B,OAAO;QAAE4B,YAAY,EAAE;MAAK,EAAE;MACtD,IACEpC,IAAI,CAACqC,gBAAgB,EAAE,KACtB7B,OAAO,CAAC4B,YAAY,IACnB5B,OAAO,CAAC8B,mBAAmB,CAAC,EAC9B;QACAL,UAAU,CAACK,mBAAmB,GAAG,IAAI;MACvC,CAAC,MAAM;QACLL,UAAU,CAACK,mBAAmB,GAAG,KAAK;MACxC;MACA,MAAMC,oBAAoB,GAAG7B,qBAAqB,CAACV,IAAI,EAAEiC,UAAU,CAAC;MACpE,IACEM,oBAAoB,CAACxB,MAAM,GAAG,CAAC,IAO/BwB,oBAAoB,CAACC,KAAK,CAACpB,CAAC,IAAIA,CAAC,CAACnB,IAAI,KAAKH,gBAAgB,CAAC,EAC5D;QACA,IACEe,qBAAqB,CAACE,MAAM,GAAG,CAAC,IAChCwB,oBAAoB,CAACC,KAAK,CAACpB,CAAC,IAC1BA,CAAC,CAACpB,IAAI,CAAC0B,gBAAgB,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAC,CAAC,CACzC,EACD;UAMAN,uBAAuB,CAACR,qBAAqB,CAAC;UAC9CS,WAAW,CAACb,IAAI,CAAC,GAAGI,qBAAqB,CAAC;UAG1C,IAAIA,qBAAqB,CAAC4B,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAACpB,IAAI,CAACN,aAAa,EAAE,CAAC,EAAE;YAC3D4B,WAAW,CAACb,IAAI,CAAC,GAAG8B,oBAAoB,CAAC;YACzCf,sCAAsC,CACpCe,oBAAoB,EACJ,IAAI,CACrB;UACH;UACAf,sCAAsC,CACpCe,oBAAoB,EACJ,KAAK,CACtB;QACH,CAAC,MAAM;UACLjB,WAAW,CAACb,IAAI,CAAC,GAAG8B,oBAAoB,CAAC;UACzC,IAAI,CAAC/B,OAAO,CAAC8B,mBAAmB,EAAE;YAChCd,sCAAsC,CACpCe,oBAAoB,EACJ,IAAI,CACrB;UACH;QACF;QACA;MACF;MACA,IAAIzB,CAAC,KAAKiB,KAAK,CAAChB,MAAM,GAAG,CAAC,EAAE;QAC1BO,WAAW,CAACb,IAAI,CAAC,GAAG8B,oBAAoB,CAAC;MAC3C,CAAC,MAAM;QACL1B,qBAAqB,GAAG,EAAE;QAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,oBAAoB,CAACxB,MAAM,EAAED,CAAC,EAAE,EAAE;UACpD,MAAMM,CAAC,GAAGmB,oBAAoB,CAACzB,CAAC,CAAC;UACjC,IAAIM,CAAC,CAACnB,IAAI,KAAKH,gBAAgB,EAAE;YAC/BwB,WAAW,CAACb,IAAI,CAACW,CAAC,CAAC;UACrB;UACA,IAAIA,CAAC,CAACnB,IAAI,KAAKJ,iBAAiB,EAAE;YAChCgB,qBAAqB,CAACJ,IAAI,CAACW,CAAC,CAAC;UAC/B;QACF;MACF;IACF;EACF,CAAC,MAAM,IAAIW,KAAK,CAAChB,MAAM,EAAE;IAIvB,KAAK,IAAID,CAAC,GAAGiB,KAAK,CAAChB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1C,MAAM4B,eAAe,GAAGhC,qBAAqB,CAACqB,KAAK,CAACjB,CAAC,CAAC,EAAEN,OAAO,CAAC;MAChE,IACEkC,eAAe,CAAC3B,MAAM,GAAG,CAAC,IACzB2B,eAAe,CAAC3B,MAAM,KAAK,CAAC,IAC3B,CAAC2B,eAAe,CAAC,CAAC,CAAC,CAAC1C,IAAI,CAAC2C,qBAAqB,EAAG,EACnD;QACArB,WAAW,CAACb,IAAI,CAAC,GAAGiC,eAAe,CAAC;QACpC;MACF;IACF;EACF;EACA,OAAOpB,WAAW;AACpB;AAEA,SAASZ,qBAAqBA,CAC5BV,IAAc,EACdQ,OAA0B,EACZ;EACd,IAAID,OAAqB,GAAG,EAAE;EAC9B,IAAIP,IAAI,CAAC4C,aAAa,EAAE,EAAE;IACxBrC,OAAO,GAAGD,oBAAoB,CAACN,IAAI,CAAC6C,GAAG,CAAC,YAAY,CAAC,EAAEtC,OAAO,EAAEC,OAAO,CAAC;IACxED,OAAO,GAAGD,oBAAoB,CAACN,IAAI,CAAC6C,GAAG,CAAC,WAAW,CAAC,EAAEtC,OAAO,EAAEC,OAAO,CAAC;EACzE,CAAC,MAAM,IACLR,IAAI,CAAC8C,cAAc,EAAE,IACrB9C,IAAI,CAAC+C,KAAK,EAAE,IACZ/C,IAAI,CAACgD,OAAO,EAAE,IACdhD,IAAI,CAACiD,kBAAkB,EAAE,EACzB;IAEA,OAAO3C,oBAAoB,CAACN,IAAI,CAAC6C,GAAG,CAAC,MAAM,CAAC,EAAEtC,OAAO,EAAEC,OAAO,CAAC;EACjE,CAAC,MAAM,IAAIR,IAAI,CAACkD,SAAS,EAAE,IAAIlD,IAAI,CAACqC,gBAAgB,EAAE,EAAE;IAEtD,OAAOP,0BAA0B,CAAC9B,IAAI,CAAC6C,GAAG,CAAC,MAAM,CAAC,EAAErC,OAAO,CAAC;EAC9D,CAAC,MAAM,IAAIR,IAAI,CAACmD,UAAU,EAAE,EAAE;IAC5B,OAAOzC,qBAAqB,CAACV,IAAI,CAAC6C,GAAG,CAAC,MAAM,CAAC,EAAErC,OAAO,CAAC;EACzD,CAAC,MAAM,IAAIR,IAAI,CAACoD,cAAc,EAAE,EAAE;IAChC7C,OAAO,GAAGD,oBAAoB,CAACN,IAAI,CAAC6C,GAAG,CAAC,OAAO,CAAC,EAAEtC,OAAO,EAAEC,OAAO,CAAC;IACnED,OAAO,GAAGD,oBAAoB,CAACN,IAAI,CAAC6C,GAAG,CAAC,SAAS,CAAC,EAAEtC,OAAO,EAAEC,OAAO,CAAC;EACvE,CAAC,MAAM,IAAIR,IAAI,CAACqD,aAAa,EAAE,EAAE;IAC/B,OAAO/C,oBAAoB,CAACN,IAAI,CAAC6C,GAAG,CAAC,MAAM,CAAC,EAAEtC,OAAO,EAAEC,OAAO,CAAC;EACjE,CAAC,MAAM,IAAIR,IAAI,CAACsD,iBAAiB,EAAE,EAAE;IACnC,OAAO3C,yBAAyB,CAACX,IAAI,CAAC6C,GAAG,CAAC,OAAO,CAAC,EAAEtC,OAAO,EAAEC,OAAO,CAAC;EACvE,CAAC,MAAM,IAAIR,IAAI,CAACuD,YAAY,EAAE,EAAE;IAC9B,OAAOzB,0BAA0B,CAAC9B,IAAI,CAAC6C,GAAG,CAAC,YAAY,CAAC,EAAE;MACxDb,YAAY,EAAE,IAAI;MAClBM,mBAAmB,EAAE,KAAK;MAC1BF,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIpC,IAAI,CAAC0B,gBAAgB,EAAE,EAAE;IAClCnB,OAAO,CAACE,IAAI,CAACP,eAAe,CAACF,IAAI,CAAC,CAAC;EACrC,CAAC,MAAM;IACLO,OAAO,CAACE,IAAI,CAACV,gBAAgB,CAACC,IAAI,CAAC,CAAC;EACtC;EAEA,OAAOO,OAAO;AAChB;AAYO,SAASiD,oBAAoBA,CAAA,EAA6B;EAC/D,MAAMjD,OAAO,GAAGG,qBAAqB,CAAC,IAAI,EAAE;IAC1CsB,YAAY,EAAE,KAAK;IACnBM,mBAAmB,EAAE,KAAK;IAC1BF,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,OAAO7B,OAAO,CAACkD,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC1D,IAAI,CAAC;AACjC;AAEO,SAASK,UAAUA,CAAiBD,GAAoB,EAAY;EACzE,OAAOuD,cAAQ,CAACd,GAAG,CAAC;IAClBe,UAAU,EAAE,IAAI,CAACA,UAAU;IAC3BC,MAAM,EAAE,IAAI,CAACA,MAAM;IACnBC,SAAS,EAAE,IAAI,CAACA,SAAS;IACzBC,OAAO,EAAE,IAAI,CAACA,OAAO;IACrB3D,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC4D,UAAU,CAAC,IAAI,CAACxD,OAAO,CAAC;AAC7B;AAEO,SAASyD,cAAcA,CAAA,EAA2B;EAEvD,OAAO,IAAI,CAAC5D,UAAU,CAAC,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC;AACtC;AAEO,SAAS8D,cAAcA,CAAA,EAA2B;EAEvD,OAAO,IAAI,CAAC7D,UAAU,CAAC,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC;AACtC;AAEO,SAAS+D,kBAAkBA,CAAA,EAA6B;EAE7D,IAAIC,IAAY,GAAG,IAAI,CAAChE,GAAG;EAC3B,IAAIiE,OAAO,GAAG,IAAI,CAAChE,UAAU,CAAC,EAAE+D,IAAI,CAAC;EACrC,MAAME,QAAQ,GAAG,EAAE;EACnB,OAAOD,OAAO,CAACE,IAAI,EAAE;IACnBD,QAAQ,CAAC7D,IAAI,CAAC4D,OAAO,CAAC;IACtBA,OAAO,GAAG,IAAI,CAAChE,UAAU,CAAC,EAAE+D,IAAI,CAAC;EACnC;EACA,OAAOE,QAAQ;AACjB;AAEO,SAASE,kBAAkBA,CAAA,EAA6B;EAE7D,IAAIJ,IAAY,GAAG,IAAI,CAAChE,GAAG;EAC3B,IAAIiE,OAAO,GAAG,IAAI,CAAChE,UAAU,CAAC,EAAE+D,IAAI,CAAC;EACrC,MAAME,QAAQ,GAAG,EAAE;EACnB,OAAOD,OAAO,CAACE,IAAI,EAAE;IACnBD,QAAQ,CAAC7D,IAAI,CAAC4D,OAAO,CAAC;IACtBA,OAAO,GAAG,IAAI,CAAChE,UAAU,CAAC,EAAE+D,IAAI,CAAC;EACnC;EACA,OAAOE,QAAQ;AACjB;AA8DA,SAASzB,GAAGA,CAEVzC,GAAW,EACXI,OAAgC,GAAG,IAAI,EAChB;EACvB,IAAIA,OAAO,KAAK,IAAI,EAAEA,OAAO,GAAG,IAAI,CAACA,OAAO;EAC5C,MAAMiE,KAAK,GAAGrE,GAAG,CAACsE,KAAK,CAAC,GAAG,CAAC;EAC5B,IAAID,KAAK,CAAC1D,MAAM,KAAK,CAAC,EAAE;IAGtB,OAAO,IAAI,CAAC4D,OAAO,CAACvE,GAAG,EAAEI,OAAO,CAAC;EACnC,CAAC,MAAM;IAEL,OAAO,IAAI,CAACoE,WAAW,CAACH,KAAK,EAAEjE,OAAO,CAAC;EACzC;AACF;AAIO,SAASmE,OAAOA,CAErBvE,GAAqB,EACrBI,OAA0B,EACH;EACvB,MAAM+D,IAAI,GAAG,IAAI,CAACA,IAAI;EACtB,MAAMT,SAAS,GAAGS,IAAI,CAACnE,GAAG,CAAC;EAE3B,IAAIyE,KAAK,CAACC,OAAO,CAAChB,SAAS,CAAC,EAAE;IAE5B,OAAOA,SAAS,CAACL,GAAG,CAAC,CAACsB,CAAC,EAAEjE,CAAC,KAAK;MAC7B,OAAO6C,cAAQ,CAACd,GAAG,CAAC;QAClBkB,OAAO,EAAE3D,GAAG;QACZwD,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAEU,IAAI;QACZT,SAAS,EAAEA,SAAS;QACpB1D,GAAG,EAAEU;MACP,CAAC,CAAC,CAACkD,UAAU,CAACxD,OAAO,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAOmD,cAAQ,CAACd,GAAG,CAAC;MAClBe,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAEU,IAAI;MACZT,SAAS,EAAES,IAAI;MACfnE,GAAG,EAAEA;IACP,CAAC,CAAC,CAAC4D,UAAU,CAACxD,OAAO,CAAC;EACxB;AACF;AAEO,SAASoE,WAAWA,CAEzBH,KAAe,EACfjE,OAA0B,EACH;EACvB,IAAIR,IAA2B,GAAG,IAAI;EACtC,KAAK,MAAMgF,IAAI,IAAIP,KAAK,EAAE;IACxB,IAAIO,IAAI,KAAK,GAAG,EAAE;MAEhBhF,IAAI,GAAGA,IAAI,CAAC4D,UAAU;IACxB,CAAC,MAAM;MACL,IAAIiB,KAAK,CAACC,OAAO,CAAC9E,IAAI,CAAC,EAAE;QAEvBA,IAAI,GAAGA,IAAI,CAACgF,IAAI,CAAC;MACnB,CAAC,MAAM;QACLhF,IAAI,GAAGA,IAAI,CAAC6C,GAAG,CAACmC,IAAI,EAAExE,OAAO,CAAC;MAChC;IACF;EACF;EACA,OAAOR,IAAI;AACb;AAYA,SAASV,qBAAqBA,CAE5B2F,UAAoB,EAC2B;EAC/C,OAAO1F,sBAAsB,CAAC,IAAI,CAACgF,IAAI,EAAEU,UAAU,CAAC;AACtD;AAcA,SAASzF,0BAA0BA,CAEjCyF,UAAoB,EAC2B;EAC/C,OAAOxF,2BAA2B,CAAC,IAAI,CAAC8E,IAAI,EAAEU,UAAU,CAAC;AAC3D;AAmBA,SAASC,yBAAyBA,CAEhCD,UAAmB,GAAG,KAAK,EAC3BE,SAAkB,GAAG,KAAK,EACyC;EACnE,MAAMnF,IAAI,GAAG,IAAI;EACjB,MAAMoF,MAAM,GAAG,CAACpF,IAAI,CAAC;EACrB,MAAMqF,GAAG,GAAGnD,MAAM,CAACoD,MAAM,CAAC,IAAI,CAAC;EAE/B,OAAOF,MAAM,CAACrE,MAAM,EAAE;IACpB,MAAMwE,EAAE,GAAGH,MAAM,CAACI,KAAK,EAAE;IACzB,IAAI,CAACD,EAAE,EAAE;IACT,IAAI,CAACA,EAAE,CAAChB,IAAI,EAAE;IAEd,MAAMkB,IAAI,GAERlG,sBAAsB,CAACkG,IAAI,CAACF,EAAE,CAAChB,IAAI,CAACtE,IAAI,CAAC;IAE3C,IAAIsF,EAAE,CAACG,YAAY,EAAE,EAAE;MACrB,IAAIT,UAAU,EAAE;QACd,MAAMU,IAAI,GAAIN,GAAG,CAACE,EAAE,CAAChB,IAAI,CAACqB,IAAI,CAAC,GAAGP,GAAG,CAACE,EAAE,CAAChB,IAAI,CAACqB,IAAI,CAAC,IAAI,EAAG;QAC1DD,IAAI,CAAClF,IAAI,CAAC8E,EAAE,CAAC;MACf,CAAC,MAAM;QACLF,GAAG,CAACE,EAAE,CAAChB,IAAI,CAACqB,IAAI,CAAC,GAAGL,EAAE;MACxB;MACA;IACF;IAEA,IAAIA,EAAE,CAACM,mBAAmB,EAAE,EAAE;MAC5B,MAAMC,WAAW,GAAGP,EAAE,CAAC1C,GAAG,CAAC,aAAa,CAAC;MACzC,IAAInD,aAAa,CAACoG,WAAW,CAAC,EAAE;QAC9BV,MAAM,CAAC3E,IAAI,CAACqF,WAAW,CAAC;MAC1B;MACA;IACF;IAEA,IAAIX,SAAS,EAAE;MACb,IAAII,EAAE,CAACQ,qBAAqB,EAAE,EAAE;QAC9BX,MAAM,CAAC3E,IAAI,CAAC8E,EAAE,CAAC1C,GAAG,CAAC,IAAI,CAAC,CAAC;QACzB;MACF;MACA,IAAI0C,EAAE,CAACS,oBAAoB,EAAE,EAAE;QAC7B;MACF;IACF;IAEA,IAAIP,IAAI,EAAE;MACR,KAAK,IAAI3E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2E,IAAI,CAAC1E,MAAM,EAAED,CAAC,EAAE,EAAE;QACpC,MAAMV,GAAG,GAAGqF,IAAI,CAAC3E,CAAC,CAAC;QACnB,MAAMmF,KAAK,GAAGV,EAAE,CAAC1C,GAAG,CAACzC,GAAG,CAAC;QACzB,IAAIyE,KAAK,CAACC,OAAO,CAACmB,KAAK,CAAC,EAAE;UACxBb,MAAM,CAAC3E,IAAI,CAAC,GAAGwF,KAAK,CAAC;QACvB,CAAC,MAAM,IAAIA,KAAK,CAAC1B,IAAI,EAAE;UACrBa,MAAM,CAAC3E,IAAI,CAACwF,KAAK,CAAC;QACpB;MACF;IACF;EACF;EAEA,OAAOZ,GAAG;AACZ;AAcA,SAASa,8BAA8BA,CAErCjB,UAAmB,GAAG,KAAK,EAC3B;EACA,OAAO,IAAI,CAACC,yBAAyB,CAACD,UAAU,EAAE,IAAI,CAAC;AACzD"}