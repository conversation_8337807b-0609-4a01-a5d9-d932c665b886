import { spacing } from '@appmaker-xyz/uikit/src/styles/index';
import { appSettings } from '@appmaker-xyz/core';

const blocks = [
  {
    name: 'appmaker/remoteImage',
    attributes: {
      name: 'LOGIN_LOGO',
      style: {
        width: 150,
        height: 50,
        resizeMode: 'contain',
        marginBottom: spacing.lg,
      },
    },
  },
  {
    name: 'appmaker/text',
    attributes: {
      content: 'Email OTP Login',
      category: 'h1Heading',
      style: {
        marginBottom: spacing.nano,
      },
    },
  },
  {
    name: 'appmaker/text',
    attributes: {
      content:
        'This is the email address that you will use to log in to your account.',
      category: 'highlighter1',
      status: 'grey',
      style: {
        marginBottom: spacing.lg,
      },
    },
  },
  {
    name: 'appmaker/floating-label-input',
    attributes: {
      label: 'Email',
      name: 'email',
      status: 'grey',
      // caption:
      // 'This is the email address that you will use to log in to your account.',
      type: 'email',
      defaultValue: '',
    },
  },
  {
    clientId: 'checkout-sumbit',
    name: 'appmaker/ActionButton',
    attributes: {
      content: 'Request OTP',
      loading: false,
      baseSize: true,
      appmakerAction: {
        action: 'LUCENT_REQUEST_EMAIL_OTP',
      },
      wholeContainerStyle: {
        borderRadius: 0,
        backgroundColor: appSettings.getOption('primary_button_color'),
      },
      __appmakerStylesClassName: 'registerButton',
    },
  },
];
const EmailOtpRequest = {
  type: 'normal',
  title: '',
  attributes: {
    renderInScroll: true,
    renderType: 'normal',
    showLoadingTillData: true,
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
      backgroundColor: '#ffffff',
    },
    contentContainerStyle: {
      flex: 1,
      justifyContent: 'center',
      paddingHorizontal: spacing.base,
      paddingTop: spacing.xl * 2,
    },
  },
  blocks: blocks,
  // fixedFooter: true,
};
export default EmailOtpRequest;
