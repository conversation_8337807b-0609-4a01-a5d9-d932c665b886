{"version": 3, "names": ["ReferencedIdentifier", "exports", "ReferencedMemberExpression", "BindingIdentifier", "Statement", "Expression", "<PERSON><PERSON>", "Referenced", "BlockScoped", "Var", "User", "Generated", "Pure", "Flow", "RestProperty", "SpreadProperty", "ExistentialTypeParam", "NumericLiteralTypeAnnotation", "ForAwaitStatement"], "sources": ["../../../src/path/lib/virtual-types.ts"], "sourcesContent": ["import type * as t from \"@babel/types\";\n\nexport interface VirtualTypeAliases {\n  BindingIdentifier: t.Identifier;\n  BlockScoped: t.Node;\n  ExistentialTypeParam: t.ExistsTypeAnnotation;\n  Expression: t.Expression;\n  Flow: t.Flow | t.ImportDeclaration | t.ExportDeclaration | t.ImportSpecifier;\n  ForAwaitStatement: t.ForOfStatement;\n  Generated: t.Node;\n  NumericLiteralTypeAnnotation: t.NumberLiteralTypeAnnotation;\n  Pure: t.Node;\n  Referenced: t.Node;\n  ReferencedIdentifier: t.Identifier | t.JSXIdentifier;\n  ReferencedMemberExpression: t.MemberExpression;\n  RestProperty: t.RestElement;\n  Scope: t.Scopable | t.Pattern;\n  SpreadProperty: t.RestElement;\n  Statement: t.Statement;\n  User: t.Node;\n  Var: t.VariableDeclaration;\n}\n\ntype VirtualTypeMapping = readonly (t.Node[\"type\"] | keyof t.Aliases)[] | null;\n\nexport const ReferencedIdentifier: VirtualTypeMapping = [\n  \"Identifier\",\n  \"JSXIdentifier\",\n] as const;\n\nexport const ReferencedMemberExpression: VirtualTypeMapping = [\n  \"MemberExpression\",\n] as const;\n\nexport const BindingIdentifier: VirtualTypeMapping = [\"Identifier\"] as const;\n\nexport const Statement: VirtualTypeMapping = [\"Statement\"] as const;\n\nexport const Expression: VirtualTypeMapping = [\"Expression\"] as const;\n\nexport const Scope: VirtualTypeMapping = [\"Scopable\", \"Pattern\"] as const;\n\nexport const Referenced: VirtualTypeMapping = null;\n\nexport const BlockScoped: VirtualTypeMapping = null;\n\nexport const Var: VirtualTypeMapping = [\"VariableDeclaration\"];\n\nexport const User: VirtualTypeMapping = null;\n\nexport const Generated: VirtualTypeMapping = null;\n\nexport const Pure: VirtualTypeMapping = null;\n\nexport const Flow: VirtualTypeMapping = [\n  \"Flow\",\n  \"ImportDeclaration\",\n  \"ExportDeclaration\",\n  \"ImportSpecifier\",\n] as const;\n\n// TODO: 7.0 Backwards Compat\nexport const RestProperty: VirtualTypeMapping = [\"RestElement\"] as const;\n\nexport const SpreadProperty: VirtualTypeMapping = [\"RestElement\"] as const;\n\nexport const ExistentialTypeParam: VirtualTypeMapping = [\n  \"ExistsTypeAnnotation\",\n] as const;\n\nexport const NumericLiteralTypeAnnotation: VirtualTypeMapping = [\n  \"NumberLiteralTypeAnnotation\",\n] as const;\n\nexport const ForAwaitStatement: VirtualTypeMapping = [\n  \"ForOfStatement\",\n] as const;\n"], "mappings": ";;;;;;AAyBO,MAAMA,oBAAwC,GAAG,CACtD,YAAY,EACZ,eAAe,CACP;AAACC,OAAA,CAAAD,oBAAA,GAAAA,oBAAA;AAEJ,MAAME,0BAA8C,GAAG,CAC5D,kBAAkB,CACV;AAACD,OAAA,CAAAC,0BAAA,GAAAA,0BAAA;AAEJ,MAAMC,iBAAqC,GAAG,CAAC,YAAY,CAAU;AAACF,OAAA,CAAAE,iBAAA,GAAAA,iBAAA;AAEtE,MAAMC,SAA6B,GAAG,CAAC,WAAW,CAAU;AAACH,OAAA,CAAAG,SAAA,GAAAA,SAAA;AAE7D,MAAMC,UAA8B,GAAG,CAAC,YAAY,CAAU;AAACJ,OAAA,CAAAI,UAAA,GAAAA,UAAA;AAE/D,MAAMC,KAAyB,GAAG,CAAC,UAAU,EAAE,SAAS,CAAU;AAACL,OAAA,CAAAK,KAAA,GAAAA,KAAA;AAEnE,MAAMC,UAA8B,GAAG,IAAI;AAACN,OAAA,CAAAM,UAAA,GAAAA,UAAA;AAE5C,MAAMC,WAA+B,GAAG,IAAI;AAACP,OAAA,CAAAO,WAAA,GAAAA,WAAA;AAE7C,MAAMC,GAAuB,GAAG,CAAC,qBAAqB,CAAC;AAACR,OAAA,CAAAQ,GAAA,GAAAA,GAAA;AAExD,MAAMC,IAAwB,GAAG,IAAI;AAACT,OAAA,CAAAS,IAAA,GAAAA,IAAA;AAEtC,MAAMC,SAA6B,GAAG,IAAI;AAACV,OAAA,CAAAU,SAAA,GAAAA,SAAA;AAE3C,MAAMC,IAAwB,GAAG,IAAI;AAACX,OAAA,CAAAW,IAAA,GAAAA,IAAA;AAEtC,MAAMC,IAAwB,GAAG,CACtC,MAAM,EACN,mBAAmB,EACnB,mBAAmB,EACnB,iBAAiB,CACT;AAACZ,OAAA,CAAAY,IAAA,GAAAA,IAAA;AAGJ,MAAMC,YAAgC,GAAG,CAAC,aAAa,CAAU;AAACb,OAAA,CAAAa,YAAA,GAAAA,YAAA;AAElE,MAAMC,cAAkC,GAAG,CAAC,aAAa,CAAU;AAACd,OAAA,CAAAc,cAAA,GAAAA,cAAA;AAEpE,MAAMC,oBAAwC,GAAG,CACtD,sBAAsB,CACd;AAACf,OAAA,CAAAe,oBAAA,GAAAA,oBAAA;AAEJ,MAAMC,4BAAgD,GAAG,CAC9D,6BAA6B,CACrB;AAAChB,OAAA,CAAAgB,4BAAA,GAAAA,4BAAA;AAEJ,MAAMC,iBAAqC,GAAG,CACnD,gBAAgB,CACR;AAACjB,OAAA,CAAAiB,iBAAA,GAAAA,iBAAA"}