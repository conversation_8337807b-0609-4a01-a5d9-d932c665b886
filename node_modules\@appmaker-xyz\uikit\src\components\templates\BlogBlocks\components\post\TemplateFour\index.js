/**
 * Post list Template View for inAppView
 */
import PropTypes from 'prop-types';
import React from 'react';
import { View, Image, Text, TouchableOpacity } from 'react-native';
// import AppTouchable from '../../../common/AppTouchable';
import styles from './styles';
// import { shadeColor } from '../../../common/Utils';
import { AppmakerText } from '../../../../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { color } from '@appmaker-xyz/uikit/src/styles/index';

// const userImage = require('../../../../image/user-grey.png');

/**
 * Template one for Post list item
 * @param { title, data, onClick } props.
 */

const TemplateFour = ({ attributes, onAction, data }) => {
  const item = data;
  const titleStyle = attributes.styles ? attributes.styles.titleStyle : {};

  const handlePress = () => onAction(attributes.appmakerAction);
  return (
    <TouchableOpacity onPress={handlePress}>
      <View style={styles.rootContainer}>
        <View style={styles.container}>
          {/* {item.image && <Image style={styles.image}
          source={{ uri: item.image }}
        />} */}
          <View style={styles.textContainer}>
            {item.category !== undefined && (
              <AppmakerText category="highlighter2">
                {item.category}
              </AppmakerText>
            )}
            <AppmakerText
              category="h1Heading"
              style={[styles.title, titleStyle]}
              numberOfLines={2}>
              {item.title}
            </AppmakerText>
            {item.author && (
              <View style={styles.authorContainer}>
                <Icon
                  name="user"
                  style={styles.userIcon}
                  size={12}
                  color={color.demiDark}
                />
                <AppmakerText style={styles.authorText} category="highlighter2">
                  {item.author}
                </AppmakerText>
              </View>
            )}
          </View>
          {item.image && (
            <Image style={styles.image} source={{ uri: item.image }} />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};
TemplateFour.propTypes = {
  data: PropTypes.any.isRequired,
  title: PropTypes.string,
  onClick: PropTypes.func,
};

TemplateFour.defaultProps = {};

export default TemplateFour;
