{"version": 3, "names": ["_t", "require", "createFlowUnionType", "createTSUnionType", "createUnionTypeAnnotation", "isFlowType", "isTSType", "createUnionType", "types"], "sources": ["../../../src/path/inference/util.ts"], "sourcesContent": ["import {\n  createFlowUnionType,\n  createTSUnionType,\n  createUnionTypeAnnotation,\n  isFlowType,\n  isTSType,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nexport function createUnionType(\n  types: Array<t.FlowType | t.TSType>,\n): t.FlowType | t.TSType {\n  if (process.env.BABEL_8_BREAKING) {\n    if (isFlowType(types[0])) {\n      return createFlowUnionType(types as t.FlowType[]);\n    }\n    if (isTSType(types[0])) {\n      return createTSUnionType(types as t.TSType[]);\n    }\n  } else {\n    if (isFlowType(types[0])) {\n      if (createFlowUnionType) {\n        return createFlowUnionType(types as t.FlowType[]);\n      }\n\n      return createUnionTypeAnnotation(types as t.FlowType[]);\n    } else {\n      if (createTSUnionType) {\n        return createTSUnionType(types as t.TSType[]);\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAMsB;EALpBC,mBAAmB;EACnBC,iBAAiB;EACjBC,yBAAyB;EACzBC,UAAU;EACVC;AAAQ,IAAAN,EAAA;AAIH,SAASO,eAAeA,CAC7BC,KAAmC,EACZ;EAQhB;IACL,IAAIH,UAAU,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,IAAIN,mBAAmB,EAAE;QACvB,OAAOA,mBAAmB,CAACM,KAAK,CAAiB;MACnD;MAEA,OAAOJ,yBAAyB,CAACI,KAAK,CAAiB;IACzD,CAAC,MAAM;MACL,IAAIL,iBAAiB,EAAE;QACrB,OAAOA,iBAAiB,CAACK,KAAK,CAAe;MAC/C;IACF;EACF;AACF"}