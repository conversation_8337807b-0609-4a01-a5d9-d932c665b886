import { StyleSheet } from 'react-native';
import { AppmakerRemoteImage } from '@appmaker-xyz/react-native';
import { ThemeText, Layout } from '@appmaker-xyz/ui';
import React from 'react';

const SuccessHeader = (props) => {
  const logoSrc = props?.attributes?.logo;
  return (
    <Layout style={styles.container}>
      <AppmakerRemoteImage name="APP_TITLE_IMAGE" style={styles.image} />
    </Layout>
  );
};

export default SuccessHeader;

const styles = StyleSheet.create({
  container: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.25);',
  },
  image: {},
});
