import React from 'react';
import striptags from 'striptags';
import { Text, Dimensions, Linking, I18nManager } from 'react-native';
import { useApThemeState } from '../../theme/ThemeContext';
import HTMLview from 'react-native-render-html';
import unescape from 'unescape';
import { testProps } from '@appmaker-xyz/core';
import { useTranslation } from 'react-i18next';
import iframe from '@native-html/iframe-plugin';
import WebView from 'react-native-webview';
const { width } = Dimensions.get('window');
const Entities = require('html-entities').XmlEntities;

const entities = new Entities();
const AppmakerText = ({
  category = 'bodyParagraph',
  children,
  status,
  style,
  fontColor,
  hideText,
  numberOfLines,
  html,
  clientId,
  testId,
  allowFontScaling = false,
  ...props
}) => {
  const { t } = useTranslation();

  const styles = useApThemeState();
  const fontStyle = styles.fonts[category];
  if (!fontColor) {
    if (status && styles.color[status]) {
      fontStyle.color = styles.color[status];
    } else {
      fontStyle.color = styles.color.dark;
    }
  } else {
    fontStyle.color = fontColor;
  }
  if (hideText) {
    return null;
  }
  if (html) {
    const imageWidth = width - 25;
    const alterNode = (node) => {
      const { name, parent } = node;
      if (node.name == 'iframe' && node.attribs.src.match(/youtube\.com/i)) {
        // this is used for youtube video running in correct size. youtube video was distorted in the previous version.
        delete node.attribs?.width;
        delete node.attribs?.height;

        // fix for: when parent div has max-width: 100% iframe not showing full width
        if (parent?.name === 'div') {
          delete parent.attribs.style;
        }
        return node;
      }
      // If the node width is larger than the screen width then convert it to fit the screen
      if (
        node?.attribs &&
        node.attribs?.width &&
        node.attribs.width > imageWidth
      ) {
        const imageHeight =
          imageWidth / (node.attribs.width / node.attribs.height);
        node.attribs = {
          ...(node.attribs || {}),
          width: imageWidth,
          height: imageHeight,
        };
        return node;
      }
    };
    const renderers = {
      iframe,
    };
    /**
     * Workaround fix
     * Issue:
     * On some devices pdp description with iframe is causing crash when navigated back;
     * react-native-render-html/issues/393#issuecomment-**********
     * appmaker-react-native-app/issues/2027
     */
    const tagsStyles = {
      iframe: {
        opacity: 0.99,
      },
    };
    // let htm = `<p>Developed by veterinarians, FLAIR Equine Nasal Strips are drug-free, self-adhesive nasal strips that promote optimum health of equine athletes, in all disciplines and every level of competition.</p> <ul> <li style="list-style-type: none;"> <ul> <li><span style="float: left; width: 160px; max-height: 12px;">BREATH EASIER</span> <span style="margin-right: 20px;">-</span> The spring-like action in FLAIR Strips supports the soft tissues over the nasal passages to reduce the tissue collapse that occurs in all horses during exercise.</li> <li><span style="float: left; width: 160px; max-height: 12px;">REDUCE FATIGUE</span> <span style="margin-right: 20px;">-</span> Horses don’t tire as quickly so there’s more energy available for tomorrow.</li> <li><span style="float: left; width: 160px; max-height: 12px;">CONSERVE ENERGY</span> <span style="margin-right: 20px;">-</span> Horses wearing have been shown to use 5-7% less energy during intensive exercise.</li> <li><span style="float: left; width: 160px; max-height: 12px;">RECOVER FASTER</span> <span style="margin-right: 20px;">-</span> Horses cool out more quickly after intensive exercise.</li> </ul> </li> </ul> <p><em><br /> Sold individually or in packs of 6.</em></p> <div style="width: 500px; max-width: 100%;"><iframe src="https://www.youtube.com/embed/jz3CNx8ITCM" width="1000" height="500"  allowfullscreen="allowfullscreen"></iframe></div>`
    return (
      <HTMLview
        {...testProps(clientId)}
        renderers={renderers}
        WebView={WebView}
        tagsStyles={tagsStyles}
        imagesMaxWidth={imageWidth}
        source={{ html: children ? children : ' ' }}
        alterNode={alterNode}
        baseFontStyle={{ fontFamily: styles.font.family.regular }}
        ignoredStyles={[
          'font-family',
          'letter-spacing',
          'position',
          'margin',
          'textTransform',
        ]}
        onLinkPress={(url, href) => {
          Linking.openURL(href);
        }}
      />
    );
  }
  // const finalChildren = props.unescape ? he.unescape(children) : children;
  // const finalChildren = entities.decode(children);
  // let finalChildren = t(children);
  let finalChildren = children;
  if (Array.isArray(children)) {
    finalChildren = [];
    children.map((value, index) => {
      if (typeof value === 'string') {
        finalChildren.push(`${t(value.trim())} `);
      } else {
        finalChildren.push(value);
      }
    });
  }
  if (typeof finalChildren === 'string') {
    if (finalChildren.includes(':')) {
      try {
        finalChildren = entities.decode(children);
      } catch (error) {
        console.log(error, 'Error');
      }
    } else {
      try {
        finalChildren = t(entities.decode(children));
      } catch (error) {
        console.log(error, 'Error');
      }
    }
  }
  return (
    <Text
      {...testProps(testId || clientId)}
      style={{ ...fontStyle, ...style }}
      numberOfLines={numberOfLines}
      allowFontScaling={allowFontScaling}
      {...props}>
      {(props.striptags === undefined || props.striptags) &&
      typeof finalChildren === 'string'
        ? striptags(finalChildren)
        : finalChildren}
    </Text>
  );
};

export default AppmakerText;
