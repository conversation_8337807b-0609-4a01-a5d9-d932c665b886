import React from 'react';
import ChumakInsider from './ChumakInsider';
import ChumbakInsiderCart from './ChumbakInsiderCart';

const ChumbakInsider = ({ attributes = {}, ...props }) => {
  if (attributes.type === 'cart_popup') {
    return <ChumbakInsiderCart attributes={{ ...attributes }} {...props} />;
  }
  return <ChumakInsider attributes={{ ...attributes }} />;
};

export default ChumbakInsider;
