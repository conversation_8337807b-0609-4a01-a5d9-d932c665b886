
import { StyleSheet, Dimensions } from 'react-native';

const { width } = Dimensions.get('window');

// Right and left margin for container. so total margin will be 20
// 0.54=195/360 , 195 is the height for 360 width

const styles = StyleSheet.create({
  rootContainer: {
    minHeight: 195,
    alignContent: 'center',
    justifyContent: 'center',
  },
  container: {
    backgroundColor: '#fff',
    alignContent: 'center',
    alignItems: 'center',
    marginHorizontal: 0,
    marginVertical: 0,
    flexDirection: 'row',
    height: width * 0.54,
  },
  image: {
    backgroundColor: 'black',
    height: width * 0.54,
    width,
    paddingLeft: 16,
    paddingBottom: 24,
    paddingRight: 21,
    borderRadius: 0,
    justifyContent: 'flex-end',
    marginBottom: 0,
    marginRight: 0,
  },
  title: {
    color: '#fff',
    fontFamily: 'Lato-Bold',
    fontSize: 16,
  },
  description: {
    color: '#99999c',
    fontSize: 13,
    marginTop: 5,
  },
  author: {
    fontStyle: 'italic',
    color: '#000000',
    marginTop: 5,
    fontSize: 15,
    alignItems: 'flex-end',
  },
  descriptionNoImage: {
    color: '#99999c',
    fontSize: 15,
    fontFamily: 'Lato-Regular',
    marginTop: 10,
  },
  dummyImage: {
    fontSize: 96,
    color: '#707070',
    fontFamily: 'NotoSans',
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  },
  authorContainer: {
    flexDirection: 'row',
    zIndex: 2,
    marginBottom: 10,
  },
  authorImage: {
    height: 9,
    width: 9,
    marginRight: 3,
  },
  authorText: {
    fontSize: 9,
    color: '#ffffff',
    fontFamily: 'NotoSans',
  },
});

export default styles;
