import React from 'react';
import { StyleSheet } from 'react-native';
import { ThemeText, Layout, AppTouchable, AppImage } from '@appmaker-xyz/ui';
import { useOrderLineItem } from '@appmaker-xyz/shopify';

const OrderDetailItemCard = (props) => {
  const {
    featureImg,
    title,
    lineItemPrice,
    quantity,
    vendor,
    variantTitle,
    openProduct,
  } = useOrderLineItem(props);
  return (
    <Layout style={styles.lineItem}>
      <AppTouchable onPress={openProduct}>
        <AppImage uri={featureImg} style={styles.variantImage} />
      </AppTouchable>
      <Layout style={styles.itemContent}>
        <ThemeText size="md" fontFamily="medium">
          {title}
        </ThemeText>
        <Layout style={styles.itemDetails}>
          <Layout style={styles.badgesContainer}>
            {variantTitle ? (
              <ThemeText style={styles.variationText} size="sm">
                {variantTitle}
              </ThemeText>
            ) : null}
          </Layout>
          <Layout style={styles.priceContainer}>
            <ThemeText fontFamily="bold">
              {quantity} x {lineItemPrice}
            </ThemeText>
          </Layout>
        </Layout>
      </Layout>
    </Layout>
  );
};

const styles = StyleSheet.create({
  lineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
    backgroundColor: '#FFFFFF',
    padding: 8,
    marginHorizontal: 12,
    flex: 1,
    borderRadius: 4,
  },
  variantImage: {
    width: 57,
    height: 72,
    marginRight: 8,
    backgroundColor: '#E5E7EB',
  },
  itemContent: {
    flexGrow: 1,
    flexShrink: 1,
  },
  itemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 2,
    flexShrink: 1,
  },
  badgesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    flex: 1,
  },
  variationText: {
    backgroundColor: '#E9ECF3',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    margin: 1,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default OrderDetailItemCard;
