import React, { useState, useEffect } from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '../../../../theme/ThemeContext';
import { Layout, ImageSwiper, LayoutIcon } from '../../..';

const ZoomableImage = ({ attributes, data, onAction, ...props }) => {
  const { imageList, index } = attributes;
  const { color, spacing, font } = useApThemeState();
  const styles = allStyles({ color, spacing });
  const [saved, setSaved] = useState(attributes.saved);
  const checkSaved = async () => {
    if (data) {
      const isSaved = await onAction({
        action: 'CHECK_ITEM_SAVED',
        params: { id: data.id },
      });
      // console.log(saved);
      setSaved(isSaved);
    }
  };
  useEffect(() => {
    checkSaved();
  }, [data]);

  return (
    <Layout style={styles.productImageContainer}>
      <Layout style={styles.overlayTop}>
        <LayoutIcon attributes={{ iconName: 'x', overlay: true }} />
      </Layout>
      <ImageSwiper
        attributes={{
          index,
          imageList: imageList || [],
          autoplay: false,
          showsButtons: true,
        }}
      />
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    productImageContainer: {
      flex: 1,
      width: '100%',
      backgroundColor: `${color.dark}CC`,
      position: 'relative',
    },
    overlayTop: {
      position: 'absolute',
      bottom: spacing.base,
      right: spacing.base,
    },
  });

export default ZoomableImage;
