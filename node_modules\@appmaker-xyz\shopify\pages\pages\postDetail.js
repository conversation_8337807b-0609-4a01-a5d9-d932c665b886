import ICONS from '../../ToolBarIcons';

const pagesData = {
  title: ' ',
  blocks: [
    {
      clientId: 'my-account-menu',
      name: 'appmaker/post-header',
      attributes: {
        id: '{{blockItem.node.id}}',
        containerStyle: {
          flex: 1,
        },
        title: '{{blockItem.node.title}}',
        timeStamp: "{{momentJS(blockItem.node.publishedAt,'MMM Do YY')}}",
        authorName: '{{blockItem.node.author.name}}',
        featureImg:
          '<% if(blockItem.node.image){echo(blockItem.node.image.src)}else{echo(null)} %>',
        excerpt: '{{blockItem.node.excerptHtml}}',
        contentHtml: '{{blockItem.node.contentHtml}}',
        blockItem: '{{blockItem}}',
        appmakerAction: {
          params: {},
          action: 'NO_ACTION',
        },
      },
    },
    {
      name: 'appmaker/expandable-text-block',
      clientId: 'widget-description',
      attributes: {
        content: '{{blockItem.node.contentHtml}}',
        accessButton: false,
        expanded: true,
        expandable: false,
      },
    },
  ],
  _id: 'postList',
  uid: 'odRhv94hf4S52SysvvNoxPyRx682',
  dataSource: {},
  toolBarItems: [ICONS.SEARCH, ICONS.WISHLIST, ICONS.CART],
};
export default pagesData;
