import { Platform } from 'react-native';

const MyAccount = {
  blocks: [
    {
      name: 'appmaker/blocksView',
      attributes: {
        removeClippedSubviews: false,
        insideKeyboardAvoidingView: Platform.OS === 'ios',
        keyboardVerticalOffset: 80,
        // renderType: 'normal',
        rootContainerStyle: {
          flex: 5,
        },
        contentContainerStyle: {
          flex: 1,
          justifyContent: 'center',
          margin: 10,
        },
      },
      innerBlocks: [
        {
          clientId: 'my-account-menu-orders',
          name: 'appmaker/actionbar',
          attributes: {
            title: 'Activate Developer Mode',
            appmakerAction: {
              params: {},
              action: 'ACTIVATE_DEVELOPER_MODE',
            },
          },
        },
        {
          clientId: 'my-account-menu-orders',
          name: 'appmaker/actionbar',
          attributes: {
            title: 'Subscribe to Test Push Channel',
            appmakerAction: {
              params: {},
              action: 'SUBSCRIBE_TO_TEST_PUSH_CHANNEL',
            },
          },
        },
        {
          clientId: 'my-account-menu-orders',
          name: 'appmaker/actionbar',
          attributes: {
            title: 'Copy App logs',
            appmakerAction: {
              params: {},
              action: 'COPY_APPMAKER_LOGS',
            },
          },
        },
        {
          clientId: 'my-account-menu-orders',
          name: 'appmaker/actionbar',
          attributes: {
            title: 'Copy App Storage data',
            appmakerAction: {
              params: {},
              action: 'COPY_APP_STORAGE_DATA',
            },
          },
        },
        {
          clientId: 'my-account-menu-orders',
          name: 'appmaker/actionbar',
          attributes: {
            title: 'Copy User Data',
            appmakerAction: {
              params: {},
              action: 'COPY_USER_DATA',
            },
          },
        },
        {
          clientId: 'my-account-menu-orders',
          name: 'appmaker/actionbar',
          attributes: {
            title: 'Copy Cart Data',
            appmakerAction: {
              params: {},
              action: 'COPY_CART_DATA',
            },
          },
        },
        {
          clientId: 'my-account-menu-orders',
          name: 'appmaker/actionbar',
          attributes: {
            fontColor: '#ff0000',
            title: 'Clear App logs',
            appmakerAction: {
              params: {},
              action: 'CLEAR_APPMAKER_LOGS',
            },
          },
        },
        {
          clientId: 'my-account-menu-orders',
          name: 'appmaker/actionbar',
          attributes: {
            fontColor: '#ff0000',
            title: 'Deactivate Developer Mode',
            appmakerAction: {
              params: {},
              action: 'DEACTIVATE_DEVELOPER_MODE',
            },
          },
        },
        {
          clientId: 'my-account-menu-orders',
          name: 'appmaker/actionbar',
          attributes: {
            title: 'Copy checkout and login',
            appmakerAction: {
              params: {},
              action: 'COPY_CHECKOUT_AND_LOGIN',
            },
          },
        },
        {
          name: 'appmaker/input',
          clientId: 'checkoutIDInput',
          attributes: {
            label: 'Enter the checkout and login code',
            leftIcon: 'shopping-cart',
            name: 'checkoutUserId',
            status: 'demiDark',
            __appmakerStylesClassName: 'inputCustom',
          },
        },
        {
          name: 'appmaker/ActionButton',
          clientId: 'submit-login',
          attributes: {
            appmakerAction: {
              action: 'SET_CHECKOUT_AND_LOGIN',
              params: '{{pageState}}',
            },
            content: 'Set Checkout and login',
            baseSize: true,
          },
          dependencies: {
            pageState: ['username', 'password'],
          },
          contextValues: true,
        },
      ],
    },
  ],
  type: 'normal',
  attributes: {
    rootContainerStyle: {
      flex: 1,
      backgroundColor: '#FFFFFF',
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  title: 'Developer Options',
};
export default MyAccount;
