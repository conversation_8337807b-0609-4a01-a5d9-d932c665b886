import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { AppTouchable, Layout, ThemeText } from '../../uikit/index';

function Stepper({}) {
  const [quantity, setQuantity] = useState(1);
  const increment = () => {
    setQuantity(quantity + 1);
  };
  const decrement = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };
  return (
    <Layout style={styles.stepperContainer}>
      <AppTouchable onPress={decrement} style={styles.button}>
        <Icon name="minus" size={16} color="#111827" />
      </AppTouchable>
      <ThemeText size="md" color="#111827" style={styles.quantityText}>
        {quantity}
      </ThemeText>
      <AppTouchable onPress={increment} style={styles.button}>
        <Icon name="plus" size={16} color="#111827" />
      </AppTouchable>
    </Layout>
  );
}

const ProductCounter = () => {
  return (
    <Layout>
      <ThemeText>Quantity</ThemeText>
      <Stepper />
    </Layout>
  );
};

const styles = StyleSheet.create({
  stepperContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderColor: '#111827',
    borderWidth: 1,
  },
  button: {
    padding: 8,
    backgroundColor: '#fff',
  },
});

export default ProductCounter;
