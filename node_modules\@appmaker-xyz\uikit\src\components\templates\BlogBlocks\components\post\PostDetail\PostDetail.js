import PropTypes from 'prop-types';
import React, {PureComponent} from 'react';
import {View, Platform} from 'react-native';
// import { WebView } from "react-native-webview";
import WebViewAuto from './WebViewAuto';
// // import { WebView as WebViewAuto } from "react-native-webview";
// import Comment from '../Comment';
// import AddComment from '../Comment/AddComment';
import AuthorView from './AuthorView/AuthorView';
// import {
//   GetUrlExtension,
//   OpenUrlInBrowser,
//   sharePost
// } from '../../misc/HelperFunctions';
// import CommentAddButton from '../Comment/Button';
import styles from './Styles';
// import ShareButton from '../ShareButton';
// TODO cleanup needed
// import * as URLHandler from "../../misc/URLHelper";
// import ActionHandler from "../../misc/ActionHandler";

/**
 * ## PostDetail
 * Shows detail of a single post using webview
 */
const PostDetail = ({attributes, dataSource}) => {
  const {data} = attributes;
  console.log('data - ', data);
  // constructor() {
  //   super();
  //   this.state = {
  //     isWebViewLoaded: false,
  //     showAddComment: false,
  //     isLoading: true
  //   };
  //   // this.onShouldStartLoadWithRequest = this.onShouldStartLoadWithRequest.bind(
  //   //   this
  //   // );
  //   // this.setModalVisible = this.setModalVisible.bind(this);
  // }

  const onWebViewLoadEnd = () => {
    // if (typeof this.props.onWebViewLoadEnd === 'function')
    //   this.props.onWebViewLoadEnd();
    // this.setState({ isWebViewLoaded: true });
  };

  /**
   * Comment Add Modal Visibility Switcher
   * @param {*} visible
   */
  const setModalVisible = visible => {
    // this.setState({ showAddComment: visible });
  };

  /** Author Action */
  const onPressAuthor = () => {
    // ActionHandler(this.props.data.data.author_action, this.props.navigation);
  };

  /**
   * Checks if url has a pdf extension
   * and sends the url to browser to download this file
   *
   * Also any url that fails our condition is send to a webview component
   */
  // onShouldStartLoadWithRequest = e => {
  //   // console.log("onShouldStartLoadWithRequest -", e);
  //   if (GetUrlExtension(e.url) === '.pdf') {
  //     OpenUrlInBrowser(e.url);
  //     return false;
  //   }

  //   /**
  //    * Open new hyperlink new webview with full heights
  //    */
  //   if (
  //     this.props.navigation &&
  //     URLHandler.is_iri(e.url) &&
  //     !e.url.includes('appmaker-wp/v1/inAppPages/wp/posts') &&
  //     URLHandler.is_iri(e.url) !== 'about:blank' &&
  //     URLHandler.is_iri(e.url) !== 'about:srcdoc'
  //   ) {
  //     const { navigation } = this.props;
  //     if (
  //       Platform.OS === 'ios' &&
  //       e.navigationType &&
  //       e.navigationType === 'other'
  //     ) {
  //       return false;
  //     }
  //     navigation.navigate('WebViewComponent', {
  //       data: {
  //         source: {
  //           uri: e.url
  //         }
  //       }
  //     });
  //     return false;
  //   }
  //   return true;
  // };

  console.log(' ************  rendering post detail = ', attributes, data);
  // const modalWidth = this.props.parentWidth - 30;
  // const { data } = this.props.data;
  const source = data.content
    ? data.content?.source?.source_type === 'url'
      ? {uri: data.content.source.url}
      : {html: data.content.source.html}
    : {};

  console.log('Post detail source - ', source);
  return (
    <View style={{flex: 1}}>
      <AuthorView
        image={data.image}
        author={data.author}
        onPress={onPressAuthor}
        title={data.title}
        category={data.category}
        authorImage={data.author_image}
      />

      <View>
        <WebViewAuto
          style={{flex: 1, height: 1000}}
          source={source}
          // startInLoadingState={true}
          defaultHeight={3000}
          onLoadEnd={this.onWebViewLoadEnd}
          onShouldStartLoadWithRequest={this.onShouldStartLoadWithRequest}
          // onNavigationStateChange={this.onShouldStartLoadWithRequest}
        />
      </View>
      {/* Show Add Comment button and reviews
        only after Post Detail webview is fully loaded */}
      {/* {this.state.isWebViewLoaded && (
          <View>
            {data.show_comment && data.comments && (
              <View>
                <Comment comments={data.comments} />
              </View>
            )}
            <View style={{ flex: 1, flexDirection: 'row' }}>
              {data.show_add_comment && (
                <CommentAddButton
                  containerStyle={styles.commentBtn}
                  onPress={() => this.setModalVisible(true)}
                >
                  Add Comment
                </CommentAddButton>
              )}
              {data.share && (
                <ShareButton
                  onPress={() => sharePost(data.share.url, data.share.message)}
                />
              )}
            </View>
          </View>
        )} */}
      <View style={styles.modalContainer}>
        {/* If Network response has show comment only then enable modal */}
        {/* {data.show_add_comment && (
            <AddComment
              visible={this.state.showAddComment}
              modalWidth={modalWidth}
              data={data.add_comment}
              modalCallback={this.setModalVisible}
            />
          )} */}
      </View>
    </View>
  );
};

PostDetail.propTypes = {
  /**
   * @param data
   * data of comment including comment submit url
   */
  data: PropTypes.object,
  /**
   * @param parentWidth
   * Width of Parent component
   */
  parentWidth: PropTypes.number,

  /**
   * @param onWebViewLoadEnd
   * Callback to notify when webview finishes loading
   */
  onWebViewLoadEnd: PropTypes.func,

  /**
   * Navigation props received from contentSDK
   */
  navigation: {
    navigate: PropTypes.func,
  },
};

export default PostDetail;
