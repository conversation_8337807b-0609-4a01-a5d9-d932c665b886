import { spacing } from '@appmaker-xyz/uikit/src/styles/index';

const FormList = {
  id: 'FormList',
  status: 'active',
  title: 'FormList',
  attributes: {
    renderType: 'normal',
    contentContainerStyle: { flex: 1 },
    rootContainerStyle: { flex: 1 },
  },
  blocks: [
    {
      name: 'appmaker/layout',
      attributes: {
        style: {
          paddingHorizontal: spacing.base,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/floating-label-input',
          attributes: {
            label: 'Mobile Number',
            name: 'phone',
            status: 'grey',
            caption: 'In case we need to contact you about your order',
            type: 'number',
            defaultValue: '9995556560',
          },
        },
        {
          name: 'appmaker/text',
          attributes: {
            content: '{{pageState._formData.phone}}',
          },
        },
      ],
    },
  ],
};
export default FormList;
