import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, I18nManager } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { Layout, ThemeText, AppTouchable, AppImage } from '../atoms';
import { Badge } from '../coreComponents';
import { testProps } from '@appmaker-xyz/core';
import { emitEvent, analytics } from '@appmaker-xyz/core';

const DrawerItem = ({
  clientId,
  title,
  featureImg,
  appmakerAction,
  type,
  nodes,
  badge,
  badgeText = 'Badge',
  badgeColor = '#212121',
  badgeTextColor = '#ffffff',
  dotIndicator,
  dotIndicatorColor = '#BC002D',
  dotSize = 10,
  fontColor = '#212121',
  onPressHandle,
}) => {
  return (
    <AppTouchable
      onPress={() => {
        if (appmakerAction?.type === 'OPEN_COLLECTION' && !nodes) {
          emitEvent('drawerCategoryClick', {
            title,
            action: 'OPEN_COLLECTION',
          });
          analytics.track('drawerCategoryClick', {
            title,
            action: 'OPEN_COLLECTION',
          });
        }
        // emitEvent('drawerMenuClick', {});
        onPressHandle(appmakerAction, nodes, title, type);
      }}
      clientId={clientId}>
      <View style={[styles.container, type === 'title' && styles.topBorder]}>
        <Layout style={styles.titleContainer}>
          {featureImg ? (
            <Layout style={styles.leftImage}>
              <AppImage
                uri={featureImg}
                style={styles.image}
                resizeMode="contain"
              />
            </Layout>
          ) : null}
          <Layout style={styles.titleContainer}>
            <ThemeText
              {...testProps(`${clientId}-title`)}
              fontFamily="medium"
              color={type === 'title' ? fontColor : '#1B1B1B'}
              numberOfLines={1}
              style={styles.drawerItemText}>
              {title}
            </ThemeText>
            {badge ? (
              <Badge
                text={badgeText}
                customStyles={{
                  containerStyle: {
                    backgroundColor: badgeColor,
                    marginLeft: 4,
                  },
                  textStyle: {
                    color: badgeTextColor,
                  },
                }}
              />
            ) : null}
            {dotIndicator ? (
              <Layout
                style={[
                  styles.dotIndicator,
                  {
                    width: dotSize,
                    height: dotSize,
                    backgroundColor: dotIndicatorColor,
                  },
                ]}
              />
            ) : null}
          </Layout>
        </Layout>
        {type !== 'title' && nodes?.length !== 0 && (
          <Icon
            name={`chevron-${I18nManager.isRTL ? 'left' : 'right'}`}
            size={18}
            color="#212121"
          />
        )}
      </View>
    </AppTouchable>
  );
};

const ListHeader = ({ stack, setContents }) => {
  if (stack[stack.length - 1].title) {
    return (
      <AppTouchable
        onPress={() => {
          stack.pop();
          setContents(stack[stack.length - 1].nodes);
        }}>
        <Layout style={[styles.titleContainer, styles.goBackStyle]}>
          <Icon
            name="arrow-left"
            size={18}
            color="#212121"
            style={styles.arrowLeftIcon}
          />
          <ThemeText
            color="#4F4F4F"
            size="md"
            numberOfLines={1}
            style={styles.menuItemText}>
            {stack[stack.length - 1].title}
          </ThemeText>
        </Layout>
      </AppTouchable>
    );
  } else {
    return <Layout />;
  }
};

const MenuItem = ({ attributes, onPress, onAction, clientId }) => {
  let dataSet = {};
  let { items } = attributes;
  if (attributes?.items?.blocks) {
    dataSet = attributes.items.blocks;
    items = dataSet
      ? dataSet[0]?.data
        ? dataSet[0]?.data
        : dataSet
      : undefined;
  }
  const [contents, setContents] = useState(items);
  const [stack, setStack] = useState([]); // stack structure used for the navigation
  const [loading, setLoading] = useState(true);
  const resetItems = () => {
    // for reseting navigation of menu items to initial.
    setContents(items);
    setStack([{ nodes: items, title: '' }]);
  };
  useEffect(() => {
    if (items) {
      setLoading(false);
      resetItems();
    } // used because there is a slight delay in loading data from props. ie, items is undefined at first
  }, [items]);
  const onPressHandle = (action, nodes, title, type) => {
    if (nodes && nodes.length > 0) {
      stack.push({ nodes, title });
      setContents(stack[stack.length - 1].nodes);
    } else if (type !== 'title') {
      if (!action?.params?.title || !action?.params?.label) {
        action.params.title = title;
        action.params.label = title;
      }
      if (action.type === 'OPEN_IN_APP_PAGE' && action.params.id === 'home') {
        onAction({ action: 'OPEN_HOME', params: { replacePage: true } });
      } else {
        onAction(action);
      }
      onAction({ action: 'TOGGLE_DRAWER' });
      resetItems();
    }
  };

  if (loading) {
    return <Layout loading={true} />;
  }
  return (
    <FlatList
      data={contents}
      ListHeaderComponent={() => (
        <ListHeader setContents={setContents} stack={stack} />
      )}
      keyExtractor={(item, index) => index.toString()}
      renderItem={({ item, key }) => (
        <DrawerItem
          key={key}
          clientId={clientId}
          title={item.title}
          featureImg={item.icon}
          badge={item.badge}
          badgeColor={item.badgeColor}
          badgeText={item.badgeText}
          badgeTextColor={item.badgeTextColor}
          fontColor={item.fontColor}
          dotIndicator={item.dotIndicator}
          dotIndicatorColor={item.dotIndicatorColor}
          dotSize={item.dotSize}
          nodes={item.nodes}
          appmakerAction={item.action}
          type={item.type}
          onPressHandle={item.type == 'title' ? () => {} : onPressHandle}
        />
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 8,
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    marginBottom: 4,
    flex: 1,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  leftImage: {
    width: 30,
    height: 30,
    marginRight: 12,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  topBorder: {
    borderTopColor: '#E5E5E5',
    borderTopWidth: 1,
  },
  goBackStyle: {
    paddingVertical: 12,
    marginTop: 0.5,
    marginBottom: 4,
  },
  dotIndicator: {
    borderRadius: 20,
    marginLeft: 6,
    overflow: 'hidden',
  },
  arrowLeftIcon: {
    paddingHorizontal: 8,
  },
  menuItemText: {
    flexShrink: 1,
    flexWrap: 'wrap',
  },
  drawerItemText: {
    flexWrap: 'wrap',
  },
});

export default MenuItem;
