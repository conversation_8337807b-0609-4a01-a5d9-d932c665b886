{"version": 3, "names": ["_async", "require", "_path", "data", "_url", "_semver", "_debug", "_rewriteStackTrace", "_configError", "_transformFile", "asyncGeneratorStep", "gen", "resolve", "reject", "_next", "_throw", "key", "arg", "info", "value", "error", "done", "Promise", "then", "_asyncToGenerator", "fn", "self", "args", "arguments", "apply", "err", "undefined", "debug", "buildDebug", "import_", "_unused", "supportsESM", "semver", "satisfies", "process", "versions", "node", "exports", "loadCodeDefault", "filepath", "asyncError", "path", "extname", "loadCjsDefault", "loadCtsDefault", "e", "code", "isAsync", "waitFor", "loadMjsDefault", "ConfigError", "ext", "hasTsSupport", "extensions", "handler", "opts", "babelrc", "configFile", "sourceType", "sourceMaps", "sourceFileName", "basename", "presets", "getTSPreset", "Object", "assign", "onlyRemoveTypeImports", "optimizeConstEnums", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m", "filename", "endsWith", "_compile", "transformFileSync", "packageJson", "lt", "version", "console", "LOADING_CJS_FILES", "Set", "has", "module", "add", "endHiddenCallStack", "delete", "_module", "__esModule", "default", "_x", "_loadMjsDefault", "pathToFileURL", "message", "pnp"], "sources": ["../../../src/config/files/module-types.ts"], "sourcesContent": ["import { isAsync, waitFor } from \"../../gensync-utils/async.ts\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport path from \"path\";\nimport { pathToFileURL } from \"url\";\nimport { createRequire } from \"module\";\nimport semver from \"semver\";\nimport buildDebug from \"debug\";\n\nimport { endHiddenCallStack } from \"../../errors/rewrite-stack-trace.ts\";\nimport ConfigError from \"../../errors/config-error.ts\";\n\nimport type { InputOptions } from \"../index.ts\";\nimport { transformFileSync } from \"../../transform-file.ts\";\n\nconst debug = buildDebug(\"babel:config:loading:files:module-types\");\n\nconst require = createRequire(import.meta.url);\n\nlet import_: ((specifier: string | URL) => any) | undefined;\ntry {\n  // Old Node.js versions don't support import() syntax.\n  import_ = require(\"./import.cjs\");\n} catch {}\n\nexport const supportsESM = semver.satisfies(\n  process.versions.node,\n  // older versions, starting from 10, support the dynamic\n  // import syntax but always return a rejected promise.\n  \"^12.17 || >=13.2\",\n);\n\nexport default function* loadCodeDefault(\n  filepath: string,\n  asyncError: string,\n): Handler<unknown> {\n  switch (path.extname(filepath)) {\n    case \".cjs\":\n      if (process.env.BABEL_8_BREAKING) {\n        return loadCjsDefault(filepath);\n      } else {\n        return loadCjsDefault(\n          filepath,\n          // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n          /* fallbackToTranspiledModule */ arguments[2],\n        );\n      }\n    case \".mjs\":\n      break;\n    case \".cts\":\n      return loadCtsDefault(filepath);\n    default:\n      try {\n        if (process.env.BABEL_8_BREAKING) {\n          return loadCjsDefault(filepath);\n        } else {\n          return loadCjsDefault(\n            filepath,\n            // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n            /* fallbackToTranspiledModule */ arguments[2],\n          );\n        }\n      } catch (e) {\n        if (e.code !== \"ERR_REQUIRE_ESM\") throw e;\n      }\n  }\n  if (yield* isAsync()) {\n    return yield* waitFor(loadMjsDefault(filepath));\n  }\n  throw new ConfigError(asyncError, filepath);\n}\n\nfunction loadCtsDefault(filepath: string) {\n  const ext = \".cts\";\n  const hasTsSupport = !!(\n    require.extensions[\".ts\"] ||\n    require.extensions[\".cts\"] ||\n    require.extensions[\".mts\"]\n  );\n\n  let handler: NodeJS.RequireExtensions[\"\"];\n\n  if (!hasTsSupport) {\n    const opts: InputOptions = {\n      babelrc: false,\n      configFile: false,\n      sourceType: \"unambiguous\",\n      sourceMaps: \"inline\",\n      sourceFileName: path.basename(filepath),\n      presets: [\n        [\n          getTSPreset(filepath),\n          {\n            onlyRemoveTypeImports: true,\n            optimizeConstEnums: true,\n            ...(process.env.BABEL_8_BREAKING\n              ? {}\n              : { allowDeclareFields: true }),\n          },\n        ],\n      ],\n    };\n\n    handler = function (m, filename) {\n      // If we want to support `.ts`, `.d.ts` must be handled specially.\n      if (handler && filename.endsWith(ext)) {\n        try {\n          // @ts-expect-error Undocumented API\n          return m._compile(\n            transformFileSync(filename, {\n              ...opts,\n              filename,\n            }).code,\n            filename,\n          );\n        } catch (error) {\n          if (!hasTsSupport) {\n            const packageJson = require(\"@babel/preset-typescript/package.json\");\n            if (semver.lt(packageJson.version, \"7.21.4\")) {\n              console.error(\n                \"`.cts` configuration file failed to load, please try to update `@babel/preset-typescript`.\",\n              );\n            }\n          }\n          throw error;\n        }\n      }\n      return require.extensions[\".js\"](m, filename);\n    };\n    require.extensions[ext] = handler;\n  }\n  try {\n    return loadCjsDefault(filepath);\n  } finally {\n    if (!hasTsSupport) {\n      if (require.extensions[ext] === handler) delete require.extensions[ext];\n      handler = undefined;\n    }\n  }\n}\n\nconst LOADING_CJS_FILES = new Set();\n\nfunction loadCjsDefault(filepath: string) {\n  // The `require()` call below can make this code reentrant if a require hook\n  // like @babel/register has been loaded into the system. That would cause\n  // Babel to attempt to compile the `.babelrc.js` file as it loads below. To\n  // cover this case, we auto-ignore re-entrant config processing. ESM loaders\n  // do not have this problem, because loaders do not apply to themselves.\n  if (LOADING_CJS_FILES.has(filepath)) {\n    debug(\"Auto-ignoring usage of config %o.\", filepath);\n    return {};\n  }\n\n  let module;\n  try {\n    LOADING_CJS_FILES.add(filepath);\n    module = endHiddenCallStack(require)(filepath);\n  } finally {\n    LOADING_CJS_FILES.delete(filepath);\n  }\n\n  if (process.env.BABEL_8_BREAKING) {\n    return module?.__esModule ? module.default : module;\n  } else {\n    return module?.__esModule\n      ? module.default ||\n          /* fallbackToTranspiledModule */ (arguments[1] ? module : undefined)\n      : module;\n  }\n}\n\nasync function loadMjsDefault(filepath: string) {\n  if (!import_) {\n    throw new ConfigError(\n      \"Internal error: Native ECMAScript modules aren't supported by this platform.\\n\",\n      filepath,\n    );\n  }\n\n  // import() expects URLs, not file paths.\n  // https://github.com/nodejs/node/issues/31710\n  const module = await endHiddenCallStack(import_)(pathToFileURL(filepath));\n  return module.default;\n}\n\nfunction getTSPreset(filepath: string) {\n  try {\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    return require(\"@babel/preset-typescript\");\n  } catch (error) {\n    if (error.code !== \"MODULE_NOT_FOUND\") throw error;\n\n    let message =\n      \"You appear to be using a .cts file as Babel configuration, but the `@babel/preset-typescript` package was not found: please install it!\";\n\n    if (!process.env.BABEL_8_BREAKING) {\n      if (process.versions.pnp) {\n        // Using Yarn PnP, which doesn't allow requiring packages that are not\n        // explicitly specified as dependencies.\n        message += `\nIf you are using Yarn Plug'n'Play, you may also need to add the following configuration to your .yarnrc.yml file:\n\npackageExtensions:\n\\t\"@babel/core@*\":\n\\t\\tpeerDependencies:\n\\t\\t\\t\"@babel/preset-typescript\": \"*\"\n`;\n      }\n    }\n\n    throw new ConfigError(message, filepath);\n  }\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA,SAAAC,MAAA;EAAA,MAAAC,IAAA,GAAAF,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAC,KAAA;EAAA,MAAAD,IAAA,GAAAF,OAAA;EAAAG,IAAA,YAAAA,CAAA;IAAA,OAAAD,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,QAAA;EAAA,MAAAF,IAAA,GAAAF,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,OAAA;EAAA,MAAAH,IAAA,GAAAF,OAAA;EAAAK,MAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAI,kBAAA,GAAAN,OAAA;AACA,IAAAO,YAAA,GAAAP,OAAA;AAGA,IAAAQ,cAAA,GAAAR,OAAA;AAA4D,SAAAS,mBAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAC,GAAA,cAAAC,IAAA,GAAAP,GAAA,CAAAK,GAAA,EAAAC,GAAA,OAAAE,KAAA,GAAAD,IAAA,CAAAC,KAAA,WAAAC,KAAA,IAAAP,MAAA,CAAAO,KAAA,iBAAAF,IAAA,CAAAG,IAAA,IAAAT,OAAA,CAAAO,KAAA,YAAAG,OAAA,CAAAV,OAAA,CAAAO,KAAA,EAAAI,IAAA,CAAAT,KAAA,EAAAC,MAAA;AAAA,SAAAS,kBAAAC,EAAA,6BAAAC,IAAA,SAAAC,IAAA,GAAAC,SAAA,aAAAN,OAAA,WAAAV,OAAA,EAAAC,MAAA,QAAAF,GAAA,GAAAc,EAAA,CAAAI,KAAA,CAAAH,IAAA,EAAAC,IAAA,YAAAb,MAAAK,KAAA,IAAAT,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,UAAAI,KAAA,cAAAJ,OAAAe,GAAA,IAAApB,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,WAAAe,GAAA,KAAAhB,KAAA,CAAAiB,SAAA;AAE5D,MAAMC,KAAK,GAAGC,OAASA,CAAC,CAAC,yCAAyC,CAAC;AAInE,IAAIC,OAAuD;AAC3D,IAAI;EAEFA,OAAO,GAAGjC,OAAO,CAAC,cAAc,CAAC;AACnC,CAAC,CAAC,OAAAkC,OAAA,EAAM,CAAC;AAEF,MAAMC,WAAW,GAAGC,QAAKA,CAAC,CAACC,SAAS,CACzCC,OAAO,CAACC,QAAQ,CAACC,IAAI,EAGrB,kBACF,CAAC;AAACC,OAAA,CAAAN,WAAA,GAAAA,WAAA;AAEa,UAAUO,eAAeA,CACtCC,QAAgB,EAChBC,UAAkB,EACA;EAClB,QAAQC,MAAGA,CAAC,CAACC,OAAO,CAACH,QAAQ,CAAC;IAC5B,KAAK,MAAM;MAGF;QACL,OAAOI,cAAc,CACnBJ,QAAQ,EAEyBhB,SAAS,CAAC,CAAC,CAC9C,CAAC;MACH;IACF,KAAK,MAAM;MACT;IACF,KAAK,MAAM;MACT,OAAOqB,cAAc,CAACL,QAAQ,CAAC;IACjC;MACE,IAAI;QAGK;UACL,OAAOI,cAAc,CACnBJ,QAAQ,EAEyBhB,SAAS,CAAC,CAAC,CAC9C,CAAC;QACH;MACF,CAAC,CAAC,OAAOsB,CAAC,EAAE;QACV,IAAIA,CAAC,CAACC,IAAI,KAAK,iBAAiB,EAAE,MAAMD,CAAC;MAC3C;EACJ;EACA,IAAI,OAAO,IAAAE,cAAO,EAAC,CAAC,EAAE;IACpB,OAAO,OAAO,IAAAC,cAAO,EAACC,cAAc,CAACV,QAAQ,CAAC,CAAC;EACjD;EACA,MAAM,IAAIW,oBAAW,CAACV,UAAU,EAAED,QAAQ,CAAC;AAC7C;AAEA,SAASK,cAAcA,CAACL,QAAgB,EAAE;EACxC,MAAMY,GAAG,GAAG,MAAM;EAClB,MAAMC,YAAY,GAAG,CAAC,EACpBxD,OAAO,CAACyD,UAAU,CAAC,KAAK,CAAC,IACzBzD,OAAO,CAACyD,UAAU,CAAC,MAAM,CAAC,IAC1BzD,OAAO,CAACyD,UAAU,CAAC,MAAM,CAAC,CAC3B;EAED,IAAIC,OAAqC;EAEzC,IAAI,CAACF,YAAY,EAAE;IACjB,MAAMG,IAAkB,GAAG;MACzBC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE,aAAa;MACzBC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAEnB,MAAGA,CAAC,CAACoB,QAAQ,CAACtB,QAAQ,CAAC;MACvCuB,OAAO,EAAE,CACP,CACEC,WAAW,CAACxB,QAAQ,CAAC,EAAAyB,MAAA,CAAAC,MAAA;QAEnBC,qBAAqB,EAAE,IAAI;QAC3BC,kBAAkB,EAAE;MAAI,GAGpB;QAAEC,kBAAkB,EAAE;MAAK,CAAC,EAEnC;IAEL,CAAC;IAEDd,OAAO,GAAG,SAAAA,CAAUe,CAAC,EAAEC,QAAQ,EAAE;MAE/B,IAAIhB,OAAO,IAAIgB,QAAQ,CAACC,QAAQ,CAACpB,GAAG,CAAC,EAAE;QACrC,IAAI;UAEF,OAAOkB,CAAC,CAACG,QAAQ,CACf,IAAAC,gCAAiB,EAACH,QAAQ,EAAAN,MAAA,CAAAC,MAAA,KACrBV,IAAI;YACPe;UAAQ,EACT,CAAC,CAACxB,IAAI,EACPwB,QACF,CAAC;QACH,CAAC,CAAC,OAAOvD,KAAK,EAAE;UACd,IAAI,CAACqC,YAAY,EAAE;YACjB,MAAMsB,WAAW,GAAG9E,OAAO,CAAC,uCAAuC,CAAC;YACpE,IAAIoC,QAAKA,CAAC,CAAC2C,EAAE,CAACD,WAAW,CAACE,OAAO,EAAE,QAAQ,CAAC,EAAE;cAC5CC,OAAO,CAAC9D,KAAK,CACX,4FACF,CAAC;YACH;UACF;UACA,MAAMA,KAAK;QACb;MACF;MACA,OAAOnB,OAAO,CAACyD,UAAU,CAAC,KAAK,CAAC,CAACgB,CAAC,EAAEC,QAAQ,CAAC;IAC/C,CAAC;IACD1E,OAAO,CAACyD,UAAU,CAACF,GAAG,CAAC,GAAGG,OAAO;EACnC;EACA,IAAI;IACF,OAAOX,cAAc,CAACJ,QAAQ,CAAC;EACjC,CAAC,SAAS;IACR,IAAI,CAACa,YAAY,EAAE;MACjB,IAAIxD,OAAO,CAACyD,UAAU,CAACF,GAAG,CAAC,KAAKG,OAAO,EAAE,OAAO1D,OAAO,CAACyD,UAAU,CAACF,GAAG,CAAC;MACvEG,OAAO,GAAG5B,SAAS;IACrB;EACF;AACF;AAEA,MAAMoD,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;AAEnC,SAASpC,cAAcA,CAACJ,QAAgB,EAAE;EAMxC,IAAIuC,iBAAiB,CAACE,GAAG,CAACzC,QAAQ,CAAC,EAAE;IACnCZ,KAAK,CAAC,mCAAmC,EAAEY,QAAQ,CAAC;IACpD,OAAO,CAAC,CAAC;EACX;EAEA,IAAI0C,MAAM;EACV,IAAI;IACFH,iBAAiB,CAACI,GAAG,CAAC3C,QAAQ,CAAC;IAC/B0C,MAAM,GAAG,IAAAE,qCAAkB,EAACvF,OAAO,CAAC,CAAC2C,QAAQ,CAAC;EAChD,CAAC,SAAS;IACRuC,iBAAiB,CAACM,MAAM,CAAC7C,QAAQ,CAAC;EACpC;EAIO;IAAA,IAAA8C,OAAA;IACL,OAAO,CAAAA,OAAA,GAAAJ,MAAM,aAANI,OAAA,CAAQC,UAAU,GACrBL,MAAM,CAACM,OAAO,KACsBhE,SAAS,CAAC,CAAC,CAAC,GAAG0D,MAAM,GAAGvD,SAAS,CAAC,GACtEuD,MAAM;EACZ;AACF;AAAC,SAEchC,cAAcA,CAAAuC,EAAA;EAAA,OAAAC,eAAA,CAAAjE,KAAA,OAAAD,SAAA;AAAA;AAAA,SAAAkE,gBAAA;EAAAA,eAAA,GAAAtE,iBAAA,CAA7B,WAA8BoB,QAAgB,EAAE;IAC9C,IAAI,CAACV,OAAO,EAAE;MACZ,MAAM,IAAIqB,oBAAW,CACnB,gFAAgF,EAChFX,QACF,CAAC;IACH;IAIA,MAAM0C,MAAM,SAAS,IAAAE,qCAAkB,EAACtD,OAAO,CAAC,CAAC,IAAA6D,oBAAa,EAACnD,QAAQ,CAAC,CAAC;IACzE,OAAO0C,MAAM,CAACM,OAAO;EACvB,CAAC;EAAA,OAAAE,eAAA,CAAAjE,KAAA,OAAAD,SAAA;AAAA;AAED,SAASwC,WAAWA,CAACxB,QAAgB,EAAE;EACrC,IAAI;IAEF,OAAO3C,OAAO,CAAC,0BAA0B,CAAC;EAC5C,CAAC,CAAC,OAAOmB,KAAK,EAAE;IACd,IAAIA,KAAK,CAAC+B,IAAI,KAAK,kBAAkB,EAAE,MAAM/B,KAAK;IAElD,IAAI4E,OAAO,GACT,yIAAyI;IAExG;MACjC,IAAIzD,OAAO,CAACC,QAAQ,CAACyD,GAAG,EAAE;QAGxBD,OAAO,IAAK;AACpB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;MACK;IACF;IAEA,MAAM,IAAIzC,oBAAW,CAACyC,OAAO,EAAEpD,QAAQ,CAAC;EAC1C;AACF;AAAC"}