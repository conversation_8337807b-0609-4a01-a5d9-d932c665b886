import React from 'react';
import { TouchableHighlight, Text } from 'react-native';
import PropTypes from 'prop-types';
import styles from './Styles';
import Config from '../../../../Config';
import { isDarkColor } from '../../../misc/HelperFunctions';

/**
 * Dummy Component for comment Add buttons
 */
const AddButton = (props) => {
  const { onPress, children, containerStyle } = props;
  const primaryColor = Config.toolbarColor;

  /**
   * If primary color is white the buttons wont be visible in white background
   * so change them to black if primary color is white
   * TODO Change this condition for inApp background color
   */
  const finalColor =
    primaryColor &&
    (primaryColor === 'white' ||
      primaryColor === '#ffffff' ||
      primaryColor === '#FFFFFF')
      ? 'black'
      : primaryColor;
  return (
    <TouchableHighlight
      style={{
        ...styles.submit,
        backgroundColor: finalColor,
        ...containerStyle,
      }}
      onPress={onPress}
      underlayColor="black">
      <Text
        allowFontScaling={false}
        style={{
          ...styles.text,
          color: isDarkColor(finalColor) ? 'white' : 'black',
        }}>
        {children}
      </Text>
    </TouchableHighlight>
  );
};

AddButton.propTypes = {
  /**
   * @param onPress
   * on click callback
   */
  onPress: PropTypes.func,

  /**
   * @param children
   * Plain text to show inside Text component
   */
  children: PropTypes.element,

  /**
   * @param containerStyle
   * Style of the button container
   */
  containerStyle: PropTypes.object,
};

export default AddButton;
