{"version": 3, "names": ["_gatherSequenceExpressions", "require", "toSequenceExpression", "nodes", "scope", "length", "declars", "result", "gatherSequenceExpressions", "declar", "push"], "sources": ["../../src/converters/toSequenceExpression.ts"], "sourcesContent": ["import gatherSequenceExpressions from \"./gatherSequenceExpressions\";\nimport type * as t from \"..\";\nimport type { DeclarationInfo } from \"./gatherSequenceExpressions\";\n\n/**\n * Turn an array of statement `nodes` into a `SequenceExpression`.\n *\n * Variable declarations are turned into simple assignments and their\n * declarations hoisted to the top of the current scope.\n *\n * Expression statements are just resolved to their expression.\n */\nexport default function toSequenceExpression(\n  nodes: ReadonlyArray<t.Node>,\n  scope: any,\n): t.SequenceExpression | undefined {\n  if (!nodes?.length) return;\n\n  const declars: DeclarationInfo[] = [];\n  const result = gatherSequenceExpressions(nodes, scope, declars);\n  if (!result) return;\n\n  for (const declar of declars) {\n    scope.push(declar);\n  }\n\n  // @ts-expect-error fixme: gatherSequenceExpressions will return an Expression when there are only one element\n  return result;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,0BAAA,GAAAC,OAAA;AAYe,SAASC,oBAAoBA,CAC1CC,KAA4B,EAC5BC,KAAU,EACwB;EAClC,IAAI,EAACD,KAAK,YAALA,KAAK,CAAEE,MAAM,GAAE;EAEpB,MAAMC,OAA0B,GAAG,EAAE;EACrC,MAAMC,MAAM,GAAG,IAAAC,kCAAyB,EAACL,KAAK,EAAEC,KAAK,EAAEE,OAAO,CAAC;EAC/D,IAAI,CAACC,MAAM,EAAE;EAEb,KAAK,MAAME,MAAM,IAAIH,OAAO,EAAE;IAC5BF,KAAK,CAACM,IAAI,CAACD,MAAM,CAAC;EACpB;EAGA,OAAOF,MAAM;AACf"}