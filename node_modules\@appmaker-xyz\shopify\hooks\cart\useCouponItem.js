import { appmaker } from '@appmaker-xyz/core';
import { currencyHelper } from '../../helper/index';
import { getSavingsValue } from './helper';
import { useCart } from './useCart';

function savingsReadable(calculatedSavingsValue) {
  return currencyHelper(calculatedSavingsValue);
}

export function useCouponItem({
  item: coupon,
  cartSubTotalAmount,
  cartTotalQuantity,
}) {
  const { lineItems } = useCart();

  const calculatedSavings = savingsReadable(
    coupon?.calculatedSavingsValue,
    coupon.value_type,
  );

  const disableCalculations = appmaker.applyFilters(
    'app-coupon-disable-calculations',
    false,
  );
  const enableAllCoupons = appmaker.applyFilters(
    'app-coupon-enable-all-coupons',
    false,
  );

  return {
    disableCalculations,
    calculatedSavings,
    calulatedSavings: calculatedSavings, // @deprecated
    calulatedSavingsValue: coupon.calculatedSavingsValue, // @deprecated
    ...coupon,
    isValid: enableAllCoupons || coupon?.isValid,
  };
}
