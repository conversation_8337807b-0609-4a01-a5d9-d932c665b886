import {
  View,
  Dimensions,
  StyleSheet,
  Pressable,
  Platform,
} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import Icon from 'react-native-vector-icons/Feather';
import Radio from './components/Radio';
import {
  useSortModal,
  useSortValue,
} from '../../hooks/filter/userCollectionFilter';
import {
  testProps,
  useDataSourceV2,
  usePageState,
  getExtensionConfig,
} from '@appmaker-xyz/core';
import { useTranslation } from 'react-i18next';
import { ThemeText } from '@appmaker-xyz/ui';
import { customStyles } from '@appmaker-xyz/shopify/styles/custom';

function ModalHeader(props) {
  const { extensionStyles, onClose } = props;
  return (
    <View style={[styles.topBar, extensionStyles?.sortModalHeaderContainer]}>
      <ThemeText
        style={[styles.sortHeadText, extensionStyles?.sortModalHeaderText]}>
        Sort By
      </ThemeText>
      <Pressable
        {...testProps(`sort-close`)}
        style={[styles.back, extensionStyles?.sortModalCloseButton]}
        onPress={onClose}>
        <Icon
          name="x"
          size={20}
          color="#000"
          style={extensionStyles?.sortModalCloseIcon}
        />
      </Pressable>
    </View>
  );
}
export default function SortModal(props) {
  const { t } = useTranslation();
  const blockData = usePageState((state) => state.blockData);
  const { isSortModalShown, closeSortModal, context } = useSortModal();
  const [{ isLoading }, { item }] = useDataSourceV2({
    dataSource: {
      source: 'shopify',
      methodName: 'productSort',
      params: {
        ...(blockData?.search && { search: blockData?.search }),
        context,
      },
    },
    enabled: true,
  });
  const finalSortOptions = item;

  const extensionStyles = getExtensionConfig?.('shopify', 'sortModalGroup', {});

  const { applySort, selectedSort } = useSortValue();
  const selectedIndex =
    selectedSort && typeof selectedSort === 'string'
      ? finalSortOptions.findIndex((item) => item.value === selectedSort)
      : selectedSort && selectedSort?.id
      ? finalSortOptions.findIndex(
          (item) => item.value?.id === selectedSort?.id,
        )
      : 0;
  return (
    <View>
      <Modal
        isVisible={isSortModalShown}
        style={[styles.modal, extensionStyles?.sortModal]}
        onDismiss={closeSortModal}
        onRequestClose={closeSortModal}>
        <View
          style={[styles.modalContainer, extensionStyles?.sortModalContainer]}>
          <ModalHeader
            onClose={closeSortModal}
            extensionStyles={extensionStyles}
          />
          <View
            style={[
              styles.sortOptions,
              extensionStyles?.sortModalListContainer,
            ]}>
            <Radio
              color={extensionStyles?.sortModalRadioColor || '#000000'}
              textStyles={extensionStyles?.sortModalRadioText}
              options={finalSortOptions}
              selectedSort={selectedSort}
              initial={selectedIndex}
              onPress={(value, label) => {
                applySort(value, { label });
              }}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
}

// Get the height and width of the screen
const { height, width } = Dimensions.get('window');

// Calculate the aspect ratio
const aspectRatio = height / width;

// Check if the aspect ratio indicates a notch
const hasNotch = aspectRatio > 1.8 && Platform.OS === 'ios';

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 18,
    borderTopRightRadius: 18,
    overflow: 'hidden',
    paddingBottom: hasNotch ? 22 : 0,
  },
  topBar: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderBottomColor: '#e2e2e2',
    borderBottomWidth: 0.5,
  },
  sortHeadText: {
    ...customStyles.styles.fontFamily.bold,
    color: '#1B1B1B',
    flexGrow: 1,
  },
  back: {
    padding: 6,
    borderRadius: 18,
  },
  sortOptions: {
    padding: 12,
  },
});
