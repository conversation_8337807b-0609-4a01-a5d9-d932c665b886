"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.SCOPE_VAR = exports.SCOPE_TS_MODULE = exports.SCOPE_SUPER = exports.SCOPE_STATIC_BLOCK = exports.SCOPE_SIMPLE_CATCH = exports.SCOPE_PROGRAM = exports.SCOPE_OTHER = exports.SCOPE_FUNCTION = exports.SCOPE_DIRECT_SUPER = exports.SCOPE_CLASS = exports.SCOPE_ARROW = exports.CLASS_ELEMENT_STATIC_SETTER = exports.CLASS_ELEMENT_STATIC_GETTER = exports.CLASS_ELEMENT_OTHER = exports.CLASS_ELEMENT_KIND_SETTER = exports.CLASS_ELEMENT_KIND_GETTER = exports.CLASS_ELEMENT_KIND_ACCESSOR = exports.CLASS_ELEMENT_INSTANCE_SETTER = exports.CLASS_ELEMENT_INSTANCE_GETTER = exports.CLASS_ELEMENT_FLAG_STATIC = exports.BIND_VAR = exports.BIND_TS_TYPE_IMPORT = exports.BIND_TS_TYPE = exports.BIND_TS_NAMESPACE = exports.BIND_TS_INTERFACE = exports.BIND_TS_ENUM = exports.BIND_TS_CONST_ENUM = exports.BIND_TS_AMBIENT = exports.BIND_SCOPE_VAR = exports.BIND_SCOPE_OUTSIDE = exports.BIND_SCOPE_LEXICAL = exports.BIND_SCOPE_FUNCTION = exports.BIND_OUTSIDE = exports.BIND_NONE = exports.BIND_LEXICAL = exports.BIND_KIND_VALUE = exports.BIND_KIND_TYPE = exports.BIND_FUNCTION = exports.BIND_FLOW_DECLARE_FN = exports.BIND_FLAGS_TS_IMPORT = exports.BIND_FLAGS_TS_EXPORT_ONLY = exports.BIND_FLAGS_TS_ENUM = exports.BIND_FLAGS_TS_CONST_ENUM = exports.BIND_FLAGS_NO_LET_IN_LEXICAL = exports.BIND_FLAGS_NONE = exports.BIND_FLAGS_FLOW_DECLARE_FN = exports.BIND_FLAGS_CLASS = exports.BIND_CLASS = exports.BIND_CATCH_PARAM = void 0;
const SCOPE_OTHER = 0b000000000,
  SCOPE_PROGRAM = 0b000000001,
  SCOPE_FUNCTION = 0b000000010,
  SCOPE_ARROW = 0b000000100,
  SCOPE_SIMPLE_CATCH = 0b000001000,
  SCOPE_SUPER = 0b000010000,
  SCOPE_DIRECT_SUPER = 0b000100000,
  SCOPE_CLASS = 0b001000000,
  SCOPE_STATIC_BLOCK = 0b010000000,
  SCOPE_TS_MODULE = 0b100000000,
  SCOPE_VAR = SCOPE_PROGRAM | SCOPE_FUNCTION | SCOPE_STATIC_BLOCK | SCOPE_TS_MODULE;
exports.SCOPE_VAR = SCOPE_VAR;
exports.SCOPE_TS_MODULE = SCOPE_TS_MODULE;
exports.SCOPE_STATIC_BLOCK = SCOPE_STATIC_BLOCK;
exports.SCOPE_CLASS = SCOPE_CLASS;
exports.SCOPE_DIRECT_SUPER = SCOPE_DIRECT_SUPER;
exports.SCOPE_SUPER = SCOPE_SUPER;
exports.SCOPE_SIMPLE_CATCH = SCOPE_SIMPLE_CATCH;
exports.SCOPE_ARROW = SCOPE_ARROW;
exports.SCOPE_FUNCTION = SCOPE_FUNCTION;
exports.SCOPE_PROGRAM = SCOPE_PROGRAM;
exports.SCOPE_OTHER = SCOPE_OTHER;
const BIND_KIND_VALUE = 0b0000000000001,
  BIND_KIND_TYPE = 0b0000000000010,
  BIND_SCOPE_VAR = 0b0000000000100,
  BIND_SCOPE_LEXICAL = 0b0000000001000,
  BIND_SCOPE_FUNCTION = 0b0000000010000,
  BIND_SCOPE_OUTSIDE = 0b0000000100000,
  BIND_FLAGS_NONE = 0b00000001000000,
  BIND_FLAGS_CLASS = 0b00000010000000,
  BIND_FLAGS_TS_ENUM = 0b00000100000000,
  BIND_FLAGS_TS_CONST_ENUM = 0b00001000000000,
  BIND_FLAGS_TS_EXPORT_ONLY = 0b00010000000000,
  BIND_FLAGS_FLOW_DECLARE_FN = 0b00100000000000,
  BIND_FLAGS_TS_IMPORT = 0b01000000000000,
  BIND_FLAGS_NO_LET_IN_LEXICAL = 0b10000000000000;
exports.BIND_FLAGS_NO_LET_IN_LEXICAL = BIND_FLAGS_NO_LET_IN_LEXICAL;
exports.BIND_FLAGS_TS_IMPORT = BIND_FLAGS_TS_IMPORT;
exports.BIND_FLAGS_FLOW_DECLARE_FN = BIND_FLAGS_FLOW_DECLARE_FN;
exports.BIND_FLAGS_TS_EXPORT_ONLY = BIND_FLAGS_TS_EXPORT_ONLY;
exports.BIND_FLAGS_TS_CONST_ENUM = BIND_FLAGS_TS_CONST_ENUM;
exports.BIND_FLAGS_TS_ENUM = BIND_FLAGS_TS_ENUM;
exports.BIND_FLAGS_CLASS = BIND_FLAGS_CLASS;
exports.BIND_FLAGS_NONE = BIND_FLAGS_NONE;
exports.BIND_SCOPE_OUTSIDE = BIND_SCOPE_OUTSIDE;
exports.BIND_SCOPE_FUNCTION = BIND_SCOPE_FUNCTION;
exports.BIND_SCOPE_LEXICAL = BIND_SCOPE_LEXICAL;
exports.BIND_SCOPE_VAR = BIND_SCOPE_VAR;
exports.BIND_KIND_TYPE = BIND_KIND_TYPE;
exports.BIND_KIND_VALUE = BIND_KIND_VALUE;
const BIND_CLASS = BIND_KIND_VALUE | BIND_KIND_TYPE | BIND_SCOPE_LEXICAL | BIND_FLAGS_CLASS | BIND_FLAGS_NO_LET_IN_LEXICAL,
  BIND_LEXICAL = BIND_KIND_VALUE | 0 | BIND_SCOPE_LEXICAL | BIND_FLAGS_NO_LET_IN_LEXICAL,
  BIND_CATCH_PARAM = BIND_KIND_VALUE | 0 | BIND_SCOPE_LEXICAL | 0,
  BIND_VAR = BIND_KIND_VALUE | 0 | BIND_SCOPE_VAR | 0,
  BIND_FUNCTION = BIND_KIND_VALUE | 0 | BIND_SCOPE_FUNCTION | 0,
  BIND_TS_INTERFACE = 0 | BIND_KIND_TYPE | 0 | BIND_FLAGS_CLASS,
  BIND_TS_TYPE = 0 | BIND_KIND_TYPE | 0 | 0,
  BIND_TS_ENUM = BIND_KIND_VALUE | BIND_KIND_TYPE | BIND_SCOPE_LEXICAL | BIND_FLAGS_TS_ENUM | BIND_FLAGS_NO_LET_IN_LEXICAL,
  BIND_TS_AMBIENT = 0 | 0 | 0 | BIND_FLAGS_TS_EXPORT_ONLY,
  BIND_NONE = 0 | 0 | 0 | BIND_FLAGS_NONE,
  BIND_OUTSIDE = BIND_KIND_VALUE | 0 | 0 | BIND_FLAGS_NONE,
  BIND_TS_CONST_ENUM = BIND_TS_ENUM | BIND_FLAGS_TS_CONST_ENUM,
  BIND_TS_NAMESPACE = 0 | 0 | 0 | BIND_FLAGS_TS_EXPORT_ONLY,
  BIND_TS_TYPE_IMPORT = 0 | BIND_KIND_TYPE | 0 | BIND_FLAGS_TS_IMPORT,
  BIND_FLOW_DECLARE_FN = BIND_FLAGS_FLOW_DECLARE_FN;
exports.BIND_FLOW_DECLARE_FN = BIND_FLOW_DECLARE_FN;
exports.BIND_TS_TYPE_IMPORT = BIND_TS_TYPE_IMPORT;
exports.BIND_TS_NAMESPACE = BIND_TS_NAMESPACE;
exports.BIND_TS_CONST_ENUM = BIND_TS_CONST_ENUM;
exports.BIND_OUTSIDE = BIND_OUTSIDE;
exports.BIND_NONE = BIND_NONE;
exports.BIND_TS_AMBIENT = BIND_TS_AMBIENT;
exports.BIND_TS_ENUM = BIND_TS_ENUM;
exports.BIND_TS_TYPE = BIND_TS_TYPE;
exports.BIND_TS_INTERFACE = BIND_TS_INTERFACE;
exports.BIND_FUNCTION = BIND_FUNCTION;
exports.BIND_VAR = BIND_VAR;
exports.BIND_CATCH_PARAM = BIND_CATCH_PARAM;
exports.BIND_LEXICAL = BIND_LEXICAL;
exports.BIND_CLASS = BIND_CLASS;
const CLASS_ELEMENT_FLAG_STATIC = 0b100,
  CLASS_ELEMENT_KIND_GETTER = 0b010,
  CLASS_ELEMENT_KIND_SETTER = 0b001,
  CLASS_ELEMENT_KIND_ACCESSOR = CLASS_ELEMENT_KIND_GETTER | CLASS_ELEMENT_KIND_SETTER;
exports.CLASS_ELEMENT_KIND_ACCESSOR = CLASS_ELEMENT_KIND_ACCESSOR;
exports.CLASS_ELEMENT_KIND_SETTER = CLASS_ELEMENT_KIND_SETTER;
exports.CLASS_ELEMENT_KIND_GETTER = CLASS_ELEMENT_KIND_GETTER;
exports.CLASS_ELEMENT_FLAG_STATIC = CLASS_ELEMENT_FLAG_STATIC;
const CLASS_ELEMENT_STATIC_GETTER = CLASS_ELEMENT_KIND_GETTER | CLASS_ELEMENT_FLAG_STATIC,
  CLASS_ELEMENT_STATIC_SETTER = CLASS_ELEMENT_KIND_SETTER | CLASS_ELEMENT_FLAG_STATIC,
  CLASS_ELEMENT_INSTANCE_GETTER = CLASS_ELEMENT_KIND_GETTER,
  CLASS_ELEMENT_INSTANCE_SETTER = CLASS_ELEMENT_KIND_SETTER,
  CLASS_ELEMENT_OTHER = 0;
exports.CLASS_ELEMENT_OTHER = CLASS_ELEMENT_OTHER;
exports.CLASS_ELEMENT_INSTANCE_SETTER = CLASS_ELEMENT_INSTANCE_SETTER;
exports.CLASS_ELEMENT_INSTANCE_GETTER = CLASS_ELEMENT_INSTANCE_GETTER;
exports.CLASS_ELEMENT_STATIC_SETTER = CLASS_ELEMENT_STATIC_SETTER;
exports.CLASS_ELEMENT_STATIC_GETTER = CLASS_ELEMENT_STATIC_GETTER;

//# sourceMappingURL=scopeflags.js.map
