export function converToShopifyParams(appliedFilter) {
  const filterKeys = Object.keys(appliedFilter);

  const filterParam = [];
  filterKeys.forEach((key) => {
    const value = appliedFilter[key];
    // const currentFilter = avilableFiltersObject[key];
    // console.log(currentFilter.type, key);
    // if (currentFilter.type === 'PRICE_RANGE') {
    //   //   return {};
    //   filterParam.push({ price: value });
    // }
    // if (currentFilter.type === 'LIST') {
    // filterParam.push(value)

    const filterValueKeys = Object.keys(value);
    console.log('value------>>>>', value);
    filterValueKeys.forEach((filterValueKey) => {
      const filterValue = value[filterValueKey];
      const { type } = filterValue;
      console.log(
        '-----------------typetypetype-->------------------->------------------->?><<><>><>K<<>><><',
        type,
      );
      switch (type) {
        case 'PRICE_RANGE':
          filterParam.push({
            [filterValueKey]: {
              min: filterValue.min,
              max: filterValue.max,
            },
          });
          break;
        default:
          console.log(filterValue, filterValueKey);
          if (filterValue) {
            try {
              filterParam.push(JSON.parse(filterValue.input));
            } catch (error) {
              filterParam.push(filterValue);
            }
          }
          break;
      }
    });

    // }
  });
  return filterParam;
}
