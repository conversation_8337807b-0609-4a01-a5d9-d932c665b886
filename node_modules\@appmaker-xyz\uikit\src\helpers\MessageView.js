/**
 * Created by saleeh on 29/08/16.
 */
import { appmaker } from '@appmaker-xyz/core';
import { Snackbar } from '@appmaker-xyz/uikit/Views';
import { Alert } from 'react-native';
import i18next from 'i18next';
import { I18nManager } from 'react-native';

const t = (key) => i18next.t(key);

const showMessage = (
  { title, buttonTitle, buttonAction, message },
  deps = {},
) => {
  if (title.length < 80) {
    Snackbar.show({
      text: t(title),
      duration: Snackbar.LENGTH_SHORT,
      rtl: I18nManager.isRTL,
      ...(buttonAction && {
        action: {
          text: t(buttonTitle),
          textColor: 'white',
          onPress: () => {
            deps.handleAction(buttonAction, deps);
          },
        },
      }),
    });
  } else {
    Alert.alert(t('Alert'), t(title));
  }
};

class MessageView {
  static success({ title, buttonTitle, buttonAction, message }, deps = {}) {
    const show = appmaker.applyFilters(
      'change-appmaker-message-view-success',
      false,
      {
        title: t(title),
        buttonTitle: t(buttonTitle),
        buttonAction,
        message: t(message),
      },
      deps,
    );
    if (!show) {
      showMessage({ title, buttonTitle, buttonAction, message }, deps);
    }
  }

  static error(title) {
    if (title.length < 80) {
      Snackbar.show({
        text: t(title),
        duration: Snackbar.LENGTH_SHORT,
      });
    } else {
      Alert.alert(t('Error'), t(title));
    }
  }

  static showHttpError(error) {
    if (error.length < 80) {
      MessageView.error(t(error.data.message));
    } else {
      Alert.alert(t('Error'), t(error.data.message));
    }
  }
}

export default MessageView;
