{"version": 3, "names": ["_isValidIdentifier", "require", "_helperValidatorIdentifier", "toIdentifier", "input", "name", "c", "isIdentifierChar", "codePointAt", "replace", "match", "toUpperCase", "isValidIdentifier"], "sources": ["../../src/converters/toIdentifier.ts"], "sourcesContent": ["import isValidIdentifier from \"../validators/isValidIdentifier\";\nimport { isIdentifierChar } from \"@babel/helper-validator-identifier\";\n\nexport default function toIdentifier(input: string): string {\n  input = input + \"\";\n\n  // replace all non-valid identifiers with dashes\n  let name = \"\";\n  for (const c of input) {\n    name += isIdentifierChar(c.codePointAt(0)) ? c : \"-\";\n  }\n\n  // remove all dashes and numbers from start of name\n  name = name.replace(/^[-0-9]+/, \"\");\n\n  // camel case\n  name = name.replace(/[-\\s]+(.)?/g, function (match, c) {\n    return c ? c.toUpperCase() : \"\";\n  });\n\n  if (!isValidIdentifier(name)) {\n    name = `_${name}`;\n  }\n\n  return name || \"_\";\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,0BAAA,GAAAD,OAAA;AAEe,SAASE,YAAYA,CAACC,KAAa,EAAU;EAC1DA,KAAK,GAAGA,KAAK,GAAG,EAAE;EAGlB,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,MAAMC,CAAC,IAAIF,KAAK,EAAE;IACrBC,IAAI,IAAI,IAAAE,2CAAgB,EAACD,CAAC,CAACE,WAAW,CAAC,CAAC,CAAC,CAAC,GAAGF,CAAC,GAAG,GAAG;EACtD;EAGAD,IAAI,GAAGA,IAAI,CAACI,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;EAGnCJ,IAAI,GAAGA,IAAI,CAACI,OAAO,CAAC,aAAa,EAAE,UAAUC,KAAK,EAAEJ,CAAC,EAAE;IACrD,OAAOA,CAAC,GAAGA,CAAC,CAACK,WAAW,CAAC,CAAC,GAAG,EAAE;EACjC,CAAC,CAAC;EAEF,IAAI,CAAC,IAAAC,0BAAiB,EAACP,IAAI,CAAC,EAAE;IAC5BA,IAAI,GAAI,IAAGA,IAAK,EAAC;EACnB;EAEA,OAAOA,IAAI,IAAI,GAAG;AACpB"}