import { color, spacing, font } from '@appmaker-xyz/uikit/src/styles/index';
import * as React from 'react';
import { useWindowDimensions, StyleSheet } from 'react-native';
import { TabView, TabBar } from 'react-native-tab-view';
import TabContent from './TabContent';
import { AppmakerText } from '@appmaker-xyz/uikit';

export default function TabViewExample({
  attributes,
  BlockItem,
  onAction,
  currentAction,
  BlocksView,
  innerBlocks,
}) {
  const layout = useWindowDimensions();
  const {
    scrollable = false,
    themeColor = color.dark,
    bgColor = '#f9f9f9',
    buttonTab = false,
  } = attributes;

  const [index, setIndex] = React.useState(0);
  const routes = React.useMemo(() => {
    return innerBlocks.map((block) => {
      return {
        key: block.attributes.pageId,
        title: block.attributes.label,
        block,
      };
    });
  });

  const tabViewStyle = [];
  const contentContainerStyle = [];
  const tabBarStyle = [];
  const indicatorStyle = [];
  const labelStyle = [];
  const tabStyle = [];

  if (buttonTab === false) {
    indicatorStyle.push(styles.indicator, { backgroundColor: themeColor });
    labelStyle.push(styles.label, { color: themeColor });
  }

  if (scrollable) {
    tabStyle.push(styles.tabStyle);
  }

  if (buttonTab) {
    tabViewStyle.push({
      borderRadius: 10,
      overflow: 'hidden',
    });
    contentContainerStyle.push({
      height: 48,
      // alignItems: 'center',
    });
    tabBarStyle.push({
      backgroundColor: '#f1f1f1',
      borderColor: '#cccccc',
      borderWidth: 1,
      borderRadius: 10,
      overflow: 'hidden',
      marginHorizontal: 12,
      marginVertical: 12,
    });
    indicatorStyle.push({
      backgroundColor: themeColor,
      height: '100%',
      borderRadius: 10,
    });
  }

  if (bgColor) {
    tabBarStyle.push({ backgroundColor: bgColor });
  }

  const renderScene = ({ route }) => {
    return (
      <TabContent
        block={route?.block}
        BlocksView={BlocksView}
        onAction={onAction}
        currentAction={currentAction}
      />
    );
  };
  const renderTabBar = (props) => {
    return (
      <TabBar
        {...props}
        scrollEnabled={false}
        indicatorStyle={indicatorStyle}
        style={tabBarStyle}
        labelStyle={labelStyle}
        tabStyle={tabStyle}
        contentContainerStyle={contentContainerStyle}
        renderLabel={({ route, focused }) => (
          <AppmakerText
            fontColor={focused && buttonTab ? '#f8f6f2' : '#231f20'}>
            {route.title}
          </AppmakerText>
        )}
      />
    );
  };
  return (
    <TabView
      lazy
      navigationState={{ index, routes }}
      swipeEnabled={attributes?.swipeEnabled}
      renderScene={renderScene}
      onIndexChange={setIndex}
      renderTabBar={renderTabBar}
      initialLayout={{ width: layout.width }}
      style={tabViewStyle}
    />
  );
}

const styles = StyleSheet.create({
  scene: {
    flex: 1,
  },
  indicator: {
    borderTopEndRadius: spacing.small,
    borderTopStartRadius: spacing.small,
  },
  label: {
    fontSize: 14,
    fontFamily: font.family.bold,
  },
  tabStyle: {
    width: 'auto',
  },
});
