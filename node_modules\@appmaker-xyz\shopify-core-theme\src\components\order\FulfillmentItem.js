import React from 'react';
import { StyleSheet } from 'react-native';
import { useFulfillments } from '@appmaker-xyz/shopify';
import { ThemeText, Layout, AppImage, Button } from '@appmaker-xyz/ui';
import Icon from 'react-native-vector-icons/Feather';

const imageSize = 40;

const FulfillmentItem = (props) => {
  const { onAction } = props;
  const {
    trackingCompany,
    images,
    fulfillmentLineItemsQuantity,
    trackingInfo,
  } = useFulfillments(props);
  const visibleImages = images.slice(0, 2);
  const remainingCount = images.length - visibleImages.length;

  const copyToClipboard = (number) => {
    onAction({
      action: 'COPY_TO_CLIPBOARD',
      text: number,
    });
  };
  const trackingDetailsItem = (item) => {
    return (
      <Layout style={styles.rowRight}>
        <Layout style={styles.textContainer}>
          <Layout style={[styles.row, {}]}>
            <ThemeText size="sm">Tracking Id: </ThemeText>
            <ThemeText fontFamily="medium" size="sm">
              {item?.number}
            </ThemeText>
            <Icon
              name="copy"
              size={12}
              color="#000"
              style={styles.copyIcon}
              onPress={() => copyToClipboard(item?.number)}
            />
          </Layout>
          <Layout style={styles.row}>
            <ThemeText size="sm">Partner: </ThemeText>
            <ThemeText size="sm" fontFamily="medium">
              {trackingCompany}
            </ThemeText>
          </Layout>
        </Layout>
        {item?.url ? (
          <Layout style={styles.row}>
            <Button
              onPress={() => {
                onAction({
                  action: 'OPEN_IN_WEBVIEW',
                  url: item?.url,
                });
              }}
              title="Track"
              size="sm"
              color="#000"
              isOutline
            />
          </Layout>
        ) : null}
      </Layout>
    );
  };

  return (
    <Layout style={styles.itemContainer}>
      <Layout style={styles.overlappedImages}>
        {visibleImages.map((image, index) => (
          <AppImage
            key={index}
            uri={image}
            style={[styles.image, { zIndex: visibleImages.length + index }]}
          />
        ))}
        {remainingCount > 0 && (
          <Layout style={[styles.image, styles.remaining]}>
            <ThemeText
              color="#000"
              fontFamily="bold">{`+${remainingCount}`}</ThemeText>
          </Layout>
        )}
      </Layout>
      <Layout style={styles.column}>
        {trackingInfo?.length > 0
          ? trackingInfo?.map((item) => {
              return trackingDetailsItem(item);
            })
          : null}
      </Layout>
    </Layout>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  overlappedImages: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 4,
    marginLeft: imageSize / 2,
  },
  image: {
    width: imageSize,
    height: imageSize,
    borderRadius: imageSize / 2,
    marginLeft: -imageSize / 2,
    borderWidth: 3,
    borderColor: '#fff',
    backgroundColor: '#E5E7EB',
  },
  remaining: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#d9d9d9',
    opacity: 0.7,
    marginLeft: -imageSize,
    zIndex: 10,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  column: {
    flexDirection: 'column',
    flex: 1,
  },
  rowRight: {
    flexDirection: 'row',
    width: '100%',
    marginVertical: 2,
  },
  copyIcon: {
    marginLeft: 4,
    paddingHorizontal: 2,
  },
  textContainer: {
    flexGrow: 1,
    flexShrink: 1,
  },
});

export default FulfillmentItem;
