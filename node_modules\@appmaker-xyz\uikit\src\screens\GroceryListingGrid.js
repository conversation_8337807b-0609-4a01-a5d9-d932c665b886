import React, {useEffect, useState} from 'react';
import {
  ProductScrollerItem,
  Layout,
  CheckoutButton,
  SearchBar,
} from '../components';
import {
  FlatList,
  SafeAreaView,
  StyleSheet,
  View,
  ActivityIndicator,
} from 'react-native';
import {color, spacing} from '../styles';

import {useApConfigState} from '@appmaker/v3';
// import {color} from '../styles';

const LoadingIndicator = (tailLoading, loadBottomButton) => {
  const paddingBottom = loadBottomButton ? (tailLoading ? 80 : 70) : 0;
  const loader = tailLoading ? (
    <ActivityIndicator size="small" color={color.dark} />
  ) : null;
  return <View style={{paddingBottom}}>{loader}</View>;
};

// const Item = ({item, onQuantityChange, quatity}) => {
//   const [quantityLoading, setLoading] = useState(false);
//   // console.log(item.sale_percentage);
//   return (
//     <ListItem
//       uri={item.thumbnail}
//       title={item.name}
//       salePrice={item.sale_price_display}
//       regularPrice={item.regular_price_display}
//       quatity={quatity}
//       outOfStock={
//         item.in_stock
//           ? false
//           : I18n.t('out_of_stock', {
//               defaultValue: 'OUT OF STOCK',
//             })
//       }
//       quantityLoading={quantityLoading}
//       saleBadge={item.sale_percentage || ''}
//       onQuantityChange={async (quantity) => {
//         setLoading(true);
//         onQuantityChange && (await onQuantityChange({item, quantity}));
//         setLoading(false);
//       }}
//     />
//   );
// };

const Item = ({item}) => {
  return (
    <Layout style={styles.itemContainer}>
      <ProductScrollerItem
        gridViewListing={true}
        uri={item.thumbnail}
        title={item.name}
        salePrice={item.regular_price_display}
        regularPrice={item.price_display}
        saleBadge="20% OFF"
        // outOfStock="Out of Stock"
        wishList={true}
        groceryMode={true}
      />
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {},
  itemContainer: {
    width: '50%',
    padding: spacing.nano,
  },
});

const GroceryListing = ({...props}) => {
  // console.log(JSON.stringify(, null, 2));
  const config = useApConfigState();
  const [items, setItems] = useState([]);
  const [tailLoading, setTailLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [nextPage, setNextPage] = useState(true);
  const [page, setPage] = useState(Number(1));
  const [cartReponse, setCartReponse] = useState({});

  useEffect(() => {
    const loadProducts = async () => {
      let params = {
        page: page,
        per_page: 24,
      };
      try {
        params = {...params, ...props.navigation.state.params.params};
      } catch (error) {}
      console.log(params);
      const cartRep = await config.restApi.getCart();
      setCartReponse(cartRep);
      const productResp = await config.restApi.getProductList(params);
      if (tailLoading) {
        setItems(items.concat(productResp));
        setNextPage(productResp.length > 0);
        setTailLoading(false);
      } else if (nextPage) {
        setLoading(false);
        setItems(productResp);
      }
    };
    loadProducts();
  }, [page]);

  const onEndReach = () => {
    if (nextPage && !tailLoading) {
      setPage(Number(page) + Number(1));
      setNextPage(true);
      setTailLoading(true);
    }
  };

  const onQuantityChange = async ({item, quantity}) => {
    // setLoading(true);
    console.log(quantity);
    const resp = await config.restApi.addToCart({
      product_id: item.id,
      quantity: 1,
    });
    setCartReponse(resp);
    // setLoading(false);
    // console.log(resp);
  };
  const getQuantity = ({id}) => {
    const prodcuts = cartReponse.products.filter(
      item => item.product_id === id,
    );

    return (prodcuts[0] && prodcuts[0].quantity) || 0;
  };
  return (
    <>
      <SafeAreaView style={{flex: 1}}>
        <SearchBar />
        <Layout loading={loading} style={{flex: 1}}>
          <FlatList
            numColumns={2}
            data={items}
            renderItem={({item}) => (
              <Item
                item={item}
                onQuantityChange={onQuantityChange}
                quatity={getQuantity(item)}
              />
            )}
            keyExtractor={item => item.id}
            onEndReached={() => onEndReach()}
            ListFooterComponent={() => LoadingIndicator(tailLoading, !loading)}
          />
        </Layout>
        {!loading && (
          <CheckoutButton
            onPress={() => {
              props.navigation.push('CartRoot');
            }}
            show={true}
            itemCount={cartReponse.product_count}
            totalPrice={cartReponse.product_subtotal_display}
          />
        )}
      </SafeAreaView>
    </>
  );
};

export default GroceryListing;
