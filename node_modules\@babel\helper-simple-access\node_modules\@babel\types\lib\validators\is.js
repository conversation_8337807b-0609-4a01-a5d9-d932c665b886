"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = is;
var _shallowEqual = require("../utils/shallowEqual");
var _isType = require("./isType");
var _isPlaceholderType = require("./isPlaceholderType");
var _definitions = require("../definitions");
function is(type, node, opts) {
  if (!node) return false;
  const matches = (0, _isType.default)(node.type, type);
  if (!matches) {
    if (!opts && node.type === "Placeholder" && type in _definitions.FLIPPED_ALIAS_KEYS) {
      return (0, _isPlaceholderType.default)(node.expectedNode, type);
    }
    return false;
  }
  if (typeof opts === "undefined") {
    return true;
  } else {
    return (0, _shallowEqual.default)(node, opts);
  }
}

//# sourceMappingURL=is.js.map
