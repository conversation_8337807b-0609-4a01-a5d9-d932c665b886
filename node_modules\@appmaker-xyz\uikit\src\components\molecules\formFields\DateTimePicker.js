import React, { useState } from 'react';
import { View, StyleSheet, Pressable } from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { AppmakerText } from '@appmaker-xyz/uikit';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';

const DateTimePicker = ({ attributes, coreDispatch, clientId, onAction }) => {
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const [choseDate, setChoseDate] = useState(null);
  const { label, placeholder, name, value, autoAction } = attributes;
  const parentName = attributes.parentName || '_formData';

  function onChangeInput(valueText) {
    coreDispatch({
      type: 'SET_VALUE',
      name,
      value: valueText,
      parent: parentName,
    });
  }

  const showDatePicker = () => {
    setDatePickerVisibility(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const handleConfirm = (date) => {
    setChoseDate(date.toDateString());
    onChangeInput(date);
    hideDatePicker();
  };

  return (
    <View style={styles.container}>
      <Pressable onPress={showDatePicker} style={styles.inputContainer}>
        <Icon name="calendar" size={20} />
        <AppmakerText status={choseDate ? 'dark' : 'grey'}>
          {choseDate ? choseDate : label}
        </AppmakerText>
      </Pressable>
      <DateTimePickerModal
        isVisible={isDatePickerVisible}
        mode="date"
        onConfirm={handleConfirm}
        onCancel={hideDatePicker}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {},
  inputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'gray',
    marginBottom: 14,
    marginTop: 2,
    backgroundColor: 'white',
    overflow: 'hidden',
  },
});

export default DateTimePicker;
