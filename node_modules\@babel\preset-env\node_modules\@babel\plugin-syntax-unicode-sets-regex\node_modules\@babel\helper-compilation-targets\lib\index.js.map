{"version": 3, "names": ["ESM_SUPPORT", "browserModulesData", "v", "OptionValidator", "validateTargetNames", "targets", "validTargets", "Object", "keys", "TargetNames", "target", "Error", "formatMessage", "findSuggestion", "isBrowsersQueryValid", "browsers", "Array", "isArray", "every", "b", "validateBrowsers", "invariant", "undefined", "String", "getLowestVersions", "reduce", "all", "browser", "browserName", "browserVersion", "split", "browserNameMap", "splitVersion", "toLowerCase", "isSplitUnreleased", "isUnreleasedVersion", "semverify", "version", "isUnreleased", "getLowestUnreleased", "parsedBrowserVersion", "semverMin", "e", "outputDecimalWarning", "decimalTargets", "length", "console", "warn", "for<PERSON>ach", "value", "semverifyTarget", "error", "node<PERSON>arget<PERSON><PERSON><PERSON>", "parsed", "process", "versions", "node", "defaultTargetParser", "generateTargets", "inputTargets", "input", "<PERSON><PERSON><PERSON><PERSON>", "resolveTargets", "queries", "env", "resolved", "browserslist", "mobileToDesktop", "targetsCache", "<PERSON><PERSON><PERSON><PERSON>", "max", "resolveTargetsCached", "cache<PERSON>ey", "join", "cached", "get", "set", "getTargets", "options", "config<PERSON><PERSON>", "shouldParseBrowsers", "hasTargets", "shouldSearchForConfig", "ignoreBrowserslistConfig", "loadConfig", "config", "configFile", "path", "browserslistEnv", "map", "queryBrowsers", "esmSupportVersion", "getHighestUnreleased", "assign", "result", "decimalWarnings", "sort", "push", "parsed<PERSON><PERSON>get", "parsedValue"], "sources": ["../src/index.ts"], "sourcesContent": ["import browserslist from \"browserslist\";\nimport { findSuggestion } from \"@babel/helper-validator-option\";\nimport browserModulesData from \"@babel/compat-data/native-modules\";\nimport LruCache from \"lru-cache\";\n\nimport {\n  semverify,\n  semverMin,\n  isUnreleasedVersion,\n  getLowestUnreleased,\n  getHighestUnreleased,\n} from \"./utils\";\nimport { OptionValidator } from \"@babel/helper-validator-option\";\nimport { browserNameMap } from \"./targets\";\nimport { TargetNames } from \"./options\";\nimport type {\n  Target,\n  Targets,\n  InputTargets,\n  Browsers,\n  BrowserslistBrowserName,\n  TargetsTuple,\n} from \"./types\";\n\nexport type { Target, Targets, InputTargets };\n\nexport { prettifyTargets } from \"./pretty\";\nexport { getInclusionReasons } from \"./debug\";\nexport { default as filterItems, isRequired } from \"./filter-items\";\nexport { unreleasedLabels } from \"./targets\";\nexport { TargetNames };\n\nconst ESM_SUPPORT = browserModulesData[\"es6.module\"];\n\ndeclare const PACKAGE_JSON: { name: string; version: string };\nconst v = new OptionValidator(PACKAGE_JSON.name);\n\nfunction validateTargetNames(targets: Targets): TargetsTuple {\n  const validTargets = Object.keys(TargetNames);\n  for (const target of Object.keys(targets)) {\n    if (!(target in TargetNames)) {\n      throw new Error(\n        v.formatMessage(`'${target}' is not a valid target\n- Did you mean '${findSuggestion(target, validTargets)}'?`),\n      );\n    }\n  }\n\n  return targets;\n}\n\nexport function isBrowsersQueryValid(browsers: unknown): boolean {\n  return (\n    typeof browsers === \"string\" ||\n    (Array.isArray(browsers) && browsers.every(b => typeof b === \"string\"))\n  );\n}\n\nfunction validateBrowsers(browsers: Browsers | undefined) {\n  v.invariant(\n    browsers === undefined || isBrowsersQueryValid(browsers),\n    `'${String(browsers)}' is not a valid browserslist query`,\n  );\n\n  return browsers;\n}\n\nfunction getLowestVersions(browsers: Array<string>): Targets {\n  return browsers.reduce((all, browser) => {\n    const [browserName, browserVersion] = browser.split(\" \") as [\n      BrowserslistBrowserName,\n      string,\n    ];\n    const target = browserNameMap[browserName];\n\n    if (!target) {\n      return all;\n    }\n\n    try {\n      // Browser version can return as \"10.0-10.2\"\n      const splitVersion = browserVersion.split(\"-\")[0].toLowerCase();\n      const isSplitUnreleased = isUnreleasedVersion(splitVersion, target);\n\n      if (!all[target]) {\n        all[target] = isSplitUnreleased\n          ? splitVersion\n          : semverify(splitVersion);\n        return all;\n      }\n\n      const version = all[target];\n      const isUnreleased = isUnreleasedVersion(version, target);\n\n      if (isUnreleased && isSplitUnreleased) {\n        all[target] = getLowestUnreleased(version, splitVersion, target);\n      } else if (isUnreleased) {\n        all[target] = semverify(splitVersion);\n      } else if (!isUnreleased && !isSplitUnreleased) {\n        const parsedBrowserVersion = semverify(splitVersion);\n\n        all[target] = semverMin(version, parsedBrowserVersion);\n      }\n    } catch (e) {}\n\n    return all;\n  }, {} as Record<Target, string>);\n}\n\nfunction outputDecimalWarning(\n  decimalTargets: Array<{ target: string; value: number }>,\n) {\n  if (!decimalTargets.length) {\n    return;\n  }\n\n  console.warn(\"Warning, the following targets are using a decimal version:\\n\");\n  decimalTargets.forEach(({ target, value }) =>\n    console.warn(`  ${target}: ${value}`),\n  );\n  console.warn(`\nWe recommend using a string for minor/patch versions to avoid numbers like 6.10\ngetting parsed as 6.1, which can lead to unexpected behavior.\n`);\n}\n\nfunction semverifyTarget(target: Target, value: string) {\n  try {\n    return semverify(value);\n  } catch (error) {\n    throw new Error(\n      v.formatMessage(\n        `'${value}' is not a valid value for 'targets.${target}'.`,\n      ),\n    );\n  }\n}\n\n// Parse `node: true` and `node: \"current\"` to version\nfunction nodeTargetParser(value: true | string) {\n  const parsed =\n    value === true || value === \"current\"\n      ? process.versions.node\n      : semverifyTarget(\"node\", value);\n  return [\"node\", parsed] as const;\n}\n\nfunction defaultTargetParser(\n  target: Exclude<Target, \"node\">,\n  value: string,\n): readonly [Exclude<Target, \"node\">, string] {\n  const version = isUnreleasedVersion(value, target)\n    ? value.toLowerCase()\n    : semverifyTarget(target, value);\n  return [target, version] as const;\n}\n\nfunction generateTargets(inputTargets: InputTargets): Targets {\n  const input = { ...inputTargets };\n  delete input.esmodules;\n  delete input.browsers;\n  return input;\n}\n\nfunction resolveTargets(queries: Browsers, env?: string): Targets {\n  const resolved = browserslist(queries, {\n    mobileToDesktop: true,\n    env,\n  });\n  return getLowestVersions(resolved);\n}\n\nconst targetsCache = new LruCache({ max: 64 });\n\nfunction resolveTargetsCached(queries: Browsers, env?: string): Targets {\n  const cacheKey = typeof queries === \"string\" ? queries : queries.join() + env;\n  let cached = targetsCache.get(cacheKey) as Targets | undefined;\n  if (!cached) {\n    cached = resolveTargets(queries, env);\n    targetsCache.set(cacheKey, cached);\n  }\n  return { ...cached };\n}\n\ntype GetTargetsOption = {\n  // This is not the path of the config file, but the path where start searching it from\n  configPath?: string;\n  // The path of the config file\n  configFile?: string;\n  // The env to pass to browserslist\n  browserslistEnv?: string;\n  // true to disable config loading\n  ignoreBrowserslistConfig?: boolean;\n};\n\nexport default function getTargets(\n  inputTargets: InputTargets = {},\n  options: GetTargetsOption = {},\n): Targets {\n  let { browsers, esmodules } = inputTargets;\n  const { configPath = \".\" } = options;\n\n  validateBrowsers(browsers);\n\n  const input = generateTargets(inputTargets);\n  let targets = validateTargetNames(input);\n\n  const shouldParseBrowsers = !!browsers;\n  const hasTargets = shouldParseBrowsers || Object.keys(targets).length > 0;\n  const shouldSearchForConfig =\n    !options.ignoreBrowserslistConfig && !hasTargets;\n\n  if (!browsers && shouldSearchForConfig) {\n    browsers = browserslist.loadConfig({\n      config: options.configFile,\n      path: configPath,\n      env: options.browserslistEnv,\n    });\n    if (browsers == null) {\n      if (process.env.BABEL_8_BREAKING) {\n        // In Babel 8, if no targets are passed, we use browserslist's defaults\n        // and exclude IE 11.\n        browsers = [\"defaults, not ie 11\"];\n      } else {\n        // If no targets are passed, we need to overwrite browserslist's defaults\n        // so that we enable all transforms (acting like the now deprecated\n        // preset-latest).\n        browsers = [];\n      }\n    }\n  }\n\n  // `esmodules` as a target indicates the specific set of browsers supporting ES Modules.\n  // These values OVERRIDE the `browsers` field.\n  if (esmodules && (esmodules !== \"intersect\" || !browsers?.length)) {\n    browsers = Object.keys(ESM_SUPPORT)\n      .map(\n        (browser: keyof typeof ESM_SUPPORT) =>\n          `${browser} >= ${ESM_SUPPORT[browser]}`,\n      )\n      .join(\", \");\n    esmodules = false;\n  }\n\n  // If current value of `browsers` is undefined (`ignoreBrowserslistConfig` should be `false`)\n  // or an empty array (without any user config, use default config),\n  // we don't need to call `resolveTargets` to execute the related methods of `browserslist` library.\n  if (browsers?.length) {\n    const queryBrowsers = resolveTargetsCached(\n      browsers,\n      options.browserslistEnv,\n    );\n\n    if (esmodules === \"intersect\") {\n      for (const browser of Object.keys(queryBrowsers) as Target[]) {\n        const version = queryBrowsers[browser];\n        const esmSupportVersion =\n          // @ts-expect-error ie is not in ESM_SUPPORT\n          ESM_SUPPORT[browser];\n\n        if (esmSupportVersion) {\n          queryBrowsers[browser] = getHighestUnreleased(\n            version,\n            semverify(esmSupportVersion),\n            browser,\n          );\n        } else {\n          delete queryBrowsers[browser];\n        }\n      }\n    }\n\n    targets = Object.assign(queryBrowsers, targets);\n  }\n\n  // Parse remaining targets\n  const result: Targets = {};\n  const decimalWarnings = [];\n  for (const target of Object.keys(targets).sort() as Target[]) {\n    const value = targets[target];\n\n    // Warn when specifying minor/patch as a decimal\n    if (typeof value === \"number\" && value % 1 !== 0) {\n      decimalWarnings.push({ target, value });\n    }\n\n    const [parsedTarget, parsedValue] =\n      target === \"node\"\n        ? nodeTargetParser(value)\n        : defaultTargetParser(target, value as string);\n\n    if (parsedValue) {\n      // Merge (lowest wins)\n      result[parsedTarget] = parsedValue;\n    }\n  }\n\n  outputDecimalWarning(decimalWarnings);\n\n  return result;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AAEA;AAQA;AACA;AAYA;AACA;AACA;AAIA,MAAMA,WAAW,GAAGC,cAAkB,CAAC,YAAY,CAAC;AAGpD,MAAMC,CAAC,GAAG,IAAIC,sCAAe,qCAAmB;AAEhD,SAASC,mBAAmB,CAACC,OAAgB,EAAgB;EAC3D,MAAMC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACC,oBAAW,CAAC;EAC7C,KAAK,MAAMC,MAAM,IAAIH,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,EAAE;IACzC,IAAI,EAAEK,MAAM,IAAID,oBAAW,CAAC,EAAE;MAC5B,MAAM,IAAIE,KAAK,CACbT,CAAC,CAACU,aAAa,CAAE,IAAGF,MAAO;AACnC,kBAAkB,IAAAG,qCAAc,EAACH,MAAM,EAAEJ,YAAY,CAAE,IAAG,CAAC,CACpD;IACH;EACF;EAEA,OAAOD,OAAO;AAChB;AAEO,SAASS,oBAAoB,CAACC,QAAiB,EAAW;EAC/D,OACE,OAAOA,QAAQ,KAAK,QAAQ,IAC3BC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,IAAIA,QAAQ,CAACG,KAAK,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,CAAE;AAE3E;AAEA,SAASC,gBAAgB,CAACL,QAA8B,EAAE;EACxDb,CAAC,CAACmB,SAAS,CACTN,QAAQ,KAAKO,SAAS,IAAIR,oBAAoB,CAACC,QAAQ,CAAC,EACvD,IAAGQ,MAAM,CAACR,QAAQ,CAAE,qCAAoC,CAC1D;EAED,OAAOA,QAAQ;AACjB;AAEA,SAASS,iBAAiB,CAACT,QAAuB,EAAW;EAC3D,OAAOA,QAAQ,CAACU,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;IACvC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGF,OAAO,CAACG,KAAK,CAAC,GAAG,CAGtD;IACD,MAAMpB,MAAM,GAAGqB,uBAAc,CAACH,WAAW,CAAC;IAE1C,IAAI,CAAClB,MAAM,EAAE;MACX,OAAOgB,GAAG;IACZ;IAEA,IAAI;MAEF,MAAMM,YAAY,GAAGH,cAAc,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACG,WAAW,EAAE;MAC/D,MAAMC,iBAAiB,GAAG,IAAAC,0BAAmB,EAACH,YAAY,EAAEtB,MAAM,CAAC;MAEnE,IAAI,CAACgB,GAAG,CAAChB,MAAM,CAAC,EAAE;QAChBgB,GAAG,CAAChB,MAAM,CAAC,GAAGwB,iBAAiB,GAC3BF,YAAY,GACZ,IAAAI,gBAAS,EAACJ,YAAY,CAAC;QAC3B,OAAON,GAAG;MACZ;MAEA,MAAMW,OAAO,GAAGX,GAAG,CAAChB,MAAM,CAAC;MAC3B,MAAM4B,YAAY,GAAG,IAAAH,0BAAmB,EAACE,OAAO,EAAE3B,MAAM,CAAC;MAEzD,IAAI4B,YAAY,IAAIJ,iBAAiB,EAAE;QACrCR,GAAG,CAAChB,MAAM,CAAC,GAAG,IAAA6B,0BAAmB,EAACF,OAAO,EAAEL,YAAY,EAAEtB,MAAM,CAAC;MAClE,CAAC,MAAM,IAAI4B,YAAY,EAAE;QACvBZ,GAAG,CAAChB,MAAM,CAAC,GAAG,IAAA0B,gBAAS,EAACJ,YAAY,CAAC;MACvC,CAAC,MAAM,IAAI,CAACM,YAAY,IAAI,CAACJ,iBAAiB,EAAE;QAC9C,MAAMM,oBAAoB,GAAG,IAAAJ,gBAAS,EAACJ,YAAY,CAAC;QAEpDN,GAAG,CAAChB,MAAM,CAAC,GAAG,IAAA+B,gBAAS,EAACJ,OAAO,EAAEG,oBAAoB,CAAC;MACxD;IACF,CAAC,CAAC,OAAOE,CAAC,EAAE,CAAC;IAEb,OAAOhB,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAA2B;AAClC;AAEA,SAASiB,oBAAoB,CAC3BC,cAAwD,EACxD;EACA,IAAI,CAACA,cAAc,CAACC,MAAM,EAAE;IAC1B;EACF;EAEAC,OAAO,CAACC,IAAI,CAAC,+DAA+D,CAAC;EAC7EH,cAAc,CAACI,OAAO,CAAC,CAAC;IAAEtC,MAAM;IAAEuC;EAAM,CAAC,KACvCH,OAAO,CAACC,IAAI,CAAE,KAAIrC,MAAO,KAAIuC,KAAM,EAAC,CAAC,CACtC;EACDH,OAAO,CAACC,IAAI,CAAE;AAChB;AACA;AACA,CAAC,CAAC;AACF;AAEA,SAASG,eAAe,CAACxC,MAAc,EAAEuC,KAAa,EAAE;EACtD,IAAI;IACF,OAAO,IAAAb,gBAAS,EAACa,KAAK,CAAC;EACzB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,MAAM,IAAIxC,KAAK,CACbT,CAAC,CAACU,aAAa,CACZ,IAAGqC,KAAM,uCAAsCvC,MAAO,IAAG,CAC3D,CACF;EACH;AACF;;AAGA,SAAS0C,gBAAgB,CAACH,KAAoB,EAAE;EAC9C,MAAMI,MAAM,GACVJ,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,SAAS,GACjCK,OAAO,CAACC,QAAQ,CAACC,IAAI,GACrBN,eAAe,CAAC,MAAM,EAAED,KAAK,CAAC;EACpC,OAAO,CAAC,MAAM,EAAEI,MAAM,CAAC;AACzB;AAEA,SAASI,mBAAmB,CAC1B/C,MAA+B,EAC/BuC,KAAa,EAC+B;EAC5C,MAAMZ,OAAO,GAAG,IAAAF,0BAAmB,EAACc,KAAK,EAAEvC,MAAM,CAAC,GAC9CuC,KAAK,CAAChB,WAAW,EAAE,GACnBiB,eAAe,CAACxC,MAAM,EAAEuC,KAAK,CAAC;EAClC,OAAO,CAACvC,MAAM,EAAE2B,OAAO,CAAC;AAC1B;AAEA,SAASqB,eAAe,CAACC,YAA0B,EAAW;EAC5D,MAAMC,KAAK,qBAAQD,YAAY,CAAE;EACjC,OAAOC,KAAK,CAACC,SAAS;EACtB,OAAOD,KAAK,CAAC7C,QAAQ;EACrB,OAAO6C,KAAK;AACd;AAEA,SAASE,cAAc,CAACC,OAAiB,EAAEC,GAAY,EAAW;EAChE,MAAMC,QAAQ,GAAGC,aAAY,CAACH,OAAO,EAAE;IACrCI,eAAe,EAAE,IAAI;IACrBH;EACF,CAAC,CAAC;EACF,OAAOxC,iBAAiB,CAACyC,QAAQ,CAAC;AACpC;AAEA,MAAMG,YAAY,GAAG,IAAIC,SAAQ,CAAC;EAAEC,GAAG,EAAE;AAAG,CAAC,CAAC;AAE9C,SAASC,oBAAoB,CAACR,OAAiB,EAAEC,GAAY,EAAW;EACtE,MAAMQ,QAAQ,GAAG,OAAOT,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGA,OAAO,CAACU,IAAI,EAAE,GAAGT,GAAG;EAC7E,IAAIU,MAAM,GAAGN,YAAY,CAACO,GAAG,CAACH,QAAQ,CAAwB;EAC9D,IAAI,CAACE,MAAM,EAAE;IACXA,MAAM,GAAGZ,cAAc,CAACC,OAAO,EAAEC,GAAG,CAAC;IACrCI,YAAY,CAACQ,GAAG,CAACJ,QAAQ,EAAEE,MAAM,CAAC;EACpC;EACA,yBAAYA,MAAM;AACpB;AAae,SAASG,UAAU,CAChClB,YAA0B,GAAG,CAAC,CAAC,EAC/BmB,OAAyB,GAAG,CAAC,CAAC,EACrB;EAAA;EACT,IAAI;IAAE/D,QAAQ;IAAE8C;EAAU,CAAC,GAAGF,YAAY;EAC1C,MAAM;IAAEoB,UAAU,GAAG;EAAI,CAAC,GAAGD,OAAO;EAEpC1D,gBAAgB,CAACL,QAAQ,CAAC;EAE1B,MAAM6C,KAAK,GAAGF,eAAe,CAACC,YAAY,CAAC;EAC3C,IAAItD,OAAO,GAAGD,mBAAmB,CAACwD,KAAK,CAAC;EAExC,MAAMoB,mBAAmB,GAAG,CAAC,CAACjE,QAAQ;EACtC,MAAMkE,UAAU,GAAGD,mBAAmB,IAAIzE,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACwC,MAAM,GAAG,CAAC;EACzE,MAAMqC,qBAAqB,GACzB,CAACJ,OAAO,CAACK,wBAAwB,IAAI,CAACF,UAAU;EAElD,IAAI,CAAClE,QAAQ,IAAImE,qBAAqB,EAAE;IACtCnE,QAAQ,GAAGmD,aAAY,CAACkB,UAAU,CAAC;MACjCC,MAAM,EAAEP,OAAO,CAACQ,UAAU;MAC1BC,IAAI,EAAER,UAAU;MAChBf,GAAG,EAAEc,OAAO,CAACU;IACf,CAAC,CAAC;IACF,IAAIzE,QAAQ,IAAI,IAAI,EAAE;MAKb;QAILA,QAAQ,GAAG,EAAE;MACf;IACF;EACF;;EAIA,IAAI8C,SAAS,KAAKA,SAAS,KAAK,WAAW,IAAI,eAAC9C,QAAQ,aAAR,UAAU8B,MAAM,EAAC,EAAE;IACjE9B,QAAQ,GAAGR,MAAM,CAACC,IAAI,CAACR,WAAW,CAAC,CAChCyF,GAAG,CACD9D,OAAiC,IAC/B,GAAEA,OAAQ,OAAM3B,WAAW,CAAC2B,OAAO,CAAE,EAAC,CAC1C,CACA8C,IAAI,CAAC,IAAI,CAAC;IACbZ,SAAS,GAAG,KAAK;EACnB;;EAKA,kBAAI9C,QAAQ,aAAR,WAAU8B,MAAM,EAAE;IACpB,MAAM6C,aAAa,GAAGnB,oBAAoB,CACxCxD,QAAQ,EACR+D,OAAO,CAACU,eAAe,CACxB;IAED,IAAI3B,SAAS,KAAK,WAAW,EAAE;MAC7B,KAAK,MAAMlC,OAAO,IAAIpB,MAAM,CAACC,IAAI,CAACkF,aAAa,CAAC,EAAc;QAC5D,MAAMrD,OAAO,GAAGqD,aAAa,CAAC/D,OAAO,CAAC;QACtC,MAAMgE,iBAAiB;QAErB3F,WAAW,CAAC2B,OAAO,CAAC;QAEtB,IAAIgE,iBAAiB,EAAE;UACrBD,aAAa,CAAC/D,OAAO,CAAC,GAAG,IAAAiE,2BAAoB,EAC3CvD,OAAO,EACP,IAAAD,gBAAS,EAACuD,iBAAiB,CAAC,EAC5BhE,OAAO,CACR;QACH,CAAC,MAAM;UACL,OAAO+D,aAAa,CAAC/D,OAAO,CAAC;QAC/B;MACF;IACF;IAEAtB,OAAO,GAAGE,MAAM,CAACsF,MAAM,CAACH,aAAa,EAAErF,OAAO,CAAC;EACjD;;EAGA,MAAMyF,MAAe,GAAG,CAAC,CAAC;EAC1B,MAAMC,eAAe,GAAG,EAAE;EAC1B,KAAK,MAAMrF,MAAM,IAAIH,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAAC2F,IAAI,EAAE,EAAc;IAC5D,MAAM/C,KAAK,GAAG5C,OAAO,CAACK,MAAM,CAAC;;IAG7B,IAAI,OAAOuC,KAAK,KAAK,QAAQ,IAAIA,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;MAChD8C,eAAe,CAACE,IAAI,CAAC;QAAEvF,MAAM;QAAEuC;MAAM,CAAC,CAAC;IACzC;IAEA,MAAM,CAACiD,YAAY,EAAEC,WAAW,CAAC,GAC/BzF,MAAM,KAAK,MAAM,GACb0C,gBAAgB,CAACH,KAAK,CAAC,GACvBQ,mBAAmB,CAAC/C,MAAM,EAAEuC,KAAK,CAAW;IAElD,IAAIkD,WAAW,EAAE;MAEfL,MAAM,CAACI,YAAY,CAAC,GAAGC,WAAW;IACpC;EACF;EAEAxD,oBAAoB,CAACoD,eAAe,CAAC;EAErC,OAAOD,MAAM;AACf"}