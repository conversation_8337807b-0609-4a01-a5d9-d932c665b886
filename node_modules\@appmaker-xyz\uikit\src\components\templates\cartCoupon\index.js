import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
} from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import { Layout, AppmakerText, Button, Input } from '../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { useTranslation } from 'react-i18next';
import { BottomSheet } from '../../organisms/index';
import { Modal } from '@appmaker-xyz/uikit/src/index';
import { usePageState } from '@appmaker-xyz/core';
import { usePluginStore } from '@appmaker-xyz/core';
function getDiscountCodeApplication(couponDiscounted) {
  let discountCodeApplication;
  try {
    discountCodeApplication = couponDiscounted.find(
      (item) => item.node.__typename === 'DiscountCodeApplication',
    );
  } catch (error) {}
  return discountCodeApplication;
}
function useShopifyCoupon({ couponDiscounted, defaultEditMode }) {
  const discountCodeApplication = getDiscountCodeApplication(couponDiscounted);
  // console.log(discountCodeApplication);
  const [input, setInput] = useState(
    discountCodeApplication?.node?.applicable
      ? true
      : defaultEditMode
      ? true
      : false,
  );
  const [coupon, setCoupon] = useState(
    discountCodeApplication ? discountCodeApplication?.node?.code : '',
  );
  return {
    couponDiscounted,
    input,
    setInput,
    coupon,
    setCoupon,
    discountCodeApplication,
  };
}
const CartCoupon = ({ attributes, onAction, pageDispatch }) => {
  const { t } = useTranslation();
  let {
    // couponDiscounted,
    couponDiscountedLength,
    enableAutoApplyCoupon,
    autoApplyCoupon,
    multipleCoupons = false,
    autoFocus = false,
    __appmakerCustomStyles = {},
  } = attributes;
  // const couponDiscounted = getCouponDetails(attributes.couponDiscounted)
  const couponState = useShopifyCoupon({
    couponDiscounted: attributes.couponDiscounted,
    defaultEditMode: attributes.defaultEditMode,
  });
  const { input, setInput, coupon, setCoupon, discountCodeApplication } =
    couponState;
  const couponApplying = usePageState((state) => state.couponApplying);

  useEffect(() => {
    setButtonLoading(couponApplying);
  }, [couponApplying]);
  const [autoCouponApplied, setAutoCouponApplied] = useState(false);
  const [success, setSuccess] = useState(false);
  const [buttonLoading, setButtonLoading] = useState(false);
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing, input });

  const appOnlyCoupons = usePluginStore(
    (state) => state.plugins['app-only-coupons'],
  );
  useEffect(() => {
    if (
      autoCouponApplied === false &&
      autoApplyCoupon &&
      (enableAutoApplyCoupon == 'true' || enableAutoApplyCoupon == true)
    ) {
      if (parseFloat(couponDiscountedLength) < 1) {
        setCoupon(autoApplyCoupon);
        applyCoupon(autoApplyCoupon);
        setSuccess(true);
        setAutoCouponApplied(true);
      }
    }
    if (discountCodeApplication?.node?.applicable) {
      setAutoCouponApplied(true);
      setCoupon(discountCodeApplication?.node?.code || '');
      // setCoupon(
      //   couponDiscounted[0].coupon ||
      //     ('value' in couponDiscounted[0] && 'Coupon'),
      // );
      setSuccess(true);
    } // used because there is a slight delay in loading couponDiscounted from props. ie, couponDiscounted is undefined at first
    else {
      // couponDiscounted = [];
    }
  }, [discountCodeApplication, couponDiscountedLength]);

  const applyCoupon = async (value) => {
    Keyboard.dismiss();
    const action = {
      action: 'APPLY_COUPON',
      params: {
        coupon: value,
      },
    };
    setButtonLoading(true);
    try {
      const couponResp = await onAction(action);
      let couponDiscounted =
        couponResp?.discountApplications?.edges[0]?.node?.code ||
        couponResp?.discountCodes[0]?.coupon;
      !couponResp?.discountApplications?.edges[0]?.node?.applicable &&
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: t('Coupon not applicable'),
          },
        });
      pageDispatch({
        type: 'set_value',
        name: 'cartResponse',
        value: couponResp,
      });
      couponDiscounted && setSuccess(true);
      setButtonLoading(false);
      if (attributes?.gobackAfterApply && !couponResp?.error) {
        onAction({
          action: 'GO_BACK',
        });
      }
    } catch (e) {
      setButtonLoading(false);
    }
  };

  const removeCoupon = async (value) => {
    const action = {
      action: 'REMOVE_COUPON',
      params: {
        coupon: value,
      },
    };
    setButtonLoading(true);
    try {
      const couponResp = await onAction(action);
      pageDispatch({
        type: 'set_value',
        name: 'cartResponse',
        value: couponResp,
      });
      setAutoCouponApplied(true);
      setSuccess(false);
      setButtonLoading(false);
    } catch (e) {
      setSuccess(false);
      setButtonLoading(true);
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: t('Something went wrong, please try again'),
        },
      });
    }
  };
  if (discountCodeApplication?.node?.applicable) {
    let appliedCouponText = discountCodeApplication?.node
      ? discountCodeApplication?.node?.code.toUpperCase()
      : ('value' in discountCodeApplication?.node && 'Coupon') ||
        discountCodeApplication?.node.code;
    if (
      appOnlyCoupons?.settings &&
      appliedCouponText &&
      appOnlyCoupons?.settings?.couponPrefix &&
      appliedCouponText.startsWith(appOnlyCoupons?.settings?.couponPrefix) &&
      appliedCouponText.endsWith(appOnlyCoupons?.settings?.couponSufix)
    ) {
      appliedCouponText = appliedCouponText.replace(
        `_${appOnlyCoupons?.settings?.couponSufix}`,
        '',
      );
    }

    return (
      <Layout backgroundColor={color.white}>
        <Layout
          style={[styles.container, __appmakerCustomStyles?.success_container]}>
          <Layout flexDirection="row" alignItems="center">
            <Icon
              name="gift"
              size={20}
              style={[
                styles.icon,
                { backgroundColor: `${color.success}40` },
                __appmakerCustomStyles?.success_icon,
              ]}
            />
            <Layout>
              <AppmakerText style={__appmakerCustomStyles?.success_couponText}>
                Coupon applied{' '}
                <AppmakerText
                  category="pageSubHeading"
                  style={__appmakerCustomStyles?.success_couponText}>
                  {appliedCouponText}
                </AppmakerText>
              </AppmakerText>
              {discountCodeApplication?.node?.value?.percentage ? (
                <AppmakerText
                  category="highlighter1"
                  status="success"
                  style={__appmakerCustomStyles?.success_discountText}>
                  Coupon saving{' '}
                  {discountCodeApplication?.node?.value?.percentage} %
                </AppmakerText>
              ) : null}
            </Layout>
            {/* <AppmakerText category="actionTitle">
              {`${couponDiscounted[0].coupon} (${couponDiscounted[0].discount_display}) applied`}
            </AppmakerText> */}
          </Layout>
          {discountCodeApplication?.node?.code ? (
            <Button
              small
              status="white"
              loading={buttonLoading}
              onPress={() => removeCoupon(discountCodeApplication?.node?.code)}>
              <AppmakerText
                category="smallButtonText"
                status={__appmakerCustomStyles ? 'primary' : null}
                style={__appmakerCustomStyles?.success_removeCtaText}>
                Remove
              </AppmakerText>
            </Button>
          ) : null}
        </Layout>
      </Layout>
    );
  }
  // applyCoupon(coupon) is rest rest
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{}}
      keyboardVerticalOffset={80}>
      <Layout backgroundColor={color.white}>
        {!input ? (
          <Layout style={[styles.container, __appmakerCustomStyles?.container]}>
            <Layout flexDirection="row" alignItems="center">
              <Icon
                name="percent"
                size={20}
                style={[styles.icon, __appmakerCustomStyles?.icon]}
                color={color.primary}
              />
              <AppmakerText
                category="actionTitle"
                style={__appmakerCustomStyles?.text}>
                {multipleCoupons ? 'View Coupons' : 'Got a coupon?'}
              </AppmakerText>
            </Layout>
            <Button
              buttonLoading={buttonLoading}
              small
              status="white"
              onPress={() => setInput(true)}>
              <AppmakerText
                category="smallButtonText"
                status={__appmakerCustomStyles ? 'primary' : null}
                style={__appmakerCustomStyles?.ctaText}>
                {multipleCoupons ? 'View Coupons' : 'Apply Coupon'}
              </AppmakerText>
            </Button>
          </Layout>
        ) : (
          <Layout style={styles.containerTwo}>
            <Layout style={styles.inputContainer}>
              <Input
                label="Enter Coupon Code"
                leftIcon="gift"
                value={coupon}
                onChangeText={(text) => {
                  setCoupon(text);
                }}
                autoFocus={autoFocus}
                status={success ? 'success' : 'grey'}
                captionIcon={success && 'check'}
                caption={success && 'Coupon applied'}
                autoCapitalize={'none'}
              />
            </Layout>
            <Layout marginTop={spacing.small}>
              <Button
                loading={buttonLoading}
                baseSize
                status="dark"
                onPress={() => applyCoupon(coupon)}>
                Apply
              </Button>
            </Layout>
          </Layout>
        )}
      </Layout>
    </KeyboardAvoidingView>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      padding: spacing.base,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: color.white,
      justifyContent: 'space-between',
    },
    icon: {
      padding: spacing.small,
      backgroundColor: `${color.primary}40`,
      borderRadius: spacing.base * 2,
      marginRight: spacing.base,
      overflow: 'hidden',
      // transform: [{ rotate: '-45deg' }],
    },
    inputContainer: {
      flex: 1,
      marginRight: spacing.base,
    },
    containerTwo: {
      flexDirection: 'row',
      paddingHorizontal: spacing.base,
      paddingTop: spacing.base,
    },
  });

export default CartCoupon;
