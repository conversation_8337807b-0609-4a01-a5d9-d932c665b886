import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Text,
  ActivityIndicator,
} from 'react-native';
import FalsyFC from './devSupport/falsyFC.component';
import { testProps } from '@appmaker-xyz/core';
import { useTranslation } from 'react-i18next';
import { color, spacing, font, fonts } from './UiKitStyles';

const UiKitButton = ({
  onPress = () => {},
  accessoryLeft,
  accessoryRight,
  children = '',
  outline = false,
  small = false,
  baseSize = false,
  link = false,
  status,
  icon,
  block,
  style,
  accessibilityLabel,
  loading = false,
  clientId,
  wholeContainerStyle,
  __appmakerCustomStyles = {},
  fontColor,
  activeOpacity = 0.6,
  testId,
  ...props
}) => {
  const { t } = useTranslation();

  const containerStyles = [styles.container];
  const textStyles = [styles.text];
  let indicatorColor = color.white;

  if (outline) {
    containerStyles.push({ borderColor: color[status] });
    textStyles.push({ color: color[status] });
    indicatorColor = color[status];
  }
  if (baseSize) {
    containerStyles.push({ padding: spacing.base });
  }
  if (small) {
    containerStyles.push(styles.containerSmall);
    textStyles.push(styles.textSmall);
    if (outline) {
      textStyles.push({ color: color[status] });
    }
  }
  if (block) {
    containerStyles.push({ width: '100%' });
  }
  if (status && color[status]) {
    containerStyles.push({ backgroundColor: color[status] });
    if (!small) {
      containerStyles.push({ borderColor: color[status] });
    }
    if (status === 'light' || status === 'white') {
      textStyles.push({ color: color.dark });
      indicatorColor = color.demiDark;
    }
    if (outline) {
      containerStyles.push(styles.containerOutline);
    }
  }

  if (props.disabled === true) {
    textStyles.push({ opacity: 0.6 });
  }

  if (link) {
    containerStyles.push(styles.linkButton);
    textStyles.push({ color: color[status], textDecorationLine: 'underline' });
  }
  if (fontColor) {
    textStyles.push({ color: fontColor });
    textStyles.push(__appmakerCustomStyles?.text);
  }
  if (__appmakerCustomStyles?.text) {
    indicatorColor = __appmakerCustomStyles?.text?.color;
  }

  const ButtonContent = () => {
    if (typeof children === 'string') {
      return (
        <Text style={textStyles} {...testProps(clientId)}>
          {accessoryLeft && children ? ' ' : null}
          {children && t(children)}
          {accessoryRight && children ? ' ' : null}
        </Text>
      );
    } else {
      return (
        <View {...testProps(clientId)} style={{ ...style }}>
          {children}
        </View>
      );
    }
  };
  return (
    <TouchableOpacity
      disabled={props.disabled}
      activeOpacity={activeOpacity}
      style={[
        containerStyles,
        wholeContainerStyle,
        __appmakerCustomStyles?.container,
      ]}
      accessibilityLabel={accessibilityLabel}
      onPress={onPress}
      {...testProps(testId)}>
      {loading ? (
        <ActivityIndicator color={indicatorColor} size="small" />
      ) : (
        <>
          <FalsyFC
            component={accessoryLeft}
            size={small ? font.size.small : font.size.lg}
          />
          <ButtonContent />
          <FalsyFC
            component={accessoryRight}
            size={small ? font.size.small : font.size.lg}
          />
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: spacing.small,
    backgroundColor: color.primary,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.md,
    borderColor: 'transparent',
    borderWidth: 1,
    flexDirection: 'row',
  },
  containerOutline: {
    backgroundColor: 'transparent',
  },
  containerSmall: {
    borderRadius: spacing.mini,
    padding: spacing.small,
  },
  text: {
    ...fonts.buttonText,
    color: color.white,
  },
  textSmall: {
    ...fonts.smallButtonText,
    color: color.white,
  },
  linkButton: {
    padding: 0,
    backgroundColor: 'transparent',
    borderWidth: 0,
  },
});

export default UiKitButton;
