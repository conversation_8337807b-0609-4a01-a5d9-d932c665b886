import * as React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { TabView, SceneMap, TabBar } from 'react-native-tab-view';
import { Layout } from '../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';

const FirstRoute = () => (
  <View style={[styles.scene, { backgroundColor: '#ff4081' }]} />
);

const SecondRoute = () => (
  <View style={[styles.scene, { backgroundColor: '#673ab7' }]} />
);

const initialLayout = { width: Dimensions.get('window').width };

export default function TabTest() {
  const [index, setIndex] = React.useState(0);
  const [routes] = React.useState([
    { key: 'first', title: 'First' },
    { key: 'second', title: 'Second' },
    { key: 'second', title: 'Second' },
    { key: 'second', title: 'Second' },
    { key: 'second', title: 'Second' },
  ]);

  const renderScene = SceneMap({
    first: FirstRoute,
    second: SecondRoute,
  });

  const tabBar = (props) => (
    <TabBar
      {...props}
      renderIcon={({ route, focused, color }) => (
        <Icon name={focused ? 'x' : 'bell'} size={16} color={color.dark} />
      )}
    />
  );

  return (
    <Layout style={styles.scene}>
      <TabView
        navigationState={{ index, routes }}
        renderScene={renderScene}
        onIndexChange={setIndex}
        renderTabBar={tabBar}
        initialLayout={initialLayout}
        tabBarPosition="bottom"
      />
    </Layout>
  );
}

const styles = StyleSheet.create({
  scene: {
    flex: 1,
  },
});
