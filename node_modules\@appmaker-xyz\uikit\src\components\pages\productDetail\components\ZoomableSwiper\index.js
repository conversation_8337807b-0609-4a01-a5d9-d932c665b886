import React, { useRef, useEffect, useState } from 'react';
import { StyleSheet, Text, Dimensions, View, SafeAreaView } from 'react-native';
import { useApThemeState } from '../../../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import {
  Layout,
  AppImage,
  AppTouchable,
  AppmakerText,
} from '../../../../../components';
import { ImageViewer } from '@appmaker-xyz/uikit/Views';

const { width, height } = Dimensions.get('window');

const ImageSwiper = ({ attributes, onPress, onAction, ...props }) => {
  const { appmakerAction } = attributes;
  const { color, spacing } = useApThemeState();

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  const imageIndex = props.currentAction?.params?.index;
  const imageList = props.currentAction?.params?.imageList;
  let images = [];
  imageList &&
    imageList.map((value, key) => {
      images.push({
        url: value,
        key: key,
        props: {
          width,
          height,
        },
      });
      return images;
    });
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <View style={{ width, height }}>
        <View
          style={{
            backgroundColor: 'black',
            flexDirection: 'row',
            justifyContent: 'flex-end',
          }}>
          <Icon
            name="x"
            size={30}
            style={{
              marginTop: 30,
              marginRight: 20,
            }}
            onPress={() => {
              onAction({ action: 'GO_BACK' });
            }}
            color="#ffffff"
          />
        </View>
        <ImageViewer
          imageUrls={images}
          saveToLocalByLongPress={false}
          useNativeDriver={true}
          index={imageIndex ? imageIndex : 0}
          onCancel={() => onAction({ action: 'GO_BACK' })}
          enableSwipeDown
        />
      </View>
    </SafeAreaView>
  );
};

export default ImageSwiper;
