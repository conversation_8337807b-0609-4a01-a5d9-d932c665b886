{"version": 3, "names": ["_validateNode", "require", "_deprecationWarning", "arrayExpression", "elements", "validateNode", "type", "assignmentExpression", "operator", "left", "right", "binaryExpression", "interpreterDirective", "value", "directive", "directiveLiteral", "blockStatement", "body", "directives", "breakStatement", "label", "callExpression", "callee", "_arguments", "arguments", "catch<PERSON><PERSON><PERSON>", "param", "conditionalExpression", "test", "consequent", "alternate", "continueStatement", "debuggerStatement", "doWhileStatement", "emptyStatement", "expressionStatement", "expression", "file", "program", "comments", "tokens", "forInStatement", "forStatement", "init", "update", "functionDeclaration", "id", "params", "generator", "async", "functionExpression", "identifier", "name", "ifStatement", "labeledStatement", "stringLiteral", "numericLiteral", "nullLiteral", "booleanLiteral", "regExpLiteral", "pattern", "flags", "logicalExpression", "memberExpression", "object", "property", "computed", "optional", "newExpression", "sourceType", "interpreter", "sourceFile", "objectExpression", "properties", "objectMethod", "kind", "key", "objectProperty", "shorthand", "decorators", "restElement", "argument", "returnStatement", "sequenceExpression", "expressions", "parenthesizedExpression", "switchCase", "switchStatement", "discriminant", "cases", "thisExpression", "throwStatement", "tryStatement", "block", "handler", "finalizer", "unaryExpression", "prefix", "updateExpression", "variableDeclaration", "declarations", "variableDeclarator", "whileStatement", "withStatement", "assignmentPattern", "arrayPattern", "arrowFunctionExpression", "classBody", "classExpression", "superClass", "classDeclaration", "exportAllDeclaration", "source", "exportDefaultDeclaration", "declaration", "exportNamedDeclaration", "specifiers", "exportSpecifier", "local", "exported", "forOfStatement", "_await", "await", "importDeclaration", "importDefaultSpecifier", "importNamespaceSpecifier", "importSpecifier", "imported", "metaProperty", "meta", "classMethod", "_static", "static", "objectPattern", "spreadElement", "_super", "taggedTemplateExpression", "tag", "quasi", "templateElement", "tail", "templateLiteral", "quasis", "yieldExpression", "delegate", "awaitExpression", "_import", "bigIntLiteral", "exportNamespaceSpecifier", "optionalMemberExpression", "optionalCallExpression", "classProperty", "typeAnnotation", "classAccessorProperty", "classPrivateProperty", "classPrivateMethod", "privateName", "staticBlock", "anyTypeAnnotation", "arrayTypeAnnotation", "elementType", "booleanTypeAnnotation", "booleanLiteralTypeAnnotation", "nullLiteralTypeAnnotation", "classImplements", "typeParameters", "declareClass", "_extends", "extends", "declareFunction", "declareInterface", "declareModule", "declareModuleExports", "declareTypeAlias", "declareOpaqueType", "supertype", "declareVariable", "declareExportDeclaration", "declareExportAllDeclaration", "declaredPredicate", "existsTypeAnnotation", "functionTypeAnnotation", "rest", "returnType", "functionTypeParam", "genericTypeAnnotation", "inferredPredicate", "interfaceExtends", "interfaceDeclaration", "interfaceTypeAnnotation", "intersectionTypeAnnotation", "types", "mixedTypeAnnotation", "emptyTypeAnnotation", "nullableTypeAnnotation", "numberLiteralTypeAnnotation", "numberTypeAnnotation", "objectTypeAnnotation", "indexers", "callProperties", "internalSlots", "exact", "objectTypeInternalSlot", "method", "objectTypeCallProperty", "objectTypeIndexer", "variance", "objectTypeProperty", "proto", "objectTypeSpreadProperty", "opaqueType", "impltype", "qualifiedTypeIdentifier", "qualification", "stringLiteralTypeAnnotation", "stringTypeAnnotation", "symbolTypeAnnotation", "thisTypeAnnotation", "tupleTypeAnnotation", "typeofTypeAnnotation", "typeAlias", "typeCastExpression", "typeParameter", "bound", "_default", "default", "typeParameterDeclaration", "typeParameterInstantiation", "unionTypeAnnotation", "voidTypeAnnotation", "enumDeclaration", "enumBooleanBody", "members", "explicitType", "hasUnknownMembers", "enumNumberBody", "enumStringBody", "enumSymbolBody", "enumBooleanMember", "enumNumberMember", "enumStringMember", "enumDefaultedMember", "indexedAccessType", "objectType", "indexType", "optionalIndexedAccessType", "jsxAttribute", "jsxClosingElement", "jsxElement", "openingElement", "closingElement", "children", "selfClosing", "jsxEmptyExpression", "jsxExpressionContainer", "jsxSpreadChild", "jsxIdentifier", "jsxMemberExpression", "jsxNamespacedName", "namespace", "jsxOpeningElement", "attributes", "jsxSpreadAttribute", "jsxText", "jsxFragment", "openingFragment", "closingFragment", "jsxOpeningFragment", "jsxClosingFragment", "noop", "placeholder", "expectedNode", "v8IntrinsicIdentifier", "argumentPlaceholder", "bindExpression", "importAttribute", "decorator", "doExpression", "exportDefaultSpecifier", "recordExpression", "tupleExpression", "decimalLiteral", "moduleExpression", "topicReference", "pipelineTopicExpression", "pipelineBareFunction", "pipelinePrimaryTopicReference", "tsParameterProperty", "parameter", "tsDeclareFunction", "tsDeclareMethod", "tsQualifiedName", "tsCallSignatureDeclaration", "parameters", "tsConstructSignatureDeclaration", "tsPropertySignature", "initializer", "tsMethodSignature", "tsIndexSignature", "tsAnyKeyword", "tsBooleanKeyword", "tsBigIntKeyword", "tsIntrinsicKeyword", "tsNeverKeyword", "tsNullKeyword", "tsNumberKeyword", "tsObjectKeyword", "tsStringKeyword", "tsSymbolKeyword", "tsUndefinedKeyword", "tsUnknownKeyword", "tsVoidKeyword", "tsThisType", "tsFunctionType", "tsConstructorType", "tsTypeReference", "typeName", "tsTypePredicate", "parameterName", "asserts", "tsTypeQuery", "exprName", "tsType<PERSON><PERSON>al", "tsArrayType", "tsTupleType", "elementTypes", "tsOptionalType", "tsRestType", "tsNamedTupleMember", "tsUnionType", "tsIntersectionType", "tsConditionalType", "checkType", "extendsType", "trueType", "falseType", "tsInferType", "tsParenthesizedType", "tsTypeOperator", "tsIndexedAccessType", "tsMappedType", "nameType", "tsLiteralType", "literal", "tsExpressionWithTypeArguments", "tsInterfaceDeclaration", "tsInterfaceBody", "tsTypeAliasDeclaration", "tsInstantiationExpression", "tsAsExpression", "tsSatisfiesExpression", "tsTypeAssertion", "tsEnumDeclaration", "tsEnumMember", "tsModuleDeclaration", "tsModuleBlock", "tsImportType", "qualifier", "tsImportEqualsDeclaration", "moduleReference", "isExport", "tsExternalModuleReference", "tsNonNullExpression", "tsExportAssignment", "tsNamespaceExportDeclaration", "tsTypeAnnotation", "tsTypeParameterInstantiation", "tsTypeParameterDeclaration", "tsTypeParameter", "constraint", "NumberLiteral", "deprecationWarning", "RegexLiteral", "RestProperty", "SpreadProperty"], "sources": ["../../../src/builders/generated/index.ts"], "sourcesContent": ["/*\n * This file is auto-generated! Do not modify it directly.\n * To re-generate run 'make build'\n */\nimport validateNode from \"../validateNode\";\nimport type * as t from \"../..\";\nimport deprecationWarning from \"../../utils/deprecationWarning\";\nexport function arrayExpression(\n  elements: Array<null | t.Expression | t.SpreadElement> = [],\n): t.ArrayExpression {\n  return validateNode<t.ArrayExpression>({\n    type: \"ArrayExpression\",\n    elements,\n  });\n}\nexport function assignmentExpression(\n  operator: string,\n  left: t.LVal,\n  right: t.Expression,\n): t.AssignmentExpression {\n  return validateNode<t.AssignmentExpression>({\n    type: \"AssignmentExpression\",\n    operator,\n    left,\n    right,\n  });\n}\nexport function binaryExpression(\n  operator:\n    | \"+\"\n    | \"-\"\n    | \"/\"\n    | \"%\"\n    | \"*\"\n    | \"**\"\n    | \"&\"\n    | \"|\"\n    | \">>\"\n    | \">>>\"\n    | \"<<\"\n    | \"^\"\n    | \"==\"\n    | \"===\"\n    | \"!=\"\n    | \"!==\"\n    | \"in\"\n    | \"instanceof\"\n    | \">\"\n    | \"<\"\n    | \">=\"\n    | \"<=\"\n    | \"|>\",\n  left: t.Expression | t.PrivateName,\n  right: t.Expression,\n): t.BinaryExpression {\n  return validateNode<t.BinaryExpression>({\n    type: \"BinaryExpression\",\n    operator,\n    left,\n    right,\n  });\n}\nexport function interpreterDirective(value: string): t.InterpreterDirective {\n  return validateNode<t.InterpreterDirective>({\n    type: \"InterpreterDirective\",\n    value,\n  });\n}\nexport function directive(value: t.DirectiveLiteral): t.Directive {\n  return validateNode<t.Directive>({\n    type: \"Directive\",\n    value,\n  });\n}\nexport function directiveLiteral(value: string): t.DirectiveLiteral {\n  return validateNode<t.DirectiveLiteral>({\n    type: \"DirectiveLiteral\",\n    value,\n  });\n}\nexport function blockStatement(\n  body: Array<t.Statement>,\n  directives: Array<t.Directive> = [],\n): t.BlockStatement {\n  return validateNode<t.BlockStatement>({\n    type: \"BlockStatement\",\n    body,\n    directives,\n  });\n}\nexport function breakStatement(\n  label: t.Identifier | null = null,\n): t.BreakStatement {\n  return validateNode<t.BreakStatement>({\n    type: \"BreakStatement\",\n    label,\n  });\n}\nexport function callExpression(\n  callee: t.Expression | t.Super | t.V8IntrinsicIdentifier,\n  _arguments: Array<\n    t.Expression | t.SpreadElement | t.JSXNamespacedName | t.ArgumentPlaceholder\n  >,\n): t.CallExpression {\n  return validateNode<t.CallExpression>({\n    type: \"CallExpression\",\n    callee,\n    arguments: _arguments,\n  });\n}\nexport function catchClause(\n  param:\n    | t.Identifier\n    | t.ArrayPattern\n    | t.ObjectPattern\n    | null\n    | undefined = null,\n  body: t.BlockStatement,\n): t.CatchClause {\n  return validateNode<t.CatchClause>({\n    type: \"CatchClause\",\n    param,\n    body,\n  });\n}\nexport function conditionalExpression(\n  test: t.Expression,\n  consequent: t.Expression,\n  alternate: t.Expression,\n): t.ConditionalExpression {\n  return validateNode<t.ConditionalExpression>({\n    type: \"ConditionalExpression\",\n    test,\n    consequent,\n    alternate,\n  });\n}\nexport function continueStatement(\n  label: t.Identifier | null = null,\n): t.ContinueStatement {\n  return validateNode<t.ContinueStatement>({\n    type: \"ContinueStatement\",\n    label,\n  });\n}\nexport function debuggerStatement(): t.DebuggerStatement {\n  return {\n    type: \"DebuggerStatement\",\n  };\n}\nexport function doWhileStatement(\n  test: t.Expression,\n  body: t.Statement,\n): t.DoWhileStatement {\n  return validateNode<t.DoWhileStatement>({\n    type: \"DoWhileStatement\",\n    test,\n    body,\n  });\n}\nexport function emptyStatement(): t.EmptyStatement {\n  return {\n    type: \"EmptyStatement\",\n  };\n}\nexport function expressionStatement(\n  expression: t.Expression,\n): t.ExpressionStatement {\n  return validateNode<t.ExpressionStatement>({\n    type: \"ExpressionStatement\",\n    expression,\n  });\n}\nexport function file(\n  program: t.Program,\n  comments: Array<t.CommentBlock | t.CommentLine> | null = null,\n  tokens: Array<any> | null = null,\n): t.File {\n  return validateNode<t.File>({\n    type: \"File\",\n    program,\n    comments,\n    tokens,\n  });\n}\nexport function forInStatement(\n  left: t.VariableDeclaration | t.LVal,\n  right: t.Expression,\n  body: t.Statement,\n): t.ForInStatement {\n  return validateNode<t.ForInStatement>({\n    type: \"ForInStatement\",\n    left,\n    right,\n    body,\n  });\n}\nexport function forStatement(\n  init: t.VariableDeclaration | t.Expression | null | undefined = null,\n  test: t.Expression | null | undefined = null,\n  update: t.Expression | null | undefined = null,\n  body: t.Statement,\n): t.ForStatement {\n  return validateNode<t.ForStatement>({\n    type: \"ForStatement\",\n    init,\n    test,\n    update,\n    body,\n  });\n}\nexport function functionDeclaration(\n  id: t.Identifier | null | undefined = null,\n  params: Array<t.Identifier | t.Pattern | t.RestElement>,\n  body: t.BlockStatement,\n  generator: boolean = false,\n  async: boolean = false,\n): t.FunctionDeclaration {\n  return validateNode<t.FunctionDeclaration>({\n    type: \"FunctionDeclaration\",\n    id,\n    params,\n    body,\n    generator,\n    async,\n  });\n}\nexport function functionExpression(\n  id: t.Identifier | null | undefined = null,\n  params: Array<t.Identifier | t.Pattern | t.RestElement>,\n  body: t.BlockStatement,\n  generator: boolean = false,\n  async: boolean = false,\n): t.FunctionExpression {\n  return validateNode<t.FunctionExpression>({\n    type: \"FunctionExpression\",\n    id,\n    params,\n    body,\n    generator,\n    async,\n  });\n}\nexport function identifier(name: string): t.Identifier {\n  return validateNode<t.Identifier>({\n    type: \"Identifier\",\n    name,\n  });\n}\nexport function ifStatement(\n  test: t.Expression,\n  consequent: t.Statement,\n  alternate: t.Statement | null = null,\n): t.IfStatement {\n  return validateNode<t.IfStatement>({\n    type: \"IfStatement\",\n    test,\n    consequent,\n    alternate,\n  });\n}\nexport function labeledStatement(\n  label: t.Identifier,\n  body: t.Statement,\n): t.LabeledStatement {\n  return validateNode<t.LabeledStatement>({\n    type: \"LabeledStatement\",\n    label,\n    body,\n  });\n}\nexport function stringLiteral(value: string): t.StringLiteral {\n  return validateNode<t.StringLiteral>({\n    type: \"StringLiteral\",\n    value,\n  });\n}\nexport function numericLiteral(value: number): t.NumericLiteral {\n  return validateNode<t.NumericLiteral>({\n    type: \"NumericLiteral\",\n    value,\n  });\n}\nexport function nullLiteral(): t.NullLiteral {\n  return {\n    type: \"NullLiteral\",\n  };\n}\nexport function booleanLiteral(value: boolean): t.BooleanLiteral {\n  return validateNode<t.BooleanLiteral>({\n    type: \"BooleanLiteral\",\n    value,\n  });\n}\nexport function regExpLiteral(\n  pattern: string,\n  flags: string = \"\",\n): t.RegExpLiteral {\n  return validateNode<t.RegExpLiteral>({\n    type: \"RegExpLiteral\",\n    pattern,\n    flags,\n  });\n}\nexport function logicalExpression(\n  operator: \"||\" | \"&&\" | \"??\",\n  left: t.Expression,\n  right: t.Expression,\n): t.LogicalExpression {\n  return validateNode<t.LogicalExpression>({\n    type: \"LogicalExpression\",\n    operator,\n    left,\n    right,\n  });\n}\nexport function memberExpression(\n  object: t.Expression | t.Super,\n  property: t.Expression | t.Identifier | t.PrivateName,\n  computed: boolean = false,\n  optional: true | false | null = null,\n): t.MemberExpression {\n  return validateNode<t.MemberExpression>({\n    type: \"MemberExpression\",\n    object,\n    property,\n    computed,\n    optional,\n  });\n}\nexport function newExpression(\n  callee: t.Expression | t.Super | t.V8IntrinsicIdentifier,\n  _arguments: Array<\n    t.Expression | t.SpreadElement | t.JSXNamespacedName | t.ArgumentPlaceholder\n  >,\n): t.NewExpression {\n  return validateNode<t.NewExpression>({\n    type: \"NewExpression\",\n    callee,\n    arguments: _arguments,\n  });\n}\nexport function program(\n  body: Array<t.Statement>,\n  directives: Array<t.Directive> = [],\n  sourceType: \"script\" | \"module\" = \"script\",\n  interpreter: t.InterpreterDirective | null = null,\n): t.Program {\n  return validateNode<t.Program>({\n    type: \"Program\",\n    body,\n    directives,\n    sourceType,\n    interpreter,\n    sourceFile: null,\n  });\n}\nexport function objectExpression(\n  properties: Array<t.ObjectMethod | t.ObjectProperty | t.SpreadElement>,\n): t.ObjectExpression {\n  return validateNode<t.ObjectExpression>({\n    type: \"ObjectExpression\",\n    properties,\n  });\n}\nexport function objectMethod(\n  kind: \"method\" | \"get\" | \"set\" | undefined = \"method\",\n  key:\n    | t.Expression\n    | t.Identifier\n    | t.StringLiteral\n    | t.NumericLiteral\n    | t.BigIntLiteral,\n  params: Array<t.Identifier | t.Pattern | t.RestElement>,\n  body: t.BlockStatement,\n  computed: boolean = false,\n  generator: boolean = false,\n  async: boolean = false,\n): t.ObjectMethod {\n  return validateNode<t.ObjectMethod>({\n    type: \"ObjectMethod\",\n    kind,\n    key,\n    params,\n    body,\n    computed,\n    generator,\n    async,\n  });\n}\nexport function objectProperty(\n  key:\n    | t.Expression\n    | t.Identifier\n    | t.StringLiteral\n    | t.NumericLiteral\n    | t.BigIntLiteral\n    | t.DecimalLiteral\n    | t.PrivateName,\n  value: t.Expression | t.PatternLike,\n  computed: boolean = false,\n  shorthand: boolean = false,\n  decorators: Array<t.Decorator> | null = null,\n): t.ObjectProperty {\n  return validateNode<t.ObjectProperty>({\n    type: \"ObjectProperty\",\n    key,\n    value,\n    computed,\n    shorthand,\n    decorators,\n  });\n}\nexport function restElement(argument: t.LVal): t.RestElement {\n  return validateNode<t.RestElement>({\n    type: \"RestElement\",\n    argument,\n  });\n}\nexport function returnStatement(\n  argument: t.Expression | null = null,\n): t.ReturnStatement {\n  return validateNode<t.ReturnStatement>({\n    type: \"ReturnStatement\",\n    argument,\n  });\n}\nexport function sequenceExpression(\n  expressions: Array<t.Expression>,\n): t.SequenceExpression {\n  return validateNode<t.SequenceExpression>({\n    type: \"SequenceExpression\",\n    expressions,\n  });\n}\nexport function parenthesizedExpression(\n  expression: t.Expression,\n): t.ParenthesizedExpression {\n  return validateNode<t.ParenthesizedExpression>({\n    type: \"ParenthesizedExpression\",\n    expression,\n  });\n}\nexport function switchCase(\n  test: t.Expression | null | undefined = null,\n  consequent: Array<t.Statement>,\n): t.SwitchCase {\n  return validateNode<t.SwitchCase>({\n    type: \"SwitchCase\",\n    test,\n    consequent,\n  });\n}\nexport function switchStatement(\n  discriminant: t.Expression,\n  cases: Array<t.SwitchCase>,\n): t.SwitchStatement {\n  return validateNode<t.SwitchStatement>({\n    type: \"SwitchStatement\",\n    discriminant,\n    cases,\n  });\n}\nexport function thisExpression(): t.ThisExpression {\n  return {\n    type: \"ThisExpression\",\n  };\n}\nexport function throwStatement(argument: t.Expression): t.ThrowStatement {\n  return validateNode<t.ThrowStatement>({\n    type: \"ThrowStatement\",\n    argument,\n  });\n}\nexport function tryStatement(\n  block: t.BlockStatement,\n  handler: t.CatchClause | null = null,\n  finalizer: t.BlockStatement | null = null,\n): t.TryStatement {\n  return validateNode<t.TryStatement>({\n    type: \"TryStatement\",\n    block,\n    handler,\n    finalizer,\n  });\n}\nexport function unaryExpression(\n  operator: \"void\" | \"throw\" | \"delete\" | \"!\" | \"+\" | \"-\" | \"~\" | \"typeof\",\n  argument: t.Expression,\n  prefix: boolean = true,\n): t.UnaryExpression {\n  return validateNode<t.UnaryExpression>({\n    type: \"UnaryExpression\",\n    operator,\n    argument,\n    prefix,\n  });\n}\nexport function updateExpression(\n  operator: \"++\" | \"--\",\n  argument: t.Expression,\n  prefix: boolean = false,\n): t.UpdateExpression {\n  return validateNode<t.UpdateExpression>({\n    type: \"UpdateExpression\",\n    operator,\n    argument,\n    prefix,\n  });\n}\nexport function variableDeclaration(\n  kind: \"var\" | \"let\" | \"const\" | \"using\",\n  declarations: Array<t.VariableDeclarator>,\n): t.VariableDeclaration {\n  return validateNode<t.VariableDeclaration>({\n    type: \"VariableDeclaration\",\n    kind,\n    declarations,\n  });\n}\nexport function variableDeclarator(\n  id: t.LVal,\n  init: t.Expression | null = null,\n): t.VariableDeclarator {\n  return validateNode<t.VariableDeclarator>({\n    type: \"VariableDeclarator\",\n    id,\n    init,\n  });\n}\nexport function whileStatement(\n  test: t.Expression,\n  body: t.Statement,\n): t.WhileStatement {\n  return validateNode<t.WhileStatement>({\n    type: \"WhileStatement\",\n    test,\n    body,\n  });\n}\nexport function withStatement(\n  object: t.Expression,\n  body: t.Statement,\n): t.WithStatement {\n  return validateNode<t.WithStatement>({\n    type: \"WithStatement\",\n    object,\n    body,\n  });\n}\nexport function assignmentPattern(\n  left:\n    | t.Identifier\n    | t.ObjectPattern\n    | t.ArrayPattern\n    | t.MemberExpression\n    | t.TSAsExpression\n    | t.TSSatisfiesExpression\n    | t.TSTypeAssertion\n    | t.TSNonNullExpression,\n  right: t.Expression,\n): t.AssignmentPattern {\n  return validateNode<t.AssignmentPattern>({\n    type: \"AssignmentPattern\",\n    left,\n    right,\n  });\n}\nexport function arrayPattern(\n  elements: Array<null | t.PatternLike | t.LVal>,\n): t.ArrayPattern {\n  return validateNode<t.ArrayPattern>({\n    type: \"ArrayPattern\",\n    elements,\n  });\n}\nexport function arrowFunctionExpression(\n  params: Array<t.Identifier | t.Pattern | t.RestElement>,\n  body: t.BlockStatement | t.Expression,\n  async: boolean = false,\n): t.ArrowFunctionExpression {\n  return validateNode<t.ArrowFunctionExpression>({\n    type: \"ArrowFunctionExpression\",\n    params,\n    body,\n    async,\n    expression: null,\n  });\n}\nexport function classBody(\n  body: Array<\n    | t.ClassMethod\n    | t.ClassPrivateMethod\n    | t.ClassProperty\n    | t.ClassPrivateProperty\n    | t.ClassAccessorProperty\n    | t.TSDeclareMethod\n    | t.TSIndexSignature\n    | t.StaticBlock\n  >,\n): t.ClassBody {\n  return validateNode<t.ClassBody>({\n    type: \"ClassBody\",\n    body,\n  });\n}\nexport function classExpression(\n  id: t.Identifier | null | undefined = null,\n  superClass: t.Expression | null | undefined = null,\n  body: t.ClassBody,\n  decorators: Array<t.Decorator> | null = null,\n): t.ClassExpression {\n  return validateNode<t.ClassExpression>({\n    type: \"ClassExpression\",\n    id,\n    superClass,\n    body,\n    decorators,\n  });\n}\nexport function classDeclaration(\n  id: t.Identifier,\n  superClass: t.Expression | null | undefined = null,\n  body: t.ClassBody,\n  decorators: Array<t.Decorator> | null = null,\n): t.ClassDeclaration {\n  return validateNode<t.ClassDeclaration>({\n    type: \"ClassDeclaration\",\n    id,\n    superClass,\n    body,\n    decorators,\n  });\n}\nexport function exportAllDeclaration(\n  source: t.StringLiteral,\n): t.ExportAllDeclaration {\n  return validateNode<t.ExportAllDeclaration>({\n    type: \"ExportAllDeclaration\",\n    source,\n  });\n}\nexport function exportDefaultDeclaration(\n  declaration:\n    | t.TSDeclareFunction\n    | t.FunctionDeclaration\n    | t.ClassDeclaration\n    | t.Expression,\n): t.ExportDefaultDeclaration {\n  return validateNode<t.ExportDefaultDeclaration>({\n    type: \"ExportDefaultDeclaration\",\n    declaration,\n  });\n}\nexport function exportNamedDeclaration(\n  declaration: t.Declaration | null = null,\n  specifiers: Array<\n    t.ExportSpecifier | t.ExportDefaultSpecifier | t.ExportNamespaceSpecifier\n  > = [],\n  source: t.StringLiteral | null = null,\n): t.ExportNamedDeclaration {\n  return validateNode<t.ExportNamedDeclaration>({\n    type: \"ExportNamedDeclaration\",\n    declaration,\n    specifiers,\n    source,\n  });\n}\nexport function exportSpecifier(\n  local: t.Identifier,\n  exported: t.Identifier | t.StringLiteral,\n): t.ExportSpecifier {\n  return validateNode<t.ExportSpecifier>({\n    type: \"ExportSpecifier\",\n    local,\n    exported,\n  });\n}\nexport function forOfStatement(\n  left: t.VariableDeclaration | t.LVal,\n  right: t.Expression,\n  body: t.Statement,\n  _await: boolean = false,\n): t.ForOfStatement {\n  return validateNode<t.ForOfStatement>({\n    type: \"ForOfStatement\",\n    left,\n    right,\n    body,\n    await: _await,\n  });\n}\nexport function importDeclaration(\n  specifiers: Array<\n    t.ImportSpecifier | t.ImportDefaultSpecifier | t.ImportNamespaceSpecifier\n  >,\n  source: t.StringLiteral,\n): t.ImportDeclaration {\n  return validateNode<t.ImportDeclaration>({\n    type: \"ImportDeclaration\",\n    specifiers,\n    source,\n  });\n}\nexport function importDefaultSpecifier(\n  local: t.Identifier,\n): t.ImportDefaultSpecifier {\n  return validateNode<t.ImportDefaultSpecifier>({\n    type: \"ImportDefaultSpecifier\",\n    local,\n  });\n}\nexport function importNamespaceSpecifier(\n  local: t.Identifier,\n): t.ImportNamespaceSpecifier {\n  return validateNode<t.ImportNamespaceSpecifier>({\n    type: \"ImportNamespaceSpecifier\",\n    local,\n  });\n}\nexport function importSpecifier(\n  local: t.Identifier,\n  imported: t.Identifier | t.StringLiteral,\n): t.ImportSpecifier {\n  return validateNode<t.ImportSpecifier>({\n    type: \"ImportSpecifier\",\n    local,\n    imported,\n  });\n}\nexport function metaProperty(\n  meta: t.Identifier,\n  property: t.Identifier,\n): t.MetaProperty {\n  return validateNode<t.MetaProperty>({\n    type: \"MetaProperty\",\n    meta,\n    property,\n  });\n}\nexport function classMethod(\n  kind: \"get\" | \"set\" | \"method\" | \"constructor\" | undefined = \"method\",\n  key:\n    | t.Identifier\n    | t.StringLiteral\n    | t.NumericLiteral\n    | t.BigIntLiteral\n    | t.Expression,\n  params: Array<\n    t.Identifier | t.Pattern | t.RestElement | t.TSParameterProperty\n  >,\n  body: t.BlockStatement,\n  computed: boolean = false,\n  _static: boolean = false,\n  generator: boolean = false,\n  async: boolean = false,\n): t.ClassMethod {\n  return validateNode<t.ClassMethod>({\n    type: \"ClassMethod\",\n    kind,\n    key,\n    params,\n    body,\n    computed,\n    static: _static,\n    generator,\n    async,\n  });\n}\nexport function objectPattern(\n  properties: Array<t.RestElement | t.ObjectProperty>,\n): t.ObjectPattern {\n  return validateNode<t.ObjectPattern>({\n    type: \"ObjectPattern\",\n    properties,\n  });\n}\nexport function spreadElement(argument: t.Expression): t.SpreadElement {\n  return validateNode<t.SpreadElement>({\n    type: \"SpreadElement\",\n    argument,\n  });\n}\nfunction _super(): t.Super {\n  return {\n    type: \"Super\",\n  };\n}\nexport { _super as super };\nexport function taggedTemplateExpression(\n  tag: t.Expression,\n  quasi: t.TemplateLiteral,\n): t.TaggedTemplateExpression {\n  return validateNode<t.TaggedTemplateExpression>({\n    type: \"TaggedTemplateExpression\",\n    tag,\n    quasi,\n  });\n}\nexport function templateElement(\n  value: { raw: string; cooked?: string },\n  tail: boolean = false,\n): t.TemplateElement {\n  return validateNode<t.TemplateElement>({\n    type: \"TemplateElement\",\n    value,\n    tail,\n  });\n}\nexport function templateLiteral(\n  quasis: Array<t.TemplateElement>,\n  expressions: Array<t.Expression | t.TSType>,\n): t.TemplateLiteral {\n  return validateNode<t.TemplateLiteral>({\n    type: \"TemplateLiteral\",\n    quasis,\n    expressions,\n  });\n}\nexport function yieldExpression(\n  argument: t.Expression | null = null,\n  delegate: boolean = false,\n): t.YieldExpression {\n  return validateNode<t.YieldExpression>({\n    type: \"YieldExpression\",\n    argument,\n    delegate,\n  });\n}\nexport function awaitExpression(argument: t.Expression): t.AwaitExpression {\n  return validateNode<t.AwaitExpression>({\n    type: \"AwaitExpression\",\n    argument,\n  });\n}\nfunction _import(): t.Import {\n  return {\n    type: \"Import\",\n  };\n}\nexport { _import as import };\nexport function bigIntLiteral(value: string): t.BigIntLiteral {\n  return validateNode<t.BigIntLiteral>({\n    type: \"BigIntLiteral\",\n    value,\n  });\n}\nexport function exportNamespaceSpecifier(\n  exported: t.Identifier,\n): t.ExportNamespaceSpecifier {\n  return validateNode<t.ExportNamespaceSpecifier>({\n    type: \"ExportNamespaceSpecifier\",\n    exported,\n  });\n}\nexport function optionalMemberExpression(\n  object: t.Expression,\n  property: t.Expression | t.Identifier,\n  computed: boolean | undefined = false,\n  optional: boolean,\n): t.OptionalMemberExpression {\n  return validateNode<t.OptionalMemberExpression>({\n    type: \"OptionalMemberExpression\",\n    object,\n    property,\n    computed,\n    optional,\n  });\n}\nexport function optionalCallExpression(\n  callee: t.Expression,\n  _arguments: Array<\n    t.Expression | t.SpreadElement | t.JSXNamespacedName | t.ArgumentPlaceholder\n  >,\n  optional: boolean,\n): t.OptionalCallExpression {\n  return validateNode<t.OptionalCallExpression>({\n    type: \"OptionalCallExpression\",\n    callee,\n    arguments: _arguments,\n    optional,\n  });\n}\nexport function classProperty(\n  key:\n    | t.Identifier\n    | t.StringLiteral\n    | t.NumericLiteral\n    | t.BigIntLiteral\n    | t.Expression,\n  value: t.Expression | null = null,\n  typeAnnotation: t.TypeAnnotation | t.TSTypeAnnotation | t.Noop | null = null,\n  decorators: Array<t.Decorator> | null = null,\n  computed: boolean = false,\n  _static: boolean = false,\n): t.ClassProperty {\n  return validateNode<t.ClassProperty>({\n    type: \"ClassProperty\",\n    key,\n    value,\n    typeAnnotation,\n    decorators,\n    computed,\n    static: _static,\n  });\n}\nexport function classAccessorProperty(\n  key:\n    | t.Identifier\n    | t.StringLiteral\n    | t.NumericLiteral\n    | t.BigIntLiteral\n    | t.Expression\n    | t.PrivateName,\n  value: t.Expression | null = null,\n  typeAnnotation: t.TypeAnnotation | t.TSTypeAnnotation | t.Noop | null = null,\n  decorators: Array<t.Decorator> | null = null,\n  computed: boolean = false,\n  _static: boolean = false,\n): t.ClassAccessorProperty {\n  return validateNode<t.ClassAccessorProperty>({\n    type: \"ClassAccessorProperty\",\n    key,\n    value,\n    typeAnnotation,\n    decorators,\n    computed,\n    static: _static,\n  });\n}\nexport function classPrivateProperty(\n  key: t.PrivateName,\n  value: t.Expression | null = null,\n  decorators: Array<t.Decorator> | null = null,\n  _static: boolean = false,\n): t.ClassPrivateProperty {\n  return validateNode<t.ClassPrivateProperty>({\n    type: \"ClassPrivateProperty\",\n    key,\n    value,\n    decorators,\n    static: _static,\n  });\n}\nexport function classPrivateMethod(\n  kind: \"get\" | \"set\" | \"method\" | undefined = \"method\",\n  key: t.PrivateName,\n  params: Array<\n    t.Identifier | t.Pattern | t.RestElement | t.TSParameterProperty\n  >,\n  body: t.BlockStatement,\n  _static: boolean = false,\n): t.ClassPrivateMethod {\n  return validateNode<t.ClassPrivateMethod>({\n    type: \"ClassPrivateMethod\",\n    kind,\n    key,\n    params,\n    body,\n    static: _static,\n  });\n}\nexport function privateName(id: t.Identifier): t.PrivateName {\n  return validateNode<t.PrivateName>({\n    type: \"PrivateName\",\n    id,\n  });\n}\nexport function staticBlock(body: Array<t.Statement>): t.StaticBlock {\n  return validateNode<t.StaticBlock>({\n    type: \"StaticBlock\",\n    body,\n  });\n}\nexport function anyTypeAnnotation(): t.AnyTypeAnnotation {\n  return {\n    type: \"AnyTypeAnnotation\",\n  };\n}\nexport function arrayTypeAnnotation(\n  elementType: t.FlowType,\n): t.ArrayTypeAnnotation {\n  return validateNode<t.ArrayTypeAnnotation>({\n    type: \"ArrayTypeAnnotation\",\n    elementType,\n  });\n}\nexport function booleanTypeAnnotation(): t.BooleanTypeAnnotation {\n  return {\n    type: \"BooleanTypeAnnotation\",\n  };\n}\nexport function booleanLiteralTypeAnnotation(\n  value: boolean,\n): t.BooleanLiteralTypeAnnotation {\n  return validateNode<t.BooleanLiteralTypeAnnotation>({\n    type: \"BooleanLiteralTypeAnnotation\",\n    value,\n  });\n}\nexport function nullLiteralTypeAnnotation(): t.NullLiteralTypeAnnotation {\n  return {\n    type: \"NullLiteralTypeAnnotation\",\n  };\n}\nexport function classImplements(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterInstantiation | null = null,\n): t.ClassImplements {\n  return validateNode<t.ClassImplements>({\n    type: \"ClassImplements\",\n    id,\n    typeParameters,\n  });\n}\nexport function declareClass(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterDeclaration | null | undefined = null,\n  _extends: Array<t.InterfaceExtends> | null | undefined = null,\n  body: t.ObjectTypeAnnotation,\n): t.DeclareClass {\n  return validateNode<t.DeclareClass>({\n    type: \"DeclareClass\",\n    id,\n    typeParameters,\n    extends: _extends,\n    body,\n  });\n}\nexport function declareFunction(id: t.Identifier): t.DeclareFunction {\n  return validateNode<t.DeclareFunction>({\n    type: \"DeclareFunction\",\n    id,\n  });\n}\nexport function declareInterface(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterDeclaration | null | undefined = null,\n  _extends: Array<t.InterfaceExtends> | null | undefined = null,\n  body: t.ObjectTypeAnnotation,\n): t.DeclareInterface {\n  return validateNode<t.DeclareInterface>({\n    type: \"DeclareInterface\",\n    id,\n    typeParameters,\n    extends: _extends,\n    body,\n  });\n}\nexport function declareModule(\n  id: t.Identifier | t.StringLiteral,\n  body: t.BlockStatement,\n  kind: \"CommonJS\" | \"ES\" | null = null,\n): t.DeclareModule {\n  return validateNode<t.DeclareModule>({\n    type: \"DeclareModule\",\n    id,\n    body,\n    kind,\n  });\n}\nexport function declareModuleExports(\n  typeAnnotation: t.TypeAnnotation,\n): t.DeclareModuleExports {\n  return validateNode<t.DeclareModuleExports>({\n    type: \"DeclareModuleExports\",\n    typeAnnotation,\n  });\n}\nexport function declareTypeAlias(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterDeclaration | null | undefined = null,\n  right: t.FlowType,\n): t.DeclareTypeAlias {\n  return validateNode<t.DeclareTypeAlias>({\n    type: \"DeclareTypeAlias\",\n    id,\n    typeParameters,\n    right,\n  });\n}\nexport function declareOpaqueType(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterDeclaration | null = null,\n  supertype: t.FlowType | null = null,\n): t.DeclareOpaqueType {\n  return validateNode<t.DeclareOpaqueType>({\n    type: \"DeclareOpaqueType\",\n    id,\n    typeParameters,\n    supertype,\n  });\n}\nexport function declareVariable(id: t.Identifier): t.DeclareVariable {\n  return validateNode<t.DeclareVariable>({\n    type: \"DeclareVariable\",\n    id,\n  });\n}\nexport function declareExportDeclaration(\n  declaration: t.Flow | null = null,\n  specifiers: Array<\n    t.ExportSpecifier | t.ExportNamespaceSpecifier\n  > | null = null,\n  source: t.StringLiteral | null = null,\n): t.DeclareExportDeclaration {\n  return validateNode<t.DeclareExportDeclaration>({\n    type: \"DeclareExportDeclaration\",\n    declaration,\n    specifiers,\n    source,\n  });\n}\nexport function declareExportAllDeclaration(\n  source: t.StringLiteral,\n): t.DeclareExportAllDeclaration {\n  return validateNode<t.DeclareExportAllDeclaration>({\n    type: \"DeclareExportAllDeclaration\",\n    source,\n  });\n}\nexport function declaredPredicate(value: t.Flow): t.DeclaredPredicate {\n  return validateNode<t.DeclaredPredicate>({\n    type: \"DeclaredPredicate\",\n    value,\n  });\n}\nexport function existsTypeAnnotation(): t.ExistsTypeAnnotation {\n  return {\n    type: \"ExistsTypeAnnotation\",\n  };\n}\nexport function functionTypeAnnotation(\n  typeParameters: t.TypeParameterDeclaration | null | undefined = null,\n  params: Array<t.FunctionTypeParam>,\n  rest: t.FunctionTypeParam | null | undefined = null,\n  returnType: t.FlowType,\n): t.FunctionTypeAnnotation {\n  return validateNode<t.FunctionTypeAnnotation>({\n    type: \"FunctionTypeAnnotation\",\n    typeParameters,\n    params,\n    rest,\n    returnType,\n  });\n}\nexport function functionTypeParam(\n  name: t.Identifier | null | undefined = null,\n  typeAnnotation: t.FlowType,\n): t.FunctionTypeParam {\n  return validateNode<t.FunctionTypeParam>({\n    type: \"FunctionTypeParam\",\n    name,\n    typeAnnotation,\n  });\n}\nexport function genericTypeAnnotation(\n  id: t.Identifier | t.QualifiedTypeIdentifier,\n  typeParameters: t.TypeParameterInstantiation | null = null,\n): t.GenericTypeAnnotation {\n  return validateNode<t.GenericTypeAnnotation>({\n    type: \"GenericTypeAnnotation\",\n    id,\n    typeParameters,\n  });\n}\nexport function inferredPredicate(): t.InferredPredicate {\n  return {\n    type: \"InferredPredicate\",\n  };\n}\nexport function interfaceExtends(\n  id: t.Identifier | t.QualifiedTypeIdentifier,\n  typeParameters: t.TypeParameterInstantiation | null = null,\n): t.InterfaceExtends {\n  return validateNode<t.InterfaceExtends>({\n    type: \"InterfaceExtends\",\n    id,\n    typeParameters,\n  });\n}\nexport function interfaceDeclaration(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterDeclaration | null | undefined = null,\n  _extends: Array<t.InterfaceExtends> | null | undefined = null,\n  body: t.ObjectTypeAnnotation,\n): t.InterfaceDeclaration {\n  return validateNode<t.InterfaceDeclaration>({\n    type: \"InterfaceDeclaration\",\n    id,\n    typeParameters,\n    extends: _extends,\n    body,\n  });\n}\nexport function interfaceTypeAnnotation(\n  _extends: Array<t.InterfaceExtends> | null | undefined = null,\n  body: t.ObjectTypeAnnotation,\n): t.InterfaceTypeAnnotation {\n  return validateNode<t.InterfaceTypeAnnotation>({\n    type: \"InterfaceTypeAnnotation\",\n    extends: _extends,\n    body,\n  });\n}\nexport function intersectionTypeAnnotation(\n  types: Array<t.FlowType>,\n): t.IntersectionTypeAnnotation {\n  return validateNode<t.IntersectionTypeAnnotation>({\n    type: \"IntersectionTypeAnnotation\",\n    types,\n  });\n}\nexport function mixedTypeAnnotation(): t.MixedTypeAnnotation {\n  return {\n    type: \"MixedTypeAnnotation\",\n  };\n}\nexport function emptyTypeAnnotation(): t.EmptyTypeAnnotation {\n  return {\n    type: \"EmptyTypeAnnotation\",\n  };\n}\nexport function nullableTypeAnnotation(\n  typeAnnotation: t.FlowType,\n): t.NullableTypeAnnotation {\n  return validateNode<t.NullableTypeAnnotation>({\n    type: \"NullableTypeAnnotation\",\n    typeAnnotation,\n  });\n}\nexport function numberLiteralTypeAnnotation(\n  value: number,\n): t.NumberLiteralTypeAnnotation {\n  return validateNode<t.NumberLiteralTypeAnnotation>({\n    type: \"NumberLiteralTypeAnnotation\",\n    value,\n  });\n}\nexport function numberTypeAnnotation(): t.NumberTypeAnnotation {\n  return {\n    type: \"NumberTypeAnnotation\",\n  };\n}\nexport function objectTypeAnnotation(\n  properties: Array<t.ObjectTypeProperty | t.ObjectTypeSpreadProperty>,\n  indexers: Array<t.ObjectTypeIndexer> = [],\n  callProperties: Array<t.ObjectTypeCallProperty> = [],\n  internalSlots: Array<t.ObjectTypeInternalSlot> = [],\n  exact: boolean = false,\n): t.ObjectTypeAnnotation {\n  return validateNode<t.ObjectTypeAnnotation>({\n    type: \"ObjectTypeAnnotation\",\n    properties,\n    indexers,\n    callProperties,\n    internalSlots,\n    exact,\n  });\n}\nexport function objectTypeInternalSlot(\n  id: t.Identifier,\n  value: t.FlowType,\n  optional: boolean,\n  _static: boolean,\n  method: boolean,\n): t.ObjectTypeInternalSlot {\n  return validateNode<t.ObjectTypeInternalSlot>({\n    type: \"ObjectTypeInternalSlot\",\n    id,\n    value,\n    optional,\n    static: _static,\n    method,\n  });\n}\nexport function objectTypeCallProperty(\n  value: t.FlowType,\n): t.ObjectTypeCallProperty {\n  return validateNode<t.ObjectTypeCallProperty>({\n    type: \"ObjectTypeCallProperty\",\n    value,\n    static: null,\n  });\n}\nexport function objectTypeIndexer(\n  id: t.Identifier | null | undefined = null,\n  key: t.FlowType,\n  value: t.FlowType,\n  variance: t.Variance | null = null,\n): t.ObjectTypeIndexer {\n  return validateNode<t.ObjectTypeIndexer>({\n    type: \"ObjectTypeIndexer\",\n    id,\n    key,\n    value,\n    variance,\n    static: null,\n  });\n}\nexport function objectTypeProperty(\n  key: t.Identifier | t.StringLiteral,\n  value: t.FlowType,\n  variance: t.Variance | null = null,\n): t.ObjectTypeProperty {\n  return validateNode<t.ObjectTypeProperty>({\n    type: \"ObjectTypeProperty\",\n    key,\n    value,\n    variance,\n    kind: null,\n    method: null,\n    optional: null,\n    proto: null,\n    static: null,\n  });\n}\nexport function objectTypeSpreadProperty(\n  argument: t.FlowType,\n): t.ObjectTypeSpreadProperty {\n  return validateNode<t.ObjectTypeSpreadProperty>({\n    type: \"ObjectTypeSpreadProperty\",\n    argument,\n  });\n}\nexport function opaqueType(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterDeclaration | null | undefined = null,\n  supertype: t.FlowType | null | undefined = null,\n  impltype: t.FlowType,\n): t.OpaqueType {\n  return validateNode<t.OpaqueType>({\n    type: \"OpaqueType\",\n    id,\n    typeParameters,\n    supertype,\n    impltype,\n  });\n}\nexport function qualifiedTypeIdentifier(\n  id: t.Identifier,\n  qualification: t.Identifier | t.QualifiedTypeIdentifier,\n): t.QualifiedTypeIdentifier {\n  return validateNode<t.QualifiedTypeIdentifier>({\n    type: \"QualifiedTypeIdentifier\",\n    id,\n    qualification,\n  });\n}\nexport function stringLiteralTypeAnnotation(\n  value: string,\n): t.StringLiteralTypeAnnotation {\n  return validateNode<t.StringLiteralTypeAnnotation>({\n    type: \"StringLiteralTypeAnnotation\",\n    value,\n  });\n}\nexport function stringTypeAnnotation(): t.StringTypeAnnotation {\n  return {\n    type: \"StringTypeAnnotation\",\n  };\n}\nexport function symbolTypeAnnotation(): t.SymbolTypeAnnotation {\n  return {\n    type: \"SymbolTypeAnnotation\",\n  };\n}\nexport function thisTypeAnnotation(): t.ThisTypeAnnotation {\n  return {\n    type: \"ThisTypeAnnotation\",\n  };\n}\nexport function tupleTypeAnnotation(\n  types: Array<t.FlowType>,\n): t.TupleTypeAnnotation {\n  return validateNode<t.TupleTypeAnnotation>({\n    type: \"TupleTypeAnnotation\",\n    types,\n  });\n}\nexport function typeofTypeAnnotation(\n  argument: t.FlowType,\n): t.TypeofTypeAnnotation {\n  return validateNode<t.TypeofTypeAnnotation>({\n    type: \"TypeofTypeAnnotation\",\n    argument,\n  });\n}\nexport function typeAlias(\n  id: t.Identifier,\n  typeParameters: t.TypeParameterDeclaration | null | undefined = null,\n  right: t.FlowType,\n): t.TypeAlias {\n  return validateNode<t.TypeAlias>({\n    type: \"TypeAlias\",\n    id,\n    typeParameters,\n    right,\n  });\n}\nexport function typeAnnotation(typeAnnotation: t.FlowType): t.TypeAnnotation {\n  return validateNode<t.TypeAnnotation>({\n    type: \"TypeAnnotation\",\n    typeAnnotation,\n  });\n}\nexport function typeCastExpression(\n  expression: t.Expression,\n  typeAnnotation: t.TypeAnnotation,\n): t.TypeCastExpression {\n  return validateNode<t.TypeCastExpression>({\n    type: \"TypeCastExpression\",\n    expression,\n    typeAnnotation,\n  });\n}\nexport function typeParameter(\n  bound: t.TypeAnnotation | null = null,\n  _default: t.FlowType | null = null,\n  variance: t.Variance | null = null,\n): t.TypeParameter {\n  return validateNode<t.TypeParameter>({\n    type: \"TypeParameter\",\n    bound,\n    default: _default,\n    variance,\n    name: null,\n  });\n}\nexport function typeParameterDeclaration(\n  params: Array<t.TypeParameter>,\n): t.TypeParameterDeclaration {\n  return validateNode<t.TypeParameterDeclaration>({\n    type: \"TypeParameterDeclaration\",\n    params,\n  });\n}\nexport function typeParameterInstantiation(\n  params: Array<t.FlowType>,\n): t.TypeParameterInstantiation {\n  return validateNode<t.TypeParameterInstantiation>({\n    type: \"TypeParameterInstantiation\",\n    params,\n  });\n}\nexport function unionTypeAnnotation(\n  types: Array<t.FlowType>,\n): t.UnionTypeAnnotation {\n  return validateNode<t.UnionTypeAnnotation>({\n    type: \"UnionTypeAnnotation\",\n    types,\n  });\n}\nexport function variance(kind: \"minus\" | \"plus\"): t.Variance {\n  return validateNode<t.Variance>({\n    type: \"Variance\",\n    kind,\n  });\n}\nexport function voidTypeAnnotation(): t.VoidTypeAnnotation {\n  return {\n    type: \"VoidTypeAnnotation\",\n  };\n}\nexport function enumDeclaration(\n  id: t.Identifier,\n  body:\n    | t.EnumBooleanBody\n    | t.EnumNumberBody\n    | t.EnumStringBody\n    | t.EnumSymbolBody,\n): t.EnumDeclaration {\n  return validateNode<t.EnumDeclaration>({\n    type: \"EnumDeclaration\",\n    id,\n    body,\n  });\n}\nexport function enumBooleanBody(\n  members: Array<t.EnumBooleanMember>,\n): t.EnumBooleanBody {\n  return validateNode<t.EnumBooleanBody>({\n    type: \"EnumBooleanBody\",\n    members,\n    explicitType: null,\n    hasUnknownMembers: null,\n  });\n}\nexport function enumNumberBody(\n  members: Array<t.EnumNumberMember>,\n): t.EnumNumberBody {\n  return validateNode<t.EnumNumberBody>({\n    type: \"EnumNumberBody\",\n    members,\n    explicitType: null,\n    hasUnknownMembers: null,\n  });\n}\nexport function enumStringBody(\n  members: Array<t.EnumStringMember | t.EnumDefaultedMember>,\n): t.EnumStringBody {\n  return validateNode<t.EnumStringBody>({\n    type: \"EnumStringBody\",\n    members,\n    explicitType: null,\n    hasUnknownMembers: null,\n  });\n}\nexport function enumSymbolBody(\n  members: Array<t.EnumDefaultedMember>,\n): t.EnumSymbolBody {\n  return validateNode<t.EnumSymbolBody>({\n    type: \"EnumSymbolBody\",\n    members,\n    hasUnknownMembers: null,\n  });\n}\nexport function enumBooleanMember(id: t.Identifier): t.EnumBooleanMember {\n  return validateNode<t.EnumBooleanMember>({\n    type: \"EnumBooleanMember\",\n    id,\n    init: null,\n  });\n}\nexport function enumNumberMember(\n  id: t.Identifier,\n  init: t.NumericLiteral,\n): t.EnumNumberMember {\n  return validateNode<t.EnumNumberMember>({\n    type: \"EnumNumberMember\",\n    id,\n    init,\n  });\n}\nexport function enumStringMember(\n  id: t.Identifier,\n  init: t.StringLiteral,\n): t.EnumStringMember {\n  return validateNode<t.EnumStringMember>({\n    type: \"EnumStringMember\",\n    id,\n    init,\n  });\n}\nexport function enumDefaultedMember(id: t.Identifier): t.EnumDefaultedMember {\n  return validateNode<t.EnumDefaultedMember>({\n    type: \"EnumDefaultedMember\",\n    id,\n  });\n}\nexport function indexedAccessType(\n  objectType: t.FlowType,\n  indexType: t.FlowType,\n): t.IndexedAccessType {\n  return validateNode<t.IndexedAccessType>({\n    type: \"IndexedAccessType\",\n    objectType,\n    indexType,\n  });\n}\nexport function optionalIndexedAccessType(\n  objectType: t.FlowType,\n  indexType: t.FlowType,\n): t.OptionalIndexedAccessType {\n  return validateNode<t.OptionalIndexedAccessType>({\n    type: \"OptionalIndexedAccessType\",\n    objectType,\n    indexType,\n    optional: null,\n  });\n}\nexport function jsxAttribute(\n  name: t.JSXIdentifier | t.JSXNamespacedName,\n  value:\n    | t.JSXElement\n    | t.JSXFragment\n    | t.StringLiteral\n    | t.JSXExpressionContainer\n    | null = null,\n): t.JSXAttribute {\n  return validateNode<t.JSXAttribute>({\n    type: \"JSXAttribute\",\n    name,\n    value,\n  });\n}\nexport { jsxAttribute as jSXAttribute };\nexport function jsxClosingElement(\n  name: t.JSXIdentifier | t.JSXMemberExpression | t.JSXNamespacedName,\n): t.JSXClosingElement {\n  return validateNode<t.JSXClosingElement>({\n    type: \"JSXClosingElement\",\n    name,\n  });\n}\nexport { jsxClosingElement as jSXClosingElement };\nexport function jsxElement(\n  openingElement: t.JSXOpeningElement,\n  closingElement: t.JSXClosingElement | null | undefined = null,\n  children: Array<\n    | t.JSXText\n    | t.JSXExpressionContainer\n    | t.JSXSpreadChild\n    | t.JSXElement\n    | t.JSXFragment\n  >,\n  selfClosing: boolean | null = null,\n): t.JSXElement {\n  return validateNode<t.JSXElement>({\n    type: \"JSXElement\",\n    openingElement,\n    closingElement,\n    children,\n    selfClosing,\n  });\n}\nexport { jsxElement as jSXElement };\nexport function jsxEmptyExpression(): t.JSXEmptyExpression {\n  return {\n    type: \"JSXEmptyExpression\",\n  };\n}\nexport { jsxEmptyExpression as jSXEmptyExpression };\nexport function jsxExpressionContainer(\n  expression: t.Expression | t.JSXEmptyExpression,\n): t.JSXExpressionContainer {\n  return validateNode<t.JSXExpressionContainer>({\n    type: \"JSXExpressionContainer\",\n    expression,\n  });\n}\nexport { jsxExpressionContainer as jSXExpressionContainer };\nexport function jsxSpreadChild(expression: t.Expression): t.JSXSpreadChild {\n  return validateNode<t.JSXSpreadChild>({\n    type: \"JSXSpreadChild\",\n    expression,\n  });\n}\nexport { jsxSpreadChild as jSXSpreadChild };\nexport function jsxIdentifier(name: string): t.JSXIdentifier {\n  return validateNode<t.JSXIdentifier>({\n    type: \"JSXIdentifier\",\n    name,\n  });\n}\nexport { jsxIdentifier as jSXIdentifier };\nexport function jsxMemberExpression(\n  object: t.JSXMemberExpression | t.JSXIdentifier,\n  property: t.JSXIdentifier,\n): t.JSXMemberExpression {\n  return validateNode<t.JSXMemberExpression>({\n    type: \"JSXMemberExpression\",\n    object,\n    property,\n  });\n}\nexport { jsxMemberExpression as jSXMemberExpression };\nexport function jsxNamespacedName(\n  namespace: t.JSXIdentifier,\n  name: t.JSXIdentifier,\n): t.JSXNamespacedName {\n  return validateNode<t.JSXNamespacedName>({\n    type: \"JSXNamespacedName\",\n    namespace,\n    name,\n  });\n}\nexport { jsxNamespacedName as jSXNamespacedName };\nexport function jsxOpeningElement(\n  name: t.JSXIdentifier | t.JSXMemberExpression | t.JSXNamespacedName,\n  attributes: Array<t.JSXAttribute | t.JSXSpreadAttribute>,\n  selfClosing: boolean = false,\n): t.JSXOpeningElement {\n  return validateNode<t.JSXOpeningElement>({\n    type: \"JSXOpeningElement\",\n    name,\n    attributes,\n    selfClosing,\n  });\n}\nexport { jsxOpeningElement as jSXOpeningElement };\nexport function jsxSpreadAttribute(\n  argument: t.Expression,\n): t.JSXSpreadAttribute {\n  return validateNode<t.JSXSpreadAttribute>({\n    type: \"JSXSpreadAttribute\",\n    argument,\n  });\n}\nexport { jsxSpreadAttribute as jSXSpreadAttribute };\nexport function jsxText(value: string): t.JSXText {\n  return validateNode<t.JSXText>({\n    type: \"JSXText\",\n    value,\n  });\n}\nexport { jsxText as jSXText };\nexport function jsxFragment(\n  openingFragment: t.JSXOpeningFragment,\n  closingFragment: t.JSXClosingFragment,\n  children: Array<\n    | t.JSXText\n    | t.JSXExpressionContainer\n    | t.JSXSpreadChild\n    | t.JSXElement\n    | t.JSXFragment\n  >,\n): t.JSXFragment {\n  return validateNode<t.JSXFragment>({\n    type: \"JSXFragment\",\n    openingFragment,\n    closingFragment,\n    children,\n  });\n}\nexport { jsxFragment as jSXFragment };\nexport function jsxOpeningFragment(): t.JSXOpeningFragment {\n  return {\n    type: \"JSXOpeningFragment\",\n  };\n}\nexport { jsxOpeningFragment as jSXOpeningFragment };\nexport function jsxClosingFragment(): t.JSXClosingFragment {\n  return {\n    type: \"JSXClosingFragment\",\n  };\n}\nexport { jsxClosingFragment as jSXClosingFragment };\nexport function noop(): t.Noop {\n  return {\n    type: \"Noop\",\n  };\n}\nexport function placeholder(\n  expectedNode:\n    | \"Identifier\"\n    | \"StringLiteral\"\n    | \"Expression\"\n    | \"Statement\"\n    | \"Declaration\"\n    | \"BlockStatement\"\n    | \"ClassBody\"\n    | \"Pattern\",\n  name: t.Identifier,\n): t.Placeholder {\n  return validateNode<t.Placeholder>({\n    type: \"Placeholder\",\n    expectedNode,\n    name,\n  });\n}\nexport function v8IntrinsicIdentifier(name: string): t.V8IntrinsicIdentifier {\n  return validateNode<t.V8IntrinsicIdentifier>({\n    type: \"V8IntrinsicIdentifier\",\n    name,\n  });\n}\nexport function argumentPlaceholder(): t.ArgumentPlaceholder {\n  return {\n    type: \"ArgumentPlaceholder\",\n  };\n}\nexport function bindExpression(\n  object: t.Expression,\n  callee: t.Expression,\n): t.BindExpression {\n  return validateNode<t.BindExpression>({\n    type: \"BindExpression\",\n    object,\n    callee,\n  });\n}\nexport function importAttribute(\n  key: t.Identifier | t.StringLiteral,\n  value: t.StringLiteral,\n): t.ImportAttribute {\n  return validateNode<t.ImportAttribute>({\n    type: \"ImportAttribute\",\n    key,\n    value,\n  });\n}\nexport function decorator(expression: t.Expression): t.Decorator {\n  return validateNode<t.Decorator>({\n    type: \"Decorator\",\n    expression,\n  });\n}\nexport function doExpression(\n  body: t.BlockStatement,\n  async: boolean = false,\n): t.DoExpression {\n  return validateNode<t.DoExpression>({\n    type: \"DoExpression\",\n    body,\n    async,\n  });\n}\nexport function exportDefaultSpecifier(\n  exported: t.Identifier,\n): t.ExportDefaultSpecifier {\n  return validateNode<t.ExportDefaultSpecifier>({\n    type: \"ExportDefaultSpecifier\",\n    exported,\n  });\n}\nexport function recordExpression(\n  properties: Array<t.ObjectProperty | t.SpreadElement>,\n): t.RecordExpression {\n  return validateNode<t.RecordExpression>({\n    type: \"RecordExpression\",\n    properties,\n  });\n}\nexport function tupleExpression(\n  elements: Array<t.Expression | t.SpreadElement> = [],\n): t.TupleExpression {\n  return validateNode<t.TupleExpression>({\n    type: \"TupleExpression\",\n    elements,\n  });\n}\nexport function decimalLiteral(value: string): t.DecimalLiteral {\n  return validateNode<t.DecimalLiteral>({\n    type: \"DecimalLiteral\",\n    value,\n  });\n}\nexport function moduleExpression(body: t.Program): t.ModuleExpression {\n  return validateNode<t.ModuleExpression>({\n    type: \"ModuleExpression\",\n    body,\n  });\n}\nexport function topicReference(): t.TopicReference {\n  return {\n    type: \"TopicReference\",\n  };\n}\nexport function pipelineTopicExpression(\n  expression: t.Expression,\n): t.PipelineTopicExpression {\n  return validateNode<t.PipelineTopicExpression>({\n    type: \"PipelineTopicExpression\",\n    expression,\n  });\n}\nexport function pipelineBareFunction(\n  callee: t.Expression,\n): t.PipelineBareFunction {\n  return validateNode<t.PipelineBareFunction>({\n    type: \"PipelineBareFunction\",\n    callee,\n  });\n}\nexport function pipelinePrimaryTopicReference(): t.PipelinePrimaryTopicReference {\n  return {\n    type: \"PipelinePrimaryTopicReference\",\n  };\n}\nexport function tsParameterProperty(\n  parameter: t.Identifier | t.AssignmentPattern,\n): t.TSParameterProperty {\n  return validateNode<t.TSParameterProperty>({\n    type: \"TSParameterProperty\",\n    parameter,\n  });\n}\nexport { tsParameterProperty as tSParameterProperty };\nexport function tsDeclareFunction(\n  id: t.Identifier | null | undefined = null,\n  typeParameters:\n    | t.TSTypeParameterDeclaration\n    | t.Noop\n    | null\n    | undefined = null,\n  params: Array<t.Identifier | t.Pattern | t.RestElement>,\n  returnType: t.TSTypeAnnotation | t.Noop | null = null,\n): t.TSDeclareFunction {\n  return validateNode<t.TSDeclareFunction>({\n    type: \"TSDeclareFunction\",\n    id,\n    typeParameters,\n    params,\n    returnType,\n  });\n}\nexport { tsDeclareFunction as tSDeclareFunction };\nexport function tsDeclareMethod(\n  decorators: Array<t.Decorator> | null | undefined = null,\n  key:\n    | t.Identifier\n    | t.StringLiteral\n    | t.NumericLiteral\n    | t.BigIntLiteral\n    | t.Expression,\n  typeParameters:\n    | t.TSTypeParameterDeclaration\n    | t.Noop\n    | null\n    | undefined = null,\n  params: Array<\n    t.Identifier | t.Pattern | t.RestElement | t.TSParameterProperty\n  >,\n  returnType: t.TSTypeAnnotation | t.Noop | null = null,\n): t.TSDeclareMethod {\n  return validateNode<t.TSDeclareMethod>({\n    type: \"TSDeclareMethod\",\n    decorators,\n    key,\n    typeParameters,\n    params,\n    returnType,\n  });\n}\nexport { tsDeclareMethod as tSDeclareMethod };\nexport function tsQualifiedName(\n  left: t.TSEntityName,\n  right: t.Identifier,\n): t.TSQualifiedName {\n  return validateNode<t.TSQualifiedName>({\n    type: \"TSQualifiedName\",\n    left,\n    right,\n  });\n}\nexport { tsQualifiedName as tSQualifiedName };\nexport function tsCallSignatureDeclaration(\n  typeParameters: t.TSTypeParameterDeclaration | null | undefined = null,\n  parameters: Array<t.Identifier | t.RestElement>,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n): t.TSCallSignatureDeclaration {\n  return validateNode<t.TSCallSignatureDeclaration>({\n    type: \"TSCallSignatureDeclaration\",\n    typeParameters,\n    parameters,\n    typeAnnotation,\n  });\n}\nexport { tsCallSignatureDeclaration as tSCallSignatureDeclaration };\nexport function tsConstructSignatureDeclaration(\n  typeParameters: t.TSTypeParameterDeclaration | null | undefined = null,\n  parameters: Array<t.Identifier | t.RestElement>,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n): t.TSConstructSignatureDeclaration {\n  return validateNode<t.TSConstructSignatureDeclaration>({\n    type: \"TSConstructSignatureDeclaration\",\n    typeParameters,\n    parameters,\n    typeAnnotation,\n  });\n}\nexport { tsConstructSignatureDeclaration as tSConstructSignatureDeclaration };\nexport function tsPropertySignature(\n  key: t.Expression,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n  initializer: t.Expression | null = null,\n): t.TSPropertySignature {\n  return validateNode<t.TSPropertySignature>({\n    type: \"TSPropertySignature\",\n    key,\n    typeAnnotation,\n    initializer,\n    kind: null,\n  });\n}\nexport { tsPropertySignature as tSPropertySignature };\nexport function tsMethodSignature(\n  key: t.Expression,\n  typeParameters: t.TSTypeParameterDeclaration | null | undefined = null,\n  parameters: Array<t.Identifier | t.RestElement>,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n): t.TSMethodSignature {\n  return validateNode<t.TSMethodSignature>({\n    type: \"TSMethodSignature\",\n    key,\n    typeParameters,\n    parameters,\n    typeAnnotation,\n    kind: null,\n  });\n}\nexport { tsMethodSignature as tSMethodSignature };\nexport function tsIndexSignature(\n  parameters: Array<t.Identifier>,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n): t.TSIndexSignature {\n  return validateNode<t.TSIndexSignature>({\n    type: \"TSIndexSignature\",\n    parameters,\n    typeAnnotation,\n  });\n}\nexport { tsIndexSignature as tSIndexSignature };\nexport function tsAnyKeyword(): t.TSAnyKeyword {\n  return {\n    type: \"TSAnyKeyword\",\n  };\n}\nexport { tsAnyKeyword as tSAnyKeyword };\nexport function tsBooleanKeyword(): t.TSBooleanKeyword {\n  return {\n    type: \"TSBooleanKeyword\",\n  };\n}\nexport { tsBooleanKeyword as tSBooleanKeyword };\nexport function tsBigIntKeyword(): t.TSBigIntKeyword {\n  return {\n    type: \"TSBigIntKeyword\",\n  };\n}\nexport { tsBigIntKeyword as tSBigIntKeyword };\nexport function tsIntrinsicKeyword(): t.TSIntrinsicKeyword {\n  return {\n    type: \"TSIntrinsicKeyword\",\n  };\n}\nexport { tsIntrinsicKeyword as tSIntrinsicKeyword };\nexport function tsNeverKeyword(): t.TSNeverKeyword {\n  return {\n    type: \"TSNeverKeyword\",\n  };\n}\nexport { tsNeverKeyword as tSNeverKeyword };\nexport function tsNullKeyword(): t.TSNullKeyword {\n  return {\n    type: \"TSNullKeyword\",\n  };\n}\nexport { tsNullKeyword as tSNullKeyword };\nexport function tsNumberKeyword(): t.TSNumberKeyword {\n  return {\n    type: \"TSNumberKeyword\",\n  };\n}\nexport { tsNumberKeyword as tSNumberKeyword };\nexport function tsObjectKeyword(): t.TSObjectKeyword {\n  return {\n    type: \"TSObjectKeyword\",\n  };\n}\nexport { tsObjectKeyword as tSObjectKeyword };\nexport function tsStringKeyword(): t.TSStringKeyword {\n  return {\n    type: \"TSStringKeyword\",\n  };\n}\nexport { tsStringKeyword as tSStringKeyword };\nexport function tsSymbolKeyword(): t.TSSymbolKeyword {\n  return {\n    type: \"TSSymbolKeyword\",\n  };\n}\nexport { tsSymbolKeyword as tSSymbolKeyword };\nexport function tsUndefinedKeyword(): t.TSUndefinedKeyword {\n  return {\n    type: \"TSUndefinedKeyword\",\n  };\n}\nexport { tsUndefinedKeyword as tSUndefinedKeyword };\nexport function tsUnknownKeyword(): t.TSUnknownKeyword {\n  return {\n    type: \"TSUnknownKeyword\",\n  };\n}\nexport { tsUnknownKeyword as tSUnknownKeyword };\nexport function tsVoidKeyword(): t.TSVoidKeyword {\n  return {\n    type: \"TSVoidKeyword\",\n  };\n}\nexport { tsVoidKeyword as tSVoidKeyword };\nexport function tsThisType(): t.TSThisType {\n  return {\n    type: \"TSThisType\",\n  };\n}\nexport { tsThisType as tSThisType };\nexport function tsFunctionType(\n  typeParameters: t.TSTypeParameterDeclaration | null | undefined = null,\n  parameters: Array<t.Identifier | t.RestElement>,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n): t.TSFunctionType {\n  return validateNode<t.TSFunctionType>({\n    type: \"TSFunctionType\",\n    typeParameters,\n    parameters,\n    typeAnnotation,\n  });\n}\nexport { tsFunctionType as tSFunctionType };\nexport function tsConstructorType(\n  typeParameters: t.TSTypeParameterDeclaration | null | undefined = null,\n  parameters: Array<t.Identifier | t.RestElement>,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n): t.TSConstructorType {\n  return validateNode<t.TSConstructorType>({\n    type: \"TSConstructorType\",\n    typeParameters,\n    parameters,\n    typeAnnotation,\n  });\n}\nexport { tsConstructorType as tSConstructorType };\nexport function tsTypeReference(\n  typeName: t.TSEntityName,\n  typeParameters: t.TSTypeParameterInstantiation | null = null,\n): t.TSTypeReference {\n  return validateNode<t.TSTypeReference>({\n    type: \"TSTypeReference\",\n    typeName,\n    typeParameters,\n  });\n}\nexport { tsTypeReference as tSTypeReference };\nexport function tsTypePredicate(\n  parameterName: t.Identifier | t.TSThisType,\n  typeAnnotation: t.TSTypeAnnotation | null = null,\n  asserts: boolean | null = null,\n): t.TSTypePredicate {\n  return validateNode<t.TSTypePredicate>({\n    type: \"TSTypePredicate\",\n    parameterName,\n    typeAnnotation,\n    asserts,\n  });\n}\nexport { tsTypePredicate as tSTypePredicate };\nexport function tsTypeQuery(\n  exprName: t.TSEntityName | t.TSImportType,\n  typeParameters: t.TSTypeParameterInstantiation | null = null,\n): t.TSTypeQuery {\n  return validateNode<t.TSTypeQuery>({\n    type: \"TSTypeQuery\",\n    exprName,\n    typeParameters,\n  });\n}\nexport { tsTypeQuery as tSTypeQuery };\nexport function tsTypeLiteral(\n  members: Array<t.TSTypeElement>,\n): t.TSTypeLiteral {\n  return validateNode<t.TSTypeLiteral>({\n    type: \"TSTypeLiteral\",\n    members,\n  });\n}\nexport { tsTypeLiteral as tSTypeLiteral };\nexport function tsArrayType(elementType: t.TSType): t.TSArrayType {\n  return validateNode<t.TSArrayType>({\n    type: \"TSArrayType\",\n    elementType,\n  });\n}\nexport { tsArrayType as tSArrayType };\nexport function tsTupleType(\n  elementTypes: Array<t.TSType | t.TSNamedTupleMember>,\n): t.TSTupleType {\n  return validateNode<t.TSTupleType>({\n    type: \"TSTupleType\",\n    elementTypes,\n  });\n}\nexport { tsTupleType as tSTupleType };\nexport function tsOptionalType(typeAnnotation: t.TSType): t.TSOptionalType {\n  return validateNode<t.TSOptionalType>({\n    type: \"TSOptionalType\",\n    typeAnnotation,\n  });\n}\nexport { tsOptionalType as tSOptionalType };\nexport function tsRestType(typeAnnotation: t.TSType): t.TSRestType {\n  return validateNode<t.TSRestType>({\n    type: \"TSRestType\",\n    typeAnnotation,\n  });\n}\nexport { tsRestType as tSRestType };\nexport function tsNamedTupleMember(\n  label: t.Identifier,\n  elementType: t.TSType,\n  optional: boolean = false,\n): t.TSNamedTupleMember {\n  return validateNode<t.TSNamedTupleMember>({\n    type: \"TSNamedTupleMember\",\n    label,\n    elementType,\n    optional,\n  });\n}\nexport { tsNamedTupleMember as tSNamedTupleMember };\nexport function tsUnionType(types: Array<t.TSType>): t.TSUnionType {\n  return validateNode<t.TSUnionType>({\n    type: \"TSUnionType\",\n    types,\n  });\n}\nexport { tsUnionType as tSUnionType };\nexport function tsIntersectionType(\n  types: Array<t.TSType>,\n): t.TSIntersectionType {\n  return validateNode<t.TSIntersectionType>({\n    type: \"TSIntersectionType\",\n    types,\n  });\n}\nexport { tsIntersectionType as tSIntersectionType };\nexport function tsConditionalType(\n  checkType: t.TSType,\n  extendsType: t.TSType,\n  trueType: t.TSType,\n  falseType: t.TSType,\n): t.TSConditionalType {\n  return validateNode<t.TSConditionalType>({\n    type: \"TSConditionalType\",\n    checkType,\n    extendsType,\n    trueType,\n    falseType,\n  });\n}\nexport { tsConditionalType as tSConditionalType };\nexport function tsInferType(typeParameter: t.TSTypeParameter): t.TSInferType {\n  return validateNode<t.TSInferType>({\n    type: \"TSInferType\",\n    typeParameter,\n  });\n}\nexport { tsInferType as tSInferType };\nexport function tsParenthesizedType(\n  typeAnnotation: t.TSType,\n): t.TSParenthesizedType {\n  return validateNode<t.TSParenthesizedType>({\n    type: \"TSParenthesizedType\",\n    typeAnnotation,\n  });\n}\nexport { tsParenthesizedType as tSParenthesizedType };\nexport function tsTypeOperator(typeAnnotation: t.TSType): t.TSTypeOperator {\n  return validateNode<t.TSTypeOperator>({\n    type: \"TSTypeOperator\",\n    typeAnnotation,\n    operator: null,\n  });\n}\nexport { tsTypeOperator as tSTypeOperator };\nexport function tsIndexedAccessType(\n  objectType: t.TSType,\n  indexType: t.TSType,\n): t.TSIndexedAccessType {\n  return validateNode<t.TSIndexedAccessType>({\n    type: \"TSIndexedAccessType\",\n    objectType,\n    indexType,\n  });\n}\nexport { tsIndexedAccessType as tSIndexedAccessType };\nexport function tsMappedType(\n  typeParameter: t.TSTypeParameter,\n  typeAnnotation: t.TSType | null = null,\n  nameType: t.TSType | null = null,\n): t.TSMappedType {\n  return validateNode<t.TSMappedType>({\n    type: \"TSMappedType\",\n    typeParameter,\n    typeAnnotation,\n    nameType,\n  });\n}\nexport { tsMappedType as tSMappedType };\nexport function tsLiteralType(\n  literal:\n    | t.NumericLiteral\n    | t.StringLiteral\n    | t.BooleanLiteral\n    | t.BigIntLiteral\n    | t.TemplateLiteral\n    | t.UnaryExpression,\n): t.TSLiteralType {\n  return validateNode<t.TSLiteralType>({\n    type: \"TSLiteralType\",\n    literal,\n  });\n}\nexport { tsLiteralType as tSLiteralType };\nexport function tsExpressionWithTypeArguments(\n  expression: t.TSEntityName,\n  typeParameters: t.TSTypeParameterInstantiation | null = null,\n): t.TSExpressionWithTypeArguments {\n  return validateNode<t.TSExpressionWithTypeArguments>({\n    type: \"TSExpressionWithTypeArguments\",\n    expression,\n    typeParameters,\n  });\n}\nexport { tsExpressionWithTypeArguments as tSExpressionWithTypeArguments };\nexport function tsInterfaceDeclaration(\n  id: t.Identifier,\n  typeParameters: t.TSTypeParameterDeclaration | null | undefined = null,\n  _extends: Array<t.TSExpressionWithTypeArguments> | null | undefined = null,\n  body: t.TSInterfaceBody,\n): t.TSInterfaceDeclaration {\n  return validateNode<t.TSInterfaceDeclaration>({\n    type: \"TSInterfaceDeclaration\",\n    id,\n    typeParameters,\n    extends: _extends,\n    body,\n  });\n}\nexport { tsInterfaceDeclaration as tSInterfaceDeclaration };\nexport function tsInterfaceBody(\n  body: Array<t.TSTypeElement>,\n): t.TSInterfaceBody {\n  return validateNode<t.TSInterfaceBody>({\n    type: \"TSInterfaceBody\",\n    body,\n  });\n}\nexport { tsInterfaceBody as tSInterfaceBody };\nexport function tsTypeAliasDeclaration(\n  id: t.Identifier,\n  typeParameters: t.TSTypeParameterDeclaration | null | undefined = null,\n  typeAnnotation: t.TSType,\n): t.TSTypeAliasDeclaration {\n  return validateNode<t.TSTypeAliasDeclaration>({\n    type: \"TSTypeAliasDeclaration\",\n    id,\n    typeParameters,\n    typeAnnotation,\n  });\n}\nexport { tsTypeAliasDeclaration as tSTypeAliasDeclaration };\nexport function tsInstantiationExpression(\n  expression: t.Expression,\n  typeParameters: t.TSTypeParameterInstantiation | null = null,\n): t.TSInstantiationExpression {\n  return validateNode<t.TSInstantiationExpression>({\n    type: \"TSInstantiationExpression\",\n    expression,\n    typeParameters,\n  });\n}\nexport { tsInstantiationExpression as tSInstantiationExpression };\nexport function tsAsExpression(\n  expression: t.Expression,\n  typeAnnotation: t.TSType,\n): t.TSAsExpression {\n  return validateNode<t.TSAsExpression>({\n    type: \"TSAsExpression\",\n    expression,\n    typeAnnotation,\n  });\n}\nexport { tsAsExpression as tSAsExpression };\nexport function tsSatisfiesExpression(\n  expression: t.Expression,\n  typeAnnotation: t.TSType,\n): t.TSSatisfiesExpression {\n  return validateNode<t.TSSatisfiesExpression>({\n    type: \"TSSatisfiesExpression\",\n    expression,\n    typeAnnotation,\n  });\n}\nexport { tsSatisfiesExpression as tSSatisfiesExpression };\nexport function tsTypeAssertion(\n  typeAnnotation: t.TSType,\n  expression: t.Expression,\n): t.TSTypeAssertion {\n  return validateNode<t.TSTypeAssertion>({\n    type: \"TSTypeAssertion\",\n    typeAnnotation,\n    expression,\n  });\n}\nexport { tsTypeAssertion as tSTypeAssertion };\nexport function tsEnumDeclaration(\n  id: t.Identifier,\n  members: Array<t.TSEnumMember>,\n): t.TSEnumDeclaration {\n  return validateNode<t.TSEnumDeclaration>({\n    type: \"TSEnumDeclaration\",\n    id,\n    members,\n  });\n}\nexport { tsEnumDeclaration as tSEnumDeclaration };\nexport function tsEnumMember(\n  id: t.Identifier | t.StringLiteral,\n  initializer: t.Expression | null = null,\n): t.TSEnumMember {\n  return validateNode<t.TSEnumMember>({\n    type: \"TSEnumMember\",\n    id,\n    initializer,\n  });\n}\nexport { tsEnumMember as tSEnumMember };\nexport function tsModuleDeclaration(\n  id: t.Identifier | t.StringLiteral,\n  body: t.TSModuleBlock | t.TSModuleDeclaration,\n): t.TSModuleDeclaration {\n  return validateNode<t.TSModuleDeclaration>({\n    type: \"TSModuleDeclaration\",\n    id,\n    body,\n  });\n}\nexport { tsModuleDeclaration as tSModuleDeclaration };\nexport function tsModuleBlock(body: Array<t.Statement>): t.TSModuleBlock {\n  return validateNode<t.TSModuleBlock>({\n    type: \"TSModuleBlock\",\n    body,\n  });\n}\nexport { tsModuleBlock as tSModuleBlock };\nexport function tsImportType(\n  argument: t.StringLiteral,\n  qualifier: t.TSEntityName | null = null,\n  typeParameters: t.TSTypeParameterInstantiation | null = null,\n): t.TSImportType {\n  return validateNode<t.TSImportType>({\n    type: \"TSImportType\",\n    argument,\n    qualifier,\n    typeParameters,\n  });\n}\nexport { tsImportType as tSImportType };\nexport function tsImportEqualsDeclaration(\n  id: t.Identifier,\n  moduleReference: t.TSEntityName | t.TSExternalModuleReference,\n): t.TSImportEqualsDeclaration {\n  return validateNode<t.TSImportEqualsDeclaration>({\n    type: \"TSImportEqualsDeclaration\",\n    id,\n    moduleReference,\n    isExport: null,\n  });\n}\nexport { tsImportEqualsDeclaration as tSImportEqualsDeclaration };\nexport function tsExternalModuleReference(\n  expression: t.StringLiteral,\n): t.TSExternalModuleReference {\n  return validateNode<t.TSExternalModuleReference>({\n    type: \"TSExternalModuleReference\",\n    expression,\n  });\n}\nexport { tsExternalModuleReference as tSExternalModuleReference };\nexport function tsNonNullExpression(\n  expression: t.Expression,\n): t.TSNonNullExpression {\n  return validateNode<t.TSNonNullExpression>({\n    type: \"TSNonNullExpression\",\n    expression,\n  });\n}\nexport { tsNonNullExpression as tSNonNullExpression };\nexport function tsExportAssignment(\n  expression: t.Expression,\n): t.TSExportAssignment {\n  return validateNode<t.TSExportAssignment>({\n    type: \"TSExportAssignment\",\n    expression,\n  });\n}\nexport { tsExportAssignment as tSExportAssignment };\nexport function tsNamespaceExportDeclaration(\n  id: t.Identifier,\n): t.TSNamespaceExportDeclaration {\n  return validateNode<t.TSNamespaceExportDeclaration>({\n    type: \"TSNamespaceExportDeclaration\",\n    id,\n  });\n}\nexport { tsNamespaceExportDeclaration as tSNamespaceExportDeclaration };\nexport function tsTypeAnnotation(typeAnnotation: t.TSType): t.TSTypeAnnotation {\n  return validateNode<t.TSTypeAnnotation>({\n    type: \"TSTypeAnnotation\",\n    typeAnnotation,\n  });\n}\nexport { tsTypeAnnotation as tSTypeAnnotation };\nexport function tsTypeParameterInstantiation(\n  params: Array<t.TSType>,\n): t.TSTypeParameterInstantiation {\n  return validateNode<t.TSTypeParameterInstantiation>({\n    type: \"TSTypeParameterInstantiation\",\n    params,\n  });\n}\nexport { tsTypeParameterInstantiation as tSTypeParameterInstantiation };\nexport function tsTypeParameterDeclaration(\n  params: Array<t.TSTypeParameter>,\n): t.TSTypeParameterDeclaration {\n  return validateNode<t.TSTypeParameterDeclaration>({\n    type: \"TSTypeParameterDeclaration\",\n    params,\n  });\n}\nexport { tsTypeParameterDeclaration as tSTypeParameterDeclaration };\nexport function tsTypeParameter(\n  constraint: t.TSType | null | undefined = null,\n  _default: t.TSType | null | undefined = null,\n  name: string,\n): t.TSTypeParameter {\n  return validateNode<t.TSTypeParameter>({\n    type: \"TSTypeParameter\",\n    constraint,\n    default: _default,\n    name,\n  });\n}\nexport { tsTypeParameter as tSTypeParameter };\n/** @deprecated */\nfunction NumberLiteral(value: number) {\n  deprecationWarning(\"NumberLiteral\", \"NumericLiteral\", \"The node type \");\n  return numericLiteral(value);\n}\nexport { NumberLiteral as numberLiteral };\n/** @deprecated */\nfunction RegexLiteral(pattern: string, flags: string = \"\") {\n  deprecationWarning(\"RegexLiteral\", \"RegExpLiteral\", \"The node type \");\n  return regExpLiteral(pattern, flags);\n}\nexport { RegexLiteral as regexLiteral };\n/** @deprecated */\nfunction RestProperty(argument: t.LVal) {\n  deprecationWarning(\"RestProperty\", \"RestElement\", \"The node type \");\n  return restElement(argument);\n}\nexport { RestProperty as restProperty };\n/** @deprecated */\nfunction SpreadProperty(argument: t.Expression) {\n  deprecationWarning(\"SpreadProperty\", \"SpreadElement\", \"The node type \");\n  return spreadElement(argument);\n}\nexport { SpreadProperty as spreadProperty };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAAA,aAAA,GAAAC,OAAA;AAEA,IAAAC,mBAAA,GAAAD,OAAA;AACO,SAASE,eAAeA,CAC7BC,QAAsD,GAAG,EAAE,EACxC;EACnB,OAAO,IAAAC,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBF;EACF,CAAC,CAAC;AACJ;AACO,SAASG,oBAAoBA,CAClCC,QAAgB,EAChBC,IAAY,EACZC,KAAmB,EACK;EACxB,OAAO,IAAAL,qBAAY,EAAyB;IAC1CC,IAAI,EAAE,sBAAsB;IAC5BE,QAAQ;IACRC,IAAI;IACJC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,gBAAgBA,CAC9BH,QAuBQ,EACRC,IAAkC,EAClCC,KAAmB,EACC;EACpB,OAAO,IAAAL,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBE,QAAQ;IACRC,IAAI;IACJC;EACF,CAAC,CAAC;AACJ;AACO,SAASE,oBAAoBA,CAACC,KAAa,EAA0B;EAC1E,OAAO,IAAAR,qBAAY,EAAyB;IAC1CC,IAAI,EAAE,sBAAsB;IAC5BO;EACF,CAAC,CAAC;AACJ;AACO,SAASC,SAASA,CAACD,KAAyB,EAAe;EAChE,OAAO,IAAAR,qBAAY,EAAc;IAC/BC,IAAI,EAAE,WAAW;IACjBO;EACF,CAAC,CAAC;AACJ;AACO,SAASE,gBAAgBA,CAACF,KAAa,EAAsB;EAClE,OAAO,IAAAR,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBO;EACF,CAAC,CAAC;AACJ;AACO,SAASG,cAAcA,CAC5BC,IAAwB,EACxBC,UAA8B,GAAG,EAAE,EACjB;EAClB,OAAO,IAAAb,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBW,IAAI;IACJC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,cAAcA,CAC5BC,KAA0B,GAAG,IAAI,EACf;EAClB,OAAO,IAAAf,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBc;EACF,CAAC,CAAC;AACJ;AACO,SAASC,cAAcA,CAC5BC,MAAwD,EACxDC,UAEC,EACiB;EAClB,OAAO,IAAAlB,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBgB,MAAM;IACNE,SAAS,EAAED;EACb,CAAC,CAAC;AACJ;AACO,SAASE,WAAWA,CACzBC,KAKa,GAAG,IAAI,EACpBT,IAAsB,EACP;EACf,OAAO,IAAAZ,qBAAY,EAAgB;IACjCC,IAAI,EAAE,aAAa;IACnBoB,KAAK;IACLT;EACF,CAAC,CAAC;AACJ;AACO,SAASU,qBAAqBA,CACnCC,IAAkB,EAClBC,UAAwB,EACxBC,SAAuB,EACE;EACzB,OAAO,IAAAzB,qBAAY,EAA0B;IAC3CC,IAAI,EAAE,uBAAuB;IAC7BsB,IAAI;IACJC,UAAU;IACVC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,iBAAiBA,CAC/BX,KAA0B,GAAG,IAAI,EACZ;EACrB,OAAO,IAAAf,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzBc;EACF,CAAC,CAAC;AACJ;AACO,SAASY,iBAAiBA,CAAA,EAAwB;EACvD,OAAO;IACL1B,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAAS2B,gBAAgBA,CAC9BL,IAAkB,EAClBX,IAAiB,EACG;EACpB,OAAO,IAAAZ,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBsB,IAAI;IACJX;EACF,CAAC,CAAC;AACJ;AACO,SAASiB,cAAcA,CAAA,EAAqB;EACjD,OAAO;IACL5B,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAAS6B,mBAAmBA,CACjCC,UAAwB,EACD;EACvB,OAAO,IAAA/B,qBAAY,EAAwB;IACzCC,IAAI,EAAE,qBAAqB;IAC3B8B;EACF,CAAC,CAAC;AACJ;AACO,SAASC,IAAIA,CAClBC,OAAkB,EAClBC,QAAsD,GAAG,IAAI,EAC7DC,MAAyB,GAAG,IAAI,EACxB;EACR,OAAO,IAAAnC,qBAAY,EAAS;IAC1BC,IAAI,EAAE,MAAM;IACZgC,OAAO;IACPC,QAAQ;IACRC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,cAAcA,CAC5BhC,IAAoC,EACpCC,KAAmB,EACnBO,IAAiB,EACC;EAClB,OAAO,IAAAZ,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBG,IAAI;IACJC,KAAK;IACLO;EACF,CAAC,CAAC;AACJ;AACO,SAASyB,YAAYA,CAC1BC,IAA6D,GAAG,IAAI,EACpEf,IAAqC,GAAG,IAAI,EAC5CgB,MAAuC,GAAG,IAAI,EAC9C3B,IAAiB,EACD;EAChB,OAAO,IAAAZ,qBAAY,EAAiB;IAClCC,IAAI,EAAE,cAAc;IACpBqC,IAAI;IACJf,IAAI;IACJgB,MAAM;IACN3B;EACF,CAAC,CAAC;AACJ;AACO,SAAS4B,mBAAmBA,CACjCC,EAAmC,GAAG,IAAI,EAC1CC,MAAuD,EACvD9B,IAAsB,EACtB+B,SAAkB,GAAG,KAAK,EAC1BC,KAAc,GAAG,KAAK,EACC;EACvB,OAAO,IAAA5C,qBAAY,EAAwB;IACzCC,IAAI,EAAE,qBAAqB;IAC3BwC,EAAE;IACFC,MAAM;IACN9B,IAAI;IACJ+B,SAAS;IACTC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,kBAAkBA,CAChCJ,EAAmC,GAAG,IAAI,EAC1CC,MAAuD,EACvD9B,IAAsB,EACtB+B,SAAkB,GAAG,KAAK,EAC1BC,KAAc,GAAG,KAAK,EACA;EACtB,OAAO,IAAA5C,qBAAY,EAAuB;IACxCC,IAAI,EAAE,oBAAoB;IAC1BwC,EAAE;IACFC,MAAM;IACN9B,IAAI;IACJ+B,SAAS;IACTC;EACF,CAAC,CAAC;AACJ;AACO,SAASE,UAAUA,CAACC,IAAY,EAAgB;EACrD,OAAO,IAAA/C,qBAAY,EAAe;IAChCC,IAAI,EAAE,YAAY;IAClB8C;EACF,CAAC,CAAC;AACJ;AACO,SAASC,WAAWA,CACzBzB,IAAkB,EAClBC,UAAuB,EACvBC,SAA6B,GAAG,IAAI,EACrB;EACf,OAAO,IAAAzB,qBAAY,EAAgB;IACjCC,IAAI,EAAE,aAAa;IACnBsB,IAAI;IACJC,UAAU;IACVC;EACF,CAAC,CAAC;AACJ;AACO,SAASwB,gBAAgBA,CAC9BlC,KAAmB,EACnBH,IAAiB,EACG;EACpB,OAAO,IAAAZ,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBc,KAAK;IACLH;EACF,CAAC,CAAC;AACJ;AACO,SAASsC,aAAaA,CAAC1C,KAAa,EAAmB;EAC5D,OAAO,IAAAR,qBAAY,EAAkB;IACnCC,IAAI,EAAE,eAAe;IACrBO;EACF,CAAC,CAAC;AACJ;AACO,SAAS2C,cAAcA,CAAC3C,KAAa,EAAoB;EAC9D,OAAO,IAAAR,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBO;EACF,CAAC,CAAC;AACJ;AACO,SAAS4C,WAAWA,CAAA,EAAkB;EAC3C,OAAO;IACLnD,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAASoD,cAAcA,CAAC7C,KAAc,EAAoB;EAC/D,OAAO,IAAAR,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBO;EACF,CAAC,CAAC;AACJ;AACO,SAAS8C,aAAaA,CAC3BC,OAAe,EACfC,KAAa,GAAG,EAAE,EACD;EACjB,OAAO,IAAAxD,qBAAY,EAAkB;IACnCC,IAAI,EAAE,eAAe;IACrBsD,OAAO;IACPC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,iBAAiBA,CAC/BtD,QAA4B,EAC5BC,IAAkB,EAClBC,KAAmB,EACE;EACrB,OAAO,IAAAL,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzBE,QAAQ;IACRC,IAAI;IACJC;EACF,CAAC,CAAC;AACJ;AACO,SAASqD,gBAAgBA,CAC9BC,MAA8B,EAC9BC,QAAqD,EACrDC,QAAiB,GAAG,KAAK,EACzBC,QAA6B,GAAG,IAAI,EAChB;EACpB,OAAO,IAAA9D,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxB0D,MAAM;IACNC,QAAQ;IACRC,QAAQ;IACRC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,aAAaA,CAC3B9C,MAAwD,EACxDC,UAEC,EACgB;EACjB,OAAO,IAAAlB,qBAAY,EAAkB;IACnCC,IAAI,EAAE,eAAe;IACrBgB,MAAM;IACNE,SAAS,EAAED;EACb,CAAC,CAAC;AACJ;AACO,SAASe,OAAOA,CACrBrB,IAAwB,EACxBC,UAA8B,GAAG,EAAE,EACnCmD,UAA+B,GAAG,QAAQ,EAC1CC,WAA0C,GAAG,IAAI,EACtC;EACX,OAAO,IAAAjE,qBAAY,EAAY;IAC7BC,IAAI,EAAE,SAAS;IACfW,IAAI;IACJC,UAAU;IACVmD,UAAU;IACVC,WAAW;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;AACO,SAASC,gBAAgBA,CAC9BC,UAAsE,EAClD;EACpB,OAAO,IAAApE,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBmE;EACF,CAAC,CAAC;AACJ;AACO,SAASC,YAAYA,CAC1BC,IAA0C,GAAG,QAAQ,EACrDC,GAKmB,EACnB7B,MAAuD,EACvD9B,IAAsB,EACtBiD,QAAiB,GAAG,KAAK,EACzBlB,SAAkB,GAAG,KAAK,EAC1BC,KAAc,GAAG,KAAK,EACN;EAChB,OAAO,IAAA5C,qBAAY,EAAiB;IAClCC,IAAI,EAAE,cAAc;IACpBqE,IAAI;IACJC,GAAG;IACH7B,MAAM;IACN9B,IAAI;IACJiD,QAAQ;IACRlB,SAAS;IACTC;EACF,CAAC,CAAC;AACJ;AACO,SAAS4B,cAAcA,CAC5BD,GAOiB,EACjB/D,KAAmC,EACnCqD,QAAiB,GAAG,KAAK,EACzBY,SAAkB,GAAG,KAAK,EAC1BC,UAAqC,GAAG,IAAI,EAC1B;EAClB,OAAO,IAAA1E,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBsE,GAAG;IACH/D,KAAK;IACLqD,QAAQ;IACRY,SAAS;IACTC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,WAAWA,CAACC,QAAgB,EAAiB;EAC3D,OAAO,IAAA5E,qBAAY,EAAgB;IACjCC,IAAI,EAAE,aAAa;IACnB2E;EACF,CAAC,CAAC;AACJ;AACO,SAASC,eAAeA,CAC7BD,QAA6B,GAAG,IAAI,EACjB;EACnB,OAAO,IAAA5E,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvB2E;EACF,CAAC,CAAC;AACJ;AACO,SAASE,kBAAkBA,CAChCC,WAAgC,EACV;EACtB,OAAO,IAAA/E,qBAAY,EAAuB;IACxCC,IAAI,EAAE,oBAAoB;IAC1B8E;EACF,CAAC,CAAC;AACJ;AACO,SAASC,uBAAuBA,CACrCjD,UAAwB,EACG;EAC3B,OAAO,IAAA/B,qBAAY,EAA4B;IAC7CC,IAAI,EAAE,yBAAyB;IAC/B8B;EACF,CAAC,CAAC;AACJ;AACO,SAASkD,UAAUA,CACxB1D,IAAqC,GAAG,IAAI,EAC5CC,UAA8B,EAChB;EACd,OAAO,IAAAxB,qBAAY,EAAe;IAChCC,IAAI,EAAE,YAAY;IAClBsB,IAAI;IACJC;EACF,CAAC,CAAC;AACJ;AACO,SAAS0D,eAAeA,CAC7BC,YAA0B,EAC1BC,KAA0B,EACP;EACnB,OAAO,IAAApF,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBkF,YAAY;IACZC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,cAAcA,CAAA,EAAqB;EACjD,OAAO;IACLpF,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAASqF,cAAcA,CAACV,QAAsB,EAAoB;EACvE,OAAO,IAAA5E,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtB2E;EACF,CAAC,CAAC;AACJ;AACO,SAASW,YAAYA,CAC1BC,KAAuB,EACvBC,OAA6B,GAAG,IAAI,EACpCC,SAAkC,GAAG,IAAI,EACzB;EAChB,OAAO,IAAA1F,qBAAY,EAAiB;IAClCC,IAAI,EAAE,cAAc;IACpBuF,KAAK;IACLC,OAAO;IACPC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,eAAeA,CAC7BxF,QAAwE,EACxEyE,QAAsB,EACtBgB,MAAe,GAAG,IAAI,EACH;EACnB,OAAO,IAAA5F,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBE,QAAQ;IACRyE,QAAQ;IACRgB;EACF,CAAC,CAAC;AACJ;AACO,SAASC,gBAAgBA,CAC9B1F,QAAqB,EACrByE,QAAsB,EACtBgB,MAAe,GAAG,KAAK,EACH;EACpB,OAAO,IAAA5F,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBE,QAAQ;IACRyE,QAAQ;IACRgB;EACF,CAAC,CAAC;AACJ;AACO,SAASE,mBAAmBA,CACjCxB,IAAuC,EACvCyB,YAAyC,EAClB;EACvB,OAAO,IAAA/F,qBAAY,EAAwB;IACzCC,IAAI,EAAE,qBAAqB;IAC3BqE,IAAI;IACJyB;EACF,CAAC,CAAC;AACJ;AACO,SAASC,kBAAkBA,CAChCvD,EAAU,EACVH,IAAyB,GAAG,IAAI,EACV;EACtB,OAAO,IAAAtC,qBAAY,EAAuB;IACxCC,IAAI,EAAE,oBAAoB;IAC1BwC,EAAE;IACFH;EACF,CAAC,CAAC;AACJ;AACO,SAAS2D,cAAcA,CAC5B1E,IAAkB,EAClBX,IAAiB,EACC;EAClB,OAAO,IAAAZ,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBsB,IAAI;IACJX;EACF,CAAC,CAAC;AACJ;AACO,SAASsF,aAAaA,CAC3BvC,MAAoB,EACpB/C,IAAiB,EACA;EACjB,OAAO,IAAAZ,qBAAY,EAAkB;IACnCC,IAAI,EAAE,eAAe;IACrB0D,MAAM;IACN/C;EACF,CAAC,CAAC;AACJ;AACO,SAASuF,iBAAiBA,CAC/B/F,IAQyB,EACzBC,KAAmB,EACE;EACrB,OAAO,IAAAL,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzBG,IAAI;IACJC;EACF,CAAC,CAAC;AACJ;AACO,SAAS+F,YAAYA,CAC1BrG,QAA8C,EAC9B;EAChB,OAAO,IAAAC,qBAAY,EAAiB;IAClCC,IAAI,EAAE,cAAc;IACpBF;EACF,CAAC,CAAC;AACJ;AACO,SAASsG,uBAAuBA,CACrC3D,MAAuD,EACvD9B,IAAqC,EACrCgC,KAAc,GAAG,KAAK,EACK;EAC3B,OAAO,IAAA5C,qBAAY,EAA4B;IAC7CC,IAAI,EAAE,yBAAyB;IAC/ByC,MAAM;IACN9B,IAAI;IACJgC,KAAK;IACLb,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;AACO,SAASuE,SAASA,CACvB1F,IASC,EACY;EACb,OAAO,IAAAZ,qBAAY,EAAc;IAC/BC,IAAI,EAAE,WAAW;IACjBW;EACF,CAAC,CAAC;AACJ;AACO,SAAS2F,eAAeA,CAC7B9D,EAAmC,GAAG,IAAI,EAC1C+D,UAA2C,GAAG,IAAI,EAClD5F,IAAiB,EACjB8D,UAAqC,GAAG,IAAI,EACzB;EACnB,OAAO,IAAA1E,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBwC,EAAE;IACF+D,UAAU;IACV5F,IAAI;IACJ8D;EACF,CAAC,CAAC;AACJ;AACO,SAAS+B,gBAAgBA,CAC9BhE,EAAgB,EAChB+D,UAA2C,GAAG,IAAI,EAClD5F,IAAiB,EACjB8D,UAAqC,GAAG,IAAI,EACxB;EACpB,OAAO,IAAA1E,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBwC,EAAE;IACF+D,UAAU;IACV5F,IAAI;IACJ8D;EACF,CAAC,CAAC;AACJ;AACO,SAASgC,oBAAoBA,CAClCC,MAAuB,EACC;EACxB,OAAO,IAAA3G,qBAAY,EAAyB;IAC1CC,IAAI,EAAE,sBAAsB;IAC5B0G;EACF,CAAC,CAAC;AACJ;AACO,SAASC,wBAAwBA,CACtCC,WAIgB,EACY;EAC5B,OAAO,IAAA7G,qBAAY,EAA6B;IAC9CC,IAAI,EAAE,0BAA0B;IAChC4G;EACF,CAAC,CAAC;AACJ;AACO,SAASC,sBAAsBA,CACpCD,WAAiC,GAAG,IAAI,EACxCE,UAEC,GAAG,EAAE,EACNJ,MAA8B,GAAG,IAAI,EACX;EAC1B,OAAO,IAAA3G,qBAAY,EAA2B;IAC5CC,IAAI,EAAE,wBAAwB;IAC9B4G,WAAW;IACXE,UAAU;IACVJ;EACF,CAAC,CAAC;AACJ;AACO,SAASK,eAAeA,CAC7BC,KAAmB,EACnBC,QAAwC,EACrB;EACnB,OAAO,IAAAlH,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBgH,KAAK;IACLC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,cAAcA,CAC5B/G,IAAoC,EACpCC,KAAmB,EACnBO,IAAiB,EACjBwG,MAAe,GAAG,KAAK,EACL;EAClB,OAAO,IAAApH,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBG,IAAI;IACJC,KAAK;IACLO,IAAI;IACJyG,KAAK,EAAED;EACT,CAAC,CAAC;AACJ;AACO,SAASE,iBAAiBA,CAC/BP,UAEC,EACDJ,MAAuB,EACF;EACrB,OAAO,IAAA3G,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzB8G,UAAU;IACVJ;EACF,CAAC,CAAC;AACJ;AACO,SAASY,sBAAsBA,CACpCN,KAAmB,EACO;EAC1B,OAAO,IAAAjH,qBAAY,EAA2B;IAC5CC,IAAI,EAAE,wBAAwB;IAC9BgH;EACF,CAAC,CAAC;AACJ;AACO,SAASO,wBAAwBA,CACtCP,KAAmB,EACS;EAC5B,OAAO,IAAAjH,qBAAY,EAA6B;IAC9CC,IAAI,EAAE,0BAA0B;IAChCgH;EACF,CAAC,CAAC;AACJ;AACO,SAASQ,eAAeA,CAC7BR,KAAmB,EACnBS,QAAwC,EACrB;EACnB,OAAO,IAAA1H,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBgH,KAAK;IACLS;EACF,CAAC,CAAC;AACJ;AACO,SAASC,YAAYA,CAC1BC,IAAkB,EAClBhE,QAAsB,EACN;EAChB,OAAO,IAAA5D,qBAAY,EAAiB;IAClCC,IAAI,EAAE,cAAc;IACpB2H,IAAI;IACJhE;EACF,CAAC,CAAC;AACJ;AACO,SAASiE,WAAWA,CACzBvD,IAA0D,GAAG,QAAQ,EACrEC,GAKgB,EAChB7B,MAEC,EACD9B,IAAsB,EACtBiD,QAAiB,GAAG,KAAK,EACzBiE,OAAgB,GAAG,KAAK,EACxBnF,SAAkB,GAAG,KAAK,EAC1BC,KAAc,GAAG,KAAK,EACP;EACf,OAAO,IAAA5C,qBAAY,EAAgB;IACjCC,IAAI,EAAE,aAAa;IACnBqE,IAAI;IACJC,GAAG;IACH7B,MAAM;IACN9B,IAAI;IACJiD,QAAQ;IACRkE,MAAM,EAAED,OAAO;IACfnF,SAAS;IACTC;EACF,CAAC,CAAC;AACJ;AACO,SAASoF,aAAaA,CAC3B5D,UAAmD,EAClC;EACjB,OAAO,IAAApE,qBAAY,EAAkB;IACnCC,IAAI,EAAE,eAAe;IACrBmE;EACF,CAAC,CAAC;AACJ;AACO,SAAS6D,aAAaA,CAACrD,QAAsB,EAAmB;EACrE,OAAO,IAAA5E,qBAAY,EAAkB;IACnCC,IAAI,EAAE,eAAe;IACrB2E;EACF,CAAC,CAAC;AACJ;AACA,SAASsD,MAAMA,CAAA,EAAY;EACzB,OAAO;IACLjI,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAASkI,wBAAwBA,CACtCC,GAAiB,EACjBC,KAAwB,EACI;EAC5B,OAAO,IAAArI,qBAAY,EAA6B;IAC9CC,IAAI,EAAE,0BAA0B;IAChCmI,GAAG;IACHC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,eAAeA,CAC7B9H,KAAuC,EACvC+H,IAAa,GAAG,KAAK,EACF;EACnB,OAAO,IAAAvI,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBO,KAAK;IACL+H;EACF,CAAC,CAAC;AACJ;AACO,SAASC,eAAeA,CAC7BC,MAAgC,EAChC1D,WAA2C,EACxB;EACnB,OAAO,IAAA/E,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBwI,MAAM;IACN1D;EACF,CAAC,CAAC;AACJ;AACO,SAAS2D,eAAeA,CAC7B9D,QAA6B,GAAG,IAAI,EACpC+D,QAAiB,GAAG,KAAK,EACN;EACnB,OAAO,IAAA3I,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvB2E,QAAQ;IACR+D;EACF,CAAC,CAAC;AACJ;AACO,SAASC,eAAeA,CAAChE,QAAsB,EAAqB;EACzE,OAAO,IAAA5E,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvB2E;EACF,CAAC,CAAC;AACJ;AACA,SAASiE,OAAOA,CAAA,EAAa;EAC3B,OAAO;IACL5I,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAAS6I,aAAaA,CAACtI,KAAa,EAAmB;EAC5D,OAAO,IAAAR,qBAAY,EAAkB;IACnCC,IAAI,EAAE,eAAe;IACrBO;EACF,CAAC,CAAC;AACJ;AACO,SAASuI,wBAAwBA,CACtC7B,QAAsB,EACM;EAC5B,OAAO,IAAAlH,qBAAY,EAA6B;IAC9CC,IAAI,EAAE,0BAA0B;IAChCiH;EACF,CAAC,CAAC;AACJ;AACO,SAAS8B,wBAAwBA,CACtCrF,MAAoB,EACpBC,QAAqC,EACrCC,QAA6B,GAAG,KAAK,EACrCC,QAAiB,EACW;EAC5B,OAAO,IAAA9D,qBAAY,EAA6B;IAC9CC,IAAI,EAAE,0BAA0B;IAChC0D,MAAM;IACNC,QAAQ;IACRC,QAAQ;IACRC;EACF,CAAC,CAAC;AACJ;AACO,SAASmF,sBAAsBA,CACpChI,MAAoB,EACpBC,UAEC,EACD4C,QAAiB,EACS;EAC1B,OAAO,IAAA9D,qBAAY,EAA2B;IAC5CC,IAAI,EAAE,wBAAwB;IAC9BgB,MAAM;IACNE,SAAS,EAAED,UAAU;IACrB4C;EACF,CAAC,CAAC;AACJ;AACO,SAASoF,aAAaA,CAC3B3E,GAKgB,EAChB/D,KAA0B,GAAG,IAAI,EACjC2I,cAAqE,GAAG,IAAI,EAC5EzE,UAAqC,GAAG,IAAI,EAC5Cb,QAAiB,GAAG,KAAK,EACzBiE,OAAgB,GAAG,KAAK,EACP;EACjB,OAAO,IAAA9H,qBAAY,EAAkB;IACnCC,IAAI,EAAE,eAAe;IACrBsE,GAAG;IACH/D,KAAK;IACL2I,cAAc;IACdzE,UAAU;IACVb,QAAQ;IACRkE,MAAM,EAAED;EACV,CAAC,CAAC;AACJ;AACO,SAASsB,qBAAqBA,CACnC7E,GAMiB,EACjB/D,KAA0B,GAAG,IAAI,EACjC2I,cAAqE,GAAG,IAAI,EAC5EzE,UAAqC,GAAG,IAAI,EAC5Cb,QAAiB,GAAG,KAAK,EACzBiE,OAAgB,GAAG,KAAK,EACC;EACzB,OAAO,IAAA9H,qBAAY,EAA0B;IAC3CC,IAAI,EAAE,uBAAuB;IAC7BsE,GAAG;IACH/D,KAAK;IACL2I,cAAc;IACdzE,UAAU;IACVb,QAAQ;IACRkE,MAAM,EAAED;EACV,CAAC,CAAC;AACJ;AACO,SAASuB,oBAAoBA,CAClC9E,GAAkB,EAClB/D,KAA0B,GAAG,IAAI,EACjCkE,UAAqC,GAAG,IAAI,EAC5CoD,OAAgB,GAAG,KAAK,EACA;EACxB,OAAO,IAAA9H,qBAAY,EAAyB;IAC1CC,IAAI,EAAE,sBAAsB;IAC5BsE,GAAG;IACH/D,KAAK;IACLkE,UAAU;IACVqD,MAAM,EAAED;EACV,CAAC,CAAC;AACJ;AACO,SAASwB,kBAAkBA,CAChChF,IAA0C,GAAG,QAAQ,EACrDC,GAAkB,EAClB7B,MAEC,EACD9B,IAAsB,EACtBkH,OAAgB,GAAG,KAAK,EACF;EACtB,OAAO,IAAA9H,qBAAY,EAAuB;IACxCC,IAAI,EAAE,oBAAoB;IAC1BqE,IAAI;IACJC,GAAG;IACH7B,MAAM;IACN9B,IAAI;IACJmH,MAAM,EAAED;EACV,CAAC,CAAC;AACJ;AACO,SAASyB,WAAWA,CAAC9G,EAAgB,EAAiB;EAC3D,OAAO,IAAAzC,qBAAY,EAAgB;IACjCC,IAAI,EAAE,aAAa;IACnBwC;EACF,CAAC,CAAC;AACJ;AACO,SAAS+G,WAAWA,CAAC5I,IAAwB,EAAiB;EACnE,OAAO,IAAAZ,qBAAY,EAAgB;IACjCC,IAAI,EAAE,aAAa;IACnBW;EACF,CAAC,CAAC;AACJ;AACO,SAAS6I,iBAAiBA,CAAA,EAAwB;EACvD,OAAO;IACLxJ,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAASyJ,mBAAmBA,CACjCC,WAAuB,EACA;EACvB,OAAO,IAAA3J,qBAAY,EAAwB;IACzCC,IAAI,EAAE,qBAAqB;IAC3B0J;EACF,CAAC,CAAC;AACJ;AACO,SAASC,qBAAqBA,CAAA,EAA4B;EAC/D,OAAO;IACL3J,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAAS4J,4BAA4BA,CAC1CrJ,KAAc,EACkB;EAChC,OAAO,IAAAR,qBAAY,EAAiC;IAClDC,IAAI,EAAE,8BAA8B;IACpCO;EACF,CAAC,CAAC;AACJ;AACO,SAASsJ,yBAAyBA,CAAA,EAAgC;EACvE,OAAO;IACL7J,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAAS8J,eAAeA,CAC7BtH,EAAgB,EAChBuH,cAAmD,GAAG,IAAI,EACvC;EACnB,OAAO,IAAAhK,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBwC,EAAE;IACFuH;EACF,CAAC,CAAC;AACJ;AACO,SAASC,YAAYA,CAC1BxH,EAAgB,EAChBuH,cAA6D,GAAG,IAAI,EACpEE,QAAsD,GAAG,IAAI,EAC7DtJ,IAA4B,EACZ;EAChB,OAAO,IAAAZ,qBAAY,EAAiB;IAClCC,IAAI,EAAE,cAAc;IACpBwC,EAAE;IACFuH,cAAc;IACdG,OAAO,EAAED,QAAQ;IACjBtJ;EACF,CAAC,CAAC;AACJ;AACO,SAASwJ,eAAeA,CAAC3H,EAAgB,EAAqB;EACnE,OAAO,IAAAzC,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBwC;EACF,CAAC,CAAC;AACJ;AACO,SAAS4H,gBAAgBA,CAC9B5H,EAAgB,EAChBuH,cAA6D,GAAG,IAAI,EACpEE,QAAsD,GAAG,IAAI,EAC7DtJ,IAA4B,EACR;EACpB,OAAO,IAAAZ,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBwC,EAAE;IACFuH,cAAc;IACdG,OAAO,EAAED,QAAQ;IACjBtJ;EACF,CAAC,CAAC;AACJ;AACO,SAAS0J,aAAaA,CAC3B7H,EAAkC,EAClC7B,IAAsB,EACtB0D,IAA8B,GAAG,IAAI,EACpB;EACjB,OAAO,IAAAtE,qBAAY,EAAkB;IACnCC,IAAI,EAAE,eAAe;IACrBwC,EAAE;IACF7B,IAAI;IACJ0D;EACF,CAAC,CAAC;AACJ;AACO,SAASiG,oBAAoBA,CAClCpB,cAAgC,EACR;EACxB,OAAO,IAAAnJ,qBAAY,EAAyB;IAC1CC,IAAI,EAAE,sBAAsB;IAC5BkJ;EACF,CAAC,CAAC;AACJ;AACO,SAASqB,gBAAgBA,CAC9B/H,EAAgB,EAChBuH,cAA6D,GAAG,IAAI,EACpE3J,KAAiB,EACG;EACpB,OAAO,IAAAL,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBwC,EAAE;IACFuH,cAAc;IACd3J;EACF,CAAC,CAAC;AACJ;AACO,SAASoK,iBAAiBA,CAC/BhI,EAAgB,EAChBuH,cAAiD,GAAG,IAAI,EACxDU,SAA4B,GAAG,IAAI,EACd;EACrB,OAAO,IAAA1K,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzBwC,EAAE;IACFuH,cAAc;IACdU;EACF,CAAC,CAAC;AACJ;AACO,SAASC,eAAeA,CAAClI,EAAgB,EAAqB;EACnE,OAAO,IAAAzC,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBwC;EACF,CAAC,CAAC;AACJ;AACO,SAASmI,wBAAwBA,CACtC/D,WAA0B,GAAG,IAAI,EACjCE,UAEQ,GAAG,IAAI,EACfJ,MAA8B,GAAG,IAAI,EACT;EAC5B,OAAO,IAAA3G,qBAAY,EAA6B;IAC9CC,IAAI,EAAE,0BAA0B;IAChC4G,WAAW;IACXE,UAAU;IACVJ;EACF,CAAC,CAAC;AACJ;AACO,SAASkE,2BAA2BA,CACzClE,MAAuB,EACQ;EAC/B,OAAO,IAAA3G,qBAAY,EAAgC;IACjDC,IAAI,EAAE,6BAA6B;IACnC0G;EACF,CAAC,CAAC;AACJ;AACO,SAASmE,iBAAiBA,CAACtK,KAAa,EAAuB;EACpE,OAAO,IAAAR,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzBO;EACF,CAAC,CAAC;AACJ;AACO,SAASuK,oBAAoBA,CAAA,EAA2B;EAC7D,OAAO;IACL9K,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAAS+K,sBAAsBA,CACpChB,cAA6D,GAAG,IAAI,EACpEtH,MAAkC,EAClCuI,IAA4C,GAAG,IAAI,EACnDC,UAAsB,EACI;EAC1B,OAAO,IAAAlL,qBAAY,EAA2B;IAC5CC,IAAI,EAAE,wBAAwB;IAC9B+J,cAAc;IACdtH,MAAM;IACNuI,IAAI;IACJC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,iBAAiBA,CAC/BpI,IAAqC,GAAG,IAAI,EAC5CoG,cAA0B,EACL;EACrB,OAAO,IAAAnJ,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzB8C,IAAI;IACJoG;EACF,CAAC,CAAC;AACJ;AACO,SAASiC,qBAAqBA,CACnC3I,EAA4C,EAC5CuH,cAAmD,GAAG,IAAI,EACjC;EACzB,OAAO,IAAAhK,qBAAY,EAA0B;IAC3CC,IAAI,EAAE,uBAAuB;IAC7BwC,EAAE;IACFuH;EACF,CAAC,CAAC;AACJ;AACO,SAASqB,iBAAiBA,CAAA,EAAwB;EACvD,OAAO;IACLpL,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAASqL,gBAAgBA,CAC9B7I,EAA4C,EAC5CuH,cAAmD,GAAG,IAAI,EACtC;EACpB,OAAO,IAAAhK,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBwC,EAAE;IACFuH;EACF,CAAC,CAAC;AACJ;AACO,SAASuB,oBAAoBA,CAClC9I,EAAgB,EAChBuH,cAA6D,GAAG,IAAI,EACpEE,QAAsD,GAAG,IAAI,EAC7DtJ,IAA4B,EACJ;EACxB,OAAO,IAAAZ,qBAAY,EAAyB;IAC1CC,IAAI,EAAE,sBAAsB;IAC5BwC,EAAE;IACFuH,cAAc;IACdG,OAAO,EAAED,QAAQ;IACjBtJ;EACF,CAAC,CAAC;AACJ;AACO,SAAS4K,uBAAuBA,CACrCtB,QAAsD,GAAG,IAAI,EAC7DtJ,IAA4B,EACD;EAC3B,OAAO,IAAAZ,qBAAY,EAA4B;IAC7CC,IAAI,EAAE,yBAAyB;IAC/BkK,OAAO,EAAED,QAAQ;IACjBtJ;EACF,CAAC,CAAC;AACJ;AACO,SAAS6K,0BAA0BA,CACxCC,KAAwB,EACM;EAC9B,OAAO,IAAA1L,qBAAY,EAA+B;IAChDC,IAAI,EAAE,4BAA4B;IAClCyL;EACF,CAAC,CAAC;AACJ;AACO,SAASC,mBAAmBA,CAAA,EAA0B;EAC3D,OAAO;IACL1L,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAAS2L,mBAAmBA,CAAA,EAA0B;EAC3D,OAAO;IACL3L,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAAS4L,sBAAsBA,CACpC1C,cAA0B,EACA;EAC1B,OAAO,IAAAnJ,qBAAY,EAA2B;IAC5CC,IAAI,EAAE,wBAAwB;IAC9BkJ;EACF,CAAC,CAAC;AACJ;AACO,SAAS2C,2BAA2BA,CACzCtL,KAAa,EACkB;EAC/B,OAAO,IAAAR,qBAAY,EAAgC;IACjDC,IAAI,EAAE,6BAA6B;IACnCO;EACF,CAAC,CAAC;AACJ;AACO,SAASuL,oBAAoBA,CAAA,EAA2B;EAC7D,OAAO;IACL9L,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAAS+L,oBAAoBA,CAClC5H,UAAoE,EACpE6H,QAAoC,GAAG,EAAE,EACzCC,cAA+C,GAAG,EAAE,EACpDC,aAA8C,GAAG,EAAE,EACnDC,KAAc,GAAG,KAAK,EACE;EACxB,OAAO,IAAApM,qBAAY,EAAyB;IAC1CC,IAAI,EAAE,sBAAsB;IAC5BmE,UAAU;IACV6H,QAAQ;IACRC,cAAc;IACdC,aAAa;IACbC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,sBAAsBA,CACpC5J,EAAgB,EAChBjC,KAAiB,EACjBsD,QAAiB,EACjBgE,OAAgB,EAChBwE,MAAe,EACW;EAC1B,OAAO,IAAAtM,qBAAY,EAA2B;IAC5CC,IAAI,EAAE,wBAAwB;IAC9BwC,EAAE;IACFjC,KAAK;IACLsD,QAAQ;IACRiE,MAAM,EAAED,OAAO;IACfwE;EACF,CAAC,CAAC;AACJ;AACO,SAASC,sBAAsBA,CACpC/L,KAAiB,EACS;EAC1B,OAAO,IAAAR,qBAAY,EAA2B;IAC5CC,IAAI,EAAE,wBAAwB;IAC9BO,KAAK;IACLuH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AACO,SAASyE,iBAAiBA,CAC/B/J,EAAmC,GAAG,IAAI,EAC1C8B,GAAe,EACf/D,KAAiB,EACjBiM,QAA2B,GAAG,IAAI,EACb;EACrB,OAAO,IAAAzM,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzBwC,EAAE;IACF8B,GAAG;IACH/D,KAAK;IACLiM,QAAQ;IACR1E,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AACO,SAAS2E,kBAAkBA,CAChCnI,GAAmC,EACnC/D,KAAiB,EACjBiM,QAA2B,GAAG,IAAI,EACZ;EACtB,OAAO,IAAAzM,qBAAY,EAAuB;IACxCC,IAAI,EAAE,oBAAoB;IAC1BsE,GAAG;IACH/D,KAAK;IACLiM,QAAQ;IACRnI,IAAI,EAAE,IAAI;IACVgI,MAAM,EAAE,IAAI;IACZxI,QAAQ,EAAE,IAAI;IACd6I,KAAK,EAAE,IAAI;IACX5E,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AACO,SAAS6E,wBAAwBA,CACtChI,QAAoB,EACQ;EAC5B,OAAO,IAAA5E,qBAAY,EAA6B;IAC9CC,IAAI,EAAE,0BAA0B;IAChC2E;EACF,CAAC,CAAC;AACJ;AACO,SAASiI,UAAUA,CACxBpK,EAAgB,EAChBuH,cAA6D,GAAG,IAAI,EACpEU,SAAwC,GAAG,IAAI,EAC/CoC,QAAoB,EACN;EACd,OAAO,IAAA9M,qBAAY,EAAe;IAChCC,IAAI,EAAE,YAAY;IAClBwC,EAAE;IACFuH,cAAc;IACdU,SAAS;IACToC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,uBAAuBA,CACrCtK,EAAgB,EAChBuK,aAAuD,EAC5B;EAC3B,OAAO,IAAAhN,qBAAY,EAA4B;IAC7CC,IAAI,EAAE,yBAAyB;IAC/BwC,EAAE;IACFuK;EACF,CAAC,CAAC;AACJ;AACO,SAASC,2BAA2BA,CACzCzM,KAAa,EACkB;EAC/B,OAAO,IAAAR,qBAAY,EAAgC;IACjDC,IAAI,EAAE,6BAA6B;IACnCO;EACF,CAAC,CAAC;AACJ;AACO,SAAS0M,oBAAoBA,CAAA,EAA2B;EAC7D,OAAO;IACLjN,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAASkN,oBAAoBA,CAAA,EAA2B;EAC7D,OAAO;IACLlN,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAASmN,kBAAkBA,CAAA,EAAyB;EACzD,OAAO;IACLnN,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAASoN,mBAAmBA,CACjC3B,KAAwB,EACD;EACvB,OAAO,IAAA1L,qBAAY,EAAwB;IACzCC,IAAI,EAAE,qBAAqB;IAC3ByL;EACF,CAAC,CAAC;AACJ;AACO,SAAS4B,oBAAoBA,CAClC1I,QAAoB,EACI;EACxB,OAAO,IAAA5E,qBAAY,EAAyB;IAC1CC,IAAI,EAAE,sBAAsB;IAC5B2E;EACF,CAAC,CAAC;AACJ;AACO,SAAS2I,SAASA,CACvB9K,EAAgB,EAChBuH,cAA6D,GAAG,IAAI,EACpE3J,KAAiB,EACJ;EACb,OAAO,IAAAL,qBAAY,EAAc;IAC/BC,IAAI,EAAE,WAAW;IACjBwC,EAAE;IACFuH,cAAc;IACd3J;EACF,CAAC,CAAC;AACJ;AACO,SAAS8I,cAAcA,CAACA,cAA0B,EAAoB;EAC3E,OAAO,IAAAnJ,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBkJ;EACF,CAAC,CAAC;AACJ;AACO,SAASqE,kBAAkBA,CAChCzL,UAAwB,EACxBoH,cAAgC,EACV;EACtB,OAAO,IAAAnJ,qBAAY,EAAuB;IACxCC,IAAI,EAAE,oBAAoB;IAC1B8B,UAAU;IACVoH;EACF,CAAC,CAAC;AACJ;AACO,SAASsE,aAAaA,CAC3BC,KAA8B,GAAG,IAAI,EACrCC,QAA2B,GAAG,IAAI,EAClClB,QAA2B,GAAG,IAAI,EACjB;EACjB,OAAO,IAAAzM,qBAAY,EAAkB;IACnCC,IAAI,EAAE,eAAe;IACrByN,KAAK;IACLE,OAAO,EAAED,QAAQ;IACjBlB,QAAQ;IACR1J,IAAI,EAAE;EACR,CAAC,CAAC;AACJ;AACO,SAAS8K,wBAAwBA,CACtCnL,MAA8B,EACF;EAC5B,OAAO,IAAA1C,qBAAY,EAA6B;IAC9CC,IAAI,EAAE,0BAA0B;IAChCyC;EACF,CAAC,CAAC;AACJ;AACO,SAASoL,0BAA0BA,CACxCpL,MAAyB,EACK;EAC9B,OAAO,IAAA1C,qBAAY,EAA+B;IAChDC,IAAI,EAAE,4BAA4B;IAClCyC;EACF,CAAC,CAAC;AACJ;AACO,SAASqL,mBAAmBA,CACjCrC,KAAwB,EACD;EACvB,OAAO,IAAA1L,qBAAY,EAAwB;IACzCC,IAAI,EAAE,qBAAqB;IAC3ByL;EACF,CAAC,CAAC;AACJ;AACO,SAASe,QAAQA,CAACnI,IAAsB,EAAc;EAC3D,OAAO,IAAAtE,qBAAY,EAAa;IAC9BC,IAAI,EAAE,UAAU;IAChBqE;EACF,CAAC,CAAC;AACJ;AACO,SAAS0J,kBAAkBA,CAAA,EAAyB;EACzD,OAAO;IACL/N,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAASgO,eAAeA,CAC7BxL,EAAgB,EAChB7B,IAIoB,EACD;EACnB,OAAO,IAAAZ,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBwC,EAAE;IACF7B;EACF,CAAC,CAAC;AACJ;AACO,SAASsN,eAAeA,CAC7BC,OAAmC,EAChB;EACnB,OAAO,IAAAnO,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBkO,OAAO;IACPC,YAAY,EAAE,IAAI;IAClBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;AACJ;AACO,SAASC,cAAcA,CAC5BH,OAAkC,EAChB;EAClB,OAAO,IAAAnO,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBkO,OAAO;IACPC,YAAY,EAAE,IAAI;IAClBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;AACJ;AACO,SAASE,cAAcA,CAC5BJ,OAA0D,EACxC;EAClB,OAAO,IAAAnO,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBkO,OAAO;IACPC,YAAY,EAAE,IAAI;IAClBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;AACJ;AACO,SAASG,cAAcA,CAC5BL,OAAqC,EACnB;EAClB,OAAO,IAAAnO,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBkO,OAAO;IACPE,iBAAiB,EAAE;EACrB,CAAC,CAAC;AACJ;AACO,SAASI,iBAAiBA,CAAChM,EAAgB,EAAuB;EACvE,OAAO,IAAAzC,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzBwC,EAAE;IACFH,IAAI,EAAE;EACR,CAAC,CAAC;AACJ;AACO,SAASoM,gBAAgBA,CAC9BjM,EAAgB,EAChBH,IAAsB,EACF;EACpB,OAAO,IAAAtC,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBwC,EAAE;IACFH;EACF,CAAC,CAAC;AACJ;AACO,SAASqM,gBAAgBA,CAC9BlM,EAAgB,EAChBH,IAAqB,EACD;EACpB,OAAO,IAAAtC,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBwC,EAAE;IACFH;EACF,CAAC,CAAC;AACJ;AACO,SAASsM,mBAAmBA,CAACnM,EAAgB,EAAyB;EAC3E,OAAO,IAAAzC,qBAAY,EAAwB;IACzCC,IAAI,EAAE,qBAAqB;IAC3BwC;EACF,CAAC,CAAC;AACJ;AACO,SAASoM,iBAAiBA,CAC/BC,UAAsB,EACtBC,SAAqB,EACA;EACrB,OAAO,IAAA/O,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzB6O,UAAU;IACVC;EACF,CAAC,CAAC;AACJ;AACO,SAASC,yBAAyBA,CACvCF,UAAsB,EACtBC,SAAqB,EACQ;EAC7B,OAAO,IAAA/O,qBAAY,EAA8B;IAC/CC,IAAI,EAAE,2BAA2B;IACjC6O,UAAU;IACVC,SAAS;IACTjL,QAAQ,EAAE;EACZ,CAAC,CAAC;AACJ;AACO,SAASmL,YAAYA,CAC1BlM,IAA2C,EAC3CvC,KAKQ,GAAG,IAAI,EACC;EAChB,OAAO,IAAAR,qBAAY,EAAiB;IAClCC,IAAI,EAAE,cAAc;IACpB8C,IAAI;IACJvC;EACF,CAAC,CAAC;AACJ;AAEO,SAAS0O,iBAAiBA,CAC/BnM,IAAmE,EAC9C;EACrB,OAAO,IAAA/C,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzB8C;EACF,CAAC,CAAC;AACJ;AAEO,SAASoM,UAAUA,CACxBC,cAAmC,EACnCC,cAAsD,GAAG,IAAI,EAC7DC,QAMC,EACDC,WAA2B,GAAG,IAAI,EACpB;EACd,OAAO,IAAAvP,qBAAY,EAAe;IAChCC,IAAI,EAAE,YAAY;IAClBmP,cAAc;IACdC,cAAc;IACdC,QAAQ;IACRC;EACF,CAAC,CAAC;AACJ;AAEO,SAASC,kBAAkBA,CAAA,EAAyB;EACzD,OAAO;IACLvP,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAASwP,sBAAsBA,CACpC1N,UAA+C,EACrB;EAC1B,OAAO,IAAA/B,qBAAY,EAA2B;IAC5CC,IAAI,EAAE,wBAAwB;IAC9B8B;EACF,CAAC,CAAC;AACJ;AAEO,SAAS2N,cAAcA,CAAC3N,UAAwB,EAAoB;EACzE,OAAO,IAAA/B,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtB8B;EACF,CAAC,CAAC;AACJ;AAEO,SAAS4N,aAAaA,CAAC5M,IAAY,EAAmB;EAC3D,OAAO,IAAA/C,qBAAY,EAAkB;IACnCC,IAAI,EAAE,eAAe;IACrB8C;EACF,CAAC,CAAC;AACJ;AAEO,SAAS6M,mBAAmBA,CACjCjM,MAA+C,EAC/CC,QAAyB,EACF;EACvB,OAAO,IAAA5D,qBAAY,EAAwB;IACzCC,IAAI,EAAE,qBAAqB;IAC3B0D,MAAM;IACNC;EACF,CAAC,CAAC;AACJ;AAEO,SAASiM,iBAAiBA,CAC/BC,SAA0B,EAC1B/M,IAAqB,EACA;EACrB,OAAO,IAAA/C,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzB6P,SAAS;IACT/M;EACF,CAAC,CAAC;AACJ;AAEO,SAASgN,iBAAiBA,CAC/BhN,IAAmE,EACnEiN,UAAwD,EACxDT,WAAoB,GAAG,KAAK,EACP;EACrB,OAAO,IAAAvP,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzB8C,IAAI;IACJiN,UAAU;IACVT;EACF,CAAC,CAAC;AACJ;AAEO,SAASU,kBAAkBA,CAChCrL,QAAsB,EACA;EACtB,OAAO,IAAA5E,qBAAY,EAAuB;IACxCC,IAAI,EAAE,oBAAoB;IAC1B2E;EACF,CAAC,CAAC;AACJ;AAEO,SAASsL,OAAOA,CAAC1P,KAAa,EAAa;EAChD,OAAO,IAAAR,qBAAY,EAAY;IAC7BC,IAAI,EAAE,SAAS;IACfO;EACF,CAAC,CAAC;AACJ;AAEO,SAAS2P,WAAWA,CACzBC,eAAqC,EACrCC,eAAqC,EACrCf,QAMC,EACc;EACf,OAAO,IAAAtP,qBAAY,EAAgB;IACjCC,IAAI,EAAE,aAAa;IACnBmQ,eAAe;IACfC,eAAe;IACff;EACF,CAAC,CAAC;AACJ;AAEO,SAASgB,kBAAkBA,CAAA,EAAyB;EACzD,OAAO;IACLrQ,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAASsQ,kBAAkBA,CAAA,EAAyB;EACzD,OAAO;IACLtQ,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAASuQ,IAAIA,CAAA,EAAW;EAC7B,OAAO;IACLvQ,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAASwQ,WAAWA,CACzBC,YAQa,EACb3N,IAAkB,EACH;EACf,OAAO,IAAA/C,qBAAY,EAAgB;IACjCC,IAAI,EAAE,aAAa;IACnByQ,YAAY;IACZ3N;EACF,CAAC,CAAC;AACJ;AACO,SAAS4N,qBAAqBA,CAAC5N,IAAY,EAA2B;EAC3E,OAAO,IAAA/C,qBAAY,EAA0B;IAC3CC,IAAI,EAAE,uBAAuB;IAC7B8C;EACF,CAAC,CAAC;AACJ;AACO,SAAS6N,mBAAmBA,CAAA,EAA0B;EAC3D,OAAO;IACL3Q,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAAS4Q,cAAcA,CAC5BlN,MAAoB,EACpB1C,MAAoB,EACF;EAClB,OAAO,IAAAjB,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtB0D,MAAM;IACN1C;EACF,CAAC,CAAC;AACJ;AACO,SAAS6P,eAAeA,CAC7BvM,GAAmC,EACnC/D,KAAsB,EACH;EACnB,OAAO,IAAAR,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBsE,GAAG;IACH/D;EACF,CAAC,CAAC;AACJ;AACO,SAASuQ,SAASA,CAAChP,UAAwB,EAAe;EAC/D,OAAO,IAAA/B,qBAAY,EAAc;IAC/BC,IAAI,EAAE,WAAW;IACjB8B;EACF,CAAC,CAAC;AACJ;AACO,SAASiP,YAAYA,CAC1BpQ,IAAsB,EACtBgC,KAAc,GAAG,KAAK,EACN;EAChB,OAAO,IAAA5C,qBAAY,EAAiB;IAClCC,IAAI,EAAE,cAAc;IACpBW,IAAI;IACJgC;EACF,CAAC,CAAC;AACJ;AACO,SAASqO,sBAAsBA,CACpC/J,QAAsB,EACI;EAC1B,OAAO,IAAAlH,qBAAY,EAA2B;IAC5CC,IAAI,EAAE,wBAAwB;IAC9BiH;EACF,CAAC,CAAC;AACJ;AACO,SAASgK,gBAAgBA,CAC9B9M,UAAqD,EACjC;EACpB,OAAO,IAAApE,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBmE;EACF,CAAC,CAAC;AACJ;AACO,SAAS+M,eAAeA,CAC7BpR,QAA+C,GAAG,EAAE,EACjC;EACnB,OAAO,IAAAC,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBF;EACF,CAAC,CAAC;AACJ;AACO,SAASqR,cAAcA,CAAC5Q,KAAa,EAAoB;EAC9D,OAAO,IAAAR,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBO;EACF,CAAC,CAAC;AACJ;AACO,SAAS6Q,gBAAgBA,CAACzQ,IAAe,EAAsB;EACpE,OAAO,IAAAZ,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBW;EACF,CAAC,CAAC;AACJ;AACO,SAAS0Q,cAAcA,CAAA,EAAqB;EACjD,OAAO;IACLrR,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAASsR,uBAAuBA,CACrCxP,UAAwB,EACG;EAC3B,OAAO,IAAA/B,qBAAY,EAA4B;IAC7CC,IAAI,EAAE,yBAAyB;IAC/B8B;EACF,CAAC,CAAC;AACJ;AACO,SAASyP,oBAAoBA,CAClCvQ,MAAoB,EACI;EACxB,OAAO,IAAAjB,qBAAY,EAAyB;IAC1CC,IAAI,EAAE,sBAAsB;IAC5BgB;EACF,CAAC,CAAC;AACJ;AACO,SAASwQ,6BAA6BA,CAAA,EAAoC;EAC/E,OAAO;IACLxR,IAAI,EAAE;EACR,CAAC;AACH;AACO,SAASyR,mBAAmBA,CACjCC,SAA6C,EACtB;EACvB,OAAO,IAAA3R,qBAAY,EAAwB;IACzCC,IAAI,EAAE,qBAAqB;IAC3B0R;EACF,CAAC,CAAC;AACJ;AAEO,SAASC,iBAAiBA,CAC/BnP,EAAmC,GAAG,IAAI,EAC1CuH,cAIa,GAAG,IAAI,EACpBtH,MAAuD,EACvDwI,UAA8C,GAAG,IAAI,EAChC;EACrB,OAAO,IAAAlL,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzBwC,EAAE;IACFuH,cAAc;IACdtH,MAAM;IACNwI;EACF,CAAC,CAAC;AACJ;AAEO,SAAS2G,eAAeA,CAC7BnN,UAAiD,GAAG,IAAI,EACxDH,GAKgB,EAChByF,cAIa,GAAG,IAAI,EACpBtH,MAEC,EACDwI,UAA8C,GAAG,IAAI,EAClC;EACnB,OAAO,IAAAlL,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvByE,UAAU;IACVH,GAAG;IACHyF,cAAc;IACdtH,MAAM;IACNwI;EACF,CAAC,CAAC;AACJ;AAEO,SAAS4G,eAAeA,CAC7B1R,IAAoB,EACpBC,KAAmB,EACA;EACnB,OAAO,IAAAL,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBG,IAAI;IACJC;EACF,CAAC,CAAC;AACJ;AAEO,SAAS0R,0BAA0BA,CACxC/H,cAA+D,GAAG,IAAI,EACtEgI,UAA+C,EAC/C7I,cAAyC,GAAG,IAAI,EAClB;EAC9B,OAAO,IAAAnJ,qBAAY,EAA+B;IAChDC,IAAI,EAAE,4BAA4B;IAClC+J,cAAc;IACdgI,UAAU;IACV7I;EACF,CAAC,CAAC;AACJ;AAEO,SAAS8I,+BAA+BA,CAC7CjI,cAA+D,GAAG,IAAI,EACtEgI,UAA+C,EAC/C7I,cAAyC,GAAG,IAAI,EACb;EACnC,OAAO,IAAAnJ,qBAAY,EAAoC;IACrDC,IAAI,EAAE,iCAAiC;IACvC+J,cAAc;IACdgI,UAAU;IACV7I;EACF,CAAC,CAAC;AACJ;AAEO,SAAS+I,mBAAmBA,CACjC3N,GAAiB,EACjB4E,cAAyC,GAAG,IAAI,EAChDgJ,WAAgC,GAAG,IAAI,EAChB;EACvB,OAAO,IAAAnS,qBAAY,EAAwB;IACzCC,IAAI,EAAE,qBAAqB;IAC3BsE,GAAG;IACH4E,cAAc;IACdgJ,WAAW;IACX7N,IAAI,EAAE;EACR,CAAC,CAAC;AACJ;AAEO,SAAS8N,iBAAiBA,CAC/B7N,GAAiB,EACjByF,cAA+D,GAAG,IAAI,EACtEgI,UAA+C,EAC/C7I,cAAyC,GAAG,IAAI,EAC3B;EACrB,OAAO,IAAAnJ,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzBsE,GAAG;IACHyF,cAAc;IACdgI,UAAU;IACV7I,cAAc;IACd7E,IAAI,EAAE;EACR,CAAC,CAAC;AACJ;AAEO,SAAS+N,gBAAgBA,CAC9BL,UAA+B,EAC/B7I,cAAyC,GAAG,IAAI,EAC5B;EACpB,OAAO,IAAAnJ,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxB+R,UAAU;IACV7I;EACF,CAAC,CAAC;AACJ;AAEO,SAASmJ,YAAYA,CAAA,EAAmB;EAC7C,OAAO;IACLrS,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAASsS,gBAAgBA,CAAA,EAAuB;EACrD,OAAO;IACLtS,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAASuS,eAAeA,CAAA,EAAsB;EACnD,OAAO;IACLvS,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAASwS,kBAAkBA,CAAA,EAAyB;EACzD,OAAO;IACLxS,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAASyS,cAAcA,CAAA,EAAqB;EACjD,OAAO;IACLzS,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAAS0S,aAAaA,CAAA,EAAoB;EAC/C,OAAO;IACL1S,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAAS2S,eAAeA,CAAA,EAAsB;EACnD,OAAO;IACL3S,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAAS4S,eAAeA,CAAA,EAAsB;EACnD,OAAO;IACL5S,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAAS6S,eAAeA,CAAA,EAAsB;EACnD,OAAO;IACL7S,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAAS8S,eAAeA,CAAA,EAAsB;EACnD,OAAO;IACL9S,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAAS+S,kBAAkBA,CAAA,EAAyB;EACzD,OAAO;IACL/S,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAASgT,gBAAgBA,CAAA,EAAuB;EACrD,OAAO;IACLhT,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAASiT,aAAaA,CAAA,EAAoB;EAC/C,OAAO;IACLjT,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAASkT,UAAUA,CAAA,EAAiB;EACzC,OAAO;IACLlT,IAAI,EAAE;EACR,CAAC;AACH;AAEO,SAASmT,cAAcA,CAC5BpJ,cAA+D,GAAG,IAAI,EACtEgI,UAA+C,EAC/C7I,cAAyC,GAAG,IAAI,EAC9B;EAClB,OAAO,IAAAnJ,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtB+J,cAAc;IACdgI,UAAU;IACV7I;EACF,CAAC,CAAC;AACJ;AAEO,SAASkK,iBAAiBA,CAC/BrJ,cAA+D,GAAG,IAAI,EACtEgI,UAA+C,EAC/C7I,cAAyC,GAAG,IAAI,EAC3B;EACrB,OAAO,IAAAnJ,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzB+J,cAAc;IACdgI,UAAU;IACV7I;EACF,CAAC,CAAC;AACJ;AAEO,SAASmK,eAAeA,CAC7BC,QAAwB,EACxBvJ,cAAqD,GAAG,IAAI,EACzC;EACnB,OAAO,IAAAhK,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBsT,QAAQ;IACRvJ;EACF,CAAC,CAAC;AACJ;AAEO,SAASwJ,eAAeA,CAC7BC,aAA0C,EAC1CtK,cAAyC,GAAG,IAAI,EAChDuK,OAAuB,GAAG,IAAI,EACX;EACnB,OAAO,IAAA1T,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBwT,aAAa;IACbtK,cAAc;IACduK;EACF,CAAC,CAAC;AACJ;AAEO,SAASC,WAAWA,CACzBC,QAAyC,EACzC5J,cAAqD,GAAG,IAAI,EAC7C;EACf,OAAO,IAAAhK,qBAAY,EAAgB;IACjCC,IAAI,EAAE,aAAa;IACnB2T,QAAQ;IACR5J;EACF,CAAC,CAAC;AACJ;AAEO,SAAS6J,aAAaA,CAC3B1F,OAA+B,EACd;EACjB,OAAO,IAAAnO,qBAAY,EAAkB;IACnCC,IAAI,EAAE,eAAe;IACrBkO;EACF,CAAC,CAAC;AACJ;AAEO,SAAS2F,WAAWA,CAACnK,WAAqB,EAAiB;EAChE,OAAO,IAAA3J,qBAAY,EAAgB;IACjCC,IAAI,EAAE,aAAa;IACnB0J;EACF,CAAC,CAAC;AACJ;AAEO,SAASoK,WAAWA,CACzBC,YAAoD,EACrC;EACf,OAAO,IAAAhU,qBAAY,EAAgB;IACjCC,IAAI,EAAE,aAAa;IACnB+T;EACF,CAAC,CAAC;AACJ;AAEO,SAASC,cAAcA,CAAC9K,cAAwB,EAAoB;EACzE,OAAO,IAAAnJ,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBkJ;EACF,CAAC,CAAC;AACJ;AAEO,SAAS+K,UAAUA,CAAC/K,cAAwB,EAAgB;EACjE,OAAO,IAAAnJ,qBAAY,EAAe;IAChCC,IAAI,EAAE,YAAY;IAClBkJ;EACF,CAAC,CAAC;AACJ;AAEO,SAASgL,kBAAkBA,CAChCpT,KAAmB,EACnB4I,WAAqB,EACrB7F,QAAiB,GAAG,KAAK,EACH;EACtB,OAAO,IAAA9D,qBAAY,EAAuB;IACxCC,IAAI,EAAE,oBAAoB;IAC1Bc,KAAK;IACL4I,WAAW;IACX7F;EACF,CAAC,CAAC;AACJ;AAEO,SAASsQ,WAAWA,CAAC1I,KAAsB,EAAiB;EACjE,OAAO,IAAA1L,qBAAY,EAAgB;IACjCC,IAAI,EAAE,aAAa;IACnByL;EACF,CAAC,CAAC;AACJ;AAEO,SAAS2I,kBAAkBA,CAChC3I,KAAsB,EACA;EACtB,OAAO,IAAA1L,qBAAY,EAAuB;IACxCC,IAAI,EAAE,oBAAoB;IAC1ByL;EACF,CAAC,CAAC;AACJ;AAEO,SAAS4I,iBAAiBA,CAC/BC,SAAmB,EACnBC,WAAqB,EACrBC,QAAkB,EAClBC,SAAmB,EACE;EACrB,OAAO,IAAA1U,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzBsU,SAAS;IACTC,WAAW;IACXC,QAAQ;IACRC;EACF,CAAC,CAAC;AACJ;AAEO,SAASC,WAAWA,CAAClH,aAAgC,EAAiB;EAC3E,OAAO,IAAAzN,qBAAY,EAAgB;IACjCC,IAAI,EAAE,aAAa;IACnBwN;EACF,CAAC,CAAC;AACJ;AAEO,SAASmH,mBAAmBA,CACjCzL,cAAwB,EACD;EACvB,OAAO,IAAAnJ,qBAAY,EAAwB;IACzCC,IAAI,EAAE,qBAAqB;IAC3BkJ;EACF,CAAC,CAAC;AACJ;AAEO,SAAS0L,cAAcA,CAAC1L,cAAwB,EAAoB;EACzE,OAAO,IAAAnJ,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtBkJ,cAAc;IACdhJ,QAAQ,EAAE;EACZ,CAAC,CAAC;AACJ;AAEO,SAAS2U,mBAAmBA,CACjChG,UAAoB,EACpBC,SAAmB,EACI;EACvB,OAAO,IAAA/O,qBAAY,EAAwB;IACzCC,IAAI,EAAE,qBAAqB;IAC3B6O,UAAU;IACVC;EACF,CAAC,CAAC;AACJ;AAEO,SAASgG,YAAYA,CAC1BtH,aAAgC,EAChCtE,cAA+B,GAAG,IAAI,EACtC6L,QAAyB,GAAG,IAAI,EAChB;EAChB,OAAO,IAAAhV,qBAAY,EAAiB;IAClCC,IAAI,EAAE,cAAc;IACpBwN,aAAa;IACbtE,cAAc;IACd6L;EACF,CAAC,CAAC;AACJ;AAEO,SAASC,aAAaA,CAC3BC,OAMqB,EACJ;EACjB,OAAO,IAAAlV,qBAAY,EAAkB;IACnCC,IAAI,EAAE,eAAe;IACrBiV;EACF,CAAC,CAAC;AACJ;AAEO,SAASC,6BAA6BA,CAC3CpT,UAA0B,EAC1BiI,cAAqD,GAAG,IAAI,EAC3B;EACjC,OAAO,IAAAhK,qBAAY,EAAkC;IACnDC,IAAI,EAAE,+BAA+B;IACrC8B,UAAU;IACViI;EACF,CAAC,CAAC;AACJ;AAEO,SAASoL,sBAAsBA,CACpC3S,EAAgB,EAChBuH,cAA+D,GAAG,IAAI,EACtEE,QAAmE,GAAG,IAAI,EAC1EtJ,IAAuB,EACG;EAC1B,OAAO,IAAAZ,qBAAY,EAA2B;IAC5CC,IAAI,EAAE,wBAAwB;IAC9BwC,EAAE;IACFuH,cAAc;IACdG,OAAO,EAAED,QAAQ;IACjBtJ;EACF,CAAC,CAAC;AACJ;AAEO,SAASyU,eAAeA,CAC7BzU,IAA4B,EACT;EACnB,OAAO,IAAAZ,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBW;EACF,CAAC,CAAC;AACJ;AAEO,SAAS0U,sBAAsBA,CACpC7S,EAAgB,EAChBuH,cAA+D,GAAG,IAAI,EACtEb,cAAwB,EACE;EAC1B,OAAO,IAAAnJ,qBAAY,EAA2B;IAC5CC,IAAI,EAAE,wBAAwB;IAC9BwC,EAAE;IACFuH,cAAc;IACdb;EACF,CAAC,CAAC;AACJ;AAEO,SAASoM,yBAAyBA,CACvCxT,UAAwB,EACxBiI,cAAqD,GAAG,IAAI,EAC/B;EAC7B,OAAO,IAAAhK,qBAAY,EAA8B;IAC/CC,IAAI,EAAE,2BAA2B;IACjC8B,UAAU;IACViI;EACF,CAAC,CAAC;AACJ;AAEO,SAASwL,cAAcA,CAC5BzT,UAAwB,EACxBoH,cAAwB,EACN;EAClB,OAAO,IAAAnJ,qBAAY,EAAmB;IACpCC,IAAI,EAAE,gBAAgB;IACtB8B,UAAU;IACVoH;EACF,CAAC,CAAC;AACJ;AAEO,SAASsM,qBAAqBA,CACnC1T,UAAwB,EACxBoH,cAAwB,EACC;EACzB,OAAO,IAAAnJ,qBAAY,EAA0B;IAC3CC,IAAI,EAAE,uBAAuB;IAC7B8B,UAAU;IACVoH;EACF,CAAC,CAAC;AACJ;AAEO,SAASuM,eAAeA,CAC7BvM,cAAwB,EACxBpH,UAAwB,EACL;EACnB,OAAO,IAAA/B,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvBkJ,cAAc;IACdpH;EACF,CAAC,CAAC;AACJ;AAEO,SAAS4T,iBAAiBA,CAC/BlT,EAAgB,EAChB0L,OAA8B,EACT;EACrB,OAAO,IAAAnO,qBAAY,EAAsB;IACvCC,IAAI,EAAE,mBAAmB;IACzBwC,EAAE;IACF0L;EACF,CAAC,CAAC;AACJ;AAEO,SAASyH,YAAYA,CAC1BnT,EAAkC,EAClC0P,WAAgC,GAAG,IAAI,EACvB;EAChB,OAAO,IAAAnS,qBAAY,EAAiB;IAClCC,IAAI,EAAE,cAAc;IACpBwC,EAAE;IACF0P;EACF,CAAC,CAAC;AACJ;AAEO,SAAS0D,mBAAmBA,CACjCpT,EAAkC,EAClC7B,IAA6C,EACtB;EACvB,OAAO,IAAAZ,qBAAY,EAAwB;IACzCC,IAAI,EAAE,qBAAqB;IAC3BwC,EAAE;IACF7B;EACF,CAAC,CAAC;AACJ;AAEO,SAASkV,aAAaA,CAAClV,IAAwB,EAAmB;EACvE,OAAO,IAAAZ,qBAAY,EAAkB;IACnCC,IAAI,EAAE,eAAe;IACrBW;EACF,CAAC,CAAC;AACJ;AAEO,SAASmV,YAAYA,CAC1BnR,QAAyB,EACzBoR,SAAgC,GAAG,IAAI,EACvChM,cAAqD,GAAG,IAAI,EAC5C;EAChB,OAAO,IAAAhK,qBAAY,EAAiB;IAClCC,IAAI,EAAE,cAAc;IACpB2E,QAAQ;IACRoR,SAAS;IACThM;EACF,CAAC,CAAC;AACJ;AAEO,SAASiM,yBAAyBA,CACvCxT,EAAgB,EAChByT,eAA6D,EAChC;EAC7B,OAAO,IAAAlW,qBAAY,EAA8B;IAC/CC,IAAI,EAAE,2BAA2B;IACjCwC,EAAE;IACFyT,eAAe;IACfC,QAAQ,EAAE;EACZ,CAAC,CAAC;AACJ;AAEO,SAASC,yBAAyBA,CACvCrU,UAA2B,EACE;EAC7B,OAAO,IAAA/B,qBAAY,EAA8B;IAC/CC,IAAI,EAAE,2BAA2B;IACjC8B;EACF,CAAC,CAAC;AACJ;AAEO,SAASsU,mBAAmBA,CACjCtU,UAAwB,EACD;EACvB,OAAO,IAAA/B,qBAAY,EAAwB;IACzCC,IAAI,EAAE,qBAAqB;IAC3B8B;EACF,CAAC,CAAC;AACJ;AAEO,SAASuU,kBAAkBA,CAChCvU,UAAwB,EACF;EACtB,OAAO,IAAA/B,qBAAY,EAAuB;IACxCC,IAAI,EAAE,oBAAoB;IAC1B8B;EACF,CAAC,CAAC;AACJ;AAEO,SAASwU,4BAA4BA,CAC1C9T,EAAgB,EACgB;EAChC,OAAO,IAAAzC,qBAAY,EAAiC;IAClDC,IAAI,EAAE,8BAA8B;IACpCwC;EACF,CAAC,CAAC;AACJ;AAEO,SAAS+T,gBAAgBA,CAACrN,cAAwB,EAAsB;EAC7E,OAAO,IAAAnJ,qBAAY,EAAqB;IACtCC,IAAI,EAAE,kBAAkB;IACxBkJ;EACF,CAAC,CAAC;AACJ;AAEO,SAASsN,4BAA4BA,CAC1C/T,MAAuB,EACS;EAChC,OAAO,IAAA1C,qBAAY,EAAiC;IAClDC,IAAI,EAAE,8BAA8B;IACpCyC;EACF,CAAC,CAAC;AACJ;AAEO,SAASgU,0BAA0BA,CACxChU,MAAgC,EACF;EAC9B,OAAO,IAAA1C,qBAAY,EAA+B;IAChDC,IAAI,EAAE,4BAA4B;IAClCyC;EACF,CAAC,CAAC;AACJ;AAEO,SAASiU,eAAeA,CAC7BC,UAAuC,GAAG,IAAI,EAC9CjJ,QAAqC,GAAG,IAAI,EAC5C5K,IAAY,EACO;EACnB,OAAO,IAAA/C,qBAAY,EAAoB;IACrCC,IAAI,EAAE,iBAAiB;IACvB2W,UAAU;IACVhJ,OAAO,EAAED,QAAQ;IACjB5K;EACF,CAAC,CAAC;AACJ;AAGA,SAAS8T,aAAaA,CAACrW,KAAa,EAAE;EACpC,IAAAsW,2BAAkB,EAAC,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;EACvE,OAAO3T,cAAc,CAAC3C,KAAK,CAAC;AAC9B;AAGA,SAASuW,YAAYA,CAACxT,OAAe,EAAEC,KAAa,GAAG,EAAE,EAAE;EACzD,IAAAsT,2BAAkB,EAAC,cAAc,EAAE,eAAe,EAAE,gBAAgB,CAAC;EACrE,OAAOxT,aAAa,CAACC,OAAO,EAAEC,KAAK,CAAC;AACtC;AAGA,SAASwT,YAAYA,CAACpS,QAAgB,EAAE;EACtC,IAAAkS,2BAAkB,EAAC,cAAc,EAAE,aAAa,EAAE,gBAAgB,CAAC;EACnE,OAAOnS,WAAW,CAACC,QAAQ,CAAC;AAC9B;AAGA,SAASqS,cAAcA,CAACrS,QAAsB,EAAE;EAC9C,IAAAkS,2BAAkB,EAAC,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,CAAC;EACvE,OAAO7O,aAAa,CAACrD,QAAQ,CAAC;AAChC"}