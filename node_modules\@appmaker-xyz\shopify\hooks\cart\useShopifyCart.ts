import { useState } from 'react';
import { appPluginStoreApi, useAppStorage } from '@appmaker-xyz/core';
import { calculateCartSavings } from '../../helper/cart';
import { currencyHelper } from '../../helper/index';

function toParseFloatOrInt(value) {
  const { display_decimals_in_cart_page } =
    appPluginStoreApi()?.getState()?.plugins?.shopify?.settings;
  try {
    if (display_decimals_in_cart_page) {
      return parseFloat?.(value);
    }
    return Math?.round?.(parseFloat?.(value));
  } catch (error) {
    return parseInt?.(value, 10);
  }
}

const useShopifyCart = (props: { onAction: (params: any) => any }) => {
  const onAction = props?.onAction;
  const cartState = useAppStorage((state) => state?.checkout);
  const lineItems = cartState?.lineItems?.edges;
  const totalPrice = cartState?.totalPrice;
  const [addToCartLoading, setAddToCartLoading] = useState(false);
  const cartTotalSavings = toParseFloatOrInt(
    calculateCartSavings(lineItems) || 0,
  );
  const cartTotal =
    toParseFloatOrInt(totalPrice?.amount || 0) + cartTotalSavings;
  const cartTotalPriceWithCurrency = currencyHelper(
    cartTotal,
    totalPrice?.currencyCode,
  );

  let totalQuantity = 0;
  lineItems?.forEach?.((item) => {
    totalQuantity += item?.node?.quantity;
  });

  return {
    cart: cartState,
    lineItems,
    addToCartLoading,
    cartTotalPriceWithCurrency,
    cartTotal,
    totalQuantity,
  };
};

export { useShopifyCart };
