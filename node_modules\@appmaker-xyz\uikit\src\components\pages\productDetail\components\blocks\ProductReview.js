import React, { useState } from 'react';
import { StyleSheet, Alert, TouchableOpacity } from 'react-native';
import { useApThemeState } from '../../../../../theme/ThemeContext';
import {
  Input,
  Layout,
  Card,
  AppmakerText,
  Button,
} from '../../../../../components';
import Modal from 'react-native-modal';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import AntIcon from '@appmaker-xyz/uikit/Icons/AntDesign';

const _ = require('lodash');

const Star = ({ isFilled = false, onPress, canChange }) => {
  const { color, font } = useApThemeState();
  return canChange ? (
    <TouchableOpacity activeOpacity={0.5} onPress={() => onPress && onPress()}>
      <AntIcon
        name={isFilled ? 'star' : 'staro'}
        size={font.size.xxl}
        color={isFilled ? color.warning : color.grey}
      />
    </TouchableOpacity>
  ) : (
    <Layout>
      <AntIcon
        name={isFilled ? 'star' : 'staro'}
        size={font.size.xxl}
        color={isFilled ? color.warning : color.grey}
      />
    </Layout>
  );
};

const ProductReview = ({ attributes, onAction }) => {
  const { productReview, productId, canChange = true } = attributes;
  const { spacing, color, font } = useApThemeState();
  const styles = allStyles({ spacing, color });
  const [visible, setVisible] = useState(false);
  const [rating, setRating] = useState(0);
  const [content, setContent] = useState('');

  let toggle = () => setVisible(!visible);
  const submitReview = async () => {
    let resp = {};
    try {
      resp = await onAction({
        action: 'SUBMIT_PRODUCT_REVIEW',
        params: {
          id: productId,
          content,
          rating,
        },
      });
      setRating(0);
      setContent('');
    } catch (err) {
      console.log(err);
    }
    if (resp) {
      toggle();
      Alert.alert(resp.message);
      // onAction({
      //   action: 'SHOW_MESSAGE',
      //   params: {
      //     title: resp.message,
      //     alert: true,
      //   },
      // });
    }
  };
  return (
    <Layout style={styles.subPadding}>
      {productReview.map((item, key) => {
        return (
          <Card
            attributes={{
              type: 'review',
              featureImg: item.avatar,
              title: item.name,
              excerpt: item.rating,
            }}>
            <AppmakerText numberOfLines={4} status="demiDark">
              {item.review}
            </AppmakerText>
          </Card>
        );
      })}
      <Layout style={styles.buttonContainer}>
        <Button outline small status="dark" onPress={toggle}>
          Add Review
        </Button>
        <Modal
          isVisible={visible}
          onSwipeComplete={toggle}
          onBackButtonPress={toggle}
          backdropTransitionOutTiming={0}
          onBackdropPress={toggle}
          style={styles.modal}>
          <Layout style={styles.modalBody}>
            <Layout style={styles.modalHeader}>
              <Layout>
                <AppmakerText category="actionTitle" status="dark">
                  Add your review
                </AppmakerText>
              </Layout>
              <Button
                onPress={toggle}
                status="white"
                accessoryLeft={() => <Icon name="x" size={font.size.lg} />}
                small
              />
            </Layout>
            <Layout style={styles.modalContent}>
              <Layout style={styles.starRatingContainer}>
                <AppmakerText>Rate product</AppmakerText>
                <Layout style={styles.starRating}>
                  {_.times(5, (i) => (
                    <Star
                      key={i + 1}
                      canChange={canChange}
                      onPress={() => {
                        setRating(i + 1);
                      }}
                      isFilled={i + 1 <= rating}
                    />
                  ))}
                </Layout>
              </Layout>
              <Input label="Write Review" onChangeText={(r) => setContent(r)} />
            </Layout>
            <Layout style={styles.submitButton}>
              <Button onPress={submitReview} baseSize>
                Submit Review
              </Button>
            </Layout>
          </Layout>
        </Modal>
      </Layout>
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    subPadding: {
      paddingHorizontal: spacing.base,
      paddingBottom: spacing.base,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginTop: spacing.mini,
    },
    modal: {
      justifyContent: 'flex-end',
      marginVertical: 0,
      marginHorizontal: 1,
    },
    modalBody: {
      backgroundColor: color.white,
      padding: spacing.md,
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      borderTopStartRadius: spacing.md,
      borderTopEndRadius: spacing.md,
      flexDirection: 'column',
      maxHeight: '80%',
    },
    modalHeader: {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderBottomWidth: 1,
      borderBottomColor: color.light,
      paddingBottom: spacing.base,
    },
    modalContent: {
      paddingVertical: spacing.base,
      flexDirection: 'column',
      width: '100%',
    },
    submitButton: {
      alignSelf: 'center',
    },
    starRatingContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: spacing.base,
      marginBottom: spacing.md,
    },
    starRating: {
      flexDirection: 'row',
    },
  });

export default ProductReview;
