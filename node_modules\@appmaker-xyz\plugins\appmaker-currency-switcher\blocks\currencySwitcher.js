import React, { useState } from 'react';
import { ActionBar } from '@appmaker-xyz/uikit';
import { View, FlatList } from 'react-native';
import { useAppStorage } from '@appmaker-xyz/core';

export function CurrencyChooser({ attributes, pageDispatch, ...props }) {
  const { items, selectedItem } = attributes;
  const [selectedCurrency, setSelectedCurrency] = useState(undefined);
  let default_key;
  if (!selectedCurrency) {
    default_key = selectedItem;
  }
  const currencyStorage = useAppStorage(
    (state) => state.appmaker_currency_switcher,
  );
  const renderItem = ({ item, index }) => {
    if (item.currency_code === default_key) {
      default_key = undefined;
      setSelectedCurrency(item);
    }
    const onPress = () => {
      default_key = undefined;
      props.onAction({
        action: 'SET_CURRENCY',
        params: { ...item, default_currency: currencyStorage.default_currency },
      });
      setSelectedCurrency(item.currency_code);
    };
    return (
      <ActionBar
        onPress={onPress}
        attributes={{
          title: item.currency_name,
          rightIcon:
            currencyStorage?.currency_code == item.currency_code
              ? 'check-circle'
              : 'circle',
        }}
      />
    );
  };
  return (
    <View>
      <FlatList data={items} renderItem={renderItem} />
    </View>
  );
}
