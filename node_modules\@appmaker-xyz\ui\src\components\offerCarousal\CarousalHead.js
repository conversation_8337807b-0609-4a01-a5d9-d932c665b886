import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const CarousalHead = (props) => {
  const { backgroundColor = '#fff', headColor = '#000' } = props;
  return (
    <Svg
      width={30}
      height={82}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <Path fill={backgroundColor} d="M.97 0h28.395v82H.97z" />
      <Path
        d="M29.365 0H.97v13.387a6.612 6.612 0 0 1 0 13.224v7.777a6.612 6.612 0 0 1 0 13.224v7.777a6.612 6.612 0 0 1 0 13.224V82h28.395V0Z"
        fill={headColor}
      />
    </Svg>
  );
};

export default CarousalHead;
