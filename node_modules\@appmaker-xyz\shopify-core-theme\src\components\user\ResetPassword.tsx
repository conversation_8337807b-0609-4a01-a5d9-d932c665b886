import React, { useState } from 'react';
import { StyleSheet, KeyboardAvoidingView } from 'react-native';
import { Layout, Input, Button, ThemeText } from '@appmaker-xyz/ui';
import Icon from 'react-native-vector-icons/Feather';
import { useNavigationActions, useForgotPassword } from '@appmaker-xyz/shopify';
import { InputFormItem } from './components/InputFormItem';

const ResetPassword = ({ onAction }) => {
  const [email, setEmail] = useState('');
  // const [success, setSuccess] = useState(false);
  const { control, isLoading, submit, isSuccess } = useForgotPassword({
    defaultValues: {
      email: '',
    },
  });
  const shopifyActions = useNavigationActions();

  if (isSuccess) {
    return (
      <KeyboardAvoidingView keyboardVerticalOffset={80} behavior={'padding'}>
        <Layout style={styles.container}>
          <Icon name="check-circle" size={40} color="#A9AEB7" />
          <ThemeText size="xl" fontFamily="medium" color="#212121">
            Reset Link Sent
          </ThemeText>
          <ThemeText color="#4F4F4F" style={styles.title}>
            We've sent you an email with a link to update your password. Please
            check your inbox.
          </ThemeText>
          <Button
            title="Back to Login"
            isLoading={false}
            activityIndicatorColor="#FFFFFF"
            size="md"
            color="#1E232C"
            onPress={shopifyActions.openLoginPage}
          />
        </Layout>
      </KeyboardAvoidingView>
    );
  }

  return (
    <KeyboardAvoidingView keyboardVerticalOffset={80} behavior={'padding'}>
      <Layout style={styles.container}>
        <ThemeText size="2xl" fontFamily="medium">
          Reset Password
        </ThemeText>
        <ThemeText color="#4F4F4F" style={styles.title}>
          Enter your email address below and we'll send you a link to reset your
          password.
        </ThemeText>
        <InputFormItem
          keyboardType="email-address"
          textContentType="emailAddress"
          style={styles.input}
          control={control}
          name="email"
          label={'Email'}
        />
        <Button
          onPress={submit}
          title="Reset Password"
          isLoading={isLoading}
          activityIndicatorColor="#FFFFFF"
          size="md"
          color="#1E232C"
        />
      </Layout>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {},
  title: {
    marginBottom: 32,
  },
  input: {
    backgroundColor: '#E8ECF4',
    borderRadius: 6,
    overflow: 'hidden',
    position: 'relative',
  },
});

export default ResetPassword;
