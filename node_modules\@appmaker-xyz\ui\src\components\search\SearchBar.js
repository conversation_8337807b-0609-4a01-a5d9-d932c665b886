import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  ActivityIndicator,
  I18nManager,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { debounce } from 'lodash';
// Used translation
import { useTranslation } from 'react-i18next';
import { styles as themeStyles } from '../../styles';

const SearchBar = ({
  defaultText,
  onPress,
  onChange,
  searchLoading,
  setFocus = true,
  topBarView,
  onBack,
  debounceInterval = 500,
  debounceOnChange,
  label,
  wholeContainerStyle,
}) => {
  const { t } = useTranslation();
  const [value, setValue] = useState(defaultText);
  // const [searching, setSearching] = useState(false);
  const containerStyles = [styles.container];

  if (wholeContainerStyle) {
    containerStyles.push(wholeContainerStyle);
  }

  const debounceCallback = (text) => {
    // console.log(text, 'db');
    debounceOnChange(text);
  };
  const [debouncedCallApi] = useState(() =>
    debounce(debounceCallback, debounceInterval),
  );
  const onChangeText = (text) => {
    setValue(text);
    // setSearching(true);
    onChange && onChange(text);
    debouncedCallApi(text);
  };

  // if (topBarView) {
  //   containerStyles.push({
  //     flexDirection: 'row',
  //     padding: spacing.small,
  //     shadowColor: "#0F172A",
  //     shadowOffset: {
  //       width: 0,
  //       height: 2,
  //     },
  //     shadowOpacity: 0.23,
  //     shadowRadius: 2.62,
  //     elevation: 10,
  //     marginBottom: 4,
  //   });
  // }

  return (
    <View style={[containerStyles]}>
      {/* {topBarView && (
        <Icon
          name={`arrow-${I18nManager.isRTL ? 'right' : 'left'}`}
          style={styles.backIcon}
          onPress={onBack}
        />
      )} */}
      <View style={styles.searchbar}>
        {searchLoading ? (
          <ActivityIndicator size={18} style={styles.icon} color={'#0F172A'} />
        ) : (
          <Icon name="search" style={styles.icon} />
        )}
        <TextInput
          style={styles.inputArea}
          placeholder={t(label)}
          autoFocus={setFocus}
          onChangeText={onChangeText}
          returnKeyType={'search'}
          onSubmitEditing={() => onPress(value)}
          value={value}
        />
        {value !== '' && (
          <Icon name="x" style={styles.icon} onPress={() => onChangeText('')} />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    padding: 12,
    alignItems: 'center',
    height: 70,
  },
  searchbar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F2F2F2',
    height: 44,
    borderRadius: 4,
    paddingHorizontal: 8,
    flex: 1,
  },
  inputArea: {
    flex: 1,
    textAlign: `${I18nManager.isRTL ? 'right' : 'left'}`,
    paddingHorizontal: 8,
    fontSize: 15,
    fontFamily: themeStyles.fontFamily.medium,
    color: '#0F172A',
    fontWeight: 'normal',
  },
  icon: {
    fontSize: 18,
    color: '#0F172A',
    padding: 4,
  },
  backIcon: {
    fontSize: 20,
    color: '#0F172A',
    padding: 8,
    marginRight: 4,
  },
});

export default SearchBar;
