import React from 'react';
import { StyleSheet, useWindowDimensions } from 'react-native';
import { Layout, ThemeText } from '../atoms';

const TextNotice = ({ attributes, onPress, onAction }) => {
  const { message, color, textColor, appmakerAction } = attributes;
  const { width } = useWindowDimensions();

  let wordCount = message?.length || 0;

  if (!message) {
    return null;
  }
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  const containerStyles = [styles.container, { width }];
  if (color) {
    containerStyles.push({ backgroundColor: color });
  }

  return (
    <Layout style={containerStyles}>
      <ThemeText
        color={textColor || '#FFFFFF'}
        size={wordCount > 30 ? 'base' : 'md'}
        style={styles.text}
        numberOfLines={2}>
        {message}
      </ThemeText>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#0F172A',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    paddingHorizontal: 12,
  },
  text: {
    textAlign: 'center',
  },
});

export default TextNotice;
