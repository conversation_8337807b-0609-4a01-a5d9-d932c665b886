import React, { useState, useCallback } from 'react';
import { StyleSheet, LayoutAnimation } from 'react-native';
import { Layout, ThemeText, BlockCard } from '@appmaker-xyz/ui';

const ExpandableTextBlock = ({ clientId, attributes, ...props }) => {
  const [lengthMore, setLengthMore] = useState(false); //to show the "Show more & show Line"
  const [textShown, setTextShown] = useState(false); //To show remaining Text
  const {
    blockTitle,
    content,
    accessButton,
    expandable,
    expanded,
    hiddenText,
    expandedText,
    accessButtonColor,
  } = attributes;

  const onTextLayout = useCallback((e) => {
    setLengthMore(e.nativeEvent.lines.length >= 3); //to check the text is more than 3 lines or not
  }, []);
  const toggleNumberOfLines = () => {
    setTextShown(!textShown); //To toggle the show text or hide it
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  };
  return (
    <BlockCard
      clientId={clientId}
      attributes={{
        title: blockTitle,
        accessButton: accessButton,
        expandable: expandable == true || expandable == '1' ? true : false,
        hiddenText: hiddenText,
        expandedText: expandedText,
        expanded: expanded == true || expanded == '1' ? true : false,
        accessButtonColor: accessButtonColor,
      }}>
      <Layout style={styles.subPadding}>
        <ThemeText
          html={true}
          fontFamily="medium"
          onTextLayout={onTextLayout}
          numberOfLines={textShown ? undefined : 3}>
          {content}
        </ThemeText>
        {lengthMore ? (
          <ThemeText
            html={true}
            fontFamily="medium"
            color="#A9AEB7"
            onPress={toggleNumberOfLines}
            style={styles.moreText}>
            {textShown ? 'Show less' : 'Show more'}
          </ThemeText>
        ) : null}
      </Layout>
    </BlockCard>
  );
};
const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
  },
  subPadding: {
    paddingHorizontal: 12,
    paddingBottom: 12,
  },
  moreText: {
    marginTop: 10,
  },
});
export default ExpandableTextBlock;
