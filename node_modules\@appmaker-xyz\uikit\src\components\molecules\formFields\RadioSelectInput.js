import React, { useState } from 'react';
import { StyleSheet, View, Text } from 'react-native';
import RadioForm from 'react-native-simple-radio-button';
import { fonts, color, spacing } from '../../../styles';
import { usePageState } from '@appmaker-xyz/core';

const RadioSelectInput = ({ attributes, coreDispatch, clientId, onAction }) => {
  console.log('RadioSelectInput attributes', attributes);
  const { options, label, parentName = '_formData', name } = attributes;

  function onChangeInput(valueText) {
    coreDispatch({
      type: 'SET_VALUE',
      name,
      value: valueText,
      parent: parentName,
    });
  }
  let optionValues;
  if (typeof options === 'object') {
    optionValues = Object.values(options);
  } else {
    optionValues = options;
  }
  return (
    <View>
      <Text>asdasd</Text>
      <RadioForm
        radio_props={optionValues}
        initial={0}
        onPress={(value) => { }}
        buttonColor={color.grey}
        selectedButtonColor={color.success}
        labelStyle={styles.customLabelStyle}
        buttonSize={10}
        buttonOuterSize={24}
      // onPress={onChangeInput}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  customLabelStyle: {
    ...fonts.bodyParagraph,
    width: '96%',
    paddingVertical: spacing.nano,
  },
});

export default RadioSelectInput;
