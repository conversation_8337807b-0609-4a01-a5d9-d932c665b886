import { uglify } from 'rollup-plugin-uglify';
import localResolve from 'rollup-plugin-local-resolve';
import babel from '@rollup/plugin-babel';
import image from '@rollup/plugin-image';
import typescript from '@rollup/plugin-typescript';
import svg from 'rollup-plugin-svg';

export default {
  input: 'src/index.js',
  plugins: [
    image(),
    localResolve(),
    svg(),
    typescript({
      jsx: 'react',
    }),
    uglify(),
    babel({
      presets: ['@babel/preset-react'],
    }),
  ],
  output: {
    // dir: 'build-output',
    file: 'build-output/index.js',
    format: 'cjs',
    // preserveModules: true,
    // preserveModulesRoot: './',
  },
  minifyInternalExports: true,
};
