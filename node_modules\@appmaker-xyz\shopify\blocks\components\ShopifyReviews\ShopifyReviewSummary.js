import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { useReviewSummary } from '../../../hooks/useReviewSummary';
import Icon from 'react-native-vector-icons/AntDesign';
import { ThemeText } from '@appmaker-xyz/ui';

const ReviewSummary = (props) => {
  // const { reviewCount, averageRating } = props;
  const { BlockItem, BlocksView, onAction, attributes, innerBlocks } = props;
  const {
    average_rating,
    total_questions,
    total_reviews,
    ratings,
    productId,
    openAddReview,
    isLoading,
  } = useReviewSummary(props);

  const RatingStatistics = () => {
    return (
      <View style={styles.ratingStatsContainer}>
        {ratings.map((rating, idx) => {
          return (
            <View key={idx} style={styles.ratingStatContainer}>
              <View style={styles.rowCenter}>
                <ThemeText fontFamily="medium">{rating.rating}</ThemeText>
                <Icon
                  name="star"
                  size={12}
                  color="#333"
                  style={styles.smallStar}
                />
              </View>
              <View style={styles.progressBarContainer}>
                <View
                  style={[
                    styles.progressBar,
                    {
                      width: `${rating.percentage}%`,
                      backgroundColor:
                        rating.rating >= 3
                          ? '#388E3C'
                          : rating.rating === 2
                          ? '#F79F03'
                          : '#F66161',
                    },
                  ]}
                />
              </View>
              <View style={styles.rowCenter}>
                <ThemeText size="sm" numberOfLines={1} style={styles.countText}>
                  {rating.count}
                </ThemeText>
              </View>
            </View>
          );
        })}
      </View>
    );
  };
  let isZeroReview = total_reviews === '0';

  return !isLoading ? (
    <View style={styles.container}>
      {!isZeroReview ? (
        <>
          <View style={styles.blockHeadContainer}>
            <ThemeText size="lg" fontFamily="medium">
              Reviews
            </ThemeText>
            <TouchableOpacity onPress={openAddReview} style={styles.addButton}>
              <ThemeText size="sm" fontFamily="bold">
                Add Review
              </ThemeText>
            </TouchableOpacity>
          </View>
          <View style={styles.ratingOverviewContainer}>
            <View style={styles.avgRatingContainer}>
              <View style={styles.ratingStarText}>
                <ThemeText size="2xl" fontFamily="bold">
                  {average_rating}
                </ThemeText>
                <Icon name="star" size={24} color="#333" style={styles.star} />
              </View>
              <ThemeText size="sm" fontColor="#888888">
                {`from ${total_reviews} review${
                  Number(total_reviews) > 1 ? 's' : ''
                }`}
              </ThemeText>
            </View>
            <View style={styles.line} />
            <RatingStatistics />
          </View>
        </>
      ) : (
        <View style={styles.noReviewContainer}>
          <ThemeText size="lg" fontFamily="bold">
            Product Review By Customers
          </ThemeText>
          <View style={styles.startsContainer}>
            {Array.from({ length: 5 }).map((_, idx) => (
              <Icon
                key={idx}
                name="staro"
                size={18}
                color="#e8c463"
                style={styles.star}
              />
            ))}
          </View>
          <ThemeText fontColor="#888888">
            Be the first to write a review
          </ThemeText>
          <TouchableOpacity
            onPress={openAddReview}
            style={styles.writeReviewButton}>
            <ThemeText size="md" fontFamily="bold" color="#fff">
              Add Review
            </ThemeText>
          </TouchableOpacity>
        </View>
      )}
      {/* <TouchableOpacity onPress={onPress} style={styles.viewAllButton}>
        <ThemeText fontFamily="medium">See All Reviews</ThemeText>
        <Icon name="right" size={14} color="#333" style={styles.arrowIcon} />
      </TouchableOpacity> */}
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    marginBottom: 1,
    paddingBottom: 10,
  },
  blockHeadContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
  },
  ratingOverviewContainer: {
    flexDirection: 'row',
    flex: 1,
    paddingHorizontal: 14,
  },
  avgRatingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 2,
  },
  ratingStarText: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  star: {
    marginLeft: 5,
  },
  ratingStatsContainer: {
    flex: 3,
  },
  rowCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  line: {
    width: 1,
    backgroundColor: '#eee',
    marginRight: 16,
  },
  ratingStatContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 2,
  },
  smallStar: {
    marginLeft: 3,
  },
  progressBarContainer: {
    height: 6,
    flex: 4,
    backgroundColor: '#eee',
    borderRadius: 5,
    overflow: 'hidden',
    marginHorizontal: 10,
  },
  progressBar: {
    height: 6,
  },
  addButton: {
    paddingVertical: 6,
    paddingHorizontal: 8,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#333',
    alignItems: 'center',
    borderRadius: 5,
  },
  countText: {
    textAlign: 'right',
    width: '100%',
  },
  viewAllButton: {
    marginTop: 20,
    paddingVertical: 10,
    borderTopWidth: 1,
    borderColor: '#eee',
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 12,
  },
  arrowIcon: {
    marginLeft: 5,
  },
  noReviewContainer: {
    paddingTop: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  writeReviewButton: {
    paddingVertical: 10,
    paddingHorizontal: 18,
    backgroundColor: '#000',
    marginTop: 10,
    alignItems: 'center',
    borderRadius: 28,
    marginBottom: 20,
  },
  startsContainer: {
    marginVertical: 10,
    flexDirection: 'row',
  },
});

export default ReviewSummary;
