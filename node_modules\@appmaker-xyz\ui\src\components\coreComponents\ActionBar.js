import React from 'react';
import { View, StyleSheet, ActivityIndicator, I18nManager } from 'react-native';
import { AppTouchable, Layout, ThemeText, AppImage } from '../atoms';
import Icon from 'react-native-vector-icons/Feather';
import { testProps } from '@appmaker-xyz/core';

const ActionBar = ({
  attributes: {
    testId,
    featureImg,
    imageResize = 'cover',
    imageSize,
    leftIcon,
    title,
    subTitle,
    rightIcon,
    drawerItem,
    type,
    appmakerAction,
    text,
    loading = false,
    image,
    noMargin = false,
    containerStyle,
    fontColor,
  },
  onPress,
  onAction,
  clientId,
}) => {
  const onPressHandle =
    appmakerAction && onAction ? () => onAction(appmakerAction) : onPress;
  const finalText = title || text;
  const finalFeatureImg = featureImg || (image && image.url);
  const leftImageStyles = StyleSheet.flatten([
    styles.leftImage,
    imageSize && { width: imageSize, height: imageSize },
  ]);

  const containerStyles = StyleSheet.flatten([
    styles.container,
    noMargin && { marginBottom: 0 },
    drawerItem && { padding: 8 },
    type === 'title' && styles.topBorder,
    containerStyle,
  ]);

  return (
    <AppTouchable
      {...testProps(testId)}
      onPress={onPressHandle}
      clientId={clientId}
      disabled={loading}>
      <View style={containerStyles}>
        <Layout style={styles.titleContainer}>
          {leftIcon && (
            <Icon
              name={leftIcon}
              size={18}
              color={loading ? '#A9AEB7' : '#4F4F4F'}
              style={styles.leftIcon}
            />
          )}
          {finalFeatureImg && (
            <Layout style={leftImageStyles}>
              <AppImage
                uri={finalFeatureImg}
                style={styles.image}
                resizeMode={imageResize}
              />
            </Layout>
          )}
          <Layout flex={1}>
            <ThemeText
              {...testProps(`${clientId}-title`)}
              size={drawerItem ? 'md' : 'lg'}
              fontFamily="medium"
              color={fontColor || undefined}
              style={styles.rtlAlign}
              numberOfLines={1}>
              {finalText}
            </ThemeText>
            {subTitle && (
              <ThemeText size="sm" color="#A9AEB7" style={styles.rtlAlign}>
                {subTitle}
              </ThemeText>
            )}
          </Layout>
        </Layout>
        {loading && (
          <ActivityIndicator size="small" color={fontColor || '#4F4F4F'} />
        )}
        {type !== 'title' && !loading && (
          <Icon
            name={
              rightIcon
                ? rightIcon
                : `chevron-${I18nManager.isRTL ? 'left' : 'right'}`
            }
            size={18}
            color={fontColor || '#1B1B1B'}
          />
        )}
      </View>
    </AppTouchable>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 12,
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    borderRadius: 4,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  leftImage: {
    width: 30,
    height: 30,
    marginRight: 12,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  topBorder: {
    borderTopColor: '#E9EDF1',
    borderTopWidth: 1,
  },
  leftIcon: {
    marginRight: 12,
  },
  rtlAlign: {
    textAlign: 'left',
  },
});

export default ActionBar;
