{"version": 3, "names": ["_utils", "require", "defineType", "defineAliasedType", "visitor", "aliases", "fields", "name", "validate", "assertNodeType", "value", "optional", "builder", "Object", "assign", "openingElement", "closingElement", "children", "chain", "assertValueType", "assertEach", "selfClosing", "expression", "object", "property", "namespace", "default", "attributes", "typeParameters", "argument", "openingFragment", "closingFragment"], "sources": ["../../src/definitions/jsx.ts"], "sourcesContent": ["import {\n  defineAliasedType,\n  assertNodeType,\n  assertValueType,\n  chain,\n  assertEach,\n} from \"./utils\";\n\nconst defineType = defineAliasedType(\"JSX\");\n\ndefineType(\"JSXAttribute\", {\n  visitor: [\"name\", \"value\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    name: {\n      validate: assertNodeType(\"JSXIdentifier\", \"JSXNamespacedName\"),\n    },\n    value: {\n      optional: true,\n      validate: assertNodeType(\n        \"JSXElement\",\n        \"JSXFragment\",\n        \"StringLiteral\",\n        \"JSXExpressionContainer\",\n      ),\n    },\n  },\n});\n\ndefineType(\"JSXClosingElement\", {\n  visitor: [\"name\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    name: {\n      validate: assertNodeType(\n        \"JSXIdentifier\",\n        \"JSXMemberExpression\",\n        \"JSXNamespacedName\",\n      ),\n    },\n  },\n});\n\ndefineType(\"JSXElement\", {\n  builder: process.env.BABEL_8_BREAKING\n    ? [\"openingElement\", \"closingElement\", \"children\"]\n    : [\"openingElement\", \"closingElement\", \"children\", \"selfClosing\"],\n  visitor: [\"openingElement\", \"children\", \"closingElement\"],\n  aliases: [\"Immutable\", \"Expression\"],\n  fields: {\n    openingElement: {\n      validate: assertNodeType(\"JSXOpeningElement\"),\n    },\n    closingElement: {\n      optional: true,\n      validate: assertNodeType(\"JSXClosingElement\"),\n    },\n    children: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"JSXText\",\n            \"JSXExpressionContainer\",\n            \"JSXSpreadChild\",\n            \"JSXElement\",\n            \"JSXFragment\",\n          ),\n        ),\n      ),\n    },\n    ...(process.env.BABEL_8_BREAKING\n      ? {}\n      : {\n          selfClosing: {\n            validate: assertValueType(\"boolean\"),\n            optional: true,\n          },\n        }),\n  },\n});\n\ndefineType(\"JSXEmptyExpression\", {});\n\ndefineType(\"JSXExpressionContainer\", {\n  visitor: [\"expression\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\", \"JSXEmptyExpression\"),\n    },\n  },\n});\n\ndefineType(\"JSXSpreadChild\", {\n  visitor: [\"expression\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"JSXIdentifier\", {\n  builder: [\"name\"],\n  fields: {\n    name: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n});\n\ndefineType(\"JSXMemberExpression\", {\n  visitor: [\"object\", \"property\"],\n  fields: {\n    object: {\n      validate: assertNodeType(\"JSXMemberExpression\", \"JSXIdentifier\"),\n    },\n    property: {\n      validate: assertNodeType(\"JSXIdentifier\"),\n    },\n  },\n});\n\ndefineType(\"JSXNamespacedName\", {\n  visitor: [\"namespace\", \"name\"],\n  fields: {\n    namespace: {\n      validate: assertNodeType(\"JSXIdentifier\"),\n    },\n    name: {\n      validate: assertNodeType(\"JSXIdentifier\"),\n    },\n  },\n});\n\ndefineType(\"JSXOpeningElement\", {\n  builder: [\"name\", \"attributes\", \"selfClosing\"],\n  visitor: [\"name\", \"attributes\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    name: {\n      validate: assertNodeType(\n        \"JSXIdentifier\",\n        \"JSXMemberExpression\",\n        \"JSXNamespacedName\",\n      ),\n    },\n    selfClosing: {\n      default: false,\n    },\n    attributes: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"JSXAttribute\", \"JSXSpreadAttribute\")),\n      ),\n    },\n    typeParameters: {\n      validate: assertNodeType(\n        \"TypeParameterInstantiation\",\n        \"TSTypeParameterInstantiation\",\n      ),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"JSXSpreadAttribute\", {\n  visitor: [\"argument\"],\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"JSXText\", {\n  aliases: [\"Immutable\"],\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n});\n\ndefineType(\"JSXFragment\", {\n  builder: [\"openingFragment\", \"closingFragment\", \"children\"],\n  visitor: [\"openingFragment\", \"children\", \"closingFragment\"],\n  aliases: [\"Immutable\", \"Expression\"],\n  fields: {\n    openingFragment: {\n      validate: assertNodeType(\"JSXOpeningFragment\"),\n    },\n    closingFragment: {\n      validate: assertNodeType(\"JSXClosingFragment\"),\n    },\n    children: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"JSXText\",\n            \"JSXExpressionContainer\",\n            \"JSXSpreadChild\",\n            \"JSXElement\",\n            \"JSXFragment\",\n          ),\n        ),\n      ),\n    },\n  },\n});\n\ndefineType(\"JSXOpeningFragment\", {\n  aliases: [\"Immutable\"],\n});\n\ndefineType(\"JSXClosingFragment\", {\n  aliases: [\"Immutable\"],\n});\n"], "mappings": ";;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAQA,MAAMC,UAAU,GAAG,IAAAC,wBAAiB,EAAC,KAAK,CAAC;AAE3CD,UAAU,CAAC,cAAc,EAAE;EACzBE,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC1BC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBC,MAAM,EAAE;IACNC,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAC,qBAAc,EAAC,eAAe,EAAE,mBAAmB;IAC/D,CAAC;IACDC,KAAK,EAAE;MACLC,QAAQ,EAAE,IAAI;MACdH,QAAQ,EAAE,IAAAC,qBAAc,EACtB,YAAY,EACZ,aAAa,EACb,eAAe,EACf,wBAAwB;IAE5B;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,mBAAmB,EAAE;EAC9BE,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBC,MAAM,EAAE;IACNC,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAC,qBAAc,EACtB,eAAe,EACf,qBAAqB,EACrB,mBAAmB;IAEvB;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,YAAY,EAAE;EACvBU,OAAO,EAEH,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,UAAU,EAAE,aAAa,CAAC;EACnER,OAAO,EAAE,CAAC,gBAAgB,EAAE,UAAU,EAAE,gBAAgB,CAAC;EACzDC,OAAO,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;EACpCC,MAAM,EAAAO,MAAA,CAAAC,MAAA;IACJC,cAAc,EAAE;MACdP,QAAQ,EAAE,IAAAC,qBAAc,EAAC,mBAAmB;IAC9C,CAAC;IACDO,cAAc,EAAE;MACdL,QAAQ,EAAE,IAAI;MACdH,QAAQ,EAAE,IAAAC,qBAAc,EAAC,mBAAmB;IAC9C,CAAC;IACDQ,QAAQ,EAAE;MACRT,QAAQ,EAAE,IAAAU,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EACR,IAAAX,qBAAc,EACZ,SAAS,EACT,wBAAwB,EACxB,gBAAgB,EAChB,YAAY,EACZ,aAAa,CACd,CACF;IAEL;EAAC,GAGG;IACEY,WAAW,EAAE;MACXb,QAAQ,EAAE,IAAAW,sBAAe,EAAC,SAAS,CAAC;MACpCR,QAAQ,EAAE;IACZ;EACF,CAAC;AAET,CAAC,CAAC;AAEFT,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;AAEpCA,UAAU,CAAC,wBAAwB,EAAE;EACnCE,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBC,MAAM,EAAE;IACNgB,UAAU,EAAE;MACVd,QAAQ,EAAE,IAAAC,qBAAc,EAAC,YAAY,EAAE,oBAAoB;IAC7D;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,gBAAgB,EAAE;EAC3BE,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBC,MAAM,EAAE;IACNgB,UAAU,EAAE;MACVd,QAAQ,EAAE,IAAAC,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,eAAe,EAAE;EAC1BU,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBN,MAAM,EAAE;IACNC,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAW,sBAAe,EAAC,QAAQ;IACpC;EACF;AACF,CAAC,CAAC;AAEFjB,UAAU,CAAC,qBAAqB,EAAE;EAChCE,OAAO,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;EAC/BE,MAAM,EAAE;IACNiB,MAAM,EAAE;MACNf,QAAQ,EAAE,IAAAC,qBAAc,EAAC,qBAAqB,EAAE,eAAe;IACjE,CAAC;IACDe,QAAQ,EAAE;MACRhB,QAAQ,EAAE,IAAAC,qBAAc,EAAC,eAAe;IAC1C;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,mBAAmB,EAAE;EAC9BE,OAAO,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC;EAC9BE,MAAM,EAAE;IACNmB,SAAS,EAAE;MACTjB,QAAQ,EAAE,IAAAC,qBAAc,EAAC,eAAe;IAC1C,CAAC;IACDF,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAC,qBAAc,EAAC,eAAe;IAC1C;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,mBAAmB,EAAE;EAC9BU,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,aAAa,CAAC;EAC9CR,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;EAC/BC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBC,MAAM,EAAE;IACNC,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAC,qBAAc,EACtB,eAAe,EACf,qBAAqB,EACrB,mBAAmB;IAEvB,CAAC;IACDY,WAAW,EAAE;MACXK,OAAO,EAAE;IACX,CAAC;IACDC,UAAU,EAAE;MACVnB,QAAQ,EAAE,IAAAU,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAX,qBAAc,EAAC,cAAc,EAAE,oBAAoB,CAAC,CAAC;IAEpE,CAAC;IACDmB,cAAc,EAAE;MACdpB,QAAQ,EAAE,IAAAC,qBAAc,EACtB,4BAA4B,EAC5B,8BAA8B,CAC/B;MACDE,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEFT,UAAU,CAAC,oBAAoB,EAAE;EAC/BE,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBE,MAAM,EAAE;IACNuB,QAAQ,EAAE;MACRrB,QAAQ,EAAE,IAAAC,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,SAAS,EAAE;EACpBG,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBO,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBN,MAAM,EAAE;IACNI,KAAK,EAAE;MACLF,QAAQ,EAAE,IAAAW,sBAAe,EAAC,QAAQ;IACpC;EACF;AACF,CAAC,CAAC;AAEFjB,UAAU,CAAC,aAAa,EAAE;EACxBU,OAAO,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,UAAU,CAAC;EAC3DR,OAAO,EAAE,CAAC,iBAAiB,EAAE,UAAU,EAAE,iBAAiB,CAAC;EAC3DC,OAAO,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;EACpCC,MAAM,EAAE;IACNwB,eAAe,EAAE;MACftB,QAAQ,EAAE,IAAAC,qBAAc,EAAC,oBAAoB;IAC/C,CAAC;IACDsB,eAAe,EAAE;MACfvB,QAAQ,EAAE,IAAAC,qBAAc,EAAC,oBAAoB;IAC/C,CAAC;IACDQ,QAAQ,EAAE;MACRT,QAAQ,EAAE,IAAAU,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EACR,IAAAX,qBAAc,EACZ,SAAS,EACT,wBAAwB,EACxB,gBAAgB,EAChB,YAAY,EACZ,aAAa,CACd,CACF;IAEL;EACF;AACF,CAAC,CAAC;AAEFP,UAAU,CAAC,oBAAoB,EAAE;EAC/BG,OAAO,EAAE,CAAC,WAAW;AACvB,CAAC,CAAC;AAEFH,UAAU,CAAC,oBAAoB,EAAE;EAC/BG,OAAO,EAAE,CAAC,WAAW;AACvB,CAAC,CAAC"}