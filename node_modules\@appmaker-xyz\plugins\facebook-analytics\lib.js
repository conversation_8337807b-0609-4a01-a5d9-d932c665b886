import { AppEventsLogger } from 'react-native-fbsdk-next';
import { appmaker, log } from '@appmaker-xyz/core';

export const logEvent = async (event, price, eventparams, eventObject) => {
  const { eventName, eventPrice, params } = appmaker.applyFilters(
    'fb_events_params',
    {
      eventName: event,
      eventPrice: price,
      params: eventparams,
      eventObject,
    },
  );
  if (eventName === 'fb_mobile_purchase') {
    try {
      const logPurchase = appmaker.applyFilters(
        'global-analytics-event-handler',
        AppEventsLogger.logPurchase,
        'facebook',
      );
      logPurchase(eventPrice, params.fb_currency, params);
    } catch (error) {
      console.log('Error while logging purchase event to facebook', error);
      log('Error while logging purchase event to facebook', error);
    }
  } else {
    try {
      const eventsHandler = appmaker.applyFilters(
        'global-analytics-event-handler',
        AppEventsLogger.logEvent,
        'facebook',
      );
      eventsHandler(eventName, eventPrice, params);
    } catch (error) {
      console.log('Error while logging event to facebook', error);
      log('Error while logging event to facebook', error);
    }
  }
};
