{"version": 3, "names": ["_generated", "require", "appendToMemberExpression", "member", "append", "computed", "object", "memberExpression", "property"], "sources": ["../../src/modifications/appendToMemberExpression.ts"], "sourcesContent": ["import { memberExpression } from \"../builders/generated\";\nimport type * as t from \"..\";\n\n/**\n * Append a node to a member expression.\n */\nexport default function appendToMemberExpression(\n  member: t.MemberExpression,\n  append: t.MemberExpression[\"property\"],\n  computed: boolean = false,\n): t.MemberExpression {\n  member.object = memberExpression(\n    member.object,\n    member.property,\n    member.computed,\n  );\n  member.property = append;\n  member.computed = !!computed;\n\n  return member;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAMe,SAASC,wBAAwBA,CAC9CC,MAA0B,EAC1BC,MAAsC,EACtCC,QAAiB,GAAG,KAAK,EACL;EACpBF,MAAM,CAACG,MAAM,GAAG,IAAAC,2BAAgB,EAC9BJ,MAAM,CAACG,MAAM,EACbH,MAAM,CAACK,QAAQ,EACfL,MAAM,CAACE,QAAQ,CAChB;EACDF,MAAM,CAACK,QAAQ,GAAGJ,MAAM;EACxBD,MAAM,CAACE,QAAQ,GAAG,CAAC,CAACA,QAAQ;EAE5B,OAAOF,MAAM;AACf"}