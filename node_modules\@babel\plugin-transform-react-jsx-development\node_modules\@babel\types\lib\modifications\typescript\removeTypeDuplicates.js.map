{"version": 3, "names": ["_index", "require", "getQualifiedName", "node", "isIdentifier", "name", "right", "left", "removeTypeDuplicates", "nodesIn", "nodes", "Array", "from", "generics", "Map", "bases", "typeGroups", "Set", "types", "i", "length", "indexOf", "isTSAnyKeyword", "isTSBaseType", "set", "type", "isTSUnionType", "has", "push", "add", "isTSTypeReference", "typeParameters", "typeName", "existing", "get", "params", "baseType", "genericName"], "sources": ["../../../src/modifications/typescript/removeTypeDuplicates.ts"], "sourcesContent": ["import {\n  isIdentifier,\n  isTSAnyKeyword,\n  isTSTypeReference,\n  isTSUnionType,\n  isTSBaseType,\n} from \"../../validators/generated/index.ts\";\nimport type * as t from \"../../index.ts\";\n\nfunction getQualifiedName(node: t.TSTypeReference[\"typeName\"]): string {\n  return isIdentifier(node)\n    ? node.name\n    : `${node.right.name}.${getQualifiedName(node.left)}`;\n}\n\n/**\n * Dedupe type annotations.\n */\nexport default function removeTypeDuplicates(\n  nodesIn: ReadonlyArray<t.TSType>,\n): Array<t.TSType> {\n  const nodes = Array.from(nodesIn);\n\n  const generics = new Map<string, t.TSTypeReference>();\n  const bases = new Map<t.TSBaseType[\"type\"], t.TSBaseType>();\n\n  // store union type groups to circular references\n  const typeGroups = new Set<t.TSType[]>();\n\n  const types: t.TSType[] = [];\n\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if (!node) continue;\n\n    // detect duplicates\n    if (types.indexOf(node) >= 0) {\n      continue;\n    }\n\n    // this type matches anything\n    if (isTSAnyKeyword(node)) {\n      return [node];\n    }\n\n    // Analogue of FlowBaseAnnotation\n    if (isTSBaseType(node)) {\n      bases.set(node.type, node);\n      continue;\n    }\n\n    if (isTSUnionType(node)) {\n      if (!typeGroups.has(node.types)) {\n        nodes.push(...node.types);\n        typeGroups.add(node.types);\n      }\n      continue;\n    }\n\n    // todo: support merging tuples: number[]\n    if (isTSTypeReference(node) && node.typeParameters) {\n      const name = getQualifiedName(node.typeName);\n\n      if (generics.has(name)) {\n        let existing: t.TypeScript = generics.get(name);\n        if (existing.typeParameters) {\n          if (node.typeParameters) {\n            existing.typeParameters.params.push(...node.typeParameters.params);\n            existing.typeParameters.params = removeTypeDuplicates(\n              existing.typeParameters.params,\n            );\n          }\n        } else {\n          existing = node.typeParameters;\n        }\n      } else {\n        generics.set(name, node);\n      }\n\n      continue;\n    }\n\n    types.push(node);\n  }\n\n  // add back in bases\n  for (const [, baseType] of bases) {\n    types.push(baseType);\n  }\n\n  // add back in generics\n  for (const [, genericName] of generics) {\n    types.push(genericName);\n  }\n\n  return types;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AASA,SAASC,gBAAgBA,CAACC,IAAmC,EAAU;EACrE,OAAO,IAAAC,mBAAY,EAACD,IAAI,CAAC,GACrBA,IAAI,CAACE,IAAI,GACR,GAAEF,IAAI,CAACG,KAAK,CAACD,IAAK,IAAGH,gBAAgB,CAACC,IAAI,CAACI,IAAI,CAAE,EAAC;AACzD;AAKe,SAASC,oBAAoBA,CAC1CC,OAAgC,EACf;EACjB,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,OAAO,CAAC;EAEjC,MAAMI,QAAQ,GAAG,IAAIC,GAAG,CAA4B,CAAC;EACrD,MAAMC,KAAK,GAAG,IAAID,GAAG,CAAqC,CAAC;EAG3D,MAAME,UAAU,GAAG,IAAIC,GAAG,CAAa,CAAC;EAExC,MAAMC,KAAiB,GAAG,EAAE;EAE5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAMhB,IAAI,GAAGO,KAAK,CAACS,CAAC,CAAC;IACrB,IAAI,CAAChB,IAAI,EAAE;IAGX,IAAIe,KAAK,CAACG,OAAO,CAAClB,IAAI,CAAC,IAAI,CAAC,EAAE;MAC5B;IACF;IAGA,IAAI,IAAAmB,qBAAc,EAACnB,IAAI,CAAC,EAAE;MACxB,OAAO,CAACA,IAAI,CAAC;IACf;IAGA,IAAI,IAAAoB,mBAAY,EAACpB,IAAI,CAAC,EAAE;MACtBY,KAAK,CAACS,GAAG,CAACrB,IAAI,CAACsB,IAAI,EAAEtB,IAAI,CAAC;MAC1B;IACF;IAEA,IAAI,IAAAuB,oBAAa,EAACvB,IAAI,CAAC,EAAE;MACvB,IAAI,CAACa,UAAU,CAACW,GAAG,CAACxB,IAAI,CAACe,KAAK,CAAC,EAAE;QAC/BR,KAAK,CAACkB,IAAI,CAAC,GAAGzB,IAAI,CAACe,KAAK,CAAC;QACzBF,UAAU,CAACa,GAAG,CAAC1B,IAAI,CAACe,KAAK,CAAC;MAC5B;MACA;IACF;IAGA,IAAI,IAAAY,wBAAiB,EAAC3B,IAAI,CAAC,IAAIA,IAAI,CAAC4B,cAAc,EAAE;MAClD,MAAM1B,IAAI,GAAGH,gBAAgB,CAACC,IAAI,CAAC6B,QAAQ,CAAC;MAE5C,IAAInB,QAAQ,CAACc,GAAG,CAACtB,IAAI,CAAC,EAAE;QACtB,IAAI4B,QAAsB,GAAGpB,QAAQ,CAACqB,GAAG,CAAC7B,IAAI,CAAC;QAC/C,IAAI4B,QAAQ,CAACF,cAAc,EAAE;UAC3B,IAAI5B,IAAI,CAAC4B,cAAc,EAAE;YACvBE,QAAQ,CAACF,cAAc,CAACI,MAAM,CAACP,IAAI,CAAC,GAAGzB,IAAI,CAAC4B,cAAc,CAACI,MAAM,CAAC;YAClEF,QAAQ,CAACF,cAAc,CAACI,MAAM,GAAG3B,oBAAoB,CACnDyB,QAAQ,CAACF,cAAc,CAACI,MAC1B,CAAC;UACH;QACF,CAAC,MAAM;UACLF,QAAQ,GAAG9B,IAAI,CAAC4B,cAAc;QAChC;MACF,CAAC,MAAM;QACLlB,QAAQ,CAACW,GAAG,CAACnB,IAAI,EAAEF,IAAI,CAAC;MAC1B;MAEA;IACF;IAEAe,KAAK,CAACU,IAAI,CAACzB,IAAI,CAAC;EAClB;EAGA,KAAK,MAAM,GAAGiC,QAAQ,CAAC,IAAIrB,KAAK,EAAE;IAChCG,KAAK,CAACU,IAAI,CAACQ,QAAQ,CAAC;EACtB;EAGA,KAAK,MAAM,GAAGC,WAAW,CAAC,IAAIxB,QAAQ,EAAE;IACtCK,KAAK,CAACU,IAAI,CAACS,WAAW,CAAC;EACzB;EAEA,OAAOnB,KAAK;AACd"}