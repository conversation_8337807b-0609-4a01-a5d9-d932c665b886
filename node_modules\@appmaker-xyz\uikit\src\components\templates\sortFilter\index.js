import React, { useEffect, useState } from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import Modal from 'react-native-modal';
import { spacing, color } from '../../../styles';
import {
  AppmakerText,
  Button,
  VariationListing,
  Layout,
  Accordion,
  CheckBox,
} from '../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';

const CloseIcon = (props) => <Icon name="x" color={color.dark} {...props} />;
const Check = (props) => <Icon name="check" color={color.white} {...props} />;
const SortIcon = (props) => (
  <Icon name="sliders" color={color.dark} {...props} />
);
const FilterIcon = (props) => (
  <Icon name="filter" color={color.dark} {...props} />
);

const CheckList = (res, selectedParams, defaultArray = []) => {
  const [response, setResponse] = useState(res);
  const [finalParams, setFinalParams] = useState(defaultArray);
  // console.log(defaultArray);
  const content = response.map((resp, key) => {
    return (
      <CheckBox
        {...resp}
        value={defaultArray.includes(response[key].value)}
        onValueChange={(status) => {
          response[key].checked = status;
          if (status) {
            finalParams.push(response[key].value);
          } else {
            finalParams.splice(response[key].value);
          }
          setFinalParams(finalParams);
          selectedParams(finalParams);
          setResponse(response);
          res = response;
        }}
        key={key}
      />
    );
  });
  return content;
};

const FilterModalContent = (props) => {
  const [selectedFilterParams, setSelectedFilterParams] = useState(
    props.defaultFilterItem,
  );
  const filterItems = Object.entries(props.data.items).map((resp, key) => {
    let defaultArray = [];
    let keys = key;
    // const [defaultArray, setDefaultArray] = useState([]);
    if (resp[1].type == 'multi_slider') {
      return null;
      // return (
      //   <MultiSlider
      //     values={resp[1].values}
      //     step={resp[1].step}
      //     min={resp[1].min}
      //     onValuesChangeFinish={(values) => { console.log('asf') }}
      //     max={resp[1].max}
      //   />
      // );
    }
    // console.log(selectedFilterParams, 'asgfasdgsadg');
    if (resp[0] in selectedFilterParams.app_filter) {
      defaultArray = selectedFilterParams.app_filter[resp[0]];
    }
    return (
      <Accordion
        key={keys}
        attributes={{
          loading: false,
          title: resp[1].label,
          data: CheckList(
            resp[1].values,
            (itemsFromResponse) => {
              selectedFilterParams.app_filter = {
                ...selectedFilterParams.app_filter,
                [resp[0]]: itemsFromResponse,
              };
              setSelectedFilterParams(selectedFilterParams);
            },
            defaultArray,
          ),
        }}
      />
    );
  });
  return (
    <Layout style={styles.modalBody}>
      <Layout style={styles.modalHeader}>
        <Layout style={styles.headerItems}>
          <Button
            onPress={props.onPress}
            status="light"
            accessoryLeft={CloseIcon}
            small
          />
          <Layout>
            <AppmakerText category="bodyParagraphBold" status="demiDark">
              {props.staticTexts.filterDetailTitle}
            </AppmakerText>
            {/* <AppmakerText category="highlighter2" status="success">
              2 Filters Selected
            </AppmakerText> */}
          </Layout>
        </Layout>
        <Layout style={styles.headerItems}>
          <Button onPress={props.onClear} status="light" small>
            <AppmakerText category="smallButtonText" status="danger">
              {props.staticTexts.clearAllText}
            </AppmakerText>
          </Button>
          <Button
            onPress={() => props.selectedParams(selectedFilterParams)}
            status="dark"
            small
            accessoryLeft={Check}>
            {props.staticTexts.applyFiltersText}
          </Button>
        </Layout>
      </Layout>
      <ScrollView style={styles.modalContent}>
        <Layout
          blank={props.data.items.length === 0 ? true : false}
          message="No filters available"
          iconName="filter">
          {filterItems}
        </Layout>
        <Layout style={{ height: 24 }} />
      </ScrollView>
    </Layout>
  );
};
const SortModalContent = (props) => {
  const [selectedSortItem, setSelectedSortItem] = useState(
    props.defaultSortItem,
  );
  const sortItems = props.data.map((resp, key) => {
    let normalButtonProps = {
      status: 'demiDark',
      small: true,
      outline: true,
      block: true,
      onPress: () => {
        props.selectedSortItem(resp.params);
        props.onPress();
        setSelectedSortItem(resp.params);
      },
    };
    if (
      selectedSortItem.order === resp.params.order &&
      selectedSortItem.orderby === resp.params.orderby
    ) {
      delete normalButtonProps.outline;
      normalButtonProps.accessoryLeft = Check;
    }
    return (
      <Layout key={key} style={styles.sortItem}>
        <Button {...normalButtonProps}>{resp.label}</Button>
      </Layout>
    );
  });
  return (
    <Layout style={styles.sortModalBody}>
      <Layout style={styles.sortModalHeader}>
        <AppmakerText category="bodyParagraphBold" status="demiDark">
          {props.staticTexts.sortByText}
        </AppmakerText>
        <Button
          onPress={props.onPress}
          status="light"
          accessoryLeft={CloseIcon}
          small
        />
      </Layout>
      <Layout style={styles.sortModalContent}>{sortItems}</Layout>
    </Layout>
  );
};

const SortFilter = (props) => {
  const [filterVisible, setFilterVisible] = useState(false);
  const [sortVisible, setSortVisible] = useState(false);

  const openFilter = () => setFilterVisible(true);
  const closeFilter = () => {
    setFilterVisible(false);
    // props.attributes.defaultFilterItem = { app_filter: {} };
  };

  const openSort = () => setSortVisible(true);
  const closeSort = () => setSortVisible(false);

  const renderFilterModal = () => {
    return (
      <Modal
        testID={'modal'}
        isVisible={filterVisible}
        onSwipeComplete={closeFilter}
        animationIn="slideInUp"
        animationOut="slideOutDown"
        onBackButtonPress={closeFilter}
        backdropTransitionOutTiming={1}
        onBackdropPress={closeFilter}
        swipeDirection="down"
        style={styles.view}>
        <FilterModalContent
          data={props.attributes.filter_data}
          staticTexts={props.attributes.staticTexts}
          onPress={closeFilter}
          onClear={() => {
            props.attributes.defaultFilterItem = { app_filter: {} };
            closeFilter();
          }}
          selectedParams={props.attributes.selected_filter_params}
          defaultFilterItem={props.attributes.defaultFilterItem}
        />
      </Modal>
    );
  };

  const renderSortModal = () => (
    <Modal
      testID={'modal'}
      isVisible={sortVisible}
      onSwipeComplete={closeSort}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      onBackButtonPress={closeSort}
      backdropTransitionOutTiming={1}
      onBackdropPress={closeSort}
      style={styles.centerView}>
      <SortModalContent
        data={props.attributes.sort_data}
        staticTexts={props.attributes.staticTexts}
        selectedSortItem={props.attributes.selectedSortItem}
        defaultSortItem={props.attributes.defaultSortItem}
        onPress={closeSort}
      />
    </Modal>
  );
  const renderButton = () => (
    <Layout style={styles.buttonContainer}>
      {props.attributes.show_filter && (
        <Layout style={styles.innerContainer}>
          <Button
            onPress={openFilter}
            small
            accessoryLeft={SortIcon}
            // accessoryRight={() => (
            //   <AppmakerText category="smallButtonText" status="primary">
            //     (Filter count)
            //   </AppmakerText>
            // )}
            status="white">
            {props.attributes.staticTexts.filtersTitle}
          </Button>
        </Layout>
      )}
      {props.attributes.show_sort && (
        <Layout style={styles.innerContainer}>
          <Button
            small
            accessoryLeft={FilterIcon}
            onPress={openSort}
            status="white">
            {props.attributes.staticTexts.sortTitle}
          </Button>
        </Layout>
      )}
    </Layout>
  );

  return (
    <Layout style={styles.view}>
      {renderButton()}
      {renderFilterModal()}
      {renderSortModal()}
    </Layout>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    borderBottomColor: color.light,
    borderBottomWidth: 1,
  },
  innerContainer: {
    backgroundColor: color.white,
    flex: 1,
    borderRightColor: color.light,
    borderRightWidth: 1,
  },
  view: {
    justifyContent: 'flex-end',
    marginVertical: 0,
    marginHorizontal: 1,
  },
  modalBody: {
    backgroundColor: color.white,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
    flexDirection: 'column',
    height: '85%',
  },
  modalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: color.light,
    padding: spacing.base,
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
  },
  modalContent: {
    padding: spacing.md,
    flexDirection: 'column',
    width: '100%',
  },
  modalFooter: {
    width: '100%',
    backgroundColor: color.white,
    padding: spacing.base,
  },
  headerItems: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  centerView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  sortModalBody: {
    width: '80%',
    backgroundColor: color.white,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: spacing.small,
    flexDirection: 'column',
  },
  sortModalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: color.light,
    padding: spacing.base,
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
  },
  sortModalContent: {
    width: '100%',
    padding: spacing.base,
  },
  sortItem: {
    marginVertical: spacing.nano,
  },
});

export default SortFilter;
