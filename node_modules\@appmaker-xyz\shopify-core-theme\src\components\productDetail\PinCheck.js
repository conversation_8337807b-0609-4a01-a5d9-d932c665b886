import React, { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import axios from 'axios';
import { shopifyIdHelper } from '@appmaker-xyz/shopify';
import { useAsyncStorage } from '@appmaker-xyz/react-native';
import { emitEvent } from '@appmaker-xyz/core';
import { BlockCard, Button, Input, Layout, ThemeText } from '@appmaker-xyz/ui';
import Icon from 'react-native-vector-icons/Feather';

async function checkData({ pincode, product_id }) {
  const resp = await axios.get(
    'https://mby6me5g2i.execute-api.ap-south-1.amazonaws.com/production/pincode-checker',
    {
      params: {
        pincode,
        product_id,
        // product_type: 'nonfurniture',
        add_buffer: false,
      },
    },
  );
  const { data } = resp;
  return {
    loaded: true,
    deliveryType: data.delivery_available
      ? 'Standard Delivery'
      : 'Undeliverable',
    deliverybyDate: data.estimated_delivery,
    cod_available: data.cod_available,
    locationName: data.city,
    // deliveryFee: 'Rs. 100',
  };
}

const PinCheck = ({ attributes = {}, onAction }) => {
  const [loading, setLoading] = React.useState(false);
  const [pincodeStorage, setPinCodeStorage] = useAsyncStorage('pincode', '');
  const [pincode, setPinCode] = useState('');
  const [result, setResult] = useState({});
  const { __appmakerCustomStyles = {} } = attributes;

  useEffect(() => {
    if (pincodeStorage) {
      async function checkPin() {
        setPinCode(pincodeStorage);
        setLoading(true);
        // const product_id = base64
        const product_id = shopifyIdHelper(
          attributes?.blockItem?.node?.id,
          true,
        );
        try {
          const resp = await checkData({ pincode: pincodeStorage, product_id });
          setResult(resp);
          setLoading(false);
        } catch (error) {
          console.log(error);
          setLoading(false);
          onAction({
            action: 'SHOW_MESSAGE',
            params: { title: 'Error checking pincode' },
          });
        }
      }
      checkPin();
    }
  }, [pincodeStorage]);
  // console.log(result);
  return (
    <BlockCard
      attributes={{
        title: result.loaded
          ? `Delivery to ${result.locationName ? result.locationName : pincode}`
          : 'Check Delivery',
        accessButton: result ? 'Change Pin' : null,
        accessButtonColor: attributes.accessButtonColor,
      }}
      onPress={() => setResult({})}>
      {!result.loaded ? (
        <Layout
          style={[
            styles.pinCheck,
            styles.subPadding,
            __appmakerCustomStyles?.container,
          ]}>
          <Layout style={styles.column1}>
            <Input
              label="PIN Code"
              maxLength={6}
              styleType="noContainer"
              value={pincode}
              onChangeText={setPinCode}
              type="number"
              __appmakerCustomStyles={__appmakerCustomStyles?.input}
            />
          </Layout>
          <Layout style={styles.column2}>
            <Button
              title="Check"
              isLoading={loading}
              onPress={async () => {
                if (pincode.length === 6) {
                  emitEvent('pincodeCheck', {
                    pincode,
                    product_name: attributes?.blockItem?.node?.title,
                  });
                  setPinCodeStorage(pincode);
                } else {
                  onAction({
                    action: 'SHOW_MESSAGE',
                    params: { title: 'Please enter a valid pincode' },
                  });
                }
              }}
              buttonStyle={__appmakerCustomStyles?.button}
            />
          </Layout>
        </Layout>
      ) : (
        <Layout style={styles.subPadding}>
          <Layout
            style={[
              styles.resultContainer,
              __appmakerCustomStyles?.success_container,
            ]}>
            <Layout flexDirection="row">
              <Icon
                name={result?.deliverIcon || 'truck'}
                size={24}
                style={[styles.icon, __appmakerCustomStyles?.success_icon]}
                color={
                  result.deliveryType === 'Undeliverable'
                    ? '#CB4141'
                    : '#1B1B1B'
                }
              />
              <Layout>
                {result?.deliveryType ? (
                  <ThemeText
                    size="lg"
                    fontFamily="bold"
                    color={
                      result.deliveryType === 'Undeliverable'
                        ? '#CB4141'
                        : '#1B1B1B'
                    }
                    style={__appmakerCustomStyles?.success_text}>
                    {result.deliveryType}
                  </ThemeText>
                ) : null}
                {result?.deliverybyDate ? (
                  <ThemeText style={__appmakerCustomStyles?.success_text}>
                    Delivery by
                    <ThemeText>{result.deliverybyDate}</ThemeText>
                  </ThemeText>
                ) : null}
              </Layout>
            </Layout>
            {result.deliveryType !== 'Undeliverable' ? (
              <ThemeText
                fontFamily="bold"
                color={result.cod_available ? '#2FA478' : '#1B1B1B'}
                style={__appmakerCustomStyles?.success_cod_text}>
                {result.cod_available ? 'COD Available' : 'COD Not Available'}
              </ThemeText>
            ) : null}
          </Layout>
        </Layout>
      )}
    </BlockCard>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
  },
  pinCheck: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // paddingTop: 6,
  },
  column1: {
    width: '70%',
  },
  column2: {
    width: '28%',
  },
  subPadding: {
    paddingHorizontal: 12,
    paddingBottom: 12,
  },
  resultContainer: {
    backgroundColor: '#E9EDF1',
    paddingVertical: 6,
    paddingHorizontal: 8,
    borderRadius: 6,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  icon: {
    marginRight: 6,
  },
});

export default PinCheck;
