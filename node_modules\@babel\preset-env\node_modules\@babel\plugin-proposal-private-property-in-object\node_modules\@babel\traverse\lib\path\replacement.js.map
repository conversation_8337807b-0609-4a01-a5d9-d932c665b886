{"version": 3, "names": ["_codeFrame", "require", "_index", "_index2", "_cache", "_parser", "_t", "_helperHoistVariables", "FUNCTION_TYPES", "arrowFunctionExpression", "assignmentExpression", "awaitExpression", "blockStatement", "callExpression", "cloneNode", "expressionStatement", "identifier", "inheritLeadingComments", "inheritTrailingComments", "inheritsComments", "isExpression", "isProgram", "isStatement", "removeComments", "returnStatement", "toSequenceExpression", "validate", "yieldExpression", "replaceWithMultiple", "nodes", "_pathCache$get", "resync", "_verifyNodeList", "node", "length", "pathCache", "get", "parent", "delete", "container", "key", "paths", "insertAfter", "requeue", "remove", "replaceWithSourceString", "replacement", "ast", "parse", "err", "loc", "message", "codeFrameColumns", "start", "line", "column", "code", "expressionAST", "program", "body", "expression", "traverse", "removeProperties", "replaceWith", "<PERSON><PERSON><PERSON>", "removed", "Error", "NodePath", "Array", "isArray", "nodePath", "isNodeType", "canHaveVariableDeclarationOrExpression", "canSwapBetweenExpressionAndStatement", "parentPath", "isExportDefaultDeclaration", "replaceExpressionWithStatements", "oldNode", "_replaceWith", "type", "setScope", "_pathCache$get2", "ReferenceError", "inList", "debug", "set", "nodesAsSequenceExpression", "scope", "functionParent", "getFunctionParent", "isParentAsync", "is", "isParentGenerator", "callee", "hoistVariables", "id", "push", "completionRecords", "getCompletionRecords", "path", "isExpressionStatement", "loop", "findParent", "isLoop", "uid", "getData", "generateDeclaredUidIdentifier", "pushContainer", "setData", "name", "arrowFunctionToExpression", "newCallee", "needToAwaitFunction", "hasType", "needToYieldFunction", "replaceInline", "_containerInsertAfter"], "sources": ["../../src/path/replacement.ts"], "sourcesContent": ["// This file contains methods responsible for replacing a node with another.\n\nimport { codeFrameColumns } from \"@babel/code-frame\";\nimport traverse from \"../index\";\nimport NodePath from \"./index\";\nimport { path as pathCache } from \"../cache\";\nimport { parse } from \"@babel/parser\";\nimport {\n  FUNCTION_TYPES,\n  arrowFunctionExpression,\n  assignmentExpression,\n  awaitExpression,\n  blockStatement,\n  callExpression,\n  cloneNode,\n  expressionStatement,\n  identifier,\n  inheritLeadingComments,\n  inheritTrailingComments,\n  inheritsComments,\n  isExpression,\n  isProgram,\n  isStatement,\n  removeComments,\n  returnStatement,\n  toSequenceExpression,\n  validate,\n  yieldExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport hoistVariables from \"@babel/helper-hoist-variables\";\n\n/**\n * Replace a node with an array of multiple. This method performs the following steps:\n *\n *  - Inherit the comments of first provided node with that of the current node.\n *  - Insert the provided nodes after the current node.\n *  - Remove the current node.\n */\n\nexport function replaceWithMultiple(\n  this: NodePath,\n  nodes: t.Node | t.Node[],\n): NodePath[] {\n  this.resync();\n\n  nodes = this._verifyNodeList(nodes);\n  inheritLeadingComments(nodes[0], this.node);\n  inheritTrailingComments(nodes[nodes.length - 1], this.node);\n  pathCache.get(this.parent)?.delete(this.node);\n  this.node =\n    // @ts-expect-error this.key must present in this.container\n    this.container[this.key] = null;\n  const paths = this.insertAfter(nodes);\n\n  if (this.node) {\n    this.requeue();\n  } else {\n    this.remove();\n  }\n  return paths;\n}\n\n/**\n * Parse a string as an expression and replace the current node with the result.\n *\n * NOTE: This is typically not a good idea to use. Building source strings when\n * transforming ASTs is an antipattern and SHOULD NOT be encouraged. Even if it's\n * easier to use, your transforms will be extremely brittle.\n */\n\nexport function replaceWithSourceString(this: NodePath, replacement: string) {\n  this.resync();\n  let ast: t.File;\n\n  try {\n    replacement = `(${replacement})`;\n    // @ts-expect-error todo: use babel-types ast typings in Babel parser\n    ast = parse(replacement);\n  } catch (err) {\n    const loc = err.loc;\n    if (loc) {\n      err.message +=\n        \" - make sure this is an expression.\\n\" +\n        codeFrameColumns(replacement, {\n          start: {\n            line: loc.line,\n            column: loc.column + 1,\n          },\n        });\n      err.code = \"BABEL_REPLACE_SOURCE_ERROR\";\n    }\n    throw err;\n  }\n\n  const expressionAST = (ast.program.body[0] as t.ExpressionStatement)\n    .expression;\n  traverse.removeProperties(expressionAST);\n  return this.replaceWith(expressionAST);\n}\n\n/**\n * Replace the current node with another.\n */\n\nexport function replaceWith<R extends t.Node>(\n  this: NodePath,\n  replacementPath: R | NodePath<R>,\n): [NodePath<R>] {\n  this.resync();\n\n  if (this.removed) {\n    throw new Error(\"You can't replace this node, we've already removed it\");\n  }\n\n  let replacement: t.Node =\n    replacementPath instanceof NodePath\n      ? replacementPath.node\n      : replacementPath;\n\n  if (!replacement) {\n    throw new Error(\n      \"You passed `path.replaceWith()` a falsy node, use `path.remove()` instead\",\n    );\n  }\n\n  if (this.node === replacement) {\n    return [this as NodePath<R>];\n  }\n\n  if (this.isProgram() && !isProgram(replacement)) {\n    throw new Error(\n      \"You can only replace a Program root node with another Program node\",\n    );\n  }\n\n  if (Array.isArray(replacement)) {\n    throw new Error(\n      \"Don't use `path.replaceWith()` with an array of nodes, use `path.replaceWithMultiple()`\",\n    );\n  }\n\n  if (typeof replacement === \"string\") {\n    throw new Error(\n      \"Don't use `path.replaceWith()` with a source string, use `path.replaceWithSourceString()`\",\n    );\n  }\n\n  let nodePath = \"\";\n\n  if (this.isNodeType(\"Statement\") && isExpression(replacement)) {\n    if (\n      !this.canHaveVariableDeclarationOrExpression() &&\n      !this.canSwapBetweenExpressionAndStatement(replacement) &&\n      !this.parentPath.isExportDefaultDeclaration()\n    ) {\n      // replacing a statement with an expression so wrap it in an expression statement\n      replacement = expressionStatement(replacement);\n      nodePath = \"expression\";\n    }\n  }\n\n  if (this.isNodeType(\"Expression\") && isStatement(replacement)) {\n    if (\n      !this.canHaveVariableDeclarationOrExpression() &&\n      !this.canSwapBetweenExpressionAndStatement(replacement)\n    ) {\n      // replacing an expression with a statement so let's explode it\n      return this.replaceExpressionWithStatements([replacement]) as [\n        NodePath<R>,\n      ];\n    }\n  }\n\n  const oldNode = this.node;\n  if (oldNode) {\n    inheritsComments(replacement, oldNode);\n    removeComments(oldNode);\n  }\n\n  // replace the node\n  this._replaceWith(replacement);\n  this.type = replacement.type;\n\n  // potentially create new scope\n  this.setScope();\n\n  // requeue for visiting\n  this.requeue();\n\n  return [\n    nodePath ? (this.get(nodePath) as NodePath<R>) : (this as NodePath<R>),\n  ];\n}\n\n/**\n * Description\n */\n\nexport function _replaceWith(this: NodePath, node: t.Node) {\n  if (!this.container) {\n    throw new ReferenceError(\"Container is falsy\");\n  }\n\n  if (this.inList) {\n    // @ts-expect-error todo(flow->ts): check if validate accepts a numeric key\n    validate(this.parent, this.key, [node]);\n  } else {\n    validate(this.parent, this.key as string, node);\n  }\n\n  this.debug(`Replace with ${node?.type}`);\n  pathCache.get(this.parent)?.set(node, this).delete(this.node);\n\n  this.node =\n    // @ts-expect-error this.key must present in this.container\n    this.container[this.key] = node;\n}\n\n/**\n * This method takes an array of statements nodes and then explodes it\n * into expressions. This method retains completion records which is\n * extremely important to retain original semantics.\n */\n\nexport function replaceExpressionWithStatements(\n  this: NodePath,\n  nodes: Array<t.Statement>,\n) {\n  this.resync();\n\n  const nodesAsSequenceExpression = toSequenceExpression(nodes, this.scope);\n\n  if (nodesAsSequenceExpression) {\n    return this.replaceWith(nodesAsSequenceExpression)[0].get(\"expressions\");\n  }\n\n  const functionParent = this.getFunctionParent();\n  const isParentAsync = functionParent?.is(\"async\");\n  const isParentGenerator = functionParent?.is(\"generator\");\n\n  const container = arrowFunctionExpression([], blockStatement(nodes));\n\n  this.replaceWith(callExpression(container, []));\n  // replaceWith changes the type of \"this\", but it isn't trackable by TS\n  type ThisType = NodePath<\n    t.CallExpression & {\n      callee: t.ArrowFunctionExpression & { body: t.BlockStatement };\n    }\n  >;\n\n  // hoist variable declaration in do block\n  // `(do { var x = 1; x;})` -> `var x; (() => { x = 1; return x; })()`\n  const callee = (this as ThisType).get(\"callee\");\n  hoistVariables(\n    callee.get(\"body\"),\n    (id: t.Identifier) => {\n      this.scope.push({ id });\n    },\n    \"var\",\n  );\n\n  // add implicit returns to all ending expression statements\n  const completionRecords: Array<NodePath> = (this as ThisType)\n    .get(\"callee\")\n    .getCompletionRecords();\n  for (const path of completionRecords) {\n    if (!path.isExpressionStatement()) continue;\n\n    const loop = path.findParent(path => path.isLoop());\n    if (loop) {\n      let uid = loop.getData(\"expressionReplacementReturnUid\");\n\n      if (!uid) {\n        uid = callee.scope.generateDeclaredUidIdentifier(\"ret\");\n        callee\n          .get(\"body\")\n          .pushContainer(\"body\", returnStatement(cloneNode(uid)));\n        loop.setData(\"expressionReplacementReturnUid\", uid);\n      } else {\n        uid = identifier(uid.name);\n      }\n\n      path\n        .get(\"expression\")\n        .replaceWith(\n          assignmentExpression(\"=\", cloneNode(uid), path.node.expression),\n        );\n    } else {\n      path.replaceWith(returnStatement(path.node.expression));\n    }\n  }\n\n  // This is an IIFE, so we don't need to worry about the noNewArrows assumption\n  callee.arrowFunctionToExpression();\n  // Fixme: we can not `assert this is NodePath<t.FunctionExpression>` in `arrowFunctionToExpression`\n  // because it is not a class method known at compile time.\n  const newCallee = callee as unknown as NodePath<t.FunctionExpression>;\n\n  // (() => await xxx)() -> await (async () => await xxx)();\n  const needToAwaitFunction =\n    isParentAsync &&\n    traverse.hasType(\n      (this.get(\"callee.body\") as NodePath<t.BlockStatement>).node,\n      \"AwaitExpression\",\n      FUNCTION_TYPES,\n    );\n  const needToYieldFunction =\n    isParentGenerator &&\n    traverse.hasType(\n      (this.get(\"callee.body\") as NodePath<t.BlockStatement>).node,\n      \"YieldExpression\",\n      FUNCTION_TYPES,\n    );\n  if (needToAwaitFunction) {\n    newCallee.set(\"async\", true);\n    // yield* will await the generator return result\n    if (!needToYieldFunction) {\n      this.replaceWith(awaitExpression((this as ThisType).node));\n    }\n  }\n  if (needToYieldFunction) {\n    newCallee.set(\"generator\", true);\n    this.replaceWith(yieldExpression((this as ThisType).node, true));\n  }\n\n  return newCallee.get(\"body.body\");\n}\n\nexport function replaceInline(this: NodePath, nodes: t.Node | Array<t.Node>) {\n  this.resync();\n\n  if (Array.isArray(nodes)) {\n    if (Array.isArray(this.container)) {\n      nodes = this._verifyNodeList(nodes);\n      const paths = this._containerInsertAfter(nodes);\n      this.remove();\n      return paths;\n    } else {\n      return this.replaceWithMultiple(nodes);\n    }\n  } else {\n    return this.replaceWith(nodes);\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAEA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,EAAA,GAAAL,OAAA;AAuBA,IAAAM,qBAAA,GAAAN,OAAA;AAA2D;EAtBzDO,cAAc;EACdC,uBAAuB;EACvBC,oBAAoB;EACpBC,eAAe;EACfC,cAAc;EACdC,cAAc;EACdC,SAAS;EACTC,mBAAmB;EACnBC,UAAU;EACVC,sBAAsB;EACtBC,uBAAuB;EACvBC,gBAAgB;EAChBC,YAAY;EACZC,SAAS;EACTC,WAAW;EACXC,cAAc;EACdC,eAAe;EACfC,oBAAoB;EACpBC,QAAQ;EACRC;AAAe,IAAArB,EAAA;AAaV,SAASsB,mBAAmBA,CAEjCC,KAAwB,EACZ;EAAA,IAAAC,cAAA;EACZ,IAAI,CAACC,MAAM,EAAE;EAEbF,KAAK,GAAG,IAAI,CAACG,eAAe,CAACH,KAAK,CAAC;EACnCZ,sBAAsB,CAACY,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAACI,IAAI,CAAC;EAC3Cf,uBAAuB,CAACW,KAAK,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAACD,IAAI,CAAC;EAC3D,CAAAH,cAAA,GAAAK,WAAS,CAACC,GAAG,CAAC,IAAI,CAACC,MAAM,CAAC,qBAA1BP,cAAA,CAA4BQ,MAAM,CAAC,IAAI,CAACL,IAAI,CAAC;EAC7C,IAAI,CAACA,IAAI,GAEP,IAAI,CAACM,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI;EACjC,MAAMC,KAAK,GAAG,IAAI,CAACC,WAAW,CAACb,KAAK,CAAC;EAErC,IAAI,IAAI,CAACI,IAAI,EAAE;IACb,IAAI,CAACU,OAAO,EAAE;EAChB,CAAC,MAAM;IACL,IAAI,CAACC,MAAM,EAAE;EACf;EACA,OAAOH,KAAK;AACd;AAUO,SAASI,uBAAuBA,CAAiBC,WAAmB,EAAE;EAC3E,IAAI,CAACf,MAAM,EAAE;EACb,IAAIgB,GAAW;EAEf,IAAI;IACFD,WAAW,GAAI,IAAGA,WAAY,GAAE;IAEhCC,GAAG,GAAG,IAAAC,aAAK,EAACF,WAAW,CAAC;EAC1B,CAAC,CAAC,OAAOG,GAAG,EAAE;IACZ,MAAMC,GAAG,GAAGD,GAAG,CAACC,GAAG;IACnB,IAAIA,GAAG,EAAE;MACPD,GAAG,CAACE,OAAO,IACT,uCAAuC,GACvC,IAAAC,2BAAgB,EAACN,WAAW,EAAE;QAC5BO,KAAK,EAAE;UACLC,IAAI,EAAEJ,GAAG,CAACI,IAAI;UACdC,MAAM,EAAEL,GAAG,CAACK,MAAM,GAAG;QACvB;MACF,CAAC,CAAC;MACJN,GAAG,CAACO,IAAI,GAAG,4BAA4B;IACzC;IACA,MAAMP,GAAG;EACX;EAEA,MAAMQ,aAAa,GAAIV,GAAG,CAACW,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,CACvCC,UAAU;EACbC,cAAQ,CAACC,gBAAgB,CAACL,aAAa,CAAC;EACxC,OAAO,IAAI,CAACM,WAAW,CAACN,aAAa,CAAC;AACxC;AAMO,SAASM,WAAWA,CAEzBC,eAAgC,EACjB;EACf,IAAI,CAACjC,MAAM,EAAE;EAEb,IAAI,IAAI,CAACkC,OAAO,EAAE;IAChB,MAAM,IAAIC,KAAK,CAAC,uDAAuD,CAAC;EAC1E;EAEA,IAAIpB,WAAmB,GACrBkB,eAAe,YAAYG,eAAQ,GAC/BH,eAAe,CAAC/B,IAAI,GACpB+B,eAAe;EAErB,IAAI,CAAClB,WAAW,EAAE;IAChB,MAAM,IAAIoB,KAAK,CACb,2EAA2E,CAC5E;EACH;EAEA,IAAI,IAAI,CAACjC,IAAI,KAAKa,WAAW,EAAE;IAC7B,OAAO,CAAC,IAAI,CAAgB;EAC9B;EAEA,IAAI,IAAI,CAACzB,SAAS,EAAE,IAAI,CAACA,SAAS,CAACyB,WAAW,CAAC,EAAE;IAC/C,MAAM,IAAIoB,KAAK,CACb,oEAAoE,CACrE;EACH;EAEA,IAAIE,KAAK,CAACC,OAAO,CAACvB,WAAW,CAAC,EAAE;IAC9B,MAAM,IAAIoB,KAAK,CACb,yFAAyF,CAC1F;EACH;EAEA,IAAI,OAAOpB,WAAW,KAAK,QAAQ,EAAE;IACnC,MAAM,IAAIoB,KAAK,CACb,2FAA2F,CAC5F;EACH;EAEA,IAAII,QAAQ,GAAG,EAAE;EAEjB,IAAI,IAAI,CAACC,UAAU,CAAC,WAAW,CAAC,IAAInD,YAAY,CAAC0B,WAAW,CAAC,EAAE;IAC7D,IACE,CAAC,IAAI,CAAC0B,sCAAsC,EAAE,IAC9C,CAAC,IAAI,CAACC,oCAAoC,CAAC3B,WAAW,CAAC,IACvD,CAAC,IAAI,CAAC4B,UAAU,CAACC,0BAA0B,EAAE,EAC7C;MAEA7B,WAAW,GAAG/B,mBAAmB,CAAC+B,WAAW,CAAC;MAC9CwB,QAAQ,GAAG,YAAY;IACzB;EACF;EAEA,IAAI,IAAI,CAACC,UAAU,CAAC,YAAY,CAAC,IAAIjD,WAAW,CAACwB,WAAW,CAAC,EAAE;IAC7D,IACE,CAAC,IAAI,CAAC0B,sCAAsC,EAAE,IAC9C,CAAC,IAAI,CAACC,oCAAoC,CAAC3B,WAAW,CAAC,EACvD;MAEA,OAAO,IAAI,CAAC8B,+BAA+B,CAAC,CAAC9B,WAAW,CAAC,CAAC;IAG5D;EACF;EAEA,MAAM+B,OAAO,GAAG,IAAI,CAAC5C,IAAI;EACzB,IAAI4C,OAAO,EAAE;IACX1D,gBAAgB,CAAC2B,WAAW,EAAE+B,OAAO,CAAC;IACtCtD,cAAc,CAACsD,OAAO,CAAC;EACzB;EAGA,IAAI,CAACC,YAAY,CAAChC,WAAW,CAAC;EAC9B,IAAI,CAACiC,IAAI,GAAGjC,WAAW,CAACiC,IAAI;EAG5B,IAAI,CAACC,QAAQ,EAAE;EAGf,IAAI,CAACrC,OAAO,EAAE;EAEd,OAAO,CACL2B,QAAQ,GAAI,IAAI,CAAClC,GAAG,CAACkC,QAAQ,CAAC,GAAoB,IAAoB,CACvE;AACH;AAMO,SAASQ,YAAYA,CAAiB7C,IAAY,EAAE;EAAA,IAAAgD,eAAA;EACzD,IAAI,CAAC,IAAI,CAAC1C,SAAS,EAAE;IACnB,MAAM,IAAI2C,cAAc,CAAC,oBAAoB,CAAC;EAChD;EAEA,IAAI,IAAI,CAACC,MAAM,EAAE;IAEfzD,QAAQ,CAAC,IAAI,CAACW,MAAM,EAAE,IAAI,CAACG,GAAG,EAAE,CAACP,IAAI,CAAC,CAAC;EACzC,CAAC,MAAM;IACLP,QAAQ,CAAC,IAAI,CAACW,MAAM,EAAE,IAAI,CAACG,GAAG,EAAYP,IAAI,CAAC;EACjD;EAEA,IAAI,CAACmD,KAAK,CAAE,gBAAenD,IAAI,oBAAJA,IAAI,CAAE8C,IAAK,EAAC,CAAC;EACxC,CAAAE,eAAA,GAAA9C,WAAS,CAACC,GAAG,CAAC,IAAI,CAACC,MAAM,CAAC,qBAA1B4C,eAAA,CAA4BI,GAAG,CAACpD,IAAI,EAAE,IAAI,CAAC,CAACK,MAAM,CAAC,IAAI,CAACL,IAAI,CAAC;EAE7D,IAAI,CAACA,IAAI,GAEP,IAAI,CAACM,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,GAAGP,IAAI;AACnC;AAQO,SAAS2C,+BAA+BA,CAE7C/C,KAAyB,EACzB;EACA,IAAI,CAACE,MAAM,EAAE;EAEb,MAAMuD,yBAAyB,GAAG7D,oBAAoB,CAACI,KAAK,EAAE,IAAI,CAAC0D,KAAK,CAAC;EAEzE,IAAID,yBAAyB,EAAE;IAC7B,OAAO,IAAI,CAACvB,WAAW,CAACuB,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAClD,GAAG,CAAC,aAAa,CAAC;EAC1E;EAEA,MAAMoD,cAAc,GAAG,IAAI,CAACC,iBAAiB,EAAE;EAC/C,MAAMC,aAAa,GAAGF,cAAc,oBAAdA,cAAc,CAAEG,EAAE,CAAC,OAAO,CAAC;EACjD,MAAMC,iBAAiB,GAAGJ,cAAc,oBAAdA,cAAc,CAAEG,EAAE,CAAC,WAAW,CAAC;EAEzD,MAAMpD,SAAS,GAAG9B,uBAAuB,CAAC,EAAE,EAAEG,cAAc,CAACiB,KAAK,CAAC,CAAC;EAEpE,IAAI,CAACkC,WAAW,CAAClD,cAAc,CAAC0B,SAAS,EAAE,EAAE,CAAC,CAAC;EAU/C,MAAMsD,MAAM,GAAI,IAAI,CAAczD,GAAG,CAAC,QAAQ,CAAC;EAC/C,IAAA0D,6BAAc,EACZD,MAAM,CAACzD,GAAG,CAAC,MAAM,CAAC,EACjB2D,EAAgB,IAAK;IACpB,IAAI,CAACR,KAAK,CAACS,IAAI,CAAC;MAAED;IAAG,CAAC,CAAC;EACzB,CAAC,EACD,KAAK,CACN;EAGD,MAAME,iBAAkC,GAAI,IAAI,CAC7C7D,GAAG,CAAC,QAAQ,CAAC,CACb8D,oBAAoB,EAAE;EACzB,KAAK,MAAMC,IAAI,IAAIF,iBAAiB,EAAE;IACpC,IAAI,CAACE,IAAI,CAACC,qBAAqB,EAAE,EAAE;IAEnC,MAAMC,IAAI,GAAGF,IAAI,CAACG,UAAU,CAACH,IAAI,IAAIA,IAAI,CAACI,MAAM,EAAE,CAAC;IACnD,IAAIF,IAAI,EAAE;MACR,IAAIG,GAAG,GAAGH,IAAI,CAACI,OAAO,CAAC,gCAAgC,CAAC;MAExD,IAAI,CAACD,GAAG,EAAE;QACRA,GAAG,GAAGX,MAAM,CAACN,KAAK,CAACmB,6BAA6B,CAAC,KAAK,CAAC;QACvDb,MAAM,CACHzD,GAAG,CAAC,MAAM,CAAC,CACXuE,aAAa,CAAC,MAAM,EAAEnF,eAAe,CAACV,SAAS,CAAC0F,GAAG,CAAC,CAAC,CAAC;QACzDH,IAAI,CAACO,OAAO,CAAC,gCAAgC,EAAEJ,GAAG,CAAC;MACrD,CAAC,MAAM;QACLA,GAAG,GAAGxF,UAAU,CAACwF,GAAG,CAACK,IAAI,CAAC;MAC5B;MAEAV,IAAI,CACD/D,GAAG,CAAC,YAAY,CAAC,CACjB2B,WAAW,CACVrD,oBAAoB,CAAC,GAAG,EAAEI,SAAS,CAAC0F,GAAG,CAAC,EAAEL,IAAI,CAAClE,IAAI,CAAC2B,UAAU,CAAC,CAChE;IACL,CAAC,MAAM;MACLuC,IAAI,CAACpC,WAAW,CAACvC,eAAe,CAAC2E,IAAI,CAAClE,IAAI,CAAC2B,UAAU,CAAC,CAAC;IACzD;EACF;EAGAiC,MAAM,CAACiB,yBAAyB,EAAE;EAGlC,MAAMC,SAAS,GAAGlB,MAAmD;EAGrE,MAAMmB,mBAAmB,GACvBtB,aAAa,IACb7B,cAAQ,CAACoD,OAAO,CACb,IAAI,CAAC7E,GAAG,CAAC,aAAa,CAAC,CAAgCH,IAAI,EAC5D,iBAAiB,EACjBzB,cAAc,CACf;EACH,MAAM0G,mBAAmB,GACvBtB,iBAAiB,IACjB/B,cAAQ,CAACoD,OAAO,CACb,IAAI,CAAC7E,GAAG,CAAC,aAAa,CAAC,CAAgCH,IAAI,EAC5D,iBAAiB,EACjBzB,cAAc,CACf;EACH,IAAIwG,mBAAmB,EAAE;IACvBD,SAAS,CAAC1B,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC;IAE5B,IAAI,CAAC6B,mBAAmB,EAAE;MACxB,IAAI,CAACnD,WAAW,CAACpD,eAAe,CAAE,IAAI,CAAcsB,IAAI,CAAC,CAAC;IAC5D;EACF;EACA,IAAIiF,mBAAmB,EAAE;IACvBH,SAAS,CAAC1B,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC;IAChC,IAAI,CAACtB,WAAW,CAACpC,eAAe,CAAE,IAAI,CAAcM,IAAI,EAAE,IAAI,CAAC,CAAC;EAClE;EAEA,OAAO8E,SAAS,CAAC3E,GAAG,CAAC,WAAW,CAAC;AACnC;AAEO,SAAS+E,aAAaA,CAAiBtF,KAA6B,EAAE;EAC3E,IAAI,CAACE,MAAM,EAAE;EAEb,IAAIqC,KAAK,CAACC,OAAO,CAACxC,KAAK,CAAC,EAAE;IACxB,IAAIuC,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC9B,SAAS,CAAC,EAAE;MACjCV,KAAK,GAAG,IAAI,CAACG,eAAe,CAACH,KAAK,CAAC;MACnC,MAAMY,KAAK,GAAG,IAAI,CAAC2E,qBAAqB,CAACvF,KAAK,CAAC;MAC/C,IAAI,CAACe,MAAM,EAAE;MACb,OAAOH,KAAK;IACd,CAAC,MAAM;MACL,OAAO,IAAI,CAACb,mBAAmB,CAACC,KAAK,CAAC;IACxC;EACF,CAAC,MAAM;IACL,OAAO,IAAI,CAACkC,WAAW,CAAClC,KAAK,CAAC;EAChC;AACF"}