import React, { useState } from 'react';
import { Button, ThemeText, Layout } from '@appmaker-xyz/ui';
import { testProps } from '@appmaker-xyz/core';
import { useCart } from '@appmaker-xyz/shopify';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';

const CheckoutButtonTwo = ({
  clientId,
  show,
  totalPrice,
  subtotalAmount,
  showSubTotalAmount = false,
  onPress,
  viewCartText = 'View Cart',
  __appmakerCustomStyles: customStyles = {},
}) => {
  const { styles, theme } = useStyles(stylesheet);
  const { totalQuantity, cartTotalPayableWithCurrency } = useCart();
  const [loading, setLoading] = useState(false);

  const containerStyles = [styles.checkoutButtonContainer];
  if (show === true) {
    containerStyles.push({ bottom: 0 });
  }
  if (customStyles.container) {
    containerStyles.push(customStyles?.container);
  }
  async function _onPress() {
    setLoading(true);
    await onPress();
    setLoading(false);
  }

  return (
    <Layout style={containerStyles} {...testProps(`${clientId}-container`)}>
      <Layout style={styles.contentContainer}>
        <ThemeText
          size="sm"
          color={customStyles?.itemCountText ? null : theme.colors.text}
          style={customStyles?.itemCountText}>
          {totalQuantity + ' Items'}
        </ThemeText>
        <Layout style={styles.priceDetails}>
          <ThemeText
            fontFamily="bold"
            color={customStyles?.itemCountText ? null : theme.colors.text}
            style={customStyles?.itemCountText}>
            {cartTotalPayableWithCurrency}
          </ThemeText>
          {showSubTotalAmount === true && totalPrice !== subtotalAmount ? (
            <ThemeText
              category="bodySubText"
              color={customStyles?.itemCountText ? null : theme.colors.text}
              style={
                ([styles.subtotalAmountText],
                {
                  ...customStyles?.itemCountText,
                  ...styles.subtotalAmountText,
                })
              }>
              {subtotalAmount}
            </ThemeText>
          ) : null}
        </Layout>
      </Layout>
      <Button
        color={customStyles?.button ? null : '#000'}
        style={styles.checkoutButton}
        onPress={_onPress}
        isLoading={loading}
        activityIndicatorColor={'#fff'}
        clientId={clientId}
        title={viewCartText}
        buttonStyle={[styles.buttonContainer, customStyles?.button]}
      />
    </Layout>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  checkoutButtonContainer: {
    width: '100%',
    backgroundColor: theme.colors.background,
    borderTopWidth: 1,
    borderTopColor: theme.colors.separator,
    flexDirection: 'row',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: theme.spacings.lg,
    paddingVertical: theme.spacings.md,
  },
  checkoutButton: {
    justifyContent: 'space-between',
    width: '98%',
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: -6,
  },
  priceDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subtotalAmountText: {
    marginLeft: 10,
    textDecorationLine: 'line-through',
    textDecorationStyle: 'solid',
  },
  fontColor: {},
  buttonContainer: {
    borderRadius: 0,
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
}));

export default CheckoutButtonTwo;
