import { Image, Pressable, StyleSheet, Text, View } from 'react-native';
import React, { useState } from 'react';
import Icon from '@appmaker-xyz/uikit/Icons/FontAwesome';
import { AppmakerText, Button, AppTouchable } from '@appmaker-xyz/uikit';
import BlockCard from '@appmaker-xyz/uikit/src/components/organisms/card/BlockCard';

export default function CardItem({
  attributes,
  blockData,
  clientId,
  onAction,
}) {
  const {
    title,
    regularPrice,
    salePrice,
    discountPercentage,
    uri,
    productType,
    salePercentage,
    appmakerAction,
    cartAction,
  } = attributes;
  const [isLoading, setIsLoading] = useState(false);
  const onPressAddToCart = async () => {
    setIsLoading(true);
    if (productType === 'variable') {
      onAction({
        action: 'SHOW_MESSAGE',
        params: { title: 'Please select a product variant' },
      });
      onAction(appmakerAction);
      setIsLoading(false);
    } else {
      await onAction(cartAction);
      setIsLoading(false);
    }
  };
  return (
    <AppTouchable
      onPress={() => onAction(appmakerAction)}
      clientId={clientId}
      style={styles.container}>
      <Image
        style={styles.image}
        source={{
          uri,
        }}
      />
      <View style={styles.contentContainer}>
        <View>
          <AppmakerText
            fontColor="#000000"
            style={styles.title}
            numberOfLines={2}>
            {title}
          </AppmakerText>
          <View style={styles.priceContainer}>
            <AppmakerText>{salePrice || ''}</AppmakerText>
            <AppmakerText
              category="highlighter1"
              status="grey"
              style={styles.strikeText}>
              {regularPrice || ''}
            </AppmakerText>
          </View>
          <AppmakerText fontColor="#F54D4D" category="highlighter1">
            {salePercentage ? `${salePercentage} Off` : ''}
          </AppmakerText>
        </View>
        <Button
          small={true}
          loading={isLoading}
          wholeContainerStyle={styles.button}
          onPress={onPressAddToCart}
          outline={true}
          status="primary">
          Add to cart
        </Button>
      </View>
    </AppTouchable>
  );
}
const styles = StyleSheet.create({
  container: {
    borderWidth: 0.5,
    borderColor: '#E6E6E6',
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    borderRadius: 8,
    overflow: 'hidden',
    padding: 4,
    marginBottom: 4,
  },
  image: {
    width: 80,
    height: 80,
    marginRight: 12,
    borderRadius: 6,
    overflow: 'hidden',
  },
  contentContainer: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    // alignItems: 'center',
    flex: 1,
    padding: 4,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 1,
  },
  strikeText: {
    textDecorationLine: 'line-through',
    marginLeft: 6,
  },
  button: {
    alignSelf: 'flex-end',
  },
});
