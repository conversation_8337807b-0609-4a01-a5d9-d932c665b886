{"name": "@appmaker-xyz/remote-bundle", "version": "0.0.6-expo-v2-build-test-25-04-e5e5c0b.0", "description": "", "main": "src/index.js", "scripts": {"test": "jest", "build:watch": "rollup --config rollup.config.js --watch"}, "devDependencies": {"@babel/core": "^7.21.3", "@babel/preset-env": "^7.20.2", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-image": "^3.0.1", "@vitest/coverage-c8": "^0.29.7", "babel-jest": "^29.5.0", "jest": "^29.5.0", "msw": "^1.1.1", "rollup": "^2.79.0", "rollup-plugin-local-resolve": "^1.0.7", "rollup-plugin-uglify": "^6.0.4", "typescript": "^5.0.2", "vitest": "^0.29.7"}, "keywords": [], "author": "", "license": "UNLICENSED", "publishConfig": {"registry": "https://flash.appmaker.xyz"}, "dependencies": {"axios": "^1.3.4", "jest-mock-axios": "^4.7.1"}}