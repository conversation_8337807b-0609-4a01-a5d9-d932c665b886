{"name": "@appmaker-xyz/shopify", "version": "0.3.68-expo-v2-build-test-25-04-e5e5c0b.0", "description": "", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "echo \"Error: no build specified\"", "build-old": "rollup --config rollup.config.js", "watch": "rollup --config rollup.config.js --watch", "codegen": "graphql-codegen", "codegen:watch": "graphql-codegen --watch"}, "keywords": [], "author": "", "license": "UNLICENSED", "devDependencies": {"@graphql-codegen/cli": "5.0.0", "@graphql-codegen/client-preset": "4.1.0", "@graphql-codegen/introspection": "4.0.0", "@graphql-codegen/typescript": "^4.0.1", "@graphql-codegen/typescript-generic-sdk": "^4.0.0", "@graphql-codegen/typescript-graphql-request": "^6.0.0", "@graphql-codegen/typescript-operations": "^4.0.1", "@graphql-codegen/typescript-react-query": "^5.0.0", "@graphql-tools/load": "^8.0.2", "@graphql-tools/merge": "^9.0.2", "@graphql-tools/schema": "^10.0.2", "@parcel/watcher": "^2.3.0", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-typescript": "^11.1.2", "@shopify/hydrogen-react": "^2023.7.4", "glob": "^8.1.0", "graphql": "^16.8.1", "graphql-config": "^5.0.2", "rollup": "^2.79.0", "rollup-plugin-local-resolve": "^1.0.7", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-uglify": "^6.0.4"}, "peerDependencies": {"react": "16.13.1", "react-dom": "16.13.1", "react-native": "0.63.5"}, "repository": "https://github.com/Appmaker-xyz/starter-app", "publishConfig": {"registry": "https://flash.appmaker.xyz", "directory": "rollup-build-output"}}