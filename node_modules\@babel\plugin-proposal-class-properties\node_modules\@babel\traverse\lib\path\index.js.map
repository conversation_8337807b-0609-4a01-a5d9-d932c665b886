{"version": 3, "names": ["virtualTypes", "require", "_debug", "_index", "_index2", "_t", "t", "cache", "_generator", "NodePath_ancestry", "NodePath_inference", "NodePath_replacement", "NodePath_evaluation", "NodePath_conversion", "NodePath_introspection", "NodePath_context", "NodePath_removal", "NodePath_modification", "NodePath_family", "NodePath_comments", "NodePath_virtual_types_validator", "validate", "debug", "buildDebug", "REMOVED", "exports", "SHOULD_STOP", "SHOULD_SKIP", "NodePath", "constructor", "hub", "parent", "contexts", "state", "opts", "_traverseFlags", "<PERSON><PERSON><PERSON><PERSON>", "parentPath", "container", "<PERSON><PERSON><PERSON>", "key", "node", "type", "data", "context", "scope", "get", "Error", "targetNode", "paths", "getOrCreateCachedPaths", "path", "set", "setup", "getScope", "isScope", "<PERSON><PERSON>", "setData", "val", "Object", "create", "getData", "def", "undefined", "hasNode", "buildCodeFrameError", "msg", "SyntaxError", "buildError", "traverse", "visitor", "getPathLocation", "parts", "inList", "unshift", "join", "message", "enabled", "toString", "generator", "code", "parent<PERSON><PERSON>", "shouldSkip", "v", "shouldStop", "removed", "assign", "prototype", "_guessExecutionStatusRelativeToDifferentFunctions", "_guessExecutionStatusRelativeTo", "TYPES", "typeKey", "fn", "TypeError", "keys", "includes", "push", "_default", "default"], "sources": ["../../src/path/index.ts"], "sourcesContent": ["import type { HubInterface } from \"../hub.ts\";\nimport type TraversalContext from \"../context.ts\";\nimport type { ExplodedTraverseOptions } from \"../index.ts\";\nimport * as virtualTypes from \"./lib/virtual-types.ts\";\nimport buildDebug from \"debug\";\nimport traverse from \"../index.ts\";\nimport type { Visitor } from \"../types.ts\";\nimport Scope from \"../scope/index.ts\";\nimport { validate } from \"@babel/types\";\nimport * as t from \"@babel/types\";\nimport * as cache from \"../cache.ts\";\nimport generator from \"@babel/generator\";\n\n// NodePath is split across many files.\nimport * as NodePath_ancestry from \"./ancestry.ts\";\nimport * as NodePath_inference from \"./inference/index.ts\";\nimport * as NodePath_replacement from \"./replacement.ts\";\nimport * as NodePath_evaluation from \"./evaluation.ts\";\nimport * as NodePath_conversion from \"./conversion.ts\";\nimport * as NodePath_introspection from \"./introspection.ts\";\nimport * as NodePath_context from \"./context.ts\";\nimport * as NodePath_removal from \"./removal.ts\";\nimport * as NodePath_modification from \"./modification.ts\";\nimport * as NodePath_family from \"./family.ts\";\nimport * as NodePath_comments from \"./comments.ts\";\nimport * as NodePath_virtual_types_validator from \"./lib/virtual-types-validator.ts\";\nimport type { NodePathAssertions } from \"./generated/asserts.ts\";\nimport type { NodePathValidators } from \"./generated/validators.ts\";\n\nconst debug = buildDebug(\"babel\");\n\nexport const REMOVED = 1 << 0;\nexport const SHOULD_STOP = 1 << 1;\nexport const SHOULD_SKIP = 1 << 2;\n\nclass NodePath<T extends t.Node = t.Node> {\n  constructor(hub: HubInterface, parent: t.ParentMaps[T[\"type\"]]) {\n    this.parent = parent;\n    this.hub = hub;\n    this.data = null;\n\n    this.context = null;\n    this.scope = null;\n  }\n\n  declare parent: t.ParentMaps[T[\"type\"]];\n  declare hub: HubInterface;\n  declare data: Record<string | symbol, unknown>;\n  // TraversalContext is configured by setContext\n  declare context: TraversalContext;\n  declare scope: Scope;\n\n  contexts: Array<TraversalContext> = [];\n  state: any = null;\n  opts: ExplodedTraverseOptions | null = null;\n  // this.shouldSkip = false; this.shouldStop = false; this.removed = false;\n  _traverseFlags: number = 0;\n  skipKeys: Record<string, boolean> | null = null;\n  parentPath: t.ParentMaps[T[\"type\"]] extends null\n    ? null\n    : NodePath<t.ParentMaps[T[\"type\"]]> | null = null;\n  container: t.Node | Array<t.Node> | null = null;\n  listKey: string | null = null;\n  key: string | number | null = null;\n  node: T = null;\n  type: T[\"type\"] | null = null;\n\n  static get({\n    hub,\n    parentPath,\n    parent,\n    container,\n    listKey,\n    key,\n  }: {\n    hub?: HubInterface;\n    parentPath: NodePath | null;\n    parent: t.Node;\n    container: t.Node | t.Node[];\n    listKey?: string;\n    key: string | number;\n  }): NodePath {\n    if (!hub && parentPath) {\n      hub = parentPath.hub;\n    }\n\n    if (!parent) {\n      throw new Error(\"To get a node path the parent needs to exist\");\n    }\n\n    const targetNode =\n      // @ts-expect-error key must present in container\n      container[key];\n\n    const paths = cache.getOrCreateCachedPaths(hub, parent);\n\n    let path = paths.get(targetNode);\n    if (!path) {\n      path = new NodePath(hub, parent);\n      if (targetNode) paths.set(targetNode, path);\n    }\n\n    path.setup(parentPath, container, listKey, key);\n\n    return path;\n  }\n\n  getScope(scope: Scope): Scope {\n    return this.isScope() ? new Scope(this) : scope;\n  }\n\n  setData(key: string | symbol, val: any): any {\n    if (this.data == null) {\n      this.data = Object.create(null);\n    }\n    return (this.data[key] = val);\n  }\n\n  getData(key: string | symbol, def?: any): any {\n    if (this.data == null) {\n      this.data = Object.create(null);\n    }\n    let val = this.data[key];\n    if (val === undefined && def !== undefined) val = this.data[key] = def;\n    return val;\n  }\n\n  hasNode(): this is NodePath<NonNullable<this[\"node\"]>> {\n    return this.node != null;\n  }\n\n  buildCodeFrameError(\n    msg: string,\n    Error: new () => Error = SyntaxError,\n  ): Error {\n    return this.hub.buildError(this.node, msg, Error);\n  }\n\n  traverse<T>(visitor: Visitor<T>, state: T): void;\n  traverse(visitor: Visitor): void;\n  traverse(visitor: any, state?: any) {\n    traverse(this.node, visitor, this.scope, state, this);\n  }\n\n  set(key: string, node: any) {\n    validate(this.node, key, node);\n    // @ts-expect-error key must present in this.node\n    this.node[key] = node;\n  }\n\n  getPathLocation(): string {\n    const parts = [];\n    let path: NodePath = this;\n    do {\n      let key = path.key;\n      if (path.inList) key = `${path.listKey}[${key}]`;\n      parts.unshift(key);\n    } while ((path = path.parentPath));\n    return parts.join(\".\");\n  }\n\n  debug(message: string) {\n    if (!debug.enabled) return;\n    debug(`${this.getPathLocation()} ${this.type}: ${message}`);\n  }\n\n  toString() {\n    return generator(this.node).code;\n  }\n\n  get inList() {\n    return !!this.listKey;\n  }\n\n  set inList(inList) {\n    if (!inList) {\n      this.listKey = null;\n    }\n    // ignore inList = true as it should depend on `listKey`\n  }\n\n  get parentKey(): string {\n    return (this.listKey || this.key) as string;\n  }\n\n  get shouldSkip() {\n    return !!(this._traverseFlags & SHOULD_SKIP);\n  }\n\n  set shouldSkip(v) {\n    if (v) {\n      this._traverseFlags |= SHOULD_SKIP;\n    } else {\n      this._traverseFlags &= ~SHOULD_SKIP;\n    }\n  }\n\n  get shouldStop() {\n    return !!(this._traverseFlags & SHOULD_STOP);\n  }\n\n  set shouldStop(v) {\n    if (v) {\n      this._traverseFlags |= SHOULD_STOP;\n    } else {\n      this._traverseFlags &= ~SHOULD_STOP;\n    }\n  }\n\n  get removed() {\n    return !!(this._traverseFlags & REMOVED);\n  }\n  set removed(v) {\n    if (v) {\n      this._traverseFlags |= REMOVED;\n    } else {\n      this._traverseFlags &= ~REMOVED;\n    }\n  }\n}\n\nObject.assign(\n  NodePath.prototype,\n  NodePath_ancestry,\n  NodePath_inference,\n  NodePath_replacement,\n  NodePath_evaluation,\n  NodePath_conversion,\n  NodePath_introspection,\n  NodePath_context,\n  NodePath_removal,\n  NodePath_modification,\n  NodePath_family,\n  NodePath_comments,\n);\n\nif (!process.env.BABEL_8_BREAKING) {\n  // @ts-expect-error The original _guessExecutionStatusRelativeToDifferentFunctions only worked for paths in\n  // different functions, but _guessExecutionStatusRelativeTo works as a replacement in those cases.\n  NodePath.prototype._guessExecutionStatusRelativeToDifferentFunctions =\n    NodePath_introspection._guessExecutionStatusRelativeTo;\n}\n\n// we can not use `import { TYPES } from \"@babel/types\"` here\n// because the transformNamedBabelTypesImportToDestructuring plugin in babel.config.js\n// does not offer live bindings for `TYPES`\n// we can change to `import { TYPES }` when we are publishing ES modules only\nfor (const type of t.TYPES) {\n  const typeKey = `is${type}`;\n  // @ts-expect-error typeKey must present in t\n  const fn = t[typeKey];\n  // @ts-expect-error augmenting NodePath prototype\n  NodePath.prototype[typeKey] = function (opts: any) {\n    return fn(this.node, opts);\n  };\n\n  // @ts-expect-error augmenting NodePath prototype\n  NodePath.prototype[`assert${type}`] = function (opts: any) {\n    if (!fn(this.node, opts)) {\n      throw new TypeError(`Expected node path of type ${type}`);\n    }\n  };\n}\n\n// Register virtual types validators after base types validators\nObject.assign(NodePath.prototype, NodePath_virtual_types_validator);\n\nfor (const type of Object.keys(virtualTypes) as (keyof typeof virtualTypes)[]) {\n  if (type[0] === \"_\") continue;\n  if (!t.TYPES.includes(type)) t.TYPES.push(type);\n}\n\ntype NodePathMixins = typeof NodePath_ancestry &\n  typeof NodePath_inference &\n  typeof NodePath_replacement &\n  typeof NodePath_evaluation &\n  typeof NodePath_conversion &\n  typeof NodePath_introspection &\n  typeof NodePath_context &\n  typeof NodePath_removal &\n  typeof NodePath_modification &\n  typeof NodePath_family &\n  typeof NodePath_comments;\n\n// @ts-expect-error TS throws because ensureBlock returns the body node path\n// however, we don't use the return value and treat it as a transform and\n// assertion utilities. For better type inference we annotate it as an\n// assertion method\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\ninterface NodePath<T>\n  extends NodePathAssertions,\n    NodePathValidators,\n    NodePathMixins {\n  /**\n   * @see ./conversion.ts for implementation\n   */\n  ensureBlock<\n    T extends\n      | t.Loop\n      | t.WithStatement\n      | t.Function\n      | t.LabeledStatement\n      | t.CatchClause,\n  >(\n    this: NodePath<T>,\n  ): asserts this is NodePath<T & { body: t.BlockStatement }>;\n}\n\nexport default NodePath;\n"], "mappings": ";;;;;;AAGA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAEA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,EAAA,GAAAJ,OAAA;AAAwC,IAAAK,CAAA,GAAAD,EAAA;AAExC,IAAAE,KAAA,GAAAN,OAAA;AACA,IAAAO,UAAA,GAAAP,OAAA;AAGA,IAAAQ,iBAAA,GAAAR,OAAA;AACA,IAAAS,kBAAA,GAAAT,OAAA;AACA,IAAAU,oBAAA,GAAAV,OAAA;AACA,IAAAW,mBAAA,GAAAX,OAAA;AACA,IAAAY,mBAAA,GAAAZ,OAAA;AACA,IAAAa,sBAAA,GAAAb,OAAA;AACA,IAAAc,gBAAA,GAAAd,OAAA;AACA,IAAAe,gBAAA,GAAAf,OAAA;AACA,IAAAgB,qBAAA,GAAAhB,OAAA;AACA,IAAAiB,eAAA,GAAAjB,OAAA;AACA,IAAAkB,iBAAA,GAAAlB,OAAA;AACA,IAAAmB,gCAAA,GAAAnB,OAAA;AAAqF;EAjB5EoB;AAAQ,IAAAhB,EAAA;AAqBjB,MAAMiB,KAAK,GAAGC,MAAU,CAAC,OAAO,CAAC;AAE1B,MAAMC,OAAO,GAAG,CAAC,IAAI,CAAC;AAACC,OAAA,CAAAD,OAAA,GAAAA,OAAA;AACvB,MAAME,WAAW,GAAG,CAAC,IAAI,CAAC;AAACD,OAAA,CAAAC,WAAA,GAAAA,WAAA;AAC3B,MAAMC,WAAW,GAAG,CAAC,IAAI,CAAC;AAACF,OAAA,CAAAE,WAAA,GAAAA,WAAA;AAElC,MAAMC,QAAQ,CAA4B;EACxCC,WAAWA,CAACC,GAAiB,EAAEC,MAA+B,EAAE;IAAA,KAgBhEC,QAAQ,GAA4B,EAAE;IAAA,KACtCC,KAAK,GAAQ,IAAI;IAAA,KACjBC,IAAI,GAAmC,IAAI;IAAA,KAE3CC,cAAc,GAAW,CAAC;IAAA,KAC1BC,QAAQ,GAAmC,IAAI;IAAA,KAC/CC,UAAU,GAEqC,IAAI;IAAA,KACnDC,SAAS,GAAkC,IAAI;IAAA,KAC/CC,OAAO,GAAkB,IAAI;IAAA,KAC7BC,GAAG,GAA2B,IAAI;IAAA,KAClCC,IAAI,GAAM,IAAI;IAAA,KACdC,IAAI,GAAqB,IAAI;IA5B3B,IAAI,CAACX,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACa,IAAI,GAAG,IAAI;IAEhB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;EACnB;EAwBA,OAAOC,GAAGA,CAAC;IACThB,GAAG;IACHO,UAAU;IACVN,MAAM;IACNO,SAAS;IACTC,OAAO;IACPC;EAQF,CAAC,EAAY;IACX,IAAI,CAACV,GAAG,IAAIO,UAAU,EAAE;MACtBP,GAAG,GAAGO,UAAU,CAACP,GAAG;IACtB;IAEA,IAAI,CAACC,MAAM,EAAE;MACX,MAAM,IAAIgB,KAAK,CAAC,8CAA8C,CAAC;IACjE;IAEA,MAAMC,UAAU,GAEdV,SAAS,CAACE,GAAG,CAAC;IAEhB,MAAMS,KAAK,GAAG1C,KAAK,CAAC2C,sBAAsB,CAACpB,GAAG,EAAEC,MAAM,CAAC;IAEvD,IAAIoB,IAAI,GAAGF,KAAK,CAACH,GAAG,CAACE,UAAU,CAAC;IAChC,IAAI,CAACG,IAAI,EAAE;MACTA,IAAI,GAAG,IAAIvB,QAAQ,CAACE,GAAG,EAAEC,MAAM,CAAC;MAChC,IAAIiB,UAAU,EAAEC,KAAK,CAACG,GAAG,CAACJ,UAAU,EAAEG,IAAI,CAAC;IAC7C;IAEAA,IAAI,CAACE,KAAK,CAAChB,UAAU,EAAEC,SAAS,EAAEC,OAAO,EAAEC,GAAG,CAAC;IAE/C,OAAOW,IAAI;EACb;EAEAG,QAAQA,CAACT,KAAY,EAAS;IAC5B,OAAO,IAAI,CAACU,OAAO,CAAC,CAAC,GAAG,IAAIC,eAAK,CAAC,IAAI,CAAC,GAAGX,KAAK;EACjD;EAEAY,OAAOA,CAACjB,GAAoB,EAAEkB,GAAQ,EAAO;IAC3C,IAAI,IAAI,CAACf,IAAI,IAAI,IAAI,EAAE;MACrB,IAAI,CAACA,IAAI,GAAGgB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACjC;IACA,OAAQ,IAAI,CAACjB,IAAI,CAACH,GAAG,CAAC,GAAGkB,GAAG;EAC9B;EAEAG,OAAOA,CAACrB,GAAoB,EAAEsB,GAAS,EAAO;IAC5C,IAAI,IAAI,CAACnB,IAAI,IAAI,IAAI,EAAE;MACrB,IAAI,CAACA,IAAI,GAAGgB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACjC;IACA,IAAIF,GAAG,GAAG,IAAI,CAACf,IAAI,CAACH,GAAG,CAAC;IACxB,IAAIkB,GAAG,KAAKK,SAAS,IAAID,GAAG,KAAKC,SAAS,EAAEL,GAAG,GAAG,IAAI,CAACf,IAAI,CAACH,GAAG,CAAC,GAAGsB,GAAG;IACtE,OAAOJ,GAAG;EACZ;EAEAM,OAAOA,CAAA,EAAgD;IACrD,OAAO,IAAI,CAACvB,IAAI,IAAI,IAAI;EAC1B;EAEAwB,mBAAmBA,CACjBC,GAAW,EACXnB,KAAsB,GAAGoB,WAAW,EAC7B;IACP,OAAO,IAAI,CAACrC,GAAG,CAACsC,UAAU,CAAC,IAAI,CAAC3B,IAAI,EAAEyB,GAAG,EAAEnB,KAAK,CAAC;EACnD;EAIAsB,QAAQA,CAACC,OAAY,EAAErC,KAAW,EAAE;IAClC,IAAAoC,cAAQ,EAAC,IAAI,CAAC5B,IAAI,EAAE6B,OAAO,EAAE,IAAI,CAACzB,KAAK,EAAEZ,KAAK,EAAE,IAAI,CAAC;EACvD;EAEAmB,GAAGA,CAACZ,GAAW,EAAEC,IAAS,EAAE;IAC1BpB,QAAQ,CAAC,IAAI,CAACoB,IAAI,EAAED,GAAG,EAAEC,IAAI,CAAC;IAE9B,IAAI,CAACA,IAAI,CAACD,GAAG,CAAC,GAAGC,IAAI;EACvB;EAEA8B,eAAeA,CAAA,EAAW;IACxB,MAAMC,KAAK,GAAG,EAAE;IAChB,IAAIrB,IAAc,GAAG,IAAI;IACzB,GAAG;MACD,IAAIX,GAAG,GAAGW,IAAI,CAACX,GAAG;MAClB,IAAIW,IAAI,CAACsB,MAAM,EAAEjC,GAAG,GAAI,GAAEW,IAAI,CAACZ,OAAQ,IAAGC,GAAI,GAAE;MAChDgC,KAAK,CAACE,OAAO,CAAClC,GAAG,CAAC;IACpB,CAAC,QAASW,IAAI,GAAGA,IAAI,CAACd,UAAU;IAChC,OAAOmC,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC;EACxB;EAEArD,KAAKA,CAACsD,OAAe,EAAE;IACrB,IAAI,CAACtD,KAAK,CAACuD,OAAO,EAAE;IACpBvD,KAAK,CAAE,GAAE,IAAI,CAACiD,eAAe,CAAC,CAAE,IAAG,IAAI,CAAC7B,IAAK,KAAIkC,OAAQ,EAAC,CAAC;EAC7D;EAEAE,QAAQA,CAAA,EAAG;IACT,OAAO,IAAAC,kBAAS,EAAC,IAAI,CAACtC,IAAI,CAAC,CAACuC,IAAI;EAClC;EAEA,IAAIP,MAAMA,CAAA,EAAG;IACX,OAAO,CAAC,CAAC,IAAI,CAAClC,OAAO;EACvB;EAEA,IAAIkC,MAAMA,CAACA,MAAM,EAAE;IACjB,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAClC,OAAO,GAAG,IAAI;IACrB;EAEF;EAEA,IAAI0C,SAASA,CAAA,EAAW;IACtB,OAAQ,IAAI,CAAC1C,OAAO,IAAI,IAAI,CAACC,GAAG;EAClC;EAEA,IAAI0C,UAAUA,CAAA,EAAG;IACf,OAAO,CAAC,EAAE,IAAI,CAAC/C,cAAc,GAAGR,WAAW,CAAC;EAC9C;EAEA,IAAIuD,UAAUA,CAACC,CAAC,EAAE;IAChB,IAAIA,CAAC,EAAE;MACL,IAAI,CAAChD,cAAc,IAAIR,WAAW;IACpC,CAAC,MAAM;MACL,IAAI,CAACQ,cAAc,IAAI,CAACR,WAAW;IACrC;EACF;EAEA,IAAIyD,UAAUA,CAAA,EAAG;IACf,OAAO,CAAC,EAAE,IAAI,CAACjD,cAAc,GAAGT,WAAW,CAAC;EAC9C;EAEA,IAAI0D,UAAUA,CAACD,CAAC,EAAE;IAChB,IAAIA,CAAC,EAAE;MACL,IAAI,CAAChD,cAAc,IAAIT,WAAW;IACpC,CAAC,MAAM;MACL,IAAI,CAACS,cAAc,IAAI,CAACT,WAAW;IACrC;EACF;EAEA,IAAI2D,OAAOA,CAAA,EAAG;IACZ,OAAO,CAAC,EAAE,IAAI,CAAClD,cAAc,GAAGX,OAAO,CAAC;EAC1C;EACA,IAAI6D,OAAOA,CAACF,CAAC,EAAE;IACb,IAAIA,CAAC,EAAE;MACL,IAAI,CAAChD,cAAc,IAAIX,OAAO;IAChC,CAAC,MAAM;MACL,IAAI,CAACW,cAAc,IAAI,CAACX,OAAO;IACjC;EACF;AACF;AAEAmC,MAAM,CAAC2B,MAAM,CACX1D,QAAQ,CAAC2D,SAAS,EAClB9E,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,mBAAmB,EACnBC,mBAAmB,EACnBC,sBAAsB,EACtBC,gBAAgB,EAChBC,gBAAgB,EAChBC,qBAAqB,EACrBC,eAAe,EACfC,iBACF,CAAC;AAEkC;EAGjCS,QAAQ,CAAC2D,SAAS,CAACC,iDAAiD,GAClE1E,sBAAsB,CAAC2E,+BAA+B;AAC1D;AAMA,KAAK,MAAM/C,IAAI,IAAIpC,CAAC,CAACoF,KAAK,EAAE;EAC1B,MAAMC,OAAO,GAAI,KAAIjD,IAAK,EAAC;EAE3B,MAAMkD,EAAE,GAAGtF,CAAC,CAACqF,OAAO,CAAC;EAErB/D,QAAQ,CAAC2D,SAAS,CAACI,OAAO,CAAC,GAAG,UAAUzD,IAAS,EAAE;IACjD,OAAO0D,EAAE,CAAC,IAAI,CAACnD,IAAI,EAAEP,IAAI,CAAC;EAC5B,CAAC;EAGDN,QAAQ,CAAC2D,SAAS,CAAE,SAAQ7C,IAAK,EAAC,CAAC,GAAG,UAAUR,IAAS,EAAE;IACzD,IAAI,CAAC0D,EAAE,CAAC,IAAI,CAACnD,IAAI,EAAEP,IAAI,CAAC,EAAE;MACxB,MAAM,IAAI2D,SAAS,CAAE,8BAA6BnD,IAAK,EAAC,CAAC;IAC3D;EACF,CAAC;AACH;AAGAiB,MAAM,CAAC2B,MAAM,CAAC1D,QAAQ,CAAC2D,SAAS,EAAEnE,gCAAgC,CAAC;AAEnE,KAAK,MAAMsB,IAAI,IAAIiB,MAAM,CAACmC,IAAI,CAAC9F,YAAY,CAAC,EAAmC;EAC7E,IAAI0C,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;EACrB,IAAI,CAACpC,CAAC,CAACoF,KAAK,CAACK,QAAQ,CAACrD,IAAI,CAAC,EAAEpC,CAAC,CAACoF,KAAK,CAACM,IAAI,CAACtD,IAAI,CAAC;AACjD;AAAC,IAAAuD,QAAA,GAsCcrE,QAAQ;AAAAH,OAAA,CAAAyE,OAAA,GAAAD,QAAA"}