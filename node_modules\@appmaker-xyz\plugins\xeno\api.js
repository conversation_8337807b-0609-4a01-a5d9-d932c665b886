import { usePluginStore } from '@appmaker-xyz/core';
import { appStorageApi } from '@appmaker-xyz/core';
import base64 from 'react-native-base64';
import { runDataSource } from '@appmaker-xyz/core';
function decodeId(id) {
  try {
    const encodedId = base64.decode(id).match(/\\?([0-9]{5,50})[?]?/g)[0];
    return encodedId;
  } catch (error) {
    return id;
  }
}
export async function ChumbakInsiderCustomerFetch({ settings }) {
  try {
    const { user } = appStorageApi().getState();
    if (user) {
      const userID = decodeId(user?.id);
      var axios = require('axios');
      var data = `shopify_customerid=${userID}&shop_url=${settings?.shopifyStoreUrl}`;

      var config = {
        method: 'post',
        url: 'https://shopify.xeno.in/loyalty_points/ajax/loyality_fetch_customer_ledger.php',
        headers: {
          Accept: 'text/html, */*; q=0.01',
          'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8,ml;q=0.7',
          Connection: 'keep-alive',
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
          Origin: settings?.StoreDomainOriginUrl,
          Referer: `${settings?.StoreDomainOriginUrl}/`,
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'cross-site',
          'User-Agent':
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Safari/537.36',
          'sec-ch-ua':
            '"Google Chrome";v="105", "Not)A;Brand";v="8", "Chromium";v="105"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"macOS"',
        },
        data: data,
      };

      axios(config)
        .then(function (response) {
          // console.log(JSON.stringify(response.data));
        })
        .catch(function (error) {
          console.log(error);
        });

      const [infoResponse] = await runDataSource(
        {
          dataSource: {
            source: 'shopify',
            attributes: {},
          },
          appId: '',
        },
        {
          methodName: 'getCustomerInfo',
          params: {
            accessToken: user?.accessToken,
          },
        },
      );
      appStorageApi().setState({
        user: {
          accessToken: user?.accessToken,
          ...infoResponse.data.data.customer,
        },
      });
    }
  } catch (error) {
    console.log(error);
  }
}
