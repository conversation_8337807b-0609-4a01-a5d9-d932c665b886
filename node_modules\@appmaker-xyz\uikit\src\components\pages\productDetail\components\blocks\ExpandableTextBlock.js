import React, { useState, useCallback } from 'react';
import { StyleSheet, LayoutAnimation } from 'react-native';

import { useApThemeState } from '../../../../../theme/ThemeContext';

import { Layout, AppmakerText, BlockCard } from '../../../../../components';

const ExpandableTextBlock = ({ clientId, attributes, ...props }) => {
  const [lengthMore, setLengthMore] = useState(false); //to show the "Show more & show Line"
  const [textShown, setTextShown] = useState(false); //To show remaining Text

  const { spacing } = useApThemeState();

  const styles = allStyles({ spacing });
  const {
    blockTitle,
    content,
    accessButton,
    expandable,
    expanded,
    hiddenText,
    expandedText,
    accessButtonColor,
  } = attributes;

  const onTextLayout = useCallback((e) => {
    setLengthMore(e.nativeEvent.lines.length >= 3); //to check the text is more than 3 lines or not
  }, []);
  const toggleNumberOfLines = () => {
    setTextShown(!textShown); //To toggle the show text or hide it
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  };
  return (
    <BlockCard
      clientId={clientId}
      attributes={{
        title: blockTitle,
        accessButton: accessButton,
        expandable: expandable == true || expandable == '1' ? true : false,
        hiddenText: hiddenText,
        expandedText: expandedText,
        expanded: expanded == true || expanded == '1' ? true : false,
        accessButtonColor: accessButtonColor,
      }}>
      <Layout style={styles.subPadding}>
        <AppmakerText
          html={true}
          category="bodyParagraph"
          onTextLayout={onTextLayout}
          numberOfLines={textShown ? undefined : 3}>
          {content}
        </AppmakerText>
        {lengthMore ? (
          <AppmakerText
            html={true}
            category="bodyParagraph"
            status="grey"
            onPress={toggleNumberOfLines}
            style={{ marginTop: 10 }}>
            {textShown ? 'Show less' : 'Show more'}
          </AppmakerText>
        ) : null}
      </Layout>
    </BlockCard>
  );
};
const allStyles = ({ spacing }) =>
  StyleSheet.create({
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: spacing.base,
    },
    subPadding: {
      paddingHorizontal: spacing.base,
      paddingBottom: spacing.base,
    },
  });
export default ExpandableTextBlock;
