import { appmaker } from '@appmaker-xyz/core';
import { ACTION_TEST } from './actions';
import SamplePage from './pages/index';
import { activateAnalyticsEvents, setContentType } from './event-v2';

const Plugin = {
  id: 'facebook analytics',
  name: 'Facebook analytics',
  activate,
};

export function activate({ settings }) {
  activateAnalyticsEvents();
  appmaker.pages.registerPage('fbPlayground', SamplePage);
  // appmaker.addFilter('home-page-id', 'fb0test', () => 'fbPlayground');
  if (settings.facebook_content_type) {
    setContentType(settings.facebook_content_type);
  }
  appmaker.actions.registerAction('ACTION_TEST', ACTION_TEST);
}
export default Plugin;
