import React from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import {
  useFilterModal,
  useIsAnyFilterSelected,
} from '../../../hooks/filter/userCollectionFilter';
import { ThemeText } from '@appmaker-xyz/ui';
import FilterIcon from '../assets/FilterIcon';
import { testProps, getExtensionConfig } from '@appmaker-xyz/core';

export function FilterButton({ attributes: { customStyles = {} } = {} }) {
  const {
    activeColor,
    bgColor,
    textColor,
    outline,
    noIcon,
    small,
    wholeContainerStyle,
  } = customStyles;

  const { openFilterModal } = useFilterModal();
  const isFilterSelected = useIsAnyFilterSelected();

  const extensionStyles = getExtensionConfig?.(
    'shopify',
    'filterButtonGroup',
    {},
  );

  const containerStyles = [
    styles.filterButton,
    extensionStyles?.filterButtonContainer,
  ];
  const activeContainerStyles = [styles.activeContainer];

  if (bgColor) {
    containerStyles.push({ backgroundColor: bgColor });
  }
  if (outline) {
    containerStyles.push({ borderWidth: 1, borderColor: outline });
  }
  if (noIcon) {
    activeContainerStyles.push({ marginLeft: 0 });
  }
  if (small) {
    containerStyles.push({ paddingVertical: 8 });
  }
  if (wholeContainerStyle) {
    containerStyles.push(wholeContainerStyle);
  }

  return (
    <Pressable
      {...testProps(`filter-button`)}
      onPress={openFilterModal}
      style={containerStyles}>
      {noIcon ? null : (
        <FilterIcon
          color={extensionStyles?.filterButtonIconColor || textColor}
          style={extensionStyles?.filterIcon}
        />
      )}
      <View style={activeContainerStyles}>
        {isFilterSelected ? (
          <View
            style={[
              styles.activeDot,
              {
                backgroundColor:
                  extensionStyles?.activeDotColor || activeColor || '#ff0000',
              },
              extensionStyles?.filterActiveDot,
            ]}
          />
        ) : null}
        <ThemeText
          style={[styles.filterButtonText, extensionStyles?.filterText]}
          color={textColor ? textColor : '#fff'}>
          Filter
        </ThemeText>
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  filterButton: {
    backgroundColor: '#000',
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  filterButtonText: {
    textAlign: 'center',
    fontSize: 16,
    lineHeight: 20,
  },
  activeContainer: {
    position: 'relative',
    marginLeft: 6,
  },
  activeDot: {
    position: 'absolute',
    zIndex: 1,
    top: 2,
    right: -16,
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
});

export default FilterButton;
