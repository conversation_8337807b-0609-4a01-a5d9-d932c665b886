declare module '@appmaker-xyz/shopify' {
  type FilterID = string;
  type FilterValueID = string;
  export interface FilterValue {
    count?: number;
    id: FilterValueID;
    input: string;
    label: string;
  }

  export type FilterType = 'LIST' | 'PRICE_RANGE';

  export interface Filter {
    id: FilterID;
    label: string;
    type: FilterType;
    values: FilterValue[];
  }

  export type Filters = Filter[];
  export type QuickFilterSetProps = {
    filterKey: string;
    filterItem: FilterValue;
  };
  export type QuickFilterRemoveProps = {
    filterKey: string;
    filterItem: FilterValue;
  };
  type SelectedFilters = {
    [filterKey: FilterID]: {
      [filterValueKey: FilterValueID]: FilterValue;
    };
  };
  export interface UseQuickFiltersReturn {
    filters: Filters | null;
    isFilterLoading: boolean;
    applyQuickFilter: (filter: QuickFilterSetProps) => void;
    clearAllFilters: () => void;
    removeQuickFilter: (filter: QuickFilterRemoveProps) => void;
    getSingleFilter: (filter: { filterKey: string }) => Filter | null;
    isFilterAvailable: boolean;
    selectedFilters: SelectedFilters | null;
  }
  export function useQuickFilters(): UseQuickFiltersReturn;
}
