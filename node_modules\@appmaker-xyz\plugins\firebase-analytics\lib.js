import { appmaker, appStorageApi, appPluginStoreApi } from '@appmaker-xyz/core';
import firebaseAnalytics from '@react-native-firebase/analytics';
import url from 'url';

export const logEvent = (key, value, eventObject) => {
  const store = appStorageApi().getState();
  let data = { ...value };
  if (store.user) {
    const user = store.user;
    data = { ...value, user_id: user.id };
  }
  const firebaseEventData = appmaker.applyFilters('firebaseEventData', {
    eventName: key,
    params: data,
    eventObject,
  });
  firebaseAnalytics().logEvent(
    firebaseEventData.eventName,
    firebaseEventData.params,
  );
};
export const logScreenView = (pageId, name) => {
  firebaseAnalytics().logScreenView({
    screen_name: name,
    screen_class: pageId,
  });
};

export const handleUTMParams = async (params) => {
  try {
    const { utm_source, utm_medium, utm_campaign } = params;
    if (utm_source && utm_medium && utm_campaign) {
      firebaseAnalytics().logCampaignDetails({
        ...(utm_source && { source: utm_source }),
        ...(utm_medium && { medium: utm_medium }),
        ...(utm_campaign && { campaign: utm_campaign }),
      });
    }
  } catch (error) {
    console.log('Error handling UTM parameters:', error);
  }
};

export const itemIdHelper = (id, vid) => {
  if (!id) {
    return null;
  }
  const pluginSettings =
    appPluginStoreApi().getState()?.plugins['firebase-analytics']?.settings;
  if (pluginSettings?.change_product_id_format_to_ga4_shopify && id) {
    const country = 'IN';
    const finalId = vid
      ? `shopify_${country}_${id}_${vid}`
      : `shopify_${country}_${id}`;
    return finalId;
  }
  return id;
};
