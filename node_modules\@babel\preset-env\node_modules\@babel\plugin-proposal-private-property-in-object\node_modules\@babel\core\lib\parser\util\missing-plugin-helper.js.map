{"version": 3, "names": ["pluginNameMap", "asyncDoExpressions", "syntax", "name", "url", "decimal", "decorators", "transform", "doExpressions", "exportDefaultFrom", "flow", "functionBind", "functionSent", "jsx", "importAssertions", "pipelineOperator", "recordAndTuple", "regexpUnicodeSets", "throwExpressions", "typescript", "asyncGenerators", "classProperties", "classPrivateProperties", "classPrivateMethods", "classStaticBlock", "dynamicImport", "exportNamespaceFrom", "importMeta", "logicalAssignment", "moduleStringNames", "numericSeparator", "nullishCoalescingOperator", "objectRestSpread", "optionalCatchBinding", "optionalChaining", "privateIn", "getNameURLCombination", "generateMissingPluginMessage", "missing<PERSON><PERSON><PERSON><PERSON><PERSON>", "loc", "codeFrame", "helpMessage", "line", "column", "pluginInfo", "syntaxPlugin", "transformPlugin", "syntaxPluginInfo", "transformPluginInfo", "sectionType", "startsWith"], "sources": ["../../../src/parser/util/missing-plugin-helper.ts"], "sourcesContent": ["const pluginNameMap: Record<\n  string,\n  Partial<Record<\"syntax\" | \"transform\", Record<\"name\" | \"url\", string>>>\n> = {\n  asyncDoExpressions: {\n    syntax: {\n      name: \"@babel/plugin-syntax-async-do-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-async-do-expressions\",\n    },\n  },\n  decimal: {\n    syntax: {\n      name: \"@babel/plugin-syntax-decimal\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-decimal\",\n    },\n  },\n  decorators: {\n    syntax: {\n      name: \"@babel/plugin-syntax-decorators\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-decorators\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-decorators\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-decorators\",\n    },\n  },\n  doExpressions: {\n    syntax: {\n      name: \"@babel/plugin-syntax-do-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-do-expressions\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-do-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-do-expressions\",\n    },\n  },\n  exportDefaultFrom: {\n    syntax: {\n      name: \"@babel/plugin-syntax-export-default-from\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-export-default-from\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-export-default-from\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-export-default-from\",\n    },\n  },\n  flow: {\n    syntax: {\n      name: \"@babel/plugin-syntax-flow\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-flow\",\n    },\n    transform: {\n      name: \"@babel/preset-flow\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-preset-flow\",\n    },\n  },\n  functionBind: {\n    syntax: {\n      name: \"@babel/plugin-syntax-function-bind\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-function-bind\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-function-bind\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-function-bind\",\n    },\n  },\n  functionSent: {\n    syntax: {\n      name: \"@babel/plugin-syntax-function-sent\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-function-sent\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-function-sent\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-function-sent\",\n    },\n  },\n  jsx: {\n    syntax: {\n      name: \"@babel/plugin-syntax-jsx\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-jsx\",\n    },\n    transform: {\n      name: \"@babel/preset-react\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-preset-react\",\n    },\n  },\n  importAssertions: {\n    syntax: {\n      name: \"@babel/plugin-syntax-import-assertions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-import-assertions\",\n    },\n  },\n  pipelineOperator: {\n    syntax: {\n      name: \"@babel/plugin-syntax-pipeline-operator\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-pipeline-operator\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-pipeline-operator\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-pipeline-operator\",\n    },\n  },\n  recordAndTuple: {\n    syntax: {\n      name: \"@babel/plugin-syntax-record-and-tuple\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-record-and-tuple\",\n    },\n  },\n  regexpUnicodeSets: {\n    syntax: {\n      name: \"@babel/plugin-syntax-unicode-sets-regex\",\n      url: \"https://github.com/babel/babel/blob/main/packages/babel-plugin-syntax-unicode-sets-regex/README.md\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-unicode-sets-regex\",\n      url: \"https://github.com/babel/babel/blob/main/packages/babel-plugin-proposalunicode-sets-regex/README.md\",\n    },\n  },\n  throwExpressions: {\n    syntax: {\n      name: \"@babel/plugin-syntax-throw-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-throw-expressions\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-throw-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-throw-expressions\",\n    },\n  },\n  typescript: {\n    syntax: {\n      name: \"@babel/plugin-syntax-typescript\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-typescript\",\n    },\n    transform: {\n      name: \"@babel/preset-typescript\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-preset-typescript\",\n    },\n  },\n\n  // TODO: This plugins are now supported by default by @babel/parser: they can\n  // be removed from this list. Although removing them isn't a breaking change,\n  // it's better to keep a nice error message for users using older versions of\n  // the parser. They can be removed in Babel 8.\n  asyncGenerators: {\n    syntax: {\n      name: \"@babel/plugin-syntax-async-generators\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-async-generators\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-async-generator-functions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-async-generator-functions\",\n    },\n  },\n  classProperties: {\n    syntax: {\n      name: \"@babel/plugin-syntax-class-properties\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-class-properties\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-class-properties\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-class-properties\",\n    },\n  },\n  classPrivateProperties: {\n    syntax: {\n      name: \"@babel/plugin-syntax-class-properties\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-class-properties\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-class-properties\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-class-properties\",\n    },\n  },\n  classPrivateMethods: {\n    syntax: {\n      name: \"@babel/plugin-syntax-class-properties\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-class-properties\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-private-methods\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-private-methods\",\n    },\n  },\n  classStaticBlock: {\n    syntax: {\n      name: \"@babel/plugin-syntax-class-static-block\",\n      url: \"https://github.com/babel/babel/tree/HEAD/packages/babel-plugin-syntax-class-static-block\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-class-static-block\",\n      url: \"https://github.com/babel/babel/tree/HEAD/packages/babel-plugin-proposal-class-static-block\",\n    },\n  },\n  dynamicImport: {\n    syntax: {\n      name: \"@babel/plugin-syntax-dynamic-import\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-dynamic-import\",\n    },\n  },\n  exportNamespaceFrom: {\n    syntax: {\n      name: \"@babel/plugin-syntax-export-namespace-from\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-export-namespace-from\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-export-namespace-from\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-export-namespace-from\",\n    },\n  },\n  importMeta: {\n    syntax: {\n      name: \"@babel/plugin-syntax-import-meta\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-import-meta\",\n    },\n  },\n  logicalAssignment: {\n    syntax: {\n      name: \"@babel/plugin-syntax-logical-assignment-operators\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-logical-assignment-operators\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-logical-assignment-operators\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-logical-assignment-operators\",\n    },\n  },\n  moduleStringNames: {\n    syntax: {\n      name: \"@babel/plugin-syntax-module-string-names\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-module-string-names\",\n    },\n  },\n  numericSeparator: {\n    syntax: {\n      name: \"@babel/plugin-syntax-numeric-separator\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-numeric-separator\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-numeric-separator\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-numeric-separator\",\n    },\n  },\n  nullishCoalescingOperator: {\n    syntax: {\n      name: \"@babel/plugin-syntax-nullish-coalescing-operator\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-nullish-coalescing-operator\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-nullish-coalescing-operator\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-nullish-coalescing-opearator\",\n    },\n  },\n  objectRestSpread: {\n    syntax: {\n      name: \"@babel/plugin-syntax-object-rest-spread\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-object-rest-spread\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-object-rest-spread\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-object-rest-spread\",\n    },\n  },\n  optionalCatchBinding: {\n    syntax: {\n      name: \"@babel/plugin-syntax-optional-catch-binding\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-optional-catch-binding\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-optional-catch-binding\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-optional-catch-binding\",\n    },\n  },\n  optionalChaining: {\n    syntax: {\n      name: \"@babel/plugin-syntax-optional-chaining\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-optional-chaining\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-optional-chaining\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-optional-chaining\",\n    },\n  },\n  privateIn: {\n    syntax: {\n      name: \"@babel/plugin-syntax-private-property-in-object\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-private-property-in-object\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-private-property-in-object\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-private-property-in-object\",\n    },\n  },\n};\n\n//todo: we don't have plugin-syntax-private-property-in-object\npluginNameMap.privateIn.syntax = pluginNameMap.privateIn.transform;\n\nconst getNameURLCombination = ({ name, url }: { name: string; url: string }) =>\n  `${name} (${url})`;\n\n/*\nReturns a string of the format:\nSupport for the experimental syntax [@babel/parser plugin name] isn't currently enabled ([loc]):\n\n[code frame]\n\nAdd [npm package name] ([url]) to the 'plugins' section of your Babel config\nto enable [parsing|transformation].\n*/\nexport default function generateMissingPluginMessage(\n  missingPluginName: string,\n  loc: {\n    line: number;\n    column: number;\n  },\n  codeFrame: string,\n): string {\n  let helpMessage =\n    `Support for the experimental syntax '${missingPluginName}' isn't currently enabled ` +\n    `(${loc.line}:${loc.column + 1}):\\n\\n` +\n    codeFrame;\n  const pluginInfo = pluginNameMap[missingPluginName];\n  if (pluginInfo) {\n    const { syntax: syntaxPlugin, transform: transformPlugin } = pluginInfo;\n    if (syntaxPlugin) {\n      const syntaxPluginInfo = getNameURLCombination(syntaxPlugin);\n      if (transformPlugin) {\n        const transformPluginInfo = getNameURLCombination(transformPlugin);\n        const sectionType = transformPlugin.name.startsWith(\"@babel/plugin\")\n          ? \"plugins\"\n          : \"presets\";\n        helpMessage += `\\n\\nAdd ${transformPluginInfo} to the '${sectionType}' section of your Babel config to enable transformation.\nIf you want to leave it as-is, add ${syntaxPluginInfo} to the 'plugins' section to enable parsing.`;\n      } else {\n        helpMessage +=\n          `\\n\\nAdd ${syntaxPluginInfo} to the 'plugins' section of your Babel config ` +\n          `to enable parsing.`;\n      }\n    }\n  }\n  return helpMessage;\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,aAGL,GAAG;EACFC,kBAAkB,EAAE;IAClBC,MAAM,EAAE;MACNC,IAAI,EAAE,2CAA2C;MACjDC,GAAG,EAAE;IACP;EACF,CAAC;EACDC,OAAO,EAAE;IACPH,MAAM,EAAE;MACNC,IAAI,EAAE,8BAA8B;MACpCC,GAAG,EAAE;IACP;EACF,CAAC;EACDE,UAAU,EAAE;IACVJ,MAAM,EAAE;MACNC,IAAI,EAAE,iCAAiC;MACvCC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,mCAAmC;MACzCC,GAAG,EAAE;IACP;EACF,CAAC;EACDI,aAAa,EAAE;IACbN,MAAM,EAAE;MACNC,IAAI,EAAE,qCAAqC;MAC3CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,uCAAuC;MAC7CC,GAAG,EAAE;IACP;EACF,CAAC;EACDK,iBAAiB,EAAE;IACjBP,MAAM,EAAE;MACNC,IAAI,EAAE,0CAA0C;MAChDC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,4CAA4C;MAClDC,GAAG,EAAE;IACP;EACF,CAAC;EACDM,IAAI,EAAE;IACJR,MAAM,EAAE;MACNC,IAAI,EAAE,2BAA2B;MACjCC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,oBAAoB;MAC1BC,GAAG,EAAE;IACP;EACF,CAAC;EACDO,YAAY,EAAE;IACZT,MAAM,EAAE;MACNC,IAAI,EAAE,oCAAoC;MAC1CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,sCAAsC;MAC5CC,GAAG,EAAE;IACP;EACF,CAAC;EACDQ,YAAY,EAAE;IACZV,MAAM,EAAE;MACNC,IAAI,EAAE,oCAAoC;MAC1CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,sCAAsC;MAC5CC,GAAG,EAAE;IACP;EACF,CAAC;EACDS,GAAG,EAAE;IACHX,MAAM,EAAE;MACNC,IAAI,EAAE,0BAA0B;MAChCC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,qBAAqB;MAC3BC,GAAG,EAAE;IACP;EACF,CAAC;EACDU,gBAAgB,EAAE;IAChBZ,MAAM,EAAE;MACNC,IAAI,EAAE,wCAAwC;MAC9CC,GAAG,EAAE;IACP;EACF,CAAC;EACDW,gBAAgB,EAAE;IAChBb,MAAM,EAAE;MACNC,IAAI,EAAE,wCAAwC;MAC9CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,0CAA0C;MAChDC,GAAG,EAAE;IACP;EACF,CAAC;EACDY,cAAc,EAAE;IACdd,MAAM,EAAE;MACNC,IAAI,EAAE,uCAAuC;MAC7CC,GAAG,EAAE;IACP;EACF,CAAC;EACDa,iBAAiB,EAAE;IACjBf,MAAM,EAAE;MACNC,IAAI,EAAE,yCAAyC;MAC/CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,2CAA2C;MACjDC,GAAG,EAAE;IACP;EACF,CAAC;EACDc,gBAAgB,EAAE;IAChBhB,MAAM,EAAE;MACNC,IAAI,EAAE,wCAAwC;MAC9CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,0CAA0C;MAChDC,GAAG,EAAE;IACP;EACF,CAAC;EACDe,UAAU,EAAE;IACVjB,MAAM,EAAE;MACNC,IAAI,EAAE,iCAAiC;MACvCC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,0BAA0B;MAChCC,GAAG,EAAE;IACP;EACF,CAAC;EAMDgB,eAAe,EAAE;IACflB,MAAM,EAAE;MACNC,IAAI,EAAE,uCAAuC;MAC7CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,kDAAkD;MACxDC,GAAG,EAAE;IACP;EACF,CAAC;EACDiB,eAAe,EAAE;IACfnB,MAAM,EAAE;MACNC,IAAI,EAAE,uCAAuC;MAC7CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,yCAAyC;MAC/CC,GAAG,EAAE;IACP;EACF,CAAC;EACDkB,sBAAsB,EAAE;IACtBpB,MAAM,EAAE;MACNC,IAAI,EAAE,uCAAuC;MAC7CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,yCAAyC;MAC/CC,GAAG,EAAE;IACP;EACF,CAAC;EACDmB,mBAAmB,EAAE;IACnBrB,MAAM,EAAE;MACNC,IAAI,EAAE,uCAAuC;MAC7CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,wCAAwC;MAC9CC,GAAG,EAAE;IACP;EACF,CAAC;EACDoB,gBAAgB,EAAE;IAChBtB,MAAM,EAAE;MACNC,IAAI,EAAE,yCAAyC;MAC/CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,2CAA2C;MACjDC,GAAG,EAAE;IACP;EACF,CAAC;EACDqB,aAAa,EAAE;IACbvB,MAAM,EAAE;MACNC,IAAI,EAAE,qCAAqC;MAC3CC,GAAG,EAAE;IACP;EACF,CAAC;EACDsB,mBAAmB,EAAE;IACnBxB,MAAM,EAAE;MACNC,IAAI,EAAE,4CAA4C;MAClDC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,8CAA8C;MACpDC,GAAG,EAAE;IACP;EACF,CAAC;EACDuB,UAAU,EAAE;IACVzB,MAAM,EAAE;MACNC,IAAI,EAAE,kCAAkC;MACxCC,GAAG,EAAE;IACP;EACF,CAAC;EACDwB,iBAAiB,EAAE;IACjB1B,MAAM,EAAE;MACNC,IAAI,EAAE,mDAAmD;MACzDC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,qDAAqD;MAC3DC,GAAG,EAAE;IACP;EACF,CAAC;EACDyB,iBAAiB,EAAE;IACjB3B,MAAM,EAAE;MACNC,IAAI,EAAE,0CAA0C;MAChDC,GAAG,EAAE;IACP;EACF,CAAC;EACD0B,gBAAgB,EAAE;IAChB5B,MAAM,EAAE;MACNC,IAAI,EAAE,wCAAwC;MAC9CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,0CAA0C;MAChDC,GAAG,EAAE;IACP;EACF,CAAC;EACD2B,yBAAyB,EAAE;IACzB7B,MAAM,EAAE;MACNC,IAAI,EAAE,kDAAkD;MACxDC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,oDAAoD;MAC1DC,GAAG,EAAE;IACP;EACF,CAAC;EACD4B,gBAAgB,EAAE;IAChB9B,MAAM,EAAE;MACNC,IAAI,EAAE,yCAAyC;MAC/CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,2CAA2C;MACjDC,GAAG,EAAE;IACP;EACF,CAAC;EACD6B,oBAAoB,EAAE;IACpB/B,MAAM,EAAE;MACNC,IAAI,EAAE,6CAA6C;MACnDC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,+CAA+C;MACrDC,GAAG,EAAE;IACP;EACF,CAAC;EACD8B,gBAAgB,EAAE;IAChBhC,MAAM,EAAE;MACNC,IAAI,EAAE,wCAAwC;MAC9CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,0CAA0C;MAChDC,GAAG,EAAE;IACP;EACF,CAAC;EACD+B,SAAS,EAAE;IACTjC,MAAM,EAAE;MACNC,IAAI,EAAE,iDAAiD;MACvDC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,mDAAmD;MACzDC,GAAG,EAAE;IACP;EACF;AACF,CAAC;AAGDJ,aAAa,CAACmC,SAAS,CAACjC,MAAM,GAAGF,aAAa,CAACmC,SAAS,CAAC5B,SAAS;AAElE,MAAM6B,qBAAqB,GAAGA,CAAC;EAAEjC,IAAI;EAAEC;AAAmC,CAAC,KACxE,GAAED,IAAK,KAAIC,GAAI,GAAE;AAWL,SAASiC,4BAA4BA,CAClDC,iBAAyB,EACzBC,GAGC,EACDC,SAAiB,EACT;EACR,IAAIC,WAAW,GACZ,wCAAuCH,iBAAkB,4BAA2B,GACpF,IAAGC,GAAG,CAACG,IAAK,IAAGH,GAAG,CAACI,MAAM,GAAG,CAAE,QAAO,GACtCH,SAAS;EACX,MAAMI,UAAU,GAAG5C,aAAa,CAACsC,iBAAiB,CAAC;EACnD,IAAIM,UAAU,EAAE;IACd,MAAM;MAAE1C,MAAM,EAAE2C,YAAY;MAAEtC,SAAS,EAAEuC;IAAgB,CAAC,GAAGF,UAAU;IACvE,IAAIC,YAAY,EAAE;MAChB,MAAME,gBAAgB,GAAGX,qBAAqB,CAACS,YAAY,CAAC;MAC5D,IAAIC,eAAe,EAAE;QACnB,MAAME,mBAAmB,GAAGZ,qBAAqB,CAACU,eAAe,CAAC;QAClE,MAAMG,WAAW,GAAGH,eAAe,CAAC3C,IAAI,CAAC+C,UAAU,CAAC,eAAe,CAAC,GAChE,SAAS,GACT,SAAS;QACbT,WAAW,IAAK,WAAUO,mBAAoB,YAAWC,WAAY;AAC7E,qCAAqCF,gBAAiB,8CAA6C;MAC7F,CAAC,MAAM;QACLN,WAAW,IACR,WAAUM,gBAAiB,iDAAgD,GAC3E,oBAAmB;MACxB;IACF;EACF;EACA,OAAON,WAAW;AACpB;AAAC"}