import { analytics, appPluginStoreApi, appmaker } from '@appmaker-xyz/core';
import { logEvent, logScreenView, itemIdHelper, handleUTMParams } from './lib';
import { shopifyIdHelper } from '@appmaker-xyz/shopify';
import { analytics as firebaseAnalytics } from '@appmaker-xyz/react-native';

function decodeIds(id) {
  try {
    const decodeId = shopifyIdHelper(id, true);
    let newID = decodeId.replace('/', '');
    return newID;
  } catch (error) {
    return id;
  }
}
export const getScreenName = (screenName) => {
  if (screenName === 'home') {
    return 'Home Screen';
  }
  if (screenName === 'productDetail') {
    return 'Product Screen';
  }
  if (screenName === 'cartPageV2') {
    return 'Cart Screen';
  }
  if (screenName === 'productsByCollection') {
    return 'Category Screen';
  }
  return;
};
const eventsHandler = {
  free_gift_applied: (event, params, context) => {
    logEvent('free_gift_applied', params, context);
  },
  in_app_review_try: (event, params, context) => {
    logEvent('in_app_review_try', params, context);
  },
  in_app_review_success: (event, params, context) => {
    logEvent('in_app_review_success', params, context);
  },
  in_app_review_fail: (event, params, context) => {
    logEvent('in_app_review_fail', params, context);
  },
  appmaker_block_click: (event, params, context) => {
    // https://github.com/Appmaker-xyz/appmaker-dashboard/issues/1
    const {
      blockType,
      blockId,
      blockLabel,
      resourceId,
      resourceUrl,
      resourceName,
      parentId,
      parentName,
    } = params;
    if (resourceUrl && resourceUrl.length > 100) {
      var shortenedUrl = resourceUrl.substring(0, 100);
    }
    logEvent(
      'appmaker_block_click',
      {
        ...(blockType && { content_type: blockType }),
        ...(blockId && { content_id: blockId }),
        ...(blockLabel && { content_name: blockLabel }),
        ...(resourceId && { content_variant: resourceId }),
        ...(parentId && { content_list_id: parentId }),
        ...(parentName && { content_list_name: parentName }),
        ...(resourceUrl && {
          resource_url: shortenedUrl ? shortenedUrl : resourceUrl,
        }),
        ...(resourceName && { resource_name: resourceName }),
      },
      context,
    );
  },
  product_added_to_cart: (event, params, context) => {
    const {
      item_id,
      item_name,
      currency,
      price,
      variant_id,
      variant_price,
      variant_currency,
      item_variant,
      item_category,
      quantity,
    } = params;
    logEvent(
      'add_to_cart',
      {
        value: price,
        currency: currency,
        eventCategory: getScreenName(context.pageId) || '',
        items: [
          {
            item_id: itemIdHelper(item_id, variant_id),
            item_name: item_name,
            price: parseFloat(variant_price || price),
            currency: variant_currency || currency,
            item_variant: item_variant,
            item_category: item_category,
            quantity: quantity || 1,
          },
        ],
      },
      context,
    );
  },
  remove_from_cart: (event, params, context) => {
    const {
      item_id,
      item_name,
      currency,
      price,
      variant_id,
      variant_price,
      variant_currency,
      item_variant,
      item_category,
      quantity,
    } = params;
    logEvent(
      'remove_from_cart',
      {
        value: price || variant_price,
        currency: currency || variant_currency,
        eventCategory: getScreenName(context.pageId) || '',
        items: [
          {
            item_id: itemIdHelper(item_id, variant_id),
            item_name: item_name,
            price: parseFloat(variant_price || price),
            currency: variant_currency || currency,
            item_variant: item_variant,
            item_category: item_category,
            quantity: quantity,
          },
        ],
      },
      context,
    );
  },
  product_viewed: (event, params, context) => {
    const {
      item_id,
      item_name,
      currency,
      price,
      variant_id,
      variant_price,
      variant_currency,
      item_variant,
      item_category,
    } = params;
    logEvent(
      'view_item',
      {
        value: price,
        currency: currency,
        eventCategory: getScreenName(context.pageId) || '',
        items: [
          {
            item_id: itemIdHelper(item_id, variant_id),
            item_name: item_name,
            price: parseFloat(variant_price || price),
            currency: variant_currency || currency,
            item_variant: item_variant,
            item_category: item_category,
          },
        ],
      },
      context,
    );
  },
  user_login: (event, params, context) => {
    const { id, email, name } = params;
    firebaseAnalytics().setUserId(id);
    firebaseAnalytics().setUserProperty(name, email);
    logEvent('user_logged_in', {
      // user_id: decodeIds(id),
      // user_name: name,
      // user_email: email,
      // user_login_provider: provider,
    });
  },
  user_logout: (event, params, context) => {
    logEvent('user_logged_out', {});
  },
  product_search_result: (event, params, context) => {
    const { list_query } = params;
    logEvent(
      'search',
      {
        search_term: list_query,
      },
      context,
    );
  },
  user_register: (event, params, context) => {
    const { id, email, name } = params;
    logEvent('sign_up', {});
  },
  'cart.applycoupon': (event, params, context) => {
    const { category, action, couponCode } = params;
    logEvent('ApplyCouponCode', {
      eventCategory: category || '',
      eventAction: 'Apply Coupon Code',
      couponCode: couponCode,
    });
  },
  product_added_to_wishlist: (event, params, context) => {
    const {
      item_id,
      item_name,
      currency,
      price,
      variant_id,
      variant_price,
      variant_currency,
      item_variant,
      item_category,
    } = params;
    logEvent(
      'add_to_wishlist',
      {
        value: price,
        currency: currency,
        eventCategory: getScreenName(context.pageId) || '',
        items: [
          {
            item_id: itemIdHelper(item_id, variant_id),
            item_name: item_name,
            price: parseFloat(variant_price || price),
            currency: variant_currency || currency,
            item_variant: item_variant,
            item_category: item_category,
          },
        ],
      },
      context,
    );
  },
  product_search: (event, params, context) => {
    const { query } = params;
    logEvent('search', {
      search_term: query,
    });
  },
  shareProduct: (event, params, context) => {
    const { product_name } = params;
    logEvent('ShareProduct', {
      eventCategory: 'Product Screen',
      eventAction: 'Share Product clicks',
      product_name: product_name,
    });
  },
  checkout_started: (event, params, context) => {
    const { currency, total_amount } = params;
    let price = parseFloat(total_amount);
    const filteredParam = {
      currency: currency,
      value: price,
      items: context.cart.lineItems.edges.map(({ node }) => ({
        item_id: itemIdHelper(
          decodeIds(node?.variant?.product?.id),
          decodeIds(node?.variant?.id),
        ),
        item_name: node?.title,
        price: parseFloat(node?.variant?.price?.amount),
        currency: node?.variant?.price?.currencyCode,
        item_category: node?.variant?.product?.productType,
        item_variant: node?.variant?.title,
        quantity: node?.quantity,
      })),
    };
    logEvent('begin_checkout', filteredParam);
  },
  view_cart: (event, params, context) => {
    const { currency, id, total_price, total_quantity } = params;
    let price = parseFloat(total_price);
    const filteredParam = {
      currency: currency,
      value: price,
      items: context.lineItems.edges.map(({ node }) => ({
        item_id: itemIdHelper(
          decodeIds(node?.variant?.product?.id),
          decodeIds(node?.variant?.id),
        ),
        item_category: node?.variant?.product?.productType,
        item_variant: node?.variant?.title,
        item_name: node.title,
        price: parseFloat(node.variant.price.amount),
        currency: node.variant.price.currencyCode,
        quantity: node?.quantity,
      })),
    };
    logEvent('view_cart', filteredParam, context);
  },
  collection_view: (event, params, context) => {
    // removed this event from firebase
  },
  view_item_list: (event, params = {}, context) => {
    const { items = [] } = params;
    params = {
      ...params,
      items: items?.map?.((item) => {
        return {
          ...item,
          item_id: itemIdHelper(
            decodeIds(item?.item_id),
            decodeIds(item?.variant_id),
          ),
        };
      }),
    };
    logEvent('view_item_list', params, context);
  },
  checkout_completed: (event, params, context) => {
    const {
      currency,
      id,
      order_id,
      order_number,
      order_name,
      products_count,
      total_amount,
    } = params;
    const pluginSettings =
      appPluginStoreApi().getState().plugins?.shopify?.settings;
    let transactionID = order_number;
    if (
      pluginSettings?.firebase_purchase_event_transaction_id === 'order_name'
    ) {
      transactionID = order_name;
    } else if (
      pluginSettings?.firebase_purchase_event_transaction_id === 'order_id'
    ) {
      transactionID = shopifyIdHelper(order_id, true);
    }
    try {
      let price = parseFloat(total_amount);
      const filteredParam = {
        currency: currency,
        value: price,
        userAgent: 'Appmaker',
        transaction_id: transactionID?.toString(),
        items: context.cart.lineItems.edges.map(({ node }) => ({
          item_id: itemIdHelper(
            decodeIds(node?.variant?.product?.id),
            decodeIds(node?.variant?.id),
          ),
          item_name: node?.title,
          price: parseFloat(node?.variant?.price?.amount),
          currency: node?.variant?.price?.currencyCode,
          item_category: node?.variant?.product?.productType,
          item_variant: node?.variant?.title,
          quantity: node?.quantity,
        })),
      };
      logEvent('purchase', filteredParam, context);
    } catch (error) {}
  },
  sortApply: (event, params, context) => {
    const { value } = params;
    logEvent('SortClick', {
      eventCategory: 'Category Screen',
      eventAction: 'Sort Clicks',
      value: value,
    });
  },
  ApplyFilter: (event, params, context) => {
    const { filterItem } = params;
    try {
      const filterItemString = '-' + JSON.stringify(filterItem) + '-'; // this is added to verify firebase not converting it to object
      logEvent('FilterClick', {
        eventCategory: 'Category Screen',
        eventAction: 'Filter Clicks',
        filter_click_item: filterItemString,
        // items: filterItem.map((v) => v),
      });
    } catch (error) {}
  },
  drawerCategoryClick: (event, params, context) => {
    const { action, title } = params;
    logEvent('SubCategoryClick', {
      eventCategory: 'Home Screen',
      eventAction: 'Drawer Category Clicks',
      sub_category_name: title,
    });
  },
  product_removed_from_wishlist: (event, params, context) => {
    const {
      currency,
      item_brand,
      item_id,
      item_name,
      item_variant,
      price,
      quantity,
      value,
      variant_currency,
      variant_id,
      variant_price,
    } = params;
    logEvent('RemoveFromWishlist', {
      eventCategory: getScreenName(context.pageId) || '',
      eventAction: 'Remove From Wishlist clicks',
      product_name: item_name,
    });
  },
  track: (event, params, context) => {
    logEvent(event, params, context);
  },
  page: ({ pageId, name, context }) => {
    logScreenView(pageId, name, context);
  },
};

export function activateAnalyticsEvents(settings) {
  analytics.onTrack(async (event, params, context) => {
    const eventFn = eventsHandler[event];
    if (eventFn) {
      eventFn(event, params, context);
    } else {
      try {
        const appmakerBlacklistedEvents = await appmaker.applyFilters(
          'firebase-analytics-blacklisted-custom-events',
          [],
        );
        const pluginBlacklistedCustomEvents =
          settings?.blackListedCustomEvents?.map((e) => e.eventName) || [];
        const appmakerWhitelistedEvents = await appmaker.applyFilters(
          'firebase-analytics-whitelisted-custom-events',
          [],
        );
        const pluginWhitelistedCustomEvents =
          settings?.whiteListedCustomEvents?.map((e) => e.eventName) || [];
        const blacklistedEvents = [
          ...appmakerBlacklistedEvents,
          ...pluginBlacklistedCustomEvents,
        ];
        const whitelistedEvents = [
          ...appmakerWhitelistedEvents,
          ...pluginWhitelistedCustomEvents,
        ];
        if (blacklistedEvents.includes(event)) {
          return;
        }
        if (whitelistedEvents.includes(event)) {
          logEvent(event, params, context);
          return;
        }
        logEvent(event, params, context);
      } catch (error) {
        console.log(`error in sending custom event ${event} `, error);
      }
    }
  }, 'firebase');
  analytics.onTrackSystemEvents((event, params, context) => {
    switch (event) {
      case 'campaign_details':
        handleUTMParams(params);
        break;
      default:
        break;
    }
  }, 'firebase-crashlytics');
  analytics.onPageTrack((id, name, context) => {
    eventsHandler?.page({ pageId: id, name, context });
  }, 'firebase');
}
