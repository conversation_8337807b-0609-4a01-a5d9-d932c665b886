{"version": 3, "names": ["_utils", "require", "defineType", "visitor", "aliases", "fields", "process", "env", "BABEL_TYPES_8_BREAKING", "object", "validate", "Object", "assign", "oneOfNodeTypes", "callee", "assertNodeType", "key", "value", "expression", "builder", "body", "async", "assertValueType", "default", "exported", "properties", "chain", "assertEach", "elements"], "sources": ["../../src/definitions/experimental.ts"], "sourcesContent": ["import defineType, {\n  assertEach,\n  assertNodeType,\n  assertValueType,\n  chain,\n} from \"./utils\";\n\ndefineType(\"ArgumentPlaceholder\", {});\n\ndefineType(\"BindExpression\", {\n  visitor: [\"object\", \"callee\"],\n  aliases: [\"Expression\"],\n  fields: !process.env.BABEL_TYPES_8_BREAKING\n    ? {\n        object: {\n          validate: Object.assign(() => {}, {\n            oneOfNodeTypes: [\"Expression\"],\n          }),\n        },\n        callee: {\n          validate: Object.assign(() => {}, {\n            oneOfNodeTypes: [\"Expression\"],\n          }),\n        },\n      }\n    : {\n        object: {\n          validate: assertNodeType(\"Expression\"),\n        },\n        callee: {\n          validate: assertNodeType(\"Expression\"),\n        },\n      },\n});\n\ndefineType(\"ImportAttribute\", {\n  visitor: [\"key\", \"value\"],\n  fields: {\n    key: {\n      validate: assertNodeType(\"Identifier\", \"StringLiteral\"),\n    },\n    value: {\n      validate: assertNodeType(\"StringLiteral\"),\n    },\n  },\n});\n\ndefineType(\"Decorator\", {\n  visitor: [\"expression\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"DoExpression\", {\n  visitor: [\"body\"],\n  builder: [\"body\", \"async\"],\n  aliases: [\"Expression\"],\n  fields: {\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n    async: {\n      validate: assertValueType(\"boolean\"),\n      default: false,\n    },\n  },\n});\n\ndefineType(\"ExportDefaultSpecifier\", {\n  visitor: [\"exported\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    exported: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"RecordExpression\", {\n  visitor: [\"properties\"],\n  aliases: [\"Expression\"],\n  fields: {\n    properties: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"ObjectProperty\", \"SpreadElement\")),\n      ),\n    },\n  },\n});\n\ndefineType(\"TupleExpression\", {\n  fields: {\n    elements: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Expression\", \"SpreadElement\")),\n      ),\n      default: [],\n    },\n  },\n  visitor: [\"elements\"],\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"DecimalLiteral\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\n// https://github.com/tc39/proposal-js-module-blocks\ndefineType(\"ModuleExpression\", {\n  visitor: [\"body\"],\n  fields: {\n    body: {\n      validate: assertNodeType(\"Program\"),\n    },\n  },\n  aliases: [\"Expression\"],\n});\n\n// https://github.com/tc39/proposal-pipeline-operator\n// https://github.com/js-choi/proposal-hack-pipes\ndefineType(\"TopicReference\", {\n  aliases: [\"Expression\"],\n});\n\n// https://github.com/tc39/proposal-pipeline-operator\n// https://github.com/js-choi/proposal-smart-pipes\ndefineType(\"PipelineTopicExpression\", {\n  builder: [\"expression\"],\n  visitor: [\"expression\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"PipelineBareFunction\", {\n  builder: [\"callee\"],\n  visitor: [\"callee\"],\n  fields: {\n    callee: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"PipelinePrimaryTopicReference\", {\n  aliases: [\"Expression\"],\n});\n"], "mappings": ";;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAOA,IAAAC,cAAU,EAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;AAErC,IAAAA,cAAU,EAAC,gBAAgB,EAAE;EAC3BC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC7BC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,MAAM,EAAE,CAACC,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACvC;IACEC,MAAM,EAAE;MACNC,QAAQ,EAAEC,MAAM,CAACC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;QAChCC,cAAc,EAAE,CAAC,YAAY;MAC/B,CAAC;IACH,CAAC;IACDC,MAAM,EAAE;MACNJ,QAAQ,EAAEC,MAAM,CAACC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;QAChCC,cAAc,EAAE,CAAC,YAAY;MAC/B,CAAC;IACH;EACF,CAAC,GACD;IACEJ,MAAM,EAAE;MACNC,QAAQ,EAAE,IAAAK,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDD,MAAM,EAAE;MACNJ,QAAQ,EAAE,IAAAK,qBAAc,EAAC,YAAY;IACvC;EACF;AACN,CAAC,CAAC;AAEF,IAAAb,cAAU,EAAC,iBAAiB,EAAE;EAC5BC,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC;EACzBE,MAAM,EAAE;IACNW,GAAG,EAAE;MACHN,QAAQ,EAAE,IAAAK,qBAAc,EAAC,YAAY,EAAE,eAAe;IACxD,CAAC;IACDE,KAAK,EAAE;MACLP,QAAQ,EAAE,IAAAK,qBAAc,EAAC,eAAe;IAC1C;EACF;AACF,CAAC,CAAC;AAEF,IAAAb,cAAU,EAAC,WAAW,EAAE;EACtBC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBE,MAAM,EAAE;IACNa,UAAU,EAAE;MACVR,QAAQ,EAAE,IAAAK,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF,IAAAb,cAAU,EAAC,cAAc,EAAE;EACzBC,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBgB,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC1Bf,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,MAAM,EAAE;IACNe,IAAI,EAAE;MACJV,QAAQ,EAAE,IAAAK,qBAAc,EAAC,gBAAgB;IAC3C,CAAC;IACDM,KAAK,EAAE;MACLX,QAAQ,EAAE,IAAAY,sBAAe,EAAC,SAAS,CAAC;MACpCC,OAAO,EAAE;IACX;EACF;AACF,CAAC,CAAC;AAEF,IAAArB,cAAU,EAAC,wBAAwB,EAAE;EACnCC,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,iBAAiB,CAAC;EAC5BC,MAAM,EAAE;IACNmB,QAAQ,EAAE;MACRd,QAAQ,EAAE,IAAAK,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF,IAAAb,cAAU,EAAC,kBAAkB,EAAE;EAC7BC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,MAAM,EAAE;IACNoB,UAAU,EAAE;MACVf,QAAQ,EAAE,IAAAgB,YAAK,EACb,IAAAJ,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAK,iBAAU,EAAC,IAAAZ,qBAAc,EAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC;IAEjE;EACF;AACF,CAAC,CAAC;AAEF,IAAAb,cAAU,EAAC,iBAAiB,EAAE;EAC5BG,MAAM,EAAE;IACNuB,QAAQ,EAAE;MACRlB,QAAQ,EAAE,IAAAgB,YAAK,EACb,IAAAJ,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAK,iBAAU,EAAC,IAAAZ,qBAAc,EAAC,YAAY,EAAE,eAAe,CAAC,CAAC,CAC1D;MACDQ,OAAO,EAAE;IACX;EACF,CAAC;EACDpB,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEF,IAAAF,cAAU,EAAC,gBAAgB,EAAE;EAC3BiB,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBd,MAAM,EAAE;IACNY,KAAK,EAAE;MACLP,QAAQ,EAAE,IAAAY,sBAAe,EAAC,QAAQ;IACpC;EACF,CAAC;EACDlB,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW;AAC3D,CAAC,CAAC;AAGF,IAAAF,cAAU,EAAC,kBAAkB,EAAE;EAC7BC,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBE,MAAM,EAAE;IACNe,IAAI,EAAE;MACJV,QAAQ,EAAE,IAAAK,qBAAc,EAAC,SAAS;IACpC;EACF,CAAC;EACDX,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAIF,IAAAF,cAAU,EAAC,gBAAgB,EAAE;EAC3BE,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAIF,IAAAF,cAAU,EAAC,yBAAyB,EAAE;EACpCiB,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBhB,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBE,MAAM,EAAE;IACNa,UAAU,EAAE;MACVR,QAAQ,EAAE,IAAAK,qBAAc,EAAC,YAAY;IACvC;EACF,CAAC;EACDX,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEF,IAAAF,cAAU,EAAC,sBAAsB,EAAE;EACjCiB,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBhB,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBE,MAAM,EAAE;IACNS,MAAM,EAAE;MACNJ,QAAQ,EAAE,IAAAK,qBAAc,EAAC,YAAY;IACvC;EACF,CAAC;EACDX,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEF,IAAAF,cAAU,EAAC,+BAA+B,EAAE;EAC1CE,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC"}