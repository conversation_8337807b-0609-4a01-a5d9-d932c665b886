import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ThemeText, AppImage } from '../atoms';
import { useShopifyProduct, useFreeGift } from '@appmaker-xyz/shopify';
import { usePluginStore } from '@appmaker-xyz/core';
import { testProps } from '@appmaker-xyz/core';

const CartFreeGiftNotice = ({ attributes = {} }) => {
  // const product = useAppStorage(
  //   (state) => state?.next_free_gift_product?.productData,
  // );
  const freeGifts = useFreeGift();
  const { nextFreeGifts } = freeGifts;
  const nextFreeGift = nextFreeGifts?.[0];
  const product = nextFreeGift?.product;
  const themeColor = usePluginStore(
    (state) => state?.plugins?.shopify?.settings?.themeAccentColor || '#15803D',
  );
  const { title, imageUrl, regularPriceWithCurrency } = useShopifyProduct({
    product,
  });
  if (!nextFreeGift) {
    return null;
  }
  const { message } = nextFreeGift;
  const messageText = {
    backgroundColor: `${themeColor}26`,
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: `${themeColor}33`,
    marginBottom: 4,
  };
  return title ? (
    <Layout {...testProps('free-gift-notice')} style={styles.container}>
      <Layout style={[styles.innerContainer, { borderColor: themeColor }]}>
        <AppImage
          {...testProps('free-gift-image')}
          uri={imageUrl}
          style={styles.image}
        />
        <Layout style={styles.contentContainer}>
          <ThemeText
            testId={'free-gift-message'}
            fontFamily="medium"
            style={messageText}
            color={themeColor}>
            {message}
          </ThemeText>
          <Layout style={styles.rowBetween}>
            <Layout style={styles.titleContainer}>
              {/* <ThemeText color="#EF1414" size="sm">
                **Ends in 34:09:08**
              </ThemeText> */}
              <ThemeText
                testId={'free-gift-title'}
                fontFamily="medium"
                numberOfLines={3}>
                {title}
              </ThemeText>
            </Layout>
            <Layout style={styles.rightAlign}>
              <ThemeText
                testId={'free-gift-regular-price'}
                color="#858585"
                style={styles.strikeText}>
                {regularPriceWithCurrency}
              </ThemeText>
              <ThemeText fontFamily="bold" size="md" color={themeColor}>
                FREE
              </ThemeText>
            </Layout>
          </Layout>
        </Layout>
      </Layout>
    </Layout>
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: 12,
  },
  innerContainer: {
    borderRadius: 12,
    borderWidth: 2,
    borderStyle: 'dashed',
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  image: {
    height: '100%',
    maxHeight: 100,
    minHeight: 75,
    width: 75,
    marginRight: 6,
    borderRadius: 6,
    overflow: 'hidden',
  },
  contentContainer: {
    flex: 1,
  },
  titleContainer: {
    flex: 1,
    marginRight: 4,
    justifyContent: 'flex-start',
  },
  rowBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 2,
  },
  rightAlign: {
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  strikeText: {
    textDecorationLine: 'line-through',
  },
});

export default CartFreeGiftNotice;
