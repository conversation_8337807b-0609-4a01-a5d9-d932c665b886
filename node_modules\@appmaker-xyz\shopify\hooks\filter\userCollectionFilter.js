import { usePageState, analytics, usePageStateApi } from '@appmaker-xyz/core';
import { isEmpty, trim } from 'lodash';
import { useShopifyFilterStore } from './filterStore';
import { converToShopifyParams } from './helper';
import { runDataSource } from '@appmaker-xyz/core';
import { useEffect } from 'react';
import { getExtensionAsBoolean } from '@appmaker-xyz/core';
import { appmaker } from '@appmaker-xyz/core';
import { applyFilters as appmakerApplyFilters } from '@appmaker-xyz/core';

const SELECTED_SORT_KEY = 'sort';
const SELECTED_FILTERS_SYNC_KEY = 'selected_filters_to_sync';
const SELECTED_FILTERS_REMOVE_KEY = 'selected_filters_to_remove';
const CLEAR_ALL_FILTERS_KEY = 'crear_all_filters';

const shopifyDatasource = {
  dataSource: {
    source: 'shopify',
    attributes: {},
  },
};

export function useCollectionFilter() {
  const setPageStateVar = usePageState((state) => state.setPageStateVar);
  const currentAction = usePageState((state) => state.currentAction);
  const blockData = usePageState((state) => state?.blockData);
  const pageStateApi = usePageStateApi();

  const disableLiveFilter = appmakerApplyFilters(
    'shopify-disable-live-filters',
    false,
  );
  const { closeFilterModal } = useFilterModal();

  const getSelectedFilters = useShopifyFilterStore(
    (store) => store.getSelectedFilters,
  );
  const setAvailableFilters = useShopifyFilterStore(
    (store) => store.setAvailableFilters,
  );

  // TODO: This part of code is written to sync filter data to filterStore when  a filter related action is performed outside of zustand provider
  const selectFilter = useShopifyFilterStore((store) => store.selectFilter);
  const removeFilter = useShopifyFilterStore((store) => store.removeFilter);
  const setPageState = usePageState((state) => state.setPageState);

  const selectedFiltersToSync = usePageState(
    (state) => state?.[SELECTED_FILTERS_SYNC_KEY],
  );
  const selectedFiltersToRemove = usePageState(
    (state) => state?.[SELECTED_FILTERS_REMOVE_KEY],
  );
  const clearSelectedFilters = useShopifyFilterStore(
    (store) => store.clearSelectedFilters,
  );
  const clearAllFiltersTrigger = usePageState(
    (state) => state?.[CLEAR_ALL_FILTERS_KEY],
  );
  useEffect(() => {
    if (selectedFiltersToSync) {
      const filterStoreSelectedFilter = getSelectedFilters();
      if (
        selectedFiltersToSync?.filterKey &&
        isEmpty(filterStoreSelectedFilter?.[selectedFiltersToSync?.filterKey])
      ) {
        selectFilter(
          selectedFiltersToSync?.filterKey,
          selectedFiltersToSync?.filterValueID,
          selectedFiltersToSync?.filterValue,
        );
        setPageState({
          [SELECTED_FILTERS_SYNC_KEY]: null,
        });
      }
    }
    if (selectedFiltersToRemove?.filterKey) {
      removeFilter(
        selectedFiltersToRemove?.filterKey,
        selectedFiltersToRemove?.filterValueID,
      );
      setPageState({
        [SELECTED_FILTERS_REMOVE_KEY]: null,
      });
    }
    // Trigger from outside filterStore context
    // to clear all selected filters in filterStore
    if (clearAllFiltersTrigger === true) {
      clearSelectedFilters();
      // resetting filter clear trigger
      setPageState({
        [CLEAR_ALL_FILTERS_KEY]: false,
      });
    }
  }, [selectedFiltersToSync, selectedFiltersToRemove, clearAllFiltersTrigger]);

  // TODO: END

  function applyFilters() {
    const filterParams = converToShopifyParams(
      getSelectedFilters(),
      avilableFilters,
    );
    analytics.track('ApplyFilter', {
      action: 'ApplyFilter',
      filterItem: filterParams,
    });
    setPageStateVar('filter', getSelectedFilters());
    // closeFilterModal();
  }

  const selectedFilters = useShopifyFilterStore(
    (store) => store.selectedFillters,
  );
  const clearFilter = useShopifyFilterStore((store) => store.clearFilter);
  const availableFiltersHook = useShopifyFilterStore(
    (store) => store.availableFilters,
  );
  const setNextFiltersLoading = useShopifyFilterStore(
    (store) => store.setNextFiltersLoading,
  );
  const isNextFiltersLoading = useShopifyFilterStore(
    (store) => store.isNextFiltersLoading,
  );
  useEffect(() => {
    const getFilters = async () => {
      if (isEmpty(selectedFilters) && !clearFilter) {
        return;
      }
      setNextFiltersLoading(true);
      const searchInput = pageStateApi?.getState()?.searchInputValue || pageStateApi?.getState()?.searchQuery;
      const [response] = await runDataSource(shopifyDatasource, {
        methodName: 'productFiltersNext',
        mapping: {
          items: 'data.data.collection.products.filters',
        },
        params: {
          ...(blockData?.sub_collection || currentAction?.params),
          ...(searchInput && { search: searchInput }),
          ...(searchInput && { surface: 'search-result' }),
          selectedFilters,
        },
      });
      let filters = response?.data?.data?.collection?.products?.filters;
      if (filters && filters?.length > 0) {
        filters = filters.filter((filter) => {
          return filter?.values?.length > 0;
        });
        const customFilters = appmaker.applyFilters(
          'appmaker-available-filters',
          filters,
        );
        const finalFilters = customFilters ? customFilters : filters;
        setAvailableFilters(finalFilters);
      }
      setNextFiltersLoading(false);
    };
    if (!(disableLiveFilter === true) && !(disableLiveFilter === '1')) {
      getFilters();
    }
  }, [selectedFilters]);
  const avilableFilters = appmaker.applyFilters(
    'appmaker-available-filters',
    availableFiltersHook,
  );
  const availableFilters = availableFiltersHook || avilableFilters;
  return {
    avilableFilters: availableFilters,
    availableFilters: availableFilters,
    applyFilters,
    closeFilterModal,
    isNextFiltersLoading,
  };
}

export function useFilterModal() {
  const setPageState = usePageState((store) => store.setPageState);
  const isFilterModalShown = usePageState((store) => store.isFilterModalShown);
  const filterModalInitialTabName = usePageState(
    (store) => store.filterModalInitialTabName,
  );
  const openFilterModal = ({ initialTabName = null }) => {
    setPageState({
      filterModalInitialTabName: initialTabName,
      isFilterModalShown: true,
    });
  };
  const closeFilterModal = () => {
    setPageState({
      filterModalInitialTabName: null,
      isFilterModalShown: false,
    });
  };
  const context = useShopifyFilterStore((store) => store.context);
  return {
    filterModalInitialTabName,
    context,
    isFilterModalShown,
    openFilterModal,
    closeFilterModal,
  };
}
export function useFilterOptions() {
  const selectFilter = useShopifyFilterStore((store) => store.selectFilter);
  const removeFilter = useShopifyFilterStore((store) => store.removeFilter);
  const clearSelectedFilters = useShopifyFilterStore(
    (store) => store.clearSelectedFilters,
  );
  const isFilterSelected = useShopifyFilterStore(
    (store) => store.isFilterSelected,
  );
  const getSelectedFilters = useShopifyFilterStore(
    (store) => store.getSelectedFilters,
  );
  const hasAnyFilterSelected = useShopifyFilterStore(
    (store) => store.hasAnyFilterSelected,
  );
  const priceRange = useShopifyFilterStore((store) => store.priceRange);

  const selectedFilters = useShopifyFilterStore(
    (store) => store.selectedFillters,
  );

  return {
    selectedFilters,
    priceRange,
    selectFilter,
    removeFilter,
    getSelectedFilters,
    isFilterSelected,
    clearSelectedFilters,
    hasAnyFilterSelected,
    selectedFilters,
  };
}
export function useSelectedFilterItem({ filterKey, filterValueId }) {
  const selectedItem = useShopifyFilterStore(
    (state) =>
      state?.selectedFillters[filterKey] &&
      state.selectedFillters[filterKey][filterValueId],
  );
  return selectedItem;
}

export function useSelectedFiltersCount(filterKey) {
  const selectedItemCount = useShopifyFilterStore(
    (state) => Object.values(state?.selectedFillters[filterKey] || {}).length,
  );
  return selectedItemCount;
}
export function useIsAnyFilterSelected() {
  const isFilterSelected = useShopifyFilterStore(
    (state) => Object.values(state?.selectedFillters || {}).length > 0,
  );
  return isFilterSelected;
}
export function useIsAnySortSelected() {
  const isSortSelected = usePageState(
    (state) => !isEmpty(trim(state[SELECTED_SORT_KEY])),
  );
  return isSortSelected;
}

export function useSortModal() {
  const isSortModalShown = usePageState((state) => state?.sortModalShown);
  const setPageState = usePageState((state) => state?.setPageState);
  const context = useShopifyFilterStore((store) => store.context);

  const closeSortModal = () => {
    setPageState({
      sortModalShown: false,
    });
  };
  const openSortModal = () => {
    setPageState({
      sortModalShown: true,
    });
  };
  return {
    context,
    isSortModalShown,
    openSortModal,
    closeSortModal,
  };
}

export function useSortValue() {
  const setPageStateVar = usePageState((state) => state.setPageStateVar);
  const selectedSort = usePageState((state) => state[SELECTED_SORT_KEY]);
  const setPageState = usePageState((state) => state?.setPageState);
  const pageStateApi = usePageStateApi();

  const closeSortModal = () => {
    setPageState({
      sortModalShown: false,
    });
  };
  function applySort(sortValue, { label = '' } = {}) {
    analytics.track('sortApply', { value: sortValue, label });
    setPageStateVar(SELECTED_SORT_KEY, sortValue);
    closeSortModal();
  }

  // Settings pre-applied sort for collection from action params
  useEffect(() => {
    const currentAction = pageStateApi?.getState()?.currentAction;
    if (
      !selectedSort &&
      currentAction?.params?.sort &&
      typeof currentAction?.params?.sort === 'string'
    ) {
      applySort(currentAction?.params?.sort);
    }
  }, []);
  return {
    applySort,
    selectedSort,
  };
}
