import React, { Component, useState } from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
// @ts-ignore
import Modal from 'react-native-modal';
import { spacing, color } from '../styles';
import { AppmakerText, But<PERSON>, Stepper, VariationListing } from '../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';

const CloseIcon = (props) => <Icon name="x" color="#000" {...props} />;
let variation_list = [
  { label: 'Item 1', value: 0 },
  { label: 'long Item 2', value: 1 },
  { label: 'Long long long long Item 3', value: 2 },
  { label: 'long Item 2', value: 1 },
  { label: 'Long long long long Item 3', value: 2 },
  { label: 'long Item 2', value: 1 },
  { label: 'Long long long long Item 3', value: 2 },
  { label: 'long Item 2', value: 1 },
  { label: 'Long long long long Item 3', value: 2 },
  { label: 'Long long long long Item 3', value: 2 },
  { label: 'long Item 2', value: 1 },
  { label: 'Long long long long Item 3', value: 2 },
  { label: 'long Item 2', value: 1 },
  { label: 'Long long long long Item 3', value: 2 },
  {
    label:
      'Lorem ipsum dolor sit amet consectetur, adipisicing elit. Laborum molestiae fugiat deserunt, ipsum, numquam eius architecto quam veritatis consectetur consequatur rem tempora earum, eum aliquam totam vel nisi aspernatur repellendus!',
    value: 3,
  },
  { label: 'Long long long long Item 3', value: 2 },
];

const DefaultModalContent = (props) => {
  return (
    <View style={styles.modalBody}>
      <View style={styles.modalHeader}>
        <View>
          <AppmakerText category="actionTitle" status="dark">
            Product Name
          </AppmakerText>
          <AppmakerText category="highlighter1" status="grey">
            Variations/Customizations/Small description
          </AppmakerText>
        </View>
        <Button
          onPress={props.onPress}
          status="white"
          accessoryLeft={CloseIcon}
          small
        />
      </View>
      <ScrollView style={styles.modalContent}>
        <VariationListing
          variationTitle="Variation Title"
          varaitionHelper="Variation helper text"
          variation_list={variation_list}
        />
      </ScrollView>
      <View style={styles.modalFooter}>
        <View style={styles.modalStepperContainer}>
          <AppmakerText category="bodyParagraph">Choose quantity</AppmakerText>
          <Stepper />
        </View>
        <Button>Add to Cart</Button>
      </View>
    </View>
  );
};

class BottomHalfModal extends Component {
  renderModal() {
    return (
      <Modal
        testID={'modal'}
        isVisible={this.isVisible()}
        onSwipeComplete={this.close}
        onBackButtonPress={this.close}
        backdropTransitionOutTiming={0}
        onBackdropPress={this.close}
        style={styles.view}>
        <DefaultModalContent onPress={this.close} />
      </Modal>
    );
  }
  constructor(props, state) {
    super(props);
    this.state = {
      ...state,
      visible: false,
    };
  }

  open = () => this.setState({ visible: true });
  close = () => this.setState({ visible: false });
  isVisible = () => this.state.visible;
  renderButton() {
    return <Button onPress={this.open}>Open modal</Button>;
  }
  render() {
    return (
      <View style={styles.view}>
        {this.renderButton()}
        {this.renderModal()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  view: {
    justifyContent: 'flex-end',
    marginVertical: 0,
    marginHorizontal: 1,
  },
  modalBody: {
    backgroundColor: color.white,
    padding: spacing.md,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
    flexDirection: 'column',
    maxHeight: '80%',
  },
  modalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: color.light,
    paddingBottom: spacing.base,
  },
  modalContent: {
    paddingVertical: spacing.base,
    flexDirection: 'column',
    width: '100%',
  },
  modalStepperContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
    marginBottom: spacing.md,
    borderBottomColor: color.light,
    borderBottomWidth: 1,
    borderTopColor: color.light,
    borderTopWidth: 1,
  },
  modalFooter: {
    width: '100%',
    backgroundColor: color.white,
  },
});

export default BottomHalfModal;
