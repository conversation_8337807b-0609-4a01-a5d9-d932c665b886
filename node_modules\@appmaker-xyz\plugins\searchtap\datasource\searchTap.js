import axios from 'axios';
import { isEmpty } from 'lodash';
import { runDataSource } from '@appmaker-xyz/core';

function imageResize(img, size) {
  if (img && img.src && size) {
    let separatorIndex = img.src.lastIndexOf('.');
    return `${img.src.slice(
      0,
      separatorIndex,
    )}_${size.toLowerCase()}${img.src.slice(separatorIndex)}`;
  }
}
function convertResponse(response) {
  response.data.results.map((item) => {
    const smallImage = imageResize(item.images[0], 'x500');
    item.smallImage = smallImage;
  });
  return response;
}

export default class SearchTapDataSource {
  constructor(config) {
    this.config = config;
  }
  async searchProduct(params, { page }) {
    const count = 15;
    if (isEmpty(params)) {
      return;
    }
    const body = {
      query: params,
      fields: [
        'id',
        'discount',
        'collections',
        'size',
        'color',
        'shape',
        'discounted_price',
        'images',
        'handle',
        'product_type',
        'hasMultiplePrice',
        'price',
        'tags',
        'title',
        'variants',
        'isActive',
      ],
      // textFacets: ['collections', 'color', 'size', 'finish', 'shape'],
      highlightFields: [],
      searchFields: ['*'],
      filter:
        this.config.filter_param || 'isSearchable = 1 AND discounted_price > 0',
      sort: this.config.sort_param || ['-isActive', '-_rank'],
      skip: count * (page - 1),
      count,
      collection: this.config.collection,
      facetCount: 100,
      groupCount: -1,
      typoTolerance: 1,
      textFacetFilters: {},
      numericFacets: {
        discount: [
          '[10,100]',
          '[20,100]',
          '[30,100]',
          '[40,100]',
          '[50,100]',
          '[60,100]',
          '[70,100]',
          '[80,100]',
          '[90,100]',
        ],
        discounted_price: [
          '[0,1000]',
          '[1001,2000]',
          '[2001,3000]',
          '[3001,4000]',
          '[4001,5000]',
          '[5001,6000]',
          '[6001,7000]',
          '[7001,8000]',
          '[8001,9000]',
          '[9001,10000]',
          '[10001,9999999]',
        ],
      },
      numericFacetFilters: {},
      textFacetQuery: null,
      geo: {},
    };
    const resp = await axios.post(
      // 'https://d9t1ygbpsmiqbalw5a9p2nu2-fast.searchtap.net/v2',
      this.config.url,
      body,
      {
        headers: {
          authorization: 'Bearer ' + this.config.auth_token,
        },
      },
    );
    return convertResponse(resp);
  }
  async searchProductList(params, { page }) {
    const resp = await this.searchProduct(params, { page });
    const data = resp.data;
    const productIds = data.results.map(
      (item) => `gid://shopify/Product/${item.id}`,
    );
    const dataSource = {
      attributes: {},
      source: 'shopify',
    };
    const attributes = {
      methodName: 'products',
      params: {
        ids: productIds,
      },
    };
    const [response] = await runDataSource({ dataSource }, attributes);
    return response;
  }
}
