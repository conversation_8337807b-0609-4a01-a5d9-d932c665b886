import { color, spacing } from '../../styles';
import { appSettings } from '@appmaker-xyz/core';

const LoginPage = {
  type: 'normal',
  title: 'Forgot Password',
  attributes: {
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/blocksView',
      attributes: {
        renderType: 'normal',
        rootContainerStyle: {
          flex: 1,
        },
        contentContainerStyle: {
          flex: 1,
          justifyContent: 'center',
          paddingHorizontal: spacing.base,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/remoteTouchableImage',
          attributes: {
            name: 'LOGIN_LOGO',
            touchable: true,
            conditionalClick: true,
            conditionalClickCount: 20,
            conditionalClickAction: {
              action: 'OPEN_APPMAKER_DEVELOPER_PAGE',
            },
            style: {
              width: 150,
              height: 60,
              alignSelf: 'center',
              resizeMode: 'contain',
              marginBottom: spacing.xl,
            },
          },
        },
        {
          name: 'appmaker/input',
          clientId: 'email',
          attributes: {
            label: 'Email',
            leftIcon: 'user',
            name: 'email',
            status: 'demiDark',
            autoCompleteType: 'email',
            __appmakerStylesClassName: 'inputCustom',
          },
        },
        {
          name: 'appmaker/ActionButton',
          clientId: 'submit-forgot-password',
          attributes: {
            appmakerAction: {
              action: 'FORGOT_PASSWORD',
            },
            content: 'Request Reset',
            baseSize: true,
            wholeContainerStyle: {
              backgroundColor: appSettings.getOption('primary_button_color'),
            },
            fontColor: appSettings.getOption('primary_button_text_color'),
            __appmakerStylesClassName: 'loginButton',
          },
          contextValues: true,
        },
      ],
    },
  ],
};
export default LoginPage;
