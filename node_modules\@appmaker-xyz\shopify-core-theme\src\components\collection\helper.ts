type LayoutConfig = {
  id: string;
  numColumns: number;
  spanEvery?: number;
  spanFirstItem?: boolean;
}

type Item = {
  ___appmakercustomblock?: {
    blockSpan: string;
  };
};

export function getSpanFromLayoutConfig({ item, index, layoutConfig }:
   { item: Item, index: number, layoutConfig: LayoutConfig }) {
  const { numColumns, spanEvery, spanFirstItem = true } = layoutConfig;
  if (
    item?.___appmakercustomblock &&
    item?.___appmakercustomblock?.blockSpan === 'full'
  ) {
    return numColumns;
  }
  const offsetIndex = spanFirstItem ? index : index + 1;
  if (spanEvery) {
    if (offsetIndex % spanEvery === 0) {
      return numColumns;
    }
  }
  return 1;
}