import { onEvent } from '@appmaker-xyz/core';
import AsyncStorage from '@react-native-community/async-storage';

import { useEffect, useState } from 'react';

type SearchQuery = string;

let SEARCH_HISTORY_KEY: string = 'search.history';

const initSave = async (): Promise<void> => {
  // Need to check if this onEvent function appends the function on every render of the search history component
  // i.e Does a new event listener get added every time the component is rendered?
  onEvent('product.search', async (key: string) => {
    try {
      const allValues = await AsyncStorage.getItem(SEARCH_HISTORY_KEY);
      if (!allValues) {
        let data = [key];
        const saveJson = JSON.stringify(data);
        AsyncStorage.setItem(SEARCH_HISTORY_KEY, saveJson);
      } else if (allValues) {
        let jsonValues = JSON.parse(allValues);
        let skipPush = false;
        if (Array.isArray(jsonValues)) {
          const indexValue = jsonValues.findIndex(
            (value) => value.toLowerCase() === key.toLowerCase(),
          );
          if (indexValue === -1 && jsonValues?.length >= 10) {
            jsonValues.pop();
          } else if (indexValue > -1) {
            const shiftedValue = jsonValues.splice?.(indexValue, 1)[0];
            jsonValues.unshift?.(shiftedValue);
            skipPush = true;
          }
          !skipPush && jsonValues.unshift(key);
          AsyncStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(jsonValues));
        }
      }
    } catch (e) {
      console.log('search-histry error = ', e);
    }
  });
};

const getAllSearchQuery = async (): Promise<string[]> => {
  try {
    const allQueries = await AsyncStorage.getItem(SEARCH_HISTORY_KEY);
    const allQueriesJSON = allQueries ? JSON.parse(allQueries) : null;
    return Array.isArray(allQueriesJSON) ? allQueriesJSON : [];
  } catch (e) {
    return [];
  }
};

const clearAllSearchQueries = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(SEARCH_HISTORY_KEY);
  } catch (e) {
    console.error(e);
  }
};

const removeSearchQuery = async (query: SearchQuery): Promise<void> => {
  try {
    const allQueries = await getAllSearchQuery();
    const updatedQueries = allQueries.filter((q) => q !== query);
    await AsyncStorage.setItem(
      SEARCH_HISTORY_KEY,
      JSON.stringify(updatedQueries),
    );
  } catch (e) {
    console.error(e);
  }
};

type UseRecentSearchReturn = {
  getQueries: () => Promise<string[]>;
  queries: SearchQuery[];
  clearQueries: () => Promise<void>;
  removeQuery: (query: SearchQuery) => Promise<void>;
};

const useRecentSearch = (): UseRecentSearchReturn => {
  const [recentQueries, setRecentQueries] = useState<SearchQuery[]>([]);
  const getQueries = async () => {
    const queries = await getAllSearchQuery();
    if (queries?.length > 0) {
      setRecentQueries(queries);
    }
  };

  const clearQueries = async () => {
    await clearAllSearchQueries();
    setRecentQueries([]); 
  };

  const removeQuery = async (query: SearchQuery) => {
    await removeSearchQuery(query);
    setRecentQueries(prev => prev.filter(q => q !== query)); 
  }

  useEffect(() => {
    initSave();
    getQueries();
  }, []);
  return {
    getQueries: getAllSearchQuery,
    queries: recentQueries,
    clearQueries,
    removeQuery,
  };
};

export { useRecentSearch };
