import React from 'react';
import { StyleSheet, Dimensions } from 'react-native';
import { Layout, AppTouchable, Badge, AppImage } from '@appmaker-xyz/ui';
import { BottomRight, ProductData, PriceData } from './Components';
import { InnerBlocks } from './InnerBlocks';
import {
  useProductListItem,
  useProductWishList,
  useProductVariations,
} from '@appmaker-xyz/shopify';
import ProductGridCTA from '../components/ProductGridCTA';

let deviceRatio =
  Dimensions.get('window').height / Dimensions.get('window').width;
const isTab = deviceRatio < 1.6;

const ProductGridThree = ({
  clientId,
  attributes,
  pageDispatch,
  onPress,
  onAction,
  blockData,
  innerBlocks = [],
  data = {},
  BlockItem,
  addCartButton,
  blocksViewItemIndex,
  ...props
}) => {
  const {
    attribute,
    wishList,
    appmakerAction,
    average_rating,
    size = '', // sm for small
    new_product,
    bestSeller,
    brandColor,
    __appmakerCustomStyles = {},
  } = attributes;

  const {
    imageUrl,
    title,
    salePrice,
    regularPrice,
    outOfStock,
    onSale,
    salePercentage,
    gridViewListing,
    numColumns,
    imageRatio,
    show_last_few_remaining,
    last_few_remaining_text,
    productType,
    isAddToCartLoading,
    openProduct,
    addToCart,
    count,
    onQuantityChange,
    updateCart,
    isDisablePurchase,
    preOrderEnabled
  } = useProductListItem({
    attributes,
    onAction,
    blockData,
    pageDispatch,
    blocksViewItemIndex,
  });
  const in_stock = !outOfStock;
  const containerStyle = [styles.container];
  const imageContainerStyle = [styles.imageContainer];

  const { variant } = useProductVariations({
    attributes,
    onAction,
    blockData,
    pageDispatch,
    ...props,
  });
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  let attributeView = null;
  if (attribute) {
    attributeView = attribute.map((value) => {
      return (
        <Layout style={styles.attributesListing}>
          <Badge
            text={value.label}
            status="demiDark"
            style={styles.attributeBadgeStyle}
          />
        </Layout>
      );
    });
  }
  if (size === 'sm') {
    imageContainerStyle.push({
      width: 100,
      height: imageRatio ? imageRatio * 80 : 100,
    });
  }

  if (gridViewListing === true) {
    containerStyle.push({ marginLeft: 0 });
    imageContainerStyle.push({ width: '100%' });
  }
  const wholeContainerStyle = gridViewListing
    ? {
        width: `${100 / numColumns}%`,
        padding: 4,
      }
    : {
        width: isTab ? 300 : 196,
        marginLeft: 12,
      };

  if (imageRatio) {
    imageContainerStyle.push({
      aspectRatio: 1 / imageRatio,
    });
  }

  const { toggleWishList, isSaved } = useProductWishList({
    onAction,
    blockData,
  });

  return (
    <AppTouchable
      onPress={openProduct}
      style={wholeContainerStyle}
      clientId={clientId}>
      <Layout style={[styles.container, __appmakerCustomStyles?.container]}>
        <Layout style={imageContainerStyle}>
          <AppImage
            uri={imageUrl}
            style={[styles.productImage, __appmakerCustomStyles?.product_image]}
          />
          <InnerBlocks
            blockData={blockData}
            BlockItem={BlockItem}
            innerBlocks={innerBlocks}
            styles={styles}
            average_rating={average_rating}
            attributes={attributes}
            bestSeller={bestSeller}
            __appmakerCustomStyles={__appmakerCustomStyles}
          />
          <BottomRight
            onAction={onAction}
            blockData={blockData}
            wishList={wishList}
            md={16}
            styles={styles}
            __appmakerCustomStyles={__appmakerCustomStyles}
          />
        </Layout>
        <Layout style={styles.contentContainer}>
          <ProductData
            title={title}
            in_stock={!outOfStock}
            new_product={new_product}
            show_last_few_remaining={show_last_few_remaining}
            last_few_remaining_text={last_few_remaining_text}
            brandColor={brandColor}
            productTitle={styles.productTitle}
            __appmakerCustomStyles={__appmakerCustomStyles}
          />
          <PriceData
            salePrice={salePrice}
            regularPrice={regularPrice}
            onSale={onSale}
            salePercentage={salePercentage}
            brandColor={brandColor}
            styles={styles}
            __appmakerCustomStyles={__appmakerCustomStyles}
          />
        </Layout>
        {isDisablePurchase ? null : (
          <ProductGridCTA
            addCartButtonType={addCartButton}
            productType={productType}
            inStock={in_stock}
            variant={variant}
            isAddToCartLoading={isAddToCartLoading}
            toggleWishList={toggleWishList}
            isSaved={isSaved}
            onQuantityChange={onQuantityChange}
            updateCart={updateCart}
            openProduct={openProduct}
            addToCart={addToCart}
            buttonStyle={styles.buttonStyle}
            preOrderEnabled = {preOrderEnabled}
          />
        )}
      </Layout>
    </AppTouchable>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: 12,
    position: 'relative',
  },
  imageContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 6,

    position: 'relative',
  },
  productImage: {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
  },
  overlayContainer: {
    position: 'absolute',
    zIndex: 10,
    flexDirection: 'row',
    bottom: 12,
    right: 12,
  },
  addToCart: {
    backgroundColor: '#ffffff',
    padding: 8,
    borderRadius: 18,
    marginLeft: 10,
  },
  wishListIcon: {
    backgroundColor: '#ffffff',
    padding: 8,
    borderRadius: 18,
  },
  shadow: {
    shadowColor: '#4F4F4F',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  contentContainer: {
    marginTop: 4,
    flex: 1,
    position: 'relative',
  },
  productTitle: {
    flex: 1,
    marginBottom: 4,
  },
  priceDataContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
  },
  offerPrice: {
    textDecorationLine: 'line-through',
    marginHorizontal: 4,
  },
  saleBadgeStyle: {
    marginRight: 4,
  },
  stockOutBadgeStyle: {},
  topLeftBlocks: {
    flexDirection: 'row',
    position: 'absolute',
    flexWrap: 'wrap',
    left: 4,
    top: 4,
  },
  extraContentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonStyle: {
    marginTop: 8,
    borderRadius: 0,
  },
});

export default ProductGridThree;
