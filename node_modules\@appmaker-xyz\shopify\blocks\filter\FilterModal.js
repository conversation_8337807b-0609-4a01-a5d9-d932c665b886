import {
  View,
  StyleSheet,
  Pressable,
  FlatList,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import Icon from 'react-native-vector-icons/Feather';
import {
  useFilterOptions,
  useCollectionFilter,
  useFilterModal,
  useSelectedFiltersCount,
  useIsAnyFilterSelected,
} from '../../hooks/filter/userCollectionFilter';
import PriceRange from './components/PriceRange';
import FilterListOption from './components/FilterListOption';
import FilterColorSwatch from './components/FilterColorSwatch';
import { ThemeText } from '@appmaker-xyz/ui';
import { appmaker, testProps, getExtensionConfig } from '@appmaker-xyz/core';

const HiddenComponent = (data) => {
  const { item } = data;
  const { selectFilter, isFilterSelected } = useFilterOptions();
  if (item?.defaultValue && !isFilterSelected(item?.id, item?.defaultValue)) {
    let filterValue = item?.values?.find(
      (option) => option?.id === item.defaultValue,
    );
    filterValue = {
      ...filterValue,
      hidden: true,
    };
    filterValue?.id && selectFilter(item.id, filterValue.id, filterValue);
  }
  return null;
};

const FilterOptions = {
  LIST: FilterListOption,
  PRICE_RANGE: PriceRange,
  COLOR_SWATCH: FilterColorSwatch,
  HIDDEN: HiddenComponent,
  BOOLEAN: FilterListOption,
};

function FilterItemContent({ item, extensionStyles }) {
  const FilterOptionsComponents = React.useMemo(() => {
    return appmaker.applyFilters('shopify-filter-components', FilterOptions);
  }, []);
  const { selectFilter, removeFilter, priceRange } = useFilterOptions();
  const { label, type } = item;
  const FilterComponent = FilterOptionsComponents[type];
  return (
    <View style={styles.itemContainer}>
      {FilterComponent ? (
        <FilterComponent
          priceRange={priceRange}
          item={item}
          selectFilter={selectFilter}
          removeFilter={removeFilter}
          filterKey={item.id}
          extensionStyles={extensionStyles}
        />
      ) : null}
    </View>
  );
}

function ModalHeader(props) {
  const { clearSelectedFilters } = useFilterOptions();
  const isFilterSelected = useIsAnyFilterSelected();
  const { extensionStyles, onClose, loading } = props;

  return (
    <View style={[styles.topBar, extensionStyles?.filterModalHeaderContainer]}>
      <Pressable
        {...testProps(`filter-close`)}
        style={[styles.back, extensionStyles?.filterModalCloseButton]}
        onPress={onClose}>
        <Icon
          name="x"
          size={20}
          color="#000"
          style={extensionStyles?.filterModalCloseButtonIcon}
        />
      </Pressable>
      <View
        style={[
          styles.filtersHeadTextContainer,
          extensionStyles?.filterModalHeaderTextContainer,
        ]}>
        <ThemeText
          fontFamily="medium"
          size="md"
          style={[
            styles.filtersHeadText,
            extensionStyles?.filterModalHeaderText,
          ]}>
          Filters
        </ThemeText>
        {loading ? <ActivityIndicator size={16} color="#1B1B1B" /> : null}
      </View>
      {isFilterSelected ? (
        <Pressable
          {...testProps(`filter-clear-all`)}
          onPress={clearSelectedFilters}
          style={[
            styles.removeAllButton,
            extensionStyles?.filterModalHeaderClearButtonContainer,
          ]}>
          <ThemeText
            style={[
              styles.clearAllText,
              extensionStyles?.filterModalHeaderClearButtonText,
            ]}>
            Clear All
          </ThemeText>
        </Pressable>
      ) : null}
    </View>
  );
}

function FilterItem({
  item,
  selectTab,
  isCurrentTab,
  testId,
  extensionStyles,
}) {
  const filtersCount = useSelectedFiltersCount(item.id);
  const currentTabStyle = isCurrentTab
    ? [
        styles.currentTab,
        extensionStyles?.filterModalListSideActiveTabContainer,
      ]
    : extensionStyles?.filterModalListSideTabContainer;
  const label = item?.upper_case ? item.label.toUpperCase() : item.label;
  const isDisabled = item?.empty === true;
  let textColor = {
    color: 'black',
  };
  if (isDisabled) {
    textColor = {
      color: '#A1A1AA',
    };
  }
  return (
    <Pressable {...testProps(testId)} onPress={() => selectTab(item.id)}>
      <View style={[styles.tabBarStyle, currentTabStyle]}>
        <ThemeText
          style={[
            styles.tabText,
            textColor,
            extensionStyles?.filterModalListSideTabText,
          ]}>
          {label}
        </ThemeText>
        {filtersCount > 0 ? (
          <ThemeText
            style={[
              styles.activeCountText,
              extensionStyles?.filterModalListSideTabCountText,
            ]}>
            {filtersCount}
          </ThemeText>
        ) : null}
      </View>
    </Pressable>
  );
}

function FilterContent({
  avilableFilters,
  extensionStyles,
  filterInitalTabName,
}) {
  const activeFilter = avilableFilters?.find(
    (filter) => filter?.label === filterInitalTabName,
  );
  const fallbackSelectedFilterTabId =
    activeFilter?.id ||
    avilableFilters?.find((filter) => filter?.type !== 'HIDDEN')?.id ||
    avilableFilters[0]?.id;
  const [currentTabId, selectTabId] = React.useState(
    fallbackSelectedFilterTabId,
  );
  const tabIndexMap = React.useMemo(() => {
    const map = {};
    avilableFilters.forEach((item, index) => {
      map[item.id] = index;
    });
    return map;
  }, [avilableFilters]);
  let counts = {};
  function renderItem({ item, index }) {
    if (typeof counts?.[item?.type] === 'number') {
      counts[item?.type] += 1;
    } else {
      counts[item?.type] = 0;
    }
    if (item?.type === 'HIDDEN') {
      return <HiddenComponent item={item} />;
    }
    return (
      <FilterItem
        testId={`filter-item-${item?.type?.toLowerCase()}-${
          counts?.[item?.type]
        }`}
        item={item}
        selectTab={selectTabId}
        isCurrentTab={item?.id === currentTabId}
        extensionStyles={extensionStyles}
      />
    );
  }
  const activeFilters = avilableFilters[tabIndexMap[currentTabId]];
  return (
    <View
      style={[styles.filterView, extensionStyles?.filterModalListContainer]}>
      <View
        style={[
          styles.filterListingView,
          extensionStyles?.filterModalListSideContainer,
        ]}>
        <FlatList
          data={avilableFilters}
          renderItem={renderItem}
          contentContainerStyle={[
            styles.containerStyle,
            extensionStyles?.filterModalListSideContentContainer,
          ]}
          keyExtractor={(item, index) => index.toString()}
        />
      </View>
      <View
        style={[
          styles.filterContentView,
          extensionStyles?.filterModalListMainContentContainer,
        ]}>
        {activeFilters ? (
          <FilterItemContent
            item={activeFilters}
            extensionStyles={extensionStyles}
          />
        ) : null}
      </View>
    </View>
  );
}

export default function FilterModal() {
  const { avilableFilters, applyFilters, isNextFiltersLoading } =
    useCollectionFilter();
  const { isFilterModalShown, closeFilterModal, filterModalInitialTabName } =
    useFilterModal();
  const extensionStyles = getExtensionConfig?.(
    'shopify',
    'filterModalGroup',
    {},
  );

  return (
    <View
      style={[
        styles.mainContainer,
        extensionStyles?.filterModalWrapperContainer,
      ]}>
      <Modal
        isVisible={isFilterModalShown}
        style={[styles.modal, extensionStyles?.filterModal]}
        onDismiss={closeFilterModal}
        onRequestClose={closeFilterModal}>
        <SafeAreaView
          style={[
            styles.modalContainer,
            extensionStyles?.filterModalContainer,
          ]}>
          <ModalHeader
            onClose={closeFilterModal}
            loading={isNextFiltersLoading}
            extensionStyles={extensionStyles}
          />
          {avilableFilters?.length > 0 ? (
            <>
              <FilterContent
                filterInitalTabName={filterModalInitialTabName}
                avilableFilters={avilableFilters}
                extensionStyles={extensionStyles}
              />
              <View
                style={[
                  styles.applyButtonContainer,
                  extensionStyles?.filterModalApplyButtonWrapperContainer,
                ]}>
                <Pressable
                  {...testProps(`filter-apply`)}
                  onPress={() => {
                    applyFilters();
                    closeFilterModal();
                  }}
                  style={[
                    styles.applyButton,
                    extensionStyles?.filterModalApplyButtonContainer,
                  ]}>
                  <ThemeText
                    style={[
                      styles.applyButtonText,
                      extensionStyles?.filterModalApplyButtonText,
                    ]}>
                    Apply Filters
                  </ThemeText>
                </Pressable>
              </View>
            </>
          ) : null}
        </SafeAreaView>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    // flex: 1,
    backgroundColor: 'red',
    margin: 0,
    padding: 0,
  },
  modal: {
    margin: 0,
    backgroundColor: '#f2f2f2',
  },
  modalContainer: {
    backgroundColor: '#fff',
    flex: 1,
    margin: 0,
    padding: 0,
    width: '100%',
  },
  topBar: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderBottomColor: '#e2e2e2',
    borderBottomWidth: 0.5,
  },
  back: {
    padding: 6,
    backgroundColor: '#f2f2f2',
    borderRadius: 18,
    marginRight: 12,
  },
  removeAllButton: {
    paddingVertical: 6,
  },
  clearAllText: {
    color: '#FF0000',
  },
  filtersHeadTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexGrow: 1,
  },
  filtersHeadText: {
    color: '#1B1B1B',
    marginRight: 12,
  },
  tabBarStyle: {
    flexGrow: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 10,
    marginVertical: 1,
  },
  currentTab: {
    backgroundColor: '#fff',
  },
  tabText: { flexShrink: 1 },
  activeCountText: {
    color: '#64748B',
    padding: 2,
    borderRadius: 4,
    marginLeft: 10,
    fontSize: 12,
  },
  filterView: {
    flex: 1,
    backgroundColor: 'white',
    flexDirection: 'row',
  },
  filterListingView: {
    flex: 2,
    backgroundColor: '#F8F6F2',
  },
  filterContentView: {
    flex: 3,
  },
  applyButtonContainer: {
    padding: 10,
    backgroundColor: '#fff',
    borderTopColor: '#E9EDF1',
    borderTopWidth: 1,
  },
  applyButton: {
    backgroundColor: '#000',
    paddingVertical: 12,
    borderRadius: 8,
  },
  applyButtonText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
});
