import { appmaker } from '@appmaker-xyz/core';

export function enableCustomEvents() {
  appmaker.addFilter(
    'moengageEventData',
    'custom-events',
    ({ eventName, params, eventObject }) => {
      switch (eventName) {
        case 'Checkout Started':
          delete params['Product ID'];
          params['Product IDs'] = eventObject;
          break;
        case 'Product Viewed':
          params['Variant ID'] =
            eventObject?.product?.variants?.edges[0].node.id;
          params['Product Handle'] = eventObject?.product?.handle;
          break;
        case 'Removed from Cart':
          params['ProductType'] = eventObject?.product?.productType;
          break;
        case 'Order placed':
          params['Variation Title'] =
            eventObject?.cart?.lineItems?.edges[0].node?.variant?.product?.title;
          break;
        default:
          break;
      }
      return {
        eventName,
        params,
      };
    },
  );
}
