import React, { useEffect } from 'react';
import { StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import { AppTouchable, AppImage, Layout, ThemeText } from '@appmaker-xyz/ui';
import { SwiperFlatList } from 'react-native-swiper-flatlist';
import { AppmakerVideo } from '@appmaker-xyz/react-native';
import { imageSizeHelper } from '@appmaker-xyz/core';
import { getExtensionAsBoolean } from '@appmaker-xyz/core';
import { testProps } from '@appmaker-xyz/core';

const screenWidth = Dimensions.get('screen').width;
// const supportedTypes = ['VIDEO', 'IMAGE'];
const MediaItem = ({
  item,
  showLoadingIndicator,
  onPressHandle,
  resizeMode,
  title,
  index,
  activeIndex,
}) => {
  const videoRef = React.useRef(null);
  if (item?.node?.mediaContentType === 'VIDEO') {
    let source = item?.node?.sources?.[item?.node?.sources?.length - 1];
    let vHeight = (source?.height / source?.width) * screenWidth;
    const placeholder = imageSizeHelper(item.node?.previewImage?.originalSrc, {
      type: 'product-detail-image-swiper',
    });
    // Play only when active in swiper
    const shouldPlay = !(activeIndex === index);
    return (
      <TouchableOpacity onPress={() => {}}>
        <AppmakerVideo
          ref={videoRef}
          source={{ uri: `${source?.url}` }}
          minBufferMs={'10000'}
          resizeMode={resizeMode}
          controls={false}
          paused={shouldPlay}
          repeat={true}
          {...(placeholder && { poster: placeholder })}
          style={{ ...styles.videoStyle, height: vHeight }}
        />
      </TouchableOpacity>
    );
  } else if (item?.node?.mediaContentType === 'IMAGE') {
    const uri = imageSizeHelper(item.node?.image?.originalSrc, {
      type: 'product-detail-image-swiper',
    });
    return (
      <AppTouchable style={styles.imageWrapper} onPress={onPressHandle}>
        <AppImage
          uri={uri}
          style={styles.productImage}
          resizeMode={resizeMode}
          fastImage={false}
        />
        {/* <Text>{uri}</Text> */}
        {title && (
          <Layout style={styles.contentContainer}>
            <ThemeText
              size="lg"
              color="#FFFFFF"
              style={styles.title}
              numberOfLines={3}>
              {item.title}
            </ThemeText>
            <ThemeText color="#E9EDF1" size="sm">
              {item.authorName} | {item.timeStamp}
            </ThemeText>
          </Layout>
        )}
      </AppTouchable>
    );
  }
  return null;
};

const ImageItem = ({
  item,
  showLoadingIndicator,
  onPressHandle,
  resizeMode,
  title,
}) => {
  const uri = typeof item === 'string' ? item : item.thumbnail;
  return (
    <AppTouchable style={styles.imageWrapper} onPress={onPressHandle}>
      <AppImage
        uri={uri}
        style={styles.productImage}
        resizeMode={resizeMode}
        fastImage={false}
      />
      {/* <Text>{uri}</Text> */}
      {title && (
        <Layout style={styles.contentContainer}>
          <ThemeText
            size="lg"
            color="#FFFFFF"
            style={styles.title}
            numberOfLines={3}>
            {item.title}
          </ThemeText>
          <ThemeText color="#E9EDF1" size="sm">
            {item.authorName} | {item.timeStamp}
          </ThemeText>
        </Layout>
      )}
    </AppTouchable>
  );
};

const getItemLayout = (data, index) => ({
  length: Dimensions.get('window').width,
  offset: Dimensions.get('window').width * index,
  index,
});

const NewSwiper = ({ attributes, openImage, swiperRef }) => {
  const [active, setActive] = React.useState(0);
  // const isMedia = getExtensionAsBoolean?.(
  //   'shopify',
  //   'enable_pdp_video_media',
  //   false,
  // );
  const {
    imageList,
    media,
    autoplay,
    resizeMode = 'contain',
    title,
    goToIndexNeeded = true,
    appmakerAction,
    showsButtons,
    showLoadingIndicator,
    customDotColor,
    centerPagination = false,
    smallPagination = false,
    isMedia = false,
  } = attributes;

  // const swiperRef = useRef();
  useEffect(() => {
    if (swiperRef.current && imageList.length > 0) {
      setTimeout(() => {
        if (attributes.index - 1 >= 0) {
          try {
            if (goToIndexNeeded) {
              swiperRef.current.scrollToIndex({ index: attributes.index - 1 });
            } else {
              swiperRef.current.scrollToIndex({ index: 0 });
            }
          } catch (error) {
            console.log(error);
          }
        }
      }, 100);
    }
  }, [attributes.index]);

  const activeDotStyles = [styles.activeDotStyle];
  const dotStyles = [styles.dotStyle];
  const dotContainerStyles = [styles.dotContainer];
  if (customDotColor) {
    activeDotStyles.push({
      backgroundColor: customDotColor,
    });
  }
  if (smallPagination) {
    dotContainerStyles.push({
      bottom: -15,
    });
    activeDotStyles.push({
      width: 12,
      height: 6,
    });
    dotStyles.push({
      width: 6,
      height: 6,
    });
  }
  if (imageList.length >= 24) {
    dotContainerStyles.push(styles.maxWidth);
  }
  if (centerPagination) {
    dotContainerStyles.push({ left: null });
  }
  let filteredMedia = media;

  const isMediaView = isMedia && filteredMedia?.length > 0;
  return (
    <Layout style={styles.container}>
      <SwiperFlatList
        {...testProps('product-image-swiper')}
        getItemLayout={getItemLayout}
        ref={swiperRef}
        showPagination
        removeClippedSubviews={isMediaView ? false : true}
        paginationStyle={dotContainerStyles}
        paginationStyleItem={dotStyles}
        onChangeIndex={({ index }) => {
          setActive(index);
        }}
        paginationStyleItemInactive={styles.inactiveDotStyle}
        paginationStyleItemActive={activeDotStyles}
        data={isMediaView ? filteredMedia : imageList}
        renderItem={({ item, index }) => (
          <Layout style={stylesRoot.child}>
            {isMediaView ? (
              <MediaItem
                // key={key}
                item={item}
                index={index}
                activeIndex={active}
                showLoadingIndicator={showLoadingIndicator}
                onPressHandle={openImage}
                title={title}
                resizeMode={resizeMode}
              />
            ) : (
              <ImageItem
                // key={key}
                item={item}
                showLoadingIndicator={showLoadingIndicator}
                onPressHandle={openImage}
                title={title}
                resizeMode={resizeMode}
              />
            )}
          </Layout>
        )}
      />
    </Layout>
  );
};

const { width } = Dimensions.get('window');

const stylesRoot = StyleSheet.create({
  container: { flex: 1, backgroundColor: 'white' },
  child: { width, justifyContent: 'center' },
  text: { fontSize: width * 0.1, textAlign: 'center' },
});

const styles = StyleSheet.create({
  imageWrapper: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    position: 'relative',
  },
  productImage: {
    width: '100%',
    height: '100%',
  },
  inactiveDotStyle: {
    backgroundColor: '#A9AEB7',
  },
  activeDotStyle: {
    backgroundColor: '#212121',
    width: 16,
    height: 8,
    borderRadius: 4,
  },
  dotStyle: {
    backgroundColor: '#E9EDF1',
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: -4,
  },
  dotContainer: {
    bottom: 4,
    left: 4,
  },
  maxWidth: {
    maxWidth: '90%',
    overflow: 'hidden',
  },
  contentContainer: {
    position: 'absolute',
    backgroundColor: '#1B1B1B50',
    width: '100%',
    height: '100%',
    justifyContent: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 48,
  },
  title: {
    textAlign: 'left',
    textShadowColor: '#4F4F4F50',
    textShadowOffset: {
      width: 0,
      height: 1,
    },
    textShadowRadius: 2,
  },
  videoStyle: {
    height: 400,
    width: screenWidth,
  },
});

export default NewSwiper;
