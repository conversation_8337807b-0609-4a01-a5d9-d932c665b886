import getConditionalCartSum from '../getConditionalCartSum';
import {
  cartData,
  cartDataWithFreeGiftInCart,
  freeGiftSettings,
} from '../../../sampleData';

describe('getConditionalCartSum', () => {
  const input = {
    lineItemsToAdd: [],
    currentCart: cartData,
    lineItemsToUpdate: [],
  };

  const collections = [];

  const dependencies = {
    log: console.log,
  };

  const discountCodeTypesToExclude = [];

  it('should return the correct sum and quantity when cartSumCondition is "all"', () => {
    const result = getConditionalCartSum({
      input,
      cartSumCondition: 'all',
      collections,
      dependencies,
      discountCodeTypesToExclude,
    });

    console.log('result', result);

    const { sum, qty } = result;

    expect(sum).toBe(67.47);
    expect(qty).toBe(3);
  });

  // it('should return the correct sum and quantity when cartSumCondition is "exclude_collection"', () => {
  //   const result = getConditionalCartSum({
  //     input,
  //     cartSumCondition: 'exclude_collection',
  //     collections,
  //     dependencies,
  //     discountCodeTypes,
  //   });

  //   expect(result.sum).toBe(10);
  //   expect(result.qty).toBe(2);
  // });
});
