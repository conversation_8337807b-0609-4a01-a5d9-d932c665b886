import { useState } from 'react';
import { useAppStorage } from '@appmaker-xyz/core';
import { isEmpty } from 'lodash';
import { getExtensionAsBoolean } from '@appmaker-xyz/core';

export function useUser({ onAction } = {}) {
  const [loginLoadingStatus, setLoginLoadingStatus] = useState(false);
  const [registerLoadingStatus, setRegisterLoadingStatus] = useState(false);
  const [resetPasswordLoadingStatus, setResetPasswordLoadingStatus] = useState(false);

  const user = useAppStorage((state) => state?.user);
  const showTermsAndService = getExtensionAsBoolean?.(
    'shopify',
    'show_accept_terms_in_register',
    false,
  );
  const showPromotionalOptions = getExtensionAsBoolean?.(
    'shopify',
    'show_accept_marketing_in_register',
    false,
  );
  let useActions = {
    logout: () => {},
    resetPassword: async (email) => {
      setResetPasswordLoadingStatus(true);
      const resp = await onAction({
        action: 'FORGOT_PASSWORD',
        params: { email },
      });
      setResetPasswordLoadingStatus(false);
      return resp;
    },
    update: () => {},
    updatePassword: () => {},
  };
  async function register(formData) {
    setRegisterLoadingStatus(true);
    const resp = await onAction({
      action: 'REGISTER_USER',
      customFormData: formData,
    });
    setRegisterLoadingStatus(false);
    return resp;
  }
  async function login(username, password) {
    setLoginLoadingStatus(true);
    const resp = await onAction({
      action: 'LOGIN_USER',
      params: {
        username,
        password,
      },
    });
    setLoginLoadingStatus(false);
    return resp;
  }

  async function loginViaGoogle() {
    await onAction({
      action: 'LOGIN_USER_VIA_GOOGLE',
    });
  }
  async function loginViaFacebook() {
    await onAction({
      action: 'LOGIN_USER_VIA_FACEBOOK',
    });
  }
  const openLoginPage = () => {
    onAction({ action: 'OPEN_LOGIN_PAGE' });
  };
  const openRegisterPage = () => {
    onAction({ action: 'OPEN_REGISTER' });
  };
  return {
    user,
    isLoggedin: !isEmpty(user),
    register,
    login,
    loginViaGoogle,
    loginViaFacebook,
    loginLoadingStatus,
    registerLoadingStatus,
    resetPasswordLoadingStatus,
    resetLoadingStatus: resetPasswordLoadingStatus,
    isShowTermsAndService: showTermsAndService,
    isShowPromotionOption: showPromotionalOptions,
    openLoginPage,
    openRegisterPage,
    ...useActions,
  };
}
