{"version": 3, "names": ["_debug", "data", "require", "_path", "_gensync", "_async", "_moduleTypes", "_url", "_importMetaResolve", "asyncGeneratorStep", "gen", "resolve", "reject", "_next", "_throw", "key", "arg", "info", "value", "error", "done", "Promise", "then", "_asyncToGenerator", "fn", "self", "args", "arguments", "apply", "err", "undefined", "debug", "buildDebug", "EXACT_RE", "BABEL_PLUGIN_PREFIX_RE", "BABEL_PRESET_PREFIX_RE", "BABEL_PLUGIN_ORG_RE", "BABEL_PRESET_ORG_RE", "OTHER_PLUGIN_ORG_RE", "OTHER_PRESET_ORG_RE", "OTHER_ORG_DEFAULT_RE", "resolvePlugin", "name", "dirname", "resolveStandardizedName", "resolvePreset", "loadPlugin", "filepath", "requireModule", "loadPreset", "standardizeName", "type", "path", "isAbsolute", "isPreset", "replace", "resolveAlternativesHelper", "standardizedName", "code", "message", "oppositeType", "tryRequireResolve", "id", "paths", "v", "w", "split", "process", "versions", "node", "r", "b", "M", "f", "_findPath", "_nodeModulePaths", "concat", "Error", "tryImportMetaResolve", "_x", "_x2", "_tryImportMetaResolve", "options", "importMetaResolve", "resolveStandardizedNameForRequire", "it", "res", "next", "resolveStandardizedNameForImport", "_x3", "_x4", "_x5", "_resolveStandardizedNameForImport", "parentUrl", "pathToFileURL", "join", "href", "fileURLToPath", "gens<PERSON>", "sync", "cwd", "async", "supportsESM", "e", "e2", "LOADING_MODULES", "Set", "isAsync", "has", "add", "loadCodeDefault", "delete"], "sources": ["../../../src/config/files/plugins.ts"], "sourcesContent": ["/**\n * This file handles all logic for converting string-based configuration references into loaded objects.\n */\n\nimport buildDebug from \"debug\";\nimport path from \"path\";\nimport gensync, { type Handler } from \"gensync\";\nimport { isAsync } from \"../../gensync-utils/async\";\nimport loadCodeDefault, { supportsESM } from \"./module-types\";\nimport { fileURLToPath, pathToFileURL } from \"url\";\n\nimport importMetaResolve from \"./import-meta-resolve\";\n\nimport { createRequire } from \"module\";\nconst require = createRequire(import.meta.url);\n\nconst debug = buildDebug(\"babel:config:loading:files:plugins\");\n\nconst EXACT_RE = /^module:/;\nconst BABEL_PLUGIN_PREFIX_RE = /^(?!@|module:|[^/]+\\/|babel-plugin-)/;\nconst BABEL_PRESET_PREFIX_RE = /^(?!@|module:|[^/]+\\/|babel-preset-)/;\nconst BABEL_PLUGIN_ORG_RE = /^(@babel\\/)(?!plugin-|[^/]+\\/)/;\nconst BABEL_PRESET_ORG_RE = /^(@babel\\/)(?!preset-|[^/]+\\/)/;\nconst OTHER_PLUGIN_ORG_RE =\n  /^(@(?!babel\\/)[^/]+\\/)(?![^/]*babel-plugin(?:-|\\/|$)|[^/]+\\/)/;\nconst OTHER_PRESET_ORG_RE =\n  /^(@(?!babel\\/)[^/]+\\/)(?![^/]*babel-preset(?:-|\\/|$)|[^/]+\\/)/;\nconst OTHER_ORG_DEFAULT_RE = /^(@(?!babel$)[^/]+)$/;\n\nexport function* resolvePlugin(name: string, dirname: string): Handler<string> {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return yield* resolveStandardizedName(\"plugin\", name, dirname);\n}\n\nexport function* resolvePreset(name: string, dirname: string): Handler<string> {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return yield* resolveStandardizedName(\"preset\", name, dirname);\n}\n\nexport function* loadPlugin(\n  name: string,\n  dirname: string,\n): Handler<{ filepath: string; value: unknown }> {\n  const filepath = yield* resolvePlugin(name, dirname);\n\n  const value = yield* requireModule(\"plugin\", filepath);\n  debug(\"Loaded plugin %o from %o.\", name, dirname);\n\n  return { filepath, value };\n}\n\nexport function* loadPreset(\n  name: string,\n  dirname: string,\n): Handler<{ filepath: string; value: unknown }> {\n  const filepath = yield* resolvePreset(name, dirname);\n\n  const value = yield* requireModule(\"preset\", filepath);\n\n  debug(\"Loaded preset %o from %o.\", name, dirname);\n\n  return { filepath, value };\n}\n\nfunction standardizeName(type: \"plugin\" | \"preset\", name: string) {\n  // Let absolute and relative paths through.\n  if (path.isAbsolute(name)) return name;\n\n  const isPreset = type === \"preset\";\n\n  return (\n    name\n      // foo -> babel-preset-foo\n      .replace(\n        isPreset ? BABEL_PRESET_PREFIX_RE : BABEL_PLUGIN_PREFIX_RE,\n        `babel-${type}-`,\n      )\n      // @babel/es2015 -> @babel/preset-es2015\n      .replace(\n        isPreset ? BABEL_PRESET_ORG_RE : BABEL_PLUGIN_ORG_RE,\n        `$1${type}-`,\n      )\n      // @foo/mypreset -> @foo/babel-preset-mypreset\n      .replace(\n        isPreset ? OTHER_PRESET_ORG_RE : OTHER_PLUGIN_ORG_RE,\n        `$1babel-${type}-`,\n      )\n      // @foo -> @foo/babel-preset\n      .replace(OTHER_ORG_DEFAULT_RE, `$1/babel-${type}`)\n      // module:mypreset -> mypreset\n      .replace(EXACT_RE, \"\")\n  );\n}\n\ntype Result<T> = { error: Error; value: null } | { error: null; value: T };\n\nfunction* resolveAlternativesHelper(\n  type: \"plugin\" | \"preset\",\n  name: string,\n): Iterator<string, string, Result<string>> {\n  const standardizedName = standardizeName(type, name);\n  const { error, value } = yield standardizedName;\n  if (!error) return value;\n\n  // @ts-expect-error code may not index error\n  if (error.code !== \"MODULE_NOT_FOUND\") throw error;\n\n  if (standardizedName !== name && !(yield name).error) {\n    error.message += `\\n- If you want to resolve \"${name}\", use \"module:${name}\"`;\n  }\n\n  if (!(yield standardizeName(type, \"@babel/\" + name)).error) {\n    error.message += `\\n- Did you mean \"@babel/${name}\"?`;\n  }\n\n  const oppositeType = type === \"preset\" ? \"plugin\" : \"preset\";\n  if (!(yield standardizeName(oppositeType, name)).error) {\n    error.message += `\\n- Did you accidentally pass a ${oppositeType} as a ${type}?`;\n  }\n\n  throw error;\n}\n\nfunction tryRequireResolve(\n  id: Parameters<RequireResolve>[0],\n  { paths: [dirname] }: Parameters<RequireResolve>[1],\n): Result<string> {\n  try {\n    return { error: null, value: require.resolve(id, { paths: [dirname] }) };\n  } catch (error) {\n    return { error, value: null };\n  }\n}\n\nasync function tryImportMetaResolve(\n  id: Parameters<ImportMeta[\"resolve\"]>[0],\n  options: Parameters<ImportMeta[\"resolve\"]>[1],\n): Promise<Result<string>> {\n  try {\n    return { error: null, value: await importMetaResolve(id, options) };\n  } catch (error) {\n    return { error, value: null };\n  }\n}\n\nfunction resolveStandardizedNameForRequire(\n  type: \"plugin\" | \"preset\",\n  name: string,\n  dirname: string,\n) {\n  const it = resolveAlternativesHelper(type, name);\n  let res = it.next();\n  while (!res.done) {\n    res = it.next(tryRequireResolve(res.value, { paths: [dirname] }));\n  }\n  return res.value;\n}\nasync function resolveStandardizedNameForImport(\n  type: \"plugin\" | \"preset\",\n  name: string,\n  dirname: string,\n) {\n  const parentUrl = pathToFileURL(\n    path.join(dirname, \"./babel-virtual-resolve-base.js\"),\n  ).href;\n\n  const it = resolveAlternativesHelper(type, name);\n  let res = it.next();\n  while (!res.done) {\n    res = it.next(await tryImportMetaResolve(res.value, parentUrl));\n  }\n  return fileURLToPath(res.value);\n}\n\nconst resolveStandardizedName = gensync<\n  [type: \"plugin\" | \"preset\", name: string, dirname?: string],\n  string\n>({\n  sync(type, name, dirname = process.cwd()) {\n    return resolveStandardizedNameForRequire(type, name, dirname);\n  },\n  async async(type, name, dirname = process.cwd()) {\n    if (!supportsESM) {\n      return resolveStandardizedNameForRequire(type, name, dirname);\n    }\n\n    try {\n      return await resolveStandardizedNameForImport(type, name, dirname);\n    } catch (e) {\n      try {\n        return resolveStandardizedNameForRequire(type, name, dirname);\n      } catch (e2) {\n        if (e.type === \"MODULE_NOT_FOUND\") throw e;\n        if (e2.type === \"MODULE_NOT_FOUND\") throw e2;\n        throw e;\n      }\n    }\n  },\n});\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var LOADING_MODULES = new Set();\n}\nfunction* requireModule(type: string, name: string): Handler<unknown> {\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!(yield* isAsync()) && LOADING_MODULES.has(name)) {\n      throw new Error(\n        `Reentrant ${type} detected trying to load \"${name}\". This module is not ignored ` +\n          \"and is trying to load itself while compiling itself, leading to a dependency cycle. \" +\n          'We recommend adding it to your \"ignore\" list in your babelrc, or to a .babelignore.',\n      );\n    }\n  }\n\n  try {\n    if (!process.env.BABEL_8_BREAKING) {\n      LOADING_MODULES.add(name);\n    }\n    return yield* loadCodeDefault(\n      name,\n      `You appear to be using a native ECMAScript module ${type}, ` +\n        \"which is only supported when running Babel asynchronously.\",\n      // For backward compatibility, we need to support malformed presets\n      // defined as separate named exports rather than a single default\n      // export.\n      // See packages/babel-core/test/fixtures/option-manager/presets/es2015_named.js\n      true,\n    );\n  } catch (err) {\n    err.message = `[BABEL]: ${err.message} (While processing: ${name})`;\n    throw err;\n  } finally {\n    if (!process.env.BABEL_8_BREAKING) {\n      LOADING_MODULES.delete(name);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;AAIA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,SAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,QAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,IAAAI,MAAA,GAAAH,OAAA;AACA,IAAAI,YAAA,GAAAJ,OAAA;AACA,SAAAK,KAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,IAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAO,kBAAA,GAAAN,OAAA;AAAsD,SAAAO,mBAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAC,GAAA,cAAAC,IAAA,GAAAP,GAAA,CAAAK,GAAA,EAAAC,GAAA,OAAAE,KAAA,GAAAD,IAAA,CAAAC,KAAA,WAAAC,KAAA,IAAAP,MAAA,CAAAO,KAAA,iBAAAF,IAAA,CAAAG,IAAA,IAAAT,OAAA,CAAAO,KAAA,YAAAG,OAAA,CAAAV,OAAA,CAAAO,KAAA,EAAAI,IAAA,CAAAT,KAAA,EAAAC,MAAA;AAAA,SAAAS,kBAAAC,EAAA,6BAAAC,IAAA,SAAAC,IAAA,GAAAC,SAAA,aAAAN,OAAA,WAAAV,OAAA,EAAAC,MAAA,QAAAF,GAAA,GAAAc,EAAA,CAAAI,KAAA,CAAAH,IAAA,EAAAC,IAAA,YAAAb,MAAAK,KAAA,IAAAT,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,UAAAI,KAAA,cAAAJ,OAAAe,GAAA,IAAApB,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,WAAAe,GAAA,KAAAhB,KAAA,CAAAiB,SAAA;AAKtD,MAAMC,KAAK,GAAGC,QAAU,CAAC,oCAAoC,CAAC;AAE9D,MAAMC,QAAQ,GAAG,UAAU;AAC3B,MAAMC,sBAAsB,GAAG,sCAAsC;AACrE,MAAMC,sBAAsB,GAAG,sCAAsC;AACrE,MAAMC,mBAAmB,GAAG,gCAAgC;AAC5D,MAAMC,mBAAmB,GAAG,gCAAgC;AAC5D,MAAMC,mBAAmB,GACvB,+DAA+D;AACjE,MAAMC,mBAAmB,GACvB,+DAA+D;AACjE,MAAMC,oBAAoB,GAAG,sBAAsB;AAE5C,UAAUC,aAAaA,CAACC,IAAY,EAAEC,OAAe,EAAmB;EAE7E,OAAO,OAAOC,uBAAuB,CAAC,QAAQ,EAAEF,IAAI,EAAEC,OAAO,CAAC;AAChE;AAEO,UAAUE,aAAaA,CAACH,IAAY,EAAEC,OAAe,EAAmB;EAE7E,OAAO,OAAOC,uBAAuB,CAAC,QAAQ,EAAEF,IAAI,EAAEC,OAAO,CAAC;AAChE;AAEO,UAAUG,UAAUA,CACzBJ,IAAY,EACZC,OAAe,EACgC;EAC/C,MAAMI,QAAQ,GAAG,OAAON,aAAa,CAACC,IAAI,EAAEC,OAAO,CAAC;EAEpD,MAAMzB,KAAK,GAAG,OAAO8B,aAAa,CAAC,QAAQ,EAAED,QAAQ,CAAC;EACtDhB,KAAK,CAAC,2BAA2B,EAAEW,IAAI,EAAEC,OAAO,CAAC;EAEjD,OAAO;IAAEI,QAAQ;IAAE7B;EAAM,CAAC;AAC5B;AAEO,UAAU+B,UAAUA,CACzBP,IAAY,EACZC,OAAe,EACgC;EAC/C,MAAMI,QAAQ,GAAG,OAAOF,aAAa,CAACH,IAAI,EAAEC,OAAO,CAAC;EAEpD,MAAMzB,KAAK,GAAG,OAAO8B,aAAa,CAAC,QAAQ,EAAED,QAAQ,CAAC;EAEtDhB,KAAK,CAAC,2BAA2B,EAAEW,IAAI,EAAEC,OAAO,CAAC;EAEjD,OAAO;IAAEI,QAAQ;IAAE7B;EAAM,CAAC;AAC5B;AAEA,SAASgC,eAAeA,CAACC,IAAyB,EAAET,IAAY,EAAE;EAEhE,IAAIU,OAAI,CAACC,UAAU,CAACX,IAAI,CAAC,EAAE,OAAOA,IAAI;EAEtC,MAAMY,QAAQ,GAAGH,IAAI,KAAK,QAAQ;EAElC,OACET,IAAI,CAEDa,OAAO,CACND,QAAQ,GAAGnB,sBAAsB,GAAGD,sBAAsB,EACzD,SAAQiB,IAAK,GAAE,CACjB,CAEAI,OAAO,CACND,QAAQ,GAAGjB,mBAAmB,GAAGD,mBAAmB,EACnD,KAAIe,IAAK,GAAE,CACb,CAEAI,OAAO,CACND,QAAQ,GAAGf,mBAAmB,GAAGD,mBAAmB,EACnD,WAAUa,IAAK,GAAE,CACnB,CAEAI,OAAO,CAACf,oBAAoB,EAAG,YAAWW,IAAK,EAAC,CAAC,CAEjDI,OAAO,CAACtB,QAAQ,EAAE,EAAE,CAAC;AAE5B;AAIA,UAAUuB,yBAAyBA,CACjCL,IAAyB,EACzBT,IAAY,EAC8B;EAC1C,MAAMe,gBAAgB,GAAGP,eAAe,CAACC,IAAI,EAAET,IAAI,CAAC;EACpD,MAAM;IAAEvB,KAAK;IAAED;EAAM,CAAC,GAAG,MAAMuC,gBAAgB;EAC/C,IAAI,CAACtC,KAAK,EAAE,OAAOD,KAAK;EAGxB,IAAIC,KAAK,CAACuC,IAAI,KAAK,kBAAkB,EAAE,MAAMvC,KAAK;EAElD,IAAIsC,gBAAgB,KAAKf,IAAI,IAAI,CAAC,CAAC,MAAMA,IAAI,EAAEvB,KAAK,EAAE;IACpDA,KAAK,CAACwC,OAAO,IAAK,+BAA8BjB,IAAK,kBAAiBA,IAAK,GAAE;EAC/E;EAEA,IAAI,CAAC,CAAC,MAAMQ,eAAe,CAACC,IAAI,EAAE,SAAS,GAAGT,IAAI,CAAC,EAAEvB,KAAK,EAAE;IAC1DA,KAAK,CAACwC,OAAO,IAAK,4BAA2BjB,IAAK,IAAG;EACvD;EAEA,MAAMkB,YAAY,GAAGT,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ;EAC5D,IAAI,CAAC,CAAC,MAAMD,eAAe,CAACU,YAAY,EAAElB,IAAI,CAAC,EAAEvB,KAAK,EAAE;IACtDA,KAAK,CAACwC,OAAO,IAAK,mCAAkCC,YAAa,SAAQT,IAAK,GAAE;EAClF;EAEA,MAAMhC,KAAK;AACb;AAEA,SAAS0C,iBAAiBA,CACxBC,EAAiC,EACjC;EAAEC,KAAK,EAAE,CAACpB,OAAO;AAAiC,CAAC,EACnC;EAChB,IAAI;IACF,OAAO;MAAExB,KAAK,EAAE,IAAI;MAAED,KAAK,EAAE,GAAA8C,CAAA,EAAAC,CAAA,MAAAD,CAAA,GAAAA,CAAA,CAAAE,KAAA,OAAAD,CAAA,GAAAA,CAAA,CAAAC,KAAA,QAAAF,CAAA,OAAAC,CAAA,OAAAD,CAAA,OAAAC,CAAA,QAAAD,CAAA,QAAAC,CAAA,MAAAE,OAAA,CAAAC,QAAA,CAAAC,IAAA,WAAAnE,OAAA,CAAAS,OAAA,IAAA2D,CAAA;QAAAP,KAAA,GAAAQ,CAAA;MAAA,GAAAC,CAAA,GAAAtE,OAAA;QAAA,IAAAuE,CAAA,GAAAD,CAAA,CAAAE,SAAA,CAAAJ,CAAA,EAAAE,CAAA,CAAAG,gBAAA,CAAAJ,CAAA,EAAAK,MAAA,CAAAL,CAAA;QAAA,IAAAE,CAAA,SAAAA,CAAA;QAAAA,CAAA,OAAAI,KAAA,2BAAAP,CAAA;QAAAG,CAAA,CAAAf,IAAA;QAAA,MAAAe,CAAA;MAAA,GAAgBX,EAAE,EAAE;QAAEC,KAAK,EAAE,CAACpB,OAAO;MAAE,CAAC;IAAE,CAAC;EAC1E,CAAC,CAAC,OAAOxB,KAAK,EAAE;IACd,OAAO;MAAEA,KAAK;MAAED,KAAK,EAAE;IAAK,CAAC;EAC/B;AACF;AAAC,SAEc4D,oBAAoBA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,qBAAA,CAAArD,KAAA,OAAAD,SAAA;AAAA;AAAA,SAAAsD,sBAAA;EAAAA,qBAAA,GAAA1D,iBAAA,CAAnC,WACEuC,EAAwC,EACxCoB,OAA6C,EACpB;IACzB,IAAI;MACF,OAAO;QAAE/D,KAAK,EAAE,IAAI;QAAED,KAAK,QAAQ,IAAAiE,0BAAiB,EAACrB,EAAE,EAAEoB,OAAO;MAAE,CAAC;IACrE,CAAC,CAAC,OAAO/D,KAAK,EAAE;MACd,OAAO;QAAEA,KAAK;QAAED,KAAK,EAAE;MAAK,CAAC;IAC/B;EACF,CAAC;EAAA,OAAA+D,qBAAA,CAAArD,KAAA,OAAAD,SAAA;AAAA;AAED,SAASyD,iCAAiCA,CACxCjC,IAAyB,EACzBT,IAAY,EACZC,OAAe,EACf;EACA,MAAM0C,EAAE,GAAG7B,yBAAyB,CAACL,IAAI,EAAET,IAAI,CAAC;EAChD,IAAI4C,GAAG,GAAGD,EAAE,CAACE,IAAI,EAAE;EACnB,OAAO,CAACD,GAAG,CAAClE,IAAI,EAAE;IAChBkE,GAAG,GAAGD,EAAE,CAACE,IAAI,CAAC1B,iBAAiB,CAACyB,GAAG,CAACpE,KAAK,EAAE;MAAE6C,KAAK,EAAE,CAACpB,OAAO;IAAE,CAAC,CAAC,CAAC;EACnE;EACA,OAAO2C,GAAG,CAACpE,KAAK;AAClB;AAAC,SACcsE,gCAAgCA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,iCAAA,CAAAhE,KAAA,OAAAD,SAAA;AAAA;AAAA,SAAAiE,kCAAA;EAAAA,iCAAA,GAAArE,iBAAA,CAA/C,WACE4B,IAAyB,EACzBT,IAAY,EACZC,OAAe,EACf;IACA,MAAMkD,SAAS,GAAG,IAAAC,oBAAa,EAC7B1C,OAAI,CAAC2C,IAAI,CAACpD,OAAO,EAAE,iCAAiC,CAAC,CACtD,CAACqD,IAAI;IAEN,MAAMX,EAAE,GAAG7B,yBAAyB,CAACL,IAAI,EAAET,IAAI,CAAC;IAChD,IAAI4C,GAAG,GAAGD,EAAE,CAACE,IAAI,EAAE;IACnB,OAAO,CAACD,GAAG,CAAClE,IAAI,EAAE;MAChBkE,GAAG,GAAGD,EAAE,CAACE,IAAI,OAAOT,oBAAoB,CAACQ,GAAG,CAACpE,KAAK,EAAE2E,SAAS,CAAC,CAAC;IACjE;IACA,OAAO,IAAAI,oBAAa,EAACX,GAAG,CAACpE,KAAK,CAAC;EACjC,CAAC;EAAA,OAAA0E,iCAAA,CAAAhE,KAAA,OAAAD,SAAA;AAAA;AAED,MAAMiB,uBAAuB,GAAGsD,UAAO,CAGrC;EACAC,IAAIA,CAAChD,IAAI,EAAET,IAAI,EAAEC,OAAO,GAAGwB,OAAO,CAACiC,GAAG,EAAE,EAAE;IACxC,OAAOhB,iCAAiC,CAACjC,IAAI,EAAET,IAAI,EAAEC,OAAO,CAAC;EAC/D,CAAC;EACK0D,KAAKA,CAAClD,IAAI,EAAET,IAAI,EAAEC,OAAO,GAAGwB,OAAO,CAACiC,GAAG,EAAE,EAAE;IAAA,OAAA7E,iBAAA;MAC/C,IAAI,CAAC+E,wBAAW,EAAE;QAChB,OAAOlB,iCAAiC,CAACjC,IAAI,EAAET,IAAI,EAAEC,OAAO,CAAC;MAC/D;MAEA,IAAI;QACF,aAAa6C,gCAAgC,CAACrC,IAAI,EAAET,IAAI,EAAEC,OAAO,CAAC;MACpE,CAAC,CAAC,OAAO4D,CAAC,EAAE;QACV,IAAI;UACF,OAAOnB,iCAAiC,CAACjC,IAAI,EAAET,IAAI,EAAEC,OAAO,CAAC;QAC/D,CAAC,CAAC,OAAO6D,EAAE,EAAE;UACX,IAAID,CAAC,CAACpD,IAAI,KAAK,kBAAkB,EAAE,MAAMoD,CAAC;UAC1C,IAAIC,EAAE,CAACrD,IAAI,KAAK,kBAAkB,EAAE,MAAMqD,EAAE;UAC5C,MAAMD,CAAC;QACT;MACF;IAAC;EACH;AACF,CAAC,CAAC;AAEiC;EAEjC,IAAIE,eAAe,GAAG,IAAIC,GAAG,EAAE;AACjC;AACA,UAAU1D,aAAaA,CAACG,IAAY,EAAET,IAAY,EAAoB;EACjC;IACjC,IAAI,EAAE,OAAO,IAAAiE,cAAO,GAAE,CAAC,IAAIF,eAAe,CAACG,GAAG,CAAClE,IAAI,CAAC,EAAE;MACpD,MAAM,IAAImC,KAAK,CACZ,aAAY1B,IAAK,6BAA4BT,IAAK,gCAA+B,GAChF,sFAAsF,GACtF,qFAAqF,CACxF;IACH;EACF;EAEA,IAAI;IACiC;MACjC+D,eAAe,CAACI,GAAG,CAACnE,IAAI,CAAC;IAC3B;IACA,OAAO,OAAO,IAAAoE,oBAAe,EAC3BpE,IAAI,EACH,qDAAoDS,IAAK,IAAG,GAC3D,4DAA4D,EAK9D,IAAI,CACL;EACH,CAAC,CAAC,OAAOtB,GAAG,EAAE;IACZA,GAAG,CAAC8B,OAAO,GAAI,YAAW9B,GAAG,CAAC8B,OAAQ,uBAAsBjB,IAAK,GAAE;IACnE,MAAMb,GAAG;EACX,CAAC,SAAS;IAC2B;MACjC4E,eAAe,CAACM,MAAM,CAACrE,IAAI,CAAC;IAC9B;EACF;AACF;AAAC"}