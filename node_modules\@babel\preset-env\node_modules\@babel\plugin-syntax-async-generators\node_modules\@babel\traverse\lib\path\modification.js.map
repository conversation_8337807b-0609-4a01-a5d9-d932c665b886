{"version": 3, "names": ["_cache", "require", "_hoister", "_index", "_t", "arrowFunctionExpression", "assertExpression", "assignmentExpression", "blockStatement", "callExpression", "cloneNode", "expressionStatement", "isAssignmentExpression", "isCallExpression", "isExportNamedDeclaration", "isExpression", "isIdentifier", "isSequenceExpression", "is<PERSON><PERSON><PERSON>", "thisExpression", "insertBefore", "nodes_", "_assertUnremoved", "nodes", "_verifyNodeList", "parentPath", "parent", "isExpressionStatement", "isLabeledStatement", "isExportDefaultDeclaration", "isDeclaration", "isNodeType", "isJSXElement", "isForStatement", "key", "node", "push", "replaceExpressionWithStatements", "Array", "isArray", "container", "_containerInsertBefore", "isStatementOrBlock", "shouldInsertCurrentNode", "expression", "replaceWith", "unshiftContainer", "Error", "_containerInsert", "from", "updateSiblingKeys", "length", "paths", "splice", "i", "to", "path", "getSibling", "context", "queue", "pushContext", "contexts", "_getQueueContexts", "setScope", "debug", "maybeQueue", "_containerInsertAfter", "last", "arr", "isHiddenInSequenceExpression", "expressions", "isAlmostConstantAssignment", "scope", "left", "blockScope", "getBlockParent", "hasOwnBinding", "name", "getOwnBinding", "constantViolations", "insertAfter", "get", "map", "isPattern", "unshift", "callee", "isPure", "isMethod", "computed", "temp", "generateDeclaredUidIdentifier", "pushContainer", "fromIndex", "incrementBy", "pathCache", "msg", "type", "NodePath", "<PERSON><PERSON><PERSON>", "setContext", "verifiedNodes", "replaceWithMultiple", "hoist", "hoister", "PathHoister", "run"], "sources": ["../../src/path/modification.ts"], "sourcesContent": ["// This file contains methods that modify the path/node in some ways.\n\nimport { path as pathCache } from \"../cache\";\nimport PathHoister from \"./lib/hoister\";\nimport NodePath from \"./index\";\nimport {\n  arrowFunctionExpression,\n  assertExpression,\n  assignmentExpression,\n  blockStatement,\n  callExpression,\n  cloneNode,\n  expressionStatement,\n  isAssignmentExpression,\n  isCallExpression,\n  isExportNamedDeclaration,\n  isExpression,\n  isIdentifier,\n  isSequenceExpression,\n  isSuper,\n  thisExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type Scope from \"../scope\";\n\n/**\n * Insert the provided nodes before the current one.\n */\n\nexport function insertBefore(\n  this: NodePath,\n  nodes_: t.Node | t.Node[],\n): NodePath[] {\n  this._assertUnremoved();\n\n  const nodes = this._verifyNodeList(nodes_);\n\n  const { parentPath, parent } = this;\n\n  if (\n    parentPath.isExpressionStatement() ||\n    parentPath.isLabeledStatement() ||\n    // https://github.com/babel/babel/issues/15293\n    // When Babel transforms `export class String { field }`, the class properties plugin will inject the defineProperty\n    // helper, which depends on the builtins e.g. String, Number, Symbol, etc. To prevent them from being shadowed by local\n    // exports, the helper injector replaces the named export into `class _String { field }; export { _String as String }`,\n    // with `parentPath` here changed to the moved ClassDeclaration, causing rare inconsistency between `parent` and `parentPath`.\n    // Here we retrieve the parent type from the `parent` property. This is a temporary fix and we should revisit when\n    // helpers should get injected.\n    isExportNamedDeclaration(parent) ||\n    (parentPath.isExportDefaultDeclaration() && this.isDeclaration())\n  ) {\n    return parentPath.insertBefore(nodes);\n  } else if (\n    (this.isNodeType(\"Expression\") && !this.isJSXElement()) ||\n    (parentPath.isForStatement() && this.key === \"init\")\n  ) {\n    if (this.node) nodes.push(this.node);\n    // @ts-expect-error todo(flow->ts): check that nodes is an array of statements\n    return this.replaceExpressionWithStatements(nodes);\n  } else if (Array.isArray(this.container)) {\n    return this._containerInsertBefore(nodes);\n  } else if (this.isStatementOrBlock()) {\n    const node = this.node as t.Statement;\n    const shouldInsertCurrentNode =\n      node &&\n      (!this.isExpressionStatement() ||\n        (node as t.ExpressionStatement).expression != null);\n\n    this.replaceWith(blockStatement(shouldInsertCurrentNode ? [node] : []));\n    return (this as NodePath<t.BlockStatement>).unshiftContainer(\n      \"body\",\n      // @ts-expect-error Fixme: refine nodes to t.BlockStatement[\"body\"] when this is a BlockStatement path\n      nodes,\n    );\n  } else {\n    throw new Error(\n      \"We don't know what to do with this node type. \" +\n        \"We were previously a Statement but we can't fit in here?\",\n    );\n  }\n}\n\nexport function _containerInsert<N extends t.Node>(\n  this: NodePath,\n  from: number,\n  nodes: N[],\n): NodePath<N>[] {\n  this.updateSiblingKeys(from, nodes.length);\n\n  const paths: NodePath<N>[] = [];\n\n  // @ts-expect-error todo(flow->ts): this.container could be a NodePath\n  this.container.splice(from, 0, ...nodes);\n  for (let i = 0; i < nodes.length; i++) {\n    const to = from + i;\n    const path = this.getSibling(to) as NodePath<N>;\n    paths.push(path);\n\n    if (this.context && this.context.queue) {\n      path.pushContext(this.context);\n    }\n  }\n\n  const contexts = this._getQueueContexts();\n\n  for (const path of paths) {\n    path.setScope();\n    path.debug(\"Inserted.\");\n\n    for (const context of contexts) {\n      context.maybeQueue(path, true);\n    }\n  }\n\n  return paths;\n}\n\nexport function _containerInsertBefore<N extends t.Node>(\n  this: NodePath,\n  nodes: N[],\n) {\n  return this._containerInsert(this.key as number, nodes);\n}\n\nexport function _containerInsertAfter<N extends t.Node>(\n  this: NodePath,\n  nodes: N[],\n) {\n  return this._containerInsert((this.key as number) + 1, nodes);\n}\n\nconst last = <T>(arr: T[]) => arr[arr.length - 1];\n\nfunction isHiddenInSequenceExpression(path: NodePath): boolean {\n  return (\n    isSequenceExpression(path.parent) &&\n    (last(path.parent.expressions) !== path.node ||\n      isHiddenInSequenceExpression(path.parentPath))\n  );\n}\n\nfunction isAlmostConstantAssignment(\n  node: t.Node,\n  scope: Scope,\n): node is t.AssignmentExpression & { left: t.Identifier } {\n  if (!isAssignmentExpression(node) || !isIdentifier(node.left)) {\n    return false;\n  }\n\n  // Not every scope can contain variables. For example, we might be in\n  // a ClassScope either in the ClassHeritage or in a computed key.\n  const blockScope = scope.getBlockParent();\n\n  // If the variable is defined in the current scope and only assigned here,\n  // we can be sure that its value won't change.\n  return (\n    blockScope.hasOwnBinding(node.left.name) &&\n    blockScope.getOwnBinding(node.left.name).constantViolations.length <= 1\n  );\n}\n\n/**\n * Insert the provided nodes after the current one. When inserting nodes after an\n * expression, ensure that the completion record is correct by pushing the current node.\n */\n\nexport function insertAfter(\n  this: NodePath,\n  nodes_: t.Node | t.Node[],\n): NodePath[] {\n  this._assertUnremoved();\n\n  if (this.isSequenceExpression()) {\n    return last(this.get(\"expressions\")).insertAfter(nodes_);\n  }\n\n  const nodes = this._verifyNodeList(nodes_);\n\n  const { parentPath, parent } = this;\n  if (\n    parentPath.isExpressionStatement() ||\n    parentPath.isLabeledStatement() ||\n    // see insertBefore\n    isExportNamedDeclaration(parent) ||\n    (parentPath.isExportDefaultDeclaration() && this.isDeclaration())\n  ) {\n    return parentPath.insertAfter(\n      nodes.map(node => {\n        // Usually after an expression we can safely insert another expression:\n        //   A.insertAfter(B)\n        //     foo = A;  -> foo = (A, B);\n        // If A is an expression statement, it isn't safe anymore so we need to\n        // convert B to an expression statement\n        //     A;        -> A; B // No semicolon! It could break if followed by [!\n        return isExpression(node) ? expressionStatement(node) : node;\n      }),\n    );\n  } else if (\n    (this.isNodeType(\"Expression\") &&\n      !this.isJSXElement() &&\n      !parentPath.isJSXElement()) ||\n    (parentPath.isForStatement() && this.key === \"init\")\n  ) {\n    if (this.node) {\n      const node = this.node as t.Expression | t.VariableDeclaration;\n      let { scope } = this;\n\n      if (scope.path.isPattern()) {\n        assertExpression(node);\n\n        this.replaceWith(callExpression(arrowFunctionExpression([], node), []));\n        (this.get(\"callee.body\") as NodePath<t.Expression>).insertAfter(nodes);\n        return [this];\n      }\n\n      if (isHiddenInSequenceExpression(this)) {\n        nodes.unshift(node);\n      }\n      // We need to preserve the value of this expression.\n      else if (isCallExpression(node) && isSuper(node.callee)) {\n        nodes.unshift(node);\n        // `super(...)` always evaluates to `this`.\n        nodes.push(thisExpression());\n      } else if (isAlmostConstantAssignment(node, scope)) {\n        nodes.unshift(node);\n        nodes.push(cloneNode(node.left));\n      } else if (scope.isPure(node, true)) {\n        // Insert the nodes before rather than after; it's not observable.\n        nodes.push(node);\n      } else {\n        // Inserting after the computed key of a method should insert the\n        // temporary binding in the method's parent's scope.\n        if (parentPath.isMethod({ computed: true, key: node })) {\n          scope = scope.parent;\n        }\n        const temp = scope.generateDeclaredUidIdentifier();\n        nodes.unshift(\n          expressionStatement(\n            // @ts-expect-error todo(flow->ts): This can be a variable\n            // declaration in the \"init\" of a for statement, but that's\n            // invalid here.\n            assignmentExpression(\"=\", cloneNode(temp), node),\n          ),\n        );\n        nodes.push(expressionStatement(cloneNode(temp)));\n      }\n    }\n    // @ts-expect-error todo(flow->ts): check that nodes is an array of statements\n    return this.replaceExpressionWithStatements(nodes);\n  } else if (Array.isArray(this.container)) {\n    return this._containerInsertAfter(nodes);\n  } else if (this.isStatementOrBlock()) {\n    const node = this.node as t.Statement;\n    const shouldInsertCurrentNode =\n      node &&\n      (!this.isExpressionStatement() ||\n        (node as t.ExpressionStatement).expression != null);\n\n    this.replaceWith(blockStatement(shouldInsertCurrentNode ? [node] : []));\n    // @ts-expect-error Fixme: refine nodes to t.BlockStatement[\"body\"] when this is a BlockStatement path\n    return this.pushContainer(\"body\", nodes);\n  } else {\n    throw new Error(\n      \"We don't know what to do with this node type. \" +\n        \"We were previously a Statement but we can't fit in here?\",\n    );\n  }\n}\n\n/**\n * Update all sibling node paths after `fromIndex` by `incrementBy`.\n */\n\nexport function updateSiblingKeys(\n  this: NodePath,\n  fromIndex: number,\n  incrementBy: number,\n) {\n  if (!this.parent) return;\n\n  const paths = pathCache.get(this.parent);\n  for (const [, path] of paths) {\n    if (path.key >= fromIndex) {\n      path.key += incrementBy;\n    }\n  }\n}\n\nexport function _verifyNodeList<N extends t.Node>(\n  this: NodePath,\n  nodes: N | N[],\n): N[] {\n  if (!nodes) {\n    return [];\n  }\n\n  if (!Array.isArray(nodes)) {\n    nodes = [nodes];\n  }\n\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    let msg;\n\n    if (!node) {\n      msg = \"has falsy node\";\n    } else if (typeof node !== \"object\") {\n      msg = \"contains a non-object node\";\n    } else if (!node.type) {\n      msg = \"without a type\";\n    } else if (node instanceof NodePath) {\n      msg = \"has a NodePath when it expected a raw object\";\n    }\n\n    if (msg) {\n      const type = Array.isArray(node) ? \"array\" : typeof node;\n      throw new Error(\n        `Node list ${msg} with the index of ${i} and type of ${type}`,\n      );\n    }\n  }\n\n  return nodes;\n}\n\nexport function unshiftContainer<N extends t.Node, K extends keyof N & string>(\n  this: NodePath<N>,\n  listKey: K,\n  nodes: N[K] extends (infer E)[]\n    ? E | E[]\n    : // todo: refine to t.Node[]\n      //  ? E extends t.Node\n      //    ? E | E[]\n      //    : never\n      never,\n) {\n  // todo: NodePaths<Nodes>\n  this._assertUnremoved();\n\n  // @ts-expect-error fixme\n  nodes = this._verifyNodeList(nodes);\n\n  // get the first path and insert our nodes before it, if it doesn't exist then it\n  // doesn't matter, our nodes will be inserted anyway\n  const path = NodePath.get({\n    parentPath: this,\n    parent: this.node,\n    container: this.node[listKey] as unknown as t.Node | t.Node[],\n    listKey,\n    key: 0,\n  }).setContext(this.context);\n\n  return path._containerInsertBefore(\n    // @ts-expect-error typings needed to narrow down nodes as t.Node[]\n    nodes,\n  );\n}\n\nexport function pushContainer<N extends t.Node, K extends keyof N & string>(\n  this: NodePath<N>,\n  listKey: K,\n  nodes: N[K] extends (infer E)[]\n    ? E | E[]\n    : // todo: refine to t.Node[]\n      //  ? E extends t.Node\n      //    ? E | E[]\n      //    : never\n      never,\n) {\n  this._assertUnremoved();\n\n  const verifiedNodes = this._verifyNodeList(\n    // @ts-expect-error refine typings\n    nodes,\n  );\n\n  // get an invisible path that represents the last node + 1 and replace it with our\n  // nodes, effectively inlining it\n\n  const container = this.node[listKey];\n  const path = NodePath.get({\n    parentPath: this,\n    parent: this.node,\n    container: container as unknown as t.Node | t.Node[],\n    listKey,\n    // @ts-expect-error TS cannot infer that container is t.Node[]\n    key: container.length,\n  }).setContext(this.context);\n\n  return path.replaceWithMultiple(verifiedNodes);\n}\n\n/**\n * Hoist the current node to the highest scope possible and return a UID\n * referencing it.\n */\nexport function hoist<T extends t.Node>(\n  this: NodePath<T>,\n  scope: Scope = this.scope,\n) {\n  const hoister = new PathHoister<T>(this, scope);\n  return hoister.run();\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAEA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,EAAA,GAAAH,OAAA;AAgBsB;EAfpBI,uBAAuB;EACvBC,gBAAgB;EAChBC,oBAAoB;EACpBC,cAAc;EACdC,cAAc;EACdC,SAAS;EACTC,mBAAmB;EACnBC,sBAAsB;EACtBC,gBAAgB;EAChBC,wBAAwB;EACxBC,YAAY;EACZC,YAAY;EACZC,oBAAoB;EACpBC,OAAO;EACPC;AAAc,IAAAf,EAAA;AAST,SAASgB,YAAYA,CAE1BC,MAAyB,EACb;EACZ,IAAI,CAACC,gBAAgB,EAAE;EAEvB,MAAMC,KAAK,GAAG,IAAI,CAACC,eAAe,CAACH,MAAM,CAAC;EAE1C,MAAM;IAAEI,UAAU;IAAEC;EAAO,CAAC,GAAG,IAAI;EAEnC,IACED,UAAU,CAACE,qBAAqB,EAAE,IAClCF,UAAU,CAACG,kBAAkB,EAAE,IAQ/Bd,wBAAwB,CAACY,MAAM,CAAC,IAC/BD,UAAU,CAACI,0BAA0B,EAAE,IAAI,IAAI,CAACC,aAAa,EAAG,EACjE;IACA,OAAOL,UAAU,CAACL,YAAY,CAACG,KAAK,CAAC;EACvC,CAAC,MAAM,IACJ,IAAI,CAACQ,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE,IACrDP,UAAU,CAACQ,cAAc,EAAE,IAAI,IAAI,CAACC,GAAG,KAAK,MAAO,EACpD;IACA,IAAI,IAAI,CAACC,IAAI,EAAEZ,KAAK,CAACa,IAAI,CAAC,IAAI,CAACD,IAAI,CAAC;IAEpC,OAAO,IAAI,CAACE,+BAA+B,CAACd,KAAK,CAAC;EACpD,CAAC,MAAM,IAAIe,KAAK,CAACC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;IACxC,OAAO,IAAI,CAACC,sBAAsB,CAAClB,KAAK,CAAC;EAC3C,CAAC,MAAM,IAAI,IAAI,CAACmB,kBAAkB,EAAE,EAAE;IACpC,MAAMP,IAAI,GAAG,IAAI,CAACA,IAAmB;IACrC,MAAMQ,uBAAuB,GAC3BR,IAAI,KACH,CAAC,IAAI,CAACR,qBAAqB,EAAE,IAC3BQ,IAAI,CAA2BS,UAAU,IAAI,IAAI,CAAC;IAEvD,IAAI,CAACC,WAAW,CAACrC,cAAc,CAACmC,uBAAuB,GAAG,CAACR,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IACvE,OAAQ,IAAI,CAAgCW,gBAAgB,CAC1D,MAAM,EAENvB,KAAK,CACN;EACH,CAAC,MAAM;IACL,MAAM,IAAIwB,KAAK,CACb,gDAAgD,GAC9C,0DAA0D,CAC7D;EACH;AACF;AAEO,SAASC,gBAAgBA,CAE9BC,IAAY,EACZ1B,KAAU,EACK;EACf,IAAI,CAAC2B,iBAAiB,CAACD,IAAI,EAAE1B,KAAK,CAAC4B,MAAM,CAAC;EAE1C,MAAMC,KAAoB,GAAG,EAAE;EAG/B,IAAI,CAACZ,SAAS,CAACa,MAAM,CAACJ,IAAI,EAAE,CAAC,EAAE,GAAG1B,KAAK,CAAC;EACxC,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,KAAK,CAAC4B,MAAM,EAAEG,CAAC,EAAE,EAAE;IACrC,MAAMC,EAAE,GAAGN,IAAI,GAAGK,CAAC;IACnB,MAAME,IAAI,GAAG,IAAI,CAACC,UAAU,CAACF,EAAE,CAAgB;IAC/CH,KAAK,CAAChB,IAAI,CAACoB,IAAI,CAAC;IAEhB,IAAI,IAAI,CAACE,OAAO,IAAI,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE;MACtCH,IAAI,CAACI,WAAW,CAAC,IAAI,CAACF,OAAO,CAAC;IAChC;EACF;EAEA,MAAMG,QAAQ,GAAG,IAAI,CAACC,iBAAiB,EAAE;EAEzC,KAAK,MAAMN,IAAI,IAAIJ,KAAK,EAAE;IACxBI,IAAI,CAACO,QAAQ,EAAE;IACfP,IAAI,CAACQ,KAAK,CAAC,WAAW,CAAC;IAEvB,KAAK,MAAMN,OAAO,IAAIG,QAAQ,EAAE;MAC9BH,OAAO,CAACO,UAAU,CAACT,IAAI,EAAE,IAAI,CAAC;IAChC;EACF;EAEA,OAAOJ,KAAK;AACd;AAEO,SAASX,sBAAsBA,CAEpClB,KAAU,EACV;EACA,OAAO,IAAI,CAACyB,gBAAgB,CAAC,IAAI,CAACd,GAAG,EAAYX,KAAK,CAAC;AACzD;AAEO,SAAS2C,qBAAqBA,CAEnC3C,KAAU,EACV;EACA,OAAO,IAAI,CAACyB,gBAAgB,CAAE,IAAI,CAACd,GAAG,GAAc,CAAC,EAAEX,KAAK,CAAC;AAC/D;AAEA,MAAM4C,IAAI,GAAOC,GAAQ,IAAKA,GAAG,CAACA,GAAG,CAACjB,MAAM,GAAG,CAAC,CAAC;AAEjD,SAASkB,4BAA4BA,CAACb,IAAc,EAAW;EAC7D,OACEvC,oBAAoB,CAACuC,IAAI,CAAC9B,MAAM,CAAC,KAChCyC,IAAI,CAACX,IAAI,CAAC9B,MAAM,CAAC4C,WAAW,CAAC,KAAKd,IAAI,CAACrB,IAAI,IAC1CkC,4BAA4B,CAACb,IAAI,CAAC/B,UAAU,CAAC,CAAC;AAEpD;AAEA,SAAS8C,0BAA0BA,CACjCpC,IAAY,EACZqC,KAAY,EAC6C;EACzD,IAAI,CAAC5D,sBAAsB,CAACuB,IAAI,CAAC,IAAI,CAACnB,YAAY,CAACmB,IAAI,CAACsC,IAAI,CAAC,EAAE;IAC7D,OAAO,KAAK;EACd;EAIA,MAAMC,UAAU,GAAGF,KAAK,CAACG,cAAc,EAAE;EAIzC,OACED,UAAU,CAACE,aAAa,CAACzC,IAAI,CAACsC,IAAI,CAACI,IAAI,CAAC,IACxCH,UAAU,CAACI,aAAa,CAAC3C,IAAI,CAACsC,IAAI,CAACI,IAAI,CAAC,CAACE,kBAAkB,CAAC5B,MAAM,IAAI,CAAC;AAE3E;AAOO,SAAS6B,WAAWA,CAEzB3D,MAAyB,EACb;EACZ,IAAI,CAACC,gBAAgB,EAAE;EAEvB,IAAI,IAAI,CAACL,oBAAoB,EAAE,EAAE;IAC/B,OAAOkD,IAAI,CAAC,IAAI,CAACc,GAAG,CAAC,aAAa,CAAC,CAAC,CAACD,WAAW,CAAC3D,MAAM,CAAC;EAC1D;EAEA,MAAME,KAAK,GAAG,IAAI,CAACC,eAAe,CAACH,MAAM,CAAC;EAE1C,MAAM;IAAEI,UAAU;IAAEC;EAAO,CAAC,GAAG,IAAI;EACnC,IACED,UAAU,CAACE,qBAAqB,EAAE,IAClCF,UAAU,CAACG,kBAAkB,EAAE,IAE/Bd,wBAAwB,CAACY,MAAM,CAAC,IAC/BD,UAAU,CAACI,0BAA0B,EAAE,IAAI,IAAI,CAACC,aAAa,EAAG,EACjE;IACA,OAAOL,UAAU,CAACuD,WAAW,CAC3BzD,KAAK,CAAC2D,GAAG,CAAC/C,IAAI,IAAI;MAOhB,OAAOpB,YAAY,CAACoB,IAAI,CAAC,GAAGxB,mBAAmB,CAACwB,IAAI,CAAC,GAAGA,IAAI;IAC9D,CAAC,CAAC,CACH;EACH,CAAC,MAAM,IACJ,IAAI,CAACJ,UAAU,CAAC,YAAY,CAAC,IAC5B,CAAC,IAAI,CAACC,YAAY,EAAE,IACpB,CAACP,UAAU,CAACO,YAAY,EAAE,IAC3BP,UAAU,CAACQ,cAAc,EAAE,IAAI,IAAI,CAACC,GAAG,KAAK,MAAO,EACpD;IACA,IAAI,IAAI,CAACC,IAAI,EAAE;MACb,MAAMA,IAAI,GAAG,IAAI,CAACA,IAA4C;MAC9D,IAAI;QAAEqC;MAAM,CAAC,GAAG,IAAI;MAEpB,IAAIA,KAAK,CAAChB,IAAI,CAAC2B,SAAS,EAAE,EAAE;QAC1B7E,gBAAgB,CAAC6B,IAAI,CAAC;QAEtB,IAAI,CAACU,WAAW,CAACpC,cAAc,CAACJ,uBAAuB,CAAC,EAAE,EAAE8B,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QACtE,IAAI,CAAC8C,GAAG,CAAC,aAAa,CAAC,CAA4BD,WAAW,CAACzD,KAAK,CAAC;QACtE,OAAO,CAAC,IAAI,CAAC;MACf;MAEA,IAAI8C,4BAA4B,CAAC,IAAI,CAAC,EAAE;QACtC9C,KAAK,CAAC6D,OAAO,CAACjD,IAAI,CAAC;MACrB,CAAC,MAEI,IAAItB,gBAAgB,CAACsB,IAAI,CAAC,IAAIjB,OAAO,CAACiB,IAAI,CAACkD,MAAM,CAAC,EAAE;QACvD9D,KAAK,CAAC6D,OAAO,CAACjD,IAAI,CAAC;QAEnBZ,KAAK,CAACa,IAAI,CAACjB,cAAc,EAAE,CAAC;MAC9B,CAAC,MAAM,IAAIoD,0BAA0B,CAACpC,IAAI,EAAEqC,KAAK,CAAC,EAAE;QAClDjD,KAAK,CAAC6D,OAAO,CAACjD,IAAI,CAAC;QACnBZ,KAAK,CAACa,IAAI,CAAC1B,SAAS,CAACyB,IAAI,CAACsC,IAAI,CAAC,CAAC;MAClC,CAAC,MAAM,IAAID,KAAK,CAACc,MAAM,CAACnD,IAAI,EAAE,IAAI,CAAC,EAAE;QAEnCZ,KAAK,CAACa,IAAI,CAACD,IAAI,CAAC;MAClB,CAAC,MAAM;QAGL,IAAIV,UAAU,CAAC8D,QAAQ,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEtD,GAAG,EAAEC;QAAK,CAAC,CAAC,EAAE;UACtDqC,KAAK,GAAGA,KAAK,CAAC9C,MAAM;QACtB;QACA,MAAM+D,IAAI,GAAGjB,KAAK,CAACkB,6BAA6B,EAAE;QAClDnE,KAAK,CAAC6D,OAAO,CACXzE,mBAAmB,CAIjBJ,oBAAoB,CAAC,GAAG,EAAEG,SAAS,CAAC+E,IAAI,CAAC,EAAEtD,IAAI,CAAC,CACjD,CACF;QACDZ,KAAK,CAACa,IAAI,CAACzB,mBAAmB,CAACD,SAAS,CAAC+E,IAAI,CAAC,CAAC,CAAC;MAClD;IACF;IAEA,OAAO,IAAI,CAACpD,+BAA+B,CAACd,KAAK,CAAC;EACpD,CAAC,MAAM,IAAIe,KAAK,CAACC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;IACxC,OAAO,IAAI,CAAC0B,qBAAqB,CAAC3C,KAAK,CAAC;EAC1C,CAAC,MAAM,IAAI,IAAI,CAACmB,kBAAkB,EAAE,EAAE;IACpC,MAAMP,IAAI,GAAG,IAAI,CAACA,IAAmB;IACrC,MAAMQ,uBAAuB,GAC3BR,IAAI,KACH,CAAC,IAAI,CAACR,qBAAqB,EAAE,IAC3BQ,IAAI,CAA2BS,UAAU,IAAI,IAAI,CAAC;IAEvD,IAAI,CAACC,WAAW,CAACrC,cAAc,CAACmC,uBAAuB,GAAG,CAACR,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IAEvE,OAAO,IAAI,CAACwD,aAAa,CAAC,MAAM,EAAEpE,KAAK,CAAC;EAC1C,CAAC,MAAM;IACL,MAAM,IAAIwB,KAAK,CACb,gDAAgD,GAC9C,0DAA0D,CAC7D;EACH;AACF;AAMO,SAASG,iBAAiBA,CAE/B0C,SAAiB,EACjBC,WAAmB,EACnB;EACA,IAAI,CAAC,IAAI,CAACnE,MAAM,EAAE;EAElB,MAAM0B,KAAK,GAAG0C,WAAS,CAACb,GAAG,CAAC,IAAI,CAACvD,MAAM,CAAC;EACxC,KAAK,MAAM,GAAG8B,IAAI,CAAC,IAAIJ,KAAK,EAAE;IAC5B,IAAII,IAAI,CAACtB,GAAG,IAAI0D,SAAS,EAAE;MACzBpC,IAAI,CAACtB,GAAG,IAAI2D,WAAW;IACzB;EACF;AACF;AAEO,SAASrE,eAAeA,CAE7BD,KAAc,EACT;EACL,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,EAAE;EACX;EAEA,IAAI,CAACe,KAAK,CAACC,OAAO,CAAChB,KAAK,CAAC,EAAE;IACzBA,KAAK,GAAG,CAACA,KAAK,CAAC;EACjB;EAEA,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,KAAK,CAAC4B,MAAM,EAAEG,CAAC,EAAE,EAAE;IACrC,MAAMnB,IAAI,GAAGZ,KAAK,CAAC+B,CAAC,CAAC;IACrB,IAAIyC,GAAG;IAEP,IAAI,CAAC5D,IAAI,EAAE;MACT4D,GAAG,GAAG,gBAAgB;IACxB,CAAC,MAAM,IAAI,OAAO5D,IAAI,KAAK,QAAQ,EAAE;MACnC4D,GAAG,GAAG,4BAA4B;IACpC,CAAC,MAAM,IAAI,CAAC5D,IAAI,CAAC6D,IAAI,EAAE;MACrBD,GAAG,GAAG,gBAAgB;IACxB,CAAC,MAAM,IAAI5D,IAAI,YAAY8D,cAAQ,EAAE;MACnCF,GAAG,GAAG,8CAA8C;IACtD;IAEA,IAAIA,GAAG,EAAE;MACP,MAAMC,IAAI,GAAG1D,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,GAAG,OAAO,GAAG,OAAOA,IAAI;MACxD,MAAM,IAAIY,KAAK,CACZ,aAAYgD,GAAI,sBAAqBzC,CAAE,gBAAe0C,IAAK,EAAC,CAC9D;IACH;EACF;EAEA,OAAOzE,KAAK;AACd;AAEO,SAASuB,gBAAgBA,CAE9BoD,OAAU,EACV3E,KAMS,EACT;EAEA,IAAI,CAACD,gBAAgB,EAAE;EAGvBC,KAAK,GAAG,IAAI,CAACC,eAAe,CAACD,KAAK,CAAC;EAInC,MAAMiC,IAAI,GAAGyC,cAAQ,CAAChB,GAAG,CAAC;IACxBxD,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,IAAI,CAACS,IAAI;IACjBK,SAAS,EAAE,IAAI,CAACL,IAAI,CAAC+D,OAAO,CAAiC;IAC7DA,OAAO;IACPhE,GAAG,EAAE;EACP,CAAC,CAAC,CAACiE,UAAU,CAAC,IAAI,CAACzC,OAAO,CAAC;EAE3B,OAAOF,IAAI,CAACf,sBAAsB,CAEhClB,KAAK,CACN;AACH;AAEO,SAASoE,aAAaA,CAE3BO,OAAU,EACV3E,KAMS,EACT;EACA,IAAI,CAACD,gBAAgB,EAAE;EAEvB,MAAM8E,aAAa,GAAG,IAAI,CAAC5E,eAAe,CAExCD,KAAK,CACN;EAKD,MAAMiB,SAAS,GAAG,IAAI,CAACL,IAAI,CAAC+D,OAAO,CAAC;EACpC,MAAM1C,IAAI,GAAGyC,cAAQ,CAAChB,GAAG,CAAC;IACxBxD,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,IAAI,CAACS,IAAI;IACjBK,SAAS,EAAEA,SAAyC;IACpD0D,OAAO;IAEPhE,GAAG,EAAEM,SAAS,CAACW;EACjB,CAAC,CAAC,CAACgD,UAAU,CAAC,IAAI,CAACzC,OAAO,CAAC;EAE3B,OAAOF,IAAI,CAAC6C,mBAAmB,CAACD,aAAa,CAAC;AAChD;AAMO,SAASE,KAAKA,CAEnB9B,KAAY,GAAG,IAAI,CAACA,KAAK,EACzB;EACA,MAAM+B,OAAO,GAAG,IAAIC,gBAAW,CAAI,IAAI,EAAEhC,KAAK,CAAC;EAC/C,OAAO+B,OAAO,CAACE,GAAG,EAAE;AACtB"}