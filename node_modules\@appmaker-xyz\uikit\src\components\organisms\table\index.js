import React from 'react';
import { StyleSheet, FlatList } from 'react-native';
import { Layout, TableCell } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';

const Table = ({ attributes }) => {
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  return (
    <Layout style={styles.tableContainer}>
      <FlatList
        data={attributes}
        renderItem={({ item }) => (
          <TableCell
            attributes={{
              type: attributes.type,
              title: item.title,
              value: item.value,
              iconName: item.iconName,
            }}
            onPress={() => console.log('table cell')}
          />
        )}
        keyExtractor={(item) => item.id}
      />
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    tableContainer: {
      padding: spacing.small,
      borderBottomWidth: 1,
      borderBottomColor: color.light,
      backgroundColor: color.white,
      borderRadius: spacing.nano,
      marginBottom: spacing.nano,
    },
  });

export default Table;
