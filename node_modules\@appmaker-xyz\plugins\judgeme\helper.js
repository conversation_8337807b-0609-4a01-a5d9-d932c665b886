import { appmaker, helpers } from '@appmaker-xyz/core';
const { findBlockIndex } = helpers;
function judgemeReviewHelper(judgeme_reviews_rating) {
  try {
    const { value } = judgeme_reviews_rating;
    const jsonObj = JSON.parse(value);
    return jsonObj.value;
  } catch (error) {}
  return '0';
}

const activateHelper = () => {
  appmaker.addFilter(
    'parse-helper-functions',
    'add-judgeme-helper',
    function (value) {
      return {
        ...value,
        judgemeReviewHelper,
      };
    },
  );
};
export function addExtraMetaFields() {
  appmaker.addFilter(
    'shopify-gql-product-extra-fields',
    'judge-me',
    (singleProductFields) => {
      // const { variants } = singleProductFields;
      singleProductFields.judgeme_reviews_count = {
        __aliasFor: 'metafield',
        __args: {
          key: 'rating_count',
          namespace: 'reviews',
        },
        value: true,
      };
      singleProductFields.judgeme_reviews_rating = {
        __aliasFor: 'metafield',
        __args: {
          key: 'rating',
          namespace: 'reviews',
        },
        value: true,
      };
      return {
        ...singleProductFields,
        tags: true,
      };
    },
  );
}
export function addReviewsAttributes() {
  appmaker.addFilter(
    'product-review-data',
    'judgeme-review-data',
    (currentData, { product }) => {
      return {
        average_rating:
          judgemeReviewHelper(product?.node?.judgeme_reviews_rating) ||
          currentData?.average_rating,
        reviews_count:
          product?.node?.judgeme_reviews_count?.value ||
          currentData?.average_rating,
      };
    },
  );
}
export function addPDPWidget() {
  const webviewBlock = {
    clientId: 'stamped-reviews',
    name: 'appmaker/auto-height-webview',
    attributes: {
      source: {
        uri: 'https://app-static-pages.appmaker.xyz/shopify/stamped/review-detail?product_base64_id={{blockItem.node.id}}&apiKey=pubkey-iH97s147EsuuB9Vn8l7mi5F35ezDgB&storeUrl=buy-wow-health.myshopify.com',
      },
    },
  };
}

export function addProductReviewButtonPDP(settings) {
  const {
    shop,
    public_token,
    force_inject_judgeme_reviews_button_in_pdp,
    judgeme_reviews_button_in_pdp_block_index,
    show_judgeme_review_native
  } = settings;
  appmaker.addFilter(
    'inapp-page-data-response',
    'bold-sub',
    (data, { pageId }) => {
      const reviewsBlock = {
        clientId: 'judgeme-reviews',
        name: 'appmaker/ShopifyReview',
        attributes: {
          __display:
            "{{checkIfTrueFalse(plugins['judgeme-reviews'].settings.force_inject_judgeme_reviews_button_in_pdp)}}",
          containerStyle: {
            flex: 1,
          },
          title: 'Reviews',
          count:
            "{{blockItem.node.judgeme_reviews_count ? (blockItem.node.judgeme_reviews_count.value) : '0' }}",
          appmakerAction: {
            action: 'OPEN_WEBVIEW',
            params: {
              url: `https://app-static-pages.appmaker.xyz/shopify/judge-me/review-detail?shop=${shop}&public_token=${public_token}&product_id={{shopifyIdHelper(blockItem.node.id,true)}}&product_title=People`,
            },
            productId: '{{blockItem.node.id}}',
          },
        },
      };
      const native_blocks = [
        {
          name: 'shopify/review-summery-wrapper',
          attributes: {},
        },
        {
          name: 'shopify/shopify-review-list-preview',
          attributes: {},
        },
        {
          name: 'shopify/show-all-review',
          attributes: {},
        },
      ];

      const judgeMeBlockIndex = findBlockIndex(data.blocks, 'judgeme-reviews');
      if (
        pageId === 'productDetail' &&
       force_inject_judgeme_reviews_button_in_pdp &&
        judgeme_reviews_button_in_pdp_block_index &&
        judgeMeBlockIndex === -1
      ) {
        
        if( show_judgeme_review_native === '1' ) {
         
          data.blocks.splice(
            parseInt(judgeme_reviews_button_in_pdp_block_index, 10),
            0,
            ...native_blocks,
          );
        } else {
          data.blocks.splice(
            parseInt(judgeme_reviews_button_in_pdp_block_index, 10),
            0,
            reviewsBlock,
          );
        }
       
      } 
      return data;
    },
  );
}

export default activateHelper;
