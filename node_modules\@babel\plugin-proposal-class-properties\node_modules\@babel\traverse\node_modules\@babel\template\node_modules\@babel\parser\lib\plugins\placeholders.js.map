{"version": 3, "names": ["_types", "require", "_parseError", "PlaceholderErrors", "ParseErrorEnum", "ClassNameIsRequired", "UnexpectedSpace", "_default", "superClass", "PlaceholdersParserMixin", "parsePlaceholder", "expectedNode", "match", "node", "startNode", "next", "assertNoSpace", "name", "parseIdentifier", "expect", "finishPlaceholder", "isFinished", "type", "finishNode", "getTokenFromCode", "code", "input", "charCodeAt", "state", "pos", "finishOp", "parseExprAtom", "refExpressionErrors", "liberal", "checkReservedWord", "word", "startLoc", "checkKeywords", "isBinding", "undefined", "parseBindingAtom", "isValidLVal", "isParenthesized", "binding", "toAssignable", "isLHS", "chStartsBindingIdentifier", "ch", "nextToken", "<PERSON><PERSON><PERSON>", "verifyBreakContinue", "isBreak", "label", "parseExpressionStatement", "expr", "extra", "parenthesized", "stmt", "body", "parseStatementOrSloppyAnnexBFunctionDeclaration", "semicolon", "parseBlock", "allowDirectives", "createNewLexicalScope", "afterBlockParse", "parseFunctionId", "requireId", "parseClass", "isStatement", "optionalId", "oldStrict", "strict", "placeholder", "id", "raise", "at", "parseClassId", "parseClassSuper", "parseClassBody", "parseExport", "decorators", "isContextual", "specifiers", "source", "declaration", "expectPlugin", "specifier", "exported", "isExportDefaultSpecifier", "nextTokenStart", "isUnparsedContextual", "startsWith", "tokenLabelName", "nextTokenStartSince", "maybeParseExportDefaultSpecifier", "maybeDefaultIdentifier", "_specifiers", "length", "checkExport", "filter", "parseImport", "startNodeAtNode", "local", "push", "eat", "hasStarImport", "maybeParseStarImportSpecifier", "parseNamedImportSpecifiers", "expectContextual", "parseImportSource", "start", "lastTokEndLoc", "index", "exports", "default"], "sources": ["../../src/plugins/placeholders.ts"], "sourcesContent": ["import * as charCodes from \"charcodes\";\n\nimport { tokenLabelName, tt } from \"../tokenizer/types\";\nimport type Parser from \"../parser\";\nimport type * as N from \"../types\";\nimport { ParseErrorEnum } from \"../parse-error\";\nimport type { Undone } from \"../parser/node\";\nimport type { ExpressionErrors } from \"../parser/util\";\nimport type { BindingTypes } from \"../util/scopeflags\";\nimport type { Position } from \"../util/location\";\n\ntype PossiblePlaceholders = {\n  Identifier: N.Identifier;\n  StringLiteral: N.StringLiteral;\n  Expression: N.Expression;\n  Statement: N.Statement;\n  Declaration: N.Declaration;\n  BlockStatement: N.BlockStatement;\n  ClassBody: N.ClassBody;\n  Pattern: N.Pattern;\n};\nexport type PlaceholderTypes = keyof PossiblePlaceholders;\n\ntype NodeOf<T extends keyof PossiblePlaceholders> = PossiblePlaceholders[T];\n// todo: when there  is proper union type for Node\n// type NodeOf<T extends PlaceholderTypes> = Extract<N.Node, { type: T }>;\n\n// todo: Placeholder<T> breaks everything, because its type is incompatible with\n// the substituted nodes.\ntype MaybePlaceholder<T extends PlaceholderTypes> = NodeOf<T>; // | Placeholder<T>\n\n/* eslint sort-keys: \"error\" */\nconst PlaceholderErrors = ParseErrorEnum`placeholders`({\n  ClassNameIsRequired: \"A class name is required.\",\n  UnexpectedSpace: \"Unexpected space in placeholder.\",\n});\n\n/* eslint-disable sort-keys */\n\nexport default (superClass: typeof Parser) =>\n  class PlaceholdersParserMixin extends superClass implements Parser {\n    parsePlaceholder<T extends PlaceholderTypes>(\n      expectedNode: T,\n    ): /*?N.Placeholder<T>*/ MaybePlaceholder<T> | undefined | null {\n      if (this.match(tt.placeholder)) {\n        const node = this.startNode();\n        this.next();\n        this.assertNoSpace();\n\n        // We can't use this.parseIdentifier because\n        // we don't want nested placeholders.\n        node.name = super.parseIdentifier(/* liberal */ true);\n\n        this.assertNoSpace();\n        this.expect(tt.placeholder);\n        // @ts-expect-error placeholder typings\n        return this.finishPlaceholder(node, expectedNode);\n      }\n    }\n\n    finishPlaceholder<T extends PlaceholderTypes>(\n      node: N.Node,\n      expectedNode: T,\n    ): /*N.Placeholder<T>*/ MaybePlaceholder<T> {\n      const isFinished = !!(node.expectedNode && node.type === \"Placeholder\");\n      node.expectedNode = expectedNode;\n\n      // @ts-expect-error todo(flow->ts)\n      return isFinished ? node : this.finishNode(node, \"Placeholder\");\n    }\n\n    /* ============================================================ *\n     * tokenizer/index.js                                           *\n     * ============================================================ */\n\n    getTokenFromCode(code: number) {\n      if (\n        code === charCodes.percentSign &&\n        this.input.charCodeAt(this.state.pos + 1) === charCodes.percentSign\n      ) {\n        this.finishOp(tt.placeholder, 2);\n      } else {\n        super.getTokenFromCode(code);\n      }\n    }\n\n    /* ============================================================ *\n     * parser/expression.js                                         *\n     * ============================================================ */\n\n    parseExprAtom(\n      refExpressionErrors?: ExpressionErrors | null,\n    ): MaybePlaceholder<\"Expression\"> {\n      return (\n        this.parsePlaceholder(\"Expression\") ||\n        super.parseExprAtom(refExpressionErrors)\n      );\n    }\n\n    parseIdentifier(liberal?: boolean): MaybePlaceholder<\"Identifier\"> {\n      // NOTE: This function only handles identifiers outside of\n      // expressions and binding patterns, since they are already\n      // handled by the parseExprAtom and parseBindingAtom functions.\n      // This is needed, for example, to parse \"class %%NAME%% {}\".\n      return (\n        this.parsePlaceholder(\"Identifier\") || super.parseIdentifier(liberal)\n      );\n    }\n\n    checkReservedWord(\n      word: string,\n      startLoc: Position,\n      checkKeywords: boolean,\n      isBinding: boolean,\n    ) {\n      // Sometimes we call #checkReservedWord(node.name), expecting\n      // that node is an Identifier. If it is a Placeholder, name\n      // will be undefined.\n      if (word !== undefined) {\n        super.checkReservedWord(word, startLoc, checkKeywords, isBinding);\n      }\n    }\n\n    /* ============================================================ *\n     * parser/lval.js                                               *\n     * ============================================================ */\n\n    parseBindingAtom(): MaybePlaceholder<\"Pattern\"> {\n      return this.parsePlaceholder(\"Pattern\") || super.parseBindingAtom();\n    }\n\n    isValidLVal(type: string, isParenthesized: boolean, binding: BindingTypes) {\n      return (\n        type === \"Placeholder\" ||\n        super.isValidLVal(type, isParenthesized, binding)\n      );\n    }\n\n    toAssignable(node: N.Node, isLHS: boolean): void {\n      if (\n        node &&\n        node.type === \"Placeholder\" &&\n        node.expectedNode === \"Expression\"\n      ) {\n        node.expectedNode = \"Pattern\";\n      } else {\n        super.toAssignable(node, isLHS);\n      }\n    }\n\n    /* ============================================================ *\n     * parser/statement.js                                          *\n     * ============================================================ */\n\n    chStartsBindingIdentifier(ch: number, pos: number): boolean {\n      if (super.chStartsBindingIdentifier(ch, pos)) {\n        return true;\n      }\n\n      // Accept \"let %%\" as the start of \"let %%placeholder%%\", as though the\n      // placeholder were an identifier.\n      const nextToken = this.lookahead();\n      if (nextToken.type === tt.placeholder) {\n        return true;\n      }\n\n      return false;\n    }\n\n    verifyBreakContinue(\n      node: N.BreakStatement | N.ContinueStatement,\n      isBreak: boolean,\n    ) {\n      // @ts-expect-error: node.label could be Placeholder\n      if (node.label && node.label.type === \"Placeholder\") return;\n      super.verifyBreakContinue(node, isBreak);\n    }\n\n    // @ts-expect-error Plugin will override parser interface\n    parseExpressionStatement(\n      node: MaybePlaceholder<\"Statement\">,\n      expr: N.Expression,\n    ): MaybePlaceholder<\"Statement\"> {\n      if (\n        expr.type !== \"Placeholder\" ||\n        (expr.extra && expr.extra.parenthesized)\n      ) {\n        // @ts-expect-error placeholder typings\n        return super.parseExpressionStatement(node, expr);\n      }\n\n      if (this.match(tt.colon)) {\n        // @ts-expect-error placeholder typings\n        const stmt: N.LabeledStatement = node;\n        stmt.label = this.finishPlaceholder(expr, \"Identifier\");\n        this.next();\n        stmt.body = super.parseStatementOrSloppyAnnexBFunctionDeclaration();\n        return this.finishNode(stmt, \"LabeledStatement\");\n      }\n\n      this.semicolon();\n      node.name = expr.name;\n      return this.finishPlaceholder(node, \"Statement\");\n    }\n\n    parseBlock(\n      allowDirectives?: boolean,\n      createNewLexicalScope?: boolean,\n      afterBlockParse?: (hasStrictModeDirective: boolean) => void,\n    ): MaybePlaceholder<\"BlockStatement\"> {\n      return (\n        this.parsePlaceholder(\"BlockStatement\") ||\n        super.parseBlock(\n          allowDirectives,\n          createNewLexicalScope,\n          afterBlockParse,\n        )\n      );\n    }\n\n    parseFunctionId(\n      requireId?: boolean,\n    ): MaybePlaceholder<\"Identifier\"> | undefined | null {\n      return (\n        this.parsePlaceholder(\"Identifier\") || super.parseFunctionId(requireId)\n      );\n    }\n    // @ts-expect-error Plugin will override parser interface\n    parseClass<T extends N.Class>(\n      node: T,\n      isStatement: /* T === ClassDeclaration */ boolean,\n      optionalId?: boolean,\n    ): T {\n      const type = isStatement ? \"ClassDeclaration\" : \"ClassExpression\";\n\n      this.next();\n      const oldStrict = this.state.strict;\n\n      const placeholder = this.parsePlaceholder(\"Identifier\");\n      if (placeholder) {\n        if (\n          this.match(tt._extends) ||\n          this.match(tt.placeholder) ||\n          this.match(tt.braceL)\n        ) {\n          node.id = placeholder;\n        } else if (optionalId || !isStatement) {\n          node.id = null;\n          node.body = this.finishPlaceholder(placeholder, \"ClassBody\");\n          return this.finishNode(node, type);\n        } else {\n          throw this.raise(PlaceholderErrors.ClassNameIsRequired, {\n            at: this.state.startLoc,\n          });\n        }\n      } else {\n        this.parseClassId(node, isStatement, optionalId);\n      }\n\n      super.parseClassSuper(node);\n      node.body =\n        this.parsePlaceholder(\"ClassBody\") ||\n        super.parseClassBody(!!node.superClass, oldStrict);\n      return this.finishNode(node, type);\n    }\n\n    parseExport(node: N.Node, decorators: N.Decorator[] | null): N.AnyExport {\n      const placeholder = this.parsePlaceholder(\"Identifier\");\n      if (!placeholder) return super.parseExport(node, decorators);\n\n      if (!this.isContextual(tt._from) && !this.match(tt.comma)) {\n        // export %%DECL%%;\n        node.specifiers = [];\n        node.source = null;\n        node.declaration = this.finishPlaceholder(placeholder, \"Declaration\");\n        return this.finishNode(node, \"ExportNamedDeclaration\");\n      }\n\n      // export %%NAME%% from \"foo\";\n      this.expectPlugin(\"exportDefaultFrom\");\n      const specifier = this.startNode();\n      specifier.exported = placeholder;\n      node.specifiers = [this.finishNode(specifier, \"ExportDefaultSpecifier\")];\n\n      return super.parseExport(node, decorators);\n    }\n\n    isExportDefaultSpecifier(): boolean {\n      if (this.match(tt._default)) {\n        const next = this.nextTokenStart();\n        if (this.isUnparsedContextual(next, \"from\")) {\n          if (\n            this.input.startsWith(\n              tokenLabelName(tt.placeholder),\n              this.nextTokenStartSince(next + 4),\n            )\n          ) {\n            return true;\n          }\n        }\n      }\n      return super.isExportDefaultSpecifier();\n    }\n\n    maybeParseExportDefaultSpecifier(\n      node: Undone<\n        | N.ExportDefaultDeclaration\n        | N.ExportAllDeclaration\n        | N.ExportNamedDeclaration\n      >,\n      maybeDefaultIdentifier: N.Identifier | null,\n    ): node is Undone<N.ExportNamedDeclaration> {\n      if ((node as N.ExportNamedDeclaration).specifiers?.length) {\n        // \"export %%NAME%%\" has already been parsed by #parseExport.\n        return true;\n      }\n      return super.maybeParseExportDefaultSpecifier(\n        node,\n        maybeDefaultIdentifier,\n      );\n    }\n\n    checkExport(node: N.ExportNamedDeclaration): void {\n      const { specifiers } = node;\n      if (specifiers?.length) {\n        node.specifiers = specifiers.filter(\n          // @ts-expect-error placeholder typings\n          node => node.exported.type === \"Placeholder\",\n        );\n      }\n      super.checkExport(node);\n      node.specifiers = specifiers;\n    }\n\n    parseImport(\n      node: Undone<N.ImportDeclaration>,\n    ): N.ImportDeclaration | N.TsImportEqualsDeclaration {\n      const placeholder = this.parsePlaceholder(\"Identifier\");\n      if (!placeholder) return super.parseImport(node);\n\n      node.specifiers = [];\n\n      if (!this.isContextual(tt._from) && !this.match(tt.comma)) {\n        // import %%STRING%%;\n        node.source = this.finishPlaceholder(placeholder, \"StringLiteral\");\n        this.semicolon();\n        return this.finishNode(node, \"ImportDeclaration\");\n      }\n\n      // import %%DEFAULT%% ...\n      const specifier =\n        this.startNodeAtNode<N.ImportDefaultSpecifier>(placeholder);\n      specifier.local = placeholder;\n      node.specifiers.push(\n        this.finishNode(specifier, \"ImportDefaultSpecifier\"),\n      );\n\n      if (this.eat(tt.comma)) {\n        // import %%DEFAULT%%, * as ...\n        const hasStarImport = this.maybeParseStarImportSpecifier(node);\n\n        // import %%DEFAULT%%, { ...\n        if (!hasStarImport) this.parseNamedImportSpecifiers(node);\n      }\n\n      this.expectContextual(tt._from);\n      node.source = this.parseImportSource();\n      this.semicolon();\n      return this.finishNode(node, \"ImportDeclaration\");\n    }\n\n    parseImportSource(): MaybePlaceholder<\"StringLiteral\"> {\n      // import ... from %%STRING%%;\n\n      return (\n        this.parsePlaceholder(\"StringLiteral\") || super.parseImportSource()\n      );\n    }\n\n    // Throws if the current token and the prev one are separated by a space.\n    assertNoSpace(): void {\n      if (this.state.start > this.state.lastTokEndLoc.index) {\n        this.raise(PlaceholderErrors.UnexpectedSpace, {\n          at: this.state.lastTokEndLoc,\n        });\n      }\n    }\n  };\n"], "mappings": ";;;;;;AAEA,IAAAA,MAAA,GAAAC,OAAA;AAGA,IAAAC,WAAA,GAAAD,OAAA;AA2BA,MAAME,iBAAiB,GAAG,IAAAC,0BAAc,CAAC,cAAa,CAAC;EACrDC,mBAAmB,EAAE,2BAA2B;EAChDC,eAAe,EAAE;AACnB,CAAC,CAAC;AAAC,IAAAC,QAAA,GAIaC,UAAyB,IACvC,MAAMC,uBAAuB,SAASD,UAAU,CAAmB;EACjEE,gBAAgBA,CACdC,YAAe,EAC+C;IAC9D,IAAI,IAAI,CAACC,KAAK,IAAe,CAAC,EAAE;MAC9B,MAAMC,IAAI,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;MAC7B,IAAI,CAACC,IAAI,CAAC,CAAC;MACX,IAAI,CAACC,aAAa,CAAC,CAAC;MAIpBH,IAAI,CAACI,IAAI,GAAG,KAAK,CAACC,eAAe,CAAe,IAAI,CAAC;MAErD,IAAI,CAACF,aAAa,CAAC,CAAC;MACpB,IAAI,CAACG,MAAM,IAAe,CAAC;MAE3B,OAAO,IAAI,CAACC,iBAAiB,CAACP,IAAI,EAAEF,YAAY,CAAC;IACnD;EACF;EAEAS,iBAAiBA,CACfP,IAAY,EACZF,YAAe,EAC2B;IAC1C,MAAMU,UAAU,GAAG,CAAC,EAAER,IAAI,CAACF,YAAY,IAAIE,IAAI,CAACS,IAAI,KAAK,aAAa,CAAC;IACvET,IAAI,CAACF,YAAY,GAAGA,YAAY;IAGhC,OAAOU,UAAU,GAAGR,IAAI,GAAG,IAAI,CAACU,UAAU,CAACV,IAAI,EAAE,aAAa,CAAC;EACjE;EAMAW,gBAAgBA,CAACC,IAAY,EAAE;IAC7B,IACEA,IAAI,OAA0B,IAC9B,IAAI,CAACC,KAAK,CAACC,UAAU,CAAC,IAAI,CAACC,KAAK,CAACC,GAAG,GAAG,CAAC,CAAC,OAA0B,EACnE;MACA,IAAI,CAACC,QAAQ,MAAiB,CAAC,CAAC;IAClC,CAAC,MAAM;MACL,KAAK,CAACN,gBAAgB,CAACC,IAAI,CAAC;IAC9B;EACF;EAMAM,aAAaA,CACXC,mBAA6C,EACb;IAChC,OACE,IAAI,CAACtB,gBAAgB,CAAC,YAAY,CAAC,IACnC,KAAK,CAACqB,aAAa,CAACC,mBAAmB,CAAC;EAE5C;EAEAd,eAAeA,CAACe,OAAiB,EAAkC;IAKjE,OACE,IAAI,CAACvB,gBAAgB,CAAC,YAAY,CAAC,IAAI,KAAK,CAACQ,eAAe,CAACe,OAAO,CAAC;EAEzE;EAEAC,iBAAiBA,CACfC,IAAY,EACZC,QAAkB,EAClBC,aAAsB,EACtBC,SAAkB,EAClB;IAIA,IAAIH,IAAI,KAAKI,SAAS,EAAE;MACtB,KAAK,CAACL,iBAAiB,CAACC,IAAI,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,SAAS,CAAC;IACnE;EACF;EAMAE,gBAAgBA,CAAA,EAAgC;IAC9C,OAAO,IAAI,CAAC9B,gBAAgB,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC8B,gBAAgB,CAAC,CAAC;EACrE;EAEAC,WAAWA,CAACnB,IAAY,EAAEoB,eAAwB,EAAEC,OAAqB,EAAE;IACzE,OACErB,IAAI,KAAK,aAAa,IACtB,KAAK,CAACmB,WAAW,CAACnB,IAAI,EAAEoB,eAAe,EAAEC,OAAO,CAAC;EAErD;EAEAC,YAAYA,CAAC/B,IAAY,EAAEgC,KAAc,EAAQ;IAC/C,IACEhC,IAAI,IACJA,IAAI,CAACS,IAAI,KAAK,aAAa,IAC3BT,IAAI,CAACF,YAAY,KAAK,YAAY,EAClC;MACAE,IAAI,CAACF,YAAY,GAAG,SAAS;IAC/B,CAAC,MAAM;MACL,KAAK,CAACiC,YAAY,CAAC/B,IAAI,EAAEgC,KAAK,CAAC;IACjC;EACF;EAMAC,yBAAyBA,CAACC,EAAU,EAAElB,GAAW,EAAW;IAC1D,IAAI,KAAK,CAACiB,yBAAyB,CAACC,EAAE,EAAElB,GAAG,CAAC,EAAE;MAC5C,OAAO,IAAI;IACb;IAIA,MAAMmB,SAAS,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAClC,IAAID,SAAS,CAAC1B,IAAI,QAAmB,EAAE;MACrC,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEA4B,mBAAmBA,CACjBrC,IAA4C,EAC5CsC,OAAgB,EAChB;IAEA,IAAItC,IAAI,CAACuC,KAAK,IAAIvC,IAAI,CAACuC,KAAK,CAAC9B,IAAI,KAAK,aAAa,EAAE;IACrD,KAAK,CAAC4B,mBAAmB,CAACrC,IAAI,EAAEsC,OAAO,CAAC;EAC1C;EAGAE,wBAAwBA,CACtBxC,IAAmC,EACnCyC,IAAkB,EACa;IAC/B,IACEA,IAAI,CAAChC,IAAI,KAAK,aAAa,IAC1BgC,IAAI,CAACC,KAAK,IAAID,IAAI,CAACC,KAAK,CAACC,aAAc,EACxC;MAEA,OAAO,KAAK,CAACH,wBAAwB,CAACxC,IAAI,EAAEyC,IAAI,CAAC;IACnD;IAEA,IAAI,IAAI,CAAC1C,KAAK,GAAS,CAAC,EAAE;MAExB,MAAM6C,IAAwB,GAAG5C,IAAI;MACrC4C,IAAI,CAACL,KAAK,GAAG,IAAI,CAAChC,iBAAiB,CAACkC,IAAI,EAAE,YAAY,CAAC;MACvD,IAAI,CAACvC,IAAI,CAAC,CAAC;MACX0C,IAAI,CAACC,IAAI,GAAG,KAAK,CAACC,+CAA+C,CAAC,CAAC;MACnE,OAAO,IAAI,CAACpC,UAAU,CAACkC,IAAI,EAAE,kBAAkB,CAAC;IAClD;IAEA,IAAI,CAACG,SAAS,CAAC,CAAC;IAChB/C,IAAI,CAACI,IAAI,GAAGqC,IAAI,CAACrC,IAAI;IACrB,OAAO,IAAI,CAACG,iBAAiB,CAACP,IAAI,EAAE,WAAW,CAAC;EAClD;EAEAgD,UAAUA,CACRC,eAAyB,EACzBC,qBAA+B,EAC/BC,eAA2D,EACvB;IACpC,OACE,IAAI,CAACtD,gBAAgB,CAAC,gBAAgB,CAAC,IACvC,KAAK,CAACmD,UAAU,CACdC,eAAe,EACfC,qBAAqB,EACrBC,eACF,CAAC;EAEL;EAEAC,eAAeA,CACbC,SAAmB,EACgC;IACnD,OACE,IAAI,CAACxD,gBAAgB,CAAC,YAAY,CAAC,IAAI,KAAK,CAACuD,eAAe,CAACC,SAAS,CAAC;EAE3E;EAEAC,UAAUA,CACRtD,IAAO,EACPuD,WAAiD,EACjDC,UAAoB,EACjB;IACH,MAAM/C,IAAI,GAAG8C,WAAW,GAAG,kBAAkB,GAAG,iBAAiB;IAEjE,IAAI,CAACrD,IAAI,CAAC,CAAC;IACX,MAAMuD,SAAS,GAAG,IAAI,CAAC1C,KAAK,CAAC2C,MAAM;IAEnC,MAAMC,WAAW,GAAG,IAAI,CAAC9D,gBAAgB,CAAC,YAAY,CAAC;IACvD,IAAI8D,WAAW,EAAE;MACf,IACE,IAAI,CAAC5D,KAAK,GAAY,CAAC,IACvB,IAAI,CAACA,KAAK,IAAe,CAAC,IAC1B,IAAI,CAACA,KAAK,EAAU,CAAC,EACrB;QACAC,IAAI,CAAC4D,EAAE,GAAGD,WAAW;MACvB,CAAC,MAAM,IAAIH,UAAU,IAAI,CAACD,WAAW,EAAE;QACrCvD,IAAI,CAAC4D,EAAE,GAAG,IAAI;QACd5D,IAAI,CAAC6C,IAAI,GAAG,IAAI,CAACtC,iBAAiB,CAACoD,WAAW,EAAE,WAAW,CAAC;QAC5D,OAAO,IAAI,CAACjD,UAAU,CAACV,IAAI,EAAES,IAAI,CAAC;MACpC,CAAC,MAAM;QACL,MAAM,IAAI,CAACoD,KAAK,CAACvE,iBAAiB,CAACE,mBAAmB,EAAE;UACtDsE,EAAE,EAAE,IAAI,CAAC/C,KAAK,CAACQ;QACjB,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACL,IAAI,CAACwC,YAAY,CAAC/D,IAAI,EAAEuD,WAAW,EAAEC,UAAU,CAAC;IAClD;IAEA,KAAK,CAACQ,eAAe,CAAChE,IAAI,CAAC;IAC3BA,IAAI,CAAC6C,IAAI,GACP,IAAI,CAAChD,gBAAgB,CAAC,WAAW,CAAC,IAClC,KAAK,CAACoE,cAAc,CAAC,CAAC,CAACjE,IAAI,CAACL,UAAU,EAAE8D,SAAS,CAAC;IACpD,OAAO,IAAI,CAAC/C,UAAU,CAACV,IAAI,EAAES,IAAI,CAAC;EACpC;EAEAyD,WAAWA,CAAClE,IAAY,EAAEmE,UAAgC,EAAe;IACvE,MAAMR,WAAW,GAAG,IAAI,CAAC9D,gBAAgB,CAAC,YAAY,CAAC;IACvD,IAAI,CAAC8D,WAAW,EAAE,OAAO,KAAK,CAACO,WAAW,CAAClE,IAAI,EAAEmE,UAAU,CAAC;IAE5D,IAAI,CAAC,IAAI,CAACC,YAAY,GAAS,CAAC,IAAI,CAAC,IAAI,CAACrE,KAAK,GAAS,CAAC,EAAE;MAEzDC,IAAI,CAACqE,UAAU,GAAG,EAAE;MACpBrE,IAAI,CAACsE,MAAM,GAAG,IAAI;MAClBtE,IAAI,CAACuE,WAAW,GAAG,IAAI,CAAChE,iBAAiB,CAACoD,WAAW,EAAE,aAAa,CAAC;MACrE,OAAO,IAAI,CAACjD,UAAU,CAACV,IAAI,EAAE,wBAAwB,CAAC;IACxD;IAGA,IAAI,CAACwE,YAAY,CAAC,mBAAmB,CAAC;IACtC,MAAMC,SAAS,GAAG,IAAI,CAACxE,SAAS,CAAC,CAAC;IAClCwE,SAAS,CAACC,QAAQ,GAAGf,WAAW;IAChC3D,IAAI,CAACqE,UAAU,GAAG,CAAC,IAAI,CAAC3D,UAAU,CAAC+D,SAAS,EAAE,wBAAwB,CAAC,CAAC;IAExE,OAAO,KAAK,CAACP,WAAW,CAAClE,IAAI,EAAEmE,UAAU,CAAC;EAC5C;EAEAQ,wBAAwBA,CAAA,EAAY;IAClC,IAAI,IAAI,CAAC5E,KAAK,GAAY,CAAC,EAAE;MAC3B,MAAMG,IAAI,GAAG,IAAI,CAAC0E,cAAc,CAAC,CAAC;MAClC,IAAI,IAAI,CAACC,oBAAoB,CAAC3E,IAAI,EAAE,MAAM,CAAC,EAAE;QAC3C,IACE,IAAI,CAACW,KAAK,CAACiE,UAAU,CACnB,IAAAC,qBAAc,KAAe,CAAC,EAC9B,IAAI,CAACC,mBAAmB,CAAC9E,IAAI,GAAG,CAAC,CACnC,CAAC,EACD;UACA,OAAO,IAAI;QACb;MACF;IACF;IACA,OAAO,KAAK,CAACyE,wBAAwB,CAAC,CAAC;EACzC;EAEAM,gCAAgCA,CAC9BjF,IAIC,EACDkF,sBAA2C,EACD;IAAA,IAAAC,WAAA;IAC1C,KAAAA,WAAA,GAAKnF,IAAI,CAA8BqE,UAAU,aAA7Cc,WAAA,CAA+CC,MAAM,EAAE;MAEzD,OAAO,IAAI;IACb;IACA,OAAO,KAAK,CAACH,gCAAgC,CAC3CjF,IAAI,EACJkF,sBACF,CAAC;EACH;EAEAG,WAAWA,CAACrF,IAA8B,EAAQ;IAChD,MAAM;MAAEqE;IAAW,CAAC,GAAGrE,IAAI;IAC3B,IAAIqE,UAAU,YAAVA,UAAU,CAAEe,MAAM,EAAE;MACtBpF,IAAI,CAACqE,UAAU,GAAGA,UAAU,CAACiB,MAAM,CAEjCtF,IAAI,IAAIA,IAAI,CAAC0E,QAAQ,CAACjE,IAAI,KAAK,aACjC,CAAC;IACH;IACA,KAAK,CAAC4E,WAAW,CAACrF,IAAI,CAAC;IACvBA,IAAI,CAACqE,UAAU,GAAGA,UAAU;EAC9B;EAEAkB,WAAWA,CACTvF,IAAiC,EACkB;IACnD,MAAM2D,WAAW,GAAG,IAAI,CAAC9D,gBAAgB,CAAC,YAAY,CAAC;IACvD,IAAI,CAAC8D,WAAW,EAAE,OAAO,KAAK,CAAC4B,WAAW,CAACvF,IAAI,CAAC;IAEhDA,IAAI,CAACqE,UAAU,GAAG,EAAE;IAEpB,IAAI,CAAC,IAAI,CAACD,YAAY,GAAS,CAAC,IAAI,CAAC,IAAI,CAACrE,KAAK,GAAS,CAAC,EAAE;MAEzDC,IAAI,CAACsE,MAAM,GAAG,IAAI,CAAC/D,iBAAiB,CAACoD,WAAW,EAAE,eAAe,CAAC;MAClE,IAAI,CAACZ,SAAS,CAAC,CAAC;MAChB,OAAO,IAAI,CAACrC,UAAU,CAACV,IAAI,EAAE,mBAAmB,CAAC;IACnD;IAGA,MAAMyE,SAAS,GACb,IAAI,CAACe,eAAe,CAA2B7B,WAAW,CAAC;IAC7Dc,SAAS,CAACgB,KAAK,GAAG9B,WAAW;IAC7B3D,IAAI,CAACqE,UAAU,CAACqB,IAAI,CAClB,IAAI,CAAChF,UAAU,CAAC+D,SAAS,EAAE,wBAAwB,CACrD,CAAC;IAED,IAAI,IAAI,CAACkB,GAAG,GAAS,CAAC,EAAE;MAEtB,MAAMC,aAAa,GAAG,IAAI,CAACC,6BAA6B,CAAC7F,IAAI,CAAC;MAG9D,IAAI,CAAC4F,aAAa,EAAE,IAAI,CAACE,0BAA0B,CAAC9F,IAAI,CAAC;IAC3D;IAEA,IAAI,CAAC+F,gBAAgB,GAAS,CAAC;IAC/B/F,IAAI,CAACsE,MAAM,GAAG,IAAI,CAAC0B,iBAAiB,CAAC,CAAC;IACtC,IAAI,CAACjD,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI,CAACrC,UAAU,CAACV,IAAI,EAAE,mBAAmB,CAAC;EACnD;EAEAgG,iBAAiBA,CAAA,EAAsC;IAGrD,OACE,IAAI,CAACnG,gBAAgB,CAAC,eAAe,CAAC,IAAI,KAAK,CAACmG,iBAAiB,CAAC,CAAC;EAEvE;EAGA7F,aAAaA,CAAA,EAAS;IACpB,IAAI,IAAI,CAACY,KAAK,CAACkF,KAAK,GAAG,IAAI,CAAClF,KAAK,CAACmF,aAAa,CAACC,KAAK,EAAE;MACrD,IAAI,CAACtC,KAAK,CAACvE,iBAAiB,CAACG,eAAe,EAAE;QAC5CqE,EAAE,EAAE,IAAI,CAAC/C,KAAK,CAACmF;MACjB,CAAC,CAAC;IACJ;EACF;AACF,CAAC;AAAAE,OAAA,CAAAC,OAAA,GAAA3G,QAAA"}