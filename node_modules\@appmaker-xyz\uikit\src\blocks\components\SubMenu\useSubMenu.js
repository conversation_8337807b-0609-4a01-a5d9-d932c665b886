import { useState } from 'react';

export function useMenuItems({ innerBlocks }) {
  const allMenu = innerBlocks;
  const [currentItems, setCurrentItems] = useState(allMenu);
  const [breadCrumb, setBreadCrumb] = useState([
    {
      attributes: {
        text: 'Main menu',
      },
    },
  ]);
  function setInnerBlocks(currentBlock, innerBlocks) {
    setCurrentItems(innerBlocks);
    setBreadCrumb([...breadCrumb, currentBlock]);
  }
  function onPressBracumb(item, index) {
    const newBreadCrumb = [...breadCrumb];
    newBreadCrumb.splice(index + 1);
    if (newBreadCrumb.length === 1) {
      setBreadCrumb(newBreadCrumb);
      setCurrentItems(allMenu);
    } else {
      setBreadCrumb(newBreadCrumb);
      setCurrentItems(item.innerBlocks);
    }
  }
  function resetMenu() {
    setBreadCrumb([
      {
        attributes: {
          text: 'Main menu',
        },
      },
    ]);
    setCurrentItems(allMenu);
  }
  return {
    items: currentItems,
    breadCrumb,
    setCurrentItems: setInnerBlocks,
    onPressBracumb,
    resetMenu,
  };
}
// function useBreadCrum
