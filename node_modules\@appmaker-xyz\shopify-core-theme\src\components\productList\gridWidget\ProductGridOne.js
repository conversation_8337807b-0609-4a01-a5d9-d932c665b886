import React from 'react';
import { FlatList, StyleSheet } from 'react-native';
import { appSettings } from '@appmaker-xyz/core';
import {
  useProductListItem,
  useProductWishList,
  useProductVariations,
  ProductBadge,
  SlotBlock,
} from '@appmaker-xyz/shopify';
import {
  Layout,
  ThemeText,
  AppTouchable,
  AppImage,
  Badge,
} from '@appmaker-xyz/ui';
import { appmaker } from '@appmaker-xyz/core';
import Icon from 'react-native-vector-icons/AntDesign';
import ProductGridCTA from './components/ProductGridCTA';
import { Images } from '../../../../assets/images';

const ProductGridWidget = (props) => {
  const {
    clientId,
    attributes,
    pageDispatch,
    onPress,
    onAction,
    blockData,
    data = {},
    addCartButton,
    blocksViewItemIndex,
    BlockItem,
  } = props;
  const ReviewsComp = appmaker.applyFilters(
    'appmaker-product-grid-reviews',
    () => null,
  );
  const {
    priceSale = '0',
    wishList,
    numColumns,
    attribute,
    appmakerAction,
    titleNumberOfLines,
    __appmakerCustomStyles = {},
  } = attributes;
  const { toggleWishList, isSaved } = useProductWishList({
    onAction,
    blockData,
  });
  const {
    gridViewListing,
    average_rating,
    productType,
    imageRatio,
    salePrice,
    regularPrice,
    onSale,
    salePercentage,
    outOfStock,
    imageUrl: uri,
    title,
    addToCart,
    updateCart,
    isAddToCartLoading,
    openProduct,
    onQuantityChange,
    containerWidth,
    isDisablePurchase,
    product,
    vendorName,
    showVendorName,
    showVariations,
    preOrderEnabled,
  } = useProductListItem({
    attributes,
    onAction,
    blockData,
    pageDispatch,
    blocksViewItemIndex,
  });
  const {
    options,
    setOptions,
    variant,
    selectedVariant,
    imageUrl: variantUrl,
    selectedOptions,
    salePercentage: variantSalePercentage,
    onSale: variantOnSale,
    regularPriceWithCurrency: variantRegularPriceWithCurrency,
    salePriceWithCurrency: variantSalePriceWithCurrency,
    isVariantAvailable,
  } = useProductVariations({
    attributes,
    onAction,
    blockData,
    pageDispatch,
    ...props,
  });

  let imageRatioSettings = appmaker.applyFilters(
    'product-grid-image-ratio',
    imageRatio,
  );
  const imageResize = appmaker.applyFilters(
    'product-grid-image-resize-mode',
    appSettings.getOptionAsBoolean('product_list_type') ? 'contain' : 'cover',
  );

  const aspectRatio = imageRatioSettings || imageRatio;
  const numberOfLines = parseFloat(titleNumberOfLines);
  const in_stock = !outOfStock;
  const ratingBadgeColor = () => {
    let rating = average_rating;
    switch (true) {
      case rating >= '3.5':
        return '#16A34A';
      case rating >= '2.5':
        return '#F59E0B';
      default:
        return '#4B5563';
    }
  };

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  let attributeView = null;
  if (attribute) {
    attributeView = attribute.map((value) => {
      return (
        <Layout style={styles.attributesListing}>
          <Badge
            text={value.label}
            color="#4B5563"
            style={styles.attributeBadgeStyle}
          />
        </Layout>
      );
    });
  }

  const containerStyle = [styles.container];
  const imageContainerStyle = [styles.imageContainer];
  const contentContainerStyle = [styles.contentContainer];
  const scrollContainerStyle = [styles.scrollContainer];
  if (containerWidth) {
    scrollContainerStyle.push({ width: containerWidth });
  }

  if (gridViewListing === true) {
    containerStyle.push({ marginLeft: 0 });
    imageContainerStyle.push({ width: '100%' });
    contentContainerStyle.push({ width: '100%' });
  }

  const gridViewListingStyle = {
    width: `${100 / numColumns}%`,
    padding: 4,
  };

  if (aspectRatio) {
    imageContainerStyle.push({
      aspectRatio: 1 / aspectRatio,
    });
  }

  const formattedData = options.flatMap((option) =>
    Object.values(option?.values).map((item) => ({
      optionLabel: option.label,
      itemData: item,
    })),
  );

  const handleOptionPress = (optionLabel, itemId) => {
    setOptions({ [optionLabel]: itemId });
  };

  const variationRenderItem = ({ item, index }) => {
    const { optionLabel, itemData } = item;
    const isSelected = selectedOptions[optionLabel] === itemData.id;
    const itemStyle = isSelected
      ? __appmakerCustomStyles?.variantItemSelected ||
        styles.variantItemSelected
      : __appmakerCustomStyles?.variantItem || styles.variantItem;
    return (
      <AppTouchable
        key={index}
        onPress={() => {
          handleOptionPress(optionLabel, itemData.id);
        }}>
        <Layout
          style={itemData?.isAvailable ? itemStyle : styles.mutedVariantItem}>
          <ThemeText
            numberOfLines={1}
            size="sm"
            fontFamily={'medium'}
            style={__appmakerCustomStyles?.variantText}>
            {itemData.label}
          </ThemeText>
          {itemData?.isAvailable ? null : (
            <AppImage src={Images.cross_line} style={styles.muteImage} />
          )}
        </Layout>
      </AppTouchable>
    );
  };

  const showVariationChips =
    showVariations &&
    (addCartButton === 'add-cart-button' || addCartButton === 'grocery-mode');
  const productOnSale = showVariationChips ? variantOnSale : onSale;
  const productSalePercentage = showVariationChips
    ? variantSalePercentage
    : salePercentage;

  return (
    <AppTouchable
      onPress={openProduct}
      style={gridViewListing ? gridViewListingStyle : scrollContainerStyle}
      clientId={clientId}>
      <Layout style={containerStyle}>
        <Layout style={imageContainerStyle}>
          <AppImage
            uri={showVariationChips ? variantUrl : uri}
            style={styles.scrollerImage}
            resizeMode={imageResize}
          />
          <ProductBadge product={product} slot="top-right" type="list" />

          <Layout style={styles.badgeContainer}>
            <Layout style={styles.topLeftBlocks}>
              {productOnSale && salePercentage ? (
                <Badge
                  style={styles.saleBadgeStyle}
                  color="#2FA478"
                  text={`${productSalePercentage} OFF`}
                  customStyles={__appmakerCustomStyles?.sale_badge}
                />
              ) : null}
              {average_rating && average_rating > 0 ? (
                <Badge
                  style={styles.saleBadgeStyle}
                  text={average_rating}
                  color={ratingBadgeColor()}
                  iconName="star"
                  customStyles={__appmakerCustomStyles?.rating_badge}
                />
              ) : null}
              {ReviewsComp ? <ReviewsComp attributes={attributes} /> : null}
              <ProductBadge product={product} slot="top-left" type="list" />
            </Layout>
            <Layout style={styles.topRightBlocks}>
              <ProductBadge product={product} slot="top-right" type="list" />
            </Layout>
          </Layout>
          {!in_stock && !preOrderEnabled ? (
            <Badge
              style={styles.stockOutBadgeStyle}
              color="#DC2626"
              text="Out of Stock"
              full={true}
              customStyles={__appmakerCustomStyles?.stock_out_badge}
            />
          ) : null}
        </Layout>
        <Layout style={contentContainerStyle}>
          {showVendorName && vendorName ? (
            <ThemeText
              size="sm"
              fontFamily="medium"
              color={'#4B5563'}
              style={styles.aboveTitleText}>
              {vendorName}
            </ThemeText>
          ) : null}
          <Layout style={styles.titleContainer}>
            <ThemeText
              clientId={`${clientId}-product-name`}
              style={styles.growShrink}
              fontFamily="medium"
              numberOfLines={
                isNaN(numberOfLines) == false ? numberOfLines : null
              }>
              {title}
            </ThemeText>
            {wishList === true ? (
              <Icon
                name={isSaved === false ? 'hearto' : 'heart'}
                color={isSaved === false ? '#A9AEB7' : '#1B1B1B'}
                size={18}
                style={styles.wishListIcon}
                onPress={() => {
                  toggleWishList && toggleWishList();
                }}
              />
            ) : null}
          </Layout>
          <Layout style={styles.priceDataContainer}>
            <ThemeText fontFamily="bold">
              {showVariationChips ? variantSalePriceWithCurrency : salePrice}
            </ThemeText>
            <ThemeText
              hideText={!productOnSale}
              style={styles.offerPrice}
              color="#475569">
              {showVariationChips
                ? variantRegularPriceWithCurrency
                : regularPrice}
            </ThemeText>
          </Layout>
          <SlotBlock slotId="grid-item-below-price" {...props} />
          {attribute ? (
            <Layout style={styles.attributesContainer}>{attributeView}</Layout>
          ) : null}
          {showVariationChips ? (
            <FlatList
              data={formattedData}
              renderItem={variationRenderItem}
              keyExtractor={(item, index) => `${item.optionLabel}_${index}`}
              contentContainerStyle={
                gridViewListing ? {} : styles.variationContainer
              }
              style={gridViewListing ? styles.variationContainer : {}}
              showsVerticalScrollIndicator={false}
              // horizontal={true}
              // showsHorizontalScrollIndicator={false}
              // ItemSeparatorComponent={() => <Layout style={styles.separator} />}
            />
          ) : null}
        </Layout>

        {isDisablePurchase ? null : (
          <ProductGridCTA
            showVariations={showVariationChips}
            addCartButtonType={addCartButton}
            productType={productType}
            inStock={showVariationChips ? isVariantAvailable : in_stock}
            variant={variant}
            isAddToCartLoading={isAddToCartLoading}
            toggleWishList={toggleWishList}
            isSaved={isSaved}
            onQuantityChange={onQuantityChange}
            updateCart={updateCart}
            openProduct={openProduct}
            addToCart={addToCart}
            buttonStyle={styles.buttonStyle}
            customStyles={__appmakerCustomStyles?.stepper}
            preOrderEnabled={preOrderEnabled}
          />
        )}
      </Layout>
    </AppTouchable>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginLeft: 12, //0 for grid
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#F8FAFC',
  },
  imageContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 6,
    overflow: 'hidden',
    position: 'relative',
    aspectRatio: 1,
  },
  scrollerImage: {
    width: '100%',
    height: '100%',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  wishListIcon: {
    padding: 4,
    opacity: 0.8,
  },
  saleBadgeStyle: {
    marginRight: 4,
    marginTop: 4,
    opacity: 0.8,
  },
  stockOutBadgeStyle: {
    position: 'absolute',
    left: 4,
    right: 4,
    bottom: 4,
    opacity: 0.8,
    textAlign: 'center',
  },
  badgeContainer: {
    position: 'absolute',
    flexDirection: 'row',
    flexWrap: 'wrap',
    top: 0,
    justifyContent: 'space-between',
    width: '100%',
    padding: 2,
  },
  topLeftBlocks: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  topRightBlocks: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  contentContainer: {
    paddingVertical: 8,
    paddingHorizontal: 4,
    flex: 1,
  },
  growShrink: {
    flexGrow: 1,
    flexShrink: 1,
  },
  aboveTitleText: {
    marginBottom: 4,
  },
  priceDataContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginBottom: 4,
    marginVertical: 4,
  },
  offerPrice: {
    textDecorationLine: 'line-through',
    marginLeft: 4,
  },
  attributesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  attributesListing: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  attributeBadgeStyle: {
    marginRight: 4,
    marginBottom: 4,
  },
  buttonStyle: {
    marginBottom: 4,
    marginHorizontal: 4,
  },
  variantItemSelected: {
    marginRight: 4,
    marginBottom: 4,
    borderWidth: 1.5,
    borderRadius: 4,
    paddingVertical: 3.5,
    paddingHorizontal: 5.5,
    position: 'relative',
    overflow: 'hidden',
  },
  variantItem: {
    marginRight: 4,
    marginBottom: 4,
    borderWidth: 1,
    borderRadius: 4,
    paddingVertical: 4,
    paddingHorizontal: 6,
    opacity: 0.6,
    position: 'relative',
    overflow: 'hidden',
  },
  variationContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  separator: {
    width: 4,
  },
  muteImage: {
    width: '120%',
    height: '130%',
    position: 'absolute',
    zIndex: 10,
    resizeMode: 'stretch',
    left: 0,
  },
  mutedVariantItem: {
    marginRight: 4,
    marginBottom: 4,
    opacity: 0.4,
    borderWidth: 1,
    borderRadius: 4,
    paddingVertical: 4,
    paddingHorizontal: 6,
    position: 'relative',
    overflow: 'hidden',
  },
});

export default ProductGridWidget;
