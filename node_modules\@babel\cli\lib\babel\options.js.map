{"version": 3, "names": ["_fs", "data", "require", "_commander", "_core", "_glob", "commander", "option", "collect", "booleanify", "undefined", "DEFAULT_EXTENSIONS", "join", "version", "usage", "action", "parseArgv", "args", "parse", "errors", "filenames", "reduce", "globbed", "input", "files", "glob", "sync", "length", "push", "Array", "from", "Set", "for<PERSON>ach", "filename", "fs", "existsSync", "outDir", "outFile", "relative", "watch", "skipInitialBuild", "deleteDirOnStart", "verbose", "quiet", "babelrc", "keepFileExtension", "outFileExtension", "console", "error", "e", "opts", "babelOptions", "presets", "plugins", "rootMode", "configFile", "envName", "sourceType", "ignore", "only", "retainLines", "compact", "minified", "auxiliaryCommentBefore", "auxiliaryCommentAfter", "sourceMaps", "sourceFileName", "sourceRoot", "highlightCode", "comments", "Object", "assign", "moduleRoot", "moduleIds", "moduleId", "key", "keys", "cliOptions", "extensions", "copyFiles", "copyIgnored", "includeDotfiles", "sourceMapTarget", "val", "value", "previousValue", "values", "split"], "sources": ["../../src/babel/options.ts"], "sourcesContent": ["import fs from \"fs\";\n\nimport commander from \"commander\";\nimport { version, DEFAULT_EXTENSIONS } from \"@babel/core\";\nimport glob from \"glob\";\n\nimport type { InputOptions } from \"@babel/core\";\n\n// Standard Babel input configs.\ncommander.option(\n  \"-f, --filename [filename]\",\n  \"The filename to use when reading from stdin. This will be used in source-maps, errors etc.\",\n);\ncommander.option(\n  \"--presets [list]\",\n  \"A comma-separated list of preset names.\",\n  collect,\n);\ncommander.option(\n  \"--plugins [list]\",\n  \"A comma-separated list of plugin names.\",\n  collect,\n);\ncommander.option(\"--config-file [path]\", \"Path to a .babelrc file to use.\");\ncommander.option(\n  \"--env-name [name]\",\n  \"The name of the 'env' to use when loading configs and plugins. \" +\n    \"Defaults to the value of BABEL_ENV, or else NODE_ENV, or else 'development'.\",\n);\ncommander.option(\n  \"--root-mode [mode]\",\n  \"The project-root resolution mode. \" +\n    \"One of 'root' (the default), 'upward', or 'upward-optional'.\",\n);\n\n// Basic file input configuration.\ncommander.option(\"--source-type [script|module]\", \"\");\ncommander.option(\n  \"--no-babelrc\",\n  \"Whether or not to look up .babelrc and .babelignore files.\",\n);\ncommander.option(\n  \"--ignore [list]\",\n  \"List of glob paths to **not** compile.\",\n  collect,\n);\ncommander.option(\n  \"--only [list]\",\n  \"List of glob paths to **only** compile.\",\n  collect,\n);\n\n// Misc babel config.\ncommander.option(\n  \"--no-highlight-code\",\n  \"Enable or disable ANSI syntax highlighting of code frames. (on by default)\",\n);\n\n// General output formatting.\ncommander.option(\n  \"--no-comments\",\n  \"Write comments to generated output. (true by default)\",\n);\ncommander.option(\n  \"--retain-lines\",\n  \"Retain line numbers. This will result in really ugly code.\",\n);\ncommander.option(\n  \"--compact [true|false|auto]\",\n  \"Do not include superfluous whitespace characters and line terminators.\",\n  booleanify,\n);\ncommander.option(\n  \"--minified\",\n  \"Save as many bytes when printing. (false by default)\",\n);\ncommander.option(\n  \"--auxiliary-comment-before [string]\",\n  \"Print a comment before any injected non-user code.\",\n);\ncommander.option(\n  \"--auxiliary-comment-after [string]\",\n  \"Print a comment after any injected non-user code.\",\n);\n\n// General source map formatting.\ncommander.option(\n  \"-s, --source-maps [true|false|inline|both]\",\n  \"\",\n  booleanify,\n  undefined,\n);\ncommander.option(\n  \"--source-map-target [string]\",\n  \"Set `file` on returned source map.\",\n);\ncommander.option(\n  \"--source-file-name [string]\",\n  \"Set `sources[0]` on returned source map.\",\n);\ncommander.option(\n  \"--source-root [filename]\",\n  \"The root from which all sources are relative.\",\n);\n\nif (!process.env.BABEL_8_BREAKING) {\n  // Config params for certain module output formats.\n  commander.option(\n    \"--module-root [filename]\",\n    \"Optional prefix for the AMD module formatter that will be prepended to the filename on module definitions.\",\n  );\n  commander.option(\"-M, --module-ids\", \"Insert an explicit id for modules.\");\n  commander.option(\n    \"--module-id [string]\",\n    \"Specify a custom name for module ids.\",\n  );\n}\n\n// \"babel\" command specific arguments that are not passed to @babel/core.\ncommander.option(\n  \"-x, --extensions [extensions]\",\n  \"List of extensions to compile when a directory has been the input. [\" +\n    DEFAULT_EXTENSIONS.join() +\n    \"]\",\n  collect,\n);\ncommander.option(\n  \"--keep-file-extension\",\n  \"Preserve the file extensions of the input files.\",\n);\ncommander.option(\"-w, --watch\", \"Recompile files on changes.\");\ncommander.option(\n  \"--skip-initial-build\",\n  \"Do not compile files before watching.\",\n);\ncommander.option(\n  \"-o, --out-file [out]\",\n  \"Compile all input files into a single file.\",\n);\ncommander.option(\n  \"-d, --out-dir [out]\",\n  \"Compile an input directory of modules into an output directory.\",\n);\ncommander.option(\n  \"--relative\",\n  \"Compile into an output directory relative to input directory or file. Requires --out-dir [out]\",\n);\n\ncommander.option(\n  \"-D, --copy-files\",\n  \"When compiling a directory copy over non-compilable files.\",\n);\ncommander.option(\n  \"--include-dotfiles\",\n  \"Include dotfiles when compiling and copying non-compilable files.\",\n);\ncommander.option(\n  \"--no-copy-ignored\",\n  \"Exclude ignored files when copying non-compilable files.\",\n);\n\ncommander.option(\n  \"--verbose\",\n  \"Log everything. This option conflicts with --quiet\",\n);\ncommander.option(\n  \"--quiet\",\n  \"Don't log anything. This option conflicts with --verbose\",\n);\ncommander.option(\n  \"--delete-dir-on-start\",\n  \"Delete the out directory before compilation.\",\n);\ncommander.option(\n  \"--out-file-extension [string]\",\n  \"Use a specific extension for the output files\",\n);\n\ncommander.version(PACKAGE_JSON.version + \" (@babel/core \" + version + \")\");\ncommander.usage(\"[options] <files ...>\");\n// register an empty action handler so that commander.js can throw on\n// unknown options _after_ args\n// see https://github.com/tj/commander.js/issues/561#issuecomment-522209408\ncommander.action(() => {});\n\nexport type CmdOptions = {\n  babelOptions: InputOptions;\n  cliOptions: {\n    filename: string;\n    filenames: string[];\n    extensions: string[];\n    keepFileExtension: boolean;\n    outFileExtension: string;\n    watch: boolean;\n    skipInitialBuild: boolean;\n    outFile: string;\n    outDir: string;\n    relative: boolean;\n    copyFiles: boolean;\n    copyIgnored: boolean;\n    includeDotfiles: boolean;\n    verbose: boolean;\n    quiet: boolean;\n    deleteDirOnStart: boolean;\n    sourceMapTarget: string;\n  };\n};\n\nexport default function parseArgv(args: Array<string>): CmdOptions | null {\n  //\n  commander.parse(args);\n\n  const errors: string[] = [];\n\n  let filenames = commander.args.reduce(function (globbed: string[], input) {\n    let files = glob.sync(input);\n    if (!files.length) files = [input];\n    globbed.push(...files);\n    return globbed;\n  }, []);\n\n  filenames = Array.from(new Set(filenames));\n\n  filenames.forEach(function (filename) {\n    if (!fs.existsSync(filename)) {\n      errors.push(filename + \" does not exist\");\n    }\n  });\n\n  if (commander.outDir && !filenames.length) {\n    errors.push(\"--out-dir requires filenames\");\n  }\n\n  if (commander.outFile && commander.outDir) {\n    errors.push(\"--out-file and --out-dir cannot be used together\");\n  }\n\n  if (commander.relative && !commander.outDir) {\n    errors.push(\"--relative requires --out-dir usage\");\n  }\n\n  if (commander.watch) {\n    if (!commander.outFile && !commander.outDir) {\n      errors.push(\"--watch requires --out-file or --out-dir\");\n    }\n\n    if (!filenames.length) {\n      errors.push(\"--watch requires filenames\");\n    }\n  }\n\n  if (commander.skipInitialBuild && !commander.watch) {\n    errors.push(\"--skip-initial-build requires --watch\");\n  }\n  if (commander.deleteDirOnStart && !commander.outDir) {\n    errors.push(\"--delete-dir-on-start requires --out-dir\");\n  }\n\n  if (commander.verbose && commander.quiet) {\n    errors.push(\"--verbose and --quiet cannot be used together\");\n  }\n\n  if (\n    !commander.outDir &&\n    filenames.length === 0 &&\n    typeof commander.filename !== \"string\" &&\n    commander.babelrc !== false\n  ) {\n    errors.push(\n      \"stdin compilation requires either -f/--filename [filename] or --no-babelrc\",\n    );\n  }\n\n  if (commander.keepFileExtension && commander.outFileExtension) {\n    errors.push(\n      \"--out-file-extension cannot be used with --keep-file-extension\",\n    );\n  }\n\n  if (errors.length) {\n    console.error(\"babel:\");\n    errors.forEach(function (e) {\n      console.error(\"  \" + e);\n    });\n    return null;\n  }\n\n  const opts = commander.opts();\n\n  const babelOptions: InputOptions = {\n    presets: opts.presets,\n    plugins: opts.plugins,\n    rootMode: opts.rootMode,\n    configFile: opts.configFile,\n    envName: opts.envName,\n    sourceType: opts.sourceType,\n    ignore: opts.ignore,\n    only: opts.only,\n    retainLines: opts.retainLines,\n    compact: opts.compact,\n    minified: opts.minified,\n    auxiliaryCommentBefore: opts.auxiliaryCommentBefore,\n    auxiliaryCommentAfter: opts.auxiliaryCommentAfter,\n    sourceMaps: opts.sourceMaps,\n    sourceFileName: opts.sourceFileName,\n    sourceRoot: opts.sourceRoot,\n\n    // Commander will default the \"--no-\" arguments to true, but we want to\n    // leave them undefined so that @babel/core can handle the\n    // default-assignment logic on its own.\n    babelrc: opts.babelrc === true ? undefined : opts.babelrc,\n    highlightCode: opts.highlightCode === true ? undefined : opts.highlightCode,\n    comments: opts.comments === true ? undefined : opts.comments,\n  };\n\n  if (!process.env.BABEL_8_BREAKING) {\n    Object.assign(babelOptions, {\n      moduleRoot: opts.moduleRoot,\n      moduleIds: opts.moduleIds,\n      moduleId: opts.moduleId,\n    });\n  }\n\n  // If the @babel/cli version is newer than the @babel/core version, and we have added\n  // new options for @babel/core, we'll potentially get option validation errors from\n  // @babel/core. To avoid that, we delete undefined options, so @babel/core will only\n  // give the error if users actually pass an unsupported CLI option.\n  for (const key of Object.keys(babelOptions) as Array<\n    keyof typeof babelOptions\n  >) {\n    if (babelOptions[key] === undefined) {\n      delete babelOptions[key];\n    }\n  }\n\n  return {\n    babelOptions,\n    cliOptions: {\n      filename: opts.filename,\n      filenames,\n      extensions: opts.extensions,\n      keepFileExtension: opts.keepFileExtension,\n      outFileExtension: opts.outFileExtension,\n      watch: opts.watch,\n      skipInitialBuild: opts.skipInitialBuild,\n      outFile: opts.outFile,\n      outDir: opts.outDir,\n      relative: opts.relative,\n      copyFiles: opts.copyFiles,\n      copyIgnored: opts.copyFiles && opts.copyIgnored,\n      includeDotfiles: opts.includeDotfiles,\n      verbose: opts.verbose,\n      quiet: opts.quiet,\n      deleteDirOnStart: opts.deleteDirOnStart,\n      sourceMapTarget: opts.sourceMapTarget,\n    },\n  };\n}\n\nfunction booleanify(val: \"false\" | 0 | \"\"): false;\nfunction booleanify(val: \"true\" | 1): true;\nfunction booleanify(val: any): any {\n  if (val === undefined) return undefined;\n\n  if (val === \"true\" || val == 1) {\n    return true;\n  }\n\n  if (val === \"false\" || val == 0 || !val) {\n    return false;\n  }\n\n  return val;\n}\n\nfunction collect(value: unknown, previousValue: Array<string>): Array<string> {\n  // If the user passed the option with no value, like \"babel file.js --presets\", do nothing.\n  if (typeof value !== \"string\") return previousValue;\n\n  const values = value.split(\",\");\n\n  if (previousValue) {\n    previousValue.push(...values);\n    return previousValue;\n  }\n  return values;\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,WAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,UAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKAK,WAAQA,CAAC,CAACC,MAAM,CACd,2BAA2B,EAC3B,4FACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,kBAAkB,EAClB,yCAAyC,EACzCC,OACF,CAAC;AACDF,WAAQA,CAAC,CAACC,MAAM,CACd,kBAAkB,EAClB,yCAAyC,EACzCC,OACF,CAAC;AACDF,WAAQA,CAAC,CAACC,MAAM,CAAC,sBAAsB,EAAE,iCAAiC,CAAC;AAC3ED,WAAQA,CAAC,CAACC,MAAM,CACd,mBAAmB,EACnB,iEAAiE,GAC/D,8EACJ,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,oBAAoB,EACpB,oCAAoC,GAClC,8DACJ,CAAC;AAGDD,WAAQA,CAAC,CAACC,MAAM,CAAC,+BAA+B,EAAE,EAAE,CAAC;AACrDD,WAAQA,CAAC,CAACC,MAAM,CACd,cAAc,EACd,4DACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,iBAAiB,EACjB,wCAAwC,EACxCC,OACF,CAAC;AACDF,WAAQA,CAAC,CAACC,MAAM,CACd,eAAe,EACf,yCAAyC,EACzCC,OACF,CAAC;AAGDF,WAAQA,CAAC,CAACC,MAAM,CACd,qBAAqB,EACrB,4EACF,CAAC;AAGDD,WAAQA,CAAC,CAACC,MAAM,CACd,eAAe,EACf,uDACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,gBAAgB,EAChB,4DACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,6BAA6B,EAC7B,wEAAwE,EACxEE,UACF,CAAC;AACDH,WAAQA,CAAC,CAACC,MAAM,CACd,YAAY,EACZ,sDACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,qCAAqC,EACrC,oDACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,oCAAoC,EACpC,mDACF,CAAC;AAGDD,WAAQA,CAAC,CAACC,MAAM,CACd,4CAA4C,EAC5C,EAAE,EACFE,UAAU,EACVC,SACF,CAAC;AACDJ,WAAQA,CAAC,CAACC,MAAM,CACd,8BAA8B,EAC9B,oCACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,6BAA6B,EAC7B,0CACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,0BAA0B,EAC1B,+CACF,CAAC;AAEkC;EAEjCD,WAAQA,CAAC,CAACC,MAAM,CACd,0BAA0B,EAC1B,4GACF,CAAC;EACDD,WAAQA,CAAC,CAACC,MAAM,CAAC,kBAAkB,EAAE,oCAAoC,CAAC;EAC1ED,WAAQA,CAAC,CAACC,MAAM,CACd,sBAAsB,EACtB,uCACF,CAAC;AACH;AAGAD,WAAQA,CAAC,CAACC,MAAM,CACd,+BAA+B,EAC/B,sEAAsE,GACpEI,0BAAkB,CAACC,IAAI,CAAC,CAAC,GACzB,GAAG,EACLJ,OACF,CAAC;AACDF,WAAQA,CAAC,CAACC,MAAM,CACd,uBAAuB,EACvB,kDACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CAAC,aAAa,EAAE,6BAA6B,CAAC;AAC9DD,WAAQA,CAAC,CAACC,MAAM,CACd,sBAAsB,EACtB,uCACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,sBAAsB,EACtB,6CACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,qBAAqB,EACrB,iEACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,YAAY,EACZ,gGACF,CAAC;AAEDD,WAAQA,CAAC,CAACC,MAAM,CACd,kBAAkB,EAClB,4DACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,oBAAoB,EACpB,mEACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,mBAAmB,EACnB,0DACF,CAAC;AAEDD,WAAQA,CAAC,CAACC,MAAM,CACd,WAAW,EACX,oDACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,SAAS,EACT,0DACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,uBAAuB,EACvB,8CACF,CAAC;AACDD,WAAQA,CAAC,CAACC,MAAM,CACd,+BAA+B,EAC/B,+CACF,CAAC;AAEDD,WAAQA,CAAC,CAACO,OAAO,CAAC,WAAuB,gBAAgB,GAAGA,eAAO,GAAG,GAAG,CAAC;AAC1EP,WAAQA,CAAC,CAACQ,KAAK,CAAC,uBAAuB,CAAC;AAIxCR,WAAQA,CAAC,CAACS,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AAyBX,SAASC,SAASA,CAACC,IAAmB,EAAqB;EAExEX,WAAQA,CAAC,CAACY,KAAK,CAACD,IAAI,CAAC;EAErB,MAAME,MAAgB,GAAG,EAAE;EAE3B,IAAIC,SAAS,GAAGd,WAAQA,CAAC,CAACW,IAAI,CAACI,MAAM,CAAC,UAAUC,OAAiB,EAAEC,KAAK,EAAE;IACxE,IAAIC,KAAK,GAAGC,MAAGA,CAAC,CAACC,IAAI,CAACH,KAAK,CAAC;IAC5B,IAAI,CAACC,KAAK,CAACG,MAAM,EAAEH,KAAK,GAAG,CAACD,KAAK,CAAC;IAClCD,OAAO,CAACM,IAAI,CAAC,GAAGJ,KAAK,CAAC;IACtB,OAAOF,OAAO;EAChB,CAAC,EAAE,EAAE,CAAC;EAENF,SAAS,GAAGS,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACX,SAAS,CAAC,CAAC;EAE1CA,SAAS,CAACY,OAAO,CAAC,UAAUC,QAAQ,EAAE;IACpC,IAAI,CAACC,IAACA,CAAC,CAACC,UAAU,CAACF,QAAQ,CAAC,EAAE;MAC5Bd,MAAM,CAACS,IAAI,CAACK,QAAQ,GAAG,iBAAiB,CAAC;IAC3C;EACF,CAAC,CAAC;EAEF,IAAI3B,WAAQA,CAAC,CAAC8B,MAAM,IAAI,CAAChB,SAAS,CAACO,MAAM,EAAE;IACzCR,MAAM,CAACS,IAAI,CAAC,8BAA8B,CAAC;EAC7C;EAEA,IAAItB,WAAQA,CAAC,CAAC+B,OAAO,IAAI/B,WAAQA,CAAC,CAAC8B,MAAM,EAAE;IACzCjB,MAAM,CAACS,IAAI,CAAC,kDAAkD,CAAC;EACjE;EAEA,IAAItB,WAAQA,CAAC,CAACgC,QAAQ,IAAI,CAAChC,WAAQA,CAAC,CAAC8B,MAAM,EAAE;IAC3CjB,MAAM,CAACS,IAAI,CAAC,qCAAqC,CAAC;EACpD;EAEA,IAAItB,WAAQA,CAAC,CAACiC,KAAK,EAAE;IACnB,IAAI,CAACjC,WAAQA,CAAC,CAAC+B,OAAO,IAAI,CAAC/B,WAAQA,CAAC,CAAC8B,MAAM,EAAE;MAC3CjB,MAAM,CAACS,IAAI,CAAC,0CAA0C,CAAC;IACzD;IAEA,IAAI,CAACR,SAAS,CAACO,MAAM,EAAE;MACrBR,MAAM,CAACS,IAAI,CAAC,4BAA4B,CAAC;IAC3C;EACF;EAEA,IAAItB,WAAQA,CAAC,CAACkC,gBAAgB,IAAI,CAAClC,WAAQA,CAAC,CAACiC,KAAK,EAAE;IAClDpB,MAAM,CAACS,IAAI,CAAC,uCAAuC,CAAC;EACtD;EACA,IAAItB,WAAQA,CAAC,CAACmC,gBAAgB,IAAI,CAACnC,WAAQA,CAAC,CAAC8B,MAAM,EAAE;IACnDjB,MAAM,CAACS,IAAI,CAAC,0CAA0C,CAAC;EACzD;EAEA,IAAItB,WAAQA,CAAC,CAACoC,OAAO,IAAIpC,WAAQA,CAAC,CAACqC,KAAK,EAAE;IACxCxB,MAAM,CAACS,IAAI,CAAC,+CAA+C,CAAC;EAC9D;EAEA,IACE,CAACtB,WAAQA,CAAC,CAAC8B,MAAM,IACjBhB,SAAS,CAACO,MAAM,KAAK,CAAC,IACtB,OAAOrB,WAAQA,CAAC,CAAC2B,QAAQ,KAAK,QAAQ,IACtC3B,WAAQA,CAAC,CAACsC,OAAO,KAAK,KAAK,EAC3B;IACAzB,MAAM,CAACS,IAAI,CACT,4EACF,CAAC;EACH;EAEA,IAAItB,WAAQA,CAAC,CAACuC,iBAAiB,IAAIvC,WAAQA,CAAC,CAACwC,gBAAgB,EAAE;IAC7D3B,MAAM,CAACS,IAAI,CACT,gEACF,CAAC;EACH;EAEA,IAAIT,MAAM,CAACQ,MAAM,EAAE;IACjBoB,OAAO,CAACC,KAAK,CAAC,QAAQ,CAAC;IACvB7B,MAAM,CAACa,OAAO,CAAC,UAAUiB,CAAC,EAAE;MAC1BF,OAAO,CAACC,KAAK,CAAC,IAAI,GAAGC,CAAC,CAAC;IACzB,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EAEA,MAAMC,IAAI,GAAG5C,WAAQA,CAAC,CAAC4C,IAAI,CAAC,CAAC;EAE7B,MAAMC,YAA0B,GAAG;IACjCC,OAAO,EAAEF,IAAI,CAACE,OAAO;IACrBC,OAAO,EAAEH,IAAI,CAACG,OAAO;IACrBC,QAAQ,EAAEJ,IAAI,CAACI,QAAQ;IACvBC,UAAU,EAAEL,IAAI,CAACK,UAAU;IAC3BC,OAAO,EAAEN,IAAI,CAACM,OAAO;IACrBC,UAAU,EAAEP,IAAI,CAACO,UAAU;IAC3BC,MAAM,EAAER,IAAI,CAACQ,MAAM;IACnBC,IAAI,EAAET,IAAI,CAACS,IAAI;IACfC,WAAW,EAAEV,IAAI,CAACU,WAAW;IAC7BC,OAAO,EAAEX,IAAI,CAACW,OAAO;IACrBC,QAAQ,EAAEZ,IAAI,CAACY,QAAQ;IACvBC,sBAAsB,EAAEb,IAAI,CAACa,sBAAsB;IACnDC,qBAAqB,EAAEd,IAAI,CAACc,qBAAqB;IACjDC,UAAU,EAAEf,IAAI,CAACe,UAAU;IAC3BC,cAAc,EAAEhB,IAAI,CAACgB,cAAc;IACnCC,UAAU,EAAEjB,IAAI,CAACiB,UAAU;IAK3BvB,OAAO,EAAEM,IAAI,CAACN,OAAO,KAAK,IAAI,GAAGlC,SAAS,GAAGwC,IAAI,CAACN,OAAO;IACzDwB,aAAa,EAAElB,IAAI,CAACkB,aAAa,KAAK,IAAI,GAAG1D,SAAS,GAAGwC,IAAI,CAACkB,aAAa;IAC3EC,QAAQ,EAAEnB,IAAI,CAACmB,QAAQ,KAAK,IAAI,GAAG3D,SAAS,GAAGwC,IAAI,CAACmB;EACtD,CAAC;EAEkC;IACjCC,MAAM,CAACC,MAAM,CAACpB,YAAY,EAAE;MAC1BqB,UAAU,EAAEtB,IAAI,CAACsB,UAAU;MAC3BC,SAAS,EAAEvB,IAAI,CAACuB,SAAS;MACzBC,QAAQ,EAAExB,IAAI,CAACwB;IACjB,CAAC,CAAC;EACJ;EAMA,KAAK,MAAMC,GAAG,IAAIL,MAAM,CAACM,IAAI,CAACzB,YAAY,CAAC,EAExC;IACD,IAAIA,YAAY,CAACwB,GAAG,CAAC,KAAKjE,SAAS,EAAE;MACnC,OAAOyC,YAAY,CAACwB,GAAG,CAAC;IAC1B;EACF;EAEA,OAAO;IACLxB,YAAY;IACZ0B,UAAU,EAAE;MACV5C,QAAQ,EAAEiB,IAAI,CAACjB,QAAQ;MACvBb,SAAS;MACT0D,UAAU,EAAE5B,IAAI,CAAC4B,UAAU;MAC3BjC,iBAAiB,EAAEK,IAAI,CAACL,iBAAiB;MACzCC,gBAAgB,EAAEI,IAAI,CAACJ,gBAAgB;MACvCP,KAAK,EAAEW,IAAI,CAACX,KAAK;MACjBC,gBAAgB,EAAEU,IAAI,CAACV,gBAAgB;MACvCH,OAAO,EAAEa,IAAI,CAACb,OAAO;MACrBD,MAAM,EAAEc,IAAI,CAACd,MAAM;MACnBE,QAAQ,EAAEY,IAAI,CAACZ,QAAQ;MACvByC,SAAS,EAAE7B,IAAI,CAAC6B,SAAS;MACzBC,WAAW,EAAE9B,IAAI,CAAC6B,SAAS,IAAI7B,IAAI,CAAC8B,WAAW;MAC/CC,eAAe,EAAE/B,IAAI,CAAC+B,eAAe;MACrCvC,OAAO,EAAEQ,IAAI,CAACR,OAAO;MACrBC,KAAK,EAAEO,IAAI,CAACP,KAAK;MACjBF,gBAAgB,EAAES,IAAI,CAACT,gBAAgB;MACvCyC,eAAe,EAAEhC,IAAI,CAACgC;IACxB;EACF,CAAC;AACH;AAIA,SAASzE,UAAUA,CAAC0E,GAAQ,EAAO;EACjC,IAAIA,GAAG,KAAKzE,SAAS,EAAE,OAAOA,SAAS;EAEvC,IAAIyE,GAAG,KAAK,MAAM,IAAIA,GAAG,IAAI,CAAC,EAAE;IAC9B,OAAO,IAAI;EACb;EAEA,IAAIA,GAAG,KAAK,OAAO,IAAIA,GAAG,IAAI,CAAC,IAAI,CAACA,GAAG,EAAE;IACvC,OAAO,KAAK;EACd;EAEA,OAAOA,GAAG;AACZ;AAEA,SAAS3E,OAAOA,CAAC4E,KAAc,EAAEC,aAA4B,EAAiB;EAE5E,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE,OAAOC,aAAa;EAEnD,MAAMC,MAAM,GAAGF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC;EAE/B,IAAIF,aAAa,EAAE;IACjBA,aAAa,CAACzD,IAAI,CAAC,GAAG0D,MAAM,CAAC;IAC7B,OAAOD,aAAa;EACtB;EACA,OAAOC,MAAM;AACf"}