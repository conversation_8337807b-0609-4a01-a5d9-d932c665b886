import React from 'react';
import { useWindowDimensions, StyleSheet } from 'react-native';
import { TabView, TabBar } from 'react-native-tab-view';
import { ThemeText } from '../../atoms';
import TabContent from './TabContent';
import { styles as themeStyles } from '../../../styles';

export default function TabBlock({
  attributes,
  BlockItem,
  onAction,
  currentAction,
  BlocksView,
  innerBlocks,
}) {
  const layout = useWindowDimensions();
  const {
    scrollable = false,
    themeColor = '#1B1B1B',
    bgColor = '#f9f9f9',
    buttonTab = false,
  } = attributes;

  const [index, setIndex] = React.useState(0);
  const routes = React.useMemo(() => {
    return innerBlocks.map((block) => {
      return {
        key: block.attributes.pageId,
        title: block.attributes.label,
        block,
      };
    });
  });

  const tabViewStyle = [];
  const contentContainerStyle = [];
  const tabBarStyle = [];
  const indicatorStyle = [];
  const labelStyle = [];
  const tabStyle = [];

  if (buttonTab === false) {
    indicatorStyle.push(styles.indicator, { backgroundColor: themeColor });
    labelStyle.push(styles.label, { color: themeColor });
  }

  if (scrollable) {
    tabStyle.push(styles.tabStyle);
  }

  if (buttonTab) {
    tabViewStyle.push({
      borderRadius: 10,
      overflow: 'hidden',
    });
    contentContainerStyle.push({
      height: 48,
    });
    tabBarStyle.push({
      backgroundColor: '#f1f1f1',
      borderColor: '#cccccc',
      borderWidth: 1,
      borderRadius: 10,
      overflow: 'hidden',
      marginHorizontal: 12,
      marginVertical: 12,
    });
    indicatorStyle.push({
      backgroundColor: themeColor,
      height: '100%',
      borderRadius: 10,
    });
  }

  if (bgColor) {
    tabBarStyle.push({ backgroundColor: bgColor });
  }

  const renderScene = ({ route }) => {
    return (
      <TabContent
        block={route?.block}
        BlocksView={BlocksView}
        onAction={onAction}
        currentAction={currentAction}
      />
    );
  };
  const renderTabBar = (props) => {
    return (
      <TabBar
        {...props}
        scrollEnabled={false}
        indicatorStyle={indicatorStyle}
        style={tabBarStyle}
        labelStyle={labelStyle}
        tabStyle={tabStyle}
        contentContainerStyle={contentContainerStyle}
        renderLabel={({ route, focused }) => (
          <ThemeText color={focused && buttonTab ? '#f8f6f2' : '#231f20'}>
            {route.title}
          </ThemeText>
        )}
      />
    );
  };
  return (
    <TabView
      lazy
      navigationState={{ index, routes }}
      swipeEnabled={attributes?.swipeEnabled}
      renderScene={renderScene}
      onIndexChange={setIndex}
      renderTabBar={renderTabBar}
      initialLayout={{ width: layout.width }}
      style={tabViewStyle}
    />
  );
}

const styles = StyleSheet.create({
  scene: {
    flex: 1,
  },
  indicator: {
    borderTopEndRadius: 8,
    borderTopStartRadius: 8,
  },
  label: {
    fontSize: 14,
    fontFamily: themeStyles.fontFamily.bold,
  },
  tabStyle: {
    width: 'auto',
  },
});
