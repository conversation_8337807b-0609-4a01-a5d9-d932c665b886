import { converToShopifyParams } from '../helper';

test('should filter param', () => {
  const appliedFilter = {
    'filter.v.option.frame color': {
      'filter.v.option.frame color.black': {
        state: true,
        value: '{"variantOption":{"name":"frame color","value":"Black"}}',
      },
      'filter.v.option.frame color.brown': {
        state: true,
        value: '{"variantOption":{"name":"frame color","value":"Brown"}}',
      },
    },
    'filter.v.price': {
      min: 587,
      max: 3315,
    },
  };
  const avilableFilters = [
    {
      id: 'filter.v.price',
      label: 'Price',
      type: 'PRICE_RANGE',
      values: [
        {
          count: 0,
          id: 'filter.v.price',
          input: '{"price":{"min":0,"max":3897.0}}',
          label: 'Price',
        },
      ],
    },
    {
      id: 'filter.v.option.frame color',
      label: 'Frame color',
      type: 'LIST',
      values: [
        {
          count: 79,
          id: 'filter.v.option.frame color.black',
          input: '{"variantOption":{"name":"frame color","value":"Black"}}',
          label: 'Black',
        },
        {
          count: 79,
          id: 'filter.v.option.frame color.brown',
          input: '{"variantOption":{"name":"frame color","value":"Brown"}}',
          label: 'Brown',
        },
        {
          count: 79,
          id: 'filter.v.option.frame color.no-frame',
          input: '{"variantOption":{"name":"frame color","value":"No Frame"}}',
          label: 'No Frame',
        },
        {
          count: 79,
          id: 'filter.v.option.frame color.white',
          input: '{"variantOption":{"name":"frame color","value":"White"}}',
          label: 'White',
        },
      ],
    },
    {
      id: 'filter.v.availability',
      label: 'Availability',
      type: 'LIST',
      values: [
        {
          count: 155,
          id: 'filter.v.availability.1',
          input: '{"available":true}',
          label: 'In stock',
        },
        {
          count: 79,
          id: 'filter.v.availability.0',
          input: '{"available":false}',
          label: 'Out of stock',
        },
      ],
    },
  ];
  const outFilter = converToShopifyParams(appliedFilter, avilableFilters);
  expect(outFilter).toEqual([
    { variantOption: { name: 'frame color', value: 'Black' } },
    { variantOption: { name: 'frame color', value: 'Brown' } },
    { price: { min: 587, max: 3315 } }
  ]);
});
