{"version": 3, "names": ["_getBindingIdentifiers", "require", "_default", "getOuterBindingIdentifiers", "exports", "default", "node", "duplicates", "getBindingIdentifiers"], "sources": ["../../src/retrievers/getOuterBindingIdentifiers.ts"], "sourcesContent": ["import getBindingIdentifiers from \"./getBindingIdentifiers\";\nimport type * as t from \"..\";\n\nexport default getOuterBindingIdentifiers as {\n  (node: t.Node, duplicates: true): Record<string, Array<t.Identifier>>;\n  (node: t.Node, duplicates?: false): Record<string, t.Identifier>;\n  (node: t.Node, duplicates?: boolean):\n    | Record<string, t.Identifier>\n    | Record<string, Array<t.Identifier>>;\n};\n\nfunction getOuterBindingIdentifiers(\n  node: t.Node,\n  duplicates: boolean,\n): Record<string, t.Identifier> | Record<string, Array<t.Identifier>> {\n  return getBindingIdentifiers(node, duplicates, true);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAA4D,IAAAC,QAAA,GAG7CC,0BAA0B;AAAAC,OAAA,CAAAC,OAAA,GAAAH,QAAA;AAQzC,SAASC,0BAA0BA,CACjCG,IAAY,EACZC,UAAmB,EACiD;EACpE,OAAO,IAAAC,8BAAqB,EAACF,IAAI,EAAEC,UAAU,EAAE,IAAI,CAAC;AACtD"}