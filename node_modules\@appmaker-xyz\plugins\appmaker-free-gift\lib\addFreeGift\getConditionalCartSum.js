// cartSumCondition = collection_include, collection_exclude
// collections = edges[].node.id
// input = { lineItemsToAdd = [], currentCart= {}, lineItemsToUpdate = [] }
// TODO memoize this function based on input
export default function getConditionalCartSum({
  input,
  cartSumCondition,
  collections,
  dependencies,
  discountCodeTypesToExclude,
  useCompareAtPrice = false,
}) {
  const { log } = dependencies;

  const { lineItemsToAdd = [], currentCart, lineItemsToUpdate = [] } = input;
  function shouldInculdeInSum(variant) {
    const { collections: currentProductCollections, handle = '' } =
      variant?.product;
    const currentProductCollectionIds = currentProductCollections?.edges?.map(
      (edge) => edge?.node?.id,
    );
    const collectionIds = collections?.map(
      (collection) => collection?.collection_id,
    );
    let shouldInclude = true;
    if (cartSumCondition === 'include_collection') {
      shouldInclude = collectionIds?.some((collection) => {
        // log('collection_include', collection, currentProductCollectionIds);
        return currentProductCollectionIds?.includes(collection);
      });
    } else if (cartSumCondition === 'exclude_collection') {
      shouldInclude = !collectionIds?.some((collection) => {
        // log('collection_exclude', collection, currentProductCollectionIds);
        return currentProductCollectionIds?.includes(collection);
      });
    }

    log('shouldInculdeInSum', {
      handle,
      shouldInclude,
      cartSumCondition,
      collectionIds,
      currentProductCollectionIds,
    });

    return shouldInclude;
  }

  try {
    let sum = 0;
    let qty = 0; // initialize total quantity to 0
    if (currentCart) {
      // sum = parseFloat(currentCart?.totalPrice.amount || 0);
      sum += currentCart.lineItems.edges.reduce((acc, item) => {
        const currentLineItem = item.node;
        const { customAttributes } = currentLineItem;
        let currentLineItemPrice = 0;
        if (shouldInculdeInSum(currentLineItem.variant)) {
          const isFreeGift = customAttributes.find(
            (attribute) => attribute.key === 'appmaker_free_gift',
          );
          if (!isFreeGift || isFreeGift === undefined) {
            const quantity = currentLineItem?.quantity;
            let currentLineItemAmount = 0;

            if (
              useCompareAtPrice &&
              currentLineItem?.variant?.compareAtPrice?.amount
            ) {
              currentLineItemAmount = parseFloat(
                currentLineItem?.variant?.compareAtPrice?.amount,
              );
            } else {
              currentLineItemAmount = parseFloat(
                currentLineItem?.variant?.price?.amount,
              );
            }

            log('currentLineItemPrice', acc, {
              quantity: quantity,
              amount: currentLineItemAmount,
            });

            currentLineItemPrice = quantity * currentLineItemAmount;

            qty += quantity; // add item quantity to total quantity

            const discountAllocations = currentLineItem?.discountAllocations;
            if (discountAllocations && discountAllocations.length > 0) {
              discountAllocations.forEach((discount) => {
                // check if the discount type is included in the list of discount types to exclude
                log('discount', { discount, discountCodeTypesToExclude });
                log('discount-discountCodeTypes', {
                  discountCodeTypesToExclude,
                });
                if (
                  discountCodeTypesToExclude?.length > 0 &&
                  discountCodeTypesToExclude?.find((type) => {
                    return (
                      type.value === 'ALL' ||
                      type.value === discount?.discountApplication?.__typename
                    );
                  })
                ) {
                  return;
                }
                currentLineItemPrice -= parseFloat(
                  discount?.allocatedAmount?.amount,
                );
              });
            }
          } // end of if (!isFreeGift || isFreeGift === undefined)
        } // end of if (shouldInculdeInSum(currentLineItem.variant))
        log(
          'currentLineItemPrice final ' + currentLineItem?.variant?.product?.handle,
          currentLineItemPrice,
        );
        return acc + currentLineItemPrice;
      }, sum);
      log('currentCart sum', sum);
    }
    lineItemsToAdd.forEach((item) => {
      if (shouldInculdeInSum(item)) {
        sum += item.quantity * parseFloat(item?.variant?.node?.price?.amount);
        qty += item.quantity; // add item quantity to total quantity
      }
    });
    lineItemsToUpdate.forEach((item) => {
      if (shouldInculdeInSum(item)) {
        const variantPrice = item.variant?.price?.amount;
        const currentLineItemObj = currentCart?.lineItems?.edges?.find(
          (lineItem) => lineItem?.node?.id === item.id,
        );
        const currentLineItemQuantity = currentLineItemObj?.node?.quantity;
        sum += (item.quantity - currentLineItemQuantity) * variantPrice;
        qty += Math.abs(item.quantity - currentLineItemQuantity); // add difference in quantity to total quantity
      }
    });
    log('total sum', sum);
    log('total qty', qty);
    return { sum, qty }; // return object with both total sum and total quantity
  } catch (e) {
    log('getCartSum - error', e);
  }
}
