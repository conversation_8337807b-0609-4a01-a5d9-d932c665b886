import React, { Component } from 'react';
import {
  View,
  TextInput,
  Text,
  TouchableHighlight,
  Platform,
  ScrollView,
  Image,
  Modal,
} from 'react-native';
import PropTypes from 'prop-types';
import Config from '../../../../Config';
import styles from './Styles';
import Button from '../Button';
import { submitComment } from './helper';
import { getUserData } from '../../../misc/AsyncStorageHandler';
import { AppmakerText } from '@appmaker-xyz/uikit/src/components';

const closeImage = require('../../../images/close.png');

// const toolbarLogo = require("../images/toolbarlogo.png");

class AddComment extends Component {
  // console.log("Add Comment item called props = ", props);
  constructor() {
    super();
    // const isDone = false;
    this.state = {
      showModal: true,
      edtName: '',
      edtEmail: '',
      edtComment: '',
      user: false,
    };
  }

  /**
   * function to post comment
   */
  submit = () => {
    let { edtName, edtComment, edtEmail } = this.state;
    if (this.state.user && this.state.user.user) {
      const userObj = this.state.user.user;
      edtName = userObj.display_name;
      edtEmail = userObj.email;
    }

    submitComment(
      this.props.data.callback_url,
      edtEmail,
      edtName,
      edtComment,
      this.props.modalCallback,
    );
  };

  componentDidMount() {
    getUserData().then((u) => {
      if (u) {
        this.setState({ user: u });
      }
    });
  }

  render() {
    const numberOfLinesComment = 6;
    const { modalWidth, modalCallback, visible } = this.props;
    const primaryColor = Config.toolbarColor;

    /**
     * If primary color is white the buttons wont be visible in white background
     * so change them to black if primary color is white
     * TODO Change this condition for inApp background color
     */
    const finalTitleColor =
      primaryColor &&
      (primaryColor === 'white' ||
        primaryColor === '#ffffff' ||
        primaryColor === '#FFFFFF')
        ? 'black'
        : primaryColor;
    return (
      <Modal
        animationType="slide"
        transparent={false}
        visible={visible}
        onRequestClose={() => {
          /**
           * Close modal on back button
           */
          modalCallback(false);
        }}>
        <View style={{ flexDirection: 'column', alignItems: 'flex-end' }}>
          <TouchableHighlight
            style={{ paddingHorizontal: 10, paddingVertical: 10 }}
            onPress={() => modalCallback(false)}>
            <Image style={styles.closeImage} source={closeImage} />
          </TouchableHighlight>
        </View>
        <View
          style={{
            flex: 1,
            alignContent: 'center',
            width: modalWidth,
            alignSelf: 'center',
            backgroundColor: 'white',
            padding: 20,
          }}>
          <ScrollView
            keyboardShouldPersistTaps={'handled'}
            showsVerticalScrollIndicator={false}>
            <View style={styles.container}>
              <AppmakerText
                style={{ ...styles.heading, color: finalTitleColor }}>
                Leave a reply
              </AppmakerText>
              <View style={styles.fieldContainer}>
                <AppmakerText style={styles.comment} category="actionTitle">
                  Comment
                </AppmakerText>
                <TextInput
                  style={styles.inputComment}
                  multiline={true}
                  numberOfLines={numberOfLinesComment}
                  minHeight={
                    Platform.OS === 'ios' ? 20 * numberOfLinesComment : null
                  }
                  editable={true}
                  onChangeText={(text) => this.setState({ edtComment: text })}
                />
              </View>
              {!this.state.user && (
                <View style={styles.fieldContainer}>
                  <AppmakerText style={styles.name} category="actionTitle">
                    Name
                  </AppmakerText>
                  <TextInput
                    style={styles.inputName}
                    minHeight={Platform.OS === 'ios' ? 30 : null}
                    editable={true}
                    onChangeText={(text) => this.setState({ edtName: text })}
                  />
                </View>
              )}
              {!this.state.user && (
                <View style={styles.fieldContainer}>
                  <AppmakerText
                    style={styles.email}
                    category="bodyParagraphBold">
                    Email
                  </AppmakerText>
                  <TextInput
                    style={styles.inputEmail}
                    minHeight={Platform.OS === 'ios' ? 30 : null}
                    editable={true}
                    onChangeText={(text) => this.setState({ edtEmail: text })}
                  />
                </View>
              )}
              <View>
                <Button
                  containerStyle={styles.submit}
                  onPress={() => this.submit()}>
                  Post Comment
                </Button>
              </View>
              {/* <TouchableHighlight
                style={styles.submit}
                onPress={() => this.submitComment()}
                underlayColor="#fff"
              >
                <AppmakerText style={styles.postCommentText}>Post Comment</AppmakerText>
              </TouchableHighlight> */}
            </View>
          </ScrollView>
        </View>
      </Modal>
    );
  }
}

AddComment.propTypes = {
  /**
   * @param data
   * Comment related data including comment submit url
   */
  data: PropTypes.object,
  /**
   * @param visible
   * Visibility of Modal as true or false
   */
  visible: PropTypes.bool.isRequired,
  /**
   * @param modalCallback
   * Parent component function to set visibility of Modal
   */
  modalCallback: PropTypes.func.isRequired,
  /**
   * @param modalWidth
   * Parent width is passed for modal width
   */
  modalWidth: PropTypes.string,
};

export default AddComment;
