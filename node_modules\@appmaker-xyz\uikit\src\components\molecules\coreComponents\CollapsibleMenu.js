import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  I18nManager,
  LayoutAnimation,
  Platform,
  UIManager,
} from 'react-native';
import {
  Layout,
  AppmakerText,
  AppTouchable,
  AppImage,
} from '../../../components';
import { emitEvent,analytics } from '@appmaker-xyz/core';
import { useApThemeState } from '../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { useAppStorage } from '@appmaker-xyz/core';
import { testProps } from '@appmaker-xyz/core';
import Badge from './Badge';

const DrawerItem = ({
  clientId,
  title,
  featureImg,
  badge,
  badgeText = 'Badge',
  badgeColor = '#212121',
  badgeTextColor = '#ffffff',
  fontColor,
  dotIndicator,
  dotIndicatorColor = '#BC002D',
  dotSize = 10,
  appmakerAction,
  type,
  hideFor,
  nodes,
  onPressHandle,
}) => {
  const user = useAppStorage((state) => state.user);
  const { color, spacing, fonts, font } = useApThemeState();
  const styles = allStyles({
    color,
    spacing,
    fonts,
    font,
  });
  const [expanded, setExpanded] = useState(false);
  // console.log(title, nodes, 'abcd');

  if (Platform.OS === 'android') {
    if (UIManager.setLayoutAnimationEnabledExperimental) {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }
  }
  const toggleExpand = () => {
    console.log('fftype', type);
    if (!nodes || nodes.length == 0) {
      onPressHandle(appmakerAction);
    }
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(!expanded);
  };
  if (
    hideFor &&
    ((hideFor === 'guest' && !user) || (hideFor === 'user' && user))
  ) {
    return null;
  }
  return (
    <AppTouchable
      //   onPress={() => onPressHandle(appmakerAction, nodes, title, type)}
      onPress={toggleExpand}
      clientId={clientId}
      style={type === 'title' ? null : styles.bottomBorder}>
      <View style={styles.container}>
        <Layout style={styles.titleContainer}>
          {featureImg ? (
            <Layout style={styles.leftImage}>
              <AppImage
                uri={featureImg}
                style={styles.image}
                resizeMode="contain"
              />
            </Layout>
          ) : null}
          <Layout style={styles.titleContainer}>
            <AppmakerText
              {...testProps(`${clientId}-title`)}
              category={type === 'title' ? 'bodySubText' : 'bodyParagraph'}
              status={type === 'title' ? 'grey' : 'dark'}
              numberOfLines={1}
              style={{ flexWrap: 'wrap', color: fontColor }}>
              {title}
            </AppmakerText>
            {badge ? (
              <Badge
                text={badgeText}
                customStyles={{
                  containerStyle: {
                    backgroundColor: badgeColor,
                    marginLeft: 4,
                  },
                  textStyle: {
                    color: badgeTextColor,
                  },
                }}
              />
            ) : null}
            {dotIndicator ? (
              <Layout
                style={[
                  styles.dotIndicator,
                  {
                    width: dotSize,
                    height: dotSize,
                    backgroundColor: dotIndicatorColor,
                  },
                ]}
              />
            ) : null}
          </Layout>
        </Layout>
        {nodes?.length > 0 ? (
          <Icon
            name={expanded ? 'minus' : 'plus'}
            size={font.size.lg}
            color={color.demiDark}
          />
        ) : null}
      </View>
      {expanded ? (
        <Layout>
          {nodes?.map((item, index) => {
            return (
              <AppTouchable
                key={index}
                onPress={() =>
                  item.type !== 'title' &&
                  onPressHandle(item.action, nodes, item.title, item.type)
                }
                style={[
                  styles.subItemContainer,
                  item.type === 'title' ? null : styles.bottomBorder,
                ]}>
                {item.icon ? (
                  <Layout style={styles.leftImageSmall}>
                    <AppImage
                      uri={item.icon}
                      style={styles.image}
                      resizeMode="contain"
                    />
                  </Layout>
                ) : null}
                <Layout style={styles.titleContainer}>
                  <AppmakerText
                    category={
                      item.type === 'title' ? 'bodySubText' : 'bodyParagraph'
                    }
                    status={item.type === 'title' ? 'grey' : 'demiDark'}
                    style={{ color: item?.fontColor }}>
                    {item.title}
                  </AppmakerText>
                  {item.badge ? (
                    <Badge
                      text={item.badgeText}
                      customStyles={{
                        containerStyle: {
                          backgroundColor: item.badgeColor,
                          marginLeft: 4,
                        },
                        textStyle: {
                          color: item.badgeTextColor,
                        },
                      }}
                    />
                  ) : null}
                  {item.dotIndicator ? (
                    <Layout
                      style={[
                        styles.dotIndicator,
                        {
                          width: item.dotSize,
                          height: item.dotSize,
                          backgroundColor: item.dotIndicatorColor,
                        },
                      ]}
                    />
                  ) : null}
                </Layout>
              </AppTouchable>
            );
          })}
        </Layout>
      ) : null}
    </AppTouchable>
  );
};

const CollapsibleMenu = ({ attributes, onPress, onAction, clientId }) => {
  let dataSet = {};
  let { items } = attributes;
  if (attributes?.items?.blocks) {
    dataSet = attributes.items.blocks;
    items = dataSet
      ? dataSet[0]?.data
        ? dataSet[0]?.data
        : dataSet
      : undefined;
  }
  const [contents, setContents] = useState(items);
  const [stack, setStack] = useState([]); // stack structure used for the navigation
  const [loading, setLoading] = useState(true);
  const { color, spacing, fonts, font } = useApThemeState();
  const styles = allStyles({
    color,
    spacing,
    fonts,
    font,
  });
  const resetItems = () => {
    // for reseting navigation of menu items to initial.
    setContents(items);
    setStack([{ nodes: items, title: '' }]);
  };
  useEffect(() => {
    if (items) {
      setLoading(false);
      resetItems();
    } // used because there is a slight delay in loading data from props. ie, items is undefined at first
  }, [items]);
  const onPressHandle = (action, nodes, title, type) => {
    if (action?.type === 'OPEN_COLLECTION') {
      emitEvent('drawerCategoryClick', {
        title,
        action: 'OPEN_COLLECTION',
      });
      analytics.track('drawerCategoryClick', { 
        title,
        action: 'OPEN_COLLECTION',
       });
    }
    if (!action?.params?.title || !action?.params?.label) {
      action.params.title = title;
      action.params.label = title;
    }
    if (action.type === 'OPEN_IN_APP_PAGE' && action.params.id === 'home') {
      onAction({ action: 'OPEN_HOME', params: { replacePage: true } });
    } else {
      onAction(action);
    }
    onAction({ action: 'TOGGLE_DRAWER' });
    resetItems();
  };

  if (loading) {
    return <Layout loading={true} />;
  }
  return (
    <FlatList
      data={contents}
      //   ListHeaderComponent={() => {
      //     if (stack[stack.length - 1].title) {
      //       return (
      //         <AppTouchable
      //           onPress={() => {
      //             stack.pop();
      //             setContents(stack[stack.length - 1].nodes);
      //           }}>
      //           <Layout style={[styles.titleContainer, styles.goBackStyle]}>
      //             <Icon
      //               name="arrow-left"
      //               size={font.size.lg}
      //               color={color.demiDark}
      //               style={{
      //                 paddingHorizontal: spacing.small,
      //               }}
      //             />
      //             <AppmakerText
      //               category={'pageSubHeading'}
      //               status={'demiDark'}
      //               numberOfLines={1}
      //               style={{ flexShrink: 1, flexWrap: 'wrap' }}>
      //               {stack[stack.length - 1].title}
      //             </AppmakerText>
      //           </Layout>
      //         </AppTouchable>
      //       );
      //     } else {
      //       return <View />;
      //     }
      //   }}
      keyExtractor={(item, index) => index.toString()}
      renderItem={({ item, key }) => (
        <DrawerItem
          key={key}
          clientId={clientId}
          title={item.title}
          badge={item.badge}
          badgeColor={item.badgeColor}
          badgeText={item.badgeText}
          badgeTextColor={item.badgeTextColor}
          fontColor={item.fontColor}
          dotIndicator={item.dotIndicator}
          dotIndicatorColor={item.dotIndicatorColor}
          dotSize={item.dotSize}
          hideFor={item?.hideFor}
          featureImg={item.icon}
          nodes={item.nodes}
          appmakerAction={item.action}
          type={item.type}
          onPressHandle={item.type == 'title' ? () => {} : onPressHandle}
        />
      )}
    />
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      padding: spacing.small,
      justifyContent: 'space-between',
      backgroundColor: color.white,
      alignItems: 'center',
      marginBottom: spacing.nano,
      flex: 1,
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },

    leftImage: {
      width: 30,
      height: 30,
      marginRight: spacing.base,
    },
    leftImageSmall: {
      width: 20,
      height: 20,
      marginRight: spacing.base,
    },
    image: {
      width: '100%',
      height: '100%',
      resizeMode: 'cover',
    },
    bottomBorder: {
      borderBottomColor: color.light,
      borderBottomWidth: 1,
    },
    goBackStyle: {
      paddingVertical: spacing.base,
      marginTop: 0.5,
      marginBottom: spacing.nano,
    },
    subItemContainer: {
      marginLeft: 16,
      marginRight: 1,
      marginBottom: 1,
      padding: 6,
      flexDirection: 'row',
      alignItems: 'center',
    },
    dotIndicator: {
      borderRadius: 20,
      marginLeft: 6,
      overflow: 'hidden',
    },
  });

export default CollapsibleMenu;
