module.exports={A:{A:{"2":"J D E F DC","129":"A B"},B:{"1":"P Q R S T U V W X Y Z a b c d e i j k l m n o p q r s t u v f H","129":"C K","1025":"L G M N O"},C:{"2":"0 1 2 3 4 5 EC uB I w J D E F A B C K L G M N O x g y z FC GC","513":"6 7 8 9 AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB vB aB wB bB cB dB eB fB gB hB iB jB kB lB h mB nB oB pB qB P Q R xB S T U V W X Y Z a b c d e i j k l m n o p q r s t u v f H yB zB"},D:{"1":"0 1 2 3 4 5 6 7 8 9 J D E F A B C K L G M N O x g y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB vB aB wB bB cB dB eB fB gB hB iB jB kB lB h mB nB oB pB qB P Q R S T U V W X Y Z a b c d e i j k l m n o p q r s t u v f H yB zB HC","2":"I w"},E:{"1":"w J D E F A B C K L G JC KC LC MC 1B rB sB 2B NC OC 3B 4B 5B 6B tB 7B 8B 9B AC PC","2":"I IC 0B"},F:{"1":"0 1 2 3 4 5 6 7 8 9 F B C G M N O x g y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB h mB nB oB pB qB P Q R xB S T U V W X Y Z a b c d e QC RC SC TC rB BC UC sB"},G:{"388":"E 0B VC CC WC XC YC ZC aC bC cC dC eC fC gC hC iC jC kC lC mC nC oC 3B 4B 5B 6B tB 7B 8B 9B AC"},H:{"2":"pC"},I:{"2":"uB qC rC sC","388":"I H tC CC uC vC"},J:{"2":"D","388":"A"},K:{"1":"A B C rB BC sB","388":"h"},L:{"388":"H"},M:{"641":"f"},N:{"388":"A B"},O:{"388":"wC"},P:{"388":"I g xC yC zC 0C 1C 1B 2C 3C 4C 5C 6C tB 7C 8C 9C"},Q:{"388":"2B"},R:{"388":"AD"},S:{"513":"BD CD"}},B:1,C:"Number input type"};
