import React from 'react';
import { StyleSheet, ScrollView, FlatList } from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import {
  Layout,
  BlockCard,
  TableCell,
  AppmakerText,
  Card,
  Button,
} from '../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';

const OrderDetail = ({ attributes, onPress, onAction }) => {
  const {
    featureImg,
    orderId,
    items,
    numberOfItems,
    totalAmount,
    appmakerAction,
    status,
    orderDate,
    deliveryDate,
    paymentMethod,
    billingAddress,
    ShippingAddress,
  } = attributes;
  function cardStatus() {
    switch (status) {
      case 'Processing':
        return 'primary';
      case 'On hold':
        return 'warning';
      case 'Completed':
        return 'success';
      case 'Cancelled':
        return 'grey';
      case 'Failed':
        return 'danger';
      case 'Pending payment':
        return 'dark';
      default:
        return 'demiDark';
    }
  }
  let cardColor = cardStatus();
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing, cardColor });

  const TableData = () => (
    <>
      <TableCell attributes={{ title: 'Sub Total', value: '$42.00' }} />
      <TableCell attributes={{ title: 'Coupon Discount', value: '-$3.44' }} />
      <TableCell attributes={{ title: 'Shipping Cost', value: '$0.00' }} />
      <TableCell
        attributes={{
          type: 'total',
          title: 'Order Total',
          value: '$38.56',
        }}
      />
    </>
  );

  const AddressCard = ({ title, data }) => (
    <BlockCard
      attributes={{ title: title, childContainerStyle: styles.subPadding }}>
      <AppmakerText status="demiDark" category="bodyParagraphBold">
        {data.first_name} {data.last_name}
      </AppmakerText>
      <AppmakerText status="demiDark" category="bodyParagraphRegular">
        {data.company}
      </AppmakerText>
      {data.email && (
        <AppmakerText status="demiDark" category="bodyParagraphRegular">
          {data.email}
        </AppmakerText>
      )}
      {data.phone && (
        <AppmakerText status="demiDark" category="bodyParagraphRegular">
          {data.phone}
        </AppmakerText>
      )}
      <AppmakerText status="demiDark" category="bodyParagraphRegular">
        {data.address_1}
      </AppmakerText>
      {data.address_2 && (
        <AppmakerText status="demiDark">{data.address_2}</AppmakerText>
      )}
      <AppmakerText status="demiDark">{data.city}</AppmakerText>
      <AppmakerText status="demiDark">
        {data.state}, {data.country}-{data.postcode}
      </AppmakerText>
    </BlockCard>
  );

  const ProductCard = ({ title, price, quantity }) => (
    <Card
      attributes={{
        type: 'type-2',
        featureImg:
          'https://images.unsplash.com/photo-1491553895911-0055eca6402d?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=500&q=80',
        title: title,
        excerpt: `${price} x ${quantity}`,
        meta: [
          {
            title: 'Blue',
            iconName: 'tag',
          },
          {
            title: 'Size: XL',
            iconName: 'tag',
          },
        ],
      }}
    />
  );
  const renderItem = ({ item }) => (
    <ProductCard
      title={item.name}
      price={item.price_display}
      quantity={item.quantity}
    />
  );

  return (
    <ScrollView style={styles.container}>
      <Layout backgroundColor={color.white}>
        <Layout style={styles.detailHeader}>
          <AppmakerText status={cardColor} category="h1Heading">
            {status.toUpperCase()}
          </AppmakerText>
          <AppmakerText>
            Order Id: #{orderId} ({numberOfItems} Items) [displayed on top bar]
          </AppmakerText>
          <AppmakerText status="demiDark" category="bodySubText">
            Order placed on {orderDate}
          </AppmakerText>
          <AppmakerText status="demiDark">
            Payment using {paymentMethod}
          </AppmakerText>
          <Layout style={styles.orderActions}>
            {(status === 'Cancelled' ||
              status === 'Failed' ||
              status === 'Refunded' ||
              status === 'Completed') && (
              <Layout flex={1}>
                <Button small status="dark">
                  Order Again
                </Button>
              </Layout>
            )}
            {status === 'Processing' && (
              <Layout flex={1}>
                <Button small status="dark">
                  Track
                </Button>
                <AppmakerText
                  status="grey"
                  category="bodySubText"
                  style={styles.shippingMsg}>
                  <Icon name="truck" size={spacing.md} /> Order shipped through
                  FedEx
                </AppmakerText>
              </Layout>
            )}
            {status === 'Pending payment' && (
              <>
                <Layout flex={1}>
                  <Button small status="dark">
                    Complete Order
                  </Button>
                </Layout>
                <Layout flex={1}>
                  <Button small status="dark">
                    Cancel Order
                  </Button>
                </Layout>
              </>
            )}
          </Layout>
        </Layout>
      </Layout>
      <BlockCard
        attributes={{
          title: 'Order Details',
          accessButton: 'Invoice',
          childContainerStyle: styles.subPadding,
        }}>
        {/* <ProductCard />
        <ProductCard />
        <ProductCard /> */}
        <FlatList data={items} renderItem={renderItem} />
        <TableData />
      </BlockCard>
      <AddressCard data={billingAddress} title="Shipping Address" />
      <AddressCard data={ShippingAddress} title="Billing Address" />
      <Layout padding={spacing.base}>
        <Button onPress={() => console.log('Go to Home')}>
          Continue Shopping
        </Button>
      </Layout>
    </ScrollView>
  );
};

const allStyles = ({ spacing, color, cardColor }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.light,
    },
    detailHeader: {
      padding: spacing.base,
      backgroundColor: `${color[cardColor]}1A`,
    },
    subPadding: {
      paddingHorizontal: spacing.base,
      paddingBottom: spacing.base,
    },
    orderActions: {
      flexDirection: 'row',
      paddingTop: spacing.base,
    },
    shippingMsg: {
      textAlign: 'center',
      marginTop: spacing.small,
    },
  });

export default OrderDetail;
