{"version": 3, "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype"], "sources": ["../../src/helpers/typeof.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\n/* eslint-disable no-func-assign */\n\nexport default function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj &&\n        typeof Symbol === \"function\" &&\n        obj.constructor === Symbol &&\n        obj !== Symbol.prototype\n        ? \"symbol\"\n        : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n"], "mappings": ";;;;;;AAIe,SAASA,OAAO,CAACC,GAAG,EAAE;EACnC,yBAAyB;;EAEzB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACvE,kBAAAH,OAAO,GAAG,UAAUC,GAAG,EAAE;MACvB,OAAO,OAAOA,GAAG;IACnB,CAAC;EACH,CAAC,MAAM;IACL,kBAAAD,OAAO,GAAG,UAAUC,GAAG,EAAE;MACvB,OAAOA,GAAG,IACR,OAAOC,MAAM,KAAK,UAAU,IAC5BD,GAAG,CAACG,WAAW,KAAKF,MAAM,IAC1BD,GAAG,KAAKC,MAAM,CAACG,SAAS,GACtB,QAAQ,GACR,OAAOJ,GAAG;IAChB,CAAC;EACH;EAEA,OAAOD,OAAO,CAACC,GAAG,CAAC;AACrB"}