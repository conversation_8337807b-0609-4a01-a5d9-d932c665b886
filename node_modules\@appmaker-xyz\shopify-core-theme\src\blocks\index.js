import FulfillmentStatus from '../components/order/FulfillmentStatus';
import FulfillmentItem from '../components/order/FulfillmentItem';
import OrderCard from '../components/order/OrderCard';
import OrderDetailHeader from '../components/order/OrderDetailHeader';
import OrderDetailItemCard from '../components/order/OrderDetailItemCard';
import OrderPriceSummary from '../components/order/OrderPriceSummary';
import ShippingAddress from '../components/order/ShippingAddress';
import ProfileCard from '../components/user/ProfileCard';
import {
  ShopifyOrderLineItems,
  ShopifyOrderPriceTable,
} from '../components/order/ShopifyOrderBlocks';
import Login from '../components/user/Login';
import Register from '../components/user/Register';
import DrawerHeader from '../components/menu/DrawerHeader';
import ResetPassword from '../components/user/ResetPassword';
import ProductGridBlock from '../components/productList/gridWidget';
import ProductImage from '../components/productDetail/ProductImage';
import ProductData from '../components/productDetail/ProductData';
import ActionButtons from '../components/productDetail/ActionButtons';
import Collection from '../components/collection/index';
import ShopifyProductOptions from '../components/productDetail/ShopifyProductOptions';
import ShopifyCollectionFilterSort from '../components/collection/Filter';
import SearchBlock from '../components/search/SearchBlock';
import MyAccountEdit from '../components/user/MyAccountEdit';
import ImageSwiper from '../components/productDetail/components/ZoomableSwiper';
import OtpLoginAccountDetails from '../components/user/OtpLoginAccountDetails';
import AddressForm from '../components/user/address/AddressForm';
import AddressList from '../components/user/address/AddressList';
import SearchSuggestionWrapper from '../components/search/SearchSuggestionWrapper';
import CollectionSuggestionWrapper from '../components/search/CollectionSuggestionWrapper';
import DrawerMenuBlock from '../components/menu/MenuBlock';
import CountDownTimerBlock from '../components/countdownTimer/CountDownTimerBlock';
import PinCheck from '../components/productDetail/PinCheck';
import ShopifyReview from '../components/productDetail/ShopifyReview';
import ResendOTP from '../components/user/otpLogin/ResendOTP';
import MobileInput from '../components/user/otpLogin/MobileInput';
import EmailOTP from '../components/user/otpLogin/EmailOTP';

import SuccessHeader from '../components/thankYou/SuccessHeader';
import SuccessBanner from '../components/thankYou/SuccessBanner';
import OrderSummary from '../components/thankYou/OrderSummary';
import OrderDetails from '../components/thankYou/OrderDetails';
import HomeButton from '../components/thankYou/HomeButton';

const blocks = [
  {
    name: 'shopify/order-line-items',
    View: ShopifyOrderLineItems,
  },
  {
    name: 'shopify/order-detail-price-table',
    View: ShopifyOrderPriceTable,
  },
  {
    name: 'shopify/profile-card-item',
    View: ProfileCard,
  },
  {
    name: 'shopify/order-card',
    View: OrderCard,
  },
  {
    name: 'shopify/order-detail-header',
    View: OrderDetailHeader,
  },
  {
    name: 'shopify/fulfillment-status',
    View: FulfillmentStatus,
  },
  {
    name: 'shopify/fulfillment-single-line-item',
    View: FulfillmentItem,
  },
  {
    name: 'shopify/single-order-line-item',
    View: OrderDetailItemCard,
  },
  {
    name: 'shopify/shipping-address',
    View: ShippingAddress,
  },
  {
    name: 'shopify/order-detail-price-summary',
    View: OrderPriceSummary,
  },
  {
    name: 'shopify/login-block',
    View: Login,
  },
  {
    name: 'shopify/register-block',
    View: Register,
  },
  {
    name: 'shopify/drawer-header',
    View: DrawerHeader,
  },
  {
    name: 'shopify/reset-password-block',
    View: ResetPassword,
  },
  {
    name: 'appmaker/product-grid-item',
    View: ProductGridBlock,
  },
  {
    name: 'appmaker/shopify-product-image',
    View: ProductImage,
  },
  {
    name: 'appmaker/product-image',
    View: ProductImage,
  },
  {
    name: 'appmaker/product-data',
    View: ProductData,
  },
  {
    name: 'shopify/products-collection',
    View: Collection,
  },
  {
    name: 'appmaker/shopify-product-variation',
    View: ShopifyProductOptions,
  },
  {
    name: 'appmaker/shopify-collection-filter',
    View: ShopifyCollectionFilterSort,
  },
  {
    name: 'shopify/search-block',
    View: SearchBlock,
  },
  {
    name: 'shopify/search-suggest',
    View: SearchSuggestionWrapper,
  },
  {
    name: 'shopify/collection-suggest',
    View: CollectionSuggestionWrapper,
  },
  {
    name: 'shopify/my-profile-edit',
    View: MyAccountEdit,
  },
  {
    name: 'shopify/zoomable-image',
    View: ImageSwiper,
  },
  {
    name: 'appmaker/buttons',
    View: ActionButtons,
  },
  {
    name: 'appmaker/account_details',
    View: OtpLoginAccountDetails,
  },
  {
    name: 'shopify/create-address',
    View: AddressForm,
  },
  {
    name: 'shopify/address-list',
    View: AddressList,
  },
  {
    name: 'appmaker/drawer-menu',
    View: DrawerMenuBlock,
  },
  {
    name: 'appmaker/count-down-timer',
    View: CountDownTimerBlock,
  },
  {
    name: 'appmaker/pin-check',
    View: PinCheck,
  },
  {
    name: 'appmaker/ShopifyReview',
    View: ShopifyReview,
  },
  {
    name: 'appmaker/otp-resend-button',
    View: ResendOTP,
  },
  {
    name: 'appmaker/otp-login',
    View: MobileInput,
  },
  {
    name: 'appmaker/email-otp-login',
    View: EmailOTP,
  },
  {
    name: 'appmaker/order-success-header',
    View: SuccessHeader,
  },
  {
    name: 'appmaker/order-success-banner',
    View: SuccessBanner,
  },
  {
    name: 'appmaker/order-success-summary',
    View: OrderSummary,
  },
  {
    name: 'appmaker/order-success-details',
    View: OrderDetails,
  },
  {
    name: 'appmaker/home-button',
    View: HomeButton,
  },
];

export { blocks };
