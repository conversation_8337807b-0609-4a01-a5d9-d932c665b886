import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, TextInput, View, I18nManager } from 'react-native';
import { AppmakerText } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { testProps } from '@appmaker-xyz/core';
import { useTranslation } from 'react-i18next';
import { usePageState } from '@appmaker-xyz/core';
function isEditable(disabled) {
  if (disabled === true || disabled === 'true' || disabled === 'disabled') {
    return false;
  } else {
    return true;
  }
}
const FormInputBlock = ({ attributes, coreDispatch, clientId, onAction }) => {
  const {
    name,
    type,
    editable: disabled,
    leftIcon,
    rightIcon,
    label,
    status,
    caption,
    captionIcon,
    value,
    defaultValue,
    // onChangeText,
    noOutline,
    onChange,
    autoCompleteType,
    blockItem,
    nextFieldName,
    maxLength = 1000,
    autoAction,
  } = attributes;
  let textMaxCount =
    typeof maxLength === 'string' ? parseInt(maxLength) : maxLength;
  const editable = isEditable(disabled);

  // useEffect(() => {
  //   pageDispatch({
  //     type: 'set_values',
  //     values: { [name]: value },
  //   });
  // }, [value, name]);
  const { t } = useTranslation();
  const [focus, setFocus] = useState(false);
  const [hidePass, setHidePass] = useState(true);
  // const [valueText, setValueText] = useState(defaultValue);
  const { color, spacing, font, fonts } = useApThemeState();
  const styles = allStyles({ color, spacing, font, fonts, focus });
  const mainContainerStyles = [styles.container];
  const containerStyles = [styles.inputContainer];
  const iconStyles = [styles.icon];
  const inputTextStyle = [styles.inputArea];

  if (status && color[status]) {
    containerStyles.push({ borderColor: color[status] });
    iconStyles.push({ color: color[status] });
  }

  if (editable === false) {
    containerStyles.push({
      backgroundColor: color.light,
      borderColor: color.grey,
    });
    iconStyles.push({ color: color.grey });
    inputTextStyle.push({ color: color.grey });
  }

  if (I18nManager.isRTL) {
    inputTextStyle.push({ textAlign: 'right' });
  }

  if (type === 'noContainer') {
    mainContainerStyles.push({
      paddingTop: 0,
      marginBottom: 0,
    });
  }
  if (noOutline === true) {
    containerStyles.push({
      borderWidth: 0,
    });
  }
  const inputRef = useRef();
  const parentName = attributes.parentName || '_formData';
  function onChangeInput(valueText) {
    valueText = valueText || '';
    coreDispatch({
      type: 'SET_VALUE',
      name,
      value: valueText.trim(),
      parent: parentName,
    });
    if (
      autoAction &&
      textMaxCount &&
      valueText &&
      valueText?.length === textMaxCount
    ) {
      coreDispatch &&
        coreDispatch({
          type: 'SET_VALUE',
          name: 'loadingButton',
          value: true,
        });
      autoAction.appmakerAction && onAction(autoAction.appmakerAction);
    }
  }
  const currentFocus = usePageState((state) => state.__currentFocus);

  const inputValue = usePageState((state) => {
    return state[parentName] && state[parentName][name]
      ? state[parentName][name]
      : '';
  });
  useEffect(() => {
    onChangeInput(defaultValue);
  }, [defaultValue]);
  useEffect(() => {
    if (currentFocus === name) {
      inputRef.current.focus();
    }
  }, [currentFocus]);

  const handleBlur = (e) => {
    if (e.target.value) {
      setFocus(true);
    } else {
      setFocus(false);
    }
  };

  return (
    <View style={mainContainerStyles}>
      <View style={containerStyles}>
        {leftIcon && <Icon name={leftIcon} style={iconStyles} />}
        {label && (focus === true || inputValue !== '') ? (
          <AppmakerText
            category="highlighter2"
            status={status}
            style={styles.floatingLabel}>
            {label}
          </AppmakerText>
        ) : null}
        <TextInput
          {...testProps(clientId)}
          ref={inputRef}
          style={inputTextStyle}
          textContentType={type}
          autoCompleteType={autoCompleteType ? autoCompleteType : 'off'}
          keyboardType={type === 'number' ? 'phone-pad' : 'default'}
          secureTextEntry={type && type === 'password' && hidePass}
          placeholder={t(label)}
          autoFocus={false}
          autoCapitalize="none"
          editable={editable}
          maxLength={textMaxCount}
          value={inputValue}
          returnKeyType={nextFieldName ? 'next' : 'default'}
          onChangeText={onChangeInput}
          onSubmitEditing={() => {
            onChangeInput &&
              coreDispatch({
                type: 'SET_VALUE',
                name: '__currentFocus',
                value: nextFieldName,
              });
          }}
          onChange={onChange}
          onFocus={() => {
            setFocus(true);
          }}
          onBlur={handleBlur}
          blurOnSubmit
        />
        {rightIcon && <Icon name={rightIcon} style={iconStyles} />}
        {type && type === 'password' && (
          <Icon
            name={hidePass === false ? 'eye-off' : 'eye'}
            style={iconStyles}
            onPress={() => {
              setHidePass(!hidePass);
            }}
          />
        )}
      </View>
      {caption && (
        <AppmakerText
          category="highlighter2"
          status={status}
          style={styles.caption}>
          {caption && captionIcon && <Icon name={captionIcon} />} {caption}
        </AppmakerText>
      )}
    </View>
  );
};

const allStyles = ({ spacing, color, font, fonts, focus }) =>
  StyleSheet.create({
    container: {
      paddingTop: spacing.small,
      position: 'relative',
      marginBottom: spacing.md,
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      height: 54,
      backgroundColor: color.white,
      borderWidth: 1,
      borderColor: color.demiDark,
      borderRadius: spacing.nano,
      paddingHorizontal: spacing.small,
      paddingTop: focus ? 8 : 0,
    },
    inputArea: {
      flex: 1,
      paddingHorizontal: spacing.small,
      ...fonts.bodyParagraph,
      color: color.dark,
      fontWeight: 'normal',
    },
    floatingLabel: {
      position: 'absolute',
      top: 2,
      left: 12,
      backgroundColor: 'transparent',
      paddingHorizontal: spacing.nano,
    },
    icon: {
      fontSize: font.size.lg,
      color: color.dark,
      padding: spacing.nano,
    },
    caption: {
      marginTop: spacing.nano,
    },
  });

export default FormInputBlock;
