{"version": 3, "names": ["_t", "require", "addComment", "_addComment", "addComments", "_addComments", "shareCommentsWithSiblings", "key", "node", "trailing", "trailingComments", "leading", "leadingComments", "prev", "getSibling", "next", "has<PERSON>rev", "Boolean", "hasNext", "type", "content", "line", "comments"], "sources": ["../../src/path/comments.ts"], "sourcesContent": ["// This file contains methods responsible for dealing with comments.\nimport type * as t from \"@babel/types\";\nimport type NodePath from \"./index\";\nimport {\n  addComment as _addComment,\n  addComments as _addComments,\n} from \"@babel/types\";\n\n/**\n * Share comments amongst siblings.\n */\n\nexport function shareCommentsWithSiblings(this: NodePath) {\n  // NOTE: this assumes numbered keys\n  if (typeof this.key === \"string\") return;\n\n  const node = this.node;\n  if (!node) return;\n\n  const trailing = node.trailingComments;\n  const leading = node.leadingComments;\n  if (!trailing && !leading) return;\n\n  const prev = this.getSibling(this.key - 1);\n  const next = this.getSibling(this.key + 1);\n  const hasPrev = Boolean(prev.node);\n  const hasNext = Boolean(next.node);\n  if (hasPrev && !hasNext) {\n    prev.addComments(\"trailing\", trailing);\n  } else if (hasNext && !hasPrev) {\n    next.addComments(\"leading\", leading);\n  }\n}\n\nexport function addComment(\n  this: NodePath,\n  type: t.CommentTypeShorthand,\n  content: string,\n  line?: boolean,\n) {\n  _addComment(this.node, type, content, line);\n}\n\n/**\n * Give node `comments` of the specified `type`.\n */\n\nexport function addComments(\n  this: NodePath,\n  type: t.CommentTypeShorthand,\n  comments: t.Comment[],\n) {\n  _addComments(this.node, type, comments);\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,EAAA,GAAAC,OAAA;AAGsB;EAFpBC,UAAU,EAAIC,WAAW;EACzBC,WAAW,EAAIC;AAAY,IAAAL,EAAA;AAOtB,SAASM,yBAAyBA,CAAA,EAAiB;EAExD,IAAI,OAAO,IAAI,CAACC,GAAG,KAAK,QAAQ,EAAE;EAElC,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI;EACtB,IAAI,CAACA,IAAI,EAAE;EAEX,MAAMC,QAAQ,GAAGD,IAAI,CAACE,gBAAgB;EACtC,MAAMC,OAAO,GAAGH,IAAI,CAACI,eAAe;EACpC,IAAI,CAACH,QAAQ,IAAI,CAACE,OAAO,EAAE;EAE3B,MAAME,IAAI,GAAG,IAAI,CAACC,UAAU,CAAC,IAAI,CAACP,GAAG,GAAG,CAAC,CAAC;EAC1C,MAAMQ,IAAI,GAAG,IAAI,CAACD,UAAU,CAAC,IAAI,CAACP,GAAG,GAAG,CAAC,CAAC;EAC1C,MAAMS,OAAO,GAAGC,OAAO,CAACJ,IAAI,CAACL,IAAI,CAAC;EAClC,MAAMU,OAAO,GAAGD,OAAO,CAACF,IAAI,CAACP,IAAI,CAAC;EAClC,IAAIQ,OAAO,IAAI,CAACE,OAAO,EAAE;IACvBL,IAAI,CAACT,WAAW,CAAC,UAAU,EAAEK,QAAQ,CAAC;EACxC,CAAC,MAAM,IAAIS,OAAO,IAAI,CAACF,OAAO,EAAE;IAC9BD,IAAI,CAACX,WAAW,CAAC,SAAS,EAAEO,OAAO,CAAC;EACtC;AACF;AAEO,SAAST,UAAUA,CAExBiB,IAA4B,EAC5BC,OAAe,EACfC,IAAc,EACd;EACAlB,WAAW,CAAC,IAAI,CAACK,IAAI,EAAEW,IAAI,EAAEC,OAAO,EAAEC,IAAI,CAAC;AAC7C;AAMO,SAASjB,WAAWA,CAEzBe,IAA4B,EAC5BG,QAAqB,EACrB;EACAjB,YAAY,CAAC,IAAI,CAACG,IAAI,EAAEW,IAAI,EAAEG,QAAQ,CAAC;AACzC"}