const ChumbakInsider = {
  type: 'normal',
  title: 'Chumbak insider',
  attributes: {
    showLoadingTillData: true,
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/chumbak-insider',
      attributes: {
        data: '{{blockData}}',
        type: 'cart_popup',
      },
    },
  ],
  dataSource: {
    source: 'shopify',
    responseType: 'replace',
    attributes: {
      mapping: {
        items: 'data.data.products.edges',
      },
      methodName: 'searchProduct',
      params: 'product_type:membership',
    },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
    dependencies: {
      pageState: ['searchKey'],
    },
  },
};

export default ChumbakInsider;
