{"version": 3, "names": ["_t", "require", "STATEMENT_OR_BLOCK_KEYS", "VISITOR_KEYS", "isBlockStatement", "isExpression", "isIdentifier", "isLiteral", "isStringLiteral", "isType", "matchesPattern", "_matchesPattern", "pattern", "allowPartial", "node", "has", "key", "val", "Array", "isArray", "length", "isStatic", "scope", "is", "exports", "isnt", "equals", "value", "isNodeType", "type", "canHaveVariableDeclarationOrExpression", "parentPath", "isFor", "canSwapBetweenExpressionAndStatement", "replacement", "isArrowFunctionExpression", "isCompletionRecord", "allowInsideFunction", "path", "first", "container", "isFunction", "isProgram", "isDoExpression", "isStatementOrBlock", "isLabeledStatement", "includes", "referencesImport", "moduleSource", "importName", "isReferencedIdentifier", "isJSXMemberExpression", "property", "name", "isMemberExpression", "isOptionalMemberExpression", "computed", "object", "get", "binding", "getBinding", "kind", "parent", "isImportDeclaration", "source", "isImportDefaultSpecifier", "isImportNamespaceSpecifier", "isImportSpecifier", "imported", "getSource", "end", "code", "hub", "getCode", "slice", "start", "willIMaybeExecuteBefore", "target", "_guessExecutionStatusRelativeTo", "getOuterFunction", "getFunctionParent", "getProgramParent", "isExecutionUncertain", "isExecutionUncertainInList", "paths", "maxIndex", "i", "parent<PERSON><PERSON>", "SYMBOL_CHECKING", "Symbol", "_guessExecutionStatusRelativeToCached", "Map", "base", "cache", "func<PERSON>arent", "this", "_guessExecutionStatusRelativeToDifferentFunctionsCached", "getAncestry", "indexOf", "commonPath", "commonIndex", "Error", "divergence", "<PERSON><PERSON><PERSON>", "keys", "keyPosition", "_guessExecutionStatusRelativeToDifferentFunctionsInternal", "isFunctionDeclaration", "isExportDeclaration", "id", "references", "referencePaths", "allStatus", "childOfFunction", "find", "isCallExpression", "status", "nodeMap", "cached", "set", "result", "resolve", "dangerous", "resolved", "_resolve", "push", "isVariableDeclarator", "constant", "ret", "isTypeCastExpression", "<PERSON><PERSON><PERSON>", "toCom<PERSON><PERSON>ey", "targetName", "isObjectExpression", "props", "prop", "isProperty", "match", "isArrayExpression", "isNaN", "elems", "elem", "isConstantExpression", "isRegExpLiteral", "isTemplateLiteral", "every", "expression", "isUnaryExpression", "operator", "isBinaryExpression", "isInStrictMode", "strictParent", "sourceType", "isClass", "body", "directive", "directives"], "sources": ["../../src/path/introspection.ts"], "sourcesContent": ["// This file contains methods responsible for introspecting the current path for certain values.\n\nimport type <PERSON>de<PERSON><PERSON> from \"./index\";\nimport {\n  STATEMENT_OR_BLOCK_KEYS,\n  VISITOR_KEYS,\n  isBlockStatement,\n  isExpression,\n  isIdentifier,\n  isLiteral,\n  isStringLiteral,\n  isType,\n  matchesPattern as _matchesPattern,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\n/**\n * Match the current node if it matches the provided `pattern`.\n *\n * For example, given the match `React.createClass` it would match the\n * parsed nodes of `React.createClass` and `React[\"createClass\"]`.\n */\n\nexport function matchesPattern(\n  this: NodePath,\n  pattern: string,\n  allowPartial?: boolean,\n): boolean {\n  return _matchesPattern(this.node, pattern, allowPartial);\n}\n\n/**\n * Check whether we have the input `key`. If the `key` references an array then we check\n * if the array has any items, otherwise we just check if it's falsy.\n */\n\nexport function has<N extends t.Node>(\n  this: NodePath<N>,\n  key: keyof N,\n): boolean {\n  const val = this.node && this.node[key];\n  if (val && Array.isArray(val)) {\n    return !!val.length;\n  } else {\n    return !!val;\n  }\n}\n\n/**\n * Description\n */\n\nexport function isStatic(this: NodePath): boolean {\n  return this.scope.isStatic(this.node);\n}\n\n/**\n * Alias of `has`.\n */\n\nexport const is = has;\n\n/**\n * Opposite of `has`.\n */\n\nexport function isnt<N extends t.Node>(\n  this: NodePath<N>,\n  key: keyof N,\n): boolean {\n  return !this.has(key);\n}\n\n/**\n * Check whether the path node `key` strict equals `value`.\n */\n\nexport function equals<N extends t.Node>(\n  this: NodePath<N>,\n  key: keyof N,\n  value: any,\n): boolean {\n  return this.node[key] === value;\n}\n\n/**\n * Check the type against our stored internal type of the node. This is handy when a node has\n * been removed yet we still internally know the type and need it to calculate node replacement.\n */\n\nexport function isNodeType(this: NodePath, type: string): boolean {\n  return isType(this.type, type);\n}\n\n/**\n * This checks whether or not we're in one of the following positions:\n *\n *   for (KEY in right);\n *   for (KEY;;);\n *\n * This is because these spots allow VariableDeclarations AND normal expressions so we need\n * to tell the path replacement that it's ok to replace this with an expression.\n */\n\nexport function canHaveVariableDeclarationOrExpression(this: NodePath) {\n  return (\n    (this.key === \"init\" || this.key === \"left\") && this.parentPath.isFor()\n  );\n}\n\n/**\n * This checks whether we are swapping an arrow function's body between an\n * expression and a block statement (or vice versa).\n *\n * This is because arrow functions may implicitly return an expression, which\n * is the same as containing a block statement.\n */\n\nexport function canSwapBetweenExpressionAndStatement(\n  this: NodePath,\n  replacement: t.Node,\n): boolean {\n  if (this.key !== \"body\" || !this.parentPath.isArrowFunctionExpression()) {\n    return false;\n  }\n\n  if (this.isExpression()) {\n    return isBlockStatement(replacement);\n  } else if (this.isBlockStatement()) {\n    return isExpression(replacement);\n  }\n\n  return false;\n}\n\n/**\n * Check whether the current path references a completion record\n */\n\nexport function isCompletionRecord(\n  this: NodePath,\n  allowInsideFunction?: boolean,\n): boolean {\n  let path = this;\n  let first = true;\n\n  do {\n    const { type, container } = path;\n\n    // we're in a function so can't be a completion record\n    if (!first && (path.isFunction() || type === \"StaticBlock\")) {\n      return !!allowInsideFunction;\n    }\n\n    first = false;\n\n    // check to see if we're the last item in the container and if we are\n    // we're a completion record!\n    if (Array.isArray(container) && path.key !== container.length - 1) {\n      return false;\n    }\n  } while (\n    (path = path.parentPath) &&\n    !path.isProgram() &&\n    !path.isDoExpression()\n  );\n\n  return true;\n}\n\n/**\n * Check whether or not the current `key` allows either a single statement or block statement\n * so we can explode it if necessary.\n */\n\nexport function isStatementOrBlock(this: NodePath): boolean {\n  if (\n    this.parentPath.isLabeledStatement() ||\n    isBlockStatement(this.container)\n  ) {\n    return false;\n  } else {\n    return STATEMENT_OR_BLOCK_KEYS.includes(this.key as string);\n  }\n}\n\n/**\n * Check if the currently assigned path references the `importName` of `moduleSource`.\n */\n\nexport function referencesImport(\n  this: NodePath,\n  moduleSource: string,\n  importName: string,\n): boolean {\n  if (!this.isReferencedIdentifier()) {\n    if (\n      (this.isJSXMemberExpression() &&\n        this.node.property.name === importName) ||\n      ((this.isMemberExpression() || this.isOptionalMemberExpression()) &&\n        (this.node.computed\n          ? isStringLiteral(this.node.property, { value: importName })\n          : (this.node.property as t.Identifier).name === importName))\n    ) {\n      const object = (\n        this as NodePath<t.MemberExpression | t.OptionalMemberExpression>\n      ).get(\"object\");\n      return (\n        object.isReferencedIdentifier() &&\n        object.referencesImport(moduleSource, \"*\")\n      );\n    }\n\n    return false;\n  }\n\n  const binding = this.scope.getBinding((this.node as t.Identifier).name);\n  if (!binding || binding.kind !== \"module\") return false;\n\n  const path = binding.path;\n  const parent = path.parentPath;\n  if (!parent.isImportDeclaration()) return false;\n\n  // check moduleSource\n  if (parent.node.source.value === moduleSource) {\n    if (!importName) return true;\n  } else {\n    return false;\n  }\n\n  if (path.isImportDefaultSpecifier() && importName === \"default\") {\n    return true;\n  }\n\n  if (path.isImportNamespaceSpecifier() && importName === \"*\") {\n    return true;\n  }\n\n  if (\n    path.isImportSpecifier() &&\n    isIdentifier(path.node.imported, { name: importName })\n  ) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * Get the source code associated with this node.\n */\n\nexport function getSource(this: NodePath): string {\n  const node = this.node;\n  if (node.end) {\n    const code = this.hub.getCode();\n    if (code) return code.slice(node.start, node.end);\n  }\n  return \"\";\n}\n\nexport function willIMaybeExecuteBefore(\n  this: NodePath,\n  target: NodePath,\n): boolean {\n  return this._guessExecutionStatusRelativeTo(target) !== \"after\";\n}\n\nfunction getOuterFunction(path: NodePath) {\n  return path.isProgram()\n    ? path\n    : (\n        path.parentPath.scope.getFunctionParent() ||\n        path.parentPath.scope.getProgramParent()\n      ).path;\n}\n\nfunction isExecutionUncertain(type: t.Node[\"type\"], key: string) {\n  switch (type) {\n    // a && FOO\n    // a || FOO\n    case \"LogicalExpression\":\n      return key === \"right\";\n\n    // a ? FOO : FOO\n    // if (a) FOO; else FOO;\n    case \"ConditionalExpression\":\n    case \"IfStatement\":\n      return key === \"consequent\" || key === \"alternate\";\n\n    // while (a) FOO;\n    case \"WhileStatement\":\n    case \"DoWhileStatement\":\n    case \"ForInStatement\":\n    case \"ForOfStatement\":\n      return key === \"body\";\n\n    // for (a; b; FOO) FOO;\n    case \"ForStatement\":\n      return key === \"body\" || key === \"update\";\n\n    // switch (a) { FOO }\n    case \"SwitchStatement\":\n      return key === \"cases\";\n\n    // try { a } catch FOO finally { b }\n    case \"TryStatement\":\n      return key === \"handler\";\n\n    // var [ x = FOO ]\n    case \"AssignmentPattern\":\n      return key === \"right\";\n\n    // a?.[FOO]\n    case \"OptionalMemberExpression\":\n      return key === \"property\";\n\n    // a?.(FOO)\n    case \"OptionalCallExpression\":\n      return key === \"arguments\";\n\n    default:\n      return false;\n  }\n}\n\nfunction isExecutionUncertainInList(paths: NodePath[], maxIndex: number) {\n  for (let i = 0; i < maxIndex; i++) {\n    const path = paths[i];\n    if (isExecutionUncertain(path.parent.type, path.parentKey)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n// TODO (Babel 8)\n// This can be { before: boolean, after: boolean, unknown: boolean }.\n// This allows transforms like the tdz one to treat cases when the status\n// is both before and unknown/after like if it were before.\ntype RelativeExecutionStatus = \"before\" | \"after\" | \"unknown\";\n\n// Used to avoid infinite recursion in cases like\n//   function f() { if (false) f(); }\n//   f();\n// It also works with indirect recursion.\nconst SYMBOL_CHECKING = Symbol();\n\ntype ExecutionStatusCache = Map<\n  t.Node,\n  Map<t.Node, RelativeExecutionStatus | typeof SYMBOL_CHECKING>\n>;\n\n/**\n * Given a `target` check the execution status of it relative to the current path.\n *\n * \"Execution status\" simply refers to where or not we **think** this will execute\n * before or after the input `target` element.\n */\n\nexport function _guessExecutionStatusRelativeTo(\n  this: NodePath,\n  target: NodePath,\n): RelativeExecutionStatus {\n  return _guessExecutionStatusRelativeToCached(this, target, new Map());\n}\n\nfunction _guessExecutionStatusRelativeToCached(\n  base: NodePath,\n  target: NodePath,\n  cache: ExecutionStatusCache,\n): RelativeExecutionStatus {\n  // check if the two paths are in different functions, we can't track execution of these\n  const funcParent = {\n    this: getOuterFunction(base),\n    target: getOuterFunction(target),\n  };\n\n  // here we check the `node` equality as sometimes we may have different paths for the\n  // same node due to path thrashing\n  if (funcParent.target.node !== funcParent.this.node) {\n    return _guessExecutionStatusRelativeToDifferentFunctionsCached(\n      base,\n      funcParent.target,\n      cache,\n    );\n  }\n\n  const paths = {\n    target: target.getAncestry(),\n    this: base.getAncestry(),\n  };\n\n  // If this is an ancestor of the target path,\n  // e.g. f(g); where this is f and target is g.\n  if (paths.target.indexOf(base) >= 0) return \"after\";\n  if (paths.this.indexOf(target) >= 0) return \"before\";\n\n  // get ancestor where the branches intersect\n  let commonPath;\n  const commonIndex = { target: 0, this: 0 };\n\n  while (!commonPath && commonIndex.this < paths.this.length) {\n    const path = paths.this[commonIndex.this];\n    commonIndex.target = paths.target.indexOf(path);\n    if (commonIndex.target >= 0) {\n      commonPath = path;\n    } else {\n      commonIndex.this++;\n    }\n  }\n\n  if (!commonPath) {\n    throw new Error(\n      \"Internal Babel error - The two compared nodes\" +\n        \" don't appear to belong to the same program.\",\n    );\n  }\n\n  if (\n    isExecutionUncertainInList(paths.this, commonIndex.this - 1) ||\n    isExecutionUncertainInList(paths.target, commonIndex.target - 1)\n  ) {\n    return \"unknown\";\n  }\n\n  const divergence = {\n    this: paths.this[commonIndex.this - 1],\n    target: paths.target[commonIndex.target - 1],\n  };\n\n  // container list so let's see which one is after the other\n  // e.g. [ THIS, TARGET ]\n  if (\n    divergence.target.listKey &&\n    divergence.this.listKey &&\n    divergence.target.container === divergence.this.container\n  ) {\n    return divergence.target.key > divergence.this.key ? \"before\" : \"after\";\n  }\n\n  // otherwise we're associated by a parent node, check which key comes before the other\n  const keys = VISITOR_KEYS[commonPath.type];\n  const keyPosition = {\n    this: keys.indexOf(divergence.this.parentKey),\n    target: keys.indexOf(divergence.target.parentKey),\n  };\n  return keyPosition.target > keyPosition.this ? \"before\" : \"after\";\n}\n\nfunction _guessExecutionStatusRelativeToDifferentFunctionsInternal(\n  base: NodePath,\n  target: NodePath,\n  cache: ExecutionStatusCache,\n): RelativeExecutionStatus {\n  if (!target.isFunctionDeclaration()) {\n    if (\n      _guessExecutionStatusRelativeToCached(base, target, cache) === \"before\"\n    ) {\n      return \"before\";\n    }\n    return \"unknown\";\n  } else if (target.parentPath.isExportDeclaration()) {\n    return \"unknown\";\n  }\n\n  // so we're in a completely different function, if this is a function declaration\n  // then we can be a bit smarter and handle cases where the function is either\n  // a. not called at all (part of an export)\n  // b. called directly\n  const binding = target.scope.getBinding(target.node.id.name);\n\n  // no references!\n  if (!binding.references) return \"before\";\n\n  const referencePaths: Array<NodePath> = binding.referencePaths;\n\n  let allStatus;\n\n  // verify that all the calls have the same execution status\n  for (const path of referencePaths) {\n    // if a reference is a child of the function we're checking against then we can\n    // safely ignore it\n    const childOfFunction = !!path.find(path => path.node === target.node);\n    if (childOfFunction) continue;\n\n    if (path.key !== \"callee\" || !path.parentPath.isCallExpression()) {\n      // This function is passed as a reference, so we don't\n      // know when it will be called.\n      return \"unknown\";\n    }\n\n    const status = _guessExecutionStatusRelativeToCached(base, path, cache);\n\n    if (allStatus && allStatus !== status) {\n      return \"unknown\";\n    } else {\n      allStatus = status;\n    }\n  }\n\n  return allStatus;\n}\n\nfunction _guessExecutionStatusRelativeToDifferentFunctionsCached(\n  base: NodePath,\n  target: NodePath,\n  cache: ExecutionStatusCache,\n): RelativeExecutionStatus {\n  let nodeMap = cache.get(base.node);\n  let cached;\n\n  if (!nodeMap) {\n    cache.set(base.node, (nodeMap = new Map()));\n  } else if ((cached = nodeMap.get(target.node))) {\n    if (cached === SYMBOL_CHECKING) {\n      return \"unknown\";\n    }\n    return cached;\n  }\n\n  nodeMap.set(target.node, SYMBOL_CHECKING);\n\n  const result = _guessExecutionStatusRelativeToDifferentFunctionsInternal(\n    base,\n    target,\n    cache,\n  );\n\n  nodeMap.set(target.node, result);\n  return result;\n}\n\n/**\n * Resolve a \"pointer\" `NodePath` to it's absolute path.\n */\nexport function resolve(\n  this: NodePath,\n  dangerous?: boolean,\n  resolved?: NodePath[],\n) {\n  return this._resolve(dangerous, resolved) || this;\n}\n\nexport function _resolve(\n  this: NodePath,\n  dangerous?: boolean,\n  resolved?: NodePath[],\n): NodePath | undefined | null {\n  // detect infinite recursion\n  // todo: possibly have a max length on this just to be safe\n  if (resolved && resolved.indexOf(this) >= 0) return;\n\n  // we store all the paths we've \"resolved\" in this array to prevent infinite recursion\n  resolved = resolved || [];\n  resolved.push(this);\n\n  if (this.isVariableDeclarator()) {\n    if (this.get(\"id\").isIdentifier()) {\n      return this.get(\"init\").resolve(dangerous, resolved);\n    } else {\n      // otherwise it's a request for a pattern and that's a bit more tricky\n    }\n  } else if (this.isReferencedIdentifier()) {\n    const binding = this.scope.getBinding(this.node.name);\n    if (!binding) return;\n\n    // reassigned so we can't really resolve it\n    if (!binding.constant) return;\n\n    // todo - lookup module in dependency graph\n    if (binding.kind === \"module\") return;\n\n    if (binding.path !== this) {\n      const ret = binding.path.resolve(dangerous, resolved);\n      // If the identifier resolves to parent node then we can't really resolve it.\n      if (this.find(parent => parent.node === ret.node)) return;\n      return ret;\n    }\n  } else if (this.isTypeCastExpression()) {\n    // @ ts-ignore todo: babel-types\n    return this.get(\"expression\").resolve(dangerous, resolved);\n  } else if (dangerous && this.isMemberExpression()) {\n    // this is dangerous, as non-direct target assignments will mutate it's state\n    // making this resolution inaccurate\n\n    const targetKey = this.toComputedKey();\n    if (!isLiteral(targetKey)) return;\n\n    // @ts-expect-error todo(flow->ts): NullLiteral\n    const targetName = targetKey.value;\n\n    const target = this.get(\"object\").resolve(dangerous, resolved);\n\n    if (target.isObjectExpression()) {\n      const props = target.get(\"properties\");\n      for (const prop of props as any[]) {\n        if (!prop.isProperty()) continue;\n\n        const key = prop.get(\"key\");\n\n        // { foo: obj }\n        let match =\n          prop.isnt(\"computed\") && key.isIdentifier({ name: targetName });\n\n        // { \"foo\": \"obj\" } or { [\"foo\"]: \"obj\" }\n        match = match || key.isLiteral({ value: targetName });\n\n        if (match) return prop.get(\"value\").resolve(dangerous, resolved);\n      }\n    } else if (target.isArrayExpression() && !isNaN(+targetName)) {\n      const elems = target.get(\"elements\");\n      const elem = elems[targetName];\n      if (elem) return elem.resolve(dangerous, resolved);\n    }\n  }\n}\n\nexport function isConstantExpression(this: NodePath): boolean {\n  if (this.isIdentifier()) {\n    const binding = this.scope.getBinding(this.node.name);\n    if (!binding) return false;\n    return binding.constant;\n  }\n\n  if (this.isLiteral()) {\n    if (this.isRegExpLiteral()) {\n      return false;\n    }\n\n    if (this.isTemplateLiteral()) {\n      return this.get(\"expressions\").every(expression =>\n        expression.isConstantExpression(),\n      );\n    }\n\n    return true;\n  }\n\n  if (this.isUnaryExpression()) {\n    if (this.node.operator !== \"void\") {\n      return false;\n    }\n\n    return this.get(\"argument\").isConstantExpression();\n  }\n\n  if (this.isBinaryExpression()) {\n    const { operator } = this.node;\n    return (\n      operator !== \"in\" &&\n      operator !== \"instanceof\" &&\n      this.get(\"left\").isConstantExpression() &&\n      this.get(\"right\").isConstantExpression()\n    );\n  }\n\n  return false;\n}\n\nexport function isInStrictMode(this: NodePath) {\n  const start = this.isProgram() ? this : this.parentPath;\n\n  const strictParent = start.find(path => {\n    if (path.isProgram({ sourceType: \"module\" })) return true;\n\n    if (path.isClass()) return true;\n\n    if (\n      path.isArrowFunctionExpression() &&\n      !path.get(\"body\").isBlockStatement()\n    ) {\n      return false;\n    }\n\n    let body: t.BlockStatement | t.Program;\n    if (path.isFunction()) {\n      body = path.node.body as t.BlockStatement;\n    } else if (path.isProgram()) {\n      body = path.node;\n    } else {\n      return false;\n    }\n\n    for (const directive of body.directives) {\n      if (directive.value.value === \"use strict\") {\n        return true;\n      }\n    }\n  });\n\n  return !!strictParent;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAAA,EAAA,GAAAC,OAAA;AAUsB;EATpBC,uBAAuB;EACvBC,YAAY;EACZC,gBAAgB;EAChBC,YAAY;EACZC,YAAY;EACZC,SAAS;EACTC,eAAe;EACfC,MAAM;EACNC,cAAc,EAAIC;AAAe,IAAAX,EAAA;AAW5B,SAASU,cAAcA,CAE5BE,OAAe,EACfC,YAAsB,EACb;EACT,OAAOF,eAAe,CAAC,IAAI,CAACG,IAAI,EAAEF,OAAO,EAAEC,YAAY,CAAC;AAC1D;AAOO,SAASE,GAAGA,CAEjBC,GAAY,EACH;EACT,MAAMC,GAAG,GAAG,IAAI,CAACH,IAAI,IAAI,IAAI,CAACA,IAAI,CAACE,GAAG,CAAC;EACvC,IAAIC,GAAG,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;IAC7B,OAAO,CAAC,CAACA,GAAG,CAACG,MAAM;EACrB,CAAC,MAAM;IACL,OAAO,CAAC,CAACH,GAAG;EACd;AACF;AAMO,SAASI,QAAQA,CAAA,EAA0B;EAChD,OAAO,IAAI,CAACC,KAAK,CAACD,QAAQ,CAAC,IAAI,CAACP,IAAI,CAAC;AACvC;AAMO,MAAMS,EAAE,GAAGR,GAAG;AAACS,OAAA,CAAAD,EAAA,GAAAA,EAAA;AAMf,SAASE,IAAIA,CAElBT,GAAY,EACH;EACT,OAAO,CAAC,IAAI,CAACD,GAAG,CAACC,GAAG,CAAC;AACvB;AAMO,SAASU,MAAMA,CAEpBV,GAAY,EACZW,KAAU,EACD;EACT,OAAO,IAAI,CAACb,IAAI,CAACE,GAAG,CAAC,KAAKW,KAAK;AACjC;AAOO,SAASC,UAAUA,CAAiBC,IAAY,EAAW;EAChE,OAAOpB,MAAM,CAAC,IAAI,CAACoB,IAAI,EAAEA,IAAI,CAAC;AAChC;AAYO,SAASC,sCAAsCA,CAAA,EAAiB;EACrE,OACE,CAAC,IAAI,CAACd,GAAG,KAAK,MAAM,IAAI,IAAI,CAACA,GAAG,KAAK,MAAM,KAAK,IAAI,CAACe,UAAU,CAACC,KAAK,EAAE;AAE3E;AAUO,SAASC,oCAAoCA,CAElDC,WAAmB,EACV;EACT,IAAI,IAAI,CAAClB,GAAG,KAAK,MAAM,IAAI,CAAC,IAAI,CAACe,UAAU,CAACI,yBAAyB,EAAE,EAAE;IACvE,OAAO,KAAK;EACd;EAEA,IAAI,IAAI,CAAC9B,YAAY,EAAE,EAAE;IACvB,OAAOD,gBAAgB,CAAC8B,WAAW,CAAC;EACtC,CAAC,MAAM,IAAI,IAAI,CAAC9B,gBAAgB,EAAE,EAAE;IAClC,OAAOC,YAAY,CAAC6B,WAAW,CAAC;EAClC;EAEA,OAAO,KAAK;AACd;AAMO,SAASE,kBAAkBA,CAEhCC,mBAA6B,EACpB;EACT,IAAIC,IAAI,GAAG,IAAI;EACf,IAAIC,KAAK,GAAG,IAAI;EAEhB,GAAG;IACD,MAAM;MAAEV,IAAI;MAAEW;IAAU,CAAC,GAAGF,IAAI;IAGhC,IAAI,CAACC,KAAK,KAAKD,IAAI,CAACG,UAAU,EAAE,IAAIZ,IAAI,KAAK,aAAa,CAAC,EAAE;MAC3D,OAAO,CAAC,CAACQ,mBAAmB;IAC9B;IAEAE,KAAK,GAAG,KAAK;IAIb,IAAIrB,KAAK,CAACC,OAAO,CAACqB,SAAS,CAAC,IAAIF,IAAI,CAACtB,GAAG,KAAKwB,SAAS,CAACpB,MAAM,GAAG,CAAC,EAAE;MACjE,OAAO,KAAK;IACd;EACF,CAAC,QACC,CAACkB,IAAI,GAAGA,IAAI,CAACP,UAAU,KACvB,CAACO,IAAI,CAACI,SAAS,EAAE,IACjB,CAACJ,IAAI,CAACK,cAAc,EAAE;EAGxB,OAAO,IAAI;AACb;AAOO,SAASC,kBAAkBA,CAAA,EAA0B;EAC1D,IACE,IAAI,CAACb,UAAU,CAACc,kBAAkB,EAAE,IACpCzC,gBAAgB,CAAC,IAAI,CAACoC,SAAS,CAAC,EAChC;IACA,OAAO,KAAK;EACd,CAAC,MAAM;IACL,OAAOtC,uBAAuB,CAAC4C,QAAQ,CAAC,IAAI,CAAC9B,GAAG,CAAW;EAC7D;AACF;AAMO,SAAS+B,gBAAgBA,CAE9BC,YAAoB,EACpBC,UAAkB,EACT;EACT,IAAI,CAAC,IAAI,CAACC,sBAAsB,EAAE,EAAE;IAClC,IACG,IAAI,CAACC,qBAAqB,EAAE,IAC3B,IAAI,CAACrC,IAAI,CAACsC,QAAQ,CAACC,IAAI,KAAKJ,UAAU,IACvC,CAAC,IAAI,CAACK,kBAAkB,EAAE,IAAI,IAAI,CAACC,0BAA0B,EAAE,MAC7D,IAAI,CAACzC,IAAI,CAAC0C,QAAQ,GACfhD,eAAe,CAAC,IAAI,CAACM,IAAI,CAACsC,QAAQ,EAAE;MAAEzB,KAAK,EAAEsB;IAAW,CAAC,CAAC,GACzD,IAAI,CAACnC,IAAI,CAACsC,QAAQ,CAAkBC,IAAI,KAAKJ,UAAU,CAAE,EAChE;MACA,MAAMQ,MAAM,GACV,IAAI,CACJC,GAAG,CAAC,QAAQ,CAAC;MACf,OACED,MAAM,CAACP,sBAAsB,EAAE,IAC/BO,MAAM,CAACV,gBAAgB,CAACC,YAAY,EAAE,GAAG,CAAC;IAE9C;IAEA,OAAO,KAAK;EACd;EAEA,MAAMW,OAAO,GAAG,IAAI,CAACrC,KAAK,CAACsC,UAAU,CAAE,IAAI,CAAC9C,IAAI,CAAkBuC,IAAI,CAAC;EACvE,IAAI,CAACM,OAAO,IAAIA,OAAO,CAACE,IAAI,KAAK,QAAQ,EAAE,OAAO,KAAK;EAEvD,MAAMvB,IAAI,GAAGqB,OAAO,CAACrB,IAAI;EACzB,MAAMwB,MAAM,GAAGxB,IAAI,CAACP,UAAU;EAC9B,IAAI,CAAC+B,MAAM,CAACC,mBAAmB,EAAE,EAAE,OAAO,KAAK;EAG/C,IAAID,MAAM,CAAChD,IAAI,CAACkD,MAAM,CAACrC,KAAK,KAAKqB,YAAY,EAAE;IAC7C,IAAI,CAACC,UAAU,EAAE,OAAO,IAAI;EAC9B,CAAC,MAAM;IACL,OAAO,KAAK;EACd;EAEA,IAAIX,IAAI,CAAC2B,wBAAwB,EAAE,IAAIhB,UAAU,KAAK,SAAS,EAAE;IAC/D,OAAO,IAAI;EACb;EAEA,IAAIX,IAAI,CAAC4B,0BAA0B,EAAE,IAAIjB,UAAU,KAAK,GAAG,EAAE;IAC3D,OAAO,IAAI;EACb;EAEA,IACEX,IAAI,CAAC6B,iBAAiB,EAAE,IACxB7D,YAAY,CAACgC,IAAI,CAACxB,IAAI,CAACsD,QAAQ,EAAE;IAAEf,IAAI,EAAEJ;EAAW,CAAC,CAAC,EACtD;IACA,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd;AAMO,SAASoB,SAASA,CAAA,EAAyB;EAChD,MAAMvD,IAAI,GAAG,IAAI,CAACA,IAAI;EACtB,IAAIA,IAAI,CAACwD,GAAG,EAAE;IACZ,MAAMC,IAAI,GAAG,IAAI,CAACC,GAAG,CAACC,OAAO,EAAE;IAC/B,IAAIF,IAAI,EAAE,OAAOA,IAAI,CAACG,KAAK,CAAC5D,IAAI,CAAC6D,KAAK,EAAE7D,IAAI,CAACwD,GAAG,CAAC;EACnD;EACA,OAAO,EAAE;AACX;AAEO,SAASM,uBAAuBA,CAErCC,MAAgB,EACP;EACT,OAAO,IAAI,CAACC,+BAA+B,CAACD,MAAM,CAAC,KAAK,OAAO;AACjE;AAEA,SAASE,gBAAgBA,CAACzC,IAAc,EAAE;EACxC,OAAOA,IAAI,CAACI,SAAS,EAAE,GACnBJ,IAAI,GACJ,CACEA,IAAI,CAACP,UAAU,CAACT,KAAK,CAAC0D,iBAAiB,EAAE,IACzC1C,IAAI,CAACP,UAAU,CAACT,KAAK,CAAC2D,gBAAgB,EAAE,EACxC3C,IAAI;AACZ;AAEA,SAAS4C,oBAAoBA,CAACrD,IAAoB,EAAEb,GAAW,EAAE;EAC/D,QAAQa,IAAI;IAGV,KAAK,mBAAmB;MACtB,OAAOb,GAAG,KAAK,OAAO;IAIxB,KAAK,uBAAuB;IAC5B,KAAK,aAAa;MAChB,OAAOA,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,WAAW;IAGpD,KAAK,gBAAgB;IACrB,KAAK,kBAAkB;IACvB,KAAK,gBAAgB;IACrB,KAAK,gBAAgB;MACnB,OAAOA,GAAG,KAAK,MAAM;IAGvB,KAAK,cAAc;MACjB,OAAOA,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,QAAQ;IAG3C,KAAK,iBAAiB;MACpB,OAAOA,GAAG,KAAK,OAAO;IAGxB,KAAK,cAAc;MACjB,OAAOA,GAAG,KAAK,SAAS;IAG1B,KAAK,mBAAmB;MACtB,OAAOA,GAAG,KAAK,OAAO;IAGxB,KAAK,0BAA0B;MAC7B,OAAOA,GAAG,KAAK,UAAU;IAG3B,KAAK,wBAAwB;MAC3B,OAAOA,GAAG,KAAK,WAAW;IAE5B;MACE,OAAO,KAAK;EAAC;AAEnB;AAEA,SAASmE,0BAA0BA,CAACC,KAAiB,EAAEC,QAAgB,EAAE;EACvE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAEC,CAAC,EAAE,EAAE;IACjC,MAAMhD,IAAI,GAAG8C,KAAK,CAACE,CAAC,CAAC;IACrB,IAAIJ,oBAAoB,CAAC5C,IAAI,CAACwB,MAAM,CAACjC,IAAI,EAAES,IAAI,CAACiD,SAAS,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AAYA,MAAMC,eAAe,GAAGC,MAAM,EAAE;AAczB,SAASX,+BAA+BA,CAE7CD,MAAgB,EACS;EACzB,OAAOa,qCAAqC,CAAC,IAAI,EAAEb,MAAM,EAAE,IAAIc,GAAG,EAAE,CAAC;AACvE;AAEA,SAASD,qCAAqCA,CAC5CE,IAAc,EACdf,MAAgB,EAChBgB,KAA2B,EACF;EAEzB,MAAMC,UAAU,GAAG;IACjBC,IAAI,EAAEhB,gBAAgB,CAACa,IAAI,CAAC;IAC5Bf,MAAM,EAAEE,gBAAgB,CAACF,MAAM;EACjC,CAAC;EAID,IAAIiB,UAAU,CAACjB,MAAM,CAAC/D,IAAI,KAAKgF,UAAU,CAACC,IAAI,CAACjF,IAAI,EAAE;IACnD,OAAOkF,uDAAuD,CAC5DJ,IAAI,EACJE,UAAU,CAACjB,MAAM,EACjBgB,KAAK,CACN;EACH;EAEA,MAAMT,KAAK,GAAG;IACZP,MAAM,EAAEA,MAAM,CAACoB,WAAW,EAAE;IAC5BF,IAAI,EAAEH,IAAI,CAACK,WAAW;EACxB,CAAC;EAID,IAAIb,KAAK,CAACP,MAAM,CAACqB,OAAO,CAACN,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,OAAO;EACnD,IAAIR,KAAK,CAACW,IAAI,CAACG,OAAO,CAACrB,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,QAAQ;EAGpD,IAAIsB,UAAU;EACd,MAAMC,WAAW,GAAG;IAAEvB,MAAM,EAAE,CAAC;IAAEkB,IAAI,EAAE;EAAE,CAAC;EAE1C,OAAO,CAACI,UAAU,IAAIC,WAAW,CAACL,IAAI,GAAGX,KAAK,CAACW,IAAI,CAAC3E,MAAM,EAAE;IAC1D,MAAMkB,IAAI,GAAG8C,KAAK,CAACW,IAAI,CAACK,WAAW,CAACL,IAAI,CAAC;IACzCK,WAAW,CAACvB,MAAM,GAAGO,KAAK,CAACP,MAAM,CAACqB,OAAO,CAAC5D,IAAI,CAAC;IAC/C,IAAI8D,WAAW,CAACvB,MAAM,IAAI,CAAC,EAAE;MAC3BsB,UAAU,GAAG7D,IAAI;IACnB,CAAC,MAAM;MACL8D,WAAW,CAACL,IAAI,EAAE;IACpB;EACF;EAEA,IAAI,CAACI,UAAU,EAAE;IACf,MAAM,IAAIE,KAAK,CACb,+CAA+C,GAC7C,8CAA8C,CACjD;EACH;EAEA,IACElB,0BAA0B,CAACC,KAAK,CAACW,IAAI,EAAEK,WAAW,CAACL,IAAI,GAAG,CAAC,CAAC,IAC5DZ,0BAA0B,CAACC,KAAK,CAACP,MAAM,EAAEuB,WAAW,CAACvB,MAAM,GAAG,CAAC,CAAC,EAChE;IACA,OAAO,SAAS;EAClB;EAEA,MAAMyB,UAAU,GAAG;IACjBP,IAAI,EAAEX,KAAK,CAACW,IAAI,CAACK,WAAW,CAACL,IAAI,GAAG,CAAC,CAAC;IACtClB,MAAM,EAAEO,KAAK,CAACP,MAAM,CAACuB,WAAW,CAACvB,MAAM,GAAG,CAAC;EAC7C,CAAC;EAID,IACEyB,UAAU,CAACzB,MAAM,CAAC0B,OAAO,IACzBD,UAAU,CAACP,IAAI,CAACQ,OAAO,IACvBD,UAAU,CAACzB,MAAM,CAACrC,SAAS,KAAK8D,UAAU,CAACP,IAAI,CAACvD,SAAS,EACzD;IACA,OAAO8D,UAAU,CAACzB,MAAM,CAAC7D,GAAG,GAAGsF,UAAU,CAACP,IAAI,CAAC/E,GAAG,GAAG,QAAQ,GAAG,OAAO;EACzE;EAGA,MAAMwF,IAAI,GAAGrG,YAAY,CAACgG,UAAU,CAACtE,IAAI,CAAC;EAC1C,MAAM4E,WAAW,GAAG;IAClBV,IAAI,EAAES,IAAI,CAACN,OAAO,CAACI,UAAU,CAACP,IAAI,CAACR,SAAS,CAAC;IAC7CV,MAAM,EAAE2B,IAAI,CAACN,OAAO,CAACI,UAAU,CAACzB,MAAM,CAACU,SAAS;EAClD,CAAC;EACD,OAAOkB,WAAW,CAAC5B,MAAM,GAAG4B,WAAW,CAACV,IAAI,GAAG,QAAQ,GAAG,OAAO;AACnE;AAEA,SAASW,yDAAyDA,CAChEd,IAAc,EACdf,MAAgB,EAChBgB,KAA2B,EACF;EACzB,IAAI,CAAChB,MAAM,CAAC8B,qBAAqB,EAAE,EAAE;IACnC,IACEjB,qCAAqC,CAACE,IAAI,EAAEf,MAAM,EAAEgB,KAAK,CAAC,KAAK,QAAQ,EACvE;MACA,OAAO,QAAQ;IACjB;IACA,OAAO,SAAS;EAClB,CAAC,MAAM,IAAIhB,MAAM,CAAC9C,UAAU,CAAC6E,mBAAmB,EAAE,EAAE;IAClD,OAAO,SAAS;EAClB;EAMA,MAAMjD,OAAO,GAAGkB,MAAM,CAACvD,KAAK,CAACsC,UAAU,CAACiB,MAAM,CAAC/D,IAAI,CAAC+F,EAAE,CAACxD,IAAI,CAAC;EAG5D,IAAI,CAACM,OAAO,CAACmD,UAAU,EAAE,OAAO,QAAQ;EAExC,MAAMC,cAA+B,GAAGpD,OAAO,CAACoD,cAAc;EAE9D,IAAIC,SAAS;EAGb,KAAK,MAAM1E,IAAI,IAAIyE,cAAc,EAAE;IAGjC,MAAME,eAAe,GAAG,CAAC,CAAC3E,IAAI,CAAC4E,IAAI,CAAC5E,IAAI,IAAIA,IAAI,CAACxB,IAAI,KAAK+D,MAAM,CAAC/D,IAAI,CAAC;IACtE,IAAImG,eAAe,EAAE;IAErB,IAAI3E,IAAI,CAACtB,GAAG,KAAK,QAAQ,IAAI,CAACsB,IAAI,CAACP,UAAU,CAACoF,gBAAgB,EAAE,EAAE;MAGhE,OAAO,SAAS;IAClB;IAEA,MAAMC,MAAM,GAAG1B,qCAAqC,CAACE,IAAI,EAAEtD,IAAI,EAAEuD,KAAK,CAAC;IAEvE,IAAImB,SAAS,IAAIA,SAAS,KAAKI,MAAM,EAAE;MACrC,OAAO,SAAS;IAClB,CAAC,MAAM;MACLJ,SAAS,GAAGI,MAAM;IACpB;EACF;EAEA,OAAOJ,SAAS;AAClB;AAEA,SAAShB,uDAAuDA,CAC9DJ,IAAc,EACdf,MAAgB,EAChBgB,KAA2B,EACF;EACzB,IAAIwB,OAAO,GAAGxB,KAAK,CAACnC,GAAG,CAACkC,IAAI,CAAC9E,IAAI,CAAC;EAClC,IAAIwG,MAAM;EAEV,IAAI,CAACD,OAAO,EAAE;IACZxB,KAAK,CAAC0B,GAAG,CAAC3B,IAAI,CAAC9E,IAAI,EAAGuG,OAAO,GAAG,IAAI1B,GAAG,EAAE,CAAE;EAC7C,CAAC,MAAM,IAAK2B,MAAM,GAAGD,OAAO,CAAC3D,GAAG,CAACmB,MAAM,CAAC/D,IAAI,CAAC,EAAG;IAC9C,IAAIwG,MAAM,KAAK9B,eAAe,EAAE;MAC9B,OAAO,SAAS;IAClB;IACA,OAAO8B,MAAM;EACf;EAEAD,OAAO,CAACE,GAAG,CAAC1C,MAAM,CAAC/D,IAAI,EAAE0E,eAAe,CAAC;EAEzC,MAAMgC,MAAM,GAAGd,yDAAyD,CACtEd,IAAI,EACJf,MAAM,EACNgB,KAAK,CACN;EAEDwB,OAAO,CAACE,GAAG,CAAC1C,MAAM,CAAC/D,IAAI,EAAE0G,MAAM,CAAC;EAChC,OAAOA,MAAM;AACf;AAKO,SAASC,OAAOA,CAErBC,SAAmB,EACnBC,QAAqB,EACrB;EACA,OAAO,IAAI,CAACC,QAAQ,CAACF,SAAS,EAAEC,QAAQ,CAAC,IAAI,IAAI;AACnD;AAEO,SAASC,QAAQA,CAEtBF,SAAmB,EACnBC,QAAqB,EACQ;EAG7B,IAAIA,QAAQ,IAAIA,QAAQ,CAACzB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;EAG7CyB,QAAQ,GAAGA,QAAQ,IAAI,EAAE;EACzBA,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC;EAEnB,IAAI,IAAI,CAACC,oBAAoB,EAAE,EAAE;IAC/B,IAAI,IAAI,CAACpE,GAAG,CAAC,IAAI,CAAC,CAACpD,YAAY,EAAE,EAAE;MACjC,OAAO,IAAI,CAACoD,GAAG,CAAC,MAAM,CAAC,CAAC+D,OAAO,CAACC,SAAS,EAAEC,QAAQ,CAAC;IACtD,CAAC,MAAM,CAEP;EACF,CAAC,MAAM,IAAI,IAAI,CAACzE,sBAAsB,EAAE,EAAE;IACxC,MAAMS,OAAO,GAAG,IAAI,CAACrC,KAAK,CAACsC,UAAU,CAAC,IAAI,CAAC9C,IAAI,CAACuC,IAAI,CAAC;IACrD,IAAI,CAACM,OAAO,EAAE;IAGd,IAAI,CAACA,OAAO,CAACoE,QAAQ,EAAE;IAGvB,IAAIpE,OAAO,CAACE,IAAI,KAAK,QAAQ,EAAE;IAE/B,IAAIF,OAAO,CAACrB,IAAI,KAAK,IAAI,EAAE;MACzB,MAAM0F,GAAG,GAAGrE,OAAO,CAACrB,IAAI,CAACmF,OAAO,CAACC,SAAS,EAAEC,QAAQ,CAAC;MAErD,IAAI,IAAI,CAACT,IAAI,CAACpD,MAAM,IAAIA,MAAM,CAAChD,IAAI,KAAKkH,GAAG,CAAClH,IAAI,CAAC,EAAE;MACnD,OAAOkH,GAAG;IACZ;EACF,CAAC,MAAM,IAAI,IAAI,CAACC,oBAAoB,EAAE,EAAE;IAEtC,OAAO,IAAI,CAACvE,GAAG,CAAC,YAAY,CAAC,CAAC+D,OAAO,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAC5D,CAAC,MAAM,IAAID,SAAS,IAAI,IAAI,CAACpE,kBAAkB,EAAE,EAAE;IAIjD,MAAM4E,SAAS,GAAG,IAAI,CAACC,aAAa,EAAE;IACtC,IAAI,CAAC5H,SAAS,CAAC2H,SAAS,CAAC,EAAE;IAG3B,MAAME,UAAU,GAAGF,SAAS,CAACvG,KAAK;IAElC,MAAMkD,MAAM,GAAG,IAAI,CAACnB,GAAG,CAAC,QAAQ,CAAC,CAAC+D,OAAO,CAACC,SAAS,EAAEC,QAAQ,CAAC;IAE9D,IAAI9C,MAAM,CAACwD,kBAAkB,EAAE,EAAE;MAC/B,MAAMC,KAAK,GAAGzD,MAAM,CAACnB,GAAG,CAAC,YAAY,CAAC;MACtC,KAAK,MAAM6E,IAAI,IAAID,KAAK,EAAW;QACjC,IAAI,CAACC,IAAI,CAACC,UAAU,EAAE,EAAE;QAExB,MAAMxH,GAAG,GAAGuH,IAAI,CAAC7E,GAAG,CAAC,KAAK,CAAC;QAG3B,IAAI+E,KAAK,GACPF,IAAI,CAAC9G,IAAI,CAAC,UAAU,CAAC,IAAIT,GAAG,CAACV,YAAY,CAAC;UAAE+C,IAAI,EAAE+E;QAAW,CAAC,CAAC;QAGjEK,KAAK,GAAGA,KAAK,IAAIzH,GAAG,CAACT,SAAS,CAAC;UAAEoB,KAAK,EAAEyG;QAAW,CAAC,CAAC;QAErD,IAAIK,KAAK,EAAE,OAAOF,IAAI,CAAC7E,GAAG,CAAC,OAAO,CAAC,CAAC+D,OAAO,CAACC,SAAS,EAAEC,QAAQ,CAAC;MAClE;IACF,CAAC,MAAM,IAAI9C,MAAM,CAAC6D,iBAAiB,EAAE,IAAI,CAACC,KAAK,CAAC,CAACP,UAAU,CAAC,EAAE;MAC5D,MAAMQ,KAAK,GAAG/D,MAAM,CAACnB,GAAG,CAAC,UAAU,CAAC;MACpC,MAAMmF,IAAI,GAAGD,KAAK,CAACR,UAAU,CAAC;MAC9B,IAAIS,IAAI,EAAE,OAAOA,IAAI,CAACpB,OAAO,CAACC,SAAS,EAAEC,QAAQ,CAAC;IACpD;EACF;AACF;AAEO,SAASmB,oBAAoBA,CAAA,EAA0B;EAC5D,IAAI,IAAI,CAACxI,YAAY,EAAE,EAAE;IACvB,MAAMqD,OAAO,GAAG,IAAI,CAACrC,KAAK,CAACsC,UAAU,CAAC,IAAI,CAAC9C,IAAI,CAACuC,IAAI,CAAC;IACrD,IAAI,CAACM,OAAO,EAAE,OAAO,KAAK;IAC1B,OAAOA,OAAO,CAACoE,QAAQ;EACzB;EAEA,IAAI,IAAI,CAACxH,SAAS,EAAE,EAAE;IACpB,IAAI,IAAI,CAACwI,eAAe,EAAE,EAAE;MAC1B,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAACC,iBAAiB,EAAE,EAAE;MAC5B,OAAO,IAAI,CAACtF,GAAG,CAAC,aAAa,CAAC,CAACuF,KAAK,CAACC,UAAU,IAC7CA,UAAU,CAACJ,oBAAoB,EAAE,CAClC;IACH;IAEA,OAAO,IAAI;EACb;EAEA,IAAI,IAAI,CAACK,iBAAiB,EAAE,EAAE;IAC5B,IAAI,IAAI,CAACrI,IAAI,CAACsI,QAAQ,KAAK,MAAM,EAAE;MACjC,OAAO,KAAK;IACd;IAEA,OAAO,IAAI,CAAC1F,GAAG,CAAC,UAAU,CAAC,CAACoF,oBAAoB,EAAE;EACpD;EAEA,IAAI,IAAI,CAACO,kBAAkB,EAAE,EAAE;IAC7B,MAAM;MAAED;IAAS,CAAC,GAAG,IAAI,CAACtI,IAAI;IAC9B,OACEsI,QAAQ,KAAK,IAAI,IACjBA,QAAQ,KAAK,YAAY,IACzB,IAAI,CAAC1F,GAAG,CAAC,MAAM,CAAC,CAACoF,oBAAoB,EAAE,IACvC,IAAI,CAACpF,GAAG,CAAC,OAAO,CAAC,CAACoF,oBAAoB,EAAE;EAE5C;EAEA,OAAO,KAAK;AACd;AAEO,SAASQ,cAAcA,CAAA,EAAiB;EAC7C,MAAM3E,KAAK,GAAG,IAAI,CAACjC,SAAS,EAAE,GAAG,IAAI,GAAG,IAAI,CAACX,UAAU;EAEvD,MAAMwH,YAAY,GAAG5E,KAAK,CAACuC,IAAI,CAAC5E,IAAI,IAAI;IACtC,IAAIA,IAAI,CAACI,SAAS,CAAC;MAAE8G,UAAU,EAAE;IAAS,CAAC,CAAC,EAAE,OAAO,IAAI;IAEzD,IAAIlH,IAAI,CAACmH,OAAO,EAAE,EAAE,OAAO,IAAI;IAE/B,IACEnH,IAAI,CAACH,yBAAyB,EAAE,IAChC,CAACG,IAAI,CAACoB,GAAG,CAAC,MAAM,CAAC,CAACtD,gBAAgB,EAAE,EACpC;MACA,OAAO,KAAK;IACd;IAEA,IAAIsJ,IAAkC;IACtC,IAAIpH,IAAI,CAACG,UAAU,EAAE,EAAE;MACrBiH,IAAI,GAAGpH,IAAI,CAACxB,IAAI,CAAC4I,IAAwB;IAC3C,CAAC,MAAM,IAAIpH,IAAI,CAACI,SAAS,EAAE,EAAE;MAC3BgH,IAAI,GAAGpH,IAAI,CAACxB,IAAI;IAClB,CAAC,MAAM;MACL,OAAO,KAAK;IACd;IAEA,KAAK,MAAM6I,SAAS,IAAID,IAAI,CAACE,UAAU,EAAE;MACvC,IAAID,SAAS,CAAChI,KAAK,CAACA,KAAK,KAAK,YAAY,EAAE;QAC1C,OAAO,IAAI;MACb;IACF;EACF,CAAC,CAAC;EAEF,OAAO,CAAC,CAAC4H,YAAY;AACvB"}