{"version": 3, "names": ["createAddInitializerMethod", "initializers", "decoratorFinishedRef", "addInitializer", "initializer", "assertNotFinished", "assertCallable", "push", "assertInstanceIfPrivate", "has", "target", "TypeError", "memberDec", "dec", "name", "desc", "kind", "isStatic", "isPrivate", "value", "hasPrivateBrand", "kindStr", "ctx", "static", "private", "v", "get", "set", "t", "call", "bind", "access", "fnName", "Error", "fn", "hint", "assertValidReturnValue", "type", "undefined", "init", "curryThis1", "curryThis2", "applyMemberDec", "ret", "base", "decInfo", "decs", "Object", "getOwnPropertyDescriptor", "newValue", "i", "length", "newInit", "instance", "ownInitializers", "originalInitializer", "args", "defineProperty", "applyMemberDecs", "Class", "decInfos", "instanceBrand", "protoInitializers", "staticInitializers", "staticBrand", "existingProtoNonFields", "Map", "existingStaticNonFields", "Array", "isArray", "_", "checkInRHS", "prototype", "existingNonFields", "existingKind", "pushInitializers", "applyClassDecs", "targetClass", "classDecs", "newClass", "nextNewClass", "applyDecs2301", "memberDecs", "e", "c"], "sources": ["../../src/helpers/applyDecs2301.js"], "sourcesContent": ["/* @minVersion 7.21.0 */\n\nimport checkInRHS from \"checkInRHS\";\n\n/**\n  Enums are used in this file, but not assigned to vars to avoid non-hoistable values\n\n  CONSTRUCTOR = 0;\n  PUBLIC = 1;\n  PRIVATE = 2;\n\n  FIELD = 0;\n  ACCESSOR = 1;\n  METHOD = 2;\n  GETTER = 3;\n  SETTER = 4;\n\n  STATIC = 5;\n\n  CLASS = 10; // only used in assertValidReturnValue\n*/\n\nfunction createAddInitializerMethod(initializers, decoratorFinishedRef) {\n  return function addInitializer(initializer) {\n    assertNotFinished(decoratorFinishedRef, \"addInitializer\");\n    assertCallable(initializer, \"An initializer\");\n    initializers.push(initializer);\n  };\n}\n\nfunction assertInstanceIfPrivate(has, target) {\n  if (!has(target)) {\n    throw new TypeError(\"Attempted to access private element on non-instance\");\n  }\n}\n\nfunction memberDec(\n  dec,\n  name,\n  desc,\n  initializers,\n  kind,\n  isStatic,\n  isPrivate,\n  value,\n  hasPrivateBrand\n) {\n  var kindStr;\n\n  switch (kind) {\n    case 1 /* ACCESSOR */:\n      kindStr = \"accessor\";\n      break;\n    case 2 /* METHOD */:\n      kindStr = \"method\";\n      break;\n    case 3 /* GETTER */:\n      kindStr = \"getter\";\n      break;\n    case 4 /* SETTER */:\n      kindStr = \"setter\";\n      break;\n    default:\n      kindStr = \"field\";\n  }\n\n  var ctx = {\n    kind: kindStr,\n    name: isPrivate ? \"#\" + name : name,\n    static: isStatic,\n    private: isPrivate,\n  };\n\n  var decoratorFinishedRef = { v: false };\n\n  if (kind !== 0 /* FIELD */) {\n    ctx.addInitializer = createAddInitializerMethod(\n      initializers,\n      decoratorFinishedRef\n    );\n  }\n\n  var get, set;\n  if (!isPrivate && (kind === 0 /* FIELD */ || kind === 2) /* METHOD */) {\n    get = function (target) {\n      return target[name];\n    };\n    if (kind === 0 /* FIELD */) {\n      set = function (target, v) {\n        target[name] = v;\n      };\n    }\n  } else if (kind === 2 /* METHOD */) {\n    // Assert: isPrivate is true.\n    get = function (target) {\n      assertInstanceIfPrivate(hasPrivateBrand, target);\n      return desc.value;\n    };\n  } else {\n    // Assert: If kind === 0, then isPrivate is true.\n    var t = kind === 0 /* FIELD */ || kind === 1; /* ACCESSOR */\n    if (t || kind === 3 /* GETTER */) {\n      if (isPrivate) {\n        get = function (target) {\n          assertInstanceIfPrivate(hasPrivateBrand, target);\n          return desc.get.call(target);\n        };\n      } else {\n        get = function (target) {\n          return desc.get.call(target);\n        };\n      }\n    }\n    if (t || kind === 4 /* SETTER */) {\n      if (isPrivate) {\n        set = function (target, value) {\n          assertInstanceIfPrivate(hasPrivateBrand, target);\n          desc.set.call(target, value);\n        };\n      } else {\n        set = function (target, value) {\n          desc.set.call(target, value);\n        };\n      }\n    }\n  }\n  var has = isPrivate\n    ? hasPrivateBrand.bind()\n    : function (target) {\n        return name in target;\n      };\n  ctx.access =\n    get && set\n      ? { get: get, set: set, has: has }\n      : get\n      ? { get: get, has: has }\n      : { set: set, has: has };\n\n  try {\n    return dec(value, ctx);\n  } finally {\n    decoratorFinishedRef.v = true;\n  }\n}\n\nfunction assertNotFinished(decoratorFinishedRef, fnName) {\n  if (decoratorFinishedRef.v) {\n    throw new Error(\n      \"attempted to call \" + fnName + \" after decoration was finished\"\n    );\n  }\n}\n\nfunction assertCallable(fn, hint) {\n  if (typeof fn !== \"function\") {\n    throw new TypeError(hint + \" must be a function\");\n  }\n}\n\nfunction assertValidReturnValue(kind, value) {\n  var type = typeof value;\n\n  if (kind === 1 /* ACCESSOR */) {\n    if (type !== \"object\" || value === null) {\n      throw new TypeError(\n        \"accessor decorators must return an object with get, set, or init properties or void 0\"\n      );\n    }\n    if (value.get !== undefined) {\n      assertCallable(value.get, \"accessor.get\");\n    }\n    if (value.set !== undefined) {\n      assertCallable(value.set, \"accessor.set\");\n    }\n    if (value.init !== undefined) {\n      assertCallable(value.init, \"accessor.init\");\n    }\n  } else if (type !== \"function\") {\n    var hint;\n    if (kind === 0 /* FIELD */) {\n      hint = \"field\";\n    } else if (kind === 10 /* CLASS */) {\n      hint = \"class\";\n    } else {\n      hint = \"method\";\n    }\n    throw new TypeError(hint + \" decorators must return a function or void 0\");\n  }\n}\n\nfunction curryThis1(fn) {\n  return function () {\n    return fn(this);\n  };\n}\nfunction curryThis2(fn) {\n  return function (value) {\n    fn(this, value);\n  };\n}\n\nfunction applyMemberDec(\n  ret,\n  base,\n  decInfo,\n  name,\n  kind,\n  isStatic,\n  isPrivate,\n  initializers,\n  hasPrivateBrand\n) {\n  var decs = decInfo[0];\n\n  var desc, init, value;\n\n  if (isPrivate) {\n    if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n      desc = {\n        get: curryThis1(decInfo[3]),\n        set: curryThis2(decInfo[4]),\n      };\n    } else {\n      if (kind === 3 /* GETTER */) {\n        desc = {\n          get: decInfo[3],\n        };\n      } else if (kind === 4 /* SETTER */) {\n        desc = {\n          set: decInfo[3],\n        };\n      } else {\n        desc = {\n          value: decInfo[3],\n        };\n      }\n    }\n  } else if (kind !== 0 /* FIELD */) {\n    desc = Object.getOwnPropertyDescriptor(base, name);\n  }\n\n  if (kind === 1 /* ACCESSOR */) {\n    value = {\n      get: desc.get,\n      set: desc.set,\n    };\n  } else if (kind === 2 /* METHOD */) {\n    value = desc.value;\n  } else if (kind === 3 /* GETTER */) {\n    value = desc.get;\n  } else if (kind === 4 /* SETTER */) {\n    value = desc.set;\n  }\n\n  var newValue, get, set;\n\n  if (typeof decs === \"function\") {\n    newValue = memberDec(\n      decs,\n      name,\n      desc,\n      initializers,\n      kind,\n      isStatic,\n      isPrivate,\n      value,\n      hasPrivateBrand\n    );\n\n    if (newValue !== void 0) {\n      assertValidReturnValue(kind, newValue);\n\n      if (kind === 0 /* FIELD */) {\n        init = newValue;\n      } else if (kind === 1 /* ACCESSOR */) {\n        init = newValue.init;\n        get = newValue.get || value.get;\n        set = newValue.set || value.set;\n\n        value = { get: get, set: set };\n      } else {\n        value = newValue;\n      }\n    }\n  } else {\n    for (var i = decs.length - 1; i >= 0; i--) {\n      var dec = decs[i];\n\n      newValue = memberDec(\n        dec,\n        name,\n        desc,\n        initializers,\n        kind,\n        isStatic,\n        isPrivate,\n        value,\n        hasPrivateBrand\n      );\n\n      if (newValue !== void 0) {\n        assertValidReturnValue(kind, newValue);\n        var newInit;\n\n        if (kind === 0 /* FIELD */) {\n          newInit = newValue;\n        } else if (kind === 1 /* ACCESSOR */) {\n          newInit = newValue.init;\n          get = newValue.get || value.get;\n          set = newValue.set || value.set;\n\n          value = { get: get, set: set };\n        } else {\n          value = newValue;\n        }\n\n        if (newInit !== void 0) {\n          if (init === void 0) {\n            init = newInit;\n          } else if (typeof init === \"function\") {\n            init = [init, newInit];\n          } else {\n            init.push(newInit);\n          }\n        }\n      }\n    }\n  }\n\n  if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n    if (init === void 0) {\n      // If the initializer was void 0, sub in a dummy initializer\n      init = function (instance, init) {\n        return init;\n      };\n    } else if (typeof init !== \"function\") {\n      var ownInitializers = init;\n\n      init = function (instance, init) {\n        var value = init;\n\n        for (var i = 0; i < ownInitializers.length; i++) {\n          value = ownInitializers[i].call(instance, value);\n        }\n\n        return value;\n      };\n    } else {\n      var originalInitializer = init;\n\n      init = function (instance, init) {\n        return originalInitializer.call(instance, init);\n      };\n    }\n\n    ret.push(init);\n  }\n\n  if (kind !== 0 /* FIELD */) {\n    if (kind === 1 /* ACCESSOR */) {\n      desc.get = value.get;\n      desc.set = value.set;\n    } else if (kind === 2 /* METHOD */) {\n      desc.value = value;\n    } else if (kind === 3 /* GETTER */) {\n      desc.get = value;\n    } else if (kind === 4 /* SETTER */) {\n      desc.set = value;\n    }\n\n    if (isPrivate) {\n      if (kind === 1 /* ACCESSOR */) {\n        ret.push(function (instance, args) {\n          return value.get.call(instance, args);\n        });\n        ret.push(function (instance, args) {\n          return value.set.call(instance, args);\n        });\n      } else if (kind === 2 /* METHOD */) {\n        ret.push(value);\n      } else {\n        ret.push(function (instance, args) {\n          return value.call(instance, args);\n        });\n      }\n    } else {\n      Object.defineProperty(base, name, desc);\n    }\n  }\n}\n\nfunction applyMemberDecs(Class, decInfos, instanceBrand) {\n  var ret = [];\n  var protoInitializers;\n  var staticInitializers;\n  var staticBrand;\n\n  var existingProtoNonFields = new Map();\n  var existingStaticNonFields = new Map();\n\n  for (var i = 0; i < decInfos.length; i++) {\n    var decInfo = decInfos[i];\n\n    // skip computed property names\n    if (!Array.isArray(decInfo)) continue;\n\n    var kind = decInfo[1];\n    var name = decInfo[2];\n    var isPrivate = decInfo.length > 3;\n\n    var isStatic = kind >= 5; /* STATIC */\n    var base;\n    var initializers;\n    var hasPrivateBrand = instanceBrand;\n\n    if (isStatic) {\n      base = Class;\n      kind = kind - 5 /* STATIC */;\n      // initialize staticInitializers when we see a non-field static member\n      if (kind !== 0 /* FIELD */) {\n        staticInitializers = staticInitializers || [];\n        initializers = staticInitializers;\n      }\n      if (isPrivate && !staticBrand) {\n        staticBrand = function (_) {\n          return checkInRHS(_) === Class;\n        };\n      }\n      hasPrivateBrand = staticBrand;\n    } else {\n      base = Class.prototype;\n      // initialize protoInitializers when we see a non-field member\n      if (kind !== 0 /* FIELD */) {\n        protoInitializers = protoInitializers || [];\n        initializers = protoInitializers;\n      }\n    }\n\n    if (kind !== 0 /* FIELD */ && !isPrivate) {\n      var existingNonFields = isStatic\n        ? existingStaticNonFields\n        : existingProtoNonFields;\n\n      var existingKind = existingNonFields.get(name) || 0;\n\n      if (\n        existingKind === true ||\n        (existingKind === 3 /* GETTER */ && kind !== 4) /* SETTER */ ||\n        (existingKind === 4 /* SETTER */ && kind !== 3) /* GETTER */\n      ) {\n        throw new Error(\n          \"Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: \" +\n            name\n        );\n      } else if (!existingKind && kind > 2 /* METHOD */) {\n        existingNonFields.set(name, kind);\n      } else {\n        existingNonFields.set(name, true);\n      }\n    }\n\n    applyMemberDec(\n      ret,\n      base,\n      decInfo,\n      name,\n      kind,\n      isStatic,\n      isPrivate,\n      initializers,\n      hasPrivateBrand\n    );\n  }\n\n  pushInitializers(ret, protoInitializers);\n  pushInitializers(ret, staticInitializers);\n  return ret;\n}\n\nfunction pushInitializers(ret, initializers) {\n  if (initializers) {\n    ret.push(function (instance) {\n      for (var i = 0; i < initializers.length; i++) {\n        initializers[i].call(instance);\n      }\n      return instance;\n    });\n  }\n}\n\nfunction applyClassDecs(targetClass, classDecs) {\n  if (classDecs.length > 0) {\n    var initializers = [];\n    var newClass = targetClass;\n    var name = targetClass.name;\n\n    for (var i = classDecs.length - 1; i >= 0; i--) {\n      var decoratorFinishedRef = { v: false };\n\n      try {\n        var nextNewClass = classDecs[i](newClass, {\n          kind: \"class\",\n          name: name,\n          addInitializer: createAddInitializerMethod(\n            initializers,\n            decoratorFinishedRef\n          ),\n        });\n      } finally {\n        decoratorFinishedRef.v = true;\n      }\n\n      if (nextNewClass !== undefined) {\n        assertValidReturnValue(10 /* CLASS */, nextNewClass);\n        newClass = nextNewClass;\n      }\n    }\n\n    return [\n      newClass,\n      function () {\n        for (var i = 0; i < initializers.length; i++) {\n          initializers[i].call(newClass);\n        }\n      },\n    ];\n  }\n  // The transformer will not emit assignment when there are no class decorators,\n  // so we don't have to return an empty array here.\n}\n\n/**\n  Basic usage:\n\n  applyDecs(\n    Class,\n    [\n      // member decorators\n      [\n        dec,                // dec or array of decs\n        0,                  // kind of value being decorated\n        'prop',             // name of public prop on class containing the value being decorated,\n        '#p',               // the name of the private property (if is private, void 0 otherwise),\n      ]\n    ],\n    [\n      // class decorators\n      dec1, dec2\n    ]\n  )\n  ```\n\n  Fully transpiled example:\n\n  ```js\n  @dec\n  class Class {\n    @dec\n    a = 123;\n\n    @dec\n    #a = 123;\n\n    @dec\n    @dec2\n    accessor b = 123;\n\n    @dec\n    accessor #b = 123;\n\n    @dec\n    c() { console.log('c'); }\n\n    @dec\n    #c() { console.log('privC'); }\n\n    @dec\n    get d() { console.log('d'); }\n\n    @dec\n    get #d() { console.log('privD'); }\n\n    @dec\n    set e(v) { console.log('e'); }\n\n    @dec\n    set #e(v) { console.log('privE'); }\n  }\n\n\n  // becomes\n  let initializeInstance;\n  let initializeClass;\n\n  let initA;\n  let initPrivA;\n\n  let initB;\n  let initPrivB, getPrivB, setPrivB;\n\n  let privC;\n  let privD;\n  let privE;\n\n  let Class;\n  class _Class {\n    static {\n      let ret = applyDecs(\n        this,\n        [\n          [dec, 0, 'a'],\n          [dec, 0, 'a', (i) => i.#a, (i, v) => i.#a = v],\n          [[dec, dec2], 1, 'b'],\n          [dec, 1, 'b', (i) => i.#privBData, (i, v) => i.#privBData = v],\n          [dec, 2, 'c'],\n          [dec, 2, 'c', () => console.log('privC')],\n          [dec, 3, 'd'],\n          [dec, 3, 'd', () => console.log('privD')],\n          [dec, 4, 'e'],\n          [dec, 4, 'e', () => console.log('privE')],\n        ],\n        [\n          dec\n        ]\n      )\n\n      initA = ret[0];\n\n      initPrivA = ret[1];\n\n      initB = ret[2];\n\n      initPrivB = ret[3];\n      getPrivB = ret[4];\n      setPrivB = ret[5];\n\n      privC = ret[6];\n\n      privD = ret[7];\n\n      privE = ret[8];\n\n      initializeInstance = ret[9];\n\n      Class = ret[10]\n\n      initializeClass = ret[11];\n    }\n\n    a = (initializeInstance(this), initA(this, 123));\n\n    #a = initPrivA(this, 123);\n\n    #bData = initB(this, 123);\n    get b() { return this.#bData }\n    set b(v) { this.#bData = v }\n\n    #privBData = initPrivB(this, 123);\n    get #b() { return getPrivB(this); }\n    set #b(v) { setPrivB(this, v); }\n\n    c() { console.log('c'); }\n\n    #c(...args) { return privC(this, ...args) }\n\n    get d() { console.log('d'); }\n\n    get #d() { return privD(this); }\n\n    set e(v) { console.log('e'); }\n\n    set #e(v) { privE(this, v); }\n  }\n\n  initializeClass(Class);\n */\n\nexport default function applyDecs2301(\n  targetClass,\n  memberDecs,\n  classDecs,\n  instanceBrand\n) {\n  return {\n    e: applyMemberDecs(targetClass, memberDecs, instanceBrand),\n    // Lazily apply class decorations so that member init locals can be properly bound.\n    get c() {\n      return applyClassDecs(targetClass, classDecs);\n    },\n  };\n}\n"], "mappings": ";;;;;;AAEA;AAoBA,SAASA,0BAA0B,CAACC,YAAY,EAAEC,oBAAoB,EAAE;EACtE,OAAO,SAASC,cAAc,CAACC,WAAW,EAAE;IAC1CC,iBAAiB,CAACH,oBAAoB,EAAE,gBAAgB,CAAC;IACzDI,cAAc,CAACF,WAAW,EAAE,gBAAgB,CAAC;IAC7CH,YAAY,CAACM,IAAI,CAACH,WAAW,CAAC;EAChC,CAAC;AACH;AAEA,SAASI,uBAAuB,CAACC,GAAG,EAAEC,MAAM,EAAE;EAC5C,IAAI,CAACD,GAAG,CAACC,MAAM,CAAC,EAAE;IAChB,MAAM,IAAIC,SAAS,CAAC,qDAAqD,CAAC;EAC5E;AACF;AAEA,SAASC,SAAS,CAChBC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJd,YAAY,EACZe,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,eAAe,EACf;EACA,IAAIC,OAAO;EAEX,QAAQL,IAAI;IACV,KAAK,CAAC;MACJK,OAAO,GAAG,UAAU;MACpB;IACF,KAAK,CAAC;MACJA,OAAO,GAAG,QAAQ;MAClB;IACF,KAAK,CAAC;MACJA,OAAO,GAAG,QAAQ;MAClB;IACF,KAAK,CAAC;MACJA,OAAO,GAAG,QAAQ;MAClB;IACF;MACEA,OAAO,GAAG,OAAO;EAAC;EAGtB,IAAIC,GAAG,GAAG;IACRN,IAAI,EAAEK,OAAO;IACbP,IAAI,EAAEI,SAAS,GAAG,GAAG,GAAGJ,IAAI,GAAGA,IAAI;IACnCS,MAAM,EAAEN,QAAQ;IAChBO,OAAO,EAAEN;EACX,CAAC;EAED,IAAIhB,oBAAoB,GAAG;IAAEuB,CAAC,EAAE;EAAM,CAAC;EAEvC,IAAIT,IAAI,KAAK,CAAC,EAAc;IAC1BM,GAAG,CAACnB,cAAc,GAAGH,0BAA0B,CAC7CC,YAAY,EACZC,oBAAoB,CACrB;EACH;EAEA,IAAIwB,GAAG,EAAEC,GAAG;EACZ,IAAI,CAACT,SAAS,KAAKF,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,CAAC,EAAe;IACrEU,GAAG,GAAG,UAAUhB,MAAM,EAAE;MACtB,OAAOA,MAAM,CAACI,IAAI,CAAC;IACrB,CAAC;IACD,IAAIE,IAAI,KAAK,CAAC,EAAc;MAC1BW,GAAG,GAAG,UAAUjB,MAAM,EAAEe,CAAC,EAAE;QACzBf,MAAM,CAACI,IAAI,CAAC,GAAGW,CAAC;MAClB,CAAC;IACH;EACF,CAAC,MAAM,IAAIT,IAAI,KAAK,CAAC,EAAe;IAElCU,GAAG,GAAG,UAAUhB,MAAM,EAAE;MACtBF,uBAAuB,CAACY,eAAe,EAAEV,MAAM,CAAC;MAChD,OAAOK,IAAI,CAACI,KAAK;IACnB,CAAC;EACH,CAAC,MAAM;IAEL,IAAIS,CAAC,GAAGZ,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC;IAC5C,IAAIY,CAAC,IAAIZ,IAAI,KAAK,CAAC,EAAe;MAChC,IAAIE,SAAS,EAAE;QACbQ,GAAG,GAAG,UAAUhB,MAAM,EAAE;UACtBF,uBAAuB,CAACY,eAAe,EAAEV,MAAM,CAAC;UAChD,OAAOK,IAAI,CAACW,GAAG,CAACG,IAAI,CAACnB,MAAM,CAAC;QAC9B,CAAC;MACH,CAAC,MAAM;QACLgB,GAAG,GAAG,UAAUhB,MAAM,EAAE;UACtB,OAAOK,IAAI,CAACW,GAAG,CAACG,IAAI,CAACnB,MAAM,CAAC;QAC9B,CAAC;MACH;IACF;IACA,IAAIkB,CAAC,IAAIZ,IAAI,KAAK,CAAC,EAAe;MAChC,IAAIE,SAAS,EAAE;QACbS,GAAG,GAAG,UAAUjB,MAAM,EAAES,KAAK,EAAE;UAC7BX,uBAAuB,CAACY,eAAe,EAAEV,MAAM,CAAC;UAChDK,IAAI,CAACY,GAAG,CAACE,IAAI,CAACnB,MAAM,EAAES,KAAK,CAAC;QAC9B,CAAC;MACH,CAAC,MAAM;QACLQ,GAAG,GAAG,UAAUjB,MAAM,EAAES,KAAK,EAAE;UAC7BJ,IAAI,CAACY,GAAG,CAACE,IAAI,CAACnB,MAAM,EAAES,KAAK,CAAC;QAC9B,CAAC;MACH;IACF;EACF;EACA,IAAIV,GAAG,GAAGS,SAAS,GACfE,eAAe,CAACU,IAAI,EAAE,GACtB,UAAUpB,MAAM,EAAE;IAChB,OAAOI,IAAI,IAAIJ,MAAM;EACvB,CAAC;EACLY,GAAG,CAACS,MAAM,GACRL,GAAG,IAAIC,GAAG,GACN;IAAED,GAAG,EAAEA,GAAG;IAAEC,GAAG,EAAEA,GAAG;IAAElB,GAAG,EAAEA;EAAI,CAAC,GAChCiB,GAAG,GACH;IAAEA,GAAG,EAAEA,GAAG;IAAEjB,GAAG,EAAEA;EAAI,CAAC,GACtB;IAAEkB,GAAG,EAAEA,GAAG;IAAElB,GAAG,EAAEA;EAAI,CAAC;EAE5B,IAAI;IACF,OAAOI,GAAG,CAACM,KAAK,EAAEG,GAAG,CAAC;EACxB,CAAC,SAAS;IACRpB,oBAAoB,CAACuB,CAAC,GAAG,IAAI;EAC/B;AACF;AAEA,SAASpB,iBAAiB,CAACH,oBAAoB,EAAE8B,MAAM,EAAE;EACvD,IAAI9B,oBAAoB,CAACuB,CAAC,EAAE;IAC1B,MAAM,IAAIQ,KAAK,CACb,oBAAoB,GAAGD,MAAM,GAAG,gCAAgC,CACjE;EACH;AACF;AAEA,SAAS1B,cAAc,CAAC4B,EAAE,EAAEC,IAAI,EAAE;EAChC,IAAI,OAAOD,EAAE,KAAK,UAAU,EAAE;IAC5B,MAAM,IAAIvB,SAAS,CAACwB,IAAI,GAAG,qBAAqB,CAAC;EACnD;AACF;AAEA,SAASC,sBAAsB,CAACpB,IAAI,EAAEG,KAAK,EAAE;EAC3C,IAAIkB,IAAI,GAAG,OAAOlB,KAAK;EAEvB,IAAIH,IAAI,KAAK,CAAC,EAAiB;IAC7B,IAAIqB,IAAI,KAAK,QAAQ,IAAIlB,KAAK,KAAK,IAAI,EAAE;MACvC,MAAM,IAAIR,SAAS,CACjB,uFAAuF,CACxF;IACH;IACA,IAAIQ,KAAK,CAACO,GAAG,KAAKY,SAAS,EAAE;MAC3BhC,cAAc,CAACa,KAAK,CAACO,GAAG,EAAE,cAAc,CAAC;IAC3C;IACA,IAAIP,KAAK,CAACQ,GAAG,KAAKW,SAAS,EAAE;MAC3BhC,cAAc,CAACa,KAAK,CAACQ,GAAG,EAAE,cAAc,CAAC;IAC3C;IACA,IAAIR,KAAK,CAACoB,IAAI,KAAKD,SAAS,EAAE;MAC5BhC,cAAc,CAACa,KAAK,CAACoB,IAAI,EAAE,eAAe,CAAC;IAC7C;EACF,CAAC,MAAM,IAAIF,IAAI,KAAK,UAAU,EAAE;IAC9B,IAAIF,IAAI;IACR,IAAInB,IAAI,KAAK,CAAC,EAAc;MAC1BmB,IAAI,GAAG,OAAO;IAChB,CAAC,MAAM,IAAInB,IAAI,KAAK,EAAE,EAAc;MAClCmB,IAAI,GAAG,OAAO;IAChB,CAAC,MAAM;MACLA,IAAI,GAAG,QAAQ;IACjB;IACA,MAAM,IAAIxB,SAAS,CAACwB,IAAI,GAAG,8CAA8C,CAAC;EAC5E;AACF;AAEA,SAASK,UAAU,CAACN,EAAE,EAAE;EACtB,OAAO,YAAY;IACjB,OAAOA,EAAE,CAAC,IAAI,CAAC;EACjB,CAAC;AACH;AACA,SAASO,UAAU,CAACP,EAAE,EAAE;EACtB,OAAO,UAAUf,KAAK,EAAE;IACtBe,EAAE,CAAC,IAAI,EAAEf,KAAK,CAAC;EACjB,CAAC;AACH;AAEA,SAASuB,cAAc,CACrBC,GAAG,EACHC,IAAI,EACJC,OAAO,EACP/B,IAAI,EACJE,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTjB,YAAY,EACZmB,eAAe,EACf;EACA,IAAI0B,IAAI,GAAGD,OAAO,CAAC,CAAC,CAAC;EAErB,IAAI9B,IAAI,EAAEwB,IAAI,EAAEpB,KAAK;EAErB,IAAID,SAAS,EAAE;IACb,IAAIF,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;MACvDD,IAAI,GAAG;QACLW,GAAG,EAAEc,UAAU,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC;QAC3BlB,GAAG,EAAEc,UAAU,CAACI,OAAO,CAAC,CAAC,CAAC;MAC5B,CAAC;IACH,CAAC,MAAM;MACL,IAAI7B,IAAI,KAAK,CAAC,EAAe;QAC3BD,IAAI,GAAG;UACLW,GAAG,EAAEmB,OAAO,CAAC,CAAC;QAChB,CAAC;MACH,CAAC,MAAM,IAAI7B,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,GAAG;UACLY,GAAG,EAAEkB,OAAO,CAAC,CAAC;QAChB,CAAC;MACH,CAAC,MAAM;QACL9B,IAAI,GAAG;UACLI,KAAK,EAAE0B,OAAO,CAAC,CAAC;QAClB,CAAC;MACH;IACF;EACF,CAAC,MAAM,IAAI7B,IAAI,KAAK,CAAC,EAAc;IACjCD,IAAI,GAAGgC,MAAM,CAACC,wBAAwB,CAACJ,IAAI,EAAE9B,IAAI,CAAC;EACpD;EAEA,IAAIE,IAAI,KAAK,CAAC,EAAiB;IAC7BG,KAAK,GAAG;MACNO,GAAG,EAAEX,IAAI,CAACW,GAAG;MACbC,GAAG,EAAEZ,IAAI,CAACY;IACZ,CAAC;EACH,CAAC,MAAM,IAAIX,IAAI,KAAK,CAAC,EAAe;IAClCG,KAAK,GAAGJ,IAAI,CAACI,KAAK;EACpB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;IAClCG,KAAK,GAAGJ,IAAI,CAACW,GAAG;EAClB,CAAC,MAAM,IAAIV,IAAI,KAAK,CAAC,EAAe;IAClCG,KAAK,GAAGJ,IAAI,CAACY,GAAG;EAClB;EAEA,IAAIsB,QAAQ,EAAEvB,GAAG,EAAEC,GAAG;EAEtB,IAAI,OAAOmB,IAAI,KAAK,UAAU,EAAE;IAC9BG,QAAQ,GAAGrC,SAAS,CAClBkC,IAAI,EACJhC,IAAI,EACJC,IAAI,EACJd,YAAY,EACZe,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,eAAe,CAChB;IAED,IAAI6B,QAAQ,KAAK,KAAK,CAAC,EAAE;MACvBb,sBAAsB,CAACpB,IAAI,EAAEiC,QAAQ,CAAC;MAEtC,IAAIjC,IAAI,KAAK,CAAC,EAAc;QAC1BuB,IAAI,GAAGU,QAAQ;MACjB,CAAC,MAAM,IAAIjC,IAAI,KAAK,CAAC,EAAiB;QACpCuB,IAAI,GAAGU,QAAQ,CAACV,IAAI;QACpBb,GAAG,GAAGuB,QAAQ,CAACvB,GAAG,IAAIP,KAAK,CAACO,GAAG;QAC/BC,GAAG,GAAGsB,QAAQ,CAACtB,GAAG,IAAIR,KAAK,CAACQ,GAAG;QAE/BR,KAAK,GAAG;UAAEO,GAAG,EAAEA,GAAG;UAAEC,GAAG,EAAEA;QAAI,CAAC;MAChC,CAAC,MAAM;QACLR,KAAK,GAAG8B,QAAQ;MAClB;IACF;EACF,CAAC,MAAM;IACL,KAAK,IAAIC,CAAC,GAAGJ,IAAI,CAACK,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzC,IAAIrC,GAAG,GAAGiC,IAAI,CAACI,CAAC,CAAC;MAEjBD,QAAQ,GAAGrC,SAAS,CAClBC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJd,YAAY,EACZe,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,eAAe,CAChB;MAED,IAAI6B,QAAQ,KAAK,KAAK,CAAC,EAAE;QACvBb,sBAAsB,CAACpB,IAAI,EAAEiC,QAAQ,CAAC;QACtC,IAAIG,OAAO;QAEX,IAAIpC,IAAI,KAAK,CAAC,EAAc;UAC1BoC,OAAO,GAAGH,QAAQ;QACpB,CAAC,MAAM,IAAIjC,IAAI,KAAK,CAAC,EAAiB;UACpCoC,OAAO,GAAGH,QAAQ,CAACV,IAAI;UACvBb,GAAG,GAAGuB,QAAQ,CAACvB,GAAG,IAAIP,KAAK,CAACO,GAAG;UAC/BC,GAAG,GAAGsB,QAAQ,CAACtB,GAAG,IAAIR,KAAK,CAACQ,GAAG;UAE/BR,KAAK,GAAG;YAAEO,GAAG,EAAEA,GAAG;YAAEC,GAAG,EAAEA;UAAI,CAAC;QAChC,CAAC,MAAM;UACLR,KAAK,GAAG8B,QAAQ;QAClB;QAEA,IAAIG,OAAO,KAAK,KAAK,CAAC,EAAE;UACtB,IAAIb,IAAI,KAAK,KAAK,CAAC,EAAE;YACnBA,IAAI,GAAGa,OAAO;UAChB,CAAC,MAAM,IAAI,OAAOb,IAAI,KAAK,UAAU,EAAE;YACrCA,IAAI,GAAG,CAACA,IAAI,EAAEa,OAAO,CAAC;UACxB,CAAC,MAAM;YACLb,IAAI,CAAChC,IAAI,CAAC6C,OAAO,CAAC;UACpB;QACF;MACF;IACF;EACF;EAEA,IAAIpC,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;IACvD,IAAIuB,IAAI,KAAK,KAAK,CAAC,EAAE;MAEnBA,IAAI,GAAG,UAAUc,QAAQ,EAAEd,IAAI,EAAE;QAC/B,OAAOA,IAAI;MACb,CAAC;IACH,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;MACrC,IAAIe,eAAe,GAAGf,IAAI;MAE1BA,IAAI,GAAG,UAAUc,QAAQ,EAAEd,IAAI,EAAE;QAC/B,IAAIpB,KAAK,GAAGoB,IAAI;QAEhB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,eAAe,CAACH,MAAM,EAAED,CAAC,EAAE,EAAE;UAC/C/B,KAAK,GAAGmC,eAAe,CAACJ,CAAC,CAAC,CAACrB,IAAI,CAACwB,QAAQ,EAAElC,KAAK,CAAC;QAClD;QAEA,OAAOA,KAAK;MACd,CAAC;IACH,CAAC,MAAM;MACL,IAAIoC,mBAAmB,GAAGhB,IAAI;MAE9BA,IAAI,GAAG,UAAUc,QAAQ,EAAEd,IAAI,EAAE;QAC/B,OAAOgB,mBAAmB,CAAC1B,IAAI,CAACwB,QAAQ,EAAEd,IAAI,CAAC;MACjD,CAAC;IACH;IAEAI,GAAG,CAACpC,IAAI,CAACgC,IAAI,CAAC;EAChB;EAEA,IAAIvB,IAAI,KAAK,CAAC,EAAc;IAC1B,IAAIA,IAAI,KAAK,CAAC,EAAiB;MAC7BD,IAAI,CAACW,GAAG,GAAGP,KAAK,CAACO,GAAG;MACpBX,IAAI,CAACY,GAAG,GAAGR,KAAK,CAACQ,GAAG;IACtB,CAAC,MAAM,IAAIX,IAAI,KAAK,CAAC,EAAe;MAClCD,IAAI,CAACI,KAAK,GAAGA,KAAK;IACpB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;MAClCD,IAAI,CAACW,GAAG,GAAGP,KAAK;IAClB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;MAClCD,IAAI,CAACY,GAAG,GAAGR,KAAK;IAClB;IAEA,IAAID,SAAS,EAAE;MACb,IAAIF,IAAI,KAAK,CAAC,EAAiB;QAC7B2B,GAAG,CAACpC,IAAI,CAAC,UAAU8C,QAAQ,EAAEG,IAAI,EAAE;UACjC,OAAOrC,KAAK,CAACO,GAAG,CAACG,IAAI,CAACwB,QAAQ,EAAEG,IAAI,CAAC;QACvC,CAAC,CAAC;QACFb,GAAG,CAACpC,IAAI,CAAC,UAAU8C,QAAQ,EAAEG,IAAI,EAAE;UACjC,OAAOrC,KAAK,CAACQ,GAAG,CAACE,IAAI,CAACwB,QAAQ,EAAEG,IAAI,CAAC;QACvC,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIxC,IAAI,KAAK,CAAC,EAAe;QAClC2B,GAAG,CAACpC,IAAI,CAACY,KAAK,CAAC;MACjB,CAAC,MAAM;QACLwB,GAAG,CAACpC,IAAI,CAAC,UAAU8C,QAAQ,EAAEG,IAAI,EAAE;UACjC,OAAOrC,KAAK,CAACU,IAAI,CAACwB,QAAQ,EAAEG,IAAI,CAAC;QACnC,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLT,MAAM,CAACU,cAAc,CAACb,IAAI,EAAE9B,IAAI,EAAEC,IAAI,CAAC;IACzC;EACF;AACF;AAEA,SAAS2C,eAAe,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAE;EACvD,IAAIlB,GAAG,GAAG,EAAE;EACZ,IAAImB,iBAAiB;EACrB,IAAIC,kBAAkB;EACtB,IAAIC,WAAW;EAEf,IAAIC,sBAAsB,GAAG,IAAIC,GAAG,EAAE;EACtC,IAAIC,uBAAuB,GAAG,IAAID,GAAG,EAAE;EAEvC,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,QAAQ,CAACT,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,IAAIL,OAAO,GAAGe,QAAQ,CAACV,CAAC,CAAC;IAGzB,IAAI,CAACkB,KAAK,CAACC,OAAO,CAACxB,OAAO,CAAC,EAAE;IAE7B,IAAI7B,IAAI,GAAG6B,OAAO,CAAC,CAAC,CAAC;IACrB,IAAI/B,IAAI,GAAG+B,OAAO,CAAC,CAAC,CAAC;IACrB,IAAI3B,SAAS,GAAG2B,OAAO,CAACM,MAAM,GAAG,CAAC;IAElC,IAAIlC,QAAQ,GAAGD,IAAI,IAAI,CAAC;IACxB,IAAI4B,IAAI;IACR,IAAI3C,YAAY;IAChB,IAAImB,eAAe,GAAGyC,aAAa;IAEnC,IAAI5C,QAAQ,EAAE;MACZ2B,IAAI,GAAGe,KAAK;MACZ3C,IAAI,GAAGA,IAAI,GAAG,CAAC;MAEf,IAAIA,IAAI,KAAK,CAAC,EAAc;QAC1B+C,kBAAkB,GAAGA,kBAAkB,IAAI,EAAE;QAC7C9D,YAAY,GAAG8D,kBAAkB;MACnC;MACA,IAAI7C,SAAS,IAAI,CAAC8C,WAAW,EAAE;QAC7BA,WAAW,GAAG,UAAUM,CAAC,EAAE;UACzB,OAAOC,WAAU,CAACD,CAAC,CAAC,KAAKX,KAAK;QAChC,CAAC;MACH;MACAvC,eAAe,GAAG4C,WAAW;IAC/B,CAAC,MAAM;MACLpB,IAAI,GAAGe,KAAK,CAACa,SAAS;MAEtB,IAAIxD,IAAI,KAAK,CAAC,EAAc;QAC1B8C,iBAAiB,GAAGA,iBAAiB,IAAI,EAAE;QAC3C7D,YAAY,GAAG6D,iBAAiB;MAClC;IACF;IAEA,IAAI9C,IAAI,KAAK,CAAC,IAAgB,CAACE,SAAS,EAAE;MACxC,IAAIuD,iBAAiB,GAAGxD,QAAQ,GAC5BkD,uBAAuB,GACvBF,sBAAsB;MAE1B,IAAIS,YAAY,GAAGD,iBAAiB,CAAC/C,GAAG,CAACZ,IAAI,CAAC,IAAI,CAAC;MAEnD,IACE4D,YAAY,KAAK,IAAI,IACpBA,YAAY,KAAK,CAAC,IAAiB1D,IAAI,KAAK,CAAE,IAC9C0D,YAAY,KAAK,CAAC,IAAiB1D,IAAI,KAAK,CAAE,EAC/C;QACA,MAAM,IAAIiB,KAAK,CACb,uMAAuM,GACrMnB,IAAI,CACP;MACH,CAAC,MAAM,IAAI,CAAC4D,YAAY,IAAI1D,IAAI,GAAG,CAAC,EAAe;QACjDyD,iBAAiB,CAAC9C,GAAG,CAACb,IAAI,EAAEE,IAAI,CAAC;MACnC,CAAC,MAAM;QACLyD,iBAAiB,CAAC9C,GAAG,CAACb,IAAI,EAAE,IAAI,CAAC;MACnC;IACF;IAEA4B,cAAc,CACZC,GAAG,EACHC,IAAI,EACJC,OAAO,EACP/B,IAAI,EACJE,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTjB,YAAY,EACZmB,eAAe,CAChB;EACH;EAEAuD,gBAAgB,CAAChC,GAAG,EAAEmB,iBAAiB,CAAC;EACxCa,gBAAgB,CAAChC,GAAG,EAAEoB,kBAAkB,CAAC;EACzC,OAAOpB,GAAG;AACZ;AAEA,SAASgC,gBAAgB,CAAChC,GAAG,EAAE1C,YAAY,EAAE;EAC3C,IAAIA,YAAY,EAAE;IAChB0C,GAAG,CAACpC,IAAI,CAAC,UAAU8C,QAAQ,EAAE;MAC3B,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjD,YAAY,CAACkD,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5CjD,YAAY,CAACiD,CAAC,CAAC,CAACrB,IAAI,CAACwB,QAAQ,CAAC;MAChC;MACA,OAAOA,QAAQ;IACjB,CAAC,CAAC;EACJ;AACF;AAEA,SAASuB,cAAc,CAACC,WAAW,EAAEC,SAAS,EAAE;EAC9C,IAAIA,SAAS,CAAC3B,MAAM,GAAG,CAAC,EAAE;IACxB,IAAIlD,YAAY,GAAG,EAAE;IACrB,IAAI8E,QAAQ,GAAGF,WAAW;IAC1B,IAAI/D,IAAI,GAAG+D,WAAW,CAAC/D,IAAI;IAE3B,KAAK,IAAIoC,CAAC,GAAG4B,SAAS,CAAC3B,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9C,IAAIhD,oBAAoB,GAAG;QAAEuB,CAAC,EAAE;MAAM,CAAC;MAEvC,IAAI;QACF,IAAIuD,YAAY,GAAGF,SAAS,CAAC5B,CAAC,CAAC,CAAC6B,QAAQ,EAAE;UACxC/D,IAAI,EAAE,OAAO;UACbF,IAAI,EAAEA,IAAI;UACVX,cAAc,EAAEH,0BAA0B,CACxCC,YAAY,EACZC,oBAAoB;QAExB,CAAC,CAAC;MACJ,CAAC,SAAS;QACRA,oBAAoB,CAACuB,CAAC,GAAG,IAAI;MAC/B;MAEA,IAAIuD,YAAY,KAAK1C,SAAS,EAAE;QAC9BF,sBAAsB,CAAC,EAAE,EAAc4C,YAAY,CAAC;QACpDD,QAAQ,GAAGC,YAAY;MACzB;IACF;IAEA,OAAO,CACLD,QAAQ,EACR,YAAY;MACV,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjD,YAAY,CAACkD,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5CjD,YAAY,CAACiD,CAAC,CAAC,CAACrB,IAAI,CAACkD,QAAQ,CAAC;MAChC;IACF,CAAC,CACF;EACH;AAGF;AAoJe,SAASE,aAAa,CACnCJ,WAAW,EACXK,UAAU,EACVJ,SAAS,EACTjB,aAAa,EACb;EACA,OAAO;IACLsB,CAAC,EAAEzB,eAAe,CAACmB,WAAW,EAAEK,UAAU,EAAErB,aAAa,CAAC;IAE1D,IAAIuB,CAAC,GAAG;MACN,OAAOR,cAAc,CAACC,WAAW,EAAEC,SAAS,CAAC;IAC/C;EACF,CAAC;AACH"}