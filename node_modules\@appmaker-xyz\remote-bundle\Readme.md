## Remote bundle

Remote bundle let you run javascript loaded from a remote server. This can elimiate release cycle of your application. This can load react-native components via cdn.

## Usage

install via npm

```
npm install @appmaker-xyz/remote-bundle
```

### Javascript API usage

```javascript
import { loadBundle } from '@appmaker-xyz/remote-bundle';

const bundleExports = await loadBundle({
  url: 'https://example.com/bundle.js',
});
// bundle will have all exports from bundle.js
```

### React Native usage

```javascript

import { RemoteComponent } from '@appmaker-xyz/remote-bundle';

const App = () => {
  return (
    <RemoteComponent
      url="https://example.com/bundle.js"
      componentName="MyComponent"
      props={{
        name: 'John',
      }}
    />
  );
};

```
