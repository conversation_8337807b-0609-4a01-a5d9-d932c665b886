import {
  ValidateEmail,
  showAlertWithButton
} from "../../../misc/HelperFunctions";
import { PostComment } from "../../../api/index";
import Config from "../../../../Config";
// TODO write test
/**
 * Shows an alert with buttons
 * @param {*} m1 Title
 * @param {*} m2 Message
 */
const showAlert = (m1, m2, modalCallback) =>
  showAlertWithButton(m1, m2, [
    {
      text: "ok",
      onPress: () => {
        modalCallback(false);
      }
    }
  ]);

/**
 * Function to call Comment submit api
 * @param {*} callbackUrl comment submit url
 * @param {*} email user email
 * @param {*} name user name
 * @param {*} comment user comment
 * @param {*} modalCallback callback to minimise Comment add modals
 */
const submitComment = (callbackUrl, email, name, comment, modalCallback) => {
  if (ValidateEmail(email)) {
    PostComment(callbackUrl, Config.api_key, {
      name,
      email,
      comment
    })
      .then()
      .then(value => {
        showAlert("Done", value.message, modalCallback);
      })
      .catch(e => {
        showAlert(
          "Error",
          "Something went wrong please try again !",
          modalCallback
        );
      });
  } else {
    showAlert("Invalid Email", "Please Enter a valid Email", modalCallback);
  }
};

export { submitComment };
