import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator, Image, FlatList } from 'react-native';
import {
  useInfiniteDataSource,
  usePageState,
  appmaker,
} from '@appmaker-xyz/core';
import { FlashList } from '@shopify/flash-list';
import { getSpanFromLayoutConfig } from './helper';
import { useCollectionListV2 } from '@appmaker-xyz/shopify';
import { useStyles } from '@appmaker-xyz/react-native';
import { isEmpty } from 'lodash';
const productGridBlockName = 'appmaker/product-grid-item';
function HeaderBlock(props) {
  const { collection, attributes, BlocksView } = props;
  if (attributes?.hideHeader) {
    return null;
  }
  if (attributes?.headerBlocks) {
    const headerBlocks = Object.keys(attributes?.headerBlocks)?.map(
      (key) => attributes?.headerBlocks?.[key],
    );
    if (headerBlocks?.length > 0) {
      return <BlocksView {...props} inAppPage={{ blocks: headerBlocks }} />;
    }
  }

  const uri = collection?.image?.url;
  if (!uri) {
    return null;
  }
  return <Image source={{ uri }} style={styles.headerImage} />;
}
const getSourceParams = ({
  attributes,
  currentCollectionState,
  currentAction,
}) => {
  const sourceParams =
    currentCollectionState ||
    attributes?.productParams ||
    currentAction?.params ||
    {};
  return {
    sourceParams,
    surface: sourceParams?.searchResult ? 'search-result' : 'collection-page',
  };
};
export default function Collection(props) {
  const { attributes, BlockItemRender, onAction, clientId } = props;
  const { layoutConfig, currentCollection } = useCollectionListV2();
  const blockData = usePageState((pageState) => pageState.blockData);
  const appliedFilter = usePageState((pageState) => pageState.filter);
  const appliedSort = usePageState((pageState) => pageState.sort);
  const currentAction = usePageState((pageState) => pageState.currentAction);
  const [viewableItems, setViewableItems] = useState([]);
  const { sourceParams, surface } = getSourceParams({
    attributes,
    currentCollectionState: currentCollection,
    currentAction,
  });
  const dataSource = {
    source: 'shopify',
    methodName: 'products',
    params: {
      ...sourceParams,
      surface,
    },
  };

  const useFlatList = attributes?.useFlatList;
  const [{ data, isLoading, isFetching, isError, hasNextPage, fetchNextPage }] =
    useInfiniteDataSource({
      dataSource,
      enabled: true,
      filterParams: {
        filter: appliedFilter,
        sort: appliedSort,
      },
      pageInfoExtractor: (lastPage) => lastPage?.data?.data?.products?.pageInfo,
    });
  const pages = data?.pages;
  let productsList = pages?.flatMap(
    (page) => page?.data?.data?.products?.edges || [],
  );
  let collection = data?.pages?.[0]?.data?.data?.collection;
  const loadNext = () => {
    if (hasNextPage) {
      fetchNextPage();
    }
  };
  const { theme } = useStyles();
  let filters = data?.pages?.[0]?.data?.data?.products?.filters;
  const filterDataSource = {
    source: 'shopify',
    methodName: 'productFilters',
    params: {
      ...sourceParams,
      surface,
    },
  };

  // If a Collection request is sent and returned without any filters, then only the filter request will be fired. (To avoid duplicate filter calls )
  const shouldGetFilter = isEmpty(filters) && data;
  const [{ data: filterData }] = useInfiniteDataSource({
    dataSource: filterDataSource,
    enabled: !!shouldGetFilter,
    filterParams: {
      filter: appliedFilter,
      sort: appliedSort,
    },
  });
  if (
    isEmpty(filters) &&
    filterData?.pages?.[0]?.data?.data?.collection?.products?.filters
  ) {
    filters = filterData.pages[0].data.data.collection.products.filters;
  }
  const currentLayoutConfig = layoutConfig || attributes?.defaultLayoutConfig;
  productsList = appmaker.applyFilters(
    'shopify-before-product-list-render',
    productsList,
    {
      blockData,
      filters,
    },
  );
  const Header = appmaker.applyFilters(
    'shopify-flashlist-dynamic-column-header',
    HeaderBlock,
  );
  const MemorizedHeader = useMemo(
    () => <Header {...props} collection={collection} />,
    [props, collection],
  );

  const numColumn =
    typeof currentLayoutConfig?.numColumns === 'number'
      ? currentLayoutConfig?.numColumns
      : 2;
  const onViewableItemsChanged = useCallback(({ viewableItems }) => {
    setViewableItems(viewableItems.map(({ index }) => index));
  }, []);

  /* Flash list wont change scroll position to top when data is changed without loading from cache as..
   * a result of applied filters
   */
  const listKey = useMemo(() => {
    return !useFlatList
      ? `${Date.now() + Math.floor(Math.random() * 1000)}`
      : '';
  }, [appliedFilter, useFlatList]);

  const ListComponent = useFlatList ? FlatList : FlashList;
  return (
    <View style={styles.container}>
      {isLoading ? (
        <View style={styles.centerContainer}>
          <ActivityIndicator
            style={styles.centerContainer}
            animating={true}
            color={theme?.colors?.text || 'black'}
          />
        </View>
      ) : (
        <ListComponent
          key={`flashlist-${clientId}-${numColumn}-${listKey}`}
          ListHeaderComponent={MemorizedHeader}
          data={productsList}
          numColumns={numColumn}
          onEndReached={loadNext}
          estimatedItemSize={381}
          onEndReachedThreshold={2}
          onViewableItemsChanged={onViewableItemsChanged}
          overrideItemLayout={(layout, item, index) => {
            if (currentLayoutConfig) {
              layout.span = getSpanFromLayoutConfig({
                item,
                index,
                layoutConfig: currentLayoutConfig,
              });
            }
          }}
          ListFooterComponent={() =>
            !isLoading && isFetching ? (
              <ActivityIndicator size="small" color="#000000" />
            ) : null
          }
          getItemType={(item, index) => {
            if (
              item?.___appmakercustomblock &&
              item?.___appmakercustomblock?.block
            ) {
              return item?.___appmakercustomblock?.block?.name;
            }
            return productGridBlockName;
          }}
          renderItem={({ item, extraData, index }) => {
            if (item?.___appmakercustomblock) {
              return (
                <BlockItemRender
                  extraData={extraData}
                  block={{
                    ...item?.___appmakercustomblock?.block,
                    attributes: {
                      ...item?.___appmakercustomblock?.block?.attributes,
                      isInView: viewableItems.includes(index),
                      bypassContext: true,
                    },
                  }}
                  onAction={onAction}
                />
              );
            } else {
              return (
                <BlockItemRender
                  extraData={extraData}
                  blockData={item}
                  onAction={onAction}
                  blocksViewItemIndex={index}
                  block={{
                    name: productGridBlockName,
                    attributes: {
                      __experimentalDisableListItemParser: true,
                      gridViewListing: true,
                      numColumns: 2,
                      dynamicColumn: true,
                      layoutConfig: currentLayoutConfig,
                      span: getSpanFromLayoutConfig({
                        item,
                        index,
                        layoutConfig: currentLayoutConfig,
                      }),
                    },
                  }}
                />
              );
            }
          }}
        />
      )}
      <BlockItemRender
        blockData={filters}
        onAction={onAction}
        block={{
          name: 'shopify/collection-filter',
          attributes: {
            disableFilterAndSort: attributes?.disableFilterAndSort,
            hideButtons: attributes?.hideFilterSortButtons,
            show_filter: true, // themeHelper
            show_sort: true, // themeHelper
          },
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  sliderContainer: {
    alignItems: 'center',
  },
  trackStyle: {
    borderRadius: 8,
    height: 2,
  },
  slideSelectedStyle: {
    backgroundColor: '#1B1B1B',
  },
  markerStyle: {
    height: 12,
    width: 12,
    borderRadius: 0,
    backgroundColor: '#1B1B1B',
    borderWidth: 2,
    borderColor: 'transparent',
    marginVertical: 16,
  },
  markerPressed: {
    backgroundColor: '#1B1B1B',
    height: 16,
    width: 16,
    borderRadius: 0,
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
});
