import React from 'react';
import { KeyboardAvoidingView } from 'react-native';
import { Layout, ThemeText, Button } from '@appmaker-xyz/ui';
import { useAccountDetailUpdateV2 } from '@appmaker-xyz/shopify';
import { InputFormItem } from './components/InputFormItem';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';

const OtpLoginAccountDetails = (props: any) => {
  const { blockData } = props;
  const {
    control,
    setFocus,
    handleSubmit,
    isLoading,
    isPhoneDisabled,
    isEmailDisabled,
  } = useAccountDetailUpdateV2({
    defaultValues: {
      email: blockData?.email || '',
      first_name: blockData?.first_name || '',
      last_name: blockData?.last_name || '',
      phone: blockData?.phone || '',
    },
    redirectAction: blockData?.redirectAction,
    props,
  });
  const { styles, theme } = useStyles(stylesheet);
  return (
    <Layout>
      <KeyboardAvoidingView keyboardVerticalOffset={80} behavior={'padding'}>
        <Layout style={styles.container}>
          <Layout style={styles.row}>
            <Layout style={styles.inputContainer}>
              <ThemeText size="sm" color={theme.colors.lightText}>
                First Name
              </ThemeText>
              <InputFormItem
                autoFocus={true}
                control={control}
                name="first_name"
                label={'First Name'}
                returnKeyType="next"
                onSubmitEditing={() => setFocus('lastName')}
                style={styles.input}
              />
            </Layout>
            <Layout style={styles.gap} />
            <Layout style={styles.inputContainer}>
              <ThemeText size="sm" color={theme.colors.lightText}>
                Last Name
              </ThemeText>
              <InputFormItem
                control={control}
                name="last_name"
                label={'Last Name'}
                returnKeyType="next"
                onSubmitEditing={() => setFocus('email')}
                style={styles.input}
              />
            </Layout>
          </Layout>
          <Layout>
            <ThemeText size="sm" color={theme.colors.lightText}>
              Email Id
            </ThemeText>
            <InputFormItem
              control={control}
              name="email"
              label={'Email'}
              returnKeyType="next"
              onSubmitEditing={() => setFocus('phone')}
              style={styles.input}
              editable={!isEmailDisabled}
            />
          </Layout>
          <Layout>
            <ThemeText size="sm" color={theme.colors.lightText}>
              Phone Number
            </ThemeText>
            <InputFormItem
              control={control}
              name="phone"
              label={'Phone'}
              returnKeyType="done"
              onSubmitEditing={handleSubmit}
              style={styles.input}
              editable={!isPhoneDisabled}
            />
          </Layout>
          <Button
            onPress={handleSubmit}
            title={'SIGN UP'}
            isLoading={isLoading}
            color={theme.colors.primaryButtonBackground}
            textColor={theme.colors.primaryButtonText}
            activityIndicatorColor={theme.colors.primaryButtonText}
            size="md"
          />
        </Layout>
      </KeyboardAvoidingView>
    </Layout>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    paddingHorizontal: 12,
    paddingTop: 24,
  },
  input: {
    marginTop: 2,
  },
  row: {
    flexDirection: 'row',
  },
  inputContainer: {
    flex: 1,
  },
  gap: {
    width: 12,
  },
}));

export default OtpLoginAccountDetails;
