{"version": 3, "names": [], "sources": ["../../../src/ast-types/generated/index.ts"], "sourcesContent": ["// NOTE: This file is autogenerated. Do not modify.\n// See packages/babel-types/scripts/generators/ast-types.js for script used.\n\ninterface BaseComment {\n  value: string;\n  start?: number;\n  end?: number;\n  loc?: SourceLocation;\n  // generator will skip the comment if ignore is true\n  ignore?: boolean;\n  type: \"CommentBlock\" | \"CommentLine\";\n}\n\nexport interface CommentBlock extends BaseComment {\n  type: \"CommentBlock\";\n}\n\nexport interface CommentLine extends BaseComment {\n  type: \"CommentLine\";\n}\n\nexport type Comment = CommentBlock | CommentLine;\n\nexport interface SourceLocation {\n  start: {\n    line: number;\n    column: number;\n  };\n\n  end: {\n    line: number;\n    column: number;\n  };\n}\n\ninterface BaseNode {\n  type: Node[\"type\"];\n  leadingComments?: Comment[] | null;\n  innerComments?: Comment[] | null;\n  trailingComments?: Comment[] | null;\n  start?: number | null;\n  end?: number | null;\n  loc?: SourceLocation | null;\n  range?: [number, number];\n  extra?: Record<string, unknown>;\n}\n\nexport type CommentTypeShorthand = \"leading\" | \"inner\" | \"trailing\";\n\nexport type Node =\n  | AnyTypeAnnotation\n  | ArgumentPlaceholder\n  | ArrayExpression\n  | ArrayPattern\n  | ArrayTypeAnnotation\n  | ArrowFunctionExpression\n  | AssignmentExpression\n  | AssignmentPattern\n  | AwaitExpression\n  | BigIntLiteral\n  | BinaryExpression\n  | BindExpression\n  | BlockStatement\n  | BooleanLiteral\n  | BooleanLiteralTypeAnnotation\n  | BooleanTypeAnnotation\n  | BreakStatement\n  | CallExpression\n  | CatchClause\n  | ClassAccessorProperty\n  | ClassBody\n  | ClassDeclaration\n  | ClassExpression\n  | ClassImplements\n  | ClassMethod\n  | ClassPrivateMethod\n  | ClassPrivateProperty\n  | ClassProperty\n  | ConditionalExpression\n  | ContinueStatement\n  | DebuggerStatement\n  | DecimalLiteral\n  | DeclareClass\n  | DeclareExportAllDeclaration\n  | DeclareExportDeclaration\n  | DeclareFunction\n  | DeclareInterface\n  | DeclareModule\n  | DeclareModuleExports\n  | DeclareOpaqueType\n  | DeclareTypeAlias\n  | DeclareVariable\n  | DeclaredPredicate\n  | Decorator\n  | Directive\n  | DirectiveLiteral\n  | DoExpression\n  | DoWhileStatement\n  | EmptyStatement\n  | EmptyTypeAnnotation\n  | EnumBooleanBody\n  | EnumBooleanMember\n  | EnumDeclaration\n  | EnumDefaultedMember\n  | EnumNumberBody\n  | EnumNumberMember\n  | EnumStringBody\n  | EnumStringMember\n  | EnumSymbolBody\n  | ExistsTypeAnnotation\n  | ExportAllDeclaration\n  | ExportDefaultDeclaration\n  | ExportDefaultSpecifier\n  | ExportNamedDeclaration\n  | ExportNamespaceSpecifier\n  | ExportSpecifier\n  | ExpressionStatement\n  | File\n  | ForInStatement\n  | ForOfStatement\n  | ForStatement\n  | FunctionDeclaration\n  | FunctionExpression\n  | FunctionTypeAnnotation\n  | FunctionTypeParam\n  | GenericTypeAnnotation\n  | Identifier\n  | IfStatement\n  | Import\n  | ImportAttribute\n  | ImportDeclaration\n  | ImportDefaultSpecifier\n  | ImportNamespaceSpecifier\n  | ImportSpecifier\n  | IndexedAccessType\n  | InferredPredicate\n  | InterfaceDeclaration\n  | InterfaceExtends\n  | InterfaceTypeAnnotation\n  | InterpreterDirective\n  | IntersectionTypeAnnotation\n  | JSXAttribute\n  | JSXClosingElement\n  | JSXClosingFragment\n  | JSXElement\n  | JSXEmptyExpression\n  | JSXExpressionContainer\n  | JSXFragment\n  | JSXIdentifier\n  | JSXMemberExpression\n  | JSXNamespacedName\n  | JSXOpeningElement\n  | JSXOpeningFragment\n  | JSXSpreadAttribute\n  | JSXSpreadChild\n  | JSXText\n  | LabeledStatement\n  | LogicalExpression\n  | MemberExpression\n  | MetaProperty\n  | MixedTypeAnnotation\n  | ModuleExpression\n  | NewExpression\n  | Noop\n  | NullLiteral\n  | NullLiteralTypeAnnotation\n  | NullableTypeAnnotation\n  | NumberLiteral\n  | NumberLiteralTypeAnnotation\n  | NumberTypeAnnotation\n  | NumericLiteral\n  | ObjectExpression\n  | ObjectMethod\n  | ObjectPattern\n  | ObjectProperty\n  | ObjectTypeAnnotation\n  | ObjectTypeCallProperty\n  | ObjectTypeIndexer\n  | ObjectTypeInternalSlot\n  | ObjectTypeProperty\n  | ObjectTypeSpreadProperty\n  | OpaqueType\n  | OptionalCallExpression\n  | OptionalIndexedAccessType\n  | OptionalMemberExpression\n  | ParenthesizedExpression\n  | PipelineBareFunction\n  | PipelinePrimaryTopicReference\n  | PipelineTopicExpression\n  | Placeholder\n  | PrivateName\n  | Program\n  | QualifiedTypeIdentifier\n  | RecordExpression\n  | RegExpLiteral\n  | RegexLiteral\n  | RestElement\n  | RestProperty\n  | ReturnStatement\n  | SequenceExpression\n  | SpreadElement\n  | SpreadProperty\n  | StaticBlock\n  | StringLiteral\n  | StringLiteralTypeAnnotation\n  | StringTypeAnnotation\n  | Super\n  | SwitchCase\n  | SwitchStatement\n  | SymbolTypeAnnotation\n  | TSAnyKeyword\n  | TSArrayType\n  | TSAsExpression\n  | TSBigIntKeyword\n  | TSBooleanKeyword\n  | TSCallSignatureDeclaration\n  | TSConditionalType\n  | TSConstructSignatureDeclaration\n  | TSConstructorType\n  | TSDeclareFunction\n  | TSDeclareMethod\n  | TSEnumDeclaration\n  | TSEnumMember\n  | TSExportAssignment\n  | TSExpressionWithTypeArguments\n  | TSExternalModuleReference\n  | TSFunctionType\n  | TSImportEqualsDeclaration\n  | TSImportType\n  | TSIndexSignature\n  | TSIndexedAccessType\n  | TSInferType\n  | TSInstantiationExpression\n  | TSInterfaceBody\n  | TSInterfaceDeclaration\n  | TSIntersectionType\n  | TSIntrinsicKeyword\n  | TSLiteralType\n  | TSMappedType\n  | TSMethodSignature\n  | TSModuleBlock\n  | TSModuleDeclaration\n  | TSNamedTupleMember\n  | TSNamespaceExportDeclaration\n  | TSNeverKeyword\n  | TSNonNullExpression\n  | TSNullKeyword\n  | TSNumberKeyword\n  | TSObjectKeyword\n  | TSOptionalType\n  | TSParameterProperty\n  | TSParenthesizedType\n  | TSPropertySignature\n  | TSQualifiedName\n  | TSRestType\n  | TSSatisfiesExpression\n  | TSStringKeyword\n  | TSSymbolKeyword\n  | TSThisType\n  | TSTupleType\n  | TSTypeAliasDeclaration\n  | TSTypeAnnotation\n  | TSTypeAssertion\n  | TSTypeLiteral\n  | TSTypeOperator\n  | TSTypeParameter\n  | TSTypeParameterDeclaration\n  | TSTypeParameterInstantiation\n  | TSTypePredicate\n  | TSTypeQuery\n  | TSTypeReference\n  | TSUndefinedKeyword\n  | TSUnionType\n  | TSUnknownKeyword\n  | TSVoidKeyword\n  | TaggedTemplateExpression\n  | TemplateElement\n  | TemplateLiteral\n  | ThisExpression\n  | ThisTypeAnnotation\n  | ThrowStatement\n  | TopicReference\n  | TryStatement\n  | TupleExpression\n  | TupleTypeAnnotation\n  | TypeAlias\n  | TypeAnnotation\n  | TypeCastExpression\n  | TypeParameter\n  | TypeParameterDeclaration\n  | TypeParameterInstantiation\n  | TypeofTypeAnnotation\n  | UnaryExpression\n  | UnionTypeAnnotation\n  | UpdateExpression\n  | V8IntrinsicIdentifier\n  | VariableDeclaration\n  | VariableDeclarator\n  | Variance\n  | VoidTypeAnnotation\n  | WhileStatement\n  | WithStatement\n  | YieldExpression;\n\nexport interface ArrayExpression extends BaseNode {\n  type: \"ArrayExpression\";\n  elements: Array<null | Expression | SpreadElement>;\n}\n\nexport interface AssignmentExpression extends BaseNode {\n  type: \"AssignmentExpression\";\n  operator: string;\n  left: LVal;\n  right: Expression;\n}\n\nexport interface BinaryExpression extends BaseNode {\n  type: \"BinaryExpression\";\n  operator:\n    | \"+\"\n    | \"-\"\n    | \"/\"\n    | \"%\"\n    | \"*\"\n    | \"**\"\n    | \"&\"\n    | \"|\"\n    | \">>\"\n    | \">>>\"\n    | \"<<\"\n    | \"^\"\n    | \"==\"\n    | \"===\"\n    | \"!=\"\n    | \"!==\"\n    | \"in\"\n    | \"instanceof\"\n    | \">\"\n    | \"<\"\n    | \">=\"\n    | \"<=\"\n    | \"|>\";\n  left: Expression | PrivateName;\n  right: Expression;\n}\n\nexport interface InterpreterDirective extends BaseNode {\n  type: \"InterpreterDirective\";\n  value: string;\n}\n\nexport interface Directive extends BaseNode {\n  type: \"Directive\";\n  value: DirectiveLiteral;\n}\n\nexport interface DirectiveLiteral extends BaseNode {\n  type: \"DirectiveLiteral\";\n  value: string;\n}\n\nexport interface BlockStatement extends BaseNode {\n  type: \"BlockStatement\";\n  body: Array<Statement>;\n  directives: Array<Directive>;\n}\n\nexport interface BreakStatement extends BaseNode {\n  type: \"BreakStatement\";\n  label?: Identifier | null;\n}\n\nexport interface CallExpression extends BaseNode {\n  type: \"CallExpression\";\n  callee: Expression | Super | V8IntrinsicIdentifier;\n  arguments: Array<\n    Expression | SpreadElement | JSXNamespacedName | ArgumentPlaceholder\n  >;\n  optional?: true | false | null;\n  typeArguments?: TypeParameterInstantiation | null;\n  typeParameters?: TSTypeParameterInstantiation | null;\n}\n\nexport interface CatchClause extends BaseNode {\n  type: \"CatchClause\";\n  param?: Identifier | ArrayPattern | ObjectPattern | null;\n  body: BlockStatement;\n}\n\nexport interface ConditionalExpression extends BaseNode {\n  type: \"ConditionalExpression\";\n  test: Expression;\n  consequent: Expression;\n  alternate: Expression;\n}\n\nexport interface ContinueStatement extends BaseNode {\n  type: \"ContinueStatement\";\n  label?: Identifier | null;\n}\n\nexport interface DebuggerStatement extends BaseNode {\n  type: \"DebuggerStatement\";\n}\n\nexport interface DoWhileStatement extends BaseNode {\n  type: \"DoWhileStatement\";\n  test: Expression;\n  body: Statement;\n}\n\nexport interface EmptyStatement extends BaseNode {\n  type: \"EmptyStatement\";\n}\n\nexport interface ExpressionStatement extends BaseNode {\n  type: \"ExpressionStatement\";\n  expression: Expression;\n}\n\nexport interface File extends BaseNode {\n  type: \"File\";\n  program: Program;\n  comments?: Array<CommentBlock | CommentLine> | null;\n  tokens?: Array<any> | null;\n}\n\nexport interface ForInStatement extends BaseNode {\n  type: \"ForInStatement\";\n  left: VariableDeclaration | LVal;\n  right: Expression;\n  body: Statement;\n}\n\nexport interface ForStatement extends BaseNode {\n  type: \"ForStatement\";\n  init?: VariableDeclaration | Expression | null;\n  test?: Expression | null;\n  update?: Expression | null;\n  body: Statement;\n}\n\nexport interface FunctionDeclaration extends BaseNode {\n  type: \"FunctionDeclaration\";\n  id?: Identifier | null;\n  params: Array<Identifier | Pattern | RestElement>;\n  body: BlockStatement;\n  generator: boolean;\n  async: boolean;\n  declare?: boolean | null;\n  predicate?: DeclaredPredicate | InferredPredicate | null;\n  returnType?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n  typeParameters?:\n    | TypeParameterDeclaration\n    | TSTypeParameterDeclaration\n    | Noop\n    | null;\n}\n\nexport interface FunctionExpression extends BaseNode {\n  type: \"FunctionExpression\";\n  id?: Identifier | null;\n  params: Array<Identifier | Pattern | RestElement>;\n  body: BlockStatement;\n  generator: boolean;\n  async: boolean;\n  predicate?: DeclaredPredicate | InferredPredicate | null;\n  returnType?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n  typeParameters?:\n    | TypeParameterDeclaration\n    | TSTypeParameterDeclaration\n    | Noop\n    | null;\n}\n\nexport interface Identifier extends BaseNode {\n  type: \"Identifier\";\n  name: string;\n  decorators?: Array<Decorator> | null;\n  optional?: boolean | null;\n  typeAnnotation?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n}\n\nexport interface IfStatement extends BaseNode {\n  type: \"IfStatement\";\n  test: Expression;\n  consequent: Statement;\n  alternate?: Statement | null;\n}\n\nexport interface LabeledStatement extends BaseNode {\n  type: \"LabeledStatement\";\n  label: Identifier;\n  body: Statement;\n}\n\nexport interface StringLiteral extends BaseNode {\n  type: \"StringLiteral\";\n  value: string;\n}\n\nexport interface NumericLiteral extends BaseNode {\n  type: \"NumericLiteral\";\n  value: number;\n}\n\n/**\n * @deprecated Use `NumericLiteral`\n */\nexport interface NumberLiteral extends BaseNode {\n  type: \"NumberLiteral\";\n  value: number;\n}\n\nexport interface NullLiteral extends BaseNode {\n  type: \"NullLiteral\";\n}\n\nexport interface BooleanLiteral extends BaseNode {\n  type: \"BooleanLiteral\";\n  value: boolean;\n}\n\nexport interface RegExpLiteral extends BaseNode {\n  type: \"RegExpLiteral\";\n  pattern: string;\n  flags: string;\n}\n\n/**\n * @deprecated Use `RegExpLiteral`\n */\nexport interface RegexLiteral extends BaseNode {\n  type: \"RegexLiteral\";\n  pattern: string;\n  flags: string;\n}\n\nexport interface LogicalExpression extends BaseNode {\n  type: \"LogicalExpression\";\n  operator: \"||\" | \"&&\" | \"??\";\n  left: Expression;\n  right: Expression;\n}\n\nexport interface MemberExpression extends BaseNode {\n  type: \"MemberExpression\";\n  object: Expression | Super;\n  property: Expression | Identifier | PrivateName;\n  computed: boolean;\n  optional?: true | false | null;\n}\n\nexport interface NewExpression extends BaseNode {\n  type: \"NewExpression\";\n  callee: Expression | Super | V8IntrinsicIdentifier;\n  arguments: Array<\n    Expression | SpreadElement | JSXNamespacedName | ArgumentPlaceholder\n  >;\n  optional?: true | false | null;\n  typeArguments?: TypeParameterInstantiation | null;\n  typeParameters?: TSTypeParameterInstantiation | null;\n}\n\nexport interface Program extends BaseNode {\n  type: \"Program\";\n  body: Array<Statement>;\n  directives: Array<Directive>;\n  sourceType: \"script\" | \"module\";\n  interpreter?: InterpreterDirective | null;\n  sourceFile: string;\n}\n\nexport interface ObjectExpression extends BaseNode {\n  type: \"ObjectExpression\";\n  properties: Array<ObjectMethod | ObjectProperty | SpreadElement>;\n}\n\nexport interface ObjectMethod extends BaseNode {\n  type: \"ObjectMethod\";\n  kind: \"method\" | \"get\" | \"set\";\n  key: Expression | Identifier | StringLiteral | NumericLiteral | BigIntLiteral;\n  params: Array<Identifier | Pattern | RestElement>;\n  body: BlockStatement;\n  computed: boolean;\n  generator: boolean;\n  async: boolean;\n  decorators?: Array<Decorator> | null;\n  returnType?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n  typeParameters?:\n    | TypeParameterDeclaration\n    | TSTypeParameterDeclaration\n    | Noop\n    | null;\n}\n\nexport interface ObjectProperty extends BaseNode {\n  type: \"ObjectProperty\";\n  key:\n    | Expression\n    | Identifier\n    | StringLiteral\n    | NumericLiteral\n    | BigIntLiteral\n    | DecimalLiteral\n    | PrivateName;\n  value: Expression | PatternLike;\n  computed: boolean;\n  shorthand: boolean;\n  decorators?: Array<Decorator> | null;\n}\n\nexport interface RestElement extends BaseNode {\n  type: \"RestElement\";\n  argument: LVal;\n  decorators?: Array<Decorator> | null;\n  optional?: boolean | null;\n  typeAnnotation?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n}\n\n/**\n * @deprecated Use `RestElement`\n */\nexport interface RestProperty extends BaseNode {\n  type: \"RestProperty\";\n  argument: LVal;\n  decorators?: Array<Decorator> | null;\n  optional?: boolean | null;\n  typeAnnotation?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n}\n\nexport interface ReturnStatement extends BaseNode {\n  type: \"ReturnStatement\";\n  argument?: Expression | null;\n}\n\nexport interface SequenceExpression extends BaseNode {\n  type: \"SequenceExpression\";\n  expressions: Array<Expression>;\n}\n\nexport interface ParenthesizedExpression extends BaseNode {\n  type: \"ParenthesizedExpression\";\n  expression: Expression;\n}\n\nexport interface SwitchCase extends BaseNode {\n  type: \"SwitchCase\";\n  test?: Expression | null;\n  consequent: Array<Statement>;\n}\n\nexport interface SwitchStatement extends BaseNode {\n  type: \"SwitchStatement\";\n  discriminant: Expression;\n  cases: Array<SwitchCase>;\n}\n\nexport interface ThisExpression extends BaseNode {\n  type: \"ThisExpression\";\n}\n\nexport interface ThrowStatement extends BaseNode {\n  type: \"ThrowStatement\";\n  argument: Expression;\n}\n\nexport interface TryStatement extends BaseNode {\n  type: \"TryStatement\";\n  block: BlockStatement;\n  handler?: CatchClause | null;\n  finalizer?: BlockStatement | null;\n}\n\nexport interface UnaryExpression extends BaseNode {\n  type: \"UnaryExpression\";\n  operator: \"void\" | \"throw\" | \"delete\" | \"!\" | \"+\" | \"-\" | \"~\" | \"typeof\";\n  argument: Expression;\n  prefix: boolean;\n}\n\nexport interface UpdateExpression extends BaseNode {\n  type: \"UpdateExpression\";\n  operator: \"++\" | \"--\";\n  argument: Expression;\n  prefix: boolean;\n}\n\nexport interface VariableDeclaration extends BaseNode {\n  type: \"VariableDeclaration\";\n  kind: \"var\" | \"let\" | \"const\" | \"using\" | \"await using\";\n  declarations: Array<VariableDeclarator>;\n  declare?: boolean | null;\n}\n\nexport interface VariableDeclarator extends BaseNode {\n  type: \"VariableDeclarator\";\n  id: LVal;\n  init?: Expression | null;\n  definite?: boolean | null;\n}\n\nexport interface WhileStatement extends BaseNode {\n  type: \"WhileStatement\";\n  test: Expression;\n  body: Statement;\n}\n\nexport interface WithStatement extends BaseNode {\n  type: \"WithStatement\";\n  object: Expression;\n  body: Statement;\n}\n\nexport interface AssignmentPattern extends BaseNode {\n  type: \"AssignmentPattern\";\n  left:\n    | Identifier\n    | ObjectPattern\n    | ArrayPattern\n    | MemberExpression\n    | TSAsExpression\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TSNonNullExpression;\n  right: Expression;\n  decorators?: Array<Decorator> | null;\n  optional?: boolean | null;\n  typeAnnotation?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n}\n\nexport interface ArrayPattern extends BaseNode {\n  type: \"ArrayPattern\";\n  elements: Array<null | PatternLike | LVal>;\n  decorators?: Array<Decorator> | null;\n  optional?: boolean | null;\n  typeAnnotation?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n}\n\nexport interface ArrowFunctionExpression extends BaseNode {\n  type: \"ArrowFunctionExpression\";\n  params: Array<Identifier | Pattern | RestElement>;\n  body: BlockStatement | Expression;\n  async: boolean;\n  expression: boolean;\n  generator?: boolean;\n  predicate?: DeclaredPredicate | InferredPredicate | null;\n  returnType?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n  typeParameters?:\n    | TypeParameterDeclaration\n    | TSTypeParameterDeclaration\n    | Noop\n    | null;\n}\n\nexport interface ClassBody extends BaseNode {\n  type: \"ClassBody\";\n  body: Array<\n    | ClassMethod\n    | ClassPrivateMethod\n    | ClassProperty\n    | ClassPrivateProperty\n    | ClassAccessorProperty\n    | TSDeclareMethod\n    | TSIndexSignature\n    | StaticBlock\n  >;\n}\n\nexport interface ClassExpression extends BaseNode {\n  type: \"ClassExpression\";\n  id?: Identifier | null;\n  superClass?: Expression | null;\n  body: ClassBody;\n  decorators?: Array<Decorator> | null;\n  implements?: Array<TSExpressionWithTypeArguments | ClassImplements> | null;\n  mixins?: InterfaceExtends | null;\n  superTypeParameters?:\n    | TypeParameterInstantiation\n    | TSTypeParameterInstantiation\n    | null;\n  typeParameters?:\n    | TypeParameterDeclaration\n    | TSTypeParameterDeclaration\n    | Noop\n    | null;\n}\n\nexport interface ClassDeclaration extends BaseNode {\n  type: \"ClassDeclaration\";\n  id: Identifier;\n  superClass?: Expression | null;\n  body: ClassBody;\n  decorators?: Array<Decorator> | null;\n  abstract?: boolean | null;\n  declare?: boolean | null;\n  implements?: Array<TSExpressionWithTypeArguments | ClassImplements> | null;\n  mixins?: InterfaceExtends | null;\n  superTypeParameters?:\n    | TypeParameterInstantiation\n    | TSTypeParameterInstantiation\n    | null;\n  typeParameters?:\n    | TypeParameterDeclaration\n    | TSTypeParameterDeclaration\n    | Noop\n    | null;\n}\n\nexport interface ExportAllDeclaration extends BaseNode {\n  type: \"ExportAllDeclaration\";\n  source: StringLiteral;\n  assertions?: Array<ImportAttribute> | null;\n  attributes?: Array<ImportAttribute> | null;\n  exportKind?: \"type\" | \"value\" | null;\n}\n\nexport interface ExportDefaultDeclaration extends BaseNode {\n  type: \"ExportDefaultDeclaration\";\n  declaration:\n    | TSDeclareFunction\n    | FunctionDeclaration\n    | ClassDeclaration\n    | Expression;\n  exportKind?: \"value\" | null;\n}\n\nexport interface ExportNamedDeclaration extends BaseNode {\n  type: \"ExportNamedDeclaration\";\n  declaration?: Declaration | null;\n  specifiers: Array<\n    ExportSpecifier | ExportDefaultSpecifier | ExportNamespaceSpecifier\n  >;\n  source?: StringLiteral | null;\n  assertions?: Array<ImportAttribute> | null;\n  attributes?: Array<ImportAttribute> | null;\n  exportKind?: \"type\" | \"value\" | null;\n}\n\nexport interface ExportSpecifier extends BaseNode {\n  type: \"ExportSpecifier\";\n  local: Identifier;\n  exported: Identifier | StringLiteral;\n  exportKind?: \"type\" | \"value\" | null;\n}\n\nexport interface ForOfStatement extends BaseNode {\n  type: \"ForOfStatement\";\n  left: VariableDeclaration | LVal;\n  right: Expression;\n  body: Statement;\n  await: boolean;\n}\n\nexport interface ImportDeclaration extends BaseNode {\n  type: \"ImportDeclaration\";\n  specifiers: Array<\n    ImportSpecifier | ImportDefaultSpecifier | ImportNamespaceSpecifier\n  >;\n  source: StringLiteral;\n  assertions?: Array<ImportAttribute> | null;\n  attributes?: Array<ImportAttribute> | null;\n  importKind?: \"type\" | \"typeof\" | \"value\" | null;\n  module?: boolean | null;\n}\n\nexport interface ImportDefaultSpecifier extends BaseNode {\n  type: \"ImportDefaultSpecifier\";\n  local: Identifier;\n}\n\nexport interface ImportNamespaceSpecifier extends BaseNode {\n  type: \"ImportNamespaceSpecifier\";\n  local: Identifier;\n}\n\nexport interface ImportSpecifier extends BaseNode {\n  type: \"ImportSpecifier\";\n  local: Identifier;\n  imported: Identifier | StringLiteral;\n  importKind?: \"type\" | \"typeof\" | \"value\" | null;\n}\n\nexport interface MetaProperty extends BaseNode {\n  type: \"MetaProperty\";\n  meta: Identifier;\n  property: Identifier;\n}\n\nexport interface ClassMethod extends BaseNode {\n  type: \"ClassMethod\";\n  kind: \"get\" | \"set\" | \"method\" | \"constructor\";\n  key: Identifier | StringLiteral | NumericLiteral | BigIntLiteral | Expression;\n  params: Array<Identifier | Pattern | RestElement | TSParameterProperty>;\n  body: BlockStatement;\n  computed: boolean;\n  static: boolean;\n  generator: boolean;\n  async: boolean;\n  abstract?: boolean | null;\n  access?: \"public\" | \"private\" | \"protected\" | null;\n  accessibility?: \"public\" | \"private\" | \"protected\" | null;\n  decorators?: Array<Decorator> | null;\n  optional?: boolean | null;\n  override?: boolean;\n  returnType?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n  typeParameters?:\n    | TypeParameterDeclaration\n    | TSTypeParameterDeclaration\n    | Noop\n    | null;\n}\n\nexport interface ObjectPattern extends BaseNode {\n  type: \"ObjectPattern\";\n  properties: Array<RestElement | ObjectProperty>;\n  decorators?: Array<Decorator> | null;\n  optional?: boolean | null;\n  typeAnnotation?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n}\n\nexport interface SpreadElement extends BaseNode {\n  type: \"SpreadElement\";\n  argument: Expression;\n}\n\n/**\n * @deprecated Use `SpreadElement`\n */\nexport interface SpreadProperty extends BaseNode {\n  type: \"SpreadProperty\";\n  argument: Expression;\n}\n\nexport interface Super extends BaseNode {\n  type: \"Super\";\n}\n\nexport interface TaggedTemplateExpression extends BaseNode {\n  type: \"TaggedTemplateExpression\";\n  tag: Expression;\n  quasi: TemplateLiteral;\n  typeParameters?:\n    | TypeParameterInstantiation\n    | TSTypeParameterInstantiation\n    | null;\n}\n\nexport interface TemplateElement extends BaseNode {\n  type: \"TemplateElement\";\n  value: { raw: string; cooked?: string };\n  tail: boolean;\n}\n\nexport interface TemplateLiteral extends BaseNode {\n  type: \"TemplateLiteral\";\n  quasis: Array<TemplateElement>;\n  expressions: Array<Expression | TSType>;\n}\n\nexport interface YieldExpression extends BaseNode {\n  type: \"YieldExpression\";\n  argument?: Expression | null;\n  delegate: boolean;\n}\n\nexport interface AwaitExpression extends BaseNode {\n  type: \"AwaitExpression\";\n  argument: Expression;\n}\n\nexport interface Import extends BaseNode {\n  type: \"Import\";\n}\n\nexport interface BigIntLiteral extends BaseNode {\n  type: \"BigIntLiteral\";\n  value: string;\n}\n\nexport interface ExportNamespaceSpecifier extends BaseNode {\n  type: \"ExportNamespaceSpecifier\";\n  exported: Identifier;\n}\n\nexport interface OptionalMemberExpression extends BaseNode {\n  type: \"OptionalMemberExpression\";\n  object: Expression;\n  property: Expression | Identifier;\n  computed: boolean;\n  optional: boolean;\n}\n\nexport interface OptionalCallExpression extends BaseNode {\n  type: \"OptionalCallExpression\";\n  callee: Expression;\n  arguments: Array<\n    Expression | SpreadElement | JSXNamespacedName | ArgumentPlaceholder\n  >;\n  optional: boolean;\n  typeArguments?: TypeParameterInstantiation | null;\n  typeParameters?: TSTypeParameterInstantiation | null;\n}\n\nexport interface ClassProperty extends BaseNode {\n  type: \"ClassProperty\";\n  key: Identifier | StringLiteral | NumericLiteral | BigIntLiteral | Expression;\n  value?: Expression | null;\n  typeAnnotation?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n  decorators?: Array<Decorator> | null;\n  computed: boolean;\n  static: boolean;\n  abstract?: boolean | null;\n  accessibility?: \"public\" | \"private\" | \"protected\" | null;\n  declare?: boolean | null;\n  definite?: boolean | null;\n  optional?: boolean | null;\n  override?: boolean;\n  readonly?: boolean | null;\n  variance?: Variance | null;\n}\n\nexport interface ClassAccessorProperty extends BaseNode {\n  type: \"ClassAccessorProperty\";\n  key:\n    | Identifier\n    | StringLiteral\n    | NumericLiteral\n    | BigIntLiteral\n    | Expression\n    | PrivateName;\n  value?: Expression | null;\n  typeAnnotation?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n  decorators?: Array<Decorator> | null;\n  computed: boolean;\n  static: boolean;\n  abstract?: boolean | null;\n  accessibility?: \"public\" | \"private\" | \"protected\" | null;\n  declare?: boolean | null;\n  definite?: boolean | null;\n  optional?: boolean | null;\n  override?: boolean;\n  readonly?: boolean | null;\n  variance?: Variance | null;\n}\n\nexport interface ClassPrivateProperty extends BaseNode {\n  type: \"ClassPrivateProperty\";\n  key: PrivateName;\n  value?: Expression | null;\n  decorators?: Array<Decorator> | null;\n  static: boolean;\n  definite?: boolean | null;\n  readonly?: boolean | null;\n  typeAnnotation?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n  variance?: Variance | null;\n}\n\nexport interface ClassPrivateMethod extends BaseNode {\n  type: \"ClassPrivateMethod\";\n  kind: \"get\" | \"set\" | \"method\";\n  key: PrivateName;\n  params: Array<Identifier | Pattern | RestElement | TSParameterProperty>;\n  body: BlockStatement;\n  static: boolean;\n  abstract?: boolean | null;\n  access?: \"public\" | \"private\" | \"protected\" | null;\n  accessibility?: \"public\" | \"private\" | \"protected\" | null;\n  async?: boolean;\n  computed?: boolean;\n  decorators?: Array<Decorator> | null;\n  generator?: boolean;\n  optional?: boolean | null;\n  override?: boolean;\n  returnType?: TypeAnnotation | TSTypeAnnotation | Noop | null;\n  typeParameters?:\n    | TypeParameterDeclaration\n    | TSTypeParameterDeclaration\n    | Noop\n    | null;\n}\n\nexport interface PrivateName extends BaseNode {\n  type: \"PrivateName\";\n  id: Identifier;\n}\n\nexport interface StaticBlock extends BaseNode {\n  type: \"StaticBlock\";\n  body: Array<Statement>;\n}\n\nexport interface AnyTypeAnnotation extends BaseNode {\n  type: \"AnyTypeAnnotation\";\n}\n\nexport interface ArrayTypeAnnotation extends BaseNode {\n  type: \"ArrayTypeAnnotation\";\n  elementType: FlowType;\n}\n\nexport interface BooleanTypeAnnotation extends BaseNode {\n  type: \"BooleanTypeAnnotation\";\n}\n\nexport interface BooleanLiteralTypeAnnotation extends BaseNode {\n  type: \"BooleanLiteralTypeAnnotation\";\n  value: boolean;\n}\n\nexport interface NullLiteralTypeAnnotation extends BaseNode {\n  type: \"NullLiteralTypeAnnotation\";\n}\n\nexport interface ClassImplements extends BaseNode {\n  type: \"ClassImplements\";\n  id: Identifier;\n  typeParameters?: TypeParameterInstantiation | null;\n}\n\nexport interface DeclareClass extends BaseNode {\n  type: \"DeclareClass\";\n  id: Identifier;\n  typeParameters?: TypeParameterDeclaration | null;\n  extends?: Array<InterfaceExtends> | null;\n  body: ObjectTypeAnnotation;\n  implements?: Array<ClassImplements> | null;\n  mixins?: Array<InterfaceExtends> | null;\n}\n\nexport interface DeclareFunction extends BaseNode {\n  type: \"DeclareFunction\";\n  id: Identifier;\n  predicate?: DeclaredPredicate | null;\n}\n\nexport interface DeclareInterface extends BaseNode {\n  type: \"DeclareInterface\";\n  id: Identifier;\n  typeParameters?: TypeParameterDeclaration | null;\n  extends?: Array<InterfaceExtends> | null;\n  body: ObjectTypeAnnotation;\n}\n\nexport interface DeclareModule extends BaseNode {\n  type: \"DeclareModule\";\n  id: Identifier | StringLiteral;\n  body: BlockStatement;\n  kind?: \"CommonJS\" | \"ES\" | null;\n}\n\nexport interface DeclareModuleExports extends BaseNode {\n  type: \"DeclareModuleExports\";\n  typeAnnotation: TypeAnnotation;\n}\n\nexport interface DeclareTypeAlias extends BaseNode {\n  type: \"DeclareTypeAlias\";\n  id: Identifier;\n  typeParameters?: TypeParameterDeclaration | null;\n  right: FlowType;\n}\n\nexport interface DeclareOpaqueType extends BaseNode {\n  type: \"DeclareOpaqueType\";\n  id: Identifier;\n  typeParameters?: TypeParameterDeclaration | null;\n  supertype?: FlowType | null;\n  impltype?: FlowType | null;\n}\n\nexport interface DeclareVariable extends BaseNode {\n  type: \"DeclareVariable\";\n  id: Identifier;\n}\n\nexport interface DeclareExportDeclaration extends BaseNode {\n  type: \"DeclareExportDeclaration\";\n  declaration?: Flow | null;\n  specifiers?: Array<ExportSpecifier | ExportNamespaceSpecifier> | null;\n  source?: StringLiteral | null;\n  default?: boolean | null;\n}\n\nexport interface DeclareExportAllDeclaration extends BaseNode {\n  type: \"DeclareExportAllDeclaration\";\n  source: StringLiteral;\n  exportKind?: \"type\" | \"value\" | null;\n}\n\nexport interface DeclaredPredicate extends BaseNode {\n  type: \"DeclaredPredicate\";\n  value: Flow;\n}\n\nexport interface ExistsTypeAnnotation extends BaseNode {\n  type: \"ExistsTypeAnnotation\";\n}\n\nexport interface FunctionTypeAnnotation extends BaseNode {\n  type: \"FunctionTypeAnnotation\";\n  typeParameters?: TypeParameterDeclaration | null;\n  params: Array<FunctionTypeParam>;\n  rest?: FunctionTypeParam | null;\n  returnType: FlowType;\n  this?: FunctionTypeParam | null;\n}\n\nexport interface FunctionTypeParam extends BaseNode {\n  type: \"FunctionTypeParam\";\n  name?: Identifier | null;\n  typeAnnotation: FlowType;\n  optional?: boolean | null;\n}\n\nexport interface GenericTypeAnnotation extends BaseNode {\n  type: \"GenericTypeAnnotation\";\n  id: Identifier | QualifiedTypeIdentifier;\n  typeParameters?: TypeParameterInstantiation | null;\n}\n\nexport interface InferredPredicate extends BaseNode {\n  type: \"InferredPredicate\";\n}\n\nexport interface InterfaceExtends extends BaseNode {\n  type: \"InterfaceExtends\";\n  id: Identifier | QualifiedTypeIdentifier;\n  typeParameters?: TypeParameterInstantiation | null;\n}\n\nexport interface InterfaceDeclaration extends BaseNode {\n  type: \"InterfaceDeclaration\";\n  id: Identifier;\n  typeParameters?: TypeParameterDeclaration | null;\n  extends?: Array<InterfaceExtends> | null;\n  body: ObjectTypeAnnotation;\n}\n\nexport interface InterfaceTypeAnnotation extends BaseNode {\n  type: \"InterfaceTypeAnnotation\";\n  extends?: Array<InterfaceExtends> | null;\n  body: ObjectTypeAnnotation;\n}\n\nexport interface IntersectionTypeAnnotation extends BaseNode {\n  type: \"IntersectionTypeAnnotation\";\n  types: Array<FlowType>;\n}\n\nexport interface MixedTypeAnnotation extends BaseNode {\n  type: \"MixedTypeAnnotation\";\n}\n\nexport interface EmptyTypeAnnotation extends BaseNode {\n  type: \"EmptyTypeAnnotation\";\n}\n\nexport interface NullableTypeAnnotation extends BaseNode {\n  type: \"NullableTypeAnnotation\";\n  typeAnnotation: FlowType;\n}\n\nexport interface NumberLiteralTypeAnnotation extends BaseNode {\n  type: \"NumberLiteralTypeAnnotation\";\n  value: number;\n}\n\nexport interface NumberTypeAnnotation extends BaseNode {\n  type: \"NumberTypeAnnotation\";\n}\n\nexport interface ObjectTypeAnnotation extends BaseNode {\n  type: \"ObjectTypeAnnotation\";\n  properties: Array<ObjectTypeProperty | ObjectTypeSpreadProperty>;\n  indexers?: Array<ObjectTypeIndexer>;\n  callProperties?: Array<ObjectTypeCallProperty>;\n  internalSlots?: Array<ObjectTypeInternalSlot>;\n  exact: boolean;\n  inexact?: boolean | null;\n}\n\nexport interface ObjectTypeInternalSlot extends BaseNode {\n  type: \"ObjectTypeInternalSlot\";\n  id: Identifier;\n  value: FlowType;\n  optional: boolean;\n  static: boolean;\n  method: boolean;\n}\n\nexport interface ObjectTypeCallProperty extends BaseNode {\n  type: \"ObjectTypeCallProperty\";\n  value: FlowType;\n  static: boolean;\n}\n\nexport interface ObjectTypeIndexer extends BaseNode {\n  type: \"ObjectTypeIndexer\";\n  id?: Identifier | null;\n  key: FlowType;\n  value: FlowType;\n  variance?: Variance | null;\n  static: boolean;\n}\n\nexport interface ObjectTypeProperty extends BaseNode {\n  type: \"ObjectTypeProperty\";\n  key: Identifier | StringLiteral;\n  value: FlowType;\n  variance?: Variance | null;\n  kind: \"init\" | \"get\" | \"set\";\n  method: boolean;\n  optional: boolean;\n  proto: boolean;\n  static: boolean;\n}\n\nexport interface ObjectTypeSpreadProperty extends BaseNode {\n  type: \"ObjectTypeSpreadProperty\";\n  argument: FlowType;\n}\n\nexport interface OpaqueType extends BaseNode {\n  type: \"OpaqueType\";\n  id: Identifier;\n  typeParameters?: TypeParameterDeclaration | null;\n  supertype?: FlowType | null;\n  impltype: FlowType;\n}\n\nexport interface QualifiedTypeIdentifier extends BaseNode {\n  type: \"QualifiedTypeIdentifier\";\n  id: Identifier;\n  qualification: Identifier | QualifiedTypeIdentifier;\n}\n\nexport interface StringLiteralTypeAnnotation extends BaseNode {\n  type: \"StringLiteralTypeAnnotation\";\n  value: string;\n}\n\nexport interface StringTypeAnnotation extends BaseNode {\n  type: \"StringTypeAnnotation\";\n}\n\nexport interface SymbolTypeAnnotation extends BaseNode {\n  type: \"SymbolTypeAnnotation\";\n}\n\nexport interface ThisTypeAnnotation extends BaseNode {\n  type: \"ThisTypeAnnotation\";\n}\n\nexport interface TupleTypeAnnotation extends BaseNode {\n  type: \"TupleTypeAnnotation\";\n  types: Array<FlowType>;\n}\n\nexport interface TypeofTypeAnnotation extends BaseNode {\n  type: \"TypeofTypeAnnotation\";\n  argument: FlowType;\n}\n\nexport interface TypeAlias extends BaseNode {\n  type: \"TypeAlias\";\n  id: Identifier;\n  typeParameters?: TypeParameterDeclaration | null;\n  right: FlowType;\n}\n\nexport interface TypeAnnotation extends BaseNode {\n  type: \"TypeAnnotation\";\n  typeAnnotation: FlowType;\n}\n\nexport interface TypeCastExpression extends BaseNode {\n  type: \"TypeCastExpression\";\n  expression: Expression;\n  typeAnnotation: TypeAnnotation;\n}\n\nexport interface TypeParameter extends BaseNode {\n  type: \"TypeParameter\";\n  bound?: TypeAnnotation | null;\n  default?: FlowType | null;\n  variance?: Variance | null;\n  name: string;\n}\n\nexport interface TypeParameterDeclaration extends BaseNode {\n  type: \"TypeParameterDeclaration\";\n  params: Array<TypeParameter>;\n}\n\nexport interface TypeParameterInstantiation extends BaseNode {\n  type: \"TypeParameterInstantiation\";\n  params: Array<FlowType>;\n}\n\nexport interface UnionTypeAnnotation extends BaseNode {\n  type: \"UnionTypeAnnotation\";\n  types: Array<FlowType>;\n}\n\nexport interface Variance extends BaseNode {\n  type: \"Variance\";\n  kind: \"minus\" | \"plus\";\n}\n\nexport interface VoidTypeAnnotation extends BaseNode {\n  type: \"VoidTypeAnnotation\";\n}\n\nexport interface EnumDeclaration extends BaseNode {\n  type: \"EnumDeclaration\";\n  id: Identifier;\n  body: EnumBooleanBody | EnumNumberBody | EnumStringBody | EnumSymbolBody;\n}\n\nexport interface EnumBooleanBody extends BaseNode {\n  type: \"EnumBooleanBody\";\n  members: Array<EnumBooleanMember>;\n  explicitType: boolean;\n  hasUnknownMembers: boolean;\n}\n\nexport interface EnumNumberBody extends BaseNode {\n  type: \"EnumNumberBody\";\n  members: Array<EnumNumberMember>;\n  explicitType: boolean;\n  hasUnknownMembers: boolean;\n}\n\nexport interface EnumStringBody extends BaseNode {\n  type: \"EnumStringBody\";\n  members: Array<EnumStringMember | EnumDefaultedMember>;\n  explicitType: boolean;\n  hasUnknownMembers: boolean;\n}\n\nexport interface EnumSymbolBody extends BaseNode {\n  type: \"EnumSymbolBody\";\n  members: Array<EnumDefaultedMember>;\n  hasUnknownMembers: boolean;\n}\n\nexport interface EnumBooleanMember extends BaseNode {\n  type: \"EnumBooleanMember\";\n  id: Identifier;\n  init: BooleanLiteral;\n}\n\nexport interface EnumNumberMember extends BaseNode {\n  type: \"EnumNumberMember\";\n  id: Identifier;\n  init: NumericLiteral;\n}\n\nexport interface EnumStringMember extends BaseNode {\n  type: \"EnumStringMember\";\n  id: Identifier;\n  init: StringLiteral;\n}\n\nexport interface EnumDefaultedMember extends BaseNode {\n  type: \"EnumDefaultedMember\";\n  id: Identifier;\n}\n\nexport interface IndexedAccessType extends BaseNode {\n  type: \"IndexedAccessType\";\n  objectType: FlowType;\n  indexType: FlowType;\n}\n\nexport interface OptionalIndexedAccessType extends BaseNode {\n  type: \"OptionalIndexedAccessType\";\n  objectType: FlowType;\n  indexType: FlowType;\n  optional: boolean;\n}\n\nexport interface JSXAttribute extends BaseNode {\n  type: \"JSXAttribute\";\n  name: JSXIdentifier | JSXNamespacedName;\n  value?:\n    | JSXElement\n    | JSXFragment\n    | StringLiteral\n    | JSXExpressionContainer\n    | null;\n}\n\nexport interface JSXClosingElement extends BaseNode {\n  type: \"JSXClosingElement\";\n  name: JSXIdentifier | JSXMemberExpression | JSXNamespacedName;\n}\n\nexport interface JSXElement extends BaseNode {\n  type: \"JSXElement\";\n  openingElement: JSXOpeningElement;\n  closingElement?: JSXClosingElement | null;\n  children: Array<\n    JSXText | JSXExpressionContainer | JSXSpreadChild | JSXElement | JSXFragment\n  >;\n  selfClosing?: boolean | null;\n}\n\nexport interface JSXEmptyExpression extends BaseNode {\n  type: \"JSXEmptyExpression\";\n}\n\nexport interface JSXExpressionContainer extends BaseNode {\n  type: \"JSXExpressionContainer\";\n  expression: Expression | JSXEmptyExpression;\n}\n\nexport interface JSXSpreadChild extends BaseNode {\n  type: \"JSXSpreadChild\";\n  expression: Expression;\n}\n\nexport interface JSXIdentifier extends BaseNode {\n  type: \"JSXIdentifier\";\n  name: string;\n}\n\nexport interface JSXMemberExpression extends BaseNode {\n  type: \"JSXMemberExpression\";\n  object: JSXMemberExpression | JSXIdentifier;\n  property: JSXIdentifier;\n}\n\nexport interface JSXNamespacedName extends BaseNode {\n  type: \"JSXNamespacedName\";\n  namespace: JSXIdentifier;\n  name: JSXIdentifier;\n}\n\nexport interface JSXOpeningElement extends BaseNode {\n  type: \"JSXOpeningElement\";\n  name: JSXIdentifier | JSXMemberExpression | JSXNamespacedName;\n  attributes: Array<JSXAttribute | JSXSpreadAttribute>;\n  selfClosing: boolean;\n  typeParameters?:\n    | TypeParameterInstantiation\n    | TSTypeParameterInstantiation\n    | null;\n}\n\nexport interface JSXSpreadAttribute extends BaseNode {\n  type: \"JSXSpreadAttribute\";\n  argument: Expression;\n}\n\nexport interface JSXText extends BaseNode {\n  type: \"JSXText\";\n  value: string;\n}\n\nexport interface JSXFragment extends BaseNode {\n  type: \"JSXFragment\";\n  openingFragment: JSXOpeningFragment;\n  closingFragment: JSXClosingFragment;\n  children: Array<\n    JSXText | JSXExpressionContainer | JSXSpreadChild | JSXElement | JSXFragment\n  >;\n}\n\nexport interface JSXOpeningFragment extends BaseNode {\n  type: \"JSXOpeningFragment\";\n}\n\nexport interface JSXClosingFragment extends BaseNode {\n  type: \"JSXClosingFragment\";\n}\n\nexport interface Noop extends BaseNode {\n  type: \"Noop\";\n}\n\nexport interface Placeholder extends BaseNode {\n  type: \"Placeholder\";\n  expectedNode:\n    | \"Identifier\"\n    | \"StringLiteral\"\n    | \"Expression\"\n    | \"Statement\"\n    | \"Declaration\"\n    | \"BlockStatement\"\n    | \"ClassBody\"\n    | \"Pattern\";\n  name: Identifier;\n}\n\nexport interface V8IntrinsicIdentifier extends BaseNode {\n  type: \"V8IntrinsicIdentifier\";\n  name: string;\n}\n\nexport interface ArgumentPlaceholder extends BaseNode {\n  type: \"ArgumentPlaceholder\";\n}\n\nexport interface BindExpression extends BaseNode {\n  type: \"BindExpression\";\n  object: Expression;\n  callee: Expression;\n}\n\nexport interface ImportAttribute extends BaseNode {\n  type: \"ImportAttribute\";\n  key: Identifier | StringLiteral;\n  value: StringLiteral;\n}\n\nexport interface Decorator extends BaseNode {\n  type: \"Decorator\";\n  expression: Expression;\n}\n\nexport interface DoExpression extends BaseNode {\n  type: \"DoExpression\";\n  body: BlockStatement;\n  async: boolean;\n}\n\nexport interface ExportDefaultSpecifier extends BaseNode {\n  type: \"ExportDefaultSpecifier\";\n  exported: Identifier;\n}\n\nexport interface RecordExpression extends BaseNode {\n  type: \"RecordExpression\";\n  properties: Array<ObjectProperty | SpreadElement>;\n}\n\nexport interface TupleExpression extends BaseNode {\n  type: \"TupleExpression\";\n  elements: Array<Expression | SpreadElement>;\n}\n\nexport interface DecimalLiteral extends BaseNode {\n  type: \"DecimalLiteral\";\n  value: string;\n}\n\nexport interface ModuleExpression extends BaseNode {\n  type: \"ModuleExpression\";\n  body: Program;\n}\n\nexport interface TopicReference extends BaseNode {\n  type: \"TopicReference\";\n}\n\nexport interface PipelineTopicExpression extends BaseNode {\n  type: \"PipelineTopicExpression\";\n  expression: Expression;\n}\n\nexport interface PipelineBareFunction extends BaseNode {\n  type: \"PipelineBareFunction\";\n  callee: Expression;\n}\n\nexport interface PipelinePrimaryTopicReference extends BaseNode {\n  type: \"PipelinePrimaryTopicReference\";\n}\n\nexport interface TSParameterProperty extends BaseNode {\n  type: \"TSParameterProperty\";\n  parameter: Identifier | AssignmentPattern;\n  accessibility?: \"public\" | \"private\" | \"protected\" | null;\n  decorators?: Array<Decorator> | null;\n  override?: boolean | null;\n  readonly?: boolean | null;\n}\n\nexport interface TSDeclareFunction extends BaseNode {\n  type: \"TSDeclareFunction\";\n  id?: Identifier | null;\n  typeParameters?: TSTypeParameterDeclaration | Noop | null;\n  params: Array<Identifier | Pattern | RestElement>;\n  returnType?: TSTypeAnnotation | Noop | null;\n  async?: boolean;\n  declare?: boolean | null;\n  generator?: boolean;\n}\n\nexport interface TSDeclareMethod extends BaseNode {\n  type: \"TSDeclareMethod\";\n  decorators?: Array<Decorator> | null;\n  key: Identifier | StringLiteral | NumericLiteral | BigIntLiteral | Expression;\n  typeParameters?: TSTypeParameterDeclaration | Noop | null;\n  params: Array<Identifier | Pattern | RestElement | TSParameterProperty>;\n  returnType?: TSTypeAnnotation | Noop | null;\n  abstract?: boolean | null;\n  access?: \"public\" | \"private\" | \"protected\" | null;\n  accessibility?: \"public\" | \"private\" | \"protected\" | null;\n  async?: boolean;\n  computed?: boolean;\n  generator?: boolean;\n  kind?: \"get\" | \"set\" | \"method\" | \"constructor\";\n  optional?: boolean | null;\n  override?: boolean;\n  static?: boolean;\n}\n\nexport interface TSQualifiedName extends BaseNode {\n  type: \"TSQualifiedName\";\n  left: TSEntityName;\n  right: Identifier;\n}\n\nexport interface TSCallSignatureDeclaration extends BaseNode {\n  type: \"TSCallSignatureDeclaration\";\n  typeParameters?: TSTypeParameterDeclaration | null;\n  parameters: Array<Identifier | RestElement>;\n  typeAnnotation?: TSTypeAnnotation | null;\n}\n\nexport interface TSConstructSignatureDeclaration extends BaseNode {\n  type: \"TSConstructSignatureDeclaration\";\n  typeParameters?: TSTypeParameterDeclaration | null;\n  parameters: Array<Identifier | RestElement>;\n  typeAnnotation?: TSTypeAnnotation | null;\n}\n\nexport interface TSPropertySignature extends BaseNode {\n  type: \"TSPropertySignature\";\n  key: Expression;\n  typeAnnotation?: TSTypeAnnotation | null;\n  initializer?: Expression | null;\n  computed?: boolean;\n  kind: \"get\" | \"set\";\n  optional?: boolean | null;\n  readonly?: boolean | null;\n}\n\nexport interface TSMethodSignature extends BaseNode {\n  type: \"TSMethodSignature\";\n  key: Expression;\n  typeParameters?: TSTypeParameterDeclaration | null;\n  parameters: Array<Identifier | RestElement>;\n  typeAnnotation?: TSTypeAnnotation | null;\n  computed?: boolean;\n  kind: \"method\" | \"get\" | \"set\";\n  optional?: boolean | null;\n}\n\nexport interface TSIndexSignature extends BaseNode {\n  type: \"TSIndexSignature\";\n  parameters: Array<Identifier>;\n  typeAnnotation?: TSTypeAnnotation | null;\n  readonly?: boolean | null;\n  static?: boolean | null;\n}\n\nexport interface TSAnyKeyword extends BaseNode {\n  type: \"TSAnyKeyword\";\n}\n\nexport interface TSBooleanKeyword extends BaseNode {\n  type: \"TSBooleanKeyword\";\n}\n\nexport interface TSBigIntKeyword extends BaseNode {\n  type: \"TSBigIntKeyword\";\n}\n\nexport interface TSIntrinsicKeyword extends BaseNode {\n  type: \"TSIntrinsicKeyword\";\n}\n\nexport interface TSNeverKeyword extends BaseNode {\n  type: \"TSNeverKeyword\";\n}\n\nexport interface TSNullKeyword extends BaseNode {\n  type: \"TSNullKeyword\";\n}\n\nexport interface TSNumberKeyword extends BaseNode {\n  type: \"TSNumberKeyword\";\n}\n\nexport interface TSObjectKeyword extends BaseNode {\n  type: \"TSObjectKeyword\";\n}\n\nexport interface TSStringKeyword extends BaseNode {\n  type: \"TSStringKeyword\";\n}\n\nexport interface TSSymbolKeyword extends BaseNode {\n  type: \"TSSymbolKeyword\";\n}\n\nexport interface TSUndefinedKeyword extends BaseNode {\n  type: \"TSUndefinedKeyword\";\n}\n\nexport interface TSUnknownKeyword extends BaseNode {\n  type: \"TSUnknownKeyword\";\n}\n\nexport interface TSVoidKeyword extends BaseNode {\n  type: \"TSVoidKeyword\";\n}\n\nexport interface TSThisType extends BaseNode {\n  type: \"TSThisType\";\n}\n\nexport interface TSFunctionType extends BaseNode {\n  type: \"TSFunctionType\";\n  typeParameters?: TSTypeParameterDeclaration | null;\n  parameters: Array<Identifier | RestElement>;\n  typeAnnotation?: TSTypeAnnotation | null;\n}\n\nexport interface TSConstructorType extends BaseNode {\n  type: \"TSConstructorType\";\n  typeParameters?: TSTypeParameterDeclaration | null;\n  parameters: Array<Identifier | RestElement>;\n  typeAnnotation?: TSTypeAnnotation | null;\n  abstract?: boolean | null;\n}\n\nexport interface TSTypeReference extends BaseNode {\n  type: \"TSTypeReference\";\n  typeName: TSEntityName;\n  typeParameters?: TSTypeParameterInstantiation | null;\n}\n\nexport interface TSTypePredicate extends BaseNode {\n  type: \"TSTypePredicate\";\n  parameterName: Identifier | TSThisType;\n  typeAnnotation?: TSTypeAnnotation | null;\n  asserts?: boolean | null;\n}\n\nexport interface TSTypeQuery extends BaseNode {\n  type: \"TSTypeQuery\";\n  exprName: TSEntityName | TSImportType;\n  typeParameters?: TSTypeParameterInstantiation | null;\n}\n\nexport interface TSTypeLiteral extends BaseNode {\n  type: \"TSTypeLiteral\";\n  members: Array<TSTypeElement>;\n}\n\nexport interface TSArrayType extends BaseNode {\n  type: \"TSArrayType\";\n  elementType: TSType;\n}\n\nexport interface TSTupleType extends BaseNode {\n  type: \"TSTupleType\";\n  elementTypes: Array<TSType | TSNamedTupleMember>;\n}\n\nexport interface TSOptionalType extends BaseNode {\n  type: \"TSOptionalType\";\n  typeAnnotation: TSType;\n}\n\nexport interface TSRestType extends BaseNode {\n  type: \"TSRestType\";\n  typeAnnotation: TSType;\n}\n\nexport interface TSNamedTupleMember extends BaseNode {\n  type: \"TSNamedTupleMember\";\n  label: Identifier;\n  elementType: TSType;\n  optional: boolean;\n}\n\nexport interface TSUnionType extends BaseNode {\n  type: \"TSUnionType\";\n  types: Array<TSType>;\n}\n\nexport interface TSIntersectionType extends BaseNode {\n  type: \"TSIntersectionType\";\n  types: Array<TSType>;\n}\n\nexport interface TSConditionalType extends BaseNode {\n  type: \"TSConditionalType\";\n  checkType: TSType;\n  extendsType: TSType;\n  trueType: TSType;\n  falseType: TSType;\n}\n\nexport interface TSInferType extends BaseNode {\n  type: \"TSInferType\";\n  typeParameter: TSTypeParameter;\n}\n\nexport interface TSParenthesizedType extends BaseNode {\n  type: \"TSParenthesizedType\";\n  typeAnnotation: TSType;\n}\n\nexport interface TSTypeOperator extends BaseNode {\n  type: \"TSTypeOperator\";\n  typeAnnotation: TSType;\n  operator: string;\n}\n\nexport interface TSIndexedAccessType extends BaseNode {\n  type: \"TSIndexedAccessType\";\n  objectType: TSType;\n  indexType: TSType;\n}\n\nexport interface TSMappedType extends BaseNode {\n  type: \"TSMappedType\";\n  typeParameter: TSTypeParameter;\n  typeAnnotation?: TSType | null;\n  nameType?: TSType | null;\n  optional?: true | false | \"+\" | \"-\" | null;\n  readonly?: true | false | \"+\" | \"-\" | null;\n}\n\nexport interface TSLiteralType extends BaseNode {\n  type: \"TSLiteralType\";\n  literal:\n    | NumericLiteral\n    | StringLiteral\n    | BooleanLiteral\n    | BigIntLiteral\n    | TemplateLiteral\n    | UnaryExpression;\n}\n\nexport interface TSExpressionWithTypeArguments extends BaseNode {\n  type: \"TSExpressionWithTypeArguments\";\n  expression: TSEntityName;\n  typeParameters?: TSTypeParameterInstantiation | null;\n}\n\nexport interface TSInterfaceDeclaration extends BaseNode {\n  type: \"TSInterfaceDeclaration\";\n  id: Identifier;\n  typeParameters?: TSTypeParameterDeclaration | null;\n  extends?: Array<TSExpressionWithTypeArguments> | null;\n  body: TSInterfaceBody;\n  declare?: boolean | null;\n}\n\nexport interface TSInterfaceBody extends BaseNode {\n  type: \"TSInterfaceBody\";\n  body: Array<TSTypeElement>;\n}\n\nexport interface TSTypeAliasDeclaration extends BaseNode {\n  type: \"TSTypeAliasDeclaration\";\n  id: Identifier;\n  typeParameters?: TSTypeParameterDeclaration | null;\n  typeAnnotation: TSType;\n  declare?: boolean | null;\n}\n\nexport interface TSInstantiationExpression extends BaseNode {\n  type: \"TSInstantiationExpression\";\n  expression: Expression;\n  typeParameters?: TSTypeParameterInstantiation | null;\n}\n\nexport interface TSAsExpression extends BaseNode {\n  type: \"TSAsExpression\";\n  expression: Expression;\n  typeAnnotation: TSType;\n}\n\nexport interface TSSatisfiesExpression extends BaseNode {\n  type: \"TSSatisfiesExpression\";\n  expression: Expression;\n  typeAnnotation: TSType;\n}\n\nexport interface TSTypeAssertion extends BaseNode {\n  type: \"TSTypeAssertion\";\n  typeAnnotation: TSType;\n  expression: Expression;\n}\n\nexport interface TSEnumDeclaration extends BaseNode {\n  type: \"TSEnumDeclaration\";\n  id: Identifier;\n  members: Array<TSEnumMember>;\n  const?: boolean | null;\n  declare?: boolean | null;\n  initializer?: Expression | null;\n}\n\nexport interface TSEnumMember extends BaseNode {\n  type: \"TSEnumMember\";\n  id: Identifier | StringLiteral;\n  initializer?: Expression | null;\n}\n\nexport interface TSModuleDeclaration extends BaseNode {\n  type: \"TSModuleDeclaration\";\n  id: Identifier | StringLiteral;\n  body: TSModuleBlock | TSModuleDeclaration;\n  declare?: boolean | null;\n  global?: boolean | null;\n}\n\nexport interface TSModuleBlock extends BaseNode {\n  type: \"TSModuleBlock\";\n  body: Array<Statement>;\n}\n\nexport interface TSImportType extends BaseNode {\n  type: \"TSImportType\";\n  argument: StringLiteral;\n  qualifier?: TSEntityName | null;\n  typeParameters?: TSTypeParameterInstantiation | null;\n}\n\nexport interface TSImportEqualsDeclaration extends BaseNode {\n  type: \"TSImportEqualsDeclaration\";\n  id: Identifier;\n  moduleReference: TSEntityName | TSExternalModuleReference;\n  importKind?: \"type\" | \"value\" | null;\n  isExport: boolean;\n}\n\nexport interface TSExternalModuleReference extends BaseNode {\n  type: \"TSExternalModuleReference\";\n  expression: StringLiteral;\n}\n\nexport interface TSNonNullExpression extends BaseNode {\n  type: \"TSNonNullExpression\";\n  expression: Expression;\n}\n\nexport interface TSExportAssignment extends BaseNode {\n  type: \"TSExportAssignment\";\n  expression: Expression;\n}\n\nexport interface TSNamespaceExportDeclaration extends BaseNode {\n  type: \"TSNamespaceExportDeclaration\";\n  id: Identifier;\n}\n\nexport interface TSTypeAnnotation extends BaseNode {\n  type: \"TSTypeAnnotation\";\n  typeAnnotation: TSType;\n}\n\nexport interface TSTypeParameterInstantiation extends BaseNode {\n  type: \"TSTypeParameterInstantiation\";\n  params: Array<TSType>;\n}\n\nexport interface TSTypeParameterDeclaration extends BaseNode {\n  type: \"TSTypeParameterDeclaration\";\n  params: Array<TSTypeParameter>;\n}\n\nexport interface TSTypeParameter extends BaseNode {\n  type: \"TSTypeParameter\";\n  constraint?: TSType | null;\n  default?: TSType | null;\n  name: string;\n  const?: boolean | null;\n  in?: boolean | null;\n  out?: boolean | null;\n}\n\nexport type Standardized =\n  | ArrayExpression\n  | AssignmentExpression\n  | BinaryExpression\n  | InterpreterDirective\n  | Directive\n  | DirectiveLiteral\n  | BlockStatement\n  | BreakStatement\n  | CallExpression\n  | CatchClause\n  | ConditionalExpression\n  | ContinueStatement\n  | DebuggerStatement\n  | DoWhileStatement\n  | EmptyStatement\n  | ExpressionStatement\n  | File\n  | ForInStatement\n  | ForStatement\n  | FunctionDeclaration\n  | FunctionExpression\n  | Identifier\n  | IfStatement\n  | LabeledStatement\n  | StringLiteral\n  | NumericLiteral\n  | NullLiteral\n  | BooleanLiteral\n  | RegExpLiteral\n  | LogicalExpression\n  | MemberExpression\n  | NewExpression\n  | Program\n  | ObjectExpression\n  | ObjectMethod\n  | ObjectProperty\n  | RestElement\n  | ReturnStatement\n  | SequenceExpression\n  | ParenthesizedExpression\n  | SwitchCase\n  | SwitchStatement\n  | ThisExpression\n  | ThrowStatement\n  | TryStatement\n  | UnaryExpression\n  | UpdateExpression\n  | VariableDeclaration\n  | VariableDeclarator\n  | WhileStatement\n  | WithStatement\n  | AssignmentPattern\n  | ArrayPattern\n  | ArrowFunctionExpression\n  | ClassBody\n  | ClassExpression\n  | ClassDeclaration\n  | ExportAllDeclaration\n  | ExportDefaultDeclaration\n  | ExportNamedDeclaration\n  | ExportSpecifier\n  | ForOfStatement\n  | ImportDeclaration\n  | ImportDefaultSpecifier\n  | ImportNamespaceSpecifier\n  | ImportSpecifier\n  | MetaProperty\n  | ClassMethod\n  | ObjectPattern\n  | SpreadElement\n  | Super\n  | TaggedTemplateExpression\n  | TemplateElement\n  | TemplateLiteral\n  | YieldExpression\n  | AwaitExpression\n  | Import\n  | BigIntLiteral\n  | ExportNamespaceSpecifier\n  | OptionalMemberExpression\n  | OptionalCallExpression\n  | ClassProperty\n  | ClassAccessorProperty\n  | ClassPrivateProperty\n  | ClassPrivateMethod\n  | PrivateName\n  | StaticBlock;\nexport type Expression =\n  | ArrayExpression\n  | AssignmentExpression\n  | BinaryExpression\n  | CallExpression\n  | ConditionalExpression\n  | FunctionExpression\n  | Identifier\n  | StringLiteral\n  | NumericLiteral\n  | NullLiteral\n  | BooleanLiteral\n  | RegExpLiteral\n  | LogicalExpression\n  | MemberExpression\n  | NewExpression\n  | ObjectExpression\n  | SequenceExpression\n  | ParenthesizedExpression\n  | ThisExpression\n  | UnaryExpression\n  | UpdateExpression\n  | ArrowFunctionExpression\n  | ClassExpression\n  | MetaProperty\n  | Super\n  | TaggedTemplateExpression\n  | TemplateLiteral\n  | YieldExpression\n  | AwaitExpression\n  | Import\n  | BigIntLiteral\n  | OptionalMemberExpression\n  | OptionalCallExpression\n  | TypeCastExpression\n  | JSXElement\n  | JSXFragment\n  | BindExpression\n  | DoExpression\n  | RecordExpression\n  | TupleExpression\n  | DecimalLiteral\n  | ModuleExpression\n  | TopicReference\n  | PipelineTopicExpression\n  | PipelineBareFunction\n  | PipelinePrimaryTopicReference\n  | TSInstantiationExpression\n  | TSAsExpression\n  | TSSatisfiesExpression\n  | TSTypeAssertion\n  | TSNonNullExpression;\nexport type Binary = BinaryExpression | LogicalExpression;\nexport type Scopable =\n  | BlockStatement\n  | CatchClause\n  | DoWhileStatement\n  | ForInStatement\n  | ForStatement\n  | FunctionDeclaration\n  | FunctionExpression\n  | Program\n  | ObjectMethod\n  | SwitchStatement\n  | WhileStatement\n  | ArrowFunctionExpression\n  | ClassExpression\n  | ClassDeclaration\n  | ForOfStatement\n  | ClassMethod\n  | ClassPrivateMethod\n  | StaticBlock\n  | TSModuleBlock;\nexport type BlockParent =\n  | BlockStatement\n  | CatchClause\n  | DoWhileStatement\n  | ForInStatement\n  | ForStatement\n  | FunctionDeclaration\n  | FunctionExpression\n  | Program\n  | ObjectMethod\n  | SwitchStatement\n  | WhileStatement\n  | ArrowFunctionExpression\n  | ForOfStatement\n  | ClassMethod\n  | ClassPrivateMethod\n  | StaticBlock\n  | TSModuleBlock;\nexport type Block = BlockStatement | Program | TSModuleBlock;\nexport type Statement =\n  | BlockStatement\n  | BreakStatement\n  | ContinueStatement\n  | DebuggerStatement\n  | DoWhileStatement\n  | EmptyStatement\n  | ExpressionStatement\n  | ForInStatement\n  | ForStatement\n  | FunctionDeclaration\n  | IfStatement\n  | LabeledStatement\n  | ReturnStatement\n  | SwitchStatement\n  | ThrowStatement\n  | TryStatement\n  | VariableDeclaration\n  | WhileStatement\n  | WithStatement\n  | ClassDeclaration\n  | ExportAllDeclaration\n  | ExportDefaultDeclaration\n  | ExportNamedDeclaration\n  | ForOfStatement\n  | ImportDeclaration\n  | DeclareClass\n  | DeclareFunction\n  | DeclareInterface\n  | DeclareModule\n  | DeclareModuleExports\n  | DeclareTypeAlias\n  | DeclareOpaqueType\n  | DeclareVariable\n  | DeclareExportDeclaration\n  | DeclareExportAllDeclaration\n  | InterfaceDeclaration\n  | OpaqueType\n  | TypeAlias\n  | EnumDeclaration\n  | TSDeclareFunction\n  | TSInterfaceDeclaration\n  | TSTypeAliasDeclaration\n  | TSEnumDeclaration\n  | TSModuleDeclaration\n  | TSImportEqualsDeclaration\n  | TSExportAssignment\n  | TSNamespaceExportDeclaration;\nexport type Terminatorless =\n  | BreakStatement\n  | ContinueStatement\n  | ReturnStatement\n  | ThrowStatement\n  | YieldExpression\n  | AwaitExpression;\nexport type CompletionStatement =\n  | BreakStatement\n  | ContinueStatement\n  | ReturnStatement\n  | ThrowStatement;\nexport type Conditional = ConditionalExpression | IfStatement;\nexport type Loop =\n  | DoWhileStatement\n  | ForInStatement\n  | ForStatement\n  | WhileStatement\n  | ForOfStatement;\nexport type While = DoWhileStatement | WhileStatement;\nexport type ExpressionWrapper =\n  | ExpressionStatement\n  | ParenthesizedExpression\n  | TypeCastExpression;\nexport type For = ForInStatement | ForStatement | ForOfStatement;\nexport type ForXStatement = ForInStatement | ForOfStatement;\nexport type Function =\n  | FunctionDeclaration\n  | FunctionExpression\n  | ObjectMethod\n  | ArrowFunctionExpression\n  | ClassMethod\n  | ClassPrivateMethod;\nexport type FunctionParent =\n  | FunctionDeclaration\n  | FunctionExpression\n  | ObjectMethod\n  | ArrowFunctionExpression\n  | ClassMethod\n  | ClassPrivateMethod\n  | StaticBlock\n  | TSModuleBlock;\nexport type Pureish =\n  | FunctionDeclaration\n  | FunctionExpression\n  | StringLiteral\n  | NumericLiteral\n  | NullLiteral\n  | BooleanLiteral\n  | RegExpLiteral\n  | ArrowFunctionExpression\n  | BigIntLiteral\n  | DecimalLiteral;\nexport type Declaration =\n  | FunctionDeclaration\n  | VariableDeclaration\n  | ClassDeclaration\n  | ExportAllDeclaration\n  | ExportDefaultDeclaration\n  | ExportNamedDeclaration\n  | ImportDeclaration\n  | DeclareClass\n  | DeclareFunction\n  | DeclareInterface\n  | DeclareModule\n  | DeclareModuleExports\n  | DeclareTypeAlias\n  | DeclareOpaqueType\n  | DeclareVariable\n  | DeclareExportDeclaration\n  | DeclareExportAllDeclaration\n  | InterfaceDeclaration\n  | OpaqueType\n  | TypeAlias\n  | EnumDeclaration\n  | TSDeclareFunction\n  | TSInterfaceDeclaration\n  | TSTypeAliasDeclaration\n  | TSEnumDeclaration\n  | TSModuleDeclaration;\nexport type PatternLike =\n  | Identifier\n  | RestElement\n  | AssignmentPattern\n  | ArrayPattern\n  | ObjectPattern\n  | TSAsExpression\n  | TSSatisfiesExpression\n  | TSTypeAssertion\n  | TSNonNullExpression;\nexport type LVal =\n  | Identifier\n  | MemberExpression\n  | RestElement\n  | AssignmentPattern\n  | ArrayPattern\n  | ObjectPattern\n  | TSParameterProperty\n  | TSAsExpression\n  | TSSatisfiesExpression\n  | TSTypeAssertion\n  | TSNonNullExpression;\nexport type TSEntityName = Identifier | TSQualifiedName;\nexport type Literal =\n  | StringLiteral\n  | NumericLiteral\n  | NullLiteral\n  | BooleanLiteral\n  | RegExpLiteral\n  | TemplateLiteral\n  | BigIntLiteral\n  | DecimalLiteral;\nexport type Immutable =\n  | StringLiteral\n  | NumericLiteral\n  | NullLiteral\n  | BooleanLiteral\n  | BigIntLiteral\n  | JSXAttribute\n  | JSXClosingElement\n  | JSXElement\n  | JSXExpressionContainer\n  | JSXSpreadChild\n  | JSXOpeningElement\n  | JSXText\n  | JSXFragment\n  | JSXOpeningFragment\n  | JSXClosingFragment\n  | DecimalLiteral;\nexport type UserWhitespacable =\n  | ObjectMethod\n  | ObjectProperty\n  | ObjectTypeInternalSlot\n  | ObjectTypeCallProperty\n  | ObjectTypeIndexer\n  | ObjectTypeProperty\n  | ObjectTypeSpreadProperty;\nexport type Method = ObjectMethod | ClassMethod | ClassPrivateMethod;\nexport type ObjectMember = ObjectMethod | ObjectProperty;\nexport type Property =\n  | ObjectProperty\n  | ClassProperty\n  | ClassAccessorProperty\n  | ClassPrivateProperty;\nexport type UnaryLike = UnaryExpression | SpreadElement;\nexport type Pattern = AssignmentPattern | ArrayPattern | ObjectPattern;\nexport type Class = ClassExpression | ClassDeclaration;\nexport type ImportOrExportDeclaration =\n  | ExportAllDeclaration\n  | ExportDefaultDeclaration\n  | ExportNamedDeclaration\n  | ImportDeclaration;\nexport type ExportDeclaration =\n  | ExportAllDeclaration\n  | ExportDefaultDeclaration\n  | ExportNamedDeclaration;\nexport type ModuleSpecifier =\n  | ExportSpecifier\n  | ImportDefaultSpecifier\n  | ImportNamespaceSpecifier\n  | ImportSpecifier\n  | ExportNamespaceSpecifier\n  | ExportDefaultSpecifier;\nexport type Accessor = ClassAccessorProperty;\nexport type Private = ClassPrivateProperty | ClassPrivateMethod | PrivateName;\nexport type Flow =\n  | AnyTypeAnnotation\n  | ArrayTypeAnnotation\n  | BooleanTypeAnnotation\n  | BooleanLiteralTypeAnnotation\n  | NullLiteralTypeAnnotation\n  | ClassImplements\n  | DeclareClass\n  | DeclareFunction\n  | DeclareInterface\n  | DeclareModule\n  | DeclareModuleExports\n  | DeclareTypeAlias\n  | DeclareOpaqueType\n  | DeclareVariable\n  | DeclareExportDeclaration\n  | DeclareExportAllDeclaration\n  | DeclaredPredicate\n  | ExistsTypeAnnotation\n  | FunctionTypeAnnotation\n  | FunctionTypeParam\n  | GenericTypeAnnotation\n  | InferredPredicate\n  | InterfaceExtends\n  | InterfaceDeclaration\n  | InterfaceTypeAnnotation\n  | IntersectionTypeAnnotation\n  | MixedTypeAnnotation\n  | EmptyTypeAnnotation\n  | NullableTypeAnnotation\n  | NumberLiteralTypeAnnotation\n  | NumberTypeAnnotation\n  | ObjectTypeAnnotation\n  | ObjectTypeInternalSlot\n  | ObjectTypeCallProperty\n  | ObjectTypeIndexer\n  | ObjectTypeProperty\n  | ObjectTypeSpreadProperty\n  | OpaqueType\n  | QualifiedTypeIdentifier\n  | StringLiteralTypeAnnotation\n  | StringTypeAnnotation\n  | SymbolTypeAnnotation\n  | ThisTypeAnnotation\n  | TupleTypeAnnotation\n  | TypeofTypeAnnotation\n  | TypeAlias\n  | TypeAnnotation\n  | TypeCastExpression\n  | TypeParameter\n  | TypeParameterDeclaration\n  | TypeParameterInstantiation\n  | UnionTypeAnnotation\n  | Variance\n  | VoidTypeAnnotation\n  | EnumDeclaration\n  | EnumBooleanBody\n  | EnumNumberBody\n  | EnumStringBody\n  | EnumSymbolBody\n  | EnumBooleanMember\n  | EnumNumberMember\n  | EnumStringMember\n  | EnumDefaultedMember\n  | IndexedAccessType\n  | OptionalIndexedAccessType;\nexport type FlowType =\n  | AnyTypeAnnotation\n  | ArrayTypeAnnotation\n  | BooleanTypeAnnotation\n  | BooleanLiteralTypeAnnotation\n  | NullLiteralTypeAnnotation\n  | ExistsTypeAnnotation\n  | FunctionTypeAnnotation\n  | GenericTypeAnnotation\n  | InterfaceTypeAnnotation\n  | IntersectionTypeAnnotation\n  | MixedTypeAnnotation\n  | EmptyTypeAnnotation\n  | NullableTypeAnnotation\n  | NumberLiteralTypeAnnotation\n  | NumberTypeAnnotation\n  | ObjectTypeAnnotation\n  | StringLiteralTypeAnnotation\n  | StringTypeAnnotation\n  | SymbolTypeAnnotation\n  | ThisTypeAnnotation\n  | TupleTypeAnnotation\n  | TypeofTypeAnnotation\n  | UnionTypeAnnotation\n  | VoidTypeAnnotation\n  | IndexedAccessType\n  | OptionalIndexedAccessType;\nexport type FlowBaseAnnotation =\n  | AnyTypeAnnotation\n  | BooleanTypeAnnotation\n  | NullLiteralTypeAnnotation\n  | MixedTypeAnnotation\n  | EmptyTypeAnnotation\n  | NumberTypeAnnotation\n  | StringTypeAnnotation\n  | SymbolTypeAnnotation\n  | ThisTypeAnnotation\n  | VoidTypeAnnotation;\nexport type FlowDeclaration =\n  | DeclareClass\n  | DeclareFunction\n  | DeclareInterface\n  | DeclareModule\n  | DeclareModuleExports\n  | DeclareTypeAlias\n  | DeclareOpaqueType\n  | DeclareVariable\n  | DeclareExportDeclaration\n  | DeclareExportAllDeclaration\n  | InterfaceDeclaration\n  | OpaqueType\n  | TypeAlias;\nexport type FlowPredicate = DeclaredPredicate | InferredPredicate;\nexport type EnumBody =\n  | EnumBooleanBody\n  | EnumNumberBody\n  | EnumStringBody\n  | EnumSymbolBody;\nexport type EnumMember =\n  | EnumBooleanMember\n  | EnumNumberMember\n  | EnumStringMember\n  | EnumDefaultedMember;\nexport type JSX =\n  | JSXAttribute\n  | JSXClosingElement\n  | JSXElement\n  | JSXEmptyExpression\n  | JSXExpressionContainer\n  | JSXSpreadChild\n  | JSXIdentifier\n  | JSXMemberExpression\n  | JSXNamespacedName\n  | JSXOpeningElement\n  | JSXSpreadAttribute\n  | JSXText\n  | JSXFragment\n  | JSXOpeningFragment\n  | JSXClosingFragment;\nexport type Miscellaneous = Noop | Placeholder | V8IntrinsicIdentifier;\nexport type TypeScript =\n  | TSParameterProperty\n  | TSDeclareFunction\n  | TSDeclareMethod\n  | TSQualifiedName\n  | TSCallSignatureDeclaration\n  | TSConstructSignatureDeclaration\n  | TSPropertySignature\n  | TSMethodSignature\n  | TSIndexSignature\n  | TSAnyKeyword\n  | TSBooleanKeyword\n  | TSBigIntKeyword\n  | TSIntrinsicKeyword\n  | TSNeverKeyword\n  | TSNullKeyword\n  | TSNumberKeyword\n  | TSObjectKeyword\n  | TSStringKeyword\n  | TSSymbolKeyword\n  | TSUndefinedKeyword\n  | TSUnknownKeyword\n  | TSVoidKeyword\n  | TSThisType\n  | TSFunctionType\n  | TSConstructorType\n  | TSTypeReference\n  | TSTypePredicate\n  | TSTypeQuery\n  | TSTypeLiteral\n  | TSArrayType\n  | TSTupleType\n  | TSOptionalType\n  | TSRestType\n  | TSNamedTupleMember\n  | TSUnionType\n  | TSIntersectionType\n  | TSConditionalType\n  | TSInferType\n  | TSParenthesizedType\n  | TSTypeOperator\n  | TSIndexedAccessType\n  | TSMappedType\n  | TSLiteralType\n  | TSExpressionWithTypeArguments\n  | TSInterfaceDeclaration\n  | TSInterfaceBody\n  | TSTypeAliasDeclaration\n  | TSInstantiationExpression\n  | TSAsExpression\n  | TSSatisfiesExpression\n  | TSTypeAssertion\n  | TSEnumDeclaration\n  | TSEnumMember\n  | TSModuleDeclaration\n  | TSModuleBlock\n  | TSImportType\n  | TSImportEqualsDeclaration\n  | TSExternalModuleReference\n  | TSNonNullExpression\n  | TSExportAssignment\n  | TSNamespaceExportDeclaration\n  | TSTypeAnnotation\n  | TSTypeParameterInstantiation\n  | TSTypeParameterDeclaration\n  | TSTypeParameter;\nexport type TSTypeElement =\n  | TSCallSignatureDeclaration\n  | TSConstructSignatureDeclaration\n  | TSPropertySignature\n  | TSMethodSignature\n  | TSIndexSignature;\nexport type TSType =\n  | TSAnyKeyword\n  | TSBooleanKeyword\n  | TSBigIntKeyword\n  | TSIntrinsicKeyword\n  | TSNeverKeyword\n  | TSNullKeyword\n  | TSNumberKeyword\n  | TSObjectKeyword\n  | TSStringKeyword\n  | TSSymbolKeyword\n  | TSUndefinedKeyword\n  | TSUnknownKeyword\n  | TSVoidKeyword\n  | TSThisType\n  | TSFunctionType\n  | TSConstructorType\n  | TSTypeReference\n  | TSTypePredicate\n  | TSTypeQuery\n  | TSTypeLiteral\n  | TSArrayType\n  | TSTupleType\n  | TSOptionalType\n  | TSRestType\n  | TSUnionType\n  | TSIntersectionType\n  | TSConditionalType\n  | TSInferType\n  | TSParenthesizedType\n  | TSTypeOperator\n  | TSIndexedAccessType\n  | TSMappedType\n  | TSLiteralType\n  | TSExpressionWithTypeArguments\n  | TSImportType;\nexport type TSBaseType =\n  | TSAnyKeyword\n  | TSBooleanKeyword\n  | TSBigIntKeyword\n  | TSIntrinsicKeyword\n  | TSNeverKeyword\n  | TSNullKeyword\n  | TSNumberKeyword\n  | TSObjectKeyword\n  | TSStringKeyword\n  | TSSymbolKeyword\n  | TSUndefinedKeyword\n  | TSUnknownKeyword\n  | TSVoidKeyword\n  | TSThisType\n  | TSLiteralType;\nexport type ModuleDeclaration =\n  | ExportAllDeclaration\n  | ExportDefaultDeclaration\n  | ExportNamedDeclaration\n  | ImportDeclaration;\n\nexport interface Aliases {\n  Standardized: Standardized;\n  Expression: Expression;\n  Binary: Binary;\n  Scopable: Scopable;\n  BlockParent: BlockParent;\n  Block: Block;\n  Statement: Statement;\n  Terminatorless: Terminatorless;\n  CompletionStatement: CompletionStatement;\n  Conditional: Conditional;\n  Loop: Loop;\n  While: While;\n  ExpressionWrapper: ExpressionWrapper;\n  For: For;\n  ForXStatement: ForXStatement;\n  Function: Function;\n  FunctionParent: FunctionParent;\n  Pureish: Pureish;\n  Declaration: Declaration;\n  PatternLike: PatternLike;\n  LVal: LVal;\n  TSEntityName: TSEntityName;\n  Literal: Literal;\n  Immutable: Immutable;\n  UserWhitespacable: UserWhitespacable;\n  Method: Method;\n  ObjectMember: ObjectMember;\n  Property: Property;\n  UnaryLike: UnaryLike;\n  Pattern: Pattern;\n  Class: Class;\n  ImportOrExportDeclaration: ImportOrExportDeclaration;\n  ExportDeclaration: ExportDeclaration;\n  ModuleSpecifier: ModuleSpecifier;\n  Accessor: Accessor;\n  Private: Private;\n  Flow: Flow;\n  FlowType: FlowType;\n  FlowBaseAnnotation: FlowBaseAnnotation;\n  FlowDeclaration: FlowDeclaration;\n  FlowPredicate: FlowPredicate;\n  EnumBody: EnumBody;\n  EnumMember: EnumMember;\n  JSX: JSX;\n  Miscellaneous: Miscellaneous;\n  TypeScript: TypeScript;\n  TSTypeElement: TSTypeElement;\n  TSType: TSType;\n  TSBaseType: TSBaseType;\n  ModuleDeclaration: ModuleDeclaration;\n}\n\nexport type DeprecatedAliases =\n  | NumberLiteral\n  | RegexLiteral\n  | RestProperty\n  | SpreadProperty;\n\nexport interface ParentMaps {\n  AnyTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  ArgumentPlaceholder: CallExpression | NewExpression | OptionalCallExpression;\n  ArrayExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  ArrayPattern:\n    | ArrayPattern\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | CatchClause\n    | ClassMethod\n    | ClassPrivateMethod\n    | ForInStatement\n    | ForOfStatement\n    | FunctionDeclaration\n    | FunctionExpression\n    | ObjectMethod\n    | ObjectProperty\n    | RestElement\n    | TSDeclareFunction\n    | TSDeclareMethod\n    | VariableDeclarator;\n  ArrayTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  ArrowFunctionExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  AssignmentExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  AssignmentPattern:\n    | ArrayPattern\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | ClassMethod\n    | ClassPrivateMethod\n    | ForInStatement\n    | ForOfStatement\n    | FunctionDeclaration\n    | FunctionExpression\n    | ObjectMethod\n    | ObjectProperty\n    | RestElement\n    | TSDeclareFunction\n    | TSDeclareMethod\n    | TSParameterProperty\n    | VariableDeclarator;\n  AwaitExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  BigIntLiteral:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSLiteralType\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  BinaryExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  BindExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  BlockStatement:\n    | ArrowFunctionExpression\n    | BlockStatement\n    | CatchClause\n    | ClassMethod\n    | ClassPrivateMethod\n    | DeclareModule\n    | DoExpression\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | FunctionDeclaration\n    | FunctionExpression\n    | IfStatement\n    | LabeledStatement\n    | ObjectMethod\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | TryStatement\n    | WhileStatement\n    | WithStatement;\n  BooleanLiteral:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | EnumBooleanMember\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSLiteralType\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  BooleanLiteralTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  BooleanTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  BreakStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  CallExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  CatchClause: TryStatement;\n  ClassAccessorProperty: ClassBody;\n  ClassBody: ClassDeclaration | ClassExpression;\n  ClassDeclaration:\n    | BlockStatement\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  ClassExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  ClassImplements:\n    | ClassDeclaration\n    | ClassExpression\n    | DeclareClass\n    | DeclareExportDeclaration\n    | DeclaredPredicate;\n  ClassMethod: ClassBody;\n  ClassPrivateMethod: ClassBody;\n  ClassPrivateProperty: ClassBody;\n  ClassProperty: ClassBody;\n  CommentBlock: File;\n  CommentLine: File;\n  ConditionalExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  ContinueStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  DebuggerStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  DecimalLiteral:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  DeclareClass:\n    | BlockStatement\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  DeclareExportAllDeclaration:\n    | BlockStatement\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  DeclareExportDeclaration:\n    | BlockStatement\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  DeclareFunction:\n    | BlockStatement\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  DeclareInterface:\n    | BlockStatement\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  DeclareModule:\n    | BlockStatement\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  DeclareModuleExports:\n    | BlockStatement\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  DeclareOpaqueType:\n    | BlockStatement\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  DeclareTypeAlias:\n    | BlockStatement\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  DeclareVariable:\n    | BlockStatement\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  DeclaredPredicate:\n    | ArrowFunctionExpression\n    | DeclareExportDeclaration\n    | DeclareFunction\n    | DeclaredPredicate\n    | FunctionDeclaration\n    | FunctionExpression;\n  Decorator:\n    | ArrayPattern\n    | AssignmentPattern\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | Identifier\n    | ObjectMethod\n    | ObjectPattern\n    | ObjectProperty\n    | RestElement\n    | TSDeclareMethod\n    | TSParameterProperty;\n  Directive: BlockStatement | Program;\n  DirectiveLiteral: Directive;\n  DoExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  DoWhileStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  EmptyStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  EmptyTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  EnumBooleanBody:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | EnumDeclaration;\n  EnumBooleanMember:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | EnumBooleanBody;\n  EnumDeclaration:\n    | BlockStatement\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  EnumDefaultedMember:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | EnumStringBody\n    | EnumSymbolBody;\n  EnumNumberBody:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | EnumDeclaration;\n  EnumNumberMember:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | EnumNumberBody;\n  EnumStringBody:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | EnumDeclaration;\n  EnumStringMember:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | EnumStringBody;\n  EnumSymbolBody:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | EnumDeclaration;\n  ExistsTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  ExportAllDeclaration:\n    | BlockStatement\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  ExportDefaultDeclaration:\n    | BlockStatement\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  ExportDefaultSpecifier: ExportNamedDeclaration;\n  ExportNamedDeclaration:\n    | BlockStatement\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  ExportNamespaceSpecifier: DeclareExportDeclaration | ExportNamedDeclaration;\n  ExportSpecifier: DeclareExportDeclaration | ExportNamedDeclaration;\n  ExpressionStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  File: null;\n  ForInStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  ForOfStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  ForStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  FunctionDeclaration:\n    | BlockStatement\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  FunctionExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  FunctionTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  FunctionTypeParam:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | FunctionTypeAnnotation;\n  GenericTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  Identifier:\n    | ArrayExpression\n    | ArrayPattern\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | BreakStatement\n    | CallExpression\n    | CatchClause\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassImplements\n    | ClassMethod\n    | ClassPrivateMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | ContinueStatement\n    | DeclareClass\n    | DeclareFunction\n    | DeclareInterface\n    | DeclareModule\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclareVariable\n    | Decorator\n    | DoWhileStatement\n    | EnumBooleanMember\n    | EnumDeclaration\n    | EnumDefaultedMember\n    | EnumNumberMember\n    | EnumStringMember\n    | ExportDefaultDeclaration\n    | ExportDefaultSpecifier\n    | ExportNamespaceSpecifier\n    | ExportSpecifier\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | FunctionDeclaration\n    | FunctionExpression\n    | FunctionTypeParam\n    | GenericTypeAnnotation\n    | IfStatement\n    | ImportAttribute\n    | ImportDefaultSpecifier\n    | ImportNamespaceSpecifier\n    | ImportSpecifier\n    | InterfaceDeclaration\n    | InterfaceExtends\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LabeledStatement\n    | LogicalExpression\n    | MemberExpression\n    | MetaProperty\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | OpaqueType\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | Placeholder\n    | PrivateName\n    | QualifiedTypeIdentifier\n    | RestElement\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSCallSignatureDeclaration\n    | TSConstructSignatureDeclaration\n    | TSConstructorType\n    | TSDeclareFunction\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSExpressionWithTypeArguments\n    | TSFunctionType\n    | TSImportEqualsDeclaration\n    | TSImportType\n    | TSIndexSignature\n    | TSInstantiationExpression\n    | TSInterfaceDeclaration\n    | TSMethodSignature\n    | TSModuleDeclaration\n    | TSNamedTupleMember\n    | TSNamespaceExportDeclaration\n    | TSNonNullExpression\n    | TSParameterProperty\n    | TSPropertySignature\n    | TSQualifiedName\n    | TSSatisfiesExpression\n    | TSTypeAliasDeclaration\n    | TSTypeAssertion\n    | TSTypePredicate\n    | TSTypeQuery\n    | TSTypeReference\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeAlias\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  IfStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  Import:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  ImportAttribute:\n    | ExportAllDeclaration\n    | ExportNamedDeclaration\n    | ImportDeclaration;\n  ImportDeclaration:\n    | BlockStatement\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  ImportDefaultSpecifier: ImportDeclaration;\n  ImportNamespaceSpecifier: ImportDeclaration;\n  ImportSpecifier: ImportDeclaration;\n  IndexedAccessType:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  InferredPredicate:\n    | ArrowFunctionExpression\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | FunctionDeclaration\n    | FunctionExpression;\n  InterfaceDeclaration:\n    | BlockStatement\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  InterfaceExtends:\n    | ClassDeclaration\n    | ClassExpression\n    | DeclareClass\n    | DeclareExportDeclaration\n    | DeclareInterface\n    | DeclaredPredicate\n    | InterfaceDeclaration\n    | InterfaceTypeAnnotation;\n  InterfaceTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  InterpreterDirective: Program;\n  IntersectionTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  JSXAttribute: JSXOpeningElement;\n  JSXClosingElement: JSXElement;\n  JSXClosingFragment: JSXFragment;\n  JSXElement:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXAttribute\n    | JSXElement\n    | JSXExpressionContainer\n    | JSXFragment\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  JSXEmptyExpression: JSXExpressionContainer;\n  JSXExpressionContainer: JSXAttribute | JSXElement | JSXFragment;\n  JSXFragment:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXAttribute\n    | JSXElement\n    | JSXExpressionContainer\n    | JSXFragment\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  JSXIdentifier:\n    | JSXAttribute\n    | JSXClosingElement\n    | JSXMemberExpression\n    | JSXNamespacedName\n    | JSXOpeningElement;\n  JSXMemberExpression:\n    | JSXClosingElement\n    | JSXMemberExpression\n    | JSXOpeningElement;\n  JSXNamespacedName:\n    | CallExpression\n    | JSXAttribute\n    | JSXClosingElement\n    | JSXOpeningElement\n    | NewExpression\n    | OptionalCallExpression;\n  JSXOpeningElement: JSXElement;\n  JSXOpeningFragment: JSXFragment;\n  JSXSpreadAttribute: JSXOpeningElement;\n  JSXSpreadChild: JSXElement | JSXFragment;\n  JSXText: JSXElement | JSXFragment;\n  LabeledStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  LogicalExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  MemberExpression:\n    | ArrayExpression\n    | ArrayPattern\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | RestElement\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  MetaProperty:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  MixedTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  ModuleExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  NewExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  Noop:\n    | ArrayPattern\n    | ArrowFunctionExpression\n    | AssignmentPattern\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | FunctionDeclaration\n    | FunctionExpression\n    | Identifier\n    | ObjectMethod\n    | ObjectPattern\n    | RestElement\n    | TSDeclareFunction\n    | TSDeclareMethod;\n  NullLiteral:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  NullLiteralTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  NullableTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  NumberLiteral: null;\n  NumberLiteralTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  NumberTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  NumericLiteral:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | EnumNumberMember\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSLiteralType\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  ObjectExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  ObjectMethod: ObjectExpression;\n  ObjectPattern:\n    | ArrayPattern\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | CatchClause\n    | ClassMethod\n    | ClassPrivateMethod\n    | ForInStatement\n    | ForOfStatement\n    | FunctionDeclaration\n    | FunctionExpression\n    | ObjectMethod\n    | ObjectProperty\n    | RestElement\n    | TSDeclareFunction\n    | TSDeclareMethod\n    | VariableDeclarator;\n  ObjectProperty: ObjectExpression | ObjectPattern | RecordExpression;\n  ObjectTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareClass\n    | DeclareExportDeclaration\n    | DeclareInterface\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | InterfaceDeclaration\n    | InterfaceTypeAnnotation\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  ObjectTypeCallProperty:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | ObjectTypeAnnotation;\n  ObjectTypeIndexer:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | ObjectTypeAnnotation;\n  ObjectTypeInternalSlot:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | ObjectTypeAnnotation;\n  ObjectTypeProperty:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | ObjectTypeAnnotation;\n  ObjectTypeSpreadProperty:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | ObjectTypeAnnotation;\n  OpaqueType:\n    | BlockStatement\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  OptionalCallExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  OptionalIndexedAccessType:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  OptionalMemberExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  ParenthesizedExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  PipelineBareFunction:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  PipelinePrimaryTopicReference:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  PipelineTopicExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  Placeholder: Node;\n  PrivateName:\n    | BinaryExpression\n    | ClassAccessorProperty\n    | ClassPrivateMethod\n    | ClassPrivateProperty\n    | MemberExpression\n    | ObjectProperty;\n  Program: File | ModuleExpression;\n  QualifiedTypeIdentifier:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | GenericTypeAnnotation\n    | InterfaceExtends\n    | QualifiedTypeIdentifier;\n  RecordExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  RegExpLiteral:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  RegexLiteral: null;\n  RestElement:\n    | ArrayPattern\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | ClassMethod\n    | ClassPrivateMethod\n    | ForInStatement\n    | ForOfStatement\n    | FunctionDeclaration\n    | FunctionExpression\n    | ObjectMethod\n    | ObjectPattern\n    | ObjectProperty\n    | RestElement\n    | TSCallSignatureDeclaration\n    | TSConstructSignatureDeclaration\n    | TSConstructorType\n    | TSDeclareFunction\n    | TSDeclareMethod\n    | TSFunctionType\n    | TSMethodSignature\n    | VariableDeclarator;\n  RestProperty: null;\n  ReturnStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  SequenceExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  SpreadElement:\n    | ArrayExpression\n    | CallExpression\n    | NewExpression\n    | ObjectExpression\n    | OptionalCallExpression\n    | RecordExpression\n    | TupleExpression;\n  SpreadProperty: null;\n  StaticBlock: ClassBody;\n  StringLiteral:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | DeclareExportAllDeclaration\n    | DeclareExportDeclaration\n    | DeclareModule\n    | Decorator\n    | DoWhileStatement\n    | EnumStringMember\n    | ExportAllDeclaration\n    | ExportDefaultDeclaration\n    | ExportNamedDeclaration\n    | ExportSpecifier\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | ImportAttribute\n    | ImportDeclaration\n    | ImportSpecifier\n    | JSXAttribute\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | ObjectTypeProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSExternalModuleReference\n    | TSImportType\n    | TSInstantiationExpression\n    | TSLiteralType\n    | TSMethodSignature\n    | TSModuleDeclaration\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  StringLiteralTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  StringTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  Super:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  SwitchCase: SwitchStatement;\n  SwitchStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  SymbolTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  TSAnyKeyword:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSArrayType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSAsExpression:\n    | ArrayExpression\n    | ArrayPattern\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | RestElement\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  TSBigIntKeyword:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSBooleanKeyword:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSCallSignatureDeclaration: TSInterfaceBody | TSTypeLiteral;\n  TSConditionalType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSConstructSignatureDeclaration: TSInterfaceBody | TSTypeLiteral;\n  TSConstructorType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSDeclareFunction:\n    | BlockStatement\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  TSDeclareMethod: ClassBody;\n  TSEnumDeclaration:\n    | BlockStatement\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  TSEnumMember: TSEnumDeclaration;\n  TSExportAssignment:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  TSExpressionWithTypeArguments:\n    | ClassDeclaration\n    | ClassExpression\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSInterfaceDeclaration\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSExternalModuleReference: TSImportEqualsDeclaration;\n  TSFunctionType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSImportEqualsDeclaration:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  TSImportType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSTypeQuery\n    | TSUnionType\n    | TemplateLiteral;\n  TSIndexSignature: ClassBody | TSInterfaceBody | TSTypeLiteral;\n  TSIndexedAccessType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSInferType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSInstantiationExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  TSInterfaceBody: TSInterfaceDeclaration;\n  TSInterfaceDeclaration:\n    | BlockStatement\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  TSIntersectionType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSIntrinsicKeyword:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSLiteralType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSMappedType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSMethodSignature: TSInterfaceBody | TSTypeLiteral;\n  TSModuleBlock: TSModuleDeclaration;\n  TSModuleDeclaration:\n    | BlockStatement\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | TSModuleDeclaration\n    | WhileStatement\n    | WithStatement;\n  TSNamedTupleMember: TSTupleType;\n  TSNamespaceExportDeclaration:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  TSNeverKeyword:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSNonNullExpression:\n    | ArrayExpression\n    | ArrayPattern\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | RestElement\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  TSNullKeyword:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSNumberKeyword:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSObjectKeyword:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSOptionalType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSParameterProperty:\n    | ArrayPattern\n    | AssignmentExpression\n    | ClassMethod\n    | ClassPrivateMethod\n    | ForInStatement\n    | ForOfStatement\n    | RestElement\n    | TSDeclareMethod\n    | VariableDeclarator;\n  TSParenthesizedType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSPropertySignature: TSInterfaceBody | TSTypeLiteral;\n  TSQualifiedName:\n    | TSExpressionWithTypeArguments\n    | TSImportEqualsDeclaration\n    | TSImportType\n    | TSQualifiedName\n    | TSTypeQuery\n    | TSTypeReference;\n  TSRestType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSSatisfiesExpression:\n    | ArrayExpression\n    | ArrayPattern\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | RestElement\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  TSStringKeyword:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSSymbolKeyword:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSThisType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSTypePredicate\n    | TSUnionType\n    | TemplateLiteral;\n  TSTupleType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSTypeAliasDeclaration:\n    | BlockStatement\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  TSTypeAnnotation:\n    | ArrayPattern\n    | ArrowFunctionExpression\n    | AssignmentPattern\n    | ClassAccessorProperty\n    | ClassMethod\n    | ClassPrivateMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | FunctionDeclaration\n    | FunctionExpression\n    | Identifier\n    | ObjectMethod\n    | ObjectPattern\n    | RestElement\n    | TSCallSignatureDeclaration\n    | TSConstructSignatureDeclaration\n    | TSConstructorType\n    | TSDeclareFunction\n    | TSDeclareMethod\n    | TSFunctionType\n    | TSIndexSignature\n    | TSMethodSignature\n    | TSPropertySignature\n    | TSTypePredicate;\n  TSTypeAssertion:\n    | ArrayExpression\n    | ArrayPattern\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | RestElement\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  TSTypeLiteral:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSTypeOperator:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSTypeParameter: TSInferType | TSMappedType | TSTypeParameterDeclaration;\n  TSTypeParameterDeclaration:\n    | ArrowFunctionExpression\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateMethod\n    | FunctionDeclaration\n    | FunctionExpression\n    | ObjectMethod\n    | TSCallSignatureDeclaration\n    | TSConstructSignatureDeclaration\n    | TSConstructorType\n    | TSDeclareFunction\n    | TSDeclareMethod\n    | TSFunctionType\n    | TSInterfaceDeclaration\n    | TSMethodSignature\n    | TSTypeAliasDeclaration;\n  TSTypeParameterInstantiation:\n    | CallExpression\n    | ClassDeclaration\n    | ClassExpression\n    | JSXOpeningElement\n    | NewExpression\n    | OptionalCallExpression\n    | TSExpressionWithTypeArguments\n    | TSImportType\n    | TSInstantiationExpression\n    | TSTypeQuery\n    | TSTypeReference\n    | TaggedTemplateExpression;\n  TSTypePredicate:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSTypeQuery:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSTypeReference:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSUndefinedKeyword:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSUnionType:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSUnknownKeyword:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TSVoidKeyword:\n    | TSArrayType\n    | TSAsExpression\n    | TSConditionalType\n    | TSIndexedAccessType\n    | TSIntersectionType\n    | TSMappedType\n    | TSNamedTupleMember\n    | TSOptionalType\n    | TSParenthesizedType\n    | TSRestType\n    | TSSatisfiesExpression\n    | TSTupleType\n    | TSTypeAliasDeclaration\n    | TSTypeAnnotation\n    | TSTypeAssertion\n    | TSTypeOperator\n    | TSTypeParameter\n    | TSTypeParameterInstantiation\n    | TSUnionType\n    | TemplateLiteral;\n  TaggedTemplateExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  TemplateElement: TemplateLiteral;\n  TemplateLiteral:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSLiteralType\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  ThisExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  ThisTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  ThrowStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  TopicReference:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  TryStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  TupleExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  TupleTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  TypeAlias:\n    | BlockStatement\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  TypeAnnotation:\n    | ArrayPattern\n    | ArrowFunctionExpression\n    | AssignmentPattern\n    | ClassAccessorProperty\n    | ClassMethod\n    | ClassPrivateMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | DeclareExportDeclaration\n    | DeclareModuleExports\n    | DeclaredPredicate\n    | FunctionDeclaration\n    | FunctionExpression\n    | Identifier\n    | ObjectMethod\n    | ObjectPattern\n    | RestElement\n    | TypeCastExpression\n    | TypeParameter;\n  TypeCastExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  TypeParameter:\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | TypeParameterDeclaration;\n  TypeParameterDeclaration:\n    | ArrowFunctionExpression\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateMethod\n    | DeclareClass\n    | DeclareExportDeclaration\n    | DeclareInterface\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionDeclaration\n    | FunctionExpression\n    | FunctionTypeAnnotation\n    | InterfaceDeclaration\n    | ObjectMethod\n    | OpaqueType\n    | TypeAlias;\n  TypeParameterInstantiation:\n    | CallExpression\n    | ClassDeclaration\n    | ClassExpression\n    | ClassImplements\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | GenericTypeAnnotation\n    | InterfaceExtends\n    | JSXOpeningElement\n    | NewExpression\n    | OptionalCallExpression\n    | TaggedTemplateExpression;\n  TypeofTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  UnaryExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSLiteralType\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  UnionTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  UpdateExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n  V8IntrinsicIdentifier: CallExpression | NewExpression;\n  VariableDeclaration:\n    | BlockStatement\n    | DoWhileStatement\n    | ExportNamedDeclaration\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  VariableDeclarator: VariableDeclaration;\n  Variance:\n    | ClassAccessorProperty\n    | ClassPrivateProperty\n    | ClassProperty\n    | DeclareExportDeclaration\n    | DeclaredPredicate\n    | ObjectTypeIndexer\n    | ObjectTypeProperty\n    | TypeParameter;\n  VoidTypeAnnotation:\n    | ArrayTypeAnnotation\n    | DeclareExportDeclaration\n    | DeclareOpaqueType\n    | DeclareTypeAlias\n    | DeclaredPredicate\n    | FunctionTypeAnnotation\n    | FunctionTypeParam\n    | IndexedAccessType\n    | IntersectionTypeAnnotation\n    | NullableTypeAnnotation\n    | ObjectTypeCallProperty\n    | ObjectTypeIndexer\n    | ObjectTypeInternalSlot\n    | ObjectTypeProperty\n    | ObjectTypeSpreadProperty\n    | OpaqueType\n    | OptionalIndexedAccessType\n    | TupleTypeAnnotation\n    | TypeAlias\n    | TypeAnnotation\n    | TypeParameter\n    | TypeParameterInstantiation\n    | TypeofTypeAnnotation\n    | UnionTypeAnnotation;\n  WhileStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  WithStatement:\n    | BlockStatement\n    | DoWhileStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | LabeledStatement\n    | Program\n    | StaticBlock\n    | SwitchCase\n    | TSModuleBlock\n    | WhileStatement\n    | WithStatement;\n  YieldExpression:\n    | ArrayExpression\n    | ArrowFunctionExpression\n    | AssignmentExpression\n    | AssignmentPattern\n    | AwaitExpression\n    | BinaryExpression\n    | BindExpression\n    | CallExpression\n    | ClassAccessorProperty\n    | ClassDeclaration\n    | ClassExpression\n    | ClassMethod\n    | ClassPrivateProperty\n    | ClassProperty\n    | ConditionalExpression\n    | Decorator\n    | DoWhileStatement\n    | ExportDefaultDeclaration\n    | ExpressionStatement\n    | ForInStatement\n    | ForOfStatement\n    | ForStatement\n    | IfStatement\n    | JSXExpressionContainer\n    | JSXSpreadAttribute\n    | JSXSpreadChild\n    | LogicalExpression\n    | MemberExpression\n    | NewExpression\n    | ObjectMethod\n    | ObjectProperty\n    | OptionalCallExpression\n    | OptionalMemberExpression\n    | ParenthesizedExpression\n    | PipelineBareFunction\n    | PipelineTopicExpression\n    | ReturnStatement\n    | SequenceExpression\n    | SpreadElement\n    | SwitchCase\n    | SwitchStatement\n    | TSAsExpression\n    | TSDeclareMethod\n    | TSEnumDeclaration\n    | TSEnumMember\n    | TSExportAssignment\n    | TSInstantiationExpression\n    | TSMethodSignature\n    | TSNonNullExpression\n    | TSPropertySignature\n    | TSSatisfiesExpression\n    | TSTypeAssertion\n    | TaggedTemplateExpression\n    | TemplateLiteral\n    | ThrowStatement\n    | TupleExpression\n    | TypeCastExpression\n    | UnaryExpression\n    | UpdateExpression\n    | VariableDeclarator\n    | WhileStatement\n    | WithStatement\n    | YieldExpression;\n}\n"], "mappings": ""}