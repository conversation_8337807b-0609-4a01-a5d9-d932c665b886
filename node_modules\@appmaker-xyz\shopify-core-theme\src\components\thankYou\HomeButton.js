import { StyleSheet, Pressable } from 'react-native';
import { ThemeText, Layout } from '@appmaker-xyz/ui';
import React from 'react';

const HomeButton = ({ onAction }) => {
  return (
    <Layout style={styles.buttonContainer}>
      <Pressable
        onPress={() => {
          onAction({
            action: 'GO_TO_HOME',
          });
        }}
        style={styles.button}>
        <ThemeText style={styles.buttonText}>Continue Shopping</ThemeText>
      </Pressable>
    </Layout>
  );
};

export default HomeButton;

const styles = StyleSheet.create({
  buttonContainer: {
    padding: 16,
  },
  button: {
    backgroundColor: '#000',
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
  },
});
