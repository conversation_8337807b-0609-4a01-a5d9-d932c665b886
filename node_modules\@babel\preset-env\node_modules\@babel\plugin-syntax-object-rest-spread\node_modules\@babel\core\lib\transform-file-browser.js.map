{"version": 3, "names": ["transformFile", "filename", "opts", "callback", "Error", "exports", "transformFileSync", "transformFileAsync", "Promise", "reject"], "sources": ["../src/transform-file-browser.ts"], "sourcesContent": ["// duplicated from transform-file so we do not have to import anything here\ntype TransformFile = {\n  (filename: string, callback: (error: Error, file: null) => void): void;\n  (\n    filename: string,\n    opts: any,\n    callback: (error: Error, file: null) => void,\n  ): void;\n};\n\nexport const transformFile: TransformFile = function transformFile(\n  filename,\n  opts,\n  callback?: (error: Error, file: null) => void,\n) {\n  if (typeof opts === \"function\") {\n    callback = opts;\n  }\n\n  callback(new Error(\"Transforming files is not supported in browsers\"), null);\n};\n\nexport function transformFileSync(): never {\n  throw new Error(\"Transforming files is not supported in browsers\");\n}\n\nexport function transformFileAsync() {\n  return Promise.reject(\n    new Error(\"Transforming files is not supported in browsers\"),\n  );\n}\n"], "mappings": ";;;;;;;;AAUO,MAAMA,aAA4B,GAAG,SAASA,aAAaA,CAChEC,QAAQ,EACRC,IAAI,EACJC,QAA6C,EAC7C;EACA,IAAI,OAAOD,IAAI,KAAK,UAAU,EAAE;IAC9BC,QAAQ,GAAGD,IAAI;EACjB;EAEAC,QAAQ,CAAC,IAAIC,KAAK,CAAC,iDAAiD,CAAC,EAAE,IAAI,CAAC;AAC9E,CAAC;AAACC,OAAA,CAAAL,aAAA,GAAAA,aAAA;AAEK,SAASM,iBAAiBA,CAAA,EAAU;EACzC,MAAM,IAAIF,KAAK,CAAC,iDAAiD,CAAC;AACpE;AAEO,SAASG,kBAAkBA,CAAA,EAAG;EACnC,OAAOC,OAAO,CAACC,MAAM,CACnB,IAAIL,KAAK,CAAC,iDAAiD,CAAC,CAC7D;AACH;AAAC"}