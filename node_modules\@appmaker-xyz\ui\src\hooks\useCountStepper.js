import { useImmer } from 'use-immer';

export function useCountStepper({
  min = 1,
  max = 10000000,
  defaultStep = 1,
  initialValue = 1,
  onChange = async () => {},
  incrementFn = async (step) => {},
  decrementFn = async (step) => {},
} = {}) {
  const [countState, updateCountState] = useImmer({
    count: initialValue,
    incrementLoading: false,
    decrementLoading: false,
  });
  const isLoading = countState.incrementLoading || countState.decrementLoading;

  async function increment(step = defaultStep) {
    if (countState.count + step > max || isLoading) {
      return;
    }
    const stepCountNumber = parseInt(step);
    updateCountState((draft) => {
      draft.incrementLoading = true;
    });
    await incrementFn(step);
    updateCountState((draft) => {
      draft.incrementLoading = false;
      draft.count = draft.count + stepCountNumber;
    });
  }

  async function decrement(step = defaultStep) {
    if (countState.count - step < min || isLoading) {
      return;
    }
    const stepCountNumber = parseInt(step);
    updateCountState((draft) => {
      draft.decrementLoading = true;
    });
    await decrementFn(step);
    updateCountState((draft) => {
      draft.decrementLoading = false;
      draft.count = draft.count - stepCountNumber;
    });
  }
  return {
    countState,
    count: countState.count,
    increment,
    decrement,
    isLoading,
  };
}
