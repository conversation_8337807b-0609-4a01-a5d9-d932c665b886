import moment from 'moment';
import React from 'react';
import {Image, Text, View} from 'react-native';
import style from './style';

// interface Props {
//   planIntervel: number;
// }

const Plan = ({planIntervel}) => {
  const secondInstallmentDate = moment()
    .add(planIntervel, 'days')
    .format('Do MMM');
  const thirdInstallmentDate = moment(secondInstallmentDate, 'Do MMM')
    .add(planIntervel, 'days')
    .format('Do MMM');
  const fourthInstallmentDate = moment(thirdInstallmentDate, 'Do MMM')
    .add(planIntervel, 'days')
    .format('Do MMM');

  return (
    <View style={style.mainContainer}>
      <View style={style.horizontalLine} />
      <View style={style.innerContainer}>
        <Image
          style={style.image}
          source={require('../assets/Schedule_circle_1.png')}
        />
        <Text style={style.percent}>25%</Text>
        <Text style={style.text}>today</Text>
      </View>
      <View style={style.innerContainer}>
        <Image
          style={style.image}
          source={require('../assets/Schedule_circle_2.png')}
        />
        <Text style={style.percent}>25%</Text>
        <Text style={style.text}>{secondInstallmentDate}</Text>
      </View>
      <View style={style.innerContainer}>
        <Image
          style={style.image}
          source={require('../assets/Schedule_circle_3.png')}
        />
        <Text style={style.percent}>25%</Text>
        <Text style={style.text}> {thirdInstallmentDate}</Text>
      </View>
      <View style={style.innerContainer}>
        <Image
          style={style.image}
          source={require('../assets/Schedule_circle_4.png')}
        />
        <Text style={style.percent}>25%</Text>
        <Text style={style.text}> {fourthInstallmentDate}</Text>
      </View>
    </View>
  );
};

export default Plan;
