import { appmaker } from '@appmaker-xyz/core';
const values = [
  {
    clientId: 'cusotm-fields-meta-color',
    name: 'appmaker/table-cell',
    attributes: {
      title: 'Color',
      value:
        '{{ blockItem.node.my_fields_color ? blockItem.node.my_fields_color.value : "" }}',
    },
  },
  {
    name: 'appmaker/table-cell',
    attributes: {
      title: 'Work',
      value: '{{  blockItem.node.my_fields_work.value }}',
    },
  },
  {
    name: 'appmaker/table-cell',
    attributes: {
      title: 'Stitch Type',
      value: '{{blockItem.node.my_fields_stitch_type.value}}',
    },
  },
  {
    name: 'appmaker/table-cell',
    attributes: {
      title: 'Occasion',
      value:
        '{{ blockItem.node.my_fields_occasion && blockItem.node.my_fields_occasion.value ? JSON.parse(blockItem.node.my_fields_occasion.value).join(", "):"" }}',
    },
  },
  {
    name: 'appmaker/table-cell',
    attributes: {
      title: 'Print / Pattern',
      value:
        '{{ blockItem.node.my_fields_print_pattern && blockItem.node.my_fields_print_pattern.value ? JSON.parse(blockItem.node.my_fields_print_pattern.value).join(", ") : ""}}',
    },
  },
  {
    name: 'appmaker/table-cell',
    attributes: {
      title: 'Specifications',
      value: '{{blockItem.node.my_fields_specifications.value}}',
    },
  },
  {
    name: 'appmaker/table-cell',
    attributes: {
      title: 'Supplier Sku',
      value: '{{blockItem.node.variants.edges[0].node.sku}}',
    },
  },
  {
    name: 'appmaker/table-cell',
    attributes: {
      title: 'Supplier',
      value: 'Tulsyan Retail Pvt. Ltd Shyam Sangini Market, Surat, Gujarat',
    },
  },
  {
    name: 'appmaker/table-cell',
    attributes: {
      title: 'Country Of Origin',
      value: 'India',
    },
  },
];
const pdpClientId = 'pdp-specifications-extra-fields';
const customblocks = [
  {
    clientId: pdpClientId,
    name: 'appmaker/expandable-text-block',
    isValid: true,
    attributes: {
      expandable: '1',
      expanded: '0',
      content: values
        .map((item) => `${item.attributes.title} : ${item.attributes.value}`)
        .join('\n'),
      accessButton: 'Show/Hide',
      blockTitle: 'Specifications',
      hiddenText: '',
      expandedText: '',
    },
    innerBlocks: [],
  },
  {
    clientId: '3378dc04-62ce-406b-9000-ae5a575f1d2d',
    name: 'appmaker/expandable-text-block',
    isValid: true,
    attributes: {
      expandable: '1',
      expanded: '0',
      content:
        'We have a flat 7 Day Full Refund / Exchange policy. For returns with full money refund or exchange requests you can visit our returns centre or chat with us on Whatsapp or email <NAME_EMAIL>',
      accessButton: 'Show/Hide',
      blockTitle: 'Return policy',
      hiddenText: '',
      expandedText: '',
    },
    innerBlocks: [],
  },
];
//find index of block
function findBlockIndex(blocks, clientId) {
  return blocks.findIndex((block) => block.clientId === clientId);
}

export function addWidgetinProductDetailPage() {
  // Adding extra blocks
  appmaker.addFilter(
    'inapp-page-data-response',
    'recently-viewed-plugin-sub',
    (data, { pageId }) => {
      if (pageId === 'productDetail') {
        const clientId = 'slot-after-description';
        const index = findBlockIndex(data.blocks, clientId);
        const colorBlockIndex = findBlockIndex(data.blocks, pdpClientId);
        if (colorBlockIndex === -1) {
          data.blocks.splice(index + 1, 0, ...customblocks);
        }
      }
      return data;
    },
  );
}
