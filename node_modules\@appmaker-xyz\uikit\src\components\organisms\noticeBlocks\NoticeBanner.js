import React from 'react';
import { StyleSheet } from 'react-native';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { Layout, AppmakerText, Button } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';

const NoticeBanner = ({ attributes, onPress, onAction }) => {
  const {
    message,
    status = 'primary',
    actionButton,
    appmakerAction,
  } = attributes;
  const { color, spacing, font, dimensions } = useApThemeState();
  const styles = allStyles({ color, spacing, dimensions, status });
  function iconName() {
    switch (status) {
      case 'success':
        return 'check-circle';
      case 'warning':
        return 'alert-triangle';
      case 'danger':
        return 'x-circle';
      default:
        return 'info';
    }
  }
  if (!message) {
    return null;
  }
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  return (
    <Layout style={styles.container}>
      <Layout style={styles.iconContainer}>
        <Icon name={iconName()} color={color[status]} size={font.size.lg} />
      </Layout>
      <Layout style={styles.contentContainer}>
        <AppmakerText status="demiDark">{message}</AppmakerText>
      </Layout>
      {actionButton && (
        <Button status="white" onPress={onPressHandle}>
          <AppmakerText status={status} category="smallButtonText">
            {actionButton}
          </AppmakerText>
        </Button>
      )}
    </Layout>
  );
};

const allStyles = ({ color, spacing, dimensions, status }) =>
  StyleSheet.create({
    container: {
      marginBottom: spacing.nano,
      backgroundColor: color.white,
      flexDirection: 'row',
      alignItems: 'center',
      width: dimensions.fullWidth,
    },
    iconContainer: {
      backgroundColor: `${color[status]}1A`,
      width: spacing.lg * 2,
      justifyContent: 'center',
      alignItems: 'center',
      borderLeftColor: color[status],
      borderLeftWidth: spacing.nano,
      height: '100%',
      ...StyleSheet.absoluteFillObject,
    },
    contentContainer: {
      padding: spacing.md,
      flex: 1,
      marginLeft: spacing.xl + spacing.md,
    },
  });

export default NoticeBanner;
