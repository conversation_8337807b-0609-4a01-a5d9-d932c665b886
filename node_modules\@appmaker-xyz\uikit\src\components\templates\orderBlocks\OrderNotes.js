import React, { useState } from 'react';
import {
  StyleSheet,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import { Layout, AppmakerText, Button } from '../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { Modal } from '@appmaker-xyz/uikit/src/index';
import { useEffect } from 'react';

const OrderNotes = ({ onAction, attributes }) => {
  // console.log(onAction);
  // const
  const { color, spacing, font } = useApThemeState();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [orderNote, setOrderNote] = useState('');
  const styles = allStyles({ color, spacing });
  useEffect(() => {
    setOrderNote(attributes.orderNote);
  }, [attributes?.orderNote]);

  let toggle = () => setVisible(!visible);
  async function addNotes() {
    setLoading(true);
    const resp = await onAction({
      action: 'ADD_ORDER_NOTE',
      params: {
        orderNote,
      },
    });
    setLoading(false);
    toggle();
  }

  return (
    <Layout>
      <Layout style={styles.orderNoteContainer}>
        <Layout style={styles.note}>
          <AppmakerText>Order Notes</AppmakerText>
          {orderNote ? (
            <AppmakerText category="bodySubText" numberOfLines={3}>
              {orderNote}
            </AppmakerText>
          ) : null}
        </Layout>
        <Button outline small status="demiDark" onPress={() => toggle()}>
          Add Notes
        </Button>
      </Layout>
      <Modal
        isVisible={visible}
        onSwipeComplete={toggle}
        onBackButtonPress={toggle}
        backdropTransitionOutTiming={0}
        onBackdropPress={toggle}
        style={styles.modal}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{}}>
          <Layout style={styles.modalBody}>
            <Layout style={styles.modalHeader}>
              <AppmakerText category="actionTitle" status="dark">
                Order Notes
              </AppmakerText>
              <Button
                onPress={toggle}
                status="white"
                accessoryLeft={() => <Icon name="x" size={font.size.lg} />}
                small
              />
            </Layout>
            <Layout style={styles.modalContent}>
              <TextInput
                value={orderNote}
                multiline={true}
                numberOfLines={4}
                placeholder="Add your order notes here"
                onChangeText={setOrderNote}
                style={styles.textArea}
              />
              <Button baseSize onPress={() => addNotes()} loading={loading}>
                Save Note
              </Button>
            </Layout>
          </Layout>
        </KeyboardAvoidingView>
      </Modal>
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    orderNoteContainer: {
      padding: spacing.base,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: color.white,
    },
    note: {
      flex: 1,
      marginRight: spacing.nano,
    },
    modal: {
      justifyContent: 'flex-end',
      marginVertical: 0,
      marginHorizontal: 1,
    },
    modalBody: {
      backgroundColor: color.white,
      padding: spacing.md,
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      borderTopStartRadius: spacing.md,
      borderTopEndRadius: spacing.md,
      flexDirection: 'column',
    },
    modalHeader: {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    modalContent: {
      paddingVertical: spacing.base,
      flexDirection: 'column',
      width: '100%',
    },
    textArea: {
      textAlignVertical: 'top',
      borderWidth: 0.5,
      borderColor: color.grey,
      borderRadius: spacing.small,
      marginBottom: spacing.small,
      padding: spacing.small,
      margin: 1,
    },
  });

export default OrderNotes;
