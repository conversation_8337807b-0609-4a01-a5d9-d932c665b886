import { View, Text } from 'react-native';
import React from 'react';
import { useSearchResult } from '../../hooks/search/index';
import { isEmpty } from 'lodash';

export default function SearchResult() {
  const { query, suggestionsResponse = {} } = useSearchResult();
  const { suggestions = [], products = [] } = suggestionsResponse;
  // console.log(suggestionsResponse, 'suggestionsResponse');
  return !isEmpty(query) ? (
    <View>
      <Text>SearchResult for {query}</Text>
      <Text>Products 56</Text>
      {products.map((product) => {
        return <Text>{product.label}</Text>;
      })}
    </View>
  ) : null;
}
