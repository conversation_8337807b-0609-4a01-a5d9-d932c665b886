import React from 'react';
import { getExtensionAsBoolean } from '@appmaker-xyz/core';

const FreeGiftMessageWrapper = (props) => {
  const { BlockItemRender, onAction, blockData } = props;
  const hideMessage = getExtensionAsBoolean?.(
    'appmaker-free-gift',
    'hide_free_gift_cart_message',
    false,
  );
  return hideMessage ? null : (
    <BlockItemRender
      blockData={blockData}
      onAction={onAction}
      block={{
        name: 'shopify/cart-free-gift-notice',
        attributes: {
          themeColor: '#760489',
        },
      }}
    />
  );
};

export default FreeGiftMessageWrapper;
