import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, TextInput, View, I18nManager } from 'react-native';
import { AppmakerText } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { testProps } from '@appmaker-xyz/core';
import { useTranslation } from 'react-i18next';
import { usePageState } from '@appmaker-xyz/core';
import CheckBox from '../coreComponents/CheckBox';

function isEditable(disabled) {
  if (disabled === true || disabled === 'true' || disabled === 'disabled') {
    return false;
  } else {
    return true;
  }
}
const FormCheckboxInputBlock = ({
  attributes,
  coreDispatch,
  clientId,
  onAction,
}) => {
  const {
    name,
    editable: disabled,
    label,
    defaultValue,
    maxLength = 1000,
    autoAction,
    activeColor,
  } = attributes;
  let textMaxCount =
    typeof maxLength === 'string' ? parseInt(maxLength) : maxLength;
  const editable = isEditable(disabled);

  // useEffect(() => {
  //   pageDispatch({
  //     type: 'set_values',
  //     values: { [name]: value },
  //   });
  // }, [value, name]);
  const { t } = useTranslation();
  const { spacing } = useApThemeState();
  const styles = allStyles({ spacing });

  const inputRef = useRef();
  const parentName = attributes.parentName || '_formData';
  function onChangeInput(valueText) {
    coreDispatch({
      type: 'SET_VALUE',
      name,
      value: valueText,
      parent: parentName,
    });
    if (
      autoAction &&
      textMaxCount &&
      valueText &&
      valueText?.length === textMaxCount
    ) {
      coreDispatch &&
        coreDispatch({
          type: 'SET_VALUE',
          name: 'loadingButton',
          value: true,
        });
      autoAction.appmakerAction && onAction(autoAction.appmakerAction);
    }
  }
  const currentFocus = usePageState((state) => state.__currentFocus);

  const inputValue = usePageState((state) => {
    return state[parentName] && state[parentName][name]
      ? state[parentName][name]
      : '';
  });
  useEffect(() => {
    onChangeInput(defaultValue);
  }, [defaultValue]);
  useEffect(() => {
    if (currentFocus === name) {
      inputRef.current.focus();
    }
  }, [currentFocus]);

  return (
    <View style={styles.container}>
      <CheckBox
        testId={attributes?.testId}
        onValueChange={onChangeInput}
        value={inputValue}
        label={label}
        activeColor={activeColor}
      />
    </View>
  );
};

const allStyles = ({ spacing }) =>
  StyleSheet.create({
    container: {
      position: 'relative',
      marginBottom: spacing.base,
    },
  });

export default FormCheckboxInputBlock;
