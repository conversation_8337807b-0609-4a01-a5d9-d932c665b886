{"version": 3, "names": ["_t", "require", "_modules", "_types2", "isDeclareExportDeclaration", "isStatement", "AnyTypeAnnotation", "word", "ArrayTypeAnnotation", "node", "print", "elementType", "token", "BooleanTypeAnnotation", "BooleanLiteralTypeAnnotation", "value", "NullLiteralTypeAnnotation", "DeclareClass", "parent", "space", "_interfaceish", "DeclareFunction", "id", "typeAnnotation", "predicate", "semicolon", "InferredPredicate", "DeclaredPredicate", "DeclareInterface", "InterfaceDeclaration", "DeclareModule", "body", "DeclareModuleExports", "DeclareTypeAlias", "TypeAlias", "DeclareOpaqueType", "OpaqueType", "DeclareVariable", "DeclareExportDeclaration", "default", "FlowExportDeclaration", "call", "DeclareExportAllDeclaration", "ExportAllDeclaration", "EnumDeclaration", "enumExplicitType", "context", "name", "hasExplicitType", "enumBody", "members", "indent", "newline", "member", "hasUnknownMembers", "dedent", "EnumBooleanBody", "explicitType", "EnumNumberBody", "EnumStringBody", "EnumSymbolBody", "EnumDefaultedMember", "enumInitializedMember", "init", "EnumBooleanMember", "EnumNumberMember", "EnumStringMember", "declaration", "declar", "specifiers", "length", "printList", "source", "ExistsTypeAnnotation", "FunctionTypeAnnotation", "typeParameters", "this", "params", "rest", "type", "method", "returnType", "FunctionTypeParam", "optional", "InterfaceExtends", "_node$extends", "extends", "mixins", "implements", "_variance", "variance", "kind", "andSeparator", "InterfaceTypeAnnotation", "IntersectionTypeAnnotation", "printJoin", "types", "separator", "MixedTypeAnnotation", "EmptyTypeAnnotation", "NullableTypeAnnotation", "NumberTypeAnnotation", "StringTypeAnnotation", "ThisTypeAnnotation", "TupleTypeAnnotation", "TypeofTypeAnnotation", "argument", "right", "TypeAnnotation", "TypeParameterInstantiation", "TypeParameter", "bound", "supertype", "impltype", "ObjectTypeAnnotation", "exact", "props", "properties", "callProperties", "indexers", "internalSlots", "addNewlines", "leading", "statement", "iterator", "inexact", "ObjectTypeInternalSlot", "static", "ObjectTypeCallProperty", "ObjectTypeIndexer", "key", "ObjectTypeProperty", "proto", "ObjectTypeSpreadProperty", "QualifiedTypeIdentifier", "qualification", "SymbolTypeAnnotation", "orSeparator", "UnionTypeAnnotation", "TypeCastExpression", "expression", "<PERSON><PERSON><PERSON>", "VoidTypeAnnotation", "IndexedAccessType", "objectType", "indexType", "OptionalIndexedAccessType"], "sources": ["../../src/generators/flow.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport { isDeclareExportDeclaration, isStatement } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport { ExportAllDeclaration } from \"./modules\";\n\nexport function AnyTypeAnnotation(this: Printer) {\n  this.word(\"any\");\n}\n\nexport function ArrayTypeAnnotation(\n  this: Printer,\n  node: t.ArrayTypeAnnotation,\n) {\n  this.print(node.elementType, node, true);\n  this.token(\"[\");\n  this.token(\"]\");\n}\n\nexport function BooleanTypeAnnotation(this: Printer) {\n  this.word(\"boolean\");\n}\n\nexport function BooleanLiteralTypeAnnotation(\n  this: Printer,\n  node: t.BooleanLiteralTypeAnnotation,\n) {\n  this.word(node.value ? \"true\" : \"false\");\n}\n\nexport function NullLiteralTypeAnnotation(this: Printer) {\n  this.word(\"null\");\n}\n\nexport function DeclareClass(\n  this: Printer,\n  node: t.DeclareClass,\n  parent: t.Node,\n) {\n  if (!isDeclareExportDeclaration(parent)) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"class\");\n  this.space();\n  this._interfaceish(node);\n}\n\nexport function DeclareFunction(\n  this: Printer,\n  node: t.DeclareFunction,\n  parent: t.Node,\n) {\n  if (!isDeclareExportDeclaration(parent)) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"function\");\n  this.space();\n  this.print(node.id, node);\n  // @ts-ignore(Babel 7 vs Babel 8) TODO(Babel 8) Remove this comment, since we'll remove the Noop node\n  this.print(node.id.typeAnnotation.typeAnnotation, node);\n\n  if (node.predicate) {\n    this.space();\n    this.print(node.predicate, node);\n  }\n\n  this.semicolon();\n}\n\nexport function InferredPredicate(this: Printer) {\n  this.token(\"%\");\n  this.word(\"checks\");\n}\n\nexport function DeclaredPredicate(this: Printer, node: t.DeclaredPredicate) {\n  this.token(\"%\");\n  this.word(\"checks\");\n  this.token(\"(\");\n  this.print(node.value, node);\n  this.token(\")\");\n}\n\nexport function DeclareInterface(this: Printer, node: t.DeclareInterface) {\n  this.word(\"declare\");\n  this.space();\n  this.InterfaceDeclaration(node);\n}\n\nexport function DeclareModule(this: Printer, node: t.DeclareModule) {\n  this.word(\"declare\");\n  this.space();\n  this.word(\"module\");\n  this.space();\n  this.print(node.id, node);\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function DeclareModuleExports(\n  this: Printer,\n  node: t.DeclareModuleExports,\n) {\n  this.word(\"declare\");\n  this.space();\n  this.word(\"module\");\n  this.token(\".\");\n  this.word(\"exports\");\n  this.print(node.typeAnnotation, node);\n}\n\nexport function DeclareTypeAlias(this: Printer, node: t.DeclareTypeAlias) {\n  this.word(\"declare\");\n  this.space();\n  this.TypeAlias(node);\n}\n\nexport function DeclareOpaqueType(\n  this: Printer,\n  node: t.DeclareOpaqueType,\n  parent: t.Node,\n) {\n  if (!isDeclareExportDeclaration(parent)) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.OpaqueType(node);\n}\n\nexport function DeclareVariable(\n  this: Printer,\n  node: t.DeclareVariable,\n  parent: t.Node,\n) {\n  if (!isDeclareExportDeclaration(parent)) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"var\");\n  this.space();\n  this.print(node.id, node);\n  this.print(node.id.typeAnnotation, node);\n  this.semicolon();\n}\n\nexport function DeclareExportDeclaration(\n  this: Printer,\n  node: t.DeclareExportDeclaration,\n) {\n  this.word(\"declare\");\n  this.space();\n  this.word(\"export\");\n  this.space();\n  if (node.default) {\n    this.word(\"default\");\n    this.space();\n  }\n\n  FlowExportDeclaration.call(this, node);\n}\n\nexport function DeclareExportAllDeclaration(\n  this: Printer,\n  node: t.DeclareExportAllDeclaration,\n) {\n  this.word(\"declare\");\n  this.space();\n  ExportAllDeclaration.call(this, node);\n}\n\nexport function EnumDeclaration(this: Printer, node: t.EnumDeclaration) {\n  const { id, body } = node;\n  this.word(\"enum\");\n  this.space();\n  this.print(id, node);\n  this.print(body, node);\n}\n\nfunction enumExplicitType(\n  context: Printer,\n  name: string,\n  hasExplicitType: boolean,\n) {\n  if (hasExplicitType) {\n    context.space();\n    context.word(\"of\");\n    context.space();\n    context.word(name);\n  }\n  context.space();\n}\n\nfunction enumBody(context: Printer, node: t.EnumBody) {\n  const { members } = node;\n  context.token(\"{\");\n  context.indent();\n  context.newline();\n  for (const member of members) {\n    context.print(member, node);\n    context.newline();\n  }\n  if (node.hasUnknownMembers) {\n    context.token(\"...\");\n    context.newline();\n  }\n  context.dedent();\n  context.token(\"}\");\n}\n\nexport function EnumBooleanBody(this: Printer, node: t.EnumBooleanBody) {\n  const { explicitType } = node;\n  enumExplicitType(this, \"boolean\", explicitType);\n  enumBody(this, node);\n}\n\nexport function EnumNumberBody(this: Printer, node: t.EnumNumberBody) {\n  const { explicitType } = node;\n  enumExplicitType(this, \"number\", explicitType);\n  enumBody(this, node);\n}\n\nexport function EnumStringBody(this: Printer, node: t.EnumStringBody) {\n  const { explicitType } = node;\n  enumExplicitType(this, \"string\", explicitType);\n  enumBody(this, node);\n}\n\nexport function EnumSymbolBody(this: Printer, node: t.EnumSymbolBody) {\n  enumExplicitType(this, \"symbol\", true);\n  enumBody(this, node);\n}\n\nexport function EnumDefaultedMember(\n  this: Printer,\n  node: t.EnumDefaultedMember,\n) {\n  const { id } = node;\n  this.print(id, node);\n  this.token(\",\");\n}\n\nfunction enumInitializedMember(\n  context: Printer,\n  node: t.EnumBooleanMember | t.EnumNumberMember | t.EnumStringMember,\n) {\n  const { id, init } = node;\n  context.print(id, node);\n  context.space();\n  context.token(\"=\");\n  context.space();\n  context.print(init, node);\n  context.token(\",\");\n}\n\nexport function EnumBooleanMember(this: Printer, node: t.EnumBooleanMember) {\n  enumInitializedMember(this, node);\n}\n\nexport function EnumNumberMember(this: Printer, node: t.EnumNumberMember) {\n  enumInitializedMember(this, node);\n}\n\nexport function EnumStringMember(this: Printer, node: t.EnumStringMember) {\n  enumInitializedMember(this, node);\n}\n\nfunction FlowExportDeclaration(\n  this: Printer,\n  node: t.DeclareExportDeclaration,\n) {\n  if (node.declaration) {\n    const declar = node.declaration;\n    this.print(declar, node);\n    if (!isStatement(declar)) this.semicolon();\n  } else {\n    this.token(\"{\");\n    if (node.specifiers.length) {\n      this.space();\n      this.printList(node.specifiers, node);\n      this.space();\n    }\n    this.token(\"}\");\n\n    if (node.source) {\n      this.space();\n      this.word(\"from\");\n      this.space();\n      this.print(node.source, node);\n    }\n\n    this.semicolon();\n  }\n}\n\nexport function ExistsTypeAnnotation(this: Printer) {\n  this.token(\"*\");\n}\n\nexport function FunctionTypeAnnotation(\n  this: Printer,\n  node: t.FunctionTypeAnnotation,\n  parent: t.Node | void,\n) {\n  this.print(node.typeParameters, node);\n  this.token(\"(\");\n\n  if (node.this) {\n    this.word(\"this\");\n    this.token(\":\");\n    this.space();\n    this.print(node.this.typeAnnotation, node);\n    if (node.params.length || node.rest) {\n      this.token(\",\");\n      this.space();\n    }\n  }\n\n  this.printList(node.params, node);\n\n  if (node.rest) {\n    if (node.params.length) {\n      this.token(\",\");\n      this.space();\n    }\n    this.token(\"...\");\n    this.print(node.rest, node);\n  }\n\n  this.token(\")\");\n\n  // this node type is overloaded, not sure why but it makes it EXTREMELY annoying\n  if (\n    parent &&\n    (parent.type === \"ObjectTypeCallProperty\" ||\n      parent.type === \"ObjectTypeInternalSlot\" ||\n      parent.type === \"DeclareFunction\" ||\n      (parent.type === \"ObjectTypeProperty\" && parent.method))\n  ) {\n    this.token(\":\");\n  } else {\n    this.space();\n    this.token(\"=>\");\n  }\n\n  this.space();\n  this.print(node.returnType, node);\n}\n\nexport function FunctionTypeParam(this: Printer, node: t.FunctionTypeParam) {\n  this.print(node.name, node);\n  if (node.optional) this.token(\"?\");\n  if (node.name) {\n    this.token(\":\");\n    this.space();\n  }\n  this.print(node.typeAnnotation, node);\n}\n\nexport function InterfaceExtends(this: Printer, node: t.InterfaceExtends) {\n  this.print(node.id, node);\n  this.print(node.typeParameters, node, true);\n}\n\nexport {\n  InterfaceExtends as ClassImplements,\n  InterfaceExtends as GenericTypeAnnotation,\n};\n\nexport function _interfaceish(\n  this: Printer,\n  node: t.InterfaceDeclaration | t.DeclareInterface | t.DeclareClass,\n) {\n  this.print(node.id, node);\n  this.print(node.typeParameters, node);\n  if (node.extends?.length) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.printList(node.extends, node);\n  }\n  if (node.mixins && node.mixins.length) {\n    this.space();\n    this.word(\"mixins\");\n    this.space();\n    this.printList(node.mixins, node);\n  }\n  if (node.implements && node.implements.length) {\n    this.space();\n    this.word(\"implements\");\n    this.space();\n    this.printList(node.implements, node);\n  }\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function _variance(\n  this: Printer,\n  node:\n    | t.TypeParameter\n    | t.ObjectTypeIndexer\n    | t.ObjectTypeProperty\n    | t.ClassProperty\n    | t.ClassPrivateProperty\n    | t.ClassAccessorProperty,\n) {\n  if (node.variance) {\n    if (node.variance.kind === \"plus\") {\n      this.token(\"+\");\n    } else if (node.variance.kind === \"minus\") {\n      this.token(\"-\");\n    }\n  }\n}\n\nexport function InterfaceDeclaration(\n  this: Printer,\n  node: t.InterfaceDeclaration | t.DeclareInterface,\n) {\n  this.word(\"interface\");\n  this.space();\n  this._interfaceish(node);\n}\n\nfunction andSeparator(this: Printer) {\n  this.space();\n  this.token(\"&\");\n  this.space();\n}\n\nexport function InterfaceTypeAnnotation(\n  this: Printer,\n  node: t.InterfaceTypeAnnotation,\n) {\n  this.word(\"interface\");\n  if (node.extends && node.extends.length) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.printList(node.extends, node);\n  }\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function IntersectionTypeAnnotation(\n  this: Printer,\n  node: t.IntersectionTypeAnnotation,\n) {\n  this.printJoin(node.types, node, { separator: andSeparator });\n}\n\nexport function MixedTypeAnnotation(this: Printer) {\n  this.word(\"mixed\");\n}\n\nexport function EmptyTypeAnnotation(this: Printer) {\n  this.word(\"empty\");\n}\n\nexport function NullableTypeAnnotation(\n  this: Printer,\n  node: t.NullableTypeAnnotation,\n) {\n  this.token(\"?\");\n  this.print(node.typeAnnotation, node);\n}\n\nexport {\n  NumericLiteral as NumberLiteralTypeAnnotation,\n  StringLiteral as StringLiteralTypeAnnotation,\n} from \"./types\";\n\nexport function NumberTypeAnnotation(this: Printer) {\n  this.word(\"number\");\n}\n\nexport function StringTypeAnnotation(this: Printer) {\n  this.word(\"string\");\n}\n\nexport function ThisTypeAnnotation(this: Printer) {\n  this.word(\"this\");\n}\n\nexport function TupleTypeAnnotation(\n  this: Printer,\n  node: t.TupleTypeAnnotation,\n) {\n  this.token(\"[\");\n  this.printList(node.types, node);\n  this.token(\"]\");\n}\n\nexport function TypeofTypeAnnotation(\n  this: Printer,\n  node: t.TypeofTypeAnnotation,\n) {\n  this.word(\"typeof\");\n  this.space();\n  this.print(node.argument, node);\n}\n\nexport function TypeAlias(\n  this: Printer,\n  node: t.TypeAlias | t.DeclareTypeAlias,\n) {\n  this.word(\"type\");\n  this.space();\n  this.print(node.id, node);\n  this.print(node.typeParameters, node);\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(node.right, node);\n  this.semicolon();\n}\n\nexport function TypeAnnotation(this: Printer, node: t.TypeAnnotation) {\n  this.token(\":\");\n  this.space();\n  // @ts-expect-error todo(flow->ts) can this be removed? `.optional` looks to be not existing property\n  if (node.optional) this.token(\"?\");\n  this.print(node.typeAnnotation, node);\n}\n\nexport function TypeParameterInstantiation(\n  this: Printer,\n  node: t.TypeParameterInstantiation,\n): void {\n  this.token(\"<\");\n  this.printList(node.params, node, {});\n  this.token(\">\");\n}\n\nexport { TypeParameterInstantiation as TypeParameterDeclaration };\n\nexport function TypeParameter(this: Printer, node: t.TypeParameter) {\n  this._variance(node);\n\n  this.word(node.name);\n\n  if (node.bound) {\n    this.print(node.bound, node);\n  }\n\n  if (node.default) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.default, node);\n  }\n}\n\nexport function OpaqueType(\n  this: Printer,\n  node: t.OpaqueType | t.DeclareOpaqueType,\n) {\n  this.word(\"opaque\");\n  this.space();\n  this.word(\"type\");\n  this.space();\n  this.print(node.id, node);\n  this.print(node.typeParameters, node);\n  if (node.supertype) {\n    this.token(\":\");\n    this.space();\n    this.print(node.supertype, node);\n  }\n\n  if (node.impltype) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.impltype, node);\n  }\n  this.semicolon();\n}\n\nexport function ObjectTypeAnnotation(\n  this: Printer,\n  node: t.ObjectTypeAnnotation,\n) {\n  if (node.exact) {\n    this.token(\"{|\");\n  } else {\n    this.token(\"{\");\n  }\n\n  // TODO: remove the array fallbacks and instead enforce the types to require an array\n  const props = [\n    ...node.properties,\n    ...(node.callProperties || []),\n    ...(node.indexers || []),\n    ...(node.internalSlots || []),\n  ];\n\n  if (props.length) {\n    this.newline();\n\n    this.space();\n\n    this.printJoin(props, node, {\n      addNewlines(leading) {\n        if (leading && !props[0]) return 1;\n      },\n      indent: true,\n      statement: true,\n      iterator: () => {\n        if (props.length !== 1 || node.inexact) {\n          this.token(\",\");\n          this.space();\n        }\n      },\n    });\n\n    this.space();\n  }\n\n  if (node.inexact) {\n    this.indent();\n    this.token(\"...\");\n    if (props.length) {\n      this.newline();\n    }\n    this.dedent();\n  }\n\n  if (node.exact) {\n    this.token(\"|}\");\n  } else {\n    this.token(\"}\");\n  }\n}\n\nexport function ObjectTypeInternalSlot(\n  this: Printer,\n  node: t.ObjectTypeInternalSlot,\n) {\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  this.token(\"[\");\n  this.token(\"[\");\n  this.print(node.id, node);\n  this.token(\"]\");\n  this.token(\"]\");\n  if (node.optional) this.token(\"?\");\n  if (!node.method) {\n    this.token(\":\");\n    this.space();\n  }\n  this.print(node.value, node);\n}\n\nexport function ObjectTypeCallProperty(\n  this: Printer,\n  node: t.ObjectTypeCallProperty,\n) {\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  this.print(node.value, node);\n}\n\nexport function ObjectTypeIndexer(this: Printer, node: t.ObjectTypeIndexer) {\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  this._variance(node);\n  this.token(\"[\");\n  if (node.id) {\n    this.print(node.id, node);\n    this.token(\":\");\n    this.space();\n  }\n  this.print(node.key, node);\n  this.token(\"]\");\n  this.token(\":\");\n  this.space();\n  this.print(node.value, node);\n}\n\nexport function ObjectTypeProperty(this: Printer, node: t.ObjectTypeProperty) {\n  if (node.proto) {\n    this.word(\"proto\");\n    this.space();\n  }\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  if (node.kind === \"get\" || node.kind === \"set\") {\n    this.word(node.kind);\n    this.space();\n  }\n  this._variance(node);\n  this.print(node.key, node);\n  if (node.optional) this.token(\"?\");\n  if (!node.method) {\n    this.token(\":\");\n    this.space();\n  }\n  this.print(node.value, node);\n}\n\nexport function ObjectTypeSpreadProperty(\n  this: Printer,\n  node: t.ObjectTypeSpreadProperty,\n) {\n  this.token(\"...\");\n  this.print(node.argument, node);\n}\n\nexport function QualifiedTypeIdentifier(\n  this: Printer,\n  node: t.QualifiedTypeIdentifier,\n) {\n  this.print(node.qualification, node);\n  this.token(\".\");\n  this.print(node.id, node);\n}\n\nexport function SymbolTypeAnnotation(this: Printer) {\n  this.word(\"symbol\");\n}\n\nfunction orSeparator(this: Printer) {\n  this.space();\n  this.token(\"|\");\n  this.space();\n}\n\nexport function UnionTypeAnnotation(\n  this: Printer,\n  node: t.UnionTypeAnnotation,\n) {\n  this.printJoin(node.types, node, { separator: orSeparator });\n}\n\nexport function TypeCastExpression(this: Printer, node: t.TypeCastExpression) {\n  this.token(\"(\");\n  this.print(node.expression, node);\n  this.print(node.typeAnnotation, node);\n  this.token(\")\");\n}\n\nexport function Variance(this: Printer, node: t.Variance) {\n  if (node.kind === \"plus\") {\n    this.token(\"+\");\n  } else {\n    this.token(\"-\");\n  }\n}\n\nexport function VoidTypeAnnotation(this: Printer) {\n  this.word(\"void\");\n}\n\nexport function IndexedAccessType(this: Printer, node: t.IndexedAccessType) {\n  this.print(node.objectType, node, true);\n  this.token(\"[\");\n  this.print(node.indexType, node);\n  this.token(\"]\");\n}\n\nexport function OptionalIndexedAccessType(\n  this: Printer,\n  node: t.OptionalIndexedAccessType,\n) {\n  this.print(node.objectType, node);\n  if (node.optional) {\n    this.token(\"?.\");\n  }\n  this.token(\"[\");\n  this.print(node.indexType, node);\n  this.token(\"]\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAD,OAAA;AAidA,IAAAE,OAAA,GAAAF,OAAA;AAGiB;EAtdRG,0BAA0B;EAAEC;AAAW,IAAAL,EAAA;AAIzC,SAASM,iBAAiBA,CAAA,EAAgB;EAC/C,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC;AAClB;AAEO,SAASC,mBAAmBA,CAEjCC,IAA2B,EAC3B;EACA,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,WAAW,EAAEF,IAAI,EAAE,IAAI,CAAC;EACxC,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACA,SAAK,IAAK;AACjB;AAEO,SAASC,qBAAqBA,CAAA,EAAgB;EACnD,IAAI,CAACN,IAAI,CAAC,SAAS,CAAC;AACtB;AAEO,SAASO,4BAA4BA,CAE1CL,IAAoC,EACpC;EACA,IAAI,CAACF,IAAI,CAACE,IAAI,CAACM,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;AAC1C;AAEO,SAASC,yBAAyBA,CAAA,EAAgB;EACvD,IAAI,CAACT,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAASU,YAAYA,CAE1BR,IAAoB,EACpBS,MAAc,EACd;EACA,IAAI,CAACd,0BAA0B,CAACc,MAAM,CAAC,EAAE;IACvC,IAAI,CAACX,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACY,KAAK,EAAE;EACd;EACA,IAAI,CAACZ,IAAI,CAAC,OAAO,CAAC;EAClB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACC,aAAa,CAACX,IAAI,CAAC;AAC1B;AAEO,SAASY,eAAeA,CAE7BZ,IAAuB,EACvBS,MAAc,EACd;EACA,IAAI,CAACd,0BAA0B,CAACc,MAAM,CAAC,EAAE;IACvC,IAAI,CAACX,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACY,KAAK,EAAE;EACd;EACA,IAAI,CAACZ,IAAI,CAAC,UAAU,CAAC;EACrB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACa,EAAE,EAAEb,IAAI,CAAC;EAEzB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACa,EAAE,CAACC,cAAc,CAACA,cAAc,EAAEd,IAAI,CAAC;EAEvD,IAAIA,IAAI,CAACe,SAAS,EAAE;IAClB,IAAI,CAACL,KAAK,EAAE;IACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACe,SAAS,EAAEf,IAAI,CAAC;EAClC;EAEA,IAAI,CAACgB,SAAS,EAAE;AAClB;AAEO,SAASC,iBAAiBA,CAAA,EAAgB;EAC/C,IAAI,CAACd,SAAK,IAAK;EACf,IAAI,CAACL,IAAI,CAAC,QAAQ,CAAC;AACrB;AAEO,SAASoB,iBAAiBA,CAAgBlB,IAAyB,EAAE;EAC1E,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACL,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACK,SAAK,IAAK;EACf,IAAI,CAACF,KAAK,CAACD,IAAI,CAACM,KAAK,EAAEN,IAAI,CAAC;EAC5B,IAAI,CAACG,SAAK,IAAK;AACjB;AAEO,SAASgB,gBAAgBA,CAAgBnB,IAAwB,EAAE;EACxE,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACU,oBAAoB,CAACpB,IAAI,CAAC;AACjC;AAEO,SAASqB,aAAaA,CAAgBrB,IAAqB,EAAE;EAClE,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACZ,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACa,EAAE,EAAEb,IAAI,CAAC;EACzB,IAAI,CAACU,KAAK,EAAE;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACsB,IAAI,EAAEtB,IAAI,CAAC;AAC7B;AAEO,SAASuB,oBAAoBA,CAElCvB,IAA4B,EAC5B;EACA,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACZ,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACK,SAAK,IAAK;EACf,IAAI,CAACL,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACG,KAAK,CAACD,IAAI,CAACc,cAAc,EAAEd,IAAI,CAAC;AACvC;AAEO,SAASwB,gBAAgBA,CAAgBxB,IAAwB,EAAE;EACxE,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACe,SAAS,CAACzB,IAAI,CAAC;AACtB;AAEO,SAAS0B,iBAAiBA,CAE/B1B,IAAyB,EACzBS,MAAc,EACd;EACA,IAAI,CAACd,0BAA0B,CAACc,MAAM,CAAC,EAAE;IACvC,IAAI,CAACX,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACY,KAAK,EAAE;EACd;EACA,IAAI,CAACiB,UAAU,CAAC3B,IAAI,CAAC;AACvB;AAEO,SAAS4B,eAAeA,CAE7B5B,IAAuB,EACvBS,MAAc,EACd;EACA,IAAI,CAACd,0BAA0B,CAACc,MAAM,CAAC,EAAE;IACvC,IAAI,CAACX,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACY,KAAK,EAAE;EACd;EACA,IAAI,CAACZ,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACa,EAAE,EAAEb,IAAI,CAAC;EACzB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACa,EAAE,CAACC,cAAc,EAAEd,IAAI,CAAC;EACxC,IAAI,CAACgB,SAAS,EAAE;AAClB;AAEO,SAASa,wBAAwBA,CAEtC7B,IAAgC,EAChC;EACA,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACZ,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAIV,IAAI,CAAC8B,OAAO,EAAE;IAChB,IAAI,CAAChC,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACY,KAAK,EAAE;EACd;EAEAqB,qBAAqB,CAACC,IAAI,CAAC,IAAI,EAAEhC,IAAI,CAAC;AACxC;AAEO,SAASiC,2BAA2BA,CAEzCjC,IAAmC,EACnC;EACA,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACY,KAAK,EAAE;EACZwB,6BAAoB,CAACF,IAAI,CAAC,IAAI,EAAEhC,IAAI,CAAC;AACvC;AAEO,SAASmC,eAAeA,CAAgBnC,IAAuB,EAAE;EACtE,MAAM;IAAEa,EAAE;IAAES;EAAK,CAAC,GAAGtB,IAAI;EACzB,IAAI,CAACF,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACT,KAAK,CAACY,EAAE,EAAEb,IAAI,CAAC;EACpB,IAAI,CAACC,KAAK,CAACqB,IAAI,EAAEtB,IAAI,CAAC;AACxB;AAEA,SAASoC,gBAAgBA,CACvBC,OAAgB,EAChBC,IAAY,EACZC,eAAwB,EACxB;EACA,IAAIA,eAAe,EAAE;IACnBF,OAAO,CAAC3B,KAAK,EAAE;IACf2B,OAAO,CAACvC,IAAI,CAAC,IAAI,CAAC;IAClBuC,OAAO,CAAC3B,KAAK,EAAE;IACf2B,OAAO,CAACvC,IAAI,CAACwC,IAAI,CAAC;EACpB;EACAD,OAAO,CAAC3B,KAAK,EAAE;AACjB;AAEA,SAAS8B,QAAQA,CAACH,OAAgB,EAAErC,IAAgB,EAAE;EACpD,MAAM;IAAEyC;EAAQ,CAAC,GAAGzC,IAAI;EACxBqC,OAAO,CAAClC,KAAK,CAAC,GAAG,CAAC;EAClBkC,OAAO,CAACK,MAAM,EAAE;EAChBL,OAAO,CAACM,OAAO,EAAE;EACjB,KAAK,MAAMC,MAAM,IAAIH,OAAO,EAAE;IAC5BJ,OAAO,CAACpC,KAAK,CAAC2C,MAAM,EAAE5C,IAAI,CAAC;IAC3BqC,OAAO,CAACM,OAAO,EAAE;EACnB;EACA,IAAI3C,IAAI,CAAC6C,iBAAiB,EAAE;IAC1BR,OAAO,CAAClC,KAAK,CAAC,KAAK,CAAC;IACpBkC,OAAO,CAACM,OAAO,EAAE;EACnB;EACAN,OAAO,CAACS,MAAM,EAAE;EAChBT,OAAO,CAAClC,KAAK,CAAC,GAAG,CAAC;AACpB;AAEO,SAAS4C,eAAeA,CAAgB/C,IAAuB,EAAE;EACtE,MAAM;IAAEgD;EAAa,CAAC,GAAGhD,IAAI;EAC7BoC,gBAAgB,CAAC,IAAI,EAAE,SAAS,EAAEY,YAAY,CAAC;EAC/CR,QAAQ,CAAC,IAAI,EAAExC,IAAI,CAAC;AACtB;AAEO,SAASiD,cAAcA,CAAgBjD,IAAsB,EAAE;EACpE,MAAM;IAAEgD;EAAa,CAAC,GAAGhD,IAAI;EAC7BoC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAEY,YAAY,CAAC;EAC9CR,QAAQ,CAAC,IAAI,EAAExC,IAAI,CAAC;AACtB;AAEO,SAASkD,cAAcA,CAAgBlD,IAAsB,EAAE;EACpE,MAAM;IAAEgD;EAAa,CAAC,GAAGhD,IAAI;EAC7BoC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAEY,YAAY,CAAC;EAC9CR,QAAQ,CAAC,IAAI,EAAExC,IAAI,CAAC;AACtB;AAEO,SAASmD,cAAcA,CAAgBnD,IAAsB,EAAE;EACpEoC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC;EACtCI,QAAQ,CAAC,IAAI,EAAExC,IAAI,CAAC;AACtB;AAEO,SAASoD,mBAAmBA,CAEjCpD,IAA2B,EAC3B;EACA,MAAM;IAAEa;EAAG,CAAC,GAAGb,IAAI;EACnB,IAAI,CAACC,KAAK,CAACY,EAAE,EAAEb,IAAI,CAAC;EACpB,IAAI,CAACG,SAAK,IAAK;AACjB;AAEA,SAASkD,qBAAqBA,CAC5BhB,OAAgB,EAChBrC,IAAmE,EACnE;EACA,MAAM;IAAEa,EAAE;IAAEyC;EAAK,CAAC,GAAGtD,IAAI;EACzBqC,OAAO,CAACpC,KAAK,CAACY,EAAE,EAAEb,IAAI,CAAC;EACvBqC,OAAO,CAAC3B,KAAK,EAAE;EACf2B,OAAO,CAAClC,KAAK,CAAC,GAAG,CAAC;EAClBkC,OAAO,CAAC3B,KAAK,EAAE;EACf2B,OAAO,CAACpC,KAAK,CAACqD,IAAI,EAAEtD,IAAI,CAAC;EACzBqC,OAAO,CAAClC,KAAK,CAAC,GAAG,CAAC;AACpB;AAEO,SAASoD,iBAAiBA,CAAgBvD,IAAyB,EAAE;EAC1EqD,qBAAqB,CAAC,IAAI,EAAErD,IAAI,CAAC;AACnC;AAEO,SAASwD,gBAAgBA,CAAgBxD,IAAwB,EAAE;EACxEqD,qBAAqB,CAAC,IAAI,EAAErD,IAAI,CAAC;AACnC;AAEO,SAASyD,gBAAgBA,CAAgBzD,IAAwB,EAAE;EACxEqD,qBAAqB,CAAC,IAAI,EAAErD,IAAI,CAAC;AACnC;AAEA,SAAS+B,qBAAqBA,CAE5B/B,IAAgC,EAChC;EACA,IAAIA,IAAI,CAAC0D,WAAW,EAAE;IACpB,MAAMC,MAAM,GAAG3D,IAAI,CAAC0D,WAAW;IAC/B,IAAI,CAACzD,KAAK,CAAC0D,MAAM,EAAE3D,IAAI,CAAC;IACxB,IAAI,CAACJ,WAAW,CAAC+D,MAAM,CAAC,EAAE,IAAI,CAAC3C,SAAS,EAAE;EAC5C,CAAC,MAAM;IACL,IAAI,CAACb,SAAK,KAAK;IACf,IAAIH,IAAI,CAAC4D,UAAU,CAACC,MAAM,EAAE;MAC1B,IAAI,CAACnD,KAAK,EAAE;MACZ,IAAI,CAACoD,SAAS,CAAC9D,IAAI,CAAC4D,UAAU,EAAE5D,IAAI,CAAC;MACrC,IAAI,CAACU,KAAK,EAAE;IACd;IACA,IAAI,CAACP,SAAK,KAAK;IAEf,IAAIH,IAAI,CAAC+D,MAAM,EAAE;MACf,IAAI,CAACrD,KAAK,EAAE;MACZ,IAAI,CAACZ,IAAI,CAAC,MAAM,CAAC;MACjB,IAAI,CAACY,KAAK,EAAE;MACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAAC+D,MAAM,EAAE/D,IAAI,CAAC;IAC/B;IAEA,IAAI,CAACgB,SAAS,EAAE;EAClB;AACF;AAEO,SAASgD,oBAAoBA,CAAA,EAAgB;EAClD,IAAI,CAAC7D,SAAK,IAAK;AACjB;AAEO,SAAS8D,sBAAsBA,CAEpCjE,IAA8B,EAC9BS,MAAqB,EACrB;EACA,IAAI,CAACR,KAAK,CAACD,IAAI,CAACkE,cAAc,EAAElE,IAAI,CAAC;EACrC,IAAI,CAACG,SAAK,IAAK;EAEf,IAAIH,IAAI,CAACmE,IAAI,EAAE;IACb,IAAI,CAACrE,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACK,SAAK,IAAK;IACf,IAAI,CAACO,KAAK,EAAE;IACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACmE,IAAI,CAACrD,cAAc,EAAEd,IAAI,CAAC;IAC1C,IAAIA,IAAI,CAACoE,MAAM,CAACP,MAAM,IAAI7D,IAAI,CAACqE,IAAI,EAAE;MACnC,IAAI,CAAClE,SAAK,IAAK;MACf,IAAI,CAACO,KAAK,EAAE;IACd;EACF;EAEA,IAAI,CAACoD,SAAS,CAAC9D,IAAI,CAACoE,MAAM,EAAEpE,IAAI,CAAC;EAEjC,IAAIA,IAAI,CAACqE,IAAI,EAAE;IACb,IAAIrE,IAAI,CAACoE,MAAM,CAACP,MAAM,EAAE;MACtB,IAAI,CAAC1D,SAAK,IAAK;MACf,IAAI,CAACO,KAAK,EAAE;IACd;IACA,IAAI,CAACP,KAAK,CAAC,KAAK,CAAC;IACjB,IAAI,CAACF,KAAK,CAACD,IAAI,CAACqE,IAAI,EAAErE,IAAI,CAAC;EAC7B;EAEA,IAAI,CAACG,SAAK,IAAK;EAGf,IACEM,MAAM,KACLA,MAAM,CAAC6D,IAAI,KAAK,wBAAwB,IACvC7D,MAAM,CAAC6D,IAAI,KAAK,wBAAwB,IACxC7D,MAAM,CAAC6D,IAAI,KAAK,iBAAiB,IAChC7D,MAAM,CAAC6D,IAAI,KAAK,oBAAoB,IAAI7D,MAAM,CAAC8D,MAAO,CAAC,EAC1D;IACA,IAAI,CAACpE,SAAK,IAAK;EACjB,CAAC,MAAM;IACL,IAAI,CAACO,KAAK,EAAE;IACZ,IAAI,CAACP,KAAK,CAAC,IAAI,CAAC;EAClB;EAEA,IAAI,CAACO,KAAK,EAAE;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACwE,UAAU,EAAExE,IAAI,CAAC;AACnC;AAEO,SAASyE,iBAAiBA,CAAgBzE,IAAyB,EAAE;EAC1E,IAAI,CAACC,KAAK,CAACD,IAAI,CAACsC,IAAI,EAAEtC,IAAI,CAAC;EAC3B,IAAIA,IAAI,CAAC0E,QAAQ,EAAE,IAAI,CAACvE,SAAK,IAAK;EAClC,IAAIH,IAAI,CAACsC,IAAI,EAAE;IACb,IAAI,CAACnC,SAAK,IAAK;IACf,IAAI,CAACO,KAAK,EAAE;EACd;EACA,IAAI,CAACT,KAAK,CAACD,IAAI,CAACc,cAAc,EAAEd,IAAI,CAAC;AACvC;AAEO,SAAS2E,gBAAgBA,CAAgB3E,IAAwB,EAAE;EACxE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACa,EAAE,EAAEb,IAAI,CAAC;EACzB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACkE,cAAc,EAAElE,IAAI,EAAE,IAAI,CAAC;AAC7C;AAOO,SAASW,aAAaA,CAE3BX,IAAkE,EAClE;EAAA,IAAA4E,aAAA;EACA,IAAI,CAAC3E,KAAK,CAACD,IAAI,CAACa,EAAE,EAAEb,IAAI,CAAC;EACzB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACkE,cAAc,EAAElE,IAAI,CAAC;EACrC,KAAA4E,aAAA,GAAI5E,IAAI,CAAC6E,OAAO,aAAZD,aAAA,CAAcf,MAAM,EAAE;IACxB,IAAI,CAACnD,KAAK,EAAE;IACZ,IAAI,CAACZ,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACY,KAAK,EAAE;IACZ,IAAI,CAACoD,SAAS,CAAC9D,IAAI,CAAC6E,OAAO,EAAE7E,IAAI,CAAC;EACpC;EACA,IAAIA,IAAI,CAAC8E,MAAM,IAAI9E,IAAI,CAAC8E,MAAM,CAACjB,MAAM,EAAE;IACrC,IAAI,CAACnD,KAAK,EAAE;IACZ,IAAI,CAACZ,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACY,KAAK,EAAE;IACZ,IAAI,CAACoD,SAAS,CAAC9D,IAAI,CAAC8E,MAAM,EAAE9E,IAAI,CAAC;EACnC;EACA,IAAIA,IAAI,CAAC+E,UAAU,IAAI/E,IAAI,CAAC+E,UAAU,CAAClB,MAAM,EAAE;IAC7C,IAAI,CAACnD,KAAK,EAAE;IACZ,IAAI,CAACZ,IAAI,CAAC,YAAY,CAAC;IACvB,IAAI,CAACY,KAAK,EAAE;IACZ,IAAI,CAACoD,SAAS,CAAC9D,IAAI,CAAC+E,UAAU,EAAE/E,IAAI,CAAC;EACvC;EACA,IAAI,CAACU,KAAK,EAAE;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACsB,IAAI,EAAEtB,IAAI,CAAC;AAC7B;AAEO,SAASgF,SAASA,CAEvBhF,IAM2B,EAC3B;EACA,IAAIA,IAAI,CAACiF,QAAQ,EAAE;IACjB,IAAIjF,IAAI,CAACiF,QAAQ,CAACC,IAAI,KAAK,MAAM,EAAE;MACjC,IAAI,CAAC/E,SAAK,IAAK;IACjB,CAAC,MAAM,IAAIH,IAAI,CAACiF,QAAQ,CAACC,IAAI,KAAK,OAAO,EAAE;MACzC,IAAI,CAAC/E,SAAK,IAAK;IACjB;EACF;AACF;AAEO,SAASiB,oBAAoBA,CAElCpB,IAAiD,EACjD;EACA,IAAI,CAACF,IAAI,CAAC,WAAW,CAAC;EACtB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACC,aAAa,CAACX,IAAI,CAAC;AAC1B;AAEA,SAASmF,YAAYA,CAAA,EAAgB;EACnC,IAAI,CAACzE,KAAK,EAAE;EACZ,IAAI,CAACP,SAAK,IAAK;EACf,IAAI,CAACO,KAAK,EAAE;AACd;AAEO,SAAS0E,uBAAuBA,CAErCpF,IAA+B,EAC/B;EACA,IAAI,CAACF,IAAI,CAAC,WAAW,CAAC;EACtB,IAAIE,IAAI,CAAC6E,OAAO,IAAI7E,IAAI,CAAC6E,OAAO,CAAChB,MAAM,EAAE;IACvC,IAAI,CAACnD,KAAK,EAAE;IACZ,IAAI,CAACZ,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACY,KAAK,EAAE;IACZ,IAAI,CAACoD,SAAS,CAAC9D,IAAI,CAAC6E,OAAO,EAAE7E,IAAI,CAAC;EACpC;EACA,IAAI,CAACU,KAAK,EAAE;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACsB,IAAI,EAAEtB,IAAI,CAAC;AAC7B;AAEO,SAASqF,0BAA0BA,CAExCrF,IAAkC,EAClC;EACA,IAAI,CAACsF,SAAS,CAACtF,IAAI,CAACuF,KAAK,EAAEvF,IAAI,EAAE;IAAEwF,SAAS,EAAEL;EAAa,CAAC,CAAC;AAC/D;AAEO,SAASM,mBAAmBA,CAAA,EAAgB;EACjD,IAAI,CAAC3F,IAAI,CAAC,OAAO,CAAC;AACpB;AAEO,SAAS4F,mBAAmBA,CAAA,EAAgB;EACjD,IAAI,CAAC5F,IAAI,CAAC,OAAO,CAAC;AACpB;AAEO,SAAS6F,sBAAsBA,CAEpC3F,IAA8B,EAC9B;EACA,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACF,KAAK,CAACD,IAAI,CAACc,cAAc,EAAEd,IAAI,CAAC;AACvC;AAOO,SAAS4F,oBAAoBA,CAAA,EAAgB;EAClD,IAAI,CAAC9F,IAAI,CAAC,QAAQ,CAAC;AACrB;AAEO,SAAS+F,oBAAoBA,CAAA,EAAgB;EAClD,IAAI,CAAC/F,IAAI,CAAC,QAAQ,CAAC;AACrB;AAEO,SAASgG,kBAAkBA,CAAA,EAAgB;EAChD,IAAI,CAAChG,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAASiG,mBAAmBA,CAEjC/F,IAA2B,EAC3B;EACA,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAAC2D,SAAS,CAAC9D,IAAI,CAACuF,KAAK,EAAEvF,IAAI,CAAC;EAChC,IAAI,CAACG,SAAK,IAAK;AACjB;AAEO,SAAS6F,oBAAoBA,CAElChG,IAA4B,EAC5B;EACA,IAAI,CAACF,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACiG,QAAQ,EAAEjG,IAAI,CAAC;AACjC;AAEO,SAASyB,SAASA,CAEvBzB,IAAsC,EACtC;EACA,IAAI,CAACF,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACa,EAAE,EAAEb,IAAI,CAAC;EACzB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACkE,cAAc,EAAElE,IAAI,CAAC;EACrC,IAAI,CAACU,KAAK,EAAE;EACZ,IAAI,CAACP,SAAK,IAAK;EACf,IAAI,CAACO,KAAK,EAAE;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACkG,KAAK,EAAElG,IAAI,CAAC;EAC5B,IAAI,CAACgB,SAAS,EAAE;AAClB;AAEO,SAASmF,cAAcA,CAAgBnG,IAAsB,EAAE;EACpE,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACO,KAAK,EAAE;EAEZ,IAAIV,IAAI,CAAC0E,QAAQ,EAAE,IAAI,CAACvE,SAAK,IAAK;EAClC,IAAI,CAACF,KAAK,CAACD,IAAI,CAACc,cAAc,EAAEd,IAAI,CAAC;AACvC;AAEO,SAASoG,0BAA0BA,CAExCpG,IAAkC,EAC5B;EACN,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAAC2D,SAAS,CAAC9D,IAAI,CAACoE,MAAM,EAAEpE,IAAI,EAAE,CAAC,CAAC,CAAC;EACrC,IAAI,CAACG,SAAK,IAAK;AACjB;AAIO,SAASkG,aAAaA,CAAgBrG,IAAqB,EAAE;EAClE,IAAI,CAACgF,SAAS,CAAChF,IAAI,CAAC;EAEpB,IAAI,CAACF,IAAI,CAACE,IAAI,CAACsC,IAAI,CAAC;EAEpB,IAAItC,IAAI,CAACsG,KAAK,EAAE;IACd,IAAI,CAACrG,KAAK,CAACD,IAAI,CAACsG,KAAK,EAAEtG,IAAI,CAAC;EAC9B;EAEA,IAAIA,IAAI,CAAC8B,OAAO,EAAE;IAChB,IAAI,CAACpB,KAAK,EAAE;IACZ,IAAI,CAACP,SAAK,IAAK;IACf,IAAI,CAACO,KAAK,EAAE;IACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAAC8B,OAAO,EAAE9B,IAAI,CAAC;EAChC;AACF;AAEO,SAAS2B,UAAUA,CAExB3B,IAAwC,EACxC;EACA,IAAI,CAACF,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACZ,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACY,KAAK,EAAE;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACa,EAAE,EAAEb,IAAI,CAAC;EACzB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACkE,cAAc,EAAElE,IAAI,CAAC;EACrC,IAAIA,IAAI,CAACuG,SAAS,EAAE;IAClB,IAAI,CAACpG,SAAK,IAAK;IACf,IAAI,CAACO,KAAK,EAAE;IACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACuG,SAAS,EAAEvG,IAAI,CAAC;EAClC;EAEA,IAAIA,IAAI,CAACwG,QAAQ,EAAE;IACjB,IAAI,CAAC9F,KAAK,EAAE;IACZ,IAAI,CAACP,SAAK,IAAK;IACf,IAAI,CAACO,KAAK,EAAE;IACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACwG,QAAQ,EAAExG,IAAI,CAAC;EACjC;EACA,IAAI,CAACgB,SAAS,EAAE;AAClB;AAEO,SAASyF,oBAAoBA,CAElCzG,IAA4B,EAC5B;EACA,IAAIA,IAAI,CAAC0G,KAAK,EAAE;IACd,IAAI,CAACvG,KAAK,CAAC,IAAI,CAAC;EAClB,CAAC,MAAM;IACL,IAAI,CAACA,SAAK,KAAK;EACjB;EAGA,MAAMwG,KAAK,GAAG,CACZ,GAAG3G,IAAI,CAAC4G,UAAU,EAClB,IAAI5G,IAAI,CAAC6G,cAAc,IAAI,EAAE,CAAC,EAC9B,IAAI7G,IAAI,CAAC8G,QAAQ,IAAI,EAAE,CAAC,EACxB,IAAI9G,IAAI,CAAC+G,aAAa,IAAI,EAAE,CAAC,CAC9B;EAED,IAAIJ,KAAK,CAAC9C,MAAM,EAAE;IAChB,IAAI,CAAClB,OAAO,EAAE;IAEd,IAAI,CAACjC,KAAK,EAAE;IAEZ,IAAI,CAAC4E,SAAS,CAACqB,KAAK,EAAE3G,IAAI,EAAE;MAC1BgH,WAAWA,CAACC,OAAO,EAAE;QACnB,IAAIA,OAAO,IAAI,CAACN,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;MACpC,CAAC;MACDjE,MAAM,EAAE,IAAI;MACZwE,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAEA,CAAA,KAAM;QACd,IAAIR,KAAK,CAAC9C,MAAM,KAAK,CAAC,IAAI7D,IAAI,CAACoH,OAAO,EAAE;UACtC,IAAI,CAACjH,SAAK,IAAK;UACf,IAAI,CAACO,KAAK,EAAE;QACd;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACA,KAAK,EAAE;EACd;EAEA,IAAIV,IAAI,CAACoH,OAAO,EAAE;IAChB,IAAI,CAAC1E,MAAM,EAAE;IACb,IAAI,CAACvC,KAAK,CAAC,KAAK,CAAC;IACjB,IAAIwG,KAAK,CAAC9C,MAAM,EAAE;MAChB,IAAI,CAAClB,OAAO,EAAE;IAChB;IACA,IAAI,CAACG,MAAM,EAAE;EACf;EAEA,IAAI9C,IAAI,CAAC0G,KAAK,EAAE;IACd,IAAI,CAACvG,KAAK,CAAC,IAAI,CAAC;EAClB,CAAC,MAAM;IACL,IAAI,CAACA,SAAK,KAAK;EACjB;AACF;AAEO,SAASkH,sBAAsBA,CAEpCrH,IAA8B,EAC9B;EACA,IAAIA,IAAI,CAACsH,MAAM,EAAE;IACf,IAAI,CAACxH,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACY,KAAK,EAAE;EACd;EACA,IAAI,CAACP,SAAK,IAAK;EACf,IAAI,CAACA,SAAK,IAAK;EACf,IAAI,CAACF,KAAK,CAACD,IAAI,CAACa,EAAE,EAAEb,IAAI,CAAC;EACzB,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACA,SAAK,IAAK;EACf,IAAIH,IAAI,CAAC0E,QAAQ,EAAE,IAAI,CAACvE,SAAK,IAAK;EAClC,IAAI,CAACH,IAAI,CAACuE,MAAM,EAAE;IAChB,IAAI,CAACpE,SAAK,IAAK;IACf,IAAI,CAACO,KAAK,EAAE;EACd;EACA,IAAI,CAACT,KAAK,CAACD,IAAI,CAACM,KAAK,EAAEN,IAAI,CAAC;AAC9B;AAEO,SAASuH,sBAAsBA,CAEpCvH,IAA8B,EAC9B;EACA,IAAIA,IAAI,CAACsH,MAAM,EAAE;IACf,IAAI,CAACxH,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACY,KAAK,EAAE;EACd;EACA,IAAI,CAACT,KAAK,CAACD,IAAI,CAACM,KAAK,EAAEN,IAAI,CAAC;AAC9B;AAEO,SAASwH,iBAAiBA,CAAgBxH,IAAyB,EAAE;EAC1E,IAAIA,IAAI,CAACsH,MAAM,EAAE;IACf,IAAI,CAACxH,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACY,KAAK,EAAE;EACd;EACA,IAAI,CAACsE,SAAS,CAAChF,IAAI,CAAC;EACpB,IAAI,CAACG,SAAK,IAAK;EACf,IAAIH,IAAI,CAACa,EAAE,EAAE;IACX,IAAI,CAACZ,KAAK,CAACD,IAAI,CAACa,EAAE,EAAEb,IAAI,CAAC;IACzB,IAAI,CAACG,SAAK,IAAK;IACf,IAAI,CAACO,KAAK,EAAE;EACd;EACA,IAAI,CAACT,KAAK,CAACD,IAAI,CAACyH,GAAG,EAAEzH,IAAI,CAAC;EAC1B,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACA,SAAK,IAAK;EACf,IAAI,CAACO,KAAK,EAAE;EACZ,IAAI,CAACT,KAAK,CAACD,IAAI,CAACM,KAAK,EAAEN,IAAI,CAAC;AAC9B;AAEO,SAAS0H,kBAAkBA,CAAgB1H,IAA0B,EAAE;EAC5E,IAAIA,IAAI,CAAC2H,KAAK,EAAE;IACd,IAAI,CAAC7H,IAAI,CAAC,OAAO,CAAC;IAClB,IAAI,CAACY,KAAK,EAAE;EACd;EACA,IAAIV,IAAI,CAACsH,MAAM,EAAE;IACf,IAAI,CAACxH,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACY,KAAK,EAAE;EACd;EACA,IAAIV,IAAI,CAACkF,IAAI,KAAK,KAAK,IAAIlF,IAAI,CAACkF,IAAI,KAAK,KAAK,EAAE;IAC9C,IAAI,CAACpF,IAAI,CAACE,IAAI,CAACkF,IAAI,CAAC;IACpB,IAAI,CAACxE,KAAK,EAAE;EACd;EACA,IAAI,CAACsE,SAAS,CAAChF,IAAI,CAAC;EACpB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACyH,GAAG,EAAEzH,IAAI,CAAC;EAC1B,IAAIA,IAAI,CAAC0E,QAAQ,EAAE,IAAI,CAACvE,SAAK,IAAK;EAClC,IAAI,CAACH,IAAI,CAACuE,MAAM,EAAE;IAChB,IAAI,CAACpE,SAAK,IAAK;IACf,IAAI,CAACO,KAAK,EAAE;EACd;EACA,IAAI,CAACT,KAAK,CAACD,IAAI,CAACM,KAAK,EAAEN,IAAI,CAAC;AAC9B;AAEO,SAAS4H,wBAAwBA,CAEtC5H,IAAgC,EAChC;EACA,IAAI,CAACG,KAAK,CAAC,KAAK,CAAC;EACjB,IAAI,CAACF,KAAK,CAACD,IAAI,CAACiG,QAAQ,EAAEjG,IAAI,CAAC;AACjC;AAEO,SAAS6H,uBAAuBA,CAErC7H,IAA+B,EAC/B;EACA,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC8H,aAAa,EAAE9H,IAAI,CAAC;EACpC,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACF,KAAK,CAACD,IAAI,CAACa,EAAE,EAAEb,IAAI,CAAC;AAC3B;AAEO,SAAS+H,oBAAoBA,CAAA,EAAgB;EAClD,IAAI,CAACjI,IAAI,CAAC,QAAQ,CAAC;AACrB;AAEA,SAASkI,WAAWA,CAAA,EAAgB;EAClC,IAAI,CAACtH,KAAK,EAAE;EACZ,IAAI,CAACP,SAAK,KAAK;EACf,IAAI,CAACO,KAAK,EAAE;AACd;AAEO,SAASuH,mBAAmBA,CAEjCjI,IAA2B,EAC3B;EACA,IAAI,CAACsF,SAAS,CAACtF,IAAI,CAACuF,KAAK,EAAEvF,IAAI,EAAE;IAAEwF,SAAS,EAAEwC;EAAY,CAAC,CAAC;AAC9D;AAEO,SAASE,kBAAkBA,CAAgBlI,IAA0B,EAAE;EAC5E,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACF,KAAK,CAACD,IAAI,CAACmI,UAAU,EAAEnI,IAAI,CAAC;EACjC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACc,cAAc,EAAEd,IAAI,CAAC;EACrC,IAAI,CAACG,SAAK,IAAK;AACjB;AAEO,SAASiI,QAAQA,CAAgBpI,IAAgB,EAAE;EACxD,IAAIA,IAAI,CAACkF,IAAI,KAAK,MAAM,EAAE;IACxB,IAAI,CAAC/E,SAAK,IAAK;EACjB,CAAC,MAAM;IACL,IAAI,CAACA,SAAK,IAAK;EACjB;AACF;AAEO,SAASkI,kBAAkBA,CAAA,EAAgB;EAChD,IAAI,CAACvI,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAASwI,iBAAiBA,CAAgBtI,IAAyB,EAAE;EAC1E,IAAI,CAACC,KAAK,CAACD,IAAI,CAACuI,UAAU,EAAEvI,IAAI,EAAE,IAAI,CAAC;EACvC,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACF,KAAK,CAACD,IAAI,CAACwI,SAAS,EAAExI,IAAI,CAAC;EAChC,IAAI,CAACG,SAAK,IAAK;AACjB;AAEO,SAASsI,yBAAyBA,CAEvCzI,IAAiC,EACjC;EACA,IAAI,CAACC,KAAK,CAACD,IAAI,CAACuI,UAAU,EAAEvI,IAAI,CAAC;EACjC,IAAIA,IAAI,CAAC0E,QAAQ,EAAE;IACjB,IAAI,CAACvE,KAAK,CAAC,IAAI,CAAC;EAClB;EACA,IAAI,CAACA,SAAK,IAAK;EACf,IAAI,CAACF,KAAK,CAACD,IAAI,CAACwI,SAAS,EAAExI,IAAI,CAAC;EAChC,IAAI,CAACG,SAAK,IAAK;AACjB"}