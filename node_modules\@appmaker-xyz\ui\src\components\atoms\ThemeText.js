import React from 'react';
import { Linking, Text, Dimensions, Platform } from 'react-native';
import { styles } from '../../styles';
import HTMLview from 'react-native-render-html';
import striptagsFn from 'striptags';
import iframe from '@native-html/iframe-plugin';
import { testProps } from '@appmaker-xyz/core';
import WebView from 'react-native-webview';
import { useTranslation } from 'react-i18next';
import { getActionFromURL } from '@appmaker-xyz/shopify';
import { handleAction } from '@appmaker-xyz/react-native';
const Entities = require('html-entities').XmlEntities;

const { width } = Dimensions.get('window');
const entities = new Entities();

export default function ThemeText({
  children,
  fontFamily = 'regular',
  size = 'base',
  style,
  color,
  striptags = false,
  hideText,
  html,
  clientId,
  testId,
  customHtmlTagsStyles = {},
  ...props
}) {
  const { t } = useTranslation();
  if (hideText) {
    return null;
  }
  if (html) {
    const imageWidth = width - 25;
    const alterNode = (node) => {
      const { name, parent } = node;
      if (node.name == 'iframe' && node.attribs.src.match(/youtube\.com/i)) {
        // this is used for youtube video running in correct size. youtube video was distorted in the previous version.
        delete node.attribs.width;
        delete node.attribs.height;

        // fix for: when parent div has max-width: 100% iframe not showing full width
        if (parent.name === 'div') {
          delete parent.attribs.style;
        }
        return node;
      }
      // If the node width is larger than the screen width then convert it to fit the screen
      if (
        node.attribs &&
        node.attribs.width &&
        node.attribs.width > imageWidth
      ) {
        const imageHeight =
          imageWidth / (node.attribs.width / node.attribs.height);
        node.attribs = {
          ...(node.attribs || {}),
          width: imageWidth,
          height: imageHeight,
        };
        return node;
      }
    };
    const renderers = {
      iframe,
    };
    /**
     * Workaround fix
     * Issue:
     * On some devices pdp description with iframe is causing crash when navigated back or to;
     * react-native-render-html/issues/393#issuecomment-1277533605
     * appmaker-react-native-app/issues/2027
     */

    const isIos = Platform.OS === 'ios';
    const defaultTagStyles = {
      iframe: {
        opacity: 0.99,
      },
      b: {
        fontWeight: isIos ? undefined : 'normal',
        fontFamily: styles.fontFamily.bold,
      },
      strong: {
        fontWeight: isIos ? undefined : 'normal',
        fontFamily: styles.fontFamily.bold,
      },
    };
    const tagStyles = {
      ...defaultTagStyles,
      ...customHtmlTagsStyles,
    };
    return (
      <HTMLview
        {...testProps(clientId)}
        renderers={renderers}
        WebView={WebView}
        tagsStyles={tagStyles}
        imagesMaxWidth={imageWidth}
        source={{ html: children ? children : ' ' }}
        alterNode={alterNode}
        baseFontStyle={{ fontFamily: styles.fontFamily.regular }}
        ignoredStyles={[
          'font-family',
          'letter-spacing',
          'position',
          'margin',
          'textTransform',
        ]}
        onLinkPress={(url, href) => {
          // handle the href using getActionFromURL and then handle the action
          if (href) {
            getActionFromURL?.(href)
              .then((action) => {
                handleAction?.(action);
              })
              .catch((e) => {
                Linking.openURL(href);
              });
          }
        }}
      />
    );
  }
  const newStyles = {
    fontFamily: styles.fontFamily[fontFamily],
    fontSize: styles.size[size],
    color: color,
  };
  let finalChildren = children;
  if (Array.isArray(children)) {
    finalChildren = [];
    children.map((value, index) => {
      if (typeof value === 'string') {
        finalChildren.push(`${t(value.trim())} `);
      } else {
        finalChildren.push(value);
      }
    });
  }
  if (typeof finalChildren === 'string') {
    if (finalChildren.includes(':')) {
      try {
        finalChildren = entities.decode(children);
      } catch (error) {
        console.log(error, 'Error');
      }
    } else {
      try {
        finalChildren = t(entities.decode(children));
      } catch (error) {
        console.log(error, 'Error');
      }
    }
  }
  finalChildren =
    striptags && typeof children === 'string'
      ? striptagsFn(finalChildren)
      : finalChildren;
  return (
    <Text {...testProps(testId)} style={[newStyles, style]} {...props}>
      {finalChildren}
    </Text>
  );
}
