import { appmaker } from '@appmaker-xyz/core';
import CustomizeOrder from './components/customizeOrder/index';
import ViewSize from './components/viewSize/index';
import ConfirmFit from './components/ConfirmFit/index';
import {
  addWidgetinCartPage,
  addOTPPhoneCustomValidation,
  addWidgetinOrderListPage,
} from './helper.js';

const Plugin = {
  id: 'bombay-shirts-164972',
  name: 'Bombay-shirts-164972',
  activate,
};

export const PluginStaging = {
  id: 'bombay-shirts-staging-168202',
  name: 'Bombay-shirts-164972',
  activate,
};

export function activate({ settings }) {
  // appmaker.blocks.registerBlockType({
  //   name: 'appmaker/confirm-fit-widget',
  //   View: ConfirmFit,
  // });
  appmaker.blocks.registerBlockType({
    name: 'appmaker/order-customization-widget',
    View: CustomizeOrder,
  });
  addWidgetinCartPage();
  addOTPPhoneCustomValidation();
  addWidgetinOrderListPage();
}
export default Plugin;
