import React from 'react';
import { StyleSheet, I18nManager, Image } from 'react-native';
import { ThemeText, AppTouchable, Layout } from '../atoms';
import Icon from 'react-native-vector-icons/Feather';
import { testProps } from '@appmaker-xyz/core';
import { SvgXml } from 'react-native-svg';

const LayoutIcon = ({
  attributes: {
    iconName,
    itemCount,
    appmakerAction,
    overlay,
    tabBar,
    iconSize = 20,
    iconColor = null,
    activeColor,
    activeTextColor,
    localImage,
    svgIcon,
    itemCountCustomStyle,
    svgXml,
    iconContainerStyle = {},
    contTextStyle = {},
  },
  onPress,
  onAction,
  clientId,
}) => {
  const onPressHandle =
    appmakerAction && onAction ? () => onAction(appmakerAction) : onPress;

  const containerStyles = [styles.container, iconContainerStyle];

  const counterStyles = [
    styles.counterText,
    I18nManager.isRTL ? { left: 0 } : { right: 0 },
    activeColor && { backgroundColor: activeColor },
    itemCountCustomStyle,
  ];

  if (tabBar) {
    containerStyles.push(styles.tabBarStyles);
  }

  if (overlay) {
    containerStyles.push(styles.overlayStyles);
  }

  function icon() {
    if (svgIcon) {
      return svgIcon;
    }
    if (svgXml) {
      return <SvgXml xml={svgXml} />;
    }
    if (localImage) {
      return (
        <Image
          style={{
            width: iconSize,
            height: iconSize,
          }}
          source={localImage}
        />
      );
    } else {
      return (
        <Icon name={iconName} size={iconSize} color={iconColor || '#1B1B1B'} />
      );
    }
  }

  return (
    <AppTouchable
      {...testProps(`toolbaricon-${clientId}`)}
      style={containerStyles}
      onPress={onPressHandle}
      clientId={clientId}>
      {icon()}
      {itemCount ? (
        <Layout style={counterStyles}>
          <ThemeText
            size="10"
            color={!activeTextColor ? '#ffffff' : activeTextColor}
            style={contTextStyle}>
            {itemCount}
          </ThemeText>
        </Layout>
      ) : null}
    </AppTouchable>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    padding: 8,
    marginStart: 6,
  },
  counterText: {
    position: 'absolute',
    top: 0,
    backgroundColor: '#212121',
    width: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  overlayStyles: {
    padding: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 32,
    overflow: 'hidden',
  },
  tabBarStyles: {
    marginStart: 0,
    padding: 0,
  },
});

export default LayoutIcon;
