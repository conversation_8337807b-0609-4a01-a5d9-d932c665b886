import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ThemeText, AppImage } from '@appmaker-xyz/ui';
import { useUser } from '@appmaker-xyz/shopify';
import { UserProfile } from '../../../assets/svg';

const TextProfile = ({ firstName, lastName }) => {
  const firstLetter = firstName?.charAt(0);
  const lastLetter = lastName?.charAt(0);
  return (
    <Layout style={styles.textProfile}>
      <ThemeText size="2xl" fontFamily="medium" color="#94A3B8">
        {firstLetter}
      </ThemeText>
      <ThemeText size="2xl" fontFamily="medium" color="#94A3B8">
        {lastLetter}
      </ThemeText>
    </Layout>
  );
};

const ProfileCard = (props) => {
  const { user } = useUser(props);

  const firstName = user?.firstName;
  const lastName = user?.lastName;
  const email = user?.email;
  const phone = user?.phone;

  return (
    <Layout style={styles.container}>
      {firstName || lastName ? (
        <TextProfile firstName={firstName} lastName={lastName} />
      ) : (
        <UserProfile style={styles.userIcon} width={85} height={85} />
      )}
      {firstName || lastName ? (
        <ThemeText fontFamily="medium" size="lg" style={styles.centered}>
          {firstName} {lastName}
        </ThemeText>
      ) : null}
      <ThemeText hide={!email} size="sm" style={styles.centered}>
        {email}
      </ThemeText>
      <ThemeText size="sm" hideText={!phone} style={styles.centered}>
        {phone}
      </ThemeText>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderBottomColor: '#E2E8F0',
    borderBottomWidth: 0.5,
    marginHorizontal: 12,
  },
  image: {
    width: 85,
    height: 85,
    borderRadius: 50,
    overflow: 'hidden',
    marginBottom: 8,
  },
  textProfile: {
    width: 85,
    height: 85,
    borderRadius: 50,
    overflow: 'hidden',
    marginBottom: 8,
    backgroundColor: '#E2E8F0',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  userIcon: {
    marginBottom: 8,
  },
  centered: {
    textAlign: 'center',
  },
});

export default ProfileCard;
