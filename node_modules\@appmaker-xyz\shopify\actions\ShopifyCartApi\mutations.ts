import {
  CartDeliveryAddressesUpdateMutationVariables,
  CartDeliveryAddressesUpdateMutation,
  CartDeliveryAddressesUpdateDocument,
  useCartDeliveryAddressesUpdateMutation,
  CartDeliveryAddressesAddMutation,
  CartDeliveryAddressesAddMutationVariables,
  CartDeliveryAddressesAddDocument,
  useCartDeliveryAddressesAddMutation,
  MailingAddress,
  CountryCode,
} from '../../datasource/api-react-query';
import { runTanstackQueryMutation } from '../../datasource/fetcher';
import { getCart } from './helper';

// WIP TODO
const updateCartDeliveryAddress = async (address: MailingAddress) => {
  const currentCart = getCart();
  const cartId = currentCart?.id;
  if (!cartId) {
    return { error: 'Cart ID not found' };
  }
  const add = [
    {
      address: address,
      selected: true,
      id: 'dfg', // todo,
      oneTimeUse: false,
    },
  ];
  const variables: CartDeliveryAddressesUpdateMutationVariables = {
    cartId,
    addresses: add,
  };

  try {
    const response = await runTanstackQueryMutation<
      CartDeliveryAddressesUpdateMutation,
      CartDeliveryAddressesUpdateMutationVariables
    >(
      CartDeliveryAddressesUpdateDocument,
      variables,
      useCartDeliveryAddressesUpdateMutation.getKey(),
    );
    return {
      cart: response?.cartDeliveryAddressesUpdate?.cart,
      userErrors: response?.cartDeliveryAddressesUpdate?.userErrors || [],
    };
  } catch (error) {
    console.error('Exception while updating cart delivery address:', error);
    return { error: error.message || 'Failed to update delivery address' };
  }
};

export const addCartDeliveryAddress = async (address: any) => {
  const currentCart = getCart();
  const cartId = currentCart?.id;
  if (!cartId) {
    return { error: 'Cart ID not found' };
  }
  try {
  // Format the address for the cartDeliveryAddressesAdd mutation
  const addressInput = {
    address: {
      deliveryAddress: {
        address1: address?.address1,
        address2: address?.address2,
        city: address?.city,
        ...(address?.company && { company: address?.company }),
        countryCode: address?.countryCode, // This should be a CountryCode enum
        firstName: address?.firstName,
        lastName: address?.lastName,
        phone: address?.phone,
        provinceCode: address?.provinceCode,
        zip: address?.zip
      }
    },
    selected: true,
    oneTimeUse: false
  };
  
  const variables: CartDeliveryAddressesAddMutationVariables = {
    cartId,
    addresses: [addressInput]
  };

    const response = await runTanstackQueryMutation<
      CartDeliveryAddressesAddMutation,
      CartDeliveryAddressesAddMutationVariables
    >(
      CartDeliveryAddressesAddDocument,
      variables,
      useCartDeliveryAddressesAddMutation.getKey(),
    );
    return {
      cart: response?.cartDeliveryAddressesAdd?.cart,
      userErrors: response?.cartDeliveryAddressesAdd?.userErrors || [],
    };
  } catch (error) {
    return { error: error.message || 'Failed to add delivery address' };
  }
};
