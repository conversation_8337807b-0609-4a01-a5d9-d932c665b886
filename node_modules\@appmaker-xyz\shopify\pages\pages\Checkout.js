import { getActionFromURL } from '../../helper/deeplink';
import {
  getShopifyBaseUrl,
  isOrderCompleted,
  webviewDeepLinkEnabled,
} from '../../helper/helper';
const onUrlChange = async (url, onAction) => {
  if (isOrderCompleted(url)) {
    onAction({
      action: 'SET_ORDER_COMPLETE',
    });
  }
  if (webviewDeepLinkEnabled()) {
    const action = await getActionFromURL(url, {
      shopifyURL: getShopifyBaseUrl(),
      isWebView: true,
    });
    if (action) {
      return onAction(action);
    }
  }
};

const page = {
  id: 'checkout',
  status: 'active',
  title: 'Checkout',
  attributes: {
    renderType: 'normal',
    contentContainerStyle: { flex: 1 },
    rootContainerStyle: { flex: 1 },
  },
  blocks: [
    {
      name: 'appmaker/webview',
      isValid: true,
      clientId: 'f496b61a-56c9-4862-b4a5-d5438bb530aa',
      attributes: {
        loadingLayout: 'home',
        urlListener: onUrlChange,
        source: '{{blockData.source}}',
      },
      dependencies: {
        appStorageState: ['checkout'],
      },
    },
  ],
};
export default page;
