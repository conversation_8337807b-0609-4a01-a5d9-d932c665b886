import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ThemeText } from '../../uikit/index';
import { useProductDetail } from '@appmaker-xyz/shopify/hooks/useProductDetail';
import Icon from 'react-native-vector-icons/AntDesign';

const ProductData = (props) => {
  const {
    buyNowLoading,
    addtoCartLoading,
    addToCart,
    buyNow,
    title,
    tax_included_text,
    product_subtitle,
    average_rating,
    reviews_count,
    salePrice,
    regularPrice,
  } = useProductDetail(props);
  return (
    <Layout style={styles.container}>
      <Layout>
        <ThemeText fontFamily="bold" size="lg" color={'#0F172A'}>
          {title}
        </ThemeText>
        {product_subtitle ? (
          <ThemeText size="sm" color={'#64748B'}>
            {product_subtitle}
          </ThemeText>
        ) : null}
        <Layout style={styles.priceContainer}>
          <ThemeText fontFamily="medium" size="lg" color={'#0F172A'}>
            {salePrice}
          </ThemeText>
          <ThemeText style={styles.strikeText} color="#94A3B8">
            {regularPrice}
          </ThemeText>
        </Layout>
        {tax_included_text ? <ThemeText>{tax_included_text}</ThemeText> : null}
      </Layout>
      {average_rating ? (
        <Layout style={styles.starRating}>
          <Layout style={styles.starReview}>
            <Icon
              name="star"
              size={16}
              color="#FBBF24"
              style={styles.starIcon}
            />
            <ThemeText size="md" fontFamily="medium" color={'#0F172A'}>
              {average_rating}
            </ThemeText>
          </Layout>
          {reviews_count ? (
            <ThemeText size="sm" color={'#475569'}>
              {reviews_count} Review
            </ThemeText>
          ) : null}
        </Layout>
      ) : null}
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 12,
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
  },
  strikeText: {
    textDecorationLine: 'line-through',
    marginLeft: 6,
  },
  starRating: {
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
    backgroundColor: '#F8FAFC',
    borderRadius: 8,
  },
  starIcon: {
    marginRight: 4,
  },
  starReview: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoIcon: {
    marginLeft: 2,
    padding: 4,
  },
});

export default ProductData;
