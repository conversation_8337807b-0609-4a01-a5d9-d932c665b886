import { handleAction } from '@appmaker-xyz/react-native';
import {
  NavigationAction,
  OpenCollectionInput,
  OpenProductInput,
  OpenSearchResultInput,
} from './navigation';

export const ShopifyNavigation = {
  openProduct: (input: OpenProductInput) =>
    handleAction(NavigationAction.openProduct(input)),
  openCollection: (input: OpenCollectionInput) =>
    handleAction(NavigationAction.openCollection(input)),
  openProductSearchResult: (input: OpenSearchResultInput) =>
    handleAction(NavigationAction.openSearchResult(input)),
  openOrders: () => handleAction(NavigationAction?.openOrderList()),
  openWishlist: () => handleAction(NavigationAction?.openWishlist()),
  openCart: () => handleAction(NavigationAction?.openCart()),
  openWebview: ({ url }: { url: string }) =>
    handleAction(NavigationAction?.openWebview({ url })),
  openUrl: ({ url }: { url: string }) =>
    handleAction(NavigationAction?.openUrl({ url })),
  openOrderDetail: (input: { id: string }) =>
    handleAction(
      NavigationAction.openOrderDetail({ orderItem: { id: input.id } }),
    ),
  openSearch: () => handleAction(NavigationAction.openSearch()),
  openAccount: () => handleAction(NavigationAction.openMyAccount()),
  openLogin: () => handleAction(NavigationAction.openLoginPage()),
  openRegister: () => handleAction(NavigationAction.openRegister()),
  openForgotPassword: () => handleAction(NavigationAction.openResetPassword()),
  openInAppPage: (input: { pageId: string }) =>
    handleAction(NavigationAction.openInAppPage(input)),
  logout: () => handleAction(NavigationAction.logout()),
  toggleDrawer: () => handleAction(NavigationAction.toggleDrawer()),
  goBack: () => handleAction(NavigationAction.goBack()),
  showToastMessage: (input: { message: string }) =>
    handleAction(NavigationAction.showToastMessage(input)),
  goToHome: () => handleAction(NavigationAction.goToHome()),
};
