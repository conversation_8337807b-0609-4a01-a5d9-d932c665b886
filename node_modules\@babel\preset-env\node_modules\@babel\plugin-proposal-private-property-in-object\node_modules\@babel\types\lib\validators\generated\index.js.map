{"version": 3, "names": ["_shallowEqual", "require", "_deprecationWarning", "isArrayExpression", "node", "opts", "nodeType", "type", "shallowEqual", "isAssignmentExpression", "isBinaryExpression", "isInterpreterDirective", "isDirective", "isDirectiveLiteral", "isBlockStatement", "isBreakStatement", "isCallExpression", "isCatchClause", "isConditionalExpression", "isContinueStatement", "isDebuggerStatement", "isDoWhileStatement", "isEmptyStatement", "isExpressionStatement", "isFile", "isForInStatement", "isForStatement", "isFunctionDeclaration", "isFunctionExpression", "isIdentifier", "isIfStatement", "isLabeledStatement", "isStringLiteral", "isNumericLiteral", "is<PERSON>ull<PERSON>iteral", "isBooleanLiteral", "isRegExpLiteral", "isLogicalExpression", "isMemberExpression", "isNewExpression", "isProgram", "isObjectExpression", "isObjectMethod", "isObjectProperty", "isRestElement", "isReturnStatement", "isSequenceExpression", "isParenthesizedExpression", "isSwitchCase", "isSwitchStatement", "isThisExpression", "isThrowStatement", "isTryStatement", "isUnaryExpression", "isUpdateExpression", "isVariableDeclaration", "isVariableDeclarator", "isWhileStatement", "isWithStatement", "isAssignmentPattern", "isArrayPattern", "isArrowFunctionExpression", "isClassBody", "isClassExpression", "isClassDeclaration", "isExportAllDeclaration", "isExportDefaultDeclaration", "isExportNamedDeclaration", "isExportSpecifier", "isForOfStatement", "isImportDeclaration", "isImportDefaultSpecifier", "isImportNamespaceSpecifier", "isImportSpecifier", "isMetaProperty", "isClassMethod", "isObjectPattern", "isSpreadElement", "is<PERSON><PERSON><PERSON>", "isTaggedTemplateExpression", "isTemplateElement", "isTemplateLiteral", "isYieldExpression", "isAwaitExpression", "isImport", "isBigIntLiteral", "isExportNamespaceSpecifier", "isOptionalMemberExpression", "isOptionalCallExpression", "isClassProperty", "isClassAccessorProperty", "isClassPrivateProperty", "isClassPrivateMethod", "isPrivateName", "isStaticBlock", "isAnyTypeAnnotation", "isArrayTypeAnnotation", "isBooleanTypeAnnotation", "isBooleanLiteralTypeAnnotation", "isNullLiteralTypeAnnotation", "isClassImplements", "isDeclareClass", "isDeclareFunction", "isDeclareInterface", "isDeclareModule", "isDeclareModuleExports", "isDeclareTypeAlias", "isDeclareOpaqueType", "isDeclareVariable", "isDeclareExportDeclaration", "isDeclareExportAllDeclaration", "isDeclaredPredicate", "isExistsTypeAnnotation", "isFunctionTypeAnnotation", "isFunctionTypeParam", "isGenericTypeAnnotation", "isInferredPredicate", "isInterfaceExtends", "isInterfaceDeclaration", "isInterfaceTypeAnnotation", "isIntersectionTypeAnnotation", "isMixedTypeAnnotation", "isEmptyTypeAnnotation", "isNullableTypeAnnotation", "isNumberLiteralTypeAnnotation", "isNumberTypeAnnotation", "isObjectTypeAnnotation", "isObjectTypeInternalSlot", "isObjectTypeCallProperty", "isObjectTypeIndexer", "isObjectTypeProperty", "isObjectTypeSpreadProperty", "isOpaqueType", "isQualifiedTypeIdentifier", "isStringLiteralTypeAnnotation", "isStringTypeAnnotation", "isSymbolTypeAnnotation", "isThisTypeAnnotation", "isTupleTypeAnnotation", "isTypeofTypeAnnotation", "isTypeAlias", "isTypeAnnotation", "isTypeCastExpression", "isTypeParameter", "isTypeParameterDeclaration", "isTypeParameterInstantiation", "isUnionTypeAnnotation", "is<PERSON><PERSON>ce", "isVoidTypeAnnotation", "isEnumDeclaration", "isEnumBooleanBody", "isEnumNumberBody", "isEnumStringBody", "isEnumSymbolBody", "isEnumBooleanMember", "isEnumNumberMember", "isEnumStringMember", "isEnumDefaultedMember", "isIndexedAccessType", "isOptionalIndexedAccessType", "isJSXAttribute", "isJSXClosingElement", "isJSXElement", "isJSXEmptyExpression", "isJSXExpressionContainer", "isJSXSpreadChild", "isJSXIdentifier", "isJSXMemberExpression", "isJSXNamespacedName", "isJSXOpeningElement", "isJSXSpreadAttribute", "isJSXText", "isJSXFragment", "isJSXOpeningFragment", "isJSXClosingFragment", "isNoop", "isPlaceholder", "isV8IntrinsicIdentifier", "isArgumentPlaceholder", "isBindExpression", "isImportAttribute", "isDecorator", "isDoExpression", "isExportDefaultSpecifier", "isRecordExpression", "isTupleExpression", "isDecimalLiteral", "isModuleExpression", "isTopicReference", "isPipelineTopicExpression", "isPipelineBareFunction", "isPipelinePrimaryTopicReference", "isTSParameterProperty", "isTSDeclareFunction", "isTSDeclareMethod", "isTSQualifiedName", "isTSCallSignatureDeclaration", "isTSConstructSignatureDeclaration", "isTSPropertySignature", "isTSMethodSignature", "isTSIndexSignature", "isTSAnyKeyword", "isTSBooleanKeyword", "isTSBigIntKeyword", "isTSIntrinsicKeyword", "isTSNeverKeyword", "isTSNullKeyword", "isTSNumberKeyword", "isTSObjectKeyword", "isTSStringKeyword", "isTSSymbolKeyword", "isTSUndefinedKeyword", "isTSUnknownKeyword", "isTSVoidKeyword", "isTSThisType", "isTSFunctionType", "isTSConstructorType", "isTSTypeReference", "isTSTypePredicate", "isTSTypeQuery", "isTSTypeLiteral", "isTSArrayType", "isTSTupleType", "isTSOptionalType", "isTSRestType", "isTSNamedTupleMember", "isTSUnionType", "isTSIntersectionType", "isTSConditionalType", "isTSInferType", "isTSParenthesizedType", "isTSTypeOperator", "isTSIndexedAccessType", "isTSMappedType", "isTSLiteralType", "isTSExpressionWithTypeArguments", "isTSInterfaceDeclaration", "isTSInterfaceBody", "isTSTypeAliasDeclaration", "isTSInstantiationExpression", "isTSAsExpression", "isTSSatisfiesExpression", "isTSTypeAssertion", "isTSEnumDeclaration", "isTSEnumMember", "isTSModuleDeclaration", "isTSModuleBlock", "isTSImportType", "isTSImportEqualsDeclaration", "isTSExternalModuleReference", "isTSNonNullExpression", "isTSExportAssignment", "isTSNamespaceExportDeclaration", "isTSTypeAnnotation", "isTSTypeParameterInstantiation", "isTSTypeParameterDeclaration", "isTSTypeParameter", "isStandardized", "expectedNode", "isExpression", "isBinary", "isScopable", "isBlockParent", "isBlock", "isStatement", "isTerminatorless", "isCompletionStatement", "isConditional", "isLoop", "<PERSON><PERSON><PERSON><PERSON>", "isExpressionWrapper", "isFor", "isForXStatement", "isFunction", "isFunctionParent", "isPureish", "isDeclaration", "isPatternLike", "isLVal", "isTSEntityName", "isLiteral", "isImmutable", "isUserWhitespacable", "isMethod", "isObjectMember", "isProperty", "isUnaryLike", "isPattern", "isClass", "isImportOrExportDeclaration", "isExportDeclaration", "isModuleSpecifier", "isAccessor", "isPrivate", "isFlow", "isFlowType", "isFlowBaseAnnotation", "isFlowDeclaration", "isFlowPredicate", "isEnumBody", "isEnumMember", "isJSX", "isMiscellaneous", "isTypeScript", "isTSTypeElement", "isTSType", "isTSBaseType", "isNumberLiteral", "deprecationWarning", "isRegexLiteral", "isRestProperty", "isSpreadProperty", "isModuleDeclaration"], "sources": ["../../../src/validators/generated/index.ts"], "sourcesContent": ["/*\n * This file is auto-generated! Do not modify it directly.\n * To re-generate run 'make build'\n */\nimport shallowEqual from \"../../utils/shallowEqual\";\nimport type * as t from \"../..\";\nimport deprecationWarning from \"../../utils/deprecationWarning\";\n\nexport function isArrayExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ArrayExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ArrayExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isAssignmentExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.AssignmentExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"AssignmentExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBinaryExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BinaryExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BinaryExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isInterpreterDirective(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.InterpreterDirective {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"InterpreterDirective\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDirective(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Directive {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Directive\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDirectiveLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DirectiveLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DirectiveLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBlockStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BlockStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BlockStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBreakStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BreakStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BreakStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isCallExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.CallExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"CallExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isCatchClause(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.CatchClause {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"CatchClause\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isConditionalExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ConditionalExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ConditionalExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isContinueStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ContinueStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ContinueStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDebuggerStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DebuggerStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DebuggerStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDoWhileStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DoWhileStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DoWhileStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEmptyStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EmptyStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EmptyStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExpressionStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExpressionStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExpressionStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFile(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.File {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"File\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isForInStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ForInStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ForInStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isForStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ForStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ForStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFunctionDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FunctionDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"FunctionDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFunctionExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FunctionExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"FunctionExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isIdentifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Identifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Identifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isIfStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.IfStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"IfStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isLabeledStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.LabeledStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"LabeledStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isStringLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.StringLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"StringLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNumericLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.NumericLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NumericLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNullLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.NullLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NullLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBooleanLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BooleanLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BooleanLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isRegExpLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.RegExpLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"RegExpLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isLogicalExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.LogicalExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"LogicalExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isMemberExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.MemberExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"MemberExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNewExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.NewExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NewExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isProgram(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Program {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Program\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectMethod(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectMethod {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectMethod\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isRestElement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.RestElement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"RestElement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isReturnStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ReturnStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ReturnStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isSequenceExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.SequenceExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"SequenceExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isParenthesizedExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ParenthesizedExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ParenthesizedExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isSwitchCase(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.SwitchCase {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"SwitchCase\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isSwitchStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.SwitchStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"SwitchStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isThisExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ThisExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ThisExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isThrowStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ThrowStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ThrowStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTryStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TryStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TryStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isUnaryExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.UnaryExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"UnaryExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isUpdateExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.UpdateExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"UpdateExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isVariableDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.VariableDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"VariableDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isVariableDeclarator(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.VariableDeclarator {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"VariableDeclarator\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isWhileStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.WhileStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"WhileStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isWithStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.WithStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"WithStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isAssignmentPattern(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.AssignmentPattern {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"AssignmentPattern\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isArrayPattern(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ArrayPattern {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ArrayPattern\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isArrowFunctionExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ArrowFunctionExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ArrowFunctionExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassBody(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassBody {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassBody\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExportAllDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExportAllDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExportAllDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExportDefaultDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExportDefaultDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExportDefaultDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExportNamedDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExportNamedDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExportNamedDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExportSpecifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExportSpecifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExportSpecifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isForOfStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ForOfStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ForOfStatement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImportDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ImportDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ImportDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImportDefaultSpecifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ImportDefaultSpecifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ImportDefaultSpecifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImportNamespaceSpecifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ImportNamespaceSpecifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ImportNamespaceSpecifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImportSpecifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ImportSpecifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ImportSpecifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isMetaProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.MetaProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"MetaProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassMethod(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassMethod {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassMethod\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectPattern(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectPattern {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectPattern\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isSpreadElement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.SpreadElement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"SpreadElement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isSuper(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Super {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Super\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTaggedTemplateExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TaggedTemplateExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TaggedTemplateExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTemplateElement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TemplateElement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TemplateElement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTemplateLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TemplateLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TemplateLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isYieldExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.YieldExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"YieldExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isAwaitExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.AwaitExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"AwaitExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImport(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Import {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Import\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBigIntLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BigIntLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BigIntLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExportNamespaceSpecifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExportNamespaceSpecifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExportNamespaceSpecifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isOptionalMemberExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.OptionalMemberExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"OptionalMemberExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isOptionalCallExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.OptionalCallExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"OptionalCallExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassAccessorProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassAccessorProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassAccessorProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassPrivateProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassPrivateProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassPrivateProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassPrivateMethod(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassPrivateMethod {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassPrivateMethod\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPrivateName(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.PrivateName {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"PrivateName\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isStaticBlock(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.StaticBlock {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"StaticBlock\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isAnyTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.AnyTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"AnyTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isArrayTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ArrayTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ArrayTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBooleanTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BooleanTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BooleanTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBooleanLiteralTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BooleanLiteralTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BooleanLiteralTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNullLiteralTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.NullLiteralTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NullLiteralTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClassImplements(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ClassImplements {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ClassImplements\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareClass(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareClass {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareClass\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareFunction(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareFunction {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareFunction\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareInterface(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareInterface {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareInterface\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareModule(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareModule {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareModule\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareModuleExports(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareModuleExports {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareModuleExports\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareTypeAlias(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareTypeAlias {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareTypeAlias\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareOpaqueType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareOpaqueType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareOpaqueType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareVariable(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareVariable {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareVariable\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareExportDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareExportDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareExportDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclareExportAllDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclareExportAllDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclareExportAllDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclaredPredicate(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DeclaredPredicate {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DeclaredPredicate\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExistsTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExistsTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExistsTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFunctionTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FunctionTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"FunctionTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFunctionTypeParam(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FunctionTypeParam {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"FunctionTypeParam\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isGenericTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.GenericTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"GenericTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isInferredPredicate(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.InferredPredicate {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"InferredPredicate\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isInterfaceExtends(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.InterfaceExtends {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"InterfaceExtends\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isInterfaceDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.InterfaceDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"InterfaceDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isInterfaceTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.InterfaceTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"InterfaceTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isIntersectionTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.IntersectionTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"IntersectionTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isMixedTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.MixedTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"MixedTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEmptyTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EmptyTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EmptyTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNullableTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.NullableTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NullableTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNumberLiteralTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.NumberLiteralTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NumberLiteralTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNumberTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.NumberTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NumberTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectTypeInternalSlot(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectTypeInternalSlot {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectTypeInternalSlot\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectTypeCallProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectTypeCallProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectTypeCallProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectTypeIndexer(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectTypeIndexer {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectTypeIndexer\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectTypeProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectTypeProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectTypeProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectTypeSpreadProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectTypeSpreadProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ObjectTypeSpreadProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isOpaqueType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.OpaqueType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"OpaqueType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isQualifiedTypeIdentifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.QualifiedTypeIdentifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"QualifiedTypeIdentifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isStringLiteralTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.StringLiteralTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"StringLiteralTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isStringTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.StringTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"StringTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isSymbolTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.SymbolTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"SymbolTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isThisTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ThisTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ThisTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTupleTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TupleTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TupleTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeofTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeofTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TypeofTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeAlias(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeAlias {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TypeAlias\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeCastExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeCastExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TypeCastExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeParameter(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeParameter {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TypeParameter\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeParameterDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeParameterDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TypeParameterDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeParameterInstantiation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeParameterInstantiation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TypeParameterInstantiation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isUnionTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.UnionTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"UnionTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isVariance(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Variance {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Variance\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isVoidTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.VoidTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"VoidTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumBooleanBody(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumBooleanBody {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumBooleanBody\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumNumberBody(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumNumberBody {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumNumberBody\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumStringBody(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumStringBody {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumStringBody\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumSymbolBody(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumSymbolBody {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumSymbolBody\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumBooleanMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumBooleanMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumBooleanMember\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumNumberMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumNumberMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumNumberMember\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumStringMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumStringMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumStringMember\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumDefaultedMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumDefaultedMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"EnumDefaultedMember\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isIndexedAccessType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.IndexedAccessType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"IndexedAccessType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isOptionalIndexedAccessType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.OptionalIndexedAccessType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"OptionalIndexedAccessType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXAttribute(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXAttribute {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXAttribute\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXClosingElement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXClosingElement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXClosingElement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXElement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXElement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXElement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXEmptyExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXEmptyExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXEmptyExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXExpressionContainer(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXExpressionContainer {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXExpressionContainer\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXSpreadChild(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXSpreadChild {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXSpreadChild\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXIdentifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXIdentifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXIdentifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXMemberExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXMemberExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXMemberExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXNamespacedName(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXNamespacedName {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXNamespacedName\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXOpeningElement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXOpeningElement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXOpeningElement\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXSpreadAttribute(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXSpreadAttribute {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXSpreadAttribute\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXText(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXText {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXText\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXFragment(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXFragment {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXFragment\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXOpeningFragment(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXOpeningFragment {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXOpeningFragment\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSXClosingFragment(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSXClosingFragment {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"JSXClosingFragment\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNoop(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Noop {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Noop\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPlaceholder(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Placeholder {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Placeholder\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isV8IntrinsicIdentifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.V8IntrinsicIdentifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"V8IntrinsicIdentifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isArgumentPlaceholder(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ArgumentPlaceholder {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ArgumentPlaceholder\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBindExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BindExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"BindExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImportAttribute(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ImportAttribute {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ImportAttribute\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDecorator(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Decorator {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"Decorator\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDoExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DoExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DoExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExportDefaultSpecifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExportDefaultSpecifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ExportDefaultSpecifier\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isRecordExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.RecordExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"RecordExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTupleExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TupleExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TupleExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDecimalLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.DecimalLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"DecimalLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isModuleExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ModuleExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"ModuleExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTopicReference(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TopicReference {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TopicReference\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPipelineTopicExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.PipelineTopicExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"PipelineTopicExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPipelineBareFunction(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.PipelineBareFunction {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"PipelineBareFunction\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPipelinePrimaryTopicReference(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.PipelinePrimaryTopicReference {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"PipelinePrimaryTopicReference\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSParameterProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSParameterProperty {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSParameterProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSDeclareFunction(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSDeclareFunction {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSDeclareFunction\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSDeclareMethod(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSDeclareMethod {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSDeclareMethod\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSQualifiedName(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSQualifiedName {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSQualifiedName\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSCallSignatureDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSCallSignatureDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSCallSignatureDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSConstructSignatureDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSConstructSignatureDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSConstructSignatureDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSPropertySignature(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSPropertySignature {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSPropertySignature\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSMethodSignature(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSMethodSignature {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSMethodSignature\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSIndexSignature(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSIndexSignature {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSIndexSignature\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSAnyKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSAnyKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSAnyKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSBooleanKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSBooleanKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSBooleanKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSBigIntKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSBigIntKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSBigIntKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSIntrinsicKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSIntrinsicKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSIntrinsicKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSNeverKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSNeverKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSNeverKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSNullKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSNullKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSNullKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSNumberKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSNumberKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSNumberKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSObjectKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSObjectKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSObjectKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSStringKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSStringKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSStringKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSSymbolKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSSymbolKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSSymbolKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSUndefinedKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSUndefinedKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSUndefinedKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSUnknownKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSUnknownKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSUnknownKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSVoidKeyword(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSVoidKeyword {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSVoidKeyword\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSThisType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSThisType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSThisType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSFunctionType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSFunctionType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSFunctionType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSConstructorType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSConstructorType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSConstructorType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeReference(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeReference {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeReference\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypePredicate(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypePredicate {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypePredicate\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeQuery(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeQuery {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeQuery\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeLiteral {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSArrayType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSArrayType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSArrayType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTupleType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTupleType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTupleType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSOptionalType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSOptionalType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSOptionalType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSRestType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSRestType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSRestType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSNamedTupleMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSNamedTupleMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSNamedTupleMember\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSUnionType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSUnionType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSUnionType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSIntersectionType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSIntersectionType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSIntersectionType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSConditionalType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSConditionalType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSConditionalType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSInferType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSInferType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSInferType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSParenthesizedType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSParenthesizedType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSParenthesizedType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeOperator(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeOperator {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeOperator\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSIndexedAccessType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSIndexedAccessType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSIndexedAccessType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSMappedType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSMappedType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSMappedType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSLiteralType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSLiteralType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSLiteralType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSExpressionWithTypeArguments(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSExpressionWithTypeArguments {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSExpressionWithTypeArguments\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSInterfaceDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSInterfaceDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSInterfaceDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSInterfaceBody(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSInterfaceBody {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSInterfaceBody\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeAliasDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeAliasDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeAliasDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSInstantiationExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSInstantiationExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSInstantiationExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSAsExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSAsExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSAsExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSSatisfiesExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSSatisfiesExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSSatisfiesExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeAssertion(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeAssertion {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeAssertion\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSEnumDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSEnumDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSEnumDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSEnumMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSEnumMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSEnumMember\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSModuleDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSModuleDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSModuleDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSModuleBlock(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSModuleBlock {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSModuleBlock\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSImportType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSImportType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSImportType\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSImportEqualsDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSImportEqualsDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSImportEqualsDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSExternalModuleReference(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSExternalModuleReference {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSExternalModuleReference\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSNonNullExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSNonNullExpression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSNonNullExpression\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSExportAssignment(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSExportAssignment {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSExportAssignment\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSNamespaceExportDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSNamespaceExportDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSNamespaceExportDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeAnnotation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeParameterInstantiation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeParameterInstantiation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeParameterInstantiation\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeParameterDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeParameterDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeParameterDeclaration\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeParameter(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeParameter {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"TSTypeParameter\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isStandardized(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Standardized {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ArrayExpression\" === nodeType ||\n    \"AssignmentExpression\" === nodeType ||\n    \"BinaryExpression\" === nodeType ||\n    \"InterpreterDirective\" === nodeType ||\n    \"Directive\" === nodeType ||\n    \"DirectiveLiteral\" === nodeType ||\n    \"BlockStatement\" === nodeType ||\n    \"BreakStatement\" === nodeType ||\n    \"CallExpression\" === nodeType ||\n    \"CatchClause\" === nodeType ||\n    \"ConditionalExpression\" === nodeType ||\n    \"ContinueStatement\" === nodeType ||\n    \"DebuggerStatement\" === nodeType ||\n    \"DoWhileStatement\" === nodeType ||\n    \"EmptyStatement\" === nodeType ||\n    \"ExpressionStatement\" === nodeType ||\n    \"File\" === nodeType ||\n    \"ForInStatement\" === nodeType ||\n    \"ForStatement\" === nodeType ||\n    \"FunctionDeclaration\" === nodeType ||\n    \"FunctionExpression\" === nodeType ||\n    \"Identifier\" === nodeType ||\n    \"IfStatement\" === nodeType ||\n    \"LabeledStatement\" === nodeType ||\n    \"StringLiteral\" === nodeType ||\n    \"NumericLiteral\" === nodeType ||\n    \"NullLiteral\" === nodeType ||\n    \"BooleanLiteral\" === nodeType ||\n    \"RegExpLiteral\" === nodeType ||\n    \"LogicalExpression\" === nodeType ||\n    \"MemberExpression\" === nodeType ||\n    \"NewExpression\" === nodeType ||\n    \"Program\" === nodeType ||\n    \"ObjectExpression\" === nodeType ||\n    \"ObjectMethod\" === nodeType ||\n    \"ObjectProperty\" === nodeType ||\n    \"RestElement\" === nodeType ||\n    \"ReturnStatement\" === nodeType ||\n    \"SequenceExpression\" === nodeType ||\n    \"ParenthesizedExpression\" === nodeType ||\n    \"SwitchCase\" === nodeType ||\n    \"SwitchStatement\" === nodeType ||\n    \"ThisExpression\" === nodeType ||\n    \"ThrowStatement\" === nodeType ||\n    \"TryStatement\" === nodeType ||\n    \"UnaryExpression\" === nodeType ||\n    \"UpdateExpression\" === nodeType ||\n    \"VariableDeclaration\" === nodeType ||\n    \"VariableDeclarator\" === nodeType ||\n    \"WhileStatement\" === nodeType ||\n    \"WithStatement\" === nodeType ||\n    \"AssignmentPattern\" === nodeType ||\n    \"ArrayPattern\" === nodeType ||\n    \"ArrowFunctionExpression\" === nodeType ||\n    \"ClassBody\" === nodeType ||\n    \"ClassExpression\" === nodeType ||\n    \"ClassDeclaration\" === nodeType ||\n    \"ExportAllDeclaration\" === nodeType ||\n    \"ExportDefaultDeclaration\" === nodeType ||\n    \"ExportNamedDeclaration\" === nodeType ||\n    \"ExportSpecifier\" === nodeType ||\n    \"ForOfStatement\" === nodeType ||\n    \"ImportDeclaration\" === nodeType ||\n    \"ImportDefaultSpecifier\" === nodeType ||\n    \"ImportNamespaceSpecifier\" === nodeType ||\n    \"ImportSpecifier\" === nodeType ||\n    \"MetaProperty\" === nodeType ||\n    \"ClassMethod\" === nodeType ||\n    \"ObjectPattern\" === nodeType ||\n    \"SpreadElement\" === nodeType ||\n    \"Super\" === nodeType ||\n    \"TaggedTemplateExpression\" === nodeType ||\n    \"TemplateElement\" === nodeType ||\n    \"TemplateLiteral\" === nodeType ||\n    \"YieldExpression\" === nodeType ||\n    \"AwaitExpression\" === nodeType ||\n    \"Import\" === nodeType ||\n    \"BigIntLiteral\" === nodeType ||\n    \"ExportNamespaceSpecifier\" === nodeType ||\n    \"OptionalMemberExpression\" === nodeType ||\n    \"OptionalCallExpression\" === nodeType ||\n    \"ClassProperty\" === nodeType ||\n    \"ClassAccessorProperty\" === nodeType ||\n    \"ClassPrivateProperty\" === nodeType ||\n    \"ClassPrivateMethod\" === nodeType ||\n    \"PrivateName\" === nodeType ||\n    \"StaticBlock\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      (\"Identifier\" === (node as t.Placeholder).expectedNode ||\n        \"StringLiteral\" === (node as t.Placeholder).expectedNode ||\n        \"BlockStatement\" === (node as t.Placeholder).expectedNode ||\n        \"ClassBody\" === (node as t.Placeholder).expectedNode))\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExpression(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Expression {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ArrayExpression\" === nodeType ||\n    \"AssignmentExpression\" === nodeType ||\n    \"BinaryExpression\" === nodeType ||\n    \"CallExpression\" === nodeType ||\n    \"ConditionalExpression\" === nodeType ||\n    \"FunctionExpression\" === nodeType ||\n    \"Identifier\" === nodeType ||\n    \"StringLiteral\" === nodeType ||\n    \"NumericLiteral\" === nodeType ||\n    \"NullLiteral\" === nodeType ||\n    \"BooleanLiteral\" === nodeType ||\n    \"RegExpLiteral\" === nodeType ||\n    \"LogicalExpression\" === nodeType ||\n    \"MemberExpression\" === nodeType ||\n    \"NewExpression\" === nodeType ||\n    \"ObjectExpression\" === nodeType ||\n    \"SequenceExpression\" === nodeType ||\n    \"ParenthesizedExpression\" === nodeType ||\n    \"ThisExpression\" === nodeType ||\n    \"UnaryExpression\" === nodeType ||\n    \"UpdateExpression\" === nodeType ||\n    \"ArrowFunctionExpression\" === nodeType ||\n    \"ClassExpression\" === nodeType ||\n    \"MetaProperty\" === nodeType ||\n    \"Super\" === nodeType ||\n    \"TaggedTemplateExpression\" === nodeType ||\n    \"TemplateLiteral\" === nodeType ||\n    \"YieldExpression\" === nodeType ||\n    \"AwaitExpression\" === nodeType ||\n    \"Import\" === nodeType ||\n    \"BigIntLiteral\" === nodeType ||\n    \"OptionalMemberExpression\" === nodeType ||\n    \"OptionalCallExpression\" === nodeType ||\n    \"TypeCastExpression\" === nodeType ||\n    \"JSXElement\" === nodeType ||\n    \"JSXFragment\" === nodeType ||\n    \"BindExpression\" === nodeType ||\n    \"DoExpression\" === nodeType ||\n    \"RecordExpression\" === nodeType ||\n    \"TupleExpression\" === nodeType ||\n    \"DecimalLiteral\" === nodeType ||\n    \"ModuleExpression\" === nodeType ||\n    \"TopicReference\" === nodeType ||\n    \"PipelineTopicExpression\" === nodeType ||\n    \"PipelineBareFunction\" === nodeType ||\n    \"PipelinePrimaryTopicReference\" === nodeType ||\n    \"TSInstantiationExpression\" === nodeType ||\n    \"TSAsExpression\" === nodeType ||\n    \"TSSatisfiesExpression\" === nodeType ||\n    \"TSTypeAssertion\" === nodeType ||\n    \"TSNonNullExpression\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      (\"Expression\" === (node as t.Placeholder).expectedNode ||\n        \"Identifier\" === (node as t.Placeholder).expectedNode ||\n        \"StringLiteral\" === (node as t.Placeholder).expectedNode))\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBinary(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Binary {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"BinaryExpression\" === nodeType || \"LogicalExpression\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isScopable(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Scopable {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"BlockStatement\" === nodeType ||\n    \"CatchClause\" === nodeType ||\n    \"DoWhileStatement\" === nodeType ||\n    \"ForInStatement\" === nodeType ||\n    \"ForStatement\" === nodeType ||\n    \"FunctionDeclaration\" === nodeType ||\n    \"FunctionExpression\" === nodeType ||\n    \"Program\" === nodeType ||\n    \"ObjectMethod\" === nodeType ||\n    \"SwitchStatement\" === nodeType ||\n    \"WhileStatement\" === nodeType ||\n    \"ArrowFunctionExpression\" === nodeType ||\n    \"ClassExpression\" === nodeType ||\n    \"ClassDeclaration\" === nodeType ||\n    \"ForOfStatement\" === nodeType ||\n    \"ClassMethod\" === nodeType ||\n    \"ClassPrivateMethod\" === nodeType ||\n    \"StaticBlock\" === nodeType ||\n    \"TSModuleBlock\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"BlockStatement\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBlockParent(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.BlockParent {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"BlockStatement\" === nodeType ||\n    \"CatchClause\" === nodeType ||\n    \"DoWhileStatement\" === nodeType ||\n    \"ForInStatement\" === nodeType ||\n    \"ForStatement\" === nodeType ||\n    \"FunctionDeclaration\" === nodeType ||\n    \"FunctionExpression\" === nodeType ||\n    \"Program\" === nodeType ||\n    \"ObjectMethod\" === nodeType ||\n    \"SwitchStatement\" === nodeType ||\n    \"WhileStatement\" === nodeType ||\n    \"ArrowFunctionExpression\" === nodeType ||\n    \"ForOfStatement\" === nodeType ||\n    \"ClassMethod\" === nodeType ||\n    \"ClassPrivateMethod\" === nodeType ||\n    \"StaticBlock\" === nodeType ||\n    \"TSModuleBlock\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"BlockStatement\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isBlock(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Block {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"BlockStatement\" === nodeType ||\n    \"Program\" === nodeType ||\n    \"TSModuleBlock\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"BlockStatement\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Statement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"BlockStatement\" === nodeType ||\n    \"BreakStatement\" === nodeType ||\n    \"ContinueStatement\" === nodeType ||\n    \"DebuggerStatement\" === nodeType ||\n    \"DoWhileStatement\" === nodeType ||\n    \"EmptyStatement\" === nodeType ||\n    \"ExpressionStatement\" === nodeType ||\n    \"ForInStatement\" === nodeType ||\n    \"ForStatement\" === nodeType ||\n    \"FunctionDeclaration\" === nodeType ||\n    \"IfStatement\" === nodeType ||\n    \"LabeledStatement\" === nodeType ||\n    \"ReturnStatement\" === nodeType ||\n    \"SwitchStatement\" === nodeType ||\n    \"ThrowStatement\" === nodeType ||\n    \"TryStatement\" === nodeType ||\n    \"VariableDeclaration\" === nodeType ||\n    \"WhileStatement\" === nodeType ||\n    \"WithStatement\" === nodeType ||\n    \"ClassDeclaration\" === nodeType ||\n    \"ExportAllDeclaration\" === nodeType ||\n    \"ExportDefaultDeclaration\" === nodeType ||\n    \"ExportNamedDeclaration\" === nodeType ||\n    \"ForOfStatement\" === nodeType ||\n    \"ImportDeclaration\" === nodeType ||\n    \"DeclareClass\" === nodeType ||\n    \"DeclareFunction\" === nodeType ||\n    \"DeclareInterface\" === nodeType ||\n    \"DeclareModule\" === nodeType ||\n    \"DeclareModuleExports\" === nodeType ||\n    \"DeclareTypeAlias\" === nodeType ||\n    \"DeclareOpaqueType\" === nodeType ||\n    \"DeclareVariable\" === nodeType ||\n    \"DeclareExportDeclaration\" === nodeType ||\n    \"DeclareExportAllDeclaration\" === nodeType ||\n    \"InterfaceDeclaration\" === nodeType ||\n    \"OpaqueType\" === nodeType ||\n    \"TypeAlias\" === nodeType ||\n    \"EnumDeclaration\" === nodeType ||\n    \"TSDeclareFunction\" === nodeType ||\n    \"TSInterfaceDeclaration\" === nodeType ||\n    \"TSTypeAliasDeclaration\" === nodeType ||\n    \"TSEnumDeclaration\" === nodeType ||\n    \"TSModuleDeclaration\" === nodeType ||\n    \"TSImportEqualsDeclaration\" === nodeType ||\n    \"TSExportAssignment\" === nodeType ||\n    \"TSNamespaceExportDeclaration\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      (\"Statement\" === (node as t.Placeholder).expectedNode ||\n        \"Declaration\" === (node as t.Placeholder).expectedNode ||\n        \"BlockStatement\" === (node as t.Placeholder).expectedNode))\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTerminatorless(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Terminatorless {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"BreakStatement\" === nodeType ||\n    \"ContinueStatement\" === nodeType ||\n    \"ReturnStatement\" === nodeType ||\n    \"ThrowStatement\" === nodeType ||\n    \"YieldExpression\" === nodeType ||\n    \"AwaitExpression\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isCompletionStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.CompletionStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"BreakStatement\" === nodeType ||\n    \"ContinueStatement\" === nodeType ||\n    \"ReturnStatement\" === nodeType ||\n    \"ThrowStatement\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isConditional(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Conditional {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"ConditionalExpression\" === nodeType || \"IfStatement\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isLoop(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Loop {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"DoWhileStatement\" === nodeType ||\n    \"ForInStatement\" === nodeType ||\n    \"ForStatement\" === nodeType ||\n    \"WhileStatement\" === nodeType ||\n    \"ForOfStatement\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isWhile(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.While {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"DoWhileStatement\" === nodeType || \"WhileStatement\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExpressionWrapper(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExpressionWrapper {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ExpressionStatement\" === nodeType ||\n    \"ParenthesizedExpression\" === nodeType ||\n    \"TypeCastExpression\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFor(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.For {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ForInStatement\" === nodeType ||\n    \"ForStatement\" === nodeType ||\n    \"ForOfStatement\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isForXStatement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ForXStatement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"ForInStatement\" === nodeType || \"ForOfStatement\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFunction(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Function {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"FunctionDeclaration\" === nodeType ||\n    \"FunctionExpression\" === nodeType ||\n    \"ObjectMethod\" === nodeType ||\n    \"ArrowFunctionExpression\" === nodeType ||\n    \"ClassMethod\" === nodeType ||\n    \"ClassPrivateMethod\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFunctionParent(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FunctionParent {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"FunctionDeclaration\" === nodeType ||\n    \"FunctionExpression\" === nodeType ||\n    \"ObjectMethod\" === nodeType ||\n    \"ArrowFunctionExpression\" === nodeType ||\n    \"ClassMethod\" === nodeType ||\n    \"ClassPrivateMethod\" === nodeType ||\n    \"StaticBlock\" === nodeType ||\n    \"TSModuleBlock\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPureish(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Pureish {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"FunctionDeclaration\" === nodeType ||\n    \"FunctionExpression\" === nodeType ||\n    \"StringLiteral\" === nodeType ||\n    \"NumericLiteral\" === nodeType ||\n    \"NullLiteral\" === nodeType ||\n    \"BooleanLiteral\" === nodeType ||\n    \"RegExpLiteral\" === nodeType ||\n    \"ArrowFunctionExpression\" === nodeType ||\n    \"BigIntLiteral\" === nodeType ||\n    \"DecimalLiteral\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"StringLiteral\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Declaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"FunctionDeclaration\" === nodeType ||\n    \"VariableDeclaration\" === nodeType ||\n    \"ClassDeclaration\" === nodeType ||\n    \"ExportAllDeclaration\" === nodeType ||\n    \"ExportDefaultDeclaration\" === nodeType ||\n    \"ExportNamedDeclaration\" === nodeType ||\n    \"ImportDeclaration\" === nodeType ||\n    \"DeclareClass\" === nodeType ||\n    \"DeclareFunction\" === nodeType ||\n    \"DeclareInterface\" === nodeType ||\n    \"DeclareModule\" === nodeType ||\n    \"DeclareModuleExports\" === nodeType ||\n    \"DeclareTypeAlias\" === nodeType ||\n    \"DeclareOpaqueType\" === nodeType ||\n    \"DeclareVariable\" === nodeType ||\n    \"DeclareExportDeclaration\" === nodeType ||\n    \"DeclareExportAllDeclaration\" === nodeType ||\n    \"InterfaceDeclaration\" === nodeType ||\n    \"OpaqueType\" === nodeType ||\n    \"TypeAlias\" === nodeType ||\n    \"EnumDeclaration\" === nodeType ||\n    \"TSDeclareFunction\" === nodeType ||\n    \"TSInterfaceDeclaration\" === nodeType ||\n    \"TSTypeAliasDeclaration\" === nodeType ||\n    \"TSEnumDeclaration\" === nodeType ||\n    \"TSModuleDeclaration\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"Declaration\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPatternLike(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.PatternLike {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"Identifier\" === nodeType ||\n    \"RestElement\" === nodeType ||\n    \"AssignmentPattern\" === nodeType ||\n    \"ArrayPattern\" === nodeType ||\n    \"ObjectPattern\" === nodeType ||\n    \"TSAsExpression\" === nodeType ||\n    \"TSSatisfiesExpression\" === nodeType ||\n    \"TSTypeAssertion\" === nodeType ||\n    \"TSNonNullExpression\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      (\"Pattern\" === (node as t.Placeholder).expectedNode ||\n        \"Identifier\" === (node as t.Placeholder).expectedNode))\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isLVal(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.LVal {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"Identifier\" === nodeType ||\n    \"MemberExpression\" === nodeType ||\n    \"RestElement\" === nodeType ||\n    \"AssignmentPattern\" === nodeType ||\n    \"ArrayPattern\" === nodeType ||\n    \"ObjectPattern\" === nodeType ||\n    \"TSParameterProperty\" === nodeType ||\n    \"TSAsExpression\" === nodeType ||\n    \"TSSatisfiesExpression\" === nodeType ||\n    \"TSTypeAssertion\" === nodeType ||\n    \"TSNonNullExpression\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      (\"Pattern\" === (node as t.Placeholder).expectedNode ||\n        \"Identifier\" === (node as t.Placeholder).expectedNode))\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSEntityName(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSEntityName {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"Identifier\" === nodeType ||\n    \"TSQualifiedName\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"Identifier\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Literal {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"StringLiteral\" === nodeType ||\n    \"NumericLiteral\" === nodeType ||\n    \"NullLiteral\" === nodeType ||\n    \"BooleanLiteral\" === nodeType ||\n    \"RegExpLiteral\" === nodeType ||\n    \"TemplateLiteral\" === nodeType ||\n    \"BigIntLiteral\" === nodeType ||\n    \"DecimalLiteral\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"StringLiteral\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImmutable(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Immutable {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"StringLiteral\" === nodeType ||\n    \"NumericLiteral\" === nodeType ||\n    \"NullLiteral\" === nodeType ||\n    \"BooleanLiteral\" === nodeType ||\n    \"BigIntLiteral\" === nodeType ||\n    \"JSXAttribute\" === nodeType ||\n    \"JSXClosingElement\" === nodeType ||\n    \"JSXElement\" === nodeType ||\n    \"JSXExpressionContainer\" === nodeType ||\n    \"JSXSpreadChild\" === nodeType ||\n    \"JSXOpeningElement\" === nodeType ||\n    \"JSXText\" === nodeType ||\n    \"JSXFragment\" === nodeType ||\n    \"JSXOpeningFragment\" === nodeType ||\n    \"JSXClosingFragment\" === nodeType ||\n    \"DecimalLiteral\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"StringLiteral\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isUserWhitespacable(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.UserWhitespacable {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ObjectMethod\" === nodeType ||\n    \"ObjectProperty\" === nodeType ||\n    \"ObjectTypeInternalSlot\" === nodeType ||\n    \"ObjectTypeCallProperty\" === nodeType ||\n    \"ObjectTypeIndexer\" === nodeType ||\n    \"ObjectTypeProperty\" === nodeType ||\n    \"ObjectTypeSpreadProperty\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isMethod(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Method {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ObjectMethod\" === nodeType ||\n    \"ClassMethod\" === nodeType ||\n    \"ClassPrivateMethod\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isObjectMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ObjectMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"ObjectMethod\" === nodeType || \"ObjectProperty\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Property {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ObjectProperty\" === nodeType ||\n    \"ClassProperty\" === nodeType ||\n    \"ClassAccessorProperty\" === nodeType ||\n    \"ClassPrivateProperty\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isUnaryLike(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.UnaryLike {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"UnaryExpression\" === nodeType || \"SpreadElement\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPattern(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Pattern {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"AssignmentPattern\" === nodeType ||\n    \"ArrayPattern\" === nodeType ||\n    \"ObjectPattern\" === nodeType ||\n    (nodeType === \"Placeholder\" &&\n      \"Pattern\" === (node as t.Placeholder).expectedNode)\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isClass(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Class {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"ClassExpression\" === nodeType || \"ClassDeclaration\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isImportOrExportDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ImportOrExportDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ExportAllDeclaration\" === nodeType ||\n    \"ExportDefaultDeclaration\" === nodeType ||\n    \"ExportNamedDeclaration\" === nodeType ||\n    \"ImportDeclaration\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isExportDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ExportDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ExportAllDeclaration\" === nodeType ||\n    \"ExportDefaultDeclaration\" === nodeType ||\n    \"ExportNamedDeclaration\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isModuleSpecifier(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ModuleSpecifier {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ExportSpecifier\" === nodeType ||\n    \"ImportDefaultSpecifier\" === nodeType ||\n    \"ImportNamespaceSpecifier\" === nodeType ||\n    \"ImportSpecifier\" === nodeType ||\n    \"ExportNamespaceSpecifier\" === nodeType ||\n    \"ExportDefaultSpecifier\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isAccessor(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Accessor {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"ClassAccessorProperty\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isPrivate(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Private {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"ClassPrivateProperty\" === nodeType ||\n    \"ClassPrivateMethod\" === nodeType ||\n    \"PrivateName\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFlow(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Flow {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"AnyTypeAnnotation\" === nodeType ||\n    \"ArrayTypeAnnotation\" === nodeType ||\n    \"BooleanTypeAnnotation\" === nodeType ||\n    \"BooleanLiteralTypeAnnotation\" === nodeType ||\n    \"NullLiteralTypeAnnotation\" === nodeType ||\n    \"ClassImplements\" === nodeType ||\n    \"DeclareClass\" === nodeType ||\n    \"DeclareFunction\" === nodeType ||\n    \"DeclareInterface\" === nodeType ||\n    \"DeclareModule\" === nodeType ||\n    \"DeclareModuleExports\" === nodeType ||\n    \"DeclareTypeAlias\" === nodeType ||\n    \"DeclareOpaqueType\" === nodeType ||\n    \"DeclareVariable\" === nodeType ||\n    \"DeclareExportDeclaration\" === nodeType ||\n    \"DeclareExportAllDeclaration\" === nodeType ||\n    \"DeclaredPredicate\" === nodeType ||\n    \"ExistsTypeAnnotation\" === nodeType ||\n    \"FunctionTypeAnnotation\" === nodeType ||\n    \"FunctionTypeParam\" === nodeType ||\n    \"GenericTypeAnnotation\" === nodeType ||\n    \"InferredPredicate\" === nodeType ||\n    \"InterfaceExtends\" === nodeType ||\n    \"InterfaceDeclaration\" === nodeType ||\n    \"InterfaceTypeAnnotation\" === nodeType ||\n    \"IntersectionTypeAnnotation\" === nodeType ||\n    \"MixedTypeAnnotation\" === nodeType ||\n    \"EmptyTypeAnnotation\" === nodeType ||\n    \"NullableTypeAnnotation\" === nodeType ||\n    \"NumberLiteralTypeAnnotation\" === nodeType ||\n    \"NumberTypeAnnotation\" === nodeType ||\n    \"ObjectTypeAnnotation\" === nodeType ||\n    \"ObjectTypeInternalSlot\" === nodeType ||\n    \"ObjectTypeCallProperty\" === nodeType ||\n    \"ObjectTypeIndexer\" === nodeType ||\n    \"ObjectTypeProperty\" === nodeType ||\n    \"ObjectTypeSpreadProperty\" === nodeType ||\n    \"OpaqueType\" === nodeType ||\n    \"QualifiedTypeIdentifier\" === nodeType ||\n    \"StringLiteralTypeAnnotation\" === nodeType ||\n    \"StringTypeAnnotation\" === nodeType ||\n    \"SymbolTypeAnnotation\" === nodeType ||\n    \"ThisTypeAnnotation\" === nodeType ||\n    \"TupleTypeAnnotation\" === nodeType ||\n    \"TypeofTypeAnnotation\" === nodeType ||\n    \"TypeAlias\" === nodeType ||\n    \"TypeAnnotation\" === nodeType ||\n    \"TypeCastExpression\" === nodeType ||\n    \"TypeParameter\" === nodeType ||\n    \"TypeParameterDeclaration\" === nodeType ||\n    \"TypeParameterInstantiation\" === nodeType ||\n    \"UnionTypeAnnotation\" === nodeType ||\n    \"Variance\" === nodeType ||\n    \"VoidTypeAnnotation\" === nodeType ||\n    \"EnumDeclaration\" === nodeType ||\n    \"EnumBooleanBody\" === nodeType ||\n    \"EnumNumberBody\" === nodeType ||\n    \"EnumStringBody\" === nodeType ||\n    \"EnumSymbolBody\" === nodeType ||\n    \"EnumBooleanMember\" === nodeType ||\n    \"EnumNumberMember\" === nodeType ||\n    \"EnumStringMember\" === nodeType ||\n    \"EnumDefaultedMember\" === nodeType ||\n    \"IndexedAccessType\" === nodeType ||\n    \"OptionalIndexedAccessType\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFlowType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FlowType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"AnyTypeAnnotation\" === nodeType ||\n    \"ArrayTypeAnnotation\" === nodeType ||\n    \"BooleanTypeAnnotation\" === nodeType ||\n    \"BooleanLiteralTypeAnnotation\" === nodeType ||\n    \"NullLiteralTypeAnnotation\" === nodeType ||\n    \"ExistsTypeAnnotation\" === nodeType ||\n    \"FunctionTypeAnnotation\" === nodeType ||\n    \"GenericTypeAnnotation\" === nodeType ||\n    \"InterfaceTypeAnnotation\" === nodeType ||\n    \"IntersectionTypeAnnotation\" === nodeType ||\n    \"MixedTypeAnnotation\" === nodeType ||\n    \"EmptyTypeAnnotation\" === nodeType ||\n    \"NullableTypeAnnotation\" === nodeType ||\n    \"NumberLiteralTypeAnnotation\" === nodeType ||\n    \"NumberTypeAnnotation\" === nodeType ||\n    \"ObjectTypeAnnotation\" === nodeType ||\n    \"StringLiteralTypeAnnotation\" === nodeType ||\n    \"StringTypeAnnotation\" === nodeType ||\n    \"SymbolTypeAnnotation\" === nodeType ||\n    \"ThisTypeAnnotation\" === nodeType ||\n    \"TupleTypeAnnotation\" === nodeType ||\n    \"TypeofTypeAnnotation\" === nodeType ||\n    \"UnionTypeAnnotation\" === nodeType ||\n    \"VoidTypeAnnotation\" === nodeType ||\n    \"IndexedAccessType\" === nodeType ||\n    \"OptionalIndexedAccessType\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFlowBaseAnnotation(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FlowBaseAnnotation {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"AnyTypeAnnotation\" === nodeType ||\n    \"BooleanTypeAnnotation\" === nodeType ||\n    \"NullLiteralTypeAnnotation\" === nodeType ||\n    \"MixedTypeAnnotation\" === nodeType ||\n    \"EmptyTypeAnnotation\" === nodeType ||\n    \"NumberTypeAnnotation\" === nodeType ||\n    \"StringTypeAnnotation\" === nodeType ||\n    \"SymbolTypeAnnotation\" === nodeType ||\n    \"ThisTypeAnnotation\" === nodeType ||\n    \"VoidTypeAnnotation\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFlowDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FlowDeclaration {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"DeclareClass\" === nodeType ||\n    \"DeclareFunction\" === nodeType ||\n    \"DeclareInterface\" === nodeType ||\n    \"DeclareModule\" === nodeType ||\n    \"DeclareModuleExports\" === nodeType ||\n    \"DeclareTypeAlias\" === nodeType ||\n    \"DeclareOpaqueType\" === nodeType ||\n    \"DeclareVariable\" === nodeType ||\n    \"DeclareExportDeclaration\" === nodeType ||\n    \"DeclareExportAllDeclaration\" === nodeType ||\n    \"InterfaceDeclaration\" === nodeType ||\n    \"OpaqueType\" === nodeType ||\n    \"TypeAlias\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isFlowPredicate(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.FlowPredicate {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\"DeclaredPredicate\" === nodeType || \"InferredPredicate\" === nodeType) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumBody(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumBody {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"EnumBooleanBody\" === nodeType ||\n    \"EnumNumberBody\" === nodeType ||\n    \"EnumStringBody\" === nodeType ||\n    \"EnumSymbolBody\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isEnumMember(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.EnumMember {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"EnumBooleanMember\" === nodeType ||\n    \"EnumNumberMember\" === nodeType ||\n    \"EnumStringMember\" === nodeType ||\n    \"EnumDefaultedMember\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isJSX(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.JSX {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"JSXAttribute\" === nodeType ||\n    \"JSXClosingElement\" === nodeType ||\n    \"JSXElement\" === nodeType ||\n    \"JSXEmptyExpression\" === nodeType ||\n    \"JSXExpressionContainer\" === nodeType ||\n    \"JSXSpreadChild\" === nodeType ||\n    \"JSXIdentifier\" === nodeType ||\n    \"JSXMemberExpression\" === nodeType ||\n    \"JSXNamespacedName\" === nodeType ||\n    \"JSXOpeningElement\" === nodeType ||\n    \"JSXSpreadAttribute\" === nodeType ||\n    \"JSXText\" === nodeType ||\n    \"JSXFragment\" === nodeType ||\n    \"JSXOpeningFragment\" === nodeType ||\n    \"JSXClosingFragment\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isMiscellaneous(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.Miscellaneous {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"Noop\" === nodeType ||\n    \"Placeholder\" === nodeType ||\n    \"V8IntrinsicIdentifier\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTypeScript(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TypeScript {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"TSParameterProperty\" === nodeType ||\n    \"TSDeclareFunction\" === nodeType ||\n    \"TSDeclareMethod\" === nodeType ||\n    \"TSQualifiedName\" === nodeType ||\n    \"TSCallSignatureDeclaration\" === nodeType ||\n    \"TSConstructSignatureDeclaration\" === nodeType ||\n    \"TSPropertySignature\" === nodeType ||\n    \"TSMethodSignature\" === nodeType ||\n    \"TSIndexSignature\" === nodeType ||\n    \"TSAnyKeyword\" === nodeType ||\n    \"TSBooleanKeyword\" === nodeType ||\n    \"TSBigIntKeyword\" === nodeType ||\n    \"TSIntrinsicKeyword\" === nodeType ||\n    \"TSNeverKeyword\" === nodeType ||\n    \"TSNullKeyword\" === nodeType ||\n    \"TSNumberKeyword\" === nodeType ||\n    \"TSObjectKeyword\" === nodeType ||\n    \"TSStringKeyword\" === nodeType ||\n    \"TSSymbolKeyword\" === nodeType ||\n    \"TSUndefinedKeyword\" === nodeType ||\n    \"TSUnknownKeyword\" === nodeType ||\n    \"TSVoidKeyword\" === nodeType ||\n    \"TSThisType\" === nodeType ||\n    \"TSFunctionType\" === nodeType ||\n    \"TSConstructorType\" === nodeType ||\n    \"TSTypeReference\" === nodeType ||\n    \"TSTypePredicate\" === nodeType ||\n    \"TSTypeQuery\" === nodeType ||\n    \"TSTypeLiteral\" === nodeType ||\n    \"TSArrayType\" === nodeType ||\n    \"TSTupleType\" === nodeType ||\n    \"TSOptionalType\" === nodeType ||\n    \"TSRestType\" === nodeType ||\n    \"TSNamedTupleMember\" === nodeType ||\n    \"TSUnionType\" === nodeType ||\n    \"TSIntersectionType\" === nodeType ||\n    \"TSConditionalType\" === nodeType ||\n    \"TSInferType\" === nodeType ||\n    \"TSParenthesizedType\" === nodeType ||\n    \"TSTypeOperator\" === nodeType ||\n    \"TSIndexedAccessType\" === nodeType ||\n    \"TSMappedType\" === nodeType ||\n    \"TSLiteralType\" === nodeType ||\n    \"TSExpressionWithTypeArguments\" === nodeType ||\n    \"TSInterfaceDeclaration\" === nodeType ||\n    \"TSInterfaceBody\" === nodeType ||\n    \"TSTypeAliasDeclaration\" === nodeType ||\n    \"TSInstantiationExpression\" === nodeType ||\n    \"TSAsExpression\" === nodeType ||\n    \"TSSatisfiesExpression\" === nodeType ||\n    \"TSTypeAssertion\" === nodeType ||\n    \"TSEnumDeclaration\" === nodeType ||\n    \"TSEnumMember\" === nodeType ||\n    \"TSModuleDeclaration\" === nodeType ||\n    \"TSModuleBlock\" === nodeType ||\n    \"TSImportType\" === nodeType ||\n    \"TSImportEqualsDeclaration\" === nodeType ||\n    \"TSExternalModuleReference\" === nodeType ||\n    \"TSNonNullExpression\" === nodeType ||\n    \"TSExportAssignment\" === nodeType ||\n    \"TSNamespaceExportDeclaration\" === nodeType ||\n    \"TSTypeAnnotation\" === nodeType ||\n    \"TSTypeParameterInstantiation\" === nodeType ||\n    \"TSTypeParameterDeclaration\" === nodeType ||\n    \"TSTypeParameter\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSTypeElement(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSTypeElement {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"TSCallSignatureDeclaration\" === nodeType ||\n    \"TSConstructSignatureDeclaration\" === nodeType ||\n    \"TSPropertySignature\" === nodeType ||\n    \"TSMethodSignature\" === nodeType ||\n    \"TSIndexSignature\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"TSAnyKeyword\" === nodeType ||\n    \"TSBooleanKeyword\" === nodeType ||\n    \"TSBigIntKeyword\" === nodeType ||\n    \"TSIntrinsicKeyword\" === nodeType ||\n    \"TSNeverKeyword\" === nodeType ||\n    \"TSNullKeyword\" === nodeType ||\n    \"TSNumberKeyword\" === nodeType ||\n    \"TSObjectKeyword\" === nodeType ||\n    \"TSStringKeyword\" === nodeType ||\n    \"TSSymbolKeyword\" === nodeType ||\n    \"TSUndefinedKeyword\" === nodeType ||\n    \"TSUnknownKeyword\" === nodeType ||\n    \"TSVoidKeyword\" === nodeType ||\n    \"TSThisType\" === nodeType ||\n    \"TSFunctionType\" === nodeType ||\n    \"TSConstructorType\" === nodeType ||\n    \"TSTypeReference\" === nodeType ||\n    \"TSTypePredicate\" === nodeType ||\n    \"TSTypeQuery\" === nodeType ||\n    \"TSTypeLiteral\" === nodeType ||\n    \"TSArrayType\" === nodeType ||\n    \"TSTupleType\" === nodeType ||\n    \"TSOptionalType\" === nodeType ||\n    \"TSRestType\" === nodeType ||\n    \"TSUnionType\" === nodeType ||\n    \"TSIntersectionType\" === nodeType ||\n    \"TSConditionalType\" === nodeType ||\n    \"TSInferType\" === nodeType ||\n    \"TSParenthesizedType\" === nodeType ||\n    \"TSTypeOperator\" === nodeType ||\n    \"TSIndexedAccessType\" === nodeType ||\n    \"TSMappedType\" === nodeType ||\n    \"TSLiteralType\" === nodeType ||\n    \"TSExpressionWithTypeArguments\" === nodeType ||\n    \"TSImportType\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isTSBaseType(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.TSBaseType {\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (\n    \"TSAnyKeyword\" === nodeType ||\n    \"TSBooleanKeyword\" === nodeType ||\n    \"TSBigIntKeyword\" === nodeType ||\n    \"TSIntrinsicKeyword\" === nodeType ||\n    \"TSNeverKeyword\" === nodeType ||\n    \"TSNullKeyword\" === nodeType ||\n    \"TSNumberKeyword\" === nodeType ||\n    \"TSObjectKeyword\" === nodeType ||\n    \"TSStringKeyword\" === nodeType ||\n    \"TSSymbolKeyword\" === nodeType ||\n    \"TSUndefinedKeyword\" === nodeType ||\n    \"TSUnknownKeyword\" === nodeType ||\n    \"TSVoidKeyword\" === nodeType ||\n    \"TSThisType\" === nodeType ||\n    \"TSLiteralType\" === nodeType\n  ) {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isNumberLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): boolean {\n  deprecationWarning(\"isNumberLiteral\", \"isNumericLiteral\");\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"NumberLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isRegexLiteral(\n  node: object | null | undefined,\n  opts?: object | null,\n): boolean {\n  deprecationWarning(\"isRegexLiteral\", \"isRegExpLiteral\");\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"RegexLiteral\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isRestProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): boolean {\n  deprecationWarning(\"isRestProperty\", \"isRestElement\");\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"RestProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isSpreadProperty(\n  node: object | null | undefined,\n  opts?: object | null,\n): boolean {\n  deprecationWarning(\"isSpreadProperty\", \"isSpreadElement\");\n  if (!node) return false;\n\n  const nodeType = (node as t.Node).type;\n  if (nodeType === \"SpreadProperty\") {\n    if (typeof opts === \"undefined\") {\n      return true;\n    } else {\n      return shallowEqual(node, opts);\n    }\n  }\n\n  return false;\n}\nexport function isModuleDeclaration(\n  node: object | null | undefined,\n  opts?: object | null,\n): node is t.ImportOrExportDeclaration {\n  deprecationWarning(\"isModuleDeclaration\", \"isImportOrExportDeclaration\");\n  return isImportOrExportDeclaration(node, opts);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAAA,aAAA,GAAAC,OAAA;AAEA,IAAAC,mBAAA,GAAAD,OAAA;AAEO,SAASE,iBAAiBA,CAC/BC,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASI,sBAAsBA,CACpCL,IAA+B,EAC/BC,IAAoB,EACY;EAChC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,sBAAsB,EAAE;IACvC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASK,kBAAkBA,CAChCN,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASM,sBAAsBA,CACpCP,IAA+B,EAC/BC,IAAoB,EACY;EAChC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,sBAAsB,EAAE;IACvC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASO,WAAWA,CACzBR,IAA+B,EAC/BC,IAAoB,EACC;EACrB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,WAAW,EAAE;IAC5B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASQ,kBAAkBA,CAChCT,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASS,gBAAgBA,CAC9BV,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASU,gBAAgBA,CAC9BX,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASW,gBAAgBA,CAC9BZ,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASY,aAAaA,CAC3Bb,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,aAAa,EAAE;IAC9B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASa,uBAAuBA,CACrCd,IAA+B,EAC/BC,IAAoB,EACa;EACjC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,uBAAuB,EAAE;IACxC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASc,mBAAmBA,CACjCf,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASe,mBAAmBA,CACjChB,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgB,kBAAkBA,CAChCjB,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiB,gBAAgBA,CAC9BlB,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkB,qBAAqBA,CACnCnB,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmB,MAAMA,CACpBpB,IAA+B,EAC/BC,IAAoB,EACJ;EAChB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,MAAM,EAAE;IACvB,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoB,gBAAgBA,CAC9BrB,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqB,cAAcA,CAC5BtB,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,cAAc,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsB,qBAAqBA,CACnCvB,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuB,oBAAoBA,CAClCxB,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwB,YAAYA,CAC1BzB,IAA+B,EAC/BC,IAAoB,EACE;EACtB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,YAAY,EAAE;IAC7B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyB,aAAaA,CAC3B1B,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,aAAa,EAAE;IAC9B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0B,kBAAkBA,CAChC3B,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2B,eAAeA,CAC7B5B,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4B,gBAAgBA,CAC9B7B,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6B,aAAaA,CAC3B9B,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,aAAa,EAAE;IAC9B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8B,gBAAgBA,CAC9B/B,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+B,eAAeA,CAC7BhC,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgC,mBAAmBA,CACjCjC,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiC,kBAAkBA,CAChClC,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkC,eAAeA,CAC7BnC,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmC,SAASA,CACvBpC,IAA+B,EAC/BC,IAAoB,EACD;EACnB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,SAAS,EAAE;IAC1B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoC,kBAAkBA,CAChCrC,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqC,cAAcA,CAC5BtC,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,cAAc,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsC,gBAAgBA,CAC9BvC,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuC,aAAaA,CAC3BxC,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,aAAa,EAAE;IAC9B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwC,iBAAiBA,CAC/BzC,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyC,oBAAoBA,CAClC1C,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0C,yBAAyBA,CACvC3C,IAA+B,EAC/BC,IAAoB,EACe;EACnC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,yBAAyB,EAAE;IAC1C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2C,YAAYA,CAC1B5C,IAA+B,EAC/BC,IAAoB,EACE;EACtB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,YAAY,EAAE;IAC7B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4C,iBAAiBA,CAC/B7C,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6C,gBAAgBA,CAC9B9C,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8C,gBAAgBA,CAC9B/C,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+C,cAAcA,CAC5BhD,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,cAAc,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgD,iBAAiBA,CAC/BjD,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiD,kBAAkBA,CAChClD,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkD,qBAAqBA,CACnCnD,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmD,oBAAoBA,CAClCpD,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoD,gBAAgBA,CAC9BrD,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqD,eAAeA,CAC7BtD,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsD,mBAAmBA,CACjCvD,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuD,cAAcA,CAC5BxD,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,cAAc,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwD,yBAAyBA,CACvCzD,IAA+B,EAC/BC,IAAoB,EACe;EACnC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,yBAAyB,EAAE;IAC1C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyD,WAAWA,CACzB1D,IAA+B,EAC/BC,IAAoB,EACC;EACrB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,WAAW,EAAE;IAC5B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0D,iBAAiBA,CAC/B3D,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2D,kBAAkBA,CAChC5D,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4D,sBAAsBA,CACpC7D,IAA+B,EAC/BC,IAAoB,EACY;EAChC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,sBAAsB,EAAE;IACvC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6D,0BAA0BA,CACxC9D,IAA+B,EAC/BC,IAAoB,EACgB;EACpC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,0BAA0B,EAAE;IAC3C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8D,wBAAwBA,CACtC/D,IAA+B,EAC/BC,IAAoB,EACc;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,wBAAwB,EAAE;IACzC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+D,iBAAiBA,CAC/BhE,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgE,gBAAgBA,CAC9BjE,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiE,mBAAmBA,CACjClE,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkE,wBAAwBA,CACtCnE,IAA+B,EAC/BC,IAAoB,EACc;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,wBAAwB,EAAE;IACzC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmE,0BAA0BA,CACxCpE,IAA+B,EAC/BC,IAAoB,EACgB;EACpC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,0BAA0B,EAAE;IAC3C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoE,iBAAiBA,CAC/BrE,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqE,cAAcA,CAC5BtE,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,cAAc,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsE,aAAaA,CAC3BvE,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,aAAa,EAAE;IAC9B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuE,eAAeA,CAC7BxE,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwE,eAAeA,CAC7BzE,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyE,OAAOA,CACrB1E,IAA+B,EAC/BC,IAAoB,EACH;EACjB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,OAAO,EAAE;IACxB,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0E,0BAA0BA,CACxC3E,IAA+B,EAC/BC,IAAoB,EACgB;EACpC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,0BAA0B,EAAE;IAC3C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2E,iBAAiBA,CAC/B5E,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4E,iBAAiBA,CAC/B7E,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6E,iBAAiBA,CAC/B9E,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8E,iBAAiBA,CAC/B/E,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+E,QAAQA,CACtBhF,IAA+B,EAC/BC,IAAoB,EACF;EAClB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,QAAQ,EAAE;IACzB,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgF,eAAeA,CAC7BjF,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiF,0BAA0BA,CACxClF,IAA+B,EAC/BC,IAAoB,EACgB;EACpC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,0BAA0B,EAAE;IAC3C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkF,0BAA0BA,CACxCnF,IAA+B,EAC/BC,IAAoB,EACgB;EACpC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,0BAA0B,EAAE;IAC3C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmF,wBAAwBA,CACtCpF,IAA+B,EAC/BC,IAAoB,EACc;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,wBAAwB,EAAE;IACzC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoF,eAAeA,CAC7BrF,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqF,uBAAuBA,CACrCtF,IAA+B,EAC/BC,IAAoB,EACa;EACjC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,uBAAuB,EAAE;IACxC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsF,sBAAsBA,CACpCvF,IAA+B,EAC/BC,IAAoB,EACY;EAChC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,sBAAsB,EAAE;IACvC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuF,oBAAoBA,CAClCxF,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwF,aAAaA,CAC3BzF,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,aAAa,EAAE;IAC9B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyF,aAAaA,CAC3B1F,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,aAAa,EAAE;IAC9B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0F,mBAAmBA,CACjC3F,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2F,qBAAqBA,CACnC5F,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4F,uBAAuBA,CACrC7F,IAA+B,EAC/BC,IAAoB,EACa;EACjC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,uBAAuB,EAAE;IACxC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6F,8BAA8BA,CAC5C9F,IAA+B,EAC/BC,IAAoB,EACoB;EACxC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,8BAA8B,EAAE;IAC/C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8F,2BAA2BA,CACzC/F,IAA+B,EAC/BC,IAAoB,EACiB;EACrC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,2BAA2B,EAAE;IAC5C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+F,iBAAiBA,CAC/BhG,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgG,cAAcA,CAC5BjG,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,cAAc,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiG,iBAAiBA,CAC/BlG,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkG,kBAAkBA,CAChCnG,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmG,eAAeA,CAC7BpG,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoG,sBAAsBA,CACpCrG,IAA+B,EAC/BC,IAAoB,EACY;EAChC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,sBAAsB,EAAE;IACvC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqG,kBAAkBA,CAChCtG,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsG,mBAAmBA,CACjCvG,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuG,iBAAiBA,CAC/BxG,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwG,0BAA0BA,CACxCzG,IAA+B,EAC/BC,IAAoB,EACgB;EACpC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,0BAA0B,EAAE;IAC3C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyG,6BAA6BA,CAC3C1G,IAA+B,EAC/BC,IAAoB,EACmB;EACvC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,6BAA6B,EAAE;IAC9C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0G,mBAAmBA,CACjC3G,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2G,sBAAsBA,CACpC5G,IAA+B,EAC/BC,IAAoB,EACY;EAChC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,sBAAsB,EAAE;IACvC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4G,wBAAwBA,CACtC7G,IAA+B,EAC/BC,IAAoB,EACc;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,wBAAwB,EAAE;IACzC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6G,mBAAmBA,CACjC9G,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8G,uBAAuBA,CACrC/G,IAA+B,EAC/BC,IAAoB,EACa;EACjC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,uBAAuB,EAAE;IACxC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+G,mBAAmBA,CACjChH,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgH,kBAAkBA,CAChCjH,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiH,sBAAsBA,CACpClH,IAA+B,EAC/BC,IAAoB,EACY;EAChC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,sBAAsB,EAAE;IACvC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkH,yBAAyBA,CACvCnH,IAA+B,EAC/BC,IAAoB,EACe;EACnC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,yBAAyB,EAAE;IAC1C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmH,4BAA4BA,CAC1CpH,IAA+B,EAC/BC,IAAoB,EACkB;EACtC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,4BAA4B,EAAE;IAC7C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoH,qBAAqBA,CACnCrH,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqH,qBAAqBA,CACnCtH,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsH,wBAAwBA,CACtCvH,IAA+B,EAC/BC,IAAoB,EACc;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,wBAAwB,EAAE;IACzC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuH,6BAA6BA,CAC3CxH,IAA+B,EAC/BC,IAAoB,EACmB;EACvC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,6BAA6B,EAAE;IAC9C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwH,sBAAsBA,CACpCzH,IAA+B,EAC/BC,IAAoB,EACY;EAChC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,sBAAsB,EAAE;IACvC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyH,sBAAsBA,CACpC1H,IAA+B,EAC/BC,IAAoB,EACY;EAChC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,sBAAsB,EAAE;IACvC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0H,wBAAwBA,CACtC3H,IAA+B,EAC/BC,IAAoB,EACc;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,wBAAwB,EAAE;IACzC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2H,wBAAwBA,CACtC5H,IAA+B,EAC/BC,IAAoB,EACc;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,wBAAwB,EAAE;IACzC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4H,mBAAmBA,CACjC7H,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6H,oBAAoBA,CAClC9H,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8H,0BAA0BA,CACxC/H,IAA+B,EAC/BC,IAAoB,EACgB;EACpC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,0BAA0B,EAAE;IAC3C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+H,YAAYA,CAC1BhI,IAA+B,EAC/BC,IAAoB,EACE;EACtB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,YAAY,EAAE;IAC7B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgI,yBAAyBA,CACvCjI,IAA+B,EAC/BC,IAAoB,EACe;EACnC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,yBAAyB,EAAE;IAC1C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiI,6BAA6BA,CAC3ClI,IAA+B,EAC/BC,IAAoB,EACmB;EACvC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,6BAA6B,EAAE;IAC9C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkI,sBAAsBA,CACpCnI,IAA+B,EAC/BC,IAAoB,EACY;EAChC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,sBAAsB,EAAE;IACvC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmI,sBAAsBA,CACpCpI,IAA+B,EAC/BC,IAAoB,EACY;EAChC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,sBAAsB,EAAE;IACvC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoI,oBAAoBA,CAClCrI,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqI,qBAAqBA,CACnCtI,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsI,sBAAsBA,CACpCvI,IAA+B,EAC/BC,IAAoB,EACY;EAChC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,sBAAsB,EAAE;IACvC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuI,WAAWA,CACzBxI,IAA+B,EAC/BC,IAAoB,EACC;EACrB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,WAAW,EAAE;IAC5B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwI,gBAAgBA,CAC9BzI,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyI,oBAAoBA,CAClC1I,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0I,eAAeA,CAC7B3I,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2I,0BAA0BA,CACxC5I,IAA+B,EAC/BC,IAAoB,EACgB;EACpC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,0BAA0B,EAAE;IAC3C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4I,4BAA4BA,CAC1C7I,IAA+B,EAC/BC,IAAoB,EACkB;EACtC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,4BAA4B,EAAE;IAC7C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6I,qBAAqBA,CACnC9I,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8I,UAAUA,CACxB/I,IAA+B,EAC/BC,IAAoB,EACA;EACpB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,UAAU,EAAE;IAC3B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+I,oBAAoBA,CAClChJ,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgJ,iBAAiBA,CAC/BjJ,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiJ,iBAAiBA,CAC/BlJ,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkJ,gBAAgBA,CAC9BnJ,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmJ,gBAAgBA,CAC9BpJ,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoJ,gBAAgBA,CAC9BrJ,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqJ,mBAAmBA,CACjCtJ,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsJ,kBAAkBA,CAChCvJ,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuJ,kBAAkBA,CAChCxJ,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwJ,qBAAqBA,CACnCzJ,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyJ,mBAAmBA,CACjC1J,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0J,2BAA2BA,CACzC3J,IAA+B,EAC/BC,IAAoB,EACiB;EACrC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,2BAA2B,EAAE;IAC5C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2J,cAAcA,CAC5B5J,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,cAAc,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4J,mBAAmBA,CACjC7J,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6J,YAAYA,CAC1B9J,IAA+B,EAC/BC,IAAoB,EACE;EACtB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,YAAY,EAAE;IAC7B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8J,oBAAoBA,CAClC/J,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+J,wBAAwBA,CACtChK,IAA+B,EAC/BC,IAAoB,EACc;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,wBAAwB,EAAE;IACzC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgK,gBAAgBA,CAC9BjK,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiK,eAAeA,CAC7BlK,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkK,qBAAqBA,CACnCnK,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmK,mBAAmBA,CACjCpK,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoK,mBAAmBA,CACjCrK,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqK,oBAAoBA,CAClCtK,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsK,SAASA,CACvBvK,IAA+B,EAC/BC,IAAoB,EACD;EACnB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,SAAS,EAAE;IAC1B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuK,aAAaA,CAC3BxK,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,aAAa,EAAE;IAC9B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwK,oBAAoBA,CAClCzK,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyK,oBAAoBA,CAClC1K,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0K,MAAMA,CACpB3K,IAA+B,EAC/BC,IAAoB,EACJ;EAChB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,MAAM,EAAE;IACvB,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2K,aAAaA,CAC3B5K,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,aAAa,EAAE;IAC9B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4K,uBAAuBA,CACrC7K,IAA+B,EAC/BC,IAAoB,EACa;EACjC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,uBAAuB,EAAE;IACxC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6K,qBAAqBA,CACnC9K,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8K,gBAAgBA,CAC9B/K,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+K,iBAAiBA,CAC/BhL,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgL,WAAWA,CACzBjL,IAA+B,EAC/BC,IAAoB,EACC;EACrB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,WAAW,EAAE;IAC5B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiL,cAAcA,CAC5BlL,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,cAAc,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkL,wBAAwBA,CACtCnL,IAA+B,EAC/BC,IAAoB,EACc;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,wBAAwB,EAAE;IACzC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmL,kBAAkBA,CAChCpL,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoL,iBAAiBA,CAC/BrL,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqL,gBAAgBA,CAC9BtL,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsL,kBAAkBA,CAChCvL,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuL,gBAAgBA,CAC9BxL,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwL,yBAAyBA,CACvCzL,IAA+B,EAC/BC,IAAoB,EACe;EACnC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,yBAAyB,EAAE;IAC1C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyL,sBAAsBA,CACpC1L,IAA+B,EAC/BC,IAAoB,EACY;EAChC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,sBAAsB,EAAE;IACvC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0L,+BAA+BA,CAC7C3L,IAA+B,EAC/BC,IAAoB,EACqB;EACzC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,+BAA+B,EAAE;IAChD,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2L,qBAAqBA,CACnC5L,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4L,mBAAmBA,CACjC7L,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6L,iBAAiBA,CAC/B9L,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8L,iBAAiBA,CAC/B/L,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+L,4BAA4BA,CAC1ChM,IAA+B,EAC/BC,IAAoB,EACkB;EACtC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,4BAA4B,EAAE;IAC7C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgM,iCAAiCA,CAC/CjM,IAA+B,EAC/BC,IAAoB,EACuB;EAC3C,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iCAAiC,EAAE;IAClD,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiM,qBAAqBA,CACnClM,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkM,mBAAmBA,CACjCnM,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmM,kBAAkBA,CAChCpM,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoM,cAAcA,CAC5BrM,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,cAAc,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqM,kBAAkBA,CAChCtM,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsM,iBAAiBA,CAC/BvM,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuM,oBAAoBA,CAClCxM,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwM,gBAAgBA,CAC9BzM,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyM,eAAeA,CAC7B1M,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0M,iBAAiBA,CAC/B3M,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2M,iBAAiBA,CAC/B5M,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4M,iBAAiBA,CAC/B7M,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6M,iBAAiBA,CAC/B9M,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8M,oBAAoBA,CAClC/M,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+M,kBAAkBA,CAChChN,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgN,eAAeA,CAC7BjN,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiN,YAAYA,CAC1BlN,IAA+B,EAC/BC,IAAoB,EACE;EACtB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,YAAY,EAAE;IAC7B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkN,gBAAgBA,CAC9BnN,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmN,mBAAmBA,CACjCpN,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoN,iBAAiBA,CAC/BrN,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqN,iBAAiBA,CAC/BtN,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsN,aAAaA,CAC3BvN,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,aAAa,EAAE;IAC9B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuN,eAAeA,CAC7BxN,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwN,aAAaA,CAC3BzN,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,aAAa,EAAE;IAC9B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyN,aAAaA,CAC3B1N,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,aAAa,EAAE;IAC9B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0N,gBAAgBA,CAC9B3N,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2N,YAAYA,CAC1B5N,IAA+B,EAC/BC,IAAoB,EACE;EACtB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,YAAY,EAAE;IAC7B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4N,oBAAoBA,CAClC7N,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6N,aAAaA,CAC3B9N,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,aAAa,EAAE;IAC9B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8N,oBAAoBA,CAClC/N,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+N,mBAAmBA,CACjChO,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgO,aAAaA,CAC3BjO,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,aAAa,EAAE;IAC9B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiO,qBAAqBA,CACnClO,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkO,gBAAgBA,CAC9BnO,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmO,qBAAqBA,CACnCpO,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoO,cAAcA,CAC5BrO,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,cAAc,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqO,eAAeA,CAC7BtO,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsO,+BAA+BA,CAC7CvO,IAA+B,EAC/BC,IAAoB,EACqB;EACzC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,+BAA+B,EAAE;IAChD,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuO,wBAAwBA,CACtCxO,IAA+B,EAC/BC,IAAoB,EACc;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,wBAAwB,EAAE;IACzC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwO,iBAAiBA,CAC/BzO,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyO,wBAAwBA,CACtC1O,IAA+B,EAC/BC,IAAoB,EACc;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,wBAAwB,EAAE;IACzC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0O,2BAA2BA,CACzC3O,IAA+B,EAC/BC,IAAoB,EACiB;EACrC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,2BAA2B,EAAE;IAC5C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2O,gBAAgBA,CAC9B5O,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4O,uBAAuBA,CACrC7O,IAA+B,EAC/BC,IAAoB,EACa;EACjC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,uBAAuB,EAAE;IACxC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6O,iBAAiBA,CAC/B9O,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8O,mBAAmBA,CACjC/O,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,mBAAmB,EAAE;IACpC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+O,cAAcA,CAC5BhP,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,cAAc,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgP,qBAAqBA,CACnCjP,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiP,eAAeA,CAC7BlP,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkP,cAAcA,CAC5BnP,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,cAAc,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmP,2BAA2BA,CACzCpP,IAA+B,EAC/BC,IAAoB,EACiB;EACrC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,2BAA2B,EAAE;IAC5C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoP,2BAA2BA,CACzCrP,IAA+B,EAC/BC,IAAoB,EACiB;EACrC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,2BAA2B,EAAE;IAC5C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqP,qBAAqBA,CACnCtP,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,qBAAqB,EAAE;IACtC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsP,oBAAoBA,CAClCvP,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,oBAAoB,EAAE;IACrC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuP,8BAA8BA,CAC5CxP,IAA+B,EAC/BC,IAAoB,EACoB;EACxC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,8BAA8B,EAAE;IAC/C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwP,kBAAkBA,CAChCzP,IAA+B,EAC/BC,IAAoB,EACQ;EAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,kBAAkB,EAAE;IACnC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyP,8BAA8BA,CAC5C1P,IAA+B,EAC/BC,IAAoB,EACoB;EACxC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,8BAA8B,EAAE;IAC/C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0P,4BAA4BA,CAC1C3P,IAA+B,EAC/BC,IAAoB,EACkB;EACtC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,4BAA4B,EAAE;IAC7C,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2P,iBAAiBA,CAC/B5P,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,iBAAiB,EAAE;IAClC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4P,cAAcA,CAC5B7P,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,iBAAiB,KAAKD,QAAQ,IAC9B,sBAAsB,KAAKA,QAAQ,IACnC,kBAAkB,KAAKA,QAAQ,IAC/B,sBAAsB,KAAKA,QAAQ,IACnC,WAAW,KAAKA,QAAQ,IACxB,kBAAkB,KAAKA,QAAQ,IAC/B,gBAAgB,KAAKA,QAAQ,IAC7B,gBAAgB,KAAKA,QAAQ,IAC7B,gBAAgB,KAAKA,QAAQ,IAC7B,aAAa,KAAKA,QAAQ,IAC1B,uBAAuB,KAAKA,QAAQ,IACpC,mBAAmB,KAAKA,QAAQ,IAChC,mBAAmB,KAAKA,QAAQ,IAChC,kBAAkB,KAAKA,QAAQ,IAC/B,gBAAgB,KAAKA,QAAQ,IAC7B,qBAAqB,KAAKA,QAAQ,IAClC,MAAM,KAAKA,QAAQ,IACnB,gBAAgB,KAAKA,QAAQ,IAC7B,cAAc,KAAKA,QAAQ,IAC3B,qBAAqB,KAAKA,QAAQ,IAClC,oBAAoB,KAAKA,QAAQ,IACjC,YAAY,KAAKA,QAAQ,IACzB,aAAa,KAAKA,QAAQ,IAC1B,kBAAkB,KAAKA,QAAQ,IAC/B,eAAe,KAAKA,QAAQ,IAC5B,gBAAgB,KAAKA,QAAQ,IAC7B,aAAa,KAAKA,QAAQ,IAC1B,gBAAgB,KAAKA,QAAQ,IAC7B,eAAe,KAAKA,QAAQ,IAC5B,mBAAmB,KAAKA,QAAQ,IAChC,kBAAkB,KAAKA,QAAQ,IAC/B,eAAe,KAAKA,QAAQ,IAC5B,SAAS,KAAKA,QAAQ,IACtB,kBAAkB,KAAKA,QAAQ,IAC/B,cAAc,KAAKA,QAAQ,IAC3B,gBAAgB,KAAKA,QAAQ,IAC7B,aAAa,KAAKA,QAAQ,IAC1B,iBAAiB,KAAKA,QAAQ,IAC9B,oBAAoB,KAAKA,QAAQ,IACjC,yBAAyB,KAAKA,QAAQ,IACtC,YAAY,KAAKA,QAAQ,IACzB,iBAAiB,KAAKA,QAAQ,IAC9B,gBAAgB,KAAKA,QAAQ,IAC7B,gBAAgB,KAAKA,QAAQ,IAC7B,cAAc,KAAKA,QAAQ,IAC3B,iBAAiB,KAAKA,QAAQ,IAC9B,kBAAkB,KAAKA,QAAQ,IAC/B,qBAAqB,KAAKA,QAAQ,IAClC,oBAAoB,KAAKA,QAAQ,IACjC,gBAAgB,KAAKA,QAAQ,IAC7B,eAAe,KAAKA,QAAQ,IAC5B,mBAAmB,KAAKA,QAAQ,IAChC,cAAc,KAAKA,QAAQ,IAC3B,yBAAyB,KAAKA,QAAQ,IACtC,WAAW,KAAKA,QAAQ,IACxB,iBAAiB,KAAKA,QAAQ,IAC9B,kBAAkB,KAAKA,QAAQ,IAC/B,sBAAsB,KAAKA,QAAQ,IACnC,0BAA0B,KAAKA,QAAQ,IACvC,wBAAwB,KAAKA,QAAQ,IACrC,iBAAiB,KAAKA,QAAQ,IAC9B,gBAAgB,KAAKA,QAAQ,IAC7B,mBAAmB,KAAKA,QAAQ,IAChC,wBAAwB,KAAKA,QAAQ,IACrC,0BAA0B,KAAKA,QAAQ,IACvC,iBAAiB,KAAKA,QAAQ,IAC9B,cAAc,KAAKA,QAAQ,IAC3B,aAAa,KAAKA,QAAQ,IAC1B,eAAe,KAAKA,QAAQ,IAC5B,eAAe,KAAKA,QAAQ,IAC5B,OAAO,KAAKA,QAAQ,IACpB,0BAA0B,KAAKA,QAAQ,IACvC,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,QAAQ,KAAKA,QAAQ,IACrB,eAAe,KAAKA,QAAQ,IAC5B,0BAA0B,KAAKA,QAAQ,IACvC,0BAA0B,KAAKA,QAAQ,IACvC,wBAAwB,KAAKA,QAAQ,IACrC,eAAe,KAAKA,QAAQ,IAC5B,uBAAuB,KAAKA,QAAQ,IACpC,sBAAsB,KAAKA,QAAQ,IACnC,oBAAoB,KAAKA,QAAQ,IACjC,aAAa,KAAKA,QAAQ,IAC1B,aAAa,KAAKA,QAAQ,IACzBA,QAAQ,KAAK,aAAa,KACxB,YAAY,KAAMF,IAAI,CAAmB8P,YAAY,IACpD,eAAe,KAAM9P,IAAI,CAAmB8P,YAAY,IACxD,gBAAgB,KAAM9P,IAAI,CAAmB8P,YAAY,IACzD,WAAW,KAAM9P,IAAI,CAAmB8P,YAAY,CAAE,EAC1D;IACA,IAAI,OAAO7P,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8P,YAAYA,CAC1B/P,IAA+B,EAC/BC,IAAoB,EACE;EACtB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,iBAAiB,KAAKD,QAAQ,IAC9B,sBAAsB,KAAKA,QAAQ,IACnC,kBAAkB,KAAKA,QAAQ,IAC/B,gBAAgB,KAAKA,QAAQ,IAC7B,uBAAuB,KAAKA,QAAQ,IACpC,oBAAoB,KAAKA,QAAQ,IACjC,YAAY,KAAKA,QAAQ,IACzB,eAAe,KAAKA,QAAQ,IAC5B,gBAAgB,KAAKA,QAAQ,IAC7B,aAAa,KAAKA,QAAQ,IAC1B,gBAAgB,KAAKA,QAAQ,IAC7B,eAAe,KAAKA,QAAQ,IAC5B,mBAAmB,KAAKA,QAAQ,IAChC,kBAAkB,KAAKA,QAAQ,IAC/B,eAAe,KAAKA,QAAQ,IAC5B,kBAAkB,KAAKA,QAAQ,IAC/B,oBAAoB,KAAKA,QAAQ,IACjC,yBAAyB,KAAKA,QAAQ,IACtC,gBAAgB,KAAKA,QAAQ,IAC7B,iBAAiB,KAAKA,QAAQ,IAC9B,kBAAkB,KAAKA,QAAQ,IAC/B,yBAAyB,KAAKA,QAAQ,IACtC,iBAAiB,KAAKA,QAAQ,IAC9B,cAAc,KAAKA,QAAQ,IAC3B,OAAO,KAAKA,QAAQ,IACpB,0BAA0B,KAAKA,QAAQ,IACvC,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,QAAQ,KAAKA,QAAQ,IACrB,eAAe,KAAKA,QAAQ,IAC5B,0BAA0B,KAAKA,QAAQ,IACvC,wBAAwB,KAAKA,QAAQ,IACrC,oBAAoB,KAAKA,QAAQ,IACjC,YAAY,KAAKA,QAAQ,IACzB,aAAa,KAAKA,QAAQ,IAC1B,gBAAgB,KAAKA,QAAQ,IAC7B,cAAc,KAAKA,QAAQ,IAC3B,kBAAkB,KAAKA,QAAQ,IAC/B,iBAAiB,KAAKA,QAAQ,IAC9B,gBAAgB,KAAKA,QAAQ,IAC7B,kBAAkB,KAAKA,QAAQ,IAC/B,gBAAgB,KAAKA,QAAQ,IAC7B,yBAAyB,KAAKA,QAAQ,IACtC,sBAAsB,KAAKA,QAAQ,IACnC,+BAA+B,KAAKA,QAAQ,IAC5C,2BAA2B,KAAKA,QAAQ,IACxC,gBAAgB,KAAKA,QAAQ,IAC7B,uBAAuB,KAAKA,QAAQ,IACpC,iBAAiB,KAAKA,QAAQ,IAC9B,qBAAqB,KAAKA,QAAQ,IACjCA,QAAQ,KAAK,aAAa,KACxB,YAAY,KAAMF,IAAI,CAAmB8P,YAAY,IACpD,YAAY,KAAM9P,IAAI,CAAmB8P,YAAY,IACrD,eAAe,KAAM9P,IAAI,CAAmB8P,YAAY,CAAE,EAC9D;IACA,IAAI,OAAO7P,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+P,QAAQA,CACtBhQ,IAA+B,EAC/BC,IAAoB,EACF;EAClB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAI,kBAAkB,KAAKD,QAAQ,IAAI,mBAAmB,KAAKA,QAAQ,EAAE;IACvE,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgQ,UAAUA,CACxBjQ,IAA+B,EAC/BC,IAAoB,EACA;EACpB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,gBAAgB,KAAKD,QAAQ,IAC7B,aAAa,KAAKA,QAAQ,IAC1B,kBAAkB,KAAKA,QAAQ,IAC/B,gBAAgB,KAAKA,QAAQ,IAC7B,cAAc,KAAKA,QAAQ,IAC3B,qBAAqB,KAAKA,QAAQ,IAClC,oBAAoB,KAAKA,QAAQ,IACjC,SAAS,KAAKA,QAAQ,IACtB,cAAc,KAAKA,QAAQ,IAC3B,iBAAiB,KAAKA,QAAQ,IAC9B,gBAAgB,KAAKA,QAAQ,IAC7B,yBAAyB,KAAKA,QAAQ,IACtC,iBAAiB,KAAKA,QAAQ,IAC9B,kBAAkB,KAAKA,QAAQ,IAC/B,gBAAgB,KAAKA,QAAQ,IAC7B,aAAa,KAAKA,QAAQ,IAC1B,oBAAoB,KAAKA,QAAQ,IACjC,aAAa,KAAKA,QAAQ,IAC1B,eAAe,KAAKA,QAAQ,IAC3BA,QAAQ,KAAK,aAAa,IACzB,gBAAgB,KAAMF,IAAI,CAAmB8P,YAAa,EAC5D;IACA,IAAI,OAAO7P,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiQ,aAAaA,CAC3BlQ,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,gBAAgB,KAAKD,QAAQ,IAC7B,aAAa,KAAKA,QAAQ,IAC1B,kBAAkB,KAAKA,QAAQ,IAC/B,gBAAgB,KAAKA,QAAQ,IAC7B,cAAc,KAAKA,QAAQ,IAC3B,qBAAqB,KAAKA,QAAQ,IAClC,oBAAoB,KAAKA,QAAQ,IACjC,SAAS,KAAKA,QAAQ,IACtB,cAAc,KAAKA,QAAQ,IAC3B,iBAAiB,KAAKA,QAAQ,IAC9B,gBAAgB,KAAKA,QAAQ,IAC7B,yBAAyB,KAAKA,QAAQ,IACtC,gBAAgB,KAAKA,QAAQ,IAC7B,aAAa,KAAKA,QAAQ,IAC1B,oBAAoB,KAAKA,QAAQ,IACjC,aAAa,KAAKA,QAAQ,IAC1B,eAAe,KAAKA,QAAQ,IAC3BA,QAAQ,KAAK,aAAa,IACzB,gBAAgB,KAAMF,IAAI,CAAmB8P,YAAa,EAC5D;IACA,IAAI,OAAO7P,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkQ,OAAOA,CACrBnQ,IAA+B,EAC/BC,IAAoB,EACH;EACjB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,gBAAgB,KAAKD,QAAQ,IAC7B,SAAS,KAAKA,QAAQ,IACtB,eAAe,KAAKA,QAAQ,IAC3BA,QAAQ,KAAK,aAAa,IACzB,gBAAgB,KAAMF,IAAI,CAAmB8P,YAAa,EAC5D;IACA,IAAI,OAAO7P,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmQ,WAAWA,CACzBpQ,IAA+B,EAC/BC,IAAoB,EACC;EACrB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,gBAAgB,KAAKD,QAAQ,IAC7B,gBAAgB,KAAKA,QAAQ,IAC7B,mBAAmB,KAAKA,QAAQ,IAChC,mBAAmB,KAAKA,QAAQ,IAChC,kBAAkB,KAAKA,QAAQ,IAC/B,gBAAgB,KAAKA,QAAQ,IAC7B,qBAAqB,KAAKA,QAAQ,IAClC,gBAAgB,KAAKA,QAAQ,IAC7B,cAAc,KAAKA,QAAQ,IAC3B,qBAAqB,KAAKA,QAAQ,IAClC,aAAa,KAAKA,QAAQ,IAC1B,kBAAkB,KAAKA,QAAQ,IAC/B,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,gBAAgB,KAAKA,QAAQ,IAC7B,cAAc,KAAKA,QAAQ,IAC3B,qBAAqB,KAAKA,QAAQ,IAClC,gBAAgB,KAAKA,QAAQ,IAC7B,eAAe,KAAKA,QAAQ,IAC5B,kBAAkB,KAAKA,QAAQ,IAC/B,sBAAsB,KAAKA,QAAQ,IACnC,0BAA0B,KAAKA,QAAQ,IACvC,wBAAwB,KAAKA,QAAQ,IACrC,gBAAgB,KAAKA,QAAQ,IAC7B,mBAAmB,KAAKA,QAAQ,IAChC,cAAc,KAAKA,QAAQ,IAC3B,iBAAiB,KAAKA,QAAQ,IAC9B,kBAAkB,KAAKA,QAAQ,IAC/B,eAAe,KAAKA,QAAQ,IAC5B,sBAAsB,KAAKA,QAAQ,IACnC,kBAAkB,KAAKA,QAAQ,IAC/B,mBAAmB,KAAKA,QAAQ,IAChC,iBAAiB,KAAKA,QAAQ,IAC9B,0BAA0B,KAAKA,QAAQ,IACvC,6BAA6B,KAAKA,QAAQ,IAC1C,sBAAsB,KAAKA,QAAQ,IACnC,YAAY,KAAKA,QAAQ,IACzB,WAAW,KAAKA,QAAQ,IACxB,iBAAiB,KAAKA,QAAQ,IAC9B,mBAAmB,KAAKA,QAAQ,IAChC,wBAAwB,KAAKA,QAAQ,IACrC,wBAAwB,KAAKA,QAAQ,IACrC,mBAAmB,KAAKA,QAAQ,IAChC,qBAAqB,KAAKA,QAAQ,IAClC,2BAA2B,KAAKA,QAAQ,IACxC,oBAAoB,KAAKA,QAAQ,IACjC,8BAA8B,KAAKA,QAAQ,IAC1CA,QAAQ,KAAK,aAAa,KACxB,WAAW,KAAMF,IAAI,CAAmB8P,YAAY,IACnD,aAAa,KAAM9P,IAAI,CAAmB8P,YAAY,IACtD,gBAAgB,KAAM9P,IAAI,CAAmB8P,YAAY,CAAE,EAC/D;IACA,IAAI,OAAO7P,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoQ,gBAAgBA,CAC9BrQ,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,gBAAgB,KAAKD,QAAQ,IAC7B,mBAAmB,KAAKA,QAAQ,IAChC,iBAAiB,KAAKA,QAAQ,IAC9B,gBAAgB,KAAKA,QAAQ,IAC7B,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,EAC9B;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqQ,qBAAqBA,CACnCtQ,IAA+B,EAC/BC,IAAoB,EACW;EAC/B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,gBAAgB,KAAKD,QAAQ,IAC7B,mBAAmB,KAAKA,QAAQ,IAChC,iBAAiB,KAAKA,QAAQ,IAC9B,gBAAgB,KAAKA,QAAQ,EAC7B;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsQ,aAAaA,CAC3BvQ,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAI,uBAAuB,KAAKD,QAAQ,IAAI,aAAa,KAAKA,QAAQ,EAAE;IACtE,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuQ,MAAMA,CACpBxQ,IAA+B,EAC/BC,IAAoB,EACJ;EAChB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,kBAAkB,KAAKD,QAAQ,IAC/B,gBAAgB,KAAKA,QAAQ,IAC7B,cAAc,KAAKA,QAAQ,IAC3B,gBAAgB,KAAKA,QAAQ,IAC7B,gBAAgB,KAAKA,QAAQ,EAC7B;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwQ,OAAOA,CACrBzQ,IAA+B,EAC/BC,IAAoB,EACH;EACjB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAI,kBAAkB,KAAKD,QAAQ,IAAI,gBAAgB,KAAKA,QAAQ,EAAE;IACpE,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyQ,mBAAmBA,CACjC1Q,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,qBAAqB,KAAKD,QAAQ,IAClC,yBAAyB,KAAKA,QAAQ,IACtC,oBAAoB,KAAKA,QAAQ,EACjC;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0Q,KAAKA,CACnB3Q,IAA+B,EAC/BC,IAAoB,EACL;EACf,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,gBAAgB,KAAKD,QAAQ,IAC7B,cAAc,KAAKA,QAAQ,IAC3B,gBAAgB,KAAKA,QAAQ,EAC7B;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2Q,eAAeA,CAC7B5Q,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAI,gBAAgB,KAAKD,QAAQ,IAAI,gBAAgB,KAAKA,QAAQ,EAAE;IAClE,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4Q,UAAUA,CACxB7Q,IAA+B,EAC/BC,IAAoB,EACA;EACpB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,qBAAqB,KAAKD,QAAQ,IAClC,oBAAoB,KAAKA,QAAQ,IACjC,cAAc,KAAKA,QAAQ,IAC3B,yBAAyB,KAAKA,QAAQ,IACtC,aAAa,KAAKA,QAAQ,IAC1B,oBAAoB,KAAKA,QAAQ,EACjC;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6Q,gBAAgBA,CAC9B9Q,IAA+B,EAC/BC,IAAoB,EACM;EAC1B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,qBAAqB,KAAKD,QAAQ,IAClC,oBAAoB,KAAKA,QAAQ,IACjC,cAAc,KAAKA,QAAQ,IAC3B,yBAAyB,KAAKA,QAAQ,IACtC,aAAa,KAAKA,QAAQ,IAC1B,oBAAoB,KAAKA,QAAQ,IACjC,aAAa,KAAKA,QAAQ,IAC1B,eAAe,KAAKA,QAAQ,EAC5B;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8Q,SAASA,CACvB/Q,IAA+B,EAC/BC,IAAoB,EACD;EACnB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,qBAAqB,KAAKD,QAAQ,IAClC,oBAAoB,KAAKA,QAAQ,IACjC,eAAe,KAAKA,QAAQ,IAC5B,gBAAgB,KAAKA,QAAQ,IAC7B,aAAa,KAAKA,QAAQ,IAC1B,gBAAgB,KAAKA,QAAQ,IAC7B,eAAe,KAAKA,QAAQ,IAC5B,yBAAyB,KAAKA,QAAQ,IACtC,eAAe,KAAKA,QAAQ,IAC5B,gBAAgB,KAAKA,QAAQ,IAC5BA,QAAQ,KAAK,aAAa,IACzB,eAAe,KAAMF,IAAI,CAAmB8P,YAAa,EAC3D;IACA,IAAI,OAAO7P,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+Q,aAAaA,CAC3BhR,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,qBAAqB,KAAKD,QAAQ,IAClC,qBAAqB,KAAKA,QAAQ,IAClC,kBAAkB,KAAKA,QAAQ,IAC/B,sBAAsB,KAAKA,QAAQ,IACnC,0BAA0B,KAAKA,QAAQ,IACvC,wBAAwB,KAAKA,QAAQ,IACrC,mBAAmB,KAAKA,QAAQ,IAChC,cAAc,KAAKA,QAAQ,IAC3B,iBAAiB,KAAKA,QAAQ,IAC9B,kBAAkB,KAAKA,QAAQ,IAC/B,eAAe,KAAKA,QAAQ,IAC5B,sBAAsB,KAAKA,QAAQ,IACnC,kBAAkB,KAAKA,QAAQ,IAC/B,mBAAmB,KAAKA,QAAQ,IAChC,iBAAiB,KAAKA,QAAQ,IAC9B,0BAA0B,KAAKA,QAAQ,IACvC,6BAA6B,KAAKA,QAAQ,IAC1C,sBAAsB,KAAKA,QAAQ,IACnC,YAAY,KAAKA,QAAQ,IACzB,WAAW,KAAKA,QAAQ,IACxB,iBAAiB,KAAKA,QAAQ,IAC9B,mBAAmB,KAAKA,QAAQ,IAChC,wBAAwB,KAAKA,QAAQ,IACrC,wBAAwB,KAAKA,QAAQ,IACrC,mBAAmB,KAAKA,QAAQ,IAChC,qBAAqB,KAAKA,QAAQ,IACjCA,QAAQ,KAAK,aAAa,IACzB,aAAa,KAAMF,IAAI,CAAmB8P,YAAa,EACzD;IACA,IAAI,OAAO7P,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgR,aAAaA,CAC3BjR,IAA+B,EAC/BC,IAAoB,EACG;EACvB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,YAAY,KAAKD,QAAQ,IACzB,aAAa,KAAKA,QAAQ,IAC1B,mBAAmB,KAAKA,QAAQ,IAChC,cAAc,KAAKA,QAAQ,IAC3B,eAAe,KAAKA,QAAQ,IAC5B,gBAAgB,KAAKA,QAAQ,IAC7B,uBAAuB,KAAKA,QAAQ,IACpC,iBAAiB,KAAKA,QAAQ,IAC9B,qBAAqB,KAAKA,QAAQ,IACjCA,QAAQ,KAAK,aAAa,KACxB,SAAS,KAAMF,IAAI,CAAmB8P,YAAY,IACjD,YAAY,KAAM9P,IAAI,CAAmB8P,YAAY,CAAE,EAC3D;IACA,IAAI,OAAO7P,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiR,MAAMA,CACpBlR,IAA+B,EAC/BC,IAAoB,EACJ;EAChB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,YAAY,KAAKD,QAAQ,IACzB,kBAAkB,KAAKA,QAAQ,IAC/B,aAAa,KAAKA,QAAQ,IAC1B,mBAAmB,KAAKA,QAAQ,IAChC,cAAc,KAAKA,QAAQ,IAC3B,eAAe,KAAKA,QAAQ,IAC5B,qBAAqB,KAAKA,QAAQ,IAClC,gBAAgB,KAAKA,QAAQ,IAC7B,uBAAuB,KAAKA,QAAQ,IACpC,iBAAiB,KAAKA,QAAQ,IAC9B,qBAAqB,KAAKA,QAAQ,IACjCA,QAAQ,KAAK,aAAa,KACxB,SAAS,KAAMF,IAAI,CAAmB8P,YAAY,IACjD,YAAY,KAAM9P,IAAI,CAAmB8P,YAAY,CAAE,EAC3D;IACA,IAAI,OAAO7P,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkR,cAAcA,CAC5BnR,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,YAAY,KAAKD,QAAQ,IACzB,iBAAiB,KAAKA,QAAQ,IAC7BA,QAAQ,KAAK,aAAa,IACzB,YAAY,KAAMF,IAAI,CAAmB8P,YAAa,EACxD;IACA,IAAI,OAAO7P,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmR,SAASA,CACvBpR,IAA+B,EAC/BC,IAAoB,EACD;EACnB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,eAAe,KAAKD,QAAQ,IAC5B,gBAAgB,KAAKA,QAAQ,IAC7B,aAAa,KAAKA,QAAQ,IAC1B,gBAAgB,KAAKA,QAAQ,IAC7B,eAAe,KAAKA,QAAQ,IAC5B,iBAAiB,KAAKA,QAAQ,IAC9B,eAAe,KAAKA,QAAQ,IAC5B,gBAAgB,KAAKA,QAAQ,IAC5BA,QAAQ,KAAK,aAAa,IACzB,eAAe,KAAMF,IAAI,CAAmB8P,YAAa,EAC3D;IACA,IAAI,OAAO7P,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoR,WAAWA,CACzBrR,IAA+B,EAC/BC,IAAoB,EACC;EACrB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,eAAe,KAAKD,QAAQ,IAC5B,gBAAgB,KAAKA,QAAQ,IAC7B,aAAa,KAAKA,QAAQ,IAC1B,gBAAgB,KAAKA,QAAQ,IAC7B,eAAe,KAAKA,QAAQ,IAC5B,cAAc,KAAKA,QAAQ,IAC3B,mBAAmB,KAAKA,QAAQ,IAChC,YAAY,KAAKA,QAAQ,IACzB,wBAAwB,KAAKA,QAAQ,IACrC,gBAAgB,KAAKA,QAAQ,IAC7B,mBAAmB,KAAKA,QAAQ,IAChC,SAAS,KAAKA,QAAQ,IACtB,aAAa,KAAKA,QAAQ,IAC1B,oBAAoB,KAAKA,QAAQ,IACjC,oBAAoB,KAAKA,QAAQ,IACjC,gBAAgB,KAAKA,QAAQ,IAC5BA,QAAQ,KAAK,aAAa,IACzB,eAAe,KAAMF,IAAI,CAAmB8P,YAAa,EAC3D;IACA,IAAI,OAAO7P,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqR,mBAAmBA,CACjCtR,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,cAAc,KAAKD,QAAQ,IAC3B,gBAAgB,KAAKA,QAAQ,IAC7B,wBAAwB,KAAKA,QAAQ,IACrC,wBAAwB,KAAKA,QAAQ,IACrC,mBAAmB,KAAKA,QAAQ,IAChC,oBAAoB,KAAKA,QAAQ,IACjC,0BAA0B,KAAKA,QAAQ,EACvC;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsR,QAAQA,CACtBvR,IAA+B,EAC/BC,IAAoB,EACF;EAClB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,cAAc,KAAKD,QAAQ,IAC3B,aAAa,KAAKA,QAAQ,IAC1B,oBAAoB,KAAKA,QAAQ,EACjC;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuR,cAAcA,CAC5BxR,IAA+B,EAC/BC,IAAoB,EACI;EACxB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAI,cAAc,KAAKD,QAAQ,IAAI,gBAAgB,KAAKA,QAAQ,EAAE;IAChE,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwR,UAAUA,CACxBzR,IAA+B,EAC/BC,IAAoB,EACA;EACpB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,gBAAgB,KAAKD,QAAQ,IAC7B,eAAe,KAAKA,QAAQ,IAC5B,uBAAuB,KAAKA,QAAQ,IACpC,sBAAsB,KAAKA,QAAQ,EACnC;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyR,WAAWA,CACzB1R,IAA+B,EAC/BC,IAAoB,EACC;EACrB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAI,iBAAiB,KAAKD,QAAQ,IAAI,eAAe,KAAKA,QAAQ,EAAE;IAClE,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0R,SAASA,CACvB3R,IAA+B,EAC/BC,IAAoB,EACD;EACnB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,mBAAmB,KAAKD,QAAQ,IAChC,cAAc,KAAKA,QAAQ,IAC3B,eAAe,KAAKA,QAAQ,IAC3BA,QAAQ,KAAK,aAAa,IACzB,SAAS,KAAMF,IAAI,CAAmB8P,YAAa,EACrD;IACA,IAAI,OAAO7P,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2R,OAAOA,CACrB5R,IAA+B,EAC/BC,IAAoB,EACH;EACjB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAI,iBAAiB,KAAKD,QAAQ,IAAI,kBAAkB,KAAKA,QAAQ,EAAE;IACrE,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4R,2BAA2BA,CACzC7R,IAA+B,EAC/BC,IAAoB,EACiB;EACrC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,sBAAsB,KAAKD,QAAQ,IACnC,0BAA0B,KAAKA,QAAQ,IACvC,wBAAwB,KAAKA,QAAQ,IACrC,mBAAmB,KAAKA,QAAQ,EAChC;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6R,mBAAmBA,CACjC9R,IAA+B,EAC/BC,IAAoB,EACS;EAC7B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,sBAAsB,KAAKD,QAAQ,IACnC,0BAA0B,KAAKA,QAAQ,IACvC,wBAAwB,KAAKA,QAAQ,EACrC;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8R,iBAAiBA,CAC/B/R,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,iBAAiB,KAAKD,QAAQ,IAC9B,wBAAwB,KAAKA,QAAQ,IACrC,0BAA0B,KAAKA,QAAQ,IACvC,iBAAiB,KAAKA,QAAQ,IAC9B,0BAA0B,KAAKA,QAAQ,IACvC,wBAAwB,KAAKA,QAAQ,EACrC;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS+R,UAAUA,CACxBhS,IAA+B,EAC/BC,IAAoB,EACA;EACpB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAI,uBAAuB,KAAKD,QAAQ,EAAE;IACxC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgS,SAASA,CACvBjS,IAA+B,EAC/BC,IAAoB,EACD;EACnB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,sBAAsB,KAAKD,QAAQ,IACnC,oBAAoB,KAAKA,QAAQ,IACjC,aAAa,KAAKA,QAAQ,EAC1B;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiS,MAAMA,CACpBlS,IAA+B,EAC/BC,IAAoB,EACJ;EAChB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,mBAAmB,KAAKD,QAAQ,IAChC,qBAAqB,KAAKA,QAAQ,IAClC,uBAAuB,KAAKA,QAAQ,IACpC,8BAA8B,KAAKA,QAAQ,IAC3C,2BAA2B,KAAKA,QAAQ,IACxC,iBAAiB,KAAKA,QAAQ,IAC9B,cAAc,KAAKA,QAAQ,IAC3B,iBAAiB,KAAKA,QAAQ,IAC9B,kBAAkB,KAAKA,QAAQ,IAC/B,eAAe,KAAKA,QAAQ,IAC5B,sBAAsB,KAAKA,QAAQ,IACnC,kBAAkB,KAAKA,QAAQ,IAC/B,mBAAmB,KAAKA,QAAQ,IAChC,iBAAiB,KAAKA,QAAQ,IAC9B,0BAA0B,KAAKA,QAAQ,IACvC,6BAA6B,KAAKA,QAAQ,IAC1C,mBAAmB,KAAKA,QAAQ,IAChC,sBAAsB,KAAKA,QAAQ,IACnC,wBAAwB,KAAKA,QAAQ,IACrC,mBAAmB,KAAKA,QAAQ,IAChC,uBAAuB,KAAKA,QAAQ,IACpC,mBAAmB,KAAKA,QAAQ,IAChC,kBAAkB,KAAKA,QAAQ,IAC/B,sBAAsB,KAAKA,QAAQ,IACnC,yBAAyB,KAAKA,QAAQ,IACtC,4BAA4B,KAAKA,QAAQ,IACzC,qBAAqB,KAAKA,QAAQ,IAClC,qBAAqB,KAAKA,QAAQ,IAClC,wBAAwB,KAAKA,QAAQ,IACrC,6BAA6B,KAAKA,QAAQ,IAC1C,sBAAsB,KAAKA,QAAQ,IACnC,sBAAsB,KAAKA,QAAQ,IACnC,wBAAwB,KAAKA,QAAQ,IACrC,wBAAwB,KAAKA,QAAQ,IACrC,mBAAmB,KAAKA,QAAQ,IAChC,oBAAoB,KAAKA,QAAQ,IACjC,0BAA0B,KAAKA,QAAQ,IACvC,YAAY,KAAKA,QAAQ,IACzB,yBAAyB,KAAKA,QAAQ,IACtC,6BAA6B,KAAKA,QAAQ,IAC1C,sBAAsB,KAAKA,QAAQ,IACnC,sBAAsB,KAAKA,QAAQ,IACnC,oBAAoB,KAAKA,QAAQ,IACjC,qBAAqB,KAAKA,QAAQ,IAClC,sBAAsB,KAAKA,QAAQ,IACnC,WAAW,KAAKA,QAAQ,IACxB,gBAAgB,KAAKA,QAAQ,IAC7B,oBAAoB,KAAKA,QAAQ,IACjC,eAAe,KAAKA,QAAQ,IAC5B,0BAA0B,KAAKA,QAAQ,IACvC,4BAA4B,KAAKA,QAAQ,IACzC,qBAAqB,KAAKA,QAAQ,IAClC,UAAU,KAAKA,QAAQ,IACvB,oBAAoB,KAAKA,QAAQ,IACjC,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,gBAAgB,KAAKA,QAAQ,IAC7B,gBAAgB,KAAKA,QAAQ,IAC7B,gBAAgB,KAAKA,QAAQ,IAC7B,mBAAmB,KAAKA,QAAQ,IAChC,kBAAkB,KAAKA,QAAQ,IAC/B,kBAAkB,KAAKA,QAAQ,IAC/B,qBAAqB,KAAKA,QAAQ,IAClC,mBAAmB,KAAKA,QAAQ,IAChC,2BAA2B,KAAKA,QAAQ,EACxC;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkS,UAAUA,CACxBnS,IAA+B,EAC/BC,IAAoB,EACA;EACpB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,mBAAmB,KAAKD,QAAQ,IAChC,qBAAqB,KAAKA,QAAQ,IAClC,uBAAuB,KAAKA,QAAQ,IACpC,8BAA8B,KAAKA,QAAQ,IAC3C,2BAA2B,KAAKA,QAAQ,IACxC,sBAAsB,KAAKA,QAAQ,IACnC,wBAAwB,KAAKA,QAAQ,IACrC,uBAAuB,KAAKA,QAAQ,IACpC,yBAAyB,KAAKA,QAAQ,IACtC,4BAA4B,KAAKA,QAAQ,IACzC,qBAAqB,KAAKA,QAAQ,IAClC,qBAAqB,KAAKA,QAAQ,IAClC,wBAAwB,KAAKA,QAAQ,IACrC,6BAA6B,KAAKA,QAAQ,IAC1C,sBAAsB,KAAKA,QAAQ,IACnC,sBAAsB,KAAKA,QAAQ,IACnC,6BAA6B,KAAKA,QAAQ,IAC1C,sBAAsB,KAAKA,QAAQ,IACnC,sBAAsB,KAAKA,QAAQ,IACnC,oBAAoB,KAAKA,QAAQ,IACjC,qBAAqB,KAAKA,QAAQ,IAClC,sBAAsB,KAAKA,QAAQ,IACnC,qBAAqB,KAAKA,QAAQ,IAClC,oBAAoB,KAAKA,QAAQ,IACjC,mBAAmB,KAAKA,QAAQ,IAChC,2BAA2B,KAAKA,QAAQ,EACxC;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmS,oBAAoBA,CAClCpS,IAA+B,EAC/BC,IAAoB,EACU;EAC9B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,mBAAmB,KAAKD,QAAQ,IAChC,uBAAuB,KAAKA,QAAQ,IACpC,2BAA2B,KAAKA,QAAQ,IACxC,qBAAqB,KAAKA,QAAQ,IAClC,qBAAqB,KAAKA,QAAQ,IAClC,sBAAsB,KAAKA,QAAQ,IACnC,sBAAsB,KAAKA,QAAQ,IACnC,sBAAsB,KAAKA,QAAQ,IACnC,oBAAoB,KAAKA,QAAQ,IACjC,oBAAoB,KAAKA,QAAQ,EACjC;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASoS,iBAAiBA,CAC/BrS,IAA+B,EAC/BC,IAAoB,EACO;EAC3B,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,cAAc,KAAKD,QAAQ,IAC3B,iBAAiB,KAAKA,QAAQ,IAC9B,kBAAkB,KAAKA,QAAQ,IAC/B,eAAe,KAAKA,QAAQ,IAC5B,sBAAsB,KAAKA,QAAQ,IACnC,kBAAkB,KAAKA,QAAQ,IAC/B,mBAAmB,KAAKA,QAAQ,IAChC,iBAAiB,KAAKA,QAAQ,IAC9B,0BAA0B,KAAKA,QAAQ,IACvC,6BAA6B,KAAKA,QAAQ,IAC1C,sBAAsB,KAAKA,QAAQ,IACnC,YAAY,KAAKA,QAAQ,IACzB,WAAW,KAAKA,QAAQ,EACxB;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASqS,eAAeA,CAC7BtS,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAI,mBAAmB,KAAKD,QAAQ,IAAI,mBAAmB,KAAKA,QAAQ,EAAE;IACxE,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASsS,UAAUA,CACxBvS,IAA+B,EAC/BC,IAAoB,EACA;EACpB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,iBAAiB,KAAKD,QAAQ,IAC9B,gBAAgB,KAAKA,QAAQ,IAC7B,gBAAgB,KAAKA,QAAQ,IAC7B,gBAAgB,KAAKA,QAAQ,EAC7B;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASuS,YAAYA,CAC1BxS,IAA+B,EAC/BC,IAAoB,EACE;EACtB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,mBAAmB,KAAKD,QAAQ,IAChC,kBAAkB,KAAKA,QAAQ,IAC/B,kBAAkB,KAAKA,QAAQ,IAC/B,qBAAqB,KAAKA,QAAQ,EAClC;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASwS,KAAKA,CACnBzS,IAA+B,EAC/BC,IAAoB,EACL;EACf,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,cAAc,KAAKD,QAAQ,IAC3B,mBAAmB,KAAKA,QAAQ,IAChC,YAAY,KAAKA,QAAQ,IACzB,oBAAoB,KAAKA,QAAQ,IACjC,wBAAwB,KAAKA,QAAQ,IACrC,gBAAgB,KAAKA,QAAQ,IAC7B,eAAe,KAAKA,QAAQ,IAC5B,qBAAqB,KAAKA,QAAQ,IAClC,mBAAmB,KAAKA,QAAQ,IAChC,mBAAmB,KAAKA,QAAQ,IAChC,oBAAoB,KAAKA,QAAQ,IACjC,SAAS,KAAKA,QAAQ,IACtB,aAAa,KAAKA,QAAQ,IAC1B,oBAAoB,KAAKA,QAAQ,IACjC,oBAAoB,KAAKA,QAAQ,EACjC;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASyS,eAAeA,CAC7B1S,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,MAAM,KAAKD,QAAQ,IACnB,aAAa,KAAKA,QAAQ,IAC1B,uBAAuB,KAAKA,QAAQ,EACpC;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS0S,YAAYA,CAC1B3S,IAA+B,EAC/BC,IAAoB,EACE;EACtB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,qBAAqB,KAAKD,QAAQ,IAClC,mBAAmB,KAAKA,QAAQ,IAChC,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,4BAA4B,KAAKA,QAAQ,IACzC,iCAAiC,KAAKA,QAAQ,IAC9C,qBAAqB,KAAKA,QAAQ,IAClC,mBAAmB,KAAKA,QAAQ,IAChC,kBAAkB,KAAKA,QAAQ,IAC/B,cAAc,KAAKA,QAAQ,IAC3B,kBAAkB,KAAKA,QAAQ,IAC/B,iBAAiB,KAAKA,QAAQ,IAC9B,oBAAoB,KAAKA,QAAQ,IACjC,gBAAgB,KAAKA,QAAQ,IAC7B,eAAe,KAAKA,QAAQ,IAC5B,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,oBAAoB,KAAKA,QAAQ,IACjC,kBAAkB,KAAKA,QAAQ,IAC/B,eAAe,KAAKA,QAAQ,IAC5B,YAAY,KAAKA,QAAQ,IACzB,gBAAgB,KAAKA,QAAQ,IAC7B,mBAAmB,KAAKA,QAAQ,IAChC,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,aAAa,KAAKA,QAAQ,IAC1B,eAAe,KAAKA,QAAQ,IAC5B,aAAa,KAAKA,QAAQ,IAC1B,aAAa,KAAKA,QAAQ,IAC1B,gBAAgB,KAAKA,QAAQ,IAC7B,YAAY,KAAKA,QAAQ,IACzB,oBAAoB,KAAKA,QAAQ,IACjC,aAAa,KAAKA,QAAQ,IAC1B,oBAAoB,KAAKA,QAAQ,IACjC,mBAAmB,KAAKA,QAAQ,IAChC,aAAa,KAAKA,QAAQ,IAC1B,qBAAqB,KAAKA,QAAQ,IAClC,gBAAgB,KAAKA,QAAQ,IAC7B,qBAAqB,KAAKA,QAAQ,IAClC,cAAc,KAAKA,QAAQ,IAC3B,eAAe,KAAKA,QAAQ,IAC5B,+BAA+B,KAAKA,QAAQ,IAC5C,wBAAwB,KAAKA,QAAQ,IACrC,iBAAiB,KAAKA,QAAQ,IAC9B,wBAAwB,KAAKA,QAAQ,IACrC,2BAA2B,KAAKA,QAAQ,IACxC,gBAAgB,KAAKA,QAAQ,IAC7B,uBAAuB,KAAKA,QAAQ,IACpC,iBAAiB,KAAKA,QAAQ,IAC9B,mBAAmB,KAAKA,QAAQ,IAChC,cAAc,KAAKA,QAAQ,IAC3B,qBAAqB,KAAKA,QAAQ,IAClC,eAAe,KAAKA,QAAQ,IAC5B,cAAc,KAAKA,QAAQ,IAC3B,2BAA2B,KAAKA,QAAQ,IACxC,2BAA2B,KAAKA,QAAQ,IACxC,qBAAqB,KAAKA,QAAQ,IAClC,oBAAoB,KAAKA,QAAQ,IACjC,8BAA8B,KAAKA,QAAQ,IAC3C,kBAAkB,KAAKA,QAAQ,IAC/B,8BAA8B,KAAKA,QAAQ,IAC3C,4BAA4B,KAAKA,QAAQ,IACzC,iBAAiB,KAAKA,QAAQ,EAC9B;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS2S,eAAeA,CAC7B5S,IAA+B,EAC/BC,IAAoB,EACK;EACzB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,4BAA4B,KAAKD,QAAQ,IACzC,iCAAiC,KAAKA,QAAQ,IAC9C,qBAAqB,KAAKA,QAAQ,IAClC,mBAAmB,KAAKA,QAAQ,IAChC,kBAAkB,KAAKA,QAAQ,EAC/B;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS4S,QAAQA,CACtB7S,IAA+B,EAC/BC,IAAoB,EACF;EAClB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,cAAc,KAAKD,QAAQ,IAC3B,kBAAkB,KAAKA,QAAQ,IAC/B,iBAAiB,KAAKA,QAAQ,IAC9B,oBAAoB,KAAKA,QAAQ,IACjC,gBAAgB,KAAKA,QAAQ,IAC7B,eAAe,KAAKA,QAAQ,IAC5B,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,oBAAoB,KAAKA,QAAQ,IACjC,kBAAkB,KAAKA,QAAQ,IAC/B,eAAe,KAAKA,QAAQ,IAC5B,YAAY,KAAKA,QAAQ,IACzB,gBAAgB,KAAKA,QAAQ,IAC7B,mBAAmB,KAAKA,QAAQ,IAChC,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,aAAa,KAAKA,QAAQ,IAC1B,eAAe,KAAKA,QAAQ,IAC5B,aAAa,KAAKA,QAAQ,IAC1B,aAAa,KAAKA,QAAQ,IAC1B,gBAAgB,KAAKA,QAAQ,IAC7B,YAAY,KAAKA,QAAQ,IACzB,aAAa,KAAKA,QAAQ,IAC1B,oBAAoB,KAAKA,QAAQ,IACjC,mBAAmB,KAAKA,QAAQ,IAChC,aAAa,KAAKA,QAAQ,IAC1B,qBAAqB,KAAKA,QAAQ,IAClC,gBAAgB,KAAKA,QAAQ,IAC7B,qBAAqB,KAAKA,QAAQ,IAClC,cAAc,KAAKA,QAAQ,IAC3B,eAAe,KAAKA,QAAQ,IAC5B,+BAA+B,KAAKA,QAAQ,IAC5C,cAAc,KAAKA,QAAQ,EAC3B;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS6S,YAAYA,CAC1B9S,IAA+B,EAC/BC,IAAoB,EACE;EACtB,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IACE,cAAc,KAAKD,QAAQ,IAC3B,kBAAkB,KAAKA,QAAQ,IAC/B,iBAAiB,KAAKA,QAAQ,IAC9B,oBAAoB,KAAKA,QAAQ,IACjC,gBAAgB,KAAKA,QAAQ,IAC7B,eAAe,KAAKA,QAAQ,IAC5B,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,iBAAiB,KAAKA,QAAQ,IAC9B,oBAAoB,KAAKA,QAAQ,IACjC,kBAAkB,KAAKA,QAAQ,IAC/B,eAAe,KAAKA,QAAQ,IAC5B,YAAY,KAAKA,QAAQ,IACzB,eAAe,KAAKA,QAAQ,EAC5B;IACA,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAAS8S,eAAeA,CAC7B/S,IAA+B,EAC/BC,IAAoB,EACX;EACT,IAAA+S,2BAAkB,EAAC,iBAAiB,EAAE,kBAAkB,CAAC;EACzD,IAAI,CAAChT,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,eAAe,EAAE;IAChC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASgT,cAAcA,CAC5BjT,IAA+B,EAC/BC,IAAoB,EACX;EACT,IAAA+S,2BAAkB,EAAC,gBAAgB,EAAE,iBAAiB,CAAC;EACvD,IAAI,CAAChT,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,cAAc,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASiT,cAAcA,CAC5BlT,IAA+B,EAC/BC,IAAoB,EACX;EACT,IAAA+S,2BAAkB,EAAC,gBAAgB,EAAE,eAAe,CAAC;EACrD,IAAI,CAAChT,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,cAAc,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASkT,gBAAgBA,CAC9BnT,IAA+B,EAC/BC,IAAoB,EACX;EACT,IAAA+S,2BAAkB,EAAC,kBAAkB,EAAE,iBAAiB,CAAC;EACzD,IAAI,CAAChT,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,QAAQ,GAAIF,IAAI,CAAYG,IAAI;EACtC,IAAID,QAAQ,KAAK,gBAAgB,EAAE;IACjC,IAAI,OAAOD,IAAI,KAAK,WAAW,EAAE;MAC/B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,IAAAG,qBAAY,EAACJ,IAAI,EAAEC,IAAI,CAAC;IACjC;EACF;EAEA,OAAO,KAAK;AACd;AACO,SAASmT,mBAAmBA,CACjCpT,IAA+B,EAC/BC,IAAoB,EACiB;EACrC,IAAA+S,2BAAkB,EAAC,qBAAqB,EAAE,6BAA6B,CAAC;EACxE,OAAOnB,2BAA2B,CAAC7R,IAAI,EAAEC,IAAI,CAAC;AAChD"}