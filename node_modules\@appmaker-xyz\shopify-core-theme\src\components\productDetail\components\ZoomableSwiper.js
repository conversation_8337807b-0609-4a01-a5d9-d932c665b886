import React, { useState } from 'react';
import {
  Dimensions,
  View,
  StyleSheet,
  FlatList,
  Image,
  Pressable,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import ImageViewer from 'react-native-image-zoom-viewer';
import { applyFilters } from '@appmaker-xyz/core';
import { useProductImages } from '@appmaker-xyz/shopify';
const { width, height } = Dimensions.get('window');

const ImageSwiper = ({ attributes, onPress, onAction, ...props }) => {
  const [imageIndex, setImageIndex] = useState(
    props.currentAction?.params?.index,
  );
  const [isImageZoomed, setIsImageZoomed] = useState(false);
  const backgroundColor = applyFilters(
    'zoomable-image-background-color',
    '#000000',
  );
  const imageList = props.currentAction?.params?.images;
  let images = [];
  imageList &&
    imageList.map((value, key) => {
      images.push({
        url: value?.uri,
        thumbnail: value?.thumbnail,
        key: key,
        props: {
          width,
          height,
        },
      });
      return images;
    });
  const imageViewKey = `pdp-zoom-image-${
    images?.[images?.length - 1]?.url || ''
  }`;

  return (
    <View style={styles.innerContainer}>
      <Icon
        name="x"
        size={22}
        style={styles.icon}
        onPress={() => {
          onAction({ action: 'GO_BACK' });
        }}
        color="#ffffff"
      />
      <ImageViewer
        key={imageViewKey}
        imageUrls={images}
        saveToLocalByLongPress={false}
        useNativeDriver={true}
        index={imageIndex ? imageIndex : 0}
        onCancel={() => onAction({ action: 'GO_BACK' })}
        renderIndicator={() => null}
        enableSwipeDown
        backgroundColor={backgroundColor}
        onDoubleClick={() => setIsImageZoomed(!isImageZoomed)}
        onChange={(index) => {
          setImageIndex(index);
          isImageZoomed && setIsImageZoomed(false);
        }}
      />
      {!isImageZoomed ? (
        <FlatList
          horizontal
          contentContainerStyle={styles.contentContainer}
          style={styles.scrollView}
          showsHorizontalScrollIndicator={false}
          data={images}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
          renderItem={({ item }) => {
            return (
              <Pressable
                onPress={() => setImageIndex(item.key)}
                style={item.key === imageIndex ? styles.activeItem : null}>
                <Image
                  source={{ uri: item.thumbnail }}
                  style={styles.indicatorImage}
                  resizeMode="cover"
                />
              </Pressable>
            );
          }}
          keyExtractor={(item) => item.key}
        />
      ) : null}
    </View>
  );
};

const aspectRatio = height / width;
const hasNotch = aspectRatio > 1.8 && Platform.OS === 'ios';

const styles = StyleSheet.create({
  container: { flex: 1 },
  innerContainer: {
    width,
    height: height,
    position: 'relative',
  },
  icon: {
    position: 'absolute',
    right: 18,
    top: hasNotch ? 64 : 18,
    zIndex: 1,
    padding: 6,
    backgroundColor: '#00000066',
    borderRadius: 20,
    overflow: 'hidden',
  },
  contentContainer: {
    justifyContent: 'center',
    paddingHorizontal: 6,
    flexGrow: 1,
  },
  scrollView: {
    position: 'absolute',
    bottom: 20,
  },
  indicatorImage: {
    width: 50,
    height: 70,
  },
  separator: {
    width: 6,
  },
  activeItem: {
    borderTopColor: '#000',
    borderTopWidth: 3,
  },
});

export default ImageSwiper;
