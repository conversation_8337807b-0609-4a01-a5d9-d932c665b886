import React, { useState } from 'react';
import { StyleSheet, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { Layout, ThemeText, AppTouchable } from '../atoms';
import PercentageIcon from './components/PercentageIcon';
import { useDiscount } from '@appmaker-xyz/shopify';
import CouponIcon from './components/CouponIcon';

const CartCouponBar = ({ attributes = {} }, props) => {
  const { themeColor = '#000000' } = attributes;
  const [isShowCoupon, setIsShowCoupon] = useState(false);
  const {
    couponsList,
    onApplyCoupon,
    onRemoveCoupon,
    couponTitle,
    couponTitleDisplay,
    hasCouponApplied,
    couponMessage,
    isCouponApplyingLoading,
    isCouponRemovingLoading,
  } = useDiscount(props);
  return (
    <Layout style={styles.container}>
      {hasCouponApplied ? (
        <Layout style={styles.innerContainer}>
          <Layout style={styles.titleContainer}>
            <CouponIcon color={themeColor} style={styles.percentageIcon} />
            <Layout>
              <ThemeText size="md" color="#000" fontFamily="medium">
                {couponTitleDisplay}
              </ThemeText>
              <ThemeText size="sm" color={themeColor} fontFamily="medium">
                {couponMessage}
              </ThemeText>
            </Layout>
          </Layout>
          {isCouponRemovingLoading ? (
            <ActivityIndicator size={18} color={'black'} />
          ) : (
            <Icon
              name="trash-2"
              size={18}
              color="#827E7E"
              onPress={onRemoveCoupon}
              style={styles.trashIcon}
            />
          )}
        </Layout>
      ) : (
        <AppTouchable
          style={styles.innerContainer}
          onPress={() => {
            setIsShowCoupon(true);
          }}>
          <Layout style={styles.titleContainer}>
            <PercentageIcon color={themeColor} style={styles.percentageIcon} />
            <Layout>
              <ThemeText size="md" color="#000" fontFamily="medium">
                Apply Coupon
              </ThemeText>
              <ThemeText size="sm" color={themeColor} fontFamily="medium">
                **You can save upto ₹499 on this order**
              </ThemeText>
            </Layout>
          </Layout>
          <Icon name="chevron-right" size={24} color="#000" />
        </AppTouchable>
      )}
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: 12,
  },
  innerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 8,
    borderWidth: 0.5,
    borderColor: '#EEEEEE',
    overflow: 'hidden',
    //shadow
    shadowColor: '#a4a4a4',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 4,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  percentageIcon: {
    marginRight: 8,
  },
  trashIcon: {
    padding: 4,
  },
});

export default CartCouponBar;
