{"version": 3, "names": ["originalGetModuleName", "getModuleName", "rootOpts", "pluginOpts", "moduleId", "moduleIds", "getModuleId", "moduleRoot", "filename", "filenameRelative", "sourceRoot", "moduleName", "sourceRootReplacer", "RegExp", "replace"], "sources": ["../src/get-module-name.ts"], "sourcesContent": ["type RootOptions = {\n  filename?: string;\n  filenameRelative?: string;\n  sourceRoot?: string;\n};\n\nexport type PluginOptions = {\n  moduleId?: string;\n  moduleIds?: boolean;\n  getModuleId?: (moduleName: string) => string | null | undefined;\n  moduleRoot?: string;\n};\n\nif (!process.env.BABEL_8_BREAKING) {\n  const originalGetModuleName = getModuleName;\n\n  // @ts-expect-error TS doesn't like reassigning a function.\n  // eslint-disable-next-line no-func-assign\n  getModuleName = function getModuleName(\n    rootOpts: RootOptions & PluginOptions,\n    pluginOpts: PluginOptions,\n  ): string | null {\n    return originalGetModuleName(rootOpts, {\n      moduleId: pluginOpts.moduleId ?? rootOpts.moduleId,\n      moduleIds: pluginOpts.moduleIds ?? rootOpts.moduleIds,\n      getModuleId: pluginOpts.getModuleId ?? rootOpts.getModuleId,\n      moduleRoot: pluginOpts.moduleRoot ?? rootOpts.moduleRoot,\n    });\n  };\n}\n\nexport default function getModuleName(\n  rootOpts: RootOptions,\n  pluginOpts: PluginOptions,\n): string | null {\n  const {\n    filename,\n    filenameRelative = filename,\n    sourceRoot = pluginOpts.moduleRoot,\n  } = rootOpts;\n\n  const {\n    moduleId,\n    moduleIds = !!moduleId,\n\n    getModuleId,\n\n    moduleRoot = sourceRoot,\n  } = pluginOpts;\n\n  if (!moduleIds) return null;\n\n  // moduleId is n/a if a `getModuleId()` is provided\n  if (moduleId != null && !getModuleId) {\n    return moduleId;\n  }\n\n  let moduleName = moduleRoot != null ? moduleRoot + \"/\" : \"\";\n\n  if (filenameRelative) {\n    const sourceRootReplacer =\n      sourceRoot != null ? new RegExp(\"^\" + sourceRoot + \"/?\") : \"\";\n\n    moduleName += filenameRelative\n      // remove sourceRoot from filename\n      .replace(sourceRootReplacer, \"\")\n      // remove extension\n      .replace(/\\.(\\w*?)$/, \"\");\n  }\n\n  // normalize path separators\n  moduleName = moduleName.replace(/\\\\/g, \"/\");\n\n  if (getModuleId) {\n    // If return is falsy, assume they want us to use our generated default name\n    return getModuleId(moduleName) || moduleName;\n  } else {\n    return moduleName;\n  }\n}\n"], "mappings": ";;;;;;AAamC;EACjC,MAAMA,qBAAqB,GAAGC,aAAa;EAI3C,kBAAAA,aAAa,GAAG,SAASA,aAAa,CACpCC,QAAqC,EACrCC,UAAyB,EACV;IAAA;IACf,OAAOH,qBAAqB,CAACE,QAAQ,EAAE;MACrCE,QAAQ,0BAAED,UAAU,CAACC,QAAQ,mCAAIF,QAAQ,CAACE,QAAQ;MAClDC,SAAS,2BAAEF,UAAU,CAACE,SAAS,oCAAIH,QAAQ,CAACG,SAAS;MACrDC,WAAW,2BAAEH,UAAU,CAACG,WAAW,oCAAIJ,QAAQ,CAACI,WAAW;MAC3DC,UAAU,2BAAEJ,UAAU,CAACI,UAAU,oCAAIL,QAAQ,CAACK;IAChD,CAAC,CAAC;EACJ,CAAC;AACH;AAEe,SAASN,aAAa,CACnCC,QAAqB,EACrBC,UAAyB,EACV;EACf,MAAM;IACJK,QAAQ;IACRC,gBAAgB,GAAGD,QAAQ;IAC3BE,UAAU,GAAGP,UAAU,CAACI;EAC1B,CAAC,GAAGL,QAAQ;EAEZ,MAAM;IACJE,QAAQ;IACRC,SAAS,GAAG,CAAC,CAACD,QAAQ;IAEtBE,WAAW;IAEXC,UAAU,GAAGG;EACf,CAAC,GAAGP,UAAU;EAEd,IAAI,CAACE,SAAS,EAAE,OAAO,IAAI;EAG3B,IAAID,QAAQ,IAAI,IAAI,IAAI,CAACE,WAAW,EAAE;IACpC,OAAOF,QAAQ;EACjB;EAEA,IAAIO,UAAU,GAAGJ,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAG,GAAG,GAAG,EAAE;EAE3D,IAAIE,gBAAgB,EAAE;IACpB,MAAMG,kBAAkB,GACtBF,UAAU,IAAI,IAAI,GAAG,IAAIG,MAAM,CAAC,GAAG,GAAGH,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE;IAE/DC,UAAU,IAAIF,gBAAgB,CAE3BK,OAAO,CAACF,kBAAkB,EAAE,EAAE,CAAC,CAE/BE,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;EAC7B;EAGAH,UAAU,GAAGA,UAAU,CAACG,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAE3C,IAAIR,WAAW,EAAE;IAEf,OAAOA,WAAW,CAACK,UAAU,CAAC,IAAIA,UAAU;EAC9C,CAAC,MAAM;IACL,OAAOA,UAAU;EACnB;AACF"}