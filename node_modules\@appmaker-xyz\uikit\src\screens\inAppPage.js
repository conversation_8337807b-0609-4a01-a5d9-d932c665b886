// import React from 'react';
// import { View, StyleSheet } from 'react-native';
// import { spacing } from '../styles';
// import { AppmakerText, ProdcutScroller } from '../components';
// import InAppPage from 'contentSDK/src/components/InAppPage';
// import { useApConfigState } from '@appmaker/v3';
// const Typography = () => {
//   const config = useApConfigState();
//   const pageId = 'home';
//   const url = config.URL;

//   const source = {
//     url: url + '?rest_route=/appmaker-wc/v1/inAppPages/',
//     id: pageId, // 'dynamic/vendors'// pageId
//   };
//   const inappConfig = {
//     IN_APP_PAGE_BACKGROUND_COLOR: config.IN_APP_PAGE_BACKGROUND_COLOR,
//     // IN_APP_PAGE_BACKGROUND_COLOR: '#fff',
//     PRIMARY_COLOR: config.PRIMARY_COLOR,
//     SECONDARY_COLOR: config.SECONDARY_COLOR,
//     TERTIARY_COLOR: config.TERTIARY_COLOR,
//     TEXT_PRIMARY_COLOR: config.TEXT_PRIMARY_COLOR,
//     TEXT_SECONDARY_COLOR: config.TEXT_SECONDARY_COLOR,
//     TEXT_TERTIARY_COLOR: config.TEXT_TERTIARY_COLOR,
//     // PERFORMANCE_IN_APP_PAGE: config.PERFORMANCE_IN_APP_PAGE,
//   };
//   const apiKey = config.TOKEN.api_key;

//   let query = { api_key: apiKey };
//   const headers = {
//     'X-appmaker-version': config.APP_VERSION,
//     // 'X-appmaker-user-id': this.props.user.user.id,
//     // 'X-appmaker-access-token': this.props.user.accessToken,
//   };

//   const customViews = { product_scroller: ProdcutScroller };
//   return (
//     <View style={{ flex: 1 }}>
//       <InAppPage
//         source={source}
//         options={{ query, headers }}
//         config={inappConfig}
//         // onTitleReceived={this.onTitleRecieved.bind(this)}
//         // onClick={this.onClickfunc.bind(this)}
//         customViews={customViews}
//       />
//     </View>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     padding: spacing.base,
//   },
// });

// export default Typography;
