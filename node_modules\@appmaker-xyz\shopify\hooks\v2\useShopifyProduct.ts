import { handleAction } from '@appmaker-xyz/react-native';
import { currencyHelper, stripHtmlTags } from '../../helper';
import { ImageEdge, Product } from '../../datasource/api-react-query';
import { getExtensionConfig, analytics } from '@appmaker-xyz/core';
import { NavigationAction } from '../../navigation';
import {
    singleProductParams,
    singleProductContext,
    trackProductViewedWithoutData
} from '../../helper/analytics';

type ShopifyPriceConfig = {
    regularPrice: number,
    regularPriceWithCurrency: string,
    salePrice: number,
    salePriceWithCurrency: string,
    isOnSale: boolean,
    salePercentage: string | null,
    finalSalePrice: number,
    finalRegularPrice: number,
    firstAvailableVariant: any
};

function getPriceConfigFromProduct(product: Product): ShopifyPriceConfig {
    let regularPrice = parseFloat(
        product.compareAtPriceRange.minVariantPrice.amount,
    );
    const currencyCode = product.priceRange.minVariantPrice.currencyCode;
    const salePrice = parseFloat(product.priceRange.minVariantPrice.amount);
    const compareAtPriceRange = parseFloat(
        product?.compareAtPriceRange?.minVariantPrice?.amount,
    );
    const priceRange = parseFloat(
        product?.priceRange?.minVariantPrice?.amount,
    );
    let finalSalePrice = salePrice;
    let finalRegularPrice = regularPrice;
    let isOnSale = compareAtPriceRange > priceRange;
    let salePercentage = isOnSale
        ? `${Math.round(
            (100 * (compareAtPriceRange - priceRange)) / compareAtPriceRange,
        )} %`
        : null;
    const plpPricingStrategyV2 = getExtensionConfig(
        'shopify',
        'first_available_product_price'
    ) as string;
    let firstAvailableVariant = null;

    if (product.variants?.edges?.length > 0) {
        firstAvailableVariant = product?.variants.edges.find(
            (variant) => variant?.node?.availableForSale === true,
        );
    }
    if (
        plpPricingStrategyV2 === 'first_available_product_price' &&
        firstAvailableVariant?.node
    ) {
        let tempSalePrice = firstAvailableVariant?.node?.price;
        let tempRegularPrice = firstAvailableVariant?.node?.compareAtPrice;
        if (tempSalePrice) {
            finalSalePrice = currencyHelper(
                firstAvailableVariant.node.price.amount,
                firstAvailableVariant.node.price.currencyCode,
            );
        }
        if (tempRegularPrice) {
            finalRegularPrice = currencyHelper(
                firstAvailableVariant?.node?.compareAtPrice?.amount,
                firstAvailableVariant?.node?.compareAtPrice?.currencyCode,
            );
        }
        if (tempRegularPrice?.amount > tempSalePrice?.amount) {
            isOnSale = true;
            salePercentage = `${Math.round(
                (100 * (tempRegularPrice?.amount - tempSalePrice.amount)) /
                tempRegularPrice?.amount,
            )} %`;
        }
        if (finalSalePrice === finalRegularPrice) {
            finalRegularPrice = 0;
        }
    }

    let regularPriceWithCurrency = '';
    let salePriceWithCurrency = currencyHelper(
        salePrice,
        currencyCode
    );
    if (regularPrice > salePrice) {
        regularPriceWithCurrency = currencyHelper(
            regularPrice,
            currencyCode
        );
    }

    return {
        regularPrice,
        regularPriceWithCurrency,
        salePrice,
        salePriceWithCurrency,
        isOnSale,
        salePercentage,
        finalSalePrice,
        finalRegularPrice,
        firstAvailableVariant
    }
}
type AppmakerCommonProduct = {
    open_product_by_id: boolean,
}
type ShopifyProductState = {
    id: string,
    title: string,
    featureImageUrl: string | null,
    product: Product
    images: Array<ImageEdge>,
}
type ShopifyProductActions = {
    openProduct: () => void,
}
export function useShopifyProductV2({
    product,
}: {
    product: Product & AppmakerCommonProduct
}): ShopifyPriceConfig & ShopifyProductState & ShopifyProductActions {
    const firstImage = product?.images?.edges[0]?.node;
    const featureImageUrl = firstImage?.url;
    const priceConfig = getPriceConfigFromProduct(product);
    async function openProduct() {
        const initialState = !product?.open_product_by_id && {
            node: product
        };
        handleAction(
            NavigationAction.openProduct({
                productId: product?.id,
                handle: product?.handle,
                initialState,
            })
        )
        if (!product?.open_product_by_id){
            analytics.track(
                'product_viewed',
                singleProductParams(product, product?.variants?.edges[0]),
                singleProductContext(product, null),
            );
        } else {
            trackProductViewedWithoutData(product?.id, product?.handle);
        }
    }
    return {
        ...priceConfig,
        id: product?.id,
        title: product?.title,
        featureImageUrl,
        images: product?.images?.edges,
        openProduct,
        product
    };
}