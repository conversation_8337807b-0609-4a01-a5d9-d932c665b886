import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { emitEvent, analytics, testProps } from '@appmaker-xyz/core';
import { AppImage } from '@appmaker-xyz/uikit';

/**
 * Simple Banner view wrapping an Image and an optional inline title.
 * @param { image, title, height, width } props.
 */
function getImageSize(attributes) {
  const { imageContainerStyle, thumbnail_meta } = attributes;
  if (thumbnail_meta) {
    return thumbnail_meta;
  } else if (imageContainerStyle) {
    return imageContainerStyle;
  } else {
    return {};
  }
}
const ScrollerImage = ({ attributes, onPress, onAction, clientId }) => {
  const {
    image,
    appmakerAction,
    PARENT_HEIGHT,
    ENABLE_GAP,
    __appmaker_analytics_parent_id,
    __appmaker_analytics_parent_name,
  } = attributes;

  const { width, height } = getImageSize(attributes);
  const imageRatio = width / height;

  const imageUrl = typeof image === 'object' ? image.url : image;
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      emitEvent('bannerClick', {
        action: appmakerAction,
        bannerName: appmakerAction?.title,
        item_id: clientId,
      });
      analytics.track(
        'appmaker_block_click',
        {
          blockType: 'appmaker_banner',
          blockId: clientId,
          blockLabel: attributes?.__appmaker_analytics?.blockLabel,
          ...(image.id && {
            resourceId: attributes?.__appmaker_analytics?.resourceId,
          }),
          ...(__appmaker_analytics_parent_id && {
            parentId: __appmaker_analytics_parent_id,
          }),
          ...(__appmaker_analytics_parent_name && {
            parentName: __appmaker_analytics_parent_name,
          }),
          resourceUrl: imageUrl,
          resourceName: attributes?.__appmaker_analytics?.resourceName,
        },
        {
          blockAttributes: attributes,
          blockClientId: clientId,
          blockAction: appmakerAction,
          __appmaker_analytics_parent_id,
          __appmaker_analytics_parent_name,
          resourceName: attributes?.__appmaker_analytics?.resourceName,
        },
      );
      onAction(appmakerAction);
    };
  }
  return (
    <TouchableOpacity
      {...testProps(`appmaker_imagescroller-item"-${clientId}`)}
      onPress={onPressHandle}
      style={{
        height: '100%',
        width: PARENT_HEIGHT * imageRatio,
        marginLeft: ENABLE_GAP ? 8 : 0,
      }}
      activeOpacity={0.98}>
      <AppImage uri={imageUrl} style={styles.image} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  image: {
    height: '100%',
    width: '100%',
  },
});

export default ScrollerImage;
