{"version": 3, "names": ["_helperSplitExportDeclaration", "require", "t", "_helperEnvironmentVisitor", "_traverseNode", "_visitors", "renameVisitor", "ReferencedIdentifier", "node", "state", "name", "old<PERSON>ame", "newName", "<PERSON><PERSON>", "path", "scope", "bindingIdentifierEquals", "binding", "identifier", "skip", "isMethod", "requeueComputedKeyAndDecorators", "AssignmentExpression|Declaration|VariableDeclarator", "isVariableDeclaration", "ids", "getOuterBindingIdentifiers", "Renamer", "constructor", "maybeConvertFromExportDeclaration", "parentDeclar", "maybeExportDeclar", "parentPath", "isExportDeclaration", "isExportDefaultDeclaration", "declaration", "isDeclaration", "id", "isExportAllDeclaration", "splitExportDeclaration", "maybeConvertFromClassFunctionDeclaration", "maybeConvertFromClassFunctionExpression", "rename", "find", "isFunctionExpression", "isClassExpression", "bindingIds", "blockToTraverse", "arguments", "block", "traverseNode", "explode", "discriminant", "removeOwnBinding", "bindings", "exports", "default"], "sources": ["../../../src/scope/lib/renamer.ts"], "sourcesContent": ["import type Binding from \"../binding\";\nimport splitExportDeclaration from \"@babel/helper-split-export-declaration\";\nimport * as t from \"@babel/types\";\nimport type { NodePath, Visitor } from \"../..\";\nimport { requeueComputedKeyAndDecorators } from \"@babel/helper-environment-visitor\";\nimport { traverseNode } from \"../../traverse-node\";\nimport { explode } from \"../../visitors\";\n\nconst renameVisitor: Visitor<Renamer> = {\n  ReferencedIdentifier({ node }, state) {\n    if (node.name === state.oldName) {\n      node.name = state.newName;\n    }\n  },\n\n  Scope(path, state) {\n    if (\n      !path.scope.bindingIdentifierEquals(\n        state.oldName,\n        state.binding.identifier,\n      )\n    ) {\n      path.skip();\n      if (path.isMethod()) {\n        requeueComputedKeyAndDecorators(path);\n      }\n    }\n  },\n\n  \"AssignmentExpression|Declaration|VariableDeclarator\"(\n    path: NodePath<t.AssignmentPattern | t.Declaration | t.VariableDeclarator>,\n    state,\n  ) {\n    if (path.isVariableDeclaration()) return;\n    const ids = path.getOuterBindingIdentifiers();\n\n    for (const name in ids) {\n      if (name === state.oldName) ids[name].name = state.newName;\n    }\n  },\n};\n\nexport default class Renamer {\n  constructor(binding: Binding, oldName: string, newName: string) {\n    this.newName = newName;\n    this.oldName = oldName;\n    this.binding = binding;\n  }\n\n  declare oldName: string;\n  declare newName: string;\n  declare binding: Binding;\n\n  maybeConvertFromExportDeclaration(parentDeclar: NodePath) {\n    const maybeExportDeclar = parentDeclar.parentPath;\n\n    if (!maybeExportDeclar.isExportDeclaration()) {\n      return;\n    }\n\n    if (maybeExportDeclar.isExportDefaultDeclaration()) {\n      const { declaration } = maybeExportDeclar.node;\n      if (t.isDeclaration(declaration) && !declaration.id) {\n        return;\n      }\n    }\n\n    if (maybeExportDeclar.isExportAllDeclaration()) {\n      return;\n    }\n\n    splitExportDeclaration(\n      maybeExportDeclar as NodePath<\n        Exclude<t.ExportDeclaration, t.ExportAllDeclaration>\n      >,\n    );\n  }\n\n  maybeConvertFromClassFunctionDeclaration(path: NodePath) {\n    return path; // TODO\n\n    // // retain the `name` of a class/function declaration\n\n    // if (!path.isFunctionDeclaration() && !path.isClassDeclaration()) return;\n    // if (this.binding.kind !== \"hoisted\") return;\n\n    // path.node.id = identifier(this.oldName);\n    // path.node._blockHoist = 3;\n\n    // path.replaceWith(\n    //   variableDeclaration(\"let\", [\n    //     variableDeclarator(identifier(this.newName), toExpression(path.node)),\n    //   ]),\n    // );\n  }\n\n  maybeConvertFromClassFunctionExpression(path: NodePath) {\n    return path; // TODO\n\n    // // retain the `name` of a class/function expression\n\n    // if (!path.isFunctionExpression() && !path.isClassExpression()) return;\n    // if (this.binding.kind !== \"local\") return;\n\n    // path.node.id = identifier(this.oldName);\n\n    // this.binding.scope.parent.push({\n    //   id: identifier(this.newName),\n    // });\n\n    // path.replaceWith(\n    //   assignmentExpression(\"=\", identifier(this.newName), path.node),\n    // );\n  }\n\n  rename(/* Babel 7 - block?: t.Pattern | t.Scopable */) {\n    const { binding, oldName, newName } = this;\n    const { scope, path } = binding;\n\n    const parentDeclar = path.find(\n      path =>\n        path.isDeclaration() ||\n        path.isFunctionExpression() ||\n        path.isClassExpression(),\n    );\n    if (parentDeclar) {\n      const bindingIds = parentDeclar.getOuterBindingIdentifiers();\n      if (bindingIds[oldName] === binding.identifier) {\n        // When we are renaming an exported identifier, we need to ensure that\n        // the exported binding keeps the old name.\n        this.maybeConvertFromExportDeclaration(parentDeclar);\n      }\n    }\n\n    const blockToTraverse = process.env.BABEL_8_BREAKING\n      ? scope.block\n      : (arguments[0] as t.Pattern | t.Scopable) || scope.block;\n    traverseNode(\n      blockToTraverse,\n      explode(renameVisitor),\n      scope,\n      this,\n      scope.path,\n      // When blockToTraverse is a SwitchStatement, the discriminant\n      // is not part of the current scope and thus should be skipped.\n      { discriminant: true },\n    );\n\n    if (process.env.BABEL_8_BREAKING) {\n      scope.removeOwnBinding(oldName);\n      scope.bindings[newName] = binding;\n      this.binding.identifier.name = newName;\n    } else if (!arguments[0]) {\n      scope.removeOwnBinding(oldName);\n      scope.bindings[newName] = binding;\n      this.binding.identifier.name = newName;\n    }\n\n    if (parentDeclar) {\n      this.maybeConvertFromClassFunctionDeclaration(path);\n      this.maybeConvertFromClassFunctionExpression(path);\n    }\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,6BAAA,GAAAC,OAAA;AACA,IAAAC,CAAA,GAAAD,OAAA;AAEA,IAAAE,yBAAA,GAAAF,OAAA;AACA,IAAAG,aAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AAEA,MAAMK,aAA+B,GAAG;EACtCC,oBAAoBA,CAAC;IAAEC;EAAK,CAAC,EAAEC,KAAK,EAAE;IACpC,IAAID,IAAI,CAACE,IAAI,KAAKD,KAAK,CAACE,OAAO,EAAE;MAC/BH,IAAI,CAACE,IAAI,GAAGD,KAAK,CAACG,OAAO;IAC3B;EACF,CAAC;EAEDC,KAAKA,CAACC,IAAI,EAAEL,KAAK,EAAE;IACjB,IACE,CAACK,IAAI,CAACC,KAAK,CAACC,uBAAuB,CACjCP,KAAK,CAACE,OAAO,EACbF,KAAK,CAACQ,OAAO,CAACC,UAAU,CACzB,EACD;MACAJ,IAAI,CAACK,IAAI,EAAE;MACX,IAAIL,IAAI,CAACM,QAAQ,EAAE,EAAE;QACnB,IAAAC,yDAA+B,EAACP,IAAI,CAAC;MACvC;IACF;EACF,CAAC;EAED,qDAAqDQ,CACnDR,IAA0E,EAC1EL,KAAK,EACL;IACA,IAAIK,IAAI,CAACS,qBAAqB,EAAE,EAAE;IAClC,MAAMC,GAAG,GAAGV,IAAI,CAACW,0BAA0B,EAAE;IAE7C,KAAK,MAAMf,IAAI,IAAIc,GAAG,EAAE;MACtB,IAAId,IAAI,KAAKD,KAAK,CAACE,OAAO,EAAEa,GAAG,CAACd,IAAI,CAAC,CAACA,IAAI,GAAGD,KAAK,CAACG,OAAO;IAC5D;EACF;AACF,CAAC;AAEc,MAAMc,OAAO,CAAC;EAC3BC,WAAWA,CAACV,OAAgB,EAAEN,OAAe,EAAEC,OAAe,EAAE;IAC9D,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACM,OAAO,GAAGA,OAAO;EACxB;EAMAW,iCAAiCA,CAACC,YAAsB,EAAE;IACxD,MAAMC,iBAAiB,GAAGD,YAAY,CAACE,UAAU;IAEjD,IAAI,CAACD,iBAAiB,CAACE,mBAAmB,EAAE,EAAE;MAC5C;IACF;IAEA,IAAIF,iBAAiB,CAACG,0BAA0B,EAAE,EAAE;MAClD,MAAM;QAAEC;MAAY,CAAC,GAAGJ,iBAAiB,CAACtB,IAAI;MAC9C,IAAIN,CAAC,CAACiC,aAAa,CAACD,WAAW,CAAC,IAAI,CAACA,WAAW,CAACE,EAAE,EAAE;QACnD;MACF;IACF;IAEA,IAAIN,iBAAiB,CAACO,sBAAsB,EAAE,EAAE;MAC9C;IACF;IAEA,IAAAC,qCAAsB,EACpBR,iBAAiB,CAGlB;EACH;EAEAS,wCAAwCA,CAACzB,IAAc,EAAE;IACvD,OAAOA,IAAI;EAeb;EAEA0B,uCAAuCA,CAAC1B,IAAc,EAAE;IACtD,OAAOA,IAAI;EAgBb;EAEA2B,MAAMA,CAAA,EAAiD;IACrD,MAAM;MAAExB,OAAO;MAAEN,OAAO;MAAEC;IAAQ,CAAC,GAAG,IAAI;IAC1C,MAAM;MAAEG,KAAK;MAAED;IAAK,CAAC,GAAGG,OAAO;IAE/B,MAAMY,YAAY,GAAGf,IAAI,CAAC4B,IAAI,CAC5B5B,IAAI,IACFA,IAAI,CAACqB,aAAa,EAAE,IACpBrB,IAAI,CAAC6B,oBAAoB,EAAE,IAC3B7B,IAAI,CAAC8B,iBAAiB,EAAE,CAC3B;IACD,IAAIf,YAAY,EAAE;MAChB,MAAMgB,UAAU,GAAGhB,YAAY,CAACJ,0BAA0B,EAAE;MAC5D,IAAIoB,UAAU,CAAClC,OAAO,CAAC,KAAKM,OAAO,CAACC,UAAU,EAAE;QAG9C,IAAI,CAACU,iCAAiC,CAACC,YAAY,CAAC;MACtD;IACF;IAEA,MAAMiB,eAAe,GAEhBC,SAAS,CAAC,CAAC,CAAC,IAA+BhC,KAAK,CAACiC,KAAK;IAC3D,IAAAC,0BAAY,EACVH,eAAe,EACf,IAAAI,iBAAO,EAAC5C,aAAa,CAAC,EACtBS,KAAK,EACL,IAAI,EACJA,KAAK,CAACD,IAAI,EAGV;MAAEqC,YAAY,EAAE;IAAK,CAAC,CACvB;IAMM,IAAI,CAACJ,SAAS,CAAC,CAAC,CAAC,EAAE;MACxBhC,KAAK,CAACqC,gBAAgB,CAACzC,OAAO,CAAC;MAC/BI,KAAK,CAACsC,QAAQ,CAACzC,OAAO,CAAC,GAAGK,OAAO;MACjC,IAAI,CAACA,OAAO,CAACC,UAAU,CAACR,IAAI,GAAGE,OAAO;IACxC;IAEA,IAAIiB,YAAY,EAAE;MAChB,IAAI,CAACU,wCAAwC,CAACzB,IAAI,CAAC;MACnD,IAAI,CAAC0B,uCAAuC,CAAC1B,IAAI,CAAC;IACpD;EACF;AACF;AAACwC,OAAA,CAAAC,OAAA,GAAA7B,OAAA"}