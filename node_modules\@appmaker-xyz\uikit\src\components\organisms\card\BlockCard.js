import React, { useState } from 'react';
import { StyleSheet, LayoutAnimation, Platform, UIManager } from 'react-native';
import { AppmakerText, Button, Layout } from '../..';
import { useApThemeState } from '../../../theme/ThemeContext';
import { testProps } from '@appmaker-xyz/core';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { AppTouchable } from '../../index';
const BlockCard = ({ clientId, attributes, children, onPress, onAction }) => {
  const {
    title,
    showTitle,
    accessButton,
    expandable = false,
    expanded = true,
    hiddenText = <Icon name="plus" size={18} />,
    expandedText = <Icon name="plus" size={18} />,
    childContainerStyle,
    appmakerAction,
    accessButtonColor,
    accessButtonStyle,
    wholeContainerStyle,
    __display = true,
  } = attributes;
  const [visibility, setVisibility] = useState(expanded);
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });

  if (Platform.OS === 'android') {
    if (UIManager.setLayoutAnimationEnabledExperimental) {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }
  }
  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setVisibility(!visibility);
  };
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  if (__display === false || __display === 'false') {
    return null;
  }

  return (
    <AppTouchable
      onPress={() => (expandable ? toggleExpand() : null)}
      disableTouch={!expandable}>
      <Layout
        style={[
          title || accessButton ? styles.container : null,
          wholeContainerStyle,
        ]}
        {...testProps(clientId)}>
        {(showTitle !== false && title) || accessButton ? (
          <Layout style={styles.titleContainer}>
            <AppmakerText category="actionTitle">{title}</AppmakerText>
            {accessButton !== '' && (
              <Button
                onPress={() => {
                  onPressHandle && onPressHandle();
                  expandable && toggleExpand();
                }}
                accessoryLeft={() =>
                  expandedText == '' && hiddenText == '' ? (
                    <Icon name={visibility ? 'minus' : 'plus'} size={18} />
                  ) : null
                }
                small
                wholeContainerStyle={accessButtonStyle}
                status="white">
                <AppmakerText
                  category={'smallButtonText'}
                  status="primary"
                  fontColor={accessButtonColor}>
                  {expandable
                    ? visibility
                      ? expandedText
                      : hiddenText
                    : accessButton}
                </AppmakerText>
              </Button>
            )}
          </Layout>
        ) : null}
        {visibility && <Layout style={childContainerStyle}>{children}</Layout>}
      </Layout>
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      marginBottom: spacing.nano,
      paddingTop: spacing.small,
    },
    titleContainer: {
      paddingHorizontal: spacing.base,
      paddingBottom: spacing.small,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    noPadding: {
      paddingHorizontal: 0,
    },
  });

export default BlockCard;
