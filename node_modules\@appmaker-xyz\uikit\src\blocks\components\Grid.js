import BlockCard from '@appmaker-xyz/uikit/src/components/organisms/card/BlockCard';
import React, { useEffect, useState } from 'react';
import { Text } from 'react-native';
// const { width } = Dimensions.get('window');

const Grid = ({
  attributes,
  BlockItem,
  BlocksView,
  innerBlocks,
  onPress,
  onAction,
  clientId,
}) => {
  // console.log('attributes');
  // console.log(BlocksView);
  // return null;
  // console.log({ attributes: attributes.itemsPerLine });
  const finalInnerBlocks = innerBlocks.map((block) => ({
    ...block,
    attributes: {
      numColumns: attributes.itemsPerLine,
      enableGap: attributes.enableGap,
      __appmaker_analytics_parent_id: clientId,
      __appmaker_analytics_parent_name:
        attributes?.__appmaker_analytics?.blockLabel,
      ...block.attributes,
    },
  }));
  return (
    <BlockCard
      attributes={{
        ...attributes,
        title: attributes.showTitle ? attributes.title : '',
        accessButton: attributes.showViewMoreButton ? attributes.ctaText : '',
      }}
      onAction={onAction}
      clientId={clientId}>
      <BlocksView
        onAction={onAction}
        inAppPage={{
          blocks: finalInnerBlocks,
          attributes: {
            horizontal: false,
            numColumns: attributes.itemsPerLine,
          },
        }}
      />
    </BlockCard>
  );
  // return <Text>{JSON.stringify(innerBlocks, null, 2)}</Text>;
};

export default Grid;
