import React from 'react';
import { FlatList, ScrollView, StyleSheet } from 'react-native';
import { Layout, ThemeText, AppTouchable } from '@appmaker-xyz/ui';
import { Recent, Trending } from '../../../assets/svg';

const Item = ({ item, trendingSearches, color }) => {
  const Icon = trendingSearches ? Trending : Recent;
  return (
    <AppTouchable style={[styles.item, { borderColor: color }]}>
      <Icon height={16} style={styles.icon} />
      <ThemeText color={color}>{item.title}</ThemeText>
    </AppTouchable>
  );
};

const MinimalSuggestions = ({ attributes = {} }) => {
  const {
    title,
    color = '#898989',
    trendingSearches,
    recentSearches,
  } = attributes;
  const DATA = recentSearches || trendingSearches || [];
  const finalData = typeof DATA === 'object' ? Object.values(DATA) : DATA;
  return (
    <Layout style={styles.container}>
      <ThemeText size="md" style={styles.title}>
        {title}
      </ThemeText>
      <ScrollView
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        directionalLockEnabled={true}
        alwaysBounceVertical={false}
        contentContainerStyle={styles.contentContainerStyle}>
        <FlatList
          data={finalData}
          renderItem={({ item }) => (
            <Item
              item={item}
              trendingSearches={trendingSearches}
              recentSearches={recentSearches}
              color={color}
            />
          )}
          keyExtractor={(item) => item.title}
          showsHorizontalScrollIndicator={false}
          numColumns={Math.ceil(finalData.length / 2)}
        />
      </ScrollView>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  title: {
    marginBottom: 8,
    marginLeft: 12,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderWidth: 1,

    borderRadius: 16,
    marginHorizontal: 2,
    marginVertical: 2,
  },
  icon: {
    marginRight: 4,
  },
  contentContainerStyle: {
    paddingHorizontal: 8,
  },
});

export default MinimalSuggestions;
