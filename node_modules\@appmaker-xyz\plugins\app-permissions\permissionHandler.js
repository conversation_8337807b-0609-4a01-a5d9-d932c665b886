import { requestMultiple, PERMISSIONS } from 'react-native-permissions';

import { onEvent, applyFilters } from '@appmaker-xyz/core';
import { Platform } from 'react-native';
// import {  } from 'packages/appmaker-core/build-output/index';
// has string in array
const hasStringInArray = (string, array) => {
  console.log('hasStringInArraypermissions', string, array);
  return array.indexOf(string) > -1;
};

function requestForPermission(permissions) {
  const platformPermissions = [];
  const debugPm = [];
  if (hasStringInArray('location', permissions)) {
    if (Platform.OS === 'ios') {
      platformPermissions.push(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
    } else {
      platformPermissions.push(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
    }
  }

  if (hasStringInArray('camera', permissions)) {
    if (Platform.OS === 'ios') {
      platformPermissions.push(PERMISSIONS.IOS.CAMERA);
    } else {
      platformPermissions.push(PERMISSIONS.ANDROID.CAMERA);
    }
  }
  if (hasStringInArray('microphone', permissions)) {
    if (Platform.OS === 'ios') {
      platformPermissions.push(PERMISSIONS.IOS.MICROPHONE);
    } else {
      platformPermissions.push(PERMISSIONS.ANDROID.RECORD_AUDIO);
    }
  }

  requestMultiple(platformPermissions)
    .then((statuses) => {
      console.log('permissions', statuses);
    })
    .catch((err) => {
      console.log('permissions->error', err);
    });
}
export function activateHandler(settings) {
  onEvent('ensure-permissions', async function (permissionsRequired, params) {
    // console.log('permissions', statuses);
    const finalPermssionsList = applyFilters(
      'ensure-app-permissions-list',
      permissionsRequired,
      params,
    );
    console.log(
      'finalPermssionsListensure-permissions',
      finalPermssionsList,
      params,
    );
    if (finalPermssionsList.length === 0) {
      return;
    } else {
      requestForPermission(finalPermssionsList);
    }

    // const
  });
}
