import React, { useEffect, useState } from 'react';
import { StyleSheet, ScrollView, SafeAreaView, View, Text } from 'react-native';
import * as productData from './product';
import ServiceAPI from '../../../../../src/config/rest';
import Config from 'Config';
import {
  ActionButtons,
  ProductData,
  ProductImage,
  ProductInfo,
} from '../../components/pages/productDetail/components';
import { Layout } from '@appmaker-xyz/uikit';
import { getDetailBlocks } from './data';
import { BlocksView } from '@appmaker-xyz/react-native';

import { addFilter } from '@appmaker-xyz/core';
ServiceAPI.init(Config.URL, Config.TOKEN);
import { color, spacing } from '../../styles';
function handleAction(params) {
  console.log(
    `action not implemeted talk to Saleeh  ${JSON.stringify(params, null, 2)}`,
  );
}
// addFilter('appmaker-blocks', 'product-image', (blocks) => {
//   blocks['appmaker/product-image'] = {
//     View: ProductImage,
//   };

//   blocks['appmaker/product-data'] = {
//     View: ProductData,
//   };
//   blocks['appmaker/buttons'] = {
//     View: ActionButtons,
//   };
//   // blocks['appmaker/woocommerce-prodict-detail-listener'] = {
//   //   View: ({ attributes, pageDispatch, pageState, data }) => {
//   //     // console.log(pageState);
//   //     // console.log(data);
//   //     // if(pageState?)
//   //     useEffect(() => {
//   //       if (pageState?.state?.filters) {
//   //         const variationSelect = new VariationSelect(data);
//   //         const attribute = 'pa_flavour',
//   //           attribute_value = pageState?.state?.filters[attribute];
//   //         const imageIndex =
//   //           variationSelect.getVariationImageId(attribute, attribute_value) + 1;
//   //         const product = variationSelect.getVariation(
//   //           attribute,
//   //           attribute_value,
//   //         );
//   //         console.log(product.id);
//   //         pageDispatch({
//   //           type: 'set_values',
//   //           values: { product, imageIndex },
//   //         });
//   //       }
//   //     }, [pageState?.state?.filters]);
//   //     return null;
//   //   },
//   // };
//   return blocks;
// });

// addFilter('appmaker-actions', 'default-actions', (actions) => {
//   actions.ADD_TO_CART = async (action) => {
//     console.log('adding');
//     console.log(action.params);
//     const finalParams = { ...action.params, }
//     try {
//       const resp = await ServiceAPI.api.addToCart(finalParams);
//       // console.log(resp);
//     } catch (error) {
//       console.log(error);
//     }
//     console.log('added');
//     return finalParams;;
//   };
//   console.log(actions);
//   return actions;
// });

const BlocksListScreen = (props) => {
  // console.log(getCurrent);
  // const dataSource = {attributes: productData.hxp};
  // const templateLoading = false;

  const blogPage = {
    blocks: [
      {
        isValid: true,
        clientId: '2b8b0580-1339-434a-bb79-9c42f3e7a4e4',
        name: 'appmaker/block-card',
        attributes: {
          title: 'Latest',
          accessButton: 'View All',
        },
        innerBlocks: [
          {
            name: 'appmaker/card-item',
            attributes: {
              horizontal: true,
              type: 'type-3',
              reverse: true,
              dataSourceMap: '{ "items": "posts" }',
              page_name: 'Tn5KFjwJyoP7SVFU1aSy',
              actionValueSource: 'url',
              appmakerAction: {
                OPEN_URL_url: 'url',
                action: 'OPEN_IN_WEBVIEW',
                params: { value: 'https://blog.upekkha.io/' },
                OPEN_IN_WEBVIEW_url: 'https://blog.upekkha.io/',
              },
              dataSourceURL:
                'https://blog.upekkha.io/ghost/api/v3/content/posts/?key=998da5a578c4a971c63b527371',
              mapValues: {
                title: 'title',
                imgUri: 'feature_image',
                featureImg: 'feature_image',
                expert: 'excerpt',
                // timeStamp: 'created_at',
              },
            },
          },
        ],
      },
      {
        isValid: true,
        clientId: '2b8b0580-1339-434a-bb79-9c42f3e7a4e4',
        name: 'appmaker/block-card',
        attributes: {
          title: 'Title',
          accessButton: 'View All',
        },
        innerBlocks: [
          {
            name: 'appmaker/card-item',
            attributes: {
              horizontal: true,
              type: 'type-1',
              reverse: true,
              dataSourceMap: '{ "items": "posts" }',
              page_name: 'Tn5KFjwJyoP7SVFU1aSy',
              actionValueSource: 'url',
              appmakerAction: {
                OPEN_URL_url: 'url',
                action: 'OPEN_IN_WEBVIEW',
                params: { value: 'https://blog.upekkha.io/' },
                OPEN_IN_WEBVIEW_url: 'https://blog.upekkha.io/',
              },
              dataSourceURL:
                'https://blog.upekkha.io/ghost/api/v3/content/posts/?key=998da5a578c4a971c63b527371',
              mapValues: {
                title: 'title',
                imgUri: 'feature_image',
                featureImg: 'feature_image',
                expert: 'excerpt',
                // timeStamp: 'created_at',
              },
            },
          },
        ],
      },
      {
        isValid: true,
        clientId: '2b8b0580-1339-434a-bb79-9c42f3e7a4e4',
        name: 'appmaker/block-card',
        attributes: {
          title: 'Title',
          accessButton: 'View All',
        },
        innerBlocks: [
          {
            name: 'appmaker/card-item',
            attributes: {
              horizontal: true,
              type: 'type-2',
              reverse: true,
              dataSourceMap: '{ "items": "posts" }',
              page_name: 'Tn5KFjwJyoP7SVFU1aSy',
              actionValueSource: 'url',
              appmakerAction: {
                OPEN_URL_url: 'url',
                action: 'OPEN_IN_WEBVIEW',
                params: { value: 'https://blog.upekkha.io/' },
                OPEN_IN_WEBVIEW_url: 'https://blog.upekkha.io/',
              },
              dataSourceURL:
                'https://blog.upekkha.io/ghost/api/v3/content/posts/?key=998da5a578c4a971c63b527371',
              mapValues: {
                title: 'title',
                imgUri: 'feature_image',
                featureImg: 'feature_image',
                expert: 'excerpt',
                // timeStamp: 'created_at',
              },
            },
          },
        ],
      },
      {
        isValid: true,
        clientId: '2b8b0580-1339-434a-bb79-9c42f3e7a4e4',
        name: 'appmaker/block-card',
        attributes: {
          title: 'Title',
          accessButton: 'View All',
        },
        innerBlocks: [
          {
            name: 'appmaker/card-item',
            attributes: {
              horizontal: true,
              type: 'type-3',
              reverse: true,
              dataSourceMap: '{ "items": "posts" }',
              page_name: 'Tn5KFjwJyoP7SVFU1aSy',
              actionValueSource: 'url',
              appmakerAction: {
                OPEN_URL_url: 'url',
                action: 'OPEN_IN_WEBVIEW',
                params: { value: 'https://blog.upekkha.io/' },
                OPEN_IN_WEBVIEW_url: 'https://blog.upekkha.io/',
              },
              dataSourceURL:
                'https://blog.upekkha.io/ghost/api/v3/content/posts/?key=998da5a578c4a971c63b527371',
              mapValues: {
                title: 'title',
                imgUri: 'feature_image',
                featureImg: 'feature_image',
                expert: 'excerpt',
                // timeStamp: 'created_at',
              },
            },
          },
        ],
      },
    ],
    title: 'Blog',
    parentID: 'Tn5KFjwJyoP7SVFU1aSy',
    attributes: false,
    id: 'Tn5KFjwJyoP7SVFU1aSy',
    status: 'active',
    version: 'v1.1',
    language: 'default',
    type: 'NORMAL',
  };
  const [productSingeData, setProductSingeData] = useState(productData.hxp);
  const [pageTemplate, setPageTemplate] = useState({});

  const [templateLoading, setTemplateLoading] = useState(false);
  const [refreshData, setrefresh] = useState(0);
  useEffect(() => {
    console.log('re');
    setPageTemplate(blogPage);
  }, []);
  return (
    <Layout
      loading={templateLoading}
      style={{ flex: 1, backgroundColor: color.light }}>
      {/* {console.log('sss')} */}
      {!templateLoading && (
        <BlocksView
          template={pageTemplate?.blocks}
          fixedFooterBlock={pageTemplate?.fixedFooter}
          blockData={productSingeData}
          isRefreshing={templateLoading}
          onRefresh={() => {
            refetch();
          }}
          onAction={(appmakerAction) => {
            // console.log(JSON.stringify(appmakerAction, null, 2));
            handleAction(appmakerAction);
          }}
        />
      )}
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.light,
    },
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: spacing.base,
    },
  });

// export default ProductDetail;
export default BlocksListScreen;
