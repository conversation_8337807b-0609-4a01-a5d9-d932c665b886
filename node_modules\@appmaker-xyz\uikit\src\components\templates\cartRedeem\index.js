import React, { useState, useEffect } from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import { Layout, AppmakerText, Button, Input } from '../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/MaterialCommunityIcons';

const cartRedeem = ({ attributes, onAction, pageDispatch }) => {
  let { redeem_block, redeem_applied } = attributes;
  if (!redeem_block) {
    redeem_block = { message: 'loading' };
  }
  const [input, setInput] = useState(redeem_applied ? true : false);
  const [coupon, setCoupon] = useState(
    redeem_applied ? redeem_applied.coupon : '',
  );
  const [redeemBlock, setRedeemBlock] = useState(redeem_block);
  const [success, setSuccess] = useState(false);
  const [buttonLoading, setButtonLoading] = useState(false);
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing, input });

  useEffect(() => {
    setRedeemBlock(redeem_block);
  }, [redeem_block]);

  useEffect(() => {
    if (redeem_applied) {
      setCoupon(redeem_applied.coupon);
      setSuccess(true);
    } // used because there is a slight delay in loading couponDiscounted from props. ie, couponDiscounted is undefined at first
    else {
      redeem_applied = [];
    }
  }, [redeem_applied]);

  const applyCoupon = async (value) => {
    const action = {
      action: 'APPLY_REDEEM',
      params: {
        redeem: value,
      },
    };
    setButtonLoading(true);
    try {
      const couponResp = await onAction(action);
      redeem_applied = couponResp.redeem_applied;
      pageDispatch({
        type: 'set_value',
        name: 'cartResponse',
        value: couponResp,
      });
      setSuccess(true);
      setButtonLoading(false);
    } catch (e) {
      setButtonLoading(false);
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'Something went wrong, please try again',
        },
      });
    }
  };

  const removeCoupon = async (value) => {
    const action = {
      action: 'REMOVE_REDEEM',
      params: {
        coupon: value,
      },
    };
    setButtonLoading(true);
    try {
      const couponResp = await onAction(action);
      redeem_applied = couponResp.redeem_applied;
      pageDispatch({
        type: 'set_value',
        name: 'cartResponse',
        value: couponResp,
      });
      setSuccess(false);
      setButtonLoading(false);
    } catch (e) {
      setSuccess(false);
      setButtonLoading(true);
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'Something went wrong, please try again',
        },
      });
    }
  };
  if (redeem_applied) {
    return (
      <Layout backgroundColor={color.white}>
        <Layout style={styles.container}>
          <Layout flexDirection="row" alignItems="center">
            <Icon
              name="credit-card"
              size={20}
              style={[styles.icon, { backgroundColor: `${color.success}40` }]}
              color={color.success}
            />
            <Layout>
              <AppmakerText>
                <AppmakerText category="pageSubHeading">
                  {redeem_applied.discount_display.toUpperCase()}
                </AppmakerText>{' '}
                redeemed
              </AppmakerText>
              {/* <AppmakerText category="highlighter1" status="success">
                Coupon saving {redeem_applied.discount_display}
              </AppmakerText> */}
            </Layout>
            {/* <AppmakerText category="actionTitle">
              {`${couponDiscounted[0].coupon} (${couponDiscounted[0].discount_display}) applied`}
            </AppmakerText> */}
          </Layout>
          <Button
            small
            status="white"
            loading={buttonLoading}
            onPress={() => removeCoupon(redeem_applied.coupon)}>
            <AppmakerText category="smallButtonText" status="primary">
              Remove
            </AppmakerText>
          </Button>
        </Layout>
      </Layout>
    );
  }
  // applyCoupon(coupon) is rest rest
  return (
    <Layout backgroundColor={color.white}>
      {!input ? (
        <Layout style={styles.container}>
          <Layout
            style={{ flex: 1, marginRight: 10 }}
            flexDirection="row"
            alignItems="center">
            <Icon
              name="credit-card"
              size={20}
              style={styles.icon}
              color={color.primary}
            />
            <AppmakerText numberOfLines={2} category="smallButtonText">
              {redeem_block.message}
            </AppmakerText>
          </Layout>
          <Button
            buttonLoading={buttonLoading}
            small
            status="white"
            onPress={() => setInput(true)}>
            <AppmakerText category="smallButtonText" status="primary">
              Redeem Points
            </AppmakerText>
          </Button>
        </Layout>
      ) : (
        <Layout style={styles.containerTwo}>
          <Layout style={styles.inputContainer}>
            <Input
              label="Enter Coupon Code"
              leftIcon="gift"
              onChangeText={(text) => {
                setCoupon(text);
              }}
              status={success ? 'success' : 'grey'}
              captionIcon={success && 'check'}
              caption={success && 'Coupon applied'}
            />
          </Layout>
          <Layout marginTop={spacing.small}>
            <Button
              loading={buttonLoading}
              baseSize
              status="dark"
              onPress={() => applyCoupon(coupon)}>
              Apply
            </Button>
          </Layout>
        </Layout>
      )}
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      padding: spacing.base,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: color.white,
      justifyContent: 'space-between',
    },
    icon: {
      padding: spacing.small,
      backgroundColor: `${color.primary}40`,
      borderRadius: spacing.base * 2,
      marginRight: spacing.base,
      transform: [{ rotate: '-45deg' }],
    },
    inputContainer: {
      flex: 1,
      marginRight: spacing.base,
    },
    containerTwo: {
      flexDirection: 'row',
      paddingHorizontal: spacing.base,
      paddingTop: spacing.base,
    },
  });

export default cartRedeem;
