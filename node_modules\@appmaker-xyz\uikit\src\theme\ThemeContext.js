import React from 'react';
import { StatusBar } from 'react-native';
import { appSettings } from '@appmaker-xyz/core';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const ApThemeStateContext = React.createContext({});
const ApThemeDispatchContext = React.createContext();
const ApThemeReducer = (state, action) => {
  switch (action.type) {
    case 'set_config': {
      return { ...state, [action.name]: action.value };
    }
    default: {
      throw new Error(`Unhandled action type: ${action.type}`);
    }
  }
};
function ApThemeProvider({ children, styles }) {
  const [state, dispatch] = React.useReducer(ApThemeReducer, { ...styles });
  const statusBarColor =
    appSettings.getOption('statusBarColor') ||
    appSettings.getOption('toolbar_color');
  const RemoveSafeAreaView = appSettings.getExtensionOptionAsBoolean(
    'app-settings',
    'disable_safe_area_view',
    false,
  );
  const appContent = () => (
    <ApThemeStateContext.Provider value={state}>
      <ApThemeDispatchContext.Provider value={dispatch}>
        {children}
      </ApThemeDispatchContext.Provider>
    </ApThemeStateContext.Provider>
  );
  const insets = useSafeAreaInsets();
  return (
    <>
      <StatusBar
        backgroundColor={statusBarColor}
        barStyle={
          appSettings.getOption('is_status_bar_light')
            ? 'dark-content'
            : 'light-content'
        }
      />
      {appContent()}
    </>
  );
}
function useApThemeState() {
  const context = React.useContext(ApThemeStateContext);
  if (context === undefined) {
    throw new Error('useApThemeState must be used within a ApThemeProvider');
  }
  return context;
}
function useApThemeDispatch() {
  const context = React.useContext(ApThemeDispatchContext);
  if (context === undefined) {
    throw new Error('useApThemeDispatch must be used within a ApThemeProvider');
  }
  return context;
}
export { ApThemeProvider, useApThemeState, useApThemeDispatch };
