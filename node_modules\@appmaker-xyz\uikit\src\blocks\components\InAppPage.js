import React from 'react';

import { StyleSheet } from 'react-native';
import { Layout } from '@appmaker-xyz/uikit/src/index';
import { useInAppPage } from '@appmaker-xyz/core';

function InAppPage({ attributes, BlocksView, currentAction, onAction }) {
  const { pageId } = attributes;
  const [{ inAppPage, loading: inAppPageLoading }, refetch] = useInAppPage({
    pageId,
    action: currentAction,
  });
  const loadingLayout =
    pageId === 'home' ? 'home' : inAppPage?.attributes?.loadingLayout;

  return (
    <Layout
      loadingLayout={loadingLayout}
      loading={inAppPageLoading && !inAppPage?.blocks}
      style={styles.container}>
      <BlocksView
        inAppPage={inAppPage}
        currentAction={currentAction}
        onAction={onAction}
        onRefresh={inAppPage?.attributes?.enablePullRefresh ? refetch : null}
        isRefreshing={inAppPageLoading}
      />
    </Layout>
  );
}
export default React.memo(InAppPage);
const styles = StyleSheet.create({});
