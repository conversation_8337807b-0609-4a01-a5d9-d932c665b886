{"version": 3, "names": ["moduleName", "dirname", "absoluteRuntime", "resolveAbsoluteRuntime", "path", "resolve", "paths", "replace", "err", "code", "Object", "assign", "Error", "runtime", "resolveFSPath", "require"], "sources": ["../../src/get-runtime-path/index.ts"], "sourcesContent": ["import path from \"path\";\n\nimport { createRequire } from \"module\";\nconst require = createRequire(import.meta.url);\n\nexport default function (\n  moduleName: string,\n  dirname: string,\n  absoluteRuntime: string | boolean,\n) {\n  if (absoluteRuntime === false) return moduleName;\n\n  return resolveAbsoluteRuntime(\n    moduleName,\n    path.resolve(dirname, absoluteRuntime === true ? \".\" : absoluteRuntime),\n  );\n}\n\nfunction resolveAbsoluteRuntime(moduleName: string, dirname: string) {\n  try {\n    return path\n      .dirname(\n        require.resolve(`${moduleName}/package.json`, { paths: [dirname] }),\n      )\n      .replace(/\\\\/g, \"/\");\n  } catch (err) {\n    if (err.code !== \"MODULE_NOT_FOUND\") throw err;\n\n    throw Object.assign(\n      new Error(`Failed to resolve \"${moduleName}\" relative to \"${dirname}\"`),\n      {\n        code: \"BABEL_RUNTIME_NOT_FOUND\",\n        runtime: moduleName,\n        dirname,\n      },\n    );\n  }\n}\n\nexport function resolveFSPath(path: string) {\n  return require.resolve(path).replace(/\\\\/g, \"/\");\n}\n"], "mappings": ";;;;;;;AAAA;AAEA;AAGe,kBACbA,UAAkB,EAClBC,OAAe,EACfC,eAAiC,EACjC;EACA,IAAIA,eAAe,KAAK,KAAK,EAAE,OAAOF,UAAU;EAEhD,OAAOG,sBAAsB,CAC3BH,UAAU,EACVI,KAAI,CAACC,OAAO,CAACJ,OAAO,EAAEC,eAAe,KAAK,IAAI,GAAG,GAAG,GAAGA,eAAe,CAAC,CACxE;AACH;AAEA,SAASC,sBAAsB,CAACH,UAAkB,EAAEC,OAAe,EAAE;EACnE,IAAI;IACF,OAAOG,KAAI,CACRH,OAAO,CACN;MAAA;IAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA,GAAiB,GAAED,UAAW,eAAc,EAAE;MAAEM,KAAK,EAAE,CAACL,OAAO;IAAE,CAAC,CAAC,CACpE,CACAM,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EACxB,CAAC,CAAC,OAAOC,GAAG,EAAE;IACZ,IAAIA,GAAG,CAACC,IAAI,KAAK,kBAAkB,EAAE,MAAMD,GAAG;IAE9C,MAAME,MAAM,CAACC,MAAM,CACjB,IAAIC,KAAK,CAAE,sBAAqBZ,UAAW,kBAAiBC,OAAQ,GAAE,CAAC,EACvE;MACEQ,IAAI,EAAE,yBAAyB;MAC/BI,OAAO,EAAEb,UAAU;MACnBC;IACF,CAAC,CACF;EACH;AACF;AAEO,SAASa,aAAa,CAACV,IAAY,EAAE;EAC1C,OAAOW,OAAO,CAACV,OAAO,CAACD,IAAI,CAAC,CAACG,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AAClD"}