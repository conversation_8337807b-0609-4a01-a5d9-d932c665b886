{"version": 3, "names": ["_helperValidatorIdentifier", "require", "keywordRelationalOperator", "exports", "isIteratorStart", "current", "next", "next2", "isIdentifierStart", "reservedWordLikeSet", "Set", "canBeReservedWord", "word", "has"], "sources": ["../../src/util/identifier.ts"], "sourcesContent": ["/* eslint max-len: 0 */\n\nimport * as charCodes from \"charcodes\";\nimport { isIdentifierStart } from \"@babel/helper-validator-identifier\";\n\nexport {\n  isIdentifierStart,\n  isIdentifierChar,\n  isReservedWord,\n  isStrictBindOnlyReservedWord,\n  isStrictBindReservedWord,\n  isStrictReservedWord,\n  isKeyword,\n} from \"@babel/helper-validator-identifier\";\n\nexport const keywordRelationalOperator = /^in(stanceof)?$/;\n\n// Test whether a current state character code and next character code is @\n\nexport function isIteratorStart(\n  current: number,\n  next: number,\n  next2: number,\n): boolean {\n  return (\n    current === charCodes.atSign &&\n    next === charCodes.atSign &&\n    isIdentifierStart(next2)\n  );\n}\n\n// This is the comprehensive set of JavaScript reserved words\n// If a word is in this set, it could be a reserved word,\n// depending on sourceType/strictMode/binding info. In other words\n// if a word is not in this set, it is not a reserved word under\n// any circumstance.\nconst reservedWordLikeSet = new Set([\n  \"break\",\n  \"case\",\n  \"catch\",\n  \"continue\",\n  \"debugger\",\n  \"default\",\n  \"do\",\n  \"else\",\n  \"finally\",\n  \"for\",\n  \"function\",\n  \"if\",\n  \"return\",\n  \"switch\",\n  \"throw\",\n  \"try\",\n  \"var\",\n  \"const\",\n  \"while\",\n  \"with\",\n  \"new\",\n  \"this\",\n  \"super\",\n  \"class\",\n  \"extends\",\n  \"export\",\n  \"import\",\n  \"null\",\n  \"true\",\n  \"false\",\n  \"in\",\n  \"instanceof\",\n  \"typeof\",\n  \"void\",\n  \"delete\",\n  // strict\n  \"implements\",\n  \"interface\",\n  \"let\",\n  \"package\",\n  \"private\",\n  \"protected\",\n  \"public\",\n  \"static\",\n  \"yield\",\n  // strictBind\n  \"eval\",\n  \"arguments\",\n  // reservedWorkLike\n  \"enum\",\n  \"await\",\n]);\n\nexport function canBeReservedWord(word: string): boolean {\n  return reservedWordLikeSet.has(word);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAAA,0BAAA,GAAAC,OAAA;AAYO,MAAMC,yBAAyB,GAAG,iBAAiB;AAACC,OAAA,CAAAD,yBAAA,GAAAA,yBAAA;AAIpD,SAASE,eAAeA,CAC7BC,OAAe,EACfC,IAAY,EACZC,KAAa,EACJ;EACT,OACEF,OAAO,OAAqB,IAC5BC,IAAI,OAAqB,IACzB,IAAAE,4CAAiB,EAACD,KAAK,CAAC;AAE5B;AAOA,MAAME,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAClC,OAAO,EACP,MAAM,EACN,OAAO,EACP,UAAU,EACV,UAAU,EACV,SAAS,EACT,IAAI,EACJ,MAAM,EACN,SAAS,EACT,KAAK,EACL,UAAU,EACV,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,KAAK,EACL,KAAK,EACL,OAAO,EACP,OAAO,EACP,MAAM,EACN,KAAK,EACL,MAAM,EACN,OAAO,EACP,OAAO,EACP,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,QAAQ,EAER,YAAY,EACZ,WAAW,EACX,KAAK,EACL,SAAS,EACT,SAAS,EACT,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,OAAO,EAEP,MAAM,EACN,WAAW,EAEX,MAAM,EACN,OAAO,CACR,CAAC;AAEK,SAASC,iBAAiBA,CAACC,IAAY,EAAW;EACvD,OAAOH,mBAAmB,CAACI,GAAG,CAACD,IAAI,CAAC;AACtC"}