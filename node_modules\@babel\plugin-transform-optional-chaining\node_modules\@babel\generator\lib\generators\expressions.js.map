{"version": 3, "names": ["_t", "require", "n", "isCallExpression", "isLiteral", "isMemberExpression", "isNewExpression", "UnaryExpression", "node", "operator", "word", "space", "token", "print", "argument", "DoExpression", "async", "body", "ParenthesizedExpression", "expression", "rightParens", "UpdateExpression", "prefix", "printTerminatorless", "ConditionalExpression", "test", "consequent", "alternate", "NewExpression", "parent", "callee", "format", "minified", "arguments", "length", "optional", "typeArguments", "typeParameters", "printList", "SequenceExpression", "expressions", "ThisExpression", "Super", "isDecoratorMemberExpression", "type", "computed", "property", "object", "shouldParenthesizeDecoratorExpression", "_shouldPrintDecoratorsBeforeExport", "decoratorsBeforeExport", "start", "declaration", "Decorator", "newline", "OptionalMemberExpression", "TypeError", "value", "OptionalCallExpression", "CallExpression", "Import", "AwaitExpression", "YieldExpression", "delegate", "EmptyStatement", "semicolon", "ExpressionStatement", "AssignmentPattern", "left", "typeAnnotation", "right", "AssignmentExpression", "parens", "inForStatementInitCounter", "needsParens", "BindExpression", "MemberExpression", "MetaProperty", "meta", "PrivateName", "id", "V8IntrinsicIdentifier", "name", "ModuleExpression", "indent", "directives", "dedent", "rightBrace"], "sources": ["../../src/generators/expressions.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport {\n  isCallExpression,\n  isLiteral,\n  isMemberExpression,\n  isNewExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport * as n from \"../node/index.ts\";\n\nexport function UnaryExpression(this: Printer, node: t.UnaryExpression) {\n  const { operator } = node;\n  if (\n    operator === \"void\" ||\n    operator === \"delete\" ||\n    operator === \"typeof\" ||\n    // throwExpressions\n    operator === \"throw\"\n  ) {\n    this.word(operator);\n    this.space();\n  } else {\n    this.token(operator);\n  }\n\n  this.print(node.argument, node);\n}\n\nexport function DoExpression(this: Printer, node: t.DoExpression) {\n  if (node.async) {\n    this.word(\"async\", true);\n    this.space();\n  }\n  this.word(\"do\");\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function ParenthesizedExpression(\n  this: Printer,\n  node: t.ParenthesizedExpression,\n) {\n  this.token(\"(\");\n  this.print(node.expression, node);\n  this.rightParens(node);\n}\n\nexport function UpdateExpression(this: Printer, node: t.UpdateExpression) {\n  if (node.prefix) {\n    this.token(node.operator);\n    this.print(node.argument, node);\n  } else {\n    this.printTerminatorless(node.argument, node, true);\n    this.token(node.operator);\n  }\n}\n\nexport function ConditionalExpression(\n  this: Printer,\n  node: t.ConditionalExpression,\n) {\n  this.print(node.test, node);\n  this.space();\n  this.token(\"?\");\n  this.space();\n  this.print(node.consequent, node);\n  this.space();\n  this.token(\":\");\n  this.space();\n  this.print(node.alternate, node);\n}\n\nexport function NewExpression(\n  this: Printer,\n  node: t.NewExpression,\n  parent: t.Node,\n) {\n  this.word(\"new\");\n  this.space();\n  this.print(node.callee, node);\n  if (\n    this.format.minified &&\n    node.arguments.length === 0 &&\n    !node.optional &&\n    !isCallExpression(parent, { callee: node }) &&\n    !isMemberExpression(parent) &&\n    !isNewExpression(parent)\n  ) {\n    return;\n  }\n\n  this.print(node.typeArguments, node); // Flow\n  this.print(node.typeParameters, node); // TS\n\n  if (node.optional) {\n    // TODO: This can never happen\n    this.token(\"?.\");\n  }\n  this.token(\"(\");\n  this.printList(node.arguments, node);\n  this.rightParens(node);\n}\n\nexport function SequenceExpression(this: Printer, node: t.SequenceExpression) {\n  this.printList(node.expressions, node);\n}\n\nexport function ThisExpression(this: Printer) {\n  this.word(\"this\");\n}\n\nexport function Super(this: Printer) {\n  this.word(\"super\");\n}\n\nfunction isDecoratorMemberExpression(\n  node: t.Expression | t.Super | t.V8IntrinsicIdentifier,\n): boolean {\n  switch (node.type) {\n    case \"Identifier\":\n      return true;\n    case \"MemberExpression\":\n      return (\n        !node.computed &&\n        node.property.type === \"Identifier\" &&\n        isDecoratorMemberExpression(node.object)\n      );\n    default:\n      return false;\n  }\n}\nfunction shouldParenthesizeDecoratorExpression(\n  node: t.Expression | t.Super | t.V8IntrinsicIdentifier,\n) {\n  if (node.type === \"ParenthesizedExpression\") {\n    // We didn't check extra?.parenthesized here because we don't track decorators in needsParen\n    return false;\n  }\n  return !isDecoratorMemberExpression(\n    node.type === \"CallExpression\" ? node.callee : node,\n  );\n}\n\nexport function _shouldPrintDecoratorsBeforeExport(\n  this: Printer,\n  node: t.ExportDeclaration & { declaration: t.ClassDeclaration },\n) {\n  if (typeof this.format.decoratorsBeforeExport === \"boolean\") {\n    return this.format.decoratorsBeforeExport;\n  }\n  return (\n    typeof node.start === \"number\" && node.start === node.declaration.start\n  );\n}\n\nexport function Decorator(this: Printer, node: t.Decorator) {\n  this.token(\"@\");\n  const { expression } = node;\n  if (shouldParenthesizeDecoratorExpression(expression)) {\n    this.token(\"(\");\n    this.print(expression, node);\n    this.token(\")\");\n  } else {\n    this.print(expression, node);\n  }\n  this.newline();\n}\n\nexport function OptionalMemberExpression(\n  this: Printer,\n  node: t.OptionalMemberExpression,\n) {\n  let { computed } = node;\n  const { optional, property } = node;\n\n  this.print(node.object, node);\n\n  if (!computed && isMemberExpression(property)) {\n    throw new TypeError(\"Got a MemberExpression for MemberExpression property\");\n  }\n\n  // @ts-expect-error todo(flow->ts) maybe instead of typeof check specific literal types?\n  if (isLiteral(property) && typeof property.value === \"number\") {\n    computed = true;\n  }\n  if (optional) {\n    this.token(\"?.\");\n  }\n\n  if (computed) {\n    this.token(\"[\");\n    this.print(property, node);\n    this.token(\"]\");\n  } else {\n    if (!optional) {\n      this.token(\".\");\n    }\n    this.print(property, node);\n  }\n}\n\nexport function OptionalCallExpression(\n  this: Printer,\n  node: t.OptionalCallExpression,\n) {\n  this.print(node.callee, node);\n\n  this.print(node.typeParameters, node); // TS\n\n  if (node.optional) {\n    this.token(\"?.\");\n  }\n\n  this.print(node.typeArguments, node); // Flow\n\n  this.token(\"(\");\n  this.printList(node.arguments, node);\n  this.rightParens(node);\n}\n\nexport function CallExpression(this: Printer, node: t.CallExpression) {\n  this.print(node.callee, node);\n\n  this.print(node.typeArguments, node); // Flow\n  this.print(node.typeParameters, node); // TS\n  this.token(\"(\");\n  this.printList(node.arguments, node);\n  this.rightParens(node);\n}\n\nexport function Import(this: Printer) {\n  this.word(\"import\");\n}\n\nexport function AwaitExpression(this: Printer, node: t.AwaitExpression) {\n  this.word(\"await\");\n\n  if (node.argument) {\n    this.space();\n    this.printTerminatorless(node.argument, node, false);\n  }\n}\n\nexport function YieldExpression(this: Printer, node: t.YieldExpression) {\n  this.word(\"yield\", true);\n\n  if (node.delegate) {\n    this.token(\"*\");\n    if (node.argument) {\n      this.space();\n      // line terminators are allowed after yield*\n      this.print(node.argument, node);\n    }\n  } else {\n    if (node.argument) {\n      this.space();\n      this.printTerminatorless(node.argument, node, false);\n    }\n  }\n}\n\nexport function EmptyStatement(this: Printer) {\n  this.semicolon(true /* force */);\n}\n\nexport function ExpressionStatement(\n  this: Printer,\n  node: t.ExpressionStatement,\n) {\n  this.print(node.expression, node);\n  this.semicolon();\n}\n\nexport function AssignmentPattern(this: Printer, node: t.AssignmentPattern) {\n  this.print(node.left, node);\n  // @ts-expect-error todo(flow->ts) property present on some of the types in union but not all\n  if (node.left.optional) this.token(\"?\");\n  // @ts-expect-error todo(flow->ts) property present on some of the types in union but not all\n  this.print(node.left.typeAnnotation, node);\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(node.right, node);\n}\n\nexport function AssignmentExpression(\n  this: Printer,\n  node: t.AssignmentExpression,\n  parent: t.Node,\n) {\n  // Somewhere inside a for statement `init` node but doesn't usually\n  // needs a paren except for `in` expressions: `for (a in b ? a : b;;)`\n  const parens =\n    this.inForStatementInitCounter &&\n    node.operator === \"in\" &&\n    !n.needsParens(node, parent);\n\n  if (parens) {\n    this.token(\"(\");\n  }\n\n  this.print(node.left, node);\n\n  this.space();\n  if (node.operator === \"in\" || node.operator === \"instanceof\") {\n    this.word(node.operator);\n  } else {\n    this.token(node.operator);\n  }\n  this.space();\n\n  this.print(node.right, node);\n\n  if (parens) {\n    this.token(\")\");\n  }\n}\n\nexport function BindExpression(this: Printer, node: t.BindExpression) {\n  this.print(node.object, node);\n  this.token(\"::\");\n  this.print(node.callee, node);\n}\n\nexport {\n  AssignmentExpression as BinaryExpression,\n  AssignmentExpression as LogicalExpression,\n};\n\nexport function MemberExpression(this: Printer, node: t.MemberExpression) {\n  this.print(node.object, node);\n\n  if (!node.computed && isMemberExpression(node.property)) {\n    throw new TypeError(\"Got a MemberExpression for MemberExpression property\");\n  }\n\n  let computed = node.computed;\n  // @ts-expect-error todo(flow->ts) maybe use specific literal types\n  if (isLiteral(node.property) && typeof node.property.value === \"number\") {\n    computed = true;\n  }\n\n  if (computed) {\n    this.token(\"[\");\n    this.print(node.property, node);\n    this.token(\"]\");\n  } else {\n    this.token(\".\");\n    this.print(node.property, node);\n  }\n}\n\nexport function MetaProperty(this: Printer, node: t.MetaProperty) {\n  this.print(node.meta, node);\n  this.token(\".\");\n  this.print(node.property, node);\n}\n\nexport function PrivateName(this: Printer, node: t.PrivateName) {\n  this.token(\"#\");\n  this.print(node.id, node);\n}\n\nexport function V8IntrinsicIdentifier(\n  this: Printer,\n  node: t.V8IntrinsicIdentifier,\n) {\n  this.token(\"%\");\n  this.word(node.name);\n}\n\nexport function ModuleExpression(this: Printer, node: t.ModuleExpression) {\n  this.word(\"module\", true);\n  this.space();\n  this.token(\"{\");\n  this.indent();\n  const { body } = node;\n  if (body.body.length || body.directives.length) {\n    this.newline();\n  }\n  this.print(body, node);\n  this.dedent();\n  this.rightBrace(node);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAOA,IAAAC,CAAA,GAAAD,OAAA;AAAsC;EANpCE,gBAAgB;EAChBC,SAAS;EACTC,kBAAkB;EAClBC;AAAe,IAAAN,EAAA;AAKV,SAASO,eAAeA,CAAgBC,IAAuB,EAAE;EACtE,MAAM;IAAEC;EAAS,CAAC,GAAGD,IAAI;EACzB,IACEC,QAAQ,KAAK,MAAM,IACnBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,QAAQ,IAErBA,QAAQ,KAAK,OAAO,EACpB;IACA,IAAI,CAACC,IAAI,CAACD,QAAQ,CAAC;IACnB,IAAI,CAACE,KAAK,CAAC,CAAC;EACd,CAAC,MAAM;IACL,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;EACtB;EAEA,IAAI,CAACI,KAAK,CAACL,IAAI,CAACM,QAAQ,EAAEN,IAAI,CAAC;AACjC;AAEO,SAASO,YAAYA,CAAgBP,IAAoB,EAAE;EAChE,IAAIA,IAAI,CAACQ,KAAK,EAAE;IACd,IAAI,CAACN,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACxB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACS,IAAI,EAAET,IAAI,CAAC;AAC7B;AAEO,SAASU,uBAAuBA,CAErCV,IAA+B,EAC/B;EACA,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACW,UAAU,EAAEX,IAAI,CAAC;EACjC,IAAI,CAACY,WAAW,CAACZ,IAAI,CAAC;AACxB;AAEO,SAASa,gBAAgBA,CAAgBb,IAAwB,EAAE;EACxE,IAAIA,IAAI,CAACc,MAAM,EAAE;IACf,IAAI,CAACV,KAAK,CAACJ,IAAI,CAACC,QAAQ,CAAC;IACzB,IAAI,CAACI,KAAK,CAACL,IAAI,CAACM,QAAQ,EAAEN,IAAI,CAAC;EACjC,CAAC,MAAM;IACL,IAAI,CAACe,mBAAmB,CAACf,IAAI,CAACM,QAAQ,EAAEN,IAAI,EAAE,IAAI,CAAC;IACnD,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACC,QAAQ,CAAC;EAC3B;AACF;AAEO,SAASe,qBAAqBA,CAEnChB,IAA6B,EAC7B;EACA,IAAI,CAACK,KAAK,CAACL,IAAI,CAACiB,IAAI,EAAEjB,IAAI,CAAC;EAC3B,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACkB,UAAU,EAAElB,IAAI,CAAC;EACjC,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACmB,SAAS,EAAEnB,IAAI,CAAC;AAClC;AAEO,SAASoB,aAAaA,CAE3BpB,IAAqB,EACrBqB,MAAc,EACd;EACA,IAAI,CAACnB,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACsB,MAAM,EAAEtB,IAAI,CAAC;EAC7B,IACE,IAAI,CAACuB,MAAM,CAACC,QAAQ,IACpBxB,IAAI,CAACyB,SAAS,CAACC,MAAM,KAAK,CAAC,IAC3B,CAAC1B,IAAI,CAAC2B,QAAQ,IACd,CAAChC,gBAAgB,CAAC0B,MAAM,EAAE;IAAEC,MAAM,EAAEtB;EAAK,CAAC,CAAC,IAC3C,CAACH,kBAAkB,CAACwB,MAAM,CAAC,IAC3B,CAACvB,eAAe,CAACuB,MAAM,CAAC,EACxB;IACA;EACF;EAEA,IAAI,CAAChB,KAAK,CAACL,IAAI,CAAC4B,aAAa,EAAE5B,IAAI,CAAC;EACpC,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC6B,cAAc,EAAE7B,IAAI,CAAC;EAErC,IAAIA,IAAI,CAAC2B,QAAQ,EAAE;IAEjB,IAAI,CAACvB,KAAK,CAAC,IAAI,CAAC;EAClB;EACA,IAAI,CAACA,SAAK,GAAI,CAAC;EACf,IAAI,CAAC0B,SAAS,CAAC9B,IAAI,CAACyB,SAAS,EAAEzB,IAAI,CAAC;EACpC,IAAI,CAACY,WAAW,CAACZ,IAAI,CAAC;AACxB;AAEO,SAAS+B,kBAAkBA,CAAgB/B,IAA0B,EAAE;EAC5E,IAAI,CAAC8B,SAAS,CAAC9B,IAAI,CAACgC,WAAW,EAAEhC,IAAI,CAAC;AACxC;AAEO,SAASiC,cAAcA,CAAA,EAAgB;EAC5C,IAAI,CAAC/B,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAASgC,KAAKA,CAAA,EAAgB;EACnC,IAAI,CAAChC,IAAI,CAAC,OAAO,CAAC;AACpB;AAEA,SAASiC,2BAA2BA,CAClCnC,IAAsD,EAC7C;EACT,QAAQA,IAAI,CAACoC,IAAI;IACf,KAAK,YAAY;MACf,OAAO,IAAI;IACb,KAAK,kBAAkB;MACrB,OACE,CAACpC,IAAI,CAACqC,QAAQ,IACdrC,IAAI,CAACsC,QAAQ,CAACF,IAAI,KAAK,YAAY,IACnCD,2BAA2B,CAACnC,IAAI,CAACuC,MAAM,CAAC;IAE5C;MACE,OAAO,KAAK;EAChB;AACF;AACA,SAASC,qCAAqCA,CAC5CxC,IAAsD,EACtD;EACA,IAAIA,IAAI,CAACoC,IAAI,KAAK,yBAAyB,EAAE;IAE3C,OAAO,KAAK;EACd;EACA,OAAO,CAACD,2BAA2B,CACjCnC,IAAI,CAACoC,IAAI,KAAK,gBAAgB,GAAGpC,IAAI,CAACsB,MAAM,GAAGtB,IACjD,CAAC;AACH;AAEO,SAASyC,kCAAkCA,CAEhDzC,IAA+D,EAC/D;EACA,IAAI,OAAO,IAAI,CAACuB,MAAM,CAACmB,sBAAsB,KAAK,SAAS,EAAE;IAC3D,OAAO,IAAI,CAACnB,MAAM,CAACmB,sBAAsB;EAC3C;EACA,OACE,OAAO1C,IAAI,CAAC2C,KAAK,KAAK,QAAQ,IAAI3C,IAAI,CAAC2C,KAAK,KAAK3C,IAAI,CAAC4C,WAAW,CAACD,KAAK;AAE3E;AAEO,SAASE,SAASA,CAAgB7C,IAAiB,EAAE;EAC1D,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,MAAM;IAAEO;EAAW,CAAC,GAAGX,IAAI;EAC3B,IAAIwC,qCAAqC,CAAC7B,UAAU,CAAC,EAAE;IACrD,IAAI,CAACP,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAACM,UAAU,EAAEX,IAAI,CAAC;IAC5B,IAAI,CAACI,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACC,KAAK,CAACM,UAAU,EAAEX,IAAI,CAAC;EAC9B;EACA,IAAI,CAAC8C,OAAO,CAAC,CAAC;AAChB;AAEO,SAASC,wBAAwBA,CAEtC/C,IAAgC,EAChC;EACA,IAAI;IAAEqC;EAAS,CAAC,GAAGrC,IAAI;EACvB,MAAM;IAAE2B,QAAQ;IAAEW;EAAS,CAAC,GAAGtC,IAAI;EAEnC,IAAI,CAACK,KAAK,CAACL,IAAI,CAACuC,MAAM,EAAEvC,IAAI,CAAC;EAE7B,IAAI,CAACqC,QAAQ,IAAIxC,kBAAkB,CAACyC,QAAQ,CAAC,EAAE;IAC7C,MAAM,IAAIU,SAAS,CAAC,sDAAsD,CAAC;EAC7E;EAGA,IAAIpD,SAAS,CAAC0C,QAAQ,CAAC,IAAI,OAAOA,QAAQ,CAACW,KAAK,KAAK,QAAQ,EAAE;IAC7DZ,QAAQ,GAAG,IAAI;EACjB;EACA,IAAIV,QAAQ,EAAE;IACZ,IAAI,CAACvB,KAAK,CAAC,IAAI,CAAC;EAClB;EAEA,IAAIiC,QAAQ,EAAE;IACZ,IAAI,CAACjC,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAACiC,QAAQ,EAAEtC,IAAI,CAAC;IAC1B,IAAI,CAACI,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACuB,QAAQ,EAAE;MACb,IAAI,CAACvB,SAAK,GAAI,CAAC;IACjB;IACA,IAAI,CAACC,KAAK,CAACiC,QAAQ,EAAEtC,IAAI,CAAC;EAC5B;AACF;AAEO,SAASkD,sBAAsBA,CAEpClD,IAA8B,EAC9B;EACA,IAAI,CAACK,KAAK,CAACL,IAAI,CAACsB,MAAM,EAAEtB,IAAI,CAAC;EAE7B,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC6B,cAAc,EAAE7B,IAAI,CAAC;EAErC,IAAIA,IAAI,CAAC2B,QAAQ,EAAE;IACjB,IAAI,CAACvB,KAAK,CAAC,IAAI,CAAC;EAClB;EAEA,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC4B,aAAa,EAAE5B,IAAI,CAAC;EAEpC,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAAC0B,SAAS,CAAC9B,IAAI,CAACyB,SAAS,EAAEzB,IAAI,CAAC;EACpC,IAAI,CAACY,WAAW,CAACZ,IAAI,CAAC;AACxB;AAEO,SAASmD,cAAcA,CAAgBnD,IAAsB,EAAE;EACpE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACsB,MAAM,EAAEtB,IAAI,CAAC;EAE7B,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC4B,aAAa,EAAE5B,IAAI,CAAC;EACpC,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC6B,cAAc,EAAE7B,IAAI,CAAC;EACrC,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAAC0B,SAAS,CAAC9B,IAAI,CAACyB,SAAS,EAAEzB,IAAI,CAAC;EACpC,IAAI,CAACY,WAAW,CAACZ,IAAI,CAAC;AACxB;AAEO,SAASoD,MAAMA,CAAA,EAAgB;EACpC,IAAI,CAAClD,IAAI,CAAC,QAAQ,CAAC;AACrB;AAEO,SAASmD,eAAeA,CAAgBrD,IAAuB,EAAE;EACtE,IAAI,CAACE,IAAI,CAAC,OAAO,CAAC;EAElB,IAAIF,IAAI,CAACM,QAAQ,EAAE;IACjB,IAAI,CAACH,KAAK,CAAC,CAAC;IACZ,IAAI,CAACY,mBAAmB,CAACf,IAAI,CAACM,QAAQ,EAAEN,IAAI,EAAE,KAAK,CAAC;EACtD;AACF;AAEO,SAASsD,eAAeA,CAAgBtD,IAAuB,EAAE;EACtE,IAAI,CAACE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;EAExB,IAAIF,IAAI,CAACuD,QAAQ,EAAE;IACjB,IAAI,CAACnD,SAAK,GAAI,CAAC;IACf,IAAIJ,IAAI,CAACM,QAAQ,EAAE;MACjB,IAAI,CAACH,KAAK,CAAC,CAAC;MAEZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACM,QAAQ,EAAEN,IAAI,CAAC;IACjC;EACF,CAAC,MAAM;IACL,IAAIA,IAAI,CAACM,QAAQ,EAAE;MACjB,IAAI,CAACH,KAAK,CAAC,CAAC;MACZ,IAAI,CAACY,mBAAmB,CAACf,IAAI,CAACM,QAAQ,EAAEN,IAAI,EAAE,KAAK,CAAC;IACtD;EACF;AACF;AAEO,SAASwD,cAAcA,CAAA,EAAgB;EAC5C,IAAI,CAACC,SAAS,CAAC,IAAgB,CAAC;AAClC;AAEO,SAASC,mBAAmBA,CAEjC1D,IAA2B,EAC3B;EACA,IAAI,CAACK,KAAK,CAACL,IAAI,CAACW,UAAU,EAAEX,IAAI,CAAC;EACjC,IAAI,CAACyD,SAAS,CAAC,CAAC;AAClB;AAEO,SAASE,iBAAiBA,CAAgB3D,IAAyB,EAAE;EAC1E,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC4D,IAAI,EAAE5D,IAAI,CAAC;EAE3B,IAAIA,IAAI,CAAC4D,IAAI,CAACjC,QAAQ,EAAE,IAAI,CAACvB,SAAK,GAAI,CAAC;EAEvC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC4D,IAAI,CAACC,cAAc,EAAE7D,IAAI,CAAC;EAC1C,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,GAAI,CAAC;EACf,IAAI,CAACD,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAAC8D,KAAK,EAAE9D,IAAI,CAAC;AAC9B;AAEO,SAAS+D,oBAAoBA,CAElC/D,IAA4B,EAC5BqB,MAAc,EACd;EAGA,MAAM2C,MAAM,GACV,IAAI,CAACC,yBAAyB,IAC9BjE,IAAI,CAACC,QAAQ,KAAK,IAAI,IACtB,CAACP,CAAC,CAACwE,WAAW,CAAClE,IAAI,EAAEqB,MAAM,CAAC;EAE9B,IAAI2C,MAAM,EAAE;IACV,IAAI,CAAC5D,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC4D,IAAI,EAAE5D,IAAI,CAAC;EAE3B,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAIH,IAAI,CAACC,QAAQ,KAAK,IAAI,IAAID,IAAI,CAACC,QAAQ,KAAK,YAAY,EAAE;IAC5D,IAAI,CAACC,IAAI,CAACF,IAAI,CAACC,QAAQ,CAAC;EAC1B,CAAC,MAAM;IACL,IAAI,CAACG,KAAK,CAACJ,IAAI,CAACC,QAAQ,CAAC;EAC3B;EACA,IAAI,CAACE,KAAK,CAAC,CAAC;EAEZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAAC8D,KAAK,EAAE9D,IAAI,CAAC;EAE5B,IAAIgE,MAAM,EAAE;IACV,IAAI,CAAC5D,SAAK,GAAI,CAAC;EACjB;AACF;AAEO,SAAS+D,cAAcA,CAAgBnE,IAAsB,EAAE;EACpE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACuC,MAAM,EAAEvC,IAAI,CAAC;EAC7B,IAAI,CAACI,KAAK,CAAC,IAAI,CAAC;EAChB,IAAI,CAACC,KAAK,CAACL,IAAI,CAACsB,MAAM,EAAEtB,IAAI,CAAC;AAC/B;AAOO,SAASoE,gBAAgBA,CAAgBpE,IAAwB,EAAE;EACxE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACuC,MAAM,EAAEvC,IAAI,CAAC;EAE7B,IAAI,CAACA,IAAI,CAACqC,QAAQ,IAAIxC,kBAAkB,CAACG,IAAI,CAACsC,QAAQ,CAAC,EAAE;IACvD,MAAM,IAAIU,SAAS,CAAC,sDAAsD,CAAC;EAC7E;EAEA,IAAIX,QAAQ,GAAGrC,IAAI,CAACqC,QAAQ;EAE5B,IAAIzC,SAAS,CAACI,IAAI,CAACsC,QAAQ,CAAC,IAAI,OAAOtC,IAAI,CAACsC,QAAQ,CAACW,KAAK,KAAK,QAAQ,EAAE;IACvEZ,QAAQ,GAAG,IAAI;EACjB;EAEA,IAAIA,QAAQ,EAAE;IACZ,IAAI,CAACjC,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACsC,QAAQ,EAAEtC,IAAI,CAAC;IAC/B,IAAI,CAACI,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACA,SAAK,GAAI,CAAC;IACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACsC,QAAQ,EAAEtC,IAAI,CAAC;EACjC;AACF;AAEO,SAASqE,YAAYA,CAAgBrE,IAAoB,EAAE;EAChE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACsE,IAAI,EAAEtE,IAAI,CAAC;EAC3B,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACsC,QAAQ,EAAEtC,IAAI,CAAC;AACjC;AAEO,SAASuE,WAAWA,CAAgBvE,IAAmB,EAAE;EAC9D,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACwE,EAAE,EAAExE,IAAI,CAAC;AAC3B;AAEO,SAASyE,qBAAqBA,CAEnCzE,IAA6B,EAC7B;EACA,IAAI,CAACI,SAAK,GAAI,CAAC;EACf,IAAI,CAACF,IAAI,CAACF,IAAI,CAAC0E,IAAI,CAAC;AACtB;AAEO,SAASC,gBAAgBA,CAAgB3E,IAAwB,EAAE;EACxE,IAAI,CAACE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC;EACzB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACC,SAAK,IAAI,CAAC;EACf,IAAI,CAACwE,MAAM,CAAC,CAAC;EACb,MAAM;IAAEnE;EAAK,CAAC,GAAGT,IAAI;EACrB,IAAIS,IAAI,CAACA,IAAI,CAACiB,MAAM,IAAIjB,IAAI,CAACoE,UAAU,CAACnD,MAAM,EAAE;IAC9C,IAAI,CAACoB,OAAO,CAAC,CAAC;EAChB;EACA,IAAI,CAACzC,KAAK,CAACI,IAAI,EAAET,IAAI,CAAC;EACtB,IAAI,CAAC8E,MAAM,CAAC,CAAC;EACb,IAAI,CAACC,UAAU,CAAC/E,IAAI,CAAC;AACvB"}