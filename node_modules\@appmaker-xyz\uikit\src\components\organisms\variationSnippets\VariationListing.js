import React from 'react';
import { StyleSheet } from 'react-native';
import { Radio, AppmakerText, Layout } from '../../../components';
import { spacing } from '../../../styles';

const VariationListing = ({
  variationTitle,
  varaitionHelper,
  variation_list,
  onPress,
}) => {
  return (
    <Layout style={styles.variationListingContainer}>
      {variationTitle && (
        <Layout style={styles.variationTitle}>
          <AppmakerText category="bodyParagraphBold" status="demiDark">
            {variationTitle}
          </AppmakerText>
          {varaitionHelper && (
            <AppmakerText category="highlighter2" status="grey">
              {varaitionHelper}
            </AppmakerText>
          )}
        </Layout>
      )}
      <Radio onPress={onPress} radio_items={variation_list} />
    </Layout>
  );
};

const styles = StyleSheet.create({
  variationListingContainer: {
    marginBottom: spacing.base,
  },
  variationTitle: {
    marginBottom: spacing.base,
  },
});

export default VariationListing;
