import { appmaker } from '@appmaker-xyz/core';
import { appStorageApi } from '@appmaker-xyz/core';
import SpecialText from './components/InsiderCartText';
import {
  ChumakInsider,
  InsiderButton,
  ChumbakInsiderFooter,
  InsiderBadge,
} from './blocks/index';
import ChumbakInsiderPage from './pages/chumbakInsider';
import ChumbakInsiderCartPage from './pages/chumbakInsiderCart';
import { addFilter } from '@appmaker-xyz/core';
import { color, spacing } from '../../shopify/pages/styles';
import { usePluginStore, onEvent } from '@appmaker-xyz/core';
import { ChumbakInsiderCustomerFetch } from './api';
import { runDataSource } from '@appmaker-xyz/core';

import {
  OPEN_CHUMBAK_INSIDER_PAGE,
  OPEN_CHUMBAK_INSIDER_PAGE_CART,
  ADD_TO_CART_XENO_PRODUCT,
} from './actions';

const membershipBlock = {
  name: 'appmaker/TouchableImage',
  clientId: 'xeno-membership-button',
  attributes: {
    appmakerAction: {
      action: 'OPEN_INAPP_PAGE',
      pageId: 'lmeosEFiN9',
    },
    style: {
      width: 200,
      height: 60,
      alignSelf: 'flex-start',
      resizeMode: 'contain',
      marginBottom: spacing.xl,
    },
    src: {
      uri: 'https://cdn.shopify.com/s/files/1/0601/1093/0098/files/join_now_logo_desktop_1024x1024.png?v=1655902882',
    },
  },
};

export async function activate({ settings }) {
  ChumbakInsiderCustomerFetch({ settings });
  appmaker.pages.registerPage('ChumbakInsiderPage', ChumbakInsiderPage);
  appmaker.pages.registerPage('ChumbakInsiderCartPage', ChumbakInsiderCartPage);

  appmaker.blocks.registerBlockType({
    name: 'appmaker/chumbak-insider',
    View: ChumakInsider,
  });
  appmaker.blocks.registerBlockType({
    name: 'appmaker/chumbak-insider-footer',
    View: ChumbakInsiderFooter,
  });
  appmaker.blocks.registerBlockType({
    name: 'appmaker/chumbak-insider-button',
    View: InsiderButton,
  });
  appmaker.blocks.registerBlockType({
    name: 'appmaker/chumbak-insider-badge',
    View: InsiderBadge,
  });
  appmaker.addFilter(
    'appmaker-cart-product-card-review-special-text',
    'cm-commerce-reviews',
    () => SpecialText,
  );
  // addFilter('home-page-id', 'custom-otp', () => 'ChumbakInsiderPage');
  appmaker.actions.registerAction(
    'OPEN_CHUMBAK_INSIDER_PAGE',
    OPEN_CHUMBAK_INSIDER_PAGE,
  );
  appmaker.actions.registerAction(
    'OPEN_CHUMBAK_INSIDER_PAGE_CART',
    OPEN_CHUMBAK_INSIDER_PAGE_CART,
  );
  appmaker.actions.registerAction(
    'ADD_TO_CART_XENO_PRODUCT',
    ADD_TO_CART_XENO_PRODUCT,
  );
  function findBlockIndex(blocks, clientId) {
    return blocks.findIndex((block) => block.clientId === clientId);
  }

  appmaker.addFilter('product-list-special-price', 'bold-sub', (data) => {
    const user = appStorageApi().getState().user;
    const startsWith = (arr, str) => {
      return arr.some((item) => item.startsWith(str));
    };
    const isInsider = user ? startsWith(user.tags, 'Xeno Tier:Member') : false;
    if (isInsider) {
      return `<% function amountFinal(a,b) {return ( parseFloat(a) - parseFloat(a) * (parseFloat(b) / 100)).toFixed(parseFloat(0));} %><%= currencyHelper(amountFinal(blockItem.node.priceRange.minVariantPrice.amount,${
        settings.discountPercentageOnMembership || 10
      }),blockItem.node.priceRange.minVariantPrice.currencyCode) %>`;
    }
  });

  appmaker.addFilter('force-update-user-tags', 'user-tags', () => true);
  /**
   * On order complete sync user tags
   * to update membership tags if user purchased xeno membership
   */
  onEvent('orderComplete', () => {
    setTimeout(async () => {
      const { user } = appStorageApi()?.getState();
      if (user) {
        const accessToken = user.accessToken;
        const [infoResponse] = await runDataSource(
          {
            dataSource: {
              source: 'shopify',
              attributes: {},
            },
            appId: 'appId',
          },
          {
            methodName: 'getCustomerInfo',
            params: {
              accessToken,
            },
          },
        );
        if (infoResponse?.data?.data?.customer?.tags) {
          appStorageApi().setState({
            user: {
              accessToken: user?.accessToken,
              ...infoResponse.data.data.customer,
            },
          });
        }
      }
    }, 3000);
  });
  appmaker.addFilter(
    'shopify-cart-can-update-line-item',
    'free-gift',
    (canRemove, { lineItem }) => {
      const customAttributes = lineItem?.customAttributes || [];
      if (
        customAttributes.find(
          (attr) => attr.key === '_membership' && attr.value === '_membership',
        )
      ) {
        return false;
      }
      return canRemove;
    },
  );
  appmaker.addFilter(
    'inapp-page-data-response',
    'bold-sub',
    (data, { pageId }) => {
      const { user, checkout } = appStorageApi().getState();
      //check if an array of object contains a key productType with a value membership
      const startsWith = (arr, str) => {
        return arr.some((item) => item.startsWith(str));
      };
      const isCurrentUserChumbakInsider =
        user && user?.tags?.length > 0
          ? startsWith(
              user.tags,
              settings?.userTagsForXenoStartsWith || 'Xeno Tier:Member',
            )
          : false;

      const index = 3;
      if (pageId == 'DrawerMenu') {
        const drawerHeaderIndex = findBlockIndex(data.blocks, 'drawer-header');
        const xenoMembershipButtonIndex = findBlockIndex(
          data.blocks,
          'xeno-membership-button',
        );
        if (
          xenoMembershipButtonIndex === -1 &&
          settings.showMembershipPageButtonOnMenu == true
        ) {
          data.blocks.splice(drawerHeaderIndex + 1, 0, membershipBlock);
        }
      }
      if (pageId === 'cartPageCheckout') {
        const isCurrentUserChumbakInsiderProductInCart =
          checkout && checkout?.lineItems?.edges.length > 0
            ? checkout?.lineItems?.edges.some((item) => {
                if (
                  item?.node?.variant?.product?.productType === 'membership'
                ) {
                  return true;
                }
              })
            : false;
        const foundIndexProductCard = findBlockIndex(data.blocks, 'cart-card');

        // alert(JSON.stringify(data));
        if (foundIndexProductCard != -1) {
          data.blocks[foundIndexProductCard].attributes.insider =
            isCurrentUserChumbakInsider ||
            isCurrentUserChumbakInsiderProductInCart;
          data.blocks[foundIndexProductCard].attributes.displayQuantity =
            '<%if(blockItem.node.variant.product.productType == "membership") { %><%="0"%><% } else{ %><%="1"%><% }%>';
          data.blocks[foundIndexProductCard].attributes.specialText =
            '<% if(blockItem.node.variant.product.productType != "membership"){function amountFinal(a,b) {return Math.round(parseFloat(a) - ( parseFloat(a) - parseFloat(a) * (parseFloat(b) / 100)).toFixed(parseFloat(2)));} %><%= echo(`Insider member discount is ${currencyHelper(amountFinal(parseFloat(blockItem.node.quantity)*parseFloat(blockItem.node.variant.price.amount),parseFloat(plugins.xeno.settings.discountPercentageOnMembership)),blockItem.node.variant.price.amount)}`) %><% } %>';

          data.blocks[foundIndexProductCard].attributes.productType =
            '{{blockItem.node.variant.product.productType}}';
        }
        const foundIndexCartFooter = findBlockIndex(
          data.stickyFooter.blocks,
          'checkout-sumbit',
        );
        const stickyFooterIndex = findBlockIndex(
          data.stickyFooter.blocks,
          'chumbak-insider-button-cart',
        );
        if (isCurrentUserChumbakInsider && stickyFooterIndex !== -1) {
          data.stickyFooter.blocks.splice(0, 1);
        }
        if (
          !isCurrentUserChumbakInsider &&
          stickyFooterIndex === -1 &&
          settings?.showMembershipPageButtonOnCart == true
        ) {
          data.stickyFooter.blocks.splice(0, 0, {
            name: 'appmaker/chumbak-insider-button',
            clientId: 'chumbak-insider-button-cart',
            attributes: {
              __display:
                '<% if(blockItem?.lineItems?.edges.filter(i => i.node?.variant?.product?.productType === `membership`).length > 0) { return false }else{ return true } %>',
              //   '{{!blockItem.product?.tags?.includes("no-insider-benefit") }}',
              type: 'cart_button',
              appmakerAction: {
                action: 'OPEN_CHUMBAK_INSIDER_PAGE_CART',
              },
            },
          });
        }

        // data.stickyFooter.blocks[foundIndexCartFooter].attributes.totalPrice =
        //   '<% function amountFinal(a,b) {return Math.round( parseFloat(a) - parseFloat(a) * (parseFloat(b) / 100)).toFixed(parseFloat(2));} %><%= currencyHelper(amountFinal(blockData.subtotalPrice.amount,10),blockData.subtotalPrice.currencyCode) %>';
      }
      if (pageId === 'productDetail') {
        // check if array contains an object with clientId 'chumbak-insider-button'

        // TODO temp fix for price api updates
        const shippingIndex = data?.blocks?.findIndex(
          (block) => block.name === 'appmaker/product-data',
        );
        if (shippingIndex >= 0) {
          data.blocks[shippingIndex].attributes.regularPrice =
            '<% if(pageState.variant.node.compareAtPrice && pageState.variant.node.compareAtPrice.amount && parseFloat(pageState.variant.node.compareAtPrice.amount)>parseFloat(pageState.variant.node.price.amount)){ %><%=pageState.variant.node.compareAtPrice.amount%><% }%>';
          data.blocks[shippingIndex].attributes.salePrice =
            '{{currencyHelper(pageState.variant.node.price.amount, pageState.variant.node.price.currencyCode)}} ';
          data.blocks[shippingIndex].attributes.onSale =
            '<% if(pageState.variant.node.compareAtPrice && parseFloat(pageState.variant.node.compareAtPrice.amount)>parseFloat(pageState.variant.node.price.amount)){ %><%=true%><% }%><%else{ %><%=false%><% }%>';
          data.blocks[shippingIndex].attributes.salePercentage =
            '<% if(pageState.variant.node.compareAtPrice&& parseFloat(pageState.variant.node.compareAtPrice.amount)>parseFloat(pageState.variant.node.price.amount)){ %><%=Math.round(100-(parseFloat(pageState.variant.node.price.amount)/parseFloat(pageState.variant.node.compareAtPrice.amount)*100))%> % Off<% }else{ %><%=""%><% } %> ';
          // data.blocks[shippingIndex].attributes.saved_amount =
          // '<% if(pageState.variant.node.compareAtPrice) { %> <%=differenceAmountCalculator(pageState.variant.node.compareAtPrice.amount, pageState.variant.node.price.amount,blockItem.node.priceRange.maxVariantPrice.currencyCode)%><% } else { %><%=""%><% }%>';
        }
        /**
         * For offline product hide some component in PDP
         */
        if (data?.blocks.length) {
          // Hide Shipping Information in PDP
          const shippingIndex = data?.blocks?.findIndex(
            (block) =>
              block.name === 'appmaker/expandable-text-block' &&
              block.clientId === '1a631fa5-6b37-44a6-ad6c-6a4992b8406e',
          );
          if (shippingIndex && data?.blocks[shippingIndex]?.attributes) {
            data.blocks[shippingIndex].attributes.__display =
              '<%= !hasTag("offline-exclusive", blockItem.node.tags) %>';
          }
          // Hide Return in PDP
          const returnIndex = data?.blocks?.findIndex(
            (block) =>
              block.name === 'appmaker/expandable-text-block' &&
              block.clientId === '6bc37e4d-6e13-4f89-b953-95668c28c831',
          );
          if (returnIndex && data?.blocks[returnIndex]?.attributes) {
            data.blocks[returnIndex].attributes.__display =
              '<%= !hasTag("offline-exclusive", blockItem.node.tags) %>';
          }
          const checkPinIndex = data?.blocks?.findIndex(
            (block) => block.name === 'appmaker/pin-check',
          );
          if (checkPinIndex >= 0 && data?.blocks[checkPinIndex]?.attributes) {
            data.blocks[checkPinIndex].attributes.__display =
              '<%= !hasTag("offline-exclusive", blockItem.node.tags) %>';
          }

          const productCounterIndex = data?.blocks?.findIndex(
            (block) => block.name === 'appmaker/product-counter',
          );
          if (
            productCounterIndex >= 0 &&
            data?.blocks[checkPinIndex]?.attributes
          ) {
            data.blocks[productCounterIndex].attributes.__display =
              '<%= !hasTag("offline-exclusive", blockItem.node.tags) %>';
          }
          // appmaker/product-counter
        }

        /**
         * For offline products replace 'add to cart' btn with store locator btn
         */
        if (data?.stickyFooter?.blocks?.length) {
          const addTocartIndex = data.stickyFooter?.blocks.findIndex(
            (block) => block.name === 'appmaker/buttons',
          );
          if (addTocartIndex >= 0) {
            data.stickyFooter.blocks[addTocartIndex].attributes.__display =
              '<%= !hasTag("offline-exclusive", blockItem.node.tags) %>';
            data.stickyFooter.blocks[addTocartIndex + 1] = {
              name: 'appmaker/button',
              clientId: 'chumbak-store-locator-btn',
              attributes: {
                __display:
                  '<%= hasTag("offline-exclusive", blockItem.node.tags) %>',
                wholeContainerStyle: {
                  borderRadius: 0,
                  backgroundColor: '#1A2034',
                },
                appmakerAction: {
                  action: 'OPEN_IN_WEBVIEW',
                  params: {
                    url: 'https://www.chumbak.com/pages/store-locator?from_mobile_app=1&inapp=true',
                  },
                },
                content: 'SHOP FROM STORE',
              },
            };
          }
        }

        if (
          data.blocks[index].attributes.title !== 'Reviews' &&
          findBlockIndex(data.blocks, 'chumbak-insider-button') == -1 &&
          settings?.showMembershipPageButtonOnPDP == true
        ) {
          data.blocks.splice(index, 0, {
            name: 'appmaker/chumbak-insider-button',
            clientId: 'chumbak-insider-button',
            attributes: {
              __display:
                '{{!blockItem.product?.tags?.includes("no-insider-benefit") }}',
              appmakerAction: {
                action: 'OPEN_CHUMBAK_INSIDER_PAGE',
              },
            },
          });
        }
        if (
          data.blocks[index].attributes.title !== 'Reviews' &&
          findBlockIndex(data.blocks, 'chumbak-insider-badge') == -1 &&
          settings.displayDiscountedPriceInPDP == true
        ) {
          data.blocks.splice(index, 0, {
            name: 'appmaker/chumbak-insider-badge',
            clientId: 'chumbak-insider-badge',
            attributes: {
              type: 'price-badge',
              specialPrice:
                '<% function amountFinal(a,b) {return ( parseFloat(a) - parseFloat(a) * (parseFloat(b) / 100)).toFixed(0);} %><%= currencyHelper(amountFinal(blockItem.node.priceRange.minVariantPrice.amount,parseFloat(plugins.xeno.settings.discountPercentageOnMembership)),blockItem.node.priceRange.minVariantPrice.currencyCode) %>',
            },
          });
        }
      }

      if (pageId === 'MyAccount') {
        if (user && isCurrentUserChumbakInsider) {
          const foundIndex = findBlockIndex(
            data.blocks,
            'my-account-menu-orders',
          );
          const ifAlreadyIndex = findBlockIndex(
            data.blocks,
            'my-account-menu-xeno-subscription',
          );
          ifAlreadyIndex === -1 &&
            data.blocks.splice(foundIndex, 0, {
              clientId: 'my-account-menu-xeno-subscription',
              name: 'appmaker/actionbar',
              attributes: {
                title: 'View Insider details',
                appmakerAction: {
                  params: {
                    deniedPatterns: ['shopify-login-page'],
                    url: `https://www.chumbak.com/pages/rewards?accessToken=${user.accessToken}&from_mobile_app=1&inapp=true`,
                  },
                  action: 'OPEN_IN_WEBVIEW',
                },
              },
              dependencies: {
                appStorageState: ['user'],
              },
            });
          // data.blocks[foundIndex].attributes.title = 'My Account';
        } else {
          const ifAlreadyExistIndex = findBlockIndex(
            data.blocks,
            'my-account-menu-xeno-subscription',
          );
          if (ifAlreadyExistIndex !== -1) {
            data.blocks.splice(ifAlreadyExistIndex, 1);
          }
        }
      }
      // alert(JSON.stringify(data));
      return data;
    },
  );
}
const Plugin = {
  id: 'xeno',
  name: 'Xeno',
  activate,
};
export default Plugin;
