import { appSettings } from '@appmaker-xyz/core';

const DrawerMenu = {
  toolbarIcon: {
    attributes: {
      enable: true,
    },
  },
  attributes: {
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'shopify/drawer-header',
      clientId: 'drawer-header',
      attributes: {},
      dependencies: {
        appStorageState: ['user'],
      },
    },

    {
      name: 'appmaker/drawer-menu',
      clientId: 'normal-btn',
      attributes: {
        items: '{{blockData.data}}',
        title: 'Thank you page',
        viewSingle: true,
        drawerItem: true,
        featureImg: 'https://img.icons8.com/material/4ac144/256/user-male.png',
        imageResize: 'contain',
        __appmakerAttributes: {
          errorViewAttribute: {
            message: 'Oops !',
          },
          emptyViewAttribute: {
            message: 'Empty',
          },
        },
      },
    },
  ],
  stickyFooter: {
    blocks: [
      {
        name: 'appmaker/actionbar',
        clientId: 'appmaker-app-settings',
        attributes: {
          __display:
            !appSettings.getOptionAsBoolean('enable_currency_switcher') ||
            !appSettings.getOptionAsBoolean('show_language_switcher'),
          appmakerAction: {
            action: 'OPEN_APP_SETTINGS',
          },
          title: 'App Settings',
          leftIcon: 'settings',
          drawerItem: true,
        },
      },
    ],
  },
  dataSource: {
    source: 'shopify',
    attributes: {
      methodName: 'getNavigationMenu',
    },
  },
};

export default DrawerMenu;
