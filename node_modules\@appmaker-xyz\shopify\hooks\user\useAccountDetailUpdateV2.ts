import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { appmaker } from '@appmaker-xyz/core';
import { useState } from 'react';

const schema = z.object({
  email: z
    .string()
    .nonempty("Email can't be empty")
    .email('Invalid email address'),
  first_name: z.string().nonempty("First name can't be empty"),
  last_name: z.string().nonempty("Last name can't be empty"),
  phone: z.string().nonempty("Phone can't be empty"),
});

type Props = {
  defaultValues?: any;
  redirectAction?: any;
  props?: any;
};

export function useAccountDetailUpdateV2({
  defaultValues,
  redirectAction,
  props,
}: Props) {
  console.log('#Props', defaultValues, redirectAction, props);
  const [isLoading, setIsLoading] = useState(false);
  const { blockData, onAction } = props;
  const { control, handleSubmit, formState, setError, setFocus } = useForm({
    resolver: zodResolver(schema),
    ...(defaultValues && { defaultValues }),
  });

  const updateAccountDetails = handleSubmit(async (data) => {
    setIsLoading(true);
    const response = await appmaker.applyFilters(
      'appmaker-submit-account-details',
      {
        onAction,
        redirectAction,
        ...data,
        extraData: {
          ...blockData,
        },
      },
    );
    if (response?.error) {
    const errorMessages = response.error;
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: errorMessages,
        },
      });
    } else {
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'User has been registered successfully',
        },
      });
    }
    setIsLoading(false);
  });

  return {
    control,
    handleSubmit: updateAccountDetails,
    formState,
    setError,
    setFocus,
    isLoading,
    isEmailDisabled: blockData?.email_disabled || false,
    isPhoneDisabled: blockData?.phone_disabled || false,
  };
}
