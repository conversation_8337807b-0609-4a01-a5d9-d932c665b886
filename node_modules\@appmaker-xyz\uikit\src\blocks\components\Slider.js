import React from 'react';
import ImageSwiper from '../components/Swiper/AnySwiper';
import SwiperFlatlist from './Swiper/SwiperFlatlist';
const Slider = ({
  attributes,
  BlockItem,
  BlocksView,
  innerBlocks,
  onPress,
  onAction,
  clientId,
}) => {
  const {
    hidePagination,
    customImageHeight,
    activeDotColor,
    centerPagination,
    renderComponent,
  } = attributes;
  const Component =
    renderComponent === 'SwiperFlatlist' ? SwiperFlatlist : ImageSwiper;
  return (
    <Component
      innerBlocks={innerBlocks}
      attributes={{
        autoplay: true,
        hidePagination: hidePagination,
        customImageHeight: customImageHeight,
        centerPagination: centerPagination,
        activeDotColor: activeDotColor,
        __appmaker_analytics_parent_id: clientId,
        __appmaker_analytics_parent_name:
          attributes?.__appmaker_analytics?.blockLabel,
        auto_swipe_delay: attributes?.auto_swipe_delay,
      }}
      onAction={onAction}
    />
  );
};

export default Slider;
