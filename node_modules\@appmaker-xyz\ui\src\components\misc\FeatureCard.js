import React from 'react';
import { StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { AppImage, AppTouchable, Layout, ThemeText } from '../atoms';

const FeatureCard = ({ onAction, onPress, attributes }) => {
  const {
    title,
    subTitle,
    iconName,
    iconSize,
    imgSrc,
    imgSize,
    appmakerAction,
  } = attributes;

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      onAction(appmakerAction);
    };
  }

  return (
    <AppTouchable onPress={onPressHandle}>
      <Layout style={styles.container}>
        {imgSrc ? (
          <AppImage uri={imgSrc} style={[styles.image, { width: imgSize }]} />
        ) : null}
        {iconName ? (
          <Icon name={iconName} size={parseInt(iconSize) || 18} color="black" />
        ) : null}
        <ThemeText fontFamily="bold" style={styles.center}>
          {title}
        </ThemeText>
        <ThemeText fontFamily="medium" size="sm" style={styles.center}>
          {subTitle}
        </ThemeText>
      </Layout>
    </AppTouchable>
  );
};

const styles = StyleSheet.create({
  container: {
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 24,
    backgroundColor: '#fff',
    marginHorizontal: 0,
    borderRightColor: '#E9EDF1',
    borderRightWidth: 1,
  },
  scroll: {
    flexGrow: 0,
  },
  image: {
    resizeMode: 'contain',
    aspectRatio: 1 / 1,
    marginBottom: 4,
  },
  center: {
    textAlign: 'center',
  },
});

export default FeatureCard;
