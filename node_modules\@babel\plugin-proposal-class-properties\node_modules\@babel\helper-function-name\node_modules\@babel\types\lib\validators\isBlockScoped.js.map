{"version": 3, "names": ["_generated", "require", "_isLet", "isBlockScoped", "node", "isFunctionDeclaration", "isClassDeclaration", "isLet"], "sources": ["../../src/validators/isBlockScoped.ts"], "sourcesContent": ["import { isClassDeclaration, isFunctionDeclaration } from \"./generated\";\nimport isLet from \"./isLet\";\nimport type * as t from \"..\";\n\n/**\n * Check if the input `node` is block scoped.\n */\nexport default function isBlockScoped(node: t.Node): boolean {\n  return isFunctionDeclaration(node) || isClassDeclaration(node) || isLet(node);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAMe,SAASE,aAAaA,CAACC,IAAY,EAAW;EAC3D,OAAO,IAAAC,gCAAqB,EAACD,IAAI,CAAC,IAAI,IAAAE,6BAAkB,EAACF,IAAI,CAAC,IAAI,IAAAG,cAAK,EAACH,IAAI,CAAC;AAC/E"}