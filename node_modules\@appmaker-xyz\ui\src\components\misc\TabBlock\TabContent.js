import React from 'react';

function TabContent({ block, BlocksView, onAction, currentAction }) {
  const { attributes } = block;
  const { pageId, actionParams } = attributes;
  return (
    <BlocksView
      onAction={onAction}
      currentAction={actionParams || currentAction}
      inAppPage={{
        blocks: [
          {
            attributes: {
              pageId,
            },
            name: 'appmaker/in-app-page',
            innerBlocks: [],
            clientId: 'product-list',
            isValid: true,
          },
        ],
      }}
    />
  );
}

export default React.memo(TabContent);
