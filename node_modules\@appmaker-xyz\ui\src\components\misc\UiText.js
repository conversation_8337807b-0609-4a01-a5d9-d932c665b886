import React from 'react';
import { I18nManager } from 'react-native';
import { ThemeText } from '../atoms';
import { isEmpty } from 'lodash';

const UiText = ({ attributes }) => {
  if (I18nManager.isRTL && attributes.splitOnRtlString) {
    const contentArray = attributes?.content?.split(
      attributes.splitOnRtlString,
    );
    return (
      <ThemeText
        style={attributes?.style}
        size={attributes?.size}
        numberOfLines={attributes?.numberOfLines}
        color={attributes?.fontColor}>
        {contentArray?.map?.((content, index) => {
          return (
            <ThemeText
              key={index}
              style={attributes?.style}
              size={attributes?.size}
              numberOfLines={attributes?.numberOfLines}
              color={attributes?.fontColor}>
              {content}{' '}
            </ThemeText>
          );
        })}
      </ThemeText>
    );
  }

  if (isEmpty(attributes.content)) {
    return null;
  }

  return (
    <ThemeText
      style={attributes?.style}
      size={attributes?.size}
      numberOfLines={attributes?.numberOfLines}
      color={attributes?.fontColor}>
      {attributes.content}
    </ThemeText>
  );
};

export default UiText;
