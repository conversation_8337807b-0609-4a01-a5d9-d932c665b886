import React from 'react';
import { useState } from 'react';
import { View, StyleSheet, Pressable } from 'react-native';
import { testProps } from '@appmaker-xyz/core';
import { ThemeText } from '@appmaker-xyz/ui';

const RadioItem = (props) => {
  const { label, value, color, activeSort, textStyles } = props;
  const isActive =
    activeSort === value || (activeSort?.id && activeSort?.id === value?.id);
  const handlePress = () => {
    props?.onPress && props?.onPress(value, label);
  };

  const themeColor = color || '#000000';

  const borderStyle = {
    borderColor: isActive ? themeColor : `${themeColor}40`,
  };

  const activeBg = { backgroundColor: themeColor };

  return (
    <Pressable
      {...testProps(props?.testId)}
      style={styles.radioItem}
      onPress={handlePress}>
      <View style={[styles.radioCircle, borderStyle]}>
        {isActive ? <View style={[styles.selectedRb, activeBg]} /> : null}
      </View>
      <ThemeText style={[styles.radioText, textStyles]}>{label}</ThemeText>
    </Pressable>
  );
};

const Radio = (props) => {
  const { color, options, onPress, initial, selectedSort, textStyles } = props;
  const [activeSort, setActiveSort] = useState(
    selectedSort || options?.[0]?.value || '',
  );
  const onSelect = (value, label) => {
    setActiveSort(value);
    onPress && onPress(value, label);
  };
  if (options?.length && options?.length > 0) {
    return options.map((item, id) => {
      return (
        <RadioItem
          testId={`sort-option-${id}`}
          key={item.id || id}
          activeSort={activeSort}
          {...item}
          color={color}
          textStyles={textStyles}
          onPress={onSelect}
        />
      );
    });
  }

  return null;
};

const styles = StyleSheet.create({
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingVertical: 6,
  },
  radioCircle: {
    height: 20,
    width: 20,
    borderRadius: 100,
    borderWidth: 1.6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedRb: {
    width: 12,
    height: 12,
    borderRadius: 50,
  },

  radioText: {
    marginLeft: 8,
    fontSize: 16,
    lineHeight: 18,
  },
});

export default Radio;
