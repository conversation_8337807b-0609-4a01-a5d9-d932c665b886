import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  ScrollView,
  TouchableWithoutFeedback,
  Alert,
} from 'react-native';
import Modal from 'react-native-modal';
import Icon from 'react-native-vector-icons/Feather';
import { usePageState, appStorageApi } from '@appmaker-xyz/core';
import Snackbar from 'react-native-snackbar';
import {
  Layout,
  ThemeText,
  UiKitButton,
  AppTouchable,
  AppImage,
} from '@appmaker-xyz/ui';
import axios from 'axios';
import { shopifyIdHelper } from '@appmaker-xyz/shopify';

export async function apiProductStatus(baseUrl, token, orderId, blockItem) {
  const { id, email } = appStorageApi().getState()?.user;
  const customerId = shopifyIdHelper(id, true);
  try {
    // 4584186773667
    var config = {
      method: 'get',
      url: `${baseUrl}/order-module/getShopiy/ProductStatus/IN-${orderId}`,
      headers: {
        authority: 'uat.bombayshirts.com',
        accept: 'application/json, text/javascript, */*; q=0.01',
        'accept-language': 'en-US,en;q=0.9',
        origin: 'https://bombayshirt-india.myshopify.com',
        referer: 'https://bombayshirt-india.myshopify.com/',
        requesttoken: `${token}`,
        'sec-ch-ua':
          '".Not/A)Brand";v="99", "Google Chrome";v="103", "Chromium";v="103"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        Cookie: 'JSESSIONID=9DEC90C24B32129BB881179126888BC4',
      },
    };
    return axios(config);
  } catch (error) {
    return { displayButton: false, fitSaved: false };
  }
}

const ConfirmFit = ({ attributes, onPress, onAction, blockData }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [checkedList, setCheckedList] = useState([]);
  const [viewPop, setViewPop] = useState(false);
  let totalSelectable = 0;
  const { appmakerAction, pluginSettings } = attributes;
  const blockItem = attributes?.blockItem?.node;
  let orderId = blockItem.id;
  const [productStatus, setProductStatus] = useState(null);
  const [productLineItem, setproductLineItem] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [vendors, setVendors] = useState([]);

  const setpageState = usePageState((state) => state.setState);

  const setProductData = () => {
    let newProductLineItem = [];
    if (productStatus && productStatus.length > 0) {
      blockData.node.lineItems.edges.map((item, key) => {
        let showOr = productStatus.filter(
          (i) => i.sku === item.node?.variant?.sku,
        );
        if (showOr.length > 0) {
          newProductLineItem.push({
            ...item,
            confirmId: showOr[0].id,
            shippingDate: showOr[0].shippingDate,
            status: showOr[0].status,
            isTrial: showOr[0].isTrial,
            isTrialConfirmed: showOr[0].isTrialConfirmed,
            message: showOr[0].message,
          });
        }
      });
      setpageState((state) => {
        state.productStatus = [...newProductLineItem];
      });
      return newProductLineItem;
    } else {
      return [];
    }
  };
  // let productLineItem = [];
  useEffect(() => {
    try {
      orderId = shopifyIdHelper(orderId, true);
      const getProductData = async () => {
        if (pluginSettings?.order_product_status_domain_url) {
          const data = await apiProductStatus(
            pluginSettings.order_product_status_domain_url,
            pluginSettings.order_product_status_token,
            orderId,
            blockItem,
          );
          if (data?.data?.success) {
            setProductStatus(data.data.data);
          }
        }
      };
      getProductData();
    } catch (error) {
      console.log(error);
    }
  }, [blockItem, modalVisible]);

  useEffect(() => {
    let newLineItems = setProductData();
    newLineItems && setproductLineItem(newLineItems);
  }, [productStatus]);

  useEffect(() => {
    if (
      blockData &&
      blockData.node?.lineItems?.edges &&
      blockData.node?.lineItems?.edges.length > 0
    ) {
      let vendorsArr = blockData.node?.lineItems?.edges.map((v) => {
        if (v.node?.variant && v.node?.variant?.product) {
          if (v.node.variant.product?.productType === 'Gift Card') {
            return v.node.variant.product.productType;
          } else {
            return v.node.variant.product?.vendor;
          }
        }
      });
      setVendors(vendorsArr);
    }
  }, [blockData]);
  let productId = '';

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  const ProductCheckBox = productLineItem
    ? productLineItem.map((item, key) => {
        const { node, confirmId, isTrial, isTrialConfirmed, status } = item;
        // if (productStatus && productStatus.length > 0) {
        //   let showOr = productStatus.filter((i) => i.sku === node.variant.sku);
        // }
        const contains =
          node?.variant?.product?.id &&
          checkedList.includes(node?.variant?.product?.id);
        //check if an array contains an element
        if (
          isTrial &&
          !isTrialConfirmed &&
          node?.variant?.product?.id &&
          (status.toLowerCase() === 'shipped' ||
            status.toLowerCase() === 'delivered' ||
            status.toLowerCase() === 'ready at store')
        ) {
          productId = node?.variant?.product?.id;
          try {
            productId = shopifyIdHelper(productId, true);
          } catch (error) {
            console.log(error);
          }
          totalSelectable = totalSelectable + 1;
          return (
            <AppTouchable
              key={key}
              style={styles.cardContainer}
              onPress={() => {
                // add an item to an array
                if (!contains) {
                  node?.variant?.product?.id &&
                    setCheckedList([...checkedList, node.variant.product.id]);
                } else {
                  // remove an item from an array
                  setCheckedList(
                    checkedList.filter(
                      (item) => item !== node.variant.product.id,
                    ),
                  );
                }
              }}>
              <Layout
                style={[
                  styles.checkBox,
                  {
                    backgroundColor: contains ? '#1B1B1B' : '#FFFFFF',
                    borderColor: contains ? '#1B1B1B' : '#4F4F4F',
                  },
                ]}>
                <Icon name="check" size={18} color="#ffffff" />
              </Layout>
              <AppImage
                uri={node?.variant?.image?.originalSrc}
                style={styles.cardImage}
              />
              <Layout>
                <ThemeText size="sm" color="#A9AEB7">
                  {node?.variant?.product?.vendor}
                </ThemeText>
                <ThemeText>{node?.title}</ThemeText>
                <ThemeText>{`${node?.discountedTotalPrice?.currencyCode} ${node?.discountedTotalPrice?.amount}`}</ThemeText>
              </Layout>
            </AppTouchable>
          );
        }
        return null;
      })
    : null;

  const showWarning = () => {
    Alert.alert(
      '',
      `Confirm fit for ${
        checkedList.length > 1
          ? checkedList.length + ' products'
          : checkedList.length + ' product'
      } ?`,
      [
        {
          text: 'Cancel',
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: 'OK',
          onPress: () => {
            confirmProduct();
          },
          style: 'ok',
        },
      ],
      {
        cancelable: true,
      },
    );
  };

  const confirmProduct = () => {
    setIsLoading(true);
    let confirmIds = [];
    checkedList.map((item, key) => {
      let filterData = productLineItem.filter(
        (v) => v.node.variant?.product?.id === item,
      );
      confirmIds.push(filterData[0].confirmId);
    });
    confirmAPI(
      pluginSettings.order_product_status_domain_url,
      pluginSettings.order_product_status_token,
      confirmIds,
      orderId,
    );
  };
  const confirmAPI = async (baseUrl, token, confirmIds, orderId) => {
    const stringArray = JSON.stringify(confirmIds);
    var config = {
      method: 'put',
      url: `${baseUrl}/order-module/order-product/confirmTrial?remoteOrderNumber=${blockData.node.name}`,
      headers: {
        authority: 'uat.bombayshirts.com',
        accept: 'application/json, text/javascript, */*; q=0.01',
        'accept-language': 'en-US,en;q=0.9',
        'content-type': 'application/json',
        origin: 'https://bombayshirt-india.myshopify.com',
        referer: 'https://bombayshirt-india.myshopify.com/',
        requesttoken: `${token}`,
        'sec-ch-ua':
          '"Chromium";v="104", " Not A;Brand";v="99", "Google Chrome";v="104"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        Cookie: 'JSESSIONID=691968B97A13ECC6770FC167F6771031',
      },
      data: stringArray,
    };
    const resp = await axios(config);
    setIsLoading(false);
    if (resp?.data?.success) {
      setTimeout(() => {
        Snackbar.show({
          text: 'Trial successfully confirmed',
          duration: Snackbar.LENGTH_LONG,
        });
      }, 200);
    } else {
      setTimeout(() => {
        Snackbar.show({
          text: 'Something went wrong, please try again',
          duration: Snackbar.LENGTH_LONG,
        });
      }, 200);
    }
    setModalVisible(!modalVisible);
  };
  const onPressAlteration = () => {
    onAction &&
      onAction({
        action: 'OPEN_URL',
        params: {
          url: 'https://api.whatsapp.com/send?phone=+918879621003&text=Hi%2C%20I%20want%20to%20alter%20my%20garment%20please%20get%20in%20touch!%20',
        },
      });
    setViewPop(!viewPop);
  };
  const onPressExchange = () => {
    onAction &&
      onAction({
        action: 'OPEN_URL',
        params: {
          url: 'https://api.whatsapp.com/send?phone=+918879621003&text=Hi%2C%20I%20want%20to%20exchange%20my%20garment%20please%20get%20in%20touch!%20',
        },
      });
    setViewPop(!viewPop);
  };
  const onPressAssistance = () => {
    onAction &&
      onAction({
        action: 'OPEN_URL',
        params: {
          url: 'https://api.whatsapp.com/send?phone=+918879621003&text=Hi%2C%20I%20have%20a%20query%20regarding%20my%20order%2C%20please%20get%20in%20touch!%20',
        },
      });
    setViewPop(!viewPop);
  };

  return (
    <Layout style={styles.actionContainer}>
      {productLineItem &&
        productLineItem.length > 0 &&
        productLineItem.filter(
          (i) =>
            i.isTrial &&
            !i.isTrialConfirmed &&
            (i.status.toLowerCase() === 'shipped' ||
              i.status.toLowerCase() === 'delivered' ||
              i.status.toLowerCase() === 'ready at store'),
        ).length > 0 && (
          <UiKitButton
            small
            baseSize
            status="dark"
            onPress={() => setModalVisible(true)}
            wholeContainerStyle={styles.confirmButton}>
            Confirm Fit
          </UiKitButton>
        )}
      <Layout style={styles.raiseRequestContainer}>
        {/* {true ? (
          <Layout style={styles.selectPop}>
            <AppTouchable style={styles.selectItem} onPress={onPressRequest}>
              <ThemeText>Request alteration</ThemeText>
            </AppTouchable>
            <AppTouchable style={styles.selectItem}>
              <ThemeText>Request exchange</ThemeText>
            </AppTouchable>
            <AppTouchable style={styles.selectItem}>
              <ThemeText>Get assistance</ThemeText>
            </AppTouchable>
          </Layout>
        ) : null} */}
        <UiKitButton
          small
          baseSize
          // outline
          status="dark"
          onPress={() => setViewPop(!viewPop)}
          wholeContainerStyle={styles.riseButton}
          accessoryRight={() => (
            <Icon
              name={viewPop ? 'chevron-up' : 'chevron-down'}
              size={18}
              color="#fff"
            />
          )}>
          Raise a request
        </UiKitButton>
        <Modal
          animationType="fade"
          transparent={true}
          visible={viewPop}
          onRequestClose={() => {
            setViewPop(!viewPop);
          }}>
          <TouchableWithoutFeedback onPress={() => setViewPop(!viewPop)}>
            <Layout style={styles.modalOverlay} />
          </TouchableWithoutFeedback>
          <Layout style={styles.selectPop}>
            {vendors &&
              vendors.length > 0 &&
              (vendors.includes('Korra') ||
                vendors.includes('Bombay Shirt Company')) && (
                <AppTouchable
                  style={styles.selectItem}
                  onPress={onPressAlteration}>
                  <ThemeText>Request alteration</ThemeText>
                </AppTouchable>
              )}
            {vendors &&
              vendors.length > 0 &&
              (vendors.includes('cityof_') || vendors.includes('pause')) && (
                <AppTouchable
                  style={styles.selectItem}
                  onPress={onPressExchange}>
                  <ThemeText>Request exchange</ThemeText>
                </AppTouchable>
              )}
            <AppTouchable style={styles.selectItem} onPress={onPressAssistance}>
              <ThemeText>Get assistance</ThemeText>
            </AppTouchable>
          </Layout>
        </Modal>
      </Layout>
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => {
          setModalVisible(!modalVisible);
        }}>
        <TouchableWithoutFeedback
          onPress={() => setModalVisible(!modalVisible)}>
          <Layout style={styles.modalOverlay} />
        </TouchableWithoutFeedback>
        <Layout style={styles.centeredView}>
          <Layout style={styles.modalView}>
            <Layout style={styles.modalContent}>
              <Layout style={styles.titleContainer}>
                <ThemeText size="xl" fontFamily="bold" style={styles.cardTitle}>
                  Confirm fit
                </ThemeText>
                <Icon
                  name="x"
                  size={20}
                  color={'#4F4F4F'}
                  onPress={() => setModalVisible(!modalVisible)}
                />
              </Layout>
              <ThemeText style={styles.cardTitle}>
                Lorem Ipsum is simply dummy text of the printing and typesetting
                industry. Lore Ipsum has been the industry's standard dummy text
                ever since the 1500s
              </ThemeText>
            </Layout>
            <ScrollView style={styles.scrollview}>{ProductCheckBox}</ScrollView>
            <UiKitButton
              status="dark"
              loading={isLoading}
              disabled={checkedList.length === 0}
              onPress={() => showWarning()}
              wholeContainerStyle={styles.modalButton}>
              {`Confirm fit ${checkedList.length} of ${totalSelectable} products)`}
            </UiKitButton>
          </Layout>
        </Layout>
      </Modal>
    </Layout>
  );
};

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 6,
    backgroundColor: '#ffffff',
    justifyContent: 'flex-end',
  },
  modalContent: {
    justifyContent: 'flex-start',
    marginBottom: 12,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  confirmButton: {
    // borderRadius: 24,
    // paddingHorizontal: 24,
    // marginRight: 12,
    // paddingVertical: 8,
    borderRadius: 10,
    marginLeft: 6,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  riseButton: {
    // borderRadius: 24,
    // paddingHorizontal: 24,
    // paddingVertical: 8,
    borderRadius: 10,
    marginLeft: 6,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  scrollview: { maxHeight: 300 },
  checkBox: {
    height: 24,
    width: 24,
    marginRight: 4 + 6,
    borderWidth: 4 / 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardContainer: {
    flexDirection: 'row',
    borderBottomColor: '#E9EDF1',
    paddingBottom: 12,
    borderBottomWidth: 1,
  },
  cardTitle: {
    marginBottom: 6,
  },
  cardImage: { height: 120, width: 80, marginHorizontal: 12 },
  modalButton: {
    borderRadius: 24,
    paddingVertical: 8,
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0)',
  },
  raiseRequestContainer: {
    position: 'relative',
    paddingVertical: 12,
    // justifyContent: 'flex-end',
    // flexDirection: 'row',
    // width: '100%',
  },
  selectPop: {
    position: 'absolute',
    top: 90,
    right: 0,
    backgroundColor: '#fff',
    borderRadius: 6,
    width: '55%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  selectItem: {
    padding: 10,
    borderBottomColor: '#E9EDF1',
    borderBottomWidth: 1,
  },
  selectModal: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
});

export default ConfirmFit;
