import React from 'react';
import { StyleSheet, ActivityIndicator } from 'react-native';
import { ThemeText, AppTouchable } from '../atoms';
import Icon from 'react-native-vector-icons/Feather';

const Button = ({
  onPress,
  title,
  icon = '',
  containerStyles = {},
  isLoading = false,
}) => {
  return (
    <AppTouchable
      style={{ ...styles.addButton, ...containerStyles }}
      onPress={onPress}>
      {!icon ? null : (
        <Icon name={icon} size={24} color="#fff" style={styles.plusIcon} />
      )}
      {!isLoading ? (
        <ThemeText size="lg" color="#fff">
          {title || ''}
        </ThemeText>
      ) : (
        <ActivityIndicator size="small" color="#fff" />
      )}
    </AppTouchable>
  );
};
export default Button;
const styles = StyleSheet.create({
  addButton: {
    flexDirection: 'row',
    backgroundColor: '#1E232C',
    marginHorizontal: 12,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 6,
  },
  plusIcon: {
    marginRight: 6,
  },
});
