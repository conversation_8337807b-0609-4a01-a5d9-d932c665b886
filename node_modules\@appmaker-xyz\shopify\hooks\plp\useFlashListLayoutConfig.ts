import { usePageState } from '@appmaker-xyz/core';
type LayoutConfig = {
  id: string;
  numColumns: number;
  spanEvery?: number;
  spanFirstItem?: boolean;
}
const useFlashListLayoutConfig = () => {
  const setPageState = usePageState((state) => state.setPageState);
  const layoutConfig = usePageState((state) => state.flashLayoutConfig);

  const setLayoutConfig = (layoutConfig: LayoutConfig) => {
    setPageState({
      flashLayoutConfig: layoutConfig,
    });
  }
  return {
    setLayoutConfig,
    layoutConfig,
  }
}

export { 
  useFlashListLayoutConfig
 };
