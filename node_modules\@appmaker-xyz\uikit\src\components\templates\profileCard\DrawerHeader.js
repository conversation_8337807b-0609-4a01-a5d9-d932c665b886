import React from 'react';
import { StyleSheet, ImageBackground } from 'react-native';
import { Layout, AppmakerText, Button, AppImage } from '../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { useApThemeState } from '../../../theme/ThemeContext';

const DrawerHeader = ({ attributes, onPress, onAction, clientId }) => {
  const {
    userName,
    bgImg,
    userImg,
    showLoginRegister,
    appmakerAction,
    textColor,
    user,
  } = attributes;
  const { color, spacing, font } = useApThemeState();
  const styles = allStyles({ color, spacing });
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  return (
    <Layout style={styles.container}>
      <ImageBackground style={styles.bgImage} source={bgImg}>
        {showLoginRegister && (
          <Button
            clientId={'-my-account'}
            link
            status="dark"
            style={styles.flexOne}
            onPress={() =>
              user &&
              Object.keys(user).length !== 0 &&
              user.constructor === Object
                ? onAction({ action: 'OPEN_MY_ACCOUNT' })
                : onAction({ action: 'OPEN_LOGIN_PAGE' })
            }>
            <Layout style={styles.userData}>
              <AppImage
                src={require('./img/user_placeholder.png')}
                uri={userImg && userImg}
                style={styles.profileImg}
              />
              <Layout style={styles.flexOne}>
                {user && // 👈 null and undefined check
                Object.keys(user).length !== 0 &&
                user.constructor === Object ? (
                  <Layout
                    clientId={'-my-account'}
                    link
                    status="dark"
                    style={styles.flexOne}>
                    {/* onPress={() => onAction({ action: 'OPEN_MY_ACCOUNT' })}> */}
                    <AppmakerText
                      style={{ color: textColor }}
                      numberOfLines={1}
                      category="buttonText">
                      {userName}
                    </AppmakerText>
                    <AppmakerText
                      style={{ color: textColor }}
                      status="demiDark"
                      category="highlighter1">
                      My Account
                      <Icon name="chevron-right" />
                    </AppmakerText>
                  </Layout>
                ) : (
                  <Layout clientId={'-login'} link status="dark">
                    {/* style={styles.flexOne}> */}
                    {/* onPress={() => onAction({ action: 'OPEN_LOGIN_PAGE' })}> */}
                    <AppmakerText
                      style={{ color: textColor }}
                      numberOfLines={1}
                      category="buttonText">
                      Login | Register
                    </AppmakerText>
                  </Layout>
                )}
              </Layout>
            </Layout>
          </Button>
        )}
      </ImageBackground>
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.light,
      height: 100,
    },
    bgImage: {
      flex: 1,
      resizeMode: 'cover',
      justifyContent: 'center',
      paddingHorizontal: spacing.base,
      paddingVertical: spacing.md,
    },
    userData: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    profileImg: {
      width: 50,
      height: 50,
      marginRight: 8,
      borderRadius: 4,
      overflow: 'hidden',
    },
    notLogged: {},
    flexOne: {
      flex: 1,
    },
  });

export default DrawerHeader;
