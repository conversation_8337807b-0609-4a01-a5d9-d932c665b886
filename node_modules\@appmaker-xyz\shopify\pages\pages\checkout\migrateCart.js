import { color, spacing } from '../../styles';

const pickAddress = {
  type: 'normal',
  title: 'Shipping Address',
  attributes: {
    showLoadingTillData: true,
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/layout',
      attributes: {
        style: {
          flexDirection: 'row',
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/button',
          attributes: {
            content: 'Migrate',

            appmakerAction: {
              action: 'MIGRAGE_CART',
            },
          },
        },
      ],
    },
  ],
};
export default pickAddress;
