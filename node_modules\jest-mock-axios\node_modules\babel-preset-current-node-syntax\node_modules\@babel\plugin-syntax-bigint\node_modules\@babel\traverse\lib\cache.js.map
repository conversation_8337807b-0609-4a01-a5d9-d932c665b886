{"version": 3, "names": ["pathsCache", "WeakMap", "exports", "path", "scope", "clear", "clear<PERSON>ath", "clearScope", "nullHub", "Object", "freeze", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hub", "parent", "_pathsCache$get", "_hub", "get", "getOrCreateCachedPaths", "_hub2", "_hub3", "parents", "set", "paths", "Map"], "sources": ["../src/cache.ts"], "sourcesContent": ["import type { Node } from \"@babel/types\";\nimport type Node<PERSON>ath from \"./path/index.ts\";\nimport type Scope from \"./scope/index.ts\";\nimport type { HubInterface } from \"./hub.ts\";\n\nlet pathsCache: WeakMap<\n  HubInterface | typeof nullHub,\n  WeakMap<Node, Map<Node, NodePath>>\n> = new WeakMap();\nexport { pathsCache as path };\nexport let scope: WeakMap<Node, Scope> = new WeakMap();\n\nexport function clear() {\n  clearPath();\n  clearScope();\n}\n\nexport function clearPath() {\n  pathsCache = new WeakMap();\n}\n\nexport function clearScope() {\n  scope = new WeakMap();\n}\n\n// NodePath#hub can be null, but it's not a valid weakmap key because it\n// cannot be collected by GC. Use an object, knowing tht it will not be\n// collected anyway. It's not a memory leak because pathsCache.get(nullHub)\n// is itself a weakmap, so its entries can still be collected.\nconst nullHub = Object.freeze({} as const);\n\nexport function getCachedPaths(hub: HubInterface | null, parent: Node) {\n  if (!process.env.BABEL_8_BREAKING) {\n    // Only use Hu<PERSON> as part of the cache key in Babel 8, because it is a\n    // breaking change (it causes incompatibilities with older `@babel/core`\n    // versions: see https://github.com/babel/babel/pull/15759)\n    hub = null;\n  }\n  return pathsCache.get(hub ?? nullHub)?.get(parent);\n}\n\nexport function getOrCreateCachedPaths(hub: HubInterface | null, parent: Node) {\n  if (!process.env.BABEL_8_BREAKING) {\n    hub = null;\n  }\n\n  let parents = pathsCache.get(hub ?? nullHub);\n  if (!parents) pathsCache.set(hub ?? nullHub, (parents = new WeakMap()));\n\n  let paths = parents.get(parent);\n  if (!paths) parents.set(parent, (paths = new Map()));\n\n  return paths;\n}\n"], "mappings": ";;;;;;;;;;;AAKA,IAAIA,UAGH,GAAG,IAAIC,OAAO,CAAC,CAAC;AAACC,OAAA,CAAAC,IAAA,GAAAH,UAAA;AAEX,IAAII,KAA2B,GAAG,IAAIH,OAAO,CAAC,CAAC;AAACC,OAAA,CAAAE,KAAA,GAAAA,KAAA;AAEhD,SAASC,KAAKA,CAAA,EAAG;EACtBC,SAAS,CAAC,CAAC;EACXC,UAAU,CAAC,CAAC;AACd;AAEO,SAASD,SAASA,CAAA,EAAG;EAC1BJ,OAAA,CAAAC,IAAA,GAAAH,UAAU,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC5B;AAEO,SAASM,UAAUA,CAAA,EAAG;EAC3BL,OAAA,CAAAE,KAAA,GAAAA,KAAK,GAAG,IAAIH,OAAO,CAAC,CAAC;AACvB;AAMA,MAAMO,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAU,CAAC;AAEnC,SAASC,cAAcA,CAACC,GAAwB,EAAEC,MAAY,EAAE;EAAA,IAAAC,eAAA,EAAAC,IAAA;EAClC;IAIjCH,GAAG,GAAG,IAAI;EACZ;EACA,QAAAE,eAAA,GAAOd,UAAU,CAACgB,GAAG,EAAAD,IAAA,GAACH,GAAG,YAAAG,IAAA,GAAIP,OAAO,CAAC,qBAA9BM,eAAA,CAAgCE,GAAG,CAACH,MAAM,CAAC;AACpD;AAEO,SAASI,sBAAsBA,CAACL,GAAwB,EAAEC,MAAY,EAAE;EAAA,IAAAK,KAAA,EAAAC,KAAA;EAC1C;IACjCP,GAAG,GAAG,IAAI;EACZ;EAEA,IAAIQ,OAAO,GAAGpB,UAAU,CAACgB,GAAG,EAAAE,KAAA,GAACN,GAAG,YAAAM,KAAA,GAAIV,OAAO,CAAC;EAC5C,IAAI,CAACY,OAAO,EAAEpB,UAAU,CAACqB,GAAG,EAAAF,KAAA,GAACP,GAAG,YAAAO,KAAA,GAAIX,OAAO,EAAGY,OAAO,GAAG,IAAInB,OAAO,CAAC,CAAE,CAAC;EAEvE,IAAIqB,KAAK,GAAGF,OAAO,CAACJ,GAAG,CAACH,MAAM,CAAC;EAC/B,IAAI,CAACS,KAAK,EAAEF,OAAO,CAACC,GAAG,CAACR,MAAM,EAAGS,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAE,CAAC;EAEpD,OAAOD,KAAK;AACd"}