import React from 'react';
import { StyleSheet, ScrollView, SafeAreaView } from 'react-native';
import {
  Login,
  SignUp,
  OtpLogin,
  ForgotPassword,
  GroceryCart,
  Account,
  Layout,
  InitialScreen,
} from '../components';
import { color, spacing } from '../styles';

const SocialIcon = ({ name }) => <Icon name={name || 'facebook'} size={16} />;
const metaData = [
  {
    id: '1',
    title: 'meta item 1 meta item 1 ',
    iconName: 'bell',
  },
  {
    id: '2',
    title: 'meta item 2',
    iconName: 'clock',
  },
  {
    id: '3',
    title: 'meta item 3',
    iconName: 'x',
  },
  {
    id: '4',
    title: 'meta item 4',
    iconName: 'user',
  },
  {
    id: '5',
    title: 'meta item 5',
    iconName: 'airplay',
  },
];

const tableData = [
  {
    id: '1',
    title: 'Mobile Number',
    value: '+************',
    iconName: 'phone',
  },
  {
    id: '2',
    title: 'Email Id',
    value: '<EMAIL>',
    iconName: 'mail',
  },
  {
    id: '3',
    title: 'Address',
    value: 'PKM HOUSE, Chemmad, Malappuram Dist.',
    iconName: 'map-pin',
  },
  {
    id: '3',
    title: 'Facebook',
    value: 'saleeh',
    iconName: 'facebook',
  },
];

const socialData = [
  {
    id: '1',
    title: 'Facebook',
  },
  {
    id: '2',
    title: 'Dribbble',
  },
  {
    id: '3',
    title: 'Linkedin',
  },
];
const LoginScreen = () => {
  return (
    <>
      <Login />
      {/* <SignUp /> */}
      {/* <OtpLogin /> */}
      {/* <ForgotPassword /> */}
      {/* <GroceryCart /> */}
      {/* <Account /> */}
      {/* <InitialScreen /> */}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: color.light,
    padding: spacing.base,
  },
});

export default LoginScreen;
