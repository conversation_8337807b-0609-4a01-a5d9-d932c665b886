{"version": 3, "names": ["_t", "require", "isExportDefaultDeclaration", "isExportNamedDeclaration", "ClassDeclaration", "node", "parent", "inExport", "_shouldPrintDecoratorsBeforeExport", "printJoin", "decorators", "declare", "word", "space", "abstract", "id", "print", "typeParameters", "superClass", "superTypeParameters", "implements", "printList", "body", "ClassBody", "token", "length", "newline", "printSequence", "indent", "endsWith", "rightBrace", "ClassProperty", "_node$key$loc", "endLine", "key", "loc", "end", "line", "catchUp", "tsPrintClassMemberModifiers", "computed", "_variance", "optional", "definite", "typeAnnotation", "value", "semicolon", "ClassAccessorProperty", "_node$key$loc2", "ClassPrivateProperty", "static", "ClassMethod", "_classMethodHead", "ClassPrivateMethod", "_node$key$loc3", "_methodHead", "StaticBlock"], "sources": ["../../src/generators/classes.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport {\n  isExportDefaultDeclaration,\n  isExportNamedDeclaration,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport * as charCodes from \"charcodes\";\n\nexport function ClassDeclaration(\n  this: Printer,\n  node: t.ClassDeclaration,\n  parent: t.Node,\n) {\n  const inExport =\n    isExportDefaultDeclaration(parent) || isExportNamedDeclaration(parent);\n\n  if (\n    !inExport ||\n    !this._shouldPrintDecoratorsBeforeExport(\n      parent as t.ExportDeclaration & { declaration: t.ClassDeclaration },\n    )\n  ) {\n    this.printJoin(node.decorators, node);\n  }\n\n  if (node.declare) {\n    // TS\n    this.word(\"declare\");\n    this.space();\n  }\n\n  if (node.abstract) {\n    // TS\n    this.word(\"abstract\");\n    this.space();\n  }\n\n  this.word(\"class\");\n\n  if (node.id) {\n    this.space();\n    this.print(node.id, node);\n  }\n\n  this.print(node.typeParameters, node);\n\n  if (node.superClass) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.print(node.superClass, node);\n    this.print(node.superTypeParameters, node);\n  }\n\n  if (node.implements) {\n    this.space();\n    this.word(\"implements\");\n    this.space();\n    this.printList(node.implements, node);\n  }\n\n  this.space();\n  this.print(node.body, node);\n}\n\nexport { ClassDeclaration as ClassExpression };\n\nexport function ClassBody(this: Printer, node: t.ClassBody) {\n  this.token(\"{\");\n  if (node.body.length === 0) {\n    this.token(\"}\");\n  } else {\n    this.newline();\n\n    this.printSequence(node.body, node, { indent: true });\n\n    if (!this.endsWith(charCodes.lineFeed)) this.newline();\n\n    this.rightBrace(node);\n  }\n}\n\nexport function ClassProperty(this: Printer, node: t.ClassProperty) {\n  this.printJoin(node.decorators, node);\n\n  // catch up to property key, avoid line break\n  // between member modifiers and the property key.\n  const endLine = node.key.loc?.end?.line;\n  if (endLine) this.catchUp(endLine);\n\n  this.tsPrintClassMemberModifiers(node);\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(node.key, node);\n    this.token(\"]\");\n  } else {\n    this._variance(node);\n    this.print(node.key, node);\n  }\n\n  // TS\n  if (node.optional) {\n    this.token(\"?\");\n  }\n  if (node.definite) {\n    this.token(\"!\");\n  }\n\n  this.print(node.typeAnnotation, node);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value, node);\n  }\n  this.semicolon();\n}\n\nexport function ClassAccessorProperty(\n  this: Printer,\n  node: t.ClassAccessorProperty,\n) {\n  this.printJoin(node.decorators, node);\n\n  // catch up to property key, avoid line break\n  // between member modifiers and the property key.\n  const endLine = node.key.loc?.end?.line;\n  if (endLine) this.catchUp(endLine);\n\n  // TS does not support class accessor property yet\n  this.tsPrintClassMemberModifiers(node);\n\n  this.word(\"accessor\", true);\n  this.space();\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(node.key, node);\n    this.token(\"]\");\n  } else {\n    // Todo: Flow does not support class accessor property yet.\n    this._variance(node);\n    this.print(node.key, node);\n  }\n\n  // TS\n  if (node.optional) {\n    this.token(\"?\");\n  }\n  if (node.definite) {\n    this.token(\"!\");\n  }\n\n  this.print(node.typeAnnotation, node);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value, node);\n  }\n  this.semicolon();\n}\n\nexport function ClassPrivateProperty(\n  this: Printer,\n  node: t.ClassPrivateProperty,\n) {\n  this.printJoin(node.decorators, node);\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  this.print(node.key, node);\n  this.print(node.typeAnnotation, node);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value, node);\n  }\n  this.semicolon();\n}\n\nexport function ClassMethod(this: Printer, node: t.ClassMethod) {\n  this._classMethodHead(node);\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function ClassPrivateMethod(this: Printer, node: t.ClassPrivateMethod) {\n  this._classMethodHead(node);\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function _classMethodHead(\n  this: Printer,\n  node: t.ClassMethod | t.ClassPrivateMethod | t.TSDeclareMethod,\n) {\n  this.printJoin(node.decorators, node);\n\n  // catch up to method key, avoid line break\n  // between member modifiers/method heads and the method key.\n  const endLine = node.key.loc?.end?.line;\n  if (endLine) this.catchUp(endLine);\n\n  this.tsPrintClassMemberModifiers(node);\n  this._methodHead(node);\n}\n\nexport function StaticBlock(this: Printer, node: t.StaticBlock) {\n  this.word(\"static\");\n  this.space();\n  this.token(\"{\");\n  if (node.body.length === 0) {\n    this.token(\"}\");\n  } else {\n    this.newline();\n    this.printSequence(node.body, node, {\n      indent: true,\n    });\n    this.rightBrace(node);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAGsB;EAFpBC,0BAA0B;EAC1BC;AAAwB,IAAAH,EAAA;AAKnB,SAASI,gBAAgBA,CAE9BC,IAAwB,EACxBC,MAAc,EACd;EACA,MAAMC,QAAQ,GACZL,0BAA0B,CAACI,MAAM,CAAC,IAAIH,wBAAwB,CAACG,MAAM,CAAC;EAExE,IACE,CAACC,QAAQ,IACT,CAAC,IAAI,CAACC,kCAAkC,CACtCF,MACF,CAAC,EACD;IACA,IAAI,CAACG,SAAS,CAACJ,IAAI,CAACK,UAAU,EAAEL,IAAI,CAAC;EACvC;EAEA,IAAIA,IAAI,CAACM,OAAO,EAAE;IAEhB,IAAI,CAACC,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEA,IAAIR,IAAI,CAACS,QAAQ,EAAE;IAEjB,IAAI,CAACF,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAACD,IAAI,CAAC,OAAO,CAAC;EAElB,IAAIP,IAAI,CAACU,EAAE,EAAE;IACX,IAAI,CAACF,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACU,EAAE,EAAEV,IAAI,CAAC;EAC3B;EAEA,IAAI,CAACW,KAAK,CAACX,IAAI,CAACY,cAAc,EAAEZ,IAAI,CAAC;EAErC,IAAIA,IAAI,CAACa,UAAU,EAAE;IACnB,IAAI,CAACL,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACa,UAAU,EAAEb,IAAI,CAAC;IACjC,IAAI,CAACW,KAAK,CAACX,IAAI,CAACc,mBAAmB,EAAEd,IAAI,CAAC;EAC5C;EAEA,IAAIA,IAAI,CAACe,UAAU,EAAE;IACnB,IAAI,CAACP,KAAK,CAAC,CAAC;IACZ,IAAI,CAACD,IAAI,CAAC,YAAY,CAAC;IACvB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACQ,SAAS,CAAChB,IAAI,CAACe,UAAU,EAAEf,IAAI,CAAC;EACvC;EAEA,IAAI,CAACQ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACiB,IAAI,EAAEjB,IAAI,CAAC;AAC7B;AAIO,SAASkB,SAASA,CAAgBlB,IAAiB,EAAE;EAC1D,IAAI,CAACmB,SAAK,IAAI,CAAC;EACf,IAAInB,IAAI,CAACiB,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;IAC1B,IAAI,CAACD,SAAK,IAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACE,OAAO,CAAC,CAAC;IAEd,IAAI,CAACC,aAAa,CAACtB,IAAI,CAACiB,IAAI,EAAEjB,IAAI,EAAE;MAAEuB,MAAM,EAAE;IAAK,CAAC,CAAC;IAErD,IAAI,CAAC,IAAI,CAACC,QAAQ,GAAmB,CAAC,EAAE,IAAI,CAACH,OAAO,CAAC,CAAC;IAEtD,IAAI,CAACI,UAAU,CAACzB,IAAI,CAAC;EACvB;AACF;AAEO,SAAS0B,aAAaA,CAAgB1B,IAAqB,EAAE;EAAA,IAAA2B,aAAA;EAClE,IAAI,CAACvB,SAAS,CAACJ,IAAI,CAACK,UAAU,EAAEL,IAAI,CAAC;EAIrC,MAAM4B,OAAO,IAAAD,aAAA,GAAG3B,IAAI,CAAC6B,GAAG,CAACC,GAAG,cAAAH,aAAA,GAAZA,aAAA,CAAcI,GAAG,qBAAjBJ,aAAA,CAAmBK,IAAI;EACvC,IAAIJ,OAAO,EAAE,IAAI,CAACK,OAAO,CAACL,OAAO,CAAC;EAElC,IAAI,CAACM,2BAA2B,CAAClC,IAAI,CAAC;EAEtC,IAAIA,IAAI,CAACmC,QAAQ,EAAE;IACjB,IAAI,CAAChB,SAAK,GAAI,CAAC;IACf,IAAI,CAACR,KAAK,CAACX,IAAI,CAAC6B,GAAG,EAAE7B,IAAI,CAAC;IAC1B,IAAI,CAACmB,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACiB,SAAS,CAACpC,IAAI,CAAC;IACpB,IAAI,CAACW,KAAK,CAACX,IAAI,CAAC6B,GAAG,EAAE7B,IAAI,CAAC;EAC5B;EAGA,IAAIA,IAAI,CAACqC,QAAQ,EAAE;IACjB,IAAI,CAAClB,SAAK,GAAI,CAAC;EACjB;EACA,IAAInB,IAAI,CAACsC,QAAQ,EAAE;IACjB,IAAI,CAACnB,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACR,KAAK,CAACX,IAAI,CAACuC,cAAc,EAAEvC,IAAI,CAAC;EACrC,IAAIA,IAAI,CAACwC,KAAK,EAAE;IACd,IAAI,CAAChC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACW,SAAK,GAAI,CAAC;IACf,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACwC,KAAK,EAAExC,IAAI,CAAC;EAC9B;EACA,IAAI,CAACyC,SAAS,CAAC,CAAC;AAClB;AAEO,SAASC,qBAAqBA,CAEnC1C,IAA6B,EAC7B;EAAA,IAAA2C,cAAA;EACA,IAAI,CAACvC,SAAS,CAACJ,IAAI,CAACK,UAAU,EAAEL,IAAI,CAAC;EAIrC,MAAM4B,OAAO,IAAAe,cAAA,GAAG3C,IAAI,CAAC6B,GAAG,CAACC,GAAG,cAAAa,cAAA,GAAZA,cAAA,CAAcZ,GAAG,qBAAjBY,cAAA,CAAmBX,IAAI;EACvC,IAAIJ,OAAO,EAAE,IAAI,CAACK,OAAO,CAACL,OAAO,CAAC;EAGlC,IAAI,CAACM,2BAA2B,CAAClC,IAAI,CAAC;EAEtC,IAAI,CAACO,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC3B,IAAI,CAACC,KAAK,CAAC,CAAC;EAEZ,IAAIR,IAAI,CAACmC,QAAQ,EAAE;IACjB,IAAI,CAAChB,SAAK,GAAI,CAAC;IACf,IAAI,CAACR,KAAK,CAACX,IAAI,CAAC6B,GAAG,EAAE7B,IAAI,CAAC;IAC1B,IAAI,CAACmB,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IAEL,IAAI,CAACiB,SAAS,CAACpC,IAAI,CAAC;IACpB,IAAI,CAACW,KAAK,CAACX,IAAI,CAAC6B,GAAG,EAAE7B,IAAI,CAAC;EAC5B;EAGA,IAAIA,IAAI,CAACqC,QAAQ,EAAE;IACjB,IAAI,CAAClB,SAAK,GAAI,CAAC;EACjB;EACA,IAAInB,IAAI,CAACsC,QAAQ,EAAE;IACjB,IAAI,CAACnB,SAAK,GAAI,CAAC;EACjB;EAEA,IAAI,CAACR,KAAK,CAACX,IAAI,CAACuC,cAAc,EAAEvC,IAAI,CAAC;EACrC,IAAIA,IAAI,CAACwC,KAAK,EAAE;IACd,IAAI,CAAChC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACW,SAAK,GAAI,CAAC;IACf,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACwC,KAAK,EAAExC,IAAI,CAAC;EAC9B;EACA,IAAI,CAACyC,SAAS,CAAC,CAAC;AAClB;AAEO,SAASG,oBAAoBA,CAElC5C,IAA4B,EAC5B;EACA,IAAI,CAACI,SAAS,CAACJ,IAAI,CAACK,UAAU,EAAEL,IAAI,CAAC;EACrC,IAAIA,IAAI,CAAC6C,MAAM,EAAE;IACf,IAAI,CAACtC,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACG,KAAK,CAACX,IAAI,CAAC6B,GAAG,EAAE7B,IAAI,CAAC;EAC1B,IAAI,CAACW,KAAK,CAACX,IAAI,CAACuC,cAAc,EAAEvC,IAAI,CAAC;EACrC,IAAIA,IAAI,CAACwC,KAAK,EAAE;IACd,IAAI,CAAChC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACW,SAAK,GAAI,CAAC;IACf,IAAI,CAACX,KAAK,CAAC,CAAC;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACwC,KAAK,EAAExC,IAAI,CAAC;EAC9B;EACA,IAAI,CAACyC,SAAS,CAAC,CAAC;AAClB;AAEO,SAASK,WAAWA,CAAgB9C,IAAmB,EAAE;EAC9D,IAAI,CAAC+C,gBAAgB,CAAC/C,IAAI,CAAC;EAC3B,IAAI,CAACQ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACiB,IAAI,EAAEjB,IAAI,CAAC;AAC7B;AAEO,SAASgD,kBAAkBA,CAAgBhD,IAA0B,EAAE;EAC5E,IAAI,CAAC+C,gBAAgB,CAAC/C,IAAI,CAAC;EAC3B,IAAI,CAACQ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACiB,IAAI,EAAEjB,IAAI,CAAC;AAC7B;AAEO,SAAS+C,gBAAgBA,CAE9B/C,IAA8D,EAC9D;EAAA,IAAAiD,cAAA;EACA,IAAI,CAAC7C,SAAS,CAACJ,IAAI,CAACK,UAAU,EAAEL,IAAI,CAAC;EAIrC,MAAM4B,OAAO,IAAAqB,cAAA,GAAGjD,IAAI,CAAC6B,GAAG,CAACC,GAAG,cAAAmB,cAAA,GAAZA,cAAA,CAAclB,GAAG,qBAAjBkB,cAAA,CAAmBjB,IAAI;EACvC,IAAIJ,OAAO,EAAE,IAAI,CAACK,OAAO,CAACL,OAAO,CAAC;EAElC,IAAI,CAACM,2BAA2B,CAAClC,IAAI,CAAC;EACtC,IAAI,CAACkD,WAAW,CAAClD,IAAI,CAAC;AACxB;AAEO,SAASmD,WAAWA,CAAgBnD,IAAmB,EAAE;EAC9D,IAAI,CAACO,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,IAAI,CAACW,SAAK,IAAI,CAAC;EACf,IAAInB,IAAI,CAACiB,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;IAC1B,IAAI,CAACD,SAAK,IAAI,CAAC;EACjB,CAAC,MAAM;IACL,IAAI,CAACE,OAAO,CAAC,CAAC;IACd,IAAI,CAACC,aAAa,CAACtB,IAAI,CAACiB,IAAI,EAAEjB,IAAI,EAAE;MAClCuB,MAAM,EAAE;IACV,CAAC,CAAC;IACF,IAAI,CAACE,UAAU,CAACzB,IAAI,CAAC;EACvB;AACF"}