import axios from 'axios';
import { settings } from '@appmaker-xyz/app-config/newConfig';
import { getProjectId, appSettings } from '@appmaker-xyz/core';

async function redirectLogin(params) {
  const formData = new FormData();
  // formData
  params = { accessToken: params, redirectPath: '/account?from_mobile_app=1' };
  Object.keys(params).map(function (key) {
    var value = params[key] ? params[key] : '';
    if (value instanceof Date) {
      value = value.toString();
    }
    formData.append(key, value);
  });
  const response = await axios.post(
    `https://shopify.appmaker.xyz/${getProjectId()}/shopify/multipass/redirect-login`,
    params,
    {
      headers: {
        'Accept-Language':
          global.globalAppStorageState?.language ||
          appSettings.getOption('defaultLanguage', 'en'),
        'Content-Type': 'Application/json',
      },
    },
  );
  return response.data;
}

export { redirectLogin };
