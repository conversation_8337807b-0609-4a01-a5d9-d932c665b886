import React from 'react';
import {StyleSheet} from 'react-native';
import {useApThemeState} from '../../../theme/ThemeContext';
import {
  Layout,
  Input,
  Button,
  AppmakerText,
  AppImage,
} from '../../../components';
import {ToSignIn} from './components';

const InitialLogin = props => {
  const {
    socialLogin = true,
    loginAction,
    createAccountAction,
    skipAction,
    imageSource,
    showSkip = true,
  } = props;
  const {color, spacing} = useApThemeState();
  const styles = allStyles({color, spacing});

  return (
    <Layout style={styles.container}>
      <Layout style={styles.skip}>
        {showSkip && (
          <Button small status="light" onPress={skipAction}>
            SKIP
          </Button>
        )}
      </Layout>
      <Layout style={styles.initialBanner}>
        <AppImage
          // uri="https://source.unsplash.com/user/erondu/1600x900"
          src={imageSource}
          style={styles.image}
        />
        {/* <AppmakerText category="h1Heading">Hello</AppmakerText> */}
      </Layout>
      <Layout style={styles.signIn}>
        <ToSignIn
          loginAction={loginAction}
          createAccountAction={createAccountAction}
          showSocialLogin={socialLogin}
        />
      </Layout>
    </Layout>
  );
};

const allStyles = ({spacing, color}) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      flex: 1,
      justifyContent: 'space-between',
    },
    skip: {
      position: 'absolute',
      zIndex: 9999,
      right: spacing.base,
      top: spacing.base,
    },
    initialBanner: {
      flex: 1,
      height: '65%',
    },
    image: {
      width: '100%',
      height: '100%',
    },
    signIn: {
      padding: spacing.base,
      height: '35%',
      justifyContent: 'center',
    },
  });

export default InitialLogin;
