import React from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '../../../../../theme/ThemeContext';
import {
  Layout,
  AppmakerText,
  Button,
  Badge,
  AppTouchable,
} from '../../../../../components';
import { useTranslation } from 'react-i18next';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
// import Icon from 'react-native-vector-icons/';

const ShopifyReview = ({ attributes, onAction }) => {
  const { t } = useTranslation();
  const {
    count,
    average_rating,
    title,
    buttonTitle,
    buttonAction,
    appmakerAction,
  } = attributes;
  const titleText = t(buttonTitle);
  const { spacing, color } = useApThemeState();
  const styles = allStyles({ spacing, color });
  const ratingBadgeColor = () => {
    let rating = average_rating;
    switch (true) {
      case rating >= '3.5':
        return 'success';
      case rating >= '2.5':
        return 'warining';
      default:
        return 'dark';
    }
  };

  return (
    <Layout style={styles.container}>
      <AppTouchable onPress={() => onAction(appmakerAction)}>
        <AppmakerText category="actionTitle">
          {title}
          <Icon name="chevron-right" />
        </AppmakerText>
        {average_rating && (
          <Badge
            text={average_rating}
            status={ratingBadgeColor()}
            iconName="star"
          />
        )}
        {count && (
          <AppmakerText category="smallButtonText" status="primary">
            {count} Reviews
          </AppmakerText>
        )}
      </AppTouchable>
      {buttonTitle ? (
        <Button
          onPress={() => onAction(buttonAction)}
          small={true}
          status="dark"
          outline={true}>
          {titleText}
        </Button>
      ) : null}
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: spacing.base,
      paddingVertical: spacing.small,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: color.white,
      marginBottom: spacing.nano,
    },
  });

export default ShopifyReview;
