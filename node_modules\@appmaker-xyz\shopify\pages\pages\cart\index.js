const CartPage = {
  blocks: [
    {
      name: 'shopify/header-notice',
      attributes: {},
    },
    {
      clientId: '143f1163-10ac-4b22-bf57-2ba78c94b540',
      name: 'shopify/cart-line-items',
    },
    {
      clientId: 'cart-summary-table',
      name: 'shopify/cart-summary-table',
      attributes: {},
    },
  ],
  stickyFooter: {
    blocks: [
      {
        name: 'appmaker/order-notes',
        attributes: {
          __display:
            '{{checkIfTrueFalse(plugins.shopify.settings.show_order_notes)}}',
          orderNote: '{{blockData.note}}',
        },
      },
      {
        name: 'shopify/apply-coupon',
        attributes: {
          __display:
            '{{checkIfTrueFalse(plugins.shopify.settings.show_apply_coupon)}}',
        },
      },
      {
        name: 'shopify/apply-coupon-modal',
        attributes: {
          __display:
            '{{checkIfTrueFalse(plugins.shopify.settings.show_apply_coupon)}}',
        },
      },
      {
        name: 'shopify/cart-free-gift-notice-wrapper',
        attributes: {
          themeColor: '#760489',
        },
      },
      {
        name: 'appmaker/address-display',
        attributes: {
          title:
            '{{ blockData.shippingAddress===null ? "Choose Address" : "Delivering to" }}',
          subTitle:
            '{{ blockData.shippingAddress.address1 + " " + blockData.shippingAddress.address2 + " " + blockData.shippingAddress.city }} ',
          loading: false,
          __display:
            '{{ (blockData && blockData.lineItems && blockData.lineItems.edges.length && (plugins.shopify && checkIfTrueFalse(plugins.shopify.settings.enable_native_checkout))) ? true : false }}',
          appmakerAction: {
            action: 'CHOOSE_ADDRESS',
          },
        },
      },
      {
        clientId: 'checkout-sumbit',
        name: 'appmaker/checkout-button',
        attributes: {
          type: 'type-1',
          show: true,
          viewCartText: 'Checkout',
          itemCount: '{{getCartTotalItemsCount(appStorageState.checkout)}}',
          totalPrice:
            '{{currencyHelper(blockData.subtotalPrice.amount,blockData.subtotalPrice.currencyCode)}}',
          showSubTotalAmount:
            '{{blockData.subtotalPrice.amount > blockData.totalPrice.amount ? 1 : 0 }}',
          subtotalAmount:
            '{{currencyHelper(blockData.totalPrice.amount,blockData.totalPrice.currencyCode)}}',
          appmakerAction: {
            action: 'START_CHECKOUT',
          },
          __appmakerStylesClassName: 'checkoutCustomStyle',
        },
      },
    ],
  },
  attributes: {
    loadingLayout: 'product',
    insideSafeAreaView: true,
    showLoadingTillData: true,
    reloadOnFocus: true,
    // renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      // paddingHorizontal: 16,
    },
  },
  title: 'Cart',
};
export default CartPage;
