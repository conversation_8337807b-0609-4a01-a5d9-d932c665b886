/**
 * Created by saleeh on 29/08/16.
 */

import { toast } from 'react-toastify';

class MessageView {
  static success(title) {
    if (title.length < 80) {
      toast(title);
    } else {
      toast('Alert', title);
    }
  }

  static error(title) {
    if (title.length < 80) {
      toast(title);
    } else {
      toast('Error', title);
    }
  }

  static showHttpError(error) {
    if (error.length < 80) {
      MessageView.error(error.data.message);
    } else {
      toast('Error', error.data.message);
    }
  }
}

export default MessageView;
