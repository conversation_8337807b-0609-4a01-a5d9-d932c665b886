import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '../../../../../theme/ThemeContext';

import {
  Layout,
  AppmakerText,
  Stepper,
  BlockCard,
} from '../../../../../components';

const CounterBlock = ({ attributes, ...props }) => {
  const { spacing } = useApThemeState();
  const styles = allStyles({ spacing });
  const {
    title = 'Select Count',
    id = 'quantity',
    min_value,
    max_value,
    label,
    input_value,
    step,
    type,
    fullWidth,
    themeColor,
    __appmakerCustomStyles = {},
  } = attributes;
  const [count, setCount] = useState(min_value || 1);

  return (
    <BlockCard attributes={{}}>
      <Layout style={[styles.row, __appmakerCustomStyles?.container]}>
        <AppmakerText
          category="actionTitle"
          style={__appmakerCustomStyles?.title}>
          {title}
        </AppmakerText>
        <Stepper
          fullWidth={fullWidth}
          themeColor={themeColor}
          count={count}
          canDelete={false}
          minValue={min_value}
          maxValue={max_value}
          step={step}
          onChange={(value) => {
            setCount(value);
            props?.pageDispatch({
              type: 'set_value',
              name: id,
              value,
            });
          }}
          __appmakerCustomStyles={__appmakerCustomStyles?.stepper}
        />
      </Layout>
    </BlockCard>
  );
};
const allStyles = ({ spacing }) =>
  StyleSheet.create({
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: spacing.base,
      backgroundColor: '#fff',
    },
  });
export default CounterBlock;
