import React from 'react';
import { View, StyleSheet } from 'react-native';
import { AppmakerText, LargeImage, AppTouchable } from '../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { useApThemeState } from '../../../theme/ThemeContext';
// console.log = (item) => console.log(JSON.stringify(item, null, 2));
const NotificationWidget = ({ attributes, onPress, onAction }) => {
  const {
    title,
    text,
    message,
    timeStamp,
    imgUri,
    iconName = 'bell',
    appmakerAction,
  } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  return (
    <AppTouchable onPress={onPressHandle}>
      <View style={styles.container}>
        <View style={styles.iconContainer}>
          <Icon name={iconName} size={spacing.md} style={styles.iconStyle} />
        </View>
        <View style={styles.contentContainer}>
          <AppmakerText category="actionTitle">{title || text}</AppmakerText>
          <AppmakerText category="bodySubText">{message}</AppmakerText>
          {imgUri ? (
            <View style={styles.imageContainer}>
              <LargeImage uri={imgUri} />
            </View>
          ) : null}
          <View style={styles.timeStamp}>
            <AppmakerText category="highlighter1" status="grey">
              {timeStamp}
            </AppmakerText>
          </View>
        </View>
      </View>
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      padding: spacing.base,
      flexDirection: 'row',
      marginHorizontal: spacing.nano,
      marginTop: spacing.nano,
      borderRadius: spacing.nano,
    },
    contentContainer: {
      marginLeft: spacing.small,
      width: '85%',
    },
    iconStyle: {
      padding: spacing.base,
      backgroundColor: `${color.primary}30`,
      color: color.primary,
      borderRadius: 200,
    },
    imageContainer: {
      marginTop: spacing.small,
    },
    timeStamp: {
      marginTop: spacing.mini,
    },
  });

export default NotificationWidget;
