{"version": 3, "names": ["TaggedTemplateExpression", "node", "print", "tag", "typeParameters", "quasi", "TemplateElement", "parent", "<PERSON><PERSON><PERSON><PERSON>", "quasis", "isLast", "length", "value", "raw", "token", "TemplateLiteral", "i", "expressions"], "sources": ["../../src/generators/template-literals.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport type * as t from \"@babel/types\";\n\nexport function TaggedTemplateExpression(\n  this: Printer,\n  node: t.TaggedTemplateExpression,\n) {\n  this.print(node.tag, node);\n  this.print(node.typeParameters, node); // TS\n  this.print(node.quasi, node);\n}\n\nexport function TemplateElement(\n  this: Printer,\n  node: t.TemplateElement,\n  parent: t.TemplateLiteral,\n) {\n  const isFirst = parent.quasis[0] === node;\n  const isLast = parent.quasis[parent.quasis.length - 1] === node;\n\n  const value = (isFirst ? \"`\" : \"}\") + node.value.raw + (isLast ? \"`\" : \"${\");\n\n  this.token(value, true);\n}\n\nexport function TemplateLiteral(this: Printer, node: t.TemplateLiteral) {\n  const quasis = node.quasis;\n\n  for (let i = 0; i < quasis.length; i++) {\n    this.print(quasis[i], node);\n\n    if (i + 1 < quasis.length) {\n      this.print(node.expressions[i], node);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;AAGO,SAASA,wBAAwBA,CAEtCC,IAAgC,EAChC;EACA,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,EAAEF,IAAI,CAAC;EAC1B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACG,cAAc,EAAEH,IAAI,CAAC;EACrC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACI,KAAK,EAAEJ,IAAI,CAAC;AAC9B;AAEO,SAASK,eAAeA,CAE7BL,IAAuB,EACvBM,MAAyB,EACzB;EACA,MAAMC,OAAO,GAAGD,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,KAAKR,IAAI;EACzC,MAAMS,MAAM,GAAGH,MAAM,CAACE,MAAM,CAACF,MAAM,CAACE,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC,KAAKV,IAAI;EAE/D,MAAMW,KAAK,GAAG,CAACJ,OAAO,GAAG,GAAG,GAAG,GAAG,IAAIP,IAAI,CAACW,KAAK,CAACC,GAAG,IAAIH,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC;EAE5E,IAAI,CAACI,KAAK,CAACF,KAAK,EAAE,IAAI,CAAC;AACzB;AAEO,SAASG,eAAeA,CAAgBd,IAAuB,EAAE;EACtE,MAAMQ,MAAM,GAAGR,IAAI,CAACQ,MAAM;EAE1B,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,MAAM,CAACE,MAAM,EAAEK,CAAC,EAAE,EAAE;IACtC,IAAI,CAACd,KAAK,CAACO,MAAM,CAACO,CAAC,CAAC,EAAEf,IAAI,CAAC;IAE3B,IAAIe,CAAC,GAAG,CAAC,GAAGP,MAAM,CAACE,MAAM,EAAE;MACzB,IAAI,CAACT,KAAK,CAACD,IAAI,CAACgB,WAAW,CAACD,CAAC,CAAC,EAAEf,IAAI,CAAC;IACvC;EACF;AACF"}