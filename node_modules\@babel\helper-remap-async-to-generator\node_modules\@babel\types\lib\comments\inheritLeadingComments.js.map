{"version": 3, "names": ["_inherit", "require", "inheritLeadingComments", "child", "parent", "inherit"], "sources": ["../../src/comments/inheritLeadingComments.ts"], "sourcesContent": ["import inherit from \"../utils/inherit\";\nimport type * as t from \"..\";\n\nexport default function inheritLeadingComments(\n  child: t.Node,\n  parent: t.Node,\n): void {\n  inherit(\"leadingComments\", child, parent);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAGe,SAASC,sBAAsBA,CAC5CC,KAAa,EACbC,MAAc,EACR;EACN,IAAAC,gBAAO,EAAC,iBAAiB,EAAEF,KAAK,EAAEC,MAAM,CAAC;AAC3C"}