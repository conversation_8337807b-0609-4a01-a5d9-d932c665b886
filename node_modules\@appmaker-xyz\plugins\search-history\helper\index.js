import { onEvent } from '@appmaker-xyz/core';
import AsyncStorage from '@react-native-community/async-storage';

let SEARCH_HISTORY_KEY = 'search.history';
const saveSearchQueries = () => {
  onEvent('product.search', async (key) => {
    try {
      const allValues = await AsyncStorage.getItem(SEARCH_HISTORY_KEY);
      if (!allValues) {
        let data = [key];
        const saveJson = JSON.stringify(data);
        AsyncStorage.setItem(SEARCH_HISTORY_KEY, saveJson);
      } else if (allValues) {
        let jsonValues = JSON.parse(allValues);
        let skipPush = false;
        if (Array.isArray(jsonValues)) {
          const indexValue = jsonValues.findIndex(
            (value) => value.toLowerCase() === key.toLowerCase(),
          );
          if (indexValue === -1 && jsonValues?.length >= 10) {
            jsonValues.pop();
          } else if (indexValue > -1) {
            const shiftedValue = jsonValues.splice?.(indexValue, 1)[0];
            jsonValues.unshift?.(shiftedValue);
            skipPush = true;
          }
          !skipPush && jsonValues.unshift(key);
          AsyncStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(jsonValues));
        }
      }
    } catch (e) {
      console.log('search-histry error = ', e);
    }
  });
};

const getAllSearchQuery = async () => {
  try {
    const allQueries = await AsyncStorage.getItem(SEARCH_HISTORY_KEY);
    const allQueriesJSON = JSON.parse(allQueries);
    return Array.isArray(allQueriesJSON) ? allQueriesJSON : [];
  } catch (e) {}
};

export { saveSearchQueries, getAllSearchQuery };
