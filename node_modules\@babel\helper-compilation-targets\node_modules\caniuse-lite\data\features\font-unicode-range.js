module.exports={A:{A:{"2":"J D E DC","4":"F A B"},B:{"1":"N O P Q R S T U V W X Y Z a b c d e i j k l m n o p q r s t u v f H","4":"C K L G M"},C:{"1":"LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB vB aB wB bB cB dB eB fB gB hB iB jB kB lB h mB nB oB pB qB P Q R xB S T U V W X Y Z a b c d e i j k l m n o p q r s t u v f H yB zB","2":"0 1 2 3 4 5 6 7 8 9 EC uB I w J D E F A B C K L G M N O x g y z AB BB CB FC GC","194":"DB EB FB GB HB IB JB KB"},D:{"1":"DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB vB aB wB bB cB dB eB fB gB hB iB jB kB lB h mB nB oB pB qB P Q R S T U V W X Y Z a b c d e i j k l m n o p q r s t u v f H yB zB HC","4":"0 1 2 3 4 5 6 7 8 9 I w J D E F A B C K L G M N O x g y z AB BB CB"},E:{"1":"A B C K L G 1B rB sB 2B NC OC 3B 4B 5B 6B tB 7B 8B 9B AC PC","4":"I w J D E F IC 0B JC KC LC MC"},F:{"1":"0 1 2 3 4 5 6 7 8 9 AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB h mB nB oB pB qB P Q R xB S T U V W X Y Z a b c d e","2":"F B C QC RC SC TC rB BC UC sB","4":"G M N O x g y z"},G:{"1":"cC dC eC fC gC hC iC jC kC lC mC nC oC 3B 4B 5B 6B tB 7B 8B 9B AC","4":"E 0B VC CC WC XC YC ZC aC bC"},H:{"2":"pC"},I:{"1":"H","4":"uB I qC rC sC tC CC uC vC"},J:{"2":"D","4":"A"},K:{"1":"h","2":"A B C rB BC sB"},L:{"1":"H"},M:{"1":"f"},N:{"4":"A B"},O:{"1":"wC"},P:{"1":"g xC yC zC 0C 1C 1B 2C 3C 4C 5C 6C tB 7C 8C 9C","4":"I"},Q:{"1":"2B"},R:{"1":"AD"},S:{"1":"BD CD"}},B:4,C:"Font unicode-range subsetting"};
