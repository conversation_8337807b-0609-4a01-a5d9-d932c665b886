import React from 'react';
import { useState } from 'react';
import { View, Pressable, FlatList, StyleSheet } from 'react-native';
import { ThemeText } from '../atoms';
import { get } from 'lodash';

const BasicTabs = (props) => {
  const { attributes, innerBlocks, BlocksView, onAction } = props;
  // const [activeItem, setActiveItem] = useState(innerBlocks[0]);
  const [activeIndex, setActiveIndex] = useState(0);
  const {
    gap = 6,
    title,
    customData,
    customDataTabTitle,
    hideTitle,
    blockContainerStyle,
    blockTitleTextStyle,
    tabContainerStyle,
    dataContainerStyle,
    activeTabStyle,
    defaultTabStyle,
    activeTabTextStyle,
    defaultTabTextStyle,
  } = attributes;
  const isCustomData = customData && customData.length > 0;
  const finalBlocks = isCustomData
    ? innerBlocks
    : innerBlocks[activeIndex]?.innerBlocks;

  const ProductScroller = () => {
    return (
      <BlocksView
        onAction={onAction}
        customBlockData={isCustomData ? customData[activeIndex] : null}
        inAppPage={{
          blocks: finalBlocks,
          attributes: { renderType: 'normal', padding: 0 },
        }}
      />
    );
  };

  return (
    <View style={blockContainerStyle}>
      {!hideTitle && title ? (
        <ThemeText size="md" fontFamily="medium" style={blockTitleTextStyle}>
          {title}
        </ThemeText>
      ) : null}
      <FlatList
        style={[styles.tab]}
        horizontal
        showsHorizontalScrollIndicator={false}
        data={isCustomData ? customData : innerBlocks}
        contentContainerStyle={[styles.tabContainer, tabContainerStyle]}
        ItemSeparatorComponent={() => <View style={{ width: gap }} />}
        renderItem={({ item, index }) => {
          const customTitle = customData
            ? get(item, customDataTabTitle, '')
            : item?.attributes?.title;
          return (
            <Pressable
              style={
                index === activeIndex
                  ? [styles.tabActive, activeTabStyle]
                  : [styles.tabInActive, defaultTabStyle]
              }
              onPress={() => {
                setActiveIndex(index);
              }}>
              <ThemeText
                style={
                  index === activeIndex
                    ? [styles.tabActiveTxt, activeTabTextStyle]
                    : [styles.tabInActiveTxt, defaultTabTextStyle]
                }>
                {customTitle || `Tab ${index + 1}`}
              </ThemeText>
            </Pressable>
          );
        }}
      />
      <View style={dataContainerStyle}>
        <ProductScroller />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  tabActive: {
    paddingHorizontal: 22,
    paddingVertical: 10,
    backgroundColor: '#F5F5F5',
  },
  tabInActive: {
    paddingHorizontal: 22,
    paddingVertical: 10,
  },
  tabActiveTxt: {
    color: 'black',

    fontSize: 16,
  },
  tabInActiveTxt: {
    color: 'black',

    fontSize: 16,
  },
  headingText: {
    paddingBottom: 8,
    paddingTop: 12,
    textAlign: 'center',
    fontSize: 22,
  },
  tab: {
    alignSelf: 'center',
    marginBottom: 12,
  },
  tabContainer: {
    paddingHorizontal: 12,
  },
});
export default BasicTabs;
