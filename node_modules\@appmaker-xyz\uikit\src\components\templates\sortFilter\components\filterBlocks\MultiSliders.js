import { View, StyleSheet } from 'react-native';
import React from 'react';
import { Layout, Button, AppmakerText } from '@appmaker-xyz/uikit';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import { spacing, color } from '../../../../../styles';
import { useFilterStore } from '../../store';
function shopifyMinMax(attributes) {
  console.log(attributes, 'attributes');
  try {
    const config = JSON.parse(attributes.values[0].input);
    return config.price;
  } catch (error) {
    return { min: 0, max: 1500 };
  }
}
export default function MultiSliders({ attributes }) {
  // const [sliding, setSliding] = useState(false);
  //   let defaultParams = selectedFilterParams.app_filter?.[resp[0]];
  // let min = value?.min;
  // let max = value?.max;
  const { min, max } = shopifyMinMax(attributes);
  const setAppliedFilter = useFilterStore((state) => state.setAppliedFilter);
  const appliedFilters = useFilterStore(
    (state) => state.appliedFilters[attributes.id] || {},
  );
  console.log(attributes.id);
  return (
    <Layout style={styles.sliderContainer}>
      <MultiSlider
        values={[appliedFilters?.min || min, appliedFilters?.max || max]}
        enableLabel={true}
        min={min}
        max={max}
        isMarkersSeparated={true}
        trackStyle={styles.trackStyle}
        selectedStyle={styles.slideSelectedStyle}
        markerStyle={styles.markerStyle}
        pressedMarkerStyle={styles.markerPressed}
        // enableLabel={sliding ? true : false}
        // onValuesChangeStart={() => setSliding(!sliding)}
        onValuesChangeFinish={(values) => {
          //   selectedFilterParams.app_filter = {
          //     ...selectedFilterParams.app_filter,
          //     [resp[0]]: { price: { min: values[0], max: values[1] } },
          //   };
          //   setSelectedFilterParams(selectedFilterParams);
          // setSliding(!sliding);
          // setValue({ min: values[0], max: values[1] });
          setAppliedFilter(attributes.id, { min: values[0], max: values[1] });
        }}
      />
      <Layout style={styles.sliderLabels}>
        <AppmakerText category="bodyParagraph">{min}</AppmakerText>
        <AppmakerText category="bodyParagraph">{max}</AppmakerText>
      </Layout>
    </Layout>
  );
}

const styles = StyleSheet.create({
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    borderBottomColor: color.light,
    borderBottomWidth: 1,
  },
  innerContainer: {
    backgroundColor: color.white,
    flex: 1,
    borderRightColor: color.light,
    borderRightWidth: 1,
  },
  view: {
    justifyContent: 'flex-end',
    marginVertical: 0,
    marginHorizontal: 1,
  },
  modalBody: {
    backgroundColor: color.white,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
    flexDirection: 'column',
    height: '85%',
  },
  modalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: color.light,
    padding: spacing.base,
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
  },
  modalContent: {
    padding: spacing.md,
    flexDirection: 'column',
    width: '100%',
  },
  modalFooter: {
    width: '100%',
    backgroundColor: color.white,
    padding: spacing.base,
  },
  headerItems: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  centerView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  sortModalBody: {
    width: '80%',
    backgroundColor: color.white,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: spacing.small,
    flexDirection: 'column',
  },
  sortModalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: color.light,
    padding: spacing.base,
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
  },
  sortModalContent: {
    width: '100%',
    padding: spacing.base,
  },
  sortItem: {
    marginVertical: spacing.nano,
  },
  sliderContainer: {
    alignItems: 'center',
    paddingVertical: spacing.base,
  },
  sliderLabels: {
    flexDirection: 'row',
    width: 310,
    justifyContent: 'space-between',
  },
  trackStyle: {
    borderRadius: spacing.small,
    height: spacing.nano / 2,
  },
  slideSelectedStyle: {
    backgroundColor: color.primary,
  },
  markerStyle: {
    height: 30,
    width: 30,
    borderRadius: spacing.md,
    backgroundColor: color.light,
    borderWidth: 0.5,
    borderColor: color.demiDark,
  },
  markerPressed: {
    backgroundColor: color.grey,
  },
});
