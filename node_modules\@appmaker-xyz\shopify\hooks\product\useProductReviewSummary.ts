import { useDataSourceV2 } from '@appmaker-xyz/core';
import { ShopifyDataSourceConfig } from '../../datasource/shopify';
interface ReviewSummary {
    average_rating: string | null;
    total_reviews: string | null;
    total_questions: string | null;
    ratings: Rating[] | null;
}

interface Rating {
    rating: string | null;
    percentage: string | null;
    frequency: string | null;
}
interface UseProductReviewSummaryReturn {
  Summary: ReviewSummary | null;
  isLoading: boolean;
}

interface UseProductReviewSummaryProps {
  productId: string;
}

const useProductReviewSummary = (
  props: UseProductReviewSummaryProps,
): UseProductReviewSummaryReturn => {
  const productId = props?.productId;
  const dataSource = ShopifyDataSourceConfig.ReviewSummary({
    productId,
  });
  const [queryResp, { item }] = useDataSourceV2({
    dataSource,
    enabled: !!productId,
  });
  return {
    Summary: item,
    isLoading: queryResp?.isLoading,
  };
};

export { useProductReviewSummary };
