import React, { useState } from 'react';
import {
  View,
  Pressable,
  LayoutAnimation,
  ActivityIndicator,
} from 'react-native';
import { ThemeText as Text } from '@appmaker-xyz/ui';
import { useCouponItem } from '@appmaker-xyz/shopify';
import { testProps } from '@appmaker-xyz/core';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';

const RotatedText = ({ text, disabled, testId }) => {
  const TEXT_LENGTH = 150;
  const TEXT_HEIGHT = 8;
  const { theme } = useStyles(stylesheet);

  return (
    <View
      style={{
        width: TEXT_HEIGHT,
        height: TEXT_LENGTH,
        backgroundColor: disabled ? theme.colors.muted : theme.colors.accent,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
      }}>
      <Text
        {...testProps(testId)}
        style={{
          transform: [{ rotate: '270deg' }],
          width: TEXT_LENGTH,
          minHeight: TEXT_HEIGHT,
          textAlign: 'center',
          fontSize: 16,
          color: 'white',
          fontFamily: theme.fontFamily.bold,
        }}>
        {text}
      </Text>
    </View>
  );
};

const CouponBlock = ({
  item,
  onApply,
  cartSubTotalAmount,
  cartTotalQuantity,
  testId,
}) => {
  const [show, setShow] = useState(false);
  const coupon = useCouponItem({
    item,
    onApply,
    cartSubTotalAmount,
    cartTotalQuantity,
  });
  const [isApplying, setIsApplying] = React.useState(false);
  async function applyCoupon() {
    setIsApplying(true);
    await onApply(coupon.coupon, {
      referrer: {
        name: 'coupon-list-page',
        type: 'list-item',
        title: coupon.coupon,
      },
    });
    setIsApplying(false);
  }
  const toggleOpen = () => {
    setShow(!show);
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  };
  const { styles, theme } = useStyles(stylesheet);
  return (
    <View
      {...testProps(testId)}
      style={[styles.wholeContainer, { marginBottom: show ? 0 : 18 }]}>
      <View style={styles.couponContainer}>
        <RotatedText
          testId={`${testId}-rotated-text`}
          text={coupon.badge}
          disabled={!coupon.isValid}
        />
        <View style={styles.couponBody}>
          <View style={styles.couponTitleContainer}>
            <Text
              {...testProps(`${testId}-coupon`)}
              style={styles.couponCodeText}>
              {coupon.coupon}
            </Text>
          </View>
          {coupon.isValid &&
          coupon.calulatedSavingsValue > 0 &&
          coupon.disableCalculations === false ? (
            <View style={{ flexDirection: 'row' }}>
              <Text style={styles.youSavetext}>Save</Text>
              <Text style={styles.amountText}>{coupon.calulatedSavings}</Text>
              <Text style={styles.youSavetext}>on this order!</Text>
            </View>
          ) : null}
          {!coupon.isValid ? (
            <Text style={styles.validMsgText}>{coupon.validMessage}</Text>
          ) : null}
          <View style={styles.bottomContainer}>
            <View style={{ flexShrink: 1 }}>
              <Text
                {...testProps(`${testId}-description`)}
                numberOfLines={3}
                style={{
                  fontSize: 14,
                  color: !coupon.isValid
                    ? theme.colors.muted
                    : theme.colors.text,
                  fontFamily: theme.fontFamily.regular,
                }}>
                {coupon.description}
              </Text>
              {/* <Pressable onPress={toggleOpen}>
                <Text style={styles.moreText}>+ More</Text>
              </Pressable> */}
            </View>
            <Pressable
              {...testProps(`${testId}-apply`)}
              disabled={!coupon.isValid}
              style={[
                styles.button,
                { backgroundColor: !coupon.isValid ? '#A8ADB0' : 'black' },
              ]}
              onPress={applyCoupon}>
              {isApplying ? (
                <ActivityIndicator color="white" size={18} />
              ) : (
                <Text style={styles.applyText}>Apply</Text>
              )}
            </Pressable>
          </View>
        </View>
      </View>
      {/* {show ? (
        <View style={styles.bottomTerms}>
          <Text style={{}}>Terms and conditions apply</Text>
          <Text style={{}}>Terms and conditions apply</Text>
        </View>
      ) : null} */}
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  wholeContainer: {
    width: '100%',
    position: 'relative',
  },
  couponContainer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.background,
    borderRadius: 18,
    overflow: 'hidden',
    shadowColor: '#ccc',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.32,
    shadowRadius: 5.46,

    elevation: 1,
  },
  couponTitleContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  couponBody: {
    flex: 1,
    padding: 18,
    justifyContent: 'space-between',
  },
  bottomContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  button: {
    borderRadius: 8,
    marginLeft: 8,
    width: 100,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomTerms: {
    paddingHorizontal: 12,
    paddingTop: 44,
    paddingBottom: 12,
    borderWidth: 1,
    borderColor: '#A8ADB0',
    position: 'relative',
    top: -32,
    zIndex: -10,
    borderRadius: 18,
  },
  amountText: {
    fontSize: 16,
    fontFamily: theme.fontFamily.bold,

    marginHorizontal: 4,
    color: theme.colors.success,
  },
  couponCodeText: {
    fontSize: 20,
    fontFamily: theme.fontFamily.bold,
    marginRight: 4,
    color: theme.colors.text,
  },
  youSavetext: {
    color: theme.colors.success,
    fontFamily: theme.fontFamily.regular,
    fontSize: 16,
  },
  validMsgText: {
    color: '#E71919',
    fontFamily: theme.fontFamily.regular,
  },
  moreText: {
    fontFamily: theme.fontFamily.bold,
  },
  applyText: {
    color: theme.colors.primaryButtonText,
    fontSize: 16,
    fontFamily: theme.fontFamily.medium,
  },
}));

export default CouponBlock;
