import {StyleSheet, Dimensions} from 'react-native';
const screenHeight = Dimensions.get('screen').height;

export default StyleSheet.create({
  text: {fontSize: 14, fontWeight: '300'},
  price: {fontWeight: 'bold'},
  touchableLogo: {flexDirection: 'row'},
  logo: {
    height: 14,
    width: 60,
    resizeMode: 'contain',
  },
  infoIcon: {
    height: 14,
    width: 14,
    resizeMode: 'contain',
  },
  modalScrollContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  modalImageBG: {width: '100%', height: '100%'},
  modalBG: {
    backgroundColor: '#fff',
    width: '90%',
    overflow: 'hidden',
    alignItems: 'center',
    borderRadius: 10,
    shadowOffset: {width: 4, height: 4},
    shadowOpacity: 0.5,
    elevation: 4,
  },
  closeButton: {
    alignSelf: 'flex-end',
    padding: 10,
  },
  close: {fontSize: 20, color: '#f08570'},
  modalLogo: {
    resizeMode: 'contain',
    width: '40%',
    height: 30,
    marginBottom: 15,
  },
  modalHeading: {
    fontSize: 16,
    color: '#000',
    textAlign: 'center',
  },
  plan: {
    width: '80%',
    resizeMode: 'contain',
    marginTop: 10,
    height: 90,
  },
  details: {
    color: '#382757',
    fontWeight: '500',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 15,
  },
  lastText: {
    color: '#382757',
    fontSize: 14,
    fontWeight: '600',
    color: '#037269',
    textAlign: 'center',
    marginTop: 20,
  },
  learnMore: {
    backgroundColor: '#A0F4D8',
    marginTop: 15,
    marginBottom: 20,
    borderRadius: 20,
    paddingVertical: 5,
    paddingHorizontal: 10,
  },
  promotionText: {
    fontStyle: 'italic',
    color: 'grey',
  },
});
