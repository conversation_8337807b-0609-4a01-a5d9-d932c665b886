import React from 'react';
import { StyleSheet, ScrollView, FlatList } from 'react-native';
import { spacing, dimensions } from '../../../styles';
import { Layout, BlockCard } from '../../../components';
import PostCard from './components/PostCard';

const CardItem = (props) => {
  const { attributes } = props;
  return attributes.horizontal ? (
    <Layout style={styles.horizontalContainer}>
      <PostCard {...props} />
    </Layout>
  ) : (
    <PostCard {...props} />
  );
};

const ScrollBlock = ({ attributes }) => {
  const { data, type, title, accessButton, reverse } = attributes;
  return (
    <BlockCard
      attributes={{
        title: title,
        accessButton: accessButton,
        childContainerStyle: styles.childContainer,
      }}>
      <FlatList
        horizontal={true}
        data={data}
        renderItem={({ item, index }) => {
          return (
            <CardItem
              attributes={{
                type: type,
                titleMeta: item.titleMeta,
                title: item.title,
                featureImg: item.featureImg,
                coverLetter: item.coverLetter,
                expert: item.expert,
                authorName: item.authorName,
                timeStamp: item.timeStamp,
              }}
            />
          );
        }}
        keyExtractor={(item, index) => index.toString()}
        showsHorizontalScrollIndicator={false}
      />
    </BlockCard>
  );
};

const styles = StyleSheet.create({
  horizontalContainer: {
    width: dimensions.fullWidth - 60,
    marginLeft: spacing.base,
  },
  childContainer: {
    paddingBottom: spacing.base,
  },
});

export default ScrollBlock;
export { CardItem };
