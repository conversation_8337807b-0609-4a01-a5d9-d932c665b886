import React from 'react';
import { applyFilters } from '@appmaker-xyz/core';
import { FlatList } from 'react-native';
import { useCart } from '../../hooks/cart/useCart';

export function ShopifyCartLineItems({
  attributes,
  BlockItem,
  onAction,
  currentAction,
  BlocksView,
  innerBlocks,
  blockData,
}) {
  const {
    lines
  } = useCart();
  attributes.specialPrice = applyFilters('product-list-special-price', false);
  const renderItem = React.useCallback(({ item, index }) => {
    return (
      <BlockItem
        key={`cart-line-items-item-${index}`}
        clientId={`cart-line-items-item-${index}`}
        block={{
          name: 'shopify/cart-line-item',
          attributes: {
            __experimentalDisableListItemParser: true,
          },
        }}
        blocksViewItemIndex={index}
        BlocksView={BlocksView}
        blockData={item}
        onAction={onAction}
      />
    );
  }, [BlocksView, onAction]);
  if (lines) {
    return <FlatList data={lines} renderItem={renderItem} />;
  }
  return null;
}
