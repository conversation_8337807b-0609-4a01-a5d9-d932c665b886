import { usePageState } from '@appmaker-xyz/core';
import { differenceAmountCalculator } from '../helper/index';
import { currencyHelper } from '../../helper/index';

//  order line item hook
export default function useOrderLineItem(props) {
  const order = usePageState((state) => state.blockData?.node);
  const lineItem = props?.attributes?.blockItem?.node;
  const vendor = lineItem?.variant?.product?.vendor;
  const quantity = lineItem?.quantity;
  const originalItemTotalAmount = parseFloat(
    lineItem?.originalTotalPrice?.amount / quantity,
  );
  const orderItemTotalAmount = parseFloat(
    lineItem?.discountedTotalPrice?.amount / quantity,
  );
  const totalLineItemPriceWithCurrency = currencyHelper(
    lineItem?.discountedTotalPrice?.amount,
    lineItem?.discountedTotalPrice?.currencyCode,
  );
  const lineItemPrice = currencyHelper(
    orderItemTotalAmount,
    lineItem?.discountedTotalPrice?.currencyCode,
  );
  const singleProductPrice = lineItem?.variant?.price.amount;
  const singleRegularPrice = lineItem?.variant?.compareAtPrice?.amount;
  const singleProductPriceWithCurrency = currencyHelper(
    lineItem?.variant?.price.amount,
    lineItem?.variant?.price?.currencyCode,
  );
  const singleRegularPriceWithCurrency = currencyHelper(
    lineItem?.variant?.compareAtPrice?.amount,
    lineItem?.variant?.compareAtPrice?.currencyCode,
  );



  const productPriceWithQuantity = lineItem?.originalTotalPrice?.amount;
  const discountAllocations = lineItem?.discountAllocations || [];
  const currencyCode = discountAllocations[0]?.allocatedAmount?.currencyCode;

  const singleProductPriceDiscount = discountAllocations.reduce((total, allocation) => {
    return total + parseFloat(allocation.allocatedAmount.amount);
  }, 0);

  const singleProductPriceDiscountWithCurrency = currencyHelper(
    singleProductPriceDiscount,
    currencyCode
 );

  const singleProductPriceWithDiscount = productPriceWithQuantity - singleProductPriceDiscount;
  const singleProductPriceWithDiscountWithCurrency = currencyHelper(
    singleProductPriceWithDiscount,
    currencyCode
  );
  

  const openProduct = () => {
    if (lineItem?.variant?.product?.id) {
      props?.onAction?.({
        action: 'OPEN_PRODUCT',
        productId: lineItem.variant.product.id,
        params: {
          id: lineItem.variant.product.id,
        },
      });
    }
  };
  return {
    openProduct,
    featureImg: lineItem.variant?.image?.url,
    title: lineItem?.title,
    totalLineItemPriceWithCurrency, // total price of line item of a product after discount multiplied by quantity
    singleProductPrice,
    singleRegularPrice,
    singleProductPriceWithCurrency,
    singleRegularPriceWithCurrency,
    singleProductPriceDiscountWithCurrency,
    lineItem,
    quantity,
    lineItemPrice,
    order,
    vendor,
    variantTitle:
      lineItem?.variant?.title === 'Default Title'
        ? ''
        : lineItem?.variant?.title,
    orderId: lineItem?.orderId,
    status: lineItem?.status,
    appmakerAction: lineItem?.appmakerAction,
    
    singleProductPriceDiscount,
    singleProductPriceWithDiscount,
    singleProductPriceWithDiscountWithCurrency
  };
}
