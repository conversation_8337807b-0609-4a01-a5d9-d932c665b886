import { color, spacing } from '../../styles';
import { blocks } from './addressBlocks';

const shippingAddressCreateGuest = {
  type: 'normal',
  title: 'Guest Create Address',
  attributes: {
    renderInScroll: true,
    renderType: 'normal',
    showLoadingTillData: true,
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: blocks,
  fixedFooter: {
    clientId: 'checkout-sumbit',
    name: 'appmaker/button',
    attributes: {
      content: 'Save Address Guest',
      loading: false,
      wholeContainerStyle: {
        borderRadius: 0,
      },
      appmakerAction: {
        action: 'SET_GUEST_ADDRESS',
      },
    },
  },
  dataSource: {
    source: 'shopify',
    attributes: {
      methodName: 'getLocalAddress',
      params: '',
    },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
  },
};
export default shippingAddressCreateGuest;
