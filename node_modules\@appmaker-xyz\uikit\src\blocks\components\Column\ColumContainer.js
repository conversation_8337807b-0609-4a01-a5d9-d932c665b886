import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout } from '@appmaker-xyz/uikit';
import ColumItem from './ColumItem';

const ColumContainer = ({ innerBlocks, BlockItem, onAction }) => {
  return (
    <Layout style={styles.container}>
      {innerBlocks.map((block, index) => {
        return (
          <ColumItem>
            <BlockItem
              key={index}
              block={block.innerBlocks[0]}
              onAction={onAction}
            />
          </ColumItem>
        );
      })}
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
  },
});

export default ColumContainer;
