import React, { useState, useEffect } from 'react';
import { StyleSheet } from 'react-native';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import {
  Layout,
  AppmakerText,
  BlockCard,
  AppTouchable,
  DropDown,
} from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';

const ButtonComponent = ({ attributes }) => {
  let {
    label,
    list,
    data,
    id,
    activeItem,
    setSelected,
    additionalText,
    pageDispatch,
  } = attributes;
  const { color, spacing } = useApThemeState();
  const [active, setActive] = useState(activeItem === id);
  const styles = allStyles({ color, spacing, active });

  let pickerData = [];
  data?.edges.length > 0 &&
    data?.edges.map((options, key) => {
      // push an object to the array
      pickerData[key] = {
        option: options.node.sellingPlan.name,
        id: options.node.sellingPlan.id,
      };
    });

  useEffect(() => {
    setActive(activeItem === id);
    if (activeItem === id) {
      pageDispatch({
        type: 'set_value',
        name: 'subscrtiption',
        value: pickerData.length > 0 ? pickerData[0].id : null,
      });
    }
  }, [activeItem]);

  const onSelect = () => {
    setActive(!active);
    setSelected(id);
    // const objIndex = contents.findIndex((obj) => obj.id == id);
    // contents[objIndex].active = true;
    // setContents(contents);
  };
  return (
    <AppTouchable style={styles.button} onPress={() => onSelect()}>
      <Layout style={styles.icon}>
        <Icon
          name={active ? 'check-circle' : 'circle'}
          size={spacing.md}
          color={active ? color.primary : color.grey}
        />
      </Layout>
      <Layout style={styles.content}>
        <AppmakerText
          category="bodyParagraphBold"
          style={active ? styles.mb : ''}>
          {label}
        </AppmakerText>
        <>
          {pickerData.length > 0 && (
            <DropDown
              attributes={{
                onSelect: (item) =>
                  pageDispatch({
                    type: 'set_value',
                    name: 'subscrtiption',
                    value: pickerData[item].id,
                  }),
                data: pickerData,
                containerStyle: styles.mb,
              }}
            />
          )}
          {additionalText && (
            <AppmakerText style={styles.mb}>{additionalText}</AppmakerText>
          )}
          {list &&
            list.map((item) => (
              <Layout style={styles.row}>
                <AppmakerText style={styles.bullet}>{'\u2022'}</AppmakerText>
                <AppmakerText category="bodyParagraphRegular">
                  {item}
                </AppmakerText>
              </Layout>
            ))}
        </>
      </Layout>
    </AppTouchable>
  );
};

const RadioSelect = ({ attributes, onPress, onAction, ...props }) => {
  const {
    appmakerAction,
    data,
    hideDefault,
    label = 'Mode of purchase',
  } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  const [activeItem, setActiveItem] = useState('onetime');
  const [contents, setContents] = useState([
    {
      id: 'onetime',
      label: 'One Time purchase',
      data: null,
    },
  ]);

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  useEffect(() => {
    let finalContents = contents;
    if (data?.edges?.length > 0) {
      if (hideDefault === 'true') {
        finalContents = [];
      }
      const checkRepeat = finalContents.find((ele) => ele.id === 'subscribe');
      if (!checkRepeat) {
        finalContents = [
          ...finalContents,
          {
            id: 'subscribe',
            label: 'Subscribe',
            data: data,
          },
        ];
        setActiveItem(hideDefault === 'true' ? 'subscribe' : 'onetime');
      }
      setContents(finalContents);
    }
  }, [data]);

  useEffect(() => {
    setContents(contents);
  }, [contents]);

  return (
    <BlockCard
      attributes={{
        title: label,
        accessButton: '',
        childContainerStyle: styles.container,
      }}>
      <Layout style={styles.borderContainer}>
        {contents.length > 0 &&
          contents.map((item, key) => (
            <ButtonComponent
              key={key}
              attributes={{
                label: item.label,
                id: item.id,
                data: item.data,
                activeItem: activeItem,
                pageDispatch: props.pageDispatch,
                setSelected: (item) => {
                  setActiveItem(item);
                },
                // list: list,
                // dropData: dropData,
              }}
            />
          ))}
      </Layout>
    </BlockCard>
  );
};

const allStyles = ({ spacing, color, active }) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: spacing.base,
      paddingBottom: spacing.base,
    },
    row: {
      flexDirection: 'row',
    },
    bullet: {
      marginRight: spacing.mini,
    },
    borderContainer: {
      borderWidth: 1,
      borderBottomWidth: 0,
      borderColor: color.light,
      borderRadius: spacing.small,
      overflow: 'hidden',
    },
    button: {
      backgroundColor: active ? `${color.primary}0D` : `${color.grey}0D`,
      paddingHorizontal: spacing.mini,
      paddingVertical: spacing.base,
      borderBottomColor: color.light,
      borderBottomWidth: 1,
      flexDirection: 'row',
    },
    icon: {
      paddingRight: spacing.small,
      paddingTop: spacing.nano,
    },
    content: {
      flex: 1,
    },
    mb: {
      marginBottom: spacing.small,
    },
  });

export default RadioSelect;
