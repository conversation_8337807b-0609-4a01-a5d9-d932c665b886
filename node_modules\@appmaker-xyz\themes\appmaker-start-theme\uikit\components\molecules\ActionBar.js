import React from 'react';
import Icon from 'react-native-vector-icons/Feather';
import AppTouchable from '../atoms/AppTouchable';
import ThemeText from '../atoms/ThemeText';
import { actionOnPressHelper } from '@appmaker-xyz/react-native';

const ActionBar = (props) => {
  const {
    color = '#fff',
    textColor = '#000',
    containerStyle,
    textStyle,
    attributes,
  } = props;
  const { title } = attributes;
  const finalContainerStyles = {
    backgroundColor: color,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
    ...containerStyle,
  };
  const finalTextStyles = {
    color: textColor,
    ...textStyle,
  };
  const onPress = actionOnPressHelper(props);
  return (
    <AppTouchable style={finalContainerStyles} onPress={onPress}>
      <ThemeText style={finalTextStyles} size={'lg'} fontFamily={'medium'}>
        {title}
      </ThemeText>
      <Icon name="chevron-right" size={18} />
    </AppTouchable>
  );
};

export default ActionBar;
