import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, Dimensions, Text } from 'react-native';
// TODO test import
import {
  TabView,
  TabBar,
  LayoutIcon,
  useApThemeState,
  AppmakerText,
} from '@appmaker-xyz/uikit';
import { useTranslation } from 'react-i18next';
import { useStateVars } from '@appmaker-xyz/core';
import { font } from '@appmaker-xyz/uikit/src/styles/index';

const SecondRoute = ({ block, BlocksView, onAction }) => {
  return (
    <BlocksView
      inAppPage={{
        blocks: block.innerBlocks,
        attributes: {
          renderType: 'normal',
          rootContainerStyle: {
            flex: 1,
          },
          contentContainerStyle: {
            flex: 1,
            // paddingHorizontal: 16,
          },
        },
      }}
      // template={}
      onAction={onAction}
      // attributes={}
    />
  );
};
const TabItemView = React.memo(SecondRoute);

const AppmakerTab = ({
  attributes,
  innerBlocks,
  BlocksView,
  onAction,
  data,
  ...props
}) => {
  const stateVars = useStateVars(
    {},
    {
      appStorageState: ['user'],
    },
  );
  const { user } = stateVars.appStorageState;
  const { t } = useTranslation();
  const [index, setIndex] = useState(0);
  const [routes, setRoutes] = useState({ routes: [] });
  const [searchQuery, setSearchQuery] = useState(undefined);
  const [cartItems, setCartItems] = useState({ count: 0 });
  const { tabBarPosition, swipeEnabled = true } = attributes;
  const { color, spacing, fonts, font } = useApThemeState();
  const inStyle = allStyles({ color, spacing, fonts, font });
  // const isLoggedIn = TEMP_FIX_useSelector((state) => state.user.isLoggedIn);
  const { dataAction } = attributes;
  useEffect(() => {
    const fetchTabs = () => {
      const finalTabs = [];
      if (innerBlocks) {
        innerBlocks.map((item, key) => {
          if (
            item.attributes?.__visibility &&
            item.attributes?.__visibility(user)
          ) {
            finalTabs.push({
              key: key + '-tab',
              title: t(item.attributes.title),
              id: item.id,
              block: item,
            });
          }
        });
      }
      if (data && data.length > 0) {
        data.map((item, key) => {
          const { action } = item;
          const block = {
            name: 'appmaker/tab-item',
            attributes: {},
            innerBlocks: [
              {
                name: 'appmaker/actionBlock',
                attributes: {
                  action: {
                    ...dataAction,
                    params: { ...dataAction?.params, ...action?.params },
                  },
                },
              },
            ],
          };
          finalTabs.push({
            key: key + '-tab',
            title: item.title,
            id: key,
            block: block,
          });
        });
      }
      setRoutes(finalTabs);
    };
    fetchTabs();
  }, [data, user]);
  const handleIndexChange = (index) => setIndex(index);

  const renderTabBar = (props) => {
    return (
      <TabBar
        {...props}
        renderIcon={({ route, focused, color }) => {
          // console.log();
          return route?.block?.attributes?.icon ? (
            <LayoutIcon
              attributes={{
                iconName: route?.block?.attributes?.icon,
                tabBar: true,
                iconSize: 22,
                iconColor: color,
              }}
            />
          ) : null;
        }}
        renderLabel={({ route, focused }) => (
          <AppmakerText
            category="highlighter2"
            status={focused ? 'dark' : 'grey'}
            style={inStyle.label}
            numberOfLines={2}>
            {route.title.toUpperCase()}
          </AppmakerText>
        )}
        // scrollEnabled
        indicatorStyle={inStyle.indicator}
        style={inStyle.tabbar}
        scrollEnabled={tabBarPosition === 'top'}
        tabStyle={tabBarPosition === 'top' && inStyle.tabStyle}
        activeColor={color.dark}
        inactiveColor={color.grey}
      />
    );
  };

  const renderScene = ({ route }) => {
    return (
      <View style={inStyle.container}>
        <TabItemView
          block={route.block}
          name={route.title}
          BlocksView={BlocksView}
          onAction={onAction}
        />
      </View>
    );
  };
  return (
    <View style={inStyle.container}>
      {routes.length > 0 && (
        <TabView
          style={inStyle.container}
          swipeEnabled={swipeEnabled}
          lazy
          navigationState={{ index, routes }}
          renderScene={renderScene}
          renderTabBar={renderTabBar}
          tabBarPosition={tabBarPosition}
          onIndexChange={handleIndexChange}
        />
      )}
    </View>
  );
};

const allStyles = ({ color }) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    tabStyle: {
      width: 'auto',
    },
    label: {
      margin: 0,
      fontFamily: font.family.bold,
      textAlign: 'center',
    },
    tabbar: {
      backgroundColor: color.white,
    },
    indicator: {
      backgroundColor: color.white,
    },
  });

export default AppmakerTab;
