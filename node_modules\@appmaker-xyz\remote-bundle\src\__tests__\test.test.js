import { loadBundle, resetCodeCache } from '../core';
import * as axios from 'axios';
// import * as ReactNative from 'react-native';
// import * as React from 'react';
// import * as AppmakerCore from '@appmaker-xyz/core';

// jest.mock('react-native');
// jest.mock('react');
// jest.mock('@appmaker-xyz/core');
jest.mock('axios');
const responseFn = jest.fn();
axios.get.mockImplementation(responseFn);
responseFn.mockResolvedValue({
  data: `
  exports.app = { 
    name: 'test app from url',
  }
  `,
});

describe('loadBundle', () => {
  // clear responseFn mock call count
  beforeEach(() => {
    resetCodeCache();
    responseFn.mockClear();
  });
  it('loadBundle string from url', async () => {
    const bundleCodeModules = await loadBundle({
      url: 'https://rest-endpoint.example/path/to/posts',
    });

    expect(bundleCodeModules).toEqual({
      app: {
        name: 'test app from url',
      },
    });
    expect(responseFn).toHaveBeenCalledTimes(1);
  });
  it('loadBundle cache string from url', async () => {
    await loadBundle({
      url: 'https://rest-endpoint.example/path/to/posts',
    });
    await loadBundle({
      url: 'https://rest-endpoint.example/path/to/posts',
    });
    await loadBundle({
      url: 'https://rest-endpoint.example/path/to/posts',
    });
    expect(responseFn).toHaveBeenCalledTimes(1);
  });
  it('loadBundle from direct CodeString', async () => {
    const codeSample = `
    exports.app = {
      name: 'test app from code',
    }
    `;
    const bundleCodeModules = await loadBundle({
      bundleCode: codeSample,
    });
    expect(bundleCodeModules).toEqual({
      app: {
        name: 'test app from code',
      },
    });
  });
});
