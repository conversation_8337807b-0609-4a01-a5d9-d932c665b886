import React from 'react';
import { StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { Layout, ThemeText, AppTouchable } from '../atoms';
import { getExtensionAsBoolean } from '@appmaker-xyz/core';

const PageHead = ({
  attributes,
  onPress,
  onAction,
  clientId,
  coreDispatch,
  ...blockItem
}) => {
  const {
    title,
    subTitle,
    layoutSelector = true,
    description = false,
  } = attributes;

  const titleContainerStyle = [styles.titleContainer];

  if (layoutSelector) {
    titleContainerStyle.push({ alignItems: 'flex-start' });
  }

  function setNumColumns(numColumns) {
    coreDispatch({
      type: 'SET_VALUE',
      name: 'numColumns',
      value: numColumns,
    });
  }

  const disableTitleNumLines = getExtensionAsBoolean?.(
    'shopify',
    'disablePlpPageHeadTitleLines',
    false,
  );

  return (
    <Layout style={styles.container}>
      <Layout style={titleContainerStyle}>
        {title ? (
          <ThemeText
            size="md"
            fontFamily="medium"
            numberOfLines={disableTitleNumLines ? 0 : 1}
            style={styles.title}>
            {title}
          </ThemeText>
        ) : null}
        {subTitle ? (
          <ThemeText size="sm" color="#A9AEB7" style={styles.subTitle}>
            {subTitle}
          </ThemeText>
        ) : null}
        {description ? (
          <ThemeText color="#4F4F4F" style={styles.description}>
            {description}
          </ThemeText>
        ) : null}
      </Layout>
      {layoutSelector ? (
        <Layout style={styles.row}>
          <AppTouchable
            style={[styles.icon, styles.active]}
            onPress={() => setNumColumns(1)}>
            <Icon name="square" size={18} color="#1B1B1B" />
          </AppTouchable>
          <AppTouchable
            style={[styles.icon, styles.active]}
            onPress={() => setNumColumns(2)}>
            <Icon name="grid" size={18} color="#1B1B1B" />
          </AppTouchable>
        </Layout>
      ) : null}
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 12,
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    borderRadius: 4,
  },
  titleContainer: {
    alignItems: 'center',
    flex: 1,
  },
  title: {
    textAlign: 'center',
  },
  icon: {
    padding: 4,
    marginStart: 6,
    borderRadius: 4,
  },
  active: {
    backgroundColor: '#E9EDF1',
  },
  row: {
    flexDirection: 'row',
  },
  subTitle: {
    marginTop: 4,
  },
  description: {
    marginTop: 12,
    marginBottom: 6,
    textAlign: 'center',
    fontSize: 13,
  },
});

export default PageHead;
