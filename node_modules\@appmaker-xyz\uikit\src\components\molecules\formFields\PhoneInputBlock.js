import React, { useState, useRef } from 'react';
import { StyleSheet, Platform } from 'react-native';
import PhoneInput from 'react-native-phone-number-input';
import { Layout, Input, Button, useApThemeState } from '@appmaker-xyz/uikit';
import { useEffect } from 'react';
import { usePageState } from '@appmaker-xyz/core';
import FloatingLabelInput from './FloatingLabelInput';
import { AppmakerText } from '../../index';
const PhoneInputBlock = ({ attributes, coreDispatch }) => {
  let {
    appmakerAction,
    defaultCountryCode,
    name,
    disabled,
    defaultValue,
    __appmakerCustomStyles = {},
  } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  const [confirm, setConfirm] = useState(null);
  const [phone, setPhone] = useState('');
  const [loading, setLoading] = useState(false);
  const [formatedPhone, setFormatedPhone] = useState('');
  const phoneInput = useRef(null);
  const parentName = attributes.parentName || '_formData';

  const inputValue = usePageState((state) => {
    return state[parentName] && state[parentName][name]
      ? state[parentName][name]
      : '';
  });
  function onChangeInput(_name, valueText) {
    coreDispatch({
      type: 'SET_VALUE',
      name: _name,
      value: valueText,
      parent: parentName,
    });
  }
  useEffect(() => {
    onChangeInput(name, defaultValue);
  }, [defaultValue]);

  useEffect(() => {
    if (formatedPhone || phone) {
      onChangeInput(name, {
        phone,
        formated: formatedPhone,
      });
    }
  }, [formatedPhone, phone]);
  return (
    <Layout style={styles.otpview}>
      {disabled ? (
        <Layout style={{ marginVertical: 12 }}>
          <AppmakerText>{defaultValue?.formated}</AppmakerText>
        </Layout>
      ) : (
        // <FloatingLabelInput
        //   attributes={{ defaultValue: defaultValue?.formated, disabled: true }}
        //   // coreDispatch={coreDispatch}
        // />
        <PhoneInput
          ref={phoneInput}
          defaultValue={inputValue}
          defaultCode={'IN'}
          layout="first"
          containerStyle={[
            styles.phoneNumberContainer,
            __appmakerCustomStyles?.input?.container,
          ]}
          textInputStyle={styles.textInputStyle}
          codeTextStyle={styles.font}
          onChangeText={(text) => {
            setPhone(text);
          }}
          onChangeFormattedText={(text) => {
            setFormatedPhone(text);
          }}
          textContainerStyle={styles.textContainerMain}
          // autoFocus
        />
      )}
    </Layout>
  );
};

const height = Platform.OS === 'ios' ? 54 : 50;

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      padding: spacing.base,
      flex: 1,
      alignItems: 'center',
      height: height,
    },
    phoneNumberContainer: {
      height: height,
      marginBottom: 20,
      borderWidth: 1,
      borderRadius: 5,
      borderColor: '#212121',
      width: '100%',
      overflow: 'hidden',
    },
    textInputStyle: {
      height: height,
    },
    otpview: {
      position: 'relative',
      width: '100%',
    },
    logo: {
      width: '50%',
      height: 80,
      alignSelf: 'center',
      resizeMode: 'contain',
      marginVertical: spacing.md,
    },
    signIn: {
      width: '100%',
      alignItems: 'center',
    },
    signInQue: {
      marginBottom: spacing.mini,
    },
    font: {
      lineHeight: Platform.OS === 'ios' ? 20 : 18,
    },
    textContainerMain: {
      height: height,
    },
  });

export default PhoneInputBlock;
