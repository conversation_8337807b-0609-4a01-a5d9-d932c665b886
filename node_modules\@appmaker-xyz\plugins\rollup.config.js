import { uglify } from 'rollup-plugin-uglify';
import localResolve from 'rollup-plugin-local-resolve';
import babel from '@rollup/plugin-babel';

export default {
    input: 'index.js',
    plugins: [
        localResolve(),
        uglify(),
        babel({
            presets: ["@babel/preset-react"],
        }),
    ],
    output: {
        // dir: 'build-output',
        file: 'build-output/index.js',
        format: 'cjs',
        // preserveModules: true, 
        // preserveModulesRoot: './',
    },
    minifyInternalExports: true,
};