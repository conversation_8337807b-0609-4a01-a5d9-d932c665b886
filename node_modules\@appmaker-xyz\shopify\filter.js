import { addFilter, applyFilters } from '@appmaker-xyz/core';
import { isEmpty } from 'lodash';
import { convertFilterParams } from './helper/filterParamsConvertor';
import { defaultPageMetadata } from './pageData/index';
import { removeProductsWithMetafield } from './helper/helper';
import { getExtensionAsBoolean } from '@appmaker-xyz/core';

addFilter(
  'shopify-product-scroller-attributes',
  'default-args',
  (attributes = {}) => {
    const defaultAttributes = {
      surface: 'product-scroller',
      referrer: {
        name: 'product-scroller',
        type: 'product-scroller',
        title: '',
      },
      groceryMode: false,
      uri: '{{blockItem.node.images.edges[0].node.url}}',
      id: '{{blockItem.node.id}}',
      hasPages: false,
      title: '{{blockItem.node.title}}',
      __appmakerStylesClassName: 'productGridWidgetCustomStyles',
      thumbnail_meta: {
        height: '{{blockItem.node.images.edges[0].node.height}}',
        width: '{{blockItem.node.images.edges[0].node.width}}',
      },
      productType:
        '{{blockItem.node.variants.edges.length > 1 ? "variable" : "normal"}}',
      in_stock: true,
      blockItem: '{{ blockItem }}',
      regularPrice:
        '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %> <% if( regularPrice>salePrice) { %><%= currencyHelper(regularPrice,blockItem.node.compareAtPriceRange.minVariantPrice.currencyCode) %><% } %>',
      salePrice:
        '{{ currencyHelper(blockItem.node.priceRange.minVariantPrice.amount,blockItem.node.priceRange.maxVariantPrice.currencyCode)}}',
      onSale:
        '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %><%=echo(regularPrice>salePrice)%>',
      salePercentage:
        '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %><% if( regularPrice>salePrice) { %><%= parseInt(100-((salePrice/regularPrice)*100 ))%> <%="%"%>%><% } %>',
    };
    return {
      ...defaultAttributes,
      ...attributes,
    };
  },
);
const applyHandlers = ({ pageData, pageId}) => {
  try {
    const enableHeaderHandler = getExtensionAsBoolean?.(
      'app-settings',
      'enable_header_icon_handler',
      false,
    );
    /**
     * Injecting Header handler to all block if enabled.
     * this handler is required to dynamically change header logo based on pages and conditions
     */
    if (enableHeaderHandler) {
      const headerBlockName = 'appmaker/header-icon-handler';
      const HeaderHandlerBlock = {
        name: 'appmaker/header-icon-handler',
        attributes: {
          iconName: 'APP_TITLE_IMAGE',
        },
      };
      pageData = injectHandler({
        pageData,
        blockName: headerBlockName,
        block: HeaderHandlerBlock,
      });
    }
    const enableBottomBarHandler = getExtensionAsBoolean?.(
      'app-settings',
      'enable_bottom_bar_handler',
      false,
    );
    /**
     * Injecting Bottom bar handler to all block if enabled.
     * this handler is required to dynamically change Bottom Tab Visibility based on pages and conditions
     */
    if (enableBottomBarHandler) {
      const bottomTabHandler = 'appmaker/bottom-tabs-handler';
      const BottomTabHandlerBlock = {
        name: bottomTabHandler,
        attributes: {
          source: 'default',
          isVisible:
            pageId === 'CheckoutPaymentWebview' || pageId === 'ZoomableImage'
              ? false
              : true,
        },
      };
      pageData = injectHandler({
        pageData,
        blockName: bottomTabHandler,
        block: BottomTabHandlerBlock,
      });
    }

    const enableTopBarHandler = getExtensionAsBoolean?.(
      'app-settings',
      'enable_top_bar_handler',
      false,
    );
    if (enableTopBarHandler) {
      const ignorePIDS = applyFilters('custom-top-bar-pageId-hide', []);
      const topBlockName = 'appmaker/top-bar-handler';
      const TopHandlerBlock = {
        name: topBlockName,
        attributes: {
          isVisible: !ignorePIDS.includes(pageId),
        },
      };
      pageData = injectHandler({
        pageData,
        blockName: topBlockName,
        block: TopHandlerBlock,
      });
    }
  } catch (e) {}
  return pageData;
};

const injectHandler = ({ pageData: data, blockName, block }) => {
  if (
    !data?.stickyFooter?.blocks?.some?.((block) => block?.name === blockName) &&
    !data?.blocks?.some?.((block) => block?.name === blockName)
  ) {
    let pageData = { ...data };
    // Spreading this wide ;) because some deep objects are protected in some cases
    const newStickyFooter = pageData?.stickyFooter
      ? {
          ...pageData.stickyFooter,
          blocks: pageData.stickyFooter?.blocks
            ? [...pageData.stickyFooter.blocks]
            : [],
        }
      : { blocks: [] };
    if (newStickyFooter?.blocks) {
      newStickyFooter.blocks.push(block);
    }
    return {
      ...pageData,
      stickyFooter: newStickyFooter,
    };
  }
  return data;
};

addFilter(
  'inapp-page-data-response',
  'shopify-pages-custom-attributes',
  (pageData, { pageId }) => {
    pageData = applyHandlers({ pageData, pageId });
    const shopifyPageMetadata = defaultPageMetadata[pageId] || {};
    const shouldLoadDataSource = applyFilters(
      'appmaker-default-page-datasource',
      true,
      {
        pageId,
        pageData,
      },
    );
    if (
      isEmpty(pageData.dataSource) &&
      shopifyPageMetadata.dataSource &&
      shouldLoadDataSource
    ) {
      pageData.dataSource = shopifyPageMetadata.dataSource;
    }
    if (isEmpty(pageData.attributes) && shopifyPageMetadata.attributes) {
      pageData.attributes = shopifyPageMetadata.attributes;
    }
    if (isEmpty(pageData.pageTypes) && shopifyPageMetadata.pageTypes) {
      pageData.pageTypes = shopifyPageMetadata.pageTypes;
    }
    if (isEmpty(pageData.pageVariants) && shopifyPageMetadata.pageVariants) {
      pageData.pageVariants = shopifyPageMetadata.pageVariants;
    }
    return pageData;
  },
);
addFilter(
  'shopify-collection-filter-params',
  'shopify-collection-filter-params-native',
  (input, { filters }) => {
    const newFilters = convertFilterParams(filters);
    return newFilters;
  },
);

addFilter(
  'shopify-response-data',
  'shopify-remove-products-tag-based',
  (response, { method, params }) => {
    let data = response;

    // Get configuration flags
    const hideOosProducts = getExtensionAsBoolean?.(
      'shopify',
      'hide_oos_products',
      false,
    );
    const showHiddenProducts = params?.showHiddenProducts;
    const disableProductHide = getExtensionAsBoolean?.(
      'shopify',
      'disable_product_hide',
      false,
    );

    // Handle OOS products for collection
    if (hideOosProducts && method === 'productsByCollection') {
      if (data?.data?.data?.collection?.products?.edges?.length > 0) {
        data.data.data.collection.products.edges =
          data.data.data.collection.products.edges.filter(
            (item) => item?.node?.availableForSale === true,
          );
      }
    }

    // Handle hidden products
    if (!disableProductHide && !showHiddenProducts) {
      if (data?.data?.data?.productRecommendations?.length > 0) {
        let productRecommendations = data?.data?.data?.productRecommendations;
        let truncatedRecommendationsProducts = removeProductsWithMetafield(
          productRecommendations,
          'appmaker_hide_product_status',
        );
        data.data.data.productRecommendations =
          truncatedRecommendationsProducts;
        return data;
      }
      if (data?.data?.data?.products?.edges?.length > 0) {
        let products = data?.data?.data?.products?.edges;
        let truncatedProducts = removeProductsWithMetafield(
          products,
          'appmaker_hide_product_status',
        );
        data.data.data.products.edges = truncatedProducts;
        if (data?.data?.data?.collection?.products?.edges?.length > 0) {
          data.data.data.collection.products.edges = truncatedProducts;
        }
      }
    }
    return data;
  },
);
