import React from 'react';
import { CheckBox } from '@appmaker-xyz/ui';
import { Control, Controller } from 'react-hook-form';
import { StyleSheet, StyleSheetProperties, Text } from 'react-native';
type Props = {
  control: Control;
  name: string;
  label: string;
  style?: any;
};
export function CheckBoxFormItem({ control, name, label, style = {} }: Props) {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { onChange, value, onBlur }, fieldState }) => (
        <CheckBox
          small={true}
          label={label}
          onValueChange={onChange}
          value={value}
          error={fieldState.error && fieldState.error.message}
        />
      )}
    />
  );
}
const defaultStyles = StyleSheet.create({
  container: {},
  input: {
    backgroundColor: '#E8ECF4',
    borderRadius: 6,
    overflow: 'hidden',
    position: 'relative',
  },
  eyeIcon: {
    position: 'absolute',
    right: 12,
    top: 10,
  },
  forgotButton: {
    alignSelf: 'flex-end',
    marginBottom: 16,
    marginTop: -6,
  },
  signUpButton: {
    marginTop: 32,
    alignItems: 'center',
  },
});
