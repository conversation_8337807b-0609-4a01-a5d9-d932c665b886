import React from 'react';
import { StyleSheet, I18nManager, Image } from 'react-native';
import { AppmakerText, AppTouchable, Layout } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { testProps } from '@appmaker-xyz/core';
import { SvgXml } from 'react-native-svg';

const LayoutIcon = ({ attributes, onPress, onAction, clientId, ...props }) => {
  const {
    iconName,
    itemCount,
    appmakerAction,
    overlay,
    tabBar,
    iconSize = 20,
    iconColor,
    activeColor,
    activeTextColor,
    localImage,
    svgIcon,
    itemCountCustomStyle,
    svgXml,
  } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing, overlay });
  const containerStyles = [styles.container];
  const counterStyles = [styles.counterText];

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  if (tabBar) {
    containerStyles.push({ marginStart: 0, padding: 0 });
  }
  if (activeColor) {
    counterStyles.push({ backgroundColor: activeColor });
  }
  if (itemCountCustomStyle) {
    counterStyles.push(itemCountCustomStyle);
  }
  counterStyles.push(I18nManager.isRTL ? { left: 0 } : { right: 0 });
  // return null;
  function icon(params) {
    if (svgIcon) {
      return svgIcon;
    }
    if (svgXml) {
      return <SvgXml xml={svgXml} />;
    }
    if (localImage) {
      return (
        <Image
          style={{
            width: iconSize,
            height: iconSize,
          }}
          source={localImage}
        />
      );
    } else {
      return (
        <Icon
          name={iconName}
          size={iconSize}
          color={iconColor ? iconColor : color.dark}
        />
      );
    }
  }
  return (
    <AppTouchable
      {...testProps(`toolbaricon-${clientId}`)}
      style={containerStyles}
      onPress={onPressHandle}
      clientId={clientId}>
      {icon()}
      {itemCount ? (
        <Layout style={counterStyles}>
          <AppmakerText
            testId={`${clientId}-count`}
            category="highlighter2"
            status={!activeTextColor && 'white'}
            style={activeTextColor && { color: activeTextColor }}>
            {itemCount}
          </AppmakerText>
        </Layout>
      ) : null}
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color, overlay }) =>
  StyleSheet.create({
    container: {
      position: 'relative',
      padding: overlay ? spacing.base : spacing.small,
      marginStart: spacing.mini,
      backgroundColor: overlay && color.white,
      borderRadius: overlay && spacing.small * 4,
      overflow: overlay ? 'hidden' : null,
    },
    counterText: {
      position: 'absolute',
      top: 0,
      backgroundColor: color.primary,
      width: spacing.md,
      height: spacing.md,
      borderRadius: spacing.nano * 2,
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

export default LayoutIcon;
