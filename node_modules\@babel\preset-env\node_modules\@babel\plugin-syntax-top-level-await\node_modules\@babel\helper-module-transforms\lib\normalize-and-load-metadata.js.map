{"version": 3, "names": ["hasExports", "metadata", "isSideEffectImport", "source", "imports", "size", "importsNamespace", "reexports", "reexportNamespace", "reexportAll", "validateImportInteropOption", "importInterop", "Error", "resolveImportInterop", "filename", "normalizeModuleAndLoadMetadata", "programPath", "exportName", "initializeReexports", "lazy", "esNamespaceOnly", "scope", "generateUidIdentifier", "name", "stringSpecifiers", "Set", "nameAnonymousExports", "local", "sources", "getModuleMetadata", "removeImportExportDeclarations", "values", "next", "value", "resolvedInterop", "interop", "exportNameListName", "getExportSpecifierName", "path", "isIdentifier", "node", "isStringLiteral", "stringValue", "isIdentifierName", "add", "type", "assertExportSpecifier", "isExportSpecifier", "isExportNamespaceSpecifier", "buildCodeFrameError", "localData", "getLocalExportMetadata", "sourceData", "Map", "getData", "sourceNode", "data", "get", "basename", "extname", "loc", "referenced", "set", "for<PERSON>ach", "child", "isImportDeclaration", "spec", "isImportDefaultSpecifier", "localName", "reexport", "delete", "names", "isImportNamespaceSpecifier", "isImportSpecifier", "importName", "isExportAllDeclaration", "isExportNamedDeclaration", "isExportDefaultDeclaration", "needsDefault", "needsNamed", "test", "Array", "isArray", "indexOf", "bindingKindLookup", "kind", "declaration", "isFunctionDeclaration", "isClassDeclaration", "isVariableDeclaration", "Object", "keys", "getOuterBindingIdentifiers", "localMetadata", "getLocalMetadata", "idPath", "undefined", "ids", "getOuterBindingIdentifierPaths", "push", "exported", "splitExportDeclaration", "remove", "_blockHoist", "replaceWith"], "sources": ["../src/normalize-and-load-metadata.ts"], "sourcesContent": ["import { basename, extname } from \"path\";\nimport type * as t from \"@babel/types\";\n\nimport { isIdentifierName } from \"@babel/helper-validator-identifier\";\nimport splitExportDeclaration from \"@babel/helper-split-export-declaration\";\nimport type { NodePath } from \"@babel/traverse\";\n\nexport interface ModuleMetadata {\n  exportName: string;\n  // The name of the variable that will reference an object containing export names.\n  exportNameListName: null | string;\n  hasExports: boolean;\n  // Lookup from local binding to export information.\n  local: Map<string, LocalExportMetadata>;\n  // Lookup of source file to source file metadata.\n  source: Map<string, SourceModuleMetadata>;\n  // List of names that should only be printed as string literals.\n  // i.e. `import { \"any unicode\" as foo } from \"some-module\"`\n  // `stringSpecifiers` is Set(1) [\"any unicode\"]\n  // In most cases `stringSpecifiers` is an empty Set\n  stringSpecifiers: Set<string>;\n}\n\nexport type InteropType =\n  | \"default\" // Babel interop for default-only imports\n  | \"namespace\" // Babel interop for namespace or default+named imports\n  | \"node-default\" // Node.js interop for default-only imports\n  | \"node-namespace\" // Node.js interop for namespace or default+named imports\n  | \"none\"; // No interop, or named-only imports\n\nexport type ImportInterop =\n  | \"none\"\n  | \"babel\"\n  | \"node\"\n  | ((source: string, filename?: string) => \"none\" | \"babel\" | \"node\");\n\nexport type Lazy = boolean | string[] | ((source: string) => boolean);\n\nexport interface SourceModuleMetadata {\n  // A unique variable name to use for this namespace object. Centralized for simplicity.\n  name: string;\n  loc: t.SourceLocation | undefined | null;\n  interop: InteropType;\n  // Local binding to reference from this source namespace. Key: Local name, value: Import name\n  imports: Map<string, string>;\n  // Local names that reference namespace object.\n  importsNamespace: Set<string>;\n  // Reexports to create for namespace. Key: Export name, value: Import name\n  reexports: Map<string, string>;\n  // List of names to re-export namespace as.\n  reexportNamespace: Set<string>;\n  // Tracks if the source should be re-exported.\n  reexportAll: null | {\n    loc: t.SourceLocation | undefined | null;\n  };\n  lazy?: Lazy;\n  referenced: boolean;\n}\n\nexport interface LocalExportMetadata {\n  names: Array<string>; // names of exports,\n  kind: \"import\" | \"hoisted\" | \"block\" | \"var\";\n}\n\n/**\n * Check if the module has any exports that need handling.\n */\nexport function hasExports(metadata: ModuleMetadata) {\n  return metadata.hasExports;\n}\n\n/**\n * Check if a given source is an anonymous import, e.g. \"import 'foo';\"\n */\nexport function isSideEffectImport(source: SourceModuleMetadata) {\n  return (\n    source.imports.size === 0 &&\n    source.importsNamespace.size === 0 &&\n    source.reexports.size === 0 &&\n    source.reexportNamespace.size === 0 &&\n    !source.reexportAll\n  );\n}\n\nexport function validateImportInteropOption(\n  importInterop: any,\n): importInterop is ImportInterop {\n  if (\n    typeof importInterop !== \"function\" &&\n    importInterop !== \"none\" &&\n    importInterop !== \"babel\" &&\n    importInterop !== \"node\"\n  ) {\n    throw new Error(\n      `.importInterop must be one of \"none\", \"babel\", \"node\", or a function returning one of those values (received ${importInterop}).`,\n    );\n  }\n  return importInterop;\n}\n\nfunction resolveImportInterop(\n  importInterop: ImportInterop,\n  source: string,\n  filename: string | undefined,\n) {\n  if (typeof importInterop === \"function\") {\n    return validateImportInteropOption(importInterop(source, filename));\n  }\n  return importInterop;\n}\n\n/**\n * Remove all imports and exports from the file, and return all metadata\n * needed to reconstruct the module's behavior.\n */\nexport default function normalizeModuleAndLoadMetadata(\n  programPath: NodePath<t.Program>,\n  exportName: string,\n  {\n    importInterop,\n    initializeReexports = false,\n    lazy = false,\n    esNamespaceOnly = false,\n    filename,\n  }: {\n    importInterop: ImportInterop;\n    initializeReexports: boolean | void;\n    lazy: Lazy;\n    esNamespaceOnly: boolean;\n    filename: string;\n  },\n): ModuleMetadata {\n  if (!exportName) {\n    exportName = programPath.scope.generateUidIdentifier(\"exports\").name;\n  }\n  const stringSpecifiers = new Set<string>();\n\n  nameAnonymousExports(programPath);\n\n  const { local, sources, hasExports } = getModuleMetadata(\n    programPath,\n    { initializeReexports, lazy },\n    stringSpecifiers,\n  );\n\n  removeImportExportDeclarations(programPath);\n\n  // Reuse the imported namespace name if there is one.\n  for (const [source, metadata] of sources) {\n    if (metadata.importsNamespace.size > 0) {\n      // This is kind of gross. If we stop using `loose: true` we should\n      // just make this destructuring assignment.\n      metadata.name = metadata.importsNamespace.values().next().value;\n    }\n\n    const resolvedInterop = resolveImportInterop(\n      importInterop,\n      source,\n      filename,\n    );\n\n    if (resolvedInterop === \"none\") {\n      metadata.interop = \"none\";\n    } else if (resolvedInterop === \"node\" && metadata.interop === \"namespace\") {\n      metadata.interop = \"node-namespace\";\n    } else if (resolvedInterop === \"node\" && metadata.interop === \"default\") {\n      metadata.interop = \"node-default\";\n    } else if (esNamespaceOnly && metadata.interop === \"namespace\") {\n      // Both the default and namespace interops pass through __esModule\n      // objects, but the namespace interop is used to enable Babel's\n      // destructuring-like interop behavior for normal CommonJS.\n      // Since some tooling has started to remove that behavior, we expose\n      // it as the `esNamespace` option.\n      metadata.interop = \"default\";\n    }\n  }\n\n  return {\n    exportName,\n    exportNameListName: null,\n    hasExports,\n    local,\n    source: sources,\n    stringSpecifiers,\n  };\n}\n\nfunction getExportSpecifierName(\n  path: NodePath,\n  stringSpecifiers: Set<string>,\n): string {\n  if (path.isIdentifier()) {\n    return path.node.name;\n  } else if (path.isStringLiteral()) {\n    const stringValue = path.node.value;\n    // add specifier value to `stringSpecifiers` only when it can not be converted to an identifier name\n    // i.e In `import { \"foo\" as bar }`\n    // we do not consider `\"foo\"` to be a `stringSpecifier` because we can treat it as\n    // `import { foo as bar }`\n    // This helps minimize the size of `stringSpecifiers` and reduce overhead of checking valid identifier names\n    // when building transpiled code from metadata\n    if (!isIdentifierName(stringValue)) {\n      stringSpecifiers.add(stringValue);\n    }\n    return stringValue;\n  } else {\n    throw new Error(\n      `Expected export specifier to be either Identifier or StringLiteral, got ${path.node.type}`,\n    );\n  }\n}\n\nfunction assertExportSpecifier(\n  path: NodePath,\n): asserts path is NodePath<t.ExportSpecifier> {\n  if (path.isExportSpecifier()) {\n    return;\n  } else if (path.isExportNamespaceSpecifier()) {\n    throw path.buildCodeFrameError(\n      \"Export namespace should be first transformed by `@babel/plugin-proposal-export-namespace-from`.\",\n    );\n  } else {\n    throw path.buildCodeFrameError(\"Unexpected export specifier type\");\n  }\n}\n\n/**\n * Get metadata about the imports and exports present in this module.\n */\nfunction getModuleMetadata(\n  programPath: NodePath<t.Program>,\n  {\n    lazy,\n    initializeReexports,\n  }: {\n    // todo(flow-ts) changed from boolean, to match expected usage inside the function\n    lazy: boolean | string[] | ((source: string) => boolean);\n    initializeReexports: boolean | void;\n  },\n  stringSpecifiers: Set<string>,\n) {\n  const localData = getLocalExportMetadata(\n    programPath,\n    initializeReexports,\n    stringSpecifiers,\n  );\n\n  const sourceData = new Map<string, SourceModuleMetadata>();\n  const getData = (sourceNode: t.StringLiteral) => {\n    const source = sourceNode.value;\n\n    let data = sourceData.get(source);\n    if (!data) {\n      data = {\n        name: programPath.scope.generateUidIdentifier(\n          basename(source, extname(source)),\n        ).name,\n\n        interop: \"none\",\n\n        loc: null,\n\n        // Data about the requested sources and names.\n        imports: new Map(),\n        importsNamespace: new Set(),\n\n        // Metadata about data that is passed directly from source to export.\n        reexports: new Map(),\n        reexportNamespace: new Set(),\n        reexportAll: null,\n\n        lazy: false,\n\n        referenced: false,\n      };\n      sourceData.set(source, data);\n    }\n    return data;\n  };\n  let hasExports = false;\n  programPath.get(\"body\").forEach(child => {\n    if (child.isImportDeclaration()) {\n      const data = getData(child.node.source);\n      if (!data.loc) data.loc = child.node.loc;\n\n      child.get(\"specifiers\").forEach(spec => {\n        if (spec.isImportDefaultSpecifier()) {\n          const localName = spec.get(\"local\").node.name;\n\n          data.imports.set(localName, \"default\");\n\n          const reexport = localData.get(localName);\n          if (reexport) {\n            localData.delete(localName);\n\n            reexport.names.forEach(name => {\n              data.reexports.set(name, \"default\");\n            });\n            data.referenced = true;\n          }\n        } else if (spec.isImportNamespaceSpecifier()) {\n          const localName = spec.get(\"local\").node.name;\n\n          data.importsNamespace.add(localName);\n          const reexport = localData.get(localName);\n          if (reexport) {\n            localData.delete(localName);\n\n            reexport.names.forEach(name => {\n              data.reexportNamespace.add(name);\n            });\n            data.referenced = true;\n          }\n        } else if (spec.isImportSpecifier()) {\n          const importName = getExportSpecifierName(\n            spec.get(\"imported\"),\n            stringSpecifiers,\n          );\n          const localName = spec.get(\"local\").node.name;\n\n          data.imports.set(localName, importName);\n\n          const reexport = localData.get(localName);\n          if (reexport) {\n            localData.delete(localName);\n\n            reexport.names.forEach(name => {\n              data.reexports.set(name, importName);\n            });\n            data.referenced = true;\n          }\n        }\n      });\n    } else if (child.isExportAllDeclaration()) {\n      hasExports = true;\n      const data = getData(child.node.source);\n      if (!data.loc) data.loc = child.node.loc;\n\n      data.reexportAll = {\n        loc: child.node.loc,\n      };\n      data.referenced = true;\n    } else if (child.isExportNamedDeclaration() && child.node.source) {\n      hasExports = true;\n      const data = getData(child.node.source);\n      if (!data.loc) data.loc = child.node.loc;\n\n      child.get(\"specifiers\").forEach(spec => {\n        assertExportSpecifier(spec);\n        const importName = getExportSpecifierName(\n          spec.get(\"local\"),\n          stringSpecifiers,\n        );\n        const exportName = getExportSpecifierName(\n          spec.get(\"exported\"),\n          stringSpecifiers,\n        );\n\n        data.reexports.set(exportName, importName);\n        data.referenced = true;\n\n        if (exportName === \"__esModule\") {\n          throw spec\n            .get(\"exported\")\n            .buildCodeFrameError('Illegal export \"__esModule\".');\n        }\n      });\n    } else if (\n      child.isExportNamedDeclaration() ||\n      child.isExportDefaultDeclaration()\n    ) {\n      hasExports = true;\n    }\n  });\n\n  for (const metadata of sourceData.values()) {\n    let needsDefault = false;\n    let needsNamed = false;\n\n    if (metadata.importsNamespace.size > 0) {\n      needsDefault = true;\n      needsNamed = true;\n    }\n\n    if (metadata.reexportAll) {\n      needsNamed = true;\n    }\n\n    for (const importName of metadata.imports.values()) {\n      if (importName === \"default\") needsDefault = true;\n      else needsNamed = true;\n    }\n    for (const importName of metadata.reexports.values()) {\n      if (importName === \"default\") needsDefault = true;\n      else needsNamed = true;\n    }\n\n    if (needsDefault && needsNamed) {\n      // TODO(logan): Using the namespace interop here is unfortunate. Revisit.\n      metadata.interop = \"namespace\";\n    } else if (needsDefault) {\n      metadata.interop = \"default\";\n    }\n  }\n\n  for (const [source, metadata] of sourceData) {\n    if (\n      lazy !== false &&\n      !(isSideEffectImport(metadata) || metadata.reexportAll)\n    ) {\n      if (lazy === true) {\n        // 'true' means that local relative files are eagerly loaded and\n        // dependency modules are loaded lazily.\n        metadata.lazy = !/\\./.test(source);\n      } else if (Array.isArray(lazy)) {\n        metadata.lazy = lazy.indexOf(source) !== -1;\n      } else if (typeof lazy === \"function\") {\n        metadata.lazy = lazy(source);\n      } else {\n        throw new Error(`.lazy must be a boolean, string array, or function`);\n      }\n    }\n  }\n\n  return {\n    hasExports,\n    local: localData,\n    sources: sourceData,\n  };\n}\n\ntype ModuleBindingKind = \"import\" | \"hoisted\" | \"block\" | \"var\";\n/**\n * Get metadata about local variables that are exported.\n */\nfunction getLocalExportMetadata(\n  programPath: NodePath<t.Program>,\n  initializeReexports: boolean | void,\n  stringSpecifiers: Set<string>,\n): Map<string, LocalExportMetadata> {\n  const bindingKindLookup = new Map();\n\n  programPath.get(\"body\").forEach((child: NodePath) => {\n    let kind: ModuleBindingKind;\n    if (child.isImportDeclaration()) {\n      kind = \"import\";\n    } else {\n      if (child.isExportDefaultDeclaration()) {\n        child = child.get(\"declaration\");\n      }\n      if (child.isExportNamedDeclaration()) {\n        if (child.node.declaration) {\n          child = child.get(\"declaration\");\n        } else if (\n          initializeReexports &&\n          child.node.source &&\n          child.get(\"source\").isStringLiteral()\n        ) {\n          child.get(\"specifiers\").forEach(spec => {\n            assertExportSpecifier(spec);\n            bindingKindLookup.set(spec.get(\"local\").node.name, \"block\");\n          });\n          return;\n        }\n      }\n\n      if (child.isFunctionDeclaration()) {\n        kind = \"hoisted\";\n      } else if (child.isClassDeclaration()) {\n        kind = \"block\";\n      } else if (child.isVariableDeclaration({ kind: \"var\" })) {\n        kind = \"var\";\n      } else if (child.isVariableDeclaration()) {\n        kind = \"block\";\n      } else {\n        return;\n      }\n    }\n\n    Object.keys(child.getOuterBindingIdentifiers()).forEach(name => {\n      bindingKindLookup.set(name, kind);\n    });\n  });\n\n  const localMetadata = new Map();\n  const getLocalMetadata = (idPath: NodePath<t.Identifier>) => {\n    const localName = idPath.node.name;\n    let metadata = localMetadata.get(localName);\n\n    if (!metadata) {\n      const kind = bindingKindLookup.get(localName);\n\n      if (kind === undefined) {\n        throw idPath.buildCodeFrameError(\n          `Exporting local \"${localName}\", which is not declared.`,\n        );\n      }\n\n      metadata = {\n        names: [],\n        kind,\n      };\n      localMetadata.set(localName, metadata);\n    }\n    return metadata;\n  };\n\n  programPath.get(\"body\").forEach(child => {\n    if (\n      child.isExportNamedDeclaration() &&\n      (initializeReexports || !child.node.source)\n    ) {\n      if (child.node.declaration) {\n        const declaration = child.get(\"declaration\");\n        const ids = declaration.getOuterBindingIdentifierPaths();\n        Object.keys(ids).forEach(name => {\n          if (name === \"__esModule\") {\n            throw declaration.buildCodeFrameError(\n              'Illegal export \"__esModule\".',\n            );\n          }\n          getLocalMetadata(ids[name]).names.push(name);\n        });\n      } else {\n        child.get(\"specifiers\").forEach(spec => {\n          const local = spec.get(\"local\");\n          const exported = spec.get(\"exported\");\n          const localMetadata = getLocalMetadata(local);\n          const exportName = getExportSpecifierName(exported, stringSpecifiers);\n\n          if (exportName === \"__esModule\") {\n            throw exported.buildCodeFrameError('Illegal export \"__esModule\".');\n          }\n          localMetadata.names.push(exportName);\n        });\n      }\n    } else if (child.isExportDefaultDeclaration()) {\n      const declaration = child.get(\"declaration\");\n      if (\n        declaration.isFunctionDeclaration() ||\n        declaration.isClassDeclaration()\n      ) {\n        // @ts-expect-error todo(flow->ts): improve babel-types\n        getLocalMetadata(declaration.get(\"id\")).names.push(\"default\");\n      } else {\n        // These should have been removed by the nameAnonymousExports() call.\n        throw declaration.buildCodeFrameError(\n          \"Unexpected default expression export.\",\n        );\n      }\n    }\n  });\n  return localMetadata;\n}\n\n/**\n * Ensure that all exported values have local binding names.\n */\nfunction nameAnonymousExports(programPath: NodePath<t.Program>) {\n  // Name anonymous exported locals.\n  programPath.get(\"body\").forEach(child => {\n    if (!child.isExportDefaultDeclaration()) return;\n    splitExportDeclaration(child);\n  });\n}\n\nfunction removeImportExportDeclarations(programPath: NodePath<t.Program>) {\n  programPath.get(\"body\").forEach(child => {\n    if (child.isImportDeclaration()) {\n      child.remove();\n    } else if (child.isExportNamedDeclaration()) {\n      if (child.node.declaration) {\n        // @ts-expect-error todo(flow->ts): avoid mutations\n        child.node.declaration._blockHoist = child.node._blockHoist;\n        child.replaceWith(child.node.declaration);\n      } else {\n        child.remove();\n      }\n    } else if (child.isExportDefaultDeclaration()) {\n      // export default foo;\n      const declaration = child.get(\"declaration\");\n      if (\n        declaration.isFunctionDeclaration() ||\n        declaration.isClassDeclaration()\n      ) {\n        // @ts-expect-error todo(flow->ts): avoid mutations\n        declaration._blockHoist = child.node._blockHoist;\n        child.replaceWith(\n          declaration as NodePath<t.FunctionDeclaration | t.ClassDeclaration>,\n        );\n      } else {\n        // These should have been removed by the nameAnonymousExports() call.\n        throw declaration.buildCodeFrameError(\n          \"Unexpected default expression export.\",\n        );\n      }\n    } else if (child.isExportAllDeclaration()) {\n      child.remove();\n    }\n  });\n}\n"], "mappings": ";;;;;;;;;AAAA;AAGA;AACA;AA+DO,SAASA,UAAU,CAACC,QAAwB,EAAE;EACnD,OAAOA,QAAQ,CAACD,UAAU;AAC5B;AAKO,SAASE,kBAAkB,CAACC,MAA4B,EAAE;EAC/D,OACEA,MAAM,CAACC,OAAO,CAACC,IAAI,KAAK,CAAC,IACzBF,MAAM,CAACG,gBAAgB,CAACD,IAAI,KAAK,CAAC,IAClCF,MAAM,CAACI,SAAS,CAACF,IAAI,KAAK,CAAC,IAC3BF,MAAM,CAACK,iBAAiB,CAACH,IAAI,KAAK,CAAC,IACnC,CAACF,MAAM,CAACM,WAAW;AAEvB;AAEO,SAASC,2BAA2B,CACzCC,aAAkB,EACc;EAChC,IACE,OAAOA,aAAa,KAAK,UAAU,IACnCA,aAAa,KAAK,MAAM,IACxBA,aAAa,KAAK,OAAO,IACzBA,aAAa,KAAK,MAAM,EACxB;IACA,MAAM,IAAIC,KAAK,CACZ,gHAA+GD,aAAc,IAAG,CAClI;EACH;EACA,OAAOA,aAAa;AACtB;AAEA,SAASE,oBAAoB,CAC3BF,aAA4B,EAC5BR,MAAc,EACdW,QAA4B,EAC5B;EACA,IAAI,OAAOH,aAAa,KAAK,UAAU,EAAE;IACvC,OAAOD,2BAA2B,CAACC,aAAa,CAACR,MAAM,EAAEW,QAAQ,CAAC,CAAC;EACrE;EACA,OAAOH,aAAa;AACtB;AAMe,SAASI,8BAA8B,CACpDC,WAAgC,EAChCC,UAAkB,EAClB;EACEN,aAAa;EACbO,mBAAmB,GAAG,KAAK;EAC3BC,IAAI,GAAG,KAAK;EACZC,eAAe,GAAG,KAAK;EACvBN;AAOF,CAAC,EACe;EAChB,IAAI,CAACG,UAAU,EAAE;IACfA,UAAU,GAAGD,WAAW,CAACK,KAAK,CAACC,qBAAqB,CAAC,SAAS,CAAC,CAACC,IAAI;EACtE;EACA,MAAMC,gBAAgB,GAAG,IAAIC,GAAG,EAAU;EAE1CC,oBAAoB,CAACV,WAAW,CAAC;EAEjC,MAAM;IAAEW,KAAK;IAAEC,OAAO;IAAE5B;EAAW,CAAC,GAAG6B,iBAAiB,CACtDb,WAAW,EACX;IAAEE,mBAAmB;IAAEC;EAAK,CAAC,EAC7BK,gBAAgB,CACjB;EAEDM,8BAA8B,CAACd,WAAW,CAAC;EAG3C,KAAK,MAAM,CAACb,MAAM,EAAEF,QAAQ,CAAC,IAAI2B,OAAO,EAAE;IACxC,IAAI3B,QAAQ,CAACK,gBAAgB,CAACD,IAAI,GAAG,CAAC,EAAE;MAGtCJ,QAAQ,CAACsB,IAAI,GAAGtB,QAAQ,CAACK,gBAAgB,CAACyB,MAAM,EAAE,CAACC,IAAI,EAAE,CAACC,KAAK;IACjE;IAEA,MAAMC,eAAe,GAAGrB,oBAAoB,CAC1CF,aAAa,EACbR,MAAM,EACNW,QAAQ,CACT;IAED,IAAIoB,eAAe,KAAK,MAAM,EAAE;MAC9BjC,QAAQ,CAACkC,OAAO,GAAG,MAAM;IAC3B,CAAC,MAAM,IAAID,eAAe,KAAK,MAAM,IAAIjC,QAAQ,CAACkC,OAAO,KAAK,WAAW,EAAE;MACzElC,QAAQ,CAACkC,OAAO,GAAG,gBAAgB;IACrC,CAAC,MAAM,IAAID,eAAe,KAAK,MAAM,IAAIjC,QAAQ,CAACkC,OAAO,KAAK,SAAS,EAAE;MACvElC,QAAQ,CAACkC,OAAO,GAAG,cAAc;IACnC,CAAC,MAAM,IAAIf,eAAe,IAAInB,QAAQ,CAACkC,OAAO,KAAK,WAAW,EAAE;MAM9DlC,QAAQ,CAACkC,OAAO,GAAG,SAAS;IAC9B;EACF;EAEA,OAAO;IACLlB,UAAU;IACVmB,kBAAkB,EAAE,IAAI;IACxBpC,UAAU;IACV2B,KAAK;IACLxB,MAAM,EAAEyB,OAAO;IACfJ;EACF,CAAC;AACH;AAEA,SAASa,sBAAsB,CAC7BC,IAAc,EACdd,gBAA6B,EACrB;EACR,IAAIc,IAAI,CAACC,YAAY,EAAE,EAAE;IACvB,OAAOD,IAAI,CAACE,IAAI,CAACjB,IAAI;EACvB,CAAC,MAAM,IAAIe,IAAI,CAACG,eAAe,EAAE,EAAE;IACjC,MAAMC,WAAW,GAAGJ,IAAI,CAACE,IAAI,CAACP,KAAK;IAOnC,IAAI,CAAC,IAAAU,2CAAgB,EAACD,WAAW,CAAC,EAAE;MAClClB,gBAAgB,CAACoB,GAAG,CAACF,WAAW,CAAC;IACnC;IACA,OAAOA,WAAW;EACpB,CAAC,MAAM;IACL,MAAM,IAAI9B,KAAK,CACZ,2EAA0E0B,IAAI,CAACE,IAAI,CAACK,IAAK,EAAC,CAC5F;EACH;AACF;AAEA,SAASC,qBAAqB,CAC5BR,IAAc,EAC+B;EAC7C,IAAIA,IAAI,CAACS,iBAAiB,EAAE,EAAE;IAC5B;EACF,CAAC,MAAM,IAAIT,IAAI,CAACU,0BAA0B,EAAE,EAAE;IAC5C,MAAMV,IAAI,CAACW,mBAAmB,CAC5B,iGAAiG,CAClG;EACH,CAAC,MAAM;IACL,MAAMX,IAAI,CAACW,mBAAmB,CAAC,kCAAkC,CAAC;EACpE;AACF;AAKA,SAASpB,iBAAiB,CACxBb,WAAgC,EAChC;EACEG,IAAI;EACJD;AAKF,CAAC,EACDM,gBAA6B,EAC7B;EACA,MAAM0B,SAAS,GAAGC,sBAAsB,CACtCnC,WAAW,EACXE,mBAAmB,EACnBM,gBAAgB,CACjB;EAED,MAAM4B,UAAU,GAAG,IAAIC,GAAG,EAAgC;EAC1D,MAAMC,OAAO,GAAIC,UAA2B,IAAK;IAC/C,MAAMpD,MAAM,GAAGoD,UAAU,CAACtB,KAAK;IAE/B,IAAIuB,IAAI,GAAGJ,UAAU,CAACK,GAAG,CAACtD,MAAM,CAAC;IACjC,IAAI,CAACqD,IAAI,EAAE;MACTA,IAAI,GAAG;QACLjC,IAAI,EAAEP,WAAW,CAACK,KAAK,CAACC,qBAAqB,CAC3C,IAAAoC,cAAQ,EAACvD,MAAM,EAAE,IAAAwD,aAAO,EAACxD,MAAM,CAAC,CAAC,CAClC,CAACoB,IAAI;QAENY,OAAO,EAAE,MAAM;QAEfyB,GAAG,EAAE,IAAI;QAGTxD,OAAO,EAAE,IAAIiD,GAAG,EAAE;QAClB/C,gBAAgB,EAAE,IAAImB,GAAG,EAAE;QAG3BlB,SAAS,EAAE,IAAI8C,GAAG,EAAE;QACpB7C,iBAAiB,EAAE,IAAIiB,GAAG,EAAE;QAC5BhB,WAAW,EAAE,IAAI;QAEjBU,IAAI,EAAE,KAAK;QAEX0C,UAAU,EAAE;MACd,CAAC;MACDT,UAAU,CAACU,GAAG,CAAC3D,MAAM,EAAEqD,IAAI,CAAC;IAC9B;IACA,OAAOA,IAAI;EACb,CAAC;EACD,IAAIxD,UAAU,GAAG,KAAK;EACtBgB,WAAW,CAACyC,GAAG,CAAC,MAAM,CAAC,CAACM,OAAO,CAACC,KAAK,IAAI;IACvC,IAAIA,KAAK,CAACC,mBAAmB,EAAE,EAAE;MAC/B,MAAMT,IAAI,GAAGF,OAAO,CAACU,KAAK,CAACxB,IAAI,CAACrC,MAAM,CAAC;MACvC,IAAI,CAACqD,IAAI,CAACI,GAAG,EAAEJ,IAAI,CAACI,GAAG,GAAGI,KAAK,CAACxB,IAAI,CAACoB,GAAG;MAExCI,KAAK,CAACP,GAAG,CAAC,YAAY,CAAC,CAACM,OAAO,CAACG,IAAI,IAAI;QACtC,IAAIA,IAAI,CAACC,wBAAwB,EAAE,EAAE;UACnC,MAAMC,SAAS,GAAGF,IAAI,CAACT,GAAG,CAAC,OAAO,CAAC,CAACjB,IAAI,CAACjB,IAAI;UAE7CiC,IAAI,CAACpD,OAAO,CAAC0D,GAAG,CAACM,SAAS,EAAE,SAAS,CAAC;UAEtC,MAAMC,QAAQ,GAAGnB,SAAS,CAACO,GAAG,CAACW,SAAS,CAAC;UACzC,IAAIC,QAAQ,EAAE;YACZnB,SAAS,CAACoB,MAAM,CAACF,SAAS,CAAC;YAE3BC,QAAQ,CAACE,KAAK,CAACR,OAAO,CAACxC,IAAI,IAAI;cAC7BiC,IAAI,CAACjD,SAAS,CAACuD,GAAG,CAACvC,IAAI,EAAE,SAAS,CAAC;YACrC,CAAC,CAAC;YACFiC,IAAI,CAACK,UAAU,GAAG,IAAI;UACxB;QACF,CAAC,MAAM,IAAIK,IAAI,CAACM,0BAA0B,EAAE,EAAE;UAC5C,MAAMJ,SAAS,GAAGF,IAAI,CAACT,GAAG,CAAC,OAAO,CAAC,CAACjB,IAAI,CAACjB,IAAI;UAE7CiC,IAAI,CAAClD,gBAAgB,CAACsC,GAAG,CAACwB,SAAS,CAAC;UACpC,MAAMC,QAAQ,GAAGnB,SAAS,CAACO,GAAG,CAACW,SAAS,CAAC;UACzC,IAAIC,QAAQ,EAAE;YACZnB,SAAS,CAACoB,MAAM,CAACF,SAAS,CAAC;YAE3BC,QAAQ,CAACE,KAAK,CAACR,OAAO,CAACxC,IAAI,IAAI;cAC7BiC,IAAI,CAAChD,iBAAiB,CAACoC,GAAG,CAACrB,IAAI,CAAC;YAClC,CAAC,CAAC;YACFiC,IAAI,CAACK,UAAU,GAAG,IAAI;UACxB;QACF,CAAC,MAAM,IAAIK,IAAI,CAACO,iBAAiB,EAAE,EAAE;UACnC,MAAMC,UAAU,GAAGrC,sBAAsB,CACvC6B,IAAI,CAACT,GAAG,CAAC,UAAU,CAAC,EACpBjC,gBAAgB,CACjB;UACD,MAAM4C,SAAS,GAAGF,IAAI,CAACT,GAAG,CAAC,OAAO,CAAC,CAACjB,IAAI,CAACjB,IAAI;UAE7CiC,IAAI,CAACpD,OAAO,CAAC0D,GAAG,CAACM,SAAS,EAAEM,UAAU,CAAC;UAEvC,MAAML,QAAQ,GAAGnB,SAAS,CAACO,GAAG,CAACW,SAAS,CAAC;UACzC,IAAIC,QAAQ,EAAE;YACZnB,SAAS,CAACoB,MAAM,CAACF,SAAS,CAAC;YAE3BC,QAAQ,CAACE,KAAK,CAACR,OAAO,CAACxC,IAAI,IAAI;cAC7BiC,IAAI,CAACjD,SAAS,CAACuD,GAAG,CAACvC,IAAI,EAAEmD,UAAU,CAAC;YACtC,CAAC,CAAC;YACFlB,IAAI,CAACK,UAAU,GAAG,IAAI;UACxB;QACF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIG,KAAK,CAACW,sBAAsB,EAAE,EAAE;MACzC3E,UAAU,GAAG,IAAI;MACjB,MAAMwD,IAAI,GAAGF,OAAO,CAACU,KAAK,CAACxB,IAAI,CAACrC,MAAM,CAAC;MACvC,IAAI,CAACqD,IAAI,CAACI,GAAG,EAAEJ,IAAI,CAACI,GAAG,GAAGI,KAAK,CAACxB,IAAI,CAACoB,GAAG;MAExCJ,IAAI,CAAC/C,WAAW,GAAG;QACjBmD,GAAG,EAAEI,KAAK,CAACxB,IAAI,CAACoB;MAClB,CAAC;MACDJ,IAAI,CAACK,UAAU,GAAG,IAAI;IACxB,CAAC,MAAM,IAAIG,KAAK,CAACY,wBAAwB,EAAE,IAAIZ,KAAK,CAACxB,IAAI,CAACrC,MAAM,EAAE;MAChEH,UAAU,GAAG,IAAI;MACjB,MAAMwD,IAAI,GAAGF,OAAO,CAACU,KAAK,CAACxB,IAAI,CAACrC,MAAM,CAAC;MACvC,IAAI,CAACqD,IAAI,CAACI,GAAG,EAAEJ,IAAI,CAACI,GAAG,GAAGI,KAAK,CAACxB,IAAI,CAACoB,GAAG;MAExCI,KAAK,CAACP,GAAG,CAAC,YAAY,CAAC,CAACM,OAAO,CAACG,IAAI,IAAI;QACtCpB,qBAAqB,CAACoB,IAAI,CAAC;QAC3B,MAAMQ,UAAU,GAAGrC,sBAAsB,CACvC6B,IAAI,CAACT,GAAG,CAAC,OAAO,CAAC,EACjBjC,gBAAgB,CACjB;QACD,MAAMP,UAAU,GAAGoB,sBAAsB,CACvC6B,IAAI,CAACT,GAAG,CAAC,UAAU,CAAC,EACpBjC,gBAAgB,CACjB;QAEDgC,IAAI,CAACjD,SAAS,CAACuD,GAAG,CAAC7C,UAAU,EAAEyD,UAAU,CAAC;QAC1ClB,IAAI,CAACK,UAAU,GAAG,IAAI;QAEtB,IAAI5C,UAAU,KAAK,YAAY,EAAE;UAC/B,MAAMiD,IAAI,CACPT,GAAG,CAAC,UAAU,CAAC,CACfR,mBAAmB,CAAC,8BAA8B,CAAC;QACxD;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IACLe,KAAK,CAACY,wBAAwB,EAAE,IAChCZ,KAAK,CAACa,0BAA0B,EAAE,EAClC;MACA7E,UAAU,GAAG,IAAI;IACnB;EACF,CAAC,CAAC;EAEF,KAAK,MAAMC,QAAQ,IAAImD,UAAU,CAACrB,MAAM,EAAE,EAAE;IAC1C,IAAI+C,YAAY,GAAG,KAAK;IACxB,IAAIC,UAAU,GAAG,KAAK;IAEtB,IAAI9E,QAAQ,CAACK,gBAAgB,CAACD,IAAI,GAAG,CAAC,EAAE;MACtCyE,YAAY,GAAG,IAAI;MACnBC,UAAU,GAAG,IAAI;IACnB;IAEA,IAAI9E,QAAQ,CAACQ,WAAW,EAAE;MACxBsE,UAAU,GAAG,IAAI;IACnB;IAEA,KAAK,MAAML,UAAU,IAAIzE,QAAQ,CAACG,OAAO,CAAC2B,MAAM,EAAE,EAAE;MAClD,IAAI2C,UAAU,KAAK,SAAS,EAAEI,YAAY,GAAG,IAAI,CAAC,KAC7CC,UAAU,GAAG,IAAI;IACxB;IACA,KAAK,MAAML,UAAU,IAAIzE,QAAQ,CAACM,SAAS,CAACwB,MAAM,EAAE,EAAE;MACpD,IAAI2C,UAAU,KAAK,SAAS,EAAEI,YAAY,GAAG,IAAI,CAAC,KAC7CC,UAAU,GAAG,IAAI;IACxB;IAEA,IAAID,YAAY,IAAIC,UAAU,EAAE;MAE9B9E,QAAQ,CAACkC,OAAO,GAAG,WAAW;IAChC,CAAC,MAAM,IAAI2C,YAAY,EAAE;MACvB7E,QAAQ,CAACkC,OAAO,GAAG,SAAS;IAC9B;EACF;EAEA,KAAK,MAAM,CAAChC,MAAM,EAAEF,QAAQ,CAAC,IAAImD,UAAU,EAAE;IAC3C,IACEjC,IAAI,KAAK,KAAK,IACd,EAAEjB,kBAAkB,CAACD,QAAQ,CAAC,IAAIA,QAAQ,CAACQ,WAAW,CAAC,EACvD;MACA,IAAIU,IAAI,KAAK,IAAI,EAAE;QAGjBlB,QAAQ,CAACkB,IAAI,GAAG,CAAC,IAAI,CAAC6D,IAAI,CAAC7E,MAAM,CAAC;MACpC,CAAC,MAAM,IAAI8E,KAAK,CAACC,OAAO,CAAC/D,IAAI,CAAC,EAAE;QAC9BlB,QAAQ,CAACkB,IAAI,GAAGA,IAAI,CAACgE,OAAO,CAAChF,MAAM,CAAC,KAAK,CAAC,CAAC;MAC7C,CAAC,MAAM,IAAI,OAAOgB,IAAI,KAAK,UAAU,EAAE;QACrClB,QAAQ,CAACkB,IAAI,GAAGA,IAAI,CAAChB,MAAM,CAAC;MAC9B,CAAC,MAAM;QACL,MAAM,IAAIS,KAAK,CAAE,oDAAmD,CAAC;MACvE;IACF;EACF;EAEA,OAAO;IACLZ,UAAU;IACV2B,KAAK,EAAEuB,SAAS;IAChBtB,OAAO,EAAEwB;EACX,CAAC;AACH;AAMA,SAASD,sBAAsB,CAC7BnC,WAAgC,EAChCE,mBAAmC,EACnCM,gBAA6B,EACK;EAClC,MAAM4D,iBAAiB,GAAG,IAAI/B,GAAG,EAAE;EAEnCrC,WAAW,CAACyC,GAAG,CAAC,MAAM,CAAC,CAACM,OAAO,CAAEC,KAAe,IAAK;IACnD,IAAIqB,IAAuB;IAC3B,IAAIrB,KAAK,CAACC,mBAAmB,EAAE,EAAE;MAC/BoB,IAAI,GAAG,QAAQ;IACjB,CAAC,MAAM;MACL,IAAIrB,KAAK,CAACa,0BAA0B,EAAE,EAAE;QACtCb,KAAK,GAAGA,KAAK,CAACP,GAAG,CAAC,aAAa,CAAC;MAClC;MACA,IAAIO,KAAK,CAACY,wBAAwB,EAAE,EAAE;QACpC,IAAIZ,KAAK,CAACxB,IAAI,CAAC8C,WAAW,EAAE;UAC1BtB,KAAK,GAAGA,KAAK,CAACP,GAAG,CAAC,aAAa,CAAC;QAClC,CAAC,MAAM,IACLvC,mBAAmB,IACnB8C,KAAK,CAACxB,IAAI,CAACrC,MAAM,IACjB6D,KAAK,CAACP,GAAG,CAAC,QAAQ,CAAC,CAAChB,eAAe,EAAE,EACrC;UACAuB,KAAK,CAACP,GAAG,CAAC,YAAY,CAAC,CAACM,OAAO,CAACG,IAAI,IAAI;YACtCpB,qBAAqB,CAACoB,IAAI,CAAC;YAC3BkB,iBAAiB,CAACtB,GAAG,CAACI,IAAI,CAACT,GAAG,CAAC,OAAO,CAAC,CAACjB,IAAI,CAACjB,IAAI,EAAE,OAAO,CAAC;UAC7D,CAAC,CAAC;UACF;QACF;MACF;MAEA,IAAIyC,KAAK,CAACuB,qBAAqB,EAAE,EAAE;QACjCF,IAAI,GAAG,SAAS;MAClB,CAAC,MAAM,IAAIrB,KAAK,CAACwB,kBAAkB,EAAE,EAAE;QACrCH,IAAI,GAAG,OAAO;MAChB,CAAC,MAAM,IAAIrB,KAAK,CAACyB,qBAAqB,CAAC;QAAEJ,IAAI,EAAE;MAAM,CAAC,CAAC,EAAE;QACvDA,IAAI,GAAG,KAAK;MACd,CAAC,MAAM,IAAIrB,KAAK,CAACyB,qBAAqB,EAAE,EAAE;QACxCJ,IAAI,GAAG,OAAO;MAChB,CAAC,MAAM;QACL;MACF;IACF;IAEAK,MAAM,CAACC,IAAI,CAAC3B,KAAK,CAAC4B,0BAA0B,EAAE,CAAC,CAAC7B,OAAO,CAACxC,IAAI,IAAI;MAC9D6D,iBAAiB,CAACtB,GAAG,CAACvC,IAAI,EAAE8D,IAAI,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,MAAMQ,aAAa,GAAG,IAAIxC,GAAG,EAAE;EAC/B,MAAMyC,gBAAgB,GAAIC,MAA8B,IAAK;IAC3D,MAAM3B,SAAS,GAAG2B,MAAM,CAACvD,IAAI,CAACjB,IAAI;IAClC,IAAItB,QAAQ,GAAG4F,aAAa,CAACpC,GAAG,CAACW,SAAS,CAAC;IAE3C,IAAI,CAACnE,QAAQ,EAAE;MACb,MAAMoF,IAAI,GAAGD,iBAAiB,CAAC3B,GAAG,CAACW,SAAS,CAAC;MAE7C,IAAIiB,IAAI,KAAKW,SAAS,EAAE;QACtB,MAAMD,MAAM,CAAC9C,mBAAmB,CAC7B,oBAAmBmB,SAAU,2BAA0B,CACzD;MACH;MAEAnE,QAAQ,GAAG;QACTsE,KAAK,EAAE,EAAE;QACTc;MACF,CAAC;MACDQ,aAAa,CAAC/B,GAAG,CAACM,SAAS,EAAEnE,QAAQ,CAAC;IACxC;IACA,OAAOA,QAAQ;EACjB,CAAC;EAEDe,WAAW,CAACyC,GAAG,CAAC,MAAM,CAAC,CAACM,OAAO,CAACC,KAAK,IAAI;IACvC,IACEA,KAAK,CAACY,wBAAwB,EAAE,KAC/B1D,mBAAmB,IAAI,CAAC8C,KAAK,CAACxB,IAAI,CAACrC,MAAM,CAAC,EAC3C;MACA,IAAI6D,KAAK,CAACxB,IAAI,CAAC8C,WAAW,EAAE;QAC1B,MAAMA,WAAW,GAAGtB,KAAK,CAACP,GAAG,CAAC,aAAa,CAAC;QAC5C,MAAMwC,GAAG,GAAGX,WAAW,CAACY,8BAA8B,EAAE;QACxDR,MAAM,CAACC,IAAI,CAACM,GAAG,CAAC,CAAClC,OAAO,CAACxC,IAAI,IAAI;UAC/B,IAAIA,IAAI,KAAK,YAAY,EAAE;YACzB,MAAM+D,WAAW,CAACrC,mBAAmB,CACnC,8BAA8B,CAC/B;UACH;UACA6C,gBAAgB,CAACG,GAAG,CAAC1E,IAAI,CAAC,CAAC,CAACgD,KAAK,CAAC4B,IAAI,CAAC5E,IAAI,CAAC;QAC9C,CAAC,CAAC;MACJ,CAAC,MAAM;QACLyC,KAAK,CAACP,GAAG,CAAC,YAAY,CAAC,CAACM,OAAO,CAACG,IAAI,IAAI;UACtC,MAAMvC,KAAK,GAAGuC,IAAI,CAACT,GAAG,CAAC,OAAO,CAAC;UAC/B,MAAM2C,QAAQ,GAAGlC,IAAI,CAACT,GAAG,CAAC,UAAU,CAAC;UACrC,MAAMoC,aAAa,GAAGC,gBAAgB,CAACnE,KAAK,CAAC;UAC7C,MAAMV,UAAU,GAAGoB,sBAAsB,CAAC+D,QAAQ,EAAE5E,gBAAgB,CAAC;UAErE,IAAIP,UAAU,KAAK,YAAY,EAAE;YAC/B,MAAMmF,QAAQ,CAACnD,mBAAmB,CAAC,8BAA8B,CAAC;UACpE;UACA4C,aAAa,CAACtB,KAAK,CAAC4B,IAAI,CAAClF,UAAU,CAAC;QACtC,CAAC,CAAC;MACJ;IACF,CAAC,MAAM,IAAI+C,KAAK,CAACa,0BAA0B,EAAE,EAAE;MAC7C,MAAMS,WAAW,GAAGtB,KAAK,CAACP,GAAG,CAAC,aAAa,CAAC;MAC5C,IACE6B,WAAW,CAACC,qBAAqB,EAAE,IACnCD,WAAW,CAACE,kBAAkB,EAAE,EAChC;QAEAM,gBAAgB,CAACR,WAAW,CAAC7B,GAAG,CAAC,IAAI,CAAC,CAAC,CAACc,KAAK,CAAC4B,IAAI,CAAC,SAAS,CAAC;MAC/D,CAAC,MAAM;QAEL,MAAMb,WAAW,CAACrC,mBAAmB,CACnC,uCAAuC,CACxC;MACH;IACF;EACF,CAAC,CAAC;EACF,OAAO4C,aAAa;AACtB;AAKA,SAASnE,oBAAoB,CAACV,WAAgC,EAAE;EAE9DA,WAAW,CAACyC,GAAG,CAAC,MAAM,CAAC,CAACM,OAAO,CAACC,KAAK,IAAI;IACvC,IAAI,CAACA,KAAK,CAACa,0BAA0B,EAAE,EAAE;IACzC,IAAAwB,qCAAsB,EAACrC,KAAK,CAAC;EAC/B,CAAC,CAAC;AACJ;AAEA,SAASlC,8BAA8B,CAACd,WAAgC,EAAE;EACxEA,WAAW,CAACyC,GAAG,CAAC,MAAM,CAAC,CAACM,OAAO,CAACC,KAAK,IAAI;IACvC,IAAIA,KAAK,CAACC,mBAAmB,EAAE,EAAE;MAC/BD,KAAK,CAACsC,MAAM,EAAE;IAChB,CAAC,MAAM,IAAItC,KAAK,CAACY,wBAAwB,EAAE,EAAE;MAC3C,IAAIZ,KAAK,CAACxB,IAAI,CAAC8C,WAAW,EAAE;QAE1BtB,KAAK,CAACxB,IAAI,CAAC8C,WAAW,CAACiB,WAAW,GAAGvC,KAAK,CAACxB,IAAI,CAAC+D,WAAW;QAC3DvC,KAAK,CAACwC,WAAW,CAACxC,KAAK,CAACxB,IAAI,CAAC8C,WAAW,CAAC;MAC3C,CAAC,MAAM;QACLtB,KAAK,CAACsC,MAAM,EAAE;MAChB;IACF,CAAC,MAAM,IAAItC,KAAK,CAACa,0BAA0B,EAAE,EAAE;MAE7C,MAAMS,WAAW,GAAGtB,KAAK,CAACP,GAAG,CAAC,aAAa,CAAC;MAC5C,IACE6B,WAAW,CAACC,qBAAqB,EAAE,IACnCD,WAAW,CAACE,kBAAkB,EAAE,EAChC;QAEAF,WAAW,CAACiB,WAAW,GAAGvC,KAAK,CAACxB,IAAI,CAAC+D,WAAW;QAChDvC,KAAK,CAACwC,WAAW,CACflB,WAAW,CACZ;MACH,CAAC,MAAM;QAEL,MAAMA,WAAW,CAACrC,mBAAmB,CACnC,uCAAuC,CACxC;MACH;IACF,CAAC,MAAM,IAAIe,KAAK,CAACW,sBAAsB,EAAE,EAAE;MACzCX,KAAK,CAACsC,MAAM,EAAE;IAChB;EACF,CAAC,CAAC;AACJ"}