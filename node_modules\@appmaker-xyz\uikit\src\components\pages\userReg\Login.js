import React from 'react';
import { StyleSheet, KeyboardAvoidingView, Platform } from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import {
  Layout,
  Input,
  Button,
  AppmakerText,
  AppImage,
} from '../../../components';
import { SocialLogin } from './components';

const Login = (props) => {
  const {
    socialLogin = true,
    onChangeUsername,
    onChangePassword,
    onLogin,
    onCreateAccount,
    onForgetPassword,
    logoUrl,
    loginButtonLeft,
    loginButtonText,
  } = props;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS == 'ios' ? 'padding' : 'height'}>
      <Layout style={styles.loginView}>
        {logoUrl && <AppImage uri={logoUrl} style={styles.logo} />}
        <Input
          label="Username or Email"
          leftIcon="user"
          onChangeText={(v) => onChangeUsername && onChangeUsername(v)}
        />
        <Input
          label="Password"
          type="password"
          leftIcon="key"
          onChangeText={(v) => onChangePassword && onChangePassword(v)}
        />
        <Layout style={styles.forgetPass}>
          <Button onPress={onForgetPassword} small status="white">
            Forget Password?
          </Button>
        </Layout>
        <Button accessoryLeft={loginButtonLeft} onPress={onLogin} status="dark">
          {loginButtonText || 'Login'}
        </Button>
      </Layout>
      {socialLogin && (
        <SocialLogin google={true} apple={true} facebook={true} />
      )}
      <Layout style={styles.signUp}>
        <AppmakerText
          category="bodyParagraph"
          status="demiDark"
          style={styles.signUpQue}>
          New user?
        </AppmakerText>
        <Button onPress={onCreateAccount} status="light" small>
          SIGN UP
        </Button>
      </Layout>
    </KeyboardAvoidingView>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      flex: 1,
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    loginView: {
      padding: spacing.base,
      position: 'relative',
      width: '100%',
    },
    logo: {
      width: '50%',
      height: 80,
      alignSelf: 'center',
      resizeMode: 'contain',
      marginVertical: spacing.md,
    },
    forgetPass: {
      alignItems: 'flex-end',
      position: 'relative',
      top: -16,
    },
    signUp: {
      flexDirection: 'row',
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: color.light,
    },
    signUpQue: {
      marginRight: spacing.mini,
    },
  });

export default Login;
