import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { ThemeText, AppTouchable, Layout } from '../atoms';

const filters = [
  'S',
  'M',
  'L',
  'Black',
  'White',
  'Shirt',
  'T-Shirt',
  '<PERSON><PERSON>',
  'Dress',
  'Sneakers',
  'Boots',
  'Formal',
  'Casual',
];

function FilterItem(props) {
  const [active, setActive] = useState(false);
  const { filter, themeColor } = props;
  const filterItemContainer = [styles.filterItem];

  if (themeColor) {
    filterItemContainer.push({
      backgroundColor: `${themeColor}12`,
      borderColor: `${themeColor}40`,
    });
  }
  if (active) {
    filterItemContainer.push({
      backgroundColor: themeColor,
    });
  }

  return (
    <AppTouchable
      style={filterItemContainer}
      onPress={() => setActive(!active)}>
      <ThemeText color={active ? '#FFFFFF' : themeColor}>{filter}</ThemeText>
    </AppTouchable>
  );
}

const QuickFilters = ({ attributes = {} }) => {
  const { themeColor = '#000000' } = attributes;
  const containerStyles = [styles.container];

  if (themeColor) {
    containerStyles.push({
      backgroundColor: `${themeColor}05`,
    });
  }
  return (
    <Layout style={containerStyles}>
      <Layout style={styles.innerContainer}>
        {filters.map((filter) => (
          <FilterItem themeColor={themeColor} filter={filter} />
        ))}
      </Layout>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 12,
  },
  innerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterItem: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 24,
    borderWidth: 1,
    margin: 2,
  },
  button: {
    marginTop: 12,
    backgroundColor: '#000',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignSelf: 'center',
  },
});

export default QuickFilters;
