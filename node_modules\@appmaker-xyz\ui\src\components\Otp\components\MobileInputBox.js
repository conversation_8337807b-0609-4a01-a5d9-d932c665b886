import React from 'react';
import PhoneInput from 'react-native-phone-number-input';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';
import { Platform } from 'react-native';

const MobileInputBox = (props) => {
  const { styles } = useStyles(stylesheet);
  return (
    <PhoneInput
      ref={props.ref}
      layout={props.layout}
      defaultCode={props.defaultCode}
      defaultValue={props.defaultValue}
      containerStyle={styles.countryMainContainer}
      textContainerStyle={styles.textContainerMain}
      textInputStyle={styles.textInputStyle}
      onChangeText={props.onChangeText}
      onChangeFormattedText={props.onChangeFormattedText}
      codeTextStyle={styles.font}
      autoFocus
      countryPickerProps={{
        countryCodes: props?.countryCodes ? props.countryCodes : null,
      }}
    />
  );
};

const height = 50;

const stylesheet = createStyleSheet((theme) => ({
  countryMainContainer: {
    flex: 1,
    width: '100%',
    borderWidth: 1,
    borderColor: '#64748B',
    height: height,
    overflow: 'hidden',
    borderRadius: 6,
  },
  textContainerMain: {
    backgroundColor: '#FFFFFF',
    borderColor: '#64748B',
    borderLeftWidth: 1,
    height: height,
  },
  textInputStyle: {
    height: height,
    fontFamily: theme.fontFamily.regular,
  },
  font: {
    fontFamily: theme.fontFamily.regular,
    // lineHeight: Platform.OS === 'ios' ? 20 : undefined,
    height: 20,
  },
}));

export default MobileInputBox;
