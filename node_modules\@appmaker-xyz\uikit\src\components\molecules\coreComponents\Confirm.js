import React from 'react';
import {
  StyleSheet,
  I18nManager,
  ActivityIndicator,
  LayoutAnimation,
  Platform,
  UIManager,
} from 'react-native';
import { Layout, AppmakerText, Button } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';

const Confirm = ({ attributes = {}, onPress, onAction, clientId }) => {
  const {
    title = 'Please confirm action',
    confirmText = 'Confirm',
    denyText = 'Cancel',
    appmakerAction,
  } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({
    color,
    spacing,
  });
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      onAction(appmakerAction);
    };
  }
  //   if (Platform.OS === 'android') {
  //     if (UIManager.setLayoutAnimationEnabledExperimental) {
  //       UIManager.setLayoutAnimationEnabledExperimental(true);
  //     }
  //   }
  //   const toggleConfirm = () => {
  //     LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  //     setConfirm(!confirm);
  //   };
  return (
    <Layout style={styles.confirmContainer}>
      <AppmakerText>{title}</AppmakerText>
      <Layout style={styles.confirm}>
        <Button
          small
          status="light"
          onPress={onPressHandle}
          wholeContainerStyle={[styles.confirmButtons, styles.mr]}>
          {denyText}
        </Button>
        <Button
          small
          status="dark"
          wholeContainerStyle={styles.confirmButtons}
          onPress={onPressHandle}>
          {confirmText}
        </Button>
      </Layout>
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    confirmContainer: {
      paddingHorizontal: spacing.base,
      paddingVertical: spacing.base,
      backgroundColor: color.white,
      borderTopWidth: 0.5,
      borderTopColor: color.gray,
    },
    confirm: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: spacing.small,
    },
    confirmButtons: {
      flex: 1,
    },
    mr: {
      marginRight: spacing.base,
    },
  });

export default Confirm;
