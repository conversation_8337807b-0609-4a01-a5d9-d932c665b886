{"version": 3, "names": ["_path", "data", "require", "_debug", "_options", "_patternToRegex", "_printer", "_rewriteStackTrace", "_configError", "_index", "_caching", "_configDescriptors", "debug", "buildDebug", "buildPresetChain", "arg", "context", "chain", "buildPresetChain<PERSON>er", "plugins", "dedupDescriptors", "presets", "options", "map", "o", "normalizeOptions", "files", "Set", "<PERSON><PERSON><PERSON><PERSON>", "root", "preset", "loadPresetDescriptors", "env", "envName", "loadPresetEnvDescriptors", "overrides", "index", "loadPresetOverridesDescriptors", "overridesEnv", "loadPresetOverridesEnvDescriptors", "createLogger", "exports", "makeWeakCacheSync", "buildRootDescriptors", "alias", "createUncachedDescriptors", "makeStrongCacheSync", "buildEnvDescriptors", "buildOverrideDescriptors", "buildOverrideEnvDescriptors", "buildRootChain", "opts", "configReport", "babelRcReport", "programmaticLogger", "ConfigPrinter", "programmatic<PERSON><PERSON><PERSON>", "loadProgrammaticChain", "dirname", "cwd", "undefined", "programmaticReport", "output", "configFile", "loadConfig", "caller", "findRootConfig", "babelrc", "babelrcRoots", "babelrcRootsDirectory", "config<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON><PERSON><PERSON>", "validatedFile", "validateConfigFile", "result", "loadFileChain", "mergeChain", "ignoreFile", "babelrcFile", "isIgnored", "file<PERSON>hain", "filename", "pkgData", "findPackageData", "babelrcLoadEnabled", "ignore", "config", "findRelativeConfig", "add", "filepath", "shouldIgnore", "validateBabelrcFile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showConfig", "console", "log", "filter", "x", "join", "fileHandling", "absoluteRoot", "directories", "indexOf", "babelrcPatterns", "Array", "isArray", "pat", "path", "resolve", "length", "some", "pathPatternToRegex", "directory", "matchPattern", "file", "validate", "validateExtendFile", "input", "createCachedDescriptors", "baseLogger", "buildProgrammaticLogger", "loadFile<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadFileDescriptors", "loadFileEnvDescriptors", "loadFileOverridesDescriptors", "loadFileOverridesEnvDescriptors", "buildFileLogger", "configure", "ChainFormatter", "Config", "descriptors", "_", "_context$caller", "Programmatic", "callerName", "name", "_options$env", "_options$overrides", "Error", "_options$overrides2", "_override$env", "override", "<PERSON><PERSON><PERSON><PERSON>", "flattenedConfigs", "rootOpts", "configIsApplicable", "push", "envOpts", "for<PERSON>ach", "overrideOps", "overrideEnvOpts", "only", "logger", "mergeExtendsChain", "mergeChainOpts", "extends", "has", "from", "delete", "target", "source", "Object", "assign", "passPerPreset", "test", "include", "exclude", "prototype", "hasOwnProperty", "call", "sourceMaps", "sourceMap", "items", "Map", "item", "value", "fnKey", "nameMap", "get", "set", "desc", "ownPass", "reduce", "acc", "config<PERSON><PERSON>", "configFieldIsApplicable", "patterns", "matchesPatterns", "ignoreListReplacer", "_key", "RegExp", "String", "_context$filename", "message", "JSON", "stringify", "_context$filename2", "pattern", "pathToTest", "endHiddenCallStack", "ConfigError"], "sources": ["../../src/config/config-chain.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-use-before-define */\n\nimport path from \"path\";\nimport buildDebug from \"debug\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport { validate } from \"./validation/options.ts\";\nimport type {\n  ValidatedOptions,\n  IgnoreList,\n  ConfigApplicableTest,\n  BabelrcSearch,\n  CallerMetadata,\n  IgnoreItem,\n} from \"./validation/options.ts\";\nimport pathPatternToRegex from \"./pattern-to-regex.ts\";\nimport { ConfigPrinter, ChainFormatter } from \"./printer.ts\";\nimport type { ReadonlyDeepArray } from \"./helpers/deep-array.ts\";\n\nimport { endHiddenCallStack } from \"../errors/rewrite-stack-trace.ts\";\nimport ConfigError from \"../errors/config-error.ts\";\nimport type { PluginAPI, PresetAPI } from \"./helpers/config-api.ts\";\n\nconst debug = buildDebug(\"babel:config:config-chain\");\n\nimport {\n  findPackageData,\n  findRelativeConfig,\n  findRootConfig,\n  loadConfig,\n} from \"./files/index.ts\";\nimport type { ConfigFile, IgnoreFile, FilePackageData } from \"./files/index.ts\";\n\nimport { makeWeakCacheSync, makeStrongCacheSync } from \"./caching.ts\";\n\nimport {\n  createCachedDescriptors,\n  createUncachedDescriptors,\n} from \"./config-descriptors.ts\";\nimport type {\n  UnloadedDescriptor,\n  OptionsAndDescriptors,\n  ValidatedFile,\n} from \"./config-descriptors.ts\";\n\nexport type ConfigChain = {\n  plugins: Array<UnloadedDescriptor<PluginAPI>>;\n  presets: Array<UnloadedDescriptor<PresetAPI>>;\n  options: Array<ValidatedOptions>;\n  files: Set<string>;\n};\n\nexport type PresetInstance = {\n  options: ValidatedOptions;\n  alias: string;\n  dirname: string;\n  externalDependencies: ReadonlyDeepArray<string>;\n};\n\nexport type ConfigContext = {\n  filename: string | undefined;\n  cwd: string;\n  root: string;\n  envName: string;\n  caller: CallerMetadata | undefined;\n  showConfig: boolean;\n};\n\n/**\n * Build a config chain for a given preset.\n */\nexport function* buildPresetChain(\n  arg: PresetInstance,\n  context: any,\n): Handler<ConfigChain | null> {\n  const chain = yield* buildPresetChainWalker(arg, context);\n  if (!chain) return null;\n\n  return {\n    plugins: dedupDescriptors(chain.plugins),\n    presets: dedupDescriptors(chain.presets),\n    options: chain.options.map(o => normalizeOptions(o)),\n    files: new Set(),\n  };\n}\n\nexport const buildPresetChainWalker = makeChainWalker<PresetInstance>({\n  root: preset => loadPresetDescriptors(preset),\n  env: (preset, envName) => loadPresetEnvDescriptors(preset)(envName),\n  overrides: (preset, index) => loadPresetOverridesDescriptors(preset)(index),\n  overridesEnv: (preset, index, envName) =>\n    loadPresetOverridesEnvDescriptors(preset)(index)(envName),\n  createLogger: () => () => {}, // Currently we don't support logging how preset is expanded\n});\nconst loadPresetDescriptors = makeWeakCacheSync((preset: PresetInstance) =>\n  buildRootDescriptors(preset, preset.alias, createUncachedDescriptors),\n);\nconst loadPresetEnvDescriptors = makeWeakCacheSync((preset: PresetInstance) =>\n  makeStrongCacheSync((envName: string) =>\n    buildEnvDescriptors(\n      preset,\n      preset.alias,\n      createUncachedDescriptors,\n      envName,\n    ),\n  ),\n);\nconst loadPresetOverridesDescriptors = makeWeakCacheSync(\n  (preset: PresetInstance) =>\n    makeStrongCacheSync((index: number) =>\n      buildOverrideDescriptors(\n        preset,\n        preset.alias,\n        createUncachedDescriptors,\n        index,\n      ),\n    ),\n);\nconst loadPresetOverridesEnvDescriptors = makeWeakCacheSync(\n  (preset: PresetInstance) =>\n    makeStrongCacheSync((index: number) =>\n      makeStrongCacheSync((envName: string) =>\n        buildOverrideEnvDescriptors(\n          preset,\n          preset.alias,\n          createUncachedDescriptors,\n          index,\n          envName,\n        ),\n      ),\n    ),\n);\n\nexport type FileHandling = \"transpile\" | \"ignored\" | \"unsupported\";\nexport type RootConfigChain = ConfigChain & {\n  babelrc: ConfigFile | void;\n  config: ConfigFile | void;\n  ignore: IgnoreFile | void;\n  fileHandling: FileHandling;\n  files: Set<string>;\n};\n\n/**\n * Build a config chain for Babel's full root configuration.\n */\nexport function* buildRootChain(\n  opts: ValidatedOptions,\n  context: ConfigContext,\n): Handler<RootConfigChain | null> {\n  let configReport, babelRcReport;\n  const programmaticLogger = new ConfigPrinter();\n  const programmaticChain = yield* loadProgrammaticChain(\n    {\n      options: opts,\n      dirname: context.cwd,\n    },\n    context,\n    undefined,\n    programmaticLogger,\n  );\n  if (!programmaticChain) return null;\n  const programmaticReport = yield* programmaticLogger.output();\n\n  let configFile;\n  if (typeof opts.configFile === \"string\") {\n    configFile = yield* loadConfig(\n      opts.configFile,\n      context.cwd,\n      context.envName,\n      context.caller,\n    );\n  } else if (opts.configFile !== false) {\n    configFile = yield* findRootConfig(\n      context.root,\n      context.envName,\n      context.caller,\n    );\n  }\n\n  let { babelrc, babelrcRoots } = opts;\n  let babelrcRootsDirectory = context.cwd;\n\n  const configFileChain = emptyChain();\n  const configFileLogger = new ConfigPrinter();\n  if (configFile) {\n    const validatedFile = validateConfigFile(configFile);\n    const result = yield* loadFileChain(\n      validatedFile,\n      context,\n      undefined,\n      configFileLogger,\n    );\n    if (!result) return null;\n    configReport = yield* configFileLogger.output();\n\n    // Allow config files to toggle `.babelrc` resolution on and off and\n    // specify where the roots are.\n    if (babelrc === undefined) {\n      babelrc = validatedFile.options.babelrc;\n    }\n    if (babelrcRoots === undefined) {\n      babelrcRootsDirectory = validatedFile.dirname;\n      babelrcRoots = validatedFile.options.babelrcRoots;\n    }\n\n    mergeChain(configFileChain, result);\n  }\n\n  let ignoreFile, babelrcFile;\n  let isIgnored = false;\n  const fileChain = emptyChain();\n  // resolve all .babelrc files\n  if (\n    (babelrc === true || babelrc === undefined) &&\n    typeof context.filename === \"string\"\n  ) {\n    const pkgData = yield* findPackageData(context.filename);\n\n    if (\n      pkgData &&\n      babelrcLoadEnabled(context, pkgData, babelrcRoots, babelrcRootsDirectory)\n    ) {\n      ({ ignore: ignoreFile, config: babelrcFile } = yield* findRelativeConfig(\n        pkgData,\n        context.envName,\n        context.caller,\n      ));\n\n      if (ignoreFile) {\n        fileChain.files.add(ignoreFile.filepath);\n      }\n\n      if (\n        ignoreFile &&\n        shouldIgnore(context, ignoreFile.ignore, null, ignoreFile.dirname)\n      ) {\n        isIgnored = true;\n      }\n\n      if (babelrcFile && !isIgnored) {\n        const validatedFile = validateBabelrcFile(babelrcFile);\n        const babelrcLogger = new ConfigPrinter();\n        const result = yield* loadFileChain(\n          validatedFile,\n          context,\n          undefined,\n          babelrcLogger,\n        );\n        if (!result) {\n          isIgnored = true;\n        } else {\n          babelRcReport = yield* babelrcLogger.output();\n          mergeChain(fileChain, result);\n        }\n      }\n\n      if (babelrcFile && isIgnored) {\n        fileChain.files.add(babelrcFile.filepath);\n      }\n    }\n  }\n\n  if (context.showConfig) {\n    console.log(\n      `Babel configs on \"${context.filename}\" (ascending priority):\\n` +\n        // print config by the order of ascending priority\n        [configReport, babelRcReport, programmaticReport]\n          .filter(x => !!x)\n          .join(\"\\n\\n\") +\n        \"\\n-----End Babel configs-----\",\n    );\n  }\n  // Insert file chain in front so programmatic options have priority\n  // over configuration file chain items.\n  const chain = mergeChain(\n    mergeChain(mergeChain(emptyChain(), configFileChain), fileChain),\n    programmaticChain,\n  );\n\n  return {\n    plugins: isIgnored ? [] : dedupDescriptors(chain.plugins),\n    presets: isIgnored ? [] : dedupDescriptors(chain.presets),\n    options: isIgnored ? [] : chain.options.map(o => normalizeOptions(o)),\n    fileHandling: isIgnored ? \"ignored\" : \"transpile\",\n    ignore: ignoreFile || undefined,\n    babelrc: babelrcFile || undefined,\n    config: configFile || undefined,\n    files: chain.files,\n  };\n}\n\nfunction babelrcLoadEnabled(\n  context: ConfigContext,\n  pkgData: FilePackageData,\n  babelrcRoots: BabelrcSearch | undefined,\n  babelrcRootsDirectory: string,\n): boolean {\n  if (typeof babelrcRoots === \"boolean\") return babelrcRoots;\n\n  const absoluteRoot = context.root;\n\n  // Fast path to avoid having to match patterns if the babelrc is just\n  // loading in the standard root directory.\n  if (babelrcRoots === undefined) {\n    return pkgData.directories.indexOf(absoluteRoot) !== -1;\n  }\n\n  let babelrcPatterns = babelrcRoots;\n  if (!Array.isArray(babelrcPatterns)) {\n    babelrcPatterns = [babelrcPatterns as IgnoreItem];\n  }\n  babelrcPatterns = babelrcPatterns.map(pat => {\n    return typeof pat === \"string\"\n      ? path.resolve(babelrcRootsDirectory, pat)\n      : pat;\n  });\n\n  // Fast path to avoid having to match patterns if the babelrc is just\n  // loading in the standard root directory.\n  if (babelrcPatterns.length === 1 && babelrcPatterns[0] === absoluteRoot) {\n    return pkgData.directories.indexOf(absoluteRoot) !== -1;\n  }\n\n  return babelrcPatterns.some(pat => {\n    if (typeof pat === \"string\") {\n      pat = pathPatternToRegex(pat, babelrcRootsDirectory);\n    }\n\n    return pkgData.directories.some(directory => {\n      return matchPattern(pat, babelrcRootsDirectory, directory, context);\n    });\n  });\n}\n\nconst validateConfigFile = makeWeakCacheSync(\n  (file: ConfigFile): ValidatedFile => ({\n    filepath: file.filepath,\n    dirname: file.dirname,\n    options: validate(\"configfile\", file.options, file.filepath),\n  }),\n);\n\nconst validateBabelrcFile = makeWeakCacheSync(\n  (file: ConfigFile): ValidatedFile => ({\n    filepath: file.filepath,\n    dirname: file.dirname,\n    options: validate(\"babelrcfile\", file.options, file.filepath),\n  }),\n);\n\nconst validateExtendFile = makeWeakCacheSync(\n  (file: ConfigFile): ValidatedFile => ({\n    filepath: file.filepath,\n    dirname: file.dirname,\n    options: validate(\"extendsfile\", file.options, file.filepath),\n  }),\n);\n\n/**\n * Build a config chain for just the programmatic options passed into Babel.\n */\nconst loadProgrammaticChain = makeChainWalker({\n  root: input => buildRootDescriptors(input, \"base\", createCachedDescriptors),\n  env: (input, envName) =>\n    buildEnvDescriptors(input, \"base\", createCachedDescriptors, envName),\n  overrides: (input, index) =>\n    buildOverrideDescriptors(input, \"base\", createCachedDescriptors, index),\n  overridesEnv: (input, index, envName) =>\n    buildOverrideEnvDescriptors(\n      input,\n      \"base\",\n      createCachedDescriptors,\n      index,\n      envName,\n    ),\n  createLogger: (input, context, baseLogger) =>\n    buildProgrammaticLogger(input, context, baseLogger),\n});\n\n/**\n * Build a config chain for a given file.\n */\nconst loadFileChainWalker = makeChainWalker<ValidatedFile>({\n  root: file => loadFileDescriptors(file),\n  env: (file, envName) => loadFileEnvDescriptors(file)(envName),\n  overrides: (file, index) => loadFileOverridesDescriptors(file)(index),\n  overridesEnv: (file, index, envName) =>\n    loadFileOverridesEnvDescriptors(file)(index)(envName),\n  createLogger: (file, context, baseLogger) =>\n    buildFileLogger(file.filepath, context, baseLogger),\n});\n\nfunction* loadFileChain(\n  input: ValidatedFile,\n  context: ConfigContext,\n  files: Set<ConfigFile>,\n  baseLogger: ConfigPrinter,\n) {\n  const chain = yield* loadFileChainWalker(input, context, files, baseLogger);\n  chain?.files.add(input.filepath);\n\n  return chain;\n}\n\nconst loadFileDescriptors = makeWeakCacheSync((file: ValidatedFile) =>\n  buildRootDescriptors(file, file.filepath, createUncachedDescriptors),\n);\nconst loadFileEnvDescriptors = makeWeakCacheSync((file: ValidatedFile) =>\n  makeStrongCacheSync((envName: string) =>\n    buildEnvDescriptors(\n      file,\n      file.filepath,\n      createUncachedDescriptors,\n      envName,\n    ),\n  ),\n);\nconst loadFileOverridesDescriptors = makeWeakCacheSync((file: ValidatedFile) =>\n  makeStrongCacheSync((index: number) =>\n    buildOverrideDescriptors(\n      file,\n      file.filepath,\n      createUncachedDescriptors,\n      index,\n    ),\n  ),\n);\nconst loadFileOverridesEnvDescriptors = makeWeakCacheSync(\n  (file: ValidatedFile) =>\n    makeStrongCacheSync((index: number) =>\n      makeStrongCacheSync((envName: string) =>\n        buildOverrideEnvDescriptors(\n          file,\n          file.filepath,\n          createUncachedDescriptors,\n          index,\n          envName,\n        ),\n      ),\n    ),\n);\n\nfunction buildFileLogger(\n  filepath: string,\n  context: ConfigContext,\n  baseLogger: ConfigPrinter | void,\n) {\n  if (!baseLogger) {\n    return () => {};\n  }\n  return baseLogger.configure(context.showConfig, ChainFormatter.Config, {\n    filepath,\n  });\n}\n\nfunction buildRootDescriptors(\n  { dirname, options }: Partial<ValidatedFile>,\n  alias: string,\n  descriptors: (\n    dirname: string,\n    options: ValidatedOptions,\n    alias: string,\n  ) => OptionsAndDescriptors,\n) {\n  return descriptors(dirname, options, alias);\n}\n\nfunction buildProgrammaticLogger(\n  _: unknown,\n  context: ConfigContext,\n  baseLogger: ConfigPrinter | void,\n) {\n  if (!baseLogger) {\n    return () => {};\n  }\n  return baseLogger.configure(context.showConfig, ChainFormatter.Programmatic, {\n    callerName: context.caller?.name,\n  });\n}\n\nfunction buildEnvDescriptors(\n  { dirname, options }: Partial<ValidatedFile>,\n  alias: string,\n  descriptors: (\n    dirname: string,\n    options: ValidatedOptions,\n    alias: string,\n  ) => OptionsAndDescriptors,\n  envName: string,\n) {\n  const opts = options.env?.[envName];\n  return opts ? descriptors(dirname, opts, `${alias}.env[\"${envName}\"]`) : null;\n}\n\nfunction buildOverrideDescriptors(\n  { dirname, options }: Partial<ValidatedFile>,\n  alias: string,\n  descriptors: (\n    dirname: string,\n    options: ValidatedOptions,\n    alias: string,\n  ) => OptionsAndDescriptors,\n  index: number,\n) {\n  const opts = options.overrides?.[index];\n  if (!opts) throw new Error(\"Assertion failure - missing override\");\n\n  return descriptors(dirname, opts, `${alias}.overrides[${index}]`);\n}\n\nfunction buildOverrideEnvDescriptors(\n  { dirname, options }: Partial<ValidatedFile>,\n  alias: string,\n  descriptors: (\n    dirname: string,\n    options: ValidatedOptions,\n    alias: string,\n  ) => OptionsAndDescriptors,\n  index: number,\n  envName: string,\n) {\n  const override = options.overrides?.[index];\n  if (!override) throw new Error(\"Assertion failure - missing override\");\n\n  const opts = override.env?.[envName];\n  return opts\n    ? descriptors(\n        dirname,\n        opts,\n        `${alias}.overrides[${index}].env[\"${envName}\"]`,\n      )\n    : null;\n}\n\nfunction makeChainWalker<\n  ArgT extends {\n    options: ValidatedOptions;\n    dirname: string;\n    filepath?: string;\n  },\n>({\n  root,\n  env,\n  overrides,\n  overridesEnv,\n  createLogger,\n}: {\n  root: (configEntry: ArgT) => OptionsAndDescriptors;\n  env: (configEntry: ArgT, env: string) => OptionsAndDescriptors | null;\n  overrides: (configEntry: ArgT, index: number) => OptionsAndDescriptors;\n  overridesEnv: (\n    configEntry: ArgT,\n    index: number,\n    env: string,\n  ) => OptionsAndDescriptors | null;\n  createLogger: (\n    configEntry: ArgT,\n    context: ConfigContext,\n    printer: ConfigPrinter | void,\n  ) => (\n    opts: OptionsAndDescriptors,\n    index?: number | null,\n    env?: string | null,\n  ) => void;\n}): (\n  configEntry: ArgT,\n  context: ConfigContext,\n  files?: Set<ConfigFile>,\n  baseLogger?: ConfigPrinter,\n) => Handler<ConfigChain | null> {\n  return function* chainWalker(input, context, files = new Set(), baseLogger) {\n    const { dirname } = input;\n\n    const flattenedConfigs: Array<{\n      config: OptionsAndDescriptors;\n      index: number | undefined | null;\n      envName: string | undefined | null;\n    }> = [];\n\n    const rootOpts = root(input);\n    if (configIsApplicable(rootOpts, dirname, context, input.filepath)) {\n      flattenedConfigs.push({\n        config: rootOpts,\n        envName: undefined,\n        index: undefined,\n      });\n\n      const envOpts = env(input, context.envName);\n      if (\n        envOpts &&\n        configIsApplicable(envOpts, dirname, context, input.filepath)\n      ) {\n        flattenedConfigs.push({\n          config: envOpts,\n          envName: context.envName,\n          index: undefined,\n        });\n      }\n\n      (rootOpts.options.overrides || []).forEach((_, index) => {\n        const overrideOps = overrides(input, index);\n        if (configIsApplicable(overrideOps, dirname, context, input.filepath)) {\n          flattenedConfigs.push({\n            config: overrideOps,\n            index,\n            envName: undefined,\n          });\n\n          const overrideEnvOpts = overridesEnv(input, index, context.envName);\n          if (\n            overrideEnvOpts &&\n            configIsApplicable(\n              overrideEnvOpts,\n              dirname,\n              context,\n              input.filepath,\n            )\n          ) {\n            flattenedConfigs.push({\n              config: overrideEnvOpts,\n              index,\n              envName: context.envName,\n            });\n          }\n        }\n      });\n    }\n\n    // Process 'ignore' and 'only' before 'extends' items are processed so\n    // that we don't do extra work loading extended configs if a file is\n    // ignored.\n    if (\n      flattenedConfigs.some(\n        ({\n          config: {\n            options: { ignore, only },\n          },\n        }) => shouldIgnore(context, ignore, only, dirname),\n      )\n    ) {\n      return null;\n    }\n\n    const chain = emptyChain();\n    const logger = createLogger(input, context, baseLogger);\n\n    for (const { config, index, envName } of flattenedConfigs) {\n      if (\n        !(yield* mergeExtendsChain(\n          chain,\n          config.options,\n          dirname,\n          context,\n          files,\n          baseLogger,\n        ))\n      ) {\n        return null;\n      }\n\n      logger(config, index, envName);\n      yield* mergeChainOpts(chain, config);\n    }\n    return chain;\n  };\n}\n\nfunction* mergeExtendsChain(\n  chain: ConfigChain,\n  opts: ValidatedOptions,\n  dirname: string,\n  context: ConfigContext,\n  files: Set<ConfigFile>,\n  baseLogger?: ConfigPrinter,\n): Handler<boolean> {\n  if (opts.extends === undefined) return true;\n\n  const file = yield* loadConfig(\n    opts.extends,\n    dirname,\n    context.envName,\n    context.caller,\n  );\n\n  if (files.has(file)) {\n    throw new Error(\n      `Configuration cycle detected loading ${file.filepath}.\\n` +\n        `File already loaded following the config chain:\\n` +\n        Array.from(files, file => ` - ${file.filepath}`).join(\"\\n\"),\n    );\n  }\n\n  files.add(file);\n  const fileChain = yield* loadFileChain(\n    validateExtendFile(file),\n    context,\n    files,\n    baseLogger,\n  );\n  files.delete(file);\n\n  if (!fileChain) return false;\n\n  mergeChain(chain, fileChain);\n\n  return true;\n}\n\nfunction mergeChain(target: ConfigChain, source: ConfigChain): ConfigChain {\n  target.options.push(...source.options);\n  target.plugins.push(...source.plugins);\n  target.presets.push(...source.presets);\n  for (const file of source.files) {\n    target.files.add(file);\n  }\n\n  return target;\n}\n\nfunction* mergeChainOpts(\n  target: ConfigChain,\n  { options, plugins, presets }: OptionsAndDescriptors,\n): Handler<ConfigChain> {\n  target.options.push(options);\n  target.plugins.push(...(yield* plugins()));\n  target.presets.push(...(yield* presets()));\n\n  return target;\n}\n\nfunction emptyChain(): ConfigChain {\n  return {\n    options: [],\n    presets: [],\n    plugins: [],\n    files: new Set(),\n  };\n}\n\nfunction normalizeOptions(opts: ValidatedOptions): ValidatedOptions {\n  const options = {\n    ...opts,\n  };\n  delete options.extends;\n  delete options.env;\n  delete options.overrides;\n  delete options.plugins;\n  delete options.presets;\n  delete options.passPerPreset;\n  delete options.ignore;\n  delete options.only;\n  delete options.test;\n  delete options.include;\n  delete options.exclude;\n\n  // \"sourceMap\" is just aliased to sourceMap, so copy it over as\n  // we merge the options together.\n  if (Object.prototype.hasOwnProperty.call(options, \"sourceMap\")) {\n    options.sourceMaps = options.sourceMap;\n    delete options.sourceMap;\n  }\n  return options;\n}\n\nfunction dedupDescriptors<API>(\n  items: Array<UnloadedDescriptor<API>>,\n): Array<UnloadedDescriptor<API>> {\n  const map: Map<\n    Function,\n    Map<string | void, { value: UnloadedDescriptor<API> }>\n  > = new Map();\n\n  const descriptors = [];\n\n  for (const item of items) {\n    if (typeof item.value === \"function\") {\n      const fnKey = item.value;\n      let nameMap = map.get(fnKey);\n      if (!nameMap) {\n        nameMap = new Map();\n        map.set(fnKey, nameMap);\n      }\n      let desc = nameMap.get(item.name);\n      if (!desc) {\n        desc = { value: item };\n        descriptors.push(desc);\n\n        // Treat passPerPreset presets as unique, skipping them\n        // in the merge processing steps.\n        if (!item.ownPass) nameMap.set(item.name, desc);\n      } else {\n        desc.value = item;\n      }\n    } else {\n      descriptors.push({ value: item });\n    }\n  }\n\n  return descriptors.reduce((acc, desc) => {\n    acc.push(desc.value);\n    return acc;\n  }, []);\n}\n\nfunction configIsApplicable(\n  { options }: OptionsAndDescriptors,\n  dirname: string,\n  context: ConfigContext,\n  configName: string,\n): boolean {\n  return (\n    (options.test === undefined ||\n      configFieldIsApplicable(context, options.test, dirname, configName)) &&\n    (options.include === undefined ||\n      configFieldIsApplicable(context, options.include, dirname, configName)) &&\n    (options.exclude === undefined ||\n      !configFieldIsApplicable(context, options.exclude, dirname, configName))\n  );\n}\n\nfunction configFieldIsApplicable(\n  context: ConfigContext,\n  test: ConfigApplicableTest,\n  dirname: string,\n  configName: string,\n): boolean {\n  const patterns = Array.isArray(test) ? test : [test];\n\n  return matchesPatterns(context, patterns, dirname, configName);\n}\n\n/**\n * Print the ignoreList-values in a more helpful way than the default.\n */\nfunction ignoreListReplacer(\n  _key: string,\n  value: IgnoreList | IgnoreItem,\n): IgnoreList | IgnoreItem | string {\n  if (value instanceof RegExp) {\n    return String(value);\n  }\n\n  return value;\n}\n\n/**\n * Tests if a filename should be ignored based on \"ignore\" and \"only\" options.\n */\nfunction shouldIgnore(\n  context: ConfigContext,\n  ignore: IgnoreList | undefined | null,\n  only: IgnoreList | undefined | null,\n  dirname: string,\n): boolean {\n  if (ignore && matchesPatterns(context, ignore, dirname)) {\n    const message = `No config is applied to \"${\n      context.filename ?? \"(unknown)\"\n    }\" because it matches one of \\`ignore: ${JSON.stringify(\n      ignore,\n      ignoreListReplacer,\n    )}\\` from \"${dirname}\"`;\n    debug(message);\n    if (context.showConfig) {\n      console.log(message);\n    }\n    return true;\n  }\n\n  if (only && !matchesPatterns(context, only, dirname)) {\n    const message = `No config is applied to \"${\n      context.filename ?? \"(unknown)\"\n    }\" because it fails to match one of \\`only: ${JSON.stringify(\n      only,\n      ignoreListReplacer,\n    )}\\` from \"${dirname}\"`;\n    debug(message);\n    if (context.showConfig) {\n      console.log(message);\n    }\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * Returns result of calling function with filename if pattern is a function.\n * Otherwise returns result of matching pattern Regex with filename.\n */\nfunction matchesPatterns(\n  context: ConfigContext,\n  patterns: IgnoreList,\n  dirname: string,\n  configName?: string,\n): boolean {\n  return patterns.some(pattern =>\n    matchPattern(pattern, dirname, context.filename, context, configName),\n  );\n}\n\nfunction matchPattern(\n  pattern: IgnoreItem,\n  dirname: string,\n  pathToTest: string | undefined,\n  context: ConfigContext,\n  configName?: string,\n): boolean {\n  if (typeof pattern === \"function\") {\n    return !!endHiddenCallStack(pattern)(pathToTest, {\n      dirname,\n      envName: context.envName,\n      caller: context.caller,\n    });\n  }\n\n  if (typeof pathToTest !== \"string\") {\n    throw new ConfigError(\n      `Configuration contains string/RegExp pattern, but no filename was passed to Babel`,\n      configName,\n    );\n  }\n\n  if (typeof pattern === \"string\") {\n    pattern = pathPatternToRegex(pattern, dirname);\n  }\n  return pattern.test(pathToTest);\n}\n"], "mappings": ";;;;;;;;AAEA,SAAAA,MAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,OAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,MAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAG,QAAA,GAAAF,OAAA;AASA,IAAAG,eAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AAGA,IAAAK,kBAAA,GAAAL,OAAA;AACA,IAAAM,YAAA,GAAAN,OAAA;AAKA,IAAAO,MAAA,GAAAP,OAAA;AAQA,IAAAQ,QAAA,GAAAR,OAAA;AAEA,IAAAS,kBAAA,GAAAT,OAAA;AAZA,MAAMU,KAAK,GAAGC,OAASA,CAAC,CAAC,2BAA2B,CAAC;AAgD9C,UAAUC,gBAAgBA,CAC/BC,GAAmB,EACnBC,OAAY,EACiB;EAC7B,MAAMC,KAAK,GAAG,OAAOC,sBAAsB,CAACH,GAAG,EAAEC,OAAO,CAAC;EACzD,IAAI,CAACC,KAAK,EAAE,OAAO,IAAI;EAEvB,OAAO;IACLE,OAAO,EAAEC,gBAAgB,CAACH,KAAK,CAACE,OAAO,CAAC;IACxCE,OAAO,EAAED,gBAAgB,CAACH,KAAK,CAACI,OAAO,CAAC;IACxCC,OAAO,EAAEL,KAAK,CAACK,OAAO,CAACC,GAAG,CAACC,CAAC,IAAIC,gBAAgB,CAACD,CAAC,CAAC,CAAC;IACpDE,KAAK,EAAE,IAAIC,GAAG,CAAC;EACjB,CAAC;AACH;AAEO,MAAMT,sBAAsB,GAAGU,eAAe,CAAiB;EACpEC,IAAI,EAAEC,MAAM,IAAIC,qBAAqB,CAACD,MAAM,CAAC;EAC7CE,GAAG,EAAEA,CAACF,MAAM,EAAEG,OAAO,KAAKC,wBAAwB,CAACJ,MAAM,CAAC,CAACG,OAAO,CAAC;EACnEE,SAAS,EAAEA,CAACL,MAAM,EAAEM,KAAK,KAAKC,8BAA8B,CAACP,MAAM,CAAC,CAACM,KAAK,CAAC;EAC3EE,YAAY,EAAEA,CAACR,MAAM,EAAEM,KAAK,EAAEH,OAAO,KACnCM,iCAAiC,CAACT,MAAM,CAAC,CAACM,KAAK,CAAC,CAACH,OAAO,CAAC;EAC3DO,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC;AAC7B,CAAC,CAAC;AAACC,OAAA,CAAAvB,sBAAA,GAAAA,sBAAA;AACH,MAAMa,qBAAqB,GAAG,IAAAW,0BAAiB,EAAEZ,MAAsB,IACrEa,oBAAoB,CAACb,MAAM,EAAEA,MAAM,CAACc,KAAK,EAAEC,4CAAyB,CACtE,CAAC;AACD,MAAMX,wBAAwB,GAAG,IAAAQ,0BAAiB,EAAEZ,MAAsB,IACxE,IAAAgB,4BAAmB,EAAEb,OAAe,IAClCc,mBAAmB,CACjBjB,MAAM,EACNA,MAAM,CAACc,KAAK,EACZC,4CAAyB,EACzBZ,OACF,CACF,CACF,CAAC;AACD,MAAMI,8BAA8B,GAAG,IAAAK,0BAAiB,EACrDZ,MAAsB,IACrB,IAAAgB,4BAAmB,EAAEV,KAAa,IAChCY,wBAAwB,CACtBlB,MAAM,EACNA,MAAM,CAACc,KAAK,EACZC,4CAAyB,EACzBT,KACF,CACF,CACJ,CAAC;AACD,MAAMG,iCAAiC,GAAG,IAAAG,0BAAiB,EACxDZ,MAAsB,IACrB,IAAAgB,4BAAmB,EAAEV,KAAa,IAChC,IAAAU,4BAAmB,EAAEb,OAAe,IAClCgB,2BAA2B,CACzBnB,MAAM,EACNA,MAAM,CAACc,KAAK,EACZC,4CAAyB,EACzBT,KAAK,EACLH,OACF,CACF,CACF,CACJ,CAAC;AAcM,UAAUiB,cAAcA,CAC7BC,IAAsB,EACtBnC,OAAsB,EACW;EACjC,IAAIoC,YAAY,EAAEC,aAAa;EAC/B,MAAMC,kBAAkB,GAAG,IAAIC,sBAAa,CAAC,CAAC;EAC9C,MAAMC,iBAAiB,GAAG,OAAOC,qBAAqB,CACpD;IACEnC,OAAO,EAAE6B,IAAI;IACbO,OAAO,EAAE1C,OAAO,CAAC2C;EACnB,CAAC,EACD3C,OAAO,EACP4C,SAAS,EACTN,kBACF,CAAC;EACD,IAAI,CAACE,iBAAiB,EAAE,OAAO,IAAI;EACnC,MAAMK,kBAAkB,GAAG,OAAOP,kBAAkB,CAACQ,MAAM,CAAC,CAAC;EAE7D,IAAIC,UAAU;EACd,IAAI,OAAOZ,IAAI,CAACY,UAAU,KAAK,QAAQ,EAAE;IACvCA,UAAU,GAAG,OAAO,IAAAC,iBAAU,EAC5Bb,IAAI,CAACY,UAAU,EACf/C,OAAO,CAAC2C,GAAG,EACX3C,OAAO,CAACiB,OAAO,EACfjB,OAAO,CAACiD,MACV,CAAC;EACH,CAAC,MAAM,IAAId,IAAI,CAACY,UAAU,KAAK,KAAK,EAAE;IACpCA,UAAU,GAAG,OAAO,IAAAG,qBAAc,EAChClD,OAAO,CAACa,IAAI,EACZb,OAAO,CAACiB,OAAO,EACfjB,OAAO,CAACiD,MACV,CAAC;EACH;EAEA,IAAI;IAAEE,OAAO;IAAEC;EAAa,CAAC,GAAGjB,IAAI;EACpC,IAAIkB,qBAAqB,GAAGrD,OAAO,CAAC2C,GAAG;EAEvC,MAAMW,eAAe,GAAGC,UAAU,CAAC,CAAC;EACpC,MAAMC,gBAAgB,GAAG,IAAIjB,sBAAa,CAAC,CAAC;EAC5C,IAAIQ,UAAU,EAAE;IACd,MAAMU,aAAa,GAAGC,kBAAkB,CAACX,UAAU,CAAC;IACpD,MAAMY,MAAM,GAAG,OAAOC,aAAa,CACjCH,aAAa,EACbzD,OAAO,EACP4C,SAAS,EACTY,gBACF,CAAC;IACD,IAAI,CAACG,MAAM,EAAE,OAAO,IAAI;IACxBvB,YAAY,GAAG,OAAOoB,gBAAgB,CAACV,MAAM,CAAC,CAAC;IAI/C,IAAIK,OAAO,KAAKP,SAAS,EAAE;MACzBO,OAAO,GAAGM,aAAa,CAACnD,OAAO,CAAC6C,OAAO;IACzC;IACA,IAAIC,YAAY,KAAKR,SAAS,EAAE;MAC9BS,qBAAqB,GAAGI,aAAa,CAACf,OAAO;MAC7CU,YAAY,GAAGK,aAAa,CAACnD,OAAO,CAAC8C,YAAY;IACnD;IAEAS,UAAU,CAACP,eAAe,EAAEK,MAAM,CAAC;EACrC;EAEA,IAAIG,UAAU,EAAEC,WAAW;EAC3B,IAAIC,SAAS,GAAG,KAAK;EACrB,MAAMC,SAAS,GAAGV,UAAU,CAAC,CAAC;EAE9B,IACE,CAACJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKP,SAAS,KAC1C,OAAO5C,OAAO,CAACkE,QAAQ,KAAK,QAAQ,EACpC;IACA,MAAMC,OAAO,GAAG,OAAO,IAAAC,sBAAe,EAACpE,OAAO,CAACkE,QAAQ,CAAC;IAExD,IACEC,OAAO,IACPE,kBAAkB,CAACrE,OAAO,EAAEmE,OAAO,EAAEf,YAAY,EAAEC,qBAAqB,CAAC,EACzE;MACA,CAAC;QAAEiB,MAAM,EAAER,UAAU;QAAES,MAAM,EAAER;MAAY,CAAC,GAAG,OAAO,IAAAS,yBAAkB,EACtEL,OAAO,EACPnE,OAAO,CAACiB,OAAO,EACfjB,OAAO,CAACiD,MACV,CAAC;MAED,IAAIa,UAAU,EAAE;QACdG,SAAS,CAACvD,KAAK,CAAC+D,GAAG,CAACX,UAAU,CAACY,QAAQ,CAAC;MAC1C;MAEA,IACEZ,UAAU,IACVa,YAAY,CAAC3E,OAAO,EAAE8D,UAAU,CAACQ,MAAM,EAAE,IAAI,EAAER,UAAU,CAACpB,OAAO,CAAC,EAClE;QACAsB,SAAS,GAAG,IAAI;MAClB;MAEA,IAAID,WAAW,IAAI,CAACC,SAAS,EAAE;QAC7B,MAAMP,aAAa,GAAGmB,mBAAmB,CAACb,WAAW,CAAC;QACtD,MAAMc,aAAa,GAAG,IAAItC,sBAAa,CAAC,CAAC;QACzC,MAAMoB,MAAM,GAAG,OAAOC,aAAa,CACjCH,aAAa,EACbzD,OAAO,EACP4C,SAAS,EACTiC,aACF,CAAC;QACD,IAAI,CAAClB,MAAM,EAAE;UACXK,SAAS,GAAG,IAAI;QAClB,CAAC,MAAM;UACL3B,aAAa,GAAG,OAAOwC,aAAa,CAAC/B,MAAM,CAAC,CAAC;UAC7Ce,UAAU,CAACI,SAAS,EAAEN,MAAM,CAAC;QAC/B;MACF;MAEA,IAAII,WAAW,IAAIC,SAAS,EAAE;QAC5BC,SAAS,CAACvD,KAAK,CAAC+D,GAAG,CAACV,WAAW,CAACW,QAAQ,CAAC;MAC3C;IACF;EACF;EAEA,IAAI1E,OAAO,CAAC8E,UAAU,EAAE;IACtBC,OAAO,CAACC,GAAG,CACR,qBAAoBhF,OAAO,CAACkE,QAAS,2BAA0B,GAE9D,CAAC9B,YAAY,EAAEC,aAAa,EAAEQ,kBAAkB,CAAC,CAC9CoC,MAAM,CAACC,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC,CAChBC,IAAI,CAAC,MAAM,CAAC,GACf,+BACJ,CAAC;EACH;EAGA,MAAMlF,KAAK,GAAG4D,UAAU,CACtBA,UAAU,CAACA,UAAU,CAACN,UAAU,CAAC,CAAC,EAAED,eAAe,CAAC,EAAEW,SAAS,CAAC,EAChEzB,iBACF,CAAC;EAED,OAAO;IACLrC,OAAO,EAAE6D,SAAS,GAAG,EAAE,GAAG5D,gBAAgB,CAACH,KAAK,CAACE,OAAO,CAAC;IACzDE,OAAO,EAAE2D,SAAS,GAAG,EAAE,GAAG5D,gBAAgB,CAACH,KAAK,CAACI,OAAO,CAAC;IACzDC,OAAO,EAAE0D,SAAS,GAAG,EAAE,GAAG/D,KAAK,CAACK,OAAO,CAACC,GAAG,CAACC,CAAC,IAAIC,gBAAgB,CAACD,CAAC,CAAC,CAAC;IACrE4E,YAAY,EAAEpB,SAAS,GAAG,SAAS,GAAG,WAAW;IACjDM,MAAM,EAAER,UAAU,IAAIlB,SAAS;IAC/BO,OAAO,EAAEY,WAAW,IAAInB,SAAS;IACjC2B,MAAM,EAAExB,UAAU,IAAIH,SAAS;IAC/BlC,KAAK,EAAET,KAAK,CAACS;EACf,CAAC;AACH;AAEA,SAAS2D,kBAAkBA,CACzBrE,OAAsB,EACtBmE,OAAwB,EACxBf,YAAuC,EACvCC,qBAA6B,EACpB;EACT,IAAI,OAAOD,YAAY,KAAK,SAAS,EAAE,OAAOA,YAAY;EAE1D,MAAMiC,YAAY,GAAGrF,OAAO,CAACa,IAAI;EAIjC,IAAIuC,YAAY,KAAKR,SAAS,EAAE;IAC9B,OAAOuB,OAAO,CAACmB,WAAW,CAACC,OAAO,CAACF,YAAY,CAAC,KAAK,CAAC,CAAC;EACzD;EAEA,IAAIG,eAAe,GAAGpC,YAAY;EAClC,IAAI,CAACqC,KAAK,CAACC,OAAO,CAACF,eAAe,CAAC,EAAE;IACnCA,eAAe,GAAG,CAACA,eAAe,CAAe;EACnD;EACAA,eAAe,GAAGA,eAAe,CAACjF,GAAG,CAACoF,GAAG,IAAI;IAC3C,OAAO,OAAOA,GAAG,KAAK,QAAQ,GAC1BC,MAAGA,CAAC,CAACC,OAAO,CAACxC,qBAAqB,EAAEsC,GAAG,CAAC,GACxCA,GAAG;EACT,CAAC,CAAC;EAIF,IAAIH,eAAe,CAACM,MAAM,KAAK,CAAC,IAAIN,eAAe,CAAC,CAAC,CAAC,KAAKH,YAAY,EAAE;IACvE,OAAOlB,OAAO,CAACmB,WAAW,CAACC,OAAO,CAACF,YAAY,CAAC,KAAK,CAAC,CAAC;EACzD;EAEA,OAAOG,eAAe,CAACO,IAAI,CAACJ,GAAG,IAAI;IACjC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC3BA,GAAG,GAAG,IAAAK,uBAAkB,EAACL,GAAG,EAAEtC,qBAAqB,CAAC;IACtD;IAEA,OAAOc,OAAO,CAACmB,WAAW,CAACS,IAAI,CAACE,SAAS,IAAI;MAC3C,OAAOC,YAAY,CAACP,GAAG,EAAEtC,qBAAqB,EAAE4C,SAAS,EAAEjG,OAAO,CAAC;IACrE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,MAAM0D,kBAAkB,GAAG,IAAAhC,0BAAiB,EACzCyE,IAAgB,KAAqB;EACpCzB,QAAQ,EAAEyB,IAAI,CAACzB,QAAQ;EACvBhC,OAAO,EAAEyD,IAAI,CAACzD,OAAO;EACrBpC,OAAO,EAAE,IAAA8F,iBAAQ,EAAC,YAAY,EAAED,IAAI,CAAC7F,OAAO,EAAE6F,IAAI,CAACzB,QAAQ;AAC7D,CAAC,CACH,CAAC;AAED,MAAME,mBAAmB,GAAG,IAAAlD,0BAAiB,EAC1CyE,IAAgB,KAAqB;EACpCzB,QAAQ,EAAEyB,IAAI,CAACzB,QAAQ;EACvBhC,OAAO,EAAEyD,IAAI,CAACzD,OAAO;EACrBpC,OAAO,EAAE,IAAA8F,iBAAQ,EAAC,aAAa,EAAED,IAAI,CAAC7F,OAAO,EAAE6F,IAAI,CAACzB,QAAQ;AAC9D,CAAC,CACH,CAAC;AAED,MAAM2B,kBAAkB,GAAG,IAAA3E,0BAAiB,EACzCyE,IAAgB,KAAqB;EACpCzB,QAAQ,EAAEyB,IAAI,CAACzB,QAAQ;EACvBhC,OAAO,EAAEyD,IAAI,CAACzD,OAAO;EACrBpC,OAAO,EAAE,IAAA8F,iBAAQ,EAAC,aAAa,EAAED,IAAI,CAAC7F,OAAO,EAAE6F,IAAI,CAACzB,QAAQ;AAC9D,CAAC,CACH,CAAC;AAKD,MAAMjC,qBAAqB,GAAG7B,eAAe,CAAC;EAC5CC,IAAI,EAAEyF,KAAK,IAAI3E,oBAAoB,CAAC2E,KAAK,EAAE,MAAM,EAAEC,0CAAuB,CAAC;EAC3EvF,GAAG,EAAEA,CAACsF,KAAK,EAAErF,OAAO,KAClBc,mBAAmB,CAACuE,KAAK,EAAE,MAAM,EAAEC,0CAAuB,EAAEtF,OAAO,CAAC;EACtEE,SAAS,EAAEA,CAACmF,KAAK,EAAElF,KAAK,KACtBY,wBAAwB,CAACsE,KAAK,EAAE,MAAM,EAAEC,0CAAuB,EAAEnF,KAAK,CAAC;EACzEE,YAAY,EAAEA,CAACgF,KAAK,EAAElF,KAAK,EAAEH,OAAO,KAClCgB,2BAA2B,CACzBqE,KAAK,EACL,MAAM,EACNC,0CAAuB,EACvBnF,KAAK,EACLH,OACF,CAAC;EACHO,YAAY,EAAEA,CAAC8E,KAAK,EAAEtG,OAAO,EAAEwG,UAAU,KACvCC,uBAAuB,CAACH,KAAK,EAAEtG,OAAO,EAAEwG,UAAU;AACtD,CAAC,CAAC;AAKF,MAAME,mBAAmB,GAAG9F,eAAe,CAAgB;EACzDC,IAAI,EAAEsF,IAAI,IAAIQ,mBAAmB,CAACR,IAAI,CAAC;EACvCnF,GAAG,EAAEA,CAACmF,IAAI,EAAElF,OAAO,KAAK2F,sBAAsB,CAACT,IAAI,CAAC,CAAClF,OAAO,CAAC;EAC7DE,SAAS,EAAEA,CAACgF,IAAI,EAAE/E,KAAK,KAAKyF,4BAA4B,CAACV,IAAI,CAAC,CAAC/E,KAAK,CAAC;EACrEE,YAAY,EAAEA,CAAC6E,IAAI,EAAE/E,KAAK,EAAEH,OAAO,KACjC6F,+BAA+B,CAACX,IAAI,CAAC,CAAC/E,KAAK,CAAC,CAACH,OAAO,CAAC;EACvDO,YAAY,EAAEA,CAAC2E,IAAI,EAAEnG,OAAO,EAAEwG,UAAU,KACtCO,eAAe,CAACZ,IAAI,CAACzB,QAAQ,EAAE1E,OAAO,EAAEwG,UAAU;AACtD,CAAC,CAAC;AAEF,UAAU5C,aAAaA,CACrB0C,KAAoB,EACpBtG,OAAsB,EACtBU,KAAsB,EACtB8F,UAAyB,EACzB;EACA,MAAMvG,KAAK,GAAG,OAAOyG,mBAAmB,CAACJ,KAAK,EAAEtG,OAAO,EAAEU,KAAK,EAAE8F,UAAU,CAAC;EAC3EvG,KAAK,oBAALA,KAAK,CAAES,KAAK,CAAC+D,GAAG,CAAC6B,KAAK,CAAC5B,QAAQ,CAAC;EAEhC,OAAOzE,KAAK;AACd;AAEA,MAAM0G,mBAAmB,GAAG,IAAAjF,0BAAiB,EAAEyE,IAAmB,IAChExE,oBAAoB,CAACwE,IAAI,EAAEA,IAAI,CAACzB,QAAQ,EAAE7C,4CAAyB,CACrE,CAAC;AACD,MAAM+E,sBAAsB,GAAG,IAAAlF,0BAAiB,EAAEyE,IAAmB,IACnE,IAAArE,4BAAmB,EAAEb,OAAe,IAClCc,mBAAmB,CACjBoE,IAAI,EACJA,IAAI,CAACzB,QAAQ,EACb7C,4CAAyB,EACzBZ,OACF,CACF,CACF,CAAC;AACD,MAAM4F,4BAA4B,GAAG,IAAAnF,0BAAiB,EAAEyE,IAAmB,IACzE,IAAArE,4BAAmB,EAAEV,KAAa,IAChCY,wBAAwB,CACtBmE,IAAI,EACJA,IAAI,CAACzB,QAAQ,EACb7C,4CAAyB,EACzBT,KACF,CACF,CACF,CAAC;AACD,MAAM0F,+BAA+B,GAAG,IAAApF,0BAAiB,EACtDyE,IAAmB,IAClB,IAAArE,4BAAmB,EAAEV,KAAa,IAChC,IAAAU,4BAAmB,EAAEb,OAAe,IAClCgB,2BAA2B,CACzBkE,IAAI,EACJA,IAAI,CAACzB,QAAQ,EACb7C,4CAAyB,EACzBT,KAAK,EACLH,OACF,CACF,CACF,CACJ,CAAC;AAED,SAAS8F,eAAeA,CACtBrC,QAAgB,EAChB1E,OAAsB,EACtBwG,UAAgC,EAChC;EACA,IAAI,CAACA,UAAU,EAAE;IACf,OAAO,MAAM,CAAC,CAAC;EACjB;EACA,OAAOA,UAAU,CAACQ,SAAS,CAAChH,OAAO,CAAC8E,UAAU,EAAEmC,uBAAc,CAACC,MAAM,EAAE;IACrExC;EACF,CAAC,CAAC;AACJ;AAEA,SAAS/C,oBAAoBA,CAC3B;EAAEe,OAAO;EAAEpC;AAAgC,CAAC,EAC5CsB,KAAa,EACbuF,WAI0B,EAC1B;EACA,OAAOA,WAAW,CAACzE,OAAO,EAAEpC,OAAO,EAAEsB,KAAK,CAAC;AAC7C;AAEA,SAAS6E,uBAAuBA,CAC9BW,CAAU,EACVpH,OAAsB,EACtBwG,UAAgC,EAChC;EAAA,IAAAa,eAAA;EACA,IAAI,CAACb,UAAU,EAAE;IACf,OAAO,MAAM,CAAC,CAAC;EACjB;EACA,OAAOA,UAAU,CAACQ,SAAS,CAAChH,OAAO,CAAC8E,UAAU,EAAEmC,uBAAc,CAACK,YAAY,EAAE;IAC3EC,UAAU,GAAAF,eAAA,GAAErH,OAAO,CAACiD,MAAM,qBAAdoE,eAAA,CAAgBG;EAC9B,CAAC,CAAC;AACJ;AAEA,SAASzF,mBAAmBA,CAC1B;EAAEW,OAAO;EAAEpC;AAAgC,CAAC,EAC5CsB,KAAa,EACbuF,WAI0B,EAC1BlG,OAAe,EACf;EAAA,IAAAwG,YAAA;EACA,MAAMtF,IAAI,IAAAsF,YAAA,GAAGnH,OAAO,CAACU,GAAG,qBAAXyG,YAAA,CAAcxG,OAAO,CAAC;EACnC,OAAOkB,IAAI,GAAGgF,WAAW,CAACzE,OAAO,EAAEP,IAAI,EAAG,GAAEP,KAAM,SAAQX,OAAQ,IAAG,CAAC,GAAG,IAAI;AAC/E;AAEA,SAASe,wBAAwBA,CAC/B;EAAEU,OAAO;EAAEpC;AAAgC,CAAC,EAC5CsB,KAAa,EACbuF,WAI0B,EAC1B/F,KAAa,EACb;EAAA,IAAAsG,kBAAA;EACA,MAAMvF,IAAI,IAAAuF,kBAAA,GAAGpH,OAAO,CAACa,SAAS,qBAAjBuG,kBAAA,CAAoBtG,KAAK,CAAC;EACvC,IAAI,CAACe,IAAI,EAAE,MAAM,IAAIwF,KAAK,CAAC,sCAAsC,CAAC;EAElE,OAAOR,WAAW,CAACzE,OAAO,EAAEP,IAAI,EAAG,GAAEP,KAAM,cAAaR,KAAM,GAAE,CAAC;AACnE;AAEA,SAASa,2BAA2BA,CAClC;EAAES,OAAO;EAAEpC;AAAgC,CAAC,EAC5CsB,KAAa,EACbuF,WAI0B,EAC1B/F,KAAa,EACbH,OAAe,EACf;EAAA,IAAA2G,mBAAA,EAAAC,aAAA;EACA,MAAMC,QAAQ,IAAAF,mBAAA,GAAGtH,OAAO,CAACa,SAAS,qBAAjByG,mBAAA,CAAoBxG,KAAK,CAAC;EAC3C,IAAI,CAAC0G,QAAQ,EAAE,MAAM,IAAIH,KAAK,CAAC,sCAAsC,CAAC;EAEtE,MAAMxF,IAAI,IAAA0F,aAAA,GAAGC,QAAQ,CAAC9G,GAAG,qBAAZ6G,aAAA,CAAe5G,OAAO,CAAC;EACpC,OAAOkB,IAAI,GACPgF,WAAW,CACTzE,OAAO,EACPP,IAAI,EACH,GAAEP,KAAM,cAAaR,KAAM,UAASH,OAAQ,IAC/C,CAAC,GACD,IAAI;AACV;AAEA,SAASL,eAAeA,CAMtB;EACAC,IAAI;EACJG,GAAG;EACHG,SAAS;EACTG,YAAY;EACZE;AAmBF,CAAC,EAKgC;EAC/B,OAAO,UAAUuG,WAAWA,CAACzB,KAAK,EAAEtG,OAAO,EAAEU,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC,EAAE6F,UAAU,EAAE;IAC1E,MAAM;MAAE9D;IAAQ,CAAC,GAAG4D,KAAK;IAEzB,MAAM0B,gBAIJ,GAAG,EAAE;IAEP,MAAMC,QAAQ,GAAGpH,IAAI,CAACyF,KAAK,CAAC;IAC5B,IAAI4B,kBAAkB,CAACD,QAAQ,EAAEvF,OAAO,EAAE1C,OAAO,EAAEsG,KAAK,CAAC5B,QAAQ,CAAC,EAAE;MAClEsD,gBAAgB,CAACG,IAAI,CAAC;QACpB5D,MAAM,EAAE0D,QAAQ;QAChBhH,OAAO,EAAE2B,SAAS;QAClBxB,KAAK,EAAEwB;MACT,CAAC,CAAC;MAEF,MAAMwF,OAAO,GAAGpH,GAAG,CAACsF,KAAK,EAAEtG,OAAO,CAACiB,OAAO,CAAC;MAC3C,IACEmH,OAAO,IACPF,kBAAkB,CAACE,OAAO,EAAE1F,OAAO,EAAE1C,OAAO,EAAEsG,KAAK,CAAC5B,QAAQ,CAAC,EAC7D;QACAsD,gBAAgB,CAACG,IAAI,CAAC;UACpB5D,MAAM,EAAE6D,OAAO;UACfnH,OAAO,EAAEjB,OAAO,CAACiB,OAAO;UACxBG,KAAK,EAAEwB;QACT,CAAC,CAAC;MACJ;MAEA,CAACqF,QAAQ,CAAC3H,OAAO,CAACa,SAAS,IAAI,EAAE,EAAEkH,OAAO,CAAC,CAACjB,CAAC,EAAEhG,KAAK,KAAK;QACvD,MAAMkH,WAAW,GAAGnH,SAAS,CAACmF,KAAK,EAAElF,KAAK,CAAC;QAC3C,IAAI8G,kBAAkB,CAACI,WAAW,EAAE5F,OAAO,EAAE1C,OAAO,EAAEsG,KAAK,CAAC5B,QAAQ,CAAC,EAAE;UACrEsD,gBAAgB,CAACG,IAAI,CAAC;YACpB5D,MAAM,EAAE+D,WAAW;YACnBlH,KAAK;YACLH,OAAO,EAAE2B;UACX,CAAC,CAAC;UAEF,MAAM2F,eAAe,GAAGjH,YAAY,CAACgF,KAAK,EAAElF,KAAK,EAAEpB,OAAO,CAACiB,OAAO,CAAC;UACnE,IACEsH,eAAe,IACfL,kBAAkB,CAChBK,eAAe,EACf7F,OAAO,EACP1C,OAAO,EACPsG,KAAK,CAAC5B,QACR,CAAC,EACD;YACAsD,gBAAgB,CAACG,IAAI,CAAC;cACpB5D,MAAM,EAAEgE,eAAe;cACvBnH,KAAK;cACLH,OAAO,EAAEjB,OAAO,CAACiB;YACnB,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;IAKA,IACE+G,gBAAgB,CAACjC,IAAI,CACnB,CAAC;MACCxB,MAAM,EAAE;QACNjE,OAAO,EAAE;UAAEgE,MAAM;UAAEkE;QAAK;MAC1B;IACF,CAAC,KAAK7D,YAAY,CAAC3E,OAAO,EAAEsE,MAAM,EAAEkE,IAAI,EAAE9F,OAAO,CACnD,CAAC,EACD;MACA,OAAO,IAAI;IACb;IAEA,MAAMzC,KAAK,GAAGsD,UAAU,CAAC,CAAC;IAC1B,MAAMkF,MAAM,GAAGjH,YAAY,CAAC8E,KAAK,EAAEtG,OAAO,EAAEwG,UAAU,CAAC;IAEvD,KAAK,MAAM;MAAEjC,MAAM;MAAEnD,KAAK;MAAEH;IAAQ,CAAC,IAAI+G,gBAAgB,EAAE;MACzD,IACE,EAAE,OAAOU,iBAAiB,CACxBzI,KAAK,EACLsE,MAAM,CAACjE,OAAO,EACdoC,OAAO,EACP1C,OAAO,EACPU,KAAK,EACL8F,UACF,CAAC,CAAC,EACF;QACA,OAAO,IAAI;MACb;MAEAiC,MAAM,CAAClE,MAAM,EAAEnD,KAAK,EAAEH,OAAO,CAAC;MAC9B,OAAO0H,cAAc,CAAC1I,KAAK,EAAEsE,MAAM,CAAC;IACtC;IACA,OAAOtE,KAAK;EACd,CAAC;AACH;AAEA,UAAUyI,iBAAiBA,CACzBzI,KAAkB,EAClBkC,IAAsB,EACtBO,OAAe,EACf1C,OAAsB,EACtBU,KAAsB,EACtB8F,UAA0B,EACR;EAClB,IAAIrE,IAAI,CAACyG,OAAO,KAAKhG,SAAS,EAAE,OAAO,IAAI;EAE3C,MAAMuD,IAAI,GAAG,OAAO,IAAAnD,iBAAU,EAC5Bb,IAAI,CAACyG,OAAO,EACZlG,OAAO,EACP1C,OAAO,CAACiB,OAAO,EACfjB,OAAO,CAACiD,MACV,CAAC;EAED,IAAIvC,KAAK,CAACmI,GAAG,CAAC1C,IAAI,CAAC,EAAE;IACnB,MAAM,IAAIwB,KAAK,CACZ,wCAAuCxB,IAAI,CAACzB,QAAS,KAAI,GACvD,mDAAkD,GACnDe,KAAK,CAACqD,IAAI,CAACpI,KAAK,EAAEyF,IAAI,IAAK,MAAKA,IAAI,CAACzB,QAAS,EAAC,CAAC,CAACS,IAAI,CAAC,IAAI,CAC9D,CAAC;EACH;EAEAzE,KAAK,CAAC+D,GAAG,CAAC0B,IAAI,CAAC;EACf,MAAMlC,SAAS,GAAG,OAAOL,aAAa,CACpCyC,kBAAkB,CAACF,IAAI,CAAC,EACxBnG,OAAO,EACPU,KAAK,EACL8F,UACF,CAAC;EACD9F,KAAK,CAACqI,MAAM,CAAC5C,IAAI,CAAC;EAElB,IAAI,CAAClC,SAAS,EAAE,OAAO,KAAK;EAE5BJ,UAAU,CAAC5D,KAAK,EAAEgE,SAAS,CAAC;EAE5B,OAAO,IAAI;AACb;AAEA,SAASJ,UAAUA,CAACmF,MAAmB,EAAEC,MAAmB,EAAe;EACzED,MAAM,CAAC1I,OAAO,CAAC6H,IAAI,CAAC,GAAGc,MAAM,CAAC3I,OAAO,CAAC;EACtC0I,MAAM,CAAC7I,OAAO,CAACgI,IAAI,CAAC,GAAGc,MAAM,CAAC9I,OAAO,CAAC;EACtC6I,MAAM,CAAC3I,OAAO,CAAC8H,IAAI,CAAC,GAAGc,MAAM,CAAC5I,OAAO,CAAC;EACtC,KAAK,MAAM8F,IAAI,IAAI8C,MAAM,CAACvI,KAAK,EAAE;IAC/BsI,MAAM,CAACtI,KAAK,CAAC+D,GAAG,CAAC0B,IAAI,CAAC;EACxB;EAEA,OAAO6C,MAAM;AACf;AAEA,UAAUL,cAAcA,CACtBK,MAAmB,EACnB;EAAE1I,OAAO;EAAEH,OAAO;EAAEE;AAA+B,CAAC,EAC9B;EACtB2I,MAAM,CAAC1I,OAAO,CAAC6H,IAAI,CAAC7H,OAAO,CAAC;EAC5B0I,MAAM,CAAC7I,OAAO,CAACgI,IAAI,CAAC,IAAI,OAAOhI,OAAO,CAAC,CAAC,CAAC,CAAC;EAC1C6I,MAAM,CAAC3I,OAAO,CAAC8H,IAAI,CAAC,IAAI,OAAO9H,OAAO,CAAC,CAAC,CAAC,CAAC;EAE1C,OAAO2I,MAAM;AACf;AAEA,SAASzF,UAAUA,CAAA,EAAgB;EACjC,OAAO;IACLjD,OAAO,EAAE,EAAE;IACXD,OAAO,EAAE,EAAE;IACXF,OAAO,EAAE,EAAE;IACXO,KAAK,EAAE,IAAIC,GAAG,CAAC;EACjB,CAAC;AACH;AAEA,SAASF,gBAAgBA,CAAC0B,IAAsB,EAAoB;EAClE,MAAM7B,OAAO,GAAA4I,MAAA,CAAAC,MAAA,KACRhH,IAAI,CACR;EACD,OAAO7B,OAAO,CAACsI,OAAO;EACtB,OAAOtI,OAAO,CAACU,GAAG;EAClB,OAAOV,OAAO,CAACa,SAAS;EACxB,OAAOb,OAAO,CAACH,OAAO;EACtB,OAAOG,OAAO,CAACD,OAAO;EACtB,OAAOC,OAAO,CAAC8I,aAAa;EAC5B,OAAO9I,OAAO,CAACgE,MAAM;EACrB,OAAOhE,OAAO,CAACkI,IAAI;EACnB,OAAOlI,OAAO,CAAC+I,IAAI;EACnB,OAAO/I,OAAO,CAACgJ,OAAO;EACtB,OAAOhJ,OAAO,CAACiJ,OAAO;EAItB,IAAIL,MAAM,CAACM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACpJ,OAAO,EAAE,WAAW,CAAC,EAAE;IAC9DA,OAAO,CAACqJ,UAAU,GAAGrJ,OAAO,CAACsJ,SAAS;IACtC,OAAOtJ,OAAO,CAACsJ,SAAS;EAC1B;EACA,OAAOtJ,OAAO;AAChB;AAEA,SAASF,gBAAgBA,CACvByJ,KAAqC,EACL;EAChC,MAAMtJ,GAGL,GAAG,IAAIuJ,GAAG,CAAC,CAAC;EAEb,MAAM3C,WAAW,GAAG,EAAE;EAEtB,KAAK,MAAM4C,IAAI,IAAIF,KAAK,EAAE;IACxB,IAAI,OAAOE,IAAI,CAACC,KAAK,KAAK,UAAU,EAAE;MACpC,MAAMC,KAAK,GAAGF,IAAI,CAACC,KAAK;MACxB,IAAIE,OAAO,GAAG3J,GAAG,CAAC4J,GAAG,CAACF,KAAK,CAAC;MAC5B,IAAI,CAACC,OAAO,EAAE;QACZA,OAAO,GAAG,IAAIJ,GAAG,CAAC,CAAC;QACnBvJ,GAAG,CAAC6J,GAAG,CAACH,KAAK,EAAEC,OAAO,CAAC;MACzB;MACA,IAAIG,IAAI,GAAGH,OAAO,CAACC,GAAG,CAACJ,IAAI,CAACvC,IAAI,CAAC;MACjC,IAAI,CAAC6C,IAAI,EAAE;QACTA,IAAI,GAAG;UAAEL,KAAK,EAAED;QAAK,CAAC;QACtB5C,WAAW,CAACgB,IAAI,CAACkC,IAAI,CAAC;QAItB,IAAI,CAACN,IAAI,CAACO,OAAO,EAAEJ,OAAO,CAACE,GAAG,CAACL,IAAI,CAACvC,IAAI,EAAE6C,IAAI,CAAC;MACjD,CAAC,MAAM;QACLA,IAAI,CAACL,KAAK,GAAGD,IAAI;MACnB;IACF,CAAC,MAAM;MACL5C,WAAW,CAACgB,IAAI,CAAC;QAAE6B,KAAK,EAAED;MAAK,CAAC,CAAC;IACnC;EACF;EAEA,OAAO5C,WAAW,CAACoD,MAAM,CAAC,CAACC,GAAG,EAAEH,IAAI,KAAK;IACvCG,GAAG,CAACrC,IAAI,CAACkC,IAAI,CAACL,KAAK,CAAC;IACpB,OAAOQ,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC;AACR;AAEA,SAAStC,kBAAkBA,CACzB;EAAE5H;AAA+B,CAAC,EAClCoC,OAAe,EACf1C,OAAsB,EACtByK,UAAkB,EACT;EACT,OACE,CAACnK,OAAO,CAAC+I,IAAI,KAAKzG,SAAS,IACzB8H,uBAAuB,CAAC1K,OAAO,EAAEM,OAAO,CAAC+I,IAAI,EAAE3G,OAAO,EAAE+H,UAAU,CAAC,MACpEnK,OAAO,CAACgJ,OAAO,KAAK1G,SAAS,IAC5B8H,uBAAuB,CAAC1K,OAAO,EAAEM,OAAO,CAACgJ,OAAO,EAAE5G,OAAO,EAAE+H,UAAU,CAAC,CAAC,KACxEnK,OAAO,CAACiJ,OAAO,KAAK3G,SAAS,IAC5B,CAAC8H,uBAAuB,CAAC1K,OAAO,EAAEM,OAAO,CAACiJ,OAAO,EAAE7G,OAAO,EAAE+H,UAAU,CAAC,CAAC;AAE9E;AAEA,SAASC,uBAAuBA,CAC9B1K,OAAsB,EACtBqJ,IAA0B,EAC1B3G,OAAe,EACf+H,UAAkB,EACT;EACT,MAAME,QAAQ,GAAGlF,KAAK,CAACC,OAAO,CAAC2D,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;EAEpD,OAAOuB,eAAe,CAAC5K,OAAO,EAAE2K,QAAQ,EAAEjI,OAAO,EAAE+H,UAAU,CAAC;AAChE;AAKA,SAASI,kBAAkBA,CACzBC,IAAY,EACZd,KAA8B,EACI;EAClC,IAAIA,KAAK,YAAYe,MAAM,EAAE;IAC3B,OAAOC,MAAM,CAAChB,KAAK,CAAC;EACtB;EAEA,OAAOA,KAAK;AACd;AAKA,SAASrF,YAAYA,CACnB3E,OAAsB,EACtBsE,MAAqC,EACrCkE,IAAmC,EACnC9F,OAAe,EACN;EACT,IAAI4B,MAAM,IAAIsG,eAAe,CAAC5K,OAAO,EAAEsE,MAAM,EAAE5B,OAAO,CAAC,EAAE;IAAA,IAAAuI,iBAAA;IACvD,MAAMC,OAAO,GAAI,4BAAyB,CAAAD,iBAAA,GACxCjL,OAAO,CAACkE,QAAQ,YAAA+G,iBAAA,GAAI,WACrB,yCAAwCE,IAAI,CAACC,SAAS,CACrD9G,MAAM,EACNuG,kBACF,CAAE,YAAWnI,OAAQ,GAAE;IACvB9C,KAAK,CAACsL,OAAO,CAAC;IACd,IAAIlL,OAAO,CAAC8E,UAAU,EAAE;MACtBC,OAAO,CAACC,GAAG,CAACkG,OAAO,CAAC;IACtB;IACA,OAAO,IAAI;EACb;EAEA,IAAI1C,IAAI,IAAI,CAACoC,eAAe,CAAC5K,OAAO,EAAEwI,IAAI,EAAE9F,OAAO,CAAC,EAAE;IAAA,IAAA2I,kBAAA;IACpD,MAAMH,OAAO,GAAI,4BAAyB,CAAAG,kBAAA,GACxCrL,OAAO,CAACkE,QAAQ,YAAAmH,kBAAA,GAAI,WACrB,8CAA6CF,IAAI,CAACC,SAAS,CAC1D5C,IAAI,EACJqC,kBACF,CAAE,YAAWnI,OAAQ,GAAE;IACvB9C,KAAK,CAACsL,OAAO,CAAC;IACd,IAAIlL,OAAO,CAAC8E,UAAU,EAAE;MACtBC,OAAO,CAACC,GAAG,CAACkG,OAAO,CAAC;IACtB;IACA,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd;AAMA,SAASN,eAAeA,CACtB5K,OAAsB,EACtB2K,QAAoB,EACpBjI,OAAe,EACf+H,UAAmB,EACV;EACT,OAAOE,QAAQ,CAAC5E,IAAI,CAACuF,OAAO,IAC1BpF,YAAY,CAACoF,OAAO,EAAE5I,OAAO,EAAE1C,OAAO,CAACkE,QAAQ,EAAElE,OAAO,EAAEyK,UAAU,CACtE,CAAC;AACH;AAEA,SAASvE,YAAYA,CACnBoF,OAAmB,EACnB5I,OAAe,EACf6I,UAA8B,EAC9BvL,OAAsB,EACtByK,UAAmB,EACV;EACT,IAAI,OAAOa,OAAO,KAAK,UAAU,EAAE;IACjC,OAAO,CAAC,CAAC,IAAAE,qCAAkB,EAACF,OAAO,CAAC,CAACC,UAAU,EAAE;MAC/C7I,OAAO;MACPzB,OAAO,EAAEjB,OAAO,CAACiB,OAAO;MACxBgC,MAAM,EAAEjD,OAAO,CAACiD;IAClB,CAAC,CAAC;EACJ;EAEA,IAAI,OAAOsI,UAAU,KAAK,QAAQ,EAAE;IAClC,MAAM,IAAIE,oBAAW,CAClB,mFAAkF,EACnFhB,UACF,CAAC;EACH;EAEA,IAAI,OAAOa,OAAO,KAAK,QAAQ,EAAE;IAC/BA,OAAO,GAAG,IAAAtF,uBAAkB,EAACsF,OAAO,EAAE5I,OAAO,CAAC;EAChD;EACA,OAAO4I,OAAO,CAACjC,IAAI,CAACkC,UAAU,CAAC;AACjC;AAAC"}