import create from 'zustand';
import createContext from 'zustand/context';
import produce from 'immer';
import { devtools } from 'zustand/middleware';
// import {  } from "zustand/middleware"

export const {
  Provider: FilterProvider,
  useStore: useFilterStore,
  useStoreApi: useFilterStoreApi,
} = createContext();
export const createFilterStore =
  ({ avilableFilters = [], value = '', avilableSortItems = [] }) =>
  () =>
    create(
      devtools((set, get) => ({
        appliedFilters: {},
        avilableFilters,
        avilableSortItems,
        appliedSort: '',
        clearAppliedFilters: () => {
          set({ appliedFilters: {} });
        },
        getFilterParams: () => {},
        setAppliedSort: (sort) => {
          set({ appliedSort: sort });
        },
        setAppliedFilter: (id, filter) => {
          set(
            produce((draft) => {
              console.log(id, filter, draft);
              draft.appliedFilters[id] = {
                ...draft.appliedFilters[id],
                ...filter,
              };
            }),
          );
        },
      })),
    );
