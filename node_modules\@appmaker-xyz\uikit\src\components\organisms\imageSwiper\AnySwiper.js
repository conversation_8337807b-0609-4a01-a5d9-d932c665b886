import React, { useRef, useEffect, useState } from 'react';
import { StyleSheet, Dimensions } from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import { emitEvent, analytics } from '@appmaker-xyz/core';
import {
  Layout,
  AppImage,
  AppTouchable,
  AppmakerText,
} from '../../../components';
import { Swiper } from '@appmaker-xyz/uikit/Views';
import { testProps } from '@appmaker-xyz/core';
import { useOrientation } from '@appmaker-xyz/react-native';

let deviceRatio =
  Dimensions.get('window').height / Dimensions.get('window').width;
const isTab = deviceRatio < 1.6;

const ImageSwiper = ({ clientId, attributes, onPress, onAction }) => {
  const {
    imageList,
    autoplay,
    resizeMode = 'contain',
    title,
    showsButtons,
    hidePagination = false,
    customImageHeight = false,
    activeDotColor,
    __appmaker_analytics_parent_id,
    __appmaker_analytics_parent_name,
  } = attributes;
  const [swiperHeight, setSwipeHeight] = useState(200);
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  const swiperRef = useRef();
  const { screenWidth: width } = useOrientation();
  const activeDotStyles = [styles.activeDotStyle];
  if (activeDotColor) {
    activeDotStyles.push({ backgroundColor: activeDotColor });
  }
  useEffect(() => {
    if (swiperRef.current) {
      swiperRef.current.scrollTo(attributes.index, true);
    }
  }, [attributes.index]);

  const ImageItem = ({ item }) => {
    let imageHeight;
    let defaultHeight = isTab ? 300 : 200;
    try {
      imageHeight = customImageHeight
        ? parseInt(customImageHeight, 0)
        : defaultHeight;
    } catch (error) {}
    if (item.dimensions && item.dimensions.width && item.dimensions.height) {
      const newHeight =
        width / (item.dimensions.width / item.dimensions.height);
      imageHeight = newHeight;
    }
    useEffect(() => {
      setSwipeHeight(imageHeight);
    }, [imageHeight]);
    const uri = typeof item === 'string' ? item : item.uri;
    const appmakerAction = typeof item === 'object' ? item.appmakerAction : {};
    let onPressHandle = onPress;
    if (appmakerAction && onAction) {
      onPressHandle = () => {
        analytics.track(
          'appmaker_block_click',
          {
            blockType: 'appmaker_banner',
            blockId: clientId,
            blockLabel: item?.attributes?.__appmaker_analytics?.blockLabel,
            ...(item.id && {
              resourceId: item?.attributes?.__appmaker_analytics?.resourceId,
            }),
            ...(__appmaker_analytics_parent_id && {
              parentId: __appmaker_analytics_parent_id,
            }),
            ...(__appmaker_analytics_parent_name && {
              parentName: __appmaker_analytics_parent_name,
            }),
            resourceUrl: uri,
            resourceName: item?.attributes?.__appmaker_analytics?.resourceName,
          },
          {
            blockAttributes: attributes,
            blockClientId: clientId,
            blockAction: appmakerAction,
            __appmaker_analytics_parent_id,
            __appmaker_analytics_parent_name,
            resourceName: attributes?.__appmaker_analytics?.resourceName,
          },
        );
        emitEvent('bannerClick', {
          action: appmakerAction,
          bannerName: appmakerAction?.title,
        });
        onAction(appmakerAction);
      };
    }
    return (
      <AppTouchable
        style={[styles.imageWrapper, { height: imageHeight }]}
        onPress={onPressHandle}>
        <AppImage
          uri={uri}
          style={styles.productImage}
          resizeMode={resizeMode}
          fastImage={true}
        />
        {title && (
          <Layout style={styles.contentContainer}>
            <AppmakerText
              category="h1Heading"
              status="white"
              style={styles.title}
              numberOfLines={3}>
              {item.title}
            </AppmakerText>
            <AppmakerText status="light" category="highlighter1">
              {item.authorName} | {item.timeStamp}
            </AppmakerText>
          </Layout>
        )}
      </AppTouchable>
    );
  };

  return imageList.length == 1 ? (
    <ImageItem item={imageList[0]} />
  ) : (
    <Layout
      style={[styles.productImageContainer, { height: swiperHeight }]}
      {...testProps(clientId)}>
      <Swiper
        showsPagination={hidePagination ? false : true}
        ref={swiperRef}
        autoplay={autoplay}
        activeDotStyle={activeDotStyles}
        dotStyle={styles.dotStyle}
        showsButtons={showsButtons}
        removeClippedSubviews={false}
        // index={attributes.index}
        paginationStyle={styles.dotContainer}>
        {imageList.map((item, index) => {
          return <ImageItem key={index} item={item} />;
        })}
      </Swiper>
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    productImageContainer: {
      // flexShrink: 1,
      // flex: 1,
      height: 392.72727272727275,
      // width: '100%',
      // position: 'relative',
    },
    imageWrapper: {
      width: '100%',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: color.white,
      position: 'relative',
    },
    productImage: {
      width: '100%',
      height: '100%',
    },
    activeDotStyle: {
      backgroundColor: color.primary,
      width: spacing.base,
      height: spacing.mini,
      borderRadius: spacing.nano,
      margin: spacing.nano / 2,
    },
    dotStyle: {
      backgroundColor: color.grey,
      width: spacing.mini,
      height: spacing.mini,
      borderRadius: spacing.nano,
      margin: spacing.nano / 2,
    },
    dotContainer: {
      justifyContent: 'flex-start',
      paddingLeft: spacing.base,
      bottom: spacing.md,
      position: 'absolute',
    },
    contentContainer: {
      position: 'absolute',
      backgroundColor: `${color.dark}50`,
      width: '100%',
      height: '100%',
      justifyContent: 'flex-end',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.lg * 2,
    },
    title: {
      textAlign: 'left',
      textShadowColor: `${color.demiDark}50`,
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
  });

export default ImageSwiper;
