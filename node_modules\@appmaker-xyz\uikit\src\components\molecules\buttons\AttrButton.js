import React from 'react';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { useApThemeState } from '../../../theme/ThemeContext';
import Button from './index';
import { usePageState } from '@appmaker-xyz/core';
import { AppmakerText } from '@appmaker-xyz/uikit/src/components/index';

const AttrButton = ({ attributes, onAction, clientId }) => {
  const { color, font } = useApThemeState();
  const [loading, setLoading] = React.useState(false);
  // const inputValue = usePageState((state) => {
  //   if (loading !== state.loadingButton) {
  //     setLoading(state.loadingButton);
  //   }
  // });
  return (
    <Button
      testId={attributes?.testId}
      block={attributes.block}
      clientId={clientId}
      small={attributes.small}
      baseSize={attributes.baseSize}
      wholeContainerStyle={attributes.wholeContainerStyle}
      fontColor={attributes.fontColor}
      loading={loading}
      onPress={async () => {
        setLoading(true);
        attributes.appmakerAction &&
          onAction &&
          (await onAction(attributes.appmakerAction));
        setLoading(false);
      }}
      link={attributes.link}
      outline={attributes.outline}
      accessoryRight={() => (
        <Icon
          name={attributes.iconName}
          size={attributes.iconSize ? attributes.iconSize : font.size.lg}
          color={attributes.iconColor ? attributes.iconColor : color.white}
        />
      )}
      status={attributes.status}>
      {attributes.content}
    </Button>
  );
};

export default AttrButton;
