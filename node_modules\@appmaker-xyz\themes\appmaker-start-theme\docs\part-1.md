## Configure Styles

The first thing we need to do is to configure the styles for our theme. We will do this by creating a `styles` folder in the root of our theme and creating a `styles.js` file inside of it. This file will contain all of the styles for our theme.

Path:

`appmaker-start-theme/styles/index.js`

sample code

```js
const fontFamilyConfig = {
  regular: 'Lora-Regular',
  medium: 'Lora-Medium',
  bold: 'Lora-Bold',
};

const sizeConfig = {
  xs: 8,
  sm: 12,
  base: 14,
  md: 16,
  lg: 18,
  xl: 24,
  '2xl': 28,
  '3xl': 32,
  '4xl': 36,
};

export const styles = {
  fontFamily: fontFamilyConfig,
  size: sizeConfig,
};
```

## Create Atoms

Basic components that can be used to build more complex components. These components are the building blocks of our theme.

### `ThemeText`

All Text must used from ThemeText component. It is a wrapper of Text component from React Native. It is used to apply theme to Text component.

Path tree:

`appmaker-start-theme/uikit/components/atoms/ThemeText.js`

Sample code

```js
import React from 'react';
import { Text } from 'react-native';
import { styles } from '@appmaker-start-theme/styles';

export default function ThemeText({
  children,
  fontFamily = 'regular',
  size = 'base',
  style,
  color,
  ...props
}) {
  const newStyles = {
    fontFamily: fontFamilyConfig[fontFamily],
    fontSize: sizeConfig[size],
    color: color,
    ...style,
  };
  return (
    <Text style={newStyles} {...props}>
      {children}
    </Text>
  );
}
```
