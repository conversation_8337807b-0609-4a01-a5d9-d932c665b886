module.exports={A:{A:{"2":"J D E F A B DC"},B:{"2":"C K L G M N O","1026":"P Q R S T U V W X Y Z a b c d e i j k l m n o p q r s t u v f H"},C:{"2":"EC uB I w J D E F A B C K L G M N O x g y FC GC","322":"0 1 2 3 4 5 6 7 8 9 z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB vB aB wB bB cB dB eB fB gB hB iB jB kB lB h mB nB oB pB qB P Q R xB S T U V W X Y Z a b c d e i j k l m n o p q r s t u v f H yB zB"},D:{"2":"0 1 I w J D E F A B C K L G M N O x g y z","164":"2 3 4 5 6 7 8 9 AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB vB aB wB bB cB dB eB fB gB hB iB jB kB lB h mB nB oB pB qB P Q R S T U V W X Y Z a b c d e i j k l m n o p q r s t u v f H yB zB HC"},E:{"2":"I w J D E F A B C K L IC 0B JC KC LC MC 1B rB sB 2B","2084":"G NC OC 3B 4B 5B 6B tB 7B 8B 9B AC PC"},F:{"2":"0 1 2 3 F B C G M N O x g y z QC RC SC TC rB BC UC sB","1026":"4 5 6 7 8 9 AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB h mB nB oB pB qB P Q R xB S T U V W X Y Z a b c d e"},G:{"2":"E 0B VC CC WC XC YC ZC aC bC cC dC eC fC gC hC iC jC kC lC mC","2084":"nC oC 3B 4B 5B 6B tB 7B 8B 9B AC"},H:{"2":"pC"},I:{"2":"uB I H qC rC sC tC CC uC vC"},J:{"2":"D A"},K:{"2":"A B C rB BC sB","164":"h"},L:{"164":"H"},M:{"2":"f"},N:{"2":"A B"},O:{"164":"wC"},P:{"164":"I g xC yC zC 0C 1C 1B 2C 3C 4C 5C 6C tB 7C 8C 9C"},Q:{"164":"2B"},R:{"164":"AD"},S:{"322":"BD CD"}},B:7,C:"Speech Recognition API"};
