import React, { useState } from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  useWindowDimensions,
} from 'react-native';
import { Layout, ThemeText, AppTouchable, AppImage } from '../../atoms';
import { useCartProduct } from '@appmaker-xyz/shopify';
import Icon from 'react-native-vector-icons/Feather';
import { StepperButton } from '../../coreComponents';
import RotatedText from '../components/RotatedText';
import { usePluginStore, appmaker } from '@appmaker-xyz/core';
import { testProps } from '@appmaker-xyz/core';
import { useStyles } from '@appmaker-xyz/react-native';
import { stylesheet } from './stylesheet';
import { getExtensionConfig } from '@appmaker-xyz/core';
function ProductImage(props) {
  const {
    openProduct,
    isFreeGift,
    themeColor,
    imageUri,
    isLineItemFree,
    disableGotoPdp,
  } = props;
  const { styles, theme } = useStyles(stylesheet);
  const imageStyles = {
    height: isFreeGift || isLineItemFree ? 100 : 120,
    width: isFreeGift || isLineItemFree ? 88 : 100,
    marginVertical: isFreeGift || isLineItemFree ? 10 : 12,
    marginLeft: isFreeGift || isLineItemFree ? 0 : 12,
    borderRadius: theme.borderRadii.lg,
  };
  return (
    <AppTouchable
      {...testProps(`${props?.testId}`)}
      disabled={disableGotoPdp}
      onPress={openProduct}
      style={styles.imageContainer}>
      {isFreeGift || isLineItemFree ? (
        <RotatedText
          text={isFreeGift ? 'FREE GIFT' : 'FREE'}
          bgColor={`${themeColor}26`}
          textColor={themeColor}
        />
      ) : null}
      <AppImage uri={imageUri} resizeMode="contain" style={imageStyles} />
    </AppTouchable>
  );
}

function VariationChip({ variationText, variationColor, testId }) {
  const { styles, theme } = useStyles(stylesheet);

  if (variationText) {
    return (
      <Layout style={styles.variantText}>
        {variationColor && (
          <Layout
            style={[styles.colorChip, { backgroundColor: variationColor }]}
          />
        )}
        <ThemeText
          testId={testId}
          fontFamily="medium"
          numberOfLines={2}
          color={theme.colors.lightText}>
          {variationText}
        </ThemeText>
      </Layout>
    );
  }
  return <Layout />;
}

function ProductPrice(props) {
  const { regularPrice, salePrice } = props;
  const { styles, theme } = useStyles(stylesheet);
  return (
    <Layout {...testProps(`${props?.testId}`)} style={styles.priceContainer}>
      {props.onSale ? (
        <ThemeText
          testId={`${props?.testId}-regular`}
          style={styles.regularPrice}
          color={theme.colors.text}
          size="sm">
          {regularPrice}
        </ThemeText>
      ) : null}
      <ThemeText
        testId={`${props?.testId}-sale`}
        size="base"
        color={theme.colors.text}
        fontFamily="medium">
        {salePrice}
      </ThemeText>
    </Layout>
  );
}

function RemoveIcon(props) {
  const { canRemoveItem, onRemoveItem, removeLoading } = props;
  const { styles, theme } = useStyles(stylesheet);
  if (canRemoveItem) {
    return (
      <AppTouchable
        {...testProps(`${props?.testId}`)}
        style={styles.deleteIcon}
        disabled={removeLoading}
        onPress={onRemoveItem}>
        {removeLoading ? (
          <ActivityIndicator size={16} color={theme.colors.lightText} />
        ) : (
          <Icon name="trash-2" size={16} color={theme.colors.lightText} />
        )}
      </AppTouchable>
    );
  }
  return null;
}

function BundleProduct(props) {
  const { imageUri, title, price, count } = props;
  const { styles } = useStyles(stylesheet);
  return (
    <Layout style={styles.bundleProduct}>
      <AppImage uri={imageUri} style={styles.bundleItemImage} />
      <Layout style={styles.bundleContent}>
        <ThemeText size="sm" numberOfLines={1}>
          {count} x {title}
        </ThemeText>
        <ThemeText size="sm" numberOfLines={1} fontFamily="medium">
          {price}
        </ThemeText>
      </Layout>
    </Layout>
  );
}

function BundleProductList(props) {
  const [show, setShow] = useState(false);
  const { imageUri, title, price, count } = props;
  const { styles } = useStyles(stylesheet);
  return (
    <Layout style={styles.productListContainer}>
      <AppTouchable onPress={() => setShow(!show)} style={styles.showButton}>
        <ThemeText size="sm">{show ? 'Hide' : 'Show'} Products</ThemeText>
        <Icon name={show ? 'chevron-up' : 'chevron-down'} size={12} />
      </AppTouchable>
      {show && (
        <Layout>
          <BundleProduct
            title={title}
            imageUri={imageUri}
            price={price}
            count={count}
          />
        </Layout>
      )}
    </Layout>
  );
}

function SpecialTextBadge(props) {
  const SpecialText = appmaker.applyFilters(
    'appmaker-cart-product-card-review-special-text',
    () => null,
  );
  return <SpecialText {...props} />;
}

const CartCard = (props) => {
  const {
    title,
    imageUri,
    variationText,
    isFreeGift,
    onSale,
    quantity,
    onRemoveItem,
    removeLoading,
    quantityLoading,
    itemSavingsWithCurrency,
    lineItemRegularPriceWithCurrency,
    openProduct,
    canRemoveItem,
    canUpdateQuantity,
    isLineItemFree,
    lineItemSubTotalPriceWithCurrency,
    increaseQuantity,
    decreaseQuantity,
    hasSavings,
    variantLineItemRegularPriceWithCurrency,
    hasDiscount,
    isMaxQuantityReached,
    maxQuantity,
    isAppmakerFreeGift,
  } = useCartProduct(props);
  const { width } = useWindowDimensions();
  const { styles, theme } = useStyles(stylesheet);

  const themeColor = theme.colors.accent;
  const { attributes } = props;
  const customField = attributes?.blockItem?.node?.customAttributes || [];
  const customField_Attributes = getExtensionConfig('shopify', 'show_custom_attributes_in_cart', false);
  const disableGotoPdp = getExtensionConfig(
    'appmaker-free-gift',
    'disable_go_to_pdp',
    false,
  );
  const isProductClickDisabled =
    (isFreeGift || isAppmakerFreeGift) && disableGotoPdp;
  const lineItemTestId = props?.clientId;
  return (
    <Layout
      {...testProps(lineItemTestId)}
      style={[styles.container, { width: width }]}>
      <ProductImage
        testId={`${lineItemTestId}-image`}
        imageUri={imageUri}
        isFreeGift={isFreeGift}
        openProduct={openProduct}
        isLineItemFree={isLineItemFree}
        themeColor={themeColor}
        disableGotoPdp={isProductClickDisabled}
      />
      <Layout style={styles.contentContainer}>
        <Layout style={styles.titleContainer}>
          <ThemeText
            testId={`${lineItemTestId}-title`}
            size="md"
            fontFamily="medium"
            style={styles.textShrink}
            numberOfLines={2}>
            {title}
          </ThemeText>
          <RemoveIcon
            testId={`${lineItemTestId}-remove`}
            canRemoveItem={canRemoveItem}
            onRemoveItem={onRemoveItem}
            removeLoading={removeLoading}
          />
        </Layout>
        <SpecialTextBadge {...props} />
        <Layout style={styles.rowBetweenContainer}>
          <VariationChip
            testId={`${lineItemTestId}-variation`}
            variationText={variationText}
          />
          {/* <BundleProductList
              title={title}
              imageUri={imageUri}
              price={lineItemSubTotalPriceWithCurrency}
              count={1}
            /> */}
          <ProductPrice
            testId={`${lineItemTestId}-price`}
            onSale={onSale || hasDiscount}
            regularPrice={
              lineItemRegularPriceWithCurrency ||
              variantLineItemRegularPriceWithCurrency
            }
            salePrice={lineItemSubTotalPriceWithCurrency}
          />
        </Layout>
        {/* //here */}
        {customField_Attributes?(
        <Layout>
          {customField
            .filter((field) => !field.key.startsWith('_'))
            .map((field, index) => (
              <Layout key={index} style={{ flexDirection: 'row' }}>
                <ThemeText size="sm">{`${field.key}: ${field.value}`}</ThemeText>
              </Layout>
            ))}
        </Layout>):null
        }
        <Layout style={styles.rowBetweenContainer}>
          {canUpdateQuantity ? (
            <StepperButton
              testId={lineItemTestId}
              quantity={quantity}
              max={maxQuantity}
              maxReached={isMaxQuantityReached}
              increaseQuantity={increaseQuantity}
              decreaseQuantity={decreaseQuantity}
              isLoading={quantityLoading}
              containerStyle={styles.stepperContainer}
              textColor={theme.colors.text}
            />
          ) : (
            <Layout />
          )}
          {hasSavings ? (
            <ThemeText
              testId={`${lineItemTestId}-price-saved`}
              size="sm"
              color={themeColor}>
              {itemSavingsWithCurrency} Saved
            </ThemeText>
          ) : null}
        </Layout>
      </Layout>
    </Layout>
  );
};

export default CartCard;
