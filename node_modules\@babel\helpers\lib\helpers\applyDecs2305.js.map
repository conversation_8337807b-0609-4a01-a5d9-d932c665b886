{"version": 3, "names": ["_checkInRHS", "require", "createAddInitializerMethod", "initializers", "decoratorFinishedRef", "addInitializer", "initializer", "assertNotFinished", "assertCallable", "push", "assertInstanceIfPrivate", "has", "target", "TypeError", "memberDec", "dec", "thisArg", "name", "desc", "kind", "isStatic", "isPrivate", "value", "hasPrivateBrand", "kindStr", "ctx", "static", "private", "v", "get", "set", "t", "call", "bind", "access", "fnName", "Error", "fn", "hint", "assertValidReturnValue", "type", "undefined", "init", "curryThis1", "curryThis2", "applyMemberDec", "ret", "base", "decInfo", "decoratorsHaveThis", "decs", "Array", "isArray", "Object", "getOwnPropertyDescriptor", "newValue", "inc", "i", "length", "newInit", "instance", "ownInitializers", "originalInitializer", "args", "defineProperty", "applyMemberDecs", "Class", "decInfos", "instanceBrand", "protoInitializers", "staticInitializers", "staticBrand", "existingProtoNonFields", "Map", "existingStaticNonFields", "_", "checkInRHS", "prototype", "existingNonFields", "existingKind", "pushInitializers", "applyClassDecs", "targetClass", "classDecs", "newClass", "nextNewClass", "applyDecs2305", "memberDecs", "classDecsHaveThis", "e", "c"], "sources": ["../../src/helpers/applyDecs2305.js"], "sourcesContent": ["/* @minVersion 7.21.0 */\n\nimport checkInRHS from \"checkInRHS\";\n\n/**\n  Enums are used in this file, but not assigned to vars to avoid non-hoistable values\n\n  CONSTRUCTOR = 0;\n  PUBLIC = 1;\n  PRIVATE = 2;\n\n  FIELD = 0;\n  ACCESSOR = 1;\n  METHOD = 2;\n  GETTER = 3;\n  SETTER = 4;\n  CLASS = 5; // only used in assertValidReturnValue\n\n  STATIC = 8;\n\n  DECORATORS_HAVE_THIS = 16;\n*/\n\nfunction createAddInitializerMethod(initializers, decoratorFinishedRef) {\n  return function addInitializer(initializer) {\n    assertNotFinished(decoratorFinishedRef, \"addInitializer\");\n    assertCallable(initializer, \"An initializer\");\n    initializers.push(initializer);\n  };\n}\n\nfunction assertInstanceIfPrivate(has, target) {\n  if (!has(target)) {\n    throw new TypeError(\"Attempted to access private element on non-instance\");\n  }\n}\n\nfunction memberDec(\n  dec,\n  thisArg,\n  name,\n  desc,\n  initializers,\n  kind,\n  isStatic,\n  isPrivate,\n  value,\n  hasPrivateBrand\n) {\n  var kindStr;\n\n  switch (kind) {\n    case 1 /* ACCESSOR */:\n      kindStr = \"accessor\";\n      break;\n    case 2 /* METHOD */:\n      kindStr = \"method\";\n      break;\n    case 3 /* GETTER */:\n      kindStr = \"getter\";\n      break;\n    case 4 /* SETTER */:\n      kindStr = \"setter\";\n      break;\n    default:\n      kindStr = \"field\";\n  }\n\n  var ctx = {\n    kind: kindStr,\n    name: isPrivate ? \"#\" + name : name,\n    static: isStatic,\n    private: isPrivate,\n  };\n\n  var decoratorFinishedRef = { v: false };\n\n  if (kind !== 0 /* FIELD */) {\n    ctx.addInitializer = createAddInitializerMethod(\n      initializers,\n      decoratorFinishedRef\n    );\n  }\n\n  var get, set;\n  if (!isPrivate && (kind === 0 /* FIELD */ || kind === 2) /* METHOD */) {\n    get = function (target) {\n      return target[name];\n    };\n    if (kind === 0 /* FIELD */) {\n      set = function (target, v) {\n        target[name] = v;\n      };\n    }\n  } else if (kind === 2 /* METHOD */) {\n    // Assert: isPrivate is true.\n    get = function (target) {\n      assertInstanceIfPrivate(hasPrivateBrand, target);\n      return desc.value;\n    };\n  } else {\n    // Assert: If kind === 0, then isPrivate is true.\n    var t = kind === 0 /* FIELD */ || kind === 1; /* ACCESSOR */\n    if (t || kind === 3 /* GETTER */) {\n      if (isPrivate) {\n        get = function (target) {\n          assertInstanceIfPrivate(hasPrivateBrand, target);\n          return desc.get.call(target);\n        };\n      } else {\n        get = function (target) {\n          return desc.get.call(target);\n        };\n      }\n    }\n    if (t || kind === 4 /* SETTER */) {\n      if (isPrivate) {\n        set = function (target, value) {\n          assertInstanceIfPrivate(hasPrivateBrand, target);\n          desc.set.call(target, value);\n        };\n      } else {\n        set = function (target, value) {\n          desc.set.call(target, value);\n        };\n      }\n    }\n  }\n  var has = isPrivate\n    ? hasPrivateBrand.bind()\n    : function (target) {\n        return name in target;\n      };\n  ctx.access =\n    get && set\n      ? { get: get, set: set, has: has }\n      : get\n      ? { get: get, has: has }\n      : { set: set, has: has };\n\n  try {\n    return dec.call(thisArg, value, ctx);\n  } finally {\n    decoratorFinishedRef.v = true;\n  }\n}\n\nfunction assertNotFinished(decoratorFinishedRef, fnName) {\n  if (decoratorFinishedRef.v) {\n    throw new Error(\n      \"attempted to call \" + fnName + \" after decoration was finished\"\n    );\n  }\n}\n\nfunction assertCallable(fn, hint) {\n  if (typeof fn !== \"function\") {\n    throw new TypeError(hint + \" must be a function\");\n  }\n}\n\nfunction assertValidReturnValue(kind, value) {\n  var type = typeof value;\n\n  if (kind === 1 /* ACCESSOR */) {\n    if (type !== \"object\" || value === null) {\n      throw new TypeError(\n        \"accessor decorators must return an object with get, set, or init properties or void 0\"\n      );\n    }\n    if (value.get !== undefined) {\n      assertCallable(value.get, \"accessor.get\");\n    }\n    if (value.set !== undefined) {\n      assertCallable(value.set, \"accessor.set\");\n    }\n    if (value.init !== undefined) {\n      assertCallable(value.init, \"accessor.init\");\n    }\n  } else if (type !== \"function\") {\n    var hint;\n    if (kind === 0 /* FIELD */) {\n      hint = \"field\";\n    } else if (kind === 5 /* CLASS */) {\n      hint = \"class\";\n    } else {\n      hint = \"method\";\n    }\n    throw new TypeError(hint + \" decorators must return a function or void 0\");\n  }\n}\n\nfunction curryThis1(fn) {\n  return function () {\n    return fn(this);\n  };\n}\nfunction curryThis2(fn) {\n  return function (value) {\n    fn(this, value);\n  };\n}\n\nfunction applyMemberDec(\n  ret,\n  base,\n  decInfo,\n  decoratorsHaveThis,\n  name,\n  kind,\n  isStatic,\n  isPrivate,\n  initializers,\n  hasPrivateBrand\n) {\n  var decs = decInfo[0];\n\n  if (!decoratorsHaveThis && !Array.isArray(decs)) {\n    decs = [decs];\n  }\n\n  var desc, init, value;\n\n  if (isPrivate) {\n    if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n      desc = {\n        get: curryThis1(decInfo[3]),\n        set: curryThis2(decInfo[4]),\n      };\n    } else {\n      if (kind === 3 /* GETTER */) {\n        desc = {\n          get: decInfo[3],\n        };\n      } else if (kind === 4 /* SETTER */) {\n        desc = {\n          set: decInfo[3],\n        };\n      } else {\n        desc = {\n          value: decInfo[3],\n        };\n      }\n    }\n  } else if (kind !== 0 /* FIELD */) {\n    desc = Object.getOwnPropertyDescriptor(base, name);\n  }\n\n  if (kind === 1 /* ACCESSOR */) {\n    value = {\n      get: desc.get,\n      set: desc.set,\n    };\n  } else if (kind === 2 /* METHOD */) {\n    value = desc.value;\n  } else if (kind === 3 /* GETTER */) {\n    value = desc.get;\n  } else if (kind === 4 /* SETTER */) {\n    value = desc.set;\n  }\n\n  var newValue, get, set;\n\n  var inc = decoratorsHaveThis ? 2 : 1;\n\n  for (var i = decs.length - 1; i >= 0; i -= inc) {\n    var dec = decs[i];\n\n    newValue = memberDec(\n      dec,\n      decoratorsHaveThis ? decs[i - 1] : undefined,\n      name,\n      desc,\n      initializers,\n      kind,\n      isStatic,\n      isPrivate,\n      value,\n      hasPrivateBrand\n    );\n\n    if (newValue !== void 0) {\n      assertValidReturnValue(kind, newValue);\n      var newInit;\n\n      if (kind === 0 /* FIELD */) {\n        newInit = newValue;\n      } else if (kind === 1 /* ACCESSOR */) {\n        newInit = newValue.init;\n        get = newValue.get || value.get;\n        set = newValue.set || value.set;\n\n        value = { get: get, set: set };\n      } else {\n        value = newValue;\n      }\n\n      if (newInit !== void 0) {\n        if (init === void 0) {\n          init = newInit;\n        } else if (typeof init === \"function\") {\n          init = [init, newInit];\n        } else {\n          init.push(newInit);\n        }\n      }\n    }\n  }\n\n  if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n    if (init === void 0) {\n      // If the initializer was void 0, sub in a dummy initializer\n      init = function (instance, init) {\n        return init;\n      };\n    } else if (typeof init !== \"function\") {\n      var ownInitializers = init;\n\n      init = function (instance, init) {\n        var value = init;\n\n        for (var i = ownInitializers.length - 1; i >= 0; i--) {\n          value = ownInitializers[i].call(instance, value);\n        }\n\n        return value;\n      };\n    } else {\n      var originalInitializer = init;\n\n      init = function (instance, init) {\n        return originalInitializer.call(instance, init);\n      };\n    }\n\n    ret.push(init);\n  }\n\n  if (kind !== 0 /* FIELD */) {\n    if (kind === 1 /* ACCESSOR */) {\n      desc.get = value.get;\n      desc.set = value.set;\n    } else if (kind === 2 /* METHOD */) {\n      desc.value = value;\n    } else if (kind === 3 /* GETTER */) {\n      desc.get = value;\n    } else if (kind === 4 /* SETTER */) {\n      desc.set = value;\n    }\n\n    if (isPrivate) {\n      if (kind === 1 /* ACCESSOR */) {\n        ret.push(function (instance, args) {\n          return value.get.call(instance, args);\n        });\n        ret.push(function (instance, args) {\n          return value.set.call(instance, args);\n        });\n      } else if (kind === 2 /* METHOD */) {\n        ret.push(value);\n      } else {\n        ret.push(function (instance, args) {\n          return value.call(instance, args);\n        });\n      }\n    } else {\n      Object.defineProperty(base, name, desc);\n    }\n  }\n}\n\nfunction applyMemberDecs(Class, decInfos, instanceBrand) {\n  var ret = [];\n  var protoInitializers;\n  var staticInitializers;\n  var staticBrand;\n\n  var existingProtoNonFields = new Map();\n  var existingStaticNonFields = new Map();\n\n  for (var i = 0; i < decInfos.length; i++) {\n    var decInfo = decInfos[i];\n\n    // skip computed property names\n    if (!Array.isArray(decInfo)) continue;\n\n    var kind = decInfo[1];\n    var name = decInfo[2];\n    var isPrivate = decInfo.length > 3;\n\n    var decoratorsHaveThis = kind & 16; /* DECORATORS_HAVE_THIS */\n    var isStatic = !!(kind & 8); /* STATIC */\n    var base;\n    var initializers;\n    var hasPrivateBrand = instanceBrand;\n\n    kind &= 7 /* 0b111 */;\n\n    if (isStatic) {\n      base = Class;\n      // initialize staticInitializers when we see a non-field static member\n      if (kind !== 0 /* FIELD */) {\n        staticInitializers = staticInitializers || [];\n        initializers = staticInitializers;\n      }\n      if (isPrivate && !staticBrand) {\n        staticBrand = function (_) {\n          return checkInRHS(_) === Class;\n        };\n      }\n      hasPrivateBrand = staticBrand;\n    } else {\n      base = Class.prototype;\n      // initialize protoInitializers when we see a non-field member\n      if (kind !== 0 /* FIELD */) {\n        protoInitializers = protoInitializers || [];\n        initializers = protoInitializers;\n      }\n    }\n\n    if (kind !== 0 /* FIELD */ && !isPrivate) {\n      var existingNonFields = isStatic\n        ? existingStaticNonFields\n        : existingProtoNonFields;\n\n      var existingKind = existingNonFields.get(name) || 0;\n\n      if (\n        existingKind === true ||\n        (existingKind === 3 /* GETTER */ && kind !== 4) /* SETTER */ ||\n        (existingKind === 4 /* SETTER */ && kind !== 3) /* GETTER */\n      ) {\n        throw new Error(\n          \"Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: \" +\n            name\n        );\n      }\n      existingNonFields.set(\n        name,\n        !existingKind && kind > 2 /* METHOD */ ? kind : true\n      );\n    }\n\n    applyMemberDec(\n      ret,\n      base,\n      decInfo,\n      decoratorsHaveThis,\n      name,\n      kind,\n      isStatic,\n      isPrivate,\n      initializers,\n      hasPrivateBrand\n    );\n  }\n\n  pushInitializers(ret, protoInitializers);\n  pushInitializers(ret, staticInitializers);\n  return ret;\n}\n\nfunction pushInitializers(ret, initializers) {\n  if (initializers) {\n    ret.push(function (instance) {\n      for (var i = 0; i < initializers.length; i++) {\n        initializers[i].call(instance);\n      }\n      return instance;\n    });\n  }\n}\n\nfunction applyClassDecs(targetClass, classDecs, decoratorsHaveThis) {\n  if (classDecs.length) {\n    var initializers = [];\n    var newClass = targetClass;\n    var name = targetClass.name;\n\n    var inc = decoratorsHaveThis ? 2 : 1;\n\n    for (var i = classDecs.length - 1; i >= 0; i -= inc) {\n      var decoratorFinishedRef = { v: false };\n\n      try {\n        var nextNewClass = classDecs[i].call(\n          decoratorsHaveThis ? classDecs[i - 1] : undefined,\n          newClass,\n          {\n            kind: \"class\",\n            name: name,\n            addInitializer: createAddInitializerMethod(\n              initializers,\n              decoratorFinishedRef\n            ),\n          }\n        );\n      } finally {\n        decoratorFinishedRef.v = true;\n      }\n\n      if (nextNewClass !== undefined) {\n        assertValidReturnValue(5 /* CLASS */, nextNewClass);\n        newClass = nextNewClass;\n      }\n    }\n\n    return [\n      newClass,\n      function () {\n        for (var i = 0; i < initializers.length; i++) {\n          initializers[i].call(newClass);\n        }\n      },\n    ];\n  }\n  // The transformer will not emit assignment when there are no class decorators,\n  // so we don't have to return an empty array here.\n}\n\n/**\n  Basic usage:\n\n  applyDecs(\n    Class,\n    [\n      // member decorators\n      [\n        decs,               // dec, or array of decs, or array of this values and decs\n        0,                  // kind of value being decorated\n        'prop',             // name of public prop on class containing the value being decorated,\n        '#p',               // the name of the private property (if is private, void 0 otherwise),\n      ]\n    ],\n    [\n      // class decorators\n      dec1, dec2\n    ]\n  )\n  ```\n\n  Fully transpiled example:\n\n  ```js\n  @dec\n  class Class {\n    @dec\n    a = 123;\n\n    @dec\n    #a = 123;\n\n    @dec\n    @dec2\n    accessor b = 123;\n\n    @dec\n    accessor #b = 123;\n\n    @dec\n    c() { console.log('c'); }\n\n    @dec\n    #c() { console.log('privC'); }\n\n    @dec\n    get d() { console.log('d'); }\n\n    @dec\n    get #d() { console.log('privD'); }\n\n    @dec\n    set e(v) { console.log('e'); }\n\n    @dec\n    set #e(v) { console.log('privE'); }\n  }\n\n\n  // becomes\n  let initializeInstance;\n  let initializeClass;\n\n  let initA;\n  let initPrivA;\n\n  let initB;\n  let initPrivB, getPrivB, setPrivB;\n\n  let privC;\n  let privD;\n  let privE;\n\n  let Class;\n  class _Class {\n    static {\n      let ret = applyDecs(\n        this,\n        [\n          [dec, 0, 'a'],\n          [dec, 0, 'a', (i) => i.#a, (i, v) => i.#a = v],\n          [[dec, dec2], 1, 'b'],\n          [dec, 1, 'b', (i) => i.#privBData, (i, v) => i.#privBData = v],\n          [dec, 2, 'c'],\n          [dec, 2, 'c', () => console.log('privC')],\n          [dec, 3, 'd'],\n          [dec, 3, 'd', () => console.log('privD')],\n          [dec, 4, 'e'],\n          [dec, 4, 'e', () => console.log('privE')],\n        ],\n        [\n          dec\n        ]\n      );\n\n      initA = ret[0];\n\n      initPrivA = ret[1];\n\n      initB = ret[2];\n\n      initPrivB = ret[3];\n      getPrivB = ret[4];\n      setPrivB = ret[5];\n\n      privC = ret[6];\n\n      privD = ret[7];\n\n      privE = ret[8];\n\n      initializeInstance = ret[9];\n\n      Class = ret[10]\n\n      initializeClass = ret[11];\n    }\n\n    a = (initializeInstance(this), initA(this, 123));\n\n    #a = initPrivA(this, 123);\n\n    #bData = initB(this, 123);\n    get b() { return this.#bData }\n    set b(v) { this.#bData = v }\n\n    #privBData = initPrivB(this, 123);\n    get #b() { return getPrivB(this); }\n    set #b(v) { setPrivB(this, v); }\n\n    c() { console.log('c'); }\n\n    #c(...args) { return privC(this, ...args) }\n\n    get d() { console.log('d'); }\n\n    get #d() { return privD(this); }\n\n    set e(v) { console.log('e'); }\n\n    set #e(v) { privE(this, v); }\n  }\n\n  initializeClass(Class);\n */\n\nexport default function applyDecs2305(\n  targetClass,\n  memberDecs,\n  classDecs,\n  classDecsHaveThis,\n  instanceBrand\n) {\n  return {\n    e: applyMemberDecs(targetClass, memberDecs, instanceBrand),\n    // Lazily apply class decorations so that member init locals can be properly bound.\n    get c() {\n      return applyClassDecs(targetClass, classDecs, classDecsHaveThis);\n    },\n  };\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,WAAA,GAAAC,OAAA;AAqBA,SAASC,0BAA0BA,CAACC,YAAY,EAAEC,oBAAoB,EAAE;EACtE,OAAO,SAASC,cAAcA,CAACC,WAAW,EAAE;IAC1CC,iBAAiB,CAACH,oBAAoB,EAAE,gBAAgB,CAAC;IACzDI,cAAc,CAACF,WAAW,EAAE,gBAAgB,CAAC;IAC7CH,YAAY,CAACM,IAAI,CAACH,WAAW,CAAC;EAChC,CAAC;AACH;AAEA,SAASI,uBAAuBA,CAACC,GAAG,EAAEC,MAAM,EAAE;EAC5C,IAAI,CAACD,GAAG,CAACC,MAAM,CAAC,EAAE;IAChB,MAAM,IAAIC,SAAS,CAAC,qDAAqD,CAAC;EAC5E;AACF;AAEA,SAASC,SAASA,CAChBC,GAAG,EACHC,OAAO,EACPC,IAAI,EACJC,IAAI,EACJf,YAAY,EACZgB,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,eAAe,EACf;EACA,IAAIC,OAAO;EAEX,QAAQL,IAAI;IACV,KAAK,CAAC;MACJK,OAAO,GAAG,UAAU;MACpB;IACF,KAAK,CAAC;MACJA,OAAO,GAAG,QAAQ;MAClB;IACF,KAAK,CAAC;MACJA,OAAO,GAAG,QAAQ;MAClB;IACF,KAAK,CAAC;MACJA,OAAO,GAAG,QAAQ;MAClB;IACF;MACEA,OAAO,GAAG,OAAO;EACrB;EAEA,IAAIC,GAAG,GAAG;IACRN,IAAI,EAAEK,OAAO;IACbP,IAAI,EAAEI,SAAS,GAAG,GAAG,GAAGJ,IAAI,GAAGA,IAAI;IACnCS,MAAM,EAAEN,QAAQ;IAChBO,OAAO,EAAEN;EACX,CAAC;EAED,IAAIjB,oBAAoB,GAAG;IAAEwB,CAAC,EAAE;EAAM,CAAC;EAEvC,IAAIT,IAAI,KAAK,CAAC,EAAc;IAC1BM,GAAG,CAACpB,cAAc,GAAGH,0BAA0B,CAC7CC,YAAY,EACZC,oBACF,CAAC;EACH;EAEA,IAAIyB,GAAG,EAAEC,GAAG;EACZ,IAAI,CAACT,SAAS,KAAKF,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,CAAC,EAAe;IACrEU,GAAG,GAAG,SAAAA,CAAUjB,MAAM,EAAE;MACtB,OAAOA,MAAM,CAACK,IAAI,CAAC;IACrB,CAAC;IACD,IAAIE,IAAI,KAAK,CAAC,EAAc;MAC1BW,GAAG,GAAG,SAAAA,CAAUlB,MAAM,EAAEgB,CAAC,EAAE;QACzBhB,MAAM,CAACK,IAAI,CAAC,GAAGW,CAAC;MAClB,CAAC;IACH;EACF,CAAC,MAAM,IAAIT,IAAI,KAAK,CAAC,EAAe;IAElCU,GAAG,GAAG,SAAAA,CAAUjB,MAAM,EAAE;MACtBF,uBAAuB,CAACa,eAAe,EAAEX,MAAM,CAAC;MAChD,OAAOM,IAAI,CAACI,KAAK;IACnB,CAAC;EACH,CAAC,MAAM;IAEL,IAAIS,CAAC,GAAGZ,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC;IAC5C,IAAIY,CAAC,IAAIZ,IAAI,KAAK,CAAC,EAAe;MAChC,IAAIE,SAAS,EAAE;QACbQ,GAAG,GAAG,SAAAA,CAAUjB,MAAM,EAAE;UACtBF,uBAAuB,CAACa,eAAe,EAAEX,MAAM,CAAC;UAChD,OAAOM,IAAI,CAACW,GAAG,CAACG,IAAI,CAACpB,MAAM,CAAC;QAC9B,CAAC;MACH,CAAC,MAAM;QACLiB,GAAG,GAAG,SAAAA,CAAUjB,MAAM,EAAE;UACtB,OAAOM,IAAI,CAACW,GAAG,CAACG,IAAI,CAACpB,MAAM,CAAC;QAC9B,CAAC;MACH;IACF;IACA,IAAImB,CAAC,IAAIZ,IAAI,KAAK,CAAC,EAAe;MAChC,IAAIE,SAAS,EAAE;QACbS,GAAG,GAAG,SAAAA,CAAUlB,MAAM,EAAEU,KAAK,EAAE;UAC7BZ,uBAAuB,CAACa,eAAe,EAAEX,MAAM,CAAC;UAChDM,IAAI,CAACY,GAAG,CAACE,IAAI,CAACpB,MAAM,EAAEU,KAAK,CAAC;QAC9B,CAAC;MACH,CAAC,MAAM;QACLQ,GAAG,GAAG,SAAAA,CAAUlB,MAAM,EAAEU,KAAK,EAAE;UAC7BJ,IAAI,CAACY,GAAG,CAACE,IAAI,CAACpB,MAAM,EAAEU,KAAK,CAAC;QAC9B,CAAC;MACH;IACF;EACF;EACA,IAAIX,GAAG,GAAGU,SAAS,GACfE,eAAe,CAACU,IAAI,CAAC,CAAC,GACtB,UAAUrB,MAAM,EAAE;IAChB,OAAOK,IAAI,IAAIL,MAAM;EACvB,CAAC;EACLa,GAAG,CAACS,MAAM,GACRL,GAAG,IAAIC,GAAG,GACN;IAAED,GAAG,EAAEA,GAAG;IAAEC,GAAG,EAAEA,GAAG;IAAEnB,GAAG,EAAEA;EAAI,CAAC,GAChCkB,GAAG,GACH;IAAEA,GAAG,EAAEA,GAAG;IAAElB,GAAG,EAAEA;EAAI,CAAC,GACtB;IAAEmB,GAAG,EAAEA,GAAG;IAAEnB,GAAG,EAAEA;EAAI,CAAC;EAE5B,IAAI;IACF,OAAOI,GAAG,CAACiB,IAAI,CAAChB,OAAO,EAAEM,KAAK,EAAEG,GAAG,CAAC;EACtC,CAAC,SAAS;IACRrB,oBAAoB,CAACwB,CAAC,GAAG,IAAI;EAC/B;AACF;AAEA,SAASrB,iBAAiBA,CAACH,oBAAoB,EAAE+B,MAAM,EAAE;EACvD,IAAI/B,oBAAoB,CAACwB,CAAC,EAAE;IAC1B,MAAM,IAAIQ,KAAK,CACb,oBAAoB,GAAGD,MAAM,GAAG,gCAClC,CAAC;EACH;AACF;AAEA,SAAS3B,cAAcA,CAAC6B,EAAE,EAAEC,IAAI,EAAE;EAChC,IAAI,OAAOD,EAAE,KAAK,UAAU,EAAE;IAC5B,MAAM,IAAIxB,SAAS,CAACyB,IAAI,GAAG,qBAAqB,CAAC;EACnD;AACF;AAEA,SAASC,sBAAsBA,CAACpB,IAAI,EAAEG,KAAK,EAAE;EAC3C,IAAIkB,IAAI,GAAG,OAAOlB,KAAK;EAEvB,IAAIH,IAAI,KAAK,CAAC,EAAiB;IAC7B,IAAIqB,IAAI,KAAK,QAAQ,IAAIlB,KAAK,KAAK,IAAI,EAAE;MACvC,MAAM,IAAIT,SAAS,CACjB,uFACF,CAAC;IACH;IACA,IAAIS,KAAK,CAACO,GAAG,KAAKY,SAAS,EAAE;MAC3BjC,cAAc,CAACc,KAAK,CAACO,GAAG,EAAE,cAAc,CAAC;IAC3C;IACA,IAAIP,KAAK,CAACQ,GAAG,KAAKW,SAAS,EAAE;MAC3BjC,cAAc,CAACc,KAAK,CAACQ,GAAG,EAAE,cAAc,CAAC;IAC3C;IACA,IAAIR,KAAK,CAACoB,IAAI,KAAKD,SAAS,EAAE;MAC5BjC,cAAc,CAACc,KAAK,CAACoB,IAAI,EAAE,eAAe,CAAC;IAC7C;EACF,CAAC,MAAM,IAAIF,IAAI,KAAK,UAAU,EAAE;IAC9B,IAAIF,IAAI;IACR,IAAInB,IAAI,KAAK,CAAC,EAAc;MAC1BmB,IAAI,GAAG,OAAO;IAChB,CAAC,MAAM,IAAInB,IAAI,KAAK,CAAC,EAAc;MACjCmB,IAAI,GAAG,OAAO;IAChB,CAAC,MAAM;MACLA,IAAI,GAAG,QAAQ;IACjB;IACA,MAAM,IAAIzB,SAAS,CAACyB,IAAI,GAAG,8CAA8C,CAAC;EAC5E;AACF;AAEA,SAASK,UAAUA,CAACN,EAAE,EAAE;EACtB,OAAO,YAAY;IACjB,OAAOA,EAAE,CAAC,IAAI,CAAC;EACjB,CAAC;AACH;AACA,SAASO,UAAUA,CAACP,EAAE,EAAE;EACtB,OAAO,UAAUf,KAAK,EAAE;IACtBe,EAAE,CAAC,IAAI,EAAEf,KAAK,CAAC;EACjB,CAAC;AACH;AAEA,SAASuB,cAAcA,CACrBC,GAAG,EACHC,IAAI,EACJC,OAAO,EACPC,kBAAkB,EAClBhC,IAAI,EACJE,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTlB,YAAY,EACZoB,eAAe,EACf;EACA,IAAI2B,IAAI,GAAGF,OAAO,CAAC,CAAC,CAAC;EAErB,IAAI,CAACC,kBAAkB,IAAI,CAACE,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;IAC/CA,IAAI,GAAG,CAACA,IAAI,CAAC;EACf;EAEA,IAAIhC,IAAI,EAAEwB,IAAI,EAAEpB,KAAK;EAErB,IAAID,SAAS,EAAE;IACb,IAAIF,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;MACvDD,IAAI,GAAG;QACLW,GAAG,EAAEc,UAAU,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC;QAC3BlB,GAAG,EAAEc,UAAU,CAACI,OAAO,CAAC,CAAC,CAAC;MAC5B,CAAC;IACH,CAAC,MAAM;MACL,IAAI7B,IAAI,KAAK,CAAC,EAAe;QAC3BD,IAAI,GAAG;UACLW,GAAG,EAAEmB,OAAO,CAAC,CAAC;QAChB,CAAC;MACH,CAAC,MAAM,IAAI7B,IAAI,KAAK,CAAC,EAAe;QAClCD,IAAI,GAAG;UACLY,GAAG,EAAEkB,OAAO,CAAC,CAAC;QAChB,CAAC;MACH,CAAC,MAAM;QACL9B,IAAI,GAAG;UACLI,KAAK,EAAE0B,OAAO,CAAC,CAAC;QAClB,CAAC;MACH;IACF;EACF,CAAC,MAAM,IAAI7B,IAAI,KAAK,CAAC,EAAc;IACjCD,IAAI,GAAGmC,MAAM,CAACC,wBAAwB,CAACP,IAAI,EAAE9B,IAAI,CAAC;EACpD;EAEA,IAAIE,IAAI,KAAK,CAAC,EAAiB;IAC7BG,KAAK,GAAG;MACNO,GAAG,EAAEX,IAAI,CAACW,GAAG;MACbC,GAAG,EAAEZ,IAAI,CAACY;IACZ,CAAC;EACH,CAAC,MAAM,IAAIX,IAAI,KAAK,CAAC,EAAe;IAClCG,KAAK,GAAGJ,IAAI,CAACI,KAAK;EACpB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;IAClCG,KAAK,GAAGJ,IAAI,CAACW,GAAG;EAClB,CAAC,MAAM,IAAIV,IAAI,KAAK,CAAC,EAAe;IAClCG,KAAK,GAAGJ,IAAI,CAACY,GAAG;EAClB;EAEA,IAAIyB,QAAQ,EAAE1B,GAAG,EAAEC,GAAG;EAEtB,IAAI0B,GAAG,GAAGP,kBAAkB,GAAG,CAAC,GAAG,CAAC;EAEpC,KAAK,IAAIQ,CAAC,GAAGP,IAAI,CAACQ,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAID,GAAG,EAAE;IAC9C,IAAIzC,GAAG,GAAGmC,IAAI,CAACO,CAAC,CAAC;IAEjBF,QAAQ,GAAGzC,SAAS,CAClBC,GAAG,EACHkC,kBAAkB,GAAGC,IAAI,CAACO,CAAC,GAAG,CAAC,CAAC,GAAGhB,SAAS,EAC5CxB,IAAI,EACJC,IAAI,EACJf,YAAY,EACZgB,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,eACF,CAAC;IAED,IAAIgC,QAAQ,KAAK,KAAK,CAAC,EAAE;MACvBhB,sBAAsB,CAACpB,IAAI,EAAEoC,QAAQ,CAAC;MACtC,IAAII,OAAO;MAEX,IAAIxC,IAAI,KAAK,CAAC,EAAc;QAC1BwC,OAAO,GAAGJ,QAAQ;MACpB,CAAC,MAAM,IAAIpC,IAAI,KAAK,CAAC,EAAiB;QACpCwC,OAAO,GAAGJ,QAAQ,CAACb,IAAI;QACvBb,GAAG,GAAG0B,QAAQ,CAAC1B,GAAG,IAAIP,KAAK,CAACO,GAAG;QAC/BC,GAAG,GAAGyB,QAAQ,CAACzB,GAAG,IAAIR,KAAK,CAACQ,GAAG;QAE/BR,KAAK,GAAG;UAAEO,GAAG,EAAEA,GAAG;UAAEC,GAAG,EAAEA;QAAI,CAAC;MAChC,CAAC,MAAM;QACLR,KAAK,GAAGiC,QAAQ;MAClB;MAEA,IAAII,OAAO,KAAK,KAAK,CAAC,EAAE;QACtB,IAAIjB,IAAI,KAAK,KAAK,CAAC,EAAE;UACnBA,IAAI,GAAGiB,OAAO;QAChB,CAAC,MAAM,IAAI,OAAOjB,IAAI,KAAK,UAAU,EAAE;UACrCA,IAAI,GAAG,CAACA,IAAI,EAAEiB,OAAO,CAAC;QACxB,CAAC,MAAM;UACLjB,IAAI,CAACjC,IAAI,CAACkD,OAAO,CAAC;QACpB;MACF;IACF;EACF;EAEA,IAAIxC,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;IACvD,IAAIuB,IAAI,KAAK,KAAK,CAAC,EAAE;MAEnBA,IAAI,GAAG,SAAAA,CAAUkB,QAAQ,EAAElB,IAAI,EAAE;QAC/B,OAAOA,IAAI;MACb,CAAC;IACH,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;MACrC,IAAImB,eAAe,GAAGnB,IAAI;MAE1BA,IAAI,GAAG,SAAAA,CAAUkB,QAAQ,EAAElB,IAAI,EAAE;QAC/B,IAAIpB,KAAK,GAAGoB,IAAI;QAEhB,KAAK,IAAIe,CAAC,GAAGI,eAAe,CAACH,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UACpDnC,KAAK,GAAGuC,eAAe,CAACJ,CAAC,CAAC,CAACzB,IAAI,CAAC4B,QAAQ,EAAEtC,KAAK,CAAC;QAClD;QAEA,OAAOA,KAAK;MACd,CAAC;IACH,CAAC,MAAM;MACL,IAAIwC,mBAAmB,GAAGpB,IAAI;MAE9BA,IAAI,GAAG,SAAAA,CAAUkB,QAAQ,EAAElB,IAAI,EAAE;QAC/B,OAAOoB,mBAAmB,CAAC9B,IAAI,CAAC4B,QAAQ,EAAElB,IAAI,CAAC;MACjD,CAAC;IACH;IAEAI,GAAG,CAACrC,IAAI,CAACiC,IAAI,CAAC;EAChB;EAEA,IAAIvB,IAAI,KAAK,CAAC,EAAc;IAC1B,IAAIA,IAAI,KAAK,CAAC,EAAiB;MAC7BD,IAAI,CAACW,GAAG,GAAGP,KAAK,CAACO,GAAG;MACpBX,IAAI,CAACY,GAAG,GAAGR,KAAK,CAACQ,GAAG;IACtB,CAAC,MAAM,IAAIX,IAAI,KAAK,CAAC,EAAe;MAClCD,IAAI,CAACI,KAAK,GAAGA,KAAK;IACpB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;MAClCD,IAAI,CAACW,GAAG,GAAGP,KAAK;IAClB,CAAC,MAAM,IAAIH,IAAI,KAAK,CAAC,EAAe;MAClCD,IAAI,CAACY,GAAG,GAAGR,KAAK;IAClB;IAEA,IAAID,SAAS,EAAE;MACb,IAAIF,IAAI,KAAK,CAAC,EAAiB;QAC7B2B,GAAG,CAACrC,IAAI,CAAC,UAAUmD,QAAQ,EAAEG,IAAI,EAAE;UACjC,OAAOzC,KAAK,CAACO,GAAG,CAACG,IAAI,CAAC4B,QAAQ,EAAEG,IAAI,CAAC;QACvC,CAAC,CAAC;QACFjB,GAAG,CAACrC,IAAI,CAAC,UAAUmD,QAAQ,EAAEG,IAAI,EAAE;UACjC,OAAOzC,KAAK,CAACQ,GAAG,CAACE,IAAI,CAAC4B,QAAQ,EAAEG,IAAI,CAAC;QACvC,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI5C,IAAI,KAAK,CAAC,EAAe;QAClC2B,GAAG,CAACrC,IAAI,CAACa,KAAK,CAAC;MACjB,CAAC,MAAM;QACLwB,GAAG,CAACrC,IAAI,CAAC,UAAUmD,QAAQ,EAAEG,IAAI,EAAE;UACjC,OAAOzC,KAAK,CAACU,IAAI,CAAC4B,QAAQ,EAAEG,IAAI,CAAC;QACnC,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLV,MAAM,CAACW,cAAc,CAACjB,IAAI,EAAE9B,IAAI,EAAEC,IAAI,CAAC;IACzC;EACF;AACF;AAEA,SAAS+C,eAAeA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAE;EACvD,IAAItB,GAAG,GAAG,EAAE;EACZ,IAAIuB,iBAAiB;EACrB,IAAIC,kBAAkB;EACtB,IAAIC,WAAW;EAEf,IAAIC,sBAAsB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACtC,IAAIC,uBAAuB,GAAG,IAAID,GAAG,CAAC,CAAC;EAEvC,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,QAAQ,CAACT,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,IAAIT,OAAO,GAAGmB,QAAQ,CAACV,CAAC,CAAC;IAGzB,IAAI,CAACN,KAAK,CAACC,OAAO,CAACJ,OAAO,CAAC,EAAE;IAE7B,IAAI7B,IAAI,GAAG6B,OAAO,CAAC,CAAC,CAAC;IACrB,IAAI/B,IAAI,GAAG+B,OAAO,CAAC,CAAC,CAAC;IACrB,IAAI3B,SAAS,GAAG2B,OAAO,CAACU,MAAM,GAAG,CAAC;IAElC,IAAIT,kBAAkB,GAAG9B,IAAI,GAAG,EAAE;IAClC,IAAIC,QAAQ,GAAG,CAAC,EAAED,IAAI,GAAG,CAAC,CAAC;IAC3B,IAAI4B,IAAI;IACR,IAAI5C,YAAY;IAChB,IAAIoB,eAAe,GAAG6C,aAAa;IAEnCjD,IAAI,IAAI,CAAC;IAET,IAAIC,QAAQ,EAAE;MACZ2B,IAAI,GAAGmB,KAAK;MAEZ,IAAI/C,IAAI,KAAK,CAAC,EAAc;QAC1BmD,kBAAkB,GAAGA,kBAAkB,IAAI,EAAE;QAC7CnE,YAAY,GAAGmE,kBAAkB;MACnC;MACA,IAAIjD,SAAS,IAAI,CAACkD,WAAW,EAAE;QAC7BA,WAAW,GAAG,SAAAA,CAAUI,CAAC,EAAE;UACzB,OAAOC,WAAU,CAACD,CAAC,CAAC,KAAKT,KAAK;QAChC,CAAC;MACH;MACA3C,eAAe,GAAGgD,WAAW;IAC/B,CAAC,MAAM;MACLxB,IAAI,GAAGmB,KAAK,CAACW,SAAS;MAEtB,IAAI1D,IAAI,KAAK,CAAC,EAAc;QAC1BkD,iBAAiB,GAAGA,iBAAiB,IAAI,EAAE;QAC3ClE,YAAY,GAAGkE,iBAAiB;MAClC;IACF;IAEA,IAAIlD,IAAI,KAAK,CAAC,IAAgB,CAACE,SAAS,EAAE;MACxC,IAAIyD,iBAAiB,GAAG1D,QAAQ,GAC5BsD,uBAAuB,GACvBF,sBAAsB;MAE1B,IAAIO,YAAY,GAAGD,iBAAiB,CAACjD,GAAG,CAACZ,IAAI,CAAC,IAAI,CAAC;MAEnD,IACE8D,YAAY,KAAK,IAAI,IACpBA,YAAY,KAAK,CAAC,IAAiB5D,IAAI,KAAK,CAAE,IAC9C4D,YAAY,KAAK,CAAC,IAAiB5D,IAAI,KAAK,CAAE,EAC/C;QACA,MAAM,IAAIiB,KAAK,CACb,uMAAuM,GACrMnB,IACJ,CAAC;MACH;MACA6D,iBAAiB,CAAChD,GAAG,CACnBb,IAAI,EACJ,CAAC8D,YAAY,IAAI5D,IAAI,GAAG,CAAC,GAAgBA,IAAI,GAAG,IAClD,CAAC;IACH;IAEA0B,cAAc,CACZC,GAAG,EACHC,IAAI,EACJC,OAAO,EACPC,kBAAkB,EAClBhC,IAAI,EACJE,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTlB,YAAY,EACZoB,eACF,CAAC;EACH;EAEAyD,gBAAgB,CAAClC,GAAG,EAAEuB,iBAAiB,CAAC;EACxCW,gBAAgB,CAAClC,GAAG,EAAEwB,kBAAkB,CAAC;EACzC,OAAOxB,GAAG;AACZ;AAEA,SAASkC,gBAAgBA,CAAClC,GAAG,EAAE3C,YAAY,EAAE;EAC3C,IAAIA,YAAY,EAAE;IAChB2C,GAAG,CAACrC,IAAI,CAAC,UAAUmD,QAAQ,EAAE;MAC3B,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtD,YAAY,CAACuD,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5CtD,YAAY,CAACsD,CAAC,CAAC,CAACzB,IAAI,CAAC4B,QAAQ,CAAC;MAChC;MACA,OAAOA,QAAQ;IACjB,CAAC,CAAC;EACJ;AACF;AAEA,SAASqB,cAAcA,CAACC,WAAW,EAAEC,SAAS,EAAElC,kBAAkB,EAAE;EAClE,IAAIkC,SAAS,CAACzB,MAAM,EAAE;IACpB,IAAIvD,YAAY,GAAG,EAAE;IACrB,IAAIiF,QAAQ,GAAGF,WAAW;IAC1B,IAAIjE,IAAI,GAAGiE,WAAW,CAACjE,IAAI;IAE3B,IAAIuC,GAAG,GAAGP,kBAAkB,GAAG,CAAC,GAAG,CAAC;IAEpC,KAAK,IAAIQ,CAAC,GAAG0B,SAAS,CAACzB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAID,GAAG,EAAE;MACnD,IAAIpD,oBAAoB,GAAG;QAAEwB,CAAC,EAAE;MAAM,CAAC;MAEvC,IAAI;QACF,IAAIyD,YAAY,GAAGF,SAAS,CAAC1B,CAAC,CAAC,CAACzB,IAAI,CAClCiB,kBAAkB,GAAGkC,SAAS,CAAC1B,CAAC,GAAG,CAAC,CAAC,GAAGhB,SAAS,EACjD2C,QAAQ,EACR;UACEjE,IAAI,EAAE,OAAO;UACbF,IAAI,EAAEA,IAAI;UACVZ,cAAc,EAAEH,0BAA0B,CACxCC,YAAY,EACZC,oBACF;QACF,CACF,CAAC;MACH,CAAC,SAAS;QACRA,oBAAoB,CAACwB,CAAC,GAAG,IAAI;MAC/B;MAEA,IAAIyD,YAAY,KAAK5C,SAAS,EAAE;QAC9BF,sBAAsB,CAAC,CAAC,EAAc8C,YAAY,CAAC;QACnDD,QAAQ,GAAGC,YAAY;MACzB;IACF;IAEA,OAAO,CACLD,QAAQ,EACR,YAAY;MACV,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtD,YAAY,CAACuD,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5CtD,YAAY,CAACsD,CAAC,CAAC,CAACzB,IAAI,CAACoD,QAAQ,CAAC;MAChC;IACF,CAAC,CACF;EACH;AAGF;AAoJe,SAASE,aAAaA,CACnCJ,WAAW,EACXK,UAAU,EACVJ,SAAS,EACTK,iBAAiB,EACjBpB,aAAa,EACb;EACA,OAAO;IACLqB,CAAC,EAAExB,eAAe,CAACiB,WAAW,EAAEK,UAAU,EAAEnB,aAAa,CAAC;IAE1D,IAAIsB,CAACA,CAAA,EAAG;MACN,OAAOT,cAAc,CAACC,WAAW,EAAEC,SAAS,EAAEK,iBAAiB,CAAC;IAClE;EACF,CAAC;AACH"}