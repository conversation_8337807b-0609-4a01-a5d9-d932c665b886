{"version": 3, "names": ["_t", "require", "isIdentifier", "_params", "node", "idNode", "parentNode", "print", "typeParameters", "nameInfo", "_getFuncIdName", "call", "sourceIdentifierName", "name", "pos", "token", "_parameters", "params", "noLineTerminator", "type", "returnType", "_noLineTerminator", "parameters", "parent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "i", "_param", "space", "parameter", "printJoin", "decorators", "optional", "typeAnnotation", "_methodHead", "kind", "key", "word", "async", "generator", "computed", "undefined", "_predicate", "noLineTerminatorAfter", "predicate", "_functionHead", "_endsWithInnerRaw", "id", "FunctionExpression", "body", "ArrowFunctionExpression", "firstParam", "format", "retainLines", "hasTypesOrComments", "printInnerComments", "param", "_param$leadingComment", "_param$trailingCommen", "leadingComments", "trailingComments", "parentType", "left", "_id$loc", "_id$loc2", "loc", "start", "identifierName", "_id$loc3", "_id$loc4", "value"], "sources": ["../../src/generators/methods.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport type * as t from \"@babel/types\";\nimport { isIdentifier } from \"@babel/types\";\nimport type { NodePath } from \"@babel/traverse\";\n\nexport function _params(\n  this: Printer,\n  node: t.Function | t.TSDeclareMethod | t.TSDeclareFunction,\n  idNode: t.Expression | t.PrivateName,\n  parentNode: NodePath<\n    t.Function | t.TSDeclareMethod | t.TSDeclareFunction\n  >[\"parent\"],\n) {\n  this.print(node.typeParameters, node);\n\n  const nameInfo = _getFuncIdName.call(this, idNode, parentNode);\n  if (nameInfo) {\n    this.sourceIdentifierName(nameInfo.name, nameInfo.pos);\n  }\n\n  this.token(\"(\");\n  this._parameters(node.params, node);\n  this.token(\")\");\n\n  const noLineTerminator = node.type === \"ArrowFunctionExpression\";\n  this.print(node.returnType, node, noLineTerminator);\n\n  this._noLineTerminator = noLineTerminator;\n}\n\nexport function _parameters(\n  this: Printer,\n  parameters: t.Function[\"params\"],\n  parent:\n    | t.Function\n    | t.TSIndexSignature\n    | t.TSDeclareMethod\n    | t.TSDeclareFunction\n    | t.TSFunctionType\n    | t.TSConstructorType,\n) {\n  const paramLength = parameters.length;\n  for (let i = 0; i < paramLength; i++) {\n    this._param(parameters[i], parent);\n\n    if (i < parameters.length - 1) {\n      this.token(\",\");\n      this.space();\n    }\n  }\n}\n\nexport function _param(\n  this: Printer,\n  parameter: t.Identifier | t.RestElement | t.Pattern | t.TSParameterProperty,\n  parent?:\n    | t.Function\n    | t.TSIndexSignature\n    | t.TSDeclareMethod\n    | t.TSDeclareFunction\n    | t.TSFunctionType\n    | t.TSConstructorType,\n) {\n  this.printJoin(parameter.decorators, parameter);\n  this.print(parameter, parent);\n  if (\n    // @ts-expect-error optional is not in TSParameterProperty\n    parameter.optional\n  ) {\n    this.token(\"?\"); // TS / flow\n  }\n\n  this.print(\n    // @ts-expect-error typeAnnotation is not in TSParameterProperty\n    parameter.typeAnnotation,\n    parameter,\n  ); // TS / flow\n}\n\nexport function _methodHead(this: Printer, node: t.Method | t.TSDeclareMethod) {\n  const kind = node.kind;\n  const key = node.key;\n\n  if (kind === \"get\" || kind === \"set\") {\n    this.word(kind);\n    this.space();\n  }\n\n  if (node.async) {\n    this.word(\"async\", true);\n    this.space();\n  }\n\n  if (\n    kind === \"method\" ||\n    // @ts-expect-error Fixme: kind: \"init\" is not defined\n    kind === \"init\"\n  ) {\n    if (node.generator) {\n      this.token(\"*\");\n    }\n  }\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(key, node);\n    this.token(\"]\");\n  } else {\n    this.print(key, node);\n  }\n\n  if (\n    // @ts-expect-error optional is not in ObjectMethod\n    node.optional\n  ) {\n    // TS\n    this.token(\"?\");\n  }\n\n  this._params(\n    node,\n    node.computed && node.key.type !== \"StringLiteral\" ? undefined : node.key,\n    undefined,\n  );\n}\n\nexport function _predicate(\n  this: Printer,\n  node:\n    | t.FunctionDeclaration\n    | t.FunctionExpression\n    | t.ArrowFunctionExpression,\n  noLineTerminatorAfter?: boolean,\n) {\n  if (node.predicate) {\n    if (!node.returnType) {\n      this.token(\":\");\n    }\n    this.space();\n    this.print(node.predicate, node, noLineTerminatorAfter);\n  }\n}\n\nexport function _functionHead(\n  this: Printer,\n  node: t.FunctionDeclaration | t.FunctionExpression | t.TSDeclareFunction,\n  parent: NodePath<\n    t.FunctionDeclaration | t.FunctionExpression | t.TSDeclareFunction\n  >[\"parent\"],\n) {\n  if (node.async) {\n    this.word(\"async\");\n    // We prevent inner comments from being printed here,\n    // so that they are always consistently printed in the\n    // same place regardless of the function type.\n    this._endsWithInnerRaw = false;\n    this.space();\n  }\n  this.word(\"function\");\n  if (node.generator) {\n    // We prevent inner comments from being printed here,\n    // so that they are always consistently printed in the\n    // same place regardless of the function type.\n    this._endsWithInnerRaw = false;\n    this.token(\"*\");\n  }\n\n  this.space();\n  if (node.id) {\n    this.print(node.id, node);\n  }\n\n  this._params(node, node.id, parent);\n  if (node.type !== \"TSDeclareFunction\") {\n    this._predicate(node);\n  }\n}\n\nexport function FunctionExpression(\n  this: Printer,\n  node: t.FunctionExpression,\n  parent: NodePath<t.FunctionExpression>[\"parent\"],\n) {\n  this._functionHead(node, parent);\n  this.space();\n  this.print(node.body, node);\n}\n\nexport { FunctionExpression as FunctionDeclaration };\n\nexport function ArrowFunctionExpression(\n  this: Printer,\n  node: t.ArrowFunctionExpression,\n  parent: NodePath<t.ArrowFunctionExpression>[\"parent\"],\n) {\n  if (node.async) {\n    this.word(\"async\", true);\n    this.space();\n  }\n\n  // Try to avoid printing parens in simple cases, but only if we're pretty\n  // sure that they aren't needed by type annotations or potential newlines.\n  let firstParam;\n  if (\n    !this.format.retainLines &&\n    node.params.length === 1 &&\n    isIdentifier((firstParam = node.params[0])) &&\n    !hasTypesOrComments(node, firstParam)\n  ) {\n    this.print(firstParam, node, true);\n  } else {\n    this._params(node, undefined, parent);\n  }\n\n  this._predicate(node, true);\n  this.space();\n  // When printing (x)/*1*/=>{}, we remove the parentheses\n  // and thus there aren't two contiguous inner tokens.\n  // We forcefully print inner comments here.\n  this.printInnerComments();\n  this.token(\"=>\");\n\n  this.space();\n\n  this.print(node.body, node);\n}\n\nfunction hasTypesOrComments(\n  node: t.ArrowFunctionExpression,\n  param: t.Identifier,\n): boolean {\n  return !!(\n    node.typeParameters ||\n    node.returnType ||\n    node.predicate ||\n    param.typeAnnotation ||\n    param.optional ||\n    // Flow does not support `foo /*: string*/ => {};`\n    param.leadingComments?.length ||\n    param.trailingComments?.length\n  );\n}\n\nfunction _getFuncIdName(\n  this: Printer,\n  idNode: t.Expression | t.PrivateName,\n  parent: NodePath<\n    t.Function | t.TSDeclareMethod | t.TSDeclareFunction\n  >[\"parent\"],\n) {\n  let id: t.Expression | t.PrivateName | t.LVal = idNode;\n\n  if (!id && parent) {\n    const parentType = parent.type;\n\n    if (parentType === \"VariableDeclarator\") {\n      id = parent.id;\n    } else if (\n      parentType === \"AssignmentExpression\" ||\n      parentType === \"AssignmentPattern\"\n    ) {\n      id = parent.left;\n    } else if (\n      parentType === \"ObjectProperty\" ||\n      parentType === \"ClassProperty\"\n    ) {\n      if (!parent.computed || parent.key.type === \"StringLiteral\") {\n        id = parent.key;\n      }\n    } else if (\n      parentType === \"ClassPrivateProperty\" ||\n      parentType === \"ClassAccessorProperty\"\n    ) {\n      id = parent.key;\n    }\n  }\n\n  if (!id) return;\n\n  let nameInfo;\n\n  if (id.type === \"Identifier\") {\n    nameInfo = {\n      pos: id.loc?.start,\n      name:\n        // @ts-expect-error Undocumented property identifierName\n        id.loc?.identifierName || id.name,\n    };\n  } else if (id.type === \"PrivateName\") {\n    nameInfo = {\n      pos: id.loc?.start,\n      name: \"#\" + id.id.name,\n    };\n  } else if (id.type === \"StringLiteral\") {\n    nameInfo = {\n      pos: id.loc?.start,\n      name: id.value,\n    };\n  }\n\n  return nameInfo;\n}\n"], "mappings": ";;;;;;;;;;;;;AAEA,IAAAA,EAAA,GAAAC,OAAA;AAA4C;EAAnCC;AAAY,IAAAF,EAAA;AAGd,SAASG,OAAOA,CAErBC,IAA0D,EAC1DC,MAAoC,EACpCC,UAEW,EACX;EACA,IAAI,CAACC,KAAK,CAACH,IAAI,CAACI,cAAc,EAAEJ,IAAI,CAAC;EAErC,MAAMK,QAAQ,GAAGC,cAAc,CAACC,IAAI,CAAC,IAAI,EAAEN,MAAM,EAAEC,UAAU,CAAC;EAC9D,IAAIG,QAAQ,EAAE;IACZ,IAAI,CAACG,oBAAoB,CAACH,QAAQ,CAACI,IAAI,EAAEJ,QAAQ,CAACK,GAAG,CAAC;EACxD;EAEA,IAAI,CAACC,SAAK,IAAK;EACf,IAAI,CAACC,WAAW,CAACZ,IAAI,CAACa,MAAM,EAAEb,IAAI,CAAC;EACnC,IAAI,CAACW,SAAK,IAAK;EAEf,MAAMG,gBAAgB,GAAGd,IAAI,CAACe,IAAI,KAAK,yBAAyB;EAChE,IAAI,CAACZ,KAAK,CAACH,IAAI,CAACgB,UAAU,EAAEhB,IAAI,EAAEc,gBAAgB,CAAC;EAEnD,IAAI,CAACG,iBAAiB,GAAGH,gBAAgB;AAC3C;AAEO,SAASF,WAAWA,CAEzBM,UAAgC,EAChCC,MAMuB,EACvB;EACA,MAAMC,WAAW,GAAGF,UAAU,CAACG,MAAM;EACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,EAAEE,CAAC,EAAE,EAAE;IACpC,IAAI,CAACC,MAAM,CAACL,UAAU,CAACI,CAAC,CAAC,EAAEH,MAAM,CAAC;IAElC,IAAIG,CAAC,GAAGJ,UAAU,CAACG,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACV,SAAK,IAAK;MACf,IAAI,CAACa,KAAK,EAAE;IACd;EACF;AACF;AAEO,SAASD,MAAMA,CAEpBE,SAA2E,EAC3EN,MAMuB,EACvB;EACA,IAAI,CAACO,SAAS,CAACD,SAAS,CAACE,UAAU,EAAEF,SAAS,CAAC;EAC/C,IAAI,CAACtB,KAAK,CAACsB,SAAS,EAAEN,MAAM,CAAC;EAC7B,IAEEM,SAAS,CAACG,QAAQ,EAClB;IACA,IAAI,CAACjB,SAAK,IAAK;EACjB;EAEA,IAAI,CAACR,KAAK,CAERsB,SAAS,CAACI,cAAc,EACxBJ,SAAS,CACV;AACH;AAEO,SAASK,WAAWA,CAAgB9B,IAAkC,EAAE;EAC7E,MAAM+B,IAAI,GAAG/B,IAAI,CAAC+B,IAAI;EACtB,MAAMC,GAAG,GAAGhC,IAAI,CAACgC,GAAG;EAEpB,IAAID,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,EAAE;IACpC,IAAI,CAACE,IAAI,CAACF,IAAI,CAAC;IACf,IAAI,CAACP,KAAK,EAAE;EACd;EAEA,IAAIxB,IAAI,CAACkC,KAAK,EAAE;IACd,IAAI,CAACD,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACxB,IAAI,CAACT,KAAK,EAAE;EACd;EAEA,IACEO,IAAI,KAAK,QAAQ,IAEjBA,IAAI,KAAK,MAAM,EACf;IACA,IAAI/B,IAAI,CAACmC,SAAS,EAAE;MAClB,IAAI,CAACxB,SAAK,IAAK;IACjB;EACF;EAEA,IAAIX,IAAI,CAACoC,QAAQ,EAAE;IACjB,IAAI,CAACzB,SAAK,IAAK;IACf,IAAI,CAACR,KAAK,CAAC6B,GAAG,EAAEhC,IAAI,CAAC;IACrB,IAAI,CAACW,SAAK,IAAK;EACjB,CAAC,MAAM;IACL,IAAI,CAACR,KAAK,CAAC6B,GAAG,EAAEhC,IAAI,CAAC;EACvB;EAEA,IAEEA,IAAI,CAAC4B,QAAQ,EACb;IAEA,IAAI,CAACjB,SAAK,IAAK;EACjB;EAEA,IAAI,CAACZ,OAAO,CACVC,IAAI,EACJA,IAAI,CAACoC,QAAQ,IAAIpC,IAAI,CAACgC,GAAG,CAACjB,IAAI,KAAK,eAAe,GAAGsB,SAAS,GAAGrC,IAAI,CAACgC,GAAG,EACzEK,SAAS,CACV;AACH;AAEO,SAASC,UAAUA,CAExBtC,IAG6B,EAC7BuC,qBAA+B,EAC/B;EACA,IAAIvC,IAAI,CAACwC,SAAS,EAAE;IAClB,IAAI,CAACxC,IAAI,CAACgB,UAAU,EAAE;MACpB,IAAI,CAACL,SAAK,IAAK;IACjB;IACA,IAAI,CAACa,KAAK,EAAE;IACZ,IAAI,CAACrB,KAAK,CAACH,IAAI,CAACwC,SAAS,EAAExC,IAAI,EAAEuC,qBAAqB,CAAC;EACzD;AACF;AAEO,SAASE,aAAaA,CAE3BzC,IAAwE,EACxEmB,MAEW,EACX;EACA,IAAInB,IAAI,CAACkC,KAAK,EAAE;IACd,IAAI,CAACD,IAAI,CAAC,OAAO,CAAC;IAIlB,IAAI,CAACS,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAClB,KAAK,EAAE;EACd;EACA,IAAI,CAACS,IAAI,CAAC,UAAU,CAAC;EACrB,IAAIjC,IAAI,CAACmC,SAAS,EAAE;IAIlB,IAAI,CAACO,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAC/B,SAAK,IAAK;EACjB;EAEA,IAAI,CAACa,KAAK,EAAE;EACZ,IAAIxB,IAAI,CAAC2C,EAAE,EAAE;IACX,IAAI,CAACxC,KAAK,CAACH,IAAI,CAAC2C,EAAE,EAAE3C,IAAI,CAAC;EAC3B;EAEA,IAAI,CAACD,OAAO,CAACC,IAAI,EAAEA,IAAI,CAAC2C,EAAE,EAAExB,MAAM,CAAC;EACnC,IAAInB,IAAI,CAACe,IAAI,KAAK,mBAAmB,EAAE;IACrC,IAAI,CAACuB,UAAU,CAACtC,IAAI,CAAC;EACvB;AACF;AAEO,SAAS4C,kBAAkBA,CAEhC5C,IAA0B,EAC1BmB,MAAgD,EAChD;EACA,IAAI,CAACsB,aAAa,CAACzC,IAAI,EAAEmB,MAAM,CAAC;EAChC,IAAI,CAACK,KAAK,EAAE;EACZ,IAAI,CAACrB,KAAK,CAACH,IAAI,CAAC6C,IAAI,EAAE7C,IAAI,CAAC;AAC7B;AAIO,SAAS8C,uBAAuBA,CAErC9C,IAA+B,EAC/BmB,MAAqD,EACrD;EACA,IAAInB,IAAI,CAACkC,KAAK,EAAE;IACd,IAAI,CAACD,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACxB,IAAI,CAACT,KAAK,EAAE;EACd;EAIA,IAAIuB,UAAU;EACd,IACE,CAAC,IAAI,CAACC,MAAM,CAACC,WAAW,IACxBjD,IAAI,CAACa,MAAM,CAACQ,MAAM,KAAK,CAAC,IACxBvB,YAAY,CAAEiD,UAAU,GAAG/C,IAAI,CAACa,MAAM,CAAC,CAAC,CAAC,CAAE,IAC3C,CAACqC,kBAAkB,CAAClD,IAAI,EAAE+C,UAAU,CAAC,EACrC;IACA,IAAI,CAAC5C,KAAK,CAAC4C,UAAU,EAAE/C,IAAI,EAAE,IAAI,CAAC;EACpC,CAAC,MAAM;IACL,IAAI,CAACD,OAAO,CAACC,IAAI,EAAEqC,SAAS,EAAElB,MAAM,CAAC;EACvC;EAEA,IAAI,CAACmB,UAAU,CAACtC,IAAI,EAAE,IAAI,CAAC;EAC3B,IAAI,CAACwB,KAAK,EAAE;EAIZ,IAAI,CAAC2B,kBAAkB,EAAE;EACzB,IAAI,CAACxC,KAAK,CAAC,IAAI,CAAC;EAEhB,IAAI,CAACa,KAAK,EAAE;EAEZ,IAAI,CAACrB,KAAK,CAACH,IAAI,CAAC6C,IAAI,EAAE7C,IAAI,CAAC;AAC7B;AAEA,SAASkD,kBAAkBA,CACzBlD,IAA+B,EAC/BoD,KAAmB,EACV;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EACT,OAAO,CAAC,EACNtD,IAAI,CAACI,cAAc,IACnBJ,IAAI,CAACgB,UAAU,IACfhB,IAAI,CAACwC,SAAS,IACdY,KAAK,CAACvB,cAAc,IACpBuB,KAAK,CAACxB,QAAQ,KAAAyB,qBAAA,GAEdD,KAAK,CAACG,eAAe,aAArBF,qBAAA,CAAuBhC,MAAM,KAAAiC,qBAAA,GAC7BF,KAAK,CAACI,gBAAgB,aAAtBF,qBAAA,CAAwBjC,MAAM,CAC/B;AACH;AAEA,SAASf,cAAcA,CAErBL,MAAoC,EACpCkB,MAEW,EACX;EACA,IAAIwB,EAAyC,GAAG1C,MAAM;EAEtD,IAAI,CAAC0C,EAAE,IAAIxB,MAAM,EAAE;IACjB,MAAMsC,UAAU,GAAGtC,MAAM,CAACJ,IAAI;IAE9B,IAAI0C,UAAU,KAAK,oBAAoB,EAAE;MACvCd,EAAE,GAAGxB,MAAM,CAACwB,EAAE;IAChB,CAAC,MAAM,IACLc,UAAU,KAAK,sBAAsB,IACrCA,UAAU,KAAK,mBAAmB,EAClC;MACAd,EAAE,GAAGxB,MAAM,CAACuC,IAAI;IAClB,CAAC,MAAM,IACLD,UAAU,KAAK,gBAAgB,IAC/BA,UAAU,KAAK,eAAe,EAC9B;MACA,IAAI,CAACtC,MAAM,CAACiB,QAAQ,IAAIjB,MAAM,CAACa,GAAG,CAACjB,IAAI,KAAK,eAAe,EAAE;QAC3D4B,EAAE,GAAGxB,MAAM,CAACa,GAAG;MACjB;IACF,CAAC,MAAM,IACLyB,UAAU,KAAK,sBAAsB,IACrCA,UAAU,KAAK,uBAAuB,EACtC;MACAd,EAAE,GAAGxB,MAAM,CAACa,GAAG;IACjB;EACF;EAEA,IAAI,CAACW,EAAE,EAAE;EAET,IAAItC,QAAQ;EAEZ,IAAIsC,EAAE,CAAC5B,IAAI,KAAK,YAAY,EAAE;IAAA,IAAA4C,OAAA,EAAAC,QAAA;IAC5BvD,QAAQ,GAAG;MACTK,GAAG,GAAAiD,OAAA,GAAEhB,EAAE,CAACkB,GAAG,qBAANF,OAAA,CAAQG,KAAK;MAClBrD,IAAI,EAEF,EAAAmD,QAAA,GAAAjB,EAAE,CAACkB,GAAG,qBAAND,QAAA,CAAQG,cAAc,KAAIpB,EAAE,CAAClC;IACjC,CAAC;EACH,CAAC,MAAM,IAAIkC,EAAE,CAAC5B,IAAI,KAAK,aAAa,EAAE;IAAA,IAAAiD,QAAA;IACpC3D,QAAQ,GAAG;MACTK,GAAG,GAAAsD,QAAA,GAAErB,EAAE,CAACkB,GAAG,qBAANG,QAAA,CAAQF,KAAK;MAClBrD,IAAI,EAAE,GAAG,GAAGkC,EAAE,CAACA,EAAE,CAAClC;IACpB,CAAC;EACH,CAAC,MAAM,IAAIkC,EAAE,CAAC5B,IAAI,KAAK,eAAe,EAAE;IAAA,IAAAkD,QAAA;IACtC5D,QAAQ,GAAG;MACTK,GAAG,GAAAuD,QAAA,GAAEtB,EAAE,CAACkB,GAAG,qBAANI,QAAA,CAAQH,KAAK;MAClBrD,IAAI,EAAEkC,EAAE,CAACuB;IACX,CAAC;EACH;EAEA,OAAO7D,QAAQ;AACjB"}