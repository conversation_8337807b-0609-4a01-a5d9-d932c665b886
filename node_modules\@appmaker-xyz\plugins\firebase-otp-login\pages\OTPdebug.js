import { spacing } from '@appmaker-xyz/uikit/src/styles/index';

const blocks = [
  {
    name: 'appmaker/layout',
    attributes: {
      style: {
        paddingHorizontal: spacing.base,
      },
    },
    innerBlocks: [
      {
        name: 'appmaker/floating-label-input',
        attributes: {
          label: 'Mobile Number',
          name: 'phone',
          status: 'grey',
          caption: 'In case we need to contact you about your order',
          type: 'number',
          defaultValue: '+919048073427',
        },
      },
      // {
      //   name: 'appmaker/form-radio-item',
      //   attributes: {
      //     label: 'Mobile Number',
      //     name: 'phone',
      //     status: 'grey',
      //     caption: 'In case we need to contact you about your order',
      //     type: 'number',
      //     defaultValue: '9995556560',
      //   },
      // },
    ],
  },
  // {
  //   clientId: 'checkout-sumbit',
  //   name: 'appmaker/button',
  //   attributes: {
  //     content: 'OPEN_CUSTOM LOGIN',
  //     loading: false,
  //     wholeContainerStyle: {
  //       borderRadius: 0,
  //     },
  //     appmakerAction: {
  //       action: 'TEST_OPEN_CUSTOM',
  //     },
  //   },
  // }
];
const OTPDebug = {
  type: 'normal',
  title: 'OTP debug',
  attributes: {
    renderInScroll: true,
    renderType: 'normal',
    showLoadingTillData: true,
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
      backgroundColor: '#ffffff',
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: blocks,
  fixedFooter: {
    clientId: 'checkout-sumbit',
    name: 'appmaker/button',
    attributes: {
      content: 'Save Address Guest',
      loading: false,
      wholeContainerStyle: {
        borderRadius: 0,
      },
      appmakerAction: {
        action: 'TEST_FIREBASE_OTP_DEBUG',
      },
    },
  },
};
export default OTPDebug;
