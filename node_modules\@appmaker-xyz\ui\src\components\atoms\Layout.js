import React from 'react';
import { View, ActivityIndicator as RNLoading, StyleSheet } from 'react-native';
import { useStyles } from '@appmaker-xyz/react-native';
import { usePluginStore } from '@appmaker-xyz/core';
import ActivityIndicator from './ActivityIndicator';

const Layout = ({
  children,
  loadingComponent,
  loadingLayout,
  loading,
  ...props
}) => {
  const { theme } = useStyles();
  const defaultLoadingStyle = usePluginStore(
    (state) => state?.plugins['app-branding']?.settings?.defaultLoadingStyle,
  );
  if (loading) {
    return loadingComponent && typeof loadingComponent === 'function' ? (
      loadingComponent()
    ) : defaultLoadingStyle ? (
      <View style={styles.container}>
        <RNLoading color={theme.colors.text} size="small" />
      </View>
    ) : (
      <View {...props}>
        <ActivityIndicator size="large" type={loadingLayout} />
      </View>
    );
  }
  return <View {...props}>{children}</View>;
};

export default Layout;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
});
