import React from 'react';
import { Pressable, View } from 'react-native';
import Modal from 'react-native-modal';
import Icon from 'react-native-vector-icons/Feather';
import { CouponIcon } from './assets/svg/index';
import { testProps } from '@appmaker-xyz/core';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';
import { ThemeText as Text } from '@appmaker-xyz/ui';

const CouponSuccess = (props) => {
  const { styles, theme } = useStyles(stylesheet);
  return (
    <View>
      <Modal
        isVisible={props.showModel}
        onRequestClose={props.closeModal}
        onDismiss={props.onCancelCoupon}>
        <View style={styles.container}>
          <Pressable style={styles.xIcon} onPress={props.closeModal}>
            <Icon
              {...testProps('coupon-success-close-icon')}
              name="x"
              size={20}
              color={theme.colors.lightText}
            />
          </Pressable>
          <View style={styles.header}>
            <CouponIcon color={theme.colors.success} />
            <Text
              {...testProps('coupon-success-subtitle')}
              style={styles.headingText}>
              {props?.couponTitleDisplay
                ? props?.couponTitleDisplay
                : props.couponTitle}
            </Text>
          </View>
          <Text
            {...testProps('coupon-success-message')}
            style={styles.successText}>
            Woohoo! Your coupon is successfully applied
          </Text>
          {/* <Text style={styles.successMessage}>
            Woohoo! Your coupon is successfully applied
          </Text> */}
          <Pressable
            {...testProps('coupon-success-ok-button')}
            style={styles.button}
            onPress={props.closeModal}>
            <Text style={styles.yay}>YAY!</Text>
          </Pressable>
        </View>
      </Modal>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.background,
    borderRadius: 16,
    padding: 28,
    position: 'relative',
  },
  xIcon: {
    position: 'absolute',
    right: 12,
    top: 12,
    padding: 6,
    flexShrink: 1,
    backgroundColor: theme.colors.lightBackground,
    borderRadius: 50,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 12,
  },
  headingText: {
    fontSize: 18,
    fontFamily: theme.fontFamily.regular,
    marginLeft: 12,
    color: theme.colors.text,
  },
  successText: {
    fontSize: 24,
    fontFamily: theme.fontFamily.bold,
    marginBottom: 12,
    color: theme.colors.text,
  },
  successMessage: {
    fontSize: 16,
    fontFamily: theme.fontFamily.regular,
  },
  button: {
    alignItems: 'center',
    marginTop: 24,
  },
  yay: {
    fontSize: 18,
    fontFamily: theme.fontFamily.bold,
    color: theme.colors.success,
  },
}));

export default CouponSuccess;
