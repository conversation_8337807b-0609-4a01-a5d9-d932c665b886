/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { View } from 'react-native';
import SezzleWidget from '../widget/widget';
export function SezzleBlock({ attributes }) {
  // return null;
  console.log(attributes);
  return (
    <View style={{ backgroundColor: 'white', paddingHorizontal: 10 }}>
      <SezzleWidget
        containerStyle={{ marginVertical: 5 }}
        totalPrice={attributes.totalPrice}
        merchant_uuid={attributes.merchant_uuid}
      />
    </View>
  );
}
