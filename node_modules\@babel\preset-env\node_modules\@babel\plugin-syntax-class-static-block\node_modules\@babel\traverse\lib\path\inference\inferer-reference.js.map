{"version": 3, "names": ["_t", "require", "_util", "BOOLEAN_NUMBER_BINARY_OPERATORS", "createTypeAnnotationBasedOnTypeof", "numberTypeAnnotation", "voidTypeAnnotation", "_default", "node", "isReferenced", "binding", "scope", "getBinding", "name", "identifier", "typeAnnotation", "getTypeAnnotationBindingConstantViolations", "path", "types", "functionConstantViolations", "constantViolations", "getConstantViolationsBefore", "testType", "getConditionalAnnotation", "testConstantViolations", "ifStatement", "filter", "indexOf", "push", "length", "violation", "getTypeAnnotation", "createUnionType", "functions", "violations", "slice", "unshift", "resolve", "status", "_guessExecutionStatusRelativeTo", "inferAnnotationFromBinaryExpression", "operator", "right", "get", "left", "target", "isIdentifier", "typeofPath", "typePath", "isUnaryExpression", "isLiteral", "typeValue", "value", "getParentConditionalPath", "parentPath", "isIfStatement", "isConditionalExpression", "key", "isFunction", "test", "paths", "i", "isLogicalExpression", "isBinaryExpression", "type"], "sources": ["../../../src/path/inference/inferer-reference.ts"], "sourcesContent": ["import type NodePath from \"../index\";\nimport {\n  BOOLEAN_NUMBER_BINARY_OPERATORS,\n  createTypeAnnotationBasedOnTypeof,\n  numberTypeAnnotation,\n  voidTypeAnnotation,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type Binding from \"../../scope/binding\";\n\nimport { createUnionType } from \"./util\";\n\nexport default function (this: NodePath<t.Identifier>, node: t.Identifier) {\n  if (!this.isReferenced()) return;\n\n  // check if a binding exists of this value and if so then return a union type of all\n  // possible types that the binding could be\n  const binding = this.scope.getBinding(node.name);\n  if (binding) {\n    if (binding.identifier.typeAnnotation) {\n      return binding.identifier.typeAnnotation;\n    } else {\n      return getTypeAnnotationBindingConstantViolations(\n        binding,\n        this,\n        node.name,\n      );\n    }\n  }\n\n  // built-in values\n  if (node.name === \"undefined\") {\n    return voidTypeAnnotation();\n  } else if (node.name === \"NaN\" || node.name === \"Infinity\") {\n    return numberTypeAnnotation();\n  } else if (node.name === \"arguments\") {\n    // todo\n  }\n}\n\nfunction getTypeAnnotationBindingConstantViolations(\n  binding: Binding,\n  path: NodePath<t.Identifier>,\n  name: string,\n) {\n  const types = [];\n\n  const functionConstantViolations: NodePath[] = [];\n  let constantViolations = getConstantViolationsBefore(\n    binding,\n    path,\n    functionConstantViolations,\n  );\n\n  const testType = getConditionalAnnotation(binding, path, name);\n  if (testType) {\n    const testConstantViolations = getConstantViolationsBefore(\n      binding,\n      testType.ifStatement,\n    );\n\n    // remove constant violations observed before the IfStatement\n    constantViolations = constantViolations.filter(\n      path => testConstantViolations.indexOf(path) < 0,\n    );\n\n    // clear current types and add in observed test type\n    types.push(testType.typeAnnotation);\n  }\n\n  if (constantViolations.length) {\n    // pick one constant from each scope which will represent the last possible\n    // control flow path that it could've taken/been\n    /* This code is broken for the following problems:\n     * It thinks that assignments can only happen in scopes.\n     * What about conditionals, if statements without block,\n     * or guarded assignments.\n     * It also checks to see if one of the assignments is in the\n     * same scope and uses that as the only \"violation\". However,\n     * the binding is returned by `getConstantViolationsBefore` so we for\n     * sure always going to return that as the only \"violation\".\n    let rawConstantViolations = constantViolations.reverse();\n    let visitedScopes = [];\n    constantViolations = [];\n    for (let violation of (rawConstantViolations: Array<NodePath>)) {\n      let violationScope = violation.scope;\n      if (visitedScopes.indexOf(violationScope) >= 0) continue;\n\n      visitedScopes.push(violationScope);\n      constantViolations.push(violation);\n\n      if (violationScope === path.scope) {\n        constantViolations = [violation];\n        break;\n      }\n    }*/\n\n    // add back on function constant violations since we can't track calls\n    constantViolations.push(...functionConstantViolations);\n\n    // push on inferred types of violated paths\n    for (const violation of constantViolations) {\n      types.push(violation.getTypeAnnotation());\n    }\n  }\n\n  if (!types.length) {\n    return;\n  }\n\n  return createUnionType(types);\n}\n\nfunction getConstantViolationsBefore(\n  binding: Binding,\n  path: NodePath,\n  functions?: NodePath[],\n) {\n  const violations = binding.constantViolations.slice();\n  violations.unshift(binding.path);\n  return violations.filter(violation => {\n    violation = violation.resolve();\n    const status = violation._guessExecutionStatusRelativeTo(path);\n    if (functions && status === \"unknown\") functions.push(violation);\n    return status === \"before\";\n  });\n}\n\nfunction inferAnnotationFromBinaryExpression(\n  name: string,\n  path: NodePath<t.BinaryExpression>,\n) {\n  const operator = path.node.operator;\n\n  const right = path.get(\"right\").resolve();\n  const left = path.get(\"left\").resolve();\n\n  let target;\n  if (left.isIdentifier({ name })) {\n    target = right;\n  } else if (right.isIdentifier({ name })) {\n    target = left;\n  }\n\n  if (target) {\n    if (operator === \"===\") {\n      return target.getTypeAnnotation();\n    }\n    if (BOOLEAN_NUMBER_BINARY_OPERATORS.indexOf(operator) >= 0) {\n      return numberTypeAnnotation();\n    }\n\n    return;\n  }\n\n  if (operator !== \"===\" && operator !== \"==\") return;\n\n  //\n  let typeofPath: NodePath<t.UnaryExpression>;\n  let typePath: NodePath<t.Expression>;\n  if (left.isUnaryExpression({ operator: \"typeof\" })) {\n    typeofPath = left;\n    typePath = right as NodePath<t.Expression>;\n  } else if (right.isUnaryExpression({ operator: \"typeof\" })) {\n    typeofPath = right;\n    typePath = left as NodePath<t.Expression>;\n  }\n\n  if (!typeofPath) return;\n  // and that the argument of the typeof path references us!\n  if (!typeofPath.get(\"argument\").isIdentifier({ name })) return;\n\n  // ensure that the type path is a Literal\n  typePath = typePath.resolve() as NodePath<t.Expression>;\n  if (!typePath.isLiteral()) return;\n\n  // and that it's a string so we can infer it\n  // @ts-expect-error todo(flow->ts): value is not defined for NullLiteral and some other\n  const typeValue = typePath.node.value;\n  if (typeof typeValue !== \"string\") return;\n\n  // turn type value into a type annotation\n  // @ts-expect-error todo(flow->ts): move validation from helper or relax type constraint to just a string\n  return createTypeAnnotationBasedOnTypeof(typeValue);\n}\n\nfunction getParentConditionalPath(\n  binding: Binding,\n  path: NodePath,\n  name: string,\n) {\n  let parentPath;\n  while ((parentPath = path.parentPath)) {\n    if (parentPath.isIfStatement() || parentPath.isConditionalExpression()) {\n      if (path.key === \"test\") {\n        return;\n      }\n\n      return parentPath as NodePath<t.IfStatement | t.ConditionalExpression>;\n    }\n    if (parentPath.isFunction()) {\n      if (parentPath.parentPath.scope.getBinding(name) !== binding) return;\n    }\n\n    path = parentPath;\n  }\n}\n\nfunction getConditionalAnnotation<T extends t.Node>(\n  binding: Binding,\n  path: NodePath<T>,\n  name?: string,\n): {\n  typeAnnotation: t.FlowType | t.TSType;\n  ifStatement: NodePath<t.IfStatement | t.ConditionalExpression>;\n} {\n  const ifStatement = getParentConditionalPath(binding, path, name);\n  if (!ifStatement) return;\n\n  const test = ifStatement.get(\"test\");\n  const paths = [test];\n  const types = [];\n\n  for (let i = 0; i < paths.length; i++) {\n    const path = paths[i];\n\n    if (path.isLogicalExpression()) {\n      if (path.node.operator === \"&&\") {\n        paths.push(path.get(\"left\"));\n        paths.push(path.get(\"right\"));\n      }\n    } else if (path.isBinaryExpression()) {\n      const type = inferAnnotationFromBinaryExpression(name, path);\n      if (type) types.push(type);\n    }\n  }\n\n  if (types.length) {\n    return {\n      typeAnnotation: createUnionType(types),\n      ifStatement,\n    };\n  }\n\n  return getConditionalAnnotation(binding, ifStatement, name);\n}\n"], "mappings": ";;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AASA,IAAAC,KAAA,GAAAD,OAAA;AAAyC;EARvCE,+BAA+B;EAC/BC,iCAAiC;EACjCC,oBAAoB;EACpBC;AAAkB,IAAAN,EAAA;AAOL,SAAAO,SAAwCC,IAAkB,EAAE;EACzE,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE,EAAE;EAI1B,MAAMC,OAAO,GAAG,IAAI,CAACC,KAAK,CAACC,UAAU,CAACJ,IAAI,CAACK,IAAI,CAAC;EAChD,IAAIH,OAAO,EAAE;IACX,IAAIA,OAAO,CAACI,UAAU,CAACC,cAAc,EAAE;MACrC,OAAOL,OAAO,CAACI,UAAU,CAACC,cAAc;IAC1C,CAAC,MAAM;MACL,OAAOC,0CAA0C,CAC/CN,OAAO,EACP,IAAI,EACJF,IAAI,CAACK,IAAI,CACV;IACH;EACF;EAGA,IAAIL,IAAI,CAACK,IAAI,KAAK,WAAW,EAAE;IAC7B,OAAOP,kBAAkB,EAAE;EAC7B,CAAC,MAAM,IAAIE,IAAI,CAACK,IAAI,KAAK,KAAK,IAAIL,IAAI,CAACK,IAAI,KAAK,UAAU,EAAE;IAC1D,OAAOR,oBAAoB,EAAE;EAC/B,CAAC,MAAM,IAAIG,IAAI,CAACK,IAAI,KAAK,WAAW,EAAE,CAEtC;AACF;AAEA,SAASG,0CAA0CA,CACjDN,OAAgB,EAChBO,IAA4B,EAC5BJ,IAAY,EACZ;EACA,MAAMK,KAAK,GAAG,EAAE;EAEhB,MAAMC,0BAAsC,GAAG,EAAE;EACjD,IAAIC,kBAAkB,GAAGC,2BAA2B,CAClDX,OAAO,EACPO,IAAI,EACJE,0BAA0B,CAC3B;EAED,MAAMG,QAAQ,GAAGC,wBAAwB,CAACb,OAAO,EAAEO,IAAI,EAAEJ,IAAI,CAAC;EAC9D,IAAIS,QAAQ,EAAE;IACZ,MAAME,sBAAsB,GAAGH,2BAA2B,CACxDX,OAAO,EACPY,QAAQ,CAACG,WAAW,CACrB;IAGDL,kBAAkB,GAAGA,kBAAkB,CAACM,MAAM,CAC5CT,IAAI,IAAIO,sBAAsB,CAACG,OAAO,CAACV,IAAI,CAAC,GAAG,CAAC,CACjD;IAGDC,KAAK,CAACU,IAAI,CAACN,QAAQ,CAACP,cAAc,CAAC;EACrC;EAEA,IAAIK,kBAAkB,CAACS,MAAM,EAAE;IA4B7BT,kBAAkB,CAACQ,IAAI,CAAC,GAAGT,0BAA0B,CAAC;IAGtD,KAAK,MAAMW,SAAS,IAAIV,kBAAkB,EAAE;MAC1CF,KAAK,CAACU,IAAI,CAACE,SAAS,CAACC,iBAAiB,EAAE,CAAC;IAC3C;EACF;EAEA,IAAI,CAACb,KAAK,CAACW,MAAM,EAAE;IACjB;EACF;EAEA,OAAO,IAAAG,qBAAe,EAACd,KAAK,CAAC;AAC/B;AAEA,SAASG,2BAA2BA,CAClCX,OAAgB,EAChBO,IAAc,EACdgB,SAAsB,EACtB;EACA,MAAMC,UAAU,GAAGxB,OAAO,CAACU,kBAAkB,CAACe,KAAK,EAAE;EACrDD,UAAU,CAACE,OAAO,CAAC1B,OAAO,CAACO,IAAI,CAAC;EAChC,OAAOiB,UAAU,CAACR,MAAM,CAACI,SAAS,IAAI;IACpCA,SAAS,GAAGA,SAAS,CAACO,OAAO,EAAE;IAC/B,MAAMC,MAAM,GAAGR,SAAS,CAACS,+BAA+B,CAACtB,IAAI,CAAC;IAC9D,IAAIgB,SAAS,IAAIK,MAAM,KAAK,SAAS,EAAEL,SAAS,CAACL,IAAI,CAACE,SAAS,CAAC;IAChE,OAAOQ,MAAM,KAAK,QAAQ;EAC5B,CAAC,CAAC;AACJ;AAEA,SAASE,mCAAmCA,CAC1C3B,IAAY,EACZI,IAAkC,EAClC;EACA,MAAMwB,QAAQ,GAAGxB,IAAI,CAACT,IAAI,CAACiC,QAAQ;EAEnC,MAAMC,KAAK,GAAGzB,IAAI,CAAC0B,GAAG,CAAC,OAAO,CAAC,CAACN,OAAO,EAAE;EACzC,MAAMO,IAAI,GAAG3B,IAAI,CAAC0B,GAAG,CAAC,MAAM,CAAC,CAACN,OAAO,EAAE;EAEvC,IAAIQ,MAAM;EACV,IAAID,IAAI,CAACE,YAAY,CAAC;IAAEjC;EAAK,CAAC,CAAC,EAAE;IAC/BgC,MAAM,GAAGH,KAAK;EAChB,CAAC,MAAM,IAAIA,KAAK,CAACI,YAAY,CAAC;IAAEjC;EAAK,CAAC,CAAC,EAAE;IACvCgC,MAAM,GAAGD,IAAI;EACf;EAEA,IAAIC,MAAM,EAAE;IACV,IAAIJ,QAAQ,KAAK,KAAK,EAAE;MACtB,OAAOI,MAAM,CAACd,iBAAiB,EAAE;IACnC;IACA,IAAI5B,+BAA+B,CAACwB,OAAO,CAACc,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC1D,OAAOpC,oBAAoB,EAAE;IAC/B;IAEA;EACF;EAEA,IAAIoC,QAAQ,KAAK,KAAK,IAAIA,QAAQ,KAAK,IAAI,EAAE;EAG7C,IAAIM,UAAuC;EAC3C,IAAIC,QAAgC;EACpC,IAAIJ,IAAI,CAACK,iBAAiB,CAAC;IAAER,QAAQ,EAAE;EAAS,CAAC,CAAC,EAAE;IAClDM,UAAU,GAAGH,IAAI;IACjBI,QAAQ,GAAGN,KAA+B;EAC5C,CAAC,MAAM,IAAIA,KAAK,CAACO,iBAAiB,CAAC;IAAER,QAAQ,EAAE;EAAS,CAAC,CAAC,EAAE;IAC1DM,UAAU,GAAGL,KAAK;IAClBM,QAAQ,GAAGJ,IAA8B;EAC3C;EAEA,IAAI,CAACG,UAAU,EAAE;EAEjB,IAAI,CAACA,UAAU,CAACJ,GAAG,CAAC,UAAU,CAAC,CAACG,YAAY,CAAC;IAAEjC;EAAK,CAAC,CAAC,EAAE;EAGxDmC,QAAQ,GAAGA,QAAQ,CAACX,OAAO,EAA4B;EACvD,IAAI,CAACW,QAAQ,CAACE,SAAS,EAAE,EAAE;EAI3B,MAAMC,SAAS,GAAGH,QAAQ,CAACxC,IAAI,CAAC4C,KAAK;EACrC,IAAI,OAAOD,SAAS,KAAK,QAAQ,EAAE;EAInC,OAAO/C,iCAAiC,CAAC+C,SAAS,CAAC;AACrD;AAEA,SAASE,wBAAwBA,CAC/B3C,OAAgB,EAChBO,IAAc,EACdJ,IAAY,EACZ;EACA,IAAIyC,UAAU;EACd,OAAQA,UAAU,GAAGrC,IAAI,CAACqC,UAAU,EAAG;IACrC,IAAIA,UAAU,CAACC,aAAa,EAAE,IAAID,UAAU,CAACE,uBAAuB,EAAE,EAAE;MACtE,IAAIvC,IAAI,CAACwC,GAAG,KAAK,MAAM,EAAE;QACvB;MACF;MAEA,OAAOH,UAAU;IACnB;IACA,IAAIA,UAAU,CAACI,UAAU,EAAE,EAAE;MAC3B,IAAIJ,UAAU,CAACA,UAAU,CAAC3C,KAAK,CAACC,UAAU,CAACC,IAAI,CAAC,KAAKH,OAAO,EAAE;IAChE;IAEAO,IAAI,GAAGqC,UAAU;EACnB;AACF;AAEA,SAAS/B,wBAAwBA,CAC/Bb,OAAgB,EAChBO,IAAiB,EACjBJ,IAAa,EAIb;EACA,MAAMY,WAAW,GAAG4B,wBAAwB,CAAC3C,OAAO,EAAEO,IAAI,EAAEJ,IAAI,CAAC;EACjE,IAAI,CAACY,WAAW,EAAE;EAElB,MAAMkC,IAAI,GAAGlC,WAAW,CAACkB,GAAG,CAAC,MAAM,CAAC;EACpC,MAAMiB,KAAK,GAAG,CAACD,IAAI,CAAC;EACpB,MAAMzC,KAAK,GAAG,EAAE;EAEhB,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAAC/B,MAAM,EAAEgC,CAAC,EAAE,EAAE;IACrC,MAAM5C,IAAI,GAAG2C,KAAK,CAACC,CAAC,CAAC;IAErB,IAAI5C,IAAI,CAAC6C,mBAAmB,EAAE,EAAE;MAC9B,IAAI7C,IAAI,CAACT,IAAI,CAACiC,QAAQ,KAAK,IAAI,EAAE;QAC/BmB,KAAK,CAAChC,IAAI,CAACX,IAAI,CAAC0B,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5BiB,KAAK,CAAChC,IAAI,CAACX,IAAI,CAAC0B,GAAG,CAAC,OAAO,CAAC,CAAC;MAC/B;IACF,CAAC,MAAM,IAAI1B,IAAI,CAAC8C,kBAAkB,EAAE,EAAE;MACpC,MAAMC,IAAI,GAAGxB,mCAAmC,CAAC3B,IAAI,EAAEI,IAAI,CAAC;MAC5D,IAAI+C,IAAI,EAAE9C,KAAK,CAACU,IAAI,CAACoC,IAAI,CAAC;IAC5B;EACF;EAEA,IAAI9C,KAAK,CAACW,MAAM,EAAE;IAChB,OAAO;MACLd,cAAc,EAAE,IAAAiB,qBAAe,EAACd,KAAK,CAAC;MACtCO;IACF,CAAC;EACH;EAEA,OAAOF,wBAAwB,CAACb,OAAO,EAAEe,WAAW,EAAEZ,IAAI,CAAC;AAC7D"}