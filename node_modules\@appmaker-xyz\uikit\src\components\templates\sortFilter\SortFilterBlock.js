import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { usePageState } from '@appmaker-xyz/core';
import { spacing, color } from '../../../styles';
import { Layout } from '../../../components';
import SortFilterButtons from './components/Buttons';
import FilterModal from './components/FilterModal';
import { createFilterStore, FilterProvider } from './store';
import SortModal from './components/sortModal/index';

const SortFilter = (props) => {
  const { attributes, coreDispatch } = props;
  const [filterVisible, setFilterVisible] = useState(false);
  const [sortVisible, setSortVisible] = useState(false);

  let pageState = usePageState((state) => state);
  props.attributes.defaultFilterItem = pageState?.filter || {
    app_filter: {},
  };
  const openFilter = () => setFilterVisible(true);
  const closeFilter = () => {
    setFilterVisible(false);
    // props.attributes.defaultFilterItem = { app_filter: {} };
  };

  const openSort = () => setSortVisible(true);
  const closeSort = () => setSortVisible(false);
  // console.log(JSON.stringify(attributes?.sort_data,null,2));
  return props.attributes?.filter_data.length > 0 ? (
    <FilterProvider
      createStore={createFilterStore({
        avilableFilters: attributes?.filter_data,
        avilableSortItems: attributes?.sort_data,
      })}>
      <Layout style={styles.view}>
        <SortFilterButtons
          attributes={{
            show_filter: attributes.show_filter,
            show_sort: attributes.show_sort,
            largeType: attributes.largeType,
            noIcon: attributes.noIcon,
            bottomPlacement: attributes.bottomPlacement,
          }}
          openSort={openSort}
          openFilter={openFilter}
        />
        <FilterModal
          filterVisible={filterVisible}
          closeFilter={closeFilter}
          avilableFilters={attributes.filter_data}
          coreDispatch={coreDispatch}
        />
        <SortModal
          sortVisible={sortVisible}
          closeSort={closeSort}
          coreDispatch={coreDispatch}
          attributes={{ bottomPlacement: attributes.bottomPlacement }}
        />
      </Layout>
    </FilterProvider>
  ) : null;
};

const styles = StyleSheet.create({
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    borderBottomColor: color.light,
    borderBottomWidth: 1,
  },
  innerContainer: {
    backgroundColor: color.white,
    flex: 1,
    borderRightColor: color.light,
    borderRightWidth: 1,
  },
  view: {
    justifyContent: 'flex-end',
    marginVertical: 0,
    marginHorizontal: 1,
  },
  modalBody: {
    backgroundColor: color.white,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
    flexDirection: 'column',
    height: '85%',
  },
  modalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: color.light,
    padding: spacing.base,
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
  },
  modalContent: {
    padding: spacing.md,
    flexDirection: 'column',
    width: '100%',
  },
  modalFooter: {
    width: '100%',
    backgroundColor: color.white,
    padding: spacing.base,
  },
  headerItems: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  centerView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  sortModalBody: {
    width: '80%',
    backgroundColor: color.white,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: spacing.small,
    flexDirection: 'column',
  },
  sortModalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: color.light,
    padding: spacing.base,
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
  },
  sortModalContent: {
    width: '100%',
    padding: spacing.base,
  },
  sortItem: {
    marginVertical: spacing.nano,
  },
  sliderContainer: {
    alignItems: 'center',
    paddingVertical: spacing.base,
  },
  sliderLabels: {
    flexDirection: 'row',
    width: 310,
    justifyContent: 'space-between',
  },
  trackStyle: {
    borderRadius: spacing.small,
    height: spacing.nano / 2,
  },
  slideSelectedStyle: {
    backgroundColor: color.primary,
  },
  markerStyle: {
    height: 30,
    width: 30,
    borderRadius: spacing.md,
    backgroundColor: color.light,
    borderWidth: 0.5,
    borderColor: color.demiDark,
  },
  markerPressed: {
    backgroundColor: color.grey,
  },
});

export default SortFilter;
