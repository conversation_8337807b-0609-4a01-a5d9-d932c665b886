import { getCartSum, hasVariantInCart } from '../lib/functionHelper';
class Cart {
  lineItems = [];
  addToCart = ({ lineItem_id, product_id, variant_id, quantity, amount }) => {
    const randomId = Math.random().toString(36).substring(2, 15);
    const item = {
      id:
        lineItem_id ||
        `gid://shopify/CheckoutLineItem/${product_id}-${randomId}`,
      variant: {
        id: `gid://shopify/ProductVariant/${variant_id}`,
        product: {
          id: `gid://shopify/Product/${product_id}`,
        },
        price: {
          amount: amount,
        },
      },
      quantity,
    };
    this.lineItems.push({ node: item });
    return this;
  };
  removeFromCart = (variantId) => {
    this.lineItems = this.lineItems.filter(
      (item) => item.node.variant.id !== variantId,
    );
    return this;
  };
  updateQuantity = (variantId, quantity) => {
    const item = this.lineItems.find(
      (_item) => _item.node.variant.id === variantId,
    );
    item.node.quantity = quantity;
    return this;
  };
  getLineItems = () => {
    return this.lineItems;
  };
  getCart() {
    return {
      lineItems: this.lineItems,
      totalPrice: {
        amount: this.lineItems.reduce(
          (acc, item) =>
            acc + item.node.variant.price.amount * item.node.quantity,
          0,
        ),
      },
      // total: this.lineItems.reduce((acc, item) => {
      //     const price = item.variant.node.price.amount;
      //     const quantity = item.quantity;
      //     return acc + price * quantity;
      // }, 0),
    };
  }
}
describe('getCartSum', () => {
  it('cartSum', () => {
    const currentCart = new Cart()
      .addToCart({
        product: '39829144436849',
        variant: '39829144436849',
        quantity: 1,
        amount: 100,
      })
      .getCart();
    const sum = getCartSum({
      lineItemsToAdd: [],
      currentCart,
    });
    expect(sum).toBe(100);
  });
  it('line items to add', () => {
    const currentCart = new Cart()
      .addToCart({
        product: '39829144436849',
        variant: '39829144436849',
        quantity: 1,
        amount: 200,
      })
      .addToCart({
        product: '39829144436841',
        variant: '39829144436845',
        quantity: 4,
        amount: 200,
      })
      .getCart();
    const sum = getCartSum({
      lineItemsToAdd: [
        {
          variantId: '39829144436849',
          quantity: 4,
          variant: {
            node: {
              price: {
                amount: 100,
              },
            },
          },
        },
      ],
      currentCart,
    });
    expect(sum).toBe(1400);
  });
  it('line items to update', () => {
    const currentCart = new Cart()
      .addToCart({
        product: '398291444368491',
        variant: '39829144436849',
        quantity: 1,
        amount: 200,
      })
      .addToCart({
        lineItem_id: 'lin-item-id-2',
        product: '39829144436841',
        variant: '39829144436845',
        quantity: 5,
        amount: 200,
      })
      .getCart();
    const lineItemsToUpdate = [
      {
        id: 'lin-item-id-2',
        variantId: '39829144436845',
        quantity: 2,
        variant: {
          //   node: {
          price: {
            amount: 200,
          },
          //   },
        },
      },
    ];
    const sum = getCartSum({
      lineItemsToUpdate,
      currentCart,
    });
    expect(sum).toBe(1200 + (2 - 5) * 200);
  });
});
