import React from 'react';
import { View } from 'react-native';
import ThemeText from '../atoms/ThemeText';

const Badge = ({
  color = '#000',
  textColor = '#fff',
  children,
  containerStyle,
  textStyle,
}) => {
  const finalContainerStyles = {
    backgroundColor: color,
    paddingHorizontal: 4,
    flexDirection: 'row',
    alignItems: 'center',
    textAlign: 'center',
    alignSelf: 'flex-start',
    ...containerStyle,
  };
  const finalTextStyles = {
    ...textStyle,
  };
  return (
    <View style={finalContainerStyles}>
      <ThemeText color={textColor} size="sm" style={finalTextStyles}>
        {children}
      </ThemeText>
    </View>
  );
};

export default Badge;
