import React, { useState } from 'react';
import { Input } from '@appmaker-xyz/ui';
import { Control, Controller } from 'react-hook-form';
import { TextInputProps } from 'react-native';

type Props = {
  control: Control;
  name: string;
  label: string;
  style?: any;
} & TextInputProps;
export function InputFormItem({
  control,
  name,
  label,
  style = {},
  ...props
}: Props) {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { onChange, value, onBlur,ref }, fieldState }) => (
        <Input
          autoCapitalize="none"
          placeholder={label}
          value={value}
          onChangeText={onChange}
          style={style ? style : {}}
          error={fieldState.error && fieldState.error.message}
          inputRef={ref}
          {...props}
        />
      )}
    />
  );
}
