import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { Layout, AppmakerText } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import { Button } from '../../molecules/index';

const AddressDisplay = ({
  attributes = {},
  onPress,
  onEditPress,
  onAction,
  coreDispatch,
}) => {
  const { title, subTitle, appmakerAction, appmakerEditAction, loading } =
    attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  let onPressEditHandle = onEditPress;
  if (appmakerAction && onAction) {
    onPressEditHandle = () => onAction(appmakerEditAction);
  }

  return (
    <Layout style={styles.container}>
      <Layout style={styles.textContainer}>
        <AppmakerText category="actionTitle">{title}</AppmakerText>
        {subTitle ? (
          <AppmakerText category="bodyParagraphRegular" numberOfLines={1}>
            {subTitle}
          </AppmakerText>
        ) : null}
      </Layout>
      <Button
        small
        outline
        status="demiDark"
        onPress={onPressHandle}
        loading={loading}>
        {subTitle ? 'Change' : 'Choose'}
      </Button>
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: spacing.base,
      paddingVertical: spacing.base,
      backgroundColor: color.white,
      position: 'relative',
      // marginBottom: spacing.nano,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      // flex: 1,
      borderTopColor: color.light,
      borderTopWidth: 1,
    },
    textContainer: { marginRight: 4, flex: 1 },
  });

export default AddressDisplay;
