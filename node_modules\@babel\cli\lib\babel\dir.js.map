{"version": 3, "names": ["_slash", "data", "require", "_path", "_fs", "util", "watcher", "asyncGeneratorStep", "gen", "resolve", "reject", "_next", "_throw", "key", "arg", "info", "value", "error", "done", "Promise", "then", "_asyncToGenerator", "fn", "self", "args", "arguments", "apply", "err", "undefined", "FILE_TYPE", "Object", "freeze", "NON_COMPILABLE", "COMPILED", "IGNORED", "ERR_COMPILATION", "outputFileSync", "filePath", "v", "w", "split", "process", "versions", "node", "mkdirSync", "sync", "path", "dirname", "recursive", "fs", "writeFileSync", "_default", "_x", "_ref", "cliOptions", "babelOptions", "write", "_x2", "_x3", "_write", "src", "base", "relative", "isCompilableExtension", "extensions", "withExtension", "keepFileExtension", "extname", "outFileExtension", "dest", "getDest", "res", "compile", "assign", "sourceFileName", "slash", "map", "outputMap", "sourceMaps", "hasDataSourcemap", "code", "mapLoc", "addSourceMappingUrl", "file", "basename", "JSON", "stringify", "chmod", "verbose", "console", "log", "cwd", "watch", "filename", "join", "outDir", "handleFile", "_x4", "_x5", "_handleFile", "written", "copyFiles", "copyIgnored", "readFileSync", "handle", "_x6", "_handle", "filenameOrDir", "existsSync", "stat", "statSync", "isDirectory", "count", "files", "readdir", "includeDotfiles", "compiledFiles", "startTime", "logSuccess", "debounce", "diff", "hrtime", "Math", "round", "enable", "enableGlobbing", "skipInitialBuild", "deleteDirOnStart", "deleteDir", "filenames", "quiet", "flush", "processing", "getBase", "length", "absoluteBase", "filenameToBaseMap", "Map", "absoluteFilename", "absoluteFilenames", "sep", "get", "absoluteFilenameOrDir", "startsWith", "set", "for<PERSON>ach", "startWatcher", "onFilesChange", "all", "filter", "Boolean"], "sources": ["../../src/babel/dir.ts"], "sourcesContent": ["import slash from \"slash\";\nimport path from \"path\";\nimport fs from \"fs\";\n\nimport * as util from \"./util.ts\";\nimport * as watcher from \"./watcher.ts\";\nimport type { CmdOptions } from \"./options.ts\";\n\nconst FILE_TYPE = Object.freeze({\n  NON_COMPILABLE: \"NON_COMPILABLE\",\n  COMPILED: \"COMPILED\",\n  IGNORED: \"IGNORED\",\n  ERR_COMPILATION: \"ERR_COMPILATION\",\n} as const);\n\nfunction outputFileSync(filePath: string, data: string | Buffer): void {\n  fs.mkdirSync(path.dirname(filePath), { recursive: true });\n  fs.writeFileSync(filePath, data);\n}\n\nexport default async function ({\n  cliOptions,\n  babelOptions,\n}: CmdOptions): Promise<void> {\n  async function write(\n    src: string,\n    base: string,\n  ): Promise<keyof typeof FILE_TYPE> {\n    let relative = path.relative(base, src);\n\n    if (!util.isCompilableExtension(relative, cliOptions.extensions)) {\n      return FILE_TYPE.NON_COMPILABLE;\n    }\n\n    relative = util.withExtension(\n      relative,\n      cliOptions.keepFileExtension\n        ? path.extname(relative)\n        : cliOptions.outFileExtension,\n    );\n\n    const dest = getDest(relative, base);\n\n    try {\n      const res = await util.compile(src, {\n        ...babelOptions,\n        sourceFileName: slash(path.relative(dest + \"/..\", src)),\n      });\n\n      if (!res) return FILE_TYPE.IGNORED;\n\n      if (res.map) {\n        let outputMap: \"both\" | \"external\" | false = false;\n        if (babelOptions.sourceMaps && babelOptions.sourceMaps !== \"inline\") {\n          outputMap = \"external\";\n        } else if (babelOptions.sourceMaps == undefined) {\n          outputMap = util.hasDataSourcemap(res.code) ? \"external\" : \"both\";\n        }\n\n        if (outputMap) {\n          const mapLoc = dest + \".map\";\n          if (outputMap === \"external\") {\n            res.code = util.addSourceMappingUrl(res.code, mapLoc);\n          }\n          res.map.file = path.basename(relative);\n          outputFileSync(mapLoc, JSON.stringify(res.map));\n        }\n      }\n\n      outputFileSync(dest, res.code);\n      util.chmod(src, dest);\n\n      if (cliOptions.verbose) {\n        console.log(path.relative(process.cwd(), src) + \" -> \" + dest);\n      }\n\n      return FILE_TYPE.COMPILED;\n    } catch (err) {\n      if (cliOptions.watch) {\n        console.error(err);\n        return FILE_TYPE.ERR_COMPILATION;\n      }\n\n      throw err;\n    }\n  }\n\n  function getDest(filename: string, base: string): string {\n    if (cliOptions.relative) {\n      return path.join(base, cliOptions.outDir, filename);\n    }\n    return path.join(cliOptions.outDir, filename);\n  }\n\n  async function handleFile(src: string, base: string): Promise<boolean> {\n    const written = await write(src, base);\n\n    if (\n      (cliOptions.copyFiles && written === FILE_TYPE.NON_COMPILABLE) ||\n      (cliOptions.copyIgnored && written === FILE_TYPE.IGNORED)\n    ) {\n      const filename = path.relative(base, src);\n      const dest = getDest(filename, base);\n      outputFileSync(dest, fs.readFileSync(src));\n      util.chmod(src, dest);\n    }\n    return written === FILE_TYPE.COMPILED;\n  }\n\n  async function handle(filenameOrDir: string): Promise<number> {\n    if (!fs.existsSync(filenameOrDir)) return 0;\n\n    const stat = fs.statSync(filenameOrDir);\n\n    if (stat.isDirectory()) {\n      const dirname = filenameOrDir;\n\n      let count = 0;\n\n      const files = util.readdir(dirname, cliOptions.includeDotfiles);\n      for (const filename of files) {\n        const src = path.join(dirname, filename);\n\n        const written = await handleFile(src, dirname);\n        if (written) count += 1;\n      }\n\n      return count;\n    } else {\n      const filename = filenameOrDir;\n      const written = await handleFile(filename, path.dirname(filename));\n\n      return written ? 1 : 0;\n    }\n  }\n\n  let compiledFiles = 0;\n  let startTime: [number, number] | null = null;\n\n  const logSuccess = util.debounce(function () {\n    if (startTime === null) {\n      // This should never happen, but just in case it's better\n      // to ignore the log message rather than making @babel/cli crash.\n      return;\n    }\n\n    const diff = process.hrtime(startTime);\n\n    console.log(\n      `Successfully compiled ${compiledFiles} ${\n        compiledFiles !== 1 ? \"files\" : \"file\"\n      } with Babel (${diff[0] * 1e3 + Math.round(diff[1] / 1e6)}ms).`,\n    );\n    compiledFiles = 0;\n    startTime = null;\n  }, 100);\n\n  if (cliOptions.watch) watcher.enable({ enableGlobbing: true });\n\n  if (!cliOptions.skipInitialBuild) {\n    if (cliOptions.deleteDirOnStart) {\n      util.deleteDir(cliOptions.outDir);\n    }\n\n    fs.mkdirSync(cliOptions.outDir, { recursive: true });\n\n    startTime = process.hrtime();\n\n    for (const filename of cliOptions.filenames) {\n      // compiledFiles is just incremented without reading its value, so we\n      // don't risk race conditions.\n      // eslint-disable-next-line require-atomic-updates\n      compiledFiles += await handle(filename);\n    }\n\n    if (!cliOptions.quiet) {\n      logSuccess();\n      logSuccess.flush();\n    }\n  }\n\n  if (cliOptions.watch) {\n    // This, alongside with debounce, allows us to only log\n    // when we are sure that all the files have been compiled.\n    let processing = 0;\n    const { filenames } = cliOptions;\n    let getBase: (filename: string) => string | null;\n    if (filenames.length === 1) {\n      // fast path: If there is only one filenames, we know it must be the base\n      const base = filenames[0];\n      const absoluteBase = path.resolve(base);\n      getBase = filename => {\n        return filename === absoluteBase ? path.dirname(base) : base;\n      };\n    } else {\n      // A map from absolute compiled file path to its base, from which\n      // the output destination will be determined\n      const filenameToBaseMap: Map<string, string> = new Map(\n        filenames.map(filename => {\n          const absoluteFilename = path.resolve(filename);\n          return [absoluteFilename, path.dirname(filename)];\n        }),\n      );\n\n      const absoluteFilenames: Map<string, string> = new Map(\n        filenames.map(filename => {\n          const absoluteFilename = path.resolve(filename);\n          return [absoluteFilename, filename];\n        }),\n      );\n\n      const { sep } = path;\n      // determine base from the absolute file path\n      getBase = filename => {\n        const base = filenameToBaseMap.get(filename);\n        if (base !== undefined) {\n          return base;\n        }\n        for (const [absoluteFilenameOrDir, relative] of absoluteFilenames) {\n          if (filename.startsWith(absoluteFilenameOrDir + sep)) {\n            filenameToBaseMap.set(filename, relative);\n            return relative;\n          }\n        }\n        // Can't determine the base, probably external deps\n        return \"\";\n      };\n    }\n\n    filenames.forEach(filenameOrDir => {\n      watcher.watch(filenameOrDir);\n    });\n\n    watcher.startWatcher();\n\n    watcher.onFilesChange(async filenames => {\n      processing++;\n      if (startTime === null) startTime = process.hrtime();\n\n      try {\n        const written = await Promise.all(\n          filenames.map(filename => handleFile(filename, getBase(filename))),\n        );\n\n        compiledFiles += written.filter(Boolean).length;\n      } catch (err) {\n        console.error(err);\n      }\n\n      processing--;\n      if (processing === 0 && !cliOptions.quiet) logSuccess();\n    });\n  }\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAI,IAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AAAwC,SAAAK,mBAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAC,GAAA,cAAAC,IAAA,GAAAP,GAAA,CAAAK,GAAA,EAAAC,GAAA,OAAAE,KAAA,GAAAD,IAAA,CAAAC,KAAA,WAAAC,KAAA,IAAAP,MAAA,CAAAO,KAAA,iBAAAF,IAAA,CAAAG,IAAA,IAAAT,OAAA,CAAAO,KAAA,YAAAG,OAAA,CAAAV,OAAA,CAAAO,KAAA,EAAAI,IAAA,CAAAT,KAAA,EAAAC,MAAA;AAAA,SAAAS,kBAAAC,EAAA,6BAAAC,IAAA,SAAAC,IAAA,GAAAC,SAAA,aAAAN,OAAA,WAAAV,OAAA,EAAAC,MAAA,QAAAF,GAAA,GAAAc,EAAA,CAAAI,KAAA,CAAAH,IAAA,EAAAC,IAAA,YAAAb,MAAAK,KAAA,IAAAT,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,UAAAI,KAAA,cAAAJ,OAAAe,GAAA,IAAApB,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,WAAAe,GAAA,KAAAhB,KAAA,CAAAiB,SAAA;AAGxC,MAAMC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC;EAC9BC,cAAc,EAAE,gBAAgB;EAChCC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,SAAS;EAClBC,eAAe,EAAE;AACnB,CAAU,CAAC;AAEX,SAASC,cAAcA,CAACC,QAAgB,EAAEpC,IAAqB,EAAQ;EACrE,GAAAqC,CAAA,EAAAC,CAAA,MAAAD,CAAA,GAAAA,CAAA,CAAAE,KAAA,OAAAD,CAAA,GAAAA,CAAA,CAAAC,KAAA,QAAAF,CAAA,OAAAC,CAAA,OAAAD,CAAA,OAAAC,CAAA,QAAAD,CAAA,QAAAC,CAAA,MAAAE,OAAA,CAAAC,QAAA,CAAAC,IAAA,aAAAvC,GAAA,GAAAwC,SAAA,GAAA1C,OAAA,aAAA2C,IAAA,EAAaC,MAAGA,CAAC,CAACC,OAAO,CAACV,QAAQ,CAAC,EAAE;IAAEW,SAAS,EAAE;EAAK,CAAC,CAAC;EACzDC,IAACA,CAAC,CAACC,aAAa,CAACb,QAAQ,EAAEpC,IAAI,CAAC;AAClC;AAAC,SAAAkD,SAAAC,EAAA;EAAA,OAAAC,IAAA,CAAA3B,KAAA,OAAAD,SAAA;AAAA;AAAA,SAAA4B,KAAA;EAAAA,IAAA,GAAAhC,iBAAA,CAEc,WAAgB;IAC7BiC,UAAU;IACVC;EACU,CAAC,EAAiB;IAAA,SACbC,KAAKA,CAAAC,GAAA,EAAAC,GAAA;MAAA,OAAAC,MAAA,CAAAjC,KAAA,OAAAD,SAAA;IAAA;IAAA,SAAAkC,OAAA;MAAAA,MAAA,GAAAtC,iBAAA,CAApB,WACEuC,GAAW,EACXC,IAAY,EACqB;QACjC,IAAIC,QAAQ,GAAGhB,MAAGA,CAAC,CAACgB,QAAQ,CAACD,IAAI,EAAED,GAAG,CAAC;QAEvC,IAAI,CAACvD,IAAI,CAAC0D,qBAAqB,CAACD,QAAQ,EAAER,UAAU,CAACU,UAAU,CAAC,EAAE;UAChE,OAAOnC,SAAS,CAACG,cAAc;QACjC;QAEA8B,QAAQ,GAAGzD,IAAI,CAAC4D,aAAa,CAC3BH,QAAQ,EACRR,UAAU,CAACY,iBAAiB,GACxBpB,MAAGA,CAAC,CAACqB,OAAO,CAACL,QAAQ,CAAC,GACtBR,UAAU,CAACc,gBACjB,CAAC;QAED,MAAMC,IAAI,GAAGC,OAAO,CAACR,QAAQ,EAAED,IAAI,CAAC;QAEpC,IAAI;UACF,MAAMU,GAAG,SAASlE,IAAI,CAACmE,OAAO,CAACZ,GAAG,EAAA9B,MAAA,CAAA2C,MAAA,KAC7BlB,YAAY;YACfmB,cAAc,EAAEC,OAAIA,CAAC,CAAC7B,MAAGA,CAAC,CAACgB,QAAQ,CAACO,IAAI,GAAG,KAAK,EAAET,GAAG,CAAC;UAAC,EACxD,CAAC;UAEF,IAAI,CAACW,GAAG,EAAE,OAAO1C,SAAS,CAACK,OAAO;UAElC,IAAIqC,GAAG,CAACK,GAAG,EAAE;YACX,IAAIC,SAAsC,GAAG,KAAK;YAClD,IAAItB,YAAY,CAACuB,UAAU,IAAIvB,YAAY,CAACuB,UAAU,KAAK,QAAQ,EAAE;cACnED,SAAS,GAAG,UAAU;YACxB,CAAC,MAAM,IAAItB,YAAY,CAACuB,UAAU,IAAIlD,SAAS,EAAE;cAC/CiD,SAAS,GAAGxE,IAAI,CAAC0E,gBAAgB,CAACR,GAAG,CAACS,IAAI,CAAC,GAAG,UAAU,GAAG,MAAM;YACnE;YAEA,IAAIH,SAAS,EAAE;cACb,MAAMI,MAAM,GAAGZ,IAAI,GAAG,MAAM;cAC5B,IAAIQ,SAAS,KAAK,UAAU,EAAE;gBAC5BN,GAAG,CAACS,IAAI,GAAG3E,IAAI,CAAC6E,mBAAmB,CAACX,GAAG,CAACS,IAAI,EAAEC,MAAM,CAAC;cACvD;cACAV,GAAG,CAACK,GAAG,CAACO,IAAI,GAAGrC,MAAGA,CAAC,CAACsC,QAAQ,CAACtB,QAAQ,CAAC;cACtC1B,cAAc,CAAC6C,MAAM,EAAEI,IAAI,CAACC,SAAS,CAACf,GAAG,CAACK,GAAG,CAAC,CAAC;YACjD;UACF;UAEAxC,cAAc,CAACiC,IAAI,EAAEE,GAAG,CAACS,IAAI,CAAC;UAC9B3E,IAAI,CAACkF,KAAK,CAAC3B,GAAG,EAAES,IAAI,CAAC;UAErB,IAAIf,UAAU,CAACkC,OAAO,EAAE;YACtBC,OAAO,CAACC,GAAG,CAAC5C,MAAGA,CAAC,CAACgB,QAAQ,CAACrB,OAAO,CAACkD,GAAG,CAAC,CAAC,EAAE/B,GAAG,CAAC,GAAG,MAAM,GAAGS,IAAI,CAAC;UAChE;UAEA,OAAOxC,SAAS,CAACI,QAAQ;QAC3B,CAAC,CAAC,OAAON,GAAG,EAAE;UACZ,IAAI2B,UAAU,CAACsC,KAAK,EAAE;YACpBH,OAAO,CAACxE,KAAK,CAACU,GAAG,CAAC;YAClB,OAAOE,SAAS,CAACM,eAAe;UAClC;UAEA,MAAMR,GAAG;QACX;MACF,CAAC;MAAA,OAAAgC,MAAA,CAAAjC,KAAA,OAAAD,SAAA;IAAA;IAED,SAAS6C,OAAOA,CAACuB,QAAgB,EAAEhC,IAAY,EAAU;MACvD,IAAIP,UAAU,CAACQ,QAAQ,EAAE;QACvB,OAAOhB,MAAGA,CAAC,CAACgD,IAAI,CAACjC,IAAI,EAAEP,UAAU,CAACyC,MAAM,EAAEF,QAAQ,CAAC;MACrD;MACA,OAAO/C,MAAGA,CAAC,CAACgD,IAAI,CAACxC,UAAU,CAACyC,MAAM,EAAEF,QAAQ,CAAC;IAC/C;IAAC,SAEcG,UAAUA,CAAAC,GAAA,EAAAC,GAAA;MAAA,OAAAC,WAAA,CAAAzE,KAAA,OAAAD,SAAA;IAAA;IAAA,SAAA0E,YAAA;MAAAA,WAAA,GAAA9E,iBAAA,CAAzB,WAA0BuC,GAAW,EAAEC,IAAY,EAAoB;QACrE,MAAMuC,OAAO,SAAS5C,KAAK,CAACI,GAAG,EAAEC,IAAI,CAAC;QAEtC,IACGP,UAAU,CAAC+C,SAAS,IAAID,OAAO,KAAKvE,SAAS,CAACG,cAAc,IAC5DsB,UAAU,CAACgD,WAAW,IAAIF,OAAO,KAAKvE,SAAS,CAACK,OAAQ,EACzD;UACA,MAAM2D,QAAQ,GAAG/C,MAAGA,CAAC,CAACgB,QAAQ,CAACD,IAAI,EAAED,GAAG,CAAC;UACzC,MAAMS,IAAI,GAAGC,OAAO,CAACuB,QAAQ,EAAEhC,IAAI,CAAC;UACpCzB,cAAc,CAACiC,IAAI,EAAEpB,IAACA,CAAC,CAACsD,YAAY,CAAC3C,GAAG,CAAC,CAAC;UAC1CvD,IAAI,CAACkF,KAAK,CAAC3B,GAAG,EAAES,IAAI,CAAC;QACvB;QACA,OAAO+B,OAAO,KAAKvE,SAAS,CAACI,QAAQ;MACvC,CAAC;MAAA,OAAAkE,WAAA,CAAAzE,KAAA,OAAAD,SAAA;IAAA;IAAA,SAEc+E,MAAMA,CAAAC,GAAA;MAAA,OAAAC,OAAA,CAAAhF,KAAA,OAAAD,SAAA;IAAA;IAAA,SAAAiF,QAAA;MAAAA,OAAA,GAAArF,iBAAA,CAArB,WAAsBsF,aAAqB,EAAmB;QAC5D,IAAI,CAAC1D,IAACA,CAAC,CAAC2D,UAAU,CAACD,aAAa,CAAC,EAAE,OAAO,CAAC;QAE3C,MAAME,IAAI,GAAG5D,IAACA,CAAC,CAAC6D,QAAQ,CAACH,aAAa,CAAC;QAEvC,IAAIE,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE;UACtB,MAAMhE,OAAO,GAAG4D,aAAa;UAE7B,IAAIK,KAAK,GAAG,CAAC;UAEb,MAAMC,KAAK,GAAG5G,IAAI,CAAC6G,OAAO,CAACnE,OAAO,EAAEO,UAAU,CAAC6D,eAAe,CAAC;UAC/D,KAAK,MAAMtB,QAAQ,IAAIoB,KAAK,EAAE;YAC5B,MAAMrD,GAAG,GAAGd,MAAGA,CAAC,CAACgD,IAAI,CAAC/C,OAAO,EAAE8C,QAAQ,CAAC;YAExC,MAAMO,OAAO,SAASJ,UAAU,CAACpC,GAAG,EAAEb,OAAO,CAAC;YAC9C,IAAIqD,OAAO,EAAEY,KAAK,IAAI,CAAC;UACzB;UAEA,OAAOA,KAAK;QACd,CAAC,MAAM;UACL,MAAMnB,QAAQ,GAAGc,aAAa;UAC9B,MAAMP,OAAO,SAASJ,UAAU,CAACH,QAAQ,EAAE/C,MAAGA,CAAC,CAACC,OAAO,CAAC8C,QAAQ,CAAC,CAAC;UAElE,OAAOO,OAAO,GAAG,CAAC,GAAG,CAAC;QACxB;MACF,CAAC;MAAA,OAAAM,OAAA,CAAAhF,KAAA,OAAAD,SAAA;IAAA;IAED,IAAI2F,aAAa,GAAG,CAAC;IACrB,IAAIC,SAAkC,GAAG,IAAI;IAE7C,MAAMC,UAAU,GAAGjH,IAAI,CAACkH,QAAQ,CAAC,YAAY;MAC3C,IAAIF,SAAS,KAAK,IAAI,EAAE;QAGtB;MACF;MAEA,MAAMG,IAAI,GAAG/E,OAAO,CAACgF,MAAM,CAACJ,SAAS,CAAC;MAEtC5B,OAAO,CAACC,GAAG,CACR,yBAAwB0B,aAAc,IACrCA,aAAa,KAAK,CAAC,GAAG,OAAO,GAAG,MACjC,gBAAeI,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGE,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAE,MAC5D,CAAC;MACDJ,aAAa,GAAG,CAAC;MACjBC,SAAS,GAAG,IAAI;IAClB,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI/D,UAAU,CAACsC,KAAK,EAAEtF,OAAO,CAACsH,MAAM,CAAC;MAAEC,cAAc,EAAE;IAAK,CAAC,CAAC;IAE9D,IAAI,CAACvE,UAAU,CAACwE,gBAAgB,EAAE;MAChC,IAAIxE,UAAU,CAACyE,gBAAgB,EAAE;QAC/B1H,IAAI,CAAC2H,SAAS,CAAC1E,UAAU,CAACyC,MAAM,CAAC;MACnC;MAEA,GAAAzD,CAAA,EAAAC,CAAA,MAAAD,CAAA,GAAAA,CAAA,CAAAE,KAAA,OAAAD,CAAA,GAAAA,CAAA,CAAAC,KAAA,QAAAF,CAAA,OAAAC,CAAA,OAAAD,CAAA,OAAAC,CAAA,QAAAD,CAAA,QAAAC,CAAA,MAAAE,OAAA,CAAAC,QAAA,CAAAC,IAAA,aAAAvC,GAAA,GAAAwC,SAAA,GAAA1C,OAAA,aAAA2C,IAAA,EAAaS,UAAU,CAACyC,MAAM,EAAE;QAAE/C,SAAS,EAAE;MAAK,CAAC,CAAC;MAEpDqE,SAAS,GAAG5E,OAAO,CAACgF,MAAM,CAAC,CAAC;MAE5B,KAAK,MAAM5B,QAAQ,IAAIvC,UAAU,CAAC2E,SAAS,EAAE;QAI3Cb,aAAa,UAAUZ,MAAM,CAACX,QAAQ,CAAC;MACzC;MAEA,IAAI,CAACvC,UAAU,CAAC4E,KAAK,EAAE;QACrBZ,UAAU,CAAC,CAAC;QACZA,UAAU,CAACa,KAAK,CAAC,CAAC;MACpB;IACF;IAEA,IAAI7E,UAAU,CAACsC,KAAK,EAAE;MAGpB,IAAIwC,UAAU,GAAG,CAAC;MAClB,MAAM;QAAEH;MAAU,CAAC,GAAG3E,UAAU;MAChC,IAAI+E,OAA4C;MAChD,IAAIJ,SAAS,CAACK,MAAM,KAAK,CAAC,EAAE;QAE1B,MAAMzE,IAAI,GAAGoE,SAAS,CAAC,CAAC,CAAC;QACzB,MAAMM,YAAY,GAAGzF,MAAGA,CAAC,CAACrC,OAAO,CAACoD,IAAI,CAAC;QACvCwE,OAAO,GAAGxC,QAAQ,IAAI;UACpB,OAAOA,QAAQ,KAAK0C,YAAY,GAAGzF,MAAGA,CAAC,CAACC,OAAO,CAACc,IAAI,CAAC,GAAGA,IAAI;QAC9D,CAAC;MACH,CAAC,MAAM;QAGL,MAAM2E,iBAAsC,GAAG,IAAIC,GAAG,CACpDR,SAAS,CAACrD,GAAG,CAACiB,QAAQ,IAAI;UACxB,MAAM6C,gBAAgB,GAAG5F,MAAGA,CAAC,CAACrC,OAAO,CAACoF,QAAQ,CAAC;UAC/C,OAAO,CAAC6C,gBAAgB,EAAE5F,MAAGA,CAAC,CAACC,OAAO,CAAC8C,QAAQ,CAAC,CAAC;QACnD,CAAC,CACH,CAAC;QAED,MAAM8C,iBAAsC,GAAG,IAAIF,GAAG,CACpDR,SAAS,CAACrD,GAAG,CAACiB,QAAQ,IAAI;UACxB,MAAM6C,gBAAgB,GAAG5F,MAAGA,CAAC,CAACrC,OAAO,CAACoF,QAAQ,CAAC;UAC/C,OAAO,CAAC6C,gBAAgB,EAAE7C,QAAQ,CAAC;QACrC,CAAC,CACH,CAAC;QAED,MAAM;UAAE+C;QAAI,CAAC,GAAG9F,MAAGA,CAAC;QAEpBuF,OAAO,GAAGxC,QAAQ,IAAI;UACpB,MAAMhC,IAAI,GAAG2E,iBAAiB,CAACK,GAAG,CAAChD,QAAQ,CAAC;UAC5C,IAAIhC,IAAI,KAAKjC,SAAS,EAAE;YACtB,OAAOiC,IAAI;UACb;UACA,KAAK,MAAM,CAACiF,qBAAqB,EAAEhF,QAAQ,CAAC,IAAI6E,iBAAiB,EAAE;YACjE,IAAI9C,QAAQ,CAACkD,UAAU,CAACD,qBAAqB,GAAGF,GAAG,CAAC,EAAE;cACpDJ,iBAAiB,CAACQ,GAAG,CAACnD,QAAQ,EAAE/B,QAAQ,CAAC;cACzC,OAAOA,QAAQ;YACjB;UACF;UAEA,OAAO,EAAE;QACX,CAAC;MACH;MAEAmE,SAAS,CAACgB,OAAO,CAACtC,aAAa,IAAI;QACjCrG,OAAO,CAACsF,KAAK,CAACe,aAAa,CAAC;MAC9B,CAAC,CAAC;MAEFrG,OAAO,CAAC4I,YAAY,CAAC,CAAC;MAEtB5I,OAAO,CAAC6I,aAAa,CAAA9H,iBAAA,CAAC,WAAM4G,SAAS,EAAI;QACvCG,UAAU,EAAE;QACZ,IAAIf,SAAS,KAAK,IAAI,EAAEA,SAAS,GAAG5E,OAAO,CAACgF,MAAM,CAAC,CAAC;QAEpD,IAAI;UACF,MAAMrB,OAAO,SAASjF,OAAO,CAACiI,GAAG,CAC/BnB,SAAS,CAACrD,GAAG,CAACiB,QAAQ,IAAIG,UAAU,CAACH,QAAQ,EAAEwC,OAAO,CAACxC,QAAQ,CAAC,CAAC,CACnE,CAAC;UAEDuB,aAAa,IAAIhB,OAAO,CAACiD,MAAM,CAACC,OAAO,CAAC,CAAChB,MAAM;QACjD,CAAC,CAAC,OAAO3G,GAAG,EAAE;UACZ8D,OAAO,CAACxE,KAAK,CAACU,GAAG,CAAC;QACpB;QAEAyG,UAAU,EAAE;QACZ,IAAIA,UAAU,KAAK,CAAC,IAAI,CAAC9E,UAAU,CAAC4E,KAAK,EAAEZ,UAAU,CAAC,CAAC;MACzD,CAAC,EAAC;IACJ;EACF,CAAC;EAAA,OAAAjE,IAAA,CAAA3B,KAAA,OAAAD,SAAA;AAAA"}