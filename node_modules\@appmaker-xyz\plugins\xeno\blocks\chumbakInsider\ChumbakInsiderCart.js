import React, { useState, useEffect } from 'react';
import { StyleSheet } from 'react-native';
import {
  appStorageApi,
  runDataSource,
  getExtensionConfig,
} from '@appmaker-xyz/core';
import {
  Layout,
  ThemeText,
  AppImage,
  UiKitButton,
  AppTouchable,
} from '@appmaker-xyz/ui';

function Radio(props) {
  return (
    <AppTouchable
      style={props.option}
      onPress={() => props.setActive(!props.active)}>
      <ThemeText size="sm">{props.title}</ThemeText>
      <Layout style={props.styles.row}>
        <ThemeText fontFamily="bold" size="sm">
          {props.price}
        </ThemeText>
        <Layout style={props.styles.radioButtonStyle}>
          <Layout style={props.radioStyle} />
        </Layout>
      </Layout>
    </AppTouchable>
  );
}

async function getProductData(productIds) {
  const ids = productIds.map((product) => {
    return `gid://shopify/Product/${product.product_id}`;
  });
  const dataSource = {
    attributes: {},
    source: 'shopify',
  };
  const [response] = await runDataSource(
    {
      dataSource,
    },
    {
      methodName: 'products',
      params: {
        ids,
      },
    },
  );
  return response.data.data.products;
}

const ChumbakInsiderCart = ({ onPress, pageDispatch, onAction }) => {
  const [productResp, setProductResp] = useState([]);
  const pIds = getExtensionConfig('xeno', 'productids', []);
  useEffect(() => {
    async function fetchData() {
      const productResp = await getProductData(pIds);
      setProductResp(productResp);
    }
    fetchData();
  }, [pIds]);

  const [active, setActive] = useState(-1);
  const [loading, setLoading] = useState(false);

  const { user, checkout } = appStorageApi().getState();
  const getLastElement = (str) => {
    return str.split('-').pop();
  };
  const onAddtoCart = async ({ quantity }) => {
    setLoading(true);
    try {
      let hasXenoProduct = checkout?.lineItems?.edges.some(
        (x) => x?.node?.variant?.product?.productType === 'membership',
      );
      if (hasXenoProduct) {
        const remove = await onAction({
          action: 'UPDATE_CART_QUANTITY',
          params: {
            product: {
              id: checkout?.lineItems?.edges.find(
                (x) => x?.node?.variant?.product?.productType === 'membership',
              )?.node?.id,
            },
            quantity: 0,
            // product: data,
          },
        });
        console.log(remove);
      }
      // if(count > quantity) {}
      let params = {
        action: 'ADD_TO_CART_V2',
        params: {
          variant: productResp.edges[active]?.node?.variants?.edges[0],
          product: productResp.edges[active],
          quantity: 1,
          fromList: true,
          customAttributes: { key: '_membership', value: '_membership' },
          updateCartPageStateRequired: true,
          // product: data,
        },
      };
      const cartResp = await onAction(params);
      if (cartResp?.status === 'success') {
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: cartResp?.message,
          },
        });
        onAction({
          action: 'GO_BACK',
        });
      } else {
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: 'Something went wrong, Please try again',
          },
        });
      }
      setLoading(false);
    } catch (e) {
      setLoading(false);
      console.log(e);
    }
  };
  return (
    <Layout style={styles.container}>
      <AppImage
        uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/popup_logo-removebg-preview_1024x1024.png"
        style={styles.image}
      />
      <Layout style={styles.becomeInsiderBadge}>
        <ThemeText fontFamily="bold">JOIN</ThemeText>
        <AppImage
          uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
          style={styles.insiderImage}
        />
        <ThemeText fontFamily="bold">TO ENJOY</ThemeText>
      </Layout>
      <ThemeText status="demiDark">
        Free Priority Shipping | Additional Discounts Exclusive Access | Gifts &
        More
      </ThemeText>
      <Layout style={styles.options}>
        {productResp.edges && productResp.edges.length > 0
          ? productResp.edges.map((item, index) => {
              return (
                <Radio
                  active={active === index}
                  setActive={() => setActive(index)}
                  styles={styles}
                  option={{
                    ...styles.option,
                    borderColor: active === index ? '#A9AEB7' : '#E9EDF1',
                  }}
                  radioStyle={{
                    ...styles.radioStyle,
                    backgroundColor:
                      active === index ? color.dark : 'transparent',
                  }}
                  title={getLastElement(item?.node?.title)}
                  price={`${item?.node?.priceRange.maxVariantPrice.currencyCode} ${item?.node?.priceRange.maxVariantPrice.amount}`}
                />
              );
            })
          : null}
      </Layout>
      <ThemeText status="demiDark">
        Your membership now gets automatically renewed on the last day of active
        plan.
      </ThemeText>
      <UiKitButton
        onPress={() => onAddtoCart({ quantity: 1 })}
        baseSize={true}
        block={true}
        loading={loading}
        status={active !== -1 ? 'dark' : 'demiDark'}
        wholeContainerStyle={styles.button}>
        ADD TO CART
      </UiKitButton>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#B6DCC9',
    padding: 12,
    alignItems: 'center',
    justifyContent: 'space-evenly',
  },
  image: {
    width: 120,
    height: 80,
    resizeMode: 'contain',
    marginBottom: 32,
    alignSelf: 'center',
  },
  insiderImage: {
    width: 120,
    height: 30,
    resizeMode: 'contain',
    marginRight: 6,
  },
  becomeInsiderBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 6,
    width: '100%',
    justifyContent: 'center',
  },
  options: {
    alignItems: 'center',
    width: '100%',
  },
  option: {
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    borderWidth: 1,
    paddingVertical: 16,
    paddingHorizontal: 12,
    alignItems: 'center',
    shadowColor: '#000',
    width: '100%',
  },
  radioButtonStyle: {
    borderColor: 'black',
    borderWidth: 1,
    borderRadius: 16,
    marginLeft: 8,
    padding: 2,
  },
  radioStyle: {
    width: 16,
    height: 16,
    borderRadius: 16,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  button: {
    borderRadius: 0,
  },
});

export default ChumbakInsiderCart;
