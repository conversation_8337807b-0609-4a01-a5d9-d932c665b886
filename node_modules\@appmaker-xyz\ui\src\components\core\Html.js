import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ThemeText } from '@appmaker-xyz/ui';

const Html = ({ attributes }) => {
  return (
    <Layout style={styles.container}>
      <ThemeText html={true}>{attributes.content}</ThemeText>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 15,
    paddingVertical: 5,
  },
});

export default Html;
