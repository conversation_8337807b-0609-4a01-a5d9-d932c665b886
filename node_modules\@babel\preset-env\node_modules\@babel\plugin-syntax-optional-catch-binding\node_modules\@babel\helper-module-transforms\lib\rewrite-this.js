"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = rewriteThis;
var _helperEnvironmentVisitor = require("@babel/helper-environment-visitor");
var _traverse = require("@babel/traverse");
var _t = require("@babel/types");
const {
  numericLiteral,
  unaryExpression
} = _t;
const rewriteThisVisitor = _traverse.default.visitors.merge([_helperEnvironmentVisitor.default, {
  ThisExpression(path) {
    path.replaceWith(unaryExpression("void", numericLiteral(0), true));
  }
}]);
function rewriteThis(programPath) {
  (0, _traverse.default)(programPath.node, Object.assign({}, rewriteThisVisitor, {
    noScope: true
  }));
}

//# sourceMappingURL=rewrite-this.js.map
