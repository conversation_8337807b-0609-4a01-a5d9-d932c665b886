import { StyleSheet, Dimensions } from 'react-native';

const { width } = Dimensions.get('window');

// spacing b/w image and text container
const marginLeftTextContainer = 10;
// Image height and width
const imageWidth = 100;
// Right and left margin for container. so total margin will be 20
const horizontalMargin = 10;
const styles = StyleSheet.create({
  rootContainer: {
    flexDirection: 'column',
    alignContent: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
  },
  container: {
    backgroundColor: '#fff',
    alignContent: 'center',
    alignItems: 'center',
    marginHorizontal: 0,
    marginVertical: 0,
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 14,
  },
  image: {
    height: width * 0.54,
    borderRadius: 0,
    marginBottom: 14,
    marginRight: 0,
  },
  title: {
    color: '#000',
    fontFamily: 'Lato-Bold',
    fontSize: 16,
    marginRight: 7,
    marginBottom: 6,
  },
  description: {
    color: '#6E6E6E',
    fontFamily: 'NotoSans',
    fontSize: 14,
    marginTop: 0,
    marginBottom: 16,
  },
  textContainer: {
    marginLeft: 0,
    // width of the whole text container will be Screen width minus image width and margin
    width:
      width - (imageWidth + marginLeftTextContainer + horizontalMargin * 2),
    flex: 1,
    flexDirection: 'column',
  },
  descriptionNoImage: {
    color: '#99999c',
    fontSize: 15,
    fontFamily: 'Lato-Regular',
    marginTop: 10,
  },
  author: {
    marginBottom: 0,
    fontSize: 9,
    color: '#1A1A1A',
    fontFamily: 'NotoSans',
  },
  imageDummy: {
    flex: 1,
    height: width * 0.54,
    borderRadius: 0,
    marginBottom: 14,
    marginRight: 0,
    justifyContent: 'center',
    backgroundColor: 'black',
  },
  firstLetter: {
    opacity: 0.36,
    alignSelf: 'center',
    color: '#707070',
    fontWeight: 'bold',
    fontSize: 96,
  },
  categoryText: {
    marginBottom: 6,
    fontSize: 9,
    color: '#1A1A1A',
    fontFamily: 'NotoSans',
  },
  authorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  userIcon: {
    marginRight: 6,
  },
});

export default styles;
