{"name": "react-native-mmkv", "version": "2.12.2", "description": "The fastest key/value storage for React Native. ~30x faster than AsyncStorage! Works on Android, iOS and Web.", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/index.d.ts", "react-native": "lib/module/index", "source": "src/index", "files": ["android/src", "android/build.gradle", "android/gradle.properties", "android/CMakeLists.txt", "cpp", "MMKV/Core", "lib/commonjs", "lib/module", "lib/typescript", "ios/**/*.h", "ios/**/*.m", "ios/**/*.mm", "ios/**/*.cpp", "ios/Mmkv.xcodeproj/project.pbxproj", "src", "react-native-mmkv.podspec", "README.md", "img/banner-light.png", "img/banner-dark.png"], "scripts": {"typescript": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\"", "lint-ci": "yarn lint -f ./node_modules/@firmnav/eslint-github-actions-formatter/dist/formatter.js", "lint-cpp": "scripts/clang-format.sh", "test": "jest", "prepare": "git submodule update --init --recursive && bob build", "prepack": "bob build", "update-submodule": "git submodule update --remote --merge", "release": "release-it", "example": "yarn --cwd example", "pods": "cd example && yarn pods", "bootstrap": "git submodule update --init --recursive && yarn && cd example && yarn bootstrap"}, "keywords": ["react-native", "ios", "android"], "repository": "https://github.com/mrousavy/react-native-mmkv", "author": "<PERSON> <<EMAIL>> (https://github.com/mrousavy)", "license": "(MIT AND BSD-3-<PERSON><PERSON>)", "bugs": {"url": "https://github.com/mrousavy/react-native-mmkv/issues"}, "homepage": "https://github.com/mrousavy/react-native-mmkv#readme", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@jamesacarr/eslint-formatter-github-actions": "^0.2.0", "@react-native-community/eslint-config": "^3.2.0", "@react-native-community/eslint-plugin": "^1.3.0", "@release-it/conventional-changelog": "^8.0.1", "@testing-library/react-native": "^12.4.3", "@tsconfig/react-native": "^3.0.3", "@types/jest": "^29.5.12", "@types/react": "^18.2.55", "@types/react-test-renderer": "^18.0.7", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "eslint": "^8.56.0", "eslint-plugin-ft-flow": "^3.0.4", "eslint-plugin-jest": "^27.8.0", "jest": "^29.7.0", "metro-react-native-babel-preset": "^0.77.0", "prettier": "^3.2.5", "react": "^18.2.0", "react-native": "^0.71.6", "react-native-builder-bob": "^0.20.4", "react-test-renderer": "^18.2.0", "release-it": "^15.10.1", "typescript": "^5.3.3"}, "peerDependencies": {"react": "*", "react-native": ">=0.71.0"}, "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/example/node_modules", "<rootDir>/lib/"]}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": {"name": "conventionalcommits", "types": [{"type": "feat", "section": "✨ Features"}, {"type": "fix", "section": "🐛 Bug Fixes"}, {"type": "perf", "section": "💨 Performance Improvements"}, {"type": "chore(deps)", "section": "🛠️ Dependency Upgrades"}, {"type": "docs", "section": "📚 Documentation"}]}}}}, "eslintConfig": {"root": true, "extends": ["@react-native-community", "prettier"], "plugins": ["jest"], "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "eslintIgnore": ["node_modules/", "lib/"], "prettier": {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.json"}]]}, "codegenConfig": {"name": "reactnativemmkv", "type": "modules", "jsSrcsDir": "./lib/module"}}