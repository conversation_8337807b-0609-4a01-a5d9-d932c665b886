import React from 'react';
import { ThemeText, Layout } from '@appmaker-xyz/ui';
import { useOrderItem, useFullfillmentStatus } from '@appmaker-xyz/shopify';
import { OrderBox } from '../../../assets/svg';
import { appmaker } from '@appmaker-xyz/core';
import { ActivityIndicator } from 'react-native';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';

function cardStatus(status, theme) {
  switch (status) {
    case 'SCHEDULED':
      return theme.colors.primaryButtonBackground;
    case 'UNFULFILLED':
      return theme.colors.warning;
    case 'FULFILLED':
      return theme.colors.success;
    case 'ON_HOLD':
      return theme.colors.warning;
    case 'PARTIALLY_FULFILLED':
      return theme.colors.warning;
    default:
      return theme.colors.secondaryButtonBackground;
  }
}
function FullfillmentStatus({ status, order }) {
  const {
    status: newStatus,
    label,
    loading,
  } = useFullfillmentStatus(status, {
    order,
  });
  const { styles, theme } = useStyles(stylesheet);
  const statusColor = cardStatus(newStatus || status, theme);
  let orderStatusText = appmaker.applyFilters(
    'custom-order-status',
    newStatus || status,
  );

  const statusCardContainer = [
    styles.statusCard,
    { backgroundColor: statusColor },
  ];
  return loading ? (
    <ActivityIndicator size={'small'} color={statusColor} />
  ) : (
    <Layout style={statusCardContainer} loading={false}>
      <Layout style={styles.statusCardTitle}>
        <OrderBox style={styles.boxIcon} height={20} width={20} />
        {
          <ThemeText fontFamily="medium" size="md" color="#FFFFFF">
            {orderStatusText || label}
          </ThemeText>
        }
      </Layout>
    </Layout>
  );
}

export default function OrderDetailHeader(props) {
  const { attributes, onAction } = props;
  const { orderId, orderDate, status, order } = useOrderItem(props);
  const { styles } = useStyles(stylesheet);

  const detailFooterItems = appmaker.applyFilters(
    'appmaker-order-detail-footer',
    [],
  );
  return (
    <Layout style={styles.container}>
      <Layout style={styles.header}>
        <ThemeText fontFamily="medium" size="md" color="#6C757E">
          ORDER ID: {orderId}
        </ThemeText>
        <ThemeText fontFamily="medium">{orderDate}</ThemeText>
      </Layout>
      <FullfillmentStatus status={status} order={order} />

      {detailFooterItems.map((Item, key) => (
        <Item
          key={`footer-items-${key}`}
          attributes={attributes}
          onAction={onAction}
        />
      ))}
    </Layout>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  container: {
    padding: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusCard: {
    backgroundColor: '#22C55E',
    borderRadius: 4,
    padding: 12,
    marginTop: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusCardTitle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  boxIcon: {
    marginRight: 8,
  },
  footer: {
    marginTop: 12,
  },
}));
