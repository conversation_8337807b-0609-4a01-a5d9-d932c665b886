declare module '@appmaker-xyz/shopify' {
  type FilterValue = {
    count?: number;
    id: string;
    input: string;
    label: string;
  };

  type FilterType = 'LIST' | 'PRICE_RANGE';

  type Filter = {
    id: string;
    label: string;
    type: FilterType;
    values: FilterValue[];
  };

  type Filters = Filter[];
  interface CollectionFilterReturn {
    availableFilters: Filters | null;
    applyFilters: () => void;
  }
  function useCollectionFilter(): CollectionFilterReturn;
}
