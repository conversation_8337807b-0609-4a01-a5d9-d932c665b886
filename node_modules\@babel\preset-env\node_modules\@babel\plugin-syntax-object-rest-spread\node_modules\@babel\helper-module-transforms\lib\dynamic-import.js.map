{"version": 3, "names": ["getDynamicImportSource", "node", "source", "arguments", "t", "isStringLiteral", "isTemplateLiteral", "template", "expression", "ast", "buildDynamicImport", "deferT<PERSON><PERSON><PERSON>", "wrapWithPromise", "builder", "specifier", "quasis", "length", "specifierToString", "identifier", "templateLiteral", "templateElement", "raw"], "sources": ["../src/dynamic-import.ts"], "sourcesContent": ["// Heavily inspired by\n// https://github.com/airbnb/babel-plugin-dynamic-import-node/blob/master/src/utils.js\n\nimport * as t from \"@babel/types\";\nimport template from \"@babel/template\";\n\n// TODO(Babel 8): Remove this\nexport function getDynamicImportSource(\n  node: t.CallExpression,\n): t.StringLiteral | t.TemplateLiteral {\n  const [source] = node.arguments;\n\n  return t.isStringLiteral(source) || t.isTemplateLiteral(source)\n    ? source\n    : (template.expression.ast`\\`\\${${source}}\\`` as t.TemplateLiteral);\n}\n\nexport function buildDynamicImport(\n  node: t.CallExpression,\n  deferToThen: boolean,\n  wrapWithPromise: boolean,\n  builder: (specifier: t.Expression) => t.Expression,\n): t.Expression {\n  const [specifier] = node.arguments;\n\n  if (\n    t.isStringLiteral(specifier) ||\n    (t.isTemplateLiteral(specifier) && specifier.quasis.length === 0)\n  ) {\n    if (deferToThen) {\n      return template.expression.ast`\n        Promise.resolve().then(() => ${builder(specifier)})\n      `;\n    } else return builder(specifier);\n  }\n\n  const specifierToString = t.isTemplateLiteral(specifier)\n    ? t.identifier(\"specifier\")\n    : t.templateLiteral(\n        [t.templateElement({ raw: \"\" }), t.templateElement({ raw: \"\" })],\n        [t.identifier(\"specifier\")],\n      );\n\n  if (deferToThen) {\n    return template.expression.ast`\n      (specifier =>\n        new Promise(r => r(${specifierToString}))\n          .then(s => ${builder(t.identifier(\"s\"))})\n      )(${specifier})\n    `;\n  } else if (wrapWithPromise) {\n    return template.expression.ast`\n      (specifier =>\n        new Promise(r => r(${builder(specifierToString)}))\n      )(${specifier})\n    `;\n  } else {\n    return template.expression.ast`\n      (specifier => ${builder(specifierToString)})(${specifier})\n    `;\n  }\n}\n"], "mappings": ";;;;;;;AAGA;AACA;AAGO,SAASA,sBAAsB,CACpCC,IAAsB,EACe;EACrC,MAAM,CAACC,MAAM,CAAC,GAAGD,IAAI,CAACE,SAAS;EAE/B,OAAOC,CAAC,CAACC,eAAe,CAACH,MAAM,CAAC,IAAIE,CAAC,CAACE,iBAAiB,CAACJ,MAAM,CAAC,GAC3DA,MAAM,GACLK,iBAAQ,CAACC,UAAU,CAACC,GAAI,QAAOP,MAAO,KAA0B;AACvE;AAEO,SAASQ,kBAAkB,CAChCT,IAAsB,EACtBU,WAAoB,EACpBC,eAAwB,EACxBC,OAAkD,EACpC;EACd,MAAM,CAACC,SAAS,CAAC,GAAGb,IAAI,CAACE,SAAS;EAElC,IACEC,CAAC,CAACC,eAAe,CAACS,SAAS,CAAC,IAC3BV,CAAC,CAACE,iBAAiB,CAACQ,SAAS,CAAC,IAAIA,SAAS,CAACC,MAAM,CAACC,MAAM,KAAK,CAAE,EACjE;IACA,IAAIL,WAAW,EAAE;MACf,OAAOJ,iBAAQ,CAACC,UAAU,CAACC,GAAI;AACrC,uCAAuCI,OAAO,CAACC,SAAS,CAAE;AAC1D,OAAO;IACH,CAAC,MAAM,OAAOD,OAAO,CAACC,SAAS,CAAC;EAClC;EAEA,MAAMG,iBAAiB,GAAGb,CAAC,CAACE,iBAAiB,CAACQ,SAAS,CAAC,GACpDV,CAAC,CAACc,UAAU,CAAC,WAAW,CAAC,GACzBd,CAAC,CAACe,eAAe,CACf,CAACf,CAAC,CAACgB,eAAe,CAAC;IAAEC,GAAG,EAAE;EAAG,CAAC,CAAC,EAAEjB,CAAC,CAACgB,eAAe,CAAC;IAAEC,GAAG,EAAE;EAAG,CAAC,CAAC,CAAC,EAChE,CAACjB,CAAC,CAACc,UAAU,CAAC,WAAW,CAAC,CAAC,CAC5B;EAEL,IAAIP,WAAW,EAAE;IACf,OAAOJ,iBAAQ,CAACC,UAAU,CAACC,GAAI;AACnC;AACA,6BAA6BQ,iBAAkB;AAC/C,uBAAuBJ,OAAO,CAACT,CAAC,CAACc,UAAU,CAAC,GAAG,CAAC,CAAE;AAClD,UAAUJ,SAAU;AACpB,KAAK;EACH,CAAC,MAAM,IAAIF,eAAe,EAAE;IAC1B,OAAOL,iBAAQ,CAACC,UAAU,CAACC,GAAI;AACnC;AACA,6BAA6BI,OAAO,CAACI,iBAAiB,CAAE;AACxD,UAAUH,SAAU;AACpB,KAAK;EACH,CAAC,MAAM;IACL,OAAOP,iBAAQ,CAACC,UAAU,CAACC,GAAI;AACnC,sBAAsBI,OAAO,CAACI,iBAAiB,CAAE,KAAIH,SAAU;AAC/D,KAAK;EACH;AACF"}