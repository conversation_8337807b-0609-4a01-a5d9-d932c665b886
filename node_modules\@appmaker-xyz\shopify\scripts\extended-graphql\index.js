#!/usr/bin/env node

/**
 * GraphQL Codegen CLI
 * 
 * This script performs two main functions:
 * 1. Scans for extended GraphQL operations in the extended-graphql directories
 * 2. Runs codegen using the generated configuration
 * 
 * Usage:
 *   node scripts/gql-injections/index.js [--config-only]
 * 
 * Options:
 *   --config-only   Only generate the operations file, don't run codegen
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');
const { execSync } = require('child_process');

// Parse command line arguments
const args = process.argv.slice(2);
const configOnly = args.includes('--config-only');

// Function to get the Shopify directory
function getShopifyDir() {
  // First try the default path
  let shopifyDir = path.join(process.cwd(), 'packages', 'shopify');
  if (fs.existsSync(shopifyDir)) {
    return shopifyDir;
  }
  
  // Try the alternate path
  shopifyDir = path.join(process.cwd(), 'node_modules/@appmaker-xyz/shopify');
  if (fs.existsSync(shopifyDir)) {
    return shopifyDir;
  }
  
  // If neither exists, throw an error
  throw new Error('Shopify directory not found. Checked paths:\n' +
    `- ${path.join(process.cwd(), 'packages', 'shopify')}\n` +
    `- ${path.join(process.cwd(), 'node_modules/@appmaker-xyz/shopify')}`);
}

// Validate Shopify directory exists early
try {
  getShopifyDir();
} catch (error) {
  console.error(`Error: ${error.message}`);
  process.exit(1);
}

// Paths to scan for extended GraphQL operations
const extendedPaths = [
  'packages/**/extended-graphql/**/*.gql',
  'packages-dev/**/extended-graphql/**/*.gql',
  'node_modules/@appmaker-packages/**/extended-graphql/**/*.gql',
];

// Function to find all extended GraphQL paths
function findExtendedPaths() {
  const foundPaths = [];
  
  extendedPaths.forEach(pattern => {
    try {
      const matches = glob.sync(pattern, { absolute: false });
      matches.forEach(match => {
        const dirPath = path.dirname(match);
        const extendedPath = path.join(dirPath, '**/*.gql');
        if (!foundPaths.includes(extendedPath)) {
          foundPaths.push(extendedPath);
        }
      });
    } catch (error) {
      console.warn(`Error searching extended pattern ${pattern}:`, error);
    }
  });

  return foundPaths;
}

// Prepare the extended GraphQL operations for merging
function prepareExtendedGraphQL() {
  const shopifyDir = getShopifyDir();
  const tempDir = path.join(shopifyDir, '.temp-graphql');
  
  // Ensure temp directory exists and is empty
  if (fs.existsSync(tempDir)) {
    fs.rmSync(tempDir, { recursive: true, force: true });
  }
  fs.mkdirSync(tempDir, { recursive: true });
  
  console.log('Preparing extended GraphQL operations...');
  
  // Find all extended GraphQL paths
  console.log('Scanning for extended GraphQL operations...');
  const extendedGraphQLPaths = findExtendedPaths();
  
  if (extendedGraphQLPaths.length === 0) {
    console.warn('Warning: No extended GraphQL paths found!');
  } else {
    console.log(`Found ${extendedGraphQLPaths.length} extended GraphQL paths.`);
  }
  
  // Create a merged-operations.json file to track what operations should be merged
  const operations = {
    operations: {},
    config: {
      paths: extendedGraphQLPaths,
      mergeOperations: true
    }
  };
  
  // Find all extended GraphQL files
  extendedPaths.forEach(pattern => {
    const matches = glob.sync(pattern, { absolute: true });
    matches.forEach(filePath => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Extract operation name from the content (simple regex approach)
        const operationMatch = content.match(/\s*(query|mutation|fragment)\s+([a-zA-Z0-9_]+)/);
        if (operationMatch) {
          const operationType = operationMatch[1];
          const operationName = operationMatch[2];
          
          // Add to the operations object
          if (!operations.operations[operationName]) {
            operations.operations[operationName] = [];
          }
          
          operations.operations[operationName].push({
            type: operationType,
            path: filePath
          });
        }
      } catch (error) {
        console.warn(`Error processing file ${filePath}:`, error);
      }
    });
  });
  
  // Write the operations file
  const operationsFile = path.join(tempDir, 'merged-operations.json');
  fs.writeFileSync(operationsFile, JSON.stringify(operations, null, 2));
  console.log(`Generated merged operations file at: ${operationsFile}`);
}

// Run codegen in the shopify package
function runCodegen() {
  const shopifyDir = getShopifyDir();
  
  // No need to check existence since getShopifyDir() handles that
  
  try {
    // First, prepare the extended GraphQL operations
    prepareExtendedGraphQL();
    
    console.log('Running GraphQL codegen in Shopify package...');
    
    execSync('yarn install', {
      cwd: shopifyDir,
      stdio: 'inherit'
    });
    
    execSync('node scripts/merge-graphql-operations.js && npm run codegen', {
      cwd: shopifyDir,
      stdio: 'inherit'
    });
    
    console.log('✅ GraphQL codegen completed successfully');
  } catch (error) {
    console.error(`Error: Failed to run GraphQL codegen: ${error.message}`);
    process.exit(1);
  }
}

// Main execution
try {
  if (!configOnly) {
    runCodegen();
  } else {
    prepareExtendedGraphQL();
  }
} catch (error) {
  console.error(`Error: ${error.message}`);
  process.exit(1);
} 