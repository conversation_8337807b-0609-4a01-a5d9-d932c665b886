import React, { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import Icon from 'react-native-vector-icons/Feather';
import { ThemeText, Layout } from '../atoms';
import { usePageState } from '@appmaker-xyz/core';
import { styles as globalStyles } from '../../styles/index';

const DropSelect = ({ attributes = {}, coreDispatch }) => {
  const {
    label,
    leftIconName,
    name,
    selectList = [],
    selectListFn,
    setValueIfOnlyOne,
    avilableCountries,
    onChange,
    value: defaultValue,
  } = attributes;
  // console.log(selectListFn(),'selectListFn');
  const [value, setValue] = useState(defaultValue);
  const listData = selectListFn ? selectListFn() : Object.values(selectList);
  const [isFocus, setIsFocus] = useState(false);
  useEffect(() => {
    if (listData.length === 1 && setValueIfOnlyOne) {
      onChangeInput(listData[0]);
      setValue?.(listData[0].value);
    }
  }, [listData, setValueIfOnlyOne]);
  function onChangeInput(valueText) {
    onChange && onChange(valueText);
  }
  return (
    <Layout style={styles.container}>
      {label && (
        <ThemeText size="sm" color="#868E96" style={styles.floatingLabel}>
          {label}
        </ThemeText>
      )}
      <Dropdown
        style={[styles.dropdown, isFocus && styles.focus]}
        placeholderStyle={styles.placeholderStyle}
        selectedTextStyle={styles.selectedTextStyle}
        inputSearchStyle={styles.inputSearchStyle}
        iconStyle={styles.iconStyle}
        data={listData}
        search={true}
        maxHeight={300}
        labelField="label"
        valueField="value"
        placeholder={!isFocus ? `Select ${label}` : '...'}
        searchPlaceholder="Search..."
        value={defaultValue || value}
        onFocus={() => setIsFocus(true)}
        onBlur={() => setIsFocus(false)}
        onChange={(item) => {
          onChangeInput(item);
        }}
        renderLeftIcon={() => (
          <Icon
            style={styles.icon}
            color={isFocus ? '#212121' : '#ccc'}
            name={leftIconName}
            size={20}
          />
        )}
      />
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    marginBottom: 12,
    marginTop: 4,
    borderColor: '#E8ECF4',
    borderWidth: 1,
    padding: 4,
    borderRadius: 6,
  },
  dropdown: {
    height: 34,
  },
  icon: {
    marginRight: 5,
  },
  floatingLabel: {
    marginLeft: 4,
  },
  selectedTextStyle: {
    fontSize: 14,
    fontFamily: globalStyles.fontFamily.regular,
  },
  focus: {
    borderColor: '#212121',
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 14,
    fontFamily: globalStyles.fontFamily.regular,
  },
});

export default DropSelect;
