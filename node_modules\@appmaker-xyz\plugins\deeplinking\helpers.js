import { getExtensionConfig } from '@appmaker-xyz/core';

export const getActionFromSettingsRedirectUrls = ({ url }) => {
  try {
    const redirectUrls = getExtensionConfig(
      'deep-linking',
      'redirect_urls',
      false,
    );
    if (redirectUrls && redirectUrls?.length > 0) {
      let value = redirectUrls?.find((obj) => {
        if (obj?.exact_match === true) {
          return obj?.url === url;
        } else if (!obj?.exact_match && obj?.url) {
          const regex = new RegExp(obj.url);
          const isMatch = regex.test(url);
          return isMatch;
        }
      });
      return value?.appmaker_action?.appmakerAction
        ? value?.appmaker_action?.appmakerAction
        : false;
    }
    return false;
  } catch (e) {
    return false;
  }
};
