import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { json2react } from './helper';
import { createElement } from 'react';

// const jsonUI = {
//   type: 'View',
//   props: {
//     style: {
//       textAlign: 'center',
//       backgroundColor: 'red',
//       // flexDirection: 'row',
//     },
//   },
//   children: [
//     {
//       type: 'Image',
//       props: {
//         style: {
//           width: 66,
//           height: 58,
//         },
//         source: {
//           uri: 'https://reactnative.dev/img/tiny_logo.png',
//         },
//       },
//     },
//     { type: 'Text', children: 'It works!' },
//     { type: 'Text', children: 'It works! 2' },
//     {
//       type: 'View',
//       props: {
//         style: {
//           // textAlign: 'center',
//           backgroundColor: 'green',
//           height: 100,
//         },
//       },
//       children: {
//         type: 'Text',
//         children: 'This component was created from JSON',
//       },
//     },
//   ],
// };

// const jsonUI = {
//   type: 'Text',
//   children: 'It works!',
// };
export default function NativeJSONBlock({ attributes: { jsonUI } = {} }) {
  try {
    const component = json2react(jsonUI);
    console.log(component, '11221');
    return component;
  } catch (error) {
    console.log(error, 'error-native-ui');
  }
  return null;
}

const styles = StyleSheet.create({});
