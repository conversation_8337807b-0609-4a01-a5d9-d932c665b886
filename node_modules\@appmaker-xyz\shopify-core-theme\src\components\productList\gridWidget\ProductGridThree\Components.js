import React from 'react';
import { Layout, ThemeText, AppTouchable, Badge } from '@appmaker-xyz/ui';
import Icon from 'react-native-vector-icons/AntDesign';
import { useProductWishList } from '@appmaker-xyz/shopify';

export function BottomRight({ onAction, blockData, ...props }) {
  const { toggleWishList, isSaved } = useProductWishList({
    onAction,
    blockData,
  });
  return (
    <Layout
      style={[
        props.styles.overlayContainer,
        props?.__appmakerCustomStyles?.overlay_container,
      ]}>
      {props.wishList == true ? (
        <AppTouchable
          onPress={() => {
            toggleWishList && toggleWishList();
          }}
          style={[
            props.styles.wishListIcon,
            props?.__appmakerCustomStyles?.wish_list_icon_container,
          ]}>
          <Icon
            name={isSaved === false ? 'hearto' : 'heart'}
            color={isSaved === false ? '#4F4F4F' : '#1B1B1B'}
            size={props.md}
            style={props?.__appmakerCustomStyles?.wish_list_icon}
          />
        </AppTouchable>
      ) : null}
    </Layout>
  );
}
export function ProductData(props) {
  return (
    <Layout style={props.productTitle}>
      {props.show_last_few_remaining &&
      (props.show_last_few_remaining === 'true' ||
        props.show_last_few_remaining === true) ? (
        <ThemeText
          size="10"
          color={props.brandColor?.secondary}
          style={props?.__appmakerCustomStyles?.last_few_remaining_text}>
          {props.last_few_remaining_text}
        </ThemeText>
      ) : null}
      {props.in_stock == true ? null : (
        <ThemeText
          size="10"
          color={props.brandColor?.secondary}
          style={props?.__appmakerCustomStyles?.out_of_stock_text}>
          Out of Stock
        </ThemeText>
      )}
      <ThemeText
        style={props?.__appmakerCustomStyles?.product_title}
        numberOfLines={props.titleNumberOfLines}
        size="sm">
        {props.title}
      </ThemeText>
    </Layout>
  );
}
export function PriceData(props) {
  return (
    <Layout
      style={[
        props.styles.priceDataContainer,
        props?.__appmakerCustomStyles?.price_data_container,
      ]}>
      <ThemeText size="sm" style={props?.__appmakerCustomStyles?.sale_price}>
        {props.salePrice}
      </ThemeText>
      <ThemeText
        size="10"
        color={
          props?.__appmakerCustomStyles?.offer_price?.color
            ? props?.__appmakerCustomStyles?.offer_price?.color
            : props.brandColor?.primary || '#4F4F4F'
        }
        hideText={!props.onSale && !props.priceSale}
        style={props.styles.offerPrice}>
        {props.regularPrice}
      </ThemeText>
      {props.onSale && (
        <Badge
          style={props.styles.saleBadgeStyle}
          color="#16A34A"
          text={props.salePercentage}
          customStyles={props?.__appmakerCustomStyles?.sale_badge}
        />
      )}
    </Layout>
  );
}
export function ExtraContents(props) {
  return (
    <Layout style={props.styles.extraContentContainer}>
      {props.new_product ? (
        <ThemeText
          size="10"
          color="#884A49"
          style={props?.__appmakerCustomStyles?.new_product_text}>
          {props.new_product.value}
        </ThemeText>
      ) : null}
      {props.customisable ? (
        <ThemeText
          size="10"
          color="#884A49"
          style={props?.__appmakerCustomStyles?.customisable_text}>
          Customisable
        </ThemeText>
      ) : null}
    </Layout>
  );
}
