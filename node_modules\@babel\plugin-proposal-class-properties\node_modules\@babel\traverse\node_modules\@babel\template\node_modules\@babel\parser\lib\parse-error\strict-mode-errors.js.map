{"version": 3, "names": ["StrictDelete", "StrictEvalArguments", "referenceName", "StrictEvalArgumentsBinding", "bindingName", "StrictFunction", "StrictNumericEscape", "StrictOctalLiteral", "StrictWith", "exports", "default", "_default"], "sources": ["../../src/parse-error/strict-mode-errors.ts"], "sourcesContent": ["export default {\n  StrictDelete: \"Deleting local variable in strict mode.\",\n\n  // `referenceName` is the StringValue[1] of an IdentifierReference[2], which\n  // is represented as just an `Identifier`[3] in the Babel AST.\n  // 1. https://tc39.es/ecma262/#sec-static-semantics-stringvalue\n  // 2. https://tc39.es/ecma262/#prod-IdentifierReference\n  // 3. https://github.com/babel/babel/blob/main/packages/babel-parser/ast/spec.md#identifier\n  StrictEvalArguments: ({ referenceName }: { referenceName: string }) =>\n    `Assigning to '${referenceName}' in strict mode.`,\n\n  // `bindingName` is the StringValue[1] of a BindingIdentifier[2], which is\n  // represented as just an `Identifier`[3] in the Babel AST.\n  // 1. https://tc39.es/ecma262/#sec-static-semantics-stringvalue\n  // 2. https://tc39.es/ecma262/#prod-BindingIdentifier\n  // 3. https://github.com/babel/babel/blob/main/packages/babel-parser/ast/spec.md#identifier\n  StrictEvalArgumentsBinding: ({ bindingName }: { bindingName: string }) =>\n    `Binding '${bindingName}' in strict mode.`,\n\n  StrictFunction:\n    \"In strict mode code, functions can only be declared at top level or inside a block.\",\n\n  StrictNumericEscape: \"The only valid numeric escape in strict mode is '\\\\0'.\",\n\n  StrictOctalLiteral: \"Legacy octal literals are not allowed in strict mode.\",\n\n  StrictWith: \"'with' in strict mode.\",\n};\n"], "mappings": ";;;;;;eAAe;EACbA,YAAY,EAAE,yCAAyC;EAOvDC,mBAAmB,EAAEA,CAAC;IAAEC;EAAyC,CAAC,KAC/D,iBAAgBA,aAAc,mBAAkB;EAOnDC,0BAA0B,EAAEA,CAAC;IAAEC;EAAqC,CAAC,KAClE,YAAWA,WAAY,mBAAkB;EAE5CC,cAAc,EACZ,qFAAqF;EAEvFC,mBAAmB,EAAE,wDAAwD;EAE7EC,kBAAkB,EAAE,uDAAuD;EAE3EC,UAAU,EAAE;AACd,CAAC;AAAAC,OAAA,CAAAC,OAAA,GAAAC,QAAA"}