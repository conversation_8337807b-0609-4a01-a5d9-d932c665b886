import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { Layout } from '../../../components';
import { Picker } from '@appmaker-xyz/uikit/Views';
import { useApThemeState } from '../../../theme/ThemeContext';
import { font } from '@appmaker-xyz/uikit/src/styles/index';

const DropDown = ({ attributes }) => {
  const { data, containerStyle, onSelect } = attributes;
  const [selectedValue, setSelectedValue] = useState();
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });

  return (
    <Layout style={[styles.container, containerStyle]}>
      <Picker
        selectedValue={selectedValue}
        style={styles.pickerStyle}
        itemStyle={styles.pickerItem}
        mode="dropdown"
        onValueChange={(itemValue, itemIndex) => {
          setSelectedValue(itemValue);
          onSelect(itemValue);
        }}>
        {data.map((options, key) => {
          return <Picker.Item label={options.option} value={key} />;
        })}
      </Picker>
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      flex: 1,
      alignItems: 'center',
      backgroundColor: color.light,
      borderRadius: spacing.nano,
      paddingLeft: spacing.mini,
    },
    pickerStyle: {
      width: '100%',
    },
    pickerItem: {
      fontFamily: font.family.bold,
      fontSize: 35,
    },
  });
export default DropDown;
