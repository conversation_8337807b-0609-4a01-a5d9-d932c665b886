{"version": 3, "names": ["_defineAccessor", "type", "obj", "key", "fn", "desc", "configurable", "enumerable", "Object", "defineProperty"], "sources": ["../../src/helpers/defineAccessor.js"], "sourcesContent": ["/* @minVersion 7.20.7 */\n\nexport default function _defineAccessor(type, obj, key, fn) {\n  var desc = { configurable: true, enumerable: true };\n  // type should be \"get\" or \"set\"\n  desc[type] = fn;\n  return Object.defineProperty(obj, key, desc);\n}\n"], "mappings": ";;;;;;AAEe,SAASA,eAAe,CAACC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAE;EAC1D,IAAIC,IAAI,GAAG;IAAEC,YAAY,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAK,CAAC;EAEnDF,IAAI,CAACJ,IAAI,CAAC,GAAGG,EAAE;EACf,OAAOI,MAAM,CAACC,cAAc,CAACP,GAAG,EAAEC,GAAG,EAAEE,IAAI,CAAC;AAC9C"}