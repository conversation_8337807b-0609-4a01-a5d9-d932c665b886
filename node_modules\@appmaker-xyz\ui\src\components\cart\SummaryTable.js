import React from 'react';
import { Layout, ThemeText } from '../atoms';
import { useCart } from '@appmaker-xyz/shopify';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';
import { getExtensionConfig } from '@appmaker-xyz/core';

function Row(props) {
  const { title, data, fontFamily = 'regular' } = props;
  const { styles } = useStyles(stylesheet);
  return (
    <Layout style={styles.row}>
      <ThemeText
        testId={`${props?.testId}-title`}
        fontFamily={fontFamily}
        style={styles.commonText}>
        {title}
      </ThemeText>
      <ThemeText
        testId={props?.testId}
        fontFamily={fontFamily}
        style={styles.commonText}>
        {data}
      </ThemeText>
    </Layout>
  );
}

function FreeDelivery({ isFreeShipping = true, shippingFeeWithCurrency }) {
  const { styles } = useStyles(stylesheet);
  return (
    <Layout style={styles.row}>
      <Layout style={styles.flex}>
        <ThemeText>Delivery Fee</ThemeText>
      </Layout>
      <Layout style={styles.flex}>
        {isFreeShipping ? (
          <ThemeText style={styles.strikeText}>
            {shippingFeeWithCurrency}
          </ThemeText>
        ) : (
          <ThemeText>{shippingFeeWithCurrency}</ThemeText>
        )}
        {isFreeShipping ? <ThemeText color="#027702">Free</ThemeText> : null}
      </Layout>
    </Layout>
  );
}

const SummaryTable = () => {
  const {
    totalQuantity,
    cartTotalPayableWithCurrency,
    cartTotalPricelwithCurrency,
    cartTotalSavingsWithCurrency,
    cartTotalSavings,
  } = useCart();
  const itemsText = totalQuantity > 1 ? 'Items' : 'Item';
  const { styles } = useStyles(stylesheet);
  const hide_total_mrp_cart = getExtensionConfig('shopify','hide_total_mrp_cart',false);
  const hide_total_amount_cart = getExtensionConfig('shopify','hide_total_amount_cart',false);
  return (
    <Layout style={styles.container}>
      <Layout style={styles.header}>
        <ThemeText fontFamily="medium" size="md" style={styles.commonText}>
          <ThemeText fontFamily="medium" size="md" style={styles.commonText}>
            Price Details
          </ThemeText>{' '}
          ({totalQuantity} {itemsText})
        </ThemeText>
      </Layout>
      {!hide_total_mrp_cart || cartTotalSavings > 0 ? (
        <Layout style={styles.body}>
          {!hide_total_mrp_cart && (
            <Row
              testId="cart-mrp"
              title="Total MRP"
              data={cartTotalPricelwithCurrency}
            />
          )}
          {cartTotalSavings > 0 ? (
            <Row
              testId="cart-savings"
              title="Savings"
              data={`- ${cartTotalSavingsWithCurrency}`}
            />
          ) : null}
          {/* <Row title="Coupon discount" data={cartDiscountSavingsWithCurrency} /> */}
          {/* <FreeDelivery
          isFreeShipping={isFreeShipping}
          shippingFeeWithCurrency={shippingFeeWithCurrency}
        /> */}
        </Layout>
      ) : null}
      {hide_total_amount_cart ? null : (
        <Layout style={styles.footer}>
          <Row
            testId="cart-amount"
            title="Total amount"
            data={cartTotalPayableWithCurrency}
            fontFamily="bold"
          />
        </Layout>
      )}
    </Layout>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.background,
    marginBottom: 4,
  },
  header: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderBottomColor: theme.colors.separator,
    borderBottomWidth: 1,
  },
  body: {
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  commonText: {
    color: theme.colors.text,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 6,
  },
  flex: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  strikeText: {
    textDecorationLine: 'line-through',
    marginRight: 4,
  },
  truck: {
    marginRight: 4,
  },
  footer: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderTopColor: theme.colors.separator,
    borderTopWidth: 1,
  },
}));

export default SummaryTable;
