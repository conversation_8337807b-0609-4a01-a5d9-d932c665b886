import React from 'react';
import { I18nManager } from 'react-native';
import { BlockCard } from '@appmaker-xyz/ui';
import ProductList from './product/ProductList';

export const ShopifyCustomProductScroller = ({
  attributes,
  BlocksView,
  innerBlocks,
  ...props
}) => {
  const backgroundColor = attributes?.backgroundColor || '#FFFFFF';
  const finalAttributes = {
    productType:
      '{{blockItem.node.variants.edges.length > 1 ? "variable" : "normal"}}',
    wishlist: true,
    isInsideCartPage: !!attributes?.isInsideCartPage,
    __appmakerStylesClassName: 'productGridWidgetCustomStyles',
    ...(attributes && attributes),
  };
  return (
    <BlockCard
      {...props}
      attributes={{
        ...attributes,
        accessButton: attributes.showViewMoreButton ? attributes.ctaText : '',
        childContainerStyle: { paddingBottom: 12 },
        wholeContainerStyle: {
          backgroundColor: backgroundColor,
        },
      }}>
      <ProductList
        blockAttributes={finalAttributes}
        productIds={attributes?.ids}
        useFlatlist
        horizontal={true}
        disablePagination={true}
        limit={10}
        surface={'appmaker-custom-product-scroller'}
        referrer={{
          name: 'appmaker-custom-product-scroller',
          type: 'product-scroller',
          title: attributes?.title,
        }}
        flashListProps={{
          style: {
            backgroundColor: backgroundColor,
          },
          showsHorizontalScrollIndicator: false,
          ...(I18nManager.isRTL && {
            maintainVisibleContentPosition: {
              minIndexForVisible: 0,
              autoscrollToTopThreshold: 0,
            },
          }),
        }}
        layoutProps={{
          loadingLayout: 'product-scroller',
        }}
        {...props}
      />
    </BlockCard>
  );
};
