{"version": 3, "names": ["_traverseNode", "require", "_index", "call", "key", "opts", "debug", "node", "_call", "_opts$this$node$type", "type", "fns", "fn", "ret", "state", "then", "Error", "_traverseFlags", "isDenylisted", "_this$opts$denylist", "denylist", "blacklist", "indexOf", "restoreContext", "path", "context", "visit", "_this$opts$shouldSkip", "_this$opts", "shouldSkip", "currentContext", "shouldStop", "traverseNode", "scope", "<PERSON><PERSON><PERSON><PERSON>", "skip", "<PERSON><PERSON><PERSON>", "stop", "SHOULD_SKIP", "SHOULD_STOP", "setScope", "_this$opts2", "_this$scope", "noScope", "parentPath", "<PERSON><PERSON><PERSON>", "isMethod", "isSwitchStatement", "target", "_path$opts", "getScope", "init", "setContext", "resync", "removed", "_resyncParent", "_resyncList", "_resync<PERSON>ey", "parent", "container", "Array", "isArray", "i", "length", "<PERSON><PERSON><PERSON>", "Object", "keys", "inList", "newContainer", "_resyncRemoved", "_mark<PERSON><PERSON>oved", "popContext", "contexts", "pop", "undefined", "pushContext", "push", "setup", "_this$node", "requeue", "pathToQueue", "maybeQueue", "_getQueueContexts"], "sources": ["../../src/path/context.ts"], "sourcesContent": ["// This file contains methods responsible for maintaining a TraversalContext.\n\nimport { traverseNode } from \"../traverse-node\";\nimport { SHOULD_SKIP, SHOULD_STOP } from \"./index\";\nimport type TraversalContext from \"../context\";\nimport type { VisitPhase } from \"../types\";\nimport type NodePath from \"./index\";\nimport type * as t from \"@babel/types\";\n\nexport function call(this: NodePath, key: VisitPhase): boolean {\n  const opts = this.opts;\n\n  this.debug(key);\n\n  if (this.node) {\n    if (this._call(opts[key])) return true;\n  }\n\n  if (this.node) {\n    return this._call(opts[this.node.type]?.[key]);\n  }\n\n  return false;\n}\n\nexport function _call(this: NodePath, fns?: Array<Function>): boolean {\n  if (!fns) return false;\n\n  for (const fn of fns) {\n    if (!fn) continue;\n\n    const node = this.node;\n    if (!node) return true;\n\n    const ret = fn.call(this.state, this, this.state);\n    if (ret && typeof ret === \"object\" && typeof ret.then === \"function\") {\n      throw new Error(\n        `You appear to be using a plugin with an async traversal visitor, ` +\n          `which your current version of Babel does not support. ` +\n          `If you're using a published plugin, you may need to upgrade ` +\n          `your @babel/core version.`,\n      );\n    }\n    if (ret) {\n      throw new Error(`Unexpected return value from visitor method ${fn}`);\n    }\n\n    // node has been replaced, it will have been requeued\n    if (this.node !== node) return true;\n\n    // this.shouldSkip || this.shouldStop || this.removed\n    if (this._traverseFlags > 0) return true;\n  }\n\n  return false;\n}\n\nexport function isDenylisted(this: NodePath): boolean {\n  // @ts-expect-error TODO(Babel 8): Remove blacklist\n  const denylist = this.opts.denylist ?? this.opts.blacklist;\n  return denylist && denylist.indexOf(this.node.type) > -1;\n}\n\n// TODO: Remove in Babel 8\nexport { isDenylisted as isBlacklisted };\n\nfunction restoreContext(path: NodePath, context: TraversalContext) {\n  if (path.context !== context) {\n    path.context = context;\n    path.state = context.state;\n    path.opts = context.opts;\n  }\n}\n\nexport function visit(this: NodePath): boolean {\n  if (!this.node) {\n    return false;\n  }\n\n  if (this.isDenylisted()) {\n    return false;\n  }\n\n  if (this.opts.shouldSkip?.(this)) {\n    return false;\n  }\n\n  const currentContext = this.context;\n  // Note: We need to check \"this.shouldSkip\" first because\n  // another visitor can set it to true. Usually .shouldSkip is false\n  // before calling the enter visitor, but it can be true in case of\n  // a requeued node (e.g. by .replaceWith()) that is then marked\n  // with .skip().\n  if (this.shouldSkip || this.call(\"enter\")) {\n    this.debug(\"Skip...\");\n    return this.shouldStop;\n  }\n  restoreContext(this, currentContext);\n\n  this.debug(\"Recursing into...\");\n  this.shouldStop = traverseNode(\n    this.node,\n    this.opts,\n    this.scope,\n    this.state,\n    this,\n    this.skipKeys,\n  );\n\n  restoreContext(this, currentContext);\n\n  this.call(\"exit\");\n\n  return this.shouldStop;\n}\n\nexport function skip(this: NodePath) {\n  this.shouldSkip = true;\n}\n\nexport function skipKey(this: NodePath, key: string) {\n  if (this.skipKeys == null) {\n    this.skipKeys = {};\n  }\n  this.skipKeys[key] = true;\n}\n\nexport function stop(this: NodePath) {\n  // this.shouldSkip = true; this.shouldStop = true;\n  this._traverseFlags |= SHOULD_SKIP | SHOULD_STOP;\n}\n\nexport function setScope(this: NodePath) {\n  if (this.opts?.noScope) return;\n\n  let path = this.parentPath;\n\n  if (\n    // Skip method scope if is computed method key or decorator expression\n    ((this.key === \"key\" || this.listKey === \"decorators\") &&\n      path.isMethod()) ||\n    // Skip switch scope if for discriminant (`x` in `switch (x) {}`).\n    (this.key === \"discriminant\" && path.isSwitchStatement())\n  ) {\n    path = path.parentPath;\n  }\n\n  let target;\n  while (path && !target) {\n    if (path.opts?.noScope) return;\n\n    target = path.scope;\n    path = path.parentPath;\n  }\n\n  this.scope = this.getScope(target);\n  this.scope?.init();\n}\n\nexport function setContext<S = unknown>(\n  this: NodePath,\n  context?: TraversalContext<S>,\n) {\n  if (this.skipKeys != null) {\n    this.skipKeys = {};\n  }\n  // this.shouldSkip = false; this.shouldStop = false; this.removed = false;\n  this._traverseFlags = 0;\n\n  if (context) {\n    this.context = context;\n    this.state = context.state;\n    // Discard the S type parameter from contect.opts\n    this.opts = context.opts as typeof this.opts;\n  }\n\n  this.setScope();\n\n  return this;\n}\n\n/**\n * Here we resync the node paths `key` and `container`. If they've changed according\n * to what we have stored internally then we attempt to resync by crawling and looking\n * for the new values.\n */\n\nexport function resync(this: NodePath) {\n  if (this.removed) return;\n\n  this._resyncParent();\n  this._resyncList();\n  this._resyncKey();\n  //this._resyncRemoved();\n}\n\nexport function _resyncParent(this: NodePath) {\n  if (this.parentPath) {\n    this.parent = this.parentPath.node;\n  }\n}\n\nexport function _resyncKey(this: NodePath) {\n  if (!this.container) return;\n\n  if (\n    this.node ===\n    // @ts-expect-error this.key should present in this.container\n    this.container[this.key]\n  ) {\n    return;\n  }\n\n  // grrr, path key is out of sync. this is likely due to a modification to the AST\n  // not done through our path APIs\n\n  if (Array.isArray(this.container)) {\n    for (let i = 0; i < this.container.length; i++) {\n      if (this.container[i] === this.node) {\n        this.setKey(i);\n        return;\n      }\n    }\n  } else {\n    for (const key of Object.keys(this.container)) {\n      // @ts-expect-error this.key should present in this.container\n      if (this.container[key] === this.node) {\n        this.setKey(key);\n        return;\n      }\n    }\n  }\n\n  // ¯\\_(ツ)_/¯ who knows where it's gone lol\n  this.key = null;\n}\n\nexport function _resyncList(this: NodePath) {\n  if (!this.parent || !this.inList) return;\n\n  const newContainer =\n    // @ts-expect-error this.listKey should present in this.parent\n    this.parent[this.listKey];\n  if (this.container === newContainer) return;\n\n  // container is out of sync. this is likely the result of it being reassigned\n  this.container = newContainer || null;\n}\n\nexport function _resyncRemoved(this: NodePath) {\n  if (\n    this.key == null ||\n    !this.container ||\n    // @ts-expect-error this.key should present in this.container\n    this.container[this.key] !== this.node\n  ) {\n    this._markRemoved();\n  }\n}\n\nexport function popContext(this: NodePath) {\n  this.contexts.pop();\n  if (this.contexts.length > 0) {\n    this.setContext(this.contexts[this.contexts.length - 1]);\n  } else {\n    this.setContext(undefined);\n  }\n}\n\nexport function pushContext(this: NodePath, context: TraversalContext) {\n  this.contexts.push(context);\n  this.setContext(context);\n}\n\nexport function setup(\n  this: NodePath,\n  parentPath: NodePath | undefined,\n  container: t.Node | t.Node[],\n  listKey: string,\n  key: string | number,\n) {\n  this.listKey = listKey;\n  this.container = container;\n\n  this.parentPath = parentPath || this.parentPath;\n  this.setKey(key);\n}\n\nexport function setKey(this: NodePath, key: string | number) {\n  this.key = key;\n  this.node =\n    // @ts-expect-error this.key must present in this.container\n    this.container[this.key];\n  this.type = this.node?.type;\n}\n\nexport function requeue(this: NodePath, pathToQueue = this) {\n  if (pathToQueue.removed) return;\n\n  // If a path is skipped, and then replaced with a\n  // new one, the new one shouldn't probably be skipped.\n  if (process.env.BABEL_8_BREAKING) {\n    pathToQueue.shouldSkip = false;\n  }\n\n  // TODO(loganfsmyth): This should be switched back to queue in parent contexts\n  // automatically once #2892 and #4135 have been resolved. See #4140.\n  // let contexts = this._getQueueContexts();\n  const contexts = this.contexts;\n\n  for (const context of contexts) {\n    context.maybeQueue(pathToQueue);\n  }\n}\n\nexport function _getQueueContexts(this: NodePath) {\n  let path = this;\n  let contexts = this.contexts;\n  while (!contexts.length) {\n    path = path.parentPath;\n    if (!path) break;\n    contexts = path.contexts;\n  }\n  return contexts;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAMO,SAASE,IAAIA,CAAiBC,GAAe,EAAW;EAC7D,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI;EAEtB,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC;EAEf,IAAI,IAAI,CAACG,IAAI,EAAE;IACb,IAAI,IAAI,CAACC,KAAK,CAACH,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE,OAAO,IAAI;EACxC;EAEA,IAAI,IAAI,CAACG,IAAI,EAAE;IAAA,IAAAE,oBAAA;IACb,OAAO,IAAI,CAACD,KAAK,EAAAC,oBAAA,GAACJ,IAAI,CAAC,IAAI,CAACE,IAAI,CAACG,IAAI,CAAC,qBAApBD,oBAAA,CAAuBL,GAAG,CAAC,CAAC;EAChD;EAEA,OAAO,KAAK;AACd;AAEO,SAASI,KAAKA,CAAiBG,GAAqB,EAAW;EACpE,IAAI,CAACA,GAAG,EAAE,OAAO,KAAK;EAEtB,KAAK,MAAMC,EAAE,IAAID,GAAG,EAAE;IACpB,IAAI,CAACC,EAAE,EAAE;IAET,MAAML,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtB,MAAMM,GAAG,GAAGD,EAAE,CAACT,IAAI,CAAC,IAAI,CAACW,KAAK,EAAE,IAAI,EAAE,IAAI,CAACA,KAAK,CAAC;IACjD,IAAID,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,CAACE,IAAI,KAAK,UAAU,EAAE;MACpE,MAAM,IAAIC,KAAK,CACZ,mEAAkE,GAChE,wDAAuD,GACvD,8DAA6D,GAC7D,2BACL,CAAC;IACH;IACA,IAAIH,GAAG,EAAE;MACP,MAAM,IAAIG,KAAK,CAAE,+CAA8CJ,EAAG,EAAC,CAAC;IACtE;IAGA,IAAI,IAAI,CAACL,IAAI,KAAKA,IAAI,EAAE,OAAO,IAAI;IAGnC,IAAI,IAAI,CAACU,cAAc,GAAG,CAAC,EAAE,OAAO,IAAI;EAC1C;EAEA,OAAO,KAAK;AACd;AAEO,SAASC,YAAYA,CAAA,EAA0B;EAAA,IAAAC,mBAAA;EAEpD,MAAMC,QAAQ,IAAAD,mBAAA,GAAG,IAAI,CAACd,IAAI,CAACe,QAAQ,YAAAD,mBAAA,GAAI,IAAI,CAACd,IAAI,CAACgB,SAAS;EAC1D,OAAOD,QAAQ,IAAIA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACf,IAAI,CAACG,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1D;AAKA,SAASa,cAAcA,CAACC,IAAc,EAAEC,OAAyB,EAAE;EACjE,IAAID,IAAI,CAACC,OAAO,KAAKA,OAAO,EAAE;IAC5BD,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtBD,IAAI,CAACV,KAAK,GAAGW,OAAO,CAACX,KAAK;IAC1BU,IAAI,CAACnB,IAAI,GAAGoB,OAAO,CAACpB,IAAI;EAC1B;AACF;AAEO,SAASqB,KAAKA,CAAA,EAA0B;EAAA,IAAAC,qBAAA,EAAAC,UAAA;EAC7C,IAAI,CAAC,IAAI,CAACrB,IAAI,EAAE;IACd,OAAO,KAAK;EACd;EAEA,IAAI,IAAI,CAACW,YAAY,CAAC,CAAC,EAAE;IACvB,OAAO,KAAK;EACd;EAEA,KAAAS,qBAAA,GAAI,CAAAC,UAAA,OAAI,CAACvB,IAAI,EAACwB,UAAU,aAApBF,qBAAA,CAAAxB,IAAA,CAAAyB,UAAA,EAAuB,IAAI,CAAC,EAAE;IAChC,OAAO,KAAK;EACd;EAEA,MAAME,cAAc,GAAG,IAAI,CAACL,OAAO;EAMnC,IAAI,IAAI,CAACI,UAAU,IAAI,IAAI,CAAC1B,IAAI,CAAC,OAAO,CAAC,EAAE;IACzC,IAAI,CAACG,KAAK,CAAC,SAAS,CAAC;IACrB,OAAO,IAAI,CAACyB,UAAU;EACxB;EACAR,cAAc,CAAC,IAAI,EAAEO,cAAc,CAAC;EAEpC,IAAI,CAACxB,KAAK,CAAC,mBAAmB,CAAC;EAC/B,IAAI,CAACyB,UAAU,GAAG,IAAAC,0BAAY,EAC5B,IAAI,CAACzB,IAAI,EACT,IAAI,CAACF,IAAI,EACT,IAAI,CAAC4B,KAAK,EACV,IAAI,CAACnB,KAAK,EACV,IAAI,EACJ,IAAI,CAACoB,QACP,CAAC;EAEDX,cAAc,CAAC,IAAI,EAAEO,cAAc,CAAC;EAEpC,IAAI,CAAC3B,IAAI,CAAC,MAAM,CAAC;EAEjB,OAAO,IAAI,CAAC4B,UAAU;AACxB;AAEO,SAASI,IAAIA,CAAA,EAAiB;EACnC,IAAI,CAACN,UAAU,GAAG,IAAI;AACxB;AAEO,SAASO,OAAOA,CAAiBhC,GAAW,EAAE;EACnD,IAAI,IAAI,CAAC8B,QAAQ,IAAI,IAAI,EAAE;IACzB,IAAI,CAACA,QAAQ,GAAG,CAAC,CAAC;EACpB;EACA,IAAI,CAACA,QAAQ,CAAC9B,GAAG,CAAC,GAAG,IAAI;AAC3B;AAEO,SAASiC,IAAIA,CAAA,EAAiB;EAEnC,IAAI,CAACpB,cAAc,IAAIqB,kBAAW,GAAGC,kBAAW;AAClD;AAEO,SAASC,QAAQA,CAAA,EAAiB;EAAA,IAAAC,WAAA,EAAAC,WAAA;EACvC,KAAAD,WAAA,GAAI,IAAI,CAACpC,IAAI,aAAToC,WAAA,CAAWE,OAAO,EAAE;EAExB,IAAInB,IAAI,GAAG,IAAI,CAACoB,UAAU;EAE1B,IAEG,CAAC,IAAI,CAACxC,GAAG,KAAK,KAAK,IAAI,IAAI,CAACyC,OAAO,KAAK,YAAY,KACnDrB,IAAI,CAACsB,QAAQ,CAAC,CAAC,IAEhB,IAAI,CAAC1C,GAAG,KAAK,cAAc,IAAIoB,IAAI,CAACuB,iBAAiB,CAAC,CAAE,EACzD;IACAvB,IAAI,GAAGA,IAAI,CAACoB,UAAU;EACxB;EAEA,IAAII,MAAM;EACV,OAAOxB,IAAI,IAAI,CAACwB,MAAM,EAAE;IAAA,IAAAC,UAAA;IACtB,KAAAA,UAAA,GAAIzB,IAAI,CAACnB,IAAI,aAAT4C,UAAA,CAAWN,OAAO,EAAE;IAExBK,MAAM,GAAGxB,IAAI,CAACS,KAAK;IACnBT,IAAI,GAAGA,IAAI,CAACoB,UAAU;EACxB;EAEA,IAAI,CAACX,KAAK,GAAG,IAAI,CAACiB,QAAQ,CAACF,MAAM,CAAC;EAClC,CAAAN,WAAA,OAAI,CAACT,KAAK,qBAAVS,WAAA,CAAYS,IAAI,CAAC,CAAC;AACpB;AAEO,SAASC,UAAUA,CAExB3B,OAA6B,EAC7B;EACA,IAAI,IAAI,CAACS,QAAQ,IAAI,IAAI,EAAE;IACzB,IAAI,CAACA,QAAQ,GAAG,CAAC,CAAC;EACpB;EAEA,IAAI,CAACjB,cAAc,GAAG,CAAC;EAEvB,IAAIQ,OAAO,EAAE;IACX,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACX,KAAK,GAAGW,OAAO,CAACX,KAAK;IAE1B,IAAI,CAACT,IAAI,GAAGoB,OAAO,CAACpB,IAAwB;EAC9C;EAEA,IAAI,CAACmC,QAAQ,CAAC,CAAC;EAEf,OAAO,IAAI;AACb;AAQO,SAASa,MAAMA,CAAA,EAAiB;EACrC,IAAI,IAAI,CAACC,OAAO,EAAE;EAElB,IAAI,CAACC,aAAa,CAAC,CAAC;EACpB,IAAI,CAACC,WAAW,CAAC,CAAC;EAClB,IAAI,CAACC,UAAU,CAAC,CAAC;AAEnB;AAEO,SAASF,aAAaA,CAAA,EAAiB;EAC5C,IAAI,IAAI,CAACX,UAAU,EAAE;IACnB,IAAI,CAACc,MAAM,GAAG,IAAI,CAACd,UAAU,CAACrC,IAAI;EACpC;AACF;AAEO,SAASkD,UAAUA,CAAA,EAAiB;EACzC,IAAI,CAAC,IAAI,CAACE,SAAS,EAAE;EAErB,IACE,IAAI,CAACpD,IAAI,KAET,IAAI,CAACoD,SAAS,CAAC,IAAI,CAACvD,GAAG,CAAC,EACxB;IACA;EACF;EAKA,IAAIwD,KAAK,CAACC,OAAO,CAAC,IAAI,CAACF,SAAS,CAAC,EAAE;IACjC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,SAAS,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MAC9C,IAAI,IAAI,CAACH,SAAS,CAACG,CAAC,CAAC,KAAK,IAAI,CAACvD,IAAI,EAAE;QACnC,IAAI,CAACyD,MAAM,CAACF,CAAC,CAAC;QACd;MACF;IACF;EACF,CAAC,MAAM;IACL,KAAK,MAAM1D,GAAG,IAAI6D,MAAM,CAACC,IAAI,CAAC,IAAI,CAACP,SAAS,CAAC,EAAE;MAE7C,IAAI,IAAI,CAACA,SAAS,CAACvD,GAAG,CAAC,KAAK,IAAI,CAACG,IAAI,EAAE;QACrC,IAAI,CAACyD,MAAM,CAAC5D,GAAG,CAAC;QAChB;MACF;IACF;EACF;EAGA,IAAI,CAACA,GAAG,GAAG,IAAI;AACjB;AAEO,SAASoD,WAAWA,CAAA,EAAiB;EAC1C,IAAI,CAAC,IAAI,CAACE,MAAM,IAAI,CAAC,IAAI,CAACS,MAAM,EAAE;EAElC,MAAMC,YAAY,GAEhB,IAAI,CAACV,MAAM,CAAC,IAAI,CAACb,OAAO,CAAC;EAC3B,IAAI,IAAI,CAACc,SAAS,KAAKS,YAAY,EAAE;EAGrC,IAAI,CAACT,SAAS,GAAGS,YAAY,IAAI,IAAI;AACvC;AAEO,SAASC,cAAcA,CAAA,EAAiB;EAC7C,IACE,IAAI,CAACjE,GAAG,IAAI,IAAI,IAChB,CAAC,IAAI,CAACuD,SAAS,IAEf,IAAI,CAACA,SAAS,CAAC,IAAI,CAACvD,GAAG,CAAC,KAAK,IAAI,CAACG,IAAI,EACtC;IACA,IAAI,CAAC+D,YAAY,CAAC,CAAC;EACrB;AACF;AAEO,SAASC,UAAUA,CAAA,EAAiB;EACzC,IAAI,CAACC,QAAQ,CAACC,GAAG,CAAC,CAAC;EACnB,IAAI,IAAI,CAACD,QAAQ,CAACT,MAAM,GAAG,CAAC,EAAE;IAC5B,IAAI,CAACX,UAAU,CAAC,IAAI,CAACoB,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACT,MAAM,GAAG,CAAC,CAAC,CAAC;EAC1D,CAAC,MAAM;IACL,IAAI,CAACX,UAAU,CAACsB,SAAS,CAAC;EAC5B;AACF;AAEO,SAASC,WAAWA,CAAiBlD,OAAyB,EAAE;EACrE,IAAI,CAAC+C,QAAQ,CAACI,IAAI,CAACnD,OAAO,CAAC;EAC3B,IAAI,CAAC2B,UAAU,CAAC3B,OAAO,CAAC;AAC1B;AAEO,SAASoD,KAAKA,CAEnBjC,UAAgC,EAChCe,SAA4B,EAC5Bd,OAAe,EACfzC,GAAoB,EACpB;EACA,IAAI,CAACyC,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACc,SAAS,GAAGA,SAAS;EAE1B,IAAI,CAACf,UAAU,GAAGA,UAAU,IAAI,IAAI,CAACA,UAAU;EAC/C,IAAI,CAACoB,MAAM,CAAC5D,GAAG,CAAC;AAClB;AAEO,SAAS4D,MAAMA,CAAiB5D,GAAoB,EAAE;EAAA,IAAA0E,UAAA;EAC3D,IAAI,CAAC1E,GAAG,GAAGA,GAAG;EACd,IAAI,CAACG,IAAI,GAEP,IAAI,CAACoD,SAAS,CAAC,IAAI,CAACvD,GAAG,CAAC;EAC1B,IAAI,CAACM,IAAI,IAAAoE,UAAA,GAAG,IAAI,CAACvE,IAAI,qBAATuE,UAAA,CAAWpE,IAAI;AAC7B;AAEO,SAASqE,OAAOA,CAAiBC,WAAW,GAAG,IAAI,EAAE;EAC1D,IAAIA,WAAW,CAAC1B,OAAO,EAAE;EAAO;EAWhC,MAAMkB,QAAQ,GAAG,IAAI,CAACA,QAAQ;EAE9B,KAAK,MAAM/C,OAAO,IAAI+C,QAAQ,EAAE;IAC9B/C,OAAO,CAACwD,UAAU,CAACD,WAAW,CAAC;EACjC;AACF;AAEO,SAASE,iBAAiBA,CAAA,EAAiB;EAChD,IAAI1D,IAAI,GAAG,IAAI;EACf,IAAIgD,QAAQ,GAAG,IAAI,CAACA,QAAQ;EAC5B,OAAO,CAACA,QAAQ,CAACT,MAAM,EAAE;IACvBvC,IAAI,GAAGA,IAAI,CAACoB,UAAU;IACtB,IAAI,CAACpB,IAAI,EAAE;IACXgD,QAAQ,GAAGhD,IAAI,CAACgD,QAAQ;EAC1B;EACA,OAAOA,QAAQ;AACjB"}