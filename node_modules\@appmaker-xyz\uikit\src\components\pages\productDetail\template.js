const productData = {
  name: 'Apple',
  price: '200',
  images: [],
};
const template = {
  version: 'v1.1',
  blocks: [
    {
      name: 'appmaker/product-image',
      clientId: 'dfaa9356-7049-44ee-baed-75fc5413ca84',
      attributes: {
        dataSource: {
          source: 'inline',
          mapValues: {
            imgUri: 'jetpack_featured_media_url',
            title: 'Name',
            timeStamp: 'date_gmt',
            dataSource: 'WEQ',
            name: '',
          },
        },
        selectedImage: '',
        internal_name: 'appmaker/notification',
        ctaText: 'View more button',
        dataSourceURL:
          'https://public-app-3fa84.firebaseio.com/1YJGaK5tOtQ1IMBrB0XD7pdFXt_1HjzC0QxTdri1NlWA/I2E.json',
        page_name: 'VlKxq0OJSAIHMNacuX9F',
        name: 'sample title',
        title: 'title',
        appmakerAction: {
          params: {
            value: 'IqRJugwiTftFrh6vhZKa',
          },
          OPEN_INAPP_PAGE_id: {
            id: 'IqRJugwiTftFrh6vhZKa',
            label: 'Detail page',
          },
          action: 'OPEN_INAPP_PAGE',
        },
        event_name: 'widget_clicked',
      },
      innerBlocks: [],
      isValid: true,
    },
  ],
  type: 'NORMAL',
  attributes: false,
  title: 'WEQ List',
  language: 'default',
  parentID: 'VlKxq0OJSAIHMNacuX9F',
  id: 'VlKxq0OJSAIHMNacuX9F',
  status: 'active',
};
