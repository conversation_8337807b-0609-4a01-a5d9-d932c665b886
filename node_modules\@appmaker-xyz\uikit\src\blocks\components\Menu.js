import React, { useEffect, useState } from 'react';
import { Text } from 'react-native';
// const { width } = Dimensions.get('window');

const Menu = ({
  attributes,
  BlockItem,
  BlocksView,
  innerBlocks,
  onPress,
  onAction,
  clientId,
}) => {
  // console.log('attributes');
  // console.log(BlocksView);
  // return null;
  return (
    <BlocksView
      onAction={onAction}
      inAppPage={{
        blocks: innerBlocks,
        attributes: {
          // horizontal: false,
          // numColumns: 2,
        },
      }}
    />
  );
  // return <Text>{JSON.stringify(innerBlocks, null, 2)}</Text>;
};

export default Menu;
