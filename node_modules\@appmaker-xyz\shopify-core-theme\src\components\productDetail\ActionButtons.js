import React from 'react';
import { StyleSheet, ActivityIndicator } from 'react-native';
import { Layout, ThemeText, AppTouchable } from '@appmaker-xyz/ui';
import { useProductDetailV2 } from '@appmaker-xyz/shopify';
import { getExtensionAsBoolean, getExtensionConfig } from '@appmaker-xyz/core';

const Button = ({
  onPress,
  bgColor,
  textColor,
  isLoading = false,
  title,
  position,
  disableBlockPadding,
}) => {
  const buttonStyles = [
    styles.button,
    { backgroundColor: bgColor, borderColor: bgColor },
    position === 'left' && styles.leftButton,
    position === 'right' && styles.rightButton,
    disableBlockPadding && styles.borderRadiusDisabled,
  ];
  return (
    <AppTouchable onPress={onPress} style={buttonStyles} disabled={isLoading}>
      {isLoading ? (
        <ActivityIndicator color={textColor} />
      ) : (
        <ThemeText
          color={textColor}
          size="md"
          fontFamily="medium"
          style={styles.buttonText}>
          {title}
        </ThemeText>
      )}
    </AppTouchable>
  );
};

const ActionButtons = (props) => {
  const {
    addToCart,
    addtoCartLoading,
    currentVariantInStock,
    buyNow,
    buyNowLoading,
    isBuyNowOnly,
    addToCartText,
    addToCartButtonColor,
    addCartFontColor,
    buyNowText,
    buyNowButtonColor,
    buyNowFontColor,
    outOfStockText,
    outOfStockButtonColor,
    outOfStockFontColor,
    isDisablePurchase,
    preOrderEnabled
  } = useProductDetailV2(props);
  const { showInReverseOrder } = props.attributes;
  const containerStyle = [styles.containerStyle];

  const disableBlockPadding = getExtensionAsBoolean?.(
    'shopify',
    'disable_pdp_action_buttons_padding',
    false,
  );

  const showBuyNowButtonOption = getExtensionConfig?.(
    'shopify',
    'enable_buynow_in_pdp',
    1,
  );

  const showBuyNowButton = showBuyNowButtonOption == 1;

  if (disableBlockPadding) {
    containerStyle.push(styles.noPadding);
  }

  const buyNowAction = () => {
    if (isBuyNowOnly) {
      buyNow && buyNow();
    } else {
      addToCart({ buynow: true });
    }
  };
  const PDPButtons = () => {
    if (isDisablePurchase) {
      return null;
    }

    return (
      <Layout
        style={[
          containerStyle,
          showInReverseOrder ? styles.reverseContainer : styles.container,
        ]}>
        {showBuyNowButton ? (
          <Button
            title={buyNowText}
            onPress={buyNowAction}
            bgColor={buyNowButtonColor}
            textColor={buyNowFontColor}
            isLoading={buyNowLoading}
            position={showInReverseOrder ? 'right' : 'left'}
            disableBlockPadding={disableBlockPadding}
          />
        ) : null}
        <Button
          title={addToCartText}
          onPress={() => addToCart({ buynow: false })}
          bgColor={addToCartButtonColor}
          textColor={addCartFontColor}
          isLoading={addtoCartLoading}
          position={
            !showBuyNowButton ? null : showInReverseOrder ? 'left' : 'right'
          }
          disableBlockPadding={disableBlockPadding}
        />
      </Layout>
    );
  };

  const OutOfStockButtons = () => {
    return (
      <Layout style={[styles.container, containerStyle]}>
        <Button
          title={outOfStockText}
          onPress={() => {}}
          bgColor={outOfStockButtonColor}
          textColor={outOfStockFontColor}
          right={false}
          disableBlockPadding={disableBlockPadding}
        />
      </Layout>
    );
  };

  const LoaderButton = () => {
    return (
      <Layout style={[styles.container, containerStyle]}>
        <Button
          bgColor={addToCartButtonColor}
          textColor={addCartFontColor}
          right={false}
          isLoading={true}
        />
      </Layout>
    );
  };

  return typeof currentVariantInStock !== 'boolean' ? (
    <LoaderButton />
  ) : !currentVariantInStock ? (
    preOrderEnabled ? (
      <Layout style={[styles.container, containerStyle]}>
        <Button
          title="Pre Order"
          onPress={addToCart}
          bgColor={outOfStockButtonColor}
          textColor={outOfStockFontColor}
          right={false}
          isLoading={addtoCartLoading}
        />
      </Layout>
    ) : <OutOfStockButtons />
  ) : (
    <PDPButtons />
  );
    
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
  reverseContainer: {
    flexDirection: 'row-reverse',
  },
  containerStyle: {
    padding: 8,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderColor: '#E5E5E5',
  },
  button: {
    flex: 1,
    height: 54,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
  },
  buttonText: {
    textAlign: 'center',
    paddingVertical: 10,
  },
  leftButton: {
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
  },
  rightButton: {
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
  },
  borderRadiusDisabled: {
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
  },
  noPadding: {
    padding: 0,
    borderTopWidth: 0,
  },
});

export default ActionButtons;
