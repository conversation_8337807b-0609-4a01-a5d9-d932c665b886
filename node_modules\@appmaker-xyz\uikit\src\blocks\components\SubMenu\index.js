import { View, Text, FlatList, ScrollView } from 'react-native';
import React, { useState } from 'react';
import { useMenuItems } from './useSubMenu';
import ActionBar from './ActionBar';
import BreadCrumbs from './BreadCrumbs';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
function MenuItem({ item, setCurrentItems, onAction, resetMenu }) {
  const { attributes, innerBlocks } = item;
  const [expand, setExpand] = useState(attributes.expandable);

  function iconName() {
    if (attributes.expandable) {
      if (expand) {
        return 'chevron-up';
      }
      return 'chevron-down';
    }
    return null;
  }
  console.log(attributes);

  return (
    <View
      style={{
        borderBottomColor: '#D8D8D8',
        borderBottomWidth: 1,
        marginHorizontal: 12,
      }}>
      <ActionBar
        attributes={{
          ...attributes,
          rightIcon: iconName(),
        }}
        onPress={() => {
          if (attributes.expandable) {
            setExpand(!expand);
          } else if (innerBlocks.length > 0) {
            setCurrentItems(item, innerBlocks);
          }
          onAction(attributes.appmakerAction);
          if (
            attributes.appmakerAction &&
            (innerBlocks.length === 0 || !innerBlocks)
          ) {
            resetMenu();
          }
        }}
      />
      {expand
        ? innerBlocks.map((submenu) => (
            <View style={{ marginLeft: 12 }}>
              <ActionBar
                attributes={submenu.attributes}
                onPress={() => {
                  onAction(submenu.attributes.appmakerAction);
                }}
              />
            </View>
          ))
        : null}
    </View>
  );
}
export default function SubMenu({ innerBlocks, onAction }) {
  // console.log(onAction);
  const { items, setCurrentItems, breadCrumb, onPressBracumb, resetMenu } =
    useMenuItems({
      innerBlocks,
    });
  // console.log(breadCrumb, 'breadCrumb');
  const renderItem = ({ item }) => (
    <MenuItem
      item={item}
      resetMenu={resetMenu}
      setCurrentItems={setCurrentItems}
      onAction={onAction}
    />
  );
  // console.log(items);
  return (
    <ScrollView>
      <View
        style={{
          paddingTop: 20,
        }}>
        <BreadCrumbs
          attributes={{
            items: breadCrumb,
          }}
          onPress={(item, index) => {
            //   console.log(item, 'item', index);
            onPressBracumb(item, index);
          }}
        />
        <FlatList
          data={items}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
        />
      </View>
    </ScrollView>
  );
}
