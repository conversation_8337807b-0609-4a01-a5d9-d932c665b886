module.exports={A:{A:{"1":"B","2":"J D E F DC","164":"A"},B:{"1":"C K L G M N O P Q R S T U V W X Y Z a b c d e i j k l m n o p q r s t u v f H"},C:{"1":"vB aB wB bB cB dB eB fB gB hB iB jB kB lB h mB nB oB pB qB P Q R xB S T U V W X Y Z a b c d e i j k l m n o p q r s t u v f H yB zB","2":"EC uB I w FC GC","8":"0 1 2 3 4 5 6 7 8 9 J D E F A B C K L G M N O x g y z AB BB CB DB EB FB GB HB","328":"IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB"},D:{"1":"WB XB YB ZB vB aB wB bB cB dB eB fB gB hB iB jB kB lB h mB nB oB pB qB P Q R S T U V W X Y Z a b c d e i j k l m n o p q r s t u v f H yB zB HC","2":"I w J D E F A B C K L G M N O x g y","8":"0 1 2 3 4 5 6 7 8 9 z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB","584":"TB UB VB"},E:{"1":"K L G 2B NC OC 3B 4B 5B 6B tB 7B 8B 9B AC PC","2":"I w J IC 0B JC","8":"D E F A B C KC LC MC 1B rB","1096":"sB"},F:{"1":"JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB h mB nB oB pB qB P Q R xB S T U V W X Y Z a b c d e","2":"F B C QC RC SC TC rB BC UC sB","8":"0 1 2 3 4 5 6 7 8 9 G M N O x g y z AB BB CB DB EB FB","584":"GB HB IB"},G:{"1":"jC kC lC mC nC oC 3B 4B 5B 6B tB 7B 8B 9B AC","8":"E 0B VC CC WC XC YC ZC aC bC cC dC eC fC gC hC","6148":"iC"},H:{"2":"pC"},I:{"1":"H","8":"uB I qC rC sC tC CC uC vC"},J:{"8":"D A"},K:{"1":"h","2":"A","8":"B C rB BC sB"},L:{"1":"H"},M:{"1":"f"},N:{"1":"B","36":"A"},O:{"1":"wC"},P:{"1":"g yC zC 0C 1C 1B 2C 3C 4C 5C 6C tB 7C 8C 9C","2":"xC","8":"I"},Q:{"1":"2B"},R:{"1":"AD"},S:{"1":"CD","328":"BD"}},B:2,C:"Pointer events"};
