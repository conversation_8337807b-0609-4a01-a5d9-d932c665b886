import React from 'react';
import { usePageStateApi } from '@appmaker-xyz/core';
import {
  ShopifyFilterProvider,
  createFilterStore,
} from '../../hooks/filter/filterStore';

const setPriceRange = (filters) => {
  if (filters?.length > 0) {
    const priceFilter = filters?.find((item) => item?.type === 'PRICE_RANGE');
    return getMinMax(priceFilter);
  }
  return null;
};
function getMinMax(item) {
  let min = 0;
  let max = 10000;
  try {
    const config = JSON.parse(item?.values[0].input);
    min = config.price.min;
    max = config.price.max;
  } catch (error) {}
  return {
    min,
    max,
    availableMin: item?.values?.[0]?.availableMin,
    availableMax: item?.values?.[0]?.availableMax,
  };
}

export default function ShopifyFilterProviderWrapper(props) {
  const { attributes, blockData, children } = props;
  let availableFilters = [];
  if (blockData && blockData?.length > 0) {
    availableFilters = blockData.filter((filter) => {
      return filter?.values?.length > 0;
    });
  }
  const pageStateApi = usePageStateApi();
  const selectedFillters = pageStateApi?.getState()?.filter || {};
  if (availableFilters?.length <= 0) {
    return null;
  }
  const priceRange = setPriceRange(availableFilters);
  return (
    <ShopifyFilterProvider
      createStore={createFilterStore({
        selectedFillters,
        availableFilters,
        priceRange,
        context: {
          type: attributes?.isSearch === 'true' ? 'search' : 'default',
        },
      })}>
      {children}
    </ShopifyFilterProvider>
  );
}
