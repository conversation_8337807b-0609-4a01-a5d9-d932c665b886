import React from 'react';
import { useWindowDimensions } from 'react-native';
import SwiperFlatList from 'react-native-swiper-flatlist';
import { emitEvent, analytics } from '@appmaker-xyz/core';
import { AppImage, AppTouchable } from '@appmaker-xyz/uikit';
import { StyleSheet } from 'react-native';

const SliderItem = ({
  item,
  ratio,
  onPress,
  onAction,
  clientId,
  attributes,
  parentId,
  parentName,
}) => {
  const { width } = useWindowDimensions();
  const uri =
    typeof item === 'string' ? item?.attributes : item?.attributes?.image?.url;
  const _clientId = item?.clientId || 'image-slider-no-client-id';
  const appmakerAction =
    typeof item === 'object' ? item.attributes.appmakerAction : {};
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      analytics.track(
        'appmaker_block_click',
        {
          blockType: 'appmaker_slider',
          blockId: _clientId,
          blockLabel: item?.attributes?.__appmaker_analytics?.blockLabel,
          ...(item.id && {
            resourceId: item?.attributes?.__appmaker_analytics?.resourceId,
          }),
          ...(parentId && {
            parentId: parentId,
          }),
          ...(parentName && {
            parentName: parentName,
          }),
          resourceUrl: uri,
          resourceName: item?.attributes?.__appmaker_analytics?.resourceName,
        },
        {
          blockAttributes: attributes,
          blockClientId: clientId,
          blockAction: appmakerAction,
          parentId,
          parentName,
          resourceName: attributes?.__appmaker_analytics?.resourceName,
        },
      );
      emitEvent('bannerClick', {
        action: appmakerAction,
        bannerName: appmakerAction?.title,
      });
      onAction(appmakerAction);
    };
  }
  return (
    <AppTouchable
      onPress={onPressHandle}
      clientId={`appmaker_slider-item-${_clientId}`}>
      <AppImage
        uri={uri}
        resizeMode="cover"
        fastImage={true}
        style={{
          width,
          height: width / ratio,
        }}
      />
    </AppTouchable>
  );
};

const SwiperFlatlist = ({
  clientId,
  attributes,
  onPress,
  onAction,
  innerBlocks,
}) => {
  const ratio =
    innerBlocks[0]?.attributes.imageContainerStyle.width /
    innerBlocks[0]?.attributes.imageContainerStyle.height;
  const {
    autoplay,
    hidePagination = false,
    activeDotColor,
    centerPagination,
    auto_swipe_delay = 3,
    __appmaker_analytics_parent_id,
    __appmaker_analytics_parent_name,
  } = attributes;
  return (
    <SwiperFlatList
      index={0}
      autoplay={autoplay}
      autoplayDelay={auto_swipe_delay}
      autoplayLoop={true}
      autoplayLoopKeepAnimation={true}
      showPagination={!hidePagination}
      paginationStyle={centerPagination ? null : styles.paginationStyle}
      paginationDefaultColor={'#D8D8D8'}
      paginationActiveColor={activeDotColor}
      paginationStyleItem={styles.paginationStyleItem}
      paginationStyleItemActive={styles.paginationStyleItemActive}
      data={innerBlocks}
      renderItem={({ item, index }) => (
        <SliderItem
          key={index}
          item={item}
          ratio={ratio}
          onPress={onPress}
          onAction={onAction}
          attributes={attributes}
          clientId={clientId}
          parentId={__appmaker_analytics_parent_id}
          parentName={__appmaker_analytics_parent_name}
        />
      )}
    />
  );
};

export default SwiperFlatlist;

const styles = StyleSheet.create({
  paginationStyleItem: {
    width: 6,
    height: 6,
    borderRadius: 5,
    marginHorizontal: 3,
  },
  paginationStyleItemActive: {
    width: 12,
    height: 6,
    borderRadius: 5,
    marginHorizontal: 3,
  },
  paginationStyle: {
    alignSelf: 'flex-start',
    position: 'absolute',
    bottom: 0,
    left: 14,
  },
});
