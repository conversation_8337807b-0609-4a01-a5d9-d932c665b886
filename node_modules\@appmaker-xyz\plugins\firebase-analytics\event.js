import { onEvent } from '@appmaker-xyz/core';

import {
  logCheckPIN,
  logReturnExchange,
  logCancelOrder,
  logPurchase,
  logScreenName,
} from './analytics/index';

export function activateEvents() {
  // onEvent('purchase.complete', (params) => {
  //   logPurchase(params);
  // });

  onEvent('pincodeCheck', (params) => {
    logCheckPIN(params);
  });

  onEvent('returnExchangeOrder', (params) => {
    logReturnExchange(params);
  });

  onEvent('cancelOrder', (params) => {
    logCancelOrder(params);
  });

  onEvent('pageId', (params) => {
    const { pageId } = params;
    logScreenName(pageId);
  });
}
