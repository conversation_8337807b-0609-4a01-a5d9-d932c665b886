import { Dimensions } from 'react-native';
import { appSettings } from '@appmaker-xyz/core';

const dimensions = {
  fullHeight: Dimensions.get('window').height,
  fullWidth: Dimensions.get('window').width,
};

let deviceRatio =
  Dimensions.get('window').height / Dimensions.get('window').width;
const isTab = deviceRatio < 1.6;

const getFontSize = (size) => {
  if (isTab) {
    size = size * 1.4;
  }
  return size;
};

const color = {
  primary: '#3380FF',
  dark: '#1B1B1B',
  demiDark: '#4F4F4F',
  grey: '#A9AEB7',
  light: '#E9EDF1',
  white: '#FFFFFF',
  danger: '#CB4141',
  warning: '#FFC829',
  success: '#2FA478',
};

const spacing = {
  nano: 4,
  mini: 6,
  small: 8,
  base: 12,
  md: 16,
  lg: 24,
  xl: 32,
  xxxl: 72,
};

const font = {
  family: {
    regular: appSettings.getExtensionOption(
      'custom-font',
      'ui_kit_custom_fonts_regular_name',
      'NunitoSans-Regular',
    ),
    semiBold: appSettings.getExtensionOption(
      'custom-font',
      'ui_kit_custom_fonts_medium_name',
      'NunitoSans-SemiBold',
    ),
    medium: appSettings.getExtensionOption(
      'custom-font',
      'ui_kit_custom_fonts_medium_name',
      'NunitoSans-SemiBold',
    ),
    bold: appSettings.getExtensionOption(
      'custom-font',
      'ui_kit_custom_fonts_bold_name',
      'NunitoSans-Bold',
    ),
  },
  size: {
    nano: getFontSize(11),
    mini: getFontSize(12),
    small: getFontSize(13),
    semi: getFontSize(15),
    md: getFontSize(16),
    lg: getFontSize(18),
    xl: getFontSize(24),
    xxl: getFontSize(27),
  },
};

const fonts = {
  largeHeading: {
    fontSize: font.size.xxl,
    fontFamily: font.family.bold,
  },
  pageHeading: {
    fontSize: font.size.xl,
    fontFamily: font.family.bold,
  },
  pageSubHeading: {
    fontSize: font.size.md,
    fontFamily: font.family.regular,
  },
  h1Heading: {
    fontSize: font.size.lg,
    fontFamily: font.family.bold,
  },
  h1SubHeading: {
    fontSize: font.size.mini,
    fontFamily: font.family.semiBold,
  },
  actionTitle: {
    fontSize: font.size.lg,
    fontFamily: font.family.semiBold,
  },
  bodyParagraphBold: {
    fontSize: font.size.semi,
    fontFamily: font.family.bold,
  },
  bodyParagraph: {
    fontSize: font.size.semi,
    fontFamily: font.family.semiBold,
  },
  bodyParagraphRegular: {
    fontSize: font.size.semi,
    fontFamily: font.family.regular,
  },
  bodySubText: {
    fontSize: font.size.small,
    fontFamily: font.family.regular,
  },
  highlighter1: {
    fontSize: font.size.mini,
    fontFamily: font.family.regular,
  },
  highlighter2: {
    fontSize: font.size.nano,
    fontFamily: font.family.regular,
  },
  buttonText: {
    fontSize: font.size.lg,
    fontFamily: font.family.semiBold,
  },
  smallButtonText: {
    fontSize: font.size.small,
    fontFamily: font.family.bold,
  },
};

export { dimensions, color, spacing, fonts, font };
