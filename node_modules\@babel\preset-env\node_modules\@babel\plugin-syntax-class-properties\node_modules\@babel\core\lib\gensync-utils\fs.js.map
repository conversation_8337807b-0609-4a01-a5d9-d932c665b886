{"version": 3, "names": ["_fs", "data", "require", "_gensync", "readFile", "gens<PERSON>", "sync", "fs", "readFileSync", "errback", "exports", "stat", "statSync"], "sources": ["../../src/gensync-utils/fs.ts"], "sourcesContent": ["import fs from \"fs\";\nimport gensync from \"gensync\";\n\nexport const readFile = gensync<[filepath: string, encoding: \"utf8\"], string>({\n  sync: fs.readFileSync,\n  errback: fs.readFile,\n});\n\nexport const stat = gensync({\n  sync: fs.statSync,\n  errback: fs.stat,\n});\n"], "mappings": ";;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,SAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,QAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAMG,QAAQ,GAAGC,UAAO,CAA+C;EAC5EC,IAAI,EAAEC,KAAE,CAACC,YAAY;EACrBC,OAAO,EAAEF,KAAE,CAACH;AACd,CAAC,CAAC;AAACM,OAAA,CAAAN,QAAA,GAAAA,QAAA;AAEI,MAAMO,IAAI,GAAGN,UAAO,CAAC;EAC1BC,IAAI,EAAEC,KAAE,CAACK,QAAQ;EACjBH,OAAO,EAAEF,KAAE,CAACI;AACd,CAAC,CAAC;AAACD,OAAA,CAAAC,IAAA,GAAAA,IAAA;AAAA"}