{"version": 3, "names": ["_generated", "require", "_cleanJSXElementLiteralChild", "buildChildren", "node", "elements", "i", "children", "length", "child", "isJSXText", "cleanJSXElementLiteralChild", "isJSXExpressionContainer", "expression", "isJSXEmptyExpression", "push"], "sources": ["../../../src/builders/react/buildChildren.ts"], "sourcesContent": ["import {\n  isJSXText,\n  isJSXExpressionContainer,\n  isJSXEmptyExpression,\n} from \"../../validators/generated\";\nimport cleanJSXElementLiteralChild from \"../../utils/react/cleanJSXElementLiteralChild\";\nimport type * as t from \"../..\";\n\ntype ReturnedChild =\n  | t.JSXSpreadChild\n  | t.JSXElement\n  | t.JSXFragment\n  | t.Expression;\n\nexport default function buildChildren(\n  node: t.JSXElement | t.JSXFragment,\n): ReturnedChild[] {\n  const elements = [];\n\n  for (let i = 0; i < node.children.length; i++) {\n    let child: any = node.children[i];\n\n    if (isJSXText(child)) {\n      cleanJSXElementLiteralChild(child, elements);\n      continue;\n    }\n\n    if (isJSXExpressionContainer(child)) child = child.expression;\n    if (isJSXEmptyExpression(child)) continue;\n\n    elements.push(child);\n  }\n\n  return elements;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAKA,IAAAC,4BAAA,GAAAD,OAAA;AASe,SAASE,aAAaA,CACnCC,IAAkC,EACjB;EACjB,MAAMC,QAAQ,GAAG,EAAE;EAEnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,QAAQ,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAC7C,IAAIG,KAAU,GAAGL,IAAI,CAACG,QAAQ,CAACD,CAAC,CAAC;IAEjC,IAAI,IAAAI,oBAAS,EAACD,KAAK,CAAC,EAAE;MACpB,IAAAE,oCAA2B,EAACF,KAAK,EAAEJ,QAAQ,CAAC;MAC5C;IACF;IAEA,IAAI,IAAAO,mCAAwB,EAACH,KAAK,CAAC,EAAEA,KAAK,GAAGA,KAAK,CAACI,UAAU;IAC7D,IAAI,IAAAC,+BAAoB,EAACL,KAAK,CAAC,EAAE;IAEjCJ,QAAQ,CAACU,IAAI,CAACN,KAAK,CAAC;EACtB;EAEA,OAAOJ,QAAQ;AACjB"}