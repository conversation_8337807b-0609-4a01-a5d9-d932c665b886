import React, { useRef, useEffect, useState } from 'react';
import { StyleSheet, ActivityIndicator, View, Image } from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import {
  Layout,
  AppImage,
  AppTouchable,
  AppmakerText,
} from '../../../components';
import { Swiper } from '@appmaker-xyz/uikit/Views';
import AnySwiper from './AnySwiper';

const ImageSwiper = ({ attributes, onPress, onAction }) => {
  const {
    imageList,
    autoplay,
    resizeMode = 'contain',
    title,
    appmakerAction,
    showsButtons,
    showLoadingIndicator,
  } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  const swiperRef = useRef();
  useEffect(() => {
    // console.log(attributes.index, '/', imageList.length);
    // console.log();
    if (swiperRef.current) {
      swiperRef.current.scrollTo(attributes.index, true);
    }
  }, [attributes.index]);

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  const ImageItem = ({ item }) => {
    const uri = typeof item === 'string' ? item : item.uri;
    const [loading, setLoading] = useState(showLoadingIndicator ? true : false);

    useEffect(() => {
      let isMounted = true; // note mutable flag
      if (showLoadingIndicator) {
        Image.prefetch(uri)
          .then(() => {
            if (isMounted) {
              setLoading(false);
            } // add conditional check
          })
          .then(() => {
            if (isMounted) {
              setLoading(false);
            } // add conditional check
          });
      }
      return () => {
        isMounted = false;
      }; // use cleanup to toggle value, if unmounted
    }, []);
    return (
      <AppTouchable style={styles.imageWrapper} onPress={onPressHandle}>
        {!loading ? (
          <AppImage
            uri={uri}
            style={styles.productImage}
            resizeMode={resizeMode}
          />
        ) : (
          <View>
            <ActivityIndicator size="small" color="#0000ff" />
          </View>
        )}
        {title && (
          <Layout style={styles.contentContainer}>
            <AppmakerText
              category="h1Heading"
              status="white"
              style={styles.title}
              numberOfLines={3}>
              {item.title}
            </AppmakerText>
            <AppmakerText status="light" category="highlighter1">
              {item.authorName} | {item.timeStamp}
            </AppmakerText>
          </Layout>
        )}
      </AppTouchable>
    );
  };

  return imageList.length == 1 ? (
    <ImageItem item={imageList[0]} />
  ) : (
    <Swiper
      ref={swiperRef}
      autoplay={autoplay}
      activeDotStyle={styles.activeDotStyle}
      dotStyle={styles.dotStyle}
      showsButtons={showsButtons}
      // index={attributes.index}
      paginationStyle={styles.dotContainer}>
      {imageList?.map &&
        imageList?.map((item, key) => {
          return <ImageItem key={key} item={item} />;
        })}
    </Swiper>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    imageWrapper: {
      width: '100%',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: color.white,
      position: 'relative',
    },
    productImage: {
      width: '100%',
      height: '100%',
    },
    activeDotStyle: {
      backgroundColor: color.primary,
      width: spacing.base,
      height: spacing.mini,
      borderRadius: spacing.nano,
      margin: spacing.nano / 2,
    },
    dotStyle: {
      backgroundColor: color.grey,
      width: spacing.mini,
      height: spacing.mini,
      borderRadius: spacing.nano,
      margin: spacing.nano / 2,
    },
    dotContainer: {
      justifyContent: 'flex-start',
      paddingLeft: spacing.base,
      bottom: spacing.md,
      position: 'absolute',
    },
    contentContainer: {
      position: 'absolute',
      backgroundColor: `${color.dark}50`,
      width: '100%',
      height: '100%',
      justifyContent: 'flex-end',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.lg * 2,
    },
    title: {
      textAlign: 'left',
      textShadowColor: `${color.demiDark}50`,
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
  });

export default ImageSwiper;
export { AnySwiper, ImageSwiper };
