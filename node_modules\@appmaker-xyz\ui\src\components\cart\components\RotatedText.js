import React from 'react';
import { Layout, ThemeText } from '../../atoms';

const RotatedText = ({
  text,
  disabled,
  textLength = 120,
  textHeight = 10,
  bgColor = '#DCFCE7',
  textColor = '#166534',
}) => {
  const TEXT_LENGTH = textLength;
  const TEXT_HEIGHT = textHeight;
  const bgStyles = {
    width: TEXT_HEIGHT,
    height: TEXT_LENGTH,
    backgroundColor: disabled ? '#C0C0C0' : bgColor,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 12,
  };
  const textStyles = {
    transform: [{ rotate: '270deg' }],
    width: TEXT_LENGTH,
    minHeight: TEXT_HEIGHT,
    textAlign: 'center',
    color: textColor,
  };
  return (
    <Layout style={bgStyles}>
      <ThemeText style={textStyles} fontFamily="bold">
        {text}
      </ThemeText>
    </Layout>
  );
};

export default RotatedText;
