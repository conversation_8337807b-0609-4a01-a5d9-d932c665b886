{"version": 3, "names": ["isDigit", "code", "forbiddenNumericSeparatorSiblings", "decBinOct", "Set", "hex", "isAllowedNumericSeparatorSibling", "bin", "ch", "oct", "dec", "readStringContents", "type", "input", "pos", "lineStart", "curLine", "errors", "initialPos", "initialLineStart", "initialCurLine", "out", "firstInvalidLoc", "chunkStart", "length", "unterminated", "slice", "charCodeAt", "isStringEnd", "res", "readEscapedChar", "str", "containsInvalid", "inTemplate", "throwOnInvalid", "readHexChar", "String", "fromCharCode", "readCodePoint", "fromCodePoint", "strictNumericEscape", "startPos", "match", "octalStr", "octal", "parseInt", "next", "len", "forceLen", "n", "readInt", "invalidEscapeSequence", "radix", "allowNumSeparator", "bailOnError", "start", "forbiddenSiblings", "isAllowedSibling", "invalid", "total", "i", "e", "Infinity", "val", "prev", "numericSeparatorInEscapeSequence", "Number", "isNaN", "has", "unexpectedNumericSeparator", "invalidDigit", "indexOf", "invalidCodePoint"], "sources": ["../src/index.ts"], "sourcesContent": ["import * as charCodes from \"charcodes\";\n\n// The following character codes are forbidden from being\n// an immediate sibling of NumericLiteralSeparator _\nconst forbiddenNumericSeparatorSiblings = {\n  decBinOct: new Set<number>([\n    charCodes.dot,\n    charCodes.uppercaseB,\n    charCodes.uppercaseE,\n    charCodes.uppercaseO,\n    charCodes.underscore, // multiple separators are not allowed\n    charCodes.lowercaseB,\n    charCodes.lowercaseE,\n    charCodes.lowercaseO,\n  ]),\n  hex: new Set<number>([\n    charCodes.dot,\n    charCodes.uppercaseX,\n    charCodes.underscore, // multiple separators are not allowed\n    charCodes.lowercaseX,\n  ]),\n};\n\nconst isAllowedNumericSeparatorSibling = {\n  // 0 - 1\n  bin: (ch: number) => ch === charCodes.digit0 || ch === charCodes.digit1,\n\n  // 0 - 7\n  oct: (ch: number) => ch >= charCodes.digit0 && ch <= charCodes.digit7,\n\n  // 0 - 9\n  dec: (ch: number) => ch >= charCodes.digit0 && ch <= charCodes.digit9,\n\n  // 0 - 9, A - F, a - f,\n  hex: (ch: number) =>\n    (ch >= charCodes.digit0 && ch <= charCodes.digit9) ||\n    (ch >= charCodes.uppercaseA && ch <= charCodes.uppercaseF) ||\n    (ch >= charCodes.lowercaseA && ch <= charCodes.lowercaseF),\n};\n\nexport type StringContentsErrorHandlers = EscapedCharErrorHandlers & {\n  unterminated(\n    initialPos: number,\n    initialLineStart: number,\n    initialCurLine: number,\n  ): void;\n};\n\nexport function readStringContents(\n  type: \"single\" | \"double\" | \"template\",\n  input: string,\n  pos: number,\n  lineStart: number,\n  curLine: number,\n  errors: StringContentsErrorHandlers,\n) {\n  const initialPos = pos;\n  const initialLineStart = lineStart;\n  const initialCurLine = curLine;\n\n  let out = \"\";\n  let firstInvalidLoc = null;\n  let chunkStart = pos;\n  const { length } = input;\n  for (;;) {\n    if (pos >= length) {\n      errors.unterminated(initialPos, initialLineStart, initialCurLine);\n      out += input.slice(chunkStart, pos);\n      break;\n    }\n    const ch = input.charCodeAt(pos);\n    if (isStringEnd(type, ch, input, pos)) {\n      out += input.slice(chunkStart, pos);\n      break;\n    }\n    if (ch === charCodes.backslash) {\n      out += input.slice(chunkStart, pos);\n      const res = readEscapedChar(\n        input,\n        pos,\n        lineStart,\n        curLine,\n        type === \"template\",\n        errors,\n      );\n      if (res.ch === null && !firstInvalidLoc) {\n        firstInvalidLoc = { pos, lineStart, curLine };\n      } else {\n        out += res.ch;\n      }\n      ({ pos, lineStart, curLine } = res);\n      chunkStart = pos;\n    } else if (\n      ch === charCodes.lineSeparator ||\n      ch === charCodes.paragraphSeparator\n    ) {\n      ++pos;\n      ++curLine;\n      lineStart = pos;\n    } else if (ch === charCodes.lineFeed || ch === charCodes.carriageReturn) {\n      if (type === \"template\") {\n        out += input.slice(chunkStart, pos) + \"\\n\";\n        ++pos;\n        if (\n          ch === charCodes.carriageReturn &&\n          input.charCodeAt(pos) === charCodes.lineFeed\n        ) {\n          ++pos;\n        }\n        ++curLine;\n        chunkStart = lineStart = pos;\n      } else {\n        errors.unterminated(initialPos, initialLineStart, initialCurLine);\n      }\n    } else {\n      ++pos;\n    }\n  }\n  return {\n    pos,\n    str: out,\n    firstInvalidLoc,\n    lineStart,\n    curLine,\n\n    // TODO(Babel 8): This is only needed for backwards compatibility,\n    // we can remove it.\n    containsInvalid: !!firstInvalidLoc,\n  };\n}\n\nfunction isStringEnd(\n  type: \"single\" | \"double\" | \"template\",\n  ch: number,\n  input: string,\n  pos: number,\n) {\n  if (type === \"template\") {\n    return (\n      ch === charCodes.graveAccent ||\n      (ch === charCodes.dollarSign &&\n        input.charCodeAt(pos + 1) === charCodes.leftCurlyBrace)\n    );\n  }\n  return (\n    ch === (type === \"double\" ? charCodes.quotationMark : charCodes.apostrophe)\n  );\n}\n\ntype EscapedCharErrorHandlers = HexCharErrorHandlers &\n  CodePointErrorHandlers & {\n    strictNumericEscape(pos: number, lineStart: number, curLine: number): void;\n  };\n\nfunction readEscapedChar(\n  input: string,\n  pos: number,\n  lineStart: number,\n  curLine: number,\n  inTemplate: boolean,\n  errors: EscapedCharErrorHandlers,\n) {\n  const throwOnInvalid = !inTemplate;\n  pos++; // skip '\\'\n\n  const res = (ch: string | null) => ({ pos, ch, lineStart, curLine });\n\n  const ch = input.charCodeAt(pos++);\n  switch (ch) {\n    case charCodes.lowercaseN:\n      return res(\"\\n\");\n    case charCodes.lowercaseR:\n      return res(\"\\r\");\n    case charCodes.lowercaseX: {\n      let code;\n      ({ code, pos } = readHexChar(\n        input,\n        pos,\n        lineStart,\n        curLine,\n        2,\n        false,\n        throwOnInvalid,\n        errors,\n      ));\n      return res(code === null ? null : String.fromCharCode(code));\n    }\n    case charCodes.lowercaseU: {\n      let code;\n      ({ code, pos } = readCodePoint(\n        input,\n        pos,\n        lineStart,\n        curLine,\n        throwOnInvalid,\n        errors,\n      ));\n      return res(code === null ? null : String.fromCodePoint(code));\n    }\n    case charCodes.lowercaseT:\n      return res(\"\\t\");\n    case charCodes.lowercaseB:\n      return res(\"\\b\");\n    case charCodes.lowercaseV:\n      return res(\"\\u000b\");\n    case charCodes.lowercaseF:\n      return res(\"\\f\");\n    case charCodes.carriageReturn:\n      if (input.charCodeAt(pos) === charCodes.lineFeed) {\n        ++pos;\n      }\n    // fall through\n    case charCodes.lineFeed:\n      lineStart = pos;\n      ++curLine;\n    // fall through\n    case charCodes.lineSeparator:\n    case charCodes.paragraphSeparator:\n      return res(\"\");\n    case charCodes.digit8:\n    case charCodes.digit9:\n      if (inTemplate) {\n        return res(null);\n      } else {\n        errors.strictNumericEscape(pos - 1, lineStart, curLine);\n      }\n    // fall through\n    default:\n      if (ch >= charCodes.digit0 && ch <= charCodes.digit7) {\n        const startPos = pos - 1;\n        const match = input.slice(startPos, pos + 2).match(/^[0-7]+/)!;\n\n        let octalStr = match[0];\n\n        let octal = parseInt(octalStr, 8);\n        if (octal > 255) {\n          octalStr = octalStr.slice(0, -1);\n          octal = parseInt(octalStr, 8);\n        }\n        pos += octalStr.length - 1;\n        const next = input.charCodeAt(pos);\n        if (\n          octalStr !== \"0\" ||\n          next === charCodes.digit8 ||\n          next === charCodes.digit9\n        ) {\n          if (inTemplate) {\n            return res(null);\n          } else {\n            errors.strictNumericEscape(startPos, lineStart, curLine);\n          }\n        }\n\n        return res(String.fromCharCode(octal));\n      }\n\n      return res(String.fromCharCode(ch));\n  }\n}\n\ntype HexCharErrorHandlers = IntErrorHandlers & {\n  invalidEscapeSequence(pos: number, lineStart: number, curLine: number): void;\n};\n\n// Used to read character escape sequences ('\\x', '\\u').\nfunction readHexChar(\n  input: string,\n  pos: number,\n  lineStart: number,\n  curLine: number,\n  len: number,\n  forceLen: boolean,\n  throwOnInvalid: boolean,\n  errors: HexCharErrorHandlers,\n) {\n  const initialPos = pos;\n  let n;\n  ({ n, pos } = readInt(\n    input,\n    pos,\n    lineStart,\n    curLine,\n    16,\n    len,\n    forceLen,\n    false,\n    errors,\n    /* bailOnError */ !throwOnInvalid,\n  ));\n  if (n === null) {\n    if (throwOnInvalid) {\n      errors.invalidEscapeSequence(initialPos, lineStart, curLine);\n    } else {\n      pos = initialPos - 1;\n    }\n  }\n  return { code: n, pos };\n}\n\nexport type IntErrorHandlers = {\n  numericSeparatorInEscapeSequence(\n    pos: number,\n    lineStart: number,\n    curLine: number,\n  ): void;\n  unexpectedNumericSeparator(\n    pos: number,\n    lineStart: number,\n    curLine: number,\n  ): void;\n  // It can return \"true\" to indicate that the error was handled\n  // and the int parsing should continue.\n  invalidDigit(\n    pos: number,\n    lineStart: number,\n    curLine: number,\n    radix: number,\n  ): boolean;\n};\n\nexport function readInt(\n  input: string,\n  pos: number,\n  lineStart: number,\n  curLine: number,\n  radix: number,\n  len: number | undefined,\n  forceLen: boolean,\n  allowNumSeparator: boolean | \"bail\",\n  errors: IntErrorHandlers,\n  bailOnError: boolean,\n) {\n  const start = pos;\n  const forbiddenSiblings =\n    radix === 16\n      ? forbiddenNumericSeparatorSiblings.hex\n      : forbiddenNumericSeparatorSiblings.decBinOct;\n  const isAllowedSibling =\n    radix === 16\n      ? isAllowedNumericSeparatorSibling.hex\n      : radix === 10\n      ? isAllowedNumericSeparatorSibling.dec\n      : radix === 8\n      ? isAllowedNumericSeparatorSibling.oct\n      : isAllowedNumericSeparatorSibling.bin;\n\n  let invalid = false;\n  let total = 0;\n\n  for (let i = 0, e = len == null ? Infinity : len; i < e; ++i) {\n    const code = input.charCodeAt(pos);\n    let val;\n\n    if (code === charCodes.underscore && allowNumSeparator !== \"bail\") {\n      const prev = input.charCodeAt(pos - 1);\n      const next = input.charCodeAt(pos + 1);\n\n      if (!allowNumSeparator) {\n        if (bailOnError) return { n: null, pos };\n        errors.numericSeparatorInEscapeSequence(pos, lineStart, curLine);\n      } else if (\n        Number.isNaN(next) ||\n        !isAllowedSibling(next) ||\n        forbiddenSiblings.has(prev) ||\n        forbiddenSiblings.has(next)\n      ) {\n        if (bailOnError) return { n: null, pos };\n        errors.unexpectedNumericSeparator(pos, lineStart, curLine);\n      }\n\n      // Ignore this _ character\n      ++pos;\n      continue;\n    }\n\n    if (code >= charCodes.lowercaseA) {\n      val = code - charCodes.lowercaseA + charCodes.lineFeed;\n    } else if (code >= charCodes.uppercaseA) {\n      val = code - charCodes.uppercaseA + charCodes.lineFeed;\n    } else if (charCodes.isDigit(code)) {\n      val = code - charCodes.digit0; // 0-9\n    } else {\n      val = Infinity;\n    }\n    if (val >= radix) {\n      // If we found a digit which is too big, errors.invalidDigit can return true to avoid\n      // breaking the loop (this is used for error recovery).\n      if (val <= 9 && bailOnError) {\n        return { n: null, pos };\n      } else if (\n        val <= 9 &&\n        errors.invalidDigit(pos, lineStart, curLine, radix)\n      ) {\n        val = 0;\n      } else if (forceLen) {\n        val = 0;\n        invalid = true;\n      } else {\n        break;\n      }\n    }\n    ++pos;\n    total = total * radix + val;\n  }\n  if (pos === start || (len != null && pos - start !== len) || invalid) {\n    return { n: null, pos };\n  }\n\n  return { n: total, pos };\n}\n\nexport type CodePointErrorHandlers = HexCharErrorHandlers & {\n  invalidCodePoint(pos: number, lineStart: number, curLine: number): void;\n};\n\nexport function readCodePoint(\n  input: string,\n  pos: number,\n  lineStart: number,\n  curLine: number,\n  throwOnInvalid: boolean,\n  errors: CodePointErrorHandlers,\n) {\n  const ch = input.charCodeAt(pos);\n  let code;\n\n  if (ch === charCodes.leftCurlyBrace) {\n    ++pos;\n    ({ code, pos } = readHexChar(\n      input,\n      pos,\n      lineStart,\n      curLine,\n      input.indexOf(\"}\", pos) - pos,\n      true,\n      throwOnInvalid,\n      errors,\n    ));\n    ++pos;\n    if (code !== null && code > 0x10ffff) {\n      if (throwOnInvalid) {\n        errors.invalidCodePoint(pos, lineStart, curLine);\n      } else {\n        return { code: null, pos };\n      }\n    }\n  } else {\n    ({ code, pos } = readHexChar(\n      input,\n      pos,\n      lineStart,\n      curLine,\n      4,\n      false,\n      throwOnInvalid,\n      errors,\n    ));\n  }\n  return { code, pos };\n}\n"], "mappings": ";;;;;;;;;eAAA,SAASA,OAAT,CAAiBC,IAAjB,EAAuB;EACrB,OAAOA,IAAI,MAAJ,IAAkBA,IAAI,MAA7B;AACD,C;;AAED,MAAMC,iCAAiC,GAAG;EACxCC,SAAS,EAAE,IAAIC,GAAJ,CAAgB,kCAAhB,CAD6B;EAWxCC,GAAG,EAAE,IAAID,GAAJ,CAAgB,iBAAhB;AAXmC,CAA1C;AAmBA,MAAME,gCAAgC,GAAG;EAEvCC,GAAG,EAAGC,EAAD,IAAgBA,EAAE,OAAF,IAA2BA,EAAE,OAFX;EAKvCC,GAAG,EAAGD,EAAD,IAAgBA,EAAE,MAAF,IAA0BA,EAAE,MALV;EAQvCE,GAAG,EAAGF,EAAD,IAAgBA,EAAE,MAAF,IAA0BA,EAAE,MARV;EAWvCH,GAAG,EAAGG,EAAD,IACFA,EAAE,MAAF,IAA0BA,EAAE,MAA7B,IACCA,EAAE,MAAF,IAA8BA,EAAE,MADjC,IAECA,EAAE,MAAF,IAA8BA,EAAE;AAdI,CAAzC;;AAyBO,SAASG,kBAAT,CACLC,IADK,EAELC,KAFK,EAGLC,GAHK,EAILC,SAJK,EAKLC,OALK,EAMLC,MANK,EAOL;EACA,MAAMC,UAAU,GAAGJ,GAAnB;EACA,MAAMK,gBAAgB,GAAGJ,SAAzB;EACA,MAAMK,cAAc,GAAGJ,OAAvB;EAEA,IAAIK,GAAG,GAAG,EAAV;EACA,IAAIC,eAAe,GAAG,IAAtB;EACA,IAAIC,UAAU,GAAGT,GAAjB;EACA,MAAM;IAAEU;EAAF,IAAaX,KAAnB;;EACA,SAAS;IACP,IAAIC,GAAG,IAAIU,MAAX,EAAmB;MACjBP,MAAM,CAACQ,YAAP,CAAoBP,UAApB,EAAgCC,gBAAhC,EAAkDC,cAAlD;MACAC,GAAG,IAAIR,KAAK,CAACa,KAAN,CAAYH,UAAZ,EAAwBT,GAAxB,CAAP;MACA;IACD;;IACD,MAAMN,EAAE,GAAGK,KAAK,CAACc,UAAN,CAAiBb,GAAjB,CAAX;;IACA,IAAIc,WAAW,CAAChB,IAAD,EAAOJ,EAAP,EAAWK,KAAX,EAAkBC,GAAlB,CAAf,EAAuC;MACrCO,GAAG,IAAIR,KAAK,CAACa,KAAN,CAAYH,UAAZ,EAAwBT,GAAxB,CAAP;MACA;IACD;;IACD,IAAIN,EAAE,OAAN,EAAgC;MAC9Ba,GAAG,IAAIR,KAAK,CAACa,KAAN,CAAYH,UAAZ,EAAwBT,GAAxB,CAAP;MACA,MAAMe,GAAG,GAAGC,eAAe,CACzBjB,KADyB,EAEzBC,GAFyB,EAGzBC,SAHyB,EAIzBC,OAJyB,EAKzBJ,IAAI,KAAK,UALgB,EAMzBK,MANyB,CAA3B;;MAQA,IAAIY,GAAG,CAACrB,EAAJ,KAAW,IAAX,IAAmB,CAACc,eAAxB,EAAyC;QACvCA,eAAe,GAAG;UAAER,GAAF;UAAOC,SAAP;UAAkBC;QAAlB,CAAlB;MACD,CAFD,MAEO;QACLK,GAAG,IAAIQ,GAAG,CAACrB,EAAX;MACD;;MACD,CAAC;QAAEM,GAAF;QAAOC,SAAP;QAAkBC;MAAlB,IAA8Ba,GAA/B;MACAN,UAAU,GAAGT,GAAb;IACD,CAjBD,MAiBO,IACLN,EAAE,SAAF,IACAA,EAAE,SAFG,EAGL;MACA,EAAEM,GAAF;MACA,EAAEE,OAAF;MACAD,SAAS,GAAGD,GAAZ;IACD,CAPM,MAOA,IAAIN,EAAE,OAAF,IAA6BA,EAAE,OAAnC,EAAkE;MACvE,IAAII,IAAI,KAAK,UAAb,EAAyB;QACvBS,GAAG,IAAIR,KAAK,CAACa,KAAN,CAAYH,UAAZ,EAAwBT,GAAxB,IAA+B,IAAtC;QACA,EAAEA,GAAF;;QACA,IACEN,EAAE,OAAF,IACAK,KAAK,CAACc,UAAN,CAAiBb,GAAjB,QAFF,EAGE;UACA,EAAEA,GAAF;QACD;;QACD,EAAEE,OAAF;QACAO,UAAU,GAAGR,SAAS,GAAGD,GAAzB;MACD,CAXD,MAWO;QACLG,MAAM,CAACQ,YAAP,CAAoBP,UAApB,EAAgCC,gBAAhC,EAAkDC,cAAlD;MACD;IACF,CAfM,MAeA;MACL,EAAEN,GAAF;IACD;EACF;;EACD,OAAO;IACLA,GADK;IAELiB,GAAG,EAAEV,GAFA;IAGLC,eAHK;IAILP,SAJK;IAKLC,OALK;IASLgB,eAAe,EAAE,CAAC,CAACV;EATd,CAAP;AAWD;;AAED,SAASM,WAAT,CACEhB,IADF,EAEEJ,EAFF,EAGEK,KAHF,EAIEC,GAJF,EAKE;EACA,IAAIF,IAAI,KAAK,UAAb,EAAyB;IACvB,OACEJ,EAAE,OAAF,IACCA,EAAE,OAAF,IACCK,KAAK,CAACc,UAAN,CAAiBb,GAAG,GAAG,CAAvB,SAHJ;EAKD;;EACD,OACEN,EAAE,MAAMI,IAAI,KAAK,QAAT,UAAN,CADJ;AAGD;;AAOD,SAASkB,eAAT,CACEjB,KADF,EAEEC,GAFF,EAGEC,SAHF,EAIEC,OAJF,EAKEiB,UALF,EAMEhB,MANF,EAOE;EACA,MAAMiB,cAAc,GAAG,CAACD,UAAxB;EACAnB,GAAG;;EAEH,MAAMe,GAAG,GAAIrB,EAAD,KAAwB;IAAEM,GAAF;IAAON,EAAP;IAAWO,SAAX;IAAsBC;EAAtB,CAAxB,CAAZ;;EAEA,MAAMR,EAAE,GAAGK,KAAK,CAACc,UAAN,CAAiBb,GAAG,EAApB,CAAX;;EACA,QAAQN,EAAR;IACE;MACE,OAAOqB,GAAG,CAAC,IAAD,CAAV;;IACF;MACE,OAAOA,GAAG,CAAC,IAAD,CAAV;;IACF;MAA2B;QACzB,IAAI5B,IAAJ;QACA,CAAC;UAAEA,IAAF;UAAQa;QAAR,IAAgBqB,WAAW,CAC1BtB,KAD0B,EAE1BC,GAF0B,EAG1BC,SAH0B,EAI1BC,OAJ0B,EAK1B,CAL0B,EAM1B,KAN0B,EAO1BkB,cAP0B,EAQ1BjB,MAR0B,CAA5B;QAUA,OAAOY,GAAG,CAAC5B,IAAI,KAAK,IAAT,GAAgB,IAAhB,GAAuBmC,MAAM,CAACC,YAAP,CAAoBpC,IAApB,CAAxB,CAAV;MACD;;IACD;MAA2B;QACzB,IAAIA,IAAJ;QACA,CAAC;UAAEA,IAAF;UAAQa;QAAR,IAAgBwB,aAAa,CAC5BzB,KAD4B,EAE5BC,GAF4B,EAG5BC,SAH4B,EAI5BC,OAJ4B,EAK5BkB,cAL4B,EAM5BjB,MAN4B,CAA9B;QAQA,OAAOY,GAAG,CAAC5B,IAAI,KAAK,IAAT,GAAgB,IAAhB,GAAuBmC,MAAM,CAACG,aAAP,CAAqBtC,IAArB,CAAxB,CAAV;MACD;;IACD;MACE,OAAO4B,GAAG,CAAC,IAAD,CAAV;;IACF;MACE,OAAOA,GAAG,CAAC,IAAD,CAAV;;IACF;MACE,OAAOA,GAAG,CAAC,QAAD,CAAV;;IACF;MACE,OAAOA,GAAG,CAAC,IAAD,CAAV;;IACF;MACE,IAAIhB,KAAK,CAACc,UAAN,CAAiBb,GAAjB,QAAJ,EAAkD;QAChD,EAAEA,GAAF;MACD;;IAEH;MACEC,SAAS,GAAGD,GAAZ;MACA,EAAEE,OAAF;;IAEF;IACA;MACE,OAAOa,GAAG,CAAC,EAAD,CAAV;;IACF;IACA;MACE,IAAII,UAAJ,EAAgB;QACd,OAAOJ,GAAG,CAAC,IAAD,CAAV;MACD,CAFD,MAEO;QACLZ,MAAM,CAACuB,mBAAP,CAA2B1B,GAAG,GAAG,CAAjC,EAAoCC,SAApC,EAA+CC,OAA/C;MACD;;IAEH;MACE,IAAIR,EAAE,MAAF,IAA0BA,EAAE,MAAhC,EAAsD;QACpD,MAAMiC,QAAQ,GAAG3B,GAAG,GAAG,CAAvB;QACA,MAAM4B,KAAK,GAAG7B,KAAK,CAACa,KAAN,CAAYe,QAAZ,EAAsB3B,GAAG,GAAG,CAA5B,EAA+B4B,KAA/B,CAAqC,SAArC,CAAd;QAEA,IAAIC,QAAQ,GAAGD,KAAK,CAAC,CAAD,CAApB;QAEA,IAAIE,KAAK,GAAGC,QAAQ,CAACF,QAAD,EAAW,CAAX,CAApB;;QACA,IAAIC,KAAK,GAAG,GAAZ,EAAiB;UACfD,QAAQ,GAAGA,QAAQ,CAACjB,KAAT,CAAe,CAAf,EAAkB,CAAC,CAAnB,CAAX;UACAkB,KAAK,GAAGC,QAAQ,CAACF,QAAD,EAAW,CAAX,CAAhB;QACD;;QACD7B,GAAG,IAAI6B,QAAQ,CAACnB,MAAT,GAAkB,CAAzB;QACA,MAAMsB,IAAI,GAAGjC,KAAK,CAACc,UAAN,CAAiBb,GAAjB,CAAb;;QACA,IACE6B,QAAQ,KAAK,GAAb,IACAG,IAAI,OADJ,IAEAA,IAAI,OAHN,EAIE;UACA,IAAIb,UAAJ,EAAgB;YACd,OAAOJ,GAAG,CAAC,IAAD,CAAV;UACD,CAFD,MAEO;YACLZ,MAAM,CAACuB,mBAAP,CAA2BC,QAA3B,EAAqC1B,SAArC,EAAgDC,OAAhD;UACD;QACF;;QAED,OAAOa,GAAG,CAACO,MAAM,CAACC,YAAP,CAAoBO,KAApB,CAAD,CAAV;MACD;;MAED,OAAOf,GAAG,CAACO,MAAM,CAACC,YAAP,CAAoB7B,EAApB,CAAD,CAAV;EAxFJ;AA0FD;;AAOD,SAAS2B,WAAT,CACEtB,KADF,EAEEC,GAFF,EAGEC,SAHF,EAIEC,OAJF,EAKE+B,GALF,EAMEC,QANF,EAOEd,cAPF,EAQEjB,MARF,EASE;EACA,MAAMC,UAAU,GAAGJ,GAAnB;EACA,IAAImC,CAAJ;EACA,CAAC;IAAEA,CAAF;IAAKnC;EAAL,IAAaoC,OAAO,CACnBrC,KADmB,EAEnBC,GAFmB,EAGnBC,SAHmB,EAInBC,OAJmB,EAKnB,EALmB,EAMnB+B,GANmB,EAOnBC,QAPmB,EAQnB,KARmB,EASnB/B,MATmB,EAUD,CAACiB,cAVA,CAArB;;EAYA,IAAIe,CAAC,KAAK,IAAV,EAAgB;IACd,IAAIf,cAAJ,EAAoB;MAClBjB,MAAM,CAACkC,qBAAP,CAA6BjC,UAA7B,EAAyCH,SAAzC,EAAoDC,OAApD;IACD,CAFD,MAEO;MACLF,GAAG,GAAGI,UAAU,GAAG,CAAnB;IACD;EACF;;EACD,OAAO;IAAEjB,IAAI,EAAEgD,CAAR;IAAWnC;EAAX,CAAP;AACD;;AAuBM,SAASoC,OAAT,CACLrC,KADK,EAELC,GAFK,EAGLC,SAHK,EAILC,OAJK,EAKLoC,KALK,EAMLL,GANK,EAOLC,QAPK,EAQLK,iBARK,EASLpC,MATK,EAULqC,WAVK,EAWL;EACA,MAAMC,KAAK,GAAGzC,GAAd;EACA,MAAM0C,iBAAiB,GACrBJ,KAAK,KAAK,EAAV,GACIlD,iCAAiC,CAACG,GADtC,GAEIH,iCAAiC,CAACC,SAHxC;EAIA,MAAMsD,gBAAgB,GACpBL,KAAK,KAAK,EAAV,GACI9C,gCAAgC,CAACD,GADrC,GAEI+C,KAAK,KAAK,EAAV,GACA9C,gCAAgC,CAACI,GADjC,GAEA0C,KAAK,KAAK,CAAV,GACA9C,gCAAgC,CAACG,GADjC,GAEAH,gCAAgC,CAACC,GAPvC;EASA,IAAImD,OAAO,GAAG,KAAd;EACA,IAAIC,KAAK,GAAG,CAAZ;;EAEA,KAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,CAAC,GAAGd,GAAG,IAAI,IAAP,GAAce,QAAd,GAAyBf,GAA7C,EAAkDa,CAAC,GAAGC,CAAtD,EAAyD,EAAED,CAA3D,EAA8D;IAC5D,MAAM3D,IAAI,GAAGY,KAAK,CAACc,UAAN,CAAiBb,GAAjB,CAAb;IACA,IAAIiD,GAAJ;;IAEA,IAAI9D,IAAI,OAAJ,IAAiCoD,iBAAiB,KAAK,MAA3D,EAAmE;MACjE,MAAMW,IAAI,GAAGnD,KAAK,CAACc,UAAN,CAAiBb,GAAG,GAAG,CAAvB,CAAb;MACA,MAAMgC,IAAI,GAAGjC,KAAK,CAACc,UAAN,CAAiBb,GAAG,GAAG,CAAvB,CAAb;;MAEA,IAAI,CAACuC,iBAAL,EAAwB;QACtB,IAAIC,WAAJ,EAAiB,OAAO;UAAEL,CAAC,EAAE,IAAL;UAAWnC;QAAX,CAAP;QACjBG,MAAM,CAACgD,gCAAP,CAAwCnD,GAAxC,EAA6CC,SAA7C,EAAwDC,OAAxD;MACD,CAHD,MAGO,IACLkD,MAAM,CAACC,KAAP,CAAarB,IAAb,KACA,CAACW,gBAAgB,CAACX,IAAD,CADjB,IAEAU,iBAAiB,CAACY,GAAlB,CAAsBJ,IAAtB,CAFA,IAGAR,iBAAiB,CAACY,GAAlB,CAAsBtB,IAAtB,CAJK,EAKL;QACA,IAAIQ,WAAJ,EAAiB,OAAO;UAAEL,CAAC,EAAE,IAAL;UAAWnC;QAAX,CAAP;QACjBG,MAAM,CAACoD,0BAAP,CAAkCvD,GAAlC,EAAuCC,SAAvC,EAAkDC,OAAlD;MACD;;MAGD,EAAEF,GAAF;MACA;IACD;;IAED,IAAIb,IAAI,MAAR,EAAkC;MAChC8D,GAAG,GAAG9D,IAAI,KAAJ,KAAN;IACD,CAFD,MAEO,IAAIA,IAAI,MAAR,EAAkC;MACvC8D,GAAG,GAAG9D,IAAI,KAAJ,KAAN;IACD,CAFM,MAEA,IAAI,SAAkBA,IAAlB,CAAJ,EAA6B;MAClC8D,GAAG,GAAG9D,IAAI,KAAV;IACD,CAFM,MAEA;MACL8D,GAAG,GAAGD,QAAN;IACD;;IACD,IAAIC,GAAG,IAAIX,KAAX,EAAkB;MAGhB,IAAIW,GAAG,IAAI,CAAP,IAAYT,WAAhB,EAA6B;QAC3B,OAAO;UAAEL,CAAC,EAAE,IAAL;UAAWnC;QAAX,CAAP;MACD,CAFD,MAEO,IACLiD,GAAG,IAAI,CAAP,IACA9C,MAAM,CAACqD,YAAP,CAAoBxD,GAApB,EAAyBC,SAAzB,EAAoCC,OAApC,EAA6CoC,KAA7C,CAFK,EAGL;QACAW,GAAG,GAAG,CAAN;MACD,CALM,MAKA,IAAIf,QAAJ,EAAc;QACnBe,GAAG,GAAG,CAAN;QACAL,OAAO,GAAG,IAAV;MACD,CAHM,MAGA;QACL;MACD;IACF;;IACD,EAAE5C,GAAF;IACA6C,KAAK,GAAGA,KAAK,GAAGP,KAAR,GAAgBW,GAAxB;EACD;;EACD,IAAIjD,GAAG,KAAKyC,KAAR,IAAkBR,GAAG,IAAI,IAAP,IAAejC,GAAG,GAAGyC,KAAN,KAAgBR,GAAjD,IAAyDW,OAA7D,EAAsE;IACpE,OAAO;MAAET,CAAC,EAAE,IAAL;MAAWnC;IAAX,CAAP;EACD;;EAED,OAAO;IAAEmC,CAAC,EAAEU,KAAL;IAAY7C;EAAZ,CAAP;AACD;;AAMM,SAASwB,aAAT,CACLzB,KADK,EAELC,GAFK,EAGLC,SAHK,EAILC,OAJK,EAKLkB,cALK,EAMLjB,MANK,EAOL;EACA,MAAMT,EAAE,GAAGK,KAAK,CAACc,UAAN,CAAiBb,GAAjB,CAAX;EACA,IAAIb,IAAJ;;EAEA,IAAIO,EAAE,QAAN,EAAqC;IACnC,EAAEM,GAAF;IACA,CAAC;MAAEb,IAAF;MAAQa;IAAR,IAAgBqB,WAAW,CAC1BtB,KAD0B,EAE1BC,GAF0B,EAG1BC,SAH0B,EAI1BC,OAJ0B,EAK1BH,KAAK,CAAC0D,OAAN,CAAc,GAAd,EAAmBzD,GAAnB,IAA0BA,GALA,EAM1B,IAN0B,EAO1BoB,cAP0B,EAQ1BjB,MAR0B,CAA5B;IAUA,EAAEH,GAAF;;IACA,IAAIb,IAAI,KAAK,IAAT,IAAiBA,IAAI,GAAG,QAA5B,EAAsC;MACpC,IAAIiC,cAAJ,EAAoB;QAClBjB,MAAM,CAACuD,gBAAP,CAAwB1D,GAAxB,EAA6BC,SAA7B,EAAwCC,OAAxC;MACD,CAFD,MAEO;QACL,OAAO;UAAEf,IAAI,EAAE,IAAR;UAAca;QAAd,CAAP;MACD;IACF;EACF,CApBD,MAoBO;IACL,CAAC;MAAEb,IAAF;MAAQa;IAAR,IAAgBqB,WAAW,CAC1BtB,KAD0B,EAE1BC,GAF0B,EAG1BC,SAH0B,EAI1BC,OAJ0B,EAK1B,CAL0B,EAM1B,KAN0B,EAO1BkB,cAP0B,EAQ1BjB,MAR0B,CAA5B;EAUD;;EACD,OAAO;IAAEhB,IAAF;IAAQa;EAAR,CAAP;AACD"}