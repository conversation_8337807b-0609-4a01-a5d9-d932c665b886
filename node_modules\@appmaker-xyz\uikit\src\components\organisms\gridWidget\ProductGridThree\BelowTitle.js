import React from 'react';
import { Layout } from '../../../../components';
import { appmaker } from '@appmaker-xyz/core';
import { StyleSheet } from 'react-native';
import { ProductSlotBlock } from './ProductSlotBlock';
function getBlocksBySlotId(innerBlocks, slotId) {
  const slotBlocks = innerBlocks.find((block) => {
    if (block.attributes.slotId === slotId) {
      return block.innerBlocks;
    }
  });
  if (slotBlocks) {
    return slotBlocks.innerBlocks;
  }
  return [];
}
export function BelowTitle({ innerBlocks, BlockItem, blockData, ...props }) {
  const ReviewsComp = appmaker.applyFilters(
    'appmaker-product-grid-reviews',
    () => null,
  );
  const topRightslotBlocks = getBlocksBySlotId(innerBlocks, 'top-left') || [];
  const topleftslotBlocks = getBlocksBySlotId(innerBlocks, 'top-right') || [];
  // console.log(JSON.stringify(slotBlocks, null, 2), 'slotBlocks');
  // return null;
  return (
    <Layout style={styles.belowTitle}>
      <ProductSlotBlock
        innerBlocks={innerBlocks}
        BlockItem={BlockItem}
        blockData={blockData}
        slotId="below-title"
        {...props}
      />
    </Layout>
  );
}
const styles = StyleSheet.create({
  belowTitle: {
    marginTop: 6,
    marginLeft: -2,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
});
