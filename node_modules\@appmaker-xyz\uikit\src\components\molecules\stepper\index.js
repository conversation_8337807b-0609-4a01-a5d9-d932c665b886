import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ActivityIndicator } from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import { Button, AppmakerText } from '../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { useTranslation } from 'react-i18next';
import { testProps } from '@appmaker-xyz/core';

const LoadingIndicator = () => (
  <ActivityIndicator size="small" color="#1B1B1B" />
);
const AddButton = ({ addToCart, count, title, accessoryLeft, customStyle }) => (
  <Button
    clientId={'stepper-add'}
    small
    status="white"
    onPress={() => {
      addToCart && addToCart({ appmakerAction: false });
    }}
    block
    accessoryLeft={accessoryLeft}
    wholeContainerStyle={customStyle?.container}>
    <AppmakerText
      category="smallButtonText"
      numberOfLines={1}
      style={customStyle?.text}>
      {title}
    </AppmakerText>
  </Button>
);
const Stepper = ({
  addToCart,
  updateCart,
  customizable = false,
  adding = false,
  onChange,
  step = 1,
  minValue = 1,
  maxValue = 1000,
  count,
  canDelete = true,
  buttonText = '+ ADD',
  buttonLoadingText = 'Updating',
  displayCount,
  fullWidth = false,
  themeColor,
  __appmakerCustomStyles = {},
}) => {
  const { t } = useTranslation();
  const setCount = (newCount) => {
    updateCart && updateCart({ quantity: newCount });
    onChange && onChange(newCount);
  };

  const { color, spacing } = useApThemeState();
  themeColor = __appmakerCustomStyles?.stepper_container?.borderColor
    ? __appmakerCustomStyles?.stepper_container?.borderColor
    : themeColor;
  const styles = allStyles({ color, spacing, fullWidth, themeColor });
  const theme = themeColor ? themeColor : color.success;
  const containerStyles = [styles.stepperContainer];
  const AddToCart = () => {
    let textLength = t(buttonText).length;
    if (textLength > 5) {
      containerStyles.push({ width: textLength * 12 });
    }
    if (__appmakerCustomStyles) {
      containerStyles.push(__appmakerCustomStyles?.stepper_container);
    }
    if ((count === 0 && !adding) || customizable) {
      return (
        <AddButton
          addToCart={addToCart}
          count={count}
          title={buttonText}
          customStyle={__appmakerCustomStyles?.initial_button}
        />
      );
    }
    // const testCount = 100;
    if (adding === true) {
      return (
        <AddButton
          setCount={addToCart}
          count={count}
          title={buttonLoadingText}
          accessoryLeft={LoadingIndicator}
          customStyle={__appmakerCustomStyles?.initial_button}
        />
      );
    } else {
      containerStyles.push(styles.addedStepperContainer);
      return (
        <>
          <Button
            {...testProps('quantity-decrement')}
            clientId={'stepper-minus'}
            small={fullWidth == 1 ? false : true}
            baseSize={fullWidth == 1 ? true : false}
            onPress={() => {
              count > minValue && setCount(count - parseFloat(step));
              canDelete && count === minValue && setCount(0);
            }}
            status="light"
            wholeContainerStyle={__appmakerCustomStyles?.buttons}>
            <Icon
              name="minus"
              color={
                __appmakerCustomStyles?.stepper_container
                  ? __appmakerCustomStyles?.stepper_container?.borderColor
                  : theme
              }
            />
          </Button>
          <View
            style={[
              styles.counterText,
              __appmakerCustomStyles?.counter_text_container,
            ]}>
            <AppmakerText
              category="smallButtonText"
              style={[
                styles.counterTexttext,
                __appmakerCustomStyles?.counter_text,
              ]}
              status="dark"
              testId={'quantity-count'}
              clientId="stepper-count">
              {displayCount || count}
            </AppmakerText>
          </View>
          <Button
            {...testProps('quantity-increment')}
            clientId={'stepper-plus'}
            small={fullWidth == 1 ? false : true}
            baseSize={fullWidth == 1 ? true : false}
            onPress={() =>
              count < parseFloat(maxValue) &&
              setCount(parseFloat(count) + parseFloat(step))
            }
            status="light"
            wholeContainerStyle={__appmakerCustomStyles?.buttons}>
            <Icon
              name="plus"
              color={
                __appmakerCustomStyles?.stepper_container
                  ? __appmakerCustomStyles?.stepper_container?.borderColor
                  : theme
              }
            />
          </Button>
        </>
      );
    }
  };

  return (
    <View
      style={[styles.container, __appmakerCustomStyles?.container]}
      {...testProps('stepper-btn-container')}>
      <View style={containerStyles}>
        <AddToCart />
      </View>
      {/* {customizable && (
        <AppmakerText category="highlighter2" status="grey">
          Customizable
        </AppmakerText>
      )} */}
    </View>
  );
};

const allStyles = ({ spacing, color, fullWidth, themeColor }) =>
  StyleSheet.create({
    container: {
      alignItems: 'center',
      flex: fullWidth == 1 ? 1 : 0,
    },
    stepperContainer: {
      flexDirection: 'row',
      borderWidth: 1,
      borderRadius: spacing.mini,
      borderColor: color.grey,
      width: 90,
      maxWidth: '100%',
      height: fullWidth == 1 ? 50 : 35,
      justifyContent: 'space-between',
    },
    addedStepperContainer: {
      borderColor: themeColor ? themeColor : color.success,
      width: fullWidth == 1 ? '100%' : 90,
      flex: fullWidth == 1 ? 1 : 0,
    },
    counterText: {
      flexDirection: 'column',
      justifyContent: 'center',
    },
    counterTexttext: {
      textAlign: 'center',
    },
    buttons: {
      backgroundColor: 'white',
      borderWidth: 0,
    },
  });

export default Stepper;
