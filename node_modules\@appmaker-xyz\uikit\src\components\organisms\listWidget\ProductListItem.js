import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { testProps } from '@appmaker-xyz/core';
import {
  AppmakerText,
  Layout,
  ListImage,
  Button,
  Stepper,
  AppTouchable,
} from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import Badge from '../../molecules/coreComponents/Badge';
import Icon from '@appmaker-xyz/uikit/Icons/AntDesign';
import { useTranslation } from 'react-i18next';
import { appSettings } from '@appmaker-xyz/core';

const ListItem = ({
  clientId,
  attributes,
  pageDispatch,
  onPress,
  onAction,
  data = {},
}) => {
  const { color, spacing, font } = useApThemeState();
  const styles = allStyles({ color, spacing });
  const {
    uri,
    title,
    salePrice,
    regularPrice,
    onSale,
    salePercentage,
    in_stock,
    saleBadge,
    staticTexts,
    groceryMode = appSettings.getOptionAsBoolean('grocery_mode'),
    attribute,
    savedItemIds = {},
    cartCountMaps = {},
    appmakerAction,
    short_description,
    thumbnail_meta = {},
    sendProductData = false,
    wishList,
  } = attributes;
  let saved = savedItemIds[data.id] === true;

  const { t } = useTranslation();
  const quatity = cartCountMaps[data.id] || 0;
  useEffect(() => {
    setAddwish(saved);
  }, [saved]);
  const [currentQuantity, setCurrentQuantity] = useState(quatity);
  const onAddtoCart = async ({ quantity }) => {
    if (data.type === 'variable') {
      return onAction(appmakerAction);
    }
    setCartLoading(true);
    try {
      // if(count > quantity) {}
      let params =
        currentQuantity < quantity
          ? {
              action: 'ADD_TO_CART',
              params: {
                product: data,
                quantity: 1,
                // product: data,
              },
            }
          : {
              action: 'UPDATE_CART_QUANTITY',
              params: {
                product: data,
                quantity,
                // product: data,
              },
            };
      const cartResp = await onAction(params);
      pageDispatch({
        type: 'set_value',
        name: 'cartResponse',
        value: cartResp,
      });
      setCartLoading(false);
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: t('Cart updated'),
        },
      });
      setCurrentQuantity(quantity);
    } catch (e) {
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: t('Something went wrong, please try again'),
        },
      });
    }
  };
  const onSaved = (saved) => {
    let action;
    if (saved) {
      action = 'SAVE_ITEM';
    } else {
      action = 'REMOVE_ITEM';
    }
    onAction({ action, params: { item: data } });
  };
  const onQuantityChange = async (quantity) => {
    setQuantityLoading(true);
    await onAddtoCart({ quantity });
    setQuantityLoading(false);
  };
  const [addwish, setAddwish] = useState(saved);
  const [addingTocartLoading, setCartLoading] = useState(false);
  const [quantityLoading, setQuantityLoading] = useState(false);

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  useEffect(() => {
    setAddwish(saved);
  }, [saved]);
  let attributeView = null;
  if (attribute) {
    attributeView = attribute.map((value) => {
      return (
        <View style={styles.attributesListing}>
          <Badge
            text={value.label}
            status="demiDark"
            style={styles.attributeBadgeStyle}
          />
        </View>
      );
    });
  }

  return (
    <AppTouchable onPress={onPressHandle} {...testProps(clientId)}>
      <Layout style={styles.container}>
        <View style={styles.imgContainer}>
          <ListImage uri={uri} />

          {onSale && (
            <Badge
              style={styles.saleBadgeStyle}
              status="success"
              text={salePercentage}
            />
          )}
          {!in_stock && (
            <Badge
              style={styles.stockOutBadgeStyle}
              status="danger"
              text="Out of Stock"
            />
          )}
        </View>
        <View style={styles.textContainer}>
          <View>
            <AppmakerText category="bodyParagraph" status="dark">
              {title}
            </AppmakerText>
            {/* <AppmakerText category="highlighter1" status="demiDark">
              Quantity Info
            </AppmakerText> */}
          </View>
          <View>
            <View style={styles.priceDataContainer}>
              <AppmakerText category="h1Heading">{salePrice}</AppmakerText>
              <AppmakerText
                category="h1SubHeading"
                status="success"
                hideText={!onSale}
                style={styles.offerPrice}>
                {regularPrice}
              </AppmakerText>
            </View>
            {!!short_description && (
              <AppmakerText
                numberOfLines={1}
                category="highlighter2"
                status="demiDark">
                {short_description}
              </AppmakerText>
            )}
          </View>
          {attribute && (
            <View style={styles.attributesContainer}>{attributeView}</View>
          )}
        </View>
        <View style={styles.stepperContainer}>
          {in_stock ? (
            <>
              {groceryMode && (
                <Stepper
                  count={quatity}
                  customizable={data.type === 'variable'}
                  displayCount={currentQuantity}
                  buttonText="+ ADD"
                  buttonLoadingText="Loading"
                  adding={quantityLoading}
                  onChange={onQuantityChange}
                />
              )}
            </>
          ) : (
            <Button
              small
              outline
              status={addwish ? 'light' : 'grey'}
              onPress={() => {
                setAddwish(!addwish);
                saved = !addwish;
                onSaved(!addwish);
              }}>
              {addwish ? 'Added to Wishlist' : 'Add to wishlist'}
            </Button>
          )}
          {wishList === true && groceryMode === false && (
            <Icon
              name={addwish === false ? 'hearto' : 'heart'}
              color={addwish === false ? color.grey : color.danger}
              size={font.size.lg}
              style={styles.wishListIcon}
              onPress={() => {
                setAddwish(!addwish);
                onSaved(!addwish);
              }}
            />
          )}
        </View>
      </Layout>
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      padding: spacing.base,
      backgroundColor: color.white,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    imgContainer: {
      position: 'relative',
    },
    saleBadgeStyle: {
      position: 'absolute',
      left: spacing.nano,
      top: spacing.nano,
      opacity: 0.8,
    },
    stockOutBadgeStyle: {
      position: 'absolute',
      left: spacing.nano,
      right: spacing.nano,
      bottom: spacing.nano,
      opacity: 0.8,
    },
    textContainer: {
      flex: 1,
      paddingHorizontal: spacing.base,
    },
    priceDataContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.nano,
      flexWrap: 'wrap',
    },
    stepperContainer: {
      maxWidth: 115,
    },
    attributesContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    attributesListing: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: spacing.nano,
    },
    attributeBadgeStyle: {
      marginRight: spacing.nano,
      marginBottom: spacing.nano,
    },
    offerPrice: {
      textDecorationLine: 'line-through',
      marginLeft: spacing.nano,
    },
    wishListIcon: {
      padding: spacing.nano,
      opacity: 0.8,
    },
  });

export default ListItem;
