declare module '@appmaker-xyz/shopify' {
  interface ReviewSummary {
    average_rating: string | null;
    total_reviews: string | null;
    total_questions: string | null;
    ratings: Rating[] | null;
  }

  interface Rating {
    rating: string | null;
    percentage: string | null;
    frequency: string | null;
  }
  interface UseProductReviewSummaryReturn {
    Summary: ReviewSummary | null;
    isLoading: boolean;
  }

  interface UseProductReviewSummaryProps {
    productId: string;
  }

  function useProductReviewSummary(
    props: UseProductReviewSummaryProps,
  ): UseProductReviewSummaryReturn;
}
