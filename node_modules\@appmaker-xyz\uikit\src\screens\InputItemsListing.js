import React, {useState} from 'react';
import {ScrollView, StyleSheet} from 'react-native';
import {color, spacing} from '../styles';
import {Layout, Input, Button} from '../components';

const InputItemsListing = () => {
  const [value, setValue] = useState('');
  console.log(value);

  return (
    <Layout>
      <ScrollView style={styles.container}>
        <Input leftIcon="user" label="Email or Phone" />
        <Input leftIcon="key" label="Password" type="password" />
        <Input leftIcon="phone" label="Phone number" type="number" />
        <Input leftIcon="mail" label="label" />
        <Input
          label="Label Here"
          leftIcon="sun"
          rightIcon="send"
          status="primary"
          value={value}
          onChangeText={(text) => {
            setValue(text);
          }}
        />
        <Input
          label="Label Here"
          leftIcon="sun"
          rightIcon="send"
          status="success"
        />
        <Input
          label="Label Here"
          leftIcon="sun"
          rightIcon="send"
          status="danger"
          caption="Error"
          captionIcon="alert-triangle"
        />
        <Input
          label="Label Here"
          leftIcon="sun"
          rightIcon="send"
          status="warning"
          caption="Warning"
          captionIcon="alert-triangle"
        />
        <Input
          value="Disabled Input"
          label="Disabled"
          leftIcon="sun"
          rightIcon="send"
          editable={false}
        />
        <Button status="dark">Login</Button>
      </ScrollView>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: color.white,
    padding: spacing.base,
  },
});

export default InputItemsListing;
