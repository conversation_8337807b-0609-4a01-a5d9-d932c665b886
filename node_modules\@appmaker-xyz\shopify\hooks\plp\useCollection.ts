import { usePageState } from '@appmaker-xyz/core';
interface Image {
    src: string;
    width: number;
    height: number;
  }
interface Collection {
  handle: string;
  id: string;
  title: string | null;
  description: string | null;
  image: Image | null;
}
const useCollection = (): Collection | null => {
  const collection = usePageState((state) => state?.blockData?.collection);
  return collection;
};

export { useCollection };
