// import t from '@appmaker/tcomb-form-native';

// // here we are: define your domain model
// var Person = t.struct({
//   name: t.String, // a required string
//   surname: t.maybe(t.String), // an optional string
//   age: t.Number, // a required number
//   rememberMe: t.<PERSON>, // a boolean,
//   AccountType: t.enums.of(['type 1', 'type 2', 'other'], 'AccountType'),
//   // birthDate: t.Date, // a date field
// });

// const formFields = {
//   items: {
//     'addon-255-multiple-0': {
//       type: 'select',
//       dependent: false,
//       id: 'addon-255-multiple-0',
//       label: 'Multiple',
//       description: '',
//       placeholder: '',
//       maxLength: null,
//       minLength: null,
//       required: false,
//       options: {
//         'ap1-1': 'Ap1 (£25.00) ',
//         'ap-2-2': 'Ap 2 (£30.00) ',
//         'ap-3-3': 'Ap 3 (£11.00) ',
//       },
//       validate: [],
//       default_value: '',
//       line_count: 4,
//       priority_value: 0,
//     },
//     'addon-255-sample-1[0]': {
//       type: 'checkbox',
//       dependent: false,
//       id: 'addon-255-sample-1[0]',
//       label: 'Sample (£52.00) ',
//       description: '',
//       placeholder: '',
//       maxLength: null,
//       minLength: null,
//       required: false,
//       options: [],
//       validate: [],
//       default_value: false,
//       line_count: 4,
//       value: 'sample',
//       priority_value: 1,
//     },
//     'addon-255-text-2': {
//       type: 'text',
//       dependent: false,
//       id: 'addon-255-text-2',
//       label: 'text',
//       description: '',
//       placeholder: '',
//       maxLength: null,
//       minLength: null,
//       required: false,
//       options: [],
//       validate: [],
//       default_value: '',
//       line_count: 4,
//       value: '',
//       priority_value: 2,
//     },
//     'addon-255-what-price-3': {
//       type: 'number',
//       dependent: false,
//       id: 'addon-255-what-price-3',
//       label: 'What price',
//       description: '',
//       placeholder: '',
//       maxLength: null,
//       minLength: null,
//       required: false,
//       options: [],
//       validate: [],
//       default_value: '',
//       line_count: 4,
//       value: '',
//       priority_value: 3,
//     },
//     'addon-255-hello-4': {
//       type: 'textarea',
//       dependent: false,
//       id: 'addon-255-hello-4',
//       label: 'hello',
//       description: '',
//       placeholder: '',
//       maxLength: null,
//       minLength: null,
//       required: false,
//       options: [],
//       validate: [],
//       default_value: '',
//       line_count: 4,
//       value: '',
//       priority_value: 4,
//     },
//     'addon-255-ss-6': {
//       type: 'textarea',
//       dependent: false,
//       id: 'addon-255-ss-6',
//       label: 'ss',
//       description: '',
//       placeholder: '',
//       maxLength: null,
//       minLength: null,
//       required: false,
//       options: [],
//       validate: [],
//       default_value: '',
//       line_count: 4,
//       value: '',
//       priority_value: 5,
//     },
//     'addon-255-anirt-7': {
//       type: 'textarea',
//       dependent: false,
//       id: 'addon-255-anirt-7',
//       label: 'anirt',
//       description: '',
//       placeholder: '',
//       maxLength: null,
//       minLength: null,
//       required: false,
//       options: [],
//       validate: [],
//       default_value: '',
//       line_count: 4,
//       value: '',
//       priority_value: 6,
//     },
//   },
//   order: [
//     'addon-255-multiple-0',
//     'addon-255-sample-1[0]',
//     'addon-255-text-2',
//     'addon-255-what-price-3',
//     'addon-255-hello-4',
//     'addon-255-ss-6',
//     'addon-255-anirt-7',
//   ],
//   dependencies: [],
// };
const detailPage = {
  status: 'active',
  version: 'v1.1',
  blocks: [
    {
      name: 'appmaker/layout',
      attributes: {
        style: {
          padding: 12,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/dynamic-form',
          attributes: {
            content: 'custom fields',
            // fields: formFields,
            // tcombType: Person,
            // mapValues: {
            //   fields: 'fields',
            // },
          },
        },
      ],
    },
    {
      name: 'appmaker/product-counter',
      attributes: {
        counter: true,
      },
      contextValues: true,
    },
  ],
  // fixedHeaderBlock: {
  //   name: 'appmaker/layout',
  //   attributes: {
  //     style: {
  //       flexDirection: 'row',
  //       alignItems: 'center',
  //       backgroundColor: '#E9EDF1',
  //       borderTopLeftRadius: 12,
  //       borderTopRightRadius: 12,
  //       padding: 16,
  //       flex: 0.2,
  //     },
  //   },
  //   innerBlocks: [
  //     {
  //       name: 'appmaker/text',
  //       attributes: {
  //         content: 'Product Name',
  //         category: 'h1Heading',
  //       },
  //     },
  //     {
  //       name: 'appmaker/icon',
  //       attributes: {
  //         name: 'x',
  //         size: 16,
  //         color: '#1B1B1B',
  //         style: {
  //           padding: 12,
  //         },
  //       },
  //     },
  //   ],
  // },
  fixedFooter: {
    name: 'appmaker/button',
    attributes: {
      content: 'Add to Cart',
      // buyNowText: 'Buy',
      mapValues: {
        in_stock: 'in_stock',
      },
      contextValues: {
        in_stock: 'product.in_stock',
      },
    },
  },
  type: 'normal',
  attributes: {
    style: {},
    showLoadingTillData: true,
  },
  // dataSource: {
  //   source: 'woocommerce-api',
  //   attributes: {
  //     transformKey: 'product-detail',
  //     methodName: 'getProduct',
  //     params: '255',
  //     mapValues: { params: 'id' },
  //   },
  // },
  title: '',
  language: 'default',
  parentID: 'home',
  id: 'home',
};
export default detailPage;
