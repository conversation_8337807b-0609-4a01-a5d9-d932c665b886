import React from 'react';
import { View, StyleSheet } from 'react-native';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { useApThemeState } from '../../../theme/ThemeContext';
import { AppmakerText } from '../../index';

//CUSTOM STYLES FORMAT
// const customStyles = {
//   textStyle: {
//     color: 'white',
//   },
//   containerStyle: {
//     backgroundColor: '#008080',
//   },
// };

const Badge = ({
  status,
  iconName,
  style,
  text,
  full = false,
  customStyles,
}) => {
  const { color, spacing, fonts, font } = useApThemeState();
  const styles = allStyles({ color, spacing, fonts, font, full });
  const containerStyles = [styles.container];
  if (status) {
    containerStyles.push({ backgroundColor: color[status] });
  }
  if (style) {
    containerStyles.push(style);
  }
  if (!text) {
    return null;
  }
  if (customStyles) {
    containerStyles.push(customStyles?.containerStyle);
  }
  return (
    <View style={containerStyles}>
      {iconName && (
        <Icon
          name={iconName}
          color={color.white}
          size={font.size.mini}
          style={styles.leftIcon}
        />
      )}
      <AppmakerText
        category="highlighter2"
        status="white"
        style={customStyles ? customStyles?.textStyle : styles.text}>
        {text?.toString().split('\\n')?.join('\n')}
      </AppmakerText>
    </View>
  );
};

const allStyles = ({ spacing, color, full }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.primary,
      paddingHorizontal: spacing.nano,
      borderRadius: 3,
      flexDirection: 'row',
      alignItems: 'center',
      textAlign: 'center',
      justifyContent: full ? 'center' : null,
    },
    leftIcon: {
      marginRight: spacing.nano,
    },
    text: {
      textAlign: 'center',
      width: full ? '100%' : null,
    },
  });

export default Badge;
export function BadgeBlock({ attributes, ...props }) {
  return (
    <Badge
      text={attributes.text}
      status={attributes.status}
      iconName={attributes.iconName}
    />
  );
}
