import { usePageState } from '@appmaker-xyz/core';
import produce from 'immer';

type ShopifyFilterKey = string;
type ShopifyFilterValueKey = string;
type ShopifyFilterValue = {
  count?: number;
  id: ShopifyFilterValueKey;
  input: string | Object;
  label?: string;
};

type FilterSetParam = {
  filterKey: ShopifyFilterKey;
  filterOptionKey: ShopifyFilterValueKey;
  filterValue: ShopifyFilterValue;
};

type FilterRemoveParam = {
  filterKey: ShopifyFilterKey;
  filterOptionKey: ShopifyFilterValueKey;
};
const useCollectionFilterActions = (props: any) => {
  const setPageState = usePageState((state) => state.setState);
  const setFilter = ({
    filterKey,
    filterOptionKey,
    filterValue,
  }: FilterSetParam) => {
    setPageState(
      produce((draft: any) => {
        draft.filter = draft.filter || {};
        draft.filter[filterKey] = draft.filter[filterKey] || {};
        draft.filter[filterKey][filterOptionKey] = filterValue;
      }),
    );
  };
  const removeFilter = ({ filterKey, filterOptionKey }: FilterRemoveParam) => {
    setPageState(
      produce((draft: any) => {
        if (draft?.filter && draft.filter[filterKey]) {
          delete draft.filter[filterKey][filterOptionKey];
          if (Object.keys(draft.filter[filterKey]).length === 0) {
            delete draft.filter[filterKey];
          }
        }
      }),
    );
  };

  const clearAllFilter = () => {
    setPageState(
      produce((draft: any) => {
        draft.filter = undefined;
      }),
    );
  };

  return {
    setFilter,
    clearAllFilter,
    removeFilter,
  };
};

export { useCollectionFilterActions };