import React from 'react';
import { BlockCard } from '@appmaker-xyz/uikit';
import { appmaker } from '@appmaker-xyz/core';
import { runDataSource } from '@appmaker-xyz/core';
import { useEffect } from 'react';
import base64 from 'react-native-base64';

export const NostoProductRecommenation = ({
  attributes,
  BlocksView,
  innerBlocks,
  ...props
}) => {
  const productId = attributes?.productId || attributes?.config?.productId;
  const section = attributes?.section || '';
  const [recommendations, setRecommendations] = React.useState([]);
  // runDataSource
  useEffect(() => {
    console.log('starting','+++++++++++');
    // const [response] = runDataSource();
    async function getData(params) {
      try {
        const [response] = await runDataSource(
          {
            dataSource: {
              source: 'nosto',
              attributes: {},
            },
          },
          {
            methodName: 'getRecommenations',
            params: {},
            // params: {
            //   type: eventName,
            //   target: eventData.id,
            // },
          },
        );
        const products = response.session.pages.forFrontPage[0].primary;
        const productIds = products.map((product) =>
          base64.encode(`gid://shopify/Product/${product.productId}`),
        );
        console.log(productIds);
        setRecommendations(productIds);
      } catch (error) {
        console.log(error, 'error');
      }
    }
    getData();
  }, []);
  const PLPdefaultAttributes = appmaker.applyFilters(
    'shopify-product-scroller-attributes',
    {
      horizontal: true,
      dataSource: {
        source: 'shopify',
        attributes: {
          mapping: {
            items: 'data.data.products.edges',
          },
          methodName: 'products',
          params: {
            ids: recommendations,
            productsLimit: 10,
          },
        },
        repeatable: 'Yes',
        repeatItem: 'DataSource',
      },
      appmakerAction: {
        pageId: 'productDetail',
        params: {
          pageData: '{{ blockItem }}',
        },
        action: 'OPEN_INAPP_PAGE',
      },
    },
  );
  const pagesData = {
    blocks: [
      {
        attributes: PLPdefaultAttributes,
        name: 'appmaker/product-grid-item',
        innerBlocks: [],
      },
    ],
  };

  // return null;
  return recommendations.length === 0 ? null : (
    <BlockCard
      attributes={{
        ...attributes,
        accessButton: attributes.showViewMoreButton ? attributes.ctaText : '',
        childContainerStyle: { paddingBottom: 12 },
      }}
      {...props}>
      <BlocksView inAppPage={pagesData} {...props} blockData={props.data} />
    </BlockCard>
  );
};
