import React from 'react';
import { I18nManager } from 'react-native';
import { Layout, ThemeText } from '../atoms';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';

const HeaderNotice = ({ attributes = {}, testId = '' }) => {
  const { styles, theme } = useStyles(stylesheet);
  const { color = theme.colors.accent, text, totalSaved } = attributes;
  const textStyle = {
    padding: 6,
    textAlign: 'center',
    backgroundColor: `${color}26`,
    borderRadius: 8,
  };

  if (I18nManager.isRTL) {
    return (
      <Layout style={[styles.container]}>
        <Layout style={[styles.rtlContainer, textStyle]}>
          <ThemeText testId={testId} color={color} fontFamily="medium">
            {'You saved'}
          </ThemeText>
          <ThemeText testId={testId} color={color} fontFamily="medium">
            {totalSaved}
          </ThemeText>
          <ThemeText testId={testId} color={color} fontFamily="medium">
            {'on this order.'}
          </ThemeText>
        </Layout>
      </Layout>
    );
  }

  return (
    <Layout style={styles.container}>
      <ThemeText
        testId={testId}
        color={color}
        style={textStyle}
        fontFamily="medium">
        {text}
      </ThemeText>
    </Layout>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.background,
    padding: 12,
  },
  rtlContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
  },
}));

export default HeaderNotice;
