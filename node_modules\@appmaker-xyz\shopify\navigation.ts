import {
  analytics,
  appmaker,
  AppmakerAction,
  appStorageApi,
  DataSourceConfig,
} from '@appmaker-xyz/core';
import { SwipeNavigatorActionParams } from '@appmaker-xyz/react-native';
import { isEmpty } from 'lodash';
import { isCartApiEnabled } from './helper/helper';
export const PageId = {
  product: 'productDetail',
  collection: 'productList',
  cart: 'cartPageCheckout', // old cart page, non cart api page
  // cartApiPage: 'cart',
  checkout: 'CheckoutPaymentWebview',
  search: 'searchPage',
  wishlist: 'WishList',
  customer: {
    account: 'MyAccount',
    loginOptions: 'LoginOptions',
    login: 'LoginPage',
    register: 'Register',
    reset_password: 'ForgetPassword',
    verify_reset_password: 'VerifyPassword',
    order: {
      detail: 'orderDetail',
      list: 'OrderList',
    },
  },
};
export const ShopifyPages = PageId;
type SingleProductQuery = {
  productHandle?: string;
  productId?: string;
};
type SingleCollectionQuery = {
  collectionHandle?: string;
  collectionId?: string;
};
type SwipeProductDirectInput = {
  productQuery: SingleProductQuery;
};
type SwipeProductFromCollectionInput = {
  collectionQuery: SingleCollectionQuery;
};
type SwipeProductFromDataSourcesInput = {
  listDataSource: DataSourceConfig<any, any>;
};
export type OpenProductInput = {
  productId?: string;
  handle?: string;
  initialState?: any;
  extraParams?: any;
};
export type OpenCollectionInput = {
  handle: string;
  collectionId?: string;
  pageTitle?: string;
};
export type OpenSearchResultInput = {
  query: string;
  pageTitle?: string;
  params?: any;
};
export const NavigationAction = {
  openSwipeProduct: (
    params: SwipeNavigatorActionParams<SingleProductQuery>,
  ) => ({
    action: 'OPEN_SWIPE_NAVIGATOR_PRODUCT',
    pageId: 'product-swipe-navigator',
    params,
  }),
  openProduct: ({
    productId,
    handle,
    extraParams,
    initialState,
  }: OpenProductInput): AppmakerAction<any> => {
    if (isEmpty(initialState)) {
      return {
        action: 'OPEN_INAPP_PAGE',
        pageId: PageId.product,
        params: {
          loadDataSource: true,
          productId: productId,
          productHandle: handle,
          showLoadingTillData: true,
          runDataSourceByDefault: true,
          ...extraParams,
        },
      };
    } else {
      return {
        params: {
          pageData: initialState,
          ...extraParams,
        },
        action: 'OPEN_PRODUCT_DETAIL',
      };
    }
  },
  resetPassword: ({ id, token }: { id: string; token: string }) => ({
    action: 'OPEN_INAPP_PAGE',
    pageId: 'ResetPassword',
    params: { id, token },
  }),
  openCollection: ({
    handle,
    collectionId,
    pageTitle,
  }: OpenCollectionInput) => {
    const collectionPageId = appmaker.applyFilters(
      'shopify-collection-page-id',
      'productList',
    );
    return {
      action: 'OPEN_INAPP_PAGE',
      pageId: collectionPageId,
      params: {
        collectionHandle: handle,
        title: pageTitle,
        collectionId: collectionId,
        collectionIdNew: collectionId,
      },
    };
  },
  openSearchResult: ({ query, pageTitle, params: inputParams }: OpenSearchResultInput) => {
    const collectionPageId = appmaker.applyFilters(
      'shopify-collection-page-id',
      'productList',
    );

    const params = {
      showSearch: inputParams?.showSearch ?? true,
      search: inputParams?.search ?? query,
      title: inputParams?.title ?? pageTitle,
      searchResult: inputParams?.searchResult ?? true,
      searchQuery: inputParams?.searchQuery ?? query,
    };

    analytics.track('product_search', { query });
    return {
      action: 'OPEN_INAPP_PAGE',
      pageId: collectionPageId,
      params: {
        title: pageTitle,
        replacePage: inputParams?.replacePage ?? true,
        isSearch: inputParams?.isSearch ?? true,
        showSearch: inputParams?.showSearch ?? true,
        params,
        pageData: params,
      },
    };
  },

  openSearch: () => {
    const isCollectionPageSearch = appmaker.applyFilters(
      'shopify-collection-page-search',
      false,
    );
    if (isCollectionPageSearch) {
      return {
        action: 'OPEN_PRODUCT_LIST',
        params: { showSearch: true, title: 'Search ', isSearch: true },
      };
    } else {
      return { action: 'OPEN_INAPP_PAGE', pageId: PageId?.search };
    }
  },
  openLoginPage: () => ({
    action: 'OPEN_INAPP_PAGE',
    pageId: PageId.customer.loginOptions,
  }),
  openLogin: () => ({
    action: 'OPEN_INAPP_PAGE',
    pageId: PageId.customer.login,
  }),
  openRegister: () => ({
    action: 'OPEN_INAPP_PAGE',
    pageId: PageId.customer.register,
  }),
  openResetPassword: () => ({
    action: 'OPEN_INAPP_PAGE',
    pageId: PageId.customer.reset_password,
  }),
  openCart: () => ({ action: 'OPEN_INAPP_PAGE', pageId: PageId.cart }),
  openCheckout: () => {
    // using appStorageState for checkout, user
  },
  openWishlist: () => ({ action: 'OPEN_INAPP_PAGE', pageId: PageId.wishlist }),
  openOrderList: () => {
    const user = appStorageApi().getState().user;
    return {
      action: 'OPEN_INAPP_PAGE',
      pageId: PageId.customer.order.list,
      params: {
        pageData: user,
      },
    };
  },
  openMyAccount: () => {
    const user = appStorageApi().getState().user;
    return {
      action: 'OPEN_INAPP_PAGE',
      pageId: PageId.customer.account,
      params: {
        pageData: user,
      },
    };
  },
  openOrderDetail: ({ orderItem }: { orderItem: any }) => {
    if (isEmpty(orderItem)) {
      return { action: 'NO_ACTION' };
    }
    return {
      pageId: PageId.customer.order.detail,
      params: {
        pageData: orderItem,
      },
      action: 'OPEN_INAPP_PAGE',
    };
  },
  openOrderDetailById: ({ orderId }: { orderId: string }) => {
    if (isEmpty(orderId)) {
      return { action: 'NO_ACTION' };
    }
    return {
      params: {
        orderId,
      },
      action: 'OPEN_ORDER_BY_ID',
    };
  },
  openThankYouPage: ({ orderId }: { orderId: string }) => {
    if (isEmpty(orderId)) {
      return { action: 'NO_ACTION' };
    }
    return {
      params: {
        orderId,
      },
      action: 'OPEN_THANK_YOU_PAGE',
    };
  },
  openWebview: ({ url }: { url: string }) => {
    if (isEmpty(url)) {
      return { action: 'NO_ACTION' };
    }
    return {
      action: 'OPEN_WEBVIEW',
      params: { url },
    };
  },
  openUrl: ({ url }: { url: string }) => {
    if (isEmpty(url)) {
      return { action: 'NO_ACTION' };
    }
    return {
      action: 'OPEN_URL',
      params: { url },
    };
  },
  logout: () => {
    return {
      action: 'LOGOUT',
    };
  },
  openInAppPage: ({ pageId }: { pageId: string }) => {
    return {
      action: 'OPEN_INAPP_PAGE',
      pageId,
    };
  },
  openAddressList: () => {
    return {
      action: 'OPEN_ADDRESS_LIST_PAGE',
    };
  },
  toggleDrawer: () => {
    return {
      action: 'TOGGLE_DRAWER',
    };
  },
  goBack: () => {
    return {
      action: 'GO_BACK',
    };
  },
  showToastMessage: ({ message }: { message: string }) => {
    return {
      action: 'SHOW_MESSAGE',
      params: {
        title: message,
      },
    };
  },
  goToHome: () => {
    return {
      action: 'GO_TO_HOME',
    };
  },
};
