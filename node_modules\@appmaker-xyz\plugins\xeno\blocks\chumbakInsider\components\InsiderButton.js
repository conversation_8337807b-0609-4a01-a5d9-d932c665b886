import React from 'react';
import { StyleSheet } from 'react-native';
import { ThemeText, AppImage, Layout } from '@appmaker-xyz/ui';
import { useAppStorage, usePluginStore } from '@appmaker-xyz/core';

const ChumbakInsiderButton = ({ attributes, onPress, onAction }) => {
  const { appmakerAction } = attributes;
  const startsWith = (arr, str) => {
    return arr.some((item) => item.startsWith(str));
  };
  const { settings } = usePluginStore((state) => state.plugins.xeno);
  const user = useAppStorage((state) => state.user);
  const insider =
    user && user?.tags?.length > 0
      ? startsWith(
          user.tags,
          settings?.userTagsForXenoStartsWith || 'Xeno Tier:Member',
        )
      : false;
  const insiderText = insider
    ? `Enjoy free shipping and extra ${settings.discountPercentageOnMembership}% off.`
    : `members get free shipping & ${settings.discountPercentageOnMembership}% off.`;
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      console.log(appmakerAction);
      onAction(appmakerAction);
    };
  }

  return (
    <>
      {insider ? (
        <Layout style={styles.membershipAddButton}>
          <AppImage
            uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
            style={styles.insiderImage}
          />
          <ThemeText size="sm">{insiderText}</ThemeText>
        </Layout>
      ) : null}
      {/* {insider == false ? (
        <AppTouchable style={styles.container} onPress={onPressHandle}>
          <Layout style={styles.membershipAddButton}>
            <AppImage
              uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
              style={styles.insiderImage}
            />
            <AppmakerText category="bodySubText">{insiderText}</AppmakerText>
          </Layout>
          <Icon name="plus" size={20} color={color.dark} style={styles.icon} />
        </AppTouchable>
      ) : (
        <Layout style={styles.membershipAddButton}>
          <AppImage
            uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
            style={styles.insiderImage}
          />
          <AppmakerText category="bodySubText">{insiderText}</AppmakerText>
        </Layout>
      )} */}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  membershipAddButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#B6DCC9',
  },
  insiderImage: {
    width: 60,
    height: 30,
    resizeMode: 'contain',
    marginRight: 6,
  },
  icon: {
    paddingHorizontal: 24,
  },
});

export default ChumbakInsiderButton;
