import React from 'react';
import { SkeletonPlaceholder } from 'react-native-skeleton-placeholder';

const blocks = [
  {
    width: 110,
    height: 110,
    margin: 6,
    borderRadius: 16,
    child: [
      {
        width: 10,
        height: 110,
        margin: 100,
        borderRadius: 16,
        child: [],
      },
    ],
  },
  {
    width: 110,
    height: 110,
    margin: 6,
    borderRadius: 16,
    child: [
      {
        width: 10,
        height: 110,
        margin: 100,
        borderRadius: 16,
        child: [],
      },
    ],
  },
];
function SkeltonItem({ block }) {
  return (
    <SkeletonPlaceholder.Item
      width={110}
      height={110}
      margin={6}
      borderRadius={16}
    />
  );
}
export default function SkeletonLoading() {
  return (
    <SkeletonPlaceholder>
      {blocks.map((block, index) => {
        return SkeltonItem({ block });
      })}
      <SkeletonPlaceholder.Item
        width={110}
        height={110}
        margin={6}
        borderRadius={16}
      />
    </SkeletonPlaceholder>
  );
}
