{"version": 3, "names": ["_gensync", "data", "require", "asyncGeneratorStep", "gen", "resolve", "reject", "_next", "_throw", "key", "arg", "info", "value", "error", "done", "Promise", "then", "_asyncToGenerator", "fn", "self", "args", "arguments", "apply", "err", "undefined", "runGenerator", "gens<PERSON>", "item", "isAsync", "sync", "errback", "cb", "exports", "maybe<PERSON><PERSON>", "message", "result", "isThenable", "Error", "async", "<PERSON><PERSON><PERSON>", "_ref", "_x", "forwardAsync", "action", "g", "kind", "adapted", "onFirstPause", "name", "arity", "firstPause", "completed", "waitFor", "x", "_ref2", "_x2", "val"], "sources": ["../../src/gensync-utils/async.ts"], "sourcesContent": ["import gensync, { type <PERSON>sync, type Handler, type Callback } from \"gensync\";\n\ntype MaybePromise<T> = T | Promise<T>;\n\nconst runGenerator: {\n  sync<Return>(gen: <PERSON><PERSON><Return>): Return;\n  async<Return>(gen: <PERSON>ler<Return>): Promise<Return>;\n  errback<Return>(gen: <PERSON><PERSON><Return>, cb: Callback<Return>): void;\n} = gensync(function* (item: Handler<any>): Handler<any> {\n  return yield* item;\n});\n\n// This Gensync returns true if the current execution context is\n// asynchronous, otherwise it returns false.\nexport const isAsync = gensync({\n  sync: () => false,\n  errback: cb => cb(null, true),\n});\n\n// This function wraps any functions (which could be either synchronous or\n// asynchronous) with a Gensync. If the wrapped function returns a promise\n// but the current execution context is synchronous, it will throw the\n// provided error.\n// This is used to handle user-provided functions which could be asynchronous.\nexport function maybeAsync<Args extends unknown[], Return>(\n  fn: (...args: Args) => Return,\n  message: string,\n): Gensync<Args, Return> {\n  return gensync({\n    sync(...args) {\n      const result = fn.apply(this, args);\n      if (isThenable(result)) throw new Error(message);\n      return result;\n    },\n    async(...args) {\n      return Promise.resolve(fn.apply(this, args));\n    },\n  });\n}\n\nconst withKind = gensync({\n  sync: cb => cb(\"sync\"),\n  async: async cb => cb(\"async\"),\n}) as <T>(cb: (kind: \"sync\" | \"async\") => MaybePromise<T>) => Handler<T>;\n\n// This function wraps a generator (or a Gensync) into another function which,\n// when called, will run the provided generator in a sync or async way, depending\n// on the execution context where this forwardAsync function is called.\n// This is useful, for example, when passing a callback to a function which isn't\n// aware of gensync, but it only knows about synchronous and asynchronous functions.\n// An example is cache.using, which being exposed to the user must be as simple as\n// possible:\n//     yield* forwardAsync(gensyncFn, wrappedFn =>\n//       cache.using(x => {\n//         // Here we don't know about gensync. wrappedFn is a\n//         // normal sync or async function\n//         return wrappedFn(x);\n//       })\n//     )\nexport function forwardAsync<Args extends unknown[], Return>(\n  action: (...args: Args) => Handler<Return>,\n  cb: (\n    adapted: (...args: Args) => MaybePromise<Return>,\n  ) => MaybePromise<Return>,\n): Handler<Return> {\n  const g = gensync(action);\n  return withKind(kind => {\n    const adapted = g[kind];\n    return cb(adapted);\n  });\n}\n\n// If the given generator is executed asynchronously, the first time that it\n// is paused (i.e. When it yields a gensync generator which can't be run\n// synchronously), call the \"firstPause\" callback.\nexport const onFirstPause = gensync<\n  [gen: Handler<unknown>, firstPause: () => void],\n  unknown\n>({\n  name: \"onFirstPause\",\n  arity: 2,\n  sync: function (item) {\n    return runGenerator.sync(item);\n  },\n  errback: function (item, firstPause, cb) {\n    let completed = false;\n\n    runGenerator.errback(item, (err, value) => {\n      completed = true;\n      cb(err, value);\n    });\n\n    if (!completed) {\n      firstPause();\n    }\n  },\n}) as <T>(gen: Handler<T>, firstPause: () => void) => Handler<T>;\n\n// Wait for the given promise to be resolved\nexport const waitFor = gensync({\n  sync: x => x,\n  async: async x => x,\n}) as <T>(p: T | Promise<T>) => Handler<T>;\n\nexport function isThenable<T = any>(val: any): val is PromiseLike<T> {\n  return (\n    !!val &&\n    (typeof val === \"object\" || typeof val === \"function\") &&\n    !!val.then &&\n    typeof val.then === \"function\"\n  );\n}\n"], "mappings": ";;;;;;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6E,SAAAE,mBAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAC,GAAA,cAAAC,IAAA,GAAAP,GAAA,CAAAK,GAAA,EAAAC,GAAA,OAAAE,KAAA,GAAAD,IAAA,CAAAC,KAAA,WAAAC,KAAA,IAAAP,MAAA,CAAAO,KAAA,iBAAAF,IAAA,CAAAG,IAAA,IAAAT,OAAA,CAAAO,KAAA,YAAAG,OAAA,CAAAV,OAAA,CAAAO,KAAA,EAAAI,IAAA,CAAAT,KAAA,EAAAC,MAAA;AAAA,SAAAS,kBAAAC,EAAA,6BAAAC,IAAA,SAAAC,IAAA,GAAAC,SAAA,aAAAN,OAAA,WAAAV,OAAA,EAAAC,MAAA,QAAAF,GAAA,GAAAc,EAAA,CAAAI,KAAA,CAAAH,IAAA,EAAAC,IAAA,YAAAb,MAAAK,KAAA,IAAAT,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,UAAAI,KAAA,cAAAJ,OAAAe,GAAA,IAAApB,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,WAAAe,GAAA,KAAAhB,KAAA,CAAAiB,SAAA;AAI7E,MAAMC,YAIL,GAAGC,UAAO,CAAC,WAAWC,IAAkB,EAAgB;EACvD,OAAO,OAAOA,IAAI;AACpB,CAAC,CAAC;AAIK,MAAMC,OAAO,GAAGF,UAAO,CAAC;EAC7BG,IAAI,EAAEA,CAAA,KAAM,KAAK;EACjBC,OAAO,EAAEC,EAAE,IAAIA,EAAE,CAAC,IAAI,EAAE,IAAI;AAC9B,CAAC,CAAC;AAACC,OAAA,CAAAJ,OAAA,GAAAA,OAAA;AAOI,SAASK,UAAUA,CACxBf,EAA6B,EAC7BgB,OAAe,EACQ;EACvB,OAAOR,UAAO,CAAC;IACbG,IAAIA,CAAC,GAAGT,IAAI,EAAE;MACZ,MAAMe,MAAM,GAAGjB,EAAE,CAACI,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;MACnC,IAAIgB,UAAU,CAACD,MAAM,CAAC,EAAE,MAAM,IAAIE,KAAK,CAACH,OAAO,CAAC;MAChD,OAAOC,MAAM;IACf,CAAC;IACDG,KAAKA,CAAC,GAAGlB,IAAI,EAAE;MACb,OAAOL,OAAO,CAACV,OAAO,CAACa,EAAE,CAACI,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC,CAAC;IAC9C;EACF,CAAC,CAAC;AACJ;AAEA,MAAMmB,QAAQ,GAAGb,UAAO,CAAC;EACvBG,IAAI,EAAEE,EAAE,IAAIA,EAAE,CAAC,MAAM,CAAC;EACtBO,KAAK;IAAA,IAAAE,IAAA,GAAAvB,iBAAA,CAAE,WAAMc,EAAE;MAAA,OAAIA,EAAE,CAAC,OAAO,CAAC;IAAA;IAAA,gBAAAO,MAAAG,EAAA;MAAA,OAAAD,IAAA,CAAAlB,KAAA,OAAAD,SAAA;IAAA;EAAA;AAChC,CAAC,CAAuE;AAgBjE,SAASqB,YAAYA,CAC1BC,MAA0C,EAC1CZ,EAEyB,EACR;EACjB,MAAMa,CAAC,GAAGlB,UAAO,CAACiB,MAAM,CAAC;EACzB,OAAOJ,QAAQ,CAACM,IAAI,IAAI;IACtB,MAAMC,OAAO,GAAGF,CAAC,CAACC,IAAI,CAAC;IACvB,OAAOd,EAAE,CAACe,OAAO,CAAC;EACpB,CAAC,CAAC;AACJ;AAKO,MAAMC,YAAY,GAAGrB,UAAO,CAGjC;EACAsB,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE,CAAC;EACRpB,IAAI,EAAE,SAAAA,CAAUF,IAAI,EAAE;IACpB,OAAOF,YAAY,CAACI,IAAI,CAACF,IAAI,CAAC;EAChC,CAAC;EACDG,OAAO,EAAE,SAAAA,CAAUH,IAAI,EAAEuB,UAAU,EAAEnB,EAAE,EAAE;IACvC,IAAIoB,SAAS,GAAG,KAAK;IAErB1B,YAAY,CAACK,OAAO,CAACH,IAAI,EAAE,CAACJ,GAAG,EAAEX,KAAK,KAAK;MACzCuC,SAAS,GAAG,IAAI;MAChBpB,EAAE,CAACR,GAAG,EAAEX,KAAK,CAAC;IAChB,CAAC,CAAC;IAEF,IAAI,CAACuC,SAAS,EAAE;MACdD,UAAU,EAAE;IACd;EACF;AACF,CAAC,CAA+D;AAAClB,OAAA,CAAAe,YAAA,GAAAA,YAAA;AAG1D,MAAMK,OAAO,GAAG1B,UAAO,CAAC;EAC7BG,IAAI,EAAEwB,CAAC,IAAIA,CAAC;EACZf,KAAK;IAAA,IAAAgB,KAAA,GAAArC,iBAAA,CAAE,WAAMoC,CAAC;MAAA,OAAIA,CAAC;IAAA;IAAA,gBAAAf,MAAAiB,GAAA;MAAA,OAAAD,KAAA,CAAAhC,KAAA,OAAAD,SAAA;IAAA;EAAA;AACrB,CAAC,CAAyC;AAACW,OAAA,CAAAoB,OAAA,GAAAA,OAAA;AAEpC,SAAShB,UAAUA,CAAUoB,GAAQ,EAAyB;EACnE,OACE,CAAC,CAACA,GAAG,KACJ,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IACtD,CAAC,CAACA,GAAG,CAACxC,IAAI,IACV,OAAOwC,GAAG,CAACxC,IAAI,KAAK,UAAU;AAElC;AAAC"}