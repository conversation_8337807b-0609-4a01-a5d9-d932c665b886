{"version": 3, "names": ["_generated", "require", "getBindingIdentifiers", "node", "duplicates", "outerOnly", "search", "concat", "ids", "Object", "create", "length", "id", "shift", "keys", "type", "isIdentifier", "_ids", "name", "push", "isExportDeclaration", "isExportAllDeclaration", "isDeclaration", "declaration", "isFunctionDeclaration", "isFunctionExpression", "i", "key", "nodes", "Array", "isArray", "DeclareClass", "DeclareFunction", "DeclareModule", "DeclareVariable", "DeclareInterface", "DeclareTypeAlias", "DeclareOpaqueType", "InterfaceDeclaration", "TypeAlias", "OpaqueType", "CatchClause", "LabeledStatement", "UnaryExpression", "AssignmentExpression", "ImportSpecifier", "ImportNamespaceSpecifier", "ImportDefaultSpecifier", "ImportDeclaration", "ExportSpecifier", "ExportNamespaceSpecifier", "ExportDefaultSpecifier", "FunctionDeclaration", "FunctionExpression", "ArrowFunctionExpression", "ObjectMethod", "ClassMethod", "ClassPrivateMethod", "ForInStatement", "ForOfStatement", "ClassDeclaration", "ClassExpression", "RestElement", "UpdateExpression", "ObjectProperty", "AssignmentPattern", "ArrayPattern", "ObjectPattern", "VariableDeclaration", "VariableDeclarator"], "sources": ["../../src/retrievers/getBindingIdentifiers.ts"], "sourcesContent": ["import {\n  isExportDeclaration,\n  isIdentifier,\n  isDeclaration,\n  isFunctionDeclaration,\n  isFunctionExpression,\n  isExportAllDeclaration,\n} from \"../validators/generated\";\nimport type * as t from \"..\";\n\nexport { getBindingIdentifiers as default };\n\nfunction getBindingIdentifiers(\n  node: t.Node,\n  duplicates: true,\n  outerOnly?: boolean,\n): Record<string, Array<t.Identifier>>;\n\nfunction getBindingIdentifiers(\n  node: t.Node,\n  duplicates?: false,\n  outerOnly?: boolean,\n): Record<string, t.Identifier>;\n\nfunction getBindingIdentifiers(\n  node: t.Node,\n  duplicates?: boolean,\n  outerOnly?: boolean,\n): Record<string, t.Identifier> | Record<string, Array<t.Identifier>>;\n\n/**\n * Return a list of binding identifiers associated with the input `node`.\n */\nfunction getBindingIdentifiers(\n  node: t.Node,\n  duplicates?: boolean,\n  outerOnly?: boolean,\n): Record<string, t.Identifier> | Record<string, Array<t.Identifier>> {\n  const search: t.Node[] = [].concat(node);\n  const ids = Object.create(null);\n\n  while (search.length) {\n    const id = search.shift();\n    if (!id) continue;\n\n    const keys =\n      // @ts-expect-error getBindingIdentifiers.keys do not cover all AST types\n      getBindingIdentifiers.keys[id.type];\n\n    if (isIdentifier(id)) {\n      if (duplicates) {\n        const _ids = (ids[id.name] = ids[id.name] || []);\n        _ids.push(id);\n      } else {\n        ids[id.name] = id;\n      }\n      continue;\n    }\n\n    if (isExportDeclaration(id) && !isExportAllDeclaration(id)) {\n      if (isDeclaration(id.declaration)) {\n        search.push(id.declaration);\n      }\n      continue;\n    }\n\n    if (outerOnly) {\n      if (isFunctionDeclaration(id)) {\n        search.push(id.id);\n        continue;\n      }\n\n      if (isFunctionExpression(id)) {\n        continue;\n      }\n    }\n\n    if (keys) {\n      for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const nodes =\n          // @ts-expect-error key must present in id\n          id[key] as t.Node[] | t.Node | undefined | null;\n        if (nodes) {\n          Array.isArray(nodes) ? search.push(...nodes) : search.push(nodes);\n        }\n      }\n    }\n  }\n\n  // $FlowIssue Object.create() seems broken\n  return ids;\n}\n\n/**\n * Mapping of types to their identifier keys.\n */\ngetBindingIdentifiers.keys = {\n  DeclareClass: [\"id\"],\n  DeclareFunction: [\"id\"],\n  DeclareModule: [\"id\"],\n  DeclareVariable: [\"id\"],\n  DeclareInterface: [\"id\"],\n  DeclareTypeAlias: [\"id\"],\n  DeclareOpaqueType: [\"id\"],\n  InterfaceDeclaration: [\"id\"],\n  TypeAlias: [\"id\"],\n  OpaqueType: [\"id\"],\n\n  CatchClause: [\"param\"],\n  LabeledStatement: [\"label\"],\n  UnaryExpression: [\"argument\"],\n  AssignmentExpression: [\"left\"],\n\n  ImportSpecifier: [\"local\"],\n  ImportNamespaceSpecifier: [\"local\"],\n  ImportDefaultSpecifier: [\"local\"],\n  ImportDeclaration: [\"specifiers\"],\n\n  ExportSpecifier: [\"exported\"],\n  ExportNamespaceSpecifier: [\"exported\"],\n  ExportDefaultSpecifier: [\"exported\"],\n\n  FunctionDeclaration: [\"id\", \"params\"],\n  FunctionExpression: [\"id\", \"params\"],\n  ArrowFunctionExpression: [\"params\"],\n  ObjectMethod: [\"params\"],\n  ClassMethod: [\"params\"],\n  ClassPrivateMethod: [\"params\"],\n\n  ForInStatement: [\"left\"],\n  ForOfStatement: [\"left\"],\n\n  ClassDeclaration: [\"id\"],\n  ClassExpression: [\"id\"],\n\n  RestElement: [\"argument\"],\n  UpdateExpression: [\"argument\"],\n\n  ObjectProperty: [\"value\"],\n\n  AssignmentPattern: [\"left\"],\n  ArrayPattern: [\"elements\"],\n  ObjectPattern: [\"properties\"],\n\n  VariableDeclaration: [\"declarations\"],\n  VariableDeclarator: [\"id\"],\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAiCA,SAASC,qBAAqBA,CAC5BC,IAAY,EACZC,UAAoB,EACpBC,SAAmB,EACiD;EACpE,MAAMC,MAAgB,GAAG,EAAE,CAACC,MAAM,CAACJ,IAAI,CAAC;EACxC,MAAMK,GAAG,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAE/B,OAAOJ,MAAM,CAACK,MAAM,EAAE;IACpB,MAAMC,EAAE,GAAGN,MAAM,CAACO,KAAK,EAAE;IACzB,IAAI,CAACD,EAAE,EAAE;IAET,MAAME,IAAI,GAERZ,qBAAqB,CAACY,IAAI,CAACF,EAAE,CAACG,IAAI,CAAC;IAErC,IAAI,IAAAC,uBAAY,EAACJ,EAAE,CAAC,EAAE;MACpB,IAAIR,UAAU,EAAE;QACd,MAAMa,IAAI,GAAIT,GAAG,CAACI,EAAE,CAACM,IAAI,CAAC,GAAGV,GAAG,CAACI,EAAE,CAACM,IAAI,CAAC,IAAI,EAAG;QAChDD,IAAI,CAACE,IAAI,CAACP,EAAE,CAAC;MACf,CAAC,MAAM;QACLJ,GAAG,CAACI,EAAE,CAACM,IAAI,CAAC,GAAGN,EAAE;MACnB;MACA;IACF;IAEA,IAAI,IAAAQ,8BAAmB,EAACR,EAAE,CAAC,IAAI,CAAC,IAAAS,iCAAsB,EAACT,EAAE,CAAC,EAAE;MAC1D,IAAI,IAAAU,wBAAa,EAACV,EAAE,CAACW,WAAW,CAAC,EAAE;QACjCjB,MAAM,CAACa,IAAI,CAACP,EAAE,CAACW,WAAW,CAAC;MAC7B;MACA;IACF;IAEA,IAAIlB,SAAS,EAAE;MACb,IAAI,IAAAmB,gCAAqB,EAACZ,EAAE,CAAC,EAAE;QAC7BN,MAAM,CAACa,IAAI,CAACP,EAAE,CAACA,EAAE,CAAC;QAClB;MACF;MAEA,IAAI,IAAAa,+BAAoB,EAACb,EAAE,CAAC,EAAE;QAC5B;MACF;IACF;IAEA,IAAIE,IAAI,EAAE;MACR,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,IAAI,CAACH,MAAM,EAAEe,CAAC,EAAE,EAAE;QACpC,MAAMC,GAAG,GAAGb,IAAI,CAACY,CAAC,CAAC;QACnB,MAAME,KAAK,GAEThB,EAAE,CAACe,GAAG,CAAyC;QACjD,IAAIC,KAAK,EAAE;UACTC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGtB,MAAM,CAACa,IAAI,CAAC,GAAGS,KAAK,CAAC,GAAGtB,MAAM,CAACa,IAAI,CAACS,KAAK,CAAC;QACnE;MACF;IACF;EACF;EAGA,OAAOpB,GAAG;AACZ;AAKAN,qBAAqB,CAACY,IAAI,GAAG;EAC3BiB,YAAY,EAAE,CAAC,IAAI,CAAC;EACpBC,eAAe,EAAE,CAAC,IAAI,CAAC;EACvBC,aAAa,EAAE,CAAC,IAAI,CAAC;EACrBC,eAAe,EAAE,CAAC,IAAI,CAAC;EACvBC,gBAAgB,EAAE,CAAC,IAAI,CAAC;EACxBC,gBAAgB,EAAE,CAAC,IAAI,CAAC;EACxBC,iBAAiB,EAAE,CAAC,IAAI,CAAC;EACzBC,oBAAoB,EAAE,CAAC,IAAI,CAAC;EAC5BC,SAAS,EAAE,CAAC,IAAI,CAAC;EACjBC,UAAU,EAAE,CAAC,IAAI,CAAC;EAElBC,WAAW,EAAE,CAAC,OAAO,CAAC;EACtBC,gBAAgB,EAAE,CAAC,OAAO,CAAC;EAC3BC,eAAe,EAAE,CAAC,UAAU,CAAC;EAC7BC,oBAAoB,EAAE,CAAC,MAAM,CAAC;EAE9BC,eAAe,EAAE,CAAC,OAAO,CAAC;EAC1BC,wBAAwB,EAAE,CAAC,OAAO,CAAC;EACnCC,sBAAsB,EAAE,CAAC,OAAO,CAAC;EACjCC,iBAAiB,EAAE,CAAC,YAAY,CAAC;EAEjCC,eAAe,EAAE,CAAC,UAAU,CAAC;EAC7BC,wBAAwB,EAAE,CAAC,UAAU,CAAC;EACtCC,sBAAsB,EAAE,CAAC,UAAU,CAAC;EAEpCC,mBAAmB,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC;EACrCC,kBAAkB,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC;EACpCC,uBAAuB,EAAE,CAAC,QAAQ,CAAC;EACnCC,YAAY,EAAE,CAAC,QAAQ,CAAC;EACxBC,WAAW,EAAE,CAAC,QAAQ,CAAC;EACvBC,kBAAkB,EAAE,CAAC,QAAQ,CAAC;EAE9BC,cAAc,EAAE,CAAC,MAAM,CAAC;EACxBC,cAAc,EAAE,CAAC,MAAM,CAAC;EAExBC,gBAAgB,EAAE,CAAC,IAAI,CAAC;EACxBC,eAAe,EAAE,CAAC,IAAI,CAAC;EAEvBC,WAAW,EAAE,CAAC,UAAU,CAAC;EACzBC,gBAAgB,EAAE,CAAC,UAAU,CAAC;EAE9BC,cAAc,EAAE,CAAC,OAAO,CAAC;EAEzBC,iBAAiB,EAAE,CAAC,MAAM,CAAC;EAC3BC,YAAY,EAAE,CAAC,UAAU,CAAC;EAC1BC,aAAa,EAAE,CAAC,YAAY,CAAC;EAE7BC,mBAAmB,EAAE,CAAC,cAAc,CAAC;EACrCC,kBAAkB,EAAE,CAAC,IAAI;AAC3B,CAAC"}