import React from 'react';
import { Button, AppmakerText } from '../../../../components';
import { StyleSheet, View, I18nManager } from 'react-native';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { useApThemeState } from '../../../../theme/ThemeContext';
import { testProps } from '@appmaker-xyz/core';

const CheckoutButtonTwo = ({
  clientId,
  show,
  itemCount,
  totalPrice,
  subtotalAmount,
  showSubTotalAmount = false,
  onPress,
  viewCartText = 'View Cart',
  __appmakerCustomStyles: customStyles = {},
}) => {
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });

  const ChevronRight = (props) => (
    <Icon name={`chevron-${I18nManager.isRTL ? 'left' : 'right'}`} {...props} />
  );
  const containerStyles = [styles.checkoutButtoncontainer];
  if (show === true) {
    containerStyles.push({ bottom: 0 });
  }
  if (customStyles.container) {
    containerStyles.push(customStyles?.container);
  }

  return (
    <View style={containerStyles} {...testProps(`${clientId}-container`)}>
      <View style={styles.contentContainer}>
        <AppmakerText
          category="highlighter1"
          status={customStyles?.itemCountText ? null : 'dark'}
          style={customStyles?.itemCountText}>
          {itemCount} Items
        </AppmakerText>
        <View style={styles.priceDetails}>
          <AppmakerText
            category="bodyParagraphBold"
            status={customStyles?.itemCountText ? null : 'dark'}
            style={customStyles?.itemCountText}>
            {totalPrice}
          </AppmakerText>
          {showSubTotalAmount == true && totalPrice != subtotalAmount ? (
            <AppmakerText
              category="bodySubText"
              status={customStyles?.itemCountText ? null : 'dark'}
              style={
                ([styles.subtotalAmountText],
                {
                  ...customStyles?.itemCountText,
                  ...styles.subtotalAmountText,
                })
              }>
              {subtotalAmount}
            </AppmakerText>
          ) : null}
        </View>
      </View>
      <Button
        status={customStyles?.button ? null : 'dark'}
        style={styles.checkoutButton}
        onPress={onPress}
        clientId={clientId}
        wholeContainerStyle={[styles.buttonContainer, customStyles?.button]}>
        {viewCartText}
      </Button>
    </View>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    checkoutButtoncontainer: {
      width: '100%',
      backgroundColor: color.white,
      //   paddingVertical: spacing.small,
      //   paddingHorizontal: spacing.base,
      borderTopWidth: 1,
      borderTopColor: color.light,
      flexDirection: 'row',
      //   flex: 1,
    },
    contentContainer: {
      flex: 1,
      justifyContent: 'center',
      paddingHorizontal: spacing.base,
    },
    checkoutButton: {
      justifyContent: 'space-between',
      width: '98%',
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: -6,
    },
    priceDetails: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    subtotalAmountText: {
      marginLeft: 10,
      textDecorationLine: 'line-through',
      textDecorationStyle: 'solid',
    },
    fontColor: {},
    buttonContainer: {
      borderRadius: 0,
      flex: 2,
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

export default CheckoutButtonTwo;
