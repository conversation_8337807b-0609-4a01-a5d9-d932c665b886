const pagesData = {
  title: 'Home page - Collections list',
  blocks: [
    {
      attributes: {
        appmakerAction: {
          pageId: 'productList',
          params: {
            pageData: '{{blockItem}}',
          },
          action: 'OPEN_INAPP_PAGE',
        },
        dataSource: {
          source: 'shopify',
          attributes: {
            mapping: {
              items: 'data.data.collections.edges',
            },
            methodName: 'collections',
          },
          repeatable: 'Yes',
          repeatItem: 'DataSource',
        },
        numColumns: '2',
        title: '{{blockItem.node.title }}',
        uri: '{{blockItem.node.image.url}} ',
      },
      name: 'appmaker/card',
      innerBlocks: [],
      clientId: '973a144a-2b29-4bb3-869f-467f16f37a1c',
      isValid: true,
    },
  ],
  _id: 'home',
};
export default pagesData;
