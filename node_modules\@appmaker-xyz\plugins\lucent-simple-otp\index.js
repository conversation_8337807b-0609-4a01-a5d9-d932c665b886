import { appmaker } from '@appmaker-xyz/core';
import { addFilter } from '@appmaker-xyz/core';
import OTPLogin from '../appmaker-ui-lib/pages/OTPLogin';
import {
  LUCENT_UPDATE_USER,
  LUCENT_DEBUG_SIGNUP,
  LUCENT_REQUEST_EMAIL_OTP,
  LUCENT_REQUEST_MOBILE_OTP,
  LUCENT_VERIFY_EMAIL_OTP,
  LUCENT_VERIFY_MOBILE_OTP,
} from './actions';
import { setShop } from './api/index';
import { addWidgetinLoginPage } from './helper';
import CustomSignUp from './pages/CustomSignUp';
import CustomSignUpDebug from './pages/Debug';
import EmailOtpRequest from './pages/EmailOtpRequest';
import EmailOtpVerify from './pages/EmailOtpVerify';
import ResendOTP from './blocks/resendOtp';
import MobileInput from './blocks/components/Login/MobileInput';
import { activateFilters } from './filters';
import {
  LoginOptions,
  AccountDetails as AccountDetailsPage,
} from './pages/index';
import AccountDetails from './blocks/components/Login/AccountDetails';
const Plugin = {
  id: 'lucent-simple-otp',
  name: 'Lucent-simple-otp',
  activate,
};

export function activate({ settings }) {
  setShop(settings.shop_name);
  appmaker.pages.registerPage('LucentCustomOTPLogin', OTPLogin);
  appmaker.pages.registerPage('LucentEmailOtpRequest', EmailOtpRequest);
  appmaker.pages.registerPage('LucentEmailOtpVerify', EmailOtpVerify);
  appmaker.pages.registerPage('LucentCustomSignUp', CustomSignUp);
  appmaker.pages.registerPage('CustomSignUpDebug', CustomSignUpDebug);
  if (settings?.use_login_ui && settings?.use_login_ui === true) {
    appmaker.pages.registerPage('LoginOptions', LoginOptions);
    appmaker.pages.registerPage('AccountDetails', AccountDetailsPage);
  }
  // addWidgetinLoginPage();
  appmaker.actions.registerAction(
    'CUSTOM_REQUEST_OTP',
    LUCENT_REQUEST_MOBILE_OTP,
  );
  appmaker.actions.registerAction(
    'CUSTOM_VERIFY_OTP',
    LUCENT_VERIFY_MOBILE_OTP,
  );
  appmaker.actions.registerAction(
    'LUCENT_REQUEST_EMAIL_OTP',
    LUCENT_REQUEST_EMAIL_OTP,
  );
  appmaker.actions.registerAction(
    'LUCENT_VERIFY_EMAIL_OTP',
    LUCENT_VERIFY_EMAIL_OTP,
  );
  appmaker.blocks.registerBlockType({
    name: 'appmaker/otp-resend-button',
    View: ResendOTP,
  });

  appmaker.blocks.registerBlockType({
    name: 'appmaker/otp-login',
    View: MobileInput,
  });
  appmaker.blocks.registerBlockType({
    name: 'appmaker/account_details',
    View: AccountDetails,
  });
  appmaker.actions.registerAction('LUCENT_DEBUG_SIGNUP', LUCENT_DEBUG_SIGNUP);
  appmaker.actions.registerAction('LUCENT_UPDATE_USER', LUCENT_UPDATE_USER);
  if (process.env.NODE_ENV === 'development') {
    // addFilter('home-page-id', 'custom-otp', () => 'EmailOtpRequest');
  }
  activateFilters();
}
export default Plugin;
