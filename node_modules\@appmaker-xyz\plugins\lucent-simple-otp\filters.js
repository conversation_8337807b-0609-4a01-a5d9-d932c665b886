import { appmaker } from '@appmaker-xyz/core';
import { requestOtp, verifyOtp, resendOtp } from './api/index';
export function activateFilters() {
  const type = 'phone';
  appmaker.addFilter(
    'appmaker-send-phone-otp',
    'lucent-otp',
    async ({ phone }) => {
      const data = await requestOtp({
        username: phone,
        type,
      });
      return {
        data: data,
      };
    },
  );
  appmaker.addFilter(
    'appmaker-re-send-phone-otp',
    'lucent-otp',
    async ({ phone }) => {
      const data = await resendOtp({
        username: phone,
        type,
      });

      return {
        data: data,
      };
    },
  );
  appmaker.addFilter(
    'appmaker-verify-phone-otp',
    'lucent-otp',
    async ({ phone, otpCode, otpResponse }) => {
      const responseData = await verifyOtp({
        username: phone,
        otp: otpCode,
        otp_id: otpResponse?.data?.otpId,
        type,
      });
      if (responseData?.status === 200) {
        const multipass_url = responseData.data.multipass_url;

        // await processLogin({});
        let multipassToken;
        if (multipass_url) {
          multipassToken = multipass_url.match(/multipass\/(.*)/)[1];
        }
        return {
          status: 'success',
          nextStep: multipassToken ? 'done' : 'custom_action',
          customAction: {
            action: 'OPEN_INAPP_PAGE',
            pageId: 'AccountDetails',
            params: {
              pageData: {
                // phone
                otpId: otpResponse?.data?.otpId,
                email: '',
                email_disabled: false,
                phone_disabled: true,
                phone,
                first_name: responseData?.data?.firstName || '',
                last_name: responseData?.data?.lastName || '',
                // type: 'create-account-with-default-email',
              },
            },
          },
          multipassToken,
          userCredentials: {},
          data: responseData,
        };
      } else {
        return {
          status: 'fail',
          error: responseData,
        };
      }
    },
  );

  appmaker.addFilter(
    'appmaker-submit-account-details',
    'lucent-account-update',
    async ({ onAction, ...attrs }) => {
      try {
        const response = await onAction?.({
          action: 'LUCENT_UPDATE_USER',
          params: {
            formData: {
              first_name: attrs?.firstName,
              last_name: attrs?.lastName,
              phone: attrs?.phone,
              email: attrs?.email,
              otp_id: attrs?.extraData?.otpId,
            },
          },
        });
        return response;
      } catch (e) {}
    },
  );
}
