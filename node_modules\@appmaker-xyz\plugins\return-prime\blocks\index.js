import { useAppStorage } from '@appmaker-xyz/core';
import { usePluginStore } from '@appmaker-xyz/core';
import { ActionBar } from '@appmaker-xyz/uikit/src/components/molecules/index';
import { emitEvent } from '@appmaker-xyz/core';
import React, { useEffect, useState } from 'react';

import { View } from 'react-native';
import { getSession, findOrder } from '../api';
function ReturnPrime({ attributes, onAction }) {
  const [buttonLoading, setButtonLoading] = useState(false);
  const [display, setDisplay] = useState(false);
  const returnPrimeStore = usePluginStore(
    (state) => state.plugins['return-prime'],
  );
  const order_number = attributes?.blockItem?.node?.name;
  const fulfillmentStatus = attributes?.blockItem?.node?.fulfillmentStatus;
  const email = useAppStorage((state) => state.user.email);

  // write a useEffect to call apis getSession and findOrder then check the status from response
  useEffect(() => {
    if (returnPrimeStore?.settings?.enableReturnableCheck == true) {
      getSession({
        order_number,
        email,
        store: returnPrimeStore?.settings?.shopifyStore,
      })
        .then((res) => {
          if (res?.session_id) {
            findOrder({ session_id: res.session_id }).then((response) => {
              response?.order[0]?.shipment_status === 'delivered' &&
                response?.order[0]?.line_items[0].returnable === true &&
                setDisplay(true);
            });
          }
        })
        .catch((err) => {
          console.log(err);
        });
    } else if (fulfillmentStatus === 'FULFILLED') {
      setDisplay(true);
    }
  }, [order_number]);

  const shopifyStore = returnPrimeStore?.settings?.shopifyStore;
  const channelID = returnPrimeStore?.settings?.channelID;
  let finalOrderNumber = order_number;
  if (
    order_number?.includes('#') &&
    returnPrimeStore?.settings?.remove_hash_from_order_number_for_webview_url
  ) {
    finalOrderNumber = order_number?.replace('#', '');
  }
  return !display ? null : (
    <View>
      <ActionBar
        attributes={{
          title: 'Return / Exchange',
          buttonLoading,
        }}
        onPress={async () => {
          emitEvent('returnExchangeOrder', { orderId: order_number });
          onAction({
            action: 'OPEN_IN_WEB_VIEW',
            params: {
              title: 'Return / Exchange',
              url: `https://admin.returnprime.com/external/fetch-order?order_number=${finalOrderNumber}&email=${email}&store=${shopifyStore}&channel_id=${channelID}`,
            },
          });
        }}
      />
    </View>
  );
  // return <Text>Hello world = {JSON.stringify(attributes, null, 2)}</Text>;
}
export { ReturnPrime };
