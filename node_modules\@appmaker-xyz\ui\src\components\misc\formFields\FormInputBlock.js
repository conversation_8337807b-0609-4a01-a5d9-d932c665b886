import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, TextInput, View, I18nManager } from 'react-native';
import { testProps, usePageState } from '@appmaker-xyz/core';
import { useTranslation } from 'react-i18next';
import { styles as themeStyles } from '../../../styles';
import { ThemeText } from '../../atoms';
import Icon from 'react-native-vector-icons/Feather';

function isEditable(disabled) {
  if (disabled === true || disabled === 'true' || disabled === 'disabled') {
    return false;
  } else {
    return true;
  }
}
const FormInputBlock = ({ attributes, coreDispatch, clientId, onAction }) => {
  const {
    name,
    type,
    editable: disabled,
    leftIcon,
    rightIcon,
    label,
    status,
    caption,
    captionIcon,
    value,
    defaultValue,
    // onChangeText,
    noOutline,
    onChange,
    autoCompleteType,
    blockItem,
    nextFieldName,
    maxLength = 1000,
    autoAction,
  } = attributes;
  let textMaxCount =
    typeof maxLength === 'string' ? parseInt(maxLength) : maxLength;
  const editable = isEditable(disabled);

  const { t } = useTranslation();
  const [focus, setFocus] = useState(false);
  const [hidePass, setHidePass] = useState(true);
  const mainContainerStyles = [styles.container];
  const containerStyles = [
    styles.inputContainer,
    {
      paddingTop: focus ? 8 : 0,
    },
  ];
  const iconStyles = [styles.icon];
  const inputTextStyle = [styles.inputArea];

  if (editable === false) {
    containerStyles.push({
      backgroundColor: '#E9EDF1',
      borderColor: '#A9AEB7',
    });
    iconStyles.push({ color: '#A9AEB7' });
    inputTextStyle.push({ color: '#A9AEB7' });
  }

  if (I18nManager.isRTL) {
    inputTextStyle.push({ textAlign: 'right' });
  }

  if (type === 'noContainer') {
    mainContainerStyles.push({
      paddingTop: 0,
      marginBottom: 0,
    });
  }
  if (noOutline === true) {
    containerStyles.push({
      borderWidth: 0,
    });
  }
  const inputRef = useRef();
  const parentName = attributes.parentName || '_formData';
  function onChangeInput(valueText) {
    valueText = valueText || '';
    coreDispatch({
      type: 'SET_VALUE',
      name,
      value: valueText.trim(),
      parent: parentName,
    });
    if (
      autoAction &&
      textMaxCount &&
      valueText &&
      valueText?.length === textMaxCount
    ) {
      coreDispatch &&
        coreDispatch({
          type: 'SET_VALUE',
          name: 'loadingButton',
          value: true,
        });
      autoAction.appmakerAction && onAction(autoAction.appmakerAction);
    }
  }
  const currentFocus = usePageState((state) => state.__currentFocus);

  const inputValue = usePageState((state) => {
    return state[parentName] && state[parentName][name]
      ? state[parentName][name]
      : '';
  });
  useEffect(() => {
    onChangeInput(defaultValue);
  }, [defaultValue]);
  useEffect(() => {
    if (currentFocus === name) {
      inputRef.current.focus();
    }
  }, [currentFocus]);

  const handleBlur = (e) => {
    if (e.target.value) {
      setFocus(true);
    } else {
      setFocus(false);
    }
  };

  return (
    <View style={mainContainerStyles}>
      <View style={containerStyles}>
        {leftIcon && <Icon name={leftIcon} style={iconStyles} />}
        {label && (focus === true || inputValue !== '') ? (
          <ThemeText size="sm" style={styles.floatingLabel}>
            {label}
          </ThemeText>
        ) : null}
        <TextInput
          {...testProps(clientId)}
          ref={inputRef}
          style={inputTextStyle}
          textContentType={type}
          autoCompleteType={autoCompleteType ? autoCompleteType : 'off'}
          keyboardType={type === 'number' ? 'phone-pad' : 'default'}
          secureTextEntry={type && type === 'password' && hidePass}
          placeholder={t(label)}
          autoFocus={false}
          autoCapitalize="none"
          editable={editable}
          maxLength={textMaxCount}
          value={inputValue}
          returnKeyType={nextFieldName ? 'next' : 'default'}
          onChangeText={onChangeInput}
          onSubmitEditing={() => {
            onChangeInput &&
              coreDispatch({
                type: 'SET_VALUE',
                name: '__currentFocus',
                value: nextFieldName,
              });
          }}
          onChange={onChange}
          onFocus={() => {
            setFocus(true);
          }}
          onBlur={handleBlur}
          blurOnSubmit
        />
        {rightIcon && <Icon name={rightIcon} style={iconStyles} />}
        {type && type === 'password' && (
          <Icon
            name={hidePass === false ? 'eye-off' : 'eye'}
            style={iconStyles}
            onPress={() => {
              setHidePass(!hidePass);
            }}
          />
        )}
      </View>
      {caption && (
        <ThemeText size="sm" style={styles.caption}>
          {caption && captionIcon && <Icon name={captionIcon} />} {caption}
        </ThemeText>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 8,
    position: 'relative',
    marginBottom: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 54,
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#4F4F4F',
    borderRadius: 4,
    paddingHorizontal: 8,
  },
  inputArea: {
    flex: 1,
    paddingHorizontal: 8,
    fontFamily: themeStyles.fontFamily.regular,
    color: '#1B1B1B',
    fontWeight: 'normal',
  },
  floatingLabel: {
    position: 'absolute',
    top: 2,
    left: 12,
    backgroundColor: 'transparent',
    paddingHorizontal: 4,
  },
  icon: {
    fontSize: 18,
    color: '#1B1B1B',
    padding: 4,
  },
  caption: {
    marginTop: 4,
  },
});

export default FormInputBlock;
