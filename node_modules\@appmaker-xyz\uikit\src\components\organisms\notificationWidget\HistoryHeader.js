import React from 'react';
import {View, StyleSheet} from 'react-native';
import {AppmakerText, Button} from '../../../components';
import {useApThemeState} from '../../../theme/ThemeContext';

const HistoryHeader = ({count, onPress}) => {
  const {color, spacing} = useApThemeState();
  const styles = allStyles({color, spacing});

  return (
    <View style={styles.container}>
      <AppmakerText category="actionTitle">
        {count} Notification(s)
      </AppmakerText>
      <Button onPress={onPress} small status="white">
        <AppmakerText category="smallButtonText" status="primary">
          Clear All
        </AppmakerText>
      </Button>
    </View>
  );
};

const allStyles = ({spacing, color}) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      padding: spacing.base,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginHorizontal: spacing.nano,
      marginTop: spacing.nano,
      borderRadius: spacing.nano,
    },
  });

export default HistoryHeader;
