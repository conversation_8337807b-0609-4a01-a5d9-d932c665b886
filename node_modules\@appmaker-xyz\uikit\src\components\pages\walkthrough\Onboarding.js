import React from 'react';
import { ImageBackground, StyleSheet } from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import {
  Layout,
  Input,
  Button,
  AppmakerText,
  AppImage,
} from '@appmaker-xyz/uikit';
import { AppmakerRemoteImage } from '@appmaker-xyz/react-native';

const Onboarding = ({ attributes, onPress, onAction }) => {
  const {
    bgImgSrc = '',
    logoSrc = '',
    text = ' ',
    skippable = true,
    buttonColor,
    roundedButton = false,
    appmakerAction,
    skipAction,
  } = attributes;
  const { color, spacing } = useApThemeState();
  const buttonBgColor = buttonColor ? buttonColor : color.primary;
  const styles = allStyles({ color, spacing, buttonBgColor, roundedButton });

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      onAction(appmakerAction);
    };
  }

  return (
    <Layout style={styles.container}>
      <AppmakerRemoteImage
        name="SPLASH_IMAGE"
        background={true}
        resizeMode="cover"
        style={styles.bgImage}>
        {skippable ? (
          <Button
            onPress={() => {
              console.log(skipAction);
              if (skipAction) {
                onAction(skipAction);
              }
            }}
            wholeContainerStyle={styles.skipButton}
            small
            fontColor="#475569">
            Skip
          </Button>
        ) : null}
        {logoSrc ? (
          <AppImage src={logoSrc} style={styles.image} resizeMode="contain" />
        ) : null}
        {text ? (
          <AppmakerText status="dark" style={styles.text}>
            {text}
          </AppmakerText>
        ) : null}
        <Button
          block
          // status="dark"
          onPress={onPressHandle}
          activeOpacity={1}
          wholeContainerStyle={styles.button}>
          Login
        </Button>
      </AppmakerRemoteImage>
    </Layout>
  );
};

const allStyles = ({ spacing, color, buttonBgColor, roundedButton }) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    bgImage: {
      flex: 1,
      justifyContent: 'flex-end',
      alignItems: 'center',
      padding: spacing.lg,
      position: 'relative',
    },
    skipButton: {
      position: 'absolute',
      right: spacing.base,
      top: spacing.base,
      paddingVertical: 3,
      backgroundColor: '#F8FAFC',
    },
    button: {
      borderRadius: roundedButton ? 40 : 0,
      marginTop: spacing.md,
      backgroundColor: buttonBgColor,
    },
    image: {
      width: 200,
      height: 60,
      marginBottom: spacing.md,
    },
    text: {
      textAlign: 'center',
    },
  });

export default Onboarding;
