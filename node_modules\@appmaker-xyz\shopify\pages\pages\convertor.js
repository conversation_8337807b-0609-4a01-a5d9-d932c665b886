const blocks = [
  {
    name: 'core/group',
    attributes: {
      renderType: 'normal',
      rootContainerStyle: {
        flex: 5,
      },
      contentContainerStyle: {
        flex: 1,
        justifyContent: 'center',
      },
    },
    innerBlocks: [
      {
        name: 'appmaker/appImage',
        attributes: {
          style: {
            width: 200,
            height: 60,
            alignSelf: 'center',
            resizeMode: 'contain',
          },
        },
      },
      {
        name: 'appmaker/input',
        clientId: 'username-email',
        attributes: {
          label: 'Username or Email',
          leftIcon: 'user',
          name: 'username',
          status: 'demiDark',
        },
      },
      {
        name: 'appmaker/input',
        clientId: 'username-password',
        attributes: {
          label: 'Password',
          leftIcon: 'key',
          type: 'password',
          name: 'password',
          status: 'demiDark',
        },
      },
      {
        name: 'core/group',
        attributes: {
          renderType: 'normal',
          contentContainerStyle: {
            alignItems: 'flex-end',
            position: 'relative',
          },
        },
        innerBlocks: [
          {
            name: 'appmaker/button',
            attributes: {
              appmakerAction: {
                action: 'OPEN_FORGET_PASSWORD',
              },
              content: 'Forgot Password?',
              status: 'white',
              small: true,
            },
          },
        ],
      },
      {
        name: 'appmaker/ActionButton',
        clientId: 'submit-login',
        attributes: {
          appmakerAction: {
            action: 'LOGIN_USER',
          },
          content: 'Sign In',
          baseSize: true,
        },
        contextValues: true,
      },
      {
        name: 'appmaker/text',
        attributes: {
          content: 'Do not have an account yet?',
          status: 'grey',
          style: {
            alignSelf: 'center',
          },
        },
      },
      {
        name: 'appmaker/button',
        attributes: {
          appmakerAction: {
            action: 'OPEN_REGISTER',
          },
          content: 'Create Account',
          outline: true,
          status: 'dark',
          baseSize: true,
        },
      },
    ],
  },
];
const validBlock = [
  {
    clientId: 'd2c318f5-344a-4d4c-b69d-f2818c7c4678',
    isValid: true,
    name: 'core/group',
    attributes: {
      tagName: 'div',
    },
    innerBlocks: [
      {
        clientId: '14b2c9a8-e81b-4387-a172-a5ba00fb9886',
        isValid: true,
        name: 'appmaker/button',
        attributes: {
          style: {
            width: 200,
            height: 60,
            alignSelf: 'center',
            resizeMode: 'contain',
          },
        },
        innerBlocks: [],
      },
    ],
  },
];
function makeid(length) {
  var result = '';
  var characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  var charactersLength = characters.length;
  for (var i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

console.log(makeid(10));
const newNames = {
  'appmaker/appImage': 'appmaker/app-image',
  'appmaker/ActionButton': 'appmaker/action-button',
};
const blockNames = {};
function modifyBlocks(block) {
  const innerBlocks = block?.innerBlocks?.map(modifyBlocks) || [];
  const newName = newNames[block.name] || block.name;
  blockNames[newName] = '123';
  return {
    ...block,
    name: newName,
    clientId: makeid(10),
    innerBlocks,
    isValid: true,
  };
}
const finalBlocks = blocks.map(modifyBlocks);
console.log(JSON.stringify(finalBlocks));
console.log(blockNames);
