import React from 'react';
import { StyleSheet, View } from 'react-native';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import { getExtensionAsBoolean } from '@appmaker-xyz/core';
import { ThemeText } from '@appmaker-xyz/ui';

import { useSelectedFilterItem } from '../../../hooks/filter/userCollectionFilter';

function getMinMax({ item, priceRange, enableInitialPriceRange = false }) {
  const availableMin = item?.values?.[0]?.availableMin;
  const availableMax = item?.values?.[0]?.availableMax;
  if (
    enableInitialPriceRange &&
    typeof priceRange?.min === 'number' &&
    priceRange?.min > -1 &&
    typeof priceRange?.max === 'number' &&
    priceRange?.max > -1
  ) {
    return {
      min: priceRange.min,
      max: priceRange.max,
      availableMin: priceRange.min,
      availableMax: priceRange.max,
    };
  }
  let min = 0;
  let max = 10000;
  try {
    const config = JSON.parse(item?.values[0].input);
    min = config.price.min;
    max = config.price.max;
  } catch (error) {}
  return {
    min,
    max,
    availableMin,
    availableMax,
  };
}

function PriceRange({
  item,
  filterKey,
  selectFilter,
  priceRange,
  extensionStyles,
}) {
  const filterValueId = 'price_range';
  const selectedItem = useSelectedFilterItem({
    filterKey,
    filterValueId: filterKey,
  });
  const disableInitialPriceRange = getExtensionAsBoolean?.(
    'shopify',
    'disable_initial_filter_price_range',
    false,
  );
  const { min, max, availableMin, availableMax } = getMinMax({
    item,
    priceRange,
    enableInitialPriceRange: !disableInitialPriceRange,
  });
  if (min === max || min > max) {
    return null;
  }
  const minRange = typeof availableMin === 'number' ? availableMin : min;
  const maxRange = typeof availableMax === 'number' ? availableMax : max;

  const color = extensionStyles?.priceRangeColor || '#1B1B1B';

  function CustomLabel(props) {
    return (
      <View style={styles.customLabelContainer}>
        <ThemeText color={color} style={styles.customLabelText}>
          {props.oneMarkerValue}
        </ThemeText>
        <ThemeText color={color} style={styles.customLabelText}>
          {props.twoMarkerValue}
        </ThemeText>
      </View>
    );
  }

  return (
    <View style={styles.sliderContainer}>
      {/* <Text>{JSON.stringify(item, null, 2)}</Text> */}
      <MultiSlider
        values={[selectedItem?.min || min, selectedItem?.max || max]}
        sliderLength={180}
        enableLabel={true}
        customLabel={CustomLabel}
        isMarkersSeparated={true}
        trackStyle={styles.trackStyle}
        selectedStyle={{
          backgroundColor: color,
        }}
        markerStyle={[styles.markerStyle, { backgroundColor: color }]}
        pressedMarkerStyle={{
          backgroundColor: `${color}E6`,
        }}
        min={minRange}
        max={maxRange}
        onValuesChangeFinish={(values) => {
          const [minValue, maxValue] = values;
          selectFilter(filterKey, filterKey, {
            type: 'PRICE_RANGE',
            min: minValue,
            max: maxValue,
          });
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  trackStyle: {
    borderRadius: 8,
    height: 2,
  },
  markerStyle: {
    height: 20,
    width: 20,
    borderRadius: 16,
    borderWidth: 0.5,
    borderColor: 'transparent',
  },
  sliderContainer: {
    alignItems: 'center',
    paddingVertical: 2,
  },
  customLabelContainer: {
    position: 'relative',
    justifyContent: 'space-between',
    flexDirection: 'row',
    width: 190,
    left: -4,
    top: 6,
  },
  customLabelText: {
    fontSize: 12,
  },
});

export default PriceRange;
