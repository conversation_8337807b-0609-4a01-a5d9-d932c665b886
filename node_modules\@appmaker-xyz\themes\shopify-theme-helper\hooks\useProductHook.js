import { useEffect } from 'react';
import { useState } from 'react';
import { emitEvent } from '@appmaker-xyz/core';

export function useProductHook({
  attributes,
  onAction,
  blockData,
  pageDispatch,
}) {
  const {
    priceSale = '0',
    uri,
    title,
    salePrice,
    regularPrice,
    wishList,
    onSale,
    salePercentage,
    in_stock,
    gridViewListing,
    numColumns,
    groceryMode,
    // saved = false,
    // onSaved,
    thumbnail_meta = {},
    savedItemIds = {},
    cartCountMaps = {},
    cartQuantity,
    attribute,
    appmakerAction,
    productType,
    id,
    average_rating,
    size = '', // sm for small
    new_product,
    show_last_few_remaining,
    last_few_remaining_text,
    bestSeller,
    brandColor,
    titleNumberOfLines,
    __appmakerCustomStyles = {},
    cartActionParams = {},
  } = attributes;
  let imageRatio =
    attributes?.thumbnail_meta?.height / attributes?.thumbnail_meta?.width || 1;
  const productListPage = attributes?.gridViewListing;
  const quatity = cartCountMaps[id] || cartQuantity || 0;
  // const { in_stock } = attributes;
  // const in
  const outOfStock =
    in_stock === 0 ||
    in_stock === '0' ||
    in_stock === 'false' ||
    in_stock === false;
  const saved = savedItemIds[id] === true;
  const [addwish, setAddwish] = useState(saved);
  const [addingTocartLoading, setCartLoading] = useState(false);
  const [quantityLoading, setQuantityLoading] = useState(false);
  const [currentQuantity, setCurrentQuantity] = useState(quatity);
  const onQuantityChange = async (quantity = 1, variant) => {
    let finalParams = { quantity };
    if (variant) {
      finalParams.variant = variant;
    }
    setQuantityLoading(true);
    await onAddtoCart(finalParams);
    setQuantityLoading(false);
  };
  useEffect(() => {
    setCurrentQuantity(quatity);
  }, [quatity]);

  const onAddtoCart = async ({ quantity, variant }) => {
    if (productType === 'variable' && !variant) {
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'Please select a product variant',
        },
      });
      return onAction(appmakerAction);
    }
    setCartLoading(true);
    try {
      let finalParams = {
        product: blockData,
        quantity: 1,
        fromList: true,
        ...cartActionParams,
        // product: data,
      };
      if (variant) {
        finalParams.variant = variant;
      }
      const cartResp = await onAction({
        action: variant ? 'ADD_TO_CART_V2' : 'ADD_TO_CART',
        params: finalParams,
      });
      if (cartResp?.status === 'fail') {
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: cartResp?.message,
            buttonTitle: 'View Cart',
            buttonAction: {
              action: 'OPEN_CART',
            },
          },
        });
        setCartLoading(false);
      } else {
        pageDispatch({
          type: 'set_value',
          name: 'cartResponse',
          value: cartResp,
        });
        setCartLoading(false);
        if (cartActionParams?.fromCart != true) {
          onAction({
            action: 'SHOW_MESSAGE',
            params: {
              title: 'Added to cart',
              buttonTitle: 'View Cart',
              buttonAction: {
                action: 'OPEN_CART',
              },
            },
          });
        }
        setCurrentQuantity(quantity);
      }
    } catch (e) {
      console.log(e);
    }
  };
  const onSaved = (saved) => {
    setAddwish(saved);
    let action;
    if (saved) {
      action = 'ADD_WISHLIST';
    } else {
      action = 'REMOVE_WISHLIST';
    }
    onAction({ action, params: { item: blockData, id } });
  };
  async function onOpenProductDetail() {
    if (attributes.appmakerAction && onAction) {
      onAction(attributes.appmakerAction);
    }
  }
  function hasTag(tagName) {
    // console.log(attributes.tags);
    return blockData?.node?.tags?.indexOf(tagName) !== -1;
  }
  let firstAvailableVariant = null;
  if (blockData?.node?.variants?.edges?.length > 0) {
    firstAvailableVariant = blockData?.node?.variants.edges.find(
      (x) => x?.node?.availableForSale === true,
    );
  }
  return {
    firstAvailableVariant,
    onQuantityChange,
    addingTocartLoading,
    onOpenProductDetail,
    outOfStock,
    imageRatio,
    productListPage,
    hasTag,
    setAddwish,
    addwish,
    onSaved,
  };
}
