import { StyleSheet, Text } from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import FilterModalContent from './FilterModalContent';
import { useFilterStore, useFilterStoreApi } from '../store';
import { converToShopifyParams } from '../helper';
import { emitEvent, analytics } from '@appmaker-xyz/core';

export default function FilterModal({
  filterVisible,
  closeFilter,
  onClear,
  onFilterSelected,
  selectedFilter,
  filter_data,
  avilableFilters,
  coreDispatch,
}) {
  const clearAppliedFilters = useFilterStore(
    (state) => state.clearAppliedFilters,
  );
  const filterStoreApi = useFilterStoreApi();
  return (
    <Modal
      testID={'modal'}
      isVisible={filterVisible}
      onSwipeComplete={closeFilter}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      onBackButtonPress={closeFilter}
      backdropTransitionOutTiming={1}
      onBackdropPress={closeFilter}
      propagateSwipe={true}
      swipeDirection="down"
      style={styles.view}>
      <FilterModalContent
        onApplyFilter={() => {
          const filterParams = converToShopifyParams(
            filterStoreApi.getState().appliedFilters,
            filterStoreApi.getState().avilableFilters,
          );
          emitEvent('filterApply', {
            action: 'ApplyFilter',
            filterItem: filterParams,
          });
          analytics.track('ApplyFilter', {
            action: 'ApplyFilter',
            filterItem: filterParams,
          });
          coreDispatch({
            type: 'SET_PAGE_VAR',
            name: 'filter',
            value: filterParams,
          });
          closeFilter();
        }}
        attributes={{
          avilableFilters,
          //   appliedFilters,
        }}
        data={filter_data}
        onClose={closeFilter}
        onClear={() => {
          clearAppliedFilters();
          coreDispatch({
            type: 'SET_PAGE_VAR',
            name: 'filter',
            value: {},
          });
          //   props.attributes.defaultFilterItem = { app_filter: {} };
          closeFilter();
        }}
        selectedParams={onFilterSelected}
        defaultFilterItem={selectedFilter}
      />
    </Modal>
  );
}

const styles = StyleSheet.create({
  view: {
    justifyContent: 'flex-end',
    marginVertical: 0,
    marginHorizontal: 1,
  },
});
