import React from 'react';
import {StyleSheet, ScrollView} from 'react-native';
import {
  Scroll<PERSON>lock,
  ListBlock,
  PostHeader,
  CarouselBlock,
} from '../components/index';
import {color} from '../styles';

const WordpressBlocks = () => {
  return (
    <ScrollView style={styles.container}>
      <PostHeader
        attributes={{
          featureImg: data[1].featureImg,
          title: data[1].title,
          category: data[1].titleMeta,
          timeStamp: data[1].timeStamp,
          authorName: data[1].authorName,
          authorImg:
            'https://images.unsplash.com/flagged/photo-1573740144655-bbb6e88fb18a?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=375&q=80',
        }}
      />
      <CarouselBlock />
      <ScrollBlock
        attributes={{
          data: data,
          title: 'Type 1',
          accessButton: 'View All',
          type: 'type-1',
          reverse: false,
        }}
      />
      <ScrollBlock
        attributes={{
          data: data,
          title: 'Type 2',
          accessButton: 'View All',
          type: 'type-2',
          reverse: false,
        }}
      />
      <ScrollBlock
        attributes={{
          data: data,
          title: 'Type 1 Reverse',
          accessButton: 'View All',
          type: 'type-1',
          reverse: true,
        }}
      />
      <ScrollBlock
        attributes={{
          data: data,
          title: 'Type 2 Reverse',
          accessButton: 'View All',
          type: 'type-2',
          reverse: true,
        }}
      />
      <ScrollBlock
        attributes={{
          data: data,
          title: 'Scroll Default Card',
          accessButton: 'View All',
          // type: 'type-3', default is like type 3, So type 3 needs to be removed.
        }}
      />
      <ListBlock
        attributes={{
          data: data,
          title: 'List Type 1',
          accessButton: 'View All',
          type: 'type-1',
          reverse: true,
        }}
      />
      <ListBlock
        attributes={{
          data: data,
          title: 'List Type 2',
          accessButton: 'View All',
          type: 'type-2',
          reverse: true,
        }}
      />
      <ListBlock
        attributes={{
          data: data,
          title: 'List Default Card',
          accessButton: 'View All',
          // type: 'type-3', default is like type 3, So type 3 needs to be removed.
        }}
      />
      <ListBlock
        attributes={{
          data: data,
          title: 'List Type 1 Reverse',
          accessButton: 'View All',
          type: 'type-1',
          reverse: false,
        }}
      />
      <ListBlock
        attributes={{
          data: data,
          title: 'List Type 2 Reverse',
          accessButton: 'View All',
          type: 'type-2',
          reverse: false,
        }}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: color.light,
  },
});

const data = [
  {
    titleMeta: 'Mobile App',
    title: 'Lorem ipsum dolor sit amet consectetur adipisicing elit.',
    featureImg:
      'https://images.unsplash.com/photo-1598418031169-4cbc3bee87e1?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=375&q=80',
    excerpt:
      'Lorem ipsum dolor sit amet consectetur adipisicing elit. Corporis beatae quibusdam est saepe quia in dicta cupiditate reiciendis nihil facere!',
    authorName: 'John Doe',
    timeStamp: 'Yesterday',
  },
  {
    titleMeta: 'WooCommerce',
    title: 'Lorem ipsum dolor sit amet consectetur adipisicing elit.',
    featureImg:
      'https://images.unsplash.com/photo-1549486047-80c486539c06?ixlib=rb-1.2.1&auto=format&fit=crop&w=750&q=80',
    excerpt:
      'Lorem ipsum dolor sit amet consectetur adipisicing elit. Corporis beatae quibusdam est saepe quia in dicta cupiditate reiciendis nihil facere!',
    authorName: 'John Doe',
    timeStamp: 'Yesterday',
  },
  {
    titleMeta: 'App Experience',
    title: 'Lorem ipsum dolor sit amet consectetur adipisicing elit.',
    featureImg:
      'https://storage.googleapis.com/stateless-blog-appmaker-xyz/2020/08/6e176818-best-multi-vendor-plugin-for-woocommerce-768x432.png',
    excerpt:
      'Lorem ipsum dolor sit amet consectetur adipisicing elit. Corporis beatae quibusdam est saepe quia in dicta cupiditate reiciendis nihil facere!',
    // authorName: 'John Doe',
    timeStamp: 'Yesterday',
  },
  {
    titleMeta: 'Daily Update',
    title: 'Lorem ipsum dolor sit amet consectetur adipisicing elit.',
    // featureImg:
    //   'https://images.unsplash.com/photo-1598418031169-4cbc3bee87e1?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=375&q=80',
    excerpt:
      'Lorem ipsum dolor sit amet consectetur adipisicing elit. Corporis beatae quibusdam est saepe quia in dicta cupiditate reiciendis nihil facere!',
    authorName: 'John Doe',
    timeStamp: 'Yesterday',
  },
];

export default WordpressBlocks;
