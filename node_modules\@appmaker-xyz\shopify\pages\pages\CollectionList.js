const pagesData = {
  title: 'Home page - Collections list',
  blocks: [
    {
      clientId: '93f1e29b-5995-47b1-9c4d-952acb7c410f',
      name: 'appmaker/grid-item',
      isValid: true,
      attributes: {
        hasPages: true,
        appmakerAction: {
          params: { title: '{{blockItem.node.title}}' },
          action: 'OPEN_COLLECTION',
          OPEN_COLLECTION_ID: {
            id: '{{blockItem.node.id}}',
            label: '{{blockItem.node.title}}',
          },
        },
        numColumns: '3',
        dataSource: {
          source: 'shopify',
          attributes: {
            mapping: {
              items: 'data.data.collections.edges',
            },
            methodName: 'collections',
            params: '{{currentAction.params}}',
          },
          repeatable: 'Yes',
          repeatItem: 'DataSource',
        },
        showText: true,
        title: '{{blockItem.node.title}}',
        uri: '{{blockItem.node.image.url}}',
        imageContainerStyle: {
          height: 350,
          width: 350,
        },
      },
      innerBlocks: [],
    },
  ],
  _id: 'home',
};
export default pagesData;
