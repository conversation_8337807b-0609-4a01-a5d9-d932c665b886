{"version": 3, "names": ["apiPolyfills", "assertVersion", "api", "range", "throwVersionError", "version", "targets", "assumption", "undefined", "declare", "builder", "options", "dirname", "clonedA<PERSON>", "name", "Object", "keys", "copyApiObject", "declarePreset", "proto", "test", "getPrototypeOf", "has", "obj", "key", "prototype", "hasOwnProperty", "call", "Number", "isInteger", "Error", "limit", "stackTraceLimit", "err", "slice", "assign", "code"], "sources": ["../src/index.ts"], "sourcesContent": ["import type {\n  PluginAPI,\n  PluginObject,\n  PluginPass,\n  PresetAPI,\n  PresetObject,\n} from \"@babel/core\";\n\ntype APIPolyfillFactory<T extends keyof PluginAPI> = (\n  api: PluginAPI,\n) => PluginAPI[T];\n\ntype APIPolyfills = {\n  assertVersion: APIPolyfillFactory<\"assertVersion\">;\n  targets: APIPolyfillFactory<\"targets\">;\n  assumption: APIPolyfillFactory<\"assumption\">;\n};\n\nconst apiPolyfills: APIPolyfills = {\n  // Not supported by Babel 7 and early versions of Babel 7 beta.\n  // It's important that this is polyfilled for older Babel versions\n  // since it's needed to report the version mismatch.\n  assertVersion: (api: PluginAPI) => (range: number | string) => {\n    throwVersionError(range, api.version);\n  },\n  // This is supported starting from Babel 7.13\n  // TODO(Babel 8): Remove this polyfill\n  targets: () => () => {\n    return {};\n  },\n  // This is supported starting from Babel 7.13\n  // TODO(Babel 8): Remove this polyfill\n  assumption: () => () => {\n    return undefined;\n  },\n};\n\nexport function declare<State = {}, Option = {}>(\n  builder: (\n    api: PluginAPI,\n    options: Option,\n    dirname: string,\n  ) => PluginObject<State & PluginPass>,\n): (\n  api: PluginAPI,\n  options: Option,\n  dirname: string,\n) => PluginObject<State & PluginPass> {\n  return (api, options: Option, dirname: string) => {\n    let clonedApi: PluginAPI;\n\n    for (const name of Object.keys(\n      apiPolyfills,\n    ) as (keyof typeof apiPolyfills)[]) {\n      if (api[name]) continue;\n\n      // TODO: Use ??= when flow lets us to do so\n      clonedApi = clonedApi ?? copyApiObject(api);\n      // @ts-expect-error The shape of API polyfill is guaranteed by APIPolyfillFactory\n      clonedApi[name] = apiPolyfills[name](clonedApi);\n    }\n\n    // @ts-expect-error options || {} may not be assigned to Options\n    return builder(clonedApi ?? api, options || {}, dirname);\n  };\n}\n\nexport const declarePreset = declare as <Option = {}>(\n  builder: (api: PresetAPI, options: Option, dirname: string) => PresetObject,\n) => (api: PresetAPI, options: Option, dirname: string) => PresetObject;\n\nfunction copyApiObject(api: PluginAPI): PluginAPI {\n  // Babel >= 7 <= beta.41 passed the API as a new object that had\n  // babel/core as the prototype. While slightly faster, it also\n  // means that the Object.assign copy below fails. Rather than\n  // keep complexity, the Babel 6 behavior has been reverted and this\n  // normalizes all that for Babel 7.\n  let proto = null;\n  if (typeof api.version === \"string\" && /^7\\./.test(api.version)) {\n    proto = Object.getPrototypeOf(api);\n    if (\n      proto &&\n      (!has(proto, \"version\") ||\n        !has(proto, \"transform\") ||\n        !has(proto, \"template\") ||\n        !has(proto, \"types\"))\n    ) {\n      proto = null;\n    }\n  }\n\n  return {\n    ...proto,\n    ...api,\n  };\n}\n\nfunction has(obj: {}, key: string) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nfunction throwVersionError(range: string | number, version: string) {\n  if (typeof range === \"number\") {\n    if (!Number.isInteger(range)) {\n      throw new Error(\"Expected string or integer value.\");\n    }\n    range = `^${range}.0.0-0`;\n  }\n  if (typeof range !== \"string\") {\n    throw new Error(\"Expected string or integer value.\");\n  }\n\n  const limit = Error.stackTraceLimit;\n\n  if (typeof limit === \"number\" && limit < 25) {\n    // Bump up the limit if needed so that users are more likely\n    // to be able to see what is calling Babel.\n    Error.stackTraceLimit = 25;\n  }\n\n  let err;\n  if (version.slice(0, 2) === \"7.\") {\n    err = new Error(\n      `Requires Babel \"^7.0.0-beta.41\", but was loaded with \"${version}\". ` +\n        `You'll need to update your @babel/core version.`,\n    );\n  } else {\n    err = new Error(\n      `Requires Babel \"${range}\", but was loaded with \"${version}\". ` +\n        `If you are sure you have a compatible version of @babel/core, ` +\n        `it is likely that something in your build process is loading the ` +\n        `wrong version. Inspect the stack trace of this error to look for ` +\n        `the first entry that doesn't mention \"@babel/core\" or \"babel-core\" ` +\n        `to see what is calling Babel.`,\n    );\n  }\n\n  if (typeof limit === \"number\") {\n    Error.stackTraceLimit = limit;\n  }\n\n  throw Object.assign(err, {\n    code: \"BABEL_VERSION_UNSUPPORTED\",\n    version,\n    range,\n  } as any);\n}\n"], "mappings": ";;;;;;;AAkBA,MAAMA,YAA0B,GAAG;EAIjCC,aAAa,EAAGC,GAAc,IAAMC,KAAsB,IAAK;IAC7DC,iBAAiB,CAACD,KAAK,EAAED,GAAG,CAACG,OAAO,CAAC;EACvC,CAAC;EAGDC,OAAO,EAAE,MAAM,MAAM;IACnB,OAAO,CAAC,CAAC;EACX,CAAC;EAGDC,UAAU,EAAE,MAAM,MAAM;IACtB,OAAOC,SAAS;EAClB;AACF,CAAC;AAEM,SAASC,OAAO,CACrBC,OAIqC,EAKD;EACpC,OAAO,CAACR,GAAG,EAAES,OAAe,EAAEC,OAAe,KAAK;IAAA;IAChD,IAAIC,SAAoB;IAExB,KAAK,MAAMC,IAAI,IAAIC,MAAM,CAACC,IAAI,CAC5BhB,YAAY,CACb,EAAmC;MAAA;MAClC,IAAIE,GAAG,CAACY,IAAI,CAAC,EAAE;;MAGfD,SAAS,iBAAGA,SAAS,yBAAII,aAAa,CAACf,GAAG,CAAC;MAE3CW,SAAS,CAACC,IAAI,CAAC,GAAGd,YAAY,CAACc,IAAI,CAAC,CAACD,SAAS,CAAC;IACjD;;IAGA,OAAOH,OAAO,gBAACG,SAAS,0BAAIX,GAAG,EAAES,OAAO,IAAI,CAAC,CAAC,EAAEC,OAAO,CAAC;EAC1D,CAAC;AACH;AAEO,MAAMM,aAAa,GAAGT,OAE0C;AAAC;AAExE,SAASQ,aAAa,CAACf,GAAc,EAAa;EAMhD,IAAIiB,KAAK,GAAG,IAAI;EAChB,IAAI,OAAOjB,GAAG,CAACG,OAAO,KAAK,QAAQ,IAAI,MAAM,CAACe,IAAI,CAAClB,GAAG,CAACG,OAAO,CAAC,EAAE;IAC/Dc,KAAK,GAAGJ,MAAM,CAACM,cAAc,CAACnB,GAAG,CAAC;IAClC,IACEiB,KAAK,KACJ,CAACG,GAAG,CAACH,KAAK,EAAE,SAAS,CAAC,IACrB,CAACG,GAAG,CAACH,KAAK,EAAE,WAAW,CAAC,IACxB,CAACG,GAAG,CAACH,KAAK,EAAE,UAAU,CAAC,IACvB,CAACG,GAAG,CAACH,KAAK,EAAE,OAAO,CAAC,CAAC,EACvB;MACAA,KAAK,GAAG,IAAI;IACd;EACF;EAEA,yBACKA,KAAK,EACLjB,GAAG;AAEV;AAEA,SAASoB,GAAG,CAACC,GAAO,EAAEC,GAAW,EAAE;EACjC,OAAOT,MAAM,CAACU,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,GAAG,EAAEC,GAAG,CAAC;AACvD;AAEA,SAASpB,iBAAiB,CAACD,KAAsB,EAAEE,OAAe,EAAE;EAClE,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAI,CAACyB,MAAM,CAACC,SAAS,CAAC1B,KAAK,CAAC,EAAE;MAC5B,MAAM,IAAI2B,KAAK,CAAC,mCAAmC,CAAC;IACtD;IACA3B,KAAK,GAAI,IAAGA,KAAM,QAAO;EAC3B;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAM,IAAI2B,KAAK,CAAC,mCAAmC,CAAC;EACtD;EAEA,MAAMC,KAAK,GAAGD,KAAK,CAACE,eAAe;EAEnC,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,GAAG,EAAE,EAAE;IAG3CD,KAAK,CAACE,eAAe,GAAG,EAAE;EAC5B;EAEA,IAAIC,GAAG;EACP,IAAI5B,OAAO,CAAC6B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;IAChCD,GAAG,GAAG,IAAIH,KAAK,CACZ,yDAAwDzB,OAAQ,KAAI,GAClE,iDAAgD,CACpD;EACH,CAAC,MAAM;IACL4B,GAAG,GAAG,IAAIH,KAAK,CACZ,mBAAkB3B,KAAM,2BAA0BE,OAAQ,KAAI,GAC5D,gEAA+D,GAC/D,mEAAkE,GAClE,mEAAkE,GAClE,qEAAoE,GACpE,+BAA8B,CAClC;EACH;EAEA,IAAI,OAAO0B,KAAK,KAAK,QAAQ,EAAE;IAC7BD,KAAK,CAACE,eAAe,GAAGD,KAAK;EAC/B;EAEA,MAAMhB,MAAM,CAACoB,MAAM,CAACF,GAAG,EAAE;IACvBG,IAAI,EAAE,2BAA2B;IACjC/B,OAAO;IACPF;EACF,CAAC,CAAQ;AACX"}