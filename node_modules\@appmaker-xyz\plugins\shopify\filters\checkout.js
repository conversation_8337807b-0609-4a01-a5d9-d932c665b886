import { appmaker } from '@appmaker-xyz/core';

export const customPaymentSchema = {
  id: 'oneclick-payment-schema',
  regex: [
    /phonepe:\/\/pay\?.*/i,
    /paytm:\/\/upi\/pay?.*/i,
    /paytmmp:\/\/pay?.*/i,
    /upi:\/\/pay?.*/i,
    /tez:\/\/upi\/pay?.*/i,
  ],
  action: (url) => {
    return {
      action: 'OPEN_URL',
      params: {
        url,
      },
    };
  },
};

export function addCheckoutWebviewURLFilters(settings) {
  if (settings.disable_upi_payment_apps_opening_in_checkout !== true) {
    appmaker.addFilter(
      'webview-custom-url-filters', // hook to add custom url filters
      'oneclick-payment-url-filters', // namespace for the filter
      (currentFilters) => {
        return [...currentFilters, customPaymentSchema]; // add the filter to the list of filters
      },
    );
  }
}
