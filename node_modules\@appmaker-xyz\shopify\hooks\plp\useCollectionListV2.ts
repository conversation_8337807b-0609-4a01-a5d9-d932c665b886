import { usePageState } from '@appmaker-xyz/core';
import { useFlashListLayoutConfig } from './useFlashListLayoutConfig';

type CollectionQuery = {
  collectionId: string;
  collectionHandle: string;
};

const useCollectionListV2 = () => {
  const flashLayoutConfigs = useFlashListLayoutConfig();
  const setPageState = usePageState((state) => state?.setPageState);
  const currentCollection = usePageState((state) => state?.currentCollection);
  const setCollectionSource = (collection: CollectionQuery) => {
    setPageState({
      currentCollection: {
        ...collection,
      },
    });
  };
  return {
    ...flashLayoutConfigs,
    setCollectionSource,
    currentCollection,
  };
};

export { useCollectionListV2 };
