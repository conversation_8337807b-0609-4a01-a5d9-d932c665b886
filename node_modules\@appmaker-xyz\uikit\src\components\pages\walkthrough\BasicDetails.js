import React, { useState } from 'react';
import { KeyboardAvoidingView, Platform, StyleSheet } from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import {
  Layout,
  Input,
  Button,
  AppmakerText,
  AppTouchable,
  AppImage,
} from '@appmaker-xyz/uikit';

const Onboarding = ({ attributes, onPress, onAction }) => {
  const {
    image,
    // image = 'https://images.unsplash.com/photo-1655069918407-ee5b4b76acc7',
    roundedButton = false,
    buttonColor,
    appmakerAction,
  } = attributes;
  var [name, setName] = useState('');
  var [email, setEmail] = useState('');

  const { color, spacing } = useApThemeState();
  const buttonBgColor = buttonColor ? buttonColor : color.dark;
  const styles = allStyles({ color, spacing, buttonBgColor, roundedButton });

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      onAction(appmakerAction);
    };
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}>
      <Layout style={styles.innerContainer}>
        <AppmakerText status="dark" category="h1Heading" style={styles.title}>
          Basic Details
        </AppmakerText>
        <Layout style={styles.initial}>
          {image ? (
            <AppImage uri={image} style={styles.image} resizeMode="cover" />
          ) : (
            <AppmakerText category="largeHeading" status="white">
              {name ? Array.from(name)[0].toUpperCase() : '#'}
            </AppmakerText>
          )}
          <AppTouchable
            style={styles.update}
            onPress={() => console.log('Update image')}>
            <AppmakerText
              category="highlighter2"
              status="white"
              style={styles.text}>
              Update
            </AppmakerText>
          </AppTouchable>
        </Layout>

        <Layout style={styles.contentContainer}>
          <Input
            label="Full Name"
            value={name}
            onChangeText={(e) => setName(e)}
          />
          <Input
            label="Email"
            value={email}
            onChangeText={(i) => setEmail(i)}
          />
          <Button
            status="dark"
            block
            wholeContainerStyle={styles.button}
            baseSize={true}
            onPress={onPressHandle}>
            Save & Continue
          </Button>
        </Layout>
      </Layout>
    </KeyboardAvoidingView>
  );
};

const allStyles = ({ spacing, color, roundedButton, buttonBgColor }) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    innerContainer: {
      flex: 1,
      paddingHorizontal: spacing.base,
      justifyContent: 'center',
      alignItems: 'center',
    },
    initial: {
      backgroundColor: buttonBgColor ? buttonBgColor : color.dark,
      borderRadius: 100,
      overflow: 'hidden',
      width: 90,
      height: 90,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: spacing.xl,
      position: 'relative',
    },
    title: {
      textAlign: 'center',
      marginBottom: spacing.lg,
    },
    contentContainer: { width: '100%' },
    button: {
      borderRadius: roundedButton ? 40 : 0,
      backgroundColor: buttonBgColor,
      borderWidth: 0,
    },
    update: {
      backgroundColor: `${color.dark}80`,
      width: '100%',
      textAlign: 'center',
      alignSelf: 'flex-end',
      paddingBottom: spacing.nano,
      position: 'absolute',
      bottom: 0,
    },
    image: {
      width: '100%',
      height: '100%',
    },
    text: { textAlign: 'center' },
  });

export default Onboarding;
