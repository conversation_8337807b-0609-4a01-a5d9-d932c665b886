import React from 'react';
import { Image, Pressable } from 'react-native';
import { SvgUri } from 'react-native-svg';

export default function ImageBlock({ attributes, onAction }) {
  const {
    uri,
    width = 100,
    height = 100,
    resizeMode,
    advancedStyle,
    appmakerAction,
    aspectRatio,
  } = attributes;

  if (!uri) {
    return null;
  }

  let ratio = parseFloat(aspectRatio);

  const isSvg = uri?.includes('.svg');

  return (
    <Pressable
      onPress={() => onAction && appmakerAction && onAction(appmakerAction)}>
      {isSvg ? (
        <SvgUri width={width} height={height} uri={uri} style={advancedStyle} />
      ) : (
        <Image
          source={{ uri }}
          style={{
            height: aspectRatio ? undefined : height,
            width: aspectRatio ? undefined : width,
            aspectRatio: aspectRatio ? ratio : undefined,
            ...advancedStyle,
          }}
          resizeMode={resizeMode}
        />
      )}
    </Pressable>
  );
}
