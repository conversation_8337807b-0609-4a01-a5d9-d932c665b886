import React from 'react';
import { Layout } from '@appmaker-xyz/ui';

function getBlocksBySlotId(innerBlocks, slotId) {
  const slotBlocks = innerBlocks.find((block) => {
    if (block.attributes.slotId === slotId) {
      return block.innerBlocks;
    }
  });
  if (slotBlocks) {
    return slotBlocks.innerBlocks;
  }
  return [];
}
export function ProductSlotBlock({
  innerBlocks,
  BlockItem,
  blockData,
  slotId,
  ...props
}) {
  const blocks = getBlocksBySlotId(innerBlocks, slotId) || [];
  return blocks.map((block) => {
    return (
      <Layout style={{ marginHorizontal: 2 }}>
        <BlockItem
          key={block.id}
          block={block}
          blockData={blockData}
          {...props}
        />
      </Layout>
    );
  });
}
