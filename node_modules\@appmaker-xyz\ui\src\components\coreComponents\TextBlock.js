import React, { useState, useCallback } from 'react';
import { StyleSheet, LayoutAnimation } from 'react-native';
import { Layout, ThemeText } from '../atoms';

const TextBlock = ({ clientId, attributes, ...props }) => {
  const [lengthMore, setLengthMore] = useState(false); //to show the "Show more & show Line"
  const [textShown, setTextShown] = useState(false); //To show remaining Text
  const { blockTitle, content } = attributes;

  const onTextLayout = useCallback((e) => {
    setLengthMore(e.nativeEvent.lines.length >= 3); //to check the text is more than 3 lines or not
  }, []);
  const toggleNumberOfLines = () => {
    setTextShown(!textShown); //To toggle the show text or hide it
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  };
  return content ? (
    <Layout style={styles.container}>
      <Layout style={styles.titleContainer}>
        <ThemeText size="md" fontFamily="medium">
          {blockTitle}
        </ThemeText>
      </Layout>
      <Layout style={styles.subPadding}>
        <ThemeText
          html={true}
          onTextLayout={onTextLayout}
          numberOfLines={textShown ? undefined : 3}>
          {typeof content === 'string' ? content?.trim?.() || content : content}
        </ThemeText>
        {lengthMore ? (
          <ThemeText
            html={true}
            color="#A9AEB7"
            onPress={toggleNumberOfLines}
            style={styles.moreText}>
            {textShown ? 'Show less' : 'Show more'}
          </ThemeText>
        ) : null}
      </Layout>
    </Layout>
  ) : null;
};
const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    marginVertical: 1,
  },
  titleContainer: {
    paddingHorizontal: 12,
    paddingTop: 12,
    marginBottom: 6,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
  },
  subPadding: {
    paddingHorizontal: 12,
    paddingBottom: 12,
  },
  moreText: { marginTop: 10 },
});
export default TextBlock;
