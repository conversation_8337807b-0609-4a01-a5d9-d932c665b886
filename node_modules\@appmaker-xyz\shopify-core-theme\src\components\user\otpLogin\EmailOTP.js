import React, { useEffect, useRef, useState } from 'react';
import {
  Button,
  Input,
  Layout,
  OtpInputCard,
  Radio,
  ThemeText,
} from '@appmaker-xyz/ui';
import { useEmailOtpLogin } from '@appmaker-xyz/shopify';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';

const EmailOTP = (props) => {
  const blockData = props?.blockData;
  const [email, setEmail] = useState(blockData?.email || '');
  const emailRef = useRef(null);
  const { styles, theme } = useStyles(stylesheet);
  const { buttonColor = '#000', buttonTextColor = '#fff' } = props;

  const onPressReceiveOtp = (data) => {
    const basicEmailRegex =
      /[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?/;
    if (email && basicEmailRegex.test(email)) {
      sendCode(email);
    } else {
      props?.onAction &&
        props?.onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: 'Please enter a valid email address',
          },
        });
    }
  };

  const {
    currentStep,
    sendCode,
    verifyCode,
    sendStatus,
    verifyErrorMessage,
    resendStatus,
    resendErrorMessage,
    resendCode,
    reset,
    failedAttemptCount,
    otpDigitCount,
  } = useEmailOtpLogin({
    handleAction: props?.onAction,
    initialData: blockData,
  });

  useEffect(() => {
    if (currentStep === 'send' && emailRef?.current) {
      emailRef.current.focus();
    }
  }, [currentStep]);

  let emailInput;

  if (blockData?.existingEmails?.length > 0) {
    const emailOptions = blockData?.existingEmails.map((email) => ({
      label: email,
      value: email,
    }));

    //set first email as default
    if (!email) {
      setEmail(emailOptions[0]?.value);
    }

    emailInput = (
      <Layout
        style={{
          marginBottom: 20,
        }}>
        <ThemeText
          fontFamily="medium"
          style={{
            marginBottom: 20,
          }}>
          Multiple emails are already registered for this phone. Kindly choose
          the default email
        </ThemeText>
        <Radio
          label="Email"
          name="email"
          options={emailOptions}
          onPress={(value) => {
            setEmail(value);
          }}
          value={email}
          search={false}
        />
      </Layout>
    );
  } else {
    emailInput = (
      <Input
        label="Email"
        value={email}
        disabled={blockData?.email_disabled}
        editable={!blockData?.email_disabled}
        inputRef={emailRef}
        onChangeText={setEmail}
        keyboardType="email-address"
        placeholder={'Enter your email'}
      />
    );
  }

  return currentStep === 'send' ||
    sendStatus === 'loading' ||
    sendStatus === 'error' ? (
    <>
      {emailInput}
      <Button
        title="Receive OTP"
        onPress={onPressReceiveOtp}
        isLoading={sendStatus === 'loading' || resendStatus === 'loading'}
        color={theme.colors.primaryButtonBackground || buttonColor}
        activityIndicatorColor={
          theme.colors.primaryButtonText || buttonTextColor
        }
        textColor={theme.colors.primaryButtonText || buttonTextColor}
        fontType={'medium'}
        size={'md'}
        buttonStyle={styles.button}
      />
    </>
  ) : (
    <OtpInputCard
      verifyCode={verifyCode}
      phoneNumber={email}
      onEditNumber={reset}
      verifyErrorMessage={verifyErrorMessage}
      failedAttemptCount={failedAttemptCount}
      resendErrorMessage={resendErrorMessage}
      resendCode={resendCode}
      resendLoading={resendStatus}
      otpLength={Number(otpDigitCount)}
      type="email"
    />
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    padding: 12,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  inputContainer: { height: 50 },
  button: {
    marginTop: 18,
  },
}));

export default EmailOTP;
