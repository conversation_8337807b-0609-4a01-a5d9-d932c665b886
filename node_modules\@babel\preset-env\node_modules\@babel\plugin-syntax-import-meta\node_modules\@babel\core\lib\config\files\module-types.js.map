{"version": 3, "names": ["_async", "require", "_path", "data", "_url", "_semver", "_rewriteStackTrace", "_configError", "_transformFile", "asyncGeneratorStep", "gen", "resolve", "reject", "_next", "_throw", "key", "arg", "info", "value", "error", "done", "Promise", "then", "_asyncToGenerator", "fn", "self", "args", "arguments", "apply", "err", "undefined", "import_", "_unused", "supportsESM", "semver", "satisfies", "process", "versions", "node", "exports", "loadCodeDefault", "filepath", "asyncError", "fallbackToTranspiledModule", "path", "extname", "loadCjsDefault", "loadCtsDefault", "e", "code", "isAsync", "waitFor", "loadMjsDefault", "ConfigError", "ext", "hasTsSupport", "extensions", "handler", "opts", "babelrc", "configFile", "sourceType", "sourceMaps", "presets", "getTSPreset", "Object", "assign", "disallowAmbiguousJSXLike", "allExtensions", "onlyRemoveTypeImports", "optimizeConstEnums", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m", "filename", "endsWith", "_compile", "transformFileSync", "endHiddenCallStack", "module", "__esModule", "default", "_x", "_loadMjsDefault", "pathToFileURL", "message", "pnp"], "sources": ["../../../src/config/files/module-types.ts"], "sourcesContent": ["import { isAsync, waitFor } from \"../../gensync-utils/async\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport path from \"path\";\nimport { pathToFileURL } from \"url\";\nimport { createRequire } from \"module\";\nimport semver from \"semver\";\n\nimport { endHiddenCallStack } from \"../../errors/rewrite-stack-trace\";\nimport ConfigError from \"../../errors/config-error\";\n\nimport type { InputOptions } from \"..\";\nimport { transformFileSync } from \"../../transform-file\";\n\nconst require = createRequire(import.meta.url);\n\nlet import_: ((specifier: string | URL) => any) | undefined;\ntry {\n  // Old Node.js versions don't support import() syntax.\n  import_ = require(\"./import.cjs\");\n} catch {}\n\nexport const supportsESM = semver.satisfies(\n  process.versions.node,\n  // older versions, starting from 10, support the dynamic\n  // import syntax but always return a rejected promise.\n  \"^12.17 || >=13.2\",\n);\n\nexport default function* loadCodeDefault(\n  filepath: string,\n  asyncError: string,\n  // TODO(Babel 8): Remove this\n  fallbackToTranspiledModule: boolean = false,\n): Handler<unknown> {\n  switch (path.extname(filepath)) {\n    case \".cjs\":\n      return loadCjsDefault(filepath, fallbackToTranspiledModule);\n    case \".mjs\":\n      break;\n    case \".cts\":\n      return loadCtsDefault(filepath);\n    default:\n      try {\n        return loadCjsDefault(filepath, fallbackToTranspiledModule);\n      } catch (e) {\n        if (e.code !== \"ERR_REQUIRE_ESM\") throw e;\n      }\n  }\n  if (yield* isAsync()) {\n    return yield* waitFor(loadMjsDefault(filepath));\n  }\n  throw new ConfigError(asyncError, filepath);\n}\n\nfunction loadCtsDefault(filepath: string) {\n  const ext = \".cts\";\n  const hasTsSupport = !!(\n    require.extensions[\".ts\"] ||\n    require.extensions[\".cts\"] ||\n    require.extensions[\".mts\"]\n  );\n\n  let handler: NodeJS.RequireExtensions[\"\"];\n\n  if (!hasTsSupport) {\n    const opts: InputOptions = {\n      babelrc: false,\n      configFile: false,\n      sourceType: \"script\",\n      sourceMaps: \"inline\",\n      presets: [\n        [\n          getTSPreset(filepath),\n          {\n            disallowAmbiguousJSXLike: true,\n            allExtensions: true,\n            onlyRemoveTypeImports: true,\n            optimizeConstEnums: true,\n            ...(!process.env.BABEL_8_BREAKING && {\n              allowDeclareFields: true,\n            }),\n          },\n        ],\n      ],\n    };\n\n    handler = function (m, filename) {\n      // If we want to support `.ts`, `.d.ts` must be handled specially.\n      if (handler && filename.endsWith(ext)) {\n        // @ts-expect-error Undocumented API\n        return m._compile(\n          transformFileSync(filename, {\n            ...opts,\n            filename,\n          }).code,\n          filename,\n        );\n      }\n      return require.extensions[\".js\"](m, filename);\n    };\n    require.extensions[ext] = handler;\n  }\n  try {\n    return endHiddenCallStack(require)(filepath);\n  } finally {\n    if (!hasTsSupport) {\n      if (require.extensions[ext] === handler) delete require.extensions[ext];\n      handler = undefined;\n    }\n  }\n}\n\nfunction loadCjsDefault(filepath: string, fallbackToTranspiledModule: boolean) {\n  const module = endHiddenCallStack(require)(filepath);\n  return module?.__esModule\n    ? // TODO (Babel 8): Remove \"module\" and \"undefined\" fallback\n      module.default || (fallbackToTranspiledModule ? module : undefined)\n    : module;\n}\n\nasync function loadMjsDefault(filepath: string) {\n  if (!import_) {\n    throw new ConfigError(\n      \"Internal error: Native ECMAScript modules aren't supported by this platform.\\n\",\n      filepath,\n    );\n  }\n\n  // import() expects URLs, not file paths.\n  // https://github.com/nodejs/node/issues/31710\n  const module = await endHiddenCallStack(import_)(pathToFileURL(filepath));\n  return module.default;\n}\n\nfunction getTSPreset(filepath: string) {\n  try {\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    return require(\"@babel/preset-typescript\");\n  } catch (error) {\n    if (error.code !== \"MODULE_NOT_FOUND\") throw error;\n\n    let message =\n      \"You appear to be using a .cts file as Babel configuration, but the `@babel/preset-typescript` package was not found: please install it!\";\n\n    if (process.versions.pnp) {\n      // Using Yarn PnP, which doesn't allow requiring packages that are not\n      // explicitly specified as dependencies.\n      // TODO(Babel 8): Explicitly add `@babel/preset-typescript` as an\n      // optional peer dependency of `@babel/core`.\n      message += `\nIf you are using Yarn Plug'n'Play, you may also need to add the following configuration to your .yarnrc.yml file:\n\npackageExtensions:\n\\t\"@babel/core@*\":\n\\t\\tpeerDependencies:\n\\t\\t\\t\"@babel/preset-typescript\": \"*\"\n`;\n    }\n\n    throw new ConfigError(message, filepath);\n  }\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA,SAAAC,MAAA;EAAA,MAAAC,IAAA,GAAAF,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAC,KAAA;EAAA,MAAAD,IAAA,GAAAF,OAAA;EAAAG,IAAA,YAAAA,CAAA;IAAA,OAAAD,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,QAAA;EAAA,MAAAF,IAAA,GAAAF,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAG,kBAAA,GAAAL,OAAA;AACA,IAAAM,YAAA,GAAAN,OAAA;AAGA,IAAAO,cAAA,GAAAP,OAAA;AAAyD,SAAAQ,mBAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAC,GAAA,cAAAC,IAAA,GAAAP,GAAA,CAAAK,GAAA,EAAAC,GAAA,OAAAE,KAAA,GAAAD,IAAA,CAAAC,KAAA,WAAAC,KAAA,IAAAP,MAAA,CAAAO,KAAA,iBAAAF,IAAA,CAAAG,IAAA,IAAAT,OAAA,CAAAO,KAAA,YAAAG,OAAA,CAAAV,OAAA,CAAAO,KAAA,EAAAI,IAAA,CAAAT,KAAA,EAAAC,MAAA;AAAA,SAAAS,kBAAAC,EAAA,6BAAAC,IAAA,SAAAC,IAAA,GAAAC,SAAA,aAAAN,OAAA,WAAAV,OAAA,EAAAC,MAAA,QAAAF,GAAA,GAAAc,EAAA,CAAAI,KAAA,CAAAH,IAAA,EAAAC,IAAA,YAAAb,MAAAK,KAAA,IAAAT,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,UAAAI,KAAA,cAAAJ,OAAAe,GAAA,IAAApB,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,WAAAe,GAAA,KAAAhB,KAAA,CAAAiB,SAAA;AAIzD,IAAIC,OAAuD;AAC3D,IAAI;EAEFA,OAAO,GAAG9B,OAAO,CAAC,cAAc,CAAC;AACnC,CAAC,CAAC,OAAA+B,OAAA,EAAM,CAAC;AAEF,MAAMC,WAAW,GAAGC,SAAM,CAACC,SAAS,CACzCC,OAAO,CAACC,QAAQ,CAACC,IAAI,EAGrB,kBAAkB,CACnB;AAACC,OAAA,CAAAN,WAAA,GAAAA,WAAA;AAEa,UAAUO,eAAeA,CACtCC,QAAgB,EAChBC,UAAkB,EAElBC,0BAAmC,GAAG,KAAK,EACzB;EAClB,QAAQC,OAAI,CAACC,OAAO,CAACJ,QAAQ,CAAC;IAC5B,KAAK,MAAM;MACT,OAAOK,cAAc,CAACL,QAAQ,EAAEE,0BAA0B,CAAC;IAC7D,KAAK,MAAM;MACT;IACF,KAAK,MAAM;MACT,OAAOI,cAAc,CAACN,QAAQ,CAAC;IACjC;MACE,IAAI;QACF,OAAOK,cAAc,CAACL,QAAQ,EAAEE,0BAA0B,CAAC;MAC7D,CAAC,CAAC,OAAOK,CAAC,EAAE;QACV,IAAIA,CAAC,CAACC,IAAI,KAAK,iBAAiB,EAAE,MAAMD,CAAC;MAC3C;EAAC;EAEL,IAAI,OAAO,IAAAE,cAAO,GAAE,EAAE;IACpB,OAAO,OAAO,IAAAC,cAAO,EAACC,cAAc,CAACX,QAAQ,CAAC,CAAC;EACjD;EACA,MAAM,IAAIY,oBAAW,CAACX,UAAU,EAAED,QAAQ,CAAC;AAC7C;AAEA,SAASM,cAAcA,CAACN,QAAgB,EAAE;EACxC,MAAMa,GAAG,GAAG,MAAM;EAClB,MAAMC,YAAY,GAAG,CAAC,EACpBtD,OAAO,CAACuD,UAAU,CAAC,KAAK,CAAC,IACzBvD,OAAO,CAACuD,UAAU,CAAC,MAAM,CAAC,IAC1BvD,OAAO,CAACuD,UAAU,CAAC,MAAM,CAAC,CAC3B;EAED,IAAIC,OAAqC;EAEzC,IAAI,CAACF,YAAY,EAAE;IACjB,MAAMG,IAAkB,GAAG;MACzBC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE,CACP,CACEC,WAAW,CAACvB,QAAQ,CAAC,EAAAwB,MAAA,CAAAC,MAAA;QAEnBC,wBAAwB,EAAE,IAAI;QAC9BC,aAAa,EAAE,IAAI;QACnBC,qBAAqB,EAAE,IAAI;QAC3BC,kBAAkB,EAAE;MAAI,GACa;QACnCC,kBAAkB,EAAE;MACtB,CAAC,EAEJ;IAEL,CAAC;IAEDd,OAAO,GAAG,SAAAA,CAAUe,CAAC,EAAEC,QAAQ,EAAE;MAE/B,IAAIhB,OAAO,IAAIgB,QAAQ,CAACC,QAAQ,CAACpB,GAAG,CAAC,EAAE;QAErC,OAAOkB,CAAC,CAACG,QAAQ,CACf,IAAAC,gCAAiB,EAACH,QAAQ,EAAAR,MAAA,CAAAC,MAAA,KACrBR,IAAI;UACPe;QAAQ,GACR,CAACxB,IAAI,EACPwB,QAAQ,CACT;MACH;MACA,OAAOxE,OAAO,CAACuD,UAAU,CAAC,KAAK,CAAC,CAACgB,CAAC,EAAEC,QAAQ,CAAC;IAC/C,CAAC;IACDxE,OAAO,CAACuD,UAAU,CAACF,GAAG,CAAC,GAAGG,OAAO;EACnC;EACA,IAAI;IACF,OAAO,IAAAoB,qCAAkB,EAAC5E,OAAO,CAAC,CAACwC,QAAQ,CAAC;EAC9C,CAAC,SAAS;IACR,IAAI,CAACc,YAAY,EAAE;MACjB,IAAItD,OAAO,CAACuD,UAAU,CAACF,GAAG,CAAC,KAAKG,OAAO,EAAE,OAAOxD,OAAO,CAACuD,UAAU,CAACF,GAAG,CAAC;MACvEG,OAAO,GAAG3B,SAAS;IACrB;EACF;AACF;AAEA,SAASgB,cAAcA,CAACL,QAAgB,EAAEE,0BAAmC,EAAE;EAC7E,MAAMmC,MAAM,GAAG,IAAAD,qCAAkB,EAAC5E,OAAO,CAAC,CAACwC,QAAQ,CAAC;EACpD,OAAOqC,MAAM,YAANA,MAAM,CAAEC,UAAU,GAErBD,MAAM,CAACE,OAAO,KAAKrC,0BAA0B,GAAGmC,MAAM,GAAGhD,SAAS,CAAC,GACnEgD,MAAM;AACZ;AAAC,SAEc1B,cAAcA,CAAA6B,EAAA;EAAA,OAAAC,eAAA,CAAAtD,KAAA,OAAAD,SAAA;AAAA;AAAA,SAAAuD,gBAAA;EAAAA,eAAA,GAAA3D,iBAAA,CAA7B,WAA8BkB,QAAgB,EAAE;IAC9C,IAAI,CAACV,OAAO,EAAE;MACZ,MAAM,IAAIsB,oBAAW,CACnB,gFAAgF,EAChFZ,QAAQ,CACT;IACH;IAIA,MAAMqC,MAAM,SAAS,IAAAD,qCAAkB,EAAC9C,OAAO,CAAC,CAAC,IAAAoD,oBAAa,EAAC1C,QAAQ,CAAC,CAAC;IACzE,OAAOqC,MAAM,CAACE,OAAO;EACvB,CAAC;EAAA,OAAAE,eAAA,CAAAtD,KAAA,OAAAD,SAAA;AAAA;AAED,SAASqC,WAAWA,CAACvB,QAAgB,EAAE;EACrC,IAAI;IAEF,OAAOxC,OAAO,CAAC,0BAA0B,CAAC;EAC5C,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACd,IAAIA,KAAK,CAAC8B,IAAI,KAAK,kBAAkB,EAAE,MAAM9B,KAAK;IAElD,IAAIiE,OAAO,GACT,yIAAyI;IAE3I,IAAIhD,OAAO,CAACC,QAAQ,CAACgD,GAAG,EAAE;MAKxBD,OAAO,IAAK;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;IACG;IAEA,MAAM,IAAI/B,oBAAW,CAAC+B,OAAO,EAAE3C,QAAQ,CAAC;EAC1C;AACF;AAAC"}