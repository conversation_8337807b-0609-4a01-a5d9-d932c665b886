import React from 'react';
import { StyleSheet } from 'react-native';
import { AppImage, AppTouchable, AppmakerText } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import { useOrientation } from '@appmaker-xyz/react-native';
import { analytics } from '@appmaker-xyz/core';

const GridItem = ({ attributes, onAction, onPress, clientId }) => {
  const {
    image,
    uri,
    text,
    appmakerAction,
    thumbnail_meta = {},
    imageContainerStyle = {},
    numColumns = 4,
    enableGap,
    __appmaker_analytics_parent_name,
    __appmaker_analytics_parent_id,
  } = attributes;

  const { color, spacing } = useApThemeState();
  const imageRatio =
    thumbnail_meta.width / thumbnail_meta.height ||
    imageContainerStyle.width / imageContainerStyle.height ||
    1;
  const { screenWidth: width } = useOrientation();
  // const width = thumbnail_meta.width;
  // const count = attributes.numColumns;
  // width of single items as screenWidth/2
  const imageWidth = width / numColumns;

  // default heigh to be half screen width
  let imageHeight = imageWidth;

  // check if everyting has value to avoid possible null division issue
  if (
    thumbnail_meta &&
    thumbnail_meta.width &&
    thumbnail_meta.height &&
    thumbnail_meta.height !== ''
  ) {
    // if response has dimensions create new Heigh
    // based on the dimensions,
    // but keep width as half of screen
    imageHeight =
      width / numColumns / (thumbnail_meta.width / thumbnail_meta.height);
  }
  let imageUrl;
  imageUrl = uri ? uri : image?.url;
  const styles = allStyles({ color, spacing, imageRatio, enableGap, text });
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      analytics.track(
        'appmaker_block_click',
        {
          blockType: 'appmaker_grid',
          blockId: clientId,
          blockLabel: attributes?.__appmaker_analytics?.blockLabel,
          ...(image.id && {
            resourceId: attributes?.__appmaker_analytics?.resourceId,
          }),
          resourceUrl: imageUrl,
          resourceName: attributes?.__appmaker_analytics?.resourceName,
          parentId: __appmaker_analytics_parent_id,
          parentName: __appmaker_analytics_parent_name,
        },
        {
          blockAttributes: attributes,
          blockClientId: clientId,
          blockAction: appmakerAction,
          __appmaker_analytics_parent_id,
          __appmaker_analytics_parent_name,
          resourceName: attributes?.__appmaker_analytics?.resourceName,
        },
      );
      onAction(appmakerAction);
    };
  }
  // return null;
  const imageStyle = {
    width: '100%',
    height: '100%',
  };

  return (
    <AppTouchable
      onPress={onPressHandle}
      clientId={clientId}
      style={[
        styles.container,
        {
          width: enableGap
            ? imageWidth - spacing.nano * (numColumns - (numColumns - 2))
            : imageWidth,
        },
      ]}>
      <AppImage uri={imageUrl} style={imageStyle} />
      {text ? (
        <AppmakerText
          category="bodySubText"
          style={styles.text}
          numberOfLines={2}>
          {text}
        </AppmakerText>
      ) : null}
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color, imageRatio, enableGap, text }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      borderRadius: spacing.nano,
      margin: enableGap ? spacing.nano : 0,
      aspectRatio: imageRatio,
      marginBottom: text ? 20 : enableGap ? spacing.nano : 0,
    },
    text: {
      textAlign: 'center',
    },
  });

export default GridItem;
