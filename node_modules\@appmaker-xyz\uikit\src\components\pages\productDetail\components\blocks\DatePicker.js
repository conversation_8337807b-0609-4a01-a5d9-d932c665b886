import React, {useState} from 'react';
import {StyleSheet, Platform} from 'react-native';
import {AppTouchable, AppmakerText} from '../../../../../components';
import {useApThemeState} from '../../../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';

const DatePicker = () => {
  const {spacing, color, font} = useApThemeState();
  const styles = allStyles({spacing, color});

  const [date, setDate] = useState(new Date());
  const [show, setShow] = useState(false);

  const onChange = (event, selectedDate) => {
    const currentDate = selectedDate || date;
    setShow(Platform.OS === 'ios');
    setDate(currentDate);
  };

  const showPicker = () => {
    setShow(true);
  };

  return (
    <AppTouchable style={styles.selectBlock} onPress={showPicker}>
      <AppmakerText>{date.toString()}</AppmakerText>
      <Icon name="chevron-down" size={font.size.semi} color={color.dark} />
      {/* {show && (
        <DateTimePicker
          testID="dateTimePicker"
          value={date}
          mode="date"
          is24Hour={true}
          display="default"
          textColor="red"
          onChange={onChange}
        />
      )} */}
    </AppTouchable>
  );
};

const allStyles = ({spacing, color}) =>
  StyleSheet.create({
    selectBlock: {
      marginHorizontal: spacing.base,
      marginBottom: spacing.base,
      backgroundColor: color.light,
      padding: spacing.base,
      borderRadius: spacing.nano,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
  });

export default DatePicker;
