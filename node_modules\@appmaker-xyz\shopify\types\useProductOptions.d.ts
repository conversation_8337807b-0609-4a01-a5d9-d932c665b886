declare module '@appmaker-xyz/shopify' {
  import { Dispatch } from 'react';

  interface ProductNode {
    id: string;
    title: string;
    options: Array<{
      name: string;
      values: string[];
    }>;
    variants: {
      edges: Array<{
        node: {
          selectedOptions: Array<{
            name: string;
            value: string;
          }>;
          availableForSale: boolean;
          quantityAvailable: number;
          id: string;
          title: string;
          compareAtPrice: string | null;
          price: string;
        };
      }>;
    };
  }

  interface BlockData {
    node: ProductNode;
  }

  interface UseProductOptionsProps {
    blockData: BlockData;
    pageDispatch: Dispatch<{
      type: string;
      variationKey: string;
      variationValue: string;
    }>;
  }

  interface VariationOption {
    name: string;
    key: string;
    options: Array<{
      id: string;
      label: string;
      value: string;
      variationName: string;
    }>;
    defaultValue: string;
  }

  interface OptionPrice {
    compareAtPrice: string | null;
    price: string | null;
    availableForSale: boolean | null;
  }

  interface UseProductOptionsReturn {
    sizeChartUrl: string;
    sizeChartVariationNames: any;
    variationOptions: VariationOption[];
    setOption: (variationKey: string, variationValue: string) => void;
    isOptionAvilable: (optionName: string, optionValue: string) => boolean;
    isOptionAvilableV2: (optionName: string, optionValue: string) => boolean;
    isOptionAvailable: (optionName: string, optionValue: string) => boolean;
    getAvailableOptions: (name: string) => string[] | undefined;
    getOptionPrice: (optionName: string, optionValue: string) => OptionPrice;
    variants: ProductNode['variants'];
    product: BlockData;
    selectedOptions: Record<string, string> | null;
    options: ProductNode['options'];
    scrollable: boolean;
  }

  function useProductOptions(props: UseProductOptionsProps): UseProductOptionsReturn;
}