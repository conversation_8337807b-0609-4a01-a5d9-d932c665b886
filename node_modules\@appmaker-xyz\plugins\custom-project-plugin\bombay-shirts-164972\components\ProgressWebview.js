import React, { useState } from 'react';
import { View } from 'react-native';
import WebView from 'react-native-webview';
// import * as Progress from 'react-native-progress';
const LoadingBar = ({ color = '#3B78E7', percent, height = 3 }) => {
  console.log(`${parseInt(percent) * 100}%`);
  const style = {
    backgroundColor: 'blue',
    width: `${percent * 100}%`,
    height,
  };
  return <View style={[style]} />;
};

const ProgressBarWebview = ({ uri, onMessage }) => {
  const [progress, setProgress] = useState(0);
  const [isLoaded, setLoaded] = useState(false);
  const source = { uri };
  return (
    <View style={{ flex: 1, position: 'relative' }}>
      {!isLoaded ? <LoadingBar percent={progress} color="#ff8300" /> : null}
      <WebView
        style={{ width: '100%' }}
        source={source}
        // source={{ html: '<h1>This is a statsampleic HTML source!</h1>' }}
        // source={source}
        onError={(event) =>
          alert(`Webview error: ${event.nativeEvent.description}`)
        }
        // injectedJavaScript={javascript}
        onMessage={onMessage}
        onLoadProgress={({ nativeEvent }) => setProgress(nativeEvent.progress)}
        onLoadEnd={() => setLoaded(true)}
        onLoadStart={() => setLoaded(true)}
      />
    </View>
  );
};

export default ProgressBarWebview;
