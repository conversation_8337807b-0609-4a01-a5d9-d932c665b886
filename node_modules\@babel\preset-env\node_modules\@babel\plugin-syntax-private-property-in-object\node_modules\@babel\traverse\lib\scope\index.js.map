{"version": 3, "names": ["_renamer", "require", "_index", "_binding", "_globals", "_t", "_cache", "NOT_LOCAL_BINDING", "callExpression", "cloneNode", "getBindingIdentifiers", "identifier", "isArrayExpression", "isBinary", "isClass", "isClassBody", "isClassDeclaration", "isExportAllDeclaration", "isExportDefaultDeclaration", "isExportNamedDeclaration", "isFunctionDeclaration", "isIdentifier", "isImportDeclaration", "isLiteral", "isMethod", "isModuleSpecifier", "is<PERSON>ull<PERSON>iteral", "isObjectExpression", "isProperty", "isPureish", "isRegExpLiteral", "is<PERSON><PERSON><PERSON>", "isTaggedTemplateExpression", "isTemplateLiteral", "isThisExpression", "isUnaryExpression", "isVariableDeclaration", "matchesPattern", "memberExpression", "numericLiteral", "toIdentifier", "unaryExpression", "variableDeclaration", "variableDeclarator", "isRecordExpression", "isTupleExpression", "isObjectProperty", "isTopicReference", "isMetaProperty", "isPrivateName", "isExportDeclaration", "gatherNodeParts", "node", "parts", "type", "source", "specifiers", "length", "e", "declaration", "local", "push", "value", "object", "property", "name", "callee", "properties", "argument", "key", "left", "id", "expression", "meta", "openingElement", "openingFragment", "namespace", "collectorVisitor", "ForStatement", "path", "declar", "get", "isVar", "scope", "parentScope", "getFunctionParent", "getProgramParent", "registerBinding", "Declaration", "isBlockScoped", "parent", "registerDeclaration", "ImportDeclaration", "getBlockParent", "ReferencedIdentifier", "state", "references", "ForXStatement", "isPattern", "constantViolations", "ExportDeclaration", "exit", "binding", "getBinding", "reference", "decl", "declarations", "Object", "keys", "LabeledStatement", "AssignmentExpression", "assignments", "UpdateExpression", "UnaryExpression", "operator", "BlockScoped", "bindings", "CatchClause", "Function", "params", "param", "isFunctionExpression", "has", "ClassExpression", "uid", "<PERSON><PERSON>", "constructor", "block", "labels", "inited", "globals", "uids", "data", "crawling", "cached", "scopeCache", "set", "Map", "_parent", "shouldSkip", "<PERSON><PERSON><PERSON>", "parentPath", "isScope", "parentBlock", "hub", "traverse", "opts", "generateDeclaredUidIdentifier", "generateUidIdentifier", "generateUid", "replace", "i", "_generateUid", "<PERSON><PERSON><PERSON><PERSON>", "hasBinding", "hasGlobal", "hasReference", "program", "generateUidBasedOnNode", "defaultName", "join", "slice", "generateUidIdentifierBasedOnNode", "isStatic", "constant", "maybeGenerateMemoised", "dont<PERSON><PERSON>", "checkBlockScopedCollisions", "kind", "duplicate", "buildError", "TypeError", "rename", "old<PERSON>ame", "newName", "renamer", "Renamer", "arguments", "_renameFromMap", "map", "dump", "sep", "repeat", "console", "log", "violations", "toArray", "arrayLikeIsIterable", "isGenericType", "helper<PERSON><PERSON>", "args", "unshift", "addHelper", "get<PERSON><PERSON><PERSON>", "registerLabel", "label", "isLabeledStatement", "declare", "isTypeDeclaration", "importKind", "specifier", "isTypeSpecifier", "isImportSpecifier", "buildUndefinedNode", "registerConstantViolation", "ids", "reassign", "bindingPath", "ReferenceError", "declarators", "getOuterBindingIdentifiers", "getOwnBinding", "Binding", "addGlobal", "hasUid", "isPure", "constantsOnly", "_node$decorators", "superClass", "decorators", "body", "method", "right", "elem", "elements", "prop", "_node$decorators2", "computed", "_node$decorators3", "static", "tag", "quasi", "expressions", "setData", "val", "getData", "removeData", "init", "crawl", "create", "programParent", "_exploded", "visit", "enter", "typeVisitors", "ref", "getPatternParent", "isBlockStatement", "isProgram", "isSwitchStatement", "isLoop", "isCatchClause", "isFunction", "ensureBlock", "unique", "blockHoist", "_blockHoist", "dataKey", "declar<PERSON><PERSON>", "unshiftContainer", "declarator", "len", "Error", "isFunctionParent", "isBlockParent", "getAllBindings", "getAllBindingsOfKind", "kinds", "bindingIdentifierEquals", "getBindingIdentifier", "previousPath", "_previousPath", "isArrowFunctionExpression", "_this$getBinding", "getOwnBindingIdentifier", "hasOwnBinding", "_opts", "_opts2", "_opts3", "noGlobals", "parentHasBinding", "noUids", "includes", "contextVariables", "_this$parent", "moveBindingTo", "info", "removeOwnBinding", "removeBinding", "_this$getBinding2", "exports", "default", "builtin"], "sources": ["../../src/scope/index.ts"], "sourcesContent": ["import Renamer from \"./lib/renamer\";\nimport type NodePath from \"../path\";\nimport traverse from \"../index\";\nimport type { TraverseOptions } from \"../index\";\nimport Binding from \"./binding\";\nimport type { BindingKind } from \"./binding\";\nimport globals from \"globals\";\nimport {\n  NOT_LOCAL_BINDING,\n  callExpression,\n  cloneNode,\n  getBindingIdentifiers,\n  identifier,\n  isArrayExpression,\n  isBinary,\n  isClass,\n  isClassBody,\n  isClassDeclaration,\n  isExportAllDeclaration,\n  isExportDefaultDeclaration,\n  isExportNamedDeclaration,\n  isFunctionDeclaration,\n  isIdentifier,\n  isImportDeclaration,\n  isLiteral,\n  isMethod,\n  isModuleSpecifier,\n  isNullLiteral,\n  isObjectExpression,\n  isProperty,\n  isPureish,\n  isRegExpLiteral,\n  isSuper,\n  isTaggedTemplateExpression,\n  isTemplateLiteral,\n  isThisExpression,\n  isUnaryExpression,\n  isVariableDeclaration,\n  matchesPattern,\n  memberExpression,\n  numericLiteral,\n  toIdentifier,\n  unaryExpression,\n  variableDeclaration,\n  variableDeclarator,\n  isRecordExpression,\n  isTupleExpression,\n  isObjectProperty,\n  isTopicReference,\n  isMetaProperty,\n  isPrivateName,\n  isExportDeclaration,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport { scope as scopeCache } from \"../cache\";\nimport type { Visitor } from \"../types\";\n\ntype NodePart = string | number | boolean;\n// Recursively gathers the identifying names of a node.\nfunction gatherNodeParts(node: t.Node, parts: NodePart[]) {\n  switch (node?.type) {\n    default:\n      if (isImportDeclaration(node) || isExportDeclaration(node)) {\n        if (\n          (isExportAllDeclaration(node) ||\n            isExportNamedDeclaration(node) ||\n            isImportDeclaration(node)) &&\n          node.source\n        ) {\n          gatherNodeParts(node.source, parts);\n        } else if (\n          (isExportNamedDeclaration(node) || isImportDeclaration(node)) &&\n          node.specifiers &&\n          node.specifiers.length\n        ) {\n          for (const e of node.specifiers) gatherNodeParts(e, parts);\n        } else if (\n          (isExportDefaultDeclaration(node) ||\n            isExportNamedDeclaration(node)) &&\n          node.declaration\n        ) {\n          gatherNodeParts(node.declaration, parts);\n        }\n      } else if (isModuleSpecifier(node)) {\n        // todo(flow->ts): should condition instead be:\n        //    ```\n        //    t.isExportSpecifier(node) ||\n        //    t.isImportDefaultSpecifier(node) ||\n        //    t.isImportNamespaceSpecifier(node) ||\n        //    t.isImportSpecifier(node)\n        //    ```\n        //    allowing only nodes with `.local`?\n        // @ts-expect-error todo(flow->ts)\n        gatherNodeParts(node.local, parts);\n      } else if (\n        isLiteral(node) &&\n        !isNullLiteral(node) &&\n        !isRegExpLiteral(node) &&\n        !isTemplateLiteral(node)\n      ) {\n        parts.push(node.value);\n      }\n      break;\n\n    case \"MemberExpression\":\n    case \"OptionalMemberExpression\":\n    case \"JSXMemberExpression\":\n      gatherNodeParts(node.object, parts);\n      gatherNodeParts(node.property, parts);\n      break;\n\n    case \"Identifier\":\n    case \"JSXIdentifier\":\n      parts.push(node.name);\n      break;\n\n    case \"CallExpression\":\n    case \"OptionalCallExpression\":\n    case \"NewExpression\":\n      gatherNodeParts(node.callee, parts);\n      break;\n\n    case \"ObjectExpression\":\n    case \"ObjectPattern\":\n      for (const e of node.properties) {\n        gatherNodeParts(e, parts);\n      }\n      break;\n\n    case \"SpreadElement\":\n    case \"RestElement\":\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"ObjectProperty\":\n    case \"ObjectMethod\":\n    case \"ClassProperty\":\n    case \"ClassMethod\":\n    case \"ClassPrivateProperty\":\n    case \"ClassPrivateMethod\":\n      gatherNodeParts(node.key, parts);\n      break;\n\n    case \"ThisExpression\":\n      parts.push(\"this\");\n      break;\n\n    case \"Super\":\n      parts.push(\"super\");\n      break;\n\n    case \"Import\":\n      parts.push(\"import\");\n      break;\n\n    case \"DoExpression\":\n      parts.push(\"do\");\n      break;\n\n    case \"YieldExpression\":\n      parts.push(\"yield\");\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"AwaitExpression\":\n      parts.push(\"await\");\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"AssignmentExpression\":\n      gatherNodeParts(node.left, parts);\n      break;\n\n    case \"VariableDeclarator\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"FunctionExpression\":\n    case \"FunctionDeclaration\":\n    case \"ClassExpression\":\n    case \"ClassDeclaration\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"PrivateName\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"ParenthesizedExpression\":\n      gatherNodeParts(node.expression, parts);\n      break;\n\n    case \"UnaryExpression\":\n    case \"UpdateExpression\":\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"MetaProperty\":\n      gatherNodeParts(node.meta, parts);\n      gatherNodeParts(node.property, parts);\n      break;\n\n    case \"JSXElement\":\n      gatherNodeParts(node.openingElement, parts);\n      break;\n\n    case \"JSXOpeningElement\":\n      gatherNodeParts(node.name, parts);\n      break;\n\n    case \"JSXFragment\":\n      gatherNodeParts(node.openingFragment, parts);\n      break;\n\n    case \"JSXOpeningFragment\":\n      parts.push(\"Fragment\");\n      break;\n\n    case \"JSXNamespacedName\":\n      gatherNodeParts(node.namespace, parts);\n      gatherNodeParts(node.name, parts);\n      break;\n  }\n}\n\n//\ninterface CollectVisitorState {\n  assignments: NodePath<t.AssignmentExpression>[];\n  references: NodePath<t.Identifier | t.JSXIdentifier>[];\n  constantViolations: NodePath[];\n}\n\nconst collectorVisitor: Visitor<CollectVisitorState> = {\n  ForStatement(path) {\n    const declar = path.get(\"init\");\n    // delegate block scope handling to the `BlockScoped` method\n    if (declar.isVar()) {\n      const { scope } = path;\n      const parentScope = scope.getFunctionParent() || scope.getProgramParent();\n      parentScope.registerBinding(\"var\", declar);\n    }\n  },\n\n  Declaration(path) {\n    // delegate block scope handling to the `BlockScoped` method\n    if (path.isBlockScoped()) return;\n\n    // delegate import handing to the `ImportDeclaration` method\n    if (path.isImportDeclaration()) return;\n\n    // this will be hit again once we traverse into it after this iteration\n    if (path.isExportDeclaration()) return;\n\n    // we've ran into a declaration!\n    const parent =\n      path.scope.getFunctionParent() || path.scope.getProgramParent();\n    parent.registerDeclaration(path);\n  },\n\n  ImportDeclaration(path) {\n    // import may only appear in the top level or inside a module/namespace (for TS/flow)\n    const parent = path.scope.getBlockParent();\n\n    parent.registerDeclaration(path);\n  },\n\n  ReferencedIdentifier(path, state) {\n    state.references.push(path);\n  },\n\n  ForXStatement(path, state) {\n    const left = path.get(\"left\");\n    if (left.isPattern() || left.isIdentifier()) {\n      state.constantViolations.push(path);\n    }\n    // delegate block scope handling to the `BlockScoped` method\n    else if (left.isVar()) {\n      const { scope } = path;\n      const parentScope = scope.getFunctionParent() || scope.getProgramParent();\n      parentScope.registerBinding(\"var\", left);\n    }\n  },\n\n  ExportDeclaration: {\n    exit(path) {\n      const { node, scope } = path;\n      // ExportAllDeclaration does not have `declaration`\n      if (isExportAllDeclaration(node)) return;\n      const declar = node.declaration;\n      if (isClassDeclaration(declar) || isFunctionDeclaration(declar)) {\n        const id = declar.id;\n        if (!id) return;\n\n        const binding = scope.getBinding(id.name);\n        binding?.reference(path);\n      } else if (isVariableDeclaration(declar)) {\n        for (const decl of declar.declarations) {\n          for (const name of Object.keys(getBindingIdentifiers(decl))) {\n            const binding = scope.getBinding(name);\n            binding?.reference(path);\n          }\n        }\n      }\n    },\n  },\n\n  LabeledStatement(path) {\n    path.scope.getBlockParent().registerDeclaration(path);\n  },\n\n  AssignmentExpression(path, state) {\n    state.assignments.push(path);\n  },\n\n  UpdateExpression(path, state) {\n    state.constantViolations.push(path);\n  },\n\n  UnaryExpression(path, state) {\n    if (path.node.operator === \"delete\") {\n      state.constantViolations.push(path);\n    }\n  },\n\n  BlockScoped(path) {\n    let scope = path.scope;\n    if (scope.path === path) scope = scope.parent;\n\n    const parent = scope.getBlockParent();\n    parent.registerDeclaration(path);\n\n    // Register class identifier in class' scope if this is a class declaration.\n    if (path.isClassDeclaration() && path.node.id) {\n      const id = path.node.id;\n      const name = id.name;\n\n      path.scope.bindings[name] = path.scope.parent.getBinding(name);\n    }\n  },\n\n  CatchClause(path) {\n    path.scope.registerBinding(\"let\", path);\n  },\n\n  Function(path) {\n    const params: Array<NodePath> = path.get(\"params\");\n    for (const param of params) {\n      path.scope.registerBinding(\"param\", param);\n    }\n\n    // Register function expression id after params. When the id\n    // collides with a function param, the id effectively can't be\n    // referenced: here we registered it as a constantViolation\n    if (\n      path.isFunctionExpression() &&\n      path.has(\"id\") &&\n      // @ts-expect-error Fixme: document symbol ast properties\n      !path.get(\"id\").node[NOT_LOCAL_BINDING]\n    ) {\n      path.scope.registerBinding(\"local\", path.get(\"id\"), path);\n    }\n  },\n\n  ClassExpression(path) {\n    if (\n      path.has(\"id\") &&\n      // @ts-expect-error Fixme: document symbol ast properties\n      !path.get(\"id\").node[NOT_LOCAL_BINDING]\n    ) {\n      path.scope.registerBinding(\"local\", path);\n    }\n  },\n};\n\nlet uid = 0;\n\nexport type { Binding };\n\nexport default class Scope {\n  uid;\n\n  path: NodePath;\n  block: t.Pattern | t.Scopable;\n\n  labels;\n  inited;\n\n  bindings: { [name: string]: Binding };\n  references: { [name: string]: true };\n  globals: { [name: string]: t.Identifier | t.JSXIdentifier };\n  uids: { [name: string]: boolean };\n  data: { [key: string | symbol]: unknown };\n  crawling: boolean;\n\n  /**\n   * This searches the current \"scope\" and collects all references/bindings\n   * within.\n   */\n  constructor(path: NodePath<t.Pattern | t.Scopable>) {\n    const { node } = path;\n    const cached = scopeCache.get(node);\n    // Sometimes, a scopable path is placed higher in the AST tree.\n    // In these cases, have to create a new Scope.\n    if (cached?.path === path) {\n      return cached;\n    }\n    scopeCache.set(node, this);\n\n    this.uid = uid++;\n\n    this.block = node;\n    this.path = path;\n\n    this.labels = new Map();\n    this.inited = false;\n  }\n\n  /**\n   * Globals.\n   */\n\n  static globals = Object.keys(globals.builtin);\n\n  /**\n   * Variables available in current context.\n   */\n\n  static contextVariables = [\"arguments\", \"undefined\", \"Infinity\", \"NaN\"];\n\n  get parent() {\n    let parent,\n      path = this.path;\n    do {\n      // Skip method scope if coming from inside computed key or decorator expression\n      const shouldSkip = path.key === \"key\" || path.listKey === \"decorators\";\n      path = path.parentPath;\n      if (shouldSkip && path.isMethod()) path = path.parentPath;\n      if (path && path.isScope()) parent = path;\n    } while (path && !parent);\n\n    return parent?.scope;\n  }\n\n  get parentBlock() {\n    return this.path.parent;\n  }\n\n  get hub() {\n    return this.path.hub;\n  }\n\n  traverse<S>(\n    node: t.Node | t.Node[],\n    opts: TraverseOptions<S>,\n    state: S,\n  ): void;\n  traverse(node: t.Node | t.Node[], opts?: TraverseOptions, state?: any): void;\n  /**\n   * Traverse node with current scope and path.\n   *\n   * !!! WARNING !!!\n   * This method assumes that `this.path` is the NodePath representing `node`.\n   * After running the traversal, the `.parentPath` of the NodePaths\n   * corresponding to `node`'s children will be set to `this.path`.\n   *\n   * There is no good reason to use this method, since the only safe way to use\n   * it is equivalent to `scope.path.traverse(opts, state)`.\n   */\n  traverse<S>(node: any, opts: any, state?: S) {\n    traverse(node, opts, this, state, this.path);\n  }\n\n  /**\n   * Generate a unique identifier and add it to the current scope.\n   */\n\n  generateDeclaredUidIdentifier(name?: string) {\n    const id = this.generateUidIdentifier(name);\n    this.push({ id });\n    return cloneNode(id);\n  }\n\n  /**\n   * Generate a unique identifier.\n   */\n\n  generateUidIdentifier(name?: string) {\n    return identifier(this.generateUid(name));\n  }\n\n  /**\n   * Generate a unique `_id1` binding.\n   */\n\n  generateUid(name: string = \"temp\"): string {\n    name = toIdentifier(name)\n      .replace(/^_+/, \"\")\n      .replace(/[0-9]+$/g, \"\");\n\n    let uid;\n    let i = 1;\n    do {\n      uid = this._generateUid(name, i);\n      i++;\n    } while (\n      this.hasLabel(uid) ||\n      this.hasBinding(uid) ||\n      this.hasGlobal(uid) ||\n      this.hasReference(uid)\n    );\n\n    const program = this.getProgramParent();\n    program.references[uid] = true;\n    program.uids[uid] = true;\n\n    return uid;\n  }\n\n  /**\n   * Generate an `_id1`.\n   */\n\n  _generateUid(name: string, i: number) {\n    let id = name;\n    if (i > 1) id += i;\n    return `_${id}`;\n  }\n\n  generateUidBasedOnNode(node: t.Node, defaultName?: string) {\n    const parts: NodePart[] = [];\n    gatherNodeParts(node, parts);\n\n    let id = parts.join(\"$\");\n    id = id.replace(/^_/, \"\") || defaultName || \"ref\";\n\n    return this.generateUid(id.slice(0, 20));\n  }\n\n  /**\n   * Generate a unique identifier based on a node.\n   */\n\n  generateUidIdentifierBasedOnNode(node: t.Node, defaultName?: string) {\n    return identifier(this.generateUidBasedOnNode(node, defaultName));\n  }\n\n  /**\n   * Determine whether evaluating the specific input `node` is a consequenceless reference. ie.\n   * evaluating it wont result in potentially arbitrary code from being ran. The following are\n   * allowed and determined not to cause side effects:\n   *\n   *  - `this` expressions\n   *  - `super` expressions\n   *  - Bound identifiers\n   */\n\n  isStatic(node: t.Node): boolean {\n    if (isThisExpression(node) || isSuper(node) || isTopicReference(node)) {\n      return true;\n    }\n\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (binding) {\n        return binding.constant;\n      } else {\n        return this.hasBinding(node.name);\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * Possibly generate a memoised identifier if it is not static and has consequences.\n   */\n\n  maybeGenerateMemoised(node: t.Node, dontPush?: boolean) {\n    if (this.isStatic(node)) {\n      return null;\n    } else {\n      const id = this.generateUidIdentifierBasedOnNode(node);\n      if (!dontPush) {\n        this.push({ id });\n        return cloneNode(id);\n      }\n      return id;\n    }\n  }\n\n  checkBlockScopedCollisions(\n    local: Binding,\n    kind: BindingKind,\n    name: string,\n    id: any,\n  ) {\n    // ignore parameters\n    if (kind === \"param\") return;\n\n    // Ignore existing binding if it's the name of the current function or\n    // class expression\n    if (local.kind === \"local\") return;\n\n    const duplicate =\n      // don't allow duplicate bindings to exist alongside\n      kind === \"let\" ||\n      local.kind === \"let\" ||\n      local.kind === \"const\" ||\n      local.kind === \"module\" ||\n      // don't allow a local of param with a kind of let\n      (local.kind === \"param\" && kind === \"const\");\n\n    if (duplicate) {\n      throw this.hub.buildError(\n        id,\n        `Duplicate declaration \"${name}\"`,\n        TypeError,\n      );\n    }\n  }\n\n  rename(\n    oldName: string,\n    newName?: string,\n    // prettier-ignore\n    /* Babel 7 - block?: t.Pattern | t.Scopable */\n  ) {\n    const binding = this.getBinding(oldName);\n    if (binding) {\n      newName ||= this.generateUidIdentifier(oldName).name;\n      const renamer = new Renamer(binding, oldName, newName);\n      if (process.env.BABEL_8_BREAKING) {\n        renamer.rename();\n      } else {\n        // @ts-ignore(Babel 7 vs Babel 8) TODO: Delete this\n        renamer.rename(arguments[2]);\n      }\n    }\n  }\n\n  /** @deprecated Not used in our codebase */\n  _renameFromMap(\n    map: Record<string | symbol, unknown>,\n    oldName: string | symbol,\n    newName: string | symbol,\n    value: unknown,\n  ) {\n    if (map[oldName]) {\n      map[newName] = value;\n      map[oldName] = null;\n    }\n  }\n\n  dump() {\n    const sep = \"-\".repeat(60);\n    console.log(sep);\n    let scope: Scope = this;\n    do {\n      console.log(\"#\", scope.block.type);\n      for (const name of Object.keys(scope.bindings)) {\n        const binding = scope.bindings[name];\n        console.log(\" -\", name, {\n          constant: binding.constant,\n          references: binding.references,\n          violations: binding.constantViolations.length,\n          kind: binding.kind,\n        });\n      }\n    } while ((scope = scope.parent));\n    console.log(sep);\n  }\n\n  // TODO: (Babel 8) Split i in two parameters, and use an object of flags\n  toArray(\n    node: t.Node,\n    i?: number | boolean,\n    arrayLikeIsIterable?: boolean | void,\n  ) {\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (binding?.constant && binding.path.isGenericType(\"Array\")) {\n        return node;\n      }\n    }\n\n    if (isArrayExpression(node)) {\n      return node;\n    }\n\n    if (isIdentifier(node, { name: \"arguments\" })) {\n      return callExpression(\n        memberExpression(\n          memberExpression(\n            memberExpression(identifier(\"Array\"), identifier(\"prototype\")),\n            identifier(\"slice\"),\n          ),\n          identifier(\"call\"),\n        ),\n        [node],\n      );\n    }\n\n    let helperName;\n    const args = [node];\n    if (i === true) {\n      // Used in array-spread to create an array.\n      helperName = \"toConsumableArray\";\n    } else if (typeof i === \"number\") {\n      args.push(numericLiteral(i));\n\n      // Used in array-rest to create an array from a subset of an iterable.\n      helperName = \"slicedToArray\";\n      // TODO if (this.hub.isLoose(\"es6.forOf\")) helperName += \"-loose\";\n    } else {\n      // Used in array-rest to create an array\n      helperName = \"toArray\";\n    }\n\n    if (arrayLikeIsIterable) {\n      args.unshift(this.hub.addHelper(helperName));\n      helperName = \"maybeArrayLike\";\n    }\n\n    // @ts-expect-error todo(flow->ts): t.Node is not valid to use in args, function argument typeneeds to be clarified\n    return callExpression(this.hub.addHelper(helperName), args);\n  }\n\n  hasLabel(name: string) {\n    return !!this.getLabel(name);\n  }\n\n  getLabel(name: string) {\n    return this.labels.get(name);\n  }\n\n  registerLabel(path: NodePath<t.LabeledStatement>) {\n    this.labels.set(path.node.label.name, path);\n  }\n\n  registerDeclaration(path: NodePath) {\n    if (path.isLabeledStatement()) {\n      this.registerLabel(path);\n    } else if (path.isFunctionDeclaration()) {\n      this.registerBinding(\"hoisted\", path.get(\"id\"), path);\n    } else if (path.isVariableDeclaration()) {\n      const declarations = path.get(\"declarations\");\n      const { kind } = path.node;\n      for (const declar of declarations) {\n        this.registerBinding(kind === \"using\" ? \"const\" : kind, declar);\n      }\n    } else if (path.isClassDeclaration()) {\n      if (path.node.declare) return;\n      this.registerBinding(\"let\", path);\n    } else if (path.isImportDeclaration()) {\n      const isTypeDeclaration =\n        path.node.importKind === \"type\" || path.node.importKind === \"typeof\";\n      const specifiers = path.get(\"specifiers\");\n      for (const specifier of specifiers) {\n        const isTypeSpecifier =\n          isTypeDeclaration ||\n          (specifier.isImportSpecifier() &&\n            (specifier.node.importKind === \"type\" ||\n              specifier.node.importKind === \"typeof\"));\n\n        this.registerBinding(isTypeSpecifier ? \"unknown\" : \"module\", specifier);\n      }\n    } else if (path.isExportDeclaration()) {\n      // todo: improve babel-types\n      const declar = path.get(\"declaration\") as NodePath;\n      if (\n        declar.isClassDeclaration() ||\n        declar.isFunctionDeclaration() ||\n        declar.isVariableDeclaration()\n      ) {\n        this.registerDeclaration(declar);\n      }\n    } else {\n      this.registerBinding(\"unknown\", path);\n    }\n  }\n\n  buildUndefinedNode() {\n    return unaryExpression(\"void\", numericLiteral(0), true);\n  }\n\n  registerConstantViolation(path: NodePath) {\n    const ids = path.getBindingIdentifiers();\n    for (const name of Object.keys(ids)) {\n      const binding = this.getBinding(name);\n      if (binding) binding.reassign(path);\n    }\n  }\n\n  registerBinding(\n    kind: Binding[\"kind\"],\n    path: NodePath,\n    bindingPath: NodePath = path,\n  ) {\n    if (!kind) throw new ReferenceError(\"no `kind`\");\n\n    if (path.isVariableDeclaration()) {\n      const declarators: Array<NodePath> = path.get(\"declarations\");\n      for (const declar of declarators) {\n        this.registerBinding(kind, declar);\n      }\n      return;\n    }\n\n    const parent = this.getProgramParent();\n    const ids = path.getOuterBindingIdentifiers(true);\n\n    for (const name of Object.keys(ids)) {\n      parent.references[name] = true;\n\n      for (const id of ids[name]) {\n        const local = this.getOwnBinding(name);\n\n        if (local) {\n          // same identifier so continue safely as we're likely trying to register it\n          // multiple times\n          if (local.identifier === id) continue;\n\n          this.checkBlockScopedCollisions(local, kind, name, id);\n        }\n\n        // A redeclaration of an existing variable is a modification\n        if (local) {\n          this.registerConstantViolation(bindingPath);\n        } else {\n          this.bindings[name] = new Binding({\n            identifier: id,\n            scope: this,\n            path: bindingPath,\n            kind: kind,\n          });\n        }\n      }\n    }\n  }\n\n  addGlobal(node: t.Identifier | t.JSXIdentifier) {\n    this.globals[node.name] = node;\n  }\n\n  hasUid(name: string): boolean {\n    let scope: Scope = this;\n\n    do {\n      if (scope.uids[name]) return true;\n    } while ((scope = scope.parent));\n\n    return false;\n  }\n\n  hasGlobal(name: string): boolean {\n    let scope: Scope = this;\n\n    do {\n      if (scope.globals[name]) return true;\n    } while ((scope = scope.parent));\n\n    return false;\n  }\n\n  hasReference(name: string): boolean {\n    return !!this.getProgramParent().references[name];\n  }\n\n  isPure(node: t.Node, constantsOnly?: boolean): boolean {\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (!binding) return false;\n      if (constantsOnly) return binding.constant;\n      return true;\n    } else if (\n      isThisExpression(node) ||\n      isMetaProperty(node) ||\n      isTopicReference(node) ||\n      isPrivateName(node)\n    ) {\n      return true;\n    } else if (isClass(node)) {\n      if (node.superClass && !this.isPure(node.superClass, constantsOnly)) {\n        return false;\n      }\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      return this.isPure(node.body, constantsOnly);\n    } else if (isClassBody(node)) {\n      for (const method of node.body) {\n        if (!this.isPure(method, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isBinary(node)) {\n      return (\n        this.isPure(node.left, constantsOnly) &&\n        this.isPure(node.right, constantsOnly)\n      );\n    } else if (isArrayExpression(node) || isTupleExpression(node)) {\n      for (const elem of node.elements) {\n        if (elem !== null && !this.isPure(elem, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isObjectExpression(node) || isRecordExpression(node)) {\n      for (const prop of node.properties) {\n        if (!this.isPure(prop, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isMethod(node)) {\n      if (node.computed && !this.isPure(node.key, constantsOnly)) return false;\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      return true;\n    } else if (isProperty(node)) {\n      // @ts-expect-error todo(flow->ts): computed in not present on private properties\n      if (node.computed && !this.isPure(node.key, constantsOnly)) return false;\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      if (isObjectProperty(node) || node.static) {\n        if (node.value !== null && !this.isPure(node.value, constantsOnly)) {\n          return false;\n        }\n      }\n      return true;\n    } else if (isUnaryExpression(node)) {\n      return this.isPure(node.argument, constantsOnly);\n    } else if (isTaggedTemplateExpression(node)) {\n      return (\n        matchesPattern(node.tag, \"String.raw\") &&\n        !this.hasBinding(\"String\", true) &&\n        this.isPure(node.quasi, constantsOnly)\n      );\n    } else if (isTemplateLiteral(node)) {\n      for (const expression of node.expressions) {\n        if (!this.isPure(expression, constantsOnly)) return false;\n      }\n      return true;\n    } else {\n      return isPureish(node);\n    }\n  }\n\n  /**\n   * Set some arbitrary data on the current scope.\n   */\n\n  setData(key: string | symbol, val: any) {\n    return (this.data[key] = val);\n  }\n\n  /**\n   * Recursively walk up scope tree looking for the data `key`.\n   */\n\n  getData(key: string | symbol): any {\n    let scope: Scope = this;\n    do {\n      const data = scope.data[key];\n      if (data != null) return data;\n    } while ((scope = scope.parent));\n  }\n\n  /**\n   * Recursively walk up scope tree looking for the data `key` and if it exists,\n   * remove it.\n   */\n\n  removeData(key: string) {\n    let scope: Scope = this;\n    do {\n      const data = scope.data[key];\n      if (data != null) scope.data[key] = null;\n    } while ((scope = scope.parent));\n  }\n\n  init() {\n    if (!this.inited) {\n      this.inited = true;\n      this.crawl();\n    }\n  }\n\n  crawl() {\n    const path = this.path;\n\n    this.references = Object.create(null);\n    this.bindings = Object.create(null);\n    this.globals = Object.create(null);\n    this.uids = Object.create(null);\n    this.data = Object.create(null);\n\n    const programParent = this.getProgramParent();\n    if (programParent.crawling) return;\n\n    const state: CollectVisitorState = {\n      references: [],\n      constantViolations: [],\n      assignments: [],\n    };\n\n    this.crawling = true;\n    // traverse does not visit the root node, here we explicitly collect\n    // root node binding info when the root is not a Program.\n    if (path.type !== \"Program\" && collectorVisitor._exploded) {\n      // @ts-expect-error when collectorVisitor is exploded, `enter` always exists\n      for (const visit of collectorVisitor.enter) {\n        visit(path, state);\n      }\n      const typeVisitors = collectorVisitor[path.type];\n      if (typeVisitors) {\n        // @ts-expect-error when collectorVisitor is exploded, `enter` always exists\n        for (const visit of typeVisitors.enter) {\n          visit(path, state);\n        }\n      }\n    }\n    path.traverse(collectorVisitor, state);\n    this.crawling = false;\n\n    // register assignments\n    for (const path of state.assignments) {\n      // register undeclared bindings as globals\n      const ids = path.getBindingIdentifiers();\n      for (const name of Object.keys(ids)) {\n        if (path.scope.getBinding(name)) continue;\n        programParent.addGlobal(ids[name]);\n      }\n\n      // register as constant violation\n      path.scope.registerConstantViolation(path);\n    }\n\n    // register references\n    for (const ref of state.references) {\n      const binding = ref.scope.getBinding(ref.node.name);\n      if (binding) {\n        binding.reference(ref);\n      } else {\n        programParent.addGlobal(ref.node);\n      }\n    }\n\n    // register constant violations\n    for (const path of state.constantViolations) {\n      path.scope.registerConstantViolation(path);\n    }\n  }\n\n  push(opts: {\n    id: t.LVal;\n    init?: t.Expression;\n    unique?: boolean;\n    _blockHoist?: number | undefined;\n    kind?: \"var\" | \"let\" | \"const\";\n  }) {\n    let path = this.path;\n\n    if (path.isPattern()) {\n      path = this.getPatternParent().path;\n    } else if (!path.isBlockStatement() && !path.isProgram()) {\n      path = this.getBlockParent().path;\n    }\n\n    if (path.isSwitchStatement()) {\n      path = (this.getFunctionParent() || this.getProgramParent()).path;\n    }\n\n    if (path.isLoop() || path.isCatchClause() || path.isFunction()) {\n      // @ts-expect-error TS can not infer NodePath<Loop> | NodePath<CatchClause> as NodePath<Loop | CatchClause>\n      path.ensureBlock();\n      // @ts-expect-error todo(flow->ts): improve types\n      path = path.get(\"body\");\n    }\n\n    const unique = opts.unique;\n    const kind = opts.kind || \"var\";\n    const blockHoist = opts._blockHoist == null ? 2 : opts._blockHoist;\n\n    const dataKey = `declaration:${kind}:${blockHoist}`;\n    let declarPath = !unique && path.getData(dataKey);\n\n    if (!declarPath) {\n      const declar = variableDeclaration(kind, []);\n      // @ts-expect-error todo(flow->ts): avoid modifying nodes\n      declar._blockHoist = blockHoist;\n\n      [declarPath] = (path as NodePath<t.BlockStatement>).unshiftContainer(\n        \"body\",\n        [declar],\n      );\n      if (!unique) path.setData(dataKey, declarPath);\n    }\n\n    const declarator = variableDeclarator(opts.id, opts.init);\n    const len = declarPath.node.declarations.push(declarator);\n    path.scope.registerBinding(kind, declarPath.get(\"declarations\")[len - 1]);\n  }\n\n  /**\n   * Walk up to the top of the scope tree and get the `Program`.\n   */\n\n  getProgramParent() {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isProgram()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    throw new Error(\"Couldn't find a Program\");\n  }\n\n  /**\n   * Walk up the scope tree until we hit either a Function or return null.\n   */\n\n  getFunctionParent(): Scope | null {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isFunctionParent()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    return null;\n  }\n\n  /**\n   * Walk up the scope tree until we hit either a BlockStatement/Loop/Program/Function/Switch or reach the\n   * very top and hit Program.\n   */\n\n  getBlockParent() {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isBlockParent()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    throw new Error(\n      \"We couldn't find a BlockStatement, For, Switch, Function, Loop or Program...\",\n    );\n  }\n\n  /**\n   * Walk up from a pattern scope (function param initializer) until we hit a non-pattern scope,\n   * then returns its block parent\n   * @returns An ancestry scope whose path is a block parent\n   */\n  getPatternParent() {\n    let scope: Scope = this;\n    do {\n      if (!scope.path.isPattern()) {\n        return scope.getBlockParent();\n      }\n    } while ((scope = scope.parent.parent));\n    throw new Error(\n      \"We couldn't find a BlockStatement, For, Switch, Function, Loop or Program...\",\n    );\n  }\n\n  /**\n   * Walks the scope tree and gathers **all** bindings.\n   */\n\n  getAllBindings(): Record<string, Binding> {\n    const ids = Object.create(null);\n\n    let scope: Scope = this;\n    do {\n      for (const key of Object.keys(scope.bindings)) {\n        if (key in ids === false) {\n          ids[key] = scope.bindings[key];\n        }\n      }\n      scope = scope.parent;\n    } while (scope);\n\n    return ids;\n  }\n\n  /**\n   * Walks the scope tree and gathers all declarations of `kind`.\n   */\n\n  getAllBindingsOfKind(...kinds: string[]): Record<string, Binding> {\n    const ids = Object.create(null);\n\n    for (const kind of kinds) {\n      let scope: Scope = this;\n      do {\n        for (const name of Object.keys(scope.bindings)) {\n          const binding = scope.bindings[name];\n          if (binding.kind === kind) ids[name] = binding;\n        }\n        scope = scope.parent;\n      } while (scope);\n    }\n\n    return ids;\n  }\n\n  bindingIdentifierEquals(name: string, node: t.Node): boolean {\n    return this.getBindingIdentifier(name) === node;\n  }\n\n  getBinding(name: string): Binding | undefined {\n    let scope: Scope = this;\n    let previousPath;\n\n    do {\n      const binding = scope.getOwnBinding(name);\n      if (binding) {\n        // Check if a pattern is a part of parameter expressions.\n        // Note: for performance reason we skip checking previousPath.parentPath.isFunction()\n        // because `scope.path` is validated as scope in packages/babel-types/src/validators/isScope.js\n        // That is, if a scope path is pattern, its parent must be Function/CatchClause\n\n        // Spec *********: The closure created by this expression should not have visibility of\n        // declarations in the function body. If the binding is not a `param`-kind (as function parameters)\n        // or `local`-kind (as id in function expression),\n        // then it must be defined inside the function body, thus it should be skipped\n        if (\n          previousPath?.isPattern() &&\n          binding.kind !== \"param\" &&\n          binding.kind !== \"local\"\n        ) {\n          // do nothing\n        } else {\n          return binding;\n        }\n      } else if (\n        !binding &&\n        name === \"arguments\" &&\n        scope.path.isFunction() &&\n        !scope.path.isArrowFunctionExpression()\n      ) {\n        break;\n      }\n      previousPath = scope.path;\n    } while ((scope = scope.parent));\n  }\n\n  getOwnBinding(name: string): Binding | undefined {\n    return this.bindings[name];\n  }\n\n  // todo: return probably can be undefined…\n  getBindingIdentifier(name: string): t.Identifier {\n    return this.getBinding(name)?.identifier;\n  }\n\n  // todo: flow->ts return probably can be undefined\n  getOwnBindingIdentifier(name: string): t.Identifier {\n    const binding = this.bindings[name];\n    return binding?.identifier;\n  }\n\n  hasOwnBinding(name: string) {\n    return !!this.getOwnBinding(name);\n  }\n\n  // By default, we consider generated UIDs as bindings.\n  // This is because they are almost always used to declare variables,\n  // and since the scope isn't always up-to-date it's better to assume that\n  // there is a variable with that name. The `noUids` option can be used to\n  // turn off this behavior, for example if you know that the generate UID\n  // was used to declare a variable in a different scope.\n  hasBinding(\n    name: string,\n    opts?: boolean | { noGlobals?: boolean; noUids?: boolean },\n  ) {\n    if (!name) return false;\n    if (this.hasOwnBinding(name)) return true;\n    {\n      // TODO: Only accept the object form.\n      if (typeof opts === \"boolean\") opts = { noGlobals: opts };\n    }\n    if (this.parentHasBinding(name, opts)) return true;\n    if (!opts?.noUids && this.hasUid(name)) return true;\n    if (!opts?.noGlobals && Scope.globals.includes(name)) return true;\n    if (!opts?.noGlobals && Scope.contextVariables.includes(name)) return true;\n    return false;\n  }\n\n  parentHasBinding(\n    name: string,\n    opts?: { noGlobals?: boolean; noUids?: boolean },\n  ) {\n    return this.parent?.hasBinding(name, opts);\n  }\n\n  /**\n   * Move a binding of `name` to another `scope`.\n   */\n\n  moveBindingTo(name: string, scope: Scope) {\n    const info = this.getBinding(name);\n    if (info) {\n      info.scope.removeOwnBinding(name);\n      info.scope = scope;\n      scope.bindings[name] = info;\n    }\n  }\n\n  removeOwnBinding(name: string) {\n    delete this.bindings[name];\n  }\n\n  removeBinding(name: string) {\n    // clear literal binding\n    this.getBinding(name)?.scope.removeOwnBinding(name);\n\n    // clear uids with this name - https://github.com/babel/babel/issues/2101\n    let scope: Scope = this;\n    do {\n      if (scope.uids[name]) {\n        scope.uids[name] = false;\n      }\n    } while ((scope = scope.parent));\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAEA,IAAAE,QAAA,GAAAF,OAAA;AAEA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,EAAA,GAAAJ,OAAA;AA+CA,IAAAK,MAAA,GAAAL,OAAA;AAA+C;EA9C7CM,iBAAiB;EACjBC,cAAc;EACdC,SAAS;EACTC,qBAAqB;EACrBC,UAAU;EACVC,iBAAiB;EACjBC,QAAQ;EACRC,OAAO;EACPC,WAAW;EACXC,kBAAkB;EAClBC,sBAAsB;EACtBC,0BAA0B;EAC1BC,wBAAwB;EACxBC,qBAAqB;EACrBC,YAAY;EACZC,mBAAmB;EACnBC,SAAS;EACTC,QAAQ;EACRC,iBAAiB;EACjBC,aAAa;EACbC,kBAAkB;EAClBC,UAAU;EACVC,SAAS;EACTC,eAAe;EACfC,OAAO;EACPC,0BAA0B;EAC1BC,iBAAiB;EACjBC,gBAAgB;EAChBC,iBAAiB;EACjBC,qBAAqB;EACrBC,cAAc;EACdC,gBAAgB;EAChBC,cAAc;EACdC,YAAY;EACZC,eAAe;EACfC,mBAAmB;EACnBC,kBAAkB;EAClBC,kBAAkB;EAClBC,iBAAiB;EACjBC,gBAAgB;EAChBC,gBAAgB;EAChBC,cAAc;EACdC,aAAa;EACbC;AAAmB,IAAA7C,EAAA;AAQrB,SAAS8C,eAAeA,CAACC,IAAY,EAAEC,KAAiB,EAAE;EACxD,QAAQD,IAAI,oBAAJA,IAAI,CAAEE,IAAI;IAChB;MACE,IAAIhC,mBAAmB,CAAC8B,IAAI,CAAC,IAAIF,mBAAmB,CAACE,IAAI,CAAC,EAAE;QAC1D,IACE,CAACnC,sBAAsB,CAACmC,IAAI,CAAC,IAC3BjC,wBAAwB,CAACiC,IAAI,CAAC,IAC9B9B,mBAAmB,CAAC8B,IAAI,CAAC,KAC3BA,IAAI,CAACG,MAAM,EACX;UACAJ,eAAe,CAACC,IAAI,CAACG,MAAM,EAAEF,KAAK,CAAC;QACrC,CAAC,MAAM,IACL,CAAClC,wBAAwB,CAACiC,IAAI,CAAC,IAAI9B,mBAAmB,CAAC8B,IAAI,CAAC,KAC5DA,IAAI,CAACI,UAAU,IACfJ,IAAI,CAACI,UAAU,CAACC,MAAM,EACtB;UACA,KAAK,MAAMC,CAAC,IAAIN,IAAI,CAACI,UAAU,EAAEL,eAAe,CAACO,CAAC,EAAEL,KAAK,CAAC;QAC5D,CAAC,MAAM,IACL,CAACnC,0BAA0B,CAACkC,IAAI,CAAC,IAC/BjC,wBAAwB,CAACiC,IAAI,CAAC,KAChCA,IAAI,CAACO,WAAW,EAChB;UACAR,eAAe,CAACC,IAAI,CAACO,WAAW,EAAEN,KAAK,CAAC;QAC1C;MACF,CAAC,MAAM,IAAI5B,iBAAiB,CAAC2B,IAAI,CAAC,EAAE;QAUlCD,eAAe,CAACC,IAAI,CAACQ,KAAK,EAAEP,KAAK,CAAC;MACpC,CAAC,MAAM,IACL9B,SAAS,CAAC6B,IAAI,CAAC,IACf,CAAC1B,aAAa,CAAC0B,IAAI,CAAC,IACpB,CAACtB,eAAe,CAACsB,IAAI,CAAC,IACtB,CAACnB,iBAAiB,CAACmB,IAAI,CAAC,EACxB;QACAC,KAAK,CAACQ,IAAI,CAACT,IAAI,CAACU,KAAK,CAAC;MACxB;MACA;IAEF,KAAK,kBAAkB;IACvB,KAAK,0BAA0B;IAC/B,KAAK,qBAAqB;MACxBX,eAAe,CAACC,IAAI,CAACW,MAAM,EAAEV,KAAK,CAAC;MACnCF,eAAe,CAACC,IAAI,CAACY,QAAQ,EAAEX,KAAK,CAAC;MACrC;IAEF,KAAK,YAAY;IACjB,KAAK,eAAe;MAClBA,KAAK,CAACQ,IAAI,CAACT,IAAI,CAACa,IAAI,CAAC;MACrB;IAEF,KAAK,gBAAgB;IACrB,KAAK,wBAAwB;IAC7B,KAAK,eAAe;MAClBd,eAAe,CAACC,IAAI,CAACc,MAAM,EAAEb,KAAK,CAAC;MACnC;IAEF,KAAK,kBAAkB;IACvB,KAAK,eAAe;MAClB,KAAK,MAAMK,CAAC,IAAIN,IAAI,CAACe,UAAU,EAAE;QAC/BhB,eAAe,CAACO,CAAC,EAAEL,KAAK,CAAC;MAC3B;MACA;IAEF,KAAK,eAAe;IACpB,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACgB,QAAQ,EAAEf,KAAK,CAAC;MACrC;IAEF,KAAK,gBAAgB;IACrB,KAAK,cAAc;IACnB,KAAK,eAAe;IACpB,KAAK,aAAa;IAClB,KAAK,sBAAsB;IAC3B,KAAK,oBAAoB;MACvBF,eAAe,CAACC,IAAI,CAACiB,GAAG,EAAEhB,KAAK,CAAC;MAChC;IAEF,KAAK,gBAAgB;MACnBA,KAAK,CAACQ,IAAI,CAAC,MAAM,CAAC;MAClB;IAEF,KAAK,OAAO;MACVR,KAAK,CAACQ,IAAI,CAAC,OAAO,CAAC;MACnB;IAEF,KAAK,QAAQ;MACXR,KAAK,CAACQ,IAAI,CAAC,QAAQ,CAAC;MACpB;IAEF,KAAK,cAAc;MACjBR,KAAK,CAACQ,IAAI,CAAC,IAAI,CAAC;MAChB;IAEF,KAAK,iBAAiB;MACpBR,KAAK,CAACQ,IAAI,CAAC,OAAO,CAAC;MACnBV,eAAe,CAACC,IAAI,CAACgB,QAAQ,EAAEf,KAAK,CAAC;MACrC;IAEF,KAAK,iBAAiB;MACpBA,KAAK,CAACQ,IAAI,CAAC,OAAO,CAAC;MACnBV,eAAe,CAACC,IAAI,CAACgB,QAAQ,EAAEf,KAAK,CAAC;MACrC;IAEF,KAAK,sBAAsB;MACzBF,eAAe,CAACC,IAAI,CAACkB,IAAI,EAAEjB,KAAK,CAAC;MACjC;IAEF,KAAK,oBAAoB;MACvBF,eAAe,CAACC,IAAI,CAACmB,EAAE,EAAElB,KAAK,CAAC;MAC/B;IAEF,KAAK,oBAAoB;IACzB,KAAK,qBAAqB;IAC1B,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrBF,eAAe,CAACC,IAAI,CAACmB,EAAE,EAAElB,KAAK,CAAC;MAC/B;IAEF,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACmB,EAAE,EAAElB,KAAK,CAAC;MAC/B;IAEF,KAAK,yBAAyB;MAC5BF,eAAe,CAACC,IAAI,CAACoB,UAAU,EAAEnB,KAAK,CAAC;MACvC;IAEF,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrBF,eAAe,CAACC,IAAI,CAACgB,QAAQ,EAAEf,KAAK,CAAC;MACrC;IAEF,KAAK,cAAc;MACjBF,eAAe,CAACC,IAAI,CAACqB,IAAI,EAAEpB,KAAK,CAAC;MACjCF,eAAe,CAACC,IAAI,CAACY,QAAQ,EAAEX,KAAK,CAAC;MACrC;IAEF,KAAK,YAAY;MACfF,eAAe,CAACC,IAAI,CAACsB,cAAc,EAAErB,KAAK,CAAC;MAC3C;IAEF,KAAK,mBAAmB;MACtBF,eAAe,CAACC,IAAI,CAACa,IAAI,EAAEZ,KAAK,CAAC;MACjC;IAEF,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACuB,eAAe,EAAEtB,KAAK,CAAC;MAC5C;IAEF,KAAK,oBAAoB;MACvBA,KAAK,CAACQ,IAAI,CAAC,UAAU,CAAC;MACtB;IAEF,KAAK,mBAAmB;MACtBV,eAAe,CAACC,IAAI,CAACwB,SAAS,EAAEvB,KAAK,CAAC;MACtCF,eAAe,CAACC,IAAI,CAACa,IAAI,EAAEZ,KAAK,CAAC;MACjC;EAAM;AAEZ;AASA,MAAMwB,gBAA8C,GAAG;EACrDC,YAAYA,CAACC,IAAI,EAAE;IACjB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IAE/B,IAAID,MAAM,CAACE,KAAK,EAAE,EAAE;MAClB,MAAM;QAAEC;MAAM,CAAC,GAAGJ,IAAI;MACtB,MAAMK,WAAW,GAAGD,KAAK,CAACE,iBAAiB,EAAE,IAAIF,KAAK,CAACG,gBAAgB,EAAE;MACzEF,WAAW,CAACG,eAAe,CAAC,KAAK,EAAEP,MAAM,CAAC;IAC5C;EACF,CAAC;EAEDQ,WAAWA,CAACT,IAAI,EAAE;IAEhB,IAAIA,IAAI,CAACU,aAAa,EAAE,EAAE;IAG1B,IAAIV,IAAI,CAACzD,mBAAmB,EAAE,EAAE;IAGhC,IAAIyD,IAAI,CAAC7B,mBAAmB,EAAE,EAAE;IAGhC,MAAMwC,MAAM,GACVX,IAAI,CAACI,KAAK,CAACE,iBAAiB,EAAE,IAAIN,IAAI,CAACI,KAAK,CAACG,gBAAgB,EAAE;IACjEI,MAAM,CAACC,mBAAmB,CAACZ,IAAI,CAAC;EAClC,CAAC;EAEDa,iBAAiBA,CAACb,IAAI,EAAE;IAEtB,MAAMW,MAAM,GAAGX,IAAI,CAACI,KAAK,CAACU,cAAc,EAAE;IAE1CH,MAAM,CAACC,mBAAmB,CAACZ,IAAI,CAAC;EAClC,CAAC;EAEDe,oBAAoBA,CAACf,IAAI,EAAEgB,KAAK,EAAE;IAChCA,KAAK,CAACC,UAAU,CAACnC,IAAI,CAACkB,IAAI,CAAC;EAC7B,CAAC;EAEDkB,aAAaA,CAAClB,IAAI,EAAEgB,KAAK,EAAE;IACzB,MAAMzB,IAAI,GAAGS,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IAC7B,IAAIX,IAAI,CAAC4B,SAAS,EAAE,IAAI5B,IAAI,CAACjD,YAAY,EAAE,EAAE;MAC3C0E,KAAK,CAACI,kBAAkB,CAACtC,IAAI,CAACkB,IAAI,CAAC;IACrC,CAAC,MAEI,IAAIT,IAAI,CAACY,KAAK,EAAE,EAAE;MACrB,MAAM;QAAEC;MAAM,CAAC,GAAGJ,IAAI;MACtB,MAAMK,WAAW,GAAGD,KAAK,CAACE,iBAAiB,EAAE,IAAIF,KAAK,CAACG,gBAAgB,EAAE;MACzEF,WAAW,CAACG,eAAe,CAAC,KAAK,EAAEjB,IAAI,CAAC;IAC1C;EACF,CAAC;EAED8B,iBAAiB,EAAE;IACjBC,IAAIA,CAACtB,IAAI,EAAE;MACT,MAAM;QAAE3B,IAAI;QAAE+B;MAAM,CAAC,GAAGJ,IAAI;MAE5B,IAAI9D,sBAAsB,CAACmC,IAAI,CAAC,EAAE;MAClC,MAAM4B,MAAM,GAAG5B,IAAI,CAACO,WAAW;MAC/B,IAAI3C,kBAAkB,CAACgE,MAAM,CAAC,IAAI5D,qBAAqB,CAAC4D,MAAM,CAAC,EAAE;QAC/D,MAAMT,EAAE,GAAGS,MAAM,CAACT,EAAE;QACpB,IAAI,CAACA,EAAE,EAAE;QAET,MAAM+B,OAAO,GAAGnB,KAAK,CAACoB,UAAU,CAAChC,EAAE,CAACN,IAAI,CAAC;QACzCqC,OAAO,oBAAPA,OAAO,CAAEE,SAAS,CAACzB,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAI3C,qBAAqB,CAAC4C,MAAM,CAAC,EAAE;QACxC,KAAK,MAAMyB,IAAI,IAAIzB,MAAM,CAAC0B,YAAY,EAAE;UACtC,KAAK,MAAMzC,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAAClG,qBAAqB,CAAC+F,IAAI,CAAC,CAAC,EAAE;YAC3D,MAAMH,OAAO,GAAGnB,KAAK,CAACoB,UAAU,CAACtC,IAAI,CAAC;YACtCqC,OAAO,oBAAPA,OAAO,CAAEE,SAAS,CAACzB,IAAI,CAAC;UAC1B;QACF;MACF;IACF;EACF,CAAC;EAED8B,gBAAgBA,CAAC9B,IAAI,EAAE;IACrBA,IAAI,CAACI,KAAK,CAACU,cAAc,EAAE,CAACF,mBAAmB,CAACZ,IAAI,CAAC;EACvD,CAAC;EAED+B,oBAAoBA,CAAC/B,IAAI,EAAEgB,KAAK,EAAE;IAChCA,KAAK,CAACgB,WAAW,CAAClD,IAAI,CAACkB,IAAI,CAAC;EAC9B,CAAC;EAEDiC,gBAAgBA,CAACjC,IAAI,EAAEgB,KAAK,EAAE;IAC5BA,KAAK,CAACI,kBAAkB,CAACtC,IAAI,CAACkB,IAAI,CAAC;EACrC,CAAC;EAEDkC,eAAeA,CAAClC,IAAI,EAAEgB,KAAK,EAAE;IAC3B,IAAIhB,IAAI,CAAC3B,IAAI,CAAC8D,QAAQ,KAAK,QAAQ,EAAE;MACnCnB,KAAK,CAACI,kBAAkB,CAACtC,IAAI,CAACkB,IAAI,CAAC;IACrC;EACF,CAAC;EAEDoC,WAAWA,CAACpC,IAAI,EAAE;IAChB,IAAII,KAAK,GAAGJ,IAAI,CAACI,KAAK;IACtB,IAAIA,KAAK,CAACJ,IAAI,KAAKA,IAAI,EAAEI,KAAK,GAAGA,KAAK,CAACO,MAAM;IAE7C,MAAMA,MAAM,GAAGP,KAAK,CAACU,cAAc,EAAE;IACrCH,MAAM,CAACC,mBAAmB,CAACZ,IAAI,CAAC;IAGhC,IAAIA,IAAI,CAAC/D,kBAAkB,EAAE,IAAI+D,IAAI,CAAC3B,IAAI,CAACmB,EAAE,EAAE;MAC7C,MAAMA,EAAE,GAAGQ,IAAI,CAAC3B,IAAI,CAACmB,EAAE;MACvB,MAAMN,IAAI,GAAGM,EAAE,CAACN,IAAI;MAEpBc,IAAI,CAACI,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC,GAAGc,IAAI,CAACI,KAAK,CAACO,MAAM,CAACa,UAAU,CAACtC,IAAI,CAAC;IAChE;EACF,CAAC;EAEDoD,WAAWA,CAACtC,IAAI,EAAE;IAChBA,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,KAAK,EAAER,IAAI,CAAC;EACzC,CAAC;EAEDuC,QAAQA,CAACvC,IAAI,EAAE;IACb,MAAMwC,MAAuB,GAAGxC,IAAI,CAACE,GAAG,CAAC,QAAQ,CAAC;IAClD,KAAK,MAAMuC,KAAK,IAAID,MAAM,EAAE;MAC1BxC,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,OAAO,EAAEiC,KAAK,CAAC;IAC5C;IAKA,IACEzC,IAAI,CAAC0C,oBAAoB,EAAE,IAC3B1C,IAAI,CAAC2C,GAAG,CAAC,IAAI,CAAC,IAEd,CAAC3C,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,CAAC7B,IAAI,CAAC7C,iBAAiB,CAAC,EACvC;MACAwE,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,OAAO,EAAER,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IAC3D;EACF,CAAC;EAED4C,eAAeA,CAAC5C,IAAI,EAAE;IACpB,IACEA,IAAI,CAAC2C,GAAG,CAAC,IAAI,CAAC,IAEd,CAAC3C,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,CAAC7B,IAAI,CAAC7C,iBAAiB,CAAC,EACvC;MACAwE,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,OAAO,EAAER,IAAI,CAAC;IAC3C;EACF;AACF,CAAC;AAED,IAAI6C,GAAG,GAAG,CAAC;AAII,MAAMC,KAAK,CAAC;EAoBzBC,WAAWA,CAAC/C,IAAsC,EAAE;IAAA,KAnBpD6C,GAAG;IAAA,KAEH7C,IAAI;IAAA,KACJgD,KAAK;IAAA,KAELC,MAAM;IAAA,KACNC,MAAM;IAAA,KAENb,QAAQ;IAAA,KACRpB,UAAU;IAAA,KACVkC,OAAO;IAAA,KACPC,IAAI;IAAA,KACJC,IAAI;IAAA,KACJC,QAAQ;IAON,MAAM;MAAEjF;IAAK,CAAC,GAAG2B,IAAI;IACrB,MAAMuD,MAAM,GAAGC,YAAU,CAACtD,GAAG,CAAC7B,IAAI,CAAC;IAGnC,IAAI,CAAAkF,MAAM,oBAANA,MAAM,CAAEvD,IAAI,MAAKA,IAAI,EAAE;MACzB,OAAOuD,MAAM;IACf;IACAC,YAAU,CAACC,GAAG,CAACpF,IAAI,EAAE,IAAI,CAAC;IAE1B,IAAI,CAACwE,GAAG,GAAGA,GAAG,EAAE;IAEhB,IAAI,CAACG,KAAK,GAAG3E,IAAI;IACjB,IAAI,CAAC2B,IAAI,GAAGA,IAAI;IAEhB,IAAI,CAACiD,MAAM,GAAG,IAAIS,GAAG,EAAE;IACvB,IAAI,CAACR,MAAM,GAAG,KAAK;EACrB;EAcA,IAAIvC,MAAMA,CAAA,EAAG;IAAA,IAAAgD,OAAA;IACX,IAAIhD,MAAM;MACRX,IAAI,GAAG,IAAI,CAACA,IAAI;IAClB,GAAG;MAED,MAAM4D,UAAU,GAAG5D,IAAI,CAACV,GAAG,KAAK,KAAK,IAAIU,IAAI,CAAC6D,OAAO,KAAK,YAAY;MACtE7D,IAAI,GAAGA,IAAI,CAAC8D,UAAU;MACtB,IAAIF,UAAU,IAAI5D,IAAI,CAACvD,QAAQ,EAAE,EAAEuD,IAAI,GAAGA,IAAI,CAAC8D,UAAU;MACzD,IAAI9D,IAAI,IAAIA,IAAI,CAAC+D,OAAO,EAAE,EAAEpD,MAAM,GAAGX,IAAI;IAC3C,CAAC,QAAQA,IAAI,IAAI,CAACW,MAAM;IAExB,QAAAgD,OAAA,GAAOhD,MAAM,qBAANgD,OAAA,CAAQvD,KAAK;EACtB;EAEA,IAAI4D,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAChE,IAAI,CAACW,MAAM;EACzB;EAEA,IAAIsD,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACjE,IAAI,CAACiE,GAAG;EACtB;EAmBAC,QAAQA,CAAI7F,IAAS,EAAE8F,IAAS,EAAEnD,KAAS,EAAE;IAC3C,IAAAkD,cAAQ,EAAC7F,IAAI,EAAE8F,IAAI,EAAE,IAAI,EAAEnD,KAAK,EAAE,IAAI,CAAChB,IAAI,CAAC;EAC9C;EAMAoE,6BAA6BA,CAAClF,IAAa,EAAE;IAC3C,MAAMM,EAAE,GAAG,IAAI,CAAC6E,qBAAqB,CAACnF,IAAI,CAAC;IAC3C,IAAI,CAACJ,IAAI,CAAC;MAAEU;IAAG,CAAC,CAAC;IACjB,OAAO9D,SAAS,CAAC8D,EAAE,CAAC;EACtB;EAMA6E,qBAAqBA,CAACnF,IAAa,EAAE;IACnC,OAAOtD,UAAU,CAAC,IAAI,CAAC0I,WAAW,CAACpF,IAAI,CAAC,CAAC;EAC3C;EAMAoF,WAAWA,CAACpF,IAAY,GAAG,MAAM,EAAU;IACzCA,IAAI,GAAGzB,YAAY,CAACyB,IAAI,CAAC,CACtBqF,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IAE1B,IAAI1B,GAAG;IACP,IAAI2B,CAAC,GAAG,CAAC;IACT,GAAG;MACD3B,GAAG,GAAG,IAAI,CAAC4B,YAAY,CAACvF,IAAI,EAAEsF,CAAC,CAAC;MAChCA,CAAC,EAAE;IACL,CAAC,QACC,IAAI,CAACE,QAAQ,CAAC7B,GAAG,CAAC,IAClB,IAAI,CAAC8B,UAAU,CAAC9B,GAAG,CAAC,IACpB,IAAI,CAAC+B,SAAS,CAAC/B,GAAG,CAAC,IACnB,IAAI,CAACgC,YAAY,CAAChC,GAAG,CAAC;IAGxB,MAAMiC,OAAO,GAAG,IAAI,CAACvE,gBAAgB,EAAE;IACvCuE,OAAO,CAAC7D,UAAU,CAAC4B,GAAG,CAAC,GAAG,IAAI;IAC9BiC,OAAO,CAAC1B,IAAI,CAACP,GAAG,CAAC,GAAG,IAAI;IAExB,OAAOA,GAAG;EACZ;EAMA4B,YAAYA,CAACvF,IAAY,EAAEsF,CAAS,EAAE;IACpC,IAAIhF,EAAE,GAAGN,IAAI;IACb,IAAIsF,CAAC,GAAG,CAAC,EAAEhF,EAAE,IAAIgF,CAAC;IAClB,OAAQ,IAAGhF,EAAG,EAAC;EACjB;EAEAuF,sBAAsBA,CAAC1G,IAAY,EAAE2G,WAAoB,EAAE;IACzD,MAAM1G,KAAiB,GAAG,EAAE;IAC5BF,eAAe,CAACC,IAAI,EAAEC,KAAK,CAAC;IAE5B,IAAIkB,EAAE,GAAGlB,KAAK,CAAC2G,IAAI,CAAC,GAAG,CAAC;IACxBzF,EAAE,GAAGA,EAAE,CAAC+E,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,IAAIS,WAAW,IAAI,KAAK;IAEjD,OAAO,IAAI,CAACV,WAAW,CAAC9E,EAAE,CAAC0F,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1C;EAMAC,gCAAgCA,CAAC9G,IAAY,EAAE2G,WAAoB,EAAE;IACnE,OAAOpJ,UAAU,CAAC,IAAI,CAACmJ,sBAAsB,CAAC1G,IAAI,EAAE2G,WAAW,CAAC,CAAC;EACnE;EAYAI,QAAQA,CAAC/G,IAAY,EAAW;IAC9B,IAAIlB,gBAAgB,CAACkB,IAAI,CAAC,IAAIrB,OAAO,CAACqB,IAAI,CAAC,IAAIL,gBAAgB,CAACK,IAAI,CAAC,EAAE;MACrE,OAAO,IAAI;IACb;IAEA,IAAI/B,YAAY,CAAC+B,IAAI,CAAC,EAAE;MACtB,MAAMkD,OAAO,GAAG,IAAI,CAACC,UAAU,CAACnD,IAAI,CAACa,IAAI,CAAC;MAC1C,IAAIqC,OAAO,EAAE;QACX,OAAOA,OAAO,CAAC8D,QAAQ;MACzB,CAAC,MAAM;QACL,OAAO,IAAI,CAACV,UAAU,CAACtG,IAAI,CAACa,IAAI,CAAC;MACnC;IACF;IAEA,OAAO,KAAK;EACd;EAMAoG,qBAAqBA,CAACjH,IAAY,EAAEkH,QAAkB,EAAE;IACtD,IAAI,IAAI,CAACH,QAAQ,CAAC/G,IAAI,CAAC,EAAE;MACvB,OAAO,IAAI;IACb,CAAC,MAAM;MACL,MAAMmB,EAAE,GAAG,IAAI,CAAC2F,gCAAgC,CAAC9G,IAAI,CAAC;MACtD,IAAI,CAACkH,QAAQ,EAAE;QACb,IAAI,CAACzG,IAAI,CAAC;UAAEU;QAAG,CAAC,CAAC;QACjB,OAAO9D,SAAS,CAAC8D,EAAE,CAAC;MACtB;MACA,OAAOA,EAAE;IACX;EACF;EAEAgG,0BAA0BA,CACxB3G,KAAc,EACd4G,IAAiB,EACjBvG,IAAY,EACZM,EAAO,EACP;IAEA,IAAIiG,IAAI,KAAK,OAAO,EAAE;IAItB,IAAI5G,KAAK,CAAC4G,IAAI,KAAK,OAAO,EAAE;IAE5B,MAAMC,SAAS,GAEbD,IAAI,KAAK,KAAK,IACd5G,KAAK,CAAC4G,IAAI,KAAK,KAAK,IACpB5G,KAAK,CAAC4G,IAAI,KAAK,OAAO,IACtB5G,KAAK,CAAC4G,IAAI,KAAK,QAAQ,IAEtB5G,KAAK,CAAC4G,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAQ;IAE9C,IAAIC,SAAS,EAAE;MACb,MAAM,IAAI,CAACzB,GAAG,CAAC0B,UAAU,CACvBnG,EAAE,EACD,0BAAyBN,IAAK,GAAE,EACjC0G,SAAS,CACV;IACH;EACF;EAEAC,MAAMA,CACJC,OAAe,EACfC,OAAgB,EAGhB;IACA,MAAMxE,OAAO,GAAG,IAAI,CAACC,UAAU,CAACsE,OAAO,CAAC;IACxC,IAAIvE,OAAO,EAAE;MACXwE,OAAO,KAAPA,OAAO,GAAK,IAAI,CAAC1B,qBAAqB,CAACyB,OAAO,CAAC,CAAC5G,IAAI;MACpD,MAAM8G,OAAO,GAAG,IAAIC,gBAAO,CAAC1E,OAAO,EAAEuE,OAAO,EAAEC,OAAO,CAAC;MAG/C;QAELC,OAAO,CAACH,MAAM,CAACK,SAAS,CAAC,CAAC,CAAC,CAAC;MAC9B;IACF;EACF;EAGAC,cAAcA,CACZC,GAAqC,EACrCN,OAAwB,EACxBC,OAAwB,EACxBhH,KAAc,EACd;IACA,IAAIqH,GAAG,CAACN,OAAO,CAAC,EAAE;MAChBM,GAAG,CAACL,OAAO,CAAC,GAAGhH,KAAK;MACpBqH,GAAG,CAACN,OAAO,CAAC,GAAG,IAAI;IACrB;EACF;EAEAO,IAAIA,CAAA,EAAG;IACL,MAAMC,GAAG,GAAG,GAAG,CAACC,MAAM,CAAC,EAAE,CAAC;IAC1BC,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;IAChB,IAAIlG,KAAY,GAAG,IAAI;IACvB,GAAG;MACDoG,OAAO,CAACC,GAAG,CAAC,GAAG,EAAErG,KAAK,CAAC4C,KAAK,CAACzE,IAAI,CAAC;MAClC,KAAK,MAAMW,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACzB,KAAK,CAACiC,QAAQ,CAAC,EAAE;QAC9C,MAAMd,OAAO,GAAGnB,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC;QACpCsH,OAAO,CAACC,GAAG,CAAC,IAAI,EAAEvH,IAAI,EAAE;UACtBmG,QAAQ,EAAE9D,OAAO,CAAC8D,QAAQ;UAC1BpE,UAAU,EAAEM,OAAO,CAACN,UAAU;UAC9ByF,UAAU,EAAEnF,OAAO,CAACH,kBAAkB,CAAC1C,MAAM;UAC7C+G,IAAI,EAAElE,OAAO,CAACkE;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,QAASrF,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9B6F,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;EAClB;EAGAK,OAAOA,CACLtI,IAAY,EACZmG,CAAoB,EACpBoC,mBAAoC,EACpC;IACA,IAAItK,YAAY,CAAC+B,IAAI,CAAC,EAAE;MACtB,MAAMkD,OAAO,GAAG,IAAI,CAACC,UAAU,CAACnD,IAAI,CAACa,IAAI,CAAC;MAC1C,IAAIqC,OAAO,YAAPA,OAAO,CAAE8D,QAAQ,IAAI9D,OAAO,CAACvB,IAAI,CAAC6G,aAAa,CAAC,OAAO,CAAC,EAAE;QAC5D,OAAOxI,IAAI;MACb;IACF;IAEA,IAAIxC,iBAAiB,CAACwC,IAAI,CAAC,EAAE;MAC3B,OAAOA,IAAI;IACb;IAEA,IAAI/B,YAAY,CAAC+B,IAAI,EAAE;MAAEa,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE;MAC7C,OAAOzD,cAAc,CACnB8B,gBAAgB,CACdA,gBAAgB,CACdA,gBAAgB,CAAC3B,UAAU,CAAC,OAAO,CAAC,EAAEA,UAAU,CAAC,WAAW,CAAC,CAAC,EAC9DA,UAAU,CAAC,OAAO,CAAC,CACpB,EACDA,UAAU,CAAC,MAAM,CAAC,CACnB,EACD,CAACyC,IAAI,CAAC,CACP;IACH;IAEA,IAAIyI,UAAU;IACd,MAAMC,IAAI,GAAG,CAAC1I,IAAI,CAAC;IACnB,IAAImG,CAAC,KAAK,IAAI,EAAE;MAEdsC,UAAU,GAAG,mBAAmB;IAClC,CAAC,MAAM,IAAI,OAAOtC,CAAC,KAAK,QAAQ,EAAE;MAChCuC,IAAI,CAACjI,IAAI,CAACtB,cAAc,CAACgH,CAAC,CAAC,CAAC;MAG5BsC,UAAU,GAAG,eAAe;IAE9B,CAAC,MAAM;MAELA,UAAU,GAAG,SAAS;IACxB;IAEA,IAAIF,mBAAmB,EAAE;MACvBG,IAAI,CAACC,OAAO,CAAC,IAAI,CAAC/C,GAAG,CAACgD,SAAS,CAACH,UAAU,CAAC,CAAC;MAC5CA,UAAU,GAAG,gBAAgB;IAC/B;IAGA,OAAOrL,cAAc,CAAC,IAAI,CAACwI,GAAG,CAACgD,SAAS,CAACH,UAAU,CAAC,EAAEC,IAAI,CAAC;EAC7D;EAEArC,QAAQA,CAACxF,IAAY,EAAE;IACrB,OAAO,CAAC,CAAC,IAAI,CAACgI,QAAQ,CAAChI,IAAI,CAAC;EAC9B;EAEAgI,QAAQA,CAAChI,IAAY,EAAE;IACrB,OAAO,IAAI,CAAC+D,MAAM,CAAC/C,GAAG,CAAChB,IAAI,CAAC;EAC9B;EAEAiI,aAAaA,CAACnH,IAAkC,EAAE;IAChD,IAAI,CAACiD,MAAM,CAACQ,GAAG,CAACzD,IAAI,CAAC3B,IAAI,CAAC+I,KAAK,CAAClI,IAAI,EAAEc,IAAI,CAAC;EAC7C;EAEAY,mBAAmBA,CAACZ,IAAc,EAAE;IAClC,IAAIA,IAAI,CAACqH,kBAAkB,EAAE,EAAE;MAC7B,IAAI,CAACF,aAAa,CAACnH,IAAI,CAAC;IAC1B,CAAC,MAAM,IAAIA,IAAI,CAAC3D,qBAAqB,EAAE,EAAE;MACvC,IAAI,CAACmE,eAAe,CAAC,SAAS,EAAER,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IACvD,CAAC,MAAM,IAAIA,IAAI,CAAC3C,qBAAqB,EAAE,EAAE;MACvC,MAAMsE,YAAY,GAAG3B,IAAI,CAACE,GAAG,CAAC,cAAc,CAAC;MAC7C,MAAM;QAAEuF;MAAK,CAAC,GAAGzF,IAAI,CAAC3B,IAAI;MAC1B,KAAK,MAAM4B,MAAM,IAAI0B,YAAY,EAAE;QACjC,IAAI,CAACnB,eAAe,CAACiF,IAAI,KAAK,OAAO,GAAG,OAAO,GAAGA,IAAI,EAAExF,MAAM,CAAC;MACjE;IACF,CAAC,MAAM,IAAID,IAAI,CAAC/D,kBAAkB,EAAE,EAAE;MACpC,IAAI+D,IAAI,CAAC3B,IAAI,CAACiJ,OAAO,EAAE;MACvB,IAAI,CAAC9G,eAAe,CAAC,KAAK,EAAER,IAAI,CAAC;IACnC,CAAC,MAAM,IAAIA,IAAI,CAACzD,mBAAmB,EAAE,EAAE;MACrC,MAAMgL,iBAAiB,GACrBvH,IAAI,CAAC3B,IAAI,CAACmJ,UAAU,KAAK,MAAM,IAAIxH,IAAI,CAAC3B,IAAI,CAACmJ,UAAU,KAAK,QAAQ;MACtE,MAAM/I,UAAU,GAAGuB,IAAI,CAACE,GAAG,CAAC,YAAY,CAAC;MACzC,KAAK,MAAMuH,SAAS,IAAIhJ,UAAU,EAAE;QAClC,MAAMiJ,eAAe,GACnBH,iBAAiB,IAChBE,SAAS,CAACE,iBAAiB,EAAE,KAC3BF,SAAS,CAACpJ,IAAI,CAACmJ,UAAU,KAAK,MAAM,IACnCC,SAAS,CAACpJ,IAAI,CAACmJ,UAAU,KAAK,QAAQ,CAAE;QAE9C,IAAI,CAAChH,eAAe,CAACkH,eAAe,GAAG,SAAS,GAAG,QAAQ,EAAED,SAAS,CAAC;MACzE;IACF,CAAC,MAAM,IAAIzH,IAAI,CAAC7B,mBAAmB,EAAE,EAAE;MAErC,MAAM8B,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAC,aAAa,CAAa;MAClD,IACED,MAAM,CAAChE,kBAAkB,EAAE,IAC3BgE,MAAM,CAAC5D,qBAAqB,EAAE,IAC9B4D,MAAM,CAAC5C,qBAAqB,EAAE,EAC9B;QACA,IAAI,CAACuD,mBAAmB,CAACX,MAAM,CAAC;MAClC;IACF,CAAC,MAAM;MACL,IAAI,CAACO,eAAe,CAAC,SAAS,EAAER,IAAI,CAAC;IACvC;EACF;EAEA4H,kBAAkBA,CAAA,EAAG;IACnB,OAAOlK,eAAe,CAAC,MAAM,EAAEF,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;EACzD;EAEAqK,yBAAyBA,CAAC7H,IAAc,EAAE;IACxC,MAAM8H,GAAG,GAAG9H,IAAI,CAACrE,qBAAqB,EAAE;IACxC,KAAK,MAAMuD,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACiG,GAAG,CAAC,EAAE;MACnC,MAAMvG,OAAO,GAAG,IAAI,CAACC,UAAU,CAACtC,IAAI,CAAC;MACrC,IAAIqC,OAAO,EAAEA,OAAO,CAACwG,QAAQ,CAAC/H,IAAI,CAAC;IACrC;EACF;EAEAQ,eAAeA,CACbiF,IAAqB,EACrBzF,IAAc,EACdgI,WAAqB,GAAGhI,IAAI,EAC5B;IACA,IAAI,CAACyF,IAAI,EAAE,MAAM,IAAIwC,cAAc,CAAC,WAAW,CAAC;IAEhD,IAAIjI,IAAI,CAAC3C,qBAAqB,EAAE,EAAE;MAChC,MAAM6K,WAA4B,GAAGlI,IAAI,CAACE,GAAG,CAAC,cAAc,CAAC;MAC7D,KAAK,MAAMD,MAAM,IAAIiI,WAAW,EAAE;QAChC,IAAI,CAAC1H,eAAe,CAACiF,IAAI,EAAExF,MAAM,CAAC;MACpC;MACA;IACF;IAEA,MAAMU,MAAM,GAAG,IAAI,CAACJ,gBAAgB,EAAE;IACtC,MAAMuH,GAAG,GAAG9H,IAAI,CAACmI,0BAA0B,CAAC,IAAI,CAAC;IAEjD,KAAK,MAAMjJ,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACiG,GAAG,CAAC,EAAE;MACnCnH,MAAM,CAACM,UAAU,CAAC/B,IAAI,CAAC,GAAG,IAAI;MAE9B,KAAK,MAAMM,EAAE,IAAIsI,GAAG,CAAC5I,IAAI,CAAC,EAAE;QAC1B,MAAML,KAAK,GAAG,IAAI,CAACuJ,aAAa,CAAClJ,IAAI,CAAC;QAEtC,IAAIL,KAAK,EAAE;UAGT,IAAIA,KAAK,CAACjD,UAAU,KAAK4D,EAAE,EAAE;UAE7B,IAAI,CAACgG,0BAA0B,CAAC3G,KAAK,EAAE4G,IAAI,EAAEvG,IAAI,EAAEM,EAAE,CAAC;QACxD;QAGA,IAAIX,KAAK,EAAE;UACT,IAAI,CAACgJ,yBAAyB,CAACG,WAAW,CAAC;QAC7C,CAAC,MAAM;UACL,IAAI,CAAC3F,QAAQ,CAACnD,IAAI,CAAC,GAAG,IAAImJ,gBAAO,CAAC;YAChCzM,UAAU,EAAE4D,EAAE;YACdY,KAAK,EAAE,IAAI;YACXJ,IAAI,EAAEgI,WAAW;YACjBvC,IAAI,EAAEA;UACR,CAAC,CAAC;QACJ;MACF;IACF;EACF;EAEA6C,SAASA,CAACjK,IAAoC,EAAE;IAC9C,IAAI,CAAC8E,OAAO,CAAC9E,IAAI,CAACa,IAAI,CAAC,GAAGb,IAAI;EAChC;EAEAkK,MAAMA,CAACrJ,IAAY,EAAW;IAC5B,IAAIkB,KAAY,GAAG,IAAI;IAEvB,GAAG;MACD,IAAIA,KAAK,CAACgD,IAAI,CAAClE,IAAI,CAAC,EAAE,OAAO,IAAI;IACnC,CAAC,QAASkB,KAAK,GAAGA,KAAK,CAACO,MAAM;IAE9B,OAAO,KAAK;EACd;EAEAiE,SAASA,CAAC1F,IAAY,EAAW;IAC/B,IAAIkB,KAAY,GAAG,IAAI;IAEvB,GAAG;MACD,IAAIA,KAAK,CAAC+C,OAAO,CAACjE,IAAI,CAAC,EAAE,OAAO,IAAI;IACtC,CAAC,QAASkB,KAAK,GAAGA,KAAK,CAACO,MAAM;IAE9B,OAAO,KAAK;EACd;EAEAkE,YAAYA,CAAC3F,IAAY,EAAW;IAClC,OAAO,CAAC,CAAC,IAAI,CAACqB,gBAAgB,EAAE,CAACU,UAAU,CAAC/B,IAAI,CAAC;EACnD;EAEAsJ,MAAMA,CAACnK,IAAY,EAAEoK,aAAuB,EAAW;IACrD,IAAInM,YAAY,CAAC+B,IAAI,CAAC,EAAE;MACtB,MAAMkD,OAAO,GAAG,IAAI,CAACC,UAAU,CAACnD,IAAI,CAACa,IAAI,CAAC;MAC1C,IAAI,CAACqC,OAAO,EAAE,OAAO,KAAK;MAC1B,IAAIkH,aAAa,EAAE,OAAOlH,OAAO,CAAC8D,QAAQ;MAC1C,OAAO,IAAI;IACb,CAAC,MAAM,IACLlI,gBAAgB,CAACkB,IAAI,CAAC,IACtBJ,cAAc,CAACI,IAAI,CAAC,IACpBL,gBAAgB,CAACK,IAAI,CAAC,IACtBH,aAAa,CAACG,IAAI,CAAC,EACnB;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAItC,OAAO,CAACsC,IAAI,CAAC,EAAE;MAAA,IAAAqK,gBAAA;MACxB,IAAIrK,IAAI,CAACsK,UAAU,IAAI,CAAC,IAAI,CAACH,MAAM,CAACnK,IAAI,CAACsK,UAAU,EAAEF,aAAa,CAAC,EAAE;QACnE,OAAO,KAAK;MACd;MACA,IAAI,EAAAC,gBAAA,GAAArK,IAAI,CAACuK,UAAU,qBAAfF,gBAAA,CAAiBhK,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI,CAAC8J,MAAM,CAACnK,IAAI,CAACwK,IAAI,EAAEJ,aAAa,CAAC;IAC9C,CAAC,MAAM,IAAIzM,WAAW,CAACqC,IAAI,CAAC,EAAE;MAC5B,KAAK,MAAMyK,MAAM,IAAIzK,IAAI,CAACwK,IAAI,EAAE;QAC9B,IAAI,CAAC,IAAI,CAACL,MAAM,CAACM,MAAM,EAAEL,aAAa,CAAC,EAAE,OAAO,KAAK;MACvD;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAI3M,QAAQ,CAACuC,IAAI,CAAC,EAAE;MACzB,OACE,IAAI,CAACmK,MAAM,CAACnK,IAAI,CAACkB,IAAI,EAAEkJ,aAAa,CAAC,IACrC,IAAI,CAACD,MAAM,CAACnK,IAAI,CAAC0K,KAAK,EAAEN,aAAa,CAAC;IAE1C,CAAC,MAAM,IAAI5M,iBAAiB,CAACwC,IAAI,CAAC,IAAIP,iBAAiB,CAACO,IAAI,CAAC,EAAE;MAC7D,KAAK,MAAM2K,IAAI,IAAI3K,IAAI,CAAC4K,QAAQ,EAAE;QAChC,IAAID,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,CAACR,MAAM,CAACQ,IAAI,EAAEP,aAAa,CAAC,EAAE,OAAO,KAAK;MACtE;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAI7L,kBAAkB,CAACyB,IAAI,CAAC,IAAIR,kBAAkB,CAACQ,IAAI,CAAC,EAAE;MAC/D,KAAK,MAAM6K,IAAI,IAAI7K,IAAI,CAACe,UAAU,EAAE;QAClC,IAAI,CAAC,IAAI,CAACoJ,MAAM,CAACU,IAAI,EAAET,aAAa,CAAC,EAAE,OAAO,KAAK;MACrD;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIhM,QAAQ,CAAC4B,IAAI,CAAC,EAAE;MAAA,IAAA8K,iBAAA;MACzB,IAAI9K,IAAI,CAAC+K,QAAQ,IAAI,CAAC,IAAI,CAACZ,MAAM,CAACnK,IAAI,CAACiB,GAAG,EAAEmJ,aAAa,CAAC,EAAE,OAAO,KAAK;MACxE,IAAI,EAAAU,iBAAA,GAAA9K,IAAI,CAACuK,UAAU,qBAAfO,iBAAA,CAAiBzK,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAI7B,UAAU,CAACwB,IAAI,CAAC,EAAE;MAAA,IAAAgL,iBAAA;MAE3B,IAAIhL,IAAI,CAAC+K,QAAQ,IAAI,CAAC,IAAI,CAACZ,MAAM,CAACnK,IAAI,CAACiB,GAAG,EAAEmJ,aAAa,CAAC,EAAE,OAAO,KAAK;MACxE,IAAI,EAAAY,iBAAA,GAAAhL,IAAI,CAACuK,UAAU,qBAAfS,iBAAA,CAAiB3K,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,IAAIX,gBAAgB,CAACM,IAAI,CAAC,IAAIA,IAAI,CAACiL,MAAM,EAAE;QACzC,IAAIjL,IAAI,CAACU,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAACyJ,MAAM,CAACnK,IAAI,CAACU,KAAK,EAAE0J,aAAa,CAAC,EAAE;UAClE,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIrL,iBAAiB,CAACiB,IAAI,CAAC,EAAE;MAClC,OAAO,IAAI,CAACmK,MAAM,CAACnK,IAAI,CAACgB,QAAQ,EAAEoJ,aAAa,CAAC;IAClD,CAAC,MAAM,IAAIxL,0BAA0B,CAACoB,IAAI,CAAC,EAAE;MAC3C,OACEf,cAAc,CAACe,IAAI,CAACkL,GAAG,EAAE,YAAY,CAAC,IACtC,CAAC,IAAI,CAAC5E,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,IAChC,IAAI,CAAC6D,MAAM,CAACnK,IAAI,CAACmL,KAAK,EAAEf,aAAa,CAAC;IAE1C,CAAC,MAAM,IAAIvL,iBAAiB,CAACmB,IAAI,CAAC,EAAE;MAClC,KAAK,MAAMoB,UAAU,IAAIpB,IAAI,CAACoL,WAAW,EAAE;QACzC,IAAI,CAAC,IAAI,CAACjB,MAAM,CAAC/I,UAAU,EAAEgJ,aAAa,CAAC,EAAE,OAAO,KAAK;MAC3D;MACA,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO3L,SAAS,CAACuB,IAAI,CAAC;IACxB;EACF;EAMAqL,OAAOA,CAACpK,GAAoB,EAAEqK,GAAQ,EAAE;IACtC,OAAQ,IAAI,CAACtG,IAAI,CAAC/D,GAAG,CAAC,GAAGqK,GAAG;EAC9B;EAMAC,OAAOA,CAACtK,GAAoB,EAAO;IACjC,IAAIc,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,MAAMiD,IAAI,GAAGjD,KAAK,CAACiD,IAAI,CAAC/D,GAAG,CAAC;MAC5B,IAAI+D,IAAI,IAAI,IAAI,EAAE,OAAOA,IAAI;IAC/B,CAAC,QAASjD,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;EAOAkJ,UAAUA,CAACvK,GAAW,EAAE;IACtB,IAAIc,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,MAAMiD,IAAI,GAAGjD,KAAK,CAACiD,IAAI,CAAC/D,GAAG,CAAC;MAC5B,IAAI+D,IAAI,IAAI,IAAI,EAAEjD,KAAK,CAACiD,IAAI,CAAC/D,GAAG,CAAC,GAAG,IAAI;IAC1C,CAAC,QAASc,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;EAEAmJ,IAAIA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAAC5G,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG,IAAI;MAClB,IAAI,CAAC6G,KAAK,EAAE;IACd;EACF;EAEAA,KAAKA,CAAA,EAAG;IACN,MAAM/J,IAAI,GAAG,IAAI,CAACA,IAAI;IAEtB,IAAI,CAACiB,UAAU,GAAGW,MAAM,CAACoI,MAAM,CAAC,IAAI,CAAC;IACrC,IAAI,CAAC3H,QAAQ,GAAGT,MAAM,CAACoI,MAAM,CAAC,IAAI,CAAC;IACnC,IAAI,CAAC7G,OAAO,GAAGvB,MAAM,CAACoI,MAAM,CAAC,IAAI,CAAC;IAClC,IAAI,CAAC5G,IAAI,GAAGxB,MAAM,CAACoI,MAAM,CAAC,IAAI,CAAC;IAC/B,IAAI,CAAC3G,IAAI,GAAGzB,MAAM,CAACoI,MAAM,CAAC,IAAI,CAAC;IAE/B,MAAMC,aAAa,GAAG,IAAI,CAAC1J,gBAAgB,EAAE;IAC7C,IAAI0J,aAAa,CAAC3G,QAAQ,EAAE;IAE5B,MAAMtC,KAA0B,GAAG;MACjCC,UAAU,EAAE,EAAE;MACdG,kBAAkB,EAAE,EAAE;MACtBY,WAAW,EAAE;IACf,CAAC;IAED,IAAI,CAACsB,QAAQ,GAAG,IAAI;IAGpB,IAAItD,IAAI,CAACzB,IAAI,KAAK,SAAS,IAAIuB,gBAAgB,CAACoK,SAAS,EAAE;MAEzD,KAAK,MAAMC,KAAK,IAAIrK,gBAAgB,CAACsK,KAAK,EAAE;QAC1CD,KAAK,CAACnK,IAAI,EAAEgB,KAAK,CAAC;MACpB;MACA,MAAMqJ,YAAY,GAAGvK,gBAAgB,CAACE,IAAI,CAACzB,IAAI,CAAC;MAChD,IAAI8L,YAAY,EAAE;QAEhB,KAAK,MAAMF,KAAK,IAAIE,YAAY,CAACD,KAAK,EAAE;UACtCD,KAAK,CAACnK,IAAI,EAAEgB,KAAK,CAAC;QACpB;MACF;IACF;IACAhB,IAAI,CAACkE,QAAQ,CAACpE,gBAAgB,EAAEkB,KAAK,CAAC;IACtC,IAAI,CAACsC,QAAQ,GAAG,KAAK;IAGrB,KAAK,MAAMtD,IAAI,IAAIgB,KAAK,CAACgB,WAAW,EAAE;MAEpC,MAAM8F,GAAG,GAAG9H,IAAI,CAACrE,qBAAqB,EAAE;MACxC,KAAK,MAAMuD,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACiG,GAAG,CAAC,EAAE;QACnC,IAAI9H,IAAI,CAACI,KAAK,CAACoB,UAAU,CAACtC,IAAI,CAAC,EAAE;QACjC+K,aAAa,CAAC3B,SAAS,CAACR,GAAG,CAAC5I,IAAI,CAAC,CAAC;MACpC;MAGAc,IAAI,CAACI,KAAK,CAACyH,yBAAyB,CAAC7H,IAAI,CAAC;IAC5C;IAGA,KAAK,MAAMsK,GAAG,IAAItJ,KAAK,CAACC,UAAU,EAAE;MAClC,MAAMM,OAAO,GAAG+I,GAAG,CAAClK,KAAK,CAACoB,UAAU,CAAC8I,GAAG,CAACjM,IAAI,CAACa,IAAI,CAAC;MACnD,IAAIqC,OAAO,EAAE;QACXA,OAAO,CAACE,SAAS,CAAC6I,GAAG,CAAC;MACxB,CAAC,MAAM;QACLL,aAAa,CAAC3B,SAAS,CAACgC,GAAG,CAACjM,IAAI,CAAC;MACnC;IACF;IAGA,KAAK,MAAM2B,IAAI,IAAIgB,KAAK,CAACI,kBAAkB,EAAE;MAC3CpB,IAAI,CAACI,KAAK,CAACyH,yBAAyB,CAAC7H,IAAI,CAAC;IAC5C;EACF;EAEAlB,IAAIA,CAACqF,IAMJ,EAAE;IACD,IAAInE,IAAI,GAAG,IAAI,CAACA,IAAI;IAEpB,IAAIA,IAAI,CAACmB,SAAS,EAAE,EAAE;MACpBnB,IAAI,GAAG,IAAI,CAACuK,gBAAgB,EAAE,CAACvK,IAAI;IACrC,CAAC,MAAM,IAAI,CAACA,IAAI,CAACwK,gBAAgB,EAAE,IAAI,CAACxK,IAAI,CAACyK,SAAS,EAAE,EAAE;MACxDzK,IAAI,GAAG,IAAI,CAACc,cAAc,EAAE,CAACd,IAAI;IACnC;IAEA,IAAIA,IAAI,CAAC0K,iBAAiB,EAAE,EAAE;MAC5B1K,IAAI,GAAG,CAAC,IAAI,CAACM,iBAAiB,EAAE,IAAI,IAAI,CAACC,gBAAgB,EAAE,EAAEP,IAAI;IACnE;IAEA,IAAIA,IAAI,CAAC2K,MAAM,EAAE,IAAI3K,IAAI,CAAC4K,aAAa,EAAE,IAAI5K,IAAI,CAAC6K,UAAU,EAAE,EAAE;MAE9D7K,IAAI,CAAC8K,WAAW,EAAE;MAElB9K,IAAI,GAAGA,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IACzB;IAEA,MAAM6K,MAAM,GAAG5G,IAAI,CAAC4G,MAAM;IAC1B,MAAMtF,IAAI,GAAGtB,IAAI,CAACsB,IAAI,IAAI,KAAK;IAC/B,MAAMuF,UAAU,GAAG7G,IAAI,CAAC8G,WAAW,IAAI,IAAI,GAAG,CAAC,GAAG9G,IAAI,CAAC8G,WAAW;IAElE,MAAMC,OAAO,GAAI,eAAczF,IAAK,IAAGuF,UAAW,EAAC;IACnD,IAAIG,UAAU,GAAG,CAACJ,MAAM,IAAI/K,IAAI,CAAC4J,OAAO,CAACsB,OAAO,CAAC;IAEjD,IAAI,CAACC,UAAU,EAAE;MACf,MAAMlL,MAAM,GAAGtC,mBAAmB,CAAC8H,IAAI,EAAE,EAAE,CAAC;MAE5CxF,MAAM,CAACgL,WAAW,GAAGD,UAAU;MAE/B,CAACG,UAAU,CAAC,GAAInL,IAAI,CAAgCoL,gBAAgB,CAClE,MAAM,EACN,CAACnL,MAAM,CAAC,CACT;MACD,IAAI,CAAC8K,MAAM,EAAE/K,IAAI,CAAC0J,OAAO,CAACwB,OAAO,EAAEC,UAAU,CAAC;IAChD;IAEA,MAAME,UAAU,GAAGzN,kBAAkB,CAACuG,IAAI,CAAC3E,EAAE,EAAE2E,IAAI,CAAC2F,IAAI,CAAC;IACzD,MAAMwB,GAAG,GAAGH,UAAU,CAAC9M,IAAI,CAACsD,YAAY,CAAC7C,IAAI,CAACuM,UAAU,CAAC;IACzDrL,IAAI,CAACI,KAAK,CAACI,eAAe,CAACiF,IAAI,EAAE0F,UAAU,CAACjL,GAAG,CAAC,cAAc,CAAC,CAACoL,GAAG,GAAG,CAAC,CAAC,CAAC;EAC3E;EAMA/K,gBAAgBA,CAAA,EAAG;IACjB,IAAIH,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACJ,IAAI,CAACyK,SAAS,EAAE,EAAE;QAC1B,OAAOrK,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9B,MAAM,IAAI4K,KAAK,CAAC,yBAAyB,CAAC;EAC5C;EAMAjL,iBAAiBA,CAAA,EAAiB;IAChC,IAAIF,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACJ,IAAI,CAACwL,gBAAgB,EAAE,EAAE;QACjC,OAAOpL,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9B,OAAO,IAAI;EACb;EAOAG,cAAcA,CAAA,EAAG;IACf,IAAIV,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACJ,IAAI,CAACyL,aAAa,EAAE,EAAE;QAC9B,OAAOrL,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9B,MAAM,IAAI4K,KAAK,CACb,8EAA8E,CAC/E;EACH;EAOAhB,gBAAgBA,CAAA,EAAG;IACjB,IAAInK,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAI,CAACA,KAAK,CAACJ,IAAI,CAACmB,SAAS,EAAE,EAAE;QAC3B,OAAOf,KAAK,CAACU,cAAc,EAAE;MAC/B;IACF,CAAC,QAASV,KAAK,GAAGA,KAAK,CAACO,MAAM,CAACA,MAAM;IACrC,MAAM,IAAI4K,KAAK,CACb,8EAA8E,CAC/E;EACH;EAMAG,cAAcA,CAAA,EAA4B;IACxC,MAAM5D,GAAG,GAAGlG,MAAM,CAACoI,MAAM,CAAC,IAAI,CAAC;IAE/B,IAAI5J,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,KAAK,MAAMd,GAAG,IAAIsC,MAAM,CAACC,IAAI,CAACzB,KAAK,CAACiC,QAAQ,CAAC,EAAE;QAC7C,IAAI/C,GAAG,IAAIwI,GAAG,KAAK,KAAK,EAAE;UACxBA,GAAG,CAACxI,GAAG,CAAC,GAAGc,KAAK,CAACiC,QAAQ,CAAC/C,GAAG,CAAC;QAChC;MACF;MACAc,KAAK,GAAGA,KAAK,CAACO,MAAM;IACtB,CAAC,QAAQP,KAAK;IAEd,OAAO0H,GAAG;EACZ;EAMA6D,oBAAoBA,CAAC,GAAGC,KAAe,EAA2B;IAChE,MAAM9D,GAAG,GAAGlG,MAAM,CAACoI,MAAM,CAAC,IAAI,CAAC;IAE/B,KAAK,MAAMvE,IAAI,IAAImG,KAAK,EAAE;MACxB,IAAIxL,KAAY,GAAG,IAAI;MACvB,GAAG;QACD,KAAK,MAAMlB,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACzB,KAAK,CAACiC,QAAQ,CAAC,EAAE;UAC9C,MAAMd,OAAO,GAAGnB,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC;UACpC,IAAIqC,OAAO,CAACkE,IAAI,KAAKA,IAAI,EAAEqC,GAAG,CAAC5I,IAAI,CAAC,GAAGqC,OAAO;QAChD;QACAnB,KAAK,GAAGA,KAAK,CAACO,MAAM;MACtB,CAAC,QAAQP,KAAK;IAChB;IAEA,OAAO0H,GAAG;EACZ;EAEA+D,uBAAuBA,CAAC3M,IAAY,EAAEb,IAAY,EAAW;IAC3D,OAAO,IAAI,CAACyN,oBAAoB,CAAC5M,IAAI,CAAC,KAAKb,IAAI;EACjD;EAEAmD,UAAUA,CAACtC,IAAY,EAAuB;IAC5C,IAAIkB,KAAY,GAAG,IAAI;IACvB,IAAI2L,YAAY;IAEhB,GAAG;MACD,MAAMxK,OAAO,GAAGnB,KAAK,CAACgI,aAAa,CAAClJ,IAAI,CAAC;MACzC,IAAIqC,OAAO,EAAE;QAAA,IAAAyK,aAAA;QAUX,IACE,CAAAA,aAAA,GAAAD,YAAY,aAAZC,aAAA,CAAc7K,SAAS,EAAE,IACzBI,OAAO,CAACkE,IAAI,KAAK,OAAO,IACxBlE,OAAO,CAACkE,IAAI,KAAK,OAAO,EACxB,CAEF,CAAC,MAAM;UACL,OAAOlE,OAAO;QAChB;MACF,CAAC,MAAM,IACL,CAACA,OAAO,IACRrC,IAAI,KAAK,WAAW,IACpBkB,KAAK,CAACJ,IAAI,CAAC6K,UAAU,EAAE,IACvB,CAACzK,KAAK,CAACJ,IAAI,CAACiM,yBAAyB,EAAE,EACvC;QACA;MACF;MACAF,YAAY,GAAG3L,KAAK,CAACJ,IAAI;IAC3B,CAAC,QAASI,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;EAEAyH,aAAaA,CAAClJ,IAAY,EAAuB;IAC/C,OAAO,IAAI,CAACmD,QAAQ,CAACnD,IAAI,CAAC;EAC5B;EAGA4M,oBAAoBA,CAAC5M,IAAY,EAAgB;IAAA,IAAAgN,gBAAA;IAC/C,QAAAA,gBAAA,GAAO,IAAI,CAAC1K,UAAU,CAACtC,IAAI,CAAC,qBAArBgN,gBAAA,CAAuBtQ,UAAU;EAC1C;EAGAuQ,uBAAuBA,CAACjN,IAAY,EAAgB;IAClD,MAAMqC,OAAO,GAAG,IAAI,CAACc,QAAQ,CAACnD,IAAI,CAAC;IACnC,OAAOqC,OAAO,oBAAPA,OAAO,CAAE3F,UAAU;EAC5B;EAEAwQ,aAAaA,CAAClN,IAAY,EAAE;IAC1B,OAAO,CAAC,CAAC,IAAI,CAACkJ,aAAa,CAAClJ,IAAI,CAAC;EACnC;EAQAyF,UAAUA,CACRzF,IAAY,EACZiF,IAA0D,EAC1D;IAAA,IAAAkI,KAAA,EAAAC,MAAA,EAAAC,MAAA;IACA,IAAI,CAACrN,IAAI,EAAE,OAAO,KAAK;IACvB,IAAI,IAAI,CAACkN,aAAa,CAAClN,IAAI,CAAC,EAAE,OAAO,IAAI;IACzC;MAEE,IAAI,OAAOiF,IAAI,KAAK,SAAS,EAAEA,IAAI,GAAG;QAAEqI,SAAS,EAAErI;MAAK,CAAC;IAC3D;IACA,IAAI,IAAI,CAACsI,gBAAgB,CAACvN,IAAI,EAAEiF,IAAI,CAAC,EAAE,OAAO,IAAI;IAClD,IAAI,GAAAkI,KAAA,GAAClI,IAAI,aAAJkI,KAAA,CAAMK,MAAM,KAAI,IAAI,CAACnE,MAAM,CAACrJ,IAAI,CAAC,EAAE,OAAO,IAAI;IACnD,IAAI,GAAAoN,MAAA,GAACnI,IAAI,aAAJmI,MAAA,CAAME,SAAS,KAAI1J,KAAK,CAACK,OAAO,CAACwJ,QAAQ,CAACzN,IAAI,CAAC,EAAE,OAAO,IAAI;IACjE,IAAI,GAAAqN,MAAA,GAACpI,IAAI,aAAJoI,MAAA,CAAMC,SAAS,KAAI1J,KAAK,CAAC8J,gBAAgB,CAACD,QAAQ,CAACzN,IAAI,CAAC,EAAE,OAAO,IAAI;IAC1E,OAAO,KAAK;EACd;EAEAuN,gBAAgBA,CACdvN,IAAY,EACZiF,IAAgD,EAChD;IAAA,IAAA0I,YAAA;IACA,QAAAA,YAAA,GAAO,IAAI,CAAClM,MAAM,qBAAXkM,YAAA,CAAalI,UAAU,CAACzF,IAAI,EAAEiF,IAAI,CAAC;EAC5C;EAMA2I,aAAaA,CAAC5N,IAAY,EAAEkB,KAAY,EAAE;IACxC,MAAM2M,IAAI,GAAG,IAAI,CAACvL,UAAU,CAACtC,IAAI,CAAC;IAClC,IAAI6N,IAAI,EAAE;MACRA,IAAI,CAAC3M,KAAK,CAAC4M,gBAAgB,CAAC9N,IAAI,CAAC;MACjC6N,IAAI,CAAC3M,KAAK,GAAGA,KAAK;MAClBA,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC,GAAG6N,IAAI;IAC7B;EACF;EAEAC,gBAAgBA,CAAC9N,IAAY,EAAE;IAC7B,OAAO,IAAI,CAACmD,QAAQ,CAACnD,IAAI,CAAC;EAC5B;EAEA+N,aAAaA,CAAC/N,IAAY,EAAE;IAAA,IAAAgO,iBAAA;IAE1B,CAAAA,iBAAA,OAAI,CAAC1L,UAAU,CAACtC,IAAI,CAAC,qBAArBgO,iBAAA,CAAuB9M,KAAK,CAAC4M,gBAAgB,CAAC9N,IAAI,CAAC;IAGnD,IAAIkB,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACgD,IAAI,CAAClE,IAAI,CAAC,EAAE;QACpBkB,KAAK,CAACgD,IAAI,CAAClE,IAAI,CAAC,GAAG,KAAK;MAC1B;IACF,CAAC,QAASkB,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;AACF;AAACwM,OAAA,CAAAC,OAAA,GAAAtK,KAAA;AAj7BoBA,KAAK,CA2CjBK,OAAO,GAAGvB,MAAM,CAACC,IAAI,CAACsB,QAAO,CAACkK,OAAO,CAAC;AA3C1BvK,KAAK,CAiDjB8J,gBAAgB,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,CAAC"}