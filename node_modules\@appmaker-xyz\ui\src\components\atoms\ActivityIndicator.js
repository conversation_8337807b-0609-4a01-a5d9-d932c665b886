import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import React from 'react';

function ProductGridSkeleton() {
  return (
    <SkeletonPlaceholder.Item width={'50%'} padding={8}>
      <SkeletonPlaceholder.Item width={'100%'} height={120} borderRadius={8} />
      <SkeletonPlaceholder.Item marginTop={8}>
        <SkeletonPlaceholder.Item width={'100%'} height={20} borderRadius={4} />
        <SkeletonPlaceholder.Item
          marginTop={6}
          width={80}
          height={20}
          borderRadius={4}
        />
      </SkeletonPlaceholder.Item>
    </SkeletonPlaceholder.Item>
  );
}
function ProductGridLayout(params) {
  return (
    <SkeletonPlaceholder.Item width={'100%'} padding={8}>
      <SkeletonPlaceholder.Item flexDirection="row" flexWrap={'wrap'}>
        {ProductGridSkeleton()}
        {ProductGridSkeleton()}
      </SkeletonPlaceholder.Item>
      <SkeletonPlaceholder.Item flexDirection="row" flexWrap={'wrap'}>
        {ProductGridSkeleton()}
        {ProductGridSkeleton()}
      </SkeletonPlaceholder.Item>
      <SkeletonPlaceholder.Item flexDirection="row" flexWrap={'wrap'}>
        {ProductGridSkeleton()}
        {ProductGridSkeleton()}
      </SkeletonPlaceholder.Item>
      <SkeletonPlaceholder.Item flexDirection="row" flexWrap={'wrap'}>
        {ProductGridSkeleton()}
        {ProductGridSkeleton()}
      </SkeletonPlaceholder.Item>
      <SkeletonPlaceholder.Item flexDirection="row" flexWrap={'wrap'}>
        {ProductGridSkeleton()}
        {ProductGridSkeleton()}
      </SkeletonPlaceholder.Item>
      <SkeletonPlaceholder.Item flexDirection="row" flexWrap={'wrap'}>
        {ProductGridSkeleton()}
        {ProductGridSkeleton()}
      </SkeletonPlaceholder.Item>
    </SkeletonPlaceholder.Item>
  );
}
function ProductScrollerLayout(params) {
  return (
    <SkeletonPlaceholder.Item width={'120%'} padding={8}>
      <SkeletonPlaceholder.Item flexDirection="row" flexWrap={'wrap'}>
        {ProductGridSkeleton()}
        {ProductGridSkeleton()}
      </SkeletonPlaceholder.Item>
    </SkeletonPlaceholder.Item>
  );
}

function HomePage() {
  return (
    <SkeletonPlaceholder.Item width={'100%'} padding={8}>
      <SkeletonPlaceholder.Item width={'100%'} height={180} borderRadius={8} />
      {Grid()}
      <SkeletonPlaceholder.Item
        width={180}
        height={30}
        borderRadius={8}
        margin={6}
      />
      <SkeletonPlaceholder.Item flexDirection="row" flexWrap={'wrap'}>
        {ProductGridSkeleton()}
        {ProductGridSkeleton()}
      </SkeletonPlaceholder.Item>
    </SkeletonPlaceholder.Item>
  );
}

function ProductDetail() {
  return (
    <SkeletonPlaceholder.Item width={'100%'} padding={8}>
      <SkeletonPlaceholder.Item width={'100%'} height={400} borderRadius={8} />
      <SkeletonPlaceholder.Item
        marginTop={8}
        height={120}
        width={'100%'}
        borderRadius={8}
      />
      <SkeletonPlaceholder.Item
        marginTop={8}
        height={50}
        width={'100%'}
        borderRadius={8}
      />
      <SkeletonPlaceholder.Item
        marginTop={8}
        height={50}
        width={'100%'}
        borderRadius={8}
      />
      <SkeletonPlaceholder.Item
        marginTop={8}
        height={50}
        width={'100%'}
        borderRadius={8}
      />
      <SkeletonPlaceholder.Item
        marginTop={8}
        height={50}
        width={'100%'}
        borderRadius={8}
      />
    </SkeletonPlaceholder.Item>
  );
}

function Grid() {
  return (
    <SkeletonPlaceholder.Item
      marginTop={8}
      flexDirection="row"
      flexWrap={'wrap'}
      justifyContent={'space-between'}>
      <SkeletonPlaceholder.Item
        width={110}
        height={110}
        margin={6}
        borderRadius={16}
      />
      <SkeletonPlaceholder.Item
        width={110}
        height={110}
        margin={6}
        borderRadius={16}
      />
      <SkeletonPlaceholder.Item
        width={110}
        height={110}
        margin={6}
        borderRadius={16}
      />
      <SkeletonPlaceholder.Item
        width={110}
        height={110}
        margin={6}
        borderRadius={16}
      />
      <SkeletonPlaceholder.Item
        width={110}
        height={110}
        margin={6}
        borderRadius={16}
      />
      <SkeletonPlaceholder.Item
        width={110}
        height={110}
        margin={6}
        borderRadius={16}
      />
    </SkeletonPlaceholder.Item>
  );
}

function Default() {
  return (
    <SkeletonPlaceholder.Item width={'100%'} padding={8}>
      <SkeletonPlaceholder.Item height={150} width={'100%'} borderRadius={8} />
      <SkeletonPlaceholder.Item
        marginTop={8}
        height={150}
        width={'100%'}
        borderRadius={8}
      />
      <SkeletonPlaceholder.Item
        marginTop={8}
        height={150}
        width={'100%'}
        borderRadius={8}
      />
      <SkeletonPlaceholder.Item
        marginTop={8}
        height={150}
        width={'100%'}
        borderRadius={8}
      />
    </SkeletonPlaceholder.Item>
  );
}
function getItem(type) {
  switch (type) {
    case 'home':
      return HomePage();
    case 'product-detail':
      return ProductDetail();
    case 'product-grid':
      return ProductGridLayout();
    case 'product-scroller':
      return ProductScrollerLayout();
    case 'grid':
      return Grid();
    default:
      return Default();
  }
}
const ActivityIndicator = ({ type = 'normal' }) => {
  let child = getItem(type);

  return (
    <SkeletonPlaceholder>
      <SkeletonPlaceholder.Item
        width={'100%'}
        flexDirection="row"
        flexWrap={'wrap'}
        padding={4}>
        {/* {ProductGridSkeleton()}
        {ProductGridSkeleton()}
        {ProductGridSkeleton()}
        {ProductGridSkeleton()}
        {ProductGridSkeleton()}
        {ProductGridSkeleton()} */}
        {/* {HomePage()} */}
        {/* {ProductDetail()} */}
        {/* {Grid()} */}
        {/* {Default()} */}
        {child}
        {/* {
          switch (type) {
            case value:
              Default()
              break;

            default:
              break;
          }
        } */}
      </SkeletonPlaceholder.Item>
    </SkeletonPlaceholder>
  );
};
export default ActivityIndicator;
