"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Var = exports.User = exports.Statement = exports.SpreadProperty = exports.Scope = exports.RestProperty = exports.ReferencedMemberExpression = exports.ReferencedIdentifier = exports.Referenced = exports.Pure = exports.NumericLiteralTypeAnnotation = exports.Generated = exports.ForAwaitStatement = exports.Flow = exports.Expression = exports.ExistentialTypeParam = exports.BlockScoped = exports.BindingIdentifier = void 0;
const ReferencedIdentifier = ["Identifier", "JSXIdentifier"];
exports.ReferencedIdentifier = ReferencedIdentifier;
const ReferencedMemberExpression = ["MemberExpression"];
exports.ReferencedMemberExpression = ReferencedMemberExpression;
const BindingIdentifier = ["Identifier"];
exports.BindingIdentifier = BindingIdentifier;
const Statement = ["Statement"];
exports.Statement = Statement;
const Expression = ["Expression"];
exports.Expression = Expression;
const Scope = ["Scopable", "Pattern"];
exports.Scope = Scope;
const Referenced = null;
exports.Referenced = Referenced;
const BlockScoped = null;
exports.BlockScoped = BlockScoped;
const Var = ["VariableDeclaration"];
exports.Var = Var;
const User = null;
exports.User = User;
const Generated = null;
exports.Generated = Generated;
const Pure = null;
exports.Pure = Pure;
const Flow = ["Flow", "ImportDeclaration", "ExportDeclaration", "ImportSpecifier"];
exports.Flow = Flow;
const RestProperty = ["RestElement"];
exports.RestProperty = RestProperty;
const SpreadProperty = ["RestElement"];
exports.SpreadProperty = SpreadProperty;
const ExistentialTypeParam = ["ExistsTypeAnnotation"];
exports.ExistentialTypeParam = ExistentialTypeParam;
const NumericLiteralTypeAnnotation = ["NumberLiteralTypeAnnotation"];
exports.NumericLiteralTypeAnnotation = NumericLiteralTypeAnnotation;
const ForAwaitStatement = ["ForOfStatement"];
exports.ForAwaitStatement = ForAwaitStatement;

//# sourceMappingURL=virtual-types.js.map
