import React, { useState } from 'react';
import Modal from 'react-native-modal';
import { StyleSheet, View } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { AppTouchable } from '@appmaker-xyz/ui';
import WebView from 'react-native-webview';

function CloseButton({ onPress }) {
  return (
    <AppTouchable
      onPress={onPress}
      style={{
        position: 'absolute',
        right: 12,
        top: 12,
        padding: 6,
        backgroundColor: '#00000066',
        zIndex: 50,
        borderRadius: 20,
      }}>
      <Icon name="x" size={18} color="#fff" />
    </AppTouchable>
  );
}
const LoadingBar = ({ color = '#3B78E7', percent, height = 3 }) => {
  const style = {
    backgroundColor: color,
    width: `${percent * 100}%`,
    height,
  };
  return <View style={[styles.container, style]} />;
};

export function ModalWebview({ visible, close, uri, onMessage }) {
  const [loadingState, setLoadingState] = useState({
    loading: true,
  });
  const _onLoadProgress = (syntheticEvent) => {
    setLoadingState((state) => ({
      ...state,
      loading: true,
      percent: syntheticEvent?.nativeEvent?.progress,
    }));
  };

  const _onError = (syntheticEvent) => {
    setLoadingState((state) => ({
      ...state,
      percent: 1,
      color: 'red',
    }));
  };

  const _onLoadStart = (syntheticEvent) => {
    setLoadingState((state) => ({
      ...state,
      loading: true,
      percent: 1,
    }));
  };

  const _onLoadEnd = (syntheticEvent) => {
    setTimeout(() => {
      setLoadingState((state) => ({
        ...state,
        percent: 100,
        loading: false,
      }));
    }, 3000);
  };
  console.log(uri);
  return (
    <Modal
      testID={'modal'}
      isVisible={visible}
      onSwipeComplete={close}
      onBackButtonPress={close}
      backdropTransitionOutTiming={0}
      // onBackdropPress={close}
      style={styles.view}>
      <CloseButton onPress={close} />

      {loadingState?.loading && <LoadingBar percent={loadingState?.percent} />}
      <ProgressBarWebview uri={uri} />
      {/* <WebView
        source={{
          uri,
        }}
        style={{ width: '100%' }}
        onMessage={onMessage}
        onLoadStart={_onLoadStart}
        onLoadEnd={_onLoadEnd}
        onLoadProgress={_onLoadProgress}
        onError={_onError}
      /> */}
    </Modal>
  );
}
const styles = StyleSheet.create({
  view: {
    borderRadius: 8,
    overflow: 'hidden',
    // margin: 0,
  },
  container: {
    position: 'absolute',
    zIndex: 10,
    top: 0,
    left: 0,
  },
});

// import * as Progress from 'react-native-progress';
const LoadingBarP = ({ color = '#3B78E7', percent, height = 3 }) => {
  console.log(`${parseInt(percent) * 100}%`);
  const style = {
    backgroundColor: 'blue',
    width: `${percent * 100}%`,
    height,
  };
  return <View style={[style]} />;
};

export const ProgressBarWebview = ({ uri }) => {
  const [progress, setProgress] = useState(0);
  const [isLoaded, setLoaded] = useState(false);
  console.log(uri);
  const source = { uri };
  const javascript = `
  document.body.style.backgroundColor = 'orange';
  window.alert('This is javascript');
`;
  return (
    <View style={{ flex: 1, position: 'relative' }}>
      {!isLoaded ? <LoadingBarP percent={progress} color="#ff8300" /> : null}
      <WebView
        style={{ width: '100%' }}
        source={source}
        // source={{ html: '<h1>This is a statsampleic HTML source!</h1>' }}
        // source={source}
        onError={(event) =>
          alert(`Webview error: ${event.nativeEvent.description}`)
        }
        // injectedJavaScript={javascript}
        onMessage={(event) => {
          alert(event.nativeEvent.data);
        }}
        onLoadProgress={({ nativeEvent }) => setProgress(nativeEvent.progress)}
        onLoadEnd={() => setLoaded(true)}
      />
    </View>
  );
};

export default function SizeChart({ visible, onClose, url }) {
  console.log('size-chart-url', url);
  return <ModalWebview visible={visible} close={onClose} uri={url} />;
}
