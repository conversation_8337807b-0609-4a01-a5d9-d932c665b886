import { StyleSheet } from "react-native";
import Config from "../../../../Config";
import { isDarkColor } from "../../../misc/HelperFunctions";

const styles = StyleSheet.create({
  submit: {
    backgroundColor: Config.toolbarColor,
    borderColor: Config.toolbarColor,
    borderRadius: 4
  },
  text: {
    fontWeight: "bold",
    color: isDarkColor(Config.toolbarColor) ? "white" : "black",
    textAlign: "center"
  }
});

export default styles;
