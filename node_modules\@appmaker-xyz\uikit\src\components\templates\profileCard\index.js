import React from 'react';
import { StyleSheet, FlatList } from 'react-native';
import { Card, Button } from '../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/FontAwesome';
import { useApThemeState } from '../../../theme/ThemeContext';

const ProfileCard = ({ attributes, onPress, onAction }) => {
  const {
    subtitle,
    image,
    title,
    description,
    socialLinks,
    appmakerAction,
    user,
    phone,
  } = attributes;
  const imageUrl = typeof image === 'object' ? image.url : image;
  const { color, spacing, font } = useApThemeState();
  const styles = allStyles({ color, spacing });
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  // console.log('imageUrl', imageUrl);
  return (
    <Card
      onPress={onPressHandle}
      attributes={{
        type: 'type-2',
        titleMeta: subtitle,
        featureImg: imageUrl,
        imgSrc: !imageUrl && require('./img/user_placeholder.png'),
        title: title,
        excerpt: description,
        desc: phone,
      }}>
      <FlatList
        columnWrapperStyle={styles.insideContainer}
        numColumns={4}
        data={socialLinks}
        renderItem={({ item }) => (
          <Button
            small
            status="light"
            accessoryLeft={() => (
              <Icon name={item.title.toLowerCase()} size={font.size.semi} />
            )}>
            {item.title}
          </Button>
        )}
        keyExtractor={(item) => item.id}
      />
    </Card>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.light,
      padding: spacing.base,
    },
    insideContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      flexWrap: 'wrap',
      paddingTop: spacing.small,
    },
    table: {
      padding: spacing.small,
      borderBottomWidth: 1,
      borderBottomColor: color.light,
    },
  });

export default ProfileCard;
