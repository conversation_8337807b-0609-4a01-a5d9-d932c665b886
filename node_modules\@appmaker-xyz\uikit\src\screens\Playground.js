import React from 'react';
import {
  StyleSheet,
  ScrollView,
  FlatList,
  SafeAreaView,
  Image,
} from 'react-native';
import { useApConfigState } from '@appmaker/v3';
import {
  AppmakerText,
  Layout,
  ProductScrollerItem,
  ProdcutScroller,
  ProductDetail,
  OrderDetail,
  OrderCard,
} from '../components';
import { color, spacing } from '../styles';
const DATA = [
  {
    id: 'bd7acbea-c1b1-46c2-aed5-3ad53abb28ba',
    title: 'English Spinach 1 Bunch',
    uri:
      'https://easygrocery.com.au/wp-content/uploads/2018/08/English-Spinach-1-Bunch-247x296.png',
    outofstock: 'Out of Stock',
  },
  {
    id: '3ac68afc-c605-48d3-a4f8-fbd91aa97f63',
    title: 'Methi Fresh Herb Bunch',
    uri:
      'https://easygrocery.com.au/wp-content/uploads/2018/08/Methi-Fresh-Herb-Bunch-247x296.jpg',
    outofstock: '',
  },
  {
    id: '58694a0f-3da1-471f-bd96-145571e29d72',
    title: 'Sharma’s Kitchen Paneer 300 gm (approx. 270gm – 320 gm)',
    uri:
      'https://easygrocery.com.au/wp-content/uploads/2018/08/Methi-Fresh-Herb-Bunch-247x296.jpg',
    outofstock: 'Out of Stock',
  },
  {
    id: 'bd7acbea-c1b1-46c2-aed5-3ad53abb27ba',
    title: 'Fresh Tomato 1 Kg',
    uri:
      'https://easygrocery.com.au/wp-content/uploads/2018/08/Methi-Fresh-Herb-Bunch-247x296.jpg',
    outofstock: '',
  },
  {
    id: '3ac68afc-c605-48d3-a4f8-fbd91aa97f63',
    title: 'Sharma’s Kitchen Yogurt (Yoghurt) 2 Kg',
    uri:
      'https://easygrocery.com.au/wp-content/uploads/2018/08/Methi-Fresh-Herb-Bunch-247x296.jpg',
    outofstock: '',
  },
  {
    id: '58694a0f-3da1-471f-bd96-145571e29d72',
    title: 'Green Capsicum 1 Each',
    uri:
      'https://easygrocery.com.au/wp-content/uploads/2018/08/Methi-Fresh-Herb-Bunch-247x296.jpg',
    outofstock: '',
  },
];

const Playground = () => {
  return (
    // <ScrollView style={styles.container}>
    //   <FlatList
    //     numColumns={2}
    //     data={DATA}
    //     renderItem={({item}) => (
    //       <Item
    //         title={item.title}
    //         uri={item.uri}
    //         outofstock={item.outofstock}
    //       />
    //     )}
    //     keyExtractor={(item) => item.id}
    //   />
    // </ScrollView>
    // <ProductDetail />
    // <ScrollView style={{padding: 12}}>
    //   <OrderCard
    //     attributes={{
    //       orderId: '1268',
    //       status: 'Processing',
    //       featureImg:
    //         'https://easygrocery.com.au/wp-content/uploads/2018/08/Methi-Fresh-Herb-Bunch-247x296.jpg',
    //       items: 'Tomato, Potato, Parseley, Onion, Sweet Potato',
    //       numberOfItems: 1,
    //       totalAmount: '$90.35',
    //       orderDate: 'Monday, Feb 12, 2020',
    //       deliveryDate: 'Monday, February 28, 2020',
    //       paymentMethod: 'Cash On-Delivery',
    //     }}
    //   />
    //   <OrderCard
    //     attributes={{
    //       orderId: '1268',
    //       status: 'On hold',
    //       featureImg: 'https://source.unsplash.com/random',
    //       items: 'Tomato, Potato, Parseley, Onion, Sweet Potato',
    //       totalAmount: '$90.35',
    //       numberOfItems: 2,
    //       orderDate: 'Monday, Feb 12, 2020',
    //       deliveryDate: 'Monday, February 28, 2020',
    //       paymentMethod: 'Cash On-Delivery',
    //     }}
    //   />
    //   <OrderCard
    //     attributes={{
    //       orderId: '1268',
    //       status: 'Completed',
    //       featureImg:
    //         'https://easygrocery.com.au/wp-content/uploads/2018/08/Methi-Fresh-Herb-Bunch-247x296.jpg',
    //       items: 'Tomato, Potato, Parseley, Onion, Sweet Potato',
    //       numberOfItems: 2,
    //       totalAmount: '$90.35',
    //       orderDate: 'Monday, Feb 12, 2020',
    //       deliveryDate: 'Monday, February 28, 2020',
    //       paymentMethod: 'Cash On-Delivery',
    //     }}
    //   />
    //   <OrderCard
    //     attributes={{
    //       orderId: '1268',
    //       status: 'Cancelled',
    //       featureImg:
    //         'https://easygrocery.com.au/wp-content/uploads/2018/08/Methi-Fresh-Herb-Bunch-247x296.jpg',
    //       items: 'Tomato, Potato, Parseley, Onion, Sweet Potato',
    //       numberOfItems: 2,
    //       totalAmount: '$90.35',
    //       orderDate: 'Monday, Feb 12, 2020',
    //       deliveryDate: 'Monday, February 28, 2020',
    //       paymentMethod: 'Cash On-Delivery',
    //     }}
    //   />
    //   <OrderCard
    //     attributes={{
    //       orderId: '1268',
    //       status: 'Failed',
    //       featureImg:
    //         'https://easygrocery.com.au/wp-content/uploads/2018/08/Methi-Fresh-Herb-Bunch-247x296.jpg',
    //       items: 'Tomato, Potato, Parseley, Onion, Sweet Potato',
    //       numberOfItems: 2,
    //       totalAmount: '$90.35',
    //       orderDate: 'Monday, Feb 12, 2020',
    //       deliveryDate: 'Monday, February 28, 2020',
    //       paymentMethod: 'Cash On-Delivery',
    //     }}
    //   />
    //   <OrderCard
    //     attributes={{
    //       orderId: '1268',
    //       status: 'Refunded',
    //       featureImg:
    //         'https://easygrocery.com.au/wp-content/uploads/2018/08/Methi-Fresh-Herb-Bunch-247x296.jpg',
    //       items: 'Items: Tomato, Potato, Parseley, Onion, Sweet Potato',
    //       numberOfItems: 2,
    //       totalAmount: '$90.35',
    //       orderDate: 'Monday, Feb 12, 2020',
    //       deliveryDate: 'Monday, February 28, 2020',
    //       paymentMethod: 'Cash On-Delivery',
    //     }}
    //   />
    //   <OrderCard
    //     attributes={{
    //       orderId: '1268',
    //       status: 'Pending payment',
    //       featureImg:
    //         'https://easygrocery.com.au/wp-content/uploads/2018/08/Methi-Fresh-Herb-Bunch-247x296.jpg',
    //       items: 'Items: Tomato, Potato, Parseley, Onion, Sweet Potato',
    //       numberOfItems: 2,
    //       totalAmount: '$90.35',
    //       orderDate: 'Monday, Feb 12, 2020',
    //       deliveryDate: 'Monday, February 28, 2020',
    //       paymentMethod: 'Cash On-Delivery',
    //     }}
    //   />
    // </ScrollView>
    <OrderDetail
      attributes={{
        orderId: detailData.id,
        status: detailData.status_label,
        numberOfItems: detailData.line_items.length,
        items: detailData.line_items,
        orderDate: detailData.date_created,
        paymentMethod: detailData.payment_method_title,
        billingAddress: detailData.billing,
        ShippingAddress: detailData.shipping,
      }}
    />
  );
};

const Item = ({ title, uri, outofstock }) => {
  return (
    <Layout style={styles.itemContainer}>
      <ScrollView horizontal>
        <ProductScrollerItem
          gridViewListing={true}
          uri={uri}
          title={title}
          regularPrice="$20"
          salePrice="$30"
          saleBadge="20% OFF"
          outOfStock={outofstock}
          wishList={true}
          // groceryMode={true}
        />
      </ScrollView>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: spacing.base,
    backgroundColor: color.white,
    marginBottom: spacing.nano,
  },
  itemContainer: {
    // width: '50%',
    padding: spacing.nano,
  },
});

export default Playground;

const detailData = {
  id: 183,
  status_label: 'Processing',
  status: 'processing',
  order_key: 'wc_order_7addGKKjxQYQE',
  number: '183',
  currency: 'INR',
  version: '4.4.1',
  date_created: '2020-09-07T04:50:10',
  date_modified: '2020-09-07T04:50:10',
  discount_total: 0,
  shipping_total: '0.00',
  shipping_tax: '0',
  cart_tax: '0',
  subtotal: 140,
  total: '140.00',
  total_tax: '0',
  billing: {
    first_name: 'hsbdb',
    last_name: 'wjsbb',
    company: 'snnsb',
    address_1: 'dnxbb',
    address_2: 'nssvv',
    city: 'dnnj',
    state: 'JH',
    postcode: 'XJXBXB',
    country: 'CI',
    email: '<EMAIL>',
    phone: '7679797667',
  },
  shipping: {
    first_name: 'hsbdb',
    last_name: 'wjsbb',
    company: 'snnsb',
    address_1: 'dnxbb',
    address_2: 'nssvv',
    city: 'dnnj',
    state: 'JH',
    postcode: 'XJXBXB',
    country: 'CI',
  },
  payment_method_title: 'Cash on delivery',
  date_completed: null,
  line_items: [
    {
      id: 122,
      name: 'Hoodie - blue, att1',
      sku: '',
      product_id: 122,
      variation_id: 122,
      quantity: 1,
      tax_class: '',
      price: 50,
      subtotal: '50',
      subtotal_tax: '0',
      total: '50',
      total_tax: '0',
      taxes: [],
      price_display: '₹50.00',
      subtotal_display: '₹50.00',
      subtotal_tax_display: '₹0.00',
      total_display: '₹50.00',
      total_tax_display: '₹0.00',
      product_price_display: '₹50.00',
    },
    {
      id: 123,
      name: 'Sunglasses',
      sku: '',
      product_id: 31,
      variation_id: 31,
      quantity: 1,
      tax_class: '',
      price: 90,
      subtotal: '90',
      subtotal_tax: '0',
      total: '90',
      total_tax: '0',
      taxes: [],
      price_display: '₹90.00',
      subtotal_display: '₹90.00',
      subtotal_tax_display: '₹0.00',
      total_display: '₹90.00',
      total_tax_display: '₹0.00',
      product_price_display: '₹90.00',
    },
  ],
  tax_lines: [],
  shipping_lines: [
    {
      id: 124,
      method_title: 'Free shipping',
      method_id: 'free_shipping',
      total: '0',
      total_tax: '0',
      taxes: [
        {
          id: 'total',
          total: [],
        },
      ],
    },
  ],
  fee_lines: [],
  coupon_lines: [],
  refunds: [],
  can_cancel_order: false,
  can_repeat_order: false,
  repeat_order_title: 'Order again',
  should_make_payment: false,
  payment_url:
    'https://shifa.site.shopilder.com/checkout/order-pay/183/?pay_for_order=true&key=wc_order_7addGKKjxQYQE',
  show_tax: true,
  discount_total_display: '₹0.00',
  shipping_total_display: '₹0.00',
  shipping_tax_display: '₹0.00',
  cart_tax_display: '₹0.00',
  total_display: '₹140.00',
  total_tax_display: '₹0.00',
  subtotal_display: '₹140.00',
  order_id_display: 183,
  instructions: 'Pay with cash upon delivery.',
  show_instructions: false,
  account_details: false,
  _links: {
    self: [
      {
        href:
          'https://shifa.site.shopilder.com/wp-json/appmaker-wc/v1/orders/183',
      },
    ],
    collection: [
      {
        href: 'https://shifa.site.shopilder.com/wp-json/appmaker-wc/v1/orders',
      },
    ],
    customer: [
      {
        href:
          'https://shifa.site.shopilder.com/wp-json/appmaker-wc/v1/customers/2',
      },
    ],
  },
};
