import React from 'react';
import { Image, View, Text } from 'react-native';
import PropTypes from 'prop-types';
import HTML from 'react-native-render-html';
import styles from './Styles';
// const toolbarLogo = require("../images/toolbarlogo.png");

const CommentItem = (props) => {
  const { image, text, author } = props.data.data;
  return (
    <View style={styles.container}>
      <View style={{ flexDirection: 'row' }}>
        <Image
          style={styles.image}
          source={{
            uri: image,
          }}
        />
        <View style={{ flex: 1, paddingTop: 5 }}>
          <Text allowFontScaling={false} style={styles.author}>
            {author}
          </Text>
          <View style={styles.comment}>
            <HTML html={text} />
          </View>
        </View>
      </View>
    </View>
  );
};

CommentItem.propTypes = {
  data: PropTypes.object.isRequired,
};

export default CommentItem;
