const ts = require('typescript');
const fs = require('fs');
const path = require('path');

function getTypeInfo(typeName) {
  const files = ['./index.d.ts'];
  const compilerOptions = {
    target: ts.ScriptTarget.ES2015,
    module: ts.ModuleKind.CommonJS,
    moduleResolution: ts.ModuleResolutionKind.NodeJs,
  };

  const program = ts.createProgram(files, compilerOptions);
  const checker = program.getTypeChecker();

  let typeInfo = null;
  const usedTypes = new Map();
  const MAX_DEPTH = 5;

  for (const sourceFile of program.getSourceFiles()) {
    if (sourceFile.isDeclarationFile) {
      ts.forEachChild(sourceFile, visit);
    }

    if (typeInfo) break;
  }

  function visit(node) {
    if (ts.isModuleDeclaration(node) && node.name.text === '@appmaker-xyz/shopify') {
      ts.forEachChild(node.body, visitModuleBody);
    } else {
      ts.forEachChild(node, visit);
    }
  }

  function visitModuleBody(node) {
    if (ts.isInterfaceDeclaration(node) && node.name.text === typeName) {
      const symbol = checker.getSymbolAtLocation(node.name);
      if (symbol) {
        const type = checker.getDeclaredTypeOfSymbol(symbol);
        typeInfo = serializeType(type, checker);
      }
    } else {
      ts.forEachChild(node, visitModuleBody);
    }
  }

  function serializeType(type, checker, indent = '', depth = 0) {
    if (depth > MAX_DEPTH) {
      return "any // Max depth exceeded";
    }

    addUsedType(type, depth);

    if (type.isClassOrInterface()) {
      const properties = type.getProperties();
      const serializedProperties = properties.map(prop => {
        const propType = checker.getTypeOfSymbolAtLocation(prop, prop.valueDeclaration);
        const propTypeString = serializeType(propType, checker, indent + '  ', depth + 1);
        const optional = (prop.flags & ts.SymbolFlags.Optional) !== 0 ? '?' : '';
        
        if (ts.isMethodSignature(prop.valueDeclaration) || ts.isMethodDeclaration(prop.valueDeclaration)) {
          const signature = checker.getSignatureFromDeclaration(prop.valueDeclaration);
          const parameters = signature.getParameters().map(param => {
            const paramType = checker.getTypeOfSymbolAtLocation(param, param.valueDeclaration);
            return `${param.name}: ${serializeType(paramType, checker, indent, depth + 1)}`;
          }).join(', ');
          const returnType = checker.getReturnTypeOfSignature(signature);
          return `${indent}  ${prop.name}${optional}: (${parameters}) => ${serializeType(returnType, checker, indent, depth + 1)};`;
        }
        
        return `${indent}  ${prop.name}${optional}: ${propTypeString};`;
      });

      return `{\n${serializedProperties.join('\n')}\n${indent}}`;
    }

    if (type.isUnion()) {
      return type.types.map(t => serializeType(t, checker, indent, depth + 1)).join(' | ');
    }

    if (type.isIntersection()) {
      return type.types.map(t => serializeType(t, checker, indent, depth + 1)).join(' & ');
    }

    if (type.isLiteral()) {
      return JSON.stringify(type.value);
    }

    if (type.symbol && type.symbol.name === 'Array') {
      const elementType = type.typeArguments[0];
      return `Array<${serializeType(elementType, checker, indent, depth + 1)}>`;
    }

    return checker.typeToString(type, undefined, ts.TypeFormatFlags.NoTruncation);
  }

  function addUsedType(type, depth) {
    if (depth > MAX_DEPTH) return;

    if (type.symbol) {
      const typeName = type.symbol.getName();
      if (!usedTypes.has(typeName) && typeName !== 'UseProductImagesReturn') {
        usedTypes.set(typeName, () => serializeType(type, checker, '  ', depth + 1));
      }
    }

    if (type.isUnion() || type.isIntersection()) {
      type.types.forEach(t => addUsedType(t, depth + 1));
    }

    if (type.isClassOrInterface()) {
      type.getProperties().forEach(prop => {
        const propType = checker.getTypeOfSymbolAtLocation(prop, prop.valueDeclaration);
        addUsedType(propType, depth + 1);
      });
    }

    if (type.symbol && type.symbol.name === 'Array' && type.typeArguments) {
      addUsedType(type.typeArguments[0], depth + 1);
    }
  }

  return { typeInfo, usedTypes };
}

const typeName = process.argv[2];

if (!typeName) {
  console.error('Please provide a type name as an argument.');
  process.exit(1);
}

const { typeInfo, usedTypes } = getTypeInfo(typeName);

if (typeInfo) {
  console.log(`export interface ${typeName} ${typeInfo}`);
  usedTypes.forEach((getTypeDefinition, usedTypeName) => {
    if (usedTypeName === 'Image') {
      const typeDefinition = getTypeDefinition();
      console.log(`\nexport interface ${usedTypeName} ${typeDefinition}`);
    }
  });
} else {
  console.log(`// Type ${typeName} not found.`);
}