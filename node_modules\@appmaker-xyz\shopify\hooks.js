import { appmaker, appSettings } from '@appmaker-xyz/core';
import { shopConfig } from './store/shopifyStore';
import { settings } from '@appmaker-xyz/app-config/newConfig';
import { appStorageApi } from '@appmaker-xyz/core';
import { appPluginStoreApi } from '@appmaker-xyz/core';
import base64 from 'base-64';
import { currencyHelper } from './helper/index';
import { calculateCartSavings } from './helper/cart';
import { cartHelpers } from './helper/cartHelper';
import { useStyles } from '@appmaker-xyz/react-native';

var moment = require('moment');

export function shopifyIdHelper(id, numberOnlyNeeded) {
  try {
    if (id.startsWith('gid://')) {
      if (numberOnlyNeeded) {
        const decodeId = id.match(/gid:\/\/shopify\/.*\/([a-zA-Z0-9]+)/i);
        let newID = decodeId[1].replace('/', '');
        return newID;
      }
      return id;
    } else {
      if (numberOnlyNeeded) {
        const decodeId = base64
          .decode(id)
          .match(/gid:\/\/shopify\/.*\/([a-zA-Z0-9]+)/i);
        let newID = decodeId[1].replace('/', '');
        return newID;
      }
      return base64.decode(id);
    }
  } catch (error) {
    return id;
  }
}
function checkIfTrueFalse(value) {
  return value === 1 || value === '1' || value === true || value === 'true';
}

export function getCurrency(currency) {
  const shopData = shopConfig.get();
  try {
    const { moneyFormat } = shopData?.shop;
    return `${moneyFormat}`.replace(
      /{\s?{\s?(amount|amount_no_decimals)\s?}\s?}/g,
      ' ',
    );
  } catch (error) {
    // console.log(error);
    return `${currency} `;
  }
}

function isBlank(str) {
  return str === null || str === '';
}

function differenceAmountCalculator(
  regular_price_value,
  price_value,
  currency,
) {
  const amount = Math.abs((regular_price_value - price_value).toFixed(2));
  const shopData = shopConfig.get();
  try {
    const { moneyFormat } = shopData?.shop;
    return `${moneyFormat}`.replace(
      /{\s?{\s?(amount|amount_no_decimals)\s?}\s?}/g,
      amount,
    );
  } catch (error) {
    // console.log(error);
    return `${currency} ${amount}`;
  }
}

function containsIframe(html) {
  try {
    const iframRegex = /(?:<iframe[^>]*)(?:(?:\/>)|(?:>.*?<\/iframe>))/;
    const iframeMatch = html.match(iframRegex);
    return iframeMatch.length > 0 ? true : false;
  } catch (error) {
    return false;
  }
}

function momentJS(datetime, format) {
  return moment(datetime).format(format);
}

function getLanguage() {
  return (
    appStorageApi().getState()?.language ||
    appSettings.getOption('defaultLanguage', 'en')
  );
}

function getCartQty(cart, productId) {
  try {
    const lineItems = cart.lineItems.edges;
    const product = lineItems.find(
      (item) => item.node.merchandise.product.id === productId,
    );
    return product?.node?.quantity;
  } catch (error) {
    // console.log(error, cart, productId);
    return null;
  }
}

function getObjectOfAnArray(array, findKey) {
  return array[findKey];
}

function hasTag(key, tags) {
  if (tags?.length && tags?.length > 0) {
    return (
      tags.filter((i) => i?.toLowerCase() === key?.toLowerCase()).length > 0
    );
  }
  return false;
}

function getDiscountCodeApplication(couponDiscounted) {
  let discountCodeApplication;
  try {
    discountCodeApplication = couponDiscounted.find(
      (item) => item.node.__typename === 'DiscountCodeApplication',
    );
    discountCodeApplication = discountCodeApplication.node.code;
  } catch (error) {}
  let AppOnlyCoupon =
    appPluginStoreApi().getState().plugins['app-only-coupons'];
  if (
    AppOnlyCoupon?.settings?.couponPrefix &&
    AppOnlyCoupon?.settings?.couponSufix &&
    discountCodeApplication.startsWith(AppOnlyCoupon?.settings?.couponPrefix) &&
    discountCodeApplication.endsWith(AppOnlyCoupon?.settings?.couponSufix)
  ) {
    discountCodeApplication = discountCodeApplication.replace(
      `_${AppOnlyCoupon?.settings?.couponSufix}`,
      '',
    );
  }
  return discountCodeApplication;
}

function cartUnitPriceAfterDiscount(discountTotal, amount, quantity) {
  let discountAmount = 0;
  if (Array.isArray(discountTotal)) {
    discountTotal.map((v) => {
      discountAmount = discountAmount + parseFloat(v?.allocatedAmount?.amount);
    });
    discountAmount = discountAmount / parseFloat(quantity);
    if (discountAmount > 0) {
      return Math.round((amount - discountAmount) * 100) / 100;
    }
  }
  return amount;
}

function returnUndefinedElse(checkValue, returnValue) {
  if (checkValue) {
    return returnValue;
  } else {
    return undefined;
  }
}

export function richToHTML(input) {
  try {
    if (typeof input === 'string') {
      input = JSON.parse(input);
    }
    return renderRichText(input);
  } catch (error) {
    console.error('Error processing rich text input:', error);
    return '';
  }
}

export function renderRichText(input) {
  try {
    const { type, children = [], ...props } = input;

    switch (type) {
      case 'root': {
        const classAttr = props.className ? ` class="${props.className}"` : '';
        return `<div${classAttr}>${children
          .map(renderRichText)
          .join('')}</div>`;
      }
      case 'paragraph': {
        return `<p>${children.map(renderRichText).join('')}</p>`;
      }
      case 'text': {
        let content = props.value || children.map(renderRichText).join('');
        if (props.bold) {
          content = `<b>${content}</b>`;
        }
        if (props.italic) {
          content = `<i>${content}</i>`;
        }
        return `<span>${content}</span>`;
      }
      case 'link': {
        const content = props.title || children.map(renderRichText).join('');
        return `<a href="${props.url}">${content}</a>`;
      }
      case 'heading': {
        return `<h${props.level}>${children.map(renderRichText).join('')}</h${
          props.level
        }>`;
      }
      case 'list': {
        const tag = props.listType === 'ordered' ? 'ol' : 'ul';
        return `<${tag}>${children.map(renderRichText).join('')}</${tag}>`;
      }
      case 'list-item': {
        return `<li>${children.map(renderRichText).join('')}</li>`;
      }
      default:
        return '';
    }
  } catch (error) {
    console.error('Error rendering rich text element:', error);
    return '';
  }
}

appmaker.addFilter(
  'parse-helper-functions',
  'add-cureency-helper',
  function (value) {
    return {
      ...value,
      shopifyIdHelper,
      returnUndefinedElse,
      hasTag,
      currencyHelper,
      getCartQty,
      calculateSavings: calculateCartSavings,
      getCurrency,
      differenceAmountCalculator,
      getLanguage,
      checkIfTrueFalse,
      isBlank,
      momentJS,
      containsIframe,
      getObjectOfAnArray,
      anotherName: 'saleeh',
      getDiscountCodeApplication,
      cartUnitPriceAfterDiscount,
      getCartTotalItemsCount: cartHelpers.getCartTotalItemsCount,
      richToHTML,
      useStyles,
    };
  },
);
