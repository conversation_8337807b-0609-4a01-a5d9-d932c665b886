import React, { useState } from 'react';
import { ImageBackground, StyleSheet } from 'react-native';
import { Layout, AppmakerText } from '@appmaker-xyz/uikit';
import { CountdownTimer } from './index';
import { useApThemeState } from '@appmaker-xyz/uikit/src/index';
import { dimensions } from '@appmaker-xyz/uikit/src/styles/index';

const CountDownTimerBlock = ({ attributes }) => {
  const {
    date,
    expiredText,
    dangerBgColor,
    dangerTextColor,
    bgColor,
    textColor,
    title,
    titleColor,
    backgroundColor,
    featureImg,
    stacked,
  } = attributes;
  const [hide, setHide] = useState(false);
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing, backgroundColor });
  const image = { uri: featureImg?.url };
  const ratio = featureImg?.width / featureImg?.height;
  const blockHeight = dimensions.fullWidth / ratio;
  const finalHeight = blockHeight <= 110 ? 110 : blockHeight;

  const onEndTimer = () => {
    setHide(true);
  };
  if (hide) {
    return null;
  }
  return (
    <Layout
      style={[styles.container, { height: featureImg ? finalHeight : null }]}>
      <ImageBackground source={image} resizeMode="cover" style={styles.image}>
        {title ? (
          <AppmakerText
            category="actionTitle"
            style={styles.text}
            fontColor={titleColor}>
            {title}
          </AppmakerText>
        ) : null}
        <CountdownTimer
          attributes={{
            targetDate: date,
            onEndTimer,
            expiredText: expiredText,
            dangerBgColor: dangerBgColor,
            dangerTextColor: dangerTextColor,
            bgColor: bgColor,
            textColor: textColor,
            size: 'lg',
            stacked: stacked,
          }}
        />
      </ImageBackground>
    </Layout>
  );
};

const allStyles = ({ spacing, color, backgroundColor }) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    image: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: spacing.base,
      backgroundColor: backgroundColor,
    },
    text: {
      marginBottom: spacing.base,
      textAlign: 'center',
    },
  });

export default CountDownTimerBlock;
