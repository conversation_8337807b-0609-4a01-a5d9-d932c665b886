import { View, StyleSheet } from 'react-native';
import React from 'react';
import { Layout, Button } from '@appmaker-xyz/uikit';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { spacing, color } from '../../../../styles';

const SortIcon = (props) => (
  <Icon name="sliders" size={18} color={color.dark} {...props} />
);
const FilterIcon = (props) => (
  <Icon name="filter" size={18} color={color.dark} {...props} />
);

export default function SortFilterButtons({
  attributes,
  openFilter,
  openSort,
}) {
  const { largeType, noIcon, bottomPlacement } = attributes;

  const buttonContainerStyles = [styles.buttonContainer];
  const innerContainerStyles = [styles.innerContainer];
  const buttonStyles = [{}];

  if (bottomPlacement) {
    buttonContainerStyles.push({
      borderColor: '#DDDDDD',
      borderTopWidth: 1,
      borderBottomWidth: 0,
    });
  }

  if (largeType) {
    buttonContainerStyles.push({
      borderBottomWidth: 0,
    });
    innerContainerStyles.push({
      borderRightWidth: 0,
    });
    buttonStyles.push({
      borderRadius: 0,
    });
  }

  return (
    <Layout style={buttonContainerStyles}>
      {attributes.show_filter ? (
        <Layout style={innerContainerStyles}>
          <Button
            onPress={openFilter}
            small={largeType ? false : true}
            baseSize={largeType ? true : false}
            accessoryLeft={noIcon ? null : () => <FilterIcon />}
            outline={largeType ? true : false}
            // accessoryRight={() => (
            //   <AppmakerText category="smallButtonText" status="primary">
            //     (Filter count)
            //   </AppmakerText>
            // )}
            status={largeType ? (bottomPlacement ? 'light' : 'dark') : 'white'}
            wholeContainerStyle={buttonStyles}>
            Filter
          </Button>
        </Layout>
      ) : null}
      {attributes.show_sort ? (
        <Layout style={innerContainerStyles}>
          <Button
            small={largeType ? false : true}
            baseSize={largeType ? true : false}
            accessoryLeft={noIcon ? null : () => <SortIcon />}
            outline={largeType ? true : false}
            onPress={openSort}
            status={largeType ? (bottomPlacement ? 'light' : 'dark') : 'white'}
            wholeContainerStyle={buttonStyles}>
            Sort
          </Button>
        </Layout>
      ) : null}
    </Layout>
  );
}

const styles = StyleSheet.create({
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    borderBottomColor: color.light,
    borderBottomWidth: 1,
  },
  innerContainer: {
    backgroundColor: color.white,
    flex: 1,
    borderRightColor: color.light,
    borderRightWidth: 1,
  },
  view: {
    justifyContent: 'flex-end',
    marginVertical: 0,
    marginHorizontal: 1,
  },
  modalBody: {
    backgroundColor: color.white,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
    flexDirection: 'column',
    height: '85%',
  },
  modalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: color.light,
    padding: spacing.base,
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
  },
  modalContent: {
    padding: spacing.md,
    flexDirection: 'column',
    width: '100%',
  },
  modalFooter: {
    width: '100%',
    backgroundColor: color.white,
    padding: spacing.base,
  },
  headerItems: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  centerView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  sortModalBody: {
    width: '80%',
    backgroundColor: color.white,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: spacing.small,
    flexDirection: 'column',
  },
  sortModalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: color.light,
    padding: spacing.base,
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
  },
  sortModalContent: {
    width: '100%',
    padding: spacing.base,
  },
  sortItem: {
    marginVertical: spacing.nano,
  },
  sliderContainer: {
    alignItems: 'center',
    paddingVertical: spacing.base,
  },
  sliderLabels: {
    flexDirection: 'row',
    width: 310,
    justifyContent: 'space-between',
  },
  trackStyle: {
    borderRadius: spacing.small,
    height: spacing.nano / 2,
  },
  slideSelectedStyle: {
    backgroundColor: color.primary,
  },
  markerStyle: {
    height: 30,
    width: 30,
    borderRadius: spacing.md,
    backgroundColor: color.light,
    borderWidth: 0.5,
    borderColor: color.demiDark,
  },
  markerPressed: {
    backgroundColor: color.grey,
  },
});
