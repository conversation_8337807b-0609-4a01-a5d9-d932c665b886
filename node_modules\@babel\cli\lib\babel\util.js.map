{"version": 3, "names": ["_fsReaddirRecursive", "data", "require", "babel", "_path", "_fs", "watcher", "asyncGeneratorStep", "gen", "resolve", "reject", "_next", "_throw", "key", "arg", "info", "value", "error", "done", "Promise", "then", "_asyncToGenerator", "fn", "self", "args", "arguments", "apply", "err", "undefined", "chmod", "src", "dest", "fs", "chmodSync", "statSync", "mode", "console", "warn", "readdir", "dirname", "includeDotfiles", "filter", "readdirRecursive", "filename", "index", "currentDirectory", "stat", "path", "join", "isDirectory", "readdirForCompilable", "altExts", "isCompilableExtension", "exts", "DEFAULT_EXTENSIONS", "ext", "extname", "includes", "addSourceMappingUrl", "code", "loc", "basename", "hasDataSourcemap", "pos", "lastIndexOf", "length", "CALLER", "name", "transformRepl", "opts", "Object", "assign", "caller", "transform", "result", "compile", "_x", "_x2", "_compile", "transformFile", "externalDependencies", "updateExternalDependencies", "deleteDir", "existsSync", "readdirSync", "for<PERSON>ach", "file", "curPath", "lstatSync", "unlinkSync", "rmdirSync", "process", "on", "exitCode", "withExtension", "newBasename", "debounce", "time", "timer", "debounced", "clearTimeout", "setTimeout", "flush"], "sources": ["../../src/babel/util.ts"], "sourcesContent": ["import readdirRecursive from \"fs-readdir-recursive\";\nimport * as babel from \"@babel/core\";\nimport path from \"path\";\nimport fs from \"fs\";\n\nimport * as watcher from \"./watcher.ts\";\n\nimport type { FileResult, InputOptions } from \"@babel/core\";\n\nexport function chmod(src: string, dest: string): void {\n  try {\n    fs.chmodSync(dest, fs.statSync(src).mode);\n  } catch (err) {\n    console.warn(`Cannot change permissions of ${dest}`);\n  }\n}\n\ntype ReaddirFilter = (filename: string) => boolean;\n\nexport function readdir(\n  dirname: string,\n  includeDotfiles: boolean,\n  filter?: ReaddirFilter,\n): Array<string> {\n  return readdirRecursive(dirname, (filename, index, currentDirectory) => {\n    const stat = fs.statSync(path.join(currentDirectory, filename));\n\n    if (stat.isDirectory()) return true;\n\n    return (\n      (includeDotfiles || filename[0] !== \".\") && (!filter || filter(filename))\n    );\n  });\n}\n\nexport function readdirForCompilable(\n  dirname: string,\n  includeDotfiles: boolean,\n  altExts?: Array<string>,\n): Array<string> {\n  return readdir(dirname, includeDotfiles, function (filename) {\n    return isCompilableExtension(filename, altExts);\n  });\n}\n\n/**\n * Test if a filename ends with a compilable extension.\n */\nexport function isCompilableExtension(\n  filename: string,\n  altExts?: readonly string[],\n): boolean {\n  const exts = altExts || babel.DEFAULT_EXTENSIONS;\n  const ext = path.extname(filename);\n  return exts.includes(ext);\n}\n\nexport function addSourceMappingUrl(code: string, loc: string): string {\n  return code + \"\\n//# sourceMappingURL=\" + path.basename(loc);\n}\n\nexport function hasDataSourcemap(code: string): boolean {\n  const pos = code.lastIndexOf(\"\\n\", code.length - 2);\n  return pos != -1 && code.lastIndexOf(\"//# sourceMappingURL\") < pos;\n}\n\nconst CALLER = {\n  name: \"@babel/cli\",\n};\n\nexport function transformRepl(filename: string, code: string, opts: any) {\n  opts = {\n    ...opts,\n    caller: CALLER,\n    filename,\n  };\n\n  return new Promise<FileResult>((resolve, reject) => {\n    babel.transform(code, opts, (err, result) => {\n      if (err) reject(err);\n      else resolve(result);\n    });\n  });\n}\n\nexport async function compile(filename: string, opts: InputOptions) {\n  opts = {\n    ...opts,\n    caller: CALLER,\n  };\n\n  const result = process.env.BABEL_8_BREAKING\n    ? await babel.transformFileAsync(filename, opts)\n    : await new Promise<FileResult>((resolve, reject) => {\n        babel.transformFile(filename, opts, (err, result) => {\n          if (err) reject(err);\n          else resolve(result);\n        });\n      });\n\n  if (result) {\n    if (!process.env.BABEL_8_BREAKING) {\n      if (!result.externalDependencies) return result;\n    }\n    watcher.updateExternalDependencies(filename, result.externalDependencies);\n  }\n\n  return result;\n}\n\nexport function deleteDir(path: string): void {\n  if (fs.existsSync(path)) {\n    fs.readdirSync(path).forEach(function (file) {\n      const curPath = path + \"/\" + file;\n      if (fs.lstatSync(curPath).isDirectory()) {\n        // recurse\n        deleteDir(curPath);\n      } else {\n        // delete file\n        fs.unlinkSync(curPath);\n      }\n    });\n    fs.rmdirSync(path);\n  }\n}\n\nprocess.on(\"uncaughtException\", function (err) {\n  console.error(err);\n  process.exitCode = 1;\n});\n\nexport function withExtension(filename: string, ext: string = \".js\") {\n  const newBasename = path.basename(filename, path.extname(filename)) + ext;\n  return path.join(path.dirname(filename), newBasename);\n}\n\nexport function debounce(fn: () => void, time: number) {\n  let timer: NodeJS.Timeout;\n  function debounced() {\n    clearTimeout(timer);\n    timer = setTimeout(fn, time);\n  }\n  debounced.flush = () => {\n    clearTimeout(timer);\n    fn();\n  };\n  return debounced;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,SAAAA,oBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,mBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,IAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,GAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAK,OAAA,GAAAJ,OAAA;AAAwC,SAAAK,mBAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAC,GAAA,cAAAC,IAAA,GAAAP,GAAA,CAAAK,GAAA,EAAAC,GAAA,OAAAE,KAAA,GAAAD,IAAA,CAAAC,KAAA,WAAAC,KAAA,IAAAP,MAAA,CAAAO,KAAA,iBAAAF,IAAA,CAAAG,IAAA,IAAAT,OAAA,CAAAO,KAAA,YAAAG,OAAA,CAAAV,OAAA,CAAAO,KAAA,EAAAI,IAAA,CAAAT,KAAA,EAAAC,MAAA;AAAA,SAAAS,kBAAAC,EAAA,6BAAAC,IAAA,SAAAC,IAAA,GAAAC,SAAA,aAAAN,OAAA,WAAAV,OAAA,EAAAC,MAAA,QAAAF,GAAA,GAAAc,EAAA,CAAAI,KAAA,CAAAH,IAAA,EAAAC,IAAA,YAAAb,MAAAK,KAAA,IAAAT,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,UAAAI,KAAA,cAAAJ,OAAAe,GAAA,IAAApB,kBAAA,CAAAC,GAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,MAAA,WAAAe,GAAA,KAAAhB,KAAA,CAAAiB,SAAA;AAIjC,SAASC,KAAKA,CAACC,GAAW,EAAEC,IAAY,EAAQ;EACrD,IAAI;IACFC,IAACA,CAAC,CAACC,SAAS,CAACF,IAAI,EAAEC,IAACA,CAAC,CAACE,QAAQ,CAACJ,GAAG,CAAC,CAACK,IAAI,CAAC;EAC3C,CAAC,CAAC,OAAOR,GAAG,EAAE;IACZS,OAAO,CAACC,IAAI,CAAE,gCAA+BN,IAAK,EAAC,CAAC;EACtD;AACF;AAIO,SAASO,OAAOA,CACrBC,OAAe,EACfC,eAAwB,EACxBC,MAAsB,EACP;EACf,OAAOC,oBAAeA,CAAC,CAACH,OAAO,EAAE,CAACI,QAAQ,EAAEC,KAAK,EAAEC,gBAAgB,KAAK;IACtE,MAAMC,IAAI,GAAGd,IAACA,CAAC,CAACE,QAAQ,CAACa,MAAGA,CAAC,CAACC,IAAI,CAACH,gBAAgB,EAAEF,QAAQ,CAAC,CAAC;IAE/D,IAAIG,IAAI,CAACG,WAAW,CAAC,CAAC,EAAE,OAAO,IAAI;IAEnC,OACE,CAACT,eAAe,IAAIG,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,CAACF,MAAM,IAAIA,MAAM,CAACE,QAAQ,CAAC,CAAC;EAE7E,CAAC,CAAC;AACJ;AAEO,SAASO,oBAAoBA,CAClCX,OAAe,EACfC,eAAwB,EACxBW,OAAuB,EACR;EACf,OAAOb,OAAO,CAACC,OAAO,EAAEC,eAAe,EAAE,UAAUG,QAAQ,EAAE;IAC3D,OAAOS,qBAAqB,CAACT,QAAQ,EAAEQ,OAAO,CAAC;EACjD,CAAC,CAAC;AACJ;AAKO,SAASC,qBAAqBA,CACnCT,QAAgB,EAChBQ,OAA2B,EAClB;EACT,MAAME,IAAI,GAAGF,OAAO,IAAIhD,KAAK,CAAD,CAAC,CAACmD,kBAAkB;EAChD,MAAMC,GAAG,GAAGR,MAAGA,CAAC,CAACS,OAAO,CAACb,QAAQ,CAAC;EAClC,OAAOU,IAAI,CAACI,QAAQ,CAACF,GAAG,CAAC;AAC3B;AAEO,SAASG,mBAAmBA,CAACC,IAAY,EAAEC,GAAW,EAAU;EACrE,OAAOD,IAAI,GAAG,yBAAyB,GAAGZ,MAAGA,CAAC,CAACc,QAAQ,CAACD,GAAG,CAAC;AAC9D;AAEO,SAASE,gBAAgBA,CAACH,IAAY,EAAW;EACtD,MAAMI,GAAG,GAAGJ,IAAI,CAACK,WAAW,CAAC,IAAI,EAAEL,IAAI,CAACM,MAAM,GAAG,CAAC,CAAC;EACnD,OAAOF,GAAG,IAAI,CAAC,CAAC,IAAIJ,IAAI,CAACK,WAAW,CAAC,sBAAsB,CAAC,GAAGD,GAAG;AACpE;AAEA,MAAMG,MAAM,GAAG;EACbC,IAAI,EAAE;AACR,CAAC;AAEM,SAASC,aAAaA,CAACzB,QAAgB,EAAEgB,IAAY,EAAEU,IAAS,EAAE;EACvEA,IAAI,GAAAC,MAAA,CAAAC,MAAA,KACCF,IAAI;IACPG,MAAM,EAAEN,MAAM;IACdvB;EAAQ,EACT;EAED,OAAO,IAAIxB,OAAO,CAAa,CAACV,OAAO,EAAEC,MAAM,KAAK;IAClDP,KAAK,CAAD,CAAC,CAACsE,SAAS,CAACd,IAAI,EAAEU,IAAI,EAAE,CAAC1C,GAAG,EAAE+C,MAAM,KAAK;MAC3C,IAAI/C,GAAG,EAAEjB,MAAM,CAACiB,GAAG,CAAC,CAAC,KAChBlB,OAAO,CAACiE,MAAM,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAAC,SAEqBC,OAAOA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,QAAA,CAAApD,KAAA,OAAAD,SAAA;AAAA;AAAA,SAAAqD,SAAA;EAAAA,QAAA,GAAAzD,iBAAA,CAAtB,WAAuBsB,QAAgB,EAAE0B,IAAkB,EAAE;IAClEA,IAAI,GAAAC,MAAA,CAAAC,MAAA,KACCF,IAAI;MACPG,MAAM,EAAEN;IAAM,EACf;IAED,MAAMQ,MAAM,SAEF,IAAIvD,OAAO,CAAa,CAACV,OAAO,EAAEC,MAAM,KAAK;MACjDP,KAAK,CAAD,CAAC,CAAC4E,aAAa,CAACpC,QAAQ,EAAE0B,IAAI,EAAE,CAAC1C,GAAG,EAAE+C,MAAM,KAAK;QACnD,IAAI/C,GAAG,EAAEjB,MAAM,CAACiB,GAAG,CAAC,CAAC,KAChBlB,OAAO,CAACiE,MAAM,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEN,IAAIA,MAAM,EAAE;MACyB;QACjC,IAAI,CAACA,MAAM,CAACM,oBAAoB,EAAE,OAAON,MAAM;MACjD;MACApE,OAAO,CAAC2E,0BAA0B,CAACtC,QAAQ,EAAE+B,MAAM,CAACM,oBAAoB,CAAC;IAC3E;IAEA,OAAON,MAAM;EACf,CAAC;EAAA,OAAAI,QAAA,CAAApD,KAAA,OAAAD,SAAA;AAAA;AAEM,SAASyD,SAASA,CAACnC,IAAY,EAAQ;EAC5C,IAAIf,IAACA,CAAC,CAACmD,UAAU,CAACpC,IAAI,CAAC,EAAE;IACvBf,IAACA,CAAC,CAACoD,WAAW,CAACrC,IAAI,CAAC,CAACsC,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC3C,MAAMC,OAAO,GAAGxC,IAAI,GAAG,GAAG,GAAGuC,IAAI;MACjC,IAAItD,IAACA,CAAC,CAACwD,SAAS,CAACD,OAAO,CAAC,CAACtC,WAAW,CAAC,CAAC,EAAE;QAEvCiC,SAAS,CAACK,OAAO,CAAC;MACpB,CAAC,MAAM;QAELvD,IAACA,CAAC,CAACyD,UAAU,CAACF,OAAO,CAAC;MACxB;IACF,CAAC,CAAC;IACFvD,IAACA,CAAC,CAAC0D,SAAS,CAAC3C,IAAI,CAAC;EACpB;AACF;AAEA4C,OAAO,CAACC,EAAE,CAAC,mBAAmB,EAAE,UAAUjE,GAAG,EAAE;EAC7CS,OAAO,CAACnB,KAAK,CAACU,GAAG,CAAC;EAClBgE,OAAO,CAACE,QAAQ,GAAG,CAAC;AACtB,CAAC,CAAC;AAEK,SAASC,aAAaA,CAACnD,QAAgB,EAAEY,GAAW,GAAG,KAAK,EAAE;EACnE,MAAMwC,WAAW,GAAGhD,MAAGA,CAAC,CAACc,QAAQ,CAAClB,QAAQ,EAAEI,MAAGA,CAAC,CAACS,OAAO,CAACb,QAAQ,CAAC,CAAC,GAAGY,GAAG;EACzE,OAAOR,MAAGA,CAAC,CAACC,IAAI,CAACD,MAAGA,CAAC,CAACR,OAAO,CAACI,QAAQ,CAAC,EAAEoD,WAAW,CAAC;AACvD;AAEO,SAASC,QAAQA,CAAC1E,EAAc,EAAE2E,IAAY,EAAE;EACrD,IAAIC,KAAqB;EACzB,SAASC,SAASA,CAAA,EAAG;IACnBC,YAAY,CAACF,KAAK,CAAC;IACnBA,KAAK,GAAGG,UAAU,CAAC/E,EAAE,EAAE2E,IAAI,CAAC;EAC9B;EACAE,SAAS,CAACG,KAAK,GAAG,MAAM;IACtBF,YAAY,CAACF,KAAK,CAAC;IACnB5E,EAAE,CAAC,CAAC;EACN,CAAC;EACD,OAAO6E,SAAS;AAClB"}