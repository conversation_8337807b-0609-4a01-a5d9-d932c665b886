import { appmaker } from '@appmaker-xyz/core';
import RNRestart from 'react-native-restart';
import axios from 'axios';
import { appStorageApi } from '@appmaker-xyz/core';

async function getConversionRate(currentCurrency, targetCurrency) {
  const { data } = await axios.get(
    `https://currency-value.mobgap.workers.dev/?currency=${currentCurrency}`,
  );
  return data.rates[targetCurrency];
}

export async function SET_CURRENCY(
  { action, params },
  { handleAction, appStorageState, coreDispatch },
) {
  const currentCurrency = appStorageApi().getState().appmaker_currency_switcher;
  params.conversion_rate = await getConversionRate(
    currentCurrency.default_currency,
    params.currency_code,
  );
  coreDispatch({
    type: 'SET_APP_VAR_LOCAL',
    name: 'appmaker_currency_switcher',
    value: { ...params },
  });
  RNRestart.Restart();
}

export function OPEN_CURRENCY_SWITCHER(
  { action, ...params },
  { handleAction, appStorageState },
) {
  handleAction({
    action: 'OPEN_INAPP_PAGE',
    pageId: 'ShopifyCurrencySwitcher',
  });
}

export function registerActions() {
  appmaker.actions.registerAction(
    'OPEN_CURRENCY_SWITCHER',
    OPEN_CURRENCY_SWITCHER,
  );
  appmaker.actions.registerAction('SET_CURRENCY', SET_CURRENCY);
}
