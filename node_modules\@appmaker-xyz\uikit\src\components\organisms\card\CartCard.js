import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { emitEvent } from '@appmaker-xyz/core';
import { appmaker } from '@appmaker-xyz/core';
import { useCartProduct } from '@appmaker-xyz/shopify/hooks/useCartProduct';

import { useApThemeState } from '../../../theme/ThemeContext';
import {
  Layout,
  AppmakerText,
  AppImage,
  Button,
  AppTouchable,
  Stepper,
} from '../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';

function SpecialTextBadge({ styles, text }) {
  const SpecialText = appmaker.applyFilters(
    'appmaker-cart-product-card-review-special-text',
    () => null,
  );
  if (SpecialText()) {
    return SpecialText;
  }

  return (
    <Layout style={styles.insiderBadge}>
      <Icon name="tag" size={12} color="#3b3b3b" style={styles.tagicon} />
      <AppmakerText fontColor="#3b3b3b" category="smallButtonText">
        {text}
      </AppmakerText>
    </Layout>
  );
}

const CartCard = (props) => {
  const { attributes, onPress, onAction, data, pageDispatch } = props;
  const {
    featureImg,
    specialText,
    insider,
    productType,
    count,
    appmakerAction,
    variation,
    aditionalInfo,
    min_value,
    max_value,
    product,
    currency,
    label = '',
    input_value,
    step,
    type,
    displayQuantity = true,
    priceSale = '',
    discountTotal,
    __appmakerCustomStyles,
  } = attributes;
  const {
    title,
    imageUri,
    variationText,
    regularPrice,
    salePrice,
    isFreeGift,
    onSale,
    unitRegularPrice,
    unitSalePrice,
    quantity,
    onRemoveItem,
    removeLoading,
    onQuantityChange,
    quantityLoading,
    itemSavingsWithCurrency,
    itemSavingsAmount,
    variantLineItemRegularPriceWithCurrency,
    variantLineItemSalePriceWithCurrency,
    discountTitle,
    canUpdateQuantity = true,
    canRemoveItem = true,
  } = useCartProduct(props);
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  const { color, spacing } = useApThemeState();

  const styles = allStyles({ color, spacing });
  const strikePrice = isFreeGift ? { ...styles.freeStrike } : {};
  console.log('onSale', onSale);
  return (
    <Layout style={[styles.productCard, __appmakerCustomStyles?.container]}>
      <Layout flexDirection="row">
        <AppTouchable onPress={onPressHandle}>
          <AppImage
            uri={featureImg}
            style={[styles.imageStyles, __appmakerCustomStyles?.image]}
          />
        </AppTouchable>
        <Layout flex={1}>
          <Layout flexDirection="row" flex={1}>
            <Layout
              style={[styles.cardContent, __appmakerCustomStyles?.cardContent]}>
              <AppmakerText
                category="bodyParagraph"
                status="demiDark"
                numberOfLines={2}
                style={__appmakerCustomStyles?.title}>
                {title}
              </AppmakerText>
              {variation ? (
                <Layout
                  style={[
                    styles.metaContainer,
                    __appmakerCustomStyles?.metaContainer,
                  ]}>
                  <Layout
                    style={[styles.metaItem, __appmakerCustomStyles?.metaItem]}>
                    <Icon
                      name="tag"
                      size={spacing.base}
                      color={color.grey}
                      style={[
                        styles.metaIcon,
                        __appmakerCustomStyles?.metaIcon,
                      ]}
                    />
                    <AppmakerText
                      category="highlighter2"
                      status="demiDark"
                      style={__appmakerCustomStyles?.variationText}>
                      {variation}
                    </AppmakerText>
                  </Layout>
                </Layout>
              ) : null}
              {aditionalInfo ? (
                <Layout
                  style={[
                    styles.metaContainer,
                    __appmakerCustomStyles?.metaContainer,
                  ]}>
                  <Layout
                    style={[styles.metaItem, __appmakerCustomStyles?.metaItem]}>
                    <Icon
                      name="tag"
                      size={spacing.base}
                      color={color.grey}
                      style={[
                        styles.metaIcon,
                        __appmakerCustomStyles?.metaIcon,
                      ]}
                    />
                    <AppmakerText
                      category="highlighter2"
                      status="demiDark"
                      style={__appmakerCustomStyles?.variationText}>
                      {aditionalInfo}
                    </AppmakerText>
                  </Layout>
                </Layout>
              ) : null}
              {discountTitle ? (
                <Layout
                  style={[
                    styles.metaContainer,
                    __appmakerCustomStyles?.metaContainer,
                  ]}>
                  {discountTitle && discountTitle !== '' ? (
                    <Layout
                      style={[
                        styles.metaItem,
                        __appmakerCustomStyles?.metaItem,
                      ]}>
                      <Icon
                        name="tag"
                        size={spacing.base}
                        color={color.grey}
                        style={[
                          styles.metaIcon,
                          __appmakerCustomStyles?.metaIcon,
                        ]}
                      />
                      <AppmakerText
                        category="highlighter2"
                        status="demiDark"
                        style={[__appmakerCustomStyles?.variationText]}>
                        {discountTitle}
                      </AppmakerText>
                    </Layout>
                  ) : null}
                </Layout>
              ) : null}
            </Layout>
            <Layout
              style={[
                styles.priceData,
                __appmakerCustomStyles?.priceDataContainer,
              ]}>
              <AppmakerText
                category="h1Heading"
                status={isFreeGift ? 'danger' : 'dark'}
                style={{
                  ...__appmakerCustomStyles?.salePriceText,
                }}>
                {isFreeGift ? 'Free' : salePrice}
              </AppmakerText>
              {onSale && !isFreeGift ? (
                <AppmakerText
                  category="highlighter1"
                  // status="success"
                  style={styles.strike}>
                  {regularPrice}
                </AppmakerText>
              ) : null}
              {/* {itemSavingsAmount > 0 && !isFreeGift ? (
                <AppmakerText category="highlighter1">
                  - {itemSavingsWithCurrency}
                </AppmakerText>
              ) : null} */}
              {/* {onSale ? (
                <AppmakerText category="highlighter1" style={styles.strike}>
                  {regularPrice}
                </AppmakerText>
              ) : null} */}
              {isFreeGift ? (
                <AppmakerText
                  category="highlighter1"
                  status="danger"
                  style={styles.strike}>
                  {regularPrice}
                </AppmakerText>
              ) : null}
            </Layout>
          </Layout>
          {specialText ? (
            <SpecialTextBadge styles={styles} text={specialText} />
          ) : null}
          <Layout flexDirection="row" justifyContent="space-between">
            <Layout>
              {canUpdateQuantity ? (
                <Stepper
                  onChange={onQuantityChange}
                  count={count}
                  minValue={min_value}
                  maxValue={max_value}
                  step={step}
                  displayCount={count}
                  adding={quantityLoading}
                  themeColor={
                    __appmakerCustomStyles?.stepper ? null : color.demiDark
                  }
                  __appmakerCustomStyles={__appmakerCustomStyles?.stepper}
                />
              ) : null}
            </Layout>
            {canRemoveItem ? (
              <Button
                loading={removeLoading}
                onPress={onRemoveItem}
                status="demiDark"
                small
                outline
                wholeContainerStyle={[
                  __appmakerCustomStyles?.removeButton,
                  styles.trashButton,
                ]}>
                <Icon
                  name="trash-2"
                  size={19}
                  color={color.demiDark}
                  style={__appmakerCustomStyles?.removeButtonIcon}
                />
              </Button>
            ) : null}
          </Layout>
        </Layout>
      </Layout>
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    productCard: {
      backgroundColor: color.white,
      padding: spacing.base,
      borderBottomColor: color.light,
      borderBottomWidth: 1,
    },
    imageStyles: {
      width: 100,
      height: 100,
      borderRadius: spacing.base,
      marginRight: spacing.base,
    },
    cardContent: {
      flex: 1,
      marginBottom: spacing.base,
    },
    metaContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.base,
    },
    metaItem: {
      flexDirection: 'row',
      marginRight: spacing.base,
    },
    metaIcon: {
      marginRight: spacing.small,
      paddingTop: 2,
    },
    priceData: {
      alignItems: 'flex-end',
    },
    strike: {
      textDecorationLine: 'line-through',
    },
    freeStrike: {
      textDecorationLine: 'line-through',
      color: color.danger,
    },
    bottomPart: {
      backgroundColor: '#ffffff',
      borderTopColor: '#E9EDF1',
      borderTopWidth: 1,
    },
    checkoutButton: {
      paddingHorizontal: spacing.base,
      paddingBottom: spacing.base,
    },
    insiderBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 6,
      paddingVertical: 2,
      backgroundColor: '#E7E7E7',
      marginBottom: 6,
    },
    tagicon: {
      marginRight: 4,
    },
    trashButton: {
      alignSelf: 'flex-end',
    },
    freeBadgeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    freeGiftBadge: {
      backgroundColor: `${color.danger}1A`,
      paddingHorizontal: 4,
      paddingVertical: 2,
      borderRadius: 4,
      overflow: 'hidden',
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.base,
    },
    giftIcon: {
      marginRight: 4,
    },
  });

export default CartCard;
