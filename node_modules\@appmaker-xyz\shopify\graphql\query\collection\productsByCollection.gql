query ProductByCollectionHandle(
  $imageWidth: Int = 800
  $handle: String!
  $after: String
  $first: Int = 20
  $filters: [ProductFilter!] = {}
  $sortKey: ProductCollectionSortKeys
) {
  collection(handle: $handle) {
    id
    title
    image {
      url(transform: { maxWidth: $imageWidth })
    }
    products(
      first: $first
      after: $after
      filters: $filters
      sortKey: $sortKey
    ) {
      pageInfo {
        hasNextPage
        hasPreviousPage
        endCursor
      }
      edges {
        cursor
        node {
          ...SimpleProductFragment
        }
      }
      filters {
        ...ShopifyFilters
      }
    }
  }
}
