import React, { useEffect, useMemo, useState } from 'react';
import { Pressable, FlatList, StyleSheet } from 'react-native';
import Modal from 'react-native-modal';
import Icon from 'react-native-vector-icons/Feather';
import { Input, ThemeText, Layout } from '@appmaker-xyz/ui';
import { useStyles } from '@appmaker-xyz/react-native';

const ModalSelector = ({
  open,
  onOpen,
  setOpen,
  value,
  items,
  setValue,
  label,
  error, 
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const {  theme } = useStyles(styles);  

  useEffect(() => {
    if (open) {
      onOpen();
    }
    setSearchTerm('');
  }, [open, onOpen]);

  const handleSelect = (itemValue) => {
    setValue(itemValue);
    setOpen(false);
  };

  const renderOption = ({ item }) => (
    <Pressable onPress={() => handleSelect(item.label)} style={styles.listItem}>
      <ThemeText fontFamily="medium">{item.label}</ThemeText>
      {item.value === value ? (
        <Icon
          name="check"
          size={16}
          color="#ffffff"
          style={styles.selectedIcon}
        />
      ) : null}
    </Pressable>
  );

  const filteredData = useMemo(
    () =>
      items.filter((item) =>
        item.label.toLowerCase().includes(searchTerm.toLowerCase()),
      ),
    [items, searchTerm],
  );

  return (
    <Layout>
      <Pressable onPress={() => setOpen(true)} style={styles.toggleButton}>
        <ThemeText>{value || label}</ThemeText>
        <Icon name="chevron-down" size={20} />
      </Pressable>

      <Modal isVisible={open} style={styles.modalContainer}>
        <Layout style={styles.modalHeader}>
          <Input
            placeholder="Search..."
            onChangeText={setSearchTerm}
            style={styles.searchInput}
          />
          <Pressable onPress={() => setOpen(false)}>
            <Icon name="x" size={20} />
          </Pressable>
        </Layout>
        <FlatList
          keyboardShouldPersistTaps="always"
          data={filteredData}
          renderItem={renderOption}
          keyExtractor={(item) => item.id}
        />
      </Modal>

      {error && (
        <ThemeText size="sm" color={theme.colors.error}>
          {error}
        </ThemeText>
      )}
    </Layout>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    margin: 0,
    backgroundColor: 'white',
    marginTop: 40,
  },
  listItem: {
    padding: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E8ECF4',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  searchInput: {
    marginTop: 12,
    marginRight: 10,
  },
  toggleButton: {
    padding: 10,
    backgroundColor: '#E8ECF4',
    borderRadius: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10
  },
  selectedIcon: {
    backgroundColor: '#000000',
    borderRadius: 10,
    padding: 2,
  },
});

export default ModalSelector;
