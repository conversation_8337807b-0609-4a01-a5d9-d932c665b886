import { addFilter } from '@appmaker-xyz/core';

export const activateImageSizeFilter = ({ settings }) => {
  const imageSizesSettings = settings?.image_size;
  let imageSizes = {};
  imageSizesSettings?.forEach?.((item) => {
    imageSizes[item?.image_context] = item;
  });
  addFilter('appmaker-image-sizes', 'appmaker-shopify', (size, context) => {
    try {
      const imageSize = imageSizes?.[context?.type] || {};
      const sizeData = {};

      if (typeof imageSize?.max_width === 'string') {
        sizeData.width = parseInt(imageSize.max_width);
      }

      if (typeof imageSize?.max_height === 'string') {
        sizeData.height = parseInt(imageSize.max_height);
      }

      if (sizeData.height || sizeData.width) {
        return sizeData;
      }
    } catch (e) {}

    return getDefaultSize(context);
  });
};

const getDefaultSize = (context) => {
  if (context?.type === 'collection-page-product') {
    return {
      width: 540,
    };
  }
  return {
    width: 540,
  };
};
