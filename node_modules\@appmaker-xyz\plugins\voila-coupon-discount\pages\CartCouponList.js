import { styles } from '@appmaker-xyz/uikit';
const { color, spacing } = styles;

const page = {
  title: 'Apply Coupon',
  attributes: {
    // headerShown: false,
    rootContainerStyle: {
      backgroundColor: color.white,
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      paddingHorizontal: spacing.base,
      paddingTop: spacing.base,
    },
  },
  blocks: [
    {
      name: 'appmaker/CartCoupon',
      attributes: {
        __display:
          '{{checkIfTrueFalse(plugins.shopify.settings.show_apply_coupon)}}',
        defaultEditMode: true,
        couponDiscounted: '{{blockData.discountCodes}}', // to be done

        __appmakerStylesClassName: 'cartCouponCustomStyle',
      },
    },
    {
      name: 'appmaker/blocksView',
      attributes: {
        headerShown: false,
        rootContainerStyle: {
          marginTop: spacing.base,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/text',
          attributes: {
            content: 'Available Coupons',
            category: 'bodyParagraph',
            status: 'demiDark',
          },
        },
        {
          name: 'appmaker/coupon-block',
          attributes: {
            couponCode: '{{blockItem.dropdown_cpn_code}}',
            description: '{{blockItem.dropdown_cpn_text}}',
            appmakerAction: {
              action: 'APPLY_COUPON',
              params: {
                coupon: '{{blockItem.dropdown_cpn_code}}',
              },
            },
            dataSource: {
              source: 'voila',
              attributes: {
                mapping: {
                  items: 'drp_cpns',
                },
                methodName: 'getCouponList',
                params:
                  "<%=plugins['voila-coupon-discount'].settings.shop_domain%>",
              },
              responseType: 'replace',
              repeatable: 'Yes',
              repeatItem: 'DataSource',
            },
          },
        },
      ],
    },
  ],
};
export default page;
