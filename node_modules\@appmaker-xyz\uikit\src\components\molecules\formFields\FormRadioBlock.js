import React, { useState, useEffect } from 'react';
import { StyleSheet } from 'react-native';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { Layout, AppmakerText, AppTouchable } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import { usePageState } from '@appmaker-xyz/core';

const ButtonComponent = ({ attributes, coreDispatch }) => {
  let { label, id, additionalText, name, value } = attributes;
  const { color, spacing } = useApThemeState();
  const [active, setActive] = useState(false);
  const styles = allStyles({ color, spacing, active });
  const inputValue = usePageState((state) => {
    return state?._formData && state._formData[name]
      ? state._formData[name]
      : '';
  });
  function selectItem() {
    coreDispatch({
      type: 'SET_VALUE',
      name,
      value: id,
      parent: '_formData',
    });
    if (value) {
      coreDispatch({
        type: 'SET_VALUE',
        name: `${name}_value`,
        value: value,
        parent: '_formData',
      });
    }
  }
  useEffect(() => {
    setActive(inputValue === id);
  }, [inputValue, id]);
  return (
    <AppTouchable style={styles.button} onPress={() => selectItem()}>
      <Layout style={styles.iconContainer}>
        <Icon
          name={active ? 'check-circle' : 'circle'}
          size={spacing.lg}
          color={active ? color.white : color.grey}
          style={styles.icon}
        />
      </Layout>
      <Layout style={styles.content}>
        <AppmakerText category="bodyParagraphBold" style={styles.mb}>
          {label}
        </AppmakerText>
        {additionalText ? (
          <AppmakerText style={styles.mb}>{additionalText}</AppmakerText>
        ) : null}
      </Layout>
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color, active }) =>
  StyleSheet.create({
    button: {
      backgroundColor: active ? `${color.primary}0D` : `${color.grey}0D`,
      paddingHorizontal: spacing.base,
      paddingVertical: spacing.base,
      borderBottomColor: color.light,
      borderBottomWidth: 1,
      flexDirection: 'row',
    },
    iconContainer: {
      paddingRight: spacing.base,
      paddingTop: spacing.nano,
    },
    icon: {
      backgroundColor: active ? color.primary : color.white,
      borderRadius: spacing.base,
      overflow: 'hidden',
    },
    content: {
      flex: 1,
    },
    mb: {
      marginBottom: spacing.nano,
      marginTop: 3,
    },
  });

export default ButtonComponent;
