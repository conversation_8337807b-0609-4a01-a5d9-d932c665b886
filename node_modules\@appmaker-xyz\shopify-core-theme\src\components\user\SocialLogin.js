import React from 'react';
import { StyleSheet, Platform } from 'react-native';
import { Layout, ThemeText, AppTouchable } from '@appmaker-xyz/ui';
import { Apple, Facebook, Google } from '../../../assets/svg';

const SocialLogin = ({ socialButtons = [] }) => {
  return socialButtons.length > 0 ? (
    <Layout style={styles.container}>
      <Layout style={styles.header}>
        <Layout style={styles.line} />
        <ThemeText color="#6A707C">Continue with</ThemeText>
        <Layout style={styles.line} />
      </Layout>
      <Layout style={styles.socialButtons}>
        {socialButtons.map((ButtonItem) => {
          const { component: Component, name } = ButtonItem;
          return <Component key={name} />;
        })}
      </Layout>
    </Layout>
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  line: {
    height: 1,
    backgroundColor: '#E8ECF4',
    flex: 1,
    marginHorizontal: 8,
  },
  socialButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  socialButton: {
    flex: 1,
    marginHorizontal: 4,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E8ECF4',
    borderRadius: 6,
    paddingVertical: 12,
  },
});

export default SocialLogin;
