{"version": 3, "names": ["helpers", "__proto__", "generated", "helper", "minVersion", "tpl", "ast", "template", "program", "AwaitValue", "wrapAsyncGenerator", "asyncToGenerator", "classCallCheck", "createClass", "defineEnumerableProperties", "defaults", "defineProperty", "extends", "objectSpread", "inherits", "inherits<PERSON><PERSON><PERSON>", "getPrototypeOf", "setPrototypeOf", "isNativeReflectConstruct", "construct", "isNativeFunction", "wrapNativeSuper", "instanceof", "interopRequireDefault", "interopRequireWildcard", "newArrowCheck", "objectDestructuringEmpty", "objectWithoutPropertiesLoose", "objectWithoutProperties", "assertThisInitialized", "possibleConstructorReturn", "createSuper", "superPropBase", "get", "set", "taggedTemplateLiteral", "taggedTemplateLiteralLoose", "readOnlyError", "writeOnlyError", "classNameTDZError", "temporalUndefined", "tdz", "temporalRef", "slicedToArray", "slicedToArrayLoose", "toArray", "toConsumableArray", "arrayWithoutHoles", "arrayWithHoles", "maybeArrayLike", "iterableToArray", "unsupportedIterableToArray", "arrayLikeToArray", "nonIterableSpread", "nonIterableRest", "createForOfIteratorHelper", "createForOfIteratorHelperLoose", "skipFirstGeneratorNext", "toPrimitive", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initializerWarningHelper", "initializerDefineProperty", "applyDecoratedDescriptor", "classPrivateFieldLooseKey", "classPrivateFieldLooseBase", "classPrivateFieldGet", "classPrivateFieldSet", "classPrivateFieldDestructureSet", "classExtractFieldDescriptor", "classStaticPrivateFieldSpecGet", "classStaticPrivateFieldSpecSet", "classStaticPrivateMethodGet", "classStaticPrivateMethodSet", "classApplyDescriptorGet", "classApplyDescriptorSet", "classApplyDescriptorDestructureSet", "classStaticPrivateFieldDestructureSet", "classCheckPrivateStaticAccess", "classCheckPrivateStaticFieldDescriptor", "decorate", "classPrivateMethodGet", "checkPrivateRedeclaration", "classPrivateFieldInitSpec", "classPrivateMethodInitSpec", "classPrivateMethodSet", "identity"], "sources": ["../src/helpers.ts"], "sourcesContent": ["import template from \"@babel/template\";\nimport type * as t from \"@babel/types\";\n\nimport generated from \"./helpers-generated\";\n\ninterface Helper {\n  minVersion: string;\n  ast: () => t.Program;\n}\n\nconst helpers: Record<string, Helper> = { __proto__: null, ...generated };\nexport default helpers;\n\nconst helper = (minVersion: string) => (tpl: TemplateStringsArray) => ({\n  minVersion,\n  ast: () => template.program.ast(tpl),\n});\n\nif (!process.env.BABEL_8_BREAKING) {\n  helpers.AwaitValue = helper(\"7.0.0-beta.0\")`\n    export default function _AwaitValue(value) {\n      this.wrapped = value;\n    }\n  `;\n}\n\nhelpers.wrapAsyncGenerator = helper(\"7.0.0-beta.0\")`\n  import AsyncGenerator from \"AsyncGenerator\";\n\n  export default function _wrapAsyncGenerator(fn) {\n    return function () {\n      return new AsyncGenerator(fn.apply(this, arguments));\n    };\n  }\n`;\n\nhelpers.asyncToGenerator = helper(\"7.0.0-beta.0\")`\n  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n    try {\n      var info = gen[key](arg);\n      var value = info.value;\n    } catch (error) {\n      reject(error);\n      return;\n    }\n\n    if (info.done) {\n      resolve(value);\n    } else {\n      Promise.resolve(value).then(_next, _throw);\n    }\n  }\n\n  export default function _asyncToGenerator(fn) {\n    return function () {\n      var self = this, args = arguments;\n      return new Promise(function (resolve, reject) {\n        var gen = fn.apply(self, args);\n        function _next(value) {\n          asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n        }\n        function _throw(err) {\n          asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n        }\n\n        _next(undefined);\n      });\n    };\n  }\n`;\n\nhelpers.classCallCheck = helper(\"7.0.0-beta.0\")`\n  export default function _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n      throw new TypeError(\"Cannot call a class as a function\");\n    }\n  }\n`;\n\nhelpers.createClass = helper(\"7.0.0-beta.0\")`\n  import toPropertyKey from \"toPropertyKey\";\n  function _defineProperties(target, props) {\n    for (var i = 0; i < props.length; i ++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, toPropertyKey(descriptor.key), descriptor);\n    }\n  }\n\n  export default function _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    Object.defineProperty(Constructor, \"prototype\", { writable: false });\n    return Constructor;\n  }\n`;\n\nhelpers.defineEnumerableProperties = helper(\"7.0.0-beta.0\")`\n  export default function _defineEnumerableProperties(obj, descs) {\n    for (var key in descs) {\n      var desc = descs[key];\n      desc.configurable = desc.enumerable = true;\n      if (\"value\" in desc) desc.writable = true;\n      Object.defineProperty(obj, key, desc);\n    }\n\n    // Symbols are not enumerated over by for-in loops. If native\n    // Symbols are available, fetch all of the descs object's own\n    // symbol properties and define them on our target object too.\n    if (Object.getOwnPropertySymbols) {\n      var objectSymbols = Object.getOwnPropertySymbols(descs);\n      for (var i = 0; i < objectSymbols.length; i++) {\n        var sym = objectSymbols[i];\n        var desc = descs[sym];\n        desc.configurable = desc.enumerable = true;\n        if (\"value\" in desc) desc.writable = true;\n        Object.defineProperty(obj, sym, desc);\n      }\n    }\n    return obj;\n  }\n`;\n\nhelpers.defaults = helper(\"7.0.0-beta.0\")`\n  export default function _defaults(obj, defaults) {\n    var keys = Object.getOwnPropertyNames(defaults);\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n      var value = Object.getOwnPropertyDescriptor(defaults, key);\n      if (value && value.configurable && obj[key] === undefined) {\n        Object.defineProperty(obj, key, value);\n      }\n    }\n    return obj;\n  }\n`;\n\nhelpers.defineProperty = helper(\"7.0.0-beta.0\")`\n  import toPropertyKey from \"toPropertyKey\";\n  export default function _defineProperty(obj, key, value) {\n    key = toPropertyKey(key);\n    // Shortcircuit the slow defineProperty path when possible.\n    // We are trying to avoid issues where setters defined on the\n    // prototype cause side effects under the fast path of simple\n    // assignment. By checking for existence of the property with\n    // the in operator, we can optimize most of this overhead away.\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n`;\n\n// need a bind because https://github.com/babel/babel/issues/14527\nhelpers.extends = helper(\"7.0.0-beta.0\")`\n  export default function _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function (target) {\n      for (var i = 1; i < arguments.length; i++) {\n        var source = arguments[i];\n        for (var key in source) {\n          if (Object.prototype.hasOwnProperty.call(source, key)) {\n            target[key] = source[key];\n          }\n        }\n      }\n      return target;\n    };\n\n    return _extends.apply(this, arguments);\n  }\n`;\n\n// TODO(babel-8): This old helper can be removed in babel v8\nhelpers.objectSpread = helper(\"7.0.0-beta.0\")`\n  import defineProperty from \"defineProperty\";\n\n  export default function _objectSpread(target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = (arguments[i] != null) ? Object(arguments[i]) : {};\n      var ownKeys = Object.keys(source);\n      if (typeof Object.getOwnPropertySymbols === 'function') {\n        ownKeys.push.apply(ownKeys, Object.getOwnPropertySymbols(source).filter(function(sym) {\n          return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n        }));\n      }\n      ownKeys.forEach(function(key) {\n        defineProperty(target, key, source[key]);\n      });\n    }\n    return target;\n  }\n`;\n\nhelpers.inherits = helper(\"7.0.0-beta.0\")`\n  import setPrototypeOf from \"setPrototypeOf\";\n\n  export default function _inherits(subClass, superClass) {\n    if (typeof superClass !== \"function\" && superClass !== null) {\n      throw new TypeError(\"Super expression must either be null or a function\");\n    }\n    // We can't use defineProperty to set the prototype in a single step because it\n    // doesn't work in Chrome <= 36. https://github.com/babel/babel/issues/14056\n    // V8 bug: https://bugs.chromium.org/p/v8/issues/detail?id=3334\n    subClass.prototype = Object.create(superClass && superClass.prototype, {\n      constructor: {\n        value: subClass,\n        writable: true,\n        configurable: true\n      }\n    });\n    Object.defineProperty(subClass, \"prototype\", { writable: false });\n    if (superClass) setPrototypeOf(subClass, superClass);\n  }\n`;\n\nhelpers.inheritsLoose = helper(\"7.0.0-beta.0\")`\n  import setPrototypeOf from \"setPrototypeOf\";\n\n  export default function _inheritsLoose(subClass, superClass) {\n    subClass.prototype = Object.create(superClass.prototype);\n    subClass.prototype.constructor = subClass;\n    setPrototypeOf(subClass, superClass);\n  }\n`;\n\n// need a bind because https://github.com/babel/babel/issues/14527\nhelpers.getPrototypeOf = helper(\"7.0.0-beta.0\")`\n  export default function _getPrototypeOf(o) {\n    _getPrototypeOf = Object.setPrototypeOf\n      ? Object.getPrototypeOf.bind()\n      : function _getPrototypeOf(o) {\n          return o.__proto__ || Object.getPrototypeOf(o);\n        };\n    return _getPrototypeOf(o);\n  }\n`;\n\nhelpers.setPrototypeOf = helper(\"7.0.0-beta.0\")`\n  export default function _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf\n      ? Object.setPrototypeOf.bind()\n      : function _setPrototypeOf(o, p) {\n          o.__proto__ = p;\n          return o;\n        };\n    return _setPrototypeOf(o, p);\n  }\n`;\n\nhelpers.isNativeReflectConstruct = helper(\"7.9.0\")`\n  export default function _isNativeReflectConstruct() {\n    if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n\n    // core-js@3\n    if (Reflect.construct.sham) return false;\n\n    // Proxy can't be polyfilled. Every browser implemented\n    // proxies before or at the same time as Reflect.construct,\n    // so if they support Proxy they also support Reflect.construct.\n    if (typeof Proxy === \"function\") return true;\n\n    // Since Reflect.construct can't be properly polyfilled, some\n    // implementations (e.g. core-js@2) don't set the correct internal slots.\n    // Those polyfills don't allow us to subclass built-ins, so we need to\n    // use our fallback implementation.\n    try {\n      // If the internal slots aren't set, this throws an error similar to\n      //   TypeError: this is not a Boolean object.\n\n      Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }\n`;\n\n// need a bind because https://github.com/babel/babel/issues/14527\nhelpers.construct = helper(\"7.0.0-beta.0\")`\n  import setPrototypeOf from \"setPrototypeOf\";\n  import isNativeReflectConstruct from \"isNativeReflectConstruct\";\n\n  export default function _construct(Parent, args, Class) {\n    if (isNativeReflectConstruct()) {\n      _construct = Reflect.construct.bind();\n    } else {\n      // NOTE: If Parent !== Class, the correct __proto__ is set *after*\n      //       calling the constructor.\n      _construct = function _construct(Parent, args, Class) {\n        var a = [null];\n        a.push.apply(a, args);\n        var Constructor = Function.bind.apply(Parent, a);\n        var instance = new Constructor();\n        if (Class) setPrototypeOf(instance, Class.prototype);\n        return instance;\n      };\n    }\n    // Avoid issues with Class being present but undefined when it wasn't\n    // present in the original call.\n    return _construct.apply(null, arguments);\n  }\n`;\n\nhelpers.isNativeFunction = helper(\"7.0.0-beta.0\")`\n  export default function _isNativeFunction(fn) {\n    // Note: This function returns \"true\" for core-js functions.\n    return Function.toString.call(fn).indexOf(\"[native code]\") !== -1;\n  }\n`;\n\n// Based on https://github.com/WebReflection/babel-plugin-transform-builtin-classes\nhelpers.wrapNativeSuper = helper(\"7.0.0-beta.0\")`\n  import getPrototypeOf from \"getPrototypeOf\";\n  import setPrototypeOf from \"setPrototypeOf\";\n  import isNativeFunction from \"isNativeFunction\";\n  import construct from \"construct\";\n\n  export default function _wrapNativeSuper(Class) {\n    var _cache = typeof Map === \"function\" ? new Map() : undefined;\n\n    _wrapNativeSuper = function _wrapNativeSuper(Class) {\n      if (Class === null || !isNativeFunction(Class)) return Class;\n      if (typeof Class !== \"function\") {\n        throw new TypeError(\"Super expression must either be null or a function\");\n      }\n      if (typeof _cache !== \"undefined\") {\n        if (_cache.has(Class)) return _cache.get(Class);\n        _cache.set(Class, Wrapper);\n      }\n      function Wrapper() {\n        return construct(Class, arguments, getPrototypeOf(this).constructor)\n      }\n      Wrapper.prototype = Object.create(Class.prototype, {\n        constructor: {\n          value: Wrapper,\n          enumerable: false,\n          writable: true,\n          configurable: true,\n        }\n      });\n\n      return setPrototypeOf(Wrapper, Class);\n    }\n\n    return _wrapNativeSuper(Class)\n  }\n`;\n\nhelpers.instanceof = helper(\"7.0.0-beta.0\")`\n  export default function _instanceof(left, right) {\n    if (right != null && typeof Symbol !== \"undefined\" && right[Symbol.hasInstance]) {\n      return !!right[Symbol.hasInstance](left);\n    } else {\n      return left instanceof right;\n    }\n  }\n`;\n\nhelpers.interopRequireDefault = helper(\"7.0.0-beta.0\")`\n  export default function _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n  }\n`;\n\nhelpers.interopRequireWildcard = helper(\"7.14.0\")`\n  function _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function (nodeInterop) {\n      return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n  }\n\n  export default function _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n      return obj;\n    }\n\n    if (obj === null || (typeof obj !== \"object\" && typeof obj !== \"function\")) {\n      return { default: obj }\n    }\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n      return cache.get(obj);\n    }\n\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for (var key in obj) {\n      if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n        var desc = hasPropertyDescriptor\n          ? Object.getOwnPropertyDescriptor(obj, key)\n          : null;\n        if (desc && (desc.get || desc.set)) {\n          Object.defineProperty(newObj, key, desc);\n        } else {\n          newObj[key] = obj[key];\n        }\n      }\n    }\n    newObj.default = obj;\n    if (cache) {\n      cache.set(obj, newObj);\n    }\n    return newObj;\n  }\n`;\n\nhelpers.newArrowCheck = helper(\"7.0.0-beta.0\")`\n  export default function _newArrowCheck(innerThis, boundThis) {\n    if (innerThis !== boundThis) {\n      throw new TypeError(\"Cannot instantiate an arrow function\");\n    }\n  }\n`;\n\nhelpers.objectDestructuringEmpty = helper(\"7.0.0-beta.0\")`\n  export default function _objectDestructuringEmpty(obj) {\n    if (obj == null) throw new TypeError(\"Cannot destructure \" + obj);\n  }\n`;\n\nhelpers.objectWithoutPropertiesLoose = helper(\"7.0.0-beta.0\")`\n  export default function _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n\n    for (i = 0; i < sourceKeys.length; i++) {\n      key = sourceKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n\n    return target;\n  }\n`;\n\nhelpers.objectWithoutProperties = helper(\"7.0.0-beta.0\")`\n  import objectWithoutPropertiesLoose from \"objectWithoutPropertiesLoose\";\n\n  export default function _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n\n    var target = objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n\n    if (Object.getOwnPropertySymbols) {\n      var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n      for (i = 0; i < sourceSymbolKeys.length; i++) {\n        key = sourceSymbolKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n        target[key] = source[key];\n      }\n    }\n\n    return target;\n  }\n`;\n\nhelpers.assertThisInitialized = helper(\"7.0.0-beta.0\")`\n  export default function _assertThisInitialized(self) {\n    if (self === void 0) {\n      throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    }\n    return self;\n  }\n`;\n\nhelpers.possibleConstructorReturn = helper(\"7.0.0-beta.0\")`\n  import assertThisInitialized from \"assertThisInitialized\";\n\n  export default function _possibleConstructorReturn(self, call) {\n    if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n      return call;\n    } else if (call !== void 0) {\n      throw new TypeError(\"Derived constructors may only return object or undefined\");\n    }\n\n    return assertThisInitialized(self);\n  }\n`;\n\n// This is duplicated to packages/babel-plugin-transform-classes/src/inline-createSuper-helpers.js\nhelpers.createSuper = helper(\"7.9.0\")`\n  import getPrototypeOf from \"getPrototypeOf\";\n  import isNativeReflectConstruct from \"isNativeReflectConstruct\";\n  import possibleConstructorReturn from \"possibleConstructorReturn\";\n\n  export default function _createSuper(Derived) {\n    var hasNativeReflectConstruct = isNativeReflectConstruct();\n\n    return function _createSuperInternal() {\n      var Super = getPrototypeOf(Derived), result;\n      if (hasNativeReflectConstruct) {\n        // NOTE: This doesn't work if this.__proto__.constructor has been modified.\n        var NewTarget = getPrototypeOf(this).constructor;\n        result = Reflect.construct(Super, arguments, NewTarget);\n      } else {\n        result = Super.apply(this, arguments);\n      }\n      return possibleConstructorReturn(this, result);\n    }\n  }\n `;\n\nhelpers.superPropBase = helper(\"7.0.0-beta.0\")`\n  import getPrototypeOf from \"getPrototypeOf\";\n\n  export default function _superPropBase(object, property) {\n    // Yes, this throws if object is null to being with, that's on purpose.\n    while (!Object.prototype.hasOwnProperty.call(object, property)) {\n      object = getPrototypeOf(object);\n      if (object === null) break;\n    }\n    return object;\n  }\n`;\n\n// need a bind because https://github.com/babel/babel/issues/14527\n// https://tc39.es/ecma262/multipage/reflection.html#sec-reflect.get\n//\n//  28.1.5 Reflect.get ( target, propertyKey [ , receiver ] )\n//\nhelpers.get = helper(\"7.0.0-beta.0\")`\n  import superPropBase from \"superPropBase\";\n\n  export default function _get() {\n    if (typeof Reflect !== \"undefined\" && Reflect.get) {\n      _get = Reflect.get.bind();\n    } else {\n      _get = function _get(target, property, receiver) {\n        var base = superPropBase(target, property);\n\n        if (!base) return;\n\n        var desc = Object.getOwnPropertyDescriptor(base, property);\n        if (desc.get) {\n          // STEP 3. If receiver is not present, then set receiver to target.\n          return desc.get.call(arguments.length < 3 ? target : receiver);\n        }\n\n        return desc.value;\n      };\n    }\n    return _get.apply(this, arguments);\n  }\n`;\n\nhelpers.set = helper(\"7.0.0-beta.0\")`\n  import superPropBase from \"superPropBase\";\n  import defineProperty from \"defineProperty\";\n\n  function set(target, property, value, receiver) {\n    if (typeof Reflect !== \"undefined\" && Reflect.set) {\n      set = Reflect.set;\n    } else {\n      set = function set(target, property, value, receiver) {\n        var base = superPropBase(target, property);\n        var desc;\n\n        if (base) {\n          desc = Object.getOwnPropertyDescriptor(base, property);\n          if (desc.set) {\n            desc.set.call(receiver, value);\n            return true;\n          } else if (!desc.writable) {\n            // Both getter and non-writable fall into this.\n            return false;\n          }\n        }\n\n        // Without a super that defines the property, spec boils down to\n        // \"define on receiver\" for some reason.\n        desc = Object.getOwnPropertyDescriptor(receiver, property);\n        if (desc) {\n          if (!desc.writable) {\n            // Setter, getter, and non-writable fall into this.\n            return false;\n          }\n\n          desc.value = value;\n          Object.defineProperty(receiver, property, desc);\n        } else {\n          // Avoid setters that may be defined on Sub's prototype, but not on\n          // the instance.\n          defineProperty(receiver, property, value);\n        }\n\n        return true;\n      };\n    }\n\n    return set(target, property, value, receiver);\n  }\n\n  export default function _set(target, property, value, receiver, isStrict) {\n    var s = set(target, property, value, receiver || target);\n    if (!s && isStrict) {\n      throw new TypeError('failed to set property');\n    }\n\n    return value;\n  }\n`;\n\nhelpers.taggedTemplateLiteral = helper(\"7.0.0-beta.0\")`\n  export default function _taggedTemplateLiteral(strings, raw) {\n    if (!raw) { raw = strings.slice(0); }\n    return Object.freeze(Object.defineProperties(strings, {\n        raw: { value: Object.freeze(raw) }\n    }));\n  }\n`;\n\nhelpers.taggedTemplateLiteralLoose = helper(\"7.0.0-beta.0\")`\n  export default function _taggedTemplateLiteralLoose(strings, raw) {\n    if (!raw) { raw = strings.slice(0); }\n    strings.raw = raw;\n    return strings;\n  }\n`;\n\nhelpers.readOnlyError = helper(\"7.0.0-beta.0\")`\n  export default function _readOnlyError(name) {\n    throw new TypeError(\"\\\\\"\" + name + \"\\\\\" is read-only\");\n  }\n`;\n\nhelpers.writeOnlyError = helper(\"7.12.13\")`\n  export default function _writeOnlyError(name) {\n    throw new TypeError(\"\\\\\"\" + name + \"\\\\\" is write-only\");\n  }\n`;\n\nhelpers.classNameTDZError = helper(\"7.0.0-beta.0\")`\n  export default function _classNameTDZError(name) {\n    throw new ReferenceError(\"Class \\\\\"\" + name + \"\\\\\" cannot be referenced in computed property keys.\");\n  }\n`;\n\nhelpers.temporalUndefined = helper(\"7.0.0-beta.0\")`\n  // This function isn't mean to be called, but to be used as a reference.\n  // We can't use a normal object because it isn't hoisted.\n  export default function _temporalUndefined() {}\n`;\n\nhelpers.tdz = helper(\"7.5.5\")`\n  export default function _tdzError(name) {\n    throw new ReferenceError(name + \" is not defined - temporal dead zone\");\n  }\n`;\n\nhelpers.temporalRef = helper(\"7.0.0-beta.0\")`\n  import undef from \"temporalUndefined\";\n  import err from \"tdz\";\n\n  export default function _temporalRef(val, name) {\n    return val === undef ? err(name) : val;\n  }\n`;\n\nhelpers.slicedToArray = helper(\"7.0.0-beta.0\")`\n  import arrayWithHoles from \"arrayWithHoles\";\n  import iterableToArrayLimit from \"iterableToArrayLimit\";\n  import unsupportedIterableToArray from \"unsupportedIterableToArray\";\n  import nonIterableRest from \"nonIterableRest\";\n\n  export default function _slicedToArray(arr, i) {\n    return (\n      arrayWithHoles(arr) ||\n      iterableToArrayLimit(arr, i) ||\n      unsupportedIterableToArray(arr, i) ||\n      nonIterableRest()\n    );\n  }\n`;\n\nhelpers.slicedToArrayLoose = helper(\"7.0.0-beta.0\")`\n  import arrayWithHoles from \"arrayWithHoles\";\n  import iterableToArrayLimitLoose from \"iterableToArrayLimitLoose\";\n  import unsupportedIterableToArray from \"unsupportedIterableToArray\";\n  import nonIterableRest from \"nonIterableRest\";\n\n  export default function _slicedToArrayLoose(arr, i) {\n    return (\n      arrayWithHoles(arr) ||\n      iterableToArrayLimitLoose(arr, i) ||\n      unsupportedIterableToArray(arr, i) ||\n      nonIterableRest()\n    );\n  }\n`;\n\nhelpers.toArray = helper(\"7.0.0-beta.0\")`\n  import arrayWithHoles from \"arrayWithHoles\";\n  import iterableToArray from \"iterableToArray\";\n  import unsupportedIterableToArray from \"unsupportedIterableToArray\";\n  import nonIterableRest from \"nonIterableRest\";\n\n  export default function _toArray(arr) {\n    return (\n      arrayWithHoles(arr) ||\n      iterableToArray(arr) ||\n      unsupportedIterableToArray(arr) ||\n      nonIterableRest()\n    );\n  }\n`;\n\nhelpers.toConsumableArray = helper(\"7.0.0-beta.0\")`\n  import arrayWithoutHoles from \"arrayWithoutHoles\";\n  import iterableToArray from \"iterableToArray\";\n  import unsupportedIterableToArray from \"unsupportedIterableToArray\";\n  import nonIterableSpread from \"nonIterableSpread\";\n\n  export default function _toConsumableArray(arr) {\n    return (\n      arrayWithoutHoles(arr) ||\n      iterableToArray(arr) ||\n      unsupportedIterableToArray(arr) ||\n      nonIterableSpread()\n    );\n  }\n`;\n\nhelpers.arrayWithoutHoles = helper(\"7.0.0-beta.0\")`\n  import arrayLikeToArray from \"arrayLikeToArray\";\n\n  export default function _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return arrayLikeToArray(arr);\n  }\n`;\n\nhelpers.arrayWithHoles = helper(\"7.0.0-beta.0\")`\n  export default function _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n  }\n`;\n\nhelpers.maybeArrayLike = helper(\"7.9.0\")`\n  import arrayLikeToArray from \"arrayLikeToArray\";\n\n  export default function _maybeArrayLike(next, arr, i) {\n    if (arr && !Array.isArray(arr) && typeof arr.length === \"number\") {\n      var len = arr.length;\n      return arrayLikeToArray(arr, i !== void 0 && i < len ? i : len);\n    }\n    return next(arr, i);\n  }\n`;\n\nhelpers.iterableToArray = helper(\"7.0.0-beta.0\")`\n  export default function _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n  }\n`;\n\nhelpers.unsupportedIterableToArray = helper(\"7.9.0\")`\n  import arrayLikeToArray from \"arrayLikeToArray\";\n\n  export default function _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))\n      return arrayLikeToArray(o, minLen);\n  }\n`;\n\nhelpers.arrayLikeToArray = helper(\"7.9.0\")`\n  export default function _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n    return arr2;\n  }\n`;\n\nhelpers.nonIterableSpread = helper(\"7.0.0-beta.0\")`\n  export default function _nonIterableSpread() {\n    throw new TypeError(\n      \"Invalid attempt to spread non-iterable instance.\\\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"\n    );\n  }\n`;\n\nhelpers.nonIterableRest = helper(\"7.0.0-beta.0\")`\n  export default function _nonIterableRest() {\n    throw new TypeError(\n      \"Invalid attempt to destructure non-iterable instance.\\\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"\n    );\n  }\n`;\n\nhelpers.createForOfIteratorHelper = helper(\"7.9.0\")`\n  import unsupportedIterableToArray from \"unsupportedIterableToArray\";\n\n  // s: start (create the iterator)\n  // n: next\n  // e: error (called whenever something throws)\n  // f: finish (always called at the end)\n\n  export default function _createForOfIteratorHelper(o, allowArrayLike) {\n    var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n\n    if (!it) {\n      // Fallback for engines without symbol support\n      if (\n        Array.isArray(o) ||\n        (it = unsupportedIterableToArray(o)) ||\n        (allowArrayLike && o && typeof o.length === \"number\")\n      ) {\n        if (it) o = it;\n        var i = 0;\n        var F = function(){};\n        return {\n          s: F,\n          n: function() {\n            if (i >= o.length) return { done: true };\n            return { done: false, value: o[i++] };\n          },\n          e: function(e) { throw e; },\n          f: F,\n        };\n      }\n\n      throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n    }\n\n    var normalCompletion = true, didErr = false, err;\n\n    return {\n      s: function() {\n        it = it.call(o);\n      },\n      n: function() {\n        var step = it.next();\n        normalCompletion = step.done;\n        return step;\n      },\n      e: function(e) {\n        didErr = true;\n        err = e;\n      },\n      f: function() {\n        try {\n          if (!normalCompletion && it.return != null) it.return();\n        } finally {\n          if (didErr) throw err;\n        }\n      }\n    };\n  }\n`;\n\nhelpers.createForOfIteratorHelperLoose = helper(\"7.9.0\")`\n  import unsupportedIterableToArray from \"unsupportedIterableToArray\";\n\n  export default function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n    var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n\n    if (it) return (it = it.call(o)).next.bind(it);\n\n    // Fallback for engines without symbol support\n    if (\n      Array.isArray(o) ||\n      (it = unsupportedIterableToArray(o)) ||\n      (allowArrayLike && o && typeof o.length === \"number\")\n    ) {\n      if (it) o = it;\n      var i = 0;\n      return function() {\n        if (i >= o.length) return { done: true };\n        return { done: false, value: o[i++] };\n      }\n    }\n\n    throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n`;\n\nhelpers.skipFirstGeneratorNext = helper(\"7.0.0-beta.0\")`\n  export default function _skipFirstGeneratorNext(fn) {\n    return function () {\n      var it = fn.apply(this, arguments);\n      it.next();\n      return it;\n    }\n  }\n`;\n\nhelpers.toPrimitive = helper(\"7.1.5\")`\n  export default function _toPrimitive(\n    input,\n    hint /*: \"default\" | \"string\" | \"number\" | void */\n  ) {\n    if (typeof input !== \"object\" || input === null) return input;\n    var prim = input[Symbol.toPrimitive];\n    if (prim !== undefined) {\n      var res = prim.call(input, hint || \"default\");\n      if (typeof res !== \"object\") return res;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (hint === \"string\" ? String : Number)(input);\n  }\n`;\n\nhelpers.toPropertyKey = helper(\"7.1.5\")`\n  import toPrimitive from \"toPrimitive\";\n\n  export default function _toPropertyKey(arg) {\n    var key = toPrimitive(arg, \"string\");\n    return typeof key === \"symbol\" ? key : String(key);\n  }\n`;\n\n/**\n * Add a helper that will throw a useful error if the transform fails to detect the class\n * property assignment, so users know something failed.\n */\nhelpers.initializerWarningHelper = helper(\"7.0.0-beta.0\")`\n    export default function _initializerWarningHelper(descriptor, context){\n        throw new Error(\n          'Decorating class property failed. Please ensure that ' +\n          'proposal-class-properties is enabled and runs after the decorators transform.'\n        );\n    }\n`;\n\n/**\n * Add a helper to call as a replacement for class property definition.\n */\nhelpers.initializerDefineProperty = helper(\"7.0.0-beta.0\")`\n    export default function _initializerDefineProperty(target, property, descriptor, context){\n        if (!descriptor) return;\n\n        Object.defineProperty(target, property, {\n            enumerable: descriptor.enumerable,\n            configurable: descriptor.configurable,\n            writable: descriptor.writable,\n            value: descriptor.initializer ? descriptor.initializer.call(context) : void 0,\n        });\n    }\n`;\n\n/**\n * Add a helper to take an initial descriptor, apply some decorators to it, and optionally\n * define the property.\n */\nhelpers.applyDecoratedDescriptor = helper(\"7.0.0-beta.0\")`\n    export default function _applyDecoratedDescriptor(target, property, decorators, descriptor, context){\n        var desc = {};\n        Object.keys(descriptor).forEach(function(key){\n            desc[key] = descriptor[key];\n        });\n        desc.enumerable = !!desc.enumerable;\n        desc.configurable = !!desc.configurable;\n        if ('value' in desc || desc.initializer){\n            desc.writable = true;\n        }\n\n        desc = decorators.slice().reverse().reduce(function(desc, decorator){\n            return decorator(target, property, desc) || desc;\n        }, desc);\n\n        if (context && desc.initializer !== void 0){\n            desc.value = desc.initializer ? desc.initializer.call(context) : void 0;\n            desc.initializer = undefined;\n        }\n\n        if (desc.initializer === void 0){\n            Object.defineProperty(target, property, desc);\n            desc = null;\n        }\n\n        return desc;\n    }\n`;\n\nhelpers.classPrivateFieldLooseKey = helper(\"7.0.0-beta.0\")`\n  var id = 0;\n  export default function _classPrivateFieldKey(name) {\n    return \"__private_\" + (id++) + \"_\" + name;\n  }\n`;\n\nhelpers.classPrivateFieldLooseBase = helper(\"7.0.0-beta.0\")`\n  export default function _classPrivateFieldBase(receiver, privateKey) {\n    if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {\n      throw new TypeError(\"attempted to use private field on non-instance\");\n    }\n    return receiver;\n  }\n`;\n\nhelpers.classPrivateFieldGet = helper(\"7.0.0-beta.0\")`\n  import classApplyDescriptorGet from \"classApplyDescriptorGet\";\n  import classExtractFieldDescriptor from \"classExtractFieldDescriptor\";\n  export default function _classPrivateFieldGet(receiver, privateMap) {\n    var descriptor = classExtractFieldDescriptor(receiver, privateMap, \"get\");\n    return classApplyDescriptorGet(receiver, descriptor);\n  }\n`;\n\nhelpers.classPrivateFieldSet = helper(\"7.0.0-beta.0\")`\n  import classApplyDescriptorSet from \"classApplyDescriptorSet\";\n  import classExtractFieldDescriptor from \"classExtractFieldDescriptor\";\n  export default function _classPrivateFieldSet(receiver, privateMap, value) {\n    var descriptor = classExtractFieldDescriptor(receiver, privateMap, \"set\");\n    classApplyDescriptorSet(receiver, descriptor, value);\n    return value;\n  }\n`;\n\nhelpers.classPrivateFieldDestructureSet = helper(\"7.4.4\")`\n  import classApplyDescriptorDestructureSet from \"classApplyDescriptorDestructureSet\";\n  import classExtractFieldDescriptor from \"classExtractFieldDescriptor\";\n  export default function _classPrivateFieldDestructureSet(receiver, privateMap) {\n    var descriptor = classExtractFieldDescriptor(receiver, privateMap, \"set\");\n    return classApplyDescriptorDestructureSet(receiver, descriptor);\n  }\n`;\n\nhelpers.classExtractFieldDescriptor = helper(\"7.13.10\")`\n  export default function _classExtractFieldDescriptor(receiver, privateMap, action) {\n    if (!privateMap.has(receiver)) {\n      throw new TypeError(\"attempted to \" + action + \" private field on non-instance\");\n    }\n    return privateMap.get(receiver);\n  }\n`;\n\nhelpers.classStaticPrivateFieldSpecGet = helper(\"7.0.2\")`\n  import classApplyDescriptorGet from \"classApplyDescriptorGet\";\n  import classCheckPrivateStaticAccess from \"classCheckPrivateStaticAccess\";\n  import classCheckPrivateStaticFieldDescriptor from \"classCheckPrivateStaticFieldDescriptor\";\n  export default function _classStaticPrivateFieldSpecGet(receiver, classConstructor, descriptor) {\n    classCheckPrivateStaticAccess(receiver, classConstructor);\n    classCheckPrivateStaticFieldDescriptor(descriptor, \"get\");\n    return classApplyDescriptorGet(receiver, descriptor);\n  }\n`;\n\nhelpers.classStaticPrivateFieldSpecSet = helper(\"7.0.2\")`\n  import classApplyDescriptorSet from \"classApplyDescriptorSet\";\n  import classCheckPrivateStaticAccess from \"classCheckPrivateStaticAccess\";\n  import classCheckPrivateStaticFieldDescriptor from \"classCheckPrivateStaticFieldDescriptor\";\n  export default function _classStaticPrivateFieldSpecSet(receiver, classConstructor, descriptor, value) {\n    classCheckPrivateStaticAccess(receiver, classConstructor);\n    classCheckPrivateStaticFieldDescriptor(descriptor, \"set\");\n    classApplyDescriptorSet(receiver, descriptor, value);\n    return value;\n  }\n`;\n\nhelpers.classStaticPrivateMethodGet = helper(\"7.3.2\")`\n  import classCheckPrivateStaticAccess from \"classCheckPrivateStaticAccess\";\n  export default function _classStaticPrivateMethodGet(receiver, classConstructor, method) {\n    classCheckPrivateStaticAccess(receiver, classConstructor);\n    return method;\n  }\n`;\n\nhelpers.classStaticPrivateMethodSet = helper(\"7.3.2\")`\n  export default function _classStaticPrivateMethodSet() {\n    throw new TypeError(\"attempted to set read only static private field\");\n  }\n`;\n\nhelpers.classApplyDescriptorGet = helper(\"7.13.10\")`\n  export default function _classApplyDescriptorGet(receiver, descriptor) {\n    if (descriptor.get) {\n      return descriptor.get.call(receiver);\n    }\n    return descriptor.value;\n  }\n`;\n\nhelpers.classApplyDescriptorSet = helper(\"7.13.10\")`\n  export default function _classApplyDescriptorSet(receiver, descriptor, value) {\n    if (descriptor.set) {\n      descriptor.set.call(receiver, value);\n    } else {\n      if (!descriptor.writable) {\n        // This should only throw in strict mode, but class bodies are\n        // always strict and private fields can only be used inside\n        // class bodies.\n        throw new TypeError(\"attempted to set read only private field\");\n      }\n      descriptor.value = value;\n    }\n  }\n`;\n\nhelpers.classApplyDescriptorDestructureSet = helper(\"7.13.10\")`\n  export default function _classApplyDescriptorDestructureSet(receiver, descriptor) {\n    if (descriptor.set) {\n      if (!(\"__destrObj\" in descriptor)) {\n        descriptor.__destrObj = {\n          set value(v) {\n            descriptor.set.call(receiver, v)\n          },\n        };\n      }\n      return descriptor.__destrObj;\n    } else {\n      if (!descriptor.writable) {\n        // This should only throw in strict mode, but class bodies are\n        // always strict and private fields can only be used inside\n        // class bodies.\n        throw new TypeError(\"attempted to set read only private field\");\n      }\n\n      return descriptor;\n    }\n  }\n`;\n\nhelpers.classStaticPrivateFieldDestructureSet = helper(\"7.13.10\")`\n  import classApplyDescriptorDestructureSet from \"classApplyDescriptorDestructureSet\";\n  import classCheckPrivateStaticAccess from \"classCheckPrivateStaticAccess\";\n  import classCheckPrivateStaticFieldDescriptor from \"classCheckPrivateStaticFieldDescriptor\";\n  export default function _classStaticPrivateFieldDestructureSet(receiver, classConstructor, descriptor) {\n    classCheckPrivateStaticAccess(receiver, classConstructor);\n    classCheckPrivateStaticFieldDescriptor(descriptor, \"set\");\n    return classApplyDescriptorDestructureSet(receiver, descriptor);\n  }\n`;\n\nhelpers.classCheckPrivateStaticAccess = helper(\"7.13.10\")`\n  export default function _classCheckPrivateStaticAccess(receiver, classConstructor) {\n    if (receiver !== classConstructor) {\n      throw new TypeError(\"Private static access of wrong provenance\");\n    }\n  }\n`;\n\nhelpers.classCheckPrivateStaticFieldDescriptor = helper(\"7.13.10\")`\n  export default function _classCheckPrivateStaticFieldDescriptor(descriptor, action) {\n    if (descriptor === undefined) {\n      throw new TypeError(\"attempted to \" + action + \" private static field before its declaration\");\n    }\n  }\n`;\n\nhelpers.decorate = helper(\"7.1.5\")`\n  import toArray from \"toArray\";\n  import toPropertyKey from \"toPropertyKey\";\n\n  // These comments are stripped by @babel/template\n  /*::\n  type PropertyDescriptor =\n    | {\n        value: any,\n        writable: boolean,\n        configurable: boolean,\n        enumerable: boolean,\n      }\n    | {\n        get?: () => any,\n        set?: (v: any) => void,\n        configurable: boolean,\n        enumerable: boolean,\n      };\n\n  type FieldDescriptor ={\n    writable: boolean,\n    configurable: boolean,\n    enumerable: boolean,\n  };\n\n  type Placement = \"static\" | \"prototype\" | \"own\";\n  type Key = string | symbol; // PrivateName is not supported yet.\n\n  type ElementDescriptor =\n    | {\n        kind: \"method\",\n        key: Key,\n        placement: Placement,\n        descriptor: PropertyDescriptor\n      }\n    | {\n        kind: \"field\",\n        key: Key,\n        placement: Placement,\n        descriptor: FieldDescriptor,\n        initializer?: () => any,\n      };\n\n  // This is exposed to the user code\n  type ElementObjectInput = ElementDescriptor & {\n    [@@toStringTag]?: \"Descriptor\"\n  };\n\n  // This is exposed to the user code\n  type ElementObjectOutput = ElementDescriptor & {\n    [@@toStringTag]?: \"Descriptor\"\n    extras?: ElementDescriptor[],\n    finisher?: ClassFinisher,\n  };\n\n  // This is exposed to the user code\n  type ClassObject = {\n    [@@toStringTag]?: \"Descriptor\",\n    kind: \"class\",\n    elements: ElementDescriptor[],\n  };\n\n  type ElementDecorator = (descriptor: ElementObjectInput) => ?ElementObjectOutput;\n  type ClassDecorator = (descriptor: ClassObject) => ?ClassObject;\n  type ClassFinisher = <A, B>(cl: Class<A>) => Class<B>;\n\n  // Only used by Babel in the transform output, not part of the spec.\n  type ElementDefinition =\n    | {\n        kind: \"method\",\n        value: any,\n        key: Key,\n        static?: boolean,\n        decorators?: ElementDecorator[],\n      }\n    | {\n        kind: \"field\",\n        value: () => any,\n        key: Key,\n        static?: boolean,\n        decorators?: ElementDecorator[],\n    };\n\n  declare function ClassFactory<C>(initialize: (instance: C) => void): {\n    F: Class<C>,\n    d: ElementDefinition[]\n  }\n\n  */\n\n  /*::\n  // Various combinations with/without extras and with one or many finishers\n\n  type ElementFinisherExtras = {\n    element: ElementDescriptor,\n    finisher?: ClassFinisher,\n    extras?: ElementDescriptor[],\n  };\n\n  type ElementFinishersExtras = {\n    element: ElementDescriptor,\n    finishers: ClassFinisher[],\n    extras: ElementDescriptor[],\n  };\n\n  type ElementsFinisher = {\n    elements: ElementDescriptor[],\n    finisher?: ClassFinisher,\n  };\n\n  type ElementsFinishers = {\n    elements: ElementDescriptor[],\n    finishers: ClassFinisher[],\n  };\n\n  */\n\n  /*::\n\n  type Placements = {\n    static: Key[],\n    prototype: Key[],\n    own: Key[],\n  };\n\n  */\n\n  // ClassDefinitionEvaluation (Steps 26-*)\n  export default function _decorate(\n    decorators /*: ClassDecorator[] */,\n    factory /*: ClassFactory */,\n    superClass /*: ?Class<*> */,\n    mixins /*: ?Array<Function> */,\n  ) /*: Class<*> */ {\n    var api = _getDecoratorsApi();\n    if (mixins) {\n      for (var i = 0; i < mixins.length; i++) {\n        api = mixins[i](api);\n      }\n    }\n\n    var r = factory(function initialize(O) {\n      api.initializeInstanceElements(O, decorated.elements);\n    }, superClass);\n    var decorated = api.decorateClass(\n      _coalesceClassElements(r.d.map(_createElementDescriptor)),\n      decorators,\n    );\n\n    api.initializeClassElements(r.F, decorated.elements);\n\n    return api.runClassFinishers(r.F, decorated.finishers);\n  }\n\n  function _getDecoratorsApi() {\n    _getDecoratorsApi = function() {\n      return api;\n    };\n\n    var api = {\n      elementsDefinitionOrder: [[\"method\"], [\"field\"]],\n\n      // InitializeInstanceElements\n      initializeInstanceElements: function(\n        /*::<C>*/ O /*: C */,\n        elements /*: ElementDescriptor[] */,\n      ) {\n        [\"method\", \"field\"].forEach(function(kind) {\n          elements.forEach(function(element /*: ElementDescriptor */) {\n            if (element.kind === kind && element.placement === \"own\") {\n              this.defineClassElement(O, element);\n            }\n          }, this);\n        }, this);\n      },\n\n      // InitializeClassElements\n      initializeClassElements: function(\n        /*::<C>*/ F /*: Class<C> */,\n        elements /*: ElementDescriptor[] */,\n      ) {\n        var proto = F.prototype;\n\n        [\"method\", \"field\"].forEach(function(kind) {\n          elements.forEach(function(element /*: ElementDescriptor */) {\n            var placement = element.placement;\n            if (\n              element.kind === kind &&\n              (placement === \"static\" || placement === \"prototype\")\n            ) {\n              var receiver = placement === \"static\" ? F : proto;\n              this.defineClassElement(receiver, element);\n            }\n          }, this);\n        }, this);\n      },\n\n      // DefineClassElement\n      defineClassElement: function(\n        /*::<C>*/ receiver /*: C | Class<C> */,\n        element /*: ElementDescriptor */,\n      ) {\n        var descriptor /*: PropertyDescriptor */ = element.descriptor;\n        if (element.kind === \"field\") {\n          var initializer = element.initializer;\n          descriptor = {\n            enumerable: descriptor.enumerable,\n            writable: descriptor.writable,\n            configurable: descriptor.configurable,\n            value: initializer === void 0 ? void 0 : initializer.call(receiver),\n          };\n        }\n        Object.defineProperty(receiver, element.key, descriptor);\n      },\n\n      // DecorateClass\n      decorateClass: function(\n        elements /*: ElementDescriptor[] */,\n        decorators /*: ClassDecorator[] */,\n      ) /*: ElementsFinishers */ {\n        var newElements /*: ElementDescriptor[] */ = [];\n        var finishers /*: ClassFinisher[] */ = [];\n        var placements /*: Placements */ = {\n          static: [],\n          prototype: [],\n          own: [],\n        };\n\n        elements.forEach(function(element /*: ElementDescriptor */) {\n          this.addElementPlacement(element, placements);\n        }, this);\n\n        elements.forEach(function(element /*: ElementDescriptor */) {\n          if (!_hasDecorators(element)) return newElements.push(element);\n\n          var elementFinishersExtras /*: ElementFinishersExtras */ = this.decorateElement(\n            element,\n            placements,\n          );\n          newElements.push(elementFinishersExtras.element);\n          newElements.push.apply(newElements, elementFinishersExtras.extras);\n          finishers.push.apply(finishers, elementFinishersExtras.finishers);\n        }, this);\n\n        if (!decorators) {\n          return { elements: newElements, finishers: finishers };\n        }\n\n        var result /*: ElementsFinishers */ = this.decorateConstructor(\n          newElements,\n          decorators,\n        );\n        finishers.push.apply(finishers, result.finishers);\n        result.finishers = finishers;\n\n        return result;\n      },\n\n      // AddElementPlacement\n      addElementPlacement: function(\n        element /*: ElementDescriptor */,\n        placements /*: Placements */,\n        silent /*: boolean */,\n      ) {\n        var keys = placements[element.placement];\n        if (!silent && keys.indexOf(element.key) !== -1) {\n          throw new TypeError(\"Duplicated element (\" + element.key + \")\");\n        }\n        keys.push(element.key);\n      },\n\n      // DecorateElement\n      decorateElement: function(\n        element /*: ElementDescriptor */,\n        placements /*: Placements */,\n      ) /*: ElementFinishersExtras */ {\n        var extras /*: ElementDescriptor[] */ = [];\n        var finishers /*: ClassFinisher[] */ = [];\n\n        for (\n          var decorators = element.decorators, i = decorators.length - 1;\n          i >= 0;\n          i--\n        ) {\n          // (inlined) RemoveElementPlacement\n          var keys = placements[element.placement];\n          keys.splice(keys.indexOf(element.key), 1);\n\n          var elementObject /*: ElementObjectInput */ = this.fromElementDescriptor(\n            element,\n          );\n          var elementFinisherExtras /*: ElementFinisherExtras */ = this.toElementFinisherExtras(\n            (0, decorators[i])(elementObject) /*: ElementObjectOutput */ ||\n              elementObject,\n          );\n\n          element = elementFinisherExtras.element;\n          this.addElementPlacement(element, placements);\n\n          if (elementFinisherExtras.finisher) {\n            finishers.push(elementFinisherExtras.finisher);\n          }\n\n          var newExtras /*: ElementDescriptor[] | void */ =\n            elementFinisherExtras.extras;\n          if (newExtras) {\n            for (var j = 0; j < newExtras.length; j++) {\n              this.addElementPlacement(newExtras[j], placements);\n            }\n            extras.push.apply(extras, newExtras);\n          }\n        }\n\n        return { element: element, finishers: finishers, extras: extras };\n      },\n\n      // DecorateConstructor\n      decorateConstructor: function(\n        elements /*: ElementDescriptor[] */,\n        decorators /*: ClassDecorator[] */,\n      ) /*: ElementsFinishers */ {\n        var finishers /*: ClassFinisher[] */ = [];\n\n        for (var i = decorators.length - 1; i >= 0; i--) {\n          var obj /*: ClassObject */ = this.fromClassDescriptor(elements);\n          var elementsAndFinisher /*: ElementsFinisher */ = this.toClassDescriptor(\n            (0, decorators[i])(obj) /*: ClassObject */ || obj,\n          );\n\n          if (elementsAndFinisher.finisher !== undefined) {\n            finishers.push(elementsAndFinisher.finisher);\n          }\n\n          if (elementsAndFinisher.elements !== undefined) {\n            elements = elementsAndFinisher.elements;\n\n            for (var j = 0; j < elements.length - 1; j++) {\n              for (var k = j + 1; k < elements.length; k++) {\n                if (\n                  elements[j].key === elements[k].key &&\n                  elements[j].placement === elements[k].placement\n                ) {\n                  throw new TypeError(\n                    \"Duplicated element (\" + elements[j].key + \")\",\n                  );\n                }\n              }\n            }\n          }\n        }\n\n        return { elements: elements, finishers: finishers };\n      },\n\n      // FromElementDescriptor\n      fromElementDescriptor: function(\n        element /*: ElementDescriptor */,\n      ) /*: ElementObject */ {\n        var obj /*: ElementObject */ = {\n          kind: element.kind,\n          key: element.key,\n          placement: element.placement,\n          descriptor: element.descriptor,\n        };\n\n        var desc = {\n          value: \"Descriptor\",\n          configurable: true,\n        };\n        Object.defineProperty(obj, Symbol.toStringTag, desc);\n\n        if (element.kind === \"field\") obj.initializer = element.initializer;\n\n        return obj;\n      },\n\n      // ToElementDescriptors\n      toElementDescriptors: function(\n        elementObjects /*: ElementObject[] */,\n      ) /*: ElementDescriptor[] */ {\n        if (elementObjects === undefined) return;\n        return toArray(elementObjects).map(function(elementObject) {\n          var element = this.toElementDescriptor(elementObject);\n          this.disallowProperty(elementObject, \"finisher\", \"An element descriptor\");\n          this.disallowProperty(elementObject, \"extras\", \"An element descriptor\");\n          return element;\n        }, this);\n      },\n\n      // ToElementDescriptor\n      toElementDescriptor: function(\n        elementObject /*: ElementObject */,\n      ) /*: ElementDescriptor */ {\n        var kind = String(elementObject.kind);\n        if (kind !== \"method\" && kind !== \"field\") {\n          throw new TypeError(\n            'An element descriptor\\\\'s .kind property must be either \"method\" or' +\n              ' \"field\", but a decorator created an element descriptor with' +\n              ' .kind \"' +\n              kind +\n              '\"',\n          );\n        }\n\n        var key = toPropertyKey(elementObject.key);\n\n        var placement = String(elementObject.placement);\n        if (\n          placement !== \"static\" &&\n          placement !== \"prototype\" &&\n          placement !== \"own\"\n        ) {\n          throw new TypeError(\n            'An element descriptor\\\\'s .placement property must be one of \"static\",' +\n              ' \"prototype\" or \"own\", but a decorator created an element descriptor' +\n              ' with .placement \"' +\n              placement +\n              '\"',\n          );\n        }\n\n        var descriptor /*: PropertyDescriptor */ = elementObject.descriptor;\n\n        this.disallowProperty(elementObject, \"elements\", \"An element descriptor\");\n\n        var element /*: ElementDescriptor */ = {\n          kind: kind,\n          key: key,\n          placement: placement,\n          descriptor: Object.assign({}, descriptor),\n        };\n\n        if (kind !== \"field\") {\n          this.disallowProperty(elementObject, \"initializer\", \"A method descriptor\");\n        } else {\n          this.disallowProperty(\n            descriptor,\n            \"get\",\n            \"The property descriptor of a field descriptor\",\n          );\n          this.disallowProperty(\n            descriptor,\n            \"set\",\n            \"The property descriptor of a field descriptor\",\n          );\n          this.disallowProperty(\n            descriptor,\n            \"value\",\n            \"The property descriptor of a field descriptor\",\n          );\n\n          element.initializer = elementObject.initializer;\n        }\n\n        return element;\n      },\n\n      toElementFinisherExtras: function(\n        elementObject /*: ElementObject */,\n      ) /*: ElementFinisherExtras */ {\n        var element /*: ElementDescriptor */ = this.toElementDescriptor(\n          elementObject,\n        );\n        var finisher /*: ClassFinisher */ = _optionalCallableProperty(\n          elementObject,\n          \"finisher\",\n        );\n        var extras /*: ElementDescriptors[] */ = this.toElementDescriptors(\n          elementObject.extras,\n        );\n\n        return { element: element, finisher: finisher, extras: extras };\n      },\n\n      // FromClassDescriptor\n      fromClassDescriptor: function(\n        elements /*: ElementDescriptor[] */,\n      ) /*: ClassObject */ {\n        var obj = {\n          kind: \"class\",\n          elements: elements.map(this.fromElementDescriptor, this),\n        };\n\n        var desc = { value: \"Descriptor\", configurable: true };\n        Object.defineProperty(obj, Symbol.toStringTag, desc);\n\n        return obj;\n      },\n\n      // ToClassDescriptor\n      toClassDescriptor: function(\n        obj /*: ClassObject */,\n      ) /*: ElementsFinisher */ {\n        var kind = String(obj.kind);\n        if (kind !== \"class\") {\n          throw new TypeError(\n            'A class descriptor\\\\'s .kind property must be \"class\", but a decorator' +\n              ' created a class descriptor with .kind \"' +\n              kind +\n              '\"',\n          );\n        }\n\n        this.disallowProperty(obj, \"key\", \"A class descriptor\");\n        this.disallowProperty(obj, \"placement\", \"A class descriptor\");\n        this.disallowProperty(obj, \"descriptor\", \"A class descriptor\");\n        this.disallowProperty(obj, \"initializer\", \"A class descriptor\");\n        this.disallowProperty(obj, \"extras\", \"A class descriptor\");\n\n        var finisher = _optionalCallableProperty(obj, \"finisher\");\n        var elements = this.toElementDescriptors(obj.elements);\n\n        return { elements: elements, finisher: finisher };\n      },\n\n      // RunClassFinishers\n      runClassFinishers: function(\n        constructor /*: Class<*> */,\n        finishers /*: ClassFinisher[] */,\n      ) /*: Class<*> */ {\n        for (var i = 0; i < finishers.length; i++) {\n          var newConstructor /*: ?Class<*> */ = (0, finishers[i])(constructor);\n          if (newConstructor !== undefined) {\n            // NOTE: This should check if IsConstructor(newConstructor) is false.\n            if (typeof newConstructor !== \"function\") {\n              throw new TypeError(\"Finishers must return a constructor.\");\n            }\n            constructor = newConstructor;\n          }\n        }\n        return constructor;\n      },\n\n      disallowProperty: function(obj, name, objectType) {\n        if (obj[name] !== undefined) {\n          throw new TypeError(objectType + \" can't have a .\" + name + \" property.\");\n        }\n      }\n    };\n\n    return api;\n  }\n\n  // ClassElementEvaluation\n  function _createElementDescriptor(\n    def /*: ElementDefinition */,\n  ) /*: ElementDescriptor */ {\n    var key = toPropertyKey(def.key);\n\n    var descriptor /*: PropertyDescriptor */;\n    if (def.kind === \"method\") {\n      descriptor = {\n        value: def.value,\n        writable: true,\n        configurable: true,\n        enumerable: false,\n      };\n    } else if (def.kind === \"get\") {\n      descriptor = { get: def.value, configurable: true, enumerable: false };\n    } else if (def.kind === \"set\") {\n      descriptor = { set: def.value, configurable: true, enumerable: false };\n    } else if (def.kind === \"field\") {\n      descriptor = { configurable: true, writable: true, enumerable: true };\n    }\n\n    var element /*: ElementDescriptor */ = {\n      kind: def.kind === \"field\" ? \"field\" : \"method\",\n      key: key,\n      placement: def.static\n        ? \"static\"\n        : def.kind === \"field\"\n        ? \"own\"\n        : \"prototype\",\n      descriptor: descriptor,\n    };\n    if (def.decorators) element.decorators = def.decorators;\n    if (def.kind === \"field\") element.initializer = def.value;\n\n    return element;\n  }\n\n  // CoalesceGetterSetter\n  function _coalesceGetterSetter(\n    element /*: ElementDescriptor */,\n    other /*: ElementDescriptor */,\n  ) {\n    if (element.descriptor.get !== undefined) {\n      other.descriptor.get = element.descriptor.get;\n    } else {\n      other.descriptor.set = element.descriptor.set;\n    }\n  }\n\n  // CoalesceClassElements\n  function _coalesceClassElements(\n    elements /*: ElementDescriptor[] */,\n  ) /*: ElementDescriptor[] */ {\n    var newElements /*: ElementDescriptor[] */ = [];\n\n    var isSameElement = function(\n      other /*: ElementDescriptor */,\n    ) /*: boolean */ {\n      return (\n        other.kind === \"method\" &&\n        other.key === element.key &&\n        other.placement === element.placement\n      );\n    };\n\n    for (var i = 0; i < elements.length; i++) {\n      var element /*: ElementDescriptor */ = elements[i];\n      var other /*: ElementDescriptor */;\n\n      if (\n        element.kind === \"method\" &&\n        (other = newElements.find(isSameElement))\n      ) {\n        if (\n          _isDataDescriptor(element.descriptor) ||\n          _isDataDescriptor(other.descriptor)\n        ) {\n          if (_hasDecorators(element) || _hasDecorators(other)) {\n            throw new ReferenceError(\n              \"Duplicated methods (\" + element.key + \") can't be decorated.\",\n            );\n          }\n          other.descriptor = element.descriptor;\n        } else {\n          if (_hasDecorators(element)) {\n            if (_hasDecorators(other)) {\n              throw new ReferenceError(\n                \"Decorators can't be placed on different accessors with for \" +\n                  \"the same property (\" +\n                  element.key +\n                  \").\",\n              );\n            }\n            other.decorators = element.decorators;\n          }\n          _coalesceGetterSetter(element, other);\n        }\n      } else {\n        newElements.push(element);\n      }\n    }\n\n    return newElements;\n  }\n\n  function _hasDecorators(element /*: ElementDescriptor */) /*: boolean */ {\n    return element.decorators && element.decorators.length;\n  }\n\n  function _isDataDescriptor(desc /*: PropertyDescriptor */) /*: boolean */ {\n    return (\n      desc !== undefined &&\n      !(desc.value === undefined && desc.writable === undefined)\n    );\n  }\n\n  function _optionalCallableProperty /*::<T>*/(\n    obj /*: T */,\n    name /*: $Keys<T> */,\n  ) /*: ?Function */ {\n    var value = obj[name];\n    if (value !== undefined && typeof value !== \"function\") {\n      throw new TypeError(\"Expected '\" + name + \"' to be a function\");\n    }\n    return value;\n  }\n\n`;\n\nhelpers.classPrivateMethodGet = helper(\"7.1.6\")`\n  export default function _classPrivateMethodGet(receiver, privateSet, fn) {\n    if (!privateSet.has(receiver)) {\n      throw new TypeError(\"attempted to get private field on non-instance\");\n    }\n    return fn;\n  }\n`;\n\nhelpers.checkPrivateRedeclaration = helper(\"7.14.1\")`\n  export default function _checkPrivateRedeclaration(obj, privateCollection) {\n    if (privateCollection.has(obj)) {\n      throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n    }\n  }\n`;\n\nhelpers.classPrivateFieldInitSpec = helper(\"7.14.1\")`\n  import checkPrivateRedeclaration from \"checkPrivateRedeclaration\";\n\n  export default function _classPrivateFieldInitSpec(obj, privateMap, value) {\n    checkPrivateRedeclaration(obj, privateMap);\n    privateMap.set(obj, value);\n  }\n`;\n\nhelpers.classPrivateMethodInitSpec = helper(\"7.14.1\")`\n  import checkPrivateRedeclaration from \"checkPrivateRedeclaration\";\n\n  export default function _classPrivateMethodInitSpec(obj, privateSet) {\n    checkPrivateRedeclaration(obj, privateSet);\n    privateSet.add(obj);\n  }\n`;\n\nif (!process.env.BABEL_8_BREAKING) {\n  // Use readOnlyError instead\n  helpers.classPrivateMethodSet = helper(\"7.1.6\")`\n    export default function _classPrivateMethodSet() {\n      throw new TypeError(\"attempted to reassign private method\");\n    }\n  `;\n}\n\nhelpers.identity = helper(\"7.17.0\")`\n  export default function _identity(x) {\n    return x;\n  }\n`;\n"], "mappings": ";;;;;;AAAA;AAGA;AAOA,MAAMA,OAA+B;EAAKC,SAAS,EAAE;AAAI,GAAKC,yBAAS,CAAE;AAAC,eAC3DF,OAAO;AAAA;AAEtB,MAAMG,MAAM,GAAIC,UAAkB,IAAMC,GAAyB,KAAM;EACrED,UAAU;EACVE,GAAG,EAAE,MAAMC,iBAAQ,CAACC,OAAO,CAACF,GAAG,CAACD,GAAG;AACrC,CAAC,CAAC;AAEiC;EACjCL,OAAO,CAACS,UAAU,GAAGN,MAAM,CAAC,cAAc,CAAE;AAC9C;AACA;AACA;AACA,GAAG;AACH;AAEAH,OAAO,CAACU,kBAAkB,GAAGP,MAAM,CAAC,cAAc,CAAE;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACW,gBAAgB,GAAGR,MAAM,CAAC,cAAc,CAAE;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACY,cAAc,GAAGT,MAAM,CAAC,cAAc,CAAE;AAChD;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACa,WAAW,GAAGV,MAAM,CAAC,cAAc,CAAE;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACc,0BAA0B,GAAGX,MAAM,CAAC,cAAc,CAAE;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACe,QAAQ,GAAGZ,MAAM,CAAC,cAAc,CAAE;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACgB,cAAc,GAAGb,MAAM,CAAC,cAAc,CAAE;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAGDH,OAAO,CAACiB,OAAO,GAAGd,MAAM,CAAC,cAAc,CAAE;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAGDH,OAAO,CAACkB,YAAY,GAAGf,MAAM,CAAC,cAAc,CAAE;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACmB,QAAQ,GAAGhB,MAAM,CAAC,cAAc,CAAE;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACoB,aAAa,GAAGjB,MAAM,CAAC,cAAc,CAAE;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAGDH,OAAO,CAACqB,cAAc,GAAGlB,MAAM,CAAC,cAAc,CAAE;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACsB,cAAc,GAAGnB,MAAM,CAAC,cAAc,CAAE;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACuB,wBAAwB,GAAGpB,MAAM,CAAC,OAAO,CAAE;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAGDH,OAAO,CAACwB,SAAS,GAAGrB,MAAM,CAAC,cAAc,CAAE;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACyB,gBAAgB,GAAGtB,MAAM,CAAC,cAAc,CAAE;AAClD;AACA;AACA;AACA;AACA,CAAC;AAGDH,OAAO,CAAC0B,eAAe,GAAGvB,MAAM,CAAC,cAAc,CAAE;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC2B,UAAU,GAAGxB,MAAM,CAAC,cAAc,CAAE;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC4B,qBAAqB,GAAGzB,MAAM,CAAC,cAAc,CAAE;AACvD;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC6B,sBAAsB,GAAG1B,MAAM,CAAC,QAAQ,CAAE;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC8B,aAAa,GAAG3B,MAAM,CAAC,cAAc,CAAE;AAC/C;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC+B,wBAAwB,GAAG5B,MAAM,CAAC,cAAc,CAAE;AAC1D;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACgC,4BAA4B,GAAG7B,MAAM,CAAC,cAAc,CAAE;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACiC,uBAAuB,GAAG9B,MAAM,CAAC,cAAc,CAAE;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACkC,qBAAqB,GAAG/B,MAAM,CAAC,cAAc,CAAE;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACmC,yBAAyB,GAAGhC,MAAM,CAAC,cAAc,CAAE;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAGDH,OAAO,CAACoC,WAAW,GAAGjC,MAAM,CAAC,OAAO,CAAE;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AAEFH,OAAO,CAACqC,aAAa,GAAGlC,MAAM,CAAC,cAAc,CAAE;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAODH,OAAO,CAACsC,GAAG,GAAGnC,MAAM,CAAC,cAAc,CAAE;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACuC,GAAG,GAAGpC,MAAM,CAAC,cAAc,CAAE;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACwC,qBAAqB,GAAGrC,MAAM,CAAC,cAAc,CAAE;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACyC,0BAA0B,GAAGtC,MAAM,CAAC,cAAc,CAAE;AAC5D;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC0C,aAAa,GAAGvC,MAAM,CAAC,cAAc,CAAE;AAC/C;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC2C,cAAc,GAAGxC,MAAM,CAAC,SAAS,CAAE;AAC3C;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC4C,iBAAiB,GAAGzC,MAAM,CAAC,cAAc,CAAE;AACnD;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC6C,iBAAiB,GAAG1C,MAAM,CAAC,cAAc,CAAE;AACnD;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC8C,GAAG,GAAG3C,MAAM,CAAC,OAAO,CAAE;AAC9B;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC+C,WAAW,GAAG5C,MAAM,CAAC,cAAc,CAAE;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACgD,aAAa,GAAG7C,MAAM,CAAC,cAAc,CAAE;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACiD,kBAAkB,GAAG9C,MAAM,CAAC,cAAc,CAAE;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACkD,OAAO,GAAG/C,MAAM,CAAC,cAAc,CAAE;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACmD,iBAAiB,GAAGhD,MAAM,CAAC,cAAc,CAAE;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACoD,iBAAiB,GAAGjD,MAAM,CAAC,cAAc,CAAE;AACnD;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACqD,cAAc,GAAGlD,MAAM,CAAC,cAAc,CAAE;AAChD;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACsD,cAAc,GAAGnD,MAAM,CAAC,OAAO,CAAE;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACuD,eAAe,GAAGpD,MAAM,CAAC,cAAc,CAAE;AACjD;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACwD,0BAA0B,GAAGrD,MAAM,CAAC,OAAO,CAAE;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACyD,gBAAgB,GAAGtD,MAAM,CAAC,OAAO,CAAE;AAC3C;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC0D,iBAAiB,GAAGvD,MAAM,CAAC,cAAc,CAAE;AACnD;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC2D,eAAe,GAAGxD,MAAM,CAAC,cAAc,CAAE;AACjD;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC4D,yBAAyB,GAAGzD,MAAM,CAAC,OAAO,CAAE;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC6D,8BAA8B,GAAG1D,MAAM,CAAC,OAAO,CAAE;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC8D,sBAAsB,GAAG3D,MAAM,CAAC,cAAc,CAAE;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC+D,WAAW,GAAG5D,MAAM,CAAC,OAAO,CAAE;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACgE,aAAa,GAAG7D,MAAM,CAAC,OAAO,CAAE;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAMDH,OAAO,CAACiE,wBAAwB,GAAG9D,MAAM,CAAC,cAAc,CAAE;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAKDH,OAAO,CAACkE,yBAAyB,GAAG/D,MAAM,CAAC,cAAc,CAAE;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAMDH,OAAO,CAACmE,wBAAwB,GAAGhE,MAAM,CAAC,cAAc,CAAE;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACoE,yBAAyB,GAAGjE,MAAM,CAAC,cAAc,CAAE;AAC3D;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACqE,0BAA0B,GAAGlE,MAAM,CAAC,cAAc,CAAE;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACsE,oBAAoB,GAAGnE,MAAM,CAAC,cAAc,CAAE;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACuE,oBAAoB,GAAGpE,MAAM,CAAC,cAAc,CAAE;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACwE,+BAA+B,GAAGrE,MAAM,CAAC,OAAO,CAAE;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACyE,2BAA2B,GAAGtE,MAAM,CAAC,SAAS,CAAE;AACxD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC0E,8BAA8B,GAAGvE,MAAM,CAAC,OAAO,CAAE;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC2E,8BAA8B,GAAGxE,MAAM,CAAC,OAAO,CAAE;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC4E,2BAA2B,GAAGzE,MAAM,CAAC,OAAO,CAAE;AACtD;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC6E,2BAA2B,GAAG1E,MAAM,CAAC,OAAO,CAAE;AACtD;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC8E,uBAAuB,GAAG3E,MAAM,CAAC,SAAS,CAAE;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAAC+E,uBAAuB,GAAG5E,MAAM,CAAC,SAAS,CAAE;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACgF,kCAAkC,GAAG7E,MAAM,CAAC,SAAS,CAAE;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACiF,qCAAqC,GAAG9E,MAAM,CAAC,SAAS,CAAE;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACkF,6BAA6B,GAAG/E,MAAM,CAAC,SAAS,CAAE;AAC1D;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACmF,sCAAsC,GAAGhF,MAAM,CAAC,SAAS,CAAE;AACnE;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACoF,QAAQ,GAAGjF,MAAM,CAAC,OAAO,CAAE;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACqF,qBAAqB,GAAGlF,MAAM,CAAC,OAAO,CAAE;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACsF,yBAAyB,GAAGnF,MAAM,CAAC,QAAQ,CAAE;AACrD;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACuF,yBAAyB,GAAGpF,MAAM,CAAC,QAAQ,CAAE;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEDH,OAAO,CAACwF,0BAA0B,GAAGrF,MAAM,CAAC,QAAQ,CAAE;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAEkC;EAEjCH,OAAO,CAACyF,qBAAqB,GAAGtF,MAAM,CAAC,OAAO,CAAE;AAClD;AACA;AACA;AACA,GAAG;AACH;AAEAH,OAAO,CAAC0F,QAAQ,GAAGvF,MAAM,CAAC,QAAQ,CAAE;AACpC;AACA;AACA;AACA,CAAC"}