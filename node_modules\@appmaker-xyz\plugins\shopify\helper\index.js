import dayjs from 'dayjs';
import { simpleLocalStore as store } from '@appmaker-xyz/react-native';
import { wishlistStoreApi } from '@appmaker-xyz/shopify';

const isCartExpired = ({ settings, checkout }) => {
  const canCartExpire = settings?.can_cart_expire;
  const cartExpireDays = settings?.cart_expire_days;
  const canClearCartByDate = settings?.clear_cart_by_date;
  const cartExpiresDate = settings?.cart_expire_date;
  const createdAt = checkout?.createdAt;
  let shouldClearCart = false;
  try {
    // Case 0  if createdAt has no value && canClearCartByDate is true  need to clear the cart
    if (!createdAt && canClearCartByDate && checkout?.id) {
      shouldClearCart = true;
    }

    // Case 1 clearing based on # of days
    if (createdAt && canCartExpire && cartExpireDays) {
      if (
        typeof parseInt(cartExpireDays) === 'number' &&
        typeof dayjs().diff(dayjs(createdAt), 'days') === 'number' &&
        dayjs().diff(dayjs(createdAt), 'days') >= parseInt(cartExpireDays)
      ) {
        shouldClearCart = true;
      }
    }
    // Case 2 clearing based on date
    if (
      canClearCartByDate &&
      cartExpiresDate &&
      checkout?.id &&
      dayjs(cartExpiresDate)
    ) {
      if (
        (typeof dayjs(createdAt).isBefore(cartExpiresDate) === 'boolean' ||
          typeof dayjs(createdAt).isBefore(cartExpiresDate) === 'Boolean') &&
        dayjs(createdAt).isBefore(cartExpiresDate)
      ) {
        shouldClearCart = true;
      }
    }
  } catch (e) {}
  return shouldClearCart;
};

const migrateWishlistToV2 = async () => {
  const oldWishlist = await store.get('saved-products');
  if (oldWishlist) {
    const idArrays = Object.keys(oldWishlist).map((id) => {
      return id;
    });
    if (Array.isArray(idArrays)) {
      wishlistStoreApi?.()
        ?.getState()
        ?.addToWishlistInBatch?.('default', idArrays);
      store.save('saved-products', null);
    }
  }
};
export function imageResize(imgUrl, { width, height }) {
  if (imgUrl && width && height) {
    const sizeLower = `${width}x${height}`;
    let separatorIndex = imgUrl.lastIndexOf('.');
    return `${imgUrl.slice(0, separatorIndex)}_${sizeLower}${imgUrl.slice(
      separatorIndex,
    )}`;
  }
  return imgUrl;
}

export { isCartExpired, migrateWishlistToV2 };
