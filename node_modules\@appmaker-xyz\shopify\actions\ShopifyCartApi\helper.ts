import { appStorageApi } from '@appmaker-xyz/core';
const CART_STORAGE_KEY = 'shopifyCart';
const CART_STORAGE_KEY_LEGACY = 'checkout';
const CART_RESTORE_STORAGE_KEY = 'shopifyCartRestore';
import { shopifyCartToLegacyCart } from '../../helper/helper';

export const saveCart = (cart) => {
  appStorageApi().setState({
    [CART_STORAGE_KEY]: cart,
  });
  // save the cart in the legacy storage key
  const cartLegacy = shopifyCartToLegacyCart(cart);
  appStorageApi().setState({
    [CART_STORAGE_KEY_LEGACY]: cartLegacy,
  });
};
export const clearCart = () => {
  appStorageApi().setState({
    [CART_STORAGE_KEY]: null,
  });
  appStorageApi().setState({
    [CART_STORAGE_KEY_LEGACY]: null,
  });
};
export const saveRestoreCart = (cart) => {
  appStorageApi().setState({
    [CART_RESTORE_STORAGE_KEY]: cart,
  });
};
export const clearRestoreCart = () => {
  appStorageApi().setState({
    [CART_RESTORE_STORAGE_KEY]: null,
  });
};

export const getCart = () => {
  return appStorageApi().getState()[CART_STORAGE_KEY];
};
