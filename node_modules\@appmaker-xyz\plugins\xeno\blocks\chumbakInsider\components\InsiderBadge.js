import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ThemeText, AppImage } from '@appmaker-xyz/ui';
import { appStorageApi, usePluginStore } from '@appmaker-xyz/core';

const InsiderBadge = ({ attributes, onPress, pageDispatch, onAction }) => {
  const { specialPrice } = attributes;
  const { settings } = usePluginStore((state) => state.plugins.xeno);
  const user = appStorageApi().getState().user;
  const startsWith = (arr, str) => {
    return arr.some((item) => item.startsWith(str));
  };
  const insider =
    user && user?.tags?.length > 0
      ? startsWith(
          user.tags,
          settings?.userTagsForXenoStartsWith || 'Xeno Tier:Member',
        )
      : false;
  return (
    <>
      {insider === true ? (
        <Layout style={styles.chumbakInsider}>
          <ThemeText fontFamily="bold" size="sm">
            {specialPrice} for{' '}
          </ThemeText>
          <AppImage
            uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
            style={styles.insiderImage}
          />
          <ThemeText fontFamily="bold" size="sm">
            members
          </ThemeText>
        </Layout>
      ) : null}
    </>
  );
};

const styles = StyleSheet.create({
  chumbakInsider: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#B6DBC9',
    paddingVertical: 4,
    paddingHorizontal: 12,
    //   marginTop: 4,
    marginBottom: 6,
  },
  insiderImage: {
    width: 60,
    height: 20,
    resizeMode: 'contain',
  },
});

export default InsiderBadge;
