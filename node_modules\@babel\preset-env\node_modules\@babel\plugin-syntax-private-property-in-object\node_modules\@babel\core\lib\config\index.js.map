{"version": 3, "names": ["_gensync", "data", "require", "_full", "_partial", "_item", "loadOptionsRunner", "gens<PERSON>", "opts", "_config$options", "config", "loadFullConfig", "options", "createConfigItemRunner", "createConfigItemImpl", "maybeErrback", "runner", "arg<PERSON>r<PERSON><PERSON><PERSON>", "maybe<PERSON><PERSON><PERSON>", "arg", "callback", "undefined", "sync", "errback", "loadPartialConfig", "loadPartialConfigRunner", "exports", "loadPartialConfigSync", "loadPartialConfigAsync", "async", "loadOptions", "loadOptionsSync", "loadOptionsAsync", "createConfigItemSync", "createConfigItemAsync", "createConfigItem", "target"], "sources": ["../../src/config/index.ts"], "sourcesContent": ["import gensync, { type Handler, type Callback } from \"gensync\";\n\nexport type {\n  ResolvedConfig,\n  InputOptions,\n  PluginPasses,\n  Plugin,\n} from \"./full\";\n\nimport type { PluginTarget } from \"./validation/options\";\n\nimport type {\n  PluginAP<PERSON> as basePluginAPI,\n  PresetAPI as basePresetAPI,\n} from \"./helpers/config-api\";\nexport type { PluginObject } from \"./validation/plugins\";\ntype PluginAPI = basePluginAPI & typeof import(\"..\");\ntype PresetAPI = basePresetAPI & typeof import(\"..\");\nexport type { PluginAPI, PresetAPI };\n// todo: may need to refine PresetObject to be a subset of ValidatedOptions\nexport type {\n  CallerMetadata,\n  ValidatedOptions as PresetObject,\n} from \"./validation/options\";\n\nimport loadFullConfig, { type ResolvedConfig } from \"./full\";\nimport { loadPartialConfig as loadPartialConfigRunner } from \"./partial\";\n\nexport { loadFullConfig as default };\nexport type { PartialConfig } from \"./partial\";\n\nimport { createConfigItem as createConfigItemImpl } from \"./item\";\nimport type { ConfigItem } from \"./item\";\n\nconst loadOptionsRunner = gensync(function* (\n  opts: unknown,\n): Handler<ResolvedConfig | null> {\n  const config = yield* loadFullConfig(opts);\n  // NOTE: We want to return \"null\" explicitly, while ?. alone returns undefined\n  return config?.options ?? null;\n});\n\nconst createConfigItemRunner = gensync(createConfigItemImpl);\n\nconst maybeErrback =\n  <Arg, Return>(runner: gensync.Gensync<[Arg], Return>) =>\n  (argOrCallback: Arg | Callback<Return>, maybeCallback?: Callback<Return>) => {\n    let arg: Arg | undefined;\n    let callback: Callback<Return>;\n    if (maybeCallback === undefined && typeof argOrCallback === \"function\") {\n      callback = argOrCallback as Callback<Return>;\n      arg = undefined;\n    } else {\n      callback = maybeCallback;\n      arg = argOrCallback as Arg;\n    }\n    if (!callback) {\n      return runner.sync(arg);\n    }\n    runner.errback(arg, callback);\n  };\n\nexport const loadPartialConfig = maybeErrback(loadPartialConfigRunner);\nexport const loadPartialConfigSync = loadPartialConfigRunner.sync;\nexport const loadPartialConfigAsync = loadPartialConfigRunner.async;\n\nexport const loadOptions = maybeErrback(loadOptionsRunner);\nexport const loadOptionsSync = loadOptionsRunner.sync;\nexport const loadOptionsAsync = loadOptionsRunner.async;\n\nexport const createConfigItemSync = createConfigItemRunner.sync;\nexport const createConfigItemAsync = createConfigItemRunner.async;\nexport function createConfigItem(\n  target: PluginTarget,\n  options: Parameters<typeof createConfigItemImpl>[1],\n  callback?: (err: Error, val: ConfigItem | null) => void,\n) {\n  if (callback !== undefined) {\n    createConfigItemRunner.errback(target, options, callback);\n  } else if (typeof options === \"function\") {\n    createConfigItemRunner.errback(target, undefined, callback);\n  } else {\n    return createConfigItemRunner.sync(target, options);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAyBA,IAAAE,KAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAKA,IAAAG,KAAA,GAAAH,OAAA;AAGA,MAAMI,iBAAiB,GAAGC,UAAO,CAAC,WAChCC,IAAa,EACmB;EAAA,IAAAC,eAAA;EAChC,MAAMC,MAAM,GAAG,OAAO,IAAAC,aAAc,EAACH,IAAI,CAAC;EAE1C,QAAAC,eAAA,GAAOC,MAAM,oBAANA,MAAM,CAAEE,OAAO,YAAAH,eAAA,GAAI,IAAI;AAChC,CAAC,CAAC;AAEF,MAAMI,sBAAsB,GAAGN,UAAO,CAACO,sBAAoB,CAAC;AAE5D,MAAMC,YAAY,GACFC,MAAsC,IACpD,CAACC,aAAqC,EAAEC,aAAgC,KAAK;EAC3E,IAAIC,GAAoB;EACxB,IAAIC,QAA0B;EAC9B,IAAIF,aAAa,KAAKG,SAAS,IAAI,OAAOJ,aAAa,KAAK,UAAU,EAAE;IACtEG,QAAQ,GAAGH,aAAiC;IAC5CE,GAAG,GAAGE,SAAS;EACjB,CAAC,MAAM;IACLD,QAAQ,GAAGF,aAAa;IACxBC,GAAG,GAAGF,aAAoB;EAC5B;EACA,IAAI,CAACG,QAAQ,EAAE;IACb,OAAOJ,MAAM,CAACM,IAAI,CAACH,GAAG,CAAC;EACzB;EACAH,MAAM,CAACO,OAAO,CAACJ,GAAG,EAAEC,QAAQ,CAAC;AAC/B,CAAC;AAEI,MAAMI,iBAAiB,GAAGT,YAAY,CAACU,0BAAuB,CAAC;AAACC,OAAA,CAAAF,iBAAA,GAAAA,iBAAA;AAChE,MAAMG,qBAAqB,GAAGF,0BAAuB,CAACH,IAAI;AAACI,OAAA,CAAAC,qBAAA,GAAAA,qBAAA;AAC3D,MAAMC,sBAAsB,GAAGH,0BAAuB,CAACI,KAAK;AAACH,OAAA,CAAAE,sBAAA,GAAAA,sBAAA;AAE7D,MAAME,WAAW,GAAGf,YAAY,CAACT,iBAAiB,CAAC;AAACoB,OAAA,CAAAI,WAAA,GAAAA,WAAA;AACpD,MAAMC,eAAe,GAAGzB,iBAAiB,CAACgB,IAAI;AAACI,OAAA,CAAAK,eAAA,GAAAA,eAAA;AAC/C,MAAMC,gBAAgB,GAAG1B,iBAAiB,CAACuB,KAAK;AAACH,OAAA,CAAAM,gBAAA,GAAAA,gBAAA;AAEjD,MAAMC,oBAAoB,GAAGpB,sBAAsB,CAACS,IAAI;AAACI,OAAA,CAAAO,oBAAA,GAAAA,oBAAA;AACzD,MAAMC,qBAAqB,GAAGrB,sBAAsB,CAACgB,KAAK;AAACH,OAAA,CAAAQ,qBAAA,GAAAA,qBAAA;AAC3D,SAASC,gBAAgBA,CAC9BC,MAAoB,EACpBxB,OAAmD,EACnDQ,QAAuD,EACvD;EACA,IAAIA,QAAQ,KAAKC,SAAS,EAAE;IAC1BR,sBAAsB,CAACU,OAAO,CAACa,MAAM,EAAExB,OAAO,EAAEQ,QAAQ,CAAC;EAC3D,CAAC,MAAM,IAAI,OAAOR,OAAO,KAAK,UAAU,EAAE;IACxCC,sBAAsB,CAACU,OAAO,CAACa,MAAM,EAAEf,SAAS,EAAED,QAAQ,CAAC;EAC7D,CAAC,MAAM;IACL,OAAOP,sBAAsB,CAACS,IAAI,CAACc,MAAM,EAAExB,OAAO,CAAC;EACrD;AACF;AAAC"}