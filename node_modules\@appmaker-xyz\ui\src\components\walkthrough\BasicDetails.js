import React, { useState } from 'react';
import { KeyboardAvoidingView, Platform, StyleSheet } from 'react-native';
import { AppImage, AppTouchable, Layout, ThemeText } from '../atoms';
import { Button, Input } from '../coreComponents';

const Onboarding = ({ attributes, onPress, onAction }) => {
  const { image, buttonColor, appmakerAction } = attributes;
  var [name, setName] = useState('');
  var [email, setEmail] = useState('');

  const buttonBgColor = buttonColor ? buttonColor : '#1B1B1B';
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      onAction(appmakerAction);
    };
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}>
      <Layout style={styles.innerContainer}>
        <ThemeText size="lg" fontFamily="bold" style={styles.title}>
          Basic Details
        </ThemeText>
        <Layout
          style={[
            styles.initial,
            {
              backgroundColor: buttonBgColor ? buttonBgColor : '#1B1B1B',
            },
          ]}>
          {image ? (
            <AppImage uri={image} style={styles.image} resizeMode="cover" />
          ) : (
            <ThemeText size="xl" color={'#ffffff'}>
              {name ? Array.from(name)[0].toUpperCase() : '#'}
            </ThemeText>
          )}
          <AppTouchable
            style={styles.update}
            onPress={() => console.log('Update image')}>
            <ThemeText size="xs" color="#ffffff" style={styles.text}>
              Update
            </ThemeText>
          </AppTouchable>
        </Layout>

        <Layout style={styles.contentContainer}>
          <Input label="Full Name" value={name} onChange={(e) => setName(e)} />
          <Input label="Email" value={email} onChange={(i) => setEmail(i)} />
          <Button
            title="Save & Continue"
            buttonStyle={styles.button}
            color={buttonBgColor}
            onPress={onPressHandle}
          />
        </Layout>
      </Layout>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  innerContainer: {
    flex: 1,
    paddingHorizontal: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  initial: {
    borderRadius: 100,
    overflow: 'hidden',
    width: 90,
    height: 90,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 32,
    position: 'relative',
  },
  title: {
    textAlign: 'center',
    marginBottom: 24,
  },
  contentContainer: { width: '100%' },
  button: {
    borderWidth: 0,
  },
  update: {
    backgroundColor: '#1B1B1B80',
    width: '100%',
    textAlign: 'center',
    alignSelf: 'flex-end',
    paddingBottom: 4,
    position: 'absolute',
    bottom: 0,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  text: { textAlign: 'center' },
});

export default Onboarding;
