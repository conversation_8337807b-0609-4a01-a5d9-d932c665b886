{"version": 3, "names": ["numericLiteral", "unaryExpression", "rewriteThisVisitor", "traverse", "visitors", "merge", "environmentVisitor", "ThisExpression", "path", "replaceWith", "rewriteThis", "programPath", "node", "noScope"], "sources": ["../src/rewrite-this.ts"], "sourcesContent": ["import environmentVisitor from \"@babel/helper-environment-visitor\";\nimport traverse from \"@babel/traverse\";\nimport { numericLiteral, unaryExpression } from \"@babel/types\";\n\nimport type { NodePath, Visitor } from \"@babel/traverse\";\n\n/**\n * A visitor to walk the tree, rewriting all `this` references in the top-level scope to be\n * `void 0` (undefined).\n */\nconst rewriteThisVisitor: Visitor = traverse.visitors.merge([\n  environmentVisitor,\n  {\n    ThisExpression(path) {\n      path.replaceWith(unaryExpression(\"void\", numericLiteral(0), true));\n    },\n  },\n]);\n\nexport default function rewriteThis(programPath: NodePath) {\n  // Rewrite \"this\" to be \"undefined\".\n  traverse(programPath.node, { ...rewriteThisVisitor, noScope: true });\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AAA+D;EAAtDA,cAAc;EAAEC;AAAe;AAQxC,MAAMC,kBAA2B,GAAGC,iBAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CAC1DC,iCAAkB,EAClB;EACEC,cAAc,CAACC,IAAI,EAAE;IACnBA,IAAI,CAACC,WAAW,CAACR,eAAe,CAAC,MAAM,EAAED,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;EACpE;AACF,CAAC,CACF,CAAC;AAEa,SAASU,WAAW,CAACC,WAAqB,EAAE;EAEzD,IAAAR,iBAAQ,EAACQ,WAAW,CAACC,IAAI,oBAAOV,kBAAkB;IAAEW,OAAO,EAAE;EAAI,GAAG;AACtE"}