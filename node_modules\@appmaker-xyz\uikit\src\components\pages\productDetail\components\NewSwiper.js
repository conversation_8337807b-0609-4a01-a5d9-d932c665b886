import { useApThemeState } from '@appmaker-xyz/uikit/src/index';
import React from 'react';
import { useRef } from 'react';
import { useEffect } from 'react';
import { useState } from 'react';
import {
  Text,
  Dimensions,
  StyleSheet,
  View,
  Image,
  ActivityIndicator,
} from 'react-native';
import { SwiperFlatList } from 'react-native-swiper-flatlist';
import { AppTouchable } from '../../../index';
import { Layout, AppmakerText } from '../../../index';
import { AppImage } from '../../../molecules/index';

// const colors = ['tomato', 'thistle', 'skyblue', 'teal'];

const allStyles = ({
  spacing,
  color,
  customDotColor,
  centerPagination,
  smallPagination,
}) =>
  StyleSheet.create({
    imageWrapper: {
      width: '100%',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: color.white,
      position: 'relative',
    },
    productImage: {
      width: '100%',
      height: '100%',
    },
    inactiveDotStyle: {
      backgroundColor: color.grey,
    },
    activeDotStyle: {
      backgroundColor: customDotColor ? customDotColor : color.primary,
      width: smallPagination ? 12 : spacing.md,
      height: smallPagination ? 6 : spacing.small,
      borderRadius: spacing.nano,
    },
    dotStyle: {
      backgroundColor: color.light,
      width: smallPagination ? 6 : spacing.small,
      height: smallPagination ? 6 : spacing.small,
      borderRadius: spacing.nano,
      marginRight: -4,
    },
    dotContainer: {
      bottom: smallPagination ? -15 : 4,
      left: centerPagination ? null : 4,
    },
    maxWidth: {
      maxWidth: '90%',
      overflow: 'hidden',
    },
    contentContainer: {
      position: 'absolute',
      backgroundColor: `${color.dark}50`,
      width: '100%',
      height: '100%',
      justifyContent: 'flex-end',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.lg * 2,
    },
    title: {
      textAlign: 'left',
      textShadowColor: `${color.demiDark}50`,
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
  });
const ImageItem = ({
  item,
  showLoadingIndicator,
  onPressHandle,
  resizeMode,
  title,
}) => {
  const { color, spacing } = useApThemeState();

  const styles = allStyles({ color, spacing });

  const uri = typeof item === 'string' ? item : item.uri;
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    let isMounted = true; // note mutable flag
    if (showLoadingIndicator) {
      // FastImage.prefetch(uri)
      //   .then(() => {
      //     if (isMounted) {
      //       setLoading(false);
      //     } // add conditional check
      //   })
      //   .then(() => {
      //     if (isMounted) {
      //       setLoading(false);
      //     } // add conditional check
      //   });
    }
    return () => {
      isMounted = false;
    }; // use cleanup to toggle value, if unmounted
  }, []);
  return (
    <AppTouchable style={styles.imageWrapper} onPress={onPressHandle}>
      {!loading ? (
        <AppImage
          uri={uri}
          style={styles.productImage}
          resizeMode={resizeMode}
          fastImage={false}
        />
      ) : (
        <View>
          <ActivityIndicator size="small" color="#0000ff" />
        </View>
      )}
      {title && (
        <Layout style={styles.contentContainer}>
          <AppmakerText
            category="h1Heading"
            status="white"
            style={styles.title}
            numberOfLines={3}>
            {item.title}
          </AppmakerText>
          <AppmakerText status="light" category="highlighter1">
            {item.authorName} | {item.timeStamp}
          </AppmakerText>
        </Layout>
      )}
    </AppTouchable>
  );
};
const getItemLayout = (data, index) => ({
  length: Dimensions.get('window').width,
  offset: Dimensions.get('window').width * index,
  index,
});

const NewSwiper = ({ attributes, onPress, onAction }) => {
  const {
    imageList,
    autoplay,
    resizeMode = 'contain',
    title,
    goToIndexNeeded = true,
    appmakerAction,
    showsButtons,
    showLoadingIndicator,
    customDotColor,
    centerPagination = false,
    smallPagination = false,
  } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({
    color,
    spacing,
    customDotColor,
    centerPagination,
    smallPagination,
  });
  const swiperRef = useRef();
  useEffect(() => {
    if (swiperRef.current && imageList.length > 0) {
      setTimeout(() => {
        if (attributes.index - 1 >= 0) {
          try {
            if (goToIndexNeeded) {
              swiperRef.current.scrollToIndex({ index: attributes.index - 1 });
            } else {
              swiperRef.current.scrollToIndex({ index: 0 });
            }
          } catch (error) {
            console.log(error);
          }
        }
      }, 500);
    }
  }, [attributes.index]);

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      const newAction = {
        ...appmakerAction,
        params: {
          ...appmakerAction.params,
          indexImage: swiperRef.current.getCurrentIndex(),
        },
      };
      onAction(newAction);
    };
  }
  return (
    <View style={styles.container}>
      <SwiperFlatList
        getItemLayout={getItemLayout}
        ref={swiperRef}
        showPagination
        paginationStyle={[
          styles.dotContainer,
          imageList.length >= 24 && styles.maxWidth,
        ]}
        paginationStyleItem={styles.dotStyle}
        paginationStyleItemInactive={styles.inactiveDotStyle}
        paginationStyleItemActive={styles.activeDotStyle}
        data={imageList}
        renderItem={({ item }) => (
          <View style={[stylesRoot.child]}>
            <ImageItem
              // key={key}
              item={item}
              showLoadingIndicator={showLoadingIndicator}
              onPressHandle={onPressHandle}
              title={title}
              resizeMode={resizeMode}
            />
          </View>
        )}
      />
    </View>
  );
};

const { width } = Dimensions.get('window');
const stylesRoot = StyleSheet.create({
  container: { flex: 1, backgroundColor: 'white' },
  child: { width, justifyContent: 'center' },
  text: { fontSize: width * 0.1, textAlign: 'center' },
});

export default NewSwiper;
