import React, { useEffect } from 'react';
import { BlockCard } from '../../../../../components';
import ProductVariations from './ProductVariations';
import { usePageState } from '@appmaker-xyz/core';

const CounterBlock = ({ attributes, clientId, pageState, ...props }) => {
  const { blockTitle, accessButton, variationKey } = attributes;
  const selectedItem = usePageState(
    (state) => state.filter && state.filter[variationKey],
  );
  if (pageState?.state?.filteredVariations) {
    if (
      pageState?.state?.filteredVariations[attributes.variationKey] &&
      Array.isArray(
        pageState?.state?.filteredVariations[attributes.variationKey],
      )
    ) {
      attributes.variations =
        pageState?.state?.filteredVariations[attributes.variationKey];
    }
  }
  useEffect(() => {
    if (!selectedItem && attributes.defaultOptionValue) {
      props?.pageDispatch({
        type: 'set_filter',
        variationKey: attributes.variationKey,
        variationValue: attributes.defaultOptionValue,
        filter: { [attributes.variationKey]: attributes.defaultOptionValue },
      });
    }
  }, [attributes.defaultOptionValue]);
  function onChange(value) {
    const variation = { [attributes.variationKey]: value };
    props?.pageDispatch({
      type: 'set_filter',
      variationKey: attributes.variationKey,
      variationValue: value,
      filter: variation,
    });
  }

  return (
    <BlockCard
      clientId={clientId}
      attributes={{ title: blockTitle, accessButton: accessButton }}>
      <ProductVariations
        testId={clientId}
        attributes={attributes}
        selectedItem={selectedItem}
        onChange={onChange}
      />
    </BlockCard>
  );
};
export default CounterBlock;
