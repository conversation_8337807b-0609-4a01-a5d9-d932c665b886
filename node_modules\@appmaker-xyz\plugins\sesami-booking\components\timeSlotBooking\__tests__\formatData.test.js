function formatDataForProduct(jsonData) {
  const formated = {};
  Object.keys(jsonData).forEach((key) => {
    if (jsonData[key] === '' || !key.match(/properties/)) {
    } else {
      const newKey = key.replace('properties[', '').replace(']', '');
      formated[newKey] = jsonData[key];
    }
  });
  return formated;
}

test('should formatData', () => {
  const attributes = {
    'properties[Date]': '2022-05-21',
    'properties[Time]': '06:30',
    'properties[Timezone]': 'Asia/Calcutta',
    'seasami-time-slot': '0',
  };
  const newRestponse = formatDataForProduct(attributes);
  expect(newRestponse).toEqual({
    Date: '2022-05-21',
    Time: '06:30',
    Timezone: 'Asia/Calcutta',
  });
});
