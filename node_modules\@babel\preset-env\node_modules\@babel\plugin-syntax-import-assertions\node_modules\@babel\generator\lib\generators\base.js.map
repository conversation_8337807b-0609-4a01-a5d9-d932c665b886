{"version": 3, "names": ["File", "node", "program", "print", "interpreter", "Program", "_node$directives", "noIndentInnerCommentsHere", "printInnerComments", "directivesLen", "directives", "length", "_node$directives$trai", "newline", "body", "printSequence", "trailingCommentsLineOffset", "trailingComments", "BlockStatement", "_node$directives2", "token", "_node$directives$trai2", "indent", "sourceWithOffset", "loc", "rightBrace", "Directive", "value", "semicolon", "unescapedSingleQuoteRE", "unescapedDoubleQuoteRE", "DirectiveLiteral", "raw", "getPossibleRaw", "format", "minified", "undefined", "test", "Error", "InterpreterDirective", "Placeholder", "name", "expectedNode"], "sources": ["../../src/generators/base.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport type * as t from \"@babel/types\";\n\nexport function File(this: Printer, node: t.File) {\n  if (node.program) {\n    // Print this here to ensure that Program node 'leadingComments' still\n    // get printed after the hashbang.\n    this.print(node.program.interpreter, node);\n  }\n\n  this.print(node.program, node);\n}\n\nexport function Program(this: Printer, node: t.Program) {\n  // An empty Program doesn't have any inner tokens, so\n  // we must explicitly print its inner comments.\n  this.noIndentInnerCommentsHere();\n  this.printInnerComments();\n\n  const directivesLen = node.directives?.length;\n  if (directivesLen) {\n    const newline = node.body.length ? 2 : 1;\n    this.printSequence(node.directives, node, {\n      trailingCommentsLineOffset: newline,\n    });\n    if (!node.directives[directivesLen - 1].trailingComments?.length) {\n      this.newline(newline);\n    }\n  }\n\n  this.printSequence(node.body, node);\n}\n\nexport function BlockStatement(this: Printer, node: t.BlockStatement) {\n  this.token(\"{\");\n\n  const directivesLen = node.directives?.length;\n  if (directivesLen) {\n    const newline = node.body.length ? 2 : 1;\n    this.printSequence(node.directives, node, {\n      indent: true,\n      trailingCommentsLineOffset: newline,\n    });\n    if (!node.directives[directivesLen - 1].trailingComments?.length) {\n      this.newline(newline);\n    }\n  }\n\n  this.printSequence(node.body, node, { indent: true });\n\n  this.sourceWithOffset(\"end\", node.loc, 0, -1);\n\n  this.rightBrace();\n}\n\nexport function Directive(this: Printer, node: t.Directive) {\n  this.print(node.value, node);\n  this.semicolon();\n}\n\n// These regexes match an even number of \\ followed by a quote\nconst unescapedSingleQuoteRE = /(?:^|[^\\\\])(?:\\\\\\\\)*'/;\nconst unescapedDoubleQuoteRE = /(?:^|[^\\\\])(?:\\\\\\\\)*\"/;\n\nexport function DirectiveLiteral(this: Printer, node: t.DirectiveLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.token(raw);\n    return;\n  }\n\n  const { value } = node;\n\n  // NOTE: In directives we can't change escapings,\n  // because they change the behavior.\n  // e.g. \"us\\x65 strict\" (\\x65 is e) is not a \"use strict\" directive.\n\n  if (!unescapedDoubleQuoteRE.test(value)) {\n    this.token(`\"${value}\"`);\n  } else if (!unescapedSingleQuoteRE.test(value)) {\n    this.token(`'${value}'`);\n  } else {\n    throw new Error(\n      \"Malformed AST: it is not possible to print a directive containing\" +\n        \" both unescaped single and double quotes.\",\n    );\n  }\n}\n\nexport function InterpreterDirective(\n  this: Printer,\n  node: t.InterpreterDirective,\n) {\n  this.token(`#!${node.value}`);\n  this.newline(1, true);\n}\n\nexport function Placeholder(this: Printer, node: t.Placeholder) {\n  this.token(\"%%\");\n  this.print(node.name);\n  this.token(\"%%\");\n\n  if (node.expectedNode === \"Statement\") {\n    this.semicolon();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;AAGO,SAASA,IAAIA,CAAgBC,IAAY,EAAE;EAChD,IAAIA,IAAI,CAACC,OAAO,EAAE;IAGhB,IAAI,CAACC,KAAK,CAACF,IAAI,CAACC,OAAO,CAACE,WAAW,EAAEH,IAAI,CAAC;EAC5C;EAEA,IAAI,CAACE,KAAK,CAACF,IAAI,CAACC,OAAO,EAAED,IAAI,CAAC;AAChC;AAEO,SAASI,OAAOA,CAAgBJ,IAAe,EAAE;EAAA,IAAAK,gBAAA;EAGtD,IAAI,CAACC,yBAAyB,EAAE;EAChC,IAAI,CAACC,kBAAkB,EAAE;EAEzB,MAAMC,aAAa,IAAAH,gBAAA,GAAGL,IAAI,CAACS,UAAU,qBAAfJ,gBAAA,CAAiBK,MAAM;EAC7C,IAAIF,aAAa,EAAE;IAAA,IAAAG,qBAAA;IACjB,MAAMC,OAAO,GAAGZ,IAAI,CAACa,IAAI,CAACH,MAAM,GAAG,CAAC,GAAG,CAAC;IACxC,IAAI,CAACI,aAAa,CAACd,IAAI,CAACS,UAAU,EAAET,IAAI,EAAE;MACxCe,0BAA0B,EAAEH;IAC9B,CAAC,CAAC;IACF,IAAI,GAAAD,qBAAA,GAACX,IAAI,CAACS,UAAU,CAACD,aAAa,GAAG,CAAC,CAAC,CAACQ,gBAAgB,aAAnDL,qBAAA,CAAqDD,MAAM,GAAE;MAChE,IAAI,CAACE,OAAO,CAACA,OAAO,CAAC;IACvB;EACF;EAEA,IAAI,CAACE,aAAa,CAACd,IAAI,CAACa,IAAI,EAAEb,IAAI,CAAC;AACrC;AAEO,SAASiB,cAAcA,CAAgBjB,IAAsB,EAAE;EAAA,IAAAkB,iBAAA;EACpE,IAAI,CAACC,SAAK,KAAK;EAEf,MAAMX,aAAa,IAAAU,iBAAA,GAAGlB,IAAI,CAACS,UAAU,qBAAfS,iBAAA,CAAiBR,MAAM;EAC7C,IAAIF,aAAa,EAAE;IAAA,IAAAY,sBAAA;IACjB,MAAMR,OAAO,GAAGZ,IAAI,CAACa,IAAI,CAACH,MAAM,GAAG,CAAC,GAAG,CAAC;IACxC,IAAI,CAACI,aAAa,CAACd,IAAI,CAACS,UAAU,EAAET,IAAI,EAAE;MACxCqB,MAAM,EAAE,IAAI;MACZN,0BAA0B,EAAEH;IAC9B,CAAC,CAAC;IACF,IAAI,GAAAQ,sBAAA,GAACpB,IAAI,CAACS,UAAU,CAACD,aAAa,GAAG,CAAC,CAAC,CAACQ,gBAAgB,aAAnDI,sBAAA,CAAqDV,MAAM,GAAE;MAChE,IAAI,CAACE,OAAO,CAACA,OAAO,CAAC;IACvB;EACF;EAEA,IAAI,CAACE,aAAa,CAACd,IAAI,CAACa,IAAI,EAAEb,IAAI,EAAE;IAAEqB,MAAM,EAAE;EAAK,CAAC,CAAC;EAErD,IAAI,CAACC,gBAAgB,CAAC,KAAK,EAAEtB,IAAI,CAACuB,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAE7C,IAAI,CAACC,UAAU,EAAE;AACnB;AAEO,SAASC,SAASA,CAAgBzB,IAAiB,EAAE;EAC1D,IAAI,CAACE,KAAK,CAACF,IAAI,CAAC0B,KAAK,EAAE1B,IAAI,CAAC;EAC5B,IAAI,CAAC2B,SAAS,EAAE;AAClB;AAGA,MAAMC,sBAAsB,GAAG,uBAAuB;AACtD,MAAMC,sBAAsB,GAAG,uBAAuB;AAE/C,SAASC,gBAAgBA,CAAgB9B,IAAwB,EAAE;EACxE,MAAM+B,GAAG,GAAG,IAAI,CAACC,cAAc,CAAChC,IAAI,CAAC;EACrC,IAAI,CAAC,IAAI,CAACiC,MAAM,CAACC,QAAQ,IAAIH,GAAG,KAAKI,SAAS,EAAE;IAC9C,IAAI,CAAChB,KAAK,CAACY,GAAG,CAAC;IACf;EACF;EAEA,MAAM;IAAEL;EAAM,CAAC,GAAG1B,IAAI;EAMtB,IAAI,CAAC6B,sBAAsB,CAACO,IAAI,CAACV,KAAK,CAAC,EAAE;IACvC,IAAI,CAACP,KAAK,CAAE,IAAGO,KAAM,GAAE,CAAC;EAC1B,CAAC,MAAM,IAAI,CAACE,sBAAsB,CAACQ,IAAI,CAACV,KAAK,CAAC,EAAE;IAC9C,IAAI,CAACP,KAAK,CAAE,IAAGO,KAAM,GAAE,CAAC;EAC1B,CAAC,MAAM;IACL,MAAM,IAAIW,KAAK,CACb,mEAAmE,GACjE,2CAA2C,CAC9C;EACH;AACF;AAEO,SAASC,oBAAoBA,CAElCtC,IAA4B,EAC5B;EACA,IAAI,CAACmB,KAAK,CAAE,KAAInB,IAAI,CAAC0B,KAAM,EAAC,CAAC;EAC7B,IAAI,CAACd,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;AACvB;AAEO,SAAS2B,WAAWA,CAAgBvC,IAAmB,EAAE;EAC9D,IAAI,CAACmB,KAAK,CAAC,IAAI,CAAC;EAChB,IAAI,CAACjB,KAAK,CAACF,IAAI,CAACwC,IAAI,CAAC;EACrB,IAAI,CAACrB,KAAK,CAAC,IAAI,CAAC;EAEhB,IAAInB,IAAI,CAACyC,YAAY,KAAK,WAAW,EAAE;IACrC,IAAI,CAACd,SAAS,EAAE;EAClB;AACF"}