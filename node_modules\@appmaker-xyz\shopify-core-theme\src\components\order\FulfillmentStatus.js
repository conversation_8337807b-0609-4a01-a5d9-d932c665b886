import React from 'react';
import { StyleSheet } from 'react-native';
import { ThemeText, Layout } from '@appmaker-xyz/ui';

const FulfillmentStatus = ({
  attributes,
  BlockItem,
  onAction,
  currentAction,
  BlocksView,
  innerBlocks,
  blockData,
}) => {
  const block = {
    name: 'shopify/fulfillment-single-line-item',
    clientId: 'order-fulfill-line-item',
    attributes: {
      dataSource: {
        repeatable: 'Yes',
        attributes: {
          params: '{{blockData.node.successfulFulfillments}}',
        },
        source: 'variables',
        repeatItem: 'DataVariable',
      },
    },
    dependencies: {
      pageState: ['productStatus', 'metaData'],
    },
  };
  if (blockData?.node?.successfulFulfillments?.length === 0) {
    return null;
  }
  return (
    <Layout style={styles.container}>
      <ThemeText size="md" fontFamily="medium">
        Fulfillment Status
      </ThemeText>
      <BlockItem
        BlockItem={BlockItem}
        BlocksView={BlocksView}
        currentAction={currentAction}
        onAction={onAction}
        block={block}
      />
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 12,
    marginBottom: 4,
    padding: 8,
    borderRadius: 4,
  },
});

export default FulfillmentStatus;
