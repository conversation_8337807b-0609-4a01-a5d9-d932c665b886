import { useWindowDimensions, StyleSheet } from 'react-native';
import React from 'react';
import { Layout, Button, AppmakerText, Accordion } from '@appmaker-xyz/uikit';
import { spacing, color, dimensions } from '../../../../../styles';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { useFilterStore } from '../../store';

const CloseIcon = (props) => <Icon name="x" color={color.dark} {...props} />;

export default function SortModalContent({
  attributes = {},
  onSelect,
  closeSort,
}) {
  const { bottomPlacement } = attributes;
  const avilableSortItems = useFilterStore((state) => state.avilableSortItems);
  const setAppliedSort = useFilterStore((state) => state.setAppliedSort);
  const appliedSort = useFilterStore((state) => state.appliedSort);
  const containerStyles = [styles.sortModalBody];
  if (bottomPlacement) {
    containerStyles.push({
      width: dimensions.fullWidth,
      borderBottomLeftRadius: 0,
      borderBottomRightRadius: 0,
    });
  }
  const sortItems = Object.values(avilableSortItems).map((sortItem, key) => {
    let normalButtonProps = {
      status: 'demiDark',
      small: true,
      outline: true,
      block: true,
      onPress: () => {
        setAppliedSort(sortItem);
        onSelect(sortItem);
      },
    };

    return (
      <Layout key={key} style={styles.sortItem}>
        <Button {...normalButtonProps}>{sortItem.label}</Button>
      </Layout>
    );
  });
  return (
    <Layout style={containerStyles}>
      <Layout style={styles.sortModalHeader}>
        <AppmakerText category="bodyParagraphBold" status="demiDark">
          Sort by
        </AppmakerText>
        <Button
          onPress={closeSort}
          status="light"
          accessoryLeft={CloseIcon}
          small
        />
      </Layout>
      <Layout style={styles.sortModalContent}>{sortItems}</Layout>
    </Layout>
  );
}

const styles = StyleSheet.create({
  sortModalBody: {
    width: '80%',
    backgroundColor: color.white,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: spacing.small,
  },
  sortModalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: color.light,
    padding: spacing.base,
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
  },
  sortModalContent: {
    width: '100%',
    padding: spacing.base,
  },
  sortItem: {
    marginVertical: spacing.nano,
  },
});
