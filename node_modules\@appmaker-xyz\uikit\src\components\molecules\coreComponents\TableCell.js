import React from 'react';
import { StyleSheet } from 'react-native';
import { AppmakerText, AppTouchable, Layout } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';

const TableCell = ({ attributes, onPress, onAction }) => {
  const { title, value, iconName, type, appmakerAction, subTitle, noMargin } =
    attributes;
  // console.log(JSON.stringify(attributes.title, null, 2));
  // return null;
  const { color, spacing, fonts, font } = useApThemeState();
  const styles = allStyles({ color, spacing, fonts, font });
  const containerStyles = [styles.container];
  const tableContainerStyles = [styles.tableContainer];

  if (type === 'stacked') {
    containerStyles.push({ flexDirection: 'column' });
  }
  if (type === 'total') {
    tableContainerStyles.push({ backgroundColor: color.light, marginTop: -5 });
  }
  if (noMargin) {
    tableContainerStyles.push({ marginBottom: 0 });
  }
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  // console.log();
  return (
    <Layout style={tableContainerStyles}>
      <AppTouchable onPress={onPressHandle} style={styles.wholeContainer}>
        <Layout style={containerStyles}>
          <Layout style={styles.column}>
            <AppmakerText
              category={type === 'total' ? 'h1Heading' : 'bodyParagraph'}
              status="demiDark">
              {title}
            </AppmakerText>
            {subTitle ? (
              <AppmakerText category="highlighter1" status="grey">
                {subTitle}
              </AppmakerText>
            ) : null}
          </Layout>
          <Layout style={styles.column}>
            <AppmakerText
              category={type === 'total' ? 'h1Heading' : 'bodyParagraphBold'}
              style={styles.textRight}>
              {value}
            </AppmakerText>
          </Layout>
        </Layout>
        {type === 'stacked' && iconName && (
          <Layout style={styles.iconContainer}>
            <Icon name={iconName} size={font.size.md} color={color.grey} />
          </Layout>
        )}
      </AppTouchable>
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    tableContainer: {
      padding: spacing.small,
      borderBottomWidth: 1,
      borderBottomColor: color.light,
      backgroundColor: color.white,
      borderRadius: spacing.nano,
      marginBottom: spacing.nano,
    },
    wholeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    container: {
      flexDirection: 'row',
      paddingVertical: spacing.nano,
      flex: 1,
    },
    column: {
      width: '50%',
    },
    textRight: {
      textAlign: 'right',
    },
    iconContainer: {
      padding: spacing.small,
    },
  });

export default TableCell;
