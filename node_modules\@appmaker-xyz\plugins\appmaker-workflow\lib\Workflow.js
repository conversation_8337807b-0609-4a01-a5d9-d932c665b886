import { onEvent } from '@appmaker-xyz/core';
import { FlowManager } from '@appmaker/workflow/flowed/index';
// const {  } = Flowed;

let workflowMap = {};
const triggers = [];
export function registerWorkflows(workflows) {
  workflowMap = {
    ...workflowMap,
    ...workflows,
  };
}

export async function executeWorkFlow(
  { id, params, workFlowParams },
  actionContext,
) {
  const workflow = workflowMap[id];
  console.log({
    id,
    params,
    workFlowParams,
    context: {
      actionContext,
    },
  });
  try {
    const resultsArray = ['localPushId'];
    const resolvers = {};
    const myLogger = {
      log: (message) => {
        console.log(message.message);
      },
    };

    FlowManager.installLogger(myLogger);
    const results = await FlowManager.run(
      workflow.params,
      workFlowParams,
      resultsArray,
      resolvers,
      {
        actionContext,
      },
    );
    console.log('------result -item-------');
    console.log('results', results);
    console.log('------END-------');
    // console.log(FlowManager);
    // runWorkFlow({ workflow, initialParams: params, workFlowParams });
  } catch (error) {
    console.log('error', error);
  }
}
export function registerTriggers(triggersList) {
  triggersList.forEach((triggerItem) => {
    onEvent(triggerItem.trigger, (workFlowParams) => {
      executeWorkFlow({
        id: triggerItem.workflowId,
        params: triggerItem.params,
        workFlowParams,
      });
    });
  });
}
