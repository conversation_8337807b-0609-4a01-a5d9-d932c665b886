/* eslint-disable no-sparse-arrays */
import { View, Text } from 'react-native';
import React from 'react';
import { SkeletonPlaceholder } from '@appmaker-xyz/uikit/Views/index';
const blocks = [
  {
    width: 110,
    height: 110,
    margin: 6,
    borderRadius: 16,
    child: [
      {
        width: 10,
        height: 110,
        margin: 100,
        borderRadius: 16,
        child: [],
      },
      ,
    ],
  },
  {
    width: 110,
    height: 110,
    margin: 6,
    borderRadius: 16,
    child: [
      {
        width: 10,
        height: 110,
        margin: 100,
        borderRadius: 16,
        child: [],
      },
      ,
    ],
  },
];
function SkeltonItem({ block }) {
    console.log(block,'itemsss');
  return (
    <SkeletonPlaceholder.Item
    width={110}
    height={110}
    margin={6}
    borderRadius={16}
  />
  );
}
export default function SkeletonPlaceholderBlocks() {
    console.log('reee');
  return (
    <View>
      <SkeletonPlaceholder>
        {blocks.map((block, index) => {
          return SkeltonItem({ block });
        })}
         <SkeletonPlaceholder.Item
        width={110}
        height={110}
        margin={6}
        borderRadius={16}
      />
      </SkeletonPlaceholder>
      <Text>SkeletonPlaceholderBlocks</Text>
    </View>
  );
}
