## Create Organisms

Complex components that are composed of Molecules and Atoms. These components are the building blocks of our application. They are used to create more complex components.

### `Product List Item`

path:

`appmaker-start-theme/uikit/components/organisms/ProductListItem.js`

Sample code

```js
import React from 'react';
import { StyleSheet, View, Text, Image, Pressable } from 'react-native';
import ThemeText from '../atoms/ThemeText';
import Badge from '../molecules/Badge';
import { useProductListItem, useProductWishList } from '@appmaker-xyz/shopify';
import Icon from 'react-native-vector-icons/AntDesign';

export default function ProductListItem(props) {
  const {
    title,
    imageUrl,
    regularPrice,
    salePrice,
    imageAspectRatio,
    openProduct,
    containerWidth,
    gridViewListing,
    salePercentage,
  } = useProductListItem(props);
  const { toggleWishList, isSaved } = useProductWishList(props);
  return (
    <Pressable
      onPress={openProduct}
      style={[
        styles.container,
        { width: containerWidth, marginLeft: gridViewListing ? 0 : 12 },
      ]}>
      <View
        style={[styles.imageContainer, { aspectRatio: 1 / imageAspectRatio }]}>
        <Image source={{ uri: imageUrl }} style={styles.image} />
        <View style={styles.bottomRightContainer}>
          <Icon
            name={isSaved ? 'heart' : 'hearto'}
            size={16}
            onPress={toggleWishList}
            style={styles.wishListIcon}
          />
        </View>
        {salePercentage ? (
          <View style={styles.topLeftContainer}>
            <Badge color="#111827">{salePercentage} off</Badge>
          </View>
        ) : null}
      </View>
      <View style={styles.textContainer}>
        <ThemeText
          fontFamily="medium"
          size="md"
          color="#111827"
          numberOfLines={1}>
          {title}
        </ThemeText>
        <View style={styles.priceContainer}>
          <ThemeText fontFamily="bold" color="#111827">
            {salePrice}
          </ThemeText>
          <ThemeText fontFamily="regular" style={styles.regularPriceText}>
            {regularPrice}
          </ThemeText>
        </View>
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {},
  imageContainer: {
    position: 'relative',
  },
  image: {
    height: '100%',
    width: '100%',
  },
  topLeftContainer: {
    position: 'absolute',
    top: 4,
    left: 4,
  },
  bottomRightContainer: {
    position: 'absolute',
    right: 6,
    bottom: 6,
  },
  wishListIcon: {
    padding: 8,
    backgroundColor: 'rgba(255,255,255,0.6)',
    borderRadius: 20,
  },
  textContainer: {
    flex: 1,
    justifyContent: 'space-between',
    paddingTop: 4,
    paddingBottom: 6,
    paddingHorizontal: 2,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginTop: 4,
  },
  regularPriceText: {
    marginLeft: 4,
    color: '#DC2626',
    textDecorationLine: 'line-through',
  },
});
```

Get product list item data

```js
import { useProductListItem } from '@appmaker-xyz/shopify';

...


const {
    title,
    regularPrice,
    salePrice,
    imageUrl,
    imageAspectRatio,
    openProduct,
    containerWidth,
    gridViewListing,
    salePercentage,
    onSale,
  } = useProductListItem(props);
```

`useProductListItem` returns the following data:

| Name             | Type     | Description                          |
| ---------------- | -------- | ------------------------------------ |
| title            | string   | Product title                        |
| regularPrice     | string   | Product regular price                |
| salePrice        | string   | Product sale price                   |
| imageUrl         | string   | Product image url                    |
| imageAspectRatio | number   | Product image aspect ratio           |
| openProduct      | function | Opens product page                   |
| containerWidth   | number   | Product list item container width    |
| gridViewListing  | boolean  | If product list item is in grid view |
| salePercentage   | number   | Product sale percentage              |
| onSale           | boolean  | If product is on sale                |

Handle product wish list

```js
import { useProductWishList } from '@appmaker-xyz/shopify';

...

const { toggleWishList, isSaved } = useProductWishList(props);
```

`useProductWishList` returns the following data:

| Name           | Type     | Description               |
| -------------- | -------- | ------------------------- |
| toggleWishList | function | Toggles product wish list |
| isSaved        | boolean  | If product is saved       |
