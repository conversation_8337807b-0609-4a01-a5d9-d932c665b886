{"version": 3, "names": ["_definitions", "require", "STANDARDIZED_TYPES", "FLIPPED_ALIAS_KEYS", "exports", "EXPRESSION_TYPES", "BINARY_TYPES", "SCOPABLE_TYPES", "BLOCKPARENT_TYPES", "BLOCK_TYPES", "STATEMENT_TYPES", "TERMINATORLESS_TYPES", "COMPLETIONSTATEMENT_TYPES", "CONDITIONAL_TYPES", "LOOP_TYPES", "WHILE_TYPES", "EXPRESSIONWRAPPER_TYPES", "FOR_TYPES", "FORXSTATEMENT_TYPES", "FUNCTION_TYPES", "FUNCTIONPARENT_TYPES", "PUREISH_TYPES", "DECLARATION_TYPES", "PATTERNLIKE_TYPES", "LVAL_TYPES", "TSENTITYNAME_TYPES", "LITERAL_TYPES", "IMMUTABLE_TYPES", "USERWHITESPACABLE_TYPES", "METHOD_TYPES", "OBJECTMEMBER_TYPES", "PROPERTY_TYPES", "UNARYLIKE_TYPES", "PATTERN_TYPES", "CLASS_TYPES", "IMPORTOREXPORTDECLARATION_TYPES", "EXPORTDECLARATION_TYPES", "MODULESPECIFIER_TYPES", "ACCESSOR_TYPES", "PRIVATE_TYPES", "FLOW_TYPES", "FLOWTYPE_TYPES", "FLOWBASEANNOTATION_TYPES", "FLOWDECLARATION_TYPES", "FLOWPREDICATE_TYPES", "ENUMBODY_TYPES", "ENUMMEMBER_TYPES", "JSX_TYPES", "MISCELLANEOUS_TYPES", "TYPESCRIPT_TYPES", "TSTYPEELEMENT_TYPES", "TSTYPE_TYPES", "TSBASETYPE_TYPES", "MODULEDECLARATION_TYPES"], "sources": ["../../../src/constants/generated/index.ts"], "sourcesContent": ["/*\n * This file is auto-generated! Do not modify it directly.\n * To re-generate run 'make build'\n */\nimport { FLIPPED_ALIAS_KEYS } from \"../../definitions\";\n\nexport const STANDARDIZED_TYPES = FLIPPED_ALIAS_KEYS[\"Standardized\"];\nexport const EXPRESSION_TYPES = FLIPPED_ALIAS_KEYS[\"Expression\"];\nexport const BINARY_TYPES = FLIPPED_ALIAS_KEYS[\"Binary\"];\nexport const SCOPABLE_TYPES = FLIPPED_ALIAS_KEYS[\"Scopable\"];\nexport const BLOCKPARENT_TYPES = FLIPPED_ALIAS_KEYS[\"BlockParent\"];\nexport const BLOCK_TYPES = FLIPPED_ALIAS_KEYS[\"Block\"];\nexport const STATEMENT_TYPES = FLIPPED_ALIAS_KEYS[\"Statement\"];\nexport const TERMINATORLESS_TYPES = FLIPPED_ALIAS_KEYS[\"Terminatorless\"];\nexport const COMPLETIONSTATEMENT_TYPES =\n  FLIPPED_ALIAS_KEYS[\"CompletionStatement\"];\nexport const CONDITIONAL_TYPES = FLIPPED_ALIAS_KEYS[\"Conditional\"];\nexport const LOOP_TYPES = FLIPPED_ALIAS_KEYS[\"Loop\"];\nexport const WHILE_TYPES = FLIPPED_ALIAS_KEYS[\"While\"];\nexport const EXPRESSIONWRAPPER_TYPES = FLIPPED_ALIAS_KEYS[\"ExpressionWrapper\"];\nexport const FOR_TYPES = FLIPPED_ALIAS_KEYS[\"For\"];\nexport const FORXSTATEMENT_TYPES = FLIPPED_ALIAS_KEYS[\"ForXStatement\"];\nexport const FUNCTION_TYPES = FLIPPED_ALIAS_KEYS[\"Function\"];\nexport const FUNCTIONPARENT_TYPES = FLIPPED_ALIAS_KEYS[\"FunctionParent\"];\nexport const PUREISH_TYPES = FLIPPED_ALIAS_KEYS[\"Pureish\"];\nexport const DECLARATION_TYPES = FLIPPED_ALIAS_KEYS[\"Declaration\"];\nexport const PATTERNLIKE_TYPES = FLIPPED_ALIAS_KEYS[\"PatternLike\"];\nexport const LVAL_TYPES = FLIPPED_ALIAS_KEYS[\"LVal\"];\nexport const TSENTITYNAME_TYPES = FLIPPED_ALIAS_KEYS[\"TSEntityName\"];\nexport const LITERAL_TYPES = FLIPPED_ALIAS_KEYS[\"Literal\"];\nexport const IMMUTABLE_TYPES = FLIPPED_ALIAS_KEYS[\"Immutable\"];\nexport const USERWHITESPACABLE_TYPES = FLIPPED_ALIAS_KEYS[\"UserWhitespacable\"];\nexport const METHOD_TYPES = FLIPPED_ALIAS_KEYS[\"Method\"];\nexport const OBJECTMEMBER_TYPES = FLIPPED_ALIAS_KEYS[\"ObjectMember\"];\nexport const PROPERTY_TYPES = FLIPPED_ALIAS_KEYS[\"Property\"];\nexport const UNARYLIKE_TYPES = FLIPPED_ALIAS_KEYS[\"UnaryLike\"];\nexport const PATTERN_TYPES = FLIPPED_ALIAS_KEYS[\"Pattern\"];\nexport const CLASS_TYPES = FLIPPED_ALIAS_KEYS[\"Class\"];\nexport const IMPORTOREXPORTDECLARATION_TYPES =\n  FLIPPED_ALIAS_KEYS[\"ImportOrExportDeclaration\"];\nexport const EXPORTDECLARATION_TYPES = FLIPPED_ALIAS_KEYS[\"ExportDeclaration\"];\nexport const MODULESPECIFIER_TYPES = FLIPPED_ALIAS_KEYS[\"ModuleSpecifier\"];\nexport const ACCESSOR_TYPES = FLIPPED_ALIAS_KEYS[\"Accessor\"];\nexport const PRIVATE_TYPES = FLIPPED_ALIAS_KEYS[\"Private\"];\nexport const FLOW_TYPES = FLIPPED_ALIAS_KEYS[\"Flow\"];\nexport const FLOWTYPE_TYPES = FLIPPED_ALIAS_KEYS[\"FlowType\"];\nexport const FLOWBASEANNOTATION_TYPES =\n  FLIPPED_ALIAS_KEYS[\"FlowBaseAnnotation\"];\nexport const FLOWDECLARATION_TYPES = FLIPPED_ALIAS_KEYS[\"FlowDeclaration\"];\nexport const FLOWPREDICATE_TYPES = FLIPPED_ALIAS_KEYS[\"FlowPredicate\"];\nexport const ENUMBODY_TYPES = FLIPPED_ALIAS_KEYS[\"EnumBody\"];\nexport const ENUMMEMBER_TYPES = FLIPPED_ALIAS_KEYS[\"EnumMember\"];\nexport const JSX_TYPES = FLIPPED_ALIAS_KEYS[\"JSX\"];\nexport const MISCELLANEOUS_TYPES = FLIPPED_ALIAS_KEYS[\"Miscellaneous\"];\nexport const TYPESCRIPT_TYPES = FLIPPED_ALIAS_KEYS[\"TypeScript\"];\nexport const TSTYPEELEMENT_TYPES = FLIPPED_ALIAS_KEYS[\"TSTypeElement\"];\nexport const TSTYPE_TYPES = FLIPPED_ALIAS_KEYS[\"TSType\"];\nexport const TSBASETYPE_TYPES = FLIPPED_ALIAS_KEYS[\"TSBaseType\"];\n/**\n * @deprecated migrate to IMPORTOREXPORTDECLARATION_TYPES.\n */\nexport const MODULEDECLARATION_TYPES = IMPORTOREXPORTDECLARATION_TYPES;\n"], "mappings": ";;;;;;AAIA,IAAAA,YAAA,GAAAC,OAAA;AAEO,MAAMC,kBAAkB,GAAGC,+BAAkB,CAAC,cAAc,CAAC;AAACC,OAAA,CAAAF,kBAAA,GAAAA,kBAAA;AAC9D,MAAMG,gBAAgB,GAAGF,+BAAkB,CAAC,YAAY,CAAC;AAACC,OAAA,CAAAC,gBAAA,GAAAA,gBAAA;AAC1D,MAAMC,YAAY,GAAGH,+BAAkB,CAAC,QAAQ,CAAC;AAACC,OAAA,CAAAE,YAAA,GAAAA,YAAA;AAClD,MAAMC,cAAc,GAAGJ,+BAAkB,CAAC,UAAU,CAAC;AAACC,OAAA,CAAAG,cAAA,GAAAA,cAAA;AACtD,MAAMC,iBAAiB,GAAGL,+BAAkB,CAAC,aAAa,CAAC;AAACC,OAAA,CAAAI,iBAAA,GAAAA,iBAAA;AAC5D,MAAMC,WAAW,GAAGN,+BAAkB,CAAC,OAAO,CAAC;AAACC,OAAA,CAAAK,WAAA,GAAAA,WAAA;AAChD,MAAMC,eAAe,GAAGP,+BAAkB,CAAC,WAAW,CAAC;AAACC,OAAA,CAAAM,eAAA,GAAAA,eAAA;AACxD,MAAMC,oBAAoB,GAAGR,+BAAkB,CAAC,gBAAgB,CAAC;AAACC,OAAA,CAAAO,oBAAA,GAAAA,oBAAA;AAClE,MAAMC,yBAAyB,GACpCT,+BAAkB,CAAC,qBAAqB,CAAC;AAACC,OAAA,CAAAQ,yBAAA,GAAAA,yBAAA;AACrC,MAAMC,iBAAiB,GAAGV,+BAAkB,CAAC,aAAa,CAAC;AAACC,OAAA,CAAAS,iBAAA,GAAAA,iBAAA;AAC5D,MAAMC,UAAU,GAAGX,+BAAkB,CAAC,MAAM,CAAC;AAACC,OAAA,CAAAU,UAAA,GAAAA,UAAA;AAC9C,MAAMC,WAAW,GAAGZ,+BAAkB,CAAC,OAAO,CAAC;AAACC,OAAA,CAAAW,WAAA,GAAAA,WAAA;AAChD,MAAMC,uBAAuB,GAAGb,+BAAkB,CAAC,mBAAmB,CAAC;AAACC,OAAA,CAAAY,uBAAA,GAAAA,uBAAA;AACxE,MAAMC,SAAS,GAAGd,+BAAkB,CAAC,KAAK,CAAC;AAACC,OAAA,CAAAa,SAAA,GAAAA,SAAA;AAC5C,MAAMC,mBAAmB,GAAGf,+BAAkB,CAAC,eAAe,CAAC;AAACC,OAAA,CAAAc,mBAAA,GAAAA,mBAAA;AAChE,MAAMC,cAAc,GAAGhB,+BAAkB,CAAC,UAAU,CAAC;AAACC,OAAA,CAAAe,cAAA,GAAAA,cAAA;AACtD,MAAMC,oBAAoB,GAAGjB,+BAAkB,CAAC,gBAAgB,CAAC;AAACC,OAAA,CAAAgB,oBAAA,GAAAA,oBAAA;AAClE,MAAMC,aAAa,GAAGlB,+BAAkB,CAAC,SAAS,CAAC;AAACC,OAAA,CAAAiB,aAAA,GAAAA,aAAA;AACpD,MAAMC,iBAAiB,GAAGnB,+BAAkB,CAAC,aAAa,CAAC;AAACC,OAAA,CAAAkB,iBAAA,GAAAA,iBAAA;AAC5D,MAAMC,iBAAiB,GAAGpB,+BAAkB,CAAC,aAAa,CAAC;AAACC,OAAA,CAAAmB,iBAAA,GAAAA,iBAAA;AAC5D,MAAMC,UAAU,GAAGrB,+BAAkB,CAAC,MAAM,CAAC;AAACC,OAAA,CAAAoB,UAAA,GAAAA,UAAA;AAC9C,MAAMC,kBAAkB,GAAGtB,+BAAkB,CAAC,cAAc,CAAC;AAACC,OAAA,CAAAqB,kBAAA,GAAAA,kBAAA;AAC9D,MAAMC,aAAa,GAAGvB,+BAAkB,CAAC,SAAS,CAAC;AAACC,OAAA,CAAAsB,aAAA,GAAAA,aAAA;AACpD,MAAMC,eAAe,GAAGxB,+BAAkB,CAAC,WAAW,CAAC;AAACC,OAAA,CAAAuB,eAAA,GAAAA,eAAA;AACxD,MAAMC,uBAAuB,GAAGzB,+BAAkB,CAAC,mBAAmB,CAAC;AAACC,OAAA,CAAAwB,uBAAA,GAAAA,uBAAA;AACxE,MAAMC,YAAY,GAAG1B,+BAAkB,CAAC,QAAQ,CAAC;AAACC,OAAA,CAAAyB,YAAA,GAAAA,YAAA;AAClD,MAAMC,kBAAkB,GAAG3B,+BAAkB,CAAC,cAAc,CAAC;AAACC,OAAA,CAAA0B,kBAAA,GAAAA,kBAAA;AAC9D,MAAMC,cAAc,GAAG5B,+BAAkB,CAAC,UAAU,CAAC;AAACC,OAAA,CAAA2B,cAAA,GAAAA,cAAA;AACtD,MAAMC,eAAe,GAAG7B,+BAAkB,CAAC,WAAW,CAAC;AAACC,OAAA,CAAA4B,eAAA,GAAAA,eAAA;AACxD,MAAMC,aAAa,GAAG9B,+BAAkB,CAAC,SAAS,CAAC;AAACC,OAAA,CAAA6B,aAAA,GAAAA,aAAA;AACpD,MAAMC,WAAW,GAAG/B,+BAAkB,CAAC,OAAO,CAAC;AAACC,OAAA,CAAA8B,WAAA,GAAAA,WAAA;AAChD,MAAMC,+BAA+B,GAC1ChC,+BAAkB,CAAC,2BAA2B,CAAC;AAACC,OAAA,CAAA+B,+BAAA,GAAAA,+BAAA;AAC3C,MAAMC,uBAAuB,GAAGjC,+BAAkB,CAAC,mBAAmB,CAAC;AAACC,OAAA,CAAAgC,uBAAA,GAAAA,uBAAA;AACxE,MAAMC,qBAAqB,GAAGlC,+BAAkB,CAAC,iBAAiB,CAAC;AAACC,OAAA,CAAAiC,qBAAA,GAAAA,qBAAA;AACpE,MAAMC,cAAc,GAAGnC,+BAAkB,CAAC,UAAU,CAAC;AAACC,OAAA,CAAAkC,cAAA,GAAAA,cAAA;AACtD,MAAMC,aAAa,GAAGpC,+BAAkB,CAAC,SAAS,CAAC;AAACC,OAAA,CAAAmC,aAAA,GAAAA,aAAA;AACpD,MAAMC,UAAU,GAAGrC,+BAAkB,CAAC,MAAM,CAAC;AAACC,OAAA,CAAAoC,UAAA,GAAAA,UAAA;AAC9C,MAAMC,cAAc,GAAGtC,+BAAkB,CAAC,UAAU,CAAC;AAACC,OAAA,CAAAqC,cAAA,GAAAA,cAAA;AACtD,MAAMC,wBAAwB,GACnCvC,+BAAkB,CAAC,oBAAoB,CAAC;AAACC,OAAA,CAAAsC,wBAAA,GAAAA,wBAAA;AACpC,MAAMC,qBAAqB,GAAGxC,+BAAkB,CAAC,iBAAiB,CAAC;AAACC,OAAA,CAAAuC,qBAAA,GAAAA,qBAAA;AACpE,MAAMC,mBAAmB,GAAGzC,+BAAkB,CAAC,eAAe,CAAC;AAACC,OAAA,CAAAwC,mBAAA,GAAAA,mBAAA;AAChE,MAAMC,cAAc,GAAG1C,+BAAkB,CAAC,UAAU,CAAC;AAACC,OAAA,CAAAyC,cAAA,GAAAA,cAAA;AACtD,MAAMC,gBAAgB,GAAG3C,+BAAkB,CAAC,YAAY,CAAC;AAACC,OAAA,CAAA0C,gBAAA,GAAAA,gBAAA;AAC1D,MAAMC,SAAS,GAAG5C,+BAAkB,CAAC,KAAK,CAAC;AAACC,OAAA,CAAA2C,SAAA,GAAAA,SAAA;AAC5C,MAAMC,mBAAmB,GAAG7C,+BAAkB,CAAC,eAAe,CAAC;AAACC,OAAA,CAAA4C,mBAAA,GAAAA,mBAAA;AAChE,MAAMC,gBAAgB,GAAG9C,+BAAkB,CAAC,YAAY,CAAC;AAACC,OAAA,CAAA6C,gBAAA,GAAAA,gBAAA;AAC1D,MAAMC,mBAAmB,GAAG/C,+BAAkB,CAAC,eAAe,CAAC;AAACC,OAAA,CAAA8C,mBAAA,GAAAA,mBAAA;AAChE,MAAMC,YAAY,GAAGhD,+BAAkB,CAAC,QAAQ,CAAC;AAACC,OAAA,CAAA+C,YAAA,GAAAA,YAAA;AAClD,MAAMC,gBAAgB,GAAGjD,+BAAkB,CAAC,YAAY,CAAC;AAACC,OAAA,CAAAgD,gBAAA,GAAAA,gBAAA;AAI1D,MAAMC,uBAAuB,GAAGlB,+BAA+B;AAAC/B,OAAA,CAAAiD,uBAAA,GAAAA,uBAAA"}