import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import RadioForm from 'react-native-simple-radio-button';
import { fonts, color, spacing } from '../../../styles';

const Radio = (props) => {
  const { radio_items, onPress, initial = 0 } = props;
  const [value, setValue] = useState(0);

  //   var radio_props = [
  //     {label: 'Lorem Lipsum', value: 0},
  //     {label: 'Lorem Lipsum dolor', value: 1},
  //     {label: 'Lorem Lipsum', value: 2},
  //     {label: 'Lorem Lipsum dolor', value: 3},
  //   ];
  return (
    <RadioForm
      radio_props={radio_items}
      initial={initial}
      buttonColor={props.buttonColor || color.grey}
      selectedButtonColor={props.selectedButtonColor || color.success}
      labelStyle={styles.customLabelStyle}
      buttonSize={10}
      buttonOuterSize={24}
      onPress={onPress}
    />
  );
};

const styles = StyleSheet.create({
  customLabelStyle: {
    ...fonts.bodyParagraph,
    flexShrink: 1,
    paddingBottom: 12,
    paddingTop: 3,
  },
});

export default Radio;
