import axios from 'axios';
import { simpleLocalStore as store } from '@appmaker-xyz/react-native';
import { getProjectId, appSettings } from '@appmaker-xyz/core';
import { Platform } from 'react-native';
import { debounce } from 'lodash';
import { firebase } from '@react-native-firebase/app';
import '@react-native-firebase/installations';

// let _baseurl = '';
const baseUrl = 'https://app-events.appmaker.xyz/v2/events';
// const baseUrl = 'http://192.168.1.199:3005/v2/events';
const installIdKey = '__appmaker_events_install_id';
let _appId = '';
let _appmaker_events_queue = [];
let _sessionId = null;
let _isSessionCreating = false;
let _sessionTime = null;

function getAppId() {
  return _appId;
}
const createAppInstall = async (installData = {}) => {
  // console.log('appmaker-events', 'createAppInstall');
  const data = {
    // project_id: projectId,
    // app_id: appId,
    template_tag: appSettings.getOption('template_tag'),
    template_branch: appSettings.getOption('template_branch'),
    template_version: appSettings.getOption('template_version'),
    device_os: Platform.OS,
    // device_screen_resolution: deviceScreenResolution,
    os_version: Platform.Version,
    device_manufacturer: Platform?.Manufacturer ? Platform.Manufacturer : '',
    device_model: Platform?.Model ? Platform.Model : '',
    install_time: Date.now(),
    // utm_source: utmSource,
    // utm_medium: utmMedium,
    // utm_campaign: utmCampaign,
    // utm_content: utmContent,
    // utm_term: utmTerm,
    // network_provider: networkProvider,
    // country: country,
    // city: city,
    ...installData,
  };
  const resp = await axios.post(`${baseUrl}/app-installs`, data);
  return resp.data.install_id;
};

const createAppSession = async (sessionData = {}) => {
  // console.log('appmaker-events', 'createAppSession');
  const { installId, projectId, appId } = sessionData;
  const data = {
    project_id: projectId,
    app_id: appId,
    install_id: installId,
    template_tag: appSettings.getOption('template_tag'),
    template_branch: appSettings.getOption('template_branch'),
    template_version: appSettings.getOption('template_version'),
    // start_time: startTime,
    // device_type: deviceType,
    device_manufacturer: Platform?.Manufacturer ? Platform.Manufacturer : '',
    device_os: Platform.OS,
    device_model: Platform?.Model ? Platform.Model : '',
    // device_screen_resolution: deviceScreenResolution,
    os_version: Platform.Version,
    start_time: Date.now(),
    ...sessionData,
  };
  const resp = await axios.post(`${baseUrl}/app-sessions`, data);
  return resp.data.session_id;
};

async function getSessionId() {
  // console.log('appmaker-events', 'getSessionId');
  if (_isSessionCreating) {
    return null;
  }
  if (
    !_sessionId ||
    typeof _sessionId !== 'string' ||
    !_sessionTime ||
    Date.now() - _sessionTime > 30 * 60 * 1000
  ) {
    try {
      _isSessionCreating = true;
      const installId = await getInstallId();
      _sessionId = await createAppSession({
        project_id: `${getProjectId()}`,
        app_id: getAppId(),
        install_id: installId,
        app_version: appSettings.getOption('app_version'),
        app_name: appSettings.getOption('appname'),
      });
      _sessionTime = Date.now();
      _isSessionCreating = false;
      if (_sessionId) {
        global.installId = installId;
        global.sessionId = _sessionId;
        sendEvents();
      }
    } catch (e) {
      console.error('appmaker-events', 'Error creating sessionId', e);
    }
  }
  _sessionTime = Date.now();
  return `${_sessionId}`;
}

async function getInstallId() {
  // console.log('appmaker-events', 'getInstallId');
  let installId = await firebase.installations().getId();
  return installId;
}

const sendEvents = debounce(async () => {
  // console.log('appmaker-events', 'sendEvents from queue');
  const install_id = await getInstallId();
  const session_id = await getSessionId();
  const events = _appmaker_events_queue;
  _appmaker_events_queue = [];
  if (!events.length) {
    return;
  }
  return axios.post(`${baseUrl}/app-events`, {
    events: events.map((eventData) => ({
      ...eventData,
      install_id,
      session_id,
    })),
  });
}, 3000);

async function sendEvent({ event, params, context, rest }) {
  // console.log('appmaker-events', 'add events to queue');
  _appmaker_events_queue.push({
    app_id: getAppId(),
    project_id: getProjectId(),
    event_name: event,
    params,
    total_value:
      params?.price ||
      params?.total_amount ||
      params?.value ||
      params?.total_value ||
      0,
    device_os: Platform.OS,
    currency: params?.currency ? params?.currency : '',
    event_time: Date.now(),
    app_version: appSettings.getOption('app_version'),
    app_name: appSettings.getOption('appname'),
  });
  const installId = await getInstallId();
  if (installId) {
    const session_id = await getSessionId();
    if (session_id) {
      //  console.log(
      //   'appmaker-events',
      //   'sendEvents called from sendEvent',
      //   session_id,
      //   installId,
      // );
      sendEvents();
    }
  }
}

export function track(event, params, context, rest) {
  sendEvent({
    event,
    params,
    context,
    rest,
  });
}

export function page(pageId, name, context, rest) {
  sendEvent({
    event: 'screen_view',
    params: {
      screen_name: name,
      screen_id: pageId,
    },
    context,
    rest,
  });
}
