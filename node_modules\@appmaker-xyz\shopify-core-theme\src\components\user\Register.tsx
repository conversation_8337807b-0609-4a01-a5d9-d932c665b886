import React, { useState } from 'react';
import { StyleSheet, KeyboardAvoidingView, Platform } from 'react-native';
import {
  Layout,
  ThemeText,
  Input,
  AppTouchable,
  CheckBox,
  Button,
} from '@appmaker-xyz/ui';
import SocialLogin from './SocialLogin';
import * as z from 'zod';
import { InputFormItem } from './components/InputFormItem';
import { CheckBoxFormItem } from './components/CheckBoxFormItem';
import { useRegister, useNavigationActions } from '@appmaker-xyz/shopify';

const Register = () => {
  const { control, isLoading, submitRegister, setFocus } = useRegister({
    defaultValues: {},
  });

  const shopifyActions = useNavigationActions();
  return (
    <KeyboardAvoidingView
      keyboardVerticalOffset={80}
      behavior={'padding'}>
      <Layout style={styles.container}>
        <ThemeText size="2xl" fontFamily="medium" style={styles.title}>
          Hello! Register now to get started
        </ThemeText>
        <Layout style={styles.row}>
          <Layout style={styles.fnameInput}>
            <InputFormItem
              control={control}
              name="first_name"
              label={'First Name'}
              style={styles.input}
              autoFocus={true}
              onSubmitEditing={() => setFocus('last_name')}
              returnKeyType="next"
              autoCapitalize='words'
            />
          </Layout>
          <Layout style={styles.lnameInput}>
            <InputFormItem
              autoCapitalize='words'
              control={control}
              name="last_name"
              label={'Last Name'}
              style={styles.input}
              onSubmitEditing={() => setFocus('email')}
              returnKeyType="next"
            />
          </Layout>
        </Layout>
        <InputFormItem
          style={styles.input}
          control={control}
          name="email"
          label={'Email'}
          onSubmitEditing={() => setFocus('password')}
          returnKeyType="next"
        />
        <InputFormItem
          autoCapitalize="none"
          autoCorrect={false}
          textContentType="password"
          secureTextEntry={true}
          control={control}
          name="password"
          label={'Password'}
          style={styles.input}
          returnKeyType="next"
          onSubmitEditing={() => setFocus('repeat_password')}
        />
        <InputFormItem
          control={control}
          secureTextEntry={true}
          name="repeat_password"
          label={'Confirm Password'}
          style={styles.input}
        />
        <CheckBoxFormItem
          control={control}
          name="accepts_marketing"
          label="Agree to receive promotions and news by email"
        />
        <CheckBoxFormItem
          control={control}
          name="agree_terms"
          label="I agree to the Terms of Service"
        />
        <Button
          title="Register"
          onPress={submitRegister}
          isLoading={isLoading}
          activityIndicatorColor="#FFFFFF"
          size="md"
          color="#1E232C"
        />
        <SocialLogin />
        <Layout style={styles.signUpButton}>
          <AppTouchable onPress={shopifyActions.openLoginPage}>
            <ThemeText color="#1E232C">
              Already have an account?{' '}
              <ThemeText color="#427FD1" fontFamily="medium">
                Login Now
              </ThemeText>
            </ThemeText>
          </AppTouchable>
        </Layout>
      </Layout>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {},
  title: {
    marginBottom: 32,
  },
  input: {
    backgroundColor: '#E8ECF4',
    borderRadius: 6,
    overflow: 'hidden',
    position: 'relative',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
  },
  fnameInput: {
    marginRight: 6,
    flex: 1,
  },
  lnameInput: {
    marginLeft: 6,
    flex: 1,
  },
  eyeIcon: {
    position: 'absolute',
    right: 12,
    top: 10,
  },
  forgotButton: {
    alignSelf: 'flex-end',
    marginBottom: 16,
    marginTop: -6,
  },
  signUpButton: {
    marginTop: 32,
    alignItems: 'center',
  },
});

export default Register;
