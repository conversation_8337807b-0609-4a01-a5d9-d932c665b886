{"version": 3, "names": ["_parseError", "require", "defineProperty", "Object", "toUnenumerable", "object", "key", "enumerable", "value", "toESTreeLocation", "node", "loc", "start", "end", "_default", "superClass", "ESTreeParserMixin", "parse", "file", "options", "tokens", "map", "parseRegExpLiteral", "pattern", "flags", "regex", "RegExp", "e", "estreeParseLiteral", "parseBigIntLiteral", "bigInt", "BigInt", "_unused", "bigint", "String", "parseDecimalLiteral", "decimal", "parseLiteral", "parseStringLiteral", "parseNumericLiteral", "parse<PERSON>ull<PERSON><PERSON><PERSON>", "parseBooleanLiteral", "directiveToStmt", "directive", "expression", "type", "raw", "extra", "expressionValue", "stmt", "rawValue", "initFunction", "isAsync", "checkDeclaration", "isObjectProperty", "getObjectOrClassMethodParams", "method", "params", "isValidDirective", "_stmt$expression$extr", "parenthesized", "parseBlockBody", "allowDirectives", "topLevel", "afterBlockParse", "directiveStatements", "directives", "d", "body", "concat", "pushClassMethod", "classBody", "isGenerator", "isConstructor", "allowsDirectSuper", "parseMethod", "typeParameters", "push", "parsePrivateName", "getPluginOption", "convertPrivateNameToPrivateIdentifier", "name", "getPrivateNameSV", "id", "isPrivateName", "parseFunctionBody", "allowExpression", "isMethod", "allowDirectSuper", "inClassScope", "funcNode", "startNode", "kind", "computed", "finishNode", "parseClassProperty", "args", "propertyNode", "parseClassPrivateProperty", "parseObjectMethod", "prop", "isPattern", "isAccessor", "shorthand", "parseObjectProperty", "startLoc", "refExpressionErrors", "isValidLVal", "isUnparenthesizedInAssign", "binding", "isAssignable", "isBinding", "toAssignable", "isLHS", "classScope", "usePrivateName", "toAssignableObjectExpressionProp", "isLast", "raise", "Errors", "PatternHasAccessor", "at", "PatternHasMethod", "finishCallExpression", "unfinished", "optional", "callee", "source", "arguments", "hasPlugin", "_node$arguments$", "attributes", "toReferencedArguments", "parseExport", "decorators", "exportStartLoc", "state", "lastTokStartLoc", "exported", "specifiers", "length", "_declaration$decorato", "declaration", "resetStartLocation", "parseSubscript", "base", "noCalls", "optionalChainMember", "substring", "stop", "chain", "startNodeAtNode", "hasPropertyAsPrivateName", "isObjectMethod", "finishNodeAt", "endLoc", "resetEndLocation", "lastTokEndLoc", "exports", "default"], "sources": ["../../src/plugins/estree.ts"], "sourcesContent": ["import type { TokenType } from \"../tokenizer/types\";\nimport type Parser from \"../parser\";\nimport type { ExpressionErrors } from \"../parser/util\";\nimport type * as N from \"../types\";\nimport type { Node as NodeType, NodeBase, File } from \"../types\";\nimport type { Position } from \"../util/location\";\nimport { Errors } from \"../parse-error\";\nimport type { Undone } from \"../parser/node\";\nimport type { BindingTypes } from \"../util/scopeflags\";\n\nconst { defineProperty } = Object;\nconst toUnenumerable = (object: any, key: string) =>\n  defineProperty(object, key, { enumerable: false, value: object[key] });\n\nfunction toESTreeLocation(node: any) {\n  node.loc.start && toUnenumerable(node.loc.start, \"index\");\n  node.loc.end && toUnenumerable(node.loc.end, \"index\");\n\n  return node;\n}\n\nexport default (superClass: typeof Parser) =>\n  class ESTreeParserMixin extends superClass implements Parser {\n    parse(): File {\n      const file = toESTreeLocation(super.parse());\n\n      if (this.options.tokens) {\n        file.tokens = file.tokens.map(toESTreeLocation);\n      }\n\n      return file;\n    }\n\n    // @ts-expect-error ESTree plugin changes node types\n    parseRegExpLiteral({ pattern, flags }): N.EstreeRegExpLiteral {\n      let regex: RegExp | null = null;\n      try {\n        regex = new RegExp(pattern, flags);\n      } catch (e) {\n        // In environments that don't support these flags value will\n        // be null as the regex can't be represented natively.\n      }\n      const node = this.estreeParseLiteral<N.EstreeRegExpLiteral>(regex);\n      node.regex = { pattern, flags };\n\n      return node;\n    }\n\n    // @ts-expect-error ESTree plugin changes node types\n    parseBigIntLiteral(value: any): N.Node {\n      // https://github.com/estree/estree/blob/master/es2020.md#bigintliteral\n      let bigInt: BigInt | null;\n      try {\n        bigInt = BigInt(value);\n      } catch {\n        bigInt = null;\n      }\n      const node = this.estreeParseLiteral<N.EstreeBigIntLiteral>(bigInt);\n      node.bigint = String(node.value || value);\n\n      return node;\n    }\n\n    // @ts-expect-error ESTree plugin changes node types\n    parseDecimalLiteral(value: any): N.Node {\n      // https://github.com/estree/estree/blob/master/experimental/decimal.md\n      // todo: use BigDecimal when node supports it.\n      const decimal: null = null;\n      const node = this.estreeParseLiteral(decimal);\n      node.decimal = String(node.value || value);\n\n      return node;\n    }\n\n    estreeParseLiteral<T extends N.Node>(value: any) {\n      // @ts-expect-error ESTree plugin changes node types\n      return this.parseLiteral<T>(value, \"Literal\");\n    }\n\n    // @ts-expect-error ESTree plugin changes node types\n    parseStringLiteral(value: any): N.Node {\n      return this.estreeParseLiteral(value);\n    }\n\n    parseNumericLiteral(value: any): any {\n      return this.estreeParseLiteral(value);\n    }\n\n    // @ts-expect-error ESTree plugin changes node types\n    parseNullLiteral(): N.Node {\n      return this.estreeParseLiteral(null);\n    }\n\n    parseBooleanLiteral(value: boolean): N.BooleanLiteral {\n      return this.estreeParseLiteral(value);\n    }\n\n    // Cast a Directive to an ExpressionStatement. Mutates the input Directive.\n    directiveToStmt(directive: N.Directive): N.ExpressionStatement {\n      const expression = directive.value as any as N.EstreeLiteral;\n      delete directive.value;\n\n      expression.type = \"Literal\";\n      // @ts-expect-error N.EstreeLiteral.raw is not defined.\n      expression.raw = expression.extra.raw;\n      expression.value = expression.extra.expressionValue;\n\n      const stmt = directive as any as N.ExpressionStatement;\n      stmt.type = \"ExpressionStatement\";\n      stmt.expression = expression;\n      // @ts-expect-error N.ExpressionStatement.directive is not defined\n      stmt.directive = expression.extra.rawValue;\n\n      delete expression.extra;\n\n      return stmt;\n    }\n\n    // ==================================\n    // Overrides\n    // ==================================\n\n    initFunction(node: N.BodilessFunctionOrMethodBase, isAsync: boolean): void {\n      super.initFunction(node, isAsync);\n      node.expression = false;\n    }\n\n    checkDeclaration(node: N.Pattern | N.ObjectProperty): void {\n      if (node != null && this.isObjectProperty(node)) {\n        // @ts-expect-error plugin typings\n        this.checkDeclaration((node as unknown as N.EstreeProperty).value);\n      } else {\n        super.checkDeclaration(node);\n      }\n    }\n\n    getObjectOrClassMethodParams(method: N.ObjectMethod | N.ClassMethod) {\n      return (method as any as N.EstreeProperty | N.EstreeMethodDefinition)\n        .value.params;\n    }\n\n    isValidDirective(stmt: N.Statement): boolean {\n      return (\n        stmt.type === \"ExpressionStatement\" &&\n        stmt.expression.type === \"Literal\" &&\n        typeof stmt.expression.value === \"string\" &&\n        !stmt.expression.extra?.parenthesized\n      );\n    }\n\n    parseBlockBody(\n      node: N.BlockStatementLike,\n      allowDirectives: boolean | undefined | null,\n      topLevel: boolean,\n      end: TokenType,\n      afterBlockParse?: (hasStrictModeDirective: boolean) => void,\n    ): void {\n      super.parseBlockBody(\n        node,\n        allowDirectives,\n        topLevel,\n        end,\n        afterBlockParse,\n      );\n\n      const directiveStatements = node.directives.map(d =>\n        this.directiveToStmt(d),\n      );\n      // @ts-expect-error estree plugin typings\n      node.body = directiveStatements.concat(node.body);\n      delete node.directives;\n    }\n\n    pushClassMethod(\n      classBody: N.ClassBody,\n      method: N.ClassMethod,\n      isGenerator: boolean,\n      isAsync: boolean,\n      isConstructor: boolean,\n      allowsDirectSuper: boolean,\n    ): void {\n      this.parseMethod(\n        method,\n        isGenerator,\n        isAsync,\n        isConstructor,\n        allowsDirectSuper,\n        \"ClassMethod\",\n        true,\n      );\n      if (method.typeParameters) {\n        // @ts-expect-error mutate AST types\n        method.value.typeParameters = method.typeParameters;\n        delete method.typeParameters;\n      }\n      classBody.body.push(method);\n    }\n\n    parsePrivateName(): any {\n      const node = super.parsePrivateName();\n      if (!process.env.BABEL_8_BREAKING) {\n        if (!this.getPluginOption(\"estree\", \"classFeatures\")) {\n          return node;\n        }\n      }\n      return this.convertPrivateNameToPrivateIdentifier(node);\n    }\n\n    convertPrivateNameToPrivateIdentifier(\n      node: N.PrivateName,\n    ): N.EstreePrivateIdentifier {\n      const name = super.getPrivateNameSV(node);\n      node = node as any;\n      delete node.id;\n      // @ts-expect-error mutate AST types\n      node.name = name;\n      // @ts-expect-error mutate AST types\n      node.type = \"PrivateIdentifier\";\n      return node as unknown as N.EstreePrivateIdentifier;\n    }\n\n    isPrivateName(node: N.Node): boolean {\n      if (!process.env.BABEL_8_BREAKING) {\n        if (!this.getPluginOption(\"estree\", \"classFeatures\")) {\n          return super.isPrivateName(node);\n        }\n      }\n      return node.type === \"PrivateIdentifier\";\n    }\n\n    getPrivateNameSV(node: N.Node): string {\n      if (!process.env.BABEL_8_BREAKING) {\n        if (!this.getPluginOption(\"estree\", \"classFeatures\")) {\n          return super.getPrivateNameSV(node);\n        }\n      }\n      return node.name;\n    }\n\n    // @ts-expect-error plugin may override interfaces\n    parseLiteral<T extends N.Literal>(value: any, type: T[\"type\"]): T {\n      const node = super.parseLiteral<T>(value, type);\n      // @ts-expect-error mutating AST types\n      node.raw = node.extra.raw;\n      delete node.extra;\n\n      return node;\n    }\n\n    parseFunctionBody(\n      node: N.Function,\n      allowExpression?: boolean | null,\n      isMethod: boolean = false,\n    ): void {\n      super.parseFunctionBody(node, allowExpression, isMethod);\n      node.expression = node.body.type !== \"BlockStatement\";\n    }\n\n    // @ts-expect-error plugin may override interfaces\n    parseMethod<\n      T extends N.ClassPrivateMethod | N.ObjectMethod | N.ClassMethod,\n    >(\n      node: Undone<T>,\n      isGenerator: boolean,\n      isAsync: boolean,\n      isConstructor: boolean,\n      allowDirectSuper: boolean,\n      type: T[\"type\"],\n      inClassScope: boolean = false,\n    ): N.EstreeMethodDefinition {\n      let funcNode = this.startNode<N.MethodLike>();\n      funcNode.kind = node.kind; // provide kind, so super method correctly sets state\n      funcNode = super.parseMethod(\n        // @ts-expect-error todo(flow->ts)\n        funcNode,\n        isGenerator,\n        isAsync,\n        isConstructor,\n        allowDirectSuper,\n        type,\n        inClassScope,\n      );\n      // @ts-expect-error mutate AST types\n      funcNode.type = \"FunctionExpression\";\n      delete funcNode.kind;\n      // @ts-expect-error mutate AST types\n      node.value = funcNode;\n      if (type === \"ClassPrivateMethod\") {\n        node.computed = false;\n      }\n      return this.finishNode(\n        // @ts-expect-error cast methods to estree types\n        node as Undone<N.EstreeMethodDefinition>,\n        \"MethodDefinition\",\n      );\n    }\n\n    parseClassProperty(...args: [N.ClassProperty]): any {\n      const propertyNode = super.parseClassProperty(...args) as any;\n      if (!process.env.BABEL_8_BREAKING) {\n        if (!this.getPluginOption(\"estree\", \"classFeatures\")) {\n          return propertyNode as N.EstreePropertyDefinition;\n        }\n      }\n      propertyNode.type = \"PropertyDefinition\";\n      return propertyNode as N.EstreePropertyDefinition;\n    }\n\n    parseClassPrivateProperty(...args: [N.ClassPrivateProperty]): any {\n      const propertyNode = super.parseClassPrivateProperty(...args) as any;\n      if (!process.env.BABEL_8_BREAKING) {\n        if (!this.getPluginOption(\"estree\", \"classFeatures\")) {\n          return propertyNode as N.EstreePropertyDefinition;\n        }\n      }\n      propertyNode.type = \"PropertyDefinition\";\n      propertyNode.computed = false;\n      return propertyNode as N.EstreePropertyDefinition;\n    }\n\n    parseObjectMethod(\n      prop: N.ObjectMethod,\n      isGenerator: boolean,\n      isAsync: boolean,\n      isPattern: boolean,\n      isAccessor: boolean,\n    ): N.ObjectMethod | undefined | null {\n      const node: N.EstreeProperty = super.parseObjectMethod(\n        prop,\n        isGenerator,\n        isAsync,\n        isPattern,\n        isAccessor,\n      ) as any;\n\n      if (node) {\n        node.type = \"Property\";\n        if ((node as any as N.ClassMethod).kind === \"method\") {\n          node.kind = \"init\";\n        }\n        node.shorthand = false;\n      }\n\n      return node as any;\n    }\n\n    parseObjectProperty(\n      prop: N.ObjectProperty,\n      startLoc: Position | undefined | null,\n      isPattern: boolean,\n      refExpressionErrors?: ExpressionErrors | null,\n    ): N.ObjectProperty | undefined | null {\n      const node: N.EstreeProperty = super.parseObjectProperty(\n        prop,\n        startLoc,\n        isPattern,\n        refExpressionErrors,\n      ) as any;\n\n      if (node) {\n        node.kind = \"init\";\n        node.type = \"Property\";\n      }\n\n      return node as any;\n    }\n\n    isValidLVal(\n      type: string,\n      isUnparenthesizedInAssign: boolean,\n      binding: BindingTypes,\n    ) {\n      return type === \"Property\"\n        ? \"value\"\n        : super.isValidLVal(type, isUnparenthesizedInAssign, binding);\n    }\n\n    isAssignable(node: N.Node, isBinding?: boolean): boolean {\n      if (node != null && this.isObjectProperty(node)) {\n        return this.isAssignable(node.value, isBinding);\n      }\n      return super.isAssignable(node, isBinding);\n    }\n\n    toAssignable(node: N.Node, isLHS: boolean = false): void {\n      if (node != null && this.isObjectProperty(node)) {\n        const { key, value } = node;\n        if (this.isPrivateName(key)) {\n          this.classScope.usePrivateName(\n            this.getPrivateNameSV(key),\n            key.loc.start,\n          );\n        }\n        this.toAssignable(value, isLHS);\n      } else {\n        super.toAssignable(node, isLHS);\n      }\n    }\n\n    toAssignableObjectExpressionProp(\n      prop: N.Node,\n      isLast: boolean,\n      isLHS: boolean,\n    ) {\n      if (prop.kind === \"get\" || prop.kind === \"set\") {\n        this.raise(Errors.PatternHasAccessor, { at: prop.key });\n      } else if (prop.method) {\n        this.raise(Errors.PatternHasMethod, { at: prop.key });\n      } else {\n        super.toAssignableObjectExpressionProp(prop, isLast, isLHS);\n      }\n    }\n\n    finishCallExpression<T extends N.CallExpression | N.OptionalCallExpression>(\n      unfinished: Undone<T>,\n      optional: boolean,\n    ): T {\n      const node = super.finishCallExpression(unfinished, optional);\n\n      if (node.callee.type === \"Import\") {\n        (node as N.Node as N.EstreeImportExpression).type = \"ImportExpression\";\n        (node as N.Node as N.EstreeImportExpression).source = node.arguments[0];\n        if (\n          this.hasPlugin(\"importAttributes\") ||\n          this.hasPlugin(\"importAssertions\")\n        ) {\n          (node as N.Node as N.EstreeImportExpression).attributes =\n            node.arguments[1] ?? null;\n        }\n        // arguments isn't optional in the type definition\n        delete node.arguments;\n        // callee isn't optional in the type definition\n        delete node.callee;\n      }\n\n      return node;\n    }\n\n    toReferencedArguments(\n      node:\n        | N.CallExpression\n        | N.OptionalCallExpression\n        | N.EstreeImportExpression,\n      /* isParenthesizedExpr?: boolean, */\n    ) {\n      // ImportExpressions do not have an arguments array.\n      if (node.type === \"ImportExpression\") {\n        return;\n      }\n\n      super.toReferencedArguments(node);\n    }\n\n    parseExport(\n      unfinished: Undone<N.AnyExport>,\n      decorators: N.Decorator[] | null,\n    ) {\n      const exportStartLoc = this.state.lastTokStartLoc;\n      const node = super.parseExport(unfinished, decorators);\n\n      switch (node.type) {\n        case \"ExportAllDeclaration\":\n          // @ts-expect-error mutating AST types\n          node.exported = null;\n          break;\n\n        case \"ExportNamedDeclaration\":\n          if (\n            node.specifiers.length === 1 &&\n            // @ts-expect-error mutating AST types\n            node.specifiers[0].type === \"ExportNamespaceSpecifier\"\n          ) {\n            // @ts-expect-error mutating AST types\n            node.type = \"ExportAllDeclaration\";\n            // @ts-expect-error mutating AST types\n            node.exported = node.specifiers[0].exported;\n            delete node.specifiers;\n          }\n\n        // fallthrough\n        case \"ExportDefaultDeclaration\":\n          {\n            const { declaration } = node;\n            if (\n              declaration?.type === \"ClassDeclaration\" &&\n              declaration.decorators?.length > 0 &&\n              // decorator comes before export\n              declaration.start === node.start\n            ) {\n              this.resetStartLocation(\n                node,\n                // For compatibility with ESLint's keyword-spacing rule, which assumes that an\n                // export declaration must start with export.\n                // https://github.com/babel/babel/issues/15085\n                // Here we reset export declaration's start to be the start of the export token\n                exportStartLoc,\n              );\n            }\n          }\n\n          break;\n      }\n\n      return node;\n    }\n\n    parseSubscript(\n      base: N.Expression,\n      startLoc: Position,\n      noCalls: boolean | undefined | null,\n      state: N.ParseSubscriptState,\n    ) {\n      const node = super.parseSubscript(base, startLoc, noCalls, state);\n\n      if (state.optionalChainMember) {\n        // https://github.com/estree/estree/blob/master/es2020.md#chainexpression\n        if (\n          node.type === \"OptionalMemberExpression\" ||\n          node.type === \"OptionalCallExpression\"\n        ) {\n          node.type = node.type.substring(8); // strip Optional prefix\n        }\n        if (state.stop) {\n          const chain = this.startNodeAtNode(node);\n          chain.expression = node;\n          return this.finishNode(chain, \"ChainExpression\");\n        }\n      } else if (\n        node.type === \"MemberExpression\" ||\n        node.type === \"CallExpression\"\n      ) {\n        node.optional = false;\n      }\n\n      return node;\n    }\n\n    hasPropertyAsPrivateName(node: N.Node): boolean {\n      if (node.type === \"ChainExpression\") {\n        node = node.expression;\n      }\n      return super.hasPropertyAsPrivateName(node);\n    }\n\n    // @ts-expect-error override interfaces\n    isObjectProperty(node: N.Node): boolean {\n      return node.type === \"Property\" && node.kind === \"init\" && !node.method;\n    }\n\n    isObjectMethod(node: N.Node): boolean {\n      return node.method || node.kind === \"get\" || node.kind === \"set\";\n    }\n\n    finishNodeAt<T extends NodeType>(\n      node: Undone<T>,\n      type: T[\"type\"],\n      endLoc: Position,\n    ): T {\n      return toESTreeLocation(super.finishNodeAt(node, type, endLoc));\n    }\n\n    resetStartLocation(node: N.Node, startLoc: Position) {\n      super.resetStartLocation(node, startLoc);\n      toESTreeLocation(node);\n    }\n\n    resetEndLocation(\n      node: NodeBase,\n      endLoc: Position = this.state.lastTokEndLoc,\n    ): void {\n      super.resetEndLocation(node, endLoc);\n      toESTreeLocation(node);\n    }\n  };\n"], "mappings": ";;;;;;AAMA,IAAAA,WAAA,GAAAC,OAAA;AAIA,MAAM;EAAEC;AAAe,CAAC,GAAGC,MAAM;AACjC,MAAMC,cAAc,GAAGA,CAACC,MAAW,EAAEC,GAAW,KAC9CJ,cAAc,CAACG,MAAM,EAAEC,GAAG,EAAE;EAAEC,UAAU,EAAE,KAAK;EAAEC,KAAK,EAAEH,MAAM,CAACC,GAAG;AAAE,CAAC,CAAC;AAExE,SAASG,gBAAgBA,CAACC,IAAS,EAAE;EACnCA,IAAI,CAACC,GAAG,CAACC,KAAK,IAAIR,cAAc,CAACM,IAAI,CAACC,GAAG,CAACC,KAAK,EAAE,OAAO,CAAC;EACzDF,IAAI,CAACC,GAAG,CAACE,GAAG,IAAIT,cAAc,CAACM,IAAI,CAACC,GAAG,CAACE,GAAG,EAAE,OAAO,CAAC;EAErD,OAAOH,IAAI;AACb;AAAC,IAAAI,QAAA,GAEeC,UAAyB,IACvC,MAAMC,iBAAiB,SAASD,UAAU,CAAmB;EAC3DE,KAAKA,CAAA,EAAS;IACZ,MAAMC,IAAI,GAAGT,gBAAgB,CAAC,KAAK,CAACQ,KAAK,CAAC,CAAC,CAAC;IAE5C,IAAI,IAAI,CAACE,OAAO,CAACC,MAAM,EAAE;MACvBF,IAAI,CAACE,MAAM,GAAGF,IAAI,CAACE,MAAM,CAACC,GAAG,CAACZ,gBAAgB,CAAC;IACjD;IAEA,OAAOS,IAAI;EACb;EAGAI,kBAAkBA,CAAC;IAAEC,OAAO;IAAEC;EAAM,CAAC,EAAyB;IAC5D,IAAIC,KAAoB,GAAG,IAAI;IAC/B,IAAI;MACFA,KAAK,GAAG,IAAIC,MAAM,CAACH,OAAO,EAAEC,KAAK,CAAC;IACpC,CAAC,CAAC,OAAOG,CAAC,EAAE,CAGZ;IACA,MAAMjB,IAAI,GAAG,IAAI,CAACkB,kBAAkB,CAAwBH,KAAK,CAAC;IAClEf,IAAI,CAACe,KAAK,GAAG;MAAEF,OAAO;MAAEC;IAAM,CAAC;IAE/B,OAAOd,IAAI;EACb;EAGAmB,kBAAkBA,CAACrB,KAAU,EAAU;IAErC,IAAIsB,MAAqB;IACzB,IAAI;MACFA,MAAM,GAAGC,MAAM,CAACvB,KAAK,CAAC;IACxB,CAAC,CAAC,OAAAwB,OAAA,EAAM;MACNF,MAAM,GAAG,IAAI;IACf;IACA,MAAMpB,IAAI,GAAG,IAAI,CAACkB,kBAAkB,CAAwBE,MAAM,CAAC;IACnEpB,IAAI,CAACuB,MAAM,GAAGC,MAAM,CAACxB,IAAI,CAACF,KAAK,IAAIA,KAAK,CAAC;IAEzC,OAAOE,IAAI;EACb;EAGAyB,mBAAmBA,CAAC3B,KAAU,EAAU;IAGtC,MAAM4B,OAAa,GAAG,IAAI;IAC1B,MAAM1B,IAAI,GAAG,IAAI,CAACkB,kBAAkB,CAACQ,OAAO,CAAC;IAC7C1B,IAAI,CAAC0B,OAAO,GAAGF,MAAM,CAACxB,IAAI,CAACF,KAAK,IAAIA,KAAK,CAAC;IAE1C,OAAOE,IAAI;EACb;EAEAkB,kBAAkBA,CAAmBpB,KAAU,EAAE;IAE/C,OAAO,IAAI,CAAC6B,YAAY,CAAI7B,KAAK,EAAE,SAAS,CAAC;EAC/C;EAGA8B,kBAAkBA,CAAC9B,KAAU,EAAU;IACrC,OAAO,IAAI,CAACoB,kBAAkB,CAACpB,KAAK,CAAC;EACvC;EAEA+B,mBAAmBA,CAAC/B,KAAU,EAAO;IACnC,OAAO,IAAI,CAACoB,kBAAkB,CAACpB,KAAK,CAAC;EACvC;EAGAgC,gBAAgBA,CAAA,EAAW;IACzB,OAAO,IAAI,CAACZ,kBAAkB,CAAC,IAAI,CAAC;EACtC;EAEAa,mBAAmBA,CAACjC,KAAc,EAAoB;IACpD,OAAO,IAAI,CAACoB,kBAAkB,CAACpB,KAAK,CAAC;EACvC;EAGAkC,eAAeA,CAACC,SAAsB,EAAyB;IAC7D,MAAMC,UAAU,GAAGD,SAAS,CAACnC,KAA+B;IAC5D,OAAOmC,SAAS,CAACnC,KAAK;IAEtBoC,UAAU,CAACC,IAAI,GAAG,SAAS;IAE3BD,UAAU,CAACE,GAAG,GAAGF,UAAU,CAACG,KAAK,CAACD,GAAG;IACrCF,UAAU,CAACpC,KAAK,GAAGoC,UAAU,CAACG,KAAK,CAACC,eAAe;IAEnD,MAAMC,IAAI,GAAGN,SAAyC;IACtDM,IAAI,CAACJ,IAAI,GAAG,qBAAqB;IACjCI,IAAI,CAACL,UAAU,GAAGA,UAAU;IAE5BK,IAAI,CAACN,SAAS,GAAGC,UAAU,CAACG,KAAK,CAACG,QAAQ;IAE1C,OAAON,UAAU,CAACG,KAAK;IAEvB,OAAOE,IAAI;EACb;EAMAE,YAAYA,CAACzC,IAAoC,EAAE0C,OAAgB,EAAQ;IACzE,KAAK,CAACD,YAAY,CAACzC,IAAI,EAAE0C,OAAO,CAAC;IACjC1C,IAAI,CAACkC,UAAU,GAAG,KAAK;EACzB;EAEAS,gBAAgBA,CAAC3C,IAAkC,EAAQ;IACzD,IAAIA,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC4C,gBAAgB,CAAC5C,IAAI,CAAC,EAAE;MAE/C,IAAI,CAAC2C,gBAAgB,CAAE3C,IAAI,CAAiCF,KAAK,CAAC;IACpE,CAAC,MAAM;MACL,KAAK,CAAC6C,gBAAgB,CAAC3C,IAAI,CAAC;IAC9B;EACF;EAEA6C,4BAA4BA,CAACC,MAAsC,EAAE;IACnE,OAAQA,MAAM,CACXhD,KAAK,CAACiD,MAAM;EACjB;EAEAC,gBAAgBA,CAACT,IAAiB,EAAW;IAAA,IAAAU,qBAAA;IAC3C,OACEV,IAAI,CAACJ,IAAI,KAAK,qBAAqB,IACnCI,IAAI,CAACL,UAAU,CAACC,IAAI,KAAK,SAAS,IAClC,OAAOI,IAAI,CAACL,UAAU,CAACpC,KAAK,KAAK,QAAQ,IACzC,GAAAmD,qBAAA,GAACV,IAAI,CAACL,UAAU,CAACG,KAAK,aAArBY,qBAAA,CAAuBC,aAAa;EAEzC;EAEAC,cAAcA,CACZnD,IAA0B,EAC1BoD,eAA2C,EAC3CC,QAAiB,EACjBlD,GAAc,EACdmD,eAA2D,EACrD;IACN,KAAK,CAACH,cAAc,CAClBnD,IAAI,EACJoD,eAAe,EACfC,QAAQ,EACRlD,GAAG,EACHmD,eACF,CAAC;IAED,MAAMC,mBAAmB,GAAGvD,IAAI,CAACwD,UAAU,CAAC7C,GAAG,CAAC8C,CAAC,IAC/C,IAAI,CAACzB,eAAe,CAACyB,CAAC,CACxB,CAAC;IAEDzD,IAAI,CAAC0D,IAAI,GAAGH,mBAAmB,CAACI,MAAM,CAAC3D,IAAI,CAAC0D,IAAI,CAAC;IACjD,OAAO1D,IAAI,CAACwD,UAAU;EACxB;EAEAI,eAAeA,CACbC,SAAsB,EACtBf,MAAqB,EACrBgB,WAAoB,EACpBpB,OAAgB,EAChBqB,aAAsB,EACtBC,iBAA0B,EACpB;IACN,IAAI,CAACC,WAAW,CACdnB,MAAM,EACNgB,WAAW,EACXpB,OAAO,EACPqB,aAAa,EACbC,iBAAiB,EACjB,aAAa,EACb,IACF,CAAC;IACD,IAAIlB,MAAM,CAACoB,cAAc,EAAE;MAEzBpB,MAAM,CAAChD,KAAK,CAACoE,cAAc,GAAGpB,MAAM,CAACoB,cAAc;MACnD,OAAOpB,MAAM,CAACoB,cAAc;IAC9B;IACAL,SAAS,CAACH,IAAI,CAACS,IAAI,CAACrB,MAAM,CAAC;EAC7B;EAEAsB,gBAAgBA,CAAA,EAAQ;IACtB,MAAMpE,IAAI,GAAG,KAAK,CAACoE,gBAAgB,CAAC,CAAC;IACF;MACjC,IAAI,CAAC,IAAI,CAACC,eAAe,CAAC,QAAQ,EAAE,eAAe,CAAC,EAAE;QACpD,OAAOrE,IAAI;MACb;IACF;IACA,OAAO,IAAI,CAACsE,qCAAqC,CAACtE,IAAI,CAAC;EACzD;EAEAsE,qCAAqCA,CACnCtE,IAAmB,EACQ;IAC3B,MAAMuE,IAAI,GAAG,KAAK,CAACC,gBAAgB,CAACxE,IAAI,CAAC;IACzCA,IAAI,GAAGA,IAAW;IAClB,OAAOA,IAAI,CAACyE,EAAE;IAEdzE,IAAI,CAACuE,IAAI,GAAGA,IAAI;IAEhBvE,IAAI,CAACmC,IAAI,GAAG,mBAAmB;IAC/B,OAAOnC,IAAI;EACb;EAEA0E,aAAaA,CAAC1E,IAAY,EAAW;IACA;MACjC,IAAI,CAAC,IAAI,CAACqE,eAAe,CAAC,QAAQ,EAAE,eAAe,CAAC,EAAE;QACpD,OAAO,KAAK,CAACK,aAAa,CAAC1E,IAAI,CAAC;MAClC;IACF;IACA,OAAOA,IAAI,CAACmC,IAAI,KAAK,mBAAmB;EAC1C;EAEAqC,gBAAgBA,CAACxE,IAAY,EAAU;IACF;MACjC,IAAI,CAAC,IAAI,CAACqE,eAAe,CAAC,QAAQ,EAAE,eAAe,CAAC,EAAE;QACpD,OAAO,KAAK,CAACG,gBAAgB,CAACxE,IAAI,CAAC;MACrC;IACF;IACA,OAAOA,IAAI,CAACuE,IAAI;EAClB;EAGA5C,YAAYA,CAAsB7B,KAAU,EAAEqC,IAAe,EAAK;IAChE,MAAMnC,IAAI,GAAG,KAAK,CAAC2B,YAAY,CAAI7B,KAAK,EAAEqC,IAAI,CAAC;IAE/CnC,IAAI,CAACoC,GAAG,GAAGpC,IAAI,CAACqC,KAAK,CAACD,GAAG;IACzB,OAAOpC,IAAI,CAACqC,KAAK;IAEjB,OAAOrC,IAAI;EACb;EAEA2E,iBAAiBA,CACf3E,IAAgB,EAChB4E,eAAgC,EAChCC,QAAiB,GAAG,KAAK,EACnB;IACN,KAAK,CAACF,iBAAiB,CAAC3E,IAAI,EAAE4E,eAAe,EAAEC,QAAQ,CAAC;IACxD7E,IAAI,CAACkC,UAAU,GAAGlC,IAAI,CAAC0D,IAAI,CAACvB,IAAI,KAAK,gBAAgB;EACvD;EAGA8B,WAAWA,CAGTjE,IAAe,EACf8D,WAAoB,EACpBpB,OAAgB,EAChBqB,aAAsB,EACtBe,gBAAyB,EACzB3C,IAAe,EACf4C,YAAqB,GAAG,KAAK,EACH;IAC1B,IAAIC,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAe,CAAC;IAC7CD,QAAQ,CAACE,IAAI,GAAGlF,IAAI,CAACkF,IAAI;IACzBF,QAAQ,GAAG,KAAK,CAACf,WAAW,CAE1Be,QAAQ,EACRlB,WAAW,EACXpB,OAAO,EACPqB,aAAa,EACbe,gBAAgB,EAChB3C,IAAI,EACJ4C,YACF,CAAC;IAEDC,QAAQ,CAAC7C,IAAI,GAAG,oBAAoB;IACpC,OAAO6C,QAAQ,CAACE,IAAI;IAEpBlF,IAAI,CAACF,KAAK,GAAGkF,QAAQ;IACrB,IAAI7C,IAAI,KAAK,oBAAoB,EAAE;MACjCnC,IAAI,CAACmF,QAAQ,GAAG,KAAK;IACvB;IACA,OAAO,IAAI,CAACC,UAAU,CAEpBpF,IAAI,EACJ,kBACF,CAAC;EACH;EAEAqF,kBAAkBA,CAAC,GAAGC,IAAuB,EAAO;IAClD,MAAMC,YAAY,GAAG,KAAK,CAACF,kBAAkB,CAAC,GAAGC,IAAI,CAAQ;IAC1B;MACjC,IAAI,CAAC,IAAI,CAACjB,eAAe,CAAC,QAAQ,EAAE,eAAe,CAAC,EAAE;QACpD,OAAOkB,YAAY;MACrB;IACF;IACAA,YAAY,CAACpD,IAAI,GAAG,oBAAoB;IACxC,OAAOoD,YAAY;EACrB;EAEAC,yBAAyBA,CAAC,GAAGF,IAA8B,EAAO;IAChE,MAAMC,YAAY,GAAG,KAAK,CAACC,yBAAyB,CAAC,GAAGF,IAAI,CAAQ;IACjC;MACjC,IAAI,CAAC,IAAI,CAACjB,eAAe,CAAC,QAAQ,EAAE,eAAe,CAAC,EAAE;QACpD,OAAOkB,YAAY;MACrB;IACF;IACAA,YAAY,CAACpD,IAAI,GAAG,oBAAoB;IACxCoD,YAAY,CAACJ,QAAQ,GAAG,KAAK;IAC7B,OAAOI,YAAY;EACrB;EAEAE,iBAAiBA,CACfC,IAAoB,EACpB5B,WAAoB,EACpBpB,OAAgB,EAChBiD,SAAkB,EAClBC,UAAmB,EACgB;IACnC,MAAM5F,IAAsB,GAAG,KAAK,CAACyF,iBAAiB,CACpDC,IAAI,EACJ5B,WAAW,EACXpB,OAAO,EACPiD,SAAS,EACTC,UACF,CAAQ;IAER,IAAI5F,IAAI,EAAE;MACRA,IAAI,CAACmC,IAAI,GAAG,UAAU;MACtB,IAAKnC,IAAI,CAA0BkF,IAAI,KAAK,QAAQ,EAAE;QACpDlF,IAAI,CAACkF,IAAI,GAAG,MAAM;MACpB;MACAlF,IAAI,CAAC6F,SAAS,GAAG,KAAK;IACxB;IAEA,OAAO7F,IAAI;EACb;EAEA8F,mBAAmBA,CACjBJ,IAAsB,EACtBK,QAAqC,EACrCJ,SAAkB,EAClBK,mBAA6C,EACR;IACrC,MAAMhG,IAAsB,GAAG,KAAK,CAAC8F,mBAAmB,CACtDJ,IAAI,EACJK,QAAQ,EACRJ,SAAS,EACTK,mBACF,CAAQ;IAER,IAAIhG,IAAI,EAAE;MACRA,IAAI,CAACkF,IAAI,GAAG,MAAM;MAClBlF,IAAI,CAACmC,IAAI,GAAG,UAAU;IACxB;IAEA,OAAOnC,IAAI;EACb;EAEAiG,WAAWA,CACT9D,IAAY,EACZ+D,yBAAkC,EAClCC,OAAqB,EACrB;IACA,OAAOhE,IAAI,KAAK,UAAU,GACtB,OAAO,GACP,KAAK,CAAC8D,WAAW,CAAC9D,IAAI,EAAE+D,yBAAyB,EAAEC,OAAO,CAAC;EACjE;EAEAC,YAAYA,CAACpG,IAAY,EAAEqG,SAAmB,EAAW;IACvD,IAAIrG,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC4C,gBAAgB,CAAC5C,IAAI,CAAC,EAAE;MAC/C,OAAO,IAAI,CAACoG,YAAY,CAACpG,IAAI,CAACF,KAAK,EAAEuG,SAAS,CAAC;IACjD;IACA,OAAO,KAAK,CAACD,YAAY,CAACpG,IAAI,EAAEqG,SAAS,CAAC;EAC5C;EAEAC,YAAYA,CAACtG,IAAY,EAAEuG,KAAc,GAAG,KAAK,EAAQ;IACvD,IAAIvG,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC4C,gBAAgB,CAAC5C,IAAI,CAAC,EAAE;MAC/C,MAAM;QAAEJ,GAAG;QAAEE;MAAM,CAAC,GAAGE,IAAI;MAC3B,IAAI,IAAI,CAAC0E,aAAa,CAAC9E,GAAG,CAAC,EAAE;QAC3B,IAAI,CAAC4G,UAAU,CAACC,cAAc,CAC5B,IAAI,CAACjC,gBAAgB,CAAC5E,GAAG,CAAC,EAC1BA,GAAG,CAACK,GAAG,CAACC,KACV,CAAC;MACH;MACA,IAAI,CAACoG,YAAY,CAACxG,KAAK,EAAEyG,KAAK,CAAC;IACjC,CAAC,MAAM;MACL,KAAK,CAACD,YAAY,CAACtG,IAAI,EAAEuG,KAAK,CAAC;IACjC;EACF;EAEAG,gCAAgCA,CAC9BhB,IAAY,EACZiB,MAAe,EACfJ,KAAc,EACd;IACA,IAAIb,IAAI,CAACR,IAAI,KAAK,KAAK,IAAIQ,IAAI,CAACR,IAAI,KAAK,KAAK,EAAE;MAC9C,IAAI,CAAC0B,KAAK,CAACC,kBAAM,CAACC,kBAAkB,EAAE;QAAEC,EAAE,EAAErB,IAAI,CAAC9F;MAAI,CAAC,CAAC;IACzD,CAAC,MAAM,IAAI8F,IAAI,CAAC5C,MAAM,EAAE;MACtB,IAAI,CAAC8D,KAAK,CAACC,kBAAM,CAACG,gBAAgB,EAAE;QAAED,EAAE,EAAErB,IAAI,CAAC9F;MAAI,CAAC,CAAC;IACvD,CAAC,MAAM;MACL,KAAK,CAAC8G,gCAAgC,CAAChB,IAAI,EAAEiB,MAAM,EAAEJ,KAAK,CAAC;IAC7D;EACF;EAEAU,oBAAoBA,CAClBC,UAAqB,EACrBC,QAAiB,EACd;IACH,MAAMnH,IAAI,GAAG,KAAK,CAACiH,oBAAoB,CAACC,UAAU,EAAEC,QAAQ,CAAC;IAE7D,IAAInH,IAAI,CAACoH,MAAM,CAACjF,IAAI,KAAK,QAAQ,EAAE;MAChCnC,IAAI,CAAwCmC,IAAI,GAAG,kBAAkB;MACrEnC,IAAI,CAAwCqH,MAAM,GAAGrH,IAAI,CAACsH,SAAS,CAAC,CAAC,CAAC;MACvE,IACE,IAAI,CAACC,SAAS,CAAC,kBAAkB,CAAC,IAClC,IAAI,CAACA,SAAS,CAAC,kBAAkB,CAAC,EAClC;QAAA,IAAAC,gBAAA;QACCxH,IAAI,CAAwCyH,UAAU,IAAAD,gBAAA,GACrDxH,IAAI,CAACsH,SAAS,CAAC,CAAC,CAAC,YAAAE,gBAAA,GAAI,IAAI;MAC7B;MAEA,OAAOxH,IAAI,CAACsH,SAAS;MAErB,OAAOtH,IAAI,CAACoH,MAAM;IACpB;IAEA,OAAOpH,IAAI;EACb;EAEA0H,qBAAqBA,CACnB1H,IAG4B,EAE5B;IAEA,IAAIA,IAAI,CAACmC,IAAI,KAAK,kBAAkB,EAAE;MACpC;IACF;IAEA,KAAK,CAACuF,qBAAqB,CAAC1H,IAAI,CAAC;EACnC;EAEA2H,WAAWA,CACTT,UAA+B,EAC/BU,UAAgC,EAChC;IACA,MAAMC,cAAc,GAAG,IAAI,CAACC,KAAK,CAACC,eAAe;IACjD,MAAM/H,IAAI,GAAG,KAAK,CAAC2H,WAAW,CAACT,UAAU,EAAEU,UAAU,CAAC;IAEtD,QAAQ5H,IAAI,CAACmC,IAAI;MACf,KAAK,sBAAsB;QAEzBnC,IAAI,CAACgI,QAAQ,GAAG,IAAI;QACpB;MAEF,KAAK,wBAAwB;QAC3B,IACEhI,IAAI,CAACiI,UAAU,CAACC,MAAM,KAAK,CAAC,IAE5BlI,IAAI,CAACiI,UAAU,CAAC,CAAC,CAAC,CAAC9F,IAAI,KAAK,0BAA0B,EACtD;UAEAnC,IAAI,CAACmC,IAAI,GAAG,sBAAsB;UAElCnC,IAAI,CAACgI,QAAQ,GAAGhI,IAAI,CAACiI,UAAU,CAAC,CAAC,CAAC,CAACD,QAAQ;UAC3C,OAAOhI,IAAI,CAACiI,UAAU;QACxB;MAGF,KAAK,0BAA0B;QAC7B;UAAA,IAAAE,qBAAA;UACE,MAAM;YAAEC;UAAY,CAAC,GAAGpI,IAAI;UAC5B,IACE,CAAAoI,WAAW,oBAAXA,WAAW,CAAEjG,IAAI,MAAK,kBAAkB,IACxC,EAAAgG,qBAAA,GAAAC,WAAW,CAACR,UAAU,qBAAtBO,qBAAA,CAAwBD,MAAM,IAAG,CAAC,IAElCE,WAAW,CAAClI,KAAK,KAAKF,IAAI,CAACE,KAAK,EAChC;YACA,IAAI,CAACmI,kBAAkB,CACrBrI,IAAI,EAKJ6H,cACF,CAAC;UACH;QACF;QAEA;IACJ;IAEA,OAAO7H,IAAI;EACb;EAEAsI,cAAcA,CACZC,IAAkB,EAClBxC,QAAkB,EAClByC,OAAmC,EACnCV,KAA4B,EAC5B;IACA,MAAM9H,IAAI,GAAG,KAAK,CAACsI,cAAc,CAACC,IAAI,EAAExC,QAAQ,EAAEyC,OAAO,EAAEV,KAAK,CAAC;IAEjE,IAAIA,KAAK,CAACW,mBAAmB,EAAE;MAE7B,IACEzI,IAAI,CAACmC,IAAI,KAAK,0BAA0B,IACxCnC,IAAI,CAACmC,IAAI,KAAK,wBAAwB,EACtC;QACAnC,IAAI,CAACmC,IAAI,GAAGnC,IAAI,CAACmC,IAAI,CAACuG,SAAS,CAAC,CAAC,CAAC;MACpC;MACA,IAAIZ,KAAK,CAACa,IAAI,EAAE;QACd,MAAMC,KAAK,GAAG,IAAI,CAACC,eAAe,CAAC7I,IAAI,CAAC;QACxC4I,KAAK,CAAC1G,UAAU,GAAGlC,IAAI;QACvB,OAAO,IAAI,CAACoF,UAAU,CAACwD,KAAK,EAAE,iBAAiB,CAAC;MAClD;IACF,CAAC,MAAM,IACL5I,IAAI,CAACmC,IAAI,KAAK,kBAAkB,IAChCnC,IAAI,CAACmC,IAAI,KAAK,gBAAgB,EAC9B;MACAnC,IAAI,CAACmH,QAAQ,GAAG,KAAK;IACvB;IAEA,OAAOnH,IAAI;EACb;EAEA8I,wBAAwBA,CAAC9I,IAAY,EAAW;IAC9C,IAAIA,IAAI,CAACmC,IAAI,KAAK,iBAAiB,EAAE;MACnCnC,IAAI,GAAGA,IAAI,CAACkC,UAAU;IACxB;IACA,OAAO,KAAK,CAAC4G,wBAAwB,CAAC9I,IAAI,CAAC;EAC7C;EAGA4C,gBAAgBA,CAAC5C,IAAY,EAAW;IACtC,OAAOA,IAAI,CAACmC,IAAI,KAAK,UAAU,IAAInC,IAAI,CAACkF,IAAI,KAAK,MAAM,IAAI,CAAClF,IAAI,CAAC8C,MAAM;EACzE;EAEAiG,cAAcA,CAAC/I,IAAY,EAAW;IACpC,OAAOA,IAAI,CAAC8C,MAAM,IAAI9C,IAAI,CAACkF,IAAI,KAAK,KAAK,IAAIlF,IAAI,CAACkF,IAAI,KAAK,KAAK;EAClE;EAEA8D,YAAYA,CACVhJ,IAAe,EACfmC,IAAe,EACf8G,MAAgB,EACb;IACH,OAAOlJ,gBAAgB,CAAC,KAAK,CAACiJ,YAAY,CAAChJ,IAAI,EAAEmC,IAAI,EAAE8G,MAAM,CAAC,CAAC;EACjE;EAEAZ,kBAAkBA,CAACrI,IAAY,EAAE+F,QAAkB,EAAE;IACnD,KAAK,CAACsC,kBAAkB,CAACrI,IAAI,EAAE+F,QAAQ,CAAC;IACxChG,gBAAgB,CAACC,IAAI,CAAC;EACxB;EAEAkJ,gBAAgBA,CACdlJ,IAAc,EACdiJ,MAAgB,GAAG,IAAI,CAACnB,KAAK,CAACqB,aAAa,EACrC;IACN,KAAK,CAACD,gBAAgB,CAAClJ,IAAI,EAAEiJ,MAAM,CAAC;IACpClJ,gBAAgB,CAACC,IAAI,CAAC;EACxB;AACF,CAAC;AAAAoJ,OAAA,CAAAC,OAAA,GAAAjJ,QAAA"}