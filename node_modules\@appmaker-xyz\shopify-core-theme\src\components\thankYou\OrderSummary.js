import React from 'react';

import { FlatList, StyleSheet, Image } from 'react-native';
import { ThemeText, Layout } from '@appmaker-xyz/ui';
import { useOrderItem } from '@appmaker-xyz/shopify';

const OrderSummary = (props) => {
  const {
    lineItems,
    orderTotalAmountWithCurrency,
    totalShippingPrice,
    subtotalPrice,
    totalTax,
    withCurrency,
  } = useOrderItem(props);

  const summaryData = [
    {
      title: 'Subtotal',
      value: subtotalPrice.amount,
      currencyCode: subtotalPrice.currencyCode,
      valueWithCurrency: withCurrency(
        subtotalPrice.amount,
        subtotalPrice.currencyCode,
      ),
    },
    {
      title: 'Shipping',
      value: totalShippingPrice.amount,
      currencyCode: totalShippingPrice.currencyCode,
      valueWithCurrency: withCurrency(
        totalShippingPrice.amount,
        totalShippingPrice.currencyCode,
      ),
    },
    {
      title: 'Total Tax',
      value: totalTax.amount,
      currencyCode: totalTax.currencyCode,
      valueWithCurrency: withCurrency(totalTax.amount, totalTax.currencyCode),
    },
  ];

  return (
    <Layout style={styles.container}>
      <Layout style={styles.titleContainer}>
        <ThemeText style={styles.titleText} fontFamily="bold">
          Order Summary
        </ThemeText>
      </Layout>
      <FlatList
        data={lineItems}
        style={styles.contentContainer}
        renderItem={({ item, index }) => (
          <Layout key={index} style={styles.itemContainer}>
            <Image
              style={styles.image}
              source={{ uri: item?.variant?.image?.url }}
            />
            <Layout style={styles.dataContainer}>
              <Layout style={styles.span}>
                <ThemeText
                  numberofLines={1}
                  style={styles.itemName}
                  fontFamily="bold">
                  {item?.title}
                </ThemeText>
                {item?.currentQuantity && item?.currentQuantity > 1 && (
                  <Layout style={styles.itemCount}>
                    <ThemeText style={styles.itemCountText}>
                      {`x${item?.currentQuantity}`}
                    </ThemeText>
                  </Layout>
                )}
              </Layout>
              <Layout style={styles.span}>
                <ThemeText numberofLines={1} style={styles.itemVariant}>
                  {item?.variant?.title}
                </ThemeText>
                <ThemeText style={styles.itemPrice} fontFamily="bold">
                  {withCurrency(
                    item?.discountedTotalPrice?.amount ||
                      item?.variant?.price?.amount ||
                      item?.originalTotalPrice?.amount ||
                      0,
                    item?.currency,
                  )}
                </ThemeText>
              </Layout>
            </Layout>
          </Layout>
        )}
        keyExtractor={(item) => item.id}
      />
      <Layout style={styles.summaryWrapper}>
        {summaryData.map((item, index) => (
          <Layout
            key={index}
            style={[
              styles.span,
              styles.summaryContainer,
              {
                borderBottomWidth: index === summaryData.length - 1 ? 1 : 0,
                borderColor: 'rgba(0,0,0,0.25)',
              },
            ]}>
            <ThemeText style={styles.summaryTitle} fontFamily="bold">
              {item.title}
            </ThemeText>
            <ThemeText style={styles.summaryValue} fontFamily="bold">
              {item.valueWithCurrency}
            </ThemeText>
          </Layout>
        ))}
      </Layout>
      <Layout style={styles.totalContainer}>
        <Layout
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <Layout>
            <ThemeText
              fontFamily="bold"
              style={{
                color: '#000',
                fontSize: 16,
              }}>
              Total
            </ThemeText>
            <ThemeText
              style={{
                color: '#000',
                opacity: 0.5,
              }}>
              Incl Taxes
            </ThemeText>
          </Layout>
          <ThemeText
            fontFamily="bold"
            style={{
              fontSize: 24,
              color: '#000',
            }}>
            {orderTotalAmountWithCurrency}
          </ThemeText>
        </Layout>
      </Layout>
    </Layout>
  );
};

export default OrderSummary;

const styles = StyleSheet.create({
  container: {},
  totalContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  summaryContainer: {
    paddingBottom: 12,
  },
  summaryWrapper: {
    padding: 16,
    paddingBottom: 12,
  },
  summaryTitle: {
    opacity: 0.5,
  },
  summaryValue: {},
  titleContainer: {
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: 'rgba(0,0,0,0.25)',
  },
  titleText: {
    padding: 16,
    fontSize: 16,
    color: '#000',
  },
  itemContainer: {
    paddingVertical: 16,
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderColor: 'rgba(0,0,0,0.25)',
  },
  contentContainer: {
    paddingHorizontal: 16,
  },
  image: {
    width: 52,
    height: 52,
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  itemName: {
    fontSize: 14,
  },
  itemCount: {
    padding: 4,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 100,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemCountText: {
    fontSize: 10,
  },
  itemVariant: {
    opacity: 0.5,
  },
  itemPrice: {
    fontSize: 14,
  },
  span: {
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  dataContainer: {
    paddingVertical: 2,
    paddingLeft: 8,
    flex: 1,
    justifyContent: 'space-between',
  },
});
