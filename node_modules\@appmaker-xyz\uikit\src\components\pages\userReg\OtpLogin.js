import React, { useState } from 'react';
import { StyleSheet, KeyboardAvoidingView, Platform } from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import {
  Layout,
  Input,
  Button,
  AppmakerText,
  AppImage,
} from '../../../components';

const OtpLogin = (props) => {
  const { logoUrl } = props;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  const [submit, setSubmit] = useState(false);

  const onSubmit = () => {
    setSubmit(!submit);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS == 'ios' ? 'padding' : 'height'}>
      <Layout style={styles.otpview}>
        {logoUrl && <AppImage uri={logoUrl} style={styles.logo} />}
        {submit === false ? (
          <>
            <Input label="Mobile Number" leftIcon="phone" />
            <Button status="dark" onPress={onSubmit}>
              Send OTP
            </Button>
          </>
        ) : (
          <>
            <Input
              label="Enter OTP"
              leftIcon="key"
              type="number"
              caption="Enter OTP send to your Mobile Number"
            />
            <Button status="dark" onPress={onSubmit}>
              Login
            </Button>
            <Button small status="white">
              Resend OTP
            </Button>
          </>
        )}
      </Layout>

      <Layout style={styles.signIn}>
        <AppmakerText
          category="bodyParagraph"
          status="demiDark"
          style={styles.signInQue}>
          Sign in using email
        </AppmakerText>
        <Button outline status="dark" block>
          Sign In
        </Button>
      </Layout>
    </KeyboardAvoidingView>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      padding: spacing.base,
      flex: 1,
      alignItems: 'center',
    },
    otpview: {
      position: 'relative',
      width: '100%',
      flex: 1,
    },
    logo: {
      width: '50%',
      height: 80,
      alignSelf: 'center',
      resizeMode: 'contain',
      marginVertical: spacing.md,
    },
    signIn: {
      width: '100%',
      alignItems: 'center',
    },
    signInQue: {
      marginBottom: spacing.mini,
    },
  });

export default OtpLogin;
