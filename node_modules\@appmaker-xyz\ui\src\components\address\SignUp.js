import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ThemeText, AppTouchable } from '../atoms';
import { Button } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { AppModal, Input } from '../coreComponents';

const SignUp = () => {
  const [isModalVisible, setModalVisible] = useState(false);
  const [value, setValue] = useState('');

  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };
  return (
    <Layout>
      <Button title="SIGN UP" onPress={toggleModal} />

      <AppModal
        isVisible={isModalVisible}
        onBackdropPress={() => setModalVisible(false)}
        style={styles.modal}>
        <Layout style={styles.modalContainer}>
          <Layout style={styles.modalHeader}>
            <ThemeText size="xl" fontFamily="medium">
              {'Hello! Sign up to get \nstarted'}
            </ThemeText>
            <Icon
              name="x"
              size={28}
              color="#000"
              style={styles.closeIcon}
              onPress={toggleModal}
            />
          </Layout>
          <Layout style={styles.inputsContainer}>
            <Input placeholder="User name" value={value} onChange={setValue} />
            <Input placeholder="Email" value={value} onChange={setValue} />
            <Input placeholder="Password" value={value} onChange={setValue} />
            <Input
              placeholder="Confirm Password"
              value={value}
              onChange={setValue}
            />
          </Layout>
          <AppTouchable style={styles.signUpButton}>
            <ThemeText size="lg" color="#fff">
              Register
            </ThemeText>
          </AppTouchable>
          <Layout style={styles.loginSeparator}>
            <Layout style={styles.line} />
            <ThemeText style={styles.loginSeparatorText}>
              Or Register with
            </ThemeText>
            <Layout style={styles.line} />
          </Layout>
        </Layout>
      </AppModal>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {},
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 18,
    borderTopRightRadius: 18,
    overflow: 'hidden',
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  closeIcon: {
    padding: 6,
  },
  inputsContainer: {
    marginVertical: 12,
  },
  signUpButton: {
    flexDirection: 'row',
    backgroundColor: '#1E232C',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 6,
  },
  loginSeparator: {
    marginVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  loginSeparatorText: {
    marginHorizontal: 8,
  },
  line: {
    flexGrow: 1,
    height: 1.5,
    backgroundColor: '#E8ECF4',
  },
});

export default SignUp;
