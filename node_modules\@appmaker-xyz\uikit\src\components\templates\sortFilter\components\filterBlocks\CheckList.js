import { FlatList } from 'react-native';
import React, { useState } from 'react';
import { CheckBox } from '@appmaker-xyz/uikit';
import { useFilterStore } from '../../store';

export default function CheckList({ attributes, value = [], setValue }) {
  //   const [response, setResponse] = useState([]);
  const { values: items } = attributes;
  const [finalParams, setFinalParams] = useState(value);
  // console.log(defaultArray);
  const setAppliedFilter = useFilterStore((state) => state.setAppliedFilter);
  const appliedFilters = useFilterStore(
    (state) => state.appliedFilters[attributes.id] || {},
  );
  const renderItem = ({ item, index }) => {
    let resp = item;
    let key = index;
    return (
      <CheckBox
        label={item.label}
        value={
          appliedFilters[item.id] && appliedFilters[item.id].state
            ? true
            : false
        }
        onValueChange={(status) => {
          setAppliedFilter(attributes.id, {
            [item.id]: { state: status, value: status ? item.input : null },
          });
          //   response[key].checked = status;
          //   let temp = [...finalParams];
          //   if (status) {
          //     temp.push(response[key].input);
          //   } else {
          //     temp && temp.splice(response[key].input);
          //   }
          //   setFinalParams(temp);
          //   selectedParams(temp);
          //   setResponse(response);
          //   res = response;
        }}
        key={key}
      />
    );
  };
  return (
    <FlatList
      data={items}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
    />
  );
}
