/**
 * This script merges base GraphQL queries with their extensions
 * It allows adding fields to existing operations without modifying the original files
 */

const fs = require('fs');
const path = require('path');
const { parse, print } = require('graphql');

// Helper function to deep clone objects to avoid reference issues
function deepClone(obj) {
  return JSON.parse(JSON.stringify(obj));
}

// Directories
const BASE_DIR = path.join(__dirname, '..', 'graphql');
const EXTENDED_DIR = path.resolve(__dirname, '../extended-graphql');
const CLIENT_DIR = path.resolve(__dirname, '../client');
const TEMP_DIR = path.resolve(__dirname, '../.temp-graphql');

// Check for merged-operations.json file from the main script
const mergedOperationsPath = path.join(TEMP_DIR, 'merged-operations.json');
let externalOperations = {};

if (fs.existsSync(mergedOperationsPath)) {
  try {
    const mergedData = JSON.parse(fs.readFileSync(mergedOperationsPath, 'utf8'));
    if (mergedData.operations) {
      externalOperations = mergedData.operations;
      console.log(`Found ${Object.keys(externalOperations).length} operations in merged-operations.json`);
    } else {
      console.warn('No operations found in merged-operations.json');
    }
  } catch (error) {
    console.error('Error reading merged-operations.json:', error);
  }
}

// Create temp directory if it doesn't exist
if (fs.existsSync(TEMP_DIR)) {
  // Clear existing temp directory
  // But don't delete merged-operations.json if it exists
  const files = fs.readdirSync(TEMP_DIR);
  files.forEach(file => {
    if (file !== 'merged-operations.json') {
      const filePath = path.join(TEMP_DIR, file);
      if (fs.lstatSync(filePath).isDirectory()) {
        fs.rmSync(filePath, { recursive: true, force: true });
      } else {
        fs.unlinkSync(filePath);
      }
    }
  });
} else {
  fs.mkdirSync(TEMP_DIR, { recursive: true });
}

// Helper function to get all files recursively
function getAllFiles(dir, fileList = []) {
  if (!fs.existsSync(dir)) return fileList;
  
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    
    if (fs.statSync(filePath).isDirectory()) {
      fileList = getAllFiles(filePath, fileList);
    } else if (file.endsWith('.gql')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Get all base and extension GraphQL files
const baseFiles = getAllFiles(BASE_DIR);
const extendedFiles = getAllFiles(EXTENDED_DIR);
const clientFiles = getAllFiles(CLIENT_DIR);

// Also include any files from external extended-graphql directories
let externalExtendedFiles = [];
if (Object.keys(externalOperations).length > 0) {
  for (const [operationName, operations] of Object.entries(externalOperations)) {
    operations.forEach(operation => {
      if (fs.existsSync(operation.path)) {
        externalExtendedFiles.push(operation.path);
        console.log(`Including extended file: ${operation.path}`);
      }
    });
  }
}

// Create a map of operation names to their extensions
const operationExtensions = {};

// Process all extension files
const allExtendedFiles = [...extendedFiles, ...externalExtendedFiles];
allExtendedFiles.forEach(filePath => {
  const content = fs.readFileSync(filePath, 'utf8');
  
  try {
    // Parse the GraphQL document
    const document = parse(content);
    
    // Extract operation or fragment name and type
    document.definitions.forEach(def => {
      if ((def.kind === 'OperationDefinition' || def.kind === 'FragmentDefinition') && def.name) {
        const definitionName = def.name.value;
        
        // Store the definition for later merging
        if (!operationExtensions[definitionName]) {
          operationExtensions[definitionName] = [];
        }
        
        operationExtensions[definitionName].push({
          filePath,
          definition: def
        });
      }
    });
  } catch (error) {
    console.error(`Error parsing ${filePath}:`, error);
  }
});

// Process and copy all base files
baseFiles.forEach(filePath => {
  const content = fs.readFileSync(filePath, 'utf8');
  let document;
  let modified = false;
  
  try {
    // Parse the GraphQL document
    document = parse(content);
    
    // Check if any definitions in this file have extensions
    document.definitions.forEach(def => {
      if ((def.kind === 'OperationDefinition' || def.kind === 'FragmentDefinition') && def.name) {
        const definitionName = def.name.value;
        
        // Check if this definition has extensions
        if (operationExtensions[definitionName]) {
          modified = true;
          
          // Helper function to merge inline fragments
          const mergeInlineFragments = (selections) => {
            // Group fragments by type condition
            const fragmentsByType = {};
            
            selections.forEach((selection, index) => {
              if (selection.kind === 'InlineFragment' && selection.typeCondition) {
                const typeName = selection.typeCondition.name.value;
                if (!fragmentsByType[typeName]) {
                  fragmentsByType[typeName] = {
                    index,
                    fragment: selection
                  };
                } else {
                  // Merge the selection sets
                  if (selection.selectionSet && fragmentsByType[typeName].fragment.selectionSet) {
                    mergeSelectionSet(
                      selection.selectionSet,
                      fragmentsByType[typeName].fragment.selectionSet
                    );
                  }
                  // Mark for removal
                  selection.merged = true;
                }
              }
            });
            
            // Remove merged fragments
            return selections.filter(s => !s.merged);
          };
          
          // Recursive function to merge selection sets
          const mergeSelectionSet = (sourceSet, targetSet) => {
            sourceSet.selections.forEach(sourceSelection => {
              if (sourceSelection.kind === 'Field') {
                const sourceFieldName = sourceSelection.name.value;
                const sourceAlias = sourceSelection.alias ? sourceSelection.alias.value : null;
                
                // Find matching field in target by name or alias
                const targetSelection = targetSet.selections.find(
                  ts => ts.kind === 'Field' && (
                    // Match by field name when no alias is present
                    (ts.name.value === sourceFieldName && !sourceAlias && !ts.alias) ||
                    // Match by alias if source has an alias
                    (sourceAlias && ts.alias && ts.alias.value === sourceAlias) ||
                    // Or match by name if aliases match up (both null or same value)
                    (ts.name.value === sourceFieldName && 
                     ((sourceAlias === null && ts.alias === undefined) || 
                      (sourceAlias && ts.alias && sourceAlias === ts.alias.value)))
                  )
                );
                
                if (targetSelection) {
                  // Field exists in target, recursively merge their selection sets if they exist
                  if (sourceSelection.selectionSet && targetSelection.selectionSet) {
                    mergeSelectionSet(sourceSelection.selectionSet, targetSelection.selectionSet);
                  }
                } else {
                  // Field doesn't exist in target, add it directly
                  targetSet.selections.push(deepClone(sourceSelection));
                }
              } else if (sourceSelection.kind === 'InlineFragment') {
                // For inline fragments, find by type condition
                const sourceTypeName = sourceSelection.typeCondition ? sourceSelection.typeCondition.name.value : null;
                
                const targetFragment = targetSet.selections.find(
                  ts => ts.kind === 'InlineFragment' && 
                       ts.typeCondition && 
                       sourceSelection.typeCondition &&
                       ts.typeCondition.name.value === sourceTypeName
                );
                
                if (targetFragment) {
                  // Fragment with same type condition exists, merge selection sets
                  if (sourceSelection.selectionSet && targetFragment.selectionSet) {
                    mergeSelectionSet(sourceSelection.selectionSet, targetFragment.selectionSet);
                  }
                } else {
                  // Fragment doesn't exist, add it directly
                  targetSet.selections.push(deepClone(sourceSelection));
                }
              } else {
                // For other selections, check if they exist before adding
                const exists = targetSet.selections.some(
                  ts => JSON.stringify(ts) === JSON.stringify(sourceSelection)
                );
                if (!exists) {
                  targetSet.selections.push(deepClone(sourceSelection));
                }
              }
            });
            
            // Merge inline fragments with the same type condition
            targetSet.selections = mergeInlineFragments(targetSet.selections);
          };
          
          // Merge the fields from extensions into this operation
          operationExtensions[definitionName].forEach(ext => {
            // Find the operation's selection set to add fields
            ext.definition.selectionSet.selections.forEach(selection => {
              // For each top-level selection (field) in the extension
              if (selection.kind === 'Field') {
                const fieldName = selection.name.value;
                
                // Check if this field already exists in the base operation
                const existingBaseSelection = def.selectionSet.selections.find(
                  baseSelection => 
                    baseSelection.kind === 'Field' && 
                    baseSelection.name.value === fieldName
                );
                
                if (existingBaseSelection) {
                  // Field exists, merge subfields
                  if (selection.selectionSet && existingBaseSelection.selectionSet) {
                    // Apply recursive merging
                    mergeSelectionSet(selection.selectionSet, existingBaseSelection.selectionSet);
                  }
                } else {
                  // Field doesn't exist in base, add it directly
                  def.selectionSet.selections.push(deepClone(selection));
                }
              }
            });
          });
        }
      }
    });
    
    // Write the document to temp dir (whether modified or not)
    const relativePath = path.relative(BASE_DIR, filePath);
    const outputPath = path.join(TEMP_DIR, relativePath);
    const outputDir = path.dirname(outputPath);
    
    // Create output directory if it doesn't exist
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Write the document
    fs.writeFileSync(outputPath, print(document));
    if (modified) {
      console.log(`Extended operation written to ${outputPath}`);
    } else {
      console.log(`Base operation copied to ${outputPath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
  }
});

// Copy client files to temp directory
clientFiles.forEach(filePath => {
  const relativePath = path.relative(CLIENT_DIR, filePath);
  const outputPath = path.join(TEMP_DIR, 'client', relativePath);
  const outputDir = path.dirname(outputPath);
  
  // Create output directory if it doesn't exist
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Copy the file
  fs.copyFileSync(filePath, outputPath);
  console.log(`Client file copied to ${outputPath}`);
});

console.log('GraphQL operations merging completed!'); 