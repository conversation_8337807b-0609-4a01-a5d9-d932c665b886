import React from 'react';
import { appmaker } from '@appmaker-xyz/core';
import ProductGridOne from './ProductGridOne';
import ProductGridTwo from './ProductGridTwo';
import ProductGridThree from './ProductGridThree';

const ProductGridBlock = ({ attributes, ...props }) => {
  const productListDesign = appmaker.applyFilters(
    'product-list-type',
    'type-1',
  );
  const addCartButton = appmaker.applyFilters(
    'product-grid-cta-button',
    'none',
  );

  if (productListDesign === 'type-2') {
    return (
      <ProductGridTwo
        attributes={attributes}
        addCartButton={addCartButton}
        {...props}
      />
    );
  }
  if (productListDesign === 'type-3') {
    return (
      <ProductGridThree
        attributes={attributes}
        addCartButton={addCartButton}
        {...props}
      />
    );
  }
  return (
    <ProductGridOne
      attributes={attributes}
      addCartButton={addCartButton}
      {...props}
    />
  );
};

export default ProductGridBlock;
