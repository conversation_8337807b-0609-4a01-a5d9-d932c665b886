import React, { useState } from 'react';
import { StyleSheet, TextInput, View } from 'react-native';
import { AppmakerText } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { testProps } from '@appmaker-xyz/core';
const Input = ({
  type,
  editable,
  leftIcon,
  rightIcon,
  label,
  status,
  caption,
  captionIcon,
  value,
  onChangeText,
  maxLength,
  onChange,
  clientId,
  styleType,
  autoFocus,
  __appmakerCustomStyles = {},
  ...props
}) => {
  const [focus, setFocus] = useState(false);
  const [hidePass, setHidePass] = useState(true);
  const { color, spacing, font, fonts } = useApThemeState();
  const styles = allStyles({ color, spacing, font, fonts });
  const mainContainerStyles = [styles.container];
  const containerStyles = [styles.inputContainer];
  const iconStyles = [styles.icon];
  const inputTextStyle = [styles.inputArea];
  const floatingLabelStyles = [styles.floatingLabel];

  if (status && color[status]) {
    containerStyles.push({ borderColor: color[status] });
    iconStyles.push({ color: color[status] });
  }

  if (editable === false) {
    containerStyles.push({
      backgroundColor: color.light,
      borderColor: color.grey,
    });
    iconStyles.push({ color: color.grey });
    inputTextStyle.push({ color: color.grey });
  }

  if (styleType === 'noContainer') {
    mainContainerStyles.push({
      paddingTop: 0,
      marginBottom: 0,
    });
  }

  if (__appmakerCustomStyles) {
    mainContainerStyles.push(__appmakerCustomStyles?.main_container);
    containerStyles.push(__appmakerCustomStyles?.container);
    iconStyles.push(__appmakerCustomStyles?.icon);
    inputTextStyle.push(__appmakerCustomStyles?.input_text);
    floatingLabelStyles.push(__appmakerCustomStyles?.floating_label);
  }

  return (
    <View style={mainContainerStyles}>
      <View style={containerStyles}>
        {leftIcon && <Icon name={leftIcon} style={iconStyles} />}
        {label && focus === true && (
          <View style={floatingLabelStyles}>
            <AppmakerText
              category="bodySubText"
              status={status}
              style={__appmakerCustomStyles?.floating_label_text}>
              {label}
            </AppmakerText>
          </View>
        )}
        <TextInput
          {...testProps(clientId)}
          style={inputTextStyle}
          textContentType={type}
          keyboardType={type === 'number' ? 'phone-pad' : 'default'}
          secureTextEntry={type && type === 'password' && hidePass}
          placeholder={label}
          autoFocus={autoFocus}
          editable={editable}
          value={value}
          maxLength={maxLength}
          onChangeText={onChangeText}
          onChange={onChange}
          onFocus={() => {
            setFocus(true);
          }}
          {...props}
        />
        {rightIcon && <Icon name={rightIcon} style={iconStyles} />}
        {type && type === 'password' && (
          <Icon
            name={hidePass === false ? 'eye-off' : 'eye'}
            style={iconStyles}
            onPress={() => {
              setHidePass(!hidePass);
            }}
          />
        )}
      </View>
      {caption && (
        <AppmakerText
          category="highlighter2"
          status={status}
          style={[styles.caption, __appmakerCustomStyles?.caption_text]}>
          {caption && captionIcon && <Icon name={captionIcon} />} {caption}
        </AppmakerText>
      )}
    </View>
  );
};

const allStyles = ({ spacing, color, font, fonts }) =>
  StyleSheet.create({
    container: {
      paddingTop: spacing.small,
      position: 'relative',
      marginBottom: spacing.md,
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      height: 50,
      backgroundColor: color.white,
      borderWidth: 1,
      borderColor: color.demiDark,
      borderRadius: 0,
      paddingHorizontal: spacing.small,
    },
    inputArea: {
      flex: 1,
      paddingHorizontal: spacing.small,
      ...fonts.bodyParagraph,
      color: color.dark,
      fontWeight: 'normal',
    },
    floatingLabel: {
      position: 'absolute',
      top: -11,
      left: 8,
      backgroundColor: color.white,
      paddingHorizontal: spacing.nano,
    },
    icon: {
      fontSize: font.size.lg,
      color: color.dark,
      padding: spacing.nano,
    },
    caption: {
      marginTop: spacing.nano,
    },
  });

export default Input;
