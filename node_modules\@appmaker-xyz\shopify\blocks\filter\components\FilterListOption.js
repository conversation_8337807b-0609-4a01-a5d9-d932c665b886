import React from 'react';
import { CheckBox } from '@appmaker-xyz/ui';
import { useSelectedFilterItem } from '../../../hooks/filter/userCollectionFilter';
import { FlatList, StyleSheet } from 'react-native';
import { ThemeText } from '@appmaker-xyz/ui';

function CheckBoxItem({
  label,
  count,
  selectFilter,
  removeFilter,
  filterKey,
  item,
  parentItem,
  testId,
  activeColor,
  textStyles,
  countTextStyles,
}) {
  const isEmpty = item?.empty === true;
  const selectedItem = useSelectedFilterItem({
    filterKey,
    filterValueId: item.id,
  });
  const isUpperCase = parentItem?.upper_case || false;
  let colorStyle = {
    color: 'black',
  };
  if (isEmpty) {
    colorStyle = {
      color: '#A1A1AA',
    };
  }
  return (
    <CheckBox
      testId={testId}
      activeColor={activeColor || '#000'}
      small={true}
      disable={isEmpty}
      label={
        <>
          <ThemeText
            fontFamily="medium"
            style={[
              isUpperCase ? styles.checkBoxItemCaptialize : styles.checkBoxItem,
              colorStyle,
              textStyles,
            ]}>
            {label}
          </ThemeText>
          {count === undefined ? null : (
            <ThemeText
              size="sm"
              style={[styles.activeCountText, countTextStyles]}>
              {' ('+count + ')'}
            </ThemeText>
          )}
        </>
      }
      value={selectedItem?.id === item.id}
      onValueChange={(status) => {
        if (status) {
          selectFilter(filterKey, item.id, item);
        } else {
          removeFilter(filterKey, item.id, item);
        }
      }}
    />
  );
}

function FilterListOption({
  item,
  selectFilter,
  removeFilter,
  extensionStyles,
}) {
  const { values } = item;

  function renderItem({ item: filterItem, index }) {
    return (
      <CheckBoxItem
        testId={`filter-option-${index}`}
        label={filterItem.label}
        count={filterItem.count}
        selectFilter={selectFilter}
        removeFilter={removeFilter}
        filterKey={item.id}
        item={filterItem}
        parentItem={item}
        activeColor={extensionStyles?.filterModalCheckboxColor}
        textStyles={extensionStyles?.filterModalCheckboxText}
        countTextStyles={extensionStyles?.filterModalCheckboxCountText}
      />
    );
  }
  return (
    <FlatList
      data={values}
      renderItem={renderItem}
      contentContainerStyle={styles.filterContentViewContainer}
      keyExtractor={(item, index) => index.toString()}
    />
  );
}

const styles = StyleSheet.create({
  activeCountText: {
    color: '#64748B',
    padding: 2,
    borderRadius: 4,
    marginLeft: 10,
    lineHeight: 18,
  },
  filterContentViewContainer: {
    padding: 10,
  },
  checkBoxItemCaptialize: {
    textTransform: 'capitalize',
    lineHeight: 18,
  },
  checkBoxItem: {
    lineHeight: 18,
  },
});

export default FilterListOption;
