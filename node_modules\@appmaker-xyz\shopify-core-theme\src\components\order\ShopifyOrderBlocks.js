import React from 'react';
import { useOrderItem } from '@appmaker-xyz/shopify';

export function ShopifyOrderLineItems({
  attributes,
  BlockItem,
  onAction,
  currentAction,
  BlocksView,
  innerBlocks,
  blockData,
}) {
  const block = {
    name: 'shopify/single-order-line-item',
    clientId: 'order-detail-product-card',
    attributes: {
      dataSource: {
        repeatable: 'Yes',
        attributes: {
          params: '{{blockData.node.lineItems.edges}}',
        },
        source: 'variables',
        repeatItem: 'DataVariable',
      },
    },
    dependencies: {
      pageState: ['productStatus', 'metaData'],
    },
  };
  return (
    <BlockItem
      BlockItem={BlockItem}
      BlocksView={BlocksView}
      currentAction={currentAction}
      onAction={onAction}
      block={block}
    />
  );
}

export function ShopifyOrderPriceTable(props) {
  const {
    attributes,
    BlockItem,
    onAction,
    currentAction,
    BlocksView,
    innerBlocks,
    blockData,
  } = props;

  const { orderTotalAmountWithCurrency } = useOrderItem(props);
  const block = {
    name: 'appmaker/table-cell',
    clientId: 'order-detail-price-table',
    attributes: {
      title: 'Total Amount',
      value: orderTotalAmountWithCurrency,
      style: { marginHorizontal: 12, marginTop: 6 },
    },
  };
  return (
    <BlockItem
      BlockItem={BlockItem}
      BlocksView={BlocksView}
      currentAction={currentAction}
      onAction={onAction}
      block={block}
    />
  );
}
