import { convertToNodes } from '../shopifyDataSource';
const withNodes = [
  {
    node: {
      id: 'Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzk4OTUyNzYwOTk=',
      title: 'Snare Boot',
    },
  },
  {
    node: {
      id: 'Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzk4OTUyODg1MTU=',
      title: 'Meteor Shirt',
    },
  },
];
const nodes = [
  {
    id: 'Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzk4OTUyNzYwOTk=',
    title: 'Snare Boot',
  },
  {
    id: 'Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0Lzk4OTUyODg1MTU=',
    title: 'Meteor Shirt',
  },
];

test('add nodes if not exist', () => {
  const inputData = {
    data: {
      data: {
        nodes,
      },
    },
  };
  const out = convertToNodes(inputData);
  expect(out.data.data.products).toEqual({ edges: withNodes });
});

test('does not add if not exist', () => {
  const inputData = {
    data: {
      data: {
        nodes: withNodes,
      },
    },
  };
  const out = convertToNodes(inputData);
  expect(out.data.data.products).toEqual({ edges: withNodes });
});
