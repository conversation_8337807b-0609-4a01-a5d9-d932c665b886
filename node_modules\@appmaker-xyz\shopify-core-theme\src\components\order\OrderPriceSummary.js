import React from 'react';
import { StyleSheet } from 'react-native';
import { ThemeText, Layout } from '@appmaker-xyz/ui';
import { useOrderItem } from '@appmaker-xyz/shopify';

function Row(props) {
  const { title, data, fontFamily = 'regular', color } = props;
  return (
    <Layout style={styles.row}>
      <ThemeText fontFamily={fontFamily} color={color}>
        {title}
      </ThemeText>
      <ThemeText fontFamily={fontFamily} color={color}>
        {data}
      </ThemeText>
    </Layout>
  );
}

const OrderPriceSummary = (props) => {
  const { orderTotalAmountWithCurrency } = useOrderItem(props);
  return (
    <Layout style={styles.container}>
      <ThemeText size="md" fontFamily="medium">
        Price Details
      </ThemeText>
      <Row title="Cart Total" data="**₹ 1,000**" />
      <Row
        title="Savings"
        data="**₹ 1,000**"
        color="#427FD1"
        fontFamily="medium"
      />
      <Row title="Delivery" data="**₹ 1,000**" />
      <Row
        title="Total Paid"
        data={orderTotalAmountWithCurrency}
        fontFamily="bold"
      />
    </Layout>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  container: {
    marginHorizontal: 12,
    marginTop: 8,
    marginBottom: 2,
    paddingHorizontal: 4,
  },
});

export default OrderPriceSummary;
