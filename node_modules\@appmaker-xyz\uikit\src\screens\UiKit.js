import { addFilter } from '@appmaker-xyz/core';
import { connect } from 'react-redux';
import { ApThemeProvider } from '../theme/ThemeContext';
import * as React from 'react';
// import { createStackNavigator, createAppContainer } from 'react-navigation';
import { Button, View } from 'react-native';
import { ProdcutScroller } from '../components';
// import HomeScreen from './Homescreen';
// import InAppPage from './inAppPage.js';
import Typography from './Typography';
import ButtonScreen from './ButtonScreen';
import Tabs from './Tabs';
import BottomSheet from './BottomSheet';
import * as styles from '../styles';
import GroceryListing from './GroceryListing';
import NotificationsHistory from './NotificationsHistory';
import InputItemsListing from './InputItemsListing';
import LoginScreen from './LoginScreen';
import Playground from './Playground';
import ProductDetail from './ProductDetail';
import Wordpress from './Wordpress';
import GroceryListingGrid from './GroceryListingGrid';
import TabTest from './TabTest';
import SortFilterEx from './SortFilterDemo';
import WordpressBlocks from './WodpressBlocks';
const HomeScreen = ({ navigation }) => {
  const items = Object.keys(screens).map((item, key) => {
    if (item !== 'UIKIT_Home') {
      return (
        <Button
          // color={styles.color.warning}
          style={{ marginTop: 3 }}
          title={screens[item].title}
          onPress={() => navigation.navigate(item)}
        />
      );
    }
  });
  return <View>{items}</View>;
};
const CartCount = connect((store) => {
  return { cartCount: store.settings.cartCount };
})((props) => {
  return (
    <Button
      // onPress={navigation.getParam('increaseCount')}
      title={`${props.cartCount}`}
      // title={props.cartCount || '231'}
      color="#000"
    />
  );
});
const screens = {
  UIKIT_Home: {
    screen: HomeScreen,
    title: 'Home Screen',
  },
  UIKIT_Typography: {
    screen: Typography,
    title: 'Typography',
  },
  UIKIT_Buttons: {
    screen: ButtonScreen,
    title: 'ButtonScreen',
  },
  Tabs: {
    screen: Tabs,
    title: 'Tabs',
    navigationOptions: {
      // header: null,
      headerRight: <CartCount />,
    },
  },
  BottomSheet: {
    screen: BottomSheet,
    title: 'BottomSheet',
  },
  GroceryListing: {
    screen: GroceryListing,
    title: 'Grocery Listing',
  },
  // InAppPage: {
  //   screen: InAppPage,
  //   title: 'InAppPage',
  // },
  NotificationsHistory: {
    screen: NotificationsHistory,
    title: 'Notifications History',
  },
  InputItemsListing: {
    screen: InputItemsListing,
    title: 'Input Items',
  },
  LoginScreen: {
    screen: LoginScreen,
    title: 'Login Screen',
  },
  Playground: {
    screen: Playground,
    title: 'Play Ground',
  },
  GroceryListingGrid: {
    screen: GroceryListingGrid,
    title: 'GroceryListingGrid',
  },
  SortFilter: {
    screen: SortFilterEx,
    title: 'Sort and Filter',
  },
  TabTest: {
    screen: TabTest,
    title: 'Tab Testing',
  },
  ProductDetail: {
    screen: ProductDetail,
    title: 'ProductDetail',
  },
  WordpressBolcks: {
    screen: WordpressBlocks,
    title: 'WordPress Blocks',
  },
  Wordpress: {
    screen: Wordpress,
    title: 'Wordpress',
  },
};
// const AppNavigator = createStackNavigator(screens, {
//   // initialRouteName: 'Wordpress', //openCart(), true, this.props.navigation
// });
// const NavContianer = createAppContainer(AppNavigator);
const AppContainer = () => (
  <ApThemeProvider styles={styles}>
    {/* <NavContianer /> */}
  </ApThemeProvider>
);
const ProductList = (props) => (
  <ApThemeProvider styles={styles}>
    <GroceryListing {...props} />
  </ApThemeProvider>
);
const ProdcutScrollerContianter = (props) => (
  <ApThemeProvider styles={styles}>
    <ProdcutScroller {...props} />
  </ApThemeProvider>
);
if (process.env.NODE_ENV === 'development') {
  addFilter('ap-stack-initRoute', 'apple-pay-test', (initialRoot) => {
    // console.log(initialRoot);
    return 'UiKitPlayground';
  });
  addFilter('ap-inAppPageBlocks', 'productScroller-replace', (items) => {
    return { ...items, product_scroller: ProdcutScrollerContianter };
  });
  addFilter('ap-stack-views', 'add-apple-pay', (routes) => {
    return {
      ...routes,
      UiKitPlayground: {
        screen: AppContainer,
        navigationOptions: {
          header: null,
        },
      },
      ProductList: {
        screen: ProductList,
        navigationOptions: {
          // header: null,
        },
      },
    };
  });
}
