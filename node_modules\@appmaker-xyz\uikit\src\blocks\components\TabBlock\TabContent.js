import { StyleSheet } from 'react-native';
import React from 'react';
import { color, font } from '@appmaker-xyz/uikit/src/styles/index';

function TabContent({ block, BlocksView, onAction, currentAction }) {
  const { attributes } = block;
  const { pageId, actionParams } = attributes;
  return (
    <BlocksView
      onAction={onAction}
      currentAction={actionParams || currentAction}
      inAppPage={{
        blocks: [
          {
            attributes: {
              pageId,
            },
            name: 'appmaker/in-app-page',
            innerBlocks: [],
            clientId: 'product-list',
            isValid: true,
          },
        ],
      }}
    />
  );
}
export default React.memo(TabContent);

const inStyle = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabStyle: {
    width: 'auto',
  },
  label: {
    margin: 0,
    fontFamily: font.family.bold,
    textAlign: 'center',
  },
  tabbar: {
    backgroundColor: color.white,
  },
  indicator: {
    backgroundColor: color.white,
  },
});
