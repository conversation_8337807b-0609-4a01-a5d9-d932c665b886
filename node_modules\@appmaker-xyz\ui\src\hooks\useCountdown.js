import { useEffect, useState } from 'react';

const useCountdown = (targetDate = new Date()) => {
  const countDownDate = new Date(targetDate).getTime();

  const [countDown, setCountDown] = useState(
    countDownDate - new Date().getTime(),
  );
  const [completed, setCompleted] = useState(false);
  useEffect(() => {
    const interval = setInterval(() => {
      if (countDown >= 0) {
        setCountDown(countDownDate - new Date().getTime());
      }
    }, 1000);
    if (countDown <= 0) {
      clearInterval(interval);
      setCompleted(true);
    }
    return () => clearInterval(interval);
  }, [countDownDate]);

  return getReturnValues(countDown, completed);
};
function getDisplayText({ days, hours, minutes, seconds }) {
  let displayText = '';
  if (days > 0) {
    displayText += `${days}d`;
  }
  // if (hours > 0) {
  displayText += ` ${hours}h`;
  // }
  // if (minutes > 0) {
  displayText += ` ${minutes}m`;
  // }
  // if (seconds > 0) {
  displayText += ` ${seconds}s`;
  // }
  return displayText;
}
const getReturnValues = (countDown, completed) => {
  // calculate time left
  const days = Math.floor(countDown / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (countDown % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
  );
  const minutes = Math.floor((countDown % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((countDown % (1000 * 60)) / 1000);
  const displayText = getDisplayText({ days, hours, minutes, seconds });
  return [days, hours, minutes, seconds, displayText, completed];
};

export { useCountdown };
