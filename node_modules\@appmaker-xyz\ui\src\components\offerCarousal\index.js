import React from 'react';
import { StyleSheet, ScrollView, useWindowDimensions } from 'react-native';
import { ThemeText, Layout } from '../atoms';
import CarousalHead from './CarousalHead';

function OfferCard(props) {
  const { width } = useWindowDimensions();
  return (
    <Layout style={[styles.cardContainer, { width: width * 0.8 }]}>
      <CarousalHead
        backgroundColor={props.headBackgroundColor}
        headColor={props.headColor}
      />
      <ThemeText
        style={styles.offerText}
        size="md"
        fontFamily="medium"
        numberOfLines={3}>
        {props.offer}
      </ThemeText>
    </Layout>
  );
}

const OFFERS = [
  'EXTRA 100 off above Rs 2000. Use code SAVE100.',
  'EXTRA 250 off above Rs 4000. Use code SAVE250',
  'Extra 10% off upto Rs. 500 with ICICI Credit Cards',
];

const OfferCarousal = ({ attributes = {} }) => {
  const {
    title = 'Offers',
    backgroundColor = '#f2ffc9',
    headColor = '#BCD85F',
  } = attributes;
  return (
    <Layout style={{ backgroundColor: backgroundColor }}>
      <ThemeText size="md" fontFamily="medium" style={styles.blockHeaderText}>
        {title}
      </ThemeText>
      <ScrollView
        horizontal={true}
        contentContainerStyle={styles.scrollViewContainer}>
        {OFFERS.map((item) => (
          <OfferCard
            offer={item}
            headBackgroundColor={backgroundColor}
            headColor={headColor}
          />
        ))}
      </ScrollView>
    </Layout>
  );
};

const styles = StyleSheet.create({
  blockHeaderText: {
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  scrollViewContainer: {
    paddingLeft: 12,
    paddingBottom: 18,
  },
  cardContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  offerText: {
    flexShrink: 1,
    marginHorizontal: 8,
  },
});

export default OfferCarousal;
