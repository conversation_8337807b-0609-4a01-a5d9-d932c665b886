import React from 'react';
import { StyleSheet, ActivityIndicator, Dimensions } from 'react-native';
import {
  Layout,
  ThemeText,
  AppTouchable,
  AppImage,
  Badge,
} from '@appmaker-xyz/ui';
import { appmaker } from '@appmaker-xyz/core';
import {
  useProductListItem,
  useProductWishList,
  useProductVariations,
} from '@appmaker-xyz/shopify';
import Icon from 'react-native-vector-icons/AntDesign';

let deviceRatio =
  Dimensions.get('window').height / Dimensions.get('window').width;
const isTab = deviceRatio < 1.6;

const LoadingIndicator = () => <ActivityIndicator size={16} color="#1B1B1B" />;

function SpecialBadge(props) {
  return (
    <Layout style={props.styles.chumbakInsider}>
      <ThemeText size="sm" fontFamily="medium">
        {props.specialPrice}
      </ThemeText>
      <AppImage
        uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
        style={props.styles.insiderImage}
      />
      <ThemeText size="sm" fontFamily="medium">
        members
      </ThemeText>
    </Layout>
  );
}

const ProductGridTwo = ({
  clientId,
  attributes,
  pageDispatch,
  onPress,
  onAction,
  blockData,
  data = {},
  addCartButton,
  blocksViewItemIndex,
  ...props
}) => {
  const ReviewsComp = appmaker.applyFilters(
    'appmaker-product-grid-reviews',
    () => null,
  );
  const {
    priceSale = '0',
    wishList,
    specialPrice,
    numColumns,
    appmakerAction,
    size = '',
    new_product,
    bestSeller,
    brandColor,
    titleNumberOfLines,
    __appmakerCustomStyles = {},
  } = attributes;
  const numberOfLines = parseFloat(titleNumberOfLines);

  const ratingBadgeColor = () => {
    let rating = average_rating;
    switch (true) {
      case rating >= '3.5':
        return '#16A34A';
      case rating >= '2.5':
        return '#F59E0B';
      default:
        return '#4B5563';
    }
  };

  const {
    gridViewListing,
    average_rating,
    imageRatio,
    salePrice,
    regularPrice,
    onSale,
    outOfStock,
    imageUrl: uri,
    title,
    addToCart,
    isAddToCartLoading,
    openProduct,
    salePercentage,
    show_last_few_remaining,
    last_few_remaining_text,
  } = useProductListItem({
    attributes,
    onAction,
    blockData,
    pageDispatch,
    blocksViewItemIndex,
  });
  const { toggleWishList, isSaved } = useProductWishList({
    onAction,
    blockData,
  });
  const { variant } = useProductVariations({
    attributes,
    onAction,
    blockData,
    pageDispatch,
    ...props,
  });
  let imageRatioSettings = appmaker.applyFilters(
    'product-grid-image-ratio',
    imageRatio,
  );

  const aspectRatio = imageRatioSettings || imageRatio;

  const in_stock = !outOfStock;
  const styles = allStyles({ imageRatio, customHeight });
  const containerStyle = [styles.container];
  const imageContainerStyle = [styles.imageContainer];
  const customHeight = gridViewListing ? 186 : 180;

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  if (size === 'sm') {
    imageContainerStyle.push({
      width: 100,
      height: imageRatio ? imageRatio * 80 : 100,
    });
  }

  if (gridViewListing === true) {
    containerStyle.push({ marginLeft: 0 });
    imageContainerStyle.push({ width: '100%' });
  }
  if (__appmakerCustomStyles) {
    containerStyle.push(__appmakerCustomStyles?.container);
    imageContainerStyle.push(__appmakerCustomStyles?.image_container);
  }

  const gridViewListingStyle = {
    width: `${100 / numColumns}%`,
    padding: 4,
  };

  const scrollViewListingStyle = {
    width: isTab ? 300 : 196,
    marginLeft: 12,
  };

  if (aspectRatio) {
    imageContainerStyle.push({
      aspectRatio: 1 / aspectRatio,
    });
  }

  return (
    <AppTouchable
      onPress={openProduct}
      style={gridViewListing ? gridViewListingStyle : scrollViewListingStyle}
      clientId={clientId}>
      <Layout style={[styles.container, __appmakerCustomStyles?.container]}>
        <Layout style={imageContainerStyle}>
          <AppImage
            uri={uri}
            style={[styles.productImage, __appmakerCustomStyles?.product_image]}
          />
          <Layout style={styles.topLeftBlocks}>
            {average_rating && average_rating > 0 && (
              <Badge
                style={styles.saleBadgeStyle}
                text={average_rating}
                color={ratingBadgeColor()}
                iconName="star"
                customStyles={__appmakerCustomStyles?.rating_badge}
              />
            )}
            {ReviewsComp && <ReviewsComp attributes={attributes} />}
            {bestSeller ? (
              <Badge
                style={styles.saleBadgeStyle}
                color="#4F4F4F"
                text={bestSeller.value}
                customStyles={__appmakerCustomStyles?.bestseller_badge}
              />
            ) : null}
          </Layout>
        </Layout>
        <Layout style={styles.contentContainer}>
          <Layout
            style={[
              styles.overlayContainer,
              __appmakerCustomStyles?.overlay_container,
            ]}>
            {wishList == true ? (
              <AppTouchable
                onPress={() => {
                  toggleWishList && toggleWishList();
                }}
                style={[
                  styles.wishListIcon,
                  styles.shadow,
                  __appmakerCustomStyles?.wish_list_icon_container,
                ]}>
                <Icon
                  name={!isSaved ? 'hearto' : 'heart'}
                  color={!isSaved ? '#4F4F4F' : '#DC2626'}
                  size={16}
                  style={__appmakerCustomStyles?.wish_list_icon}
                />
              </AppTouchable>
            ) : null}
            {addCartButton === 'add-cart-button' && in_stock == true ? (
              <AppTouchable
                onPress={() => addToCart({ appmakerAction: false })}
                style={[
                  styles.addToCart,
                  styles.shadow,
                  __appmakerCustomStyles?.add_icon_container,
                ]}>
                {isAddToCartLoading ? (
                  <LoadingIndicator />
                ) : (
                  <Icon
                    name="plus"
                    size={16}
                    color={'#4F4F4F'}
                    style={__appmakerCustomStyles?.add_icon}
                  />
                )}
              </AppTouchable>
            ) : null}
          </Layout>
          <Layout style={styles.productTitle}>
            {new_product ? (
              <ThemeText
                size="10"
                color={brandColor?.secondary}
                style={__appmakerCustomStyles?.new_product_text}>
                {new_product.value}
              </ThemeText>
            ) : null}
            {show_last_few_remaining &&
            (show_last_few_remaining === 'true' ||
              show_last_few_remaining === true) ? (
              <ThemeText
                size="10"
                color={brandColor?.secondary}
                style={__appmakerCustomStyles?.last_few_remaining_text}>
                {last_few_remaining_text}
              </ThemeText>
            ) : null}
            {in_stock == true ? null : (
              <ThemeText
                size="10"
                color={brandColor?.secondary}
                style={__appmakerCustomStyles?.out_of_stock_text}>
                Out of Stock
              </ThemeText>
            )}
            {isNaN(numberOfLines) == false ? (
              <ThemeText
                style={__appmakerCustomStyles?.product_title}
                numberOfLines={titleNumberOfLines}>
                {title}
              </ThemeText>
            ) : (
              <ThemeText style={__appmakerCustomStyles?.product_title}>
                {title}
              </ThemeText>
            )}
          </Layout>
          <Layout
            style={[
              styles.priceDataContainer,
              __appmakerCustomStyles?.price_data_container,
            ]}>
            <ThemeText fontFamily="bold">
              <ThemeText style={__appmakerCustomStyles?.sale_price}>
                {salePrice}
              </ThemeText>
            </ThemeText>
            {regularPrice !== ' ' ? (
              <ThemeText
                size="10"
                color={
                  __appmakerCustomStyles?.offer_price?.color
                    ? __appmakerCustomStyles?.offer_price?.color
                    : brandColor?.primary || '#4F4F4F'
                }
                hideText={!onSale && !priceSale}
                style={styles.offerPrice}>
                {regularPrice}
              </ThemeText>
            ) : null}
            {onSale && (
              <Badge
                style={styles.saleBadgeStyle}
                text={salePercentage}
                customStyles={__appmakerCustomStyles?.sale_badge}
              />
            )}
          </Layout>
          {specialPrice ? (
            <SpecialBadge specialPrice={specialPrice} styles={styles} />
          ) : null}
        </Layout>
      </Layout>
    </AppTouchable>
  );
};

const allStyles = ({ imageRatio }) =>
  StyleSheet.create({
    container: {
      flex: 1,
      marginBottom: 12,
      position: 'relative',
    },
    imageContainer: {
      backgroundColor: '#FFFFFF',
      borderRadius: 8,
      aspectRatio: 1,
      position: 'relative',
    },
    productImage: {
      width: '100%',
      height: '100%',
      borderRadius: 8,
      overflow: 'hidden',
    },
    overlayContainer: {
      position: 'absolute',
      zIndex: 10,
      flexDirection: 'row',
      top: -42,
      right: 12,
    },
    addToCart: {
      backgroundColor: '#FFFFFF',
      padding: 8,
      borderRadius: 18,
      marginLeft: 10,
    },
    wishListIcon: {
      backgroundColor: '#FFFFFF',
      padding: 8,
      borderRadius: 18,
    },
    shadow: {
      shadowColor: '#4F4F4F',
      shadowOffset: {
        width: 0,
        height: 8,
      },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    contentContainer: {
      marginTop: 18,
      flex: 1,
      position: 'relative',
    },
    productTitle: {
      flex: 1,
      marginBottom: 4,
    },
    priceDataContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-start',
      flexWrap: 'wrap',
    },
    offerPrice: {
      textDecorationLine: 'line-through',
      marginHorizontal: 4,
    },
    saleBadgeStyle: {
      marginRight: 4,
    },
    stockOutBadgeStyle: {},
    topLeftBlocks: {
      flexDirection: 'row',
      position: 'absolute',
      flexWrap: 'wrap',
      left: 4,
      top: 4,
    },
    chumbakInsider: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#B6DBC9',
      paddingVertical: 2,
      paddingLeft: 6,
      marginTop: 4,
    },
    insiderImage: {
      width: 60,
      height: 20,
      resizeMode: 'contain',
    },
    buttonStyle: {
      marginTop: 8,
    },
  });

export default ProductGridTwo;
