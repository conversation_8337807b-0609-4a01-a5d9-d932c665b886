import React from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import { Layout, LayoutIcon } from '@appmaker-xyz/uikit';

const SocialLinkBar = ({ attributes, onPress, onAction }) => {
  const { appmakerAction } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  return (
    <Layout style={styles.container}>
      <LayoutIcon attributes={{ iconName: 'instagram' }} />
      <LayoutIcon attributes={{ iconName: 'facebook' }} />
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-around',
      backgroundColor: color.white,
      padding: spacing.mini,
    },
  });

export default SocialLinkBar;
