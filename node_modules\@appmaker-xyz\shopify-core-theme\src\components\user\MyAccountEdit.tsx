import React, { FunctionComponent } from 'react';
import { KeyboardAvoidingView } from 'react-native';
import { Layout, Button } from '@appmaker-xyz/ui';
import { InputFormItem } from './components/InputFormItem';
import { useUserProfileV2 } from '@appmaker-xyz/shopify';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';

const MyAccountEdit: FunctionComponent = (props) => {
  const { isLoading, control, updateProfile, setFocus } =
    useUserProfileV2(props);

  const { styles, theme } = useStyles(stylesheet);

  return (
    <KeyboardAvoidingView keyboardVerticalOffset={80} behavior={'padding'}>
      <Layout style={styles.container}>
        <InputFormItem
          autoFocus={true}
          control={control}
          name="firstName"
          label={'First Name'}
          style={styles.input}
          onSubmitEditing={() => setFocus('lastName')}
          returnKeyType="next"
        />
        <InputFormItem
          control={control}
          name="lastName"
          label={'Last Name'}
          style={styles.input}
          onSubmitEditing={() => setFocus('email')}
          returnKeyType="next"
        />
        <InputFormItem
          control={control}
          name="email"
          label={'Email'}
          style={styles.input}
          onSubmitEditing={updateProfile}
          returnKeyType="done"
        />
        <Layout style={styles.buttons}>
          <Button
            title={'cancel'}
            onPress={() => props.onAction({ action: 'GO_BACK' })}
            isOutline={true}
            color={theme.colors.primaryButtonBackground}
            activityIndicatorColor={theme.colors.primaryButtonBackground}
          />
          <Layout style={styles.gap} />
          <Button
            onPress={updateProfile}
            title={'Update account'}
            isLoading={isLoading}
            color={theme.colors.primaryButtonBackground}
            activityIndicatorColor={theme.colors.primaryButtonText}
          />
        </Layout>
      </Layout>
    </KeyboardAvoidingView>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    padding: 12,
  },
  input: {
    backgroundColor: theme.colors.lightBackground,
    borderRadius: 6,
    overflow: 'hidden',
    position: 'relative',
  },
  buttons: {
    flexDirection: 'row',
    marginTop: 12,
  },
  gap: {
    width: 8,
  },
}));

export default MyAccountEdit;
