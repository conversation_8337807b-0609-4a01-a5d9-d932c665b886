{"version": 3, "names": ["_t", "require", "isFor", "isForStatement", "isIfStatement", "isStatement", "WithStatement", "node", "word", "space", "token", "print", "object", "printBlock", "IfStatement", "test", "needsBlock", "alternate", "getLastStatement", "consequent", "newline", "indent", "printAndIndentOnComments", "dedent", "endsWith", "statement", "body", "ForStatement", "inForStatementInitCounter", "init", "update", "WhileStatement", "ForXStatement", "isForOf", "type", "await", "noIndentInnerCommentsHere", "left", "right", "ForInStatement", "exports", "ForOfStatement", "DoWhileStatement", "semicolon", "printStatementAfterKeyword", "printer", "parent", "isLabel", "printTerminatorless", "BreakStatement", "label", "ContinueStatement", "ReturnStatement", "argument", "ThrowStatement", "LabeledStatement", "TryStatement", "block", "handlers", "handler", "finalizer", "CatchClause", "param", "typeAnnotation", "SwitchStatement", "discriminant", "printSequence", "cases", "addNewlines", "leading", "cas", "length", "SwitchCase", "DebuggerStatement", "VariableDeclaration", "declare", "kind", "hasInits", "declar", "declarations", "printList", "separator", "undefined", "VariableDeclarator", "id", "definite"], "sources": ["../../src/generators/statements.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport {\n  isFor,\n  isForStatement,\n  isIfStatement,\n  isStatement,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport * as charCodes from \"charcodes\";\n\nexport function WithStatement(this: Printer, node: t.WithStatement) {\n  this.word(\"with\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.object, node);\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nexport function IfStatement(this: Printer, node: t.IfStatement) {\n  this.word(\"if\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.test, node);\n  this.token(\")\");\n  this.space();\n\n  const needsBlock =\n    node.alternate && isIfStatement(getLastStatement(node.consequent));\n  if (needsBlock) {\n    this.token(\"{\");\n    this.newline();\n    this.indent();\n  }\n\n  this.printAndIndentOnComments(node.consequent, node);\n\n  if (needsBlock) {\n    this.dedent();\n    this.newline();\n    this.token(\"}\");\n  }\n\n  if (node.alternate) {\n    if (this.endsWith(charCodes.rightCurlyBrace)) this.space();\n    this.word(\"else\");\n    this.space();\n    this.printAndIndentOnComments(node.alternate, node);\n  }\n}\n\n// Recursively get the last statement.\nfunction getLastStatement(statement: t.Statement): t.Statement {\n  // @ts-expect-error: If statement.body is empty or not a Node, isStatement will return false\n  const { body } = statement;\n  if (isStatement(body) === false) {\n    return statement;\n  }\n\n  return getLastStatement(body);\n}\n\nexport function ForStatement(this: Printer, node: t.ForStatement) {\n  this.word(\"for\");\n  this.space();\n  this.token(\"(\");\n\n  this.inForStatementInitCounter++;\n  this.print(node.init, node);\n  this.inForStatementInitCounter--;\n  this.token(\";\");\n\n  if (node.test) {\n    this.space();\n    this.print(node.test, node);\n  }\n  this.token(\";\");\n\n  if (node.update) {\n    this.space();\n    this.print(node.update, node);\n  }\n\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nexport function WhileStatement(this: Printer, node: t.WhileStatement) {\n  this.word(\"while\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.test, node);\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nfunction ForXStatement(this: Printer, node: t.ForXStatement) {\n  this.word(\"for\");\n  this.space();\n  const isForOf = node.type === \"ForOfStatement\";\n  if (isForOf && node.await) {\n    this.word(\"await\");\n    this.space();\n  }\n  this.noIndentInnerCommentsHere();\n  this.token(\"(\");\n  this.print(node.left, node);\n  this.space();\n  this.word(isForOf ? \"of\" : \"in\");\n  this.space();\n  this.print(node.right, node);\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nexport const ForInStatement = ForXStatement;\nexport const ForOfStatement = ForXStatement;\n\nexport function DoWhileStatement(this: Printer, node: t.DoWhileStatement) {\n  this.word(\"do\");\n  this.space();\n  this.print(node.body, node);\n  this.space();\n  this.word(\"while\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.test, node);\n  this.token(\")\");\n  this.semicolon();\n}\n\nfunction printStatementAfterKeyword(\n  printer: Printer,\n  node: t.Node,\n  parent: t.Node,\n  isLabel: boolean,\n) {\n  if (node) {\n    printer.space();\n    printer.printTerminatorless(node, parent, isLabel);\n  }\n\n  printer.semicolon();\n}\n\nexport function BreakStatement(this: Printer, node: t.ContinueStatement) {\n  this.word(\"break\");\n  printStatementAfterKeyword(this, node.label, node, true);\n}\n\nexport function ContinueStatement(this: Printer, node: t.ContinueStatement) {\n  this.word(\"continue\");\n  printStatementAfterKeyword(this, node.label, node, true);\n}\n\nexport function ReturnStatement(this: Printer, node: t.ReturnStatement) {\n  this.word(\"return\");\n  printStatementAfterKeyword(this, node.argument, node, false);\n}\n\nexport function ThrowStatement(this: Printer, node: t.ThrowStatement) {\n  this.word(\"throw\");\n  printStatementAfterKeyword(this, node.argument, node, false);\n}\n\nexport function LabeledStatement(this: Printer, node: t.LabeledStatement) {\n  this.print(node.label, node);\n  this.token(\":\");\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function TryStatement(this: Printer, node: t.TryStatement) {\n  this.word(\"try\");\n  this.space();\n  this.print(node.block, node);\n  this.space();\n\n  // Esprima bug puts the catch clause in a `handlers` array.\n  // see https://code.google.com/p/esprima/issues/detail?id=433\n  // We run into this from regenerator generated ast.\n  // @ts-expect-error todo(flow->ts) should ast node type be updated to support this?\n  if (node.handlers) {\n    // @ts-expect-error todo(flow->ts) should ast node type be updated to support this?\n    this.print(node.handlers[0], node);\n  } else {\n    this.print(node.handler, node);\n  }\n\n  if (node.finalizer) {\n    this.space();\n    this.word(\"finally\");\n    this.space();\n    this.print(node.finalizer, node);\n  }\n}\n\nexport function CatchClause(this: Printer, node: t.CatchClause) {\n  this.word(\"catch\");\n  this.space();\n  if (node.param) {\n    this.token(\"(\");\n    this.print(node.param, node);\n    this.print(node.param.typeAnnotation, node);\n    this.token(\")\");\n    this.space();\n  }\n  this.print(node.body, node);\n}\n\nexport function SwitchStatement(this: Printer, node: t.SwitchStatement) {\n  this.word(\"switch\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.discriminant, node);\n  this.token(\")\");\n  this.space();\n  this.token(\"{\");\n\n  this.printSequence(node.cases, node, {\n    indent: true,\n    addNewlines(leading, cas) {\n      if (!leading && node.cases[node.cases.length - 1] === cas) return -1;\n    },\n  });\n\n  this.token(\"}\");\n}\n\nexport function SwitchCase(this: Printer, node: t.SwitchCase) {\n  if (node.test) {\n    this.word(\"case\");\n    this.space();\n    this.print(node.test, node);\n    this.token(\":\");\n  } else {\n    this.word(\"default\");\n    this.token(\":\");\n  }\n\n  if (node.consequent.length) {\n    this.newline();\n    this.printSequence(node.consequent, node, { indent: true });\n  }\n}\n\nexport function DebuggerStatement(this: Printer) {\n  this.word(\"debugger\");\n  this.semicolon();\n}\n\nexport function VariableDeclaration(\n  this: Printer,\n  node: t.VariableDeclaration,\n  parent: t.Node,\n) {\n  if (node.declare) {\n    // TS\n    this.word(\"declare\");\n    this.space();\n  }\n\n  const { kind } = node;\n  this.word(kind, kind === \"using\");\n  this.space();\n\n  let hasInits = false;\n  // don't add whitespace to loop heads\n  if (!isFor(parent)) {\n    for (const declar of node.declarations) {\n      if (declar.init) {\n        // has an init so let's split it up over multiple lines\n        hasInits = true;\n      }\n    }\n  }\n\n  //\n  // use a pretty separator when we aren't in compact mode, have initializers and don't have retainLines on\n  // this will format declarations like:\n  //\n  //   let foo = \"bar\", bar = \"foo\";\n  //\n  // into\n  //\n  //   let foo = \"bar\",\n  //       bar = \"foo\";\n  //\n\n  this.printList(node.declarations, node, {\n    separator: hasInits\n      ? function (this: Printer) {\n          this.token(\",\");\n          this.newline();\n        }\n      : undefined,\n    indent: node.declarations.length > 1 ? true : false,\n  });\n\n  if (isFor(parent)) {\n    // don't give semicolons to these nodes since they'll be inserted in the parent generator\n    if (isForStatement(parent)) {\n      if (parent.init === node) return;\n    } else {\n      if (parent.left === node) return;\n    }\n  }\n\n  this.semicolon();\n}\n\nexport function VariableDeclarator(this: Printer, node: t.VariableDeclarator) {\n  this.print(node.id, node);\n  if (node.definite) this.token(\"!\"); // TS\n  // @ts-expect-error todo(flow-ts) Property 'typeAnnotation' does not exist on type 'MemberExpression'.\n  this.print(node.id.typeAnnotation, node);\n  if (node.init) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.init, node);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAKsB;EAJpBC,KAAK;EACLC,cAAc;EACdC,aAAa;EACbC;AAAW,IAAAL,EAAA;AAKN,SAASM,aAAaA,CAAgBC,IAAqB,EAAE;EAClE,IAAI,CAACC,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACC,SAAK,IAAK;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACK,MAAM,EAAEL,IAAI,CAAC;EAC7B,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACG,UAAU,CAACN,IAAI,CAAC;AACvB;AAEO,SAASO,WAAWA,CAAgBP,IAAmB,EAAE;EAC9D,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACC,SAAK,IAAK;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACQ,IAAI,EAAER,IAAI,CAAC;EAC3B,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACD,KAAK,EAAE;EAEZ,MAAMO,UAAU,GACdT,IAAI,CAACU,SAAS,IAAIb,aAAa,CAACc,gBAAgB,CAACX,IAAI,CAACY,UAAU,CAAC,CAAC;EACpE,IAAIH,UAAU,EAAE;IACd,IAAI,CAACN,SAAK,KAAK;IACf,IAAI,CAACU,OAAO,EAAE;IACd,IAAI,CAACC,MAAM,EAAE;EACf;EAEA,IAAI,CAACC,wBAAwB,CAACf,IAAI,CAACY,UAAU,EAAEZ,IAAI,CAAC;EAEpD,IAAIS,UAAU,EAAE;IACd,IAAI,CAACO,MAAM,EAAE;IACb,IAAI,CAACH,OAAO,EAAE;IACd,IAAI,CAACV,SAAK,KAAK;EACjB;EAEA,IAAIH,IAAI,CAACU,SAAS,EAAE;IAClB,IAAI,IAAI,CAACO,QAAQ,KAA2B,EAAE,IAAI,CAACf,KAAK,EAAE;IAC1D,IAAI,CAACD,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,EAAE;IACZ,IAAI,CAACa,wBAAwB,CAACf,IAAI,CAACU,SAAS,EAAEV,IAAI,CAAC;EACrD;AACF;AAGA,SAASW,gBAAgBA,CAACO,SAAsB,EAAe;EAE7D,MAAM;IAAEC;EAAK,CAAC,GAAGD,SAAS;EAC1B,IAAIpB,WAAW,CAACqB,IAAI,CAAC,KAAK,KAAK,EAAE;IAC/B,OAAOD,SAAS;EAClB;EAEA,OAAOP,gBAAgB,CAACQ,IAAI,CAAC;AAC/B;AAEO,SAASC,YAAYA,CAAgBpB,IAAoB,EAAE;EAChE,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACC,SAAK,IAAK;EAEf,IAAI,CAACkB,yBAAyB,EAAE;EAChC,IAAI,CAACjB,KAAK,CAACJ,IAAI,CAACsB,IAAI,EAAEtB,IAAI,CAAC;EAC3B,IAAI,CAACqB,yBAAyB,EAAE;EAChC,IAAI,CAAClB,SAAK,IAAK;EAEf,IAAIH,IAAI,CAACQ,IAAI,EAAE;IACb,IAAI,CAACN,KAAK,EAAE;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACQ,IAAI,EAAER,IAAI,CAAC;EAC7B;EACA,IAAI,CAACG,SAAK,IAAK;EAEf,IAAIH,IAAI,CAACuB,MAAM,EAAE;IACf,IAAI,CAACrB,KAAK,EAAE;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACuB,MAAM,EAAEvB,IAAI,CAAC;EAC/B;EAEA,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACG,UAAU,CAACN,IAAI,CAAC;AACvB;AAEO,SAASwB,cAAcA,CAAgBxB,IAAsB,EAAE;EACpE,IAAI,CAACC,IAAI,CAAC,OAAO,CAAC;EAClB,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACC,SAAK,IAAK;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACQ,IAAI,EAAER,IAAI,CAAC;EAC3B,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACG,UAAU,CAACN,IAAI,CAAC;AACvB;AAEA,SAASyB,aAAaA,CAAgBzB,IAAqB,EAAE;EAC3D,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACC,KAAK,EAAE;EACZ,MAAMwB,OAAO,GAAG1B,IAAI,CAAC2B,IAAI,KAAK,gBAAgB;EAC9C,IAAID,OAAO,IAAI1B,IAAI,CAAC4B,KAAK,EAAE;IACzB,IAAI,CAAC3B,IAAI,CAAC,OAAO,CAAC;IAClB,IAAI,CAACC,KAAK,EAAE;EACd;EACA,IAAI,CAAC2B,yBAAyB,EAAE;EAChC,IAAI,CAAC1B,SAAK,IAAK;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC8B,IAAI,EAAE9B,IAAI,CAAC;EAC3B,IAAI,CAACE,KAAK,EAAE;EACZ,IAAI,CAACD,IAAI,CAACyB,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;EAChC,IAAI,CAACxB,KAAK,EAAE;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAAC+B,KAAK,EAAE/B,IAAI,CAAC;EAC5B,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACG,UAAU,CAACN,IAAI,CAAC;AACvB;AAEO,MAAMgC,cAAc,GAAGP,aAAa;AAACQ,OAAA,CAAAD,cAAA,GAAAA,cAAA;AACrC,MAAME,cAAc,GAAGT,aAAa;AAACQ,OAAA,CAAAC,cAAA,GAAAA,cAAA;AAErC,SAASC,gBAAgBA,CAAgBnC,IAAwB,EAAE;EACxE,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACmB,IAAI,EAAEnB,IAAI,CAAC;EAC3B,IAAI,CAACE,KAAK,EAAE;EACZ,IAAI,CAACD,IAAI,CAAC,OAAO,CAAC;EAClB,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACC,SAAK,IAAK;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACQ,IAAI,EAAER,IAAI,CAAC;EAC3B,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACiC,SAAS,EAAE;AAClB;AAEA,SAASC,0BAA0BA,CACjCC,OAAgB,EAChBtC,IAAY,EACZuC,MAAc,EACdC,OAAgB,EAChB;EACA,IAAIxC,IAAI,EAAE;IACRsC,OAAO,CAACpC,KAAK,EAAE;IACfoC,OAAO,CAACG,mBAAmB,CAACzC,IAAI,EAAEuC,MAAM,EAAEC,OAAO,CAAC;EACpD;EAEAF,OAAO,CAACF,SAAS,EAAE;AACrB;AAEO,SAASM,cAAcA,CAAgB1C,IAAyB,EAAE;EACvE,IAAI,CAACC,IAAI,CAAC,OAAO,CAAC;EAClBoC,0BAA0B,CAAC,IAAI,EAAErC,IAAI,CAAC2C,KAAK,EAAE3C,IAAI,EAAE,IAAI,CAAC;AAC1D;AAEO,SAAS4C,iBAAiBA,CAAgB5C,IAAyB,EAAE;EAC1E,IAAI,CAACC,IAAI,CAAC,UAAU,CAAC;EACrBoC,0BAA0B,CAAC,IAAI,EAAErC,IAAI,CAAC2C,KAAK,EAAE3C,IAAI,EAAE,IAAI,CAAC;AAC1D;AAEO,SAAS6C,eAAeA,CAAgB7C,IAAuB,EAAE;EACtE,IAAI,CAACC,IAAI,CAAC,QAAQ,CAAC;EACnBoC,0BAA0B,CAAC,IAAI,EAAErC,IAAI,CAAC8C,QAAQ,EAAE9C,IAAI,EAAE,KAAK,CAAC;AAC9D;AAEO,SAAS+C,cAAcA,CAAgB/C,IAAsB,EAAE;EACpE,IAAI,CAACC,IAAI,CAAC,OAAO,CAAC;EAClBoC,0BAA0B,CAAC,IAAI,EAAErC,IAAI,CAAC8C,QAAQ,EAAE9C,IAAI,EAAE,KAAK,CAAC;AAC9D;AAEO,SAASgD,gBAAgBA,CAAgBhD,IAAwB,EAAE;EACxE,IAAI,CAACI,KAAK,CAACJ,IAAI,CAAC2C,KAAK,EAAE3C,IAAI,CAAC;EAC5B,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACD,KAAK,EAAE;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACmB,IAAI,EAAEnB,IAAI,CAAC;AAC7B;AAEO,SAASiD,YAAYA,CAAgBjD,IAAoB,EAAE;EAChE,IAAI,CAACC,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACkD,KAAK,EAAElD,IAAI,CAAC;EAC5B,IAAI,CAACE,KAAK,EAAE;EAMZ,IAAIF,IAAI,CAACmD,QAAQ,EAAE;IAEjB,IAAI,CAAC/C,KAAK,CAACJ,IAAI,CAACmD,QAAQ,CAAC,CAAC,CAAC,EAAEnD,IAAI,CAAC;EACpC,CAAC,MAAM;IACL,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACoD,OAAO,EAAEpD,IAAI,CAAC;EAChC;EAEA,IAAIA,IAAI,CAACqD,SAAS,EAAE;IAClB,IAAI,CAACnD,KAAK,EAAE;IACZ,IAAI,CAACD,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACC,KAAK,EAAE;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACqD,SAAS,EAAErD,IAAI,CAAC;EAClC;AACF;AAEO,SAASsD,WAAWA,CAAgBtD,IAAmB,EAAE;EAC9D,IAAI,CAACC,IAAI,CAAC,OAAO,CAAC;EAClB,IAAI,CAACC,KAAK,EAAE;EACZ,IAAIF,IAAI,CAACuD,KAAK,EAAE;IACd,IAAI,CAACpD,SAAK,IAAK;IACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACuD,KAAK,EAAEvD,IAAI,CAAC;IAC5B,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACuD,KAAK,CAACC,cAAc,EAAExD,IAAI,CAAC;IAC3C,IAAI,CAACG,SAAK,IAAK;IACf,IAAI,CAACD,KAAK,EAAE;EACd;EACA,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACmB,IAAI,EAAEnB,IAAI,CAAC;AAC7B;AAEO,SAASyD,eAAeA,CAAgBzD,IAAuB,EAAE;EACtE,IAAI,CAACC,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACC,SAAK,IAAK;EACf,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC0D,YAAY,EAAE1D,IAAI,CAAC;EACnC,IAAI,CAACG,SAAK,IAAK;EACf,IAAI,CAACD,KAAK,EAAE;EACZ,IAAI,CAACC,SAAK,KAAK;EAEf,IAAI,CAACwD,aAAa,CAAC3D,IAAI,CAAC4D,KAAK,EAAE5D,IAAI,EAAE;IACnCc,MAAM,EAAE,IAAI;IACZ+C,WAAWA,CAACC,OAAO,EAAEC,GAAG,EAAE;MACxB,IAAI,CAACD,OAAO,IAAI9D,IAAI,CAAC4D,KAAK,CAAC5D,IAAI,CAAC4D,KAAK,CAACI,MAAM,GAAG,CAAC,CAAC,KAAKD,GAAG,EAAE,OAAO,CAAC,CAAC;IACtE;EACF,CAAC,CAAC;EAEF,IAAI,CAAC5D,SAAK,KAAK;AACjB;AAEO,SAAS8D,UAAUA,CAAgBjE,IAAkB,EAAE;EAC5D,IAAIA,IAAI,CAACQ,IAAI,EAAE;IACb,IAAI,CAACP,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,EAAE;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACQ,IAAI,EAAER,IAAI,CAAC;IAC3B,IAAI,CAACG,SAAK,IAAK;EACjB,CAAC,MAAM;IACL,IAAI,CAACF,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACE,SAAK,IAAK;EACjB;EAEA,IAAIH,IAAI,CAACY,UAAU,CAACoD,MAAM,EAAE;IAC1B,IAAI,CAACnD,OAAO,EAAE;IACd,IAAI,CAAC8C,aAAa,CAAC3D,IAAI,CAACY,UAAU,EAAEZ,IAAI,EAAE;MAAEc,MAAM,EAAE;IAAK,CAAC,CAAC;EAC7D;AACF;AAEO,SAASoD,iBAAiBA,CAAA,EAAgB;EAC/C,IAAI,CAACjE,IAAI,CAAC,UAAU,CAAC;EACrB,IAAI,CAACmC,SAAS,EAAE;AAClB;AAEO,SAAS+B,mBAAmBA,CAEjCnE,IAA2B,EAC3BuC,MAAc,EACd;EACA,IAAIvC,IAAI,CAACoE,OAAO,EAAE;IAEhB,IAAI,CAACnE,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACC,KAAK,EAAE;EACd;EAEA,MAAM;IAAEmE;EAAK,CAAC,GAAGrE,IAAI;EACrB,IAAI,CAACC,IAAI,CAACoE,IAAI,EAAEA,IAAI,KAAK,OAAO,CAAC;EACjC,IAAI,CAACnE,KAAK,EAAE;EAEZ,IAAIoE,QAAQ,GAAG,KAAK;EAEpB,IAAI,CAAC3E,KAAK,CAAC4C,MAAM,CAAC,EAAE;IAClB,KAAK,MAAMgC,MAAM,IAAIvE,IAAI,CAACwE,YAAY,EAAE;MACtC,IAAID,MAAM,CAACjD,IAAI,EAAE;QAEfgD,QAAQ,GAAG,IAAI;MACjB;IACF;EACF;EAcA,IAAI,CAACG,SAAS,CAACzE,IAAI,CAACwE,YAAY,EAAExE,IAAI,EAAE;IACtC0E,SAAS,EAAEJ,QAAQ,GACf,YAAyB;MACvB,IAAI,CAACnE,SAAK,IAAK;MACf,IAAI,CAACU,OAAO,EAAE;IAChB,CAAC,GACD8D,SAAS;IACb7D,MAAM,EAAEd,IAAI,CAACwE,YAAY,CAACR,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG;EAChD,CAAC,CAAC;EAEF,IAAIrE,KAAK,CAAC4C,MAAM,CAAC,EAAE;IAEjB,IAAI3C,cAAc,CAAC2C,MAAM,CAAC,EAAE;MAC1B,IAAIA,MAAM,CAACjB,IAAI,KAAKtB,IAAI,EAAE;IAC5B,CAAC,MAAM;MACL,IAAIuC,MAAM,CAACT,IAAI,KAAK9B,IAAI,EAAE;IAC5B;EACF;EAEA,IAAI,CAACoC,SAAS,EAAE;AAClB;AAEO,SAASwC,kBAAkBA,CAAgB5E,IAA0B,EAAE;EAC5E,IAAI,CAACI,KAAK,CAACJ,IAAI,CAAC6E,EAAE,EAAE7E,IAAI,CAAC;EACzB,IAAIA,IAAI,CAAC8E,QAAQ,EAAE,IAAI,CAAC3E,SAAK,IAAK;EAElC,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC6E,EAAE,CAACrB,cAAc,EAAExD,IAAI,CAAC;EACxC,IAAIA,IAAI,CAACsB,IAAI,EAAE;IACb,IAAI,CAACpB,KAAK,EAAE;IACZ,IAAI,CAACC,SAAK,IAAK;IACf,IAAI,CAACD,KAAK,EAAE;IACZ,IAAI,CAACE,KAAK,CAACJ,IAAI,CAACsB,IAAI,EAAEtB,IAAI,CAAC;EAC7B;AACF"}