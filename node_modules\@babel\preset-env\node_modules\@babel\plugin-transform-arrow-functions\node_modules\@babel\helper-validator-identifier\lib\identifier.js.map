{"version": 3, "names": ["nonASCIIidentifierStartChars", "nonASCIIidentifierChars", "nonASCIIidentifierStart", "RegExp", "nonASCIIidentifier", "astralIdentifierStartCodes", "astralIdentifierCodes", "isInAstralSet", "code", "set", "pos", "i", "length", "isIdentifierStart", "test", "String", "fromCharCode", "isIdentifierChar", "isIdentifierName", "name", "<PERSON><PERSON><PERSON><PERSON>", "cp", "charCodeAt", "trail"], "sources": ["../src/identifier.ts"], "sourcesContent": ["import * as charCodes from \"charcodes\";\n\n// ## Character categories\n\n// Big ugly regular expressions that match characters in the\n// whitespace, identifier, and identifier-start categories. These\n// are only applied when a character is found to actually have a\n// code point between 0x80 and 0xffff.\n// Generated by `scripts/generate-identifier-regex.js`.\n\n/* prettier-ignore */\nlet nonASCIIidentifierStartChars = \"\\xaa\\xb5\\xba\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\u02c1\\u02c6-\\u02d1\\u02e0-\\u02e4\\u02ec\\u02ee\\u0370-\\u0374\\u0376\\u0377\\u037a-\\u037d\\u037f\\u0386\\u0388-\\u038a\\u038c\\u038e-\\u03a1\\u03a3-\\u03f5\\u03f7-\\u0481\\u048a-\\u052f\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05d0-\\u05ea\\u05ef-\\u05f2\\u0620-\\u064a\\u066e\\u066f\\u0671-\\u06d3\\u06d5\\u06e5\\u06e6\\u06ee\\u06ef\\u06fa-\\u06fc\\u06ff\\u0710\\u0712-\\u072f\\u074d-\\u07a5\\u07b1\\u07ca-\\u07ea\\u07f4\\u07f5\\u07fa\\u0800-\\u0815\\u081a\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086a\\u0870-\\u0887\\u0889-\\u088e\\u08a0-\\u08c9\\u0904-\\u0939\\u093d\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098c\\u098f\\u0990\\u0993-\\u09a8\\u09aa-\\u09b0\\u09b2\\u09b6-\\u09b9\\u09bd\\u09ce\\u09dc\\u09dd\\u09df-\\u09e1\\u09f0\\u09f1\\u09fc\\u0a05-\\u0a0a\\u0a0f\\u0a10\\u0a13-\\u0a28\\u0a2a-\\u0a30\\u0a32\\u0a33\\u0a35\\u0a36\\u0a38\\u0a39\\u0a59-\\u0a5c\\u0a5e\\u0a72-\\u0a74\\u0a85-\\u0a8d\\u0a8f-\\u0a91\\u0a93-\\u0aa8\\u0aaa-\\u0ab0\\u0ab2\\u0ab3\\u0ab5-\\u0ab9\\u0abd\\u0ad0\\u0ae0\\u0ae1\\u0af9\\u0b05-\\u0b0c\\u0b0f\\u0b10\\u0b13-\\u0b28\\u0b2a-\\u0b30\\u0b32\\u0b33\\u0b35-\\u0b39\\u0b3d\\u0b5c\\u0b5d\\u0b5f-\\u0b61\\u0b71\\u0b83\\u0b85-\\u0b8a\\u0b8e-\\u0b90\\u0b92-\\u0b95\\u0b99\\u0b9a\\u0b9c\\u0b9e\\u0b9f\\u0ba3\\u0ba4\\u0ba8-\\u0baa\\u0bae-\\u0bb9\\u0bd0\\u0c05-\\u0c0c\\u0c0e-\\u0c10\\u0c12-\\u0c28\\u0c2a-\\u0c39\\u0c3d\\u0c58-\\u0c5a\\u0c5d\\u0c60\\u0c61\\u0c80\\u0c85-\\u0c8c\\u0c8e-\\u0c90\\u0c92-\\u0ca8\\u0caa-\\u0cb3\\u0cb5-\\u0cb9\\u0cbd\\u0cdd\\u0cde\\u0ce0\\u0ce1\\u0cf1\\u0cf2\\u0d04-\\u0d0c\\u0d0e-\\u0d10\\u0d12-\\u0d3a\\u0d3d\\u0d4e\\u0d54-\\u0d56\\u0d5f-\\u0d61\\u0d7a-\\u0d7f\\u0d85-\\u0d96\\u0d9a-\\u0db1\\u0db3-\\u0dbb\\u0dbd\\u0dc0-\\u0dc6\\u0e01-\\u0e30\\u0e32\\u0e33\\u0e40-\\u0e46\\u0e81\\u0e82\\u0e84\\u0e86-\\u0e8a\\u0e8c-\\u0ea3\\u0ea5\\u0ea7-\\u0eb0\\u0eb2\\u0eb3\\u0ebd\\u0ec0-\\u0ec4\\u0ec6\\u0edc-\\u0edf\\u0f00\\u0f40-\\u0f47\\u0f49-\\u0f6c\\u0f88-\\u0f8c\\u1000-\\u102a\\u103f\\u1050-\\u1055\\u105a-\\u105d\\u1061\\u1065\\u1066\\u106e-\\u1070\\u1075-\\u1081\\u108e\\u10a0-\\u10c5\\u10c7\\u10cd\\u10d0-\\u10fa\\u10fc-\\u1248\\u124a-\\u124d\\u1250-\\u1256\\u1258\\u125a-\\u125d\\u1260-\\u1288\\u128a-\\u128d\\u1290-\\u12b0\\u12b2-\\u12b5\\u12b8-\\u12be\\u12c0\\u12c2-\\u12c5\\u12c8-\\u12d6\\u12d8-\\u1310\\u1312-\\u1315\\u1318-\\u135a\\u1380-\\u138f\\u13a0-\\u13f5\\u13f8-\\u13fd\\u1401-\\u166c\\u166f-\\u167f\\u1681-\\u169a\\u16a0-\\u16ea\\u16ee-\\u16f8\\u1700-\\u1711\\u171f-\\u1731\\u1740-\\u1751\\u1760-\\u176c\\u176e-\\u1770\\u1780-\\u17b3\\u17d7\\u17dc\\u1820-\\u1878\\u1880-\\u18a8\\u18aa\\u18b0-\\u18f5\\u1900-\\u191e\\u1950-\\u196d\\u1970-\\u1974\\u1980-\\u19ab\\u19b0-\\u19c9\\u1a00-\\u1a16\\u1a20-\\u1a54\\u1aa7\\u1b05-\\u1b33\\u1b45-\\u1b4c\\u1b83-\\u1ba0\\u1bae\\u1baf\\u1bba-\\u1be5\\u1c00-\\u1c23\\u1c4d-\\u1c4f\\u1c5a-\\u1c7d\\u1c80-\\u1c88\\u1c90-\\u1cba\\u1cbd-\\u1cbf\\u1ce9-\\u1cec\\u1cee-\\u1cf3\\u1cf5\\u1cf6\\u1cfa\\u1d00-\\u1dbf\\u1e00-\\u1f15\\u1f18-\\u1f1d\\u1f20-\\u1f45\\u1f48-\\u1f4d\\u1f50-\\u1f57\\u1f59\\u1f5b\\u1f5d\\u1f5f-\\u1f7d\\u1f80-\\u1fb4\\u1fb6-\\u1fbc\\u1fbe\\u1fc2-\\u1fc4\\u1fc6-\\u1fcc\\u1fd0-\\u1fd3\\u1fd6-\\u1fdb\\u1fe0-\\u1fec\\u1ff2-\\u1ff4\\u1ff6-\\u1ffc\\u2071\\u207f\\u2090-\\u209c\\u2102\\u2107\\u210a-\\u2113\\u2115\\u2118-\\u211d\\u2124\\u2126\\u2128\\u212a-\\u2139\\u213c-\\u213f\\u2145-\\u2149\\u214e\\u2160-\\u2188\\u2c00-\\u2ce4\\u2ceb-\\u2cee\\u2cf2\\u2cf3\\u2d00-\\u2d25\\u2d27\\u2d2d\\u2d30-\\u2d67\\u2d6f\\u2d80-\\u2d96\\u2da0-\\u2da6\\u2da8-\\u2dae\\u2db0-\\u2db6\\u2db8-\\u2dbe\\u2dc0-\\u2dc6\\u2dc8-\\u2dce\\u2dd0-\\u2dd6\\u2dd8-\\u2dde\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303c\\u3041-\\u3096\\u309b-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312f\\u3131-\\u318e\\u31a0-\\u31bf\\u31f0-\\u31ff\\u3400-\\u4dbf\\u4e00-\\ua48c\\ua4d0-\\ua4fd\\ua500-\\ua60c\\ua610-\\ua61f\\ua62a\\ua62b\\ua640-\\ua66e\\ua67f-\\ua69d\\ua6a0-\\ua6ef\\ua717-\\ua71f\\ua722-\\ua788\\ua78b-\\ua7ca\\ua7d0\\ua7d1\\ua7d3\\ua7d5-\\ua7d9\\ua7f2-\\ua801\\ua803-\\ua805\\ua807-\\ua80a\\ua80c-\\ua822\\ua840-\\ua873\\ua882-\\ua8b3\\ua8f2-\\ua8f7\\ua8fb\\ua8fd\\ua8fe\\ua90a-\\ua925\\ua930-\\ua946\\ua960-\\ua97c\\ua984-\\ua9b2\\ua9cf\\ua9e0-\\ua9e4\\ua9e6-\\ua9ef\\ua9fa-\\ua9fe\\uaa00-\\uaa28\\uaa40-\\uaa42\\uaa44-\\uaa4b\\uaa60-\\uaa76\\uaa7a\\uaa7e-\\uaaaf\\uaab1\\uaab5\\uaab6\\uaab9-\\uaabd\\uaac0\\uaac2\\uaadb-\\uaadd\\uaae0-\\uaaea\\uaaf2-\\uaaf4\\uab01-\\uab06\\uab09-\\uab0e\\uab11-\\uab16\\uab20-\\uab26\\uab28-\\uab2e\\uab30-\\uab5a\\uab5c-\\uab69\\uab70-\\uabe2\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufb00-\\ufb06\\ufb13-\\ufb17\\ufb1d\\ufb1f-\\ufb28\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40\\ufb41\\ufb43\\ufb44\\ufb46-\\ufbb1\\ufbd3-\\ufd3d\\ufd50-\\ufd8f\\ufd92-\\ufdc7\\ufdf0-\\ufdfb\\ufe70-\\ufe74\\ufe76-\\ufefc\\uff21-\\uff3a\\uff41-\\uff5a\\uff66-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc\";\n/* prettier-ignore */\nlet nonASCIIidentifierChars = \"\\u200c\\u200d\\xb7\\u0300-\\u036f\\u0387\\u0483-\\u0487\\u0591-\\u05bd\\u05bf\\u05c1\\u05c2\\u05c4\\u05c5\\u05c7\\u0610-\\u061a\\u064b-\\u0669\\u0670\\u06d6-\\u06dc\\u06df-\\u06e4\\u06e7\\u06e8\\u06ea-\\u06ed\\u06f0-\\u06f9\\u0711\\u0730-\\u074a\\u07a6-\\u07b0\\u07c0-\\u07c9\\u07eb-\\u07f3\\u07fd\\u0816-\\u0819\\u081b-\\u0823\\u0825-\\u0827\\u0829-\\u082d\\u0859-\\u085b\\u0898-\\u089f\\u08ca-\\u08e1\\u08e3-\\u0903\\u093a-\\u093c\\u093e-\\u094f\\u0951-\\u0957\\u0962\\u0963\\u0966-\\u096f\\u0981-\\u0983\\u09bc\\u09be-\\u09c4\\u09c7\\u09c8\\u09cb-\\u09cd\\u09d7\\u09e2\\u09e3\\u09e6-\\u09ef\\u09fe\\u0a01-\\u0a03\\u0a3c\\u0a3e-\\u0a42\\u0a47\\u0a48\\u0a4b-\\u0a4d\\u0a51\\u0a66-\\u0a71\\u0a75\\u0a81-\\u0a83\\u0abc\\u0abe-\\u0ac5\\u0ac7-\\u0ac9\\u0acb-\\u0acd\\u0ae2\\u0ae3\\u0ae6-\\u0aef\\u0afa-\\u0aff\\u0b01-\\u0b03\\u0b3c\\u0b3e-\\u0b44\\u0b47\\u0b48\\u0b4b-\\u0b4d\\u0b55-\\u0b57\\u0b62\\u0b63\\u0b66-\\u0b6f\\u0b82\\u0bbe-\\u0bc2\\u0bc6-\\u0bc8\\u0bca-\\u0bcd\\u0bd7\\u0be6-\\u0bef\\u0c00-\\u0c04\\u0c3c\\u0c3e-\\u0c44\\u0c46-\\u0c48\\u0c4a-\\u0c4d\\u0c55\\u0c56\\u0c62\\u0c63\\u0c66-\\u0c6f\\u0c81-\\u0c83\\u0cbc\\u0cbe-\\u0cc4\\u0cc6-\\u0cc8\\u0cca-\\u0ccd\\u0cd5\\u0cd6\\u0ce2\\u0ce3\\u0ce6-\\u0cef\\u0cf3\\u0d00-\\u0d03\\u0d3b\\u0d3c\\u0d3e-\\u0d44\\u0d46-\\u0d48\\u0d4a-\\u0d4d\\u0d57\\u0d62\\u0d63\\u0d66-\\u0d6f\\u0d81-\\u0d83\\u0dca\\u0dcf-\\u0dd4\\u0dd6\\u0dd8-\\u0ddf\\u0de6-\\u0def\\u0df2\\u0df3\\u0e31\\u0e34-\\u0e3a\\u0e47-\\u0e4e\\u0e50-\\u0e59\\u0eb1\\u0eb4-\\u0ebc\\u0ec8-\\u0ece\\u0ed0-\\u0ed9\\u0f18\\u0f19\\u0f20-\\u0f29\\u0f35\\u0f37\\u0f39\\u0f3e\\u0f3f\\u0f71-\\u0f84\\u0f86\\u0f87\\u0f8d-\\u0f97\\u0f99-\\u0fbc\\u0fc6\\u102b-\\u103e\\u1040-\\u1049\\u1056-\\u1059\\u105e-\\u1060\\u1062-\\u1064\\u1067-\\u106d\\u1071-\\u1074\\u1082-\\u108d\\u108f-\\u109d\\u135d-\\u135f\\u1369-\\u1371\\u1712-\\u1715\\u1732-\\u1734\\u1752\\u1753\\u1772\\u1773\\u17b4-\\u17d3\\u17dd\\u17e0-\\u17e9\\u180b-\\u180d\\u180f-\\u1819\\u18a9\\u1920-\\u192b\\u1930-\\u193b\\u1946-\\u194f\\u19d0-\\u19da\\u1a17-\\u1a1b\\u1a55-\\u1a5e\\u1a60-\\u1a7c\\u1a7f-\\u1a89\\u1a90-\\u1a99\\u1ab0-\\u1abd\\u1abf-\\u1ace\\u1b00-\\u1b04\\u1b34-\\u1b44\\u1b50-\\u1b59\\u1b6b-\\u1b73\\u1b80-\\u1b82\\u1ba1-\\u1bad\\u1bb0-\\u1bb9\\u1be6-\\u1bf3\\u1c24-\\u1c37\\u1c40-\\u1c49\\u1c50-\\u1c59\\u1cd0-\\u1cd2\\u1cd4-\\u1ce8\\u1ced\\u1cf4\\u1cf7-\\u1cf9\\u1dc0-\\u1dff\\u203f\\u2040\\u2054\\u20d0-\\u20dc\\u20e1\\u20e5-\\u20f0\\u2cef-\\u2cf1\\u2d7f\\u2de0-\\u2dff\\u302a-\\u302f\\u3099\\u309a\\ua620-\\ua629\\ua66f\\ua674-\\ua67d\\ua69e\\ua69f\\ua6f0\\ua6f1\\ua802\\ua806\\ua80b\\ua823-\\ua827\\ua82c\\ua880\\ua881\\ua8b4-\\ua8c5\\ua8d0-\\ua8d9\\ua8e0-\\ua8f1\\ua8ff-\\ua909\\ua926-\\ua92d\\ua947-\\ua953\\ua980-\\ua983\\ua9b3-\\ua9c0\\ua9d0-\\ua9d9\\ua9e5\\ua9f0-\\ua9f9\\uaa29-\\uaa36\\uaa43\\uaa4c\\uaa4d\\uaa50-\\uaa59\\uaa7b-\\uaa7d\\uaab0\\uaab2-\\uaab4\\uaab7\\uaab8\\uaabe\\uaabf\\uaac1\\uaaeb-\\uaaef\\uaaf5\\uaaf6\\uabe3-\\uabea\\uabec\\uabed\\uabf0-\\uabf9\\ufb1e\\ufe00-\\ufe0f\\ufe20-\\ufe2f\\ufe33\\ufe34\\ufe4d-\\ufe4f\\uff10-\\uff19\\uff3f\";\n\nconst nonASCIIidentifierStart = new RegExp(\n  \"[\" + nonASCIIidentifierStartChars + \"]\",\n);\nconst nonASCIIidentifier = new RegExp(\n  \"[\" + nonASCIIidentifierStartChars + nonASCIIidentifierChars + \"]\",\n);\n\nnonASCIIidentifierStartChars = nonASCIIidentifierChars = null;\n\n// These are a run-length and offset-encoded representation of the\n// >0xffff code points that are a valid part of identifiers. The\n// offset starts at 0x10000, and each pair of numbers represents an\n// offset to the next range, and then a size of the range. They were\n// generated by `scripts/generate-identifier-regex.js`.\n/* prettier-ignore */\nconst astralIdentifierStartCodes = [0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,68,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,4026,582,8634,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,757,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,3104,541,1507,4938,6,4191];\n/* prettier-ignore */\nconst astralIdentifierCodes = [509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,81,2,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,9,5351,0,7,14,13835,9,87,9,39,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,4706,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,983,6,110,6,6,9,4759,9,787719,239];\n\n// This has a complexity linear to the value of the code. The\n// assumption is that looking up astral identifier characters is\n// rare.\nfunction isInAstralSet(code: number, set: readonly number[]): boolean {\n  let pos = 0x10000;\n  for (let i = 0, length = set.length; i < length; i += 2) {\n    pos += set[i];\n    if (pos > code) return false;\n\n    pos += set[i + 1];\n    if (pos >= code) return true;\n  }\n  return false;\n}\n\n// Test whether a given character code starts an identifier.\n\nexport function isIdentifierStart(code: number): boolean {\n  if (code < charCodes.uppercaseA) return code === charCodes.dollarSign;\n  if (code <= charCodes.uppercaseZ) return true;\n  if (code < charCodes.lowercaseA) return code === charCodes.underscore;\n  if (code <= charCodes.lowercaseZ) return true;\n  if (code <= 0xffff) {\n    return (\n      code >= 0xaa && nonASCIIidentifierStart.test(String.fromCharCode(code))\n    );\n  }\n  return isInAstralSet(code, astralIdentifierStartCodes);\n}\n\n// Test whether a given character is part of an identifier.\n\nexport function isIdentifierChar(code: number): boolean {\n  if (code < charCodes.digit0) return code === charCodes.dollarSign;\n  if (code < charCodes.colon) return true;\n  if (code < charCodes.uppercaseA) return false;\n  if (code <= charCodes.uppercaseZ) return true;\n  if (code < charCodes.lowercaseA) return code === charCodes.underscore;\n  if (code <= charCodes.lowercaseZ) return true;\n  if (code <= 0xffff) {\n    return code >= 0xaa && nonASCIIidentifier.test(String.fromCharCode(code));\n  }\n  return (\n    isInAstralSet(code, astralIdentifierStartCodes) ||\n    isInAstralSet(code, astralIdentifierCodes)\n  );\n}\n\n// Test whether a given string is a valid identifier name\n\nexport function isIdentifierName(name: string): boolean {\n  let isFirst = true;\n  for (let i = 0; i < name.length; i++) {\n    // The implementation is based on\n    // https://source.chromium.org/chromium/chromium/src/+/master:v8/src/builtins/builtins-string-gen.cc;l=1455;drc=221e331b49dfefadbc6fa40b0c68e6f97606d0b3;bpv=0;bpt=1\n    // We reimplement `codePointAt` because `codePointAt` is a V8 builtin which is not inlined by TurboFan (as of M91)\n    // since `name` is mostly ASCII, an inlined `charCodeAt` wins here\n    let cp = name.charCodeAt(i);\n    if ((cp & 0xfc00) === 0xd800 && i + 1 < name.length) {\n      const trail = name.charCodeAt(++i);\n      if ((trail & 0xfc00) === 0xdc00) {\n        cp = 0x10000 + ((cp & 0x3ff) << 10) + (trail & 0x3ff);\n      }\n    }\n    if (isFirst) {\n      isFirst = false;\n      if (!isIdentifierStart(cp)) {\n        return false;\n      }\n    } else if (!isIdentifierChar(cp)) {\n      return false;\n    }\n  }\n  return !isFirst;\n}\n"], "mappings": ";;;;;;;;AAWA,IAAIA,4BAA4B,GAAG,8qIAAnC;AAEA,IAAIC,uBAAuB,GAAG,mkFAA9B;AAEA,MAAMC,uBAAuB,GAAG,IAAIC,MAAJ,CAC9B,MAAMH,4BAAN,GAAqC,GADP,CAAhC;AAGA,MAAMI,kBAAkB,GAAG,IAAID,MAAJ,CACzB,MAAMH,4BAAN,GAAqCC,uBAArC,GAA+D,GADtC,CAA3B;AAIAD,4BAA4B,GAAGC,uBAAuB,GAAG,IAAzD;AAQA,MAAMI,0BAA0B,GAAG,CAAC,CAAD,EAAG,EAAH,EAAM,CAAN,EAAQ,EAAR,EAAW,CAAX,EAAa,EAAb,EAAgB,CAAhB,EAAkB,CAAlB,EAAoB,CAApB,EAAsB,EAAtB,EAAyB,CAAzB,EAA2B,EAA3B,EAA8B,EAA9B,EAAiC,GAAjC,EAAqC,EAArC,EAAwC,EAAxC,EAA2C,GAA3C,EAA+C,EAA/C,EAAkD,CAAlD,EAAoD,EAApD,EAAuD,EAAvD,EAA0D,EAA1D,EAA6D,EAA7D,EAAgE,EAAhE,EAAmE,CAAnE,EAAqE,EAArE,EAAwE,EAAxE,EAA2E,EAA3E,EAA8E,CAA9E,EAAgF,EAAhF,EAAmF,CAAnF,EAAqF,CAArF,EAAuF,CAAvF,EAAyF,CAAzF,EAA2F,EAA3F,EAA8F,GAA9F,EAAkG,EAAlG,EAAqG,EAArG,EAAwG,CAAxG,EAA0G,EAA1G,EAA6G,CAA7G,EAA+G,EAA/G,EAAkH,CAAlH,EAAoH,EAApH,EAAuH,EAAvH,EAA0H,EAA1H,EAA6H,CAA7H,EAA+H,EAA/H,EAAkI,CAAlI,EAAoI,CAApI,EAAsI,CAAtI,EAAwI,CAAxI,EAA0I,CAA1I,EAA4I,EAA5I,EAA+I,CAA/I,EAAiJ,EAAjJ,EAAoJ,CAApJ,EAAsJ,CAAtJ,EAAwJ,CAAxJ,EAA0J,CAA1J,EAA4J,EAA5J,EAA+J,GAA/J,EAAmK,EAAnK,EAAsK,EAAtK,EAAyK,EAAzK,EAA4K,CAA5K,EAA8K,EAA9K,EAAiL,CAAjL,EAAmL,CAAnL,EAAqL,EAArL,EAAwL,CAAxL,EAA0L,CAA1L,EAA4L,EAA5L,EAA+L,CAA/L,EAAiM,CAAjM,EAAmM,CAAnM,EAAqM,CAArM,EAAuM,EAAvM,EAA0M,CAA1M,EAA4M,CAA5M,EAA8M,CAA9M,EAAgN,CAAhN,EAAkN,CAAlN,EAAoN,EAApN,EAAuN,EAAvN,EAA0N,EAA1N,EAA6N,EAA7N,EAAgO,EAAhO,EAAmO,EAAnO,EAAsO,EAAtO,EAAyO,CAAzO,EAA2O,CAA3O,EAA6O,EAA7O,EAAgP,EAAhP,EAAmP,EAAnP,EAAsP,EAAtP,EAAyP,EAAzP,EAA4P,EAA5P,EAA+P,CAA/P,EAAiQ,CAAjQ,EAAmQ,EAAnQ,EAAsQ,CAAtQ,EAAwQ,EAAxQ,EAA2Q,CAA3Q,EAA6Q,CAA7Q,EAA+Q,CAA/Q,EAAiR,CAAjR,EAAmR,EAAnR,EAAsR,EAAtR,EAAyR,EAAzR,EAA4R,CAA5R,EAA8R,EAA9R,EAAiS,EAAjS,EAAoS,CAApS,EAAsS,CAAtS,EAAwS,EAAxS,EAA2S,EAA3S,EAA8S,EAA9S,EAAiT,EAAjT,EAAoT,EAApT,EAAuT,EAAvT,EAA0T,EAA1T,EAA6T,EAA7T,EAAgU,EAAhU,EAAmU,GAAnU,EAAuU,EAAvU,EAA0U,EAA1U,EAA6U,EAA7U,EAAgV,EAAhV,EAAmV,EAAnV,EAAsV,EAAtV,EAAyV,EAAzV,EAA4V,GAA5V,EAAgW,EAAhW,EAAmW,CAAnW,EAAqW,CAArW,EAAuW,EAAvW,EAA0W,EAA1W,EAA6W,EAA7W,EAAgX,CAAhX,EAAkX,CAAlX,EAAoX,EAApX,EAAuX,EAAvX,EAA0X,EAA1X,EAA6X,EAA7X,EAAgY,EAAhY,EAAmY,EAAnY,EAAsY,EAAtY,EAAyY,EAAzY,EAA4Y,EAA5Y,EAA+Y,EAA/Y,EAAkZ,CAAlZ,EAAoZ,CAApZ,EAAsZ,CAAtZ,EAAwZ,EAAxZ,EAA2Z,EAA3Z,EAA8Z,EAA9Z,EAAia,EAAja,EAAoa,EAApa,EAAua,EAAva,EAA0a,EAA1a,EAA6a,CAA7a,EAA+a,CAA/a,EAAib,CAAjb,EAAmb,CAAnb,EAAqb,EAArb,EAAwb,CAAxb,EAA0b,CAA1b,EAA4b,EAA5b,EAA+b,EAA/b,EAAkc,EAAlc,EAAqc,CAArc,EAAuc,EAAvc,EAA0c,CAA1c,EAA4c,CAA5c,EAA8c,CAA9c,EAAgd,EAAhd,EAAmd,EAAnd,EAAsd,CAAtd,EAAwd,EAAxd,EAA2d,EAA3d,EAA8d,CAA9d,EAAge,EAAhe,EAAme,CAAne,EAAqe,CAAre,EAAue,CAAve,EAAye,CAAze,EAA2e,CAA3e,EAA6e,CAA7e,EAA+e,EAA/e,EAAkf,CAAlf,EAAof,CAApf,EAAsf,CAAtf,EAAwf,EAAxf,EAA2f,EAA3f,EAA8f,CAA9f,EAAggB,CAAhgB,EAAkgB,CAAlgB,EAAogB,CAApgB,EAAsgB,EAAtgB,EAAygB,CAAzgB,EAA2gB,CAA3gB,EAA6gB,CAA7gB,EAA+gB,CAA/gB,EAAihB,CAAjhB,EAAmhB,CAAnhB,EAAqhB,CAArhB,EAAuhB,CAAvhB,EAAyhB,EAAzhB,EAA4hB,CAA5hB,EAA8hB,EAA9hB,EAAiiB,CAAjiB,EAAmiB,GAAniB,EAAuiB,EAAviB,EAA0iB,EAA1iB,EAA6iB,CAA7iB,EAA+iB,EAA/iB,EAAkjB,CAAljB,EAAojB,EAApjB,EAAujB,EAAvjB,EAA0jB,EAA1jB,EAA6jB,CAA7jB,EAA+jB,CAA/jB,EAAikB,CAAjkB,EAAmkB,GAAnkB,EAAukB,EAAvkB,EAA0kB,EAA1kB,EAA6kB,CAA7kB,EAA+kB,EAA/kB,EAAklB,EAAllB,EAAqlB,EAArlB,EAAwlB,CAAxlB,EAA0lB,EAA1lB,EAA6lB,EAA7lB,EAAgmB,EAAhmB,EAAmmB,CAAnmB,EAAqmB,EAArmB,EAAwmB,EAAxmB,EAA2mB,EAA3mB,EAA8mB,CAA9mB,EAAgnB,GAAhnB,EAAonB,EAApnB,EAAunB,GAAvnB,EAA2nB,EAA3nB,EAA8nB,EAA9nB,EAAioB,CAAjoB,EAAmoB,CAAnoB,EAAqoB,CAAroB,EAAuoB,CAAvoB,EAAyoB,CAAzoB,EAA2oB,CAA3oB,EAA6oB,CAA7oB,EAA+oB,CAA/oB,EAAipB,EAAjpB,EAAopB,EAAppB,EAAupB,CAAvpB,EAAypB,CAAzpB,EAA2pB,CAA3pB,EAA6pB,EAA7pB,EAAgqB,CAAhqB,EAAkqB,CAAlqB,EAAoqB,EAApqB,EAAuqB,EAAvqB,EAA0qB,CAA1qB,EAA4qB,CAA5qB,EAA8qB,CAA9qB,EAAgrB,EAAhrB,EAAmrB,CAAnrB,EAAqrB,EAArrB,EAAwrB,EAAxrB,EAA2rB,CAA3rB,EAA6rB,CAA7rB,EAA+rB,EAA/rB,EAAksB,CAAlsB,EAAosB,EAApsB,EAAusB,EAAvsB,EAA0sB,EAA1sB,EAA6sB,CAA7sB,EAA+sB,EAA/sB,EAAktB,EAAltB,EAAqtB,GAArtB,EAAytB,CAAztB,EAA2tB,CAA3tB,EAA6tB,EAA7tB,EAAguB,EAAhuB,EAAmuB,CAAnuB,EAAquB,EAAruB,EAAwuB,EAAxuB,EAA2uB,GAA3uB,EAA+uB,CAA/uB,EAAivB,CAAjvB,EAAmvB,CAAnvB,EAAqvB,CAArvB,EAAuvB,EAAvvB,EAA0vB,EAA1vB,EAA6vB,CAA7vB,EAA+vB,EAA/vB,EAAkwB,CAAlwB,EAAowB,CAApwB,EAAswB,CAAtwB,EAAwwB,CAAxwB,EAA0wB,EAA1wB,EAA6wB,EAA7wB,EAAgxB,CAAhxB,EAAkxB,GAAlxB,EAAsxB,EAAtxB,EAAyxB,EAAzxB,EAA4xB,CAA5xB,EAA8xB,CAA9xB,EAAgyB,EAAhyB,EAAmyB,CAAnyB,EAAqyB,EAAryB,EAAwyB,GAAxyB,EAA4yB,CAA5yB,EAA8yB,EAA9yB,EAAizB,GAAjzB,EAAqzB,GAArzB,EAAyzB,GAAzzB,EAA6zB,EAA7zB,EAAg0B,GAAh0B,EAAo0B,IAAp0B,EAAy0B,EAAz0B,EAA40B,EAA50B,EAA+0B,IAA/0B,EAAo1B,EAAp1B,EAAu1B,CAAv1B,EAAy1B,IAAz1B,EAA81B,GAA91B,EAAk2B,IAAl2B,EAAu2B,GAAv2B,EAA22B,CAA32B,EAA62B,EAA72B,EAAg3B,EAAh3B,EAAm3B,EAAn3B,EAAs3B,EAAt3B,EAAy3B,EAAz3B,EAA43B,EAA53B,EAA+3B,EAA/3B,EAAk4B,EAAl4B,EAAq4B,CAAr4B,EAAu4B,EAAv4B,EAA04B,EAA14B,EAA64B,CAA74B,EAA+4B,EAA/4B,EAAk5B,GAAl5B,EAAs5B,EAAt5B,EAAy5B,GAAz5B,EAA65B,EAA75B,EAAg6B,CAAh6B,EAAk6B,CAAl6B,EAAo6B,EAAp6B,EAAu6B,EAAv6B,EAA06B,EAA16B,EAA66B,CAA76B,EAA+6B,CAA/6B,EAAi7B,CAAj7B,EAAm7B,EAAn7B,EAAs7B,IAAt7B,EAA27B,CAA37B,EAA67B,IAA77B,EAAk8B,EAAl8B,EAAq8B,CAAr8B,EAAu8B,IAAv8B,EAA48B,CAA58B,EAA88B,CAA98B,EAAg9B,CAAh9B,EAAk9B,CAAl9B,EAAo9B,CAAp9B,EAAs9B,CAAt9B,EAAw9B,GAAx9B,EAA49B,EAA59B,EAA+9B,CAA/9B,EAAi+B,EAAj+B,EAAo+B,CAAp+B,EAAs+B,CAAt+B,EAAw+B,CAAx+B,EAA0+B,EAA1+B,EAA6+B,CAA7+B,EAA++B,CAA/+B,EAAi/B,GAAj/B,EAAq/B,IAAr/B,EAA0/B,GAA1/B,EAA8/B,CAA9/B,EAAggC,EAAhgC,EAAmgC,CAAngC,EAAqgC,CAArgC,EAAugC,CAAvgC,EAAygC,CAAzgC,EAA2gC,IAA3gC,EAAghC,EAAhhC,EAAmhC,CAAnhC,EAAqhC,EAArhC,EAAwhC,CAAxhC,EAA0hC,CAA1hC,EAA4hC,CAA5hC,EAA8hC,CAA9hC,EAAgiC,CAAhiC,EAAkiC,CAAliC,EAAoiC,CAApiC,EAAsiC,CAAtiC,EAAwiC,CAAxiC,EAA0iC,EAA1iC,EAA6iC,CAA7iC,EAA+iC,CAA/iC,EAAijC,CAAjjC,EAAmjC,CAAnjC,EAAqjC,CAArjC,EAAujC,EAAvjC,EAA0jC,CAA1jC,EAA4jC,CAA5jC,EAA8jC,CAA9jC,EAAgkC,CAAhkC,EAAkkC,CAAlkC,EAAokC,CAApkC,EAAskC,CAAtkC,EAAwkC,EAAxkC,EAA2kC,CAA3kC,EAA6kC,CAA7kC,EAA+kC,CAA/kC,EAAilC,CAAjlC,EAAmlC,CAAnlC,EAAqlC,CAArlC,EAAulC,CAAvlC,EAAylC,CAAzlC,EAA2lC,CAA3lC,EAA6lC,GAA7lC,EAAimC,CAAjmC,EAAmmC,EAAnmC,EAAsmC,CAAtmC,EAAwmC,EAAxmC,EAA2mC,CAA3mC,EAA6mC,EAA7mC,EAAgnC,CAAhnC,EAAknC,EAAlnC,EAAqnC,CAArnC,EAAunC,EAAvnC,EAA0nC,CAA1nC,EAA4nC,EAA5nC,EAA+nC,CAA/nC,EAAioC,EAAjoC,EAAooC,CAApoC,EAAsoC,EAAtoC,EAAyoC,CAAzoC,EAA2oC,EAA3oC,EAA8oC,CAA9oC,EAAgpC,EAAhpC,EAAmpC,CAAnpC,EAAqpC,CAArpC,EAAupC,IAAvpC,EAA4pC,EAA5pC,EAA+pC,CAA/pC,EAAiqC,CAAjqC,EAAmqC,GAAnqC,EAAuqC,EAAvqC,EAA0qC,GAA1qC,EAA8qC,EAA9qC,EAAirC,EAAjrC,EAAorC,CAAprC,EAAsrC,EAAtrC,EAAyrC,CAAzrC,EAA2rC,GAA3rC,EAA+rC,EAA/rC,EAAksC,EAAlsC,EAAqsC,EAArsC,EAAwsC,GAAxsC,EAA4sC,EAA5sC,EAA+sC,GAA/sC,EAAmtC,CAAntC,EAAqtC,CAArtC,EAAutC,CAAvtC,EAAytC,CAAztC,EAA2tC,CAA3tC,EAA6tC,CAA7tC,EAA+tC,EAA/tC,EAAkuC,CAAluC,EAAouC,GAApuC,EAAwuC,EAAxuC,EAA2uC,EAA3uC,EAA8uC,CAA9uC,EAAgvC,CAAhvC,EAAkvC,IAAlvC,EAAuvC,CAAvvC,EAAyvC,CAAzvC,EAA2vC,EAA3vC,EAA8vC,CAA9vC,EAAgwC,CAAhwC,EAAkwC,CAAlwC,EAAowC,CAApwC,EAAswC,CAAtwC,EAAwwC,CAAxwC,EAA0wC,CAA1wC,EAA4wC,CAA5wC,EAA8wC,CAA9wC,EAAgxC,CAAhxC,EAAkxC,CAAlxC,EAAoxC,CAApxC,EAAsxC,CAAtxC,EAAwxC,CAAxxC,EAA0xC,CAA1xC,EAA4xC,CAA5xC,EAA8xC,CAA9xC,EAAgyC,CAAhyC,EAAkyC,CAAlyC,EAAoyC,CAApyC,EAAsyC,CAAtyC,EAAwyC,CAAxyC,EAA0yC,CAA1yC,EAA4yC,CAA5yC,EAA8yC,CAA9yC,EAAgzC,CAAhzC,EAAkzC,CAAlzC,EAAozC,CAApzC,EAAszC,CAAtzC,EAAwzC,CAAxzC,EAA0zC,CAA1zC,EAA4zC,CAA5zC,EAA8zC,CAA9zC,EAAg0C,CAAh0C,EAAk0C,CAAl0C,EAAo0C,CAAp0C,EAAs0C,CAAt0C,EAAw0C,CAAx0C,EAA00C,CAA10C,EAA40C,CAA50C,EAA80C,CAA90C,EAAg1C,CAAh1C,EAAk1C,CAAl1C,EAAo1C,CAAp1C,EAAs1C,CAAt1C,EAAw1C,CAAx1C,EAA01C,CAA11C,EAA41C,CAA51C,EAA81C,CAA91C,EAAg2C,CAAh2C,EAAk2C,CAAl2C,EAAo2C,CAAp2C,EAAs2C,CAAt2C,EAAw2C,CAAx2C,EAA02C,CAA12C,EAA42C,EAA52C,EAA+2C,CAA/2C,EAAi3C,CAAj3C,EAAm3C,CAAn3C,EAAq3C,CAAr3C,EAAu3C,CAAv3C,EAAy3C,EAAz3C,EAA43C,IAA53C,EAAi4C,KAAj4C,EAAu4C,EAAv4C,EAA04C,IAA14C,EAA+4C,CAA/4C,EAAi5C,GAAj5C,EAAq5C,CAAr5C,EAAu5C,IAAv5C,EAA45C,EAA55C,EAA+5C,IAA/5C,EAAo6C,IAAp6C,EAAy6C,GAAz6C,EAA66C,IAA76C,EAAk7C,IAAl7C,EAAu7C,CAAv7C,EAAy7C,IAAz7C,CAAnC;AAEA,MAAMC,qBAAqB,GAAG,CAAC,GAAD,EAAK,CAAL,EAAO,GAAP,EAAW,CAAX,EAAa,GAAb,EAAiB,CAAjB,EAAmB,GAAnB,EAAuB,CAAvB,EAAyB,IAAzB,EAA8B,CAA9B,EAAgC,CAAhC,EAAkC,CAAlC,EAAoC,CAApC,EAAsC,CAAtC,EAAwC,EAAxC,EAA2C,CAA3C,EAA6C,CAA7C,EAA+C,CAA/C,EAAiD,GAAjD,EAAqD,CAArD,EAAuD,GAAvD,EAA2D,CAA3D,EAA6D,CAA7D,EAA+D,CAA/D,EAAiE,GAAjE,EAAqE,CAArE,EAAuE,EAAvE,EAA0E,CAA1E,EAA4E,EAA5E,EAA+E,EAA/E,EAAkF,EAAlF,EAAqF,CAArF,EAAuF,GAAvF,EAA2F,CAA3F,EAA6F,EAA7F,EAAgG,EAAhG,EAAmG,EAAnG,EAAsG,EAAtG,EAAyG,CAAzG,EAA2G,CAA3G,EAA6G,EAA7G,EAAgH,CAAhH,EAAkH,EAAlH,EAAqH,EAArH,EAAwH,CAAxH,EAA0H,CAA1H,EAA4H,EAA5H,EAA+H,CAA/H,EAAiI,CAAjI,EAAmI,CAAnI,EAAqI,EAArI,EAAwI,EAAxI,EAA2I,CAA3I,EAA6I,CAA7I,EAA+I,CAA/I,EAAiJ,CAAjJ,EAAmJ,EAAnJ,EAAsJ,CAAtJ,EAAwJ,EAAxJ,EAA2J,CAA3J,EAA6J,EAA7J,EAAgK,EAAhK,EAAmK,CAAnK,EAAqK,CAArK,EAAuK,CAAvK,EAAyK,EAAzK,EAA4K,EAA5K,EAA+K,EAA/K,EAAkL,CAAlL,EAAoL,CAApL,EAAsL,CAAtL,EAAwL,CAAxL,EAA0L,GAA1L,EAA8L,EAA9L,EAAiM,CAAjM,EAAmM,CAAnM,EAAqM,CAArM,EAAuM,CAAvM,EAAyM,EAAzM,EAA4M,CAA5M,EAA8M,CAA9M,EAAgN,CAAhN,EAAkN,CAAlN,EAAoN,CAApN,EAAsN,CAAtN,EAAwN,CAAxN,EAA0N,EAA1N,EAA6N,CAA7N,EAA+N,EAA/N,EAAkO,CAAlO,EAAoO,CAApO,EAAsO,CAAtO,EAAwO,CAAxO,EAA0O,CAA1O,EAA4O,GAA5O,EAAgP,EAAhP,EAAmP,EAAnP,EAAsP,CAAtP,EAAwP,CAAxP,EAA0P,CAA1P,EAA4P,EAA5P,EAA+P,EAA/P,EAAkQ,EAAlQ,EAAqQ,CAArQ,EAAuQ,GAAvQ,EAA2Q,CAA3Q,EAA6Q,CAA7Q,EAA+Q,CAA/Q,EAAiR,EAAjR,EAAoR,CAApR,EAAsR,EAAtR,EAAyR,EAAzR,EAA4R,EAA5R,EAA+R,CAA/R,EAAiS,EAAjS,EAAoS,EAApS,EAAuS,CAAvS,EAAyS,CAAzS,EAA2S,EAA3S,EAA8S,EAA9S,EAAiT,CAAjT,EAAmT,CAAnT,EAAqT,GAArT,EAAyT,EAAzT,EAA4T,GAA5T,EAAgU,CAAhU,EAAkU,EAAlU,EAAqU,CAArU,EAAuU,CAAvU,EAAyU,CAAzU,EAA2U,CAA3U,EAA6U,CAA7U,EAA+U,CAA/U,EAAiV,CAAjV,EAAmV,CAAnV,EAAqV,CAArV,EAAuV,EAAvV,EAA0V,CAA1V,EAA4V,GAA5V,EAAgW,CAAhW,EAAkW,CAAlW,EAAoW,CAApW,EAAsW,CAAtW,EAAwW,CAAxW,EAA0W,EAA1W,EAA6W,CAA7W,EAA+W,EAA/W,EAAkX,CAAlX,EAAoX,CAApX,EAAsX,CAAtX,EAAwX,CAAxX,EAA0X,CAA1X,EAA4X,EAA5X,EAA+X,EAA/X,EAAkY,EAAlY,EAAqY,EAArY,EAAwY,GAAxY,EAA4Y,CAA5Y,EAA8Y,CAA9Y,EAAgZ,CAAhZ,EAAkZ,EAAlZ,EAAqZ,CAArZ,EAAuZ,EAAvZ,EAA0Z,EAA1Z,EAA6Z,CAA7Z,EAA+Z,EAA/Z,EAAka,GAAla,EAAsa,CAAta,EAAwa,CAAxa,EAA0a,CAA1a,EAA4a,CAA5a,EAA8a,CAA9a,EAAgb,CAAhb,EAAkb,CAAlb,EAAob,CAApb,EAAsb,CAAtb,EAAwb,CAAxb,EAA0b,CAA1b,EAA4b,EAA5b,EAA+b,CAA/b,EAAic,CAAjc,EAAmc,CAAnc,EAAqc,CAArc,EAAuc,CAAvc,EAAyc,CAAzc,EAA2c,CAA3c,EAA6c,GAA7c,EAAid,CAAjd,EAAmd,EAAnd,EAAsd,CAAtd,EAAwd,CAAxd,EAA0d,CAA1d,EAA4d,EAA5d,EAA+d,CAA/d,EAAie,CAAje,EAAme,CAAne,EAAqe,EAAre,EAAwe,CAAxe,EAA0e,IAA1e,EAA+e,CAA/e,EAAif,CAAjf,EAAmf,EAAnf,EAAsf,KAAtf,EAA4f,CAA5f,EAA8f,EAA9f,EAAigB,CAAjgB,EAAmgB,EAAngB,EAAsgB,CAAtgB,EAAwgB,EAAxgB,EAA2gB,CAA3gB,EAA6gB,EAA7gB,EAAghB,CAAhhB,EAAkhB,IAAlhB,EAAuhB,CAAvhB,EAAyhB,CAAzhB,EAA2hB,EAA3hB,EAA8hB,CAA9hB,EAAgiB,CAAhiB,EAAkiB,EAAliB,EAAqiB,CAAriB,EAAuiB,EAAviB,EAA0iB,CAA1iB,EAA4iB,KAA5iB,EAAkjB,CAAljB,EAAojB,IAApjB,EAAyjB,EAAzjB,EAA4jB,CAA5jB,EAA8jB,EAA9jB,EAAikB,GAAjkB,EAAqkB,CAArkB,EAAukB,CAAvkB,EAAykB,CAAzkB,EAA2kB,CAA3kB,EAA6kB,CAA7kB,EAA+kB,CAA/kB,EAAilB,CAAjlB,EAAmlB,EAAnlB,EAAslB,CAAtlB,EAAwlB,GAAxlB,EAA4lB,CAA5lB,EAA8lB,IAA9lB,EAAmmB,EAAnmB,EAAsmB,GAAtmB,EAA0mB,EAA1mB,EAA6mB,CAA7mB,EAA+mB,EAA/mB,EAAknB,CAAlnB,EAAonB,CAApnB,EAAsnB,EAAtnB,EAAynB,CAAznB,EAA2nB,EAA3nB,EAA8nB,CAA9nB,EAAgoB,CAAhoB,EAAkoB,EAAloB,EAAqoB,IAAroB,EAA0oB,CAA1oB,EAA4oB,CAA5oB,EAA8oB,EAA9oB,EAAipB,CAAjpB,EAAmpB,CAAnpB,EAAqpB,CAArpB,EAAupB,CAAvpB,EAAypB,CAAzpB,EAA2pB,CAA3pB,EAA6pB,GAA7pB,EAAiqB,CAAjqB,EAAmqB,GAAnqB,EAAuqB,CAAvqB,EAAyqB,EAAzqB,EAA4qB,CAA5qB,EAA8qB,GAA9qB,EAAkrB,CAAlrB,EAAorB,EAAprB,EAAurB,EAAvrB,EAA0rB,GAA1rB,EAA8rB,EAA9rB,EAAisB,GAAjsB,EAAqsB,CAArsB,EAAusB,GAAvsB,EAA2sB,CAA3sB,EAA6sB,CAA7sB,EAA+sB,CAA/sB,EAAitB,IAAjtB,EAAstB,CAAttB,EAAwtB,MAAxtB,EAA+tB,GAA/tB,CAA9B;;AAKA,SAASC,aAAT,CAAuBC,IAAvB,EAAqCC,GAArC,EAAsE;EACpE,IAAIC,GAAG,GAAG,OAAV;;EACA,KAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,MAAM,GAAGH,GAAG,CAACG,MAA7B,EAAqCD,CAAC,GAAGC,MAAzC,EAAiDD,CAAC,IAAI,CAAtD,EAAyD;IACvDD,GAAG,IAAID,GAAG,CAACE,CAAD,CAAV;IACA,IAAID,GAAG,GAAGF,IAAV,EAAgB,OAAO,KAAP;IAEhBE,GAAG,IAAID,GAAG,CAACE,CAAC,GAAG,CAAL,CAAV;IACA,IAAID,GAAG,IAAIF,IAAX,EAAiB,OAAO,IAAP;EAClB;;EACD,OAAO,KAAP;AACD;;AAIM,SAASK,iBAAT,CAA2BL,IAA3B,EAAkD;EACvD,IAAIA,IAAI,KAAR,EAAiC,OAAOA,IAAI,OAAX;EACjC,IAAIA,IAAI,MAAR,EAAkC,OAAO,IAAP;EAClC,IAAIA,IAAI,KAAR,EAAiC,OAAOA,IAAI,OAAX;EACjC,IAAIA,IAAI,OAAR,EAAkC,OAAO,IAAP;;EAClC,IAAIA,IAAI,IAAI,MAAZ,EAAoB;IAClB,OACEA,IAAI,IAAI,IAAR,IAAgBN,uBAAuB,CAACY,IAAxB,CAA6BC,MAAM,CAACC,YAAP,CAAoBR,IAApB,CAA7B,CADlB;EAGD;;EACD,OAAOD,aAAa,CAACC,IAAD,EAAOH,0BAAP,CAApB;AACD;;AAIM,SAASY,gBAAT,CAA0BT,IAA1B,EAAiD;EACtD,IAAIA,IAAI,KAAR,EAA6B,OAAOA,IAAI,OAAX;EAC7B,IAAIA,IAAI,KAAR,EAA4B,OAAO,IAAP;EAC5B,IAAIA,IAAI,KAAR,EAAiC,OAAO,KAAP;EACjC,IAAIA,IAAI,MAAR,EAAkC,OAAO,IAAP;EAClC,IAAIA,IAAI,KAAR,EAAiC,OAAOA,IAAI,OAAX;EACjC,IAAIA,IAAI,OAAR,EAAkC,OAAO,IAAP;;EAClC,IAAIA,IAAI,IAAI,MAAZ,EAAoB;IAClB,OAAOA,IAAI,IAAI,IAAR,IAAgBJ,kBAAkB,CAACU,IAAnB,CAAwBC,MAAM,CAACC,YAAP,CAAoBR,IAApB,CAAxB,CAAvB;EACD;;EACD,OACED,aAAa,CAACC,IAAD,EAAOH,0BAAP,CAAb,IACAE,aAAa,CAACC,IAAD,EAAOF,qBAAP,CAFf;AAID;;AAIM,SAASY,gBAAT,CAA0BC,IAA1B,EAAiD;EACtD,IAAIC,OAAO,GAAG,IAAd;;EACA,KAAK,IAAIT,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGQ,IAAI,CAACP,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;IAKpC,IAAIU,EAAE,GAAGF,IAAI,CAACG,UAAL,CAAgBX,CAAhB,CAAT;;IACA,IAAI,CAACU,EAAE,GAAG,MAAN,MAAkB,MAAlB,IAA4BV,CAAC,GAAG,CAAJ,GAAQQ,IAAI,CAACP,MAA7C,EAAqD;MACnD,MAAMW,KAAK,GAAGJ,IAAI,CAACG,UAAL,CAAgB,EAAEX,CAAlB,CAAd;;MACA,IAAI,CAACY,KAAK,GAAG,MAAT,MAAqB,MAAzB,EAAiC;QAC/BF,EAAE,GAAG,WAAW,CAACA,EAAE,GAAG,KAAN,KAAgB,EAA3B,KAAkCE,KAAK,GAAG,KAA1C,CAAL;MACD;IACF;;IACD,IAAIH,OAAJ,EAAa;MACXA,OAAO,GAAG,KAAV;;MACA,IAAI,CAACP,iBAAiB,CAACQ,EAAD,CAAtB,EAA4B;QAC1B,OAAO,KAAP;MACD;IACF,CALD,MAKO,IAAI,CAACJ,gBAAgB,CAACI,EAAD,CAArB,EAA2B;MAChC,OAAO,KAAP;IACD;EACF;;EACD,OAAO,CAACD,OAAR;AACD"}