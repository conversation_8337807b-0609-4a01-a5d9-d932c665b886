import { shopifyIdHelper } from '@appmaker-xyz/shopify';
import { logEvent } from '../lib';
import { analytics } from '@appmaker-xyz/react-native';

function decodeIds(id) {
  try {
    const decodeId = shopifyIdHelper(id, true);
    let newID = decodeId[0].replace('/', '');
    return newID;
  } catch (error) {
    return id;
  }
}

export const logViewProduct = async ({ pageId, product }) => {
  const { id, title, priceRange } = product;
  const { amount, currencyCode } = priceRange?.minVariantPrice;
  const price = parseInt(amount);
  // const viewIteParams =
  logEvent(
    'view_item',    
    {
      currency: currencyCode,
      value: price,
      actionCategory: getScreenName(pageId) || '',
      items: [
        {
          item_name: title,
          item_id: decodeIds(id),
          price: price,
        },
      ],
    },
    product,
  );
  //   logEvent('view_product', 'facebook', {
  //     item_name: title,
  //     item_id: decodeIds(id),
  //     value: price,
  //   });
};

export const logProductList = async ({
  items,
  item_list_name,
  item_list_id,
}) => {
  logEvent('view_item_list', {
    ...item_list_name,
    ...item_list_id,
    items, // should be an array of items eg: [{item_id:1,item_name:title },{item_id:2,item_name:title}]
  });
};

export const logUserLogin = async ({ user }) => {
  const { id, email, displayName: name } = user;
  analytics().setUserId(id);
  analytics().setUserProperty(name, email);
  logEvent('user_logged_in', {
    // user_id: decodeIds(id),
    // user_name: name,
    // user_email: email,
    // user_login_provider: provider,
  });
  //   logEvent('user_logged_in', 'facebook', {
  //     user_id: decodeIds(id),
  //     user_name: name,
  //     user_email: email,
  //     // user_login_provider: provider,
  //   });
};

export const logUserRegister = async ({ user }) => {
  const { id, displayName: name, email } = user;
  logEvent('sign_up', {
    // user_id: decodeIds(id),
    // user_name: name,
    // user_email: email,
    // user_login_provider: provider,
  });
  //   logEvent('COMPLETE_REGISTRATION', 'facebook', {
  //     user_id: decodeIds(id),
  //     content_name: name,
  //     user_email: email,
  //     status: true,
  //     // user_login_provider: provider,
  //   });
};

export const logUserLogout = async () => {
  // logEvent('user_logged_out', 'firebase', {});
  logEvent('user_logged_out', {});
};

export const logPurchase = async (params) => {
  logEvent('purchase', params);
};

export const logAddToWishList = async (product) => {
  const { id, title, priceRange, pageId } = product;
  const { amount, currencyCode } = priceRange?.minVariantPrice;
  const price = parseInt(amount);
  logEvent(
    'add_to_wishlist',    
    {
      value: price,
      currency: currencyCode,
      eventCategory: getScreenName(pageId) || '',
      items: [
        {
          item_id: decodeIds(id),
          item_name: title,
          price: price,
          currency: currencyCode,
        },
      ],
    },
    product,
  );
};

export const logSearchQuery = async (query) => {
  logEvent('search', {
    search_term: query,
  });
  //   logEvent('SEARCH', 'facebook', {
  //     search_string: query,
  //   });
};

// done
export const logBannerClick = async (query) => {
  const { category, action, bannerName, item_id } = query;
  logEvent('BannerClick', {
    eventCategory: 'Home Screen',
    eventAction: 'Banner Click',
    banner_name: bannerName || '',
    item_id,
  });
};

// done
export const logNavbarClicks = async (query) => {
  const { category, action, bannerName } = query;
  logEvent('NavbarClicks', {
    eventCategory: 'Home Screen',
    eventAction: 'Navbar Clicks',
    category_name: bannerName || '',
  });
};

export const logBestSellersCategory = async (query) => {
  const { category, action, bannerName } = query;
  logEvent('BestSellersCategory', {
    eventCategory: category || '',
    eventAction: action,
    best_sellers_category: bannerName,
  });
};

// HamburgerCategory
export const logHamburgerCategory = async (query) => {
  const { category, action, bannerName } = query;
  logEvent('HamburgerCategory', {
    eventCategory: 'Home Screen',
    eventAction: 'Hamburger Category Clicks',
    category_name: bannerName,
  });
};

//CartIconClick done
export const logCartIconClick = async (query) => {
  const { pageId, action } = query;
  let screen = getScreenName(pageId);
  logEvent('CartIconClick', {
    eventCategory: screen || '',
    eventAction: 'Cart Icon Click',
  });
};

// UserIconClick done
export const logUserIconClick = async (query) => {
  const { category, action } = query;
  logEvent('UserIconClick', {
    eventCategory: 'Home Screen',
    eventAction: 'User Icon Click',
  });
};

// SortClick done
export const logSortClick = async (query) => {
  const { category, action, label, value } = query;
  logEvent('SortClick', {
    eventCategory: 'Category Screen',
    eventAction: 'Sort Clicks',
    // sort_click_item: [{ label, value }],
    label: label,
    value: value,
  });
};

// FilterClick done
export const logFilterClick = async (query) => {
  const { category, action, filterItem } = query;
  // let test = filterItem.map((v) => v);
  try {
    const filterItemString = JSON.stringify(filterItem);
    logEvent('FilterClick', {
      eventCategory: 'Category Screen',
      eventAction: 'Filter Clicks',
      filter_click_item: filterItemString,
      // items: filterItem.map((v) => v),
    });
  } catch (error) {}
};

// SubCategoryClick
export const logSubCategoryClick = async (query) => {
  const { category, action, subCategoryName } = query;
  logEvent('SubCategoryClick', {
    eventCategory: category || '',
    eventAction: 'SubCategory Clicks',
    sub_category_name: subCategoryName,
  });
};

// RemoveFromWishlist
export const logRemoveFromWishlist = async (query) => {
  const { category, action, productName, pageId } = query;
  logEvent('RemoveFromWishlist', {
    eventCategory: getScreenName(pageId) || '',
    eventAction: 'Remove From Wishlist clicks',
    product_name: productName,
  });
};

// ShareProduct
export const logShareProduct = async (query) => {
  const { productName } = query;
  logEvent('ShareProduct', {
    eventCategory: 'Product Screen',
    eventAction: 'Share Product clicks',
    product_name: productName,
  });
};

// ApplyCouponCode
export const logApplyCouponCode = async (query) => {
  const { category, action, couponCode } = query;
  logEvent('ApplyCouponCode', {
    eventCategory: category || '',
    eventAction: 'Apply Coupon Code',
    couponCode: couponCode,
  });
};
// CheckPIN
export const logCheckPIN = async (query) => {
  const { pincode, product_name } = query;
  logEvent('CheckPIN', {
    pincode,
    eventCategory: 'Product Screen',
    eventAction: 'Check PIN Clicks',
    product_name,
  });
};

export const logDrawerCategoryClick = async (query) => {
  const { action, title } = query;
  logEvent('SubCategoryClick', {
    eventCategory: 'Home Screen',
    eventAction: 'Drawer Category Clicks',
    sub_category_name: title,
  });
};

// TrackOrder
export const logTrackOrder = async (query) => {
  const { orderId } = query;
  logEvent('TrackOrder', {
    eventCategory: 'Order Screen',
    eventAction: 'Track Order Clicks',
    orderId,
  });
};

// ReturnExchange
export const logReturnExchange = async (query) => {
  const { orderId } = query;
  logEvent('ReturnOrder', {
    eventCategory: 'Order Screen',
    eventAction: 'ReturnExchange Clicks',
    orderId,
  });
};

// CancelOrder
export const logCancelOrder = async (query) => {
  const { orderId } = query;
  logEvent('CancelOrder', {
    eventCategory: 'Order Screen',
    eventAction: 'Cancel Order Clicks',
    orderId,
  });
};

// BeginCheckout
export const logBeginCheckout = async (query) => {
  // const {  } = query;
  logEvent('begin_checkout', query);
};

// ViewCart
export const logViewCart = async (query) => {
  // const {  } = query;
  logEvent('view_cart', query);
};

export const getScreenName = (screenName) => {
  if (screenName === 'home') {
    return 'Home Screen';
  }
  if (screenName === 'productDetail') {
    return 'Product Screen';
  }
  if (screenName === 'cartPageV2') {
    return 'Cart Screen';
  }
  if (screenName === 'productsByCollection') {
    return 'Category Screen';
  }
  return;
};

export const logScreenName = async (pageId) => {
  // const screen = getScreenName(pageId);
  // if (screen) {
  //   await analytics().logScreenView({
  //     screen_name: screen,
  //     screen_class: screen,
  //   });
  // }
};
