import create from 'zustand';
import createContext from 'zustand/context';
import produce from 'immer';
import { devtools } from 'zustand/middleware';
import { applyFilters } from '@appmaker-xyz/core';

export const {
  Provider: ShopifyFilterProvider,
  useStore: useShopifyFilterStore,
  useStoreApi: useShopifyFilterStoreApi,
} = createContext();
function removeZeroCountItems(filterList) {
  const disableEmptyFilters = applyFilters(
    'shopify-disable-empty-filters',
    true,
  );
  const hideEmptyFilters = applyFilters('shopify-hide-empty-filters', false);

  if (hideEmptyFilters || disableEmptyFilters === true) {
    const newFilterItems = filterList.map((filter) => {
      const newFilter = { ...filter };
      if (hideEmptyFilters) {
        newFilter.values = newFilter.values.filter((value) => {
          if (value?.hasOwnProperty?.('count') && newFilter?.type === 'LIST') {
            return value?.count > 0;
          }
          return true;
        });
      } else if (disableEmptyFilters === true) {
        newFilter.values = newFilter.values.map((value) => {
          if (value?.hasOwnProperty?.('count') && newFilter?.type === 'LIST') {
            if (value?.count === 0) {
              return { ...value, empty: true };
            }
          }
          return value;
        });
      }
      return newFilter;
    });
    let newFilterList = newFilterItems;
    if (!hideEmptyFilters && disableEmptyFilters === true) {
      newFilterList = newFilterItems.map((filter) => {
        if (filter?.values?.every?.((item) => item.empty === true)) {
          return { ...filter, empty: true };
        }
        return filter;
      });
    }
    newFilterList = newFilterList.filter((filter) => {
      return filter.values.length > 0;
    });
    return newFilterList;
  } else {
    return filterList;
  }
}

const filterHiddenObjects = (filterValue) => {
  const filteredObj = {};
  for (const key in filterValue) {
    if (filterValue[key]?.hidden === true) {
      filteredObj[key] = filterValue[key];
    }
  }
  return filteredObj;
};

export const createFilterStore =
  ({
    selectedFillters = {},
    isNextFiltersLoading = false,
    selectedSortValue = null,
    filterModalShown = false,
    sortModalShown = false,
    availableFilters = [],
    priceRange = null,
    context,
    clearFilter = false,
  }) =>
  () =>
    create(
      devtools((set, get) => ({
        selectedFillters: selectedFillters,
        filterModalShown: filterModalShown,
        sortModalShown: sortModalShown,
        availableFilters: availableFilters,
        priceRange: priceRange,
        context,
        openSortModal: () => {
          set(
            produce((draft) => {
              draft.sortModalShown = true;
            }),
          );
        },
        closeSortModal: () => {
          set(
            produce((draft) => {
              draft.sortModalShown = false;
            }),
          );
        },
        clearSelectedSort: () => {
          set(
            produce((draft) => {
              draft.selectedSortValue = null;
            }),
          );
        },
        setSelectedSort: (sort) => {
          set(
            produce((draft) => {
              draft.selectedSortValue = sort;
            }),
          );
        },
        setAvailableFilters: (filters) => {
          set(
            produce((draft) => {
              draft.availableFilters = removeZeroCountItems(filters);
            }),
          );
        },
        getSelectedSort: () => {
          return get().selectedSortValue;
        },
        openFilterModal: () => {
          set(
            produce((draft) => {
              draft.filterModalShown = true;
            }),
          );
        },
        closeFilterModal: () => {
          set(
            produce((draft) => {
              draft.filterModalShown = false;
            }),
          );
        },
        clearSelectedFilters: () => {
          const selectedFilters = get().selectedFillters;
          const filteredObject = {};
          for (const key in selectedFilters) {
            const filteredSubObject = filterHiddenObjects(selectedFilters[key]);
            if (Object.keys(filteredSubObject).length > 0) {
              filteredObject[key] = filteredSubObject;
            }
          }
          set({ selectedFillters: filteredObject, clearFilter: true });
        },

        getSelectedFilters: () => {
          return get().selectedFillters;
        },
        isFilterSelected: (filterKey, filterValue) => {
          const selectedFilters = get().selectedFillters;
          if (selectedFilters[filterKey]) {
            return selectedFilters[filterKey][filterValue];
          }
          return false;
        },
        hasAnyFilterSelected: (filterKey) => {
          const selectedFilters = get().selectedFillters;
          if (selectedFilters[filterKey]) {
            return Object.keys(selectedFilters[filterKey]).length > 0;
          }
          return false;
        },
        removeFilter: (filterKey, filterValue, value) => {
          set(
            produce((draft) => {
              if (draft.selectedFillters[filterKey]) {
                delete draft.selectedFillters[filterKey][filterValue];
              }
            }),
          );
        },
        selectFilter: (filterKey, filterValue, value) => {
          set(
            produce((draft) => {
              if (value?.type === 'PRICE_RANGE') {
                // Clear any existing price filters first
                Object.keys(draft.selectedFillters).forEach((key) => {
                  if (
                    draft.selectedFillters[key]?.[key]?.type === 'PRICE_RANGE'
                  ) {
                    delete draft.selectedFillters[key];
                  }
                });
              }

              if (!draft.selectedFillters[filterKey]) {
                draft.selectedFillters[filterKey] = {};
              }
              draft.selectedFillters[filterKey][filterValue] = value;
            }),
          );
        },
        setNextFiltersLoading: (loading) => {
          set(
            produce((draft) => {
              draft.isNextFiltersLoading = loading;
            }),
          );
        },
      })),
    );
