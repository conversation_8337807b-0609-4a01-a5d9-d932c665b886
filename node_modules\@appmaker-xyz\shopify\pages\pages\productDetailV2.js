const productDetailV2 = {
  title: '',
  blocks: [
    {
      name: 'appmaker/template-block',
      attibutes: {
        product: '{{}}',
      },
    },
  ],
  dataSource: {
    runByDefault: false,
    source: 'shopify',
    attributes: {
      mapping: {
        items: 'data.data',
      },
      methodName: 'product',
      params: {
        productId: '{{currentAction.params.productId}}',
        productHandle: '{{currentAction.params.productHandle}}',
      },
    },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
  },
  attributes: {
    pageTemplateSupport: true,
    pageTemplateId: 'customProductDetail',
    loadingLayout: 'product',
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      // paddingHorizontal: 16,
    },
    // headerShown: false,
  },
};
export default productDetailV2;
