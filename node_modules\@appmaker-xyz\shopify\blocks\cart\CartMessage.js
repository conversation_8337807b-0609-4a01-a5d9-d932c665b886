import { View } from 'react-native';
import React from 'react';
import { TextNotice } from '@appmaker-xyz/ui';
import { useCart } from '../../hooks/cart/useCart';
import { useEffect } from 'react';
import { appmakerFunctions } from '@appmaker-xyz/core';

export default function CartMessage() {
  const [message, setMessage] = React.useState('');
  const { cart, setCanCheckout } = useCart();

  useEffect(() => {
    async function syncMessage(params) {
      const validateCheckout = await appmakerFunctions.runAppmakerFunction(
        'appmaker-checkout-messages',
        {
          valid: true,
        },
        { checkout: cart },
      );
      if (!validateCheckout.valid) {
        if (validateCheckout.disable_checkout) {
          setCanCheckout(false);
        }
        setMessage(validateCheckout.message || 'Unable to checkout');
      } else {
        setCanCheckout(true);
        setMessage('');
      }
    }
    syncMessage();
  }, [cart]);
  return message ? (
    <View>
      <TextNotice
        attributes={{
          message: message,
          status: 'danger',
        }}
      />
    </View>
  ) : null;
}
