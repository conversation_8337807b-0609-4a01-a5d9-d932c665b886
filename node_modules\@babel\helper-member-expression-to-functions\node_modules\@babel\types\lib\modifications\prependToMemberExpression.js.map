{"version": 3, "names": ["_generated", "require", "_", "prependToMemberExpression", "member", "prepend", "is<PERSON><PERSON><PERSON>", "object", "Error", "memberExpression"], "sources": ["../../src/modifications/prependToMemberExpression.ts"], "sourcesContent": ["import { memberExpression } from \"../builders/generated\";\nimport { isSuper } from \"..\";\nimport type * as t from \"..\";\n\n/**\n * Prepend a node to a member expression.\n */\nexport default function prependToMemberExpression<\n  T extends Pick<t.MemberExpression, \"object\" | \"property\">,\n>(member: T, prepend: t.MemberExpression[\"object\"]): T {\n  if (isSuper(member.object)) {\n    throw new Error(\n      \"Cannot prepend node to super property access (`super.foo`).\",\n    );\n  }\n  member.object = memberExpression(prepend, member.object);\n\n  return member;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,CAAA,GAAAD,OAAA;AAMe,SAASE,yBAAyBA,CAE/CC,MAAS,EAAEC,OAAqC,EAAK;EACrD,IAAI,IAAAC,SAAO,EAACF,MAAM,CAACG,MAAM,CAAC,EAAE;IAC1B,MAAM,IAAIC,KAAK,CACb,6DACF,CAAC;EACH;EACAJ,MAAM,CAACG,MAAM,GAAG,IAAAE,2BAAgB,EAACJ,OAAO,EAAED,MAAM,CAACG,MAAM,CAAC;EAExD,OAAOH,MAAM;AACf"}