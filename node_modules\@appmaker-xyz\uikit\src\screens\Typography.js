import React from 'react';
import {View, StyleSheet} from 'react-native';
import {spacing} from '../styles';
import {AppmakerText, GridItem, TableCell, ActionBar} from '../components';

const Typography = () => {
  return (
    <View style={styles.container}>
      <AppmakerText category="largeHeading">Large Heading</AppmakerText>
      <AppmakerText category="pageHeading">Page Heading </AppmakerText>
      <AppmakerText category="pageSubHeading">Page Sub Heading</AppmakerText>
      <AppmakerText category="h1Heading">H1 Heading</AppmakerText>
      <AppmakerText category="h1SubHeading">H1 Sub Heading</AppmakerText>
      <AppmakerText category="actionTitle">Action Title</AppmakerText>
      <AppmakerText category="bodyParagraphBold">
        Body Paragraph Bold
      </AppmakerText>
      <AppmakerText category="bodyParagraph">Body Paragraph</AppmakerText>
      <AppmakerText category="bodySubText">Body Subtext</AppmakerText>
      <AppmakerText category="highlighter1">Highlighter 1</AppmakerText>
      <AppmakerText category="highlighter2">Highlighter 2</AppmakerText>
      <View style={styles.gridContainer}>
        <GridItem uri="https://picsum.photos/200/300?random" title="Vegies" />
        <GridItem
          uri="https://picsum.photos/200/300?grayscale"
          title="Fruits & Vegitables"
        />
        <GridItem uri="https://picsum.photos/200/300" title="Nuts" />
        <GridItem uri="https://picsum.photos/200/300" title="Nuts" />
        <GridItem uri="https://picsum.photos/200/300" title="Nuts" />
        <GridItem uri="https://picsum.photos/200/300" title="Nuts" />
      </View>
      <View>
        <TableCell attributes={{title: 'Title Here', value: 'Data here'}} />
        <TableCell attributes={{title: 'Title Here', value: 'Data here'}} />
        <TableCell attributes={{title: 'Title Here', value: 'Data here'}} />
      </View>
      <ActionBar title="Go Somewhere" />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: spacing.base,
  },
  gridContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
});

export default Typography;
