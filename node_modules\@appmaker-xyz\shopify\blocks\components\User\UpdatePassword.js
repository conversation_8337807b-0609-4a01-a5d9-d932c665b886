import React from 'react';
import { View, TextInput, StyleSheet } from 'react-native';
import { ThemeText, Button } from '@appmaker-xyz/ui';
import { useUserProfile } from '../../../hooks/user/useUserProfile';
function Input(props) {
  return (
    <View>
      <ThemeText size="sm">{props.label}</ThemeText>
      <TextInput
        placeholder={props.placeholder}
        value={props.value}
        secureTextEntry={true}
        onChangeText={props.onChangeText}
        {...props}
      />
    </View>
  );
}

const UpdatePassword = (props) => {
  const {
    setNewFirstName,
    setNewLastName,
    setNewEmail,
    setNewPhone,
    firstName,
    lastName,
    email,
    phone,
    updateProfile,
    changePassword,
    setNewPassword,
    setConfirmPassword,
    isLoading,
  } = useUserProfile(props);
  return (
    <View>
      <Input
        label="Password"
        placeholder="Enter new password"
        onChangeText={setNewPassword}
        style={[styles.input, styles.inputHeight]}
      />
      <Input
        label="Confirm password"
        placeholder="Confirm password"
        onChangeText={setConfirmPassword}
        // editable={user?.email ? false : true}
        style={[styles.input, styles.inputHeight]}
      />
      <Button
        onPress={changePassword}
        isLoading={isLoading}
        title=" Update Profile"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
    marginTop: 2,
  },
  inputHeight: {
    height: 46,
  },
  textAreaHeight: {
    height: 100,
    textAlignVertical: 'top',
  },
  starRatingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    marginTop: 2,
  },
  starIcon: {
    marginRight: 5,
    padding: 5,
  },
});

export default UpdatePassword;
