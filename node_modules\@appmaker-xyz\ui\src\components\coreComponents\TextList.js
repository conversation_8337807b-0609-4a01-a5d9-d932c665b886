import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ThemeText } from '../atoms';
import Icon from 'react-native-vector-icons/Feather';

const TextList = ({ attributes = {}, clientId }) => {
  const { text, iconName, iconColor } = attributes;

  return (
    <Layout style={styles.listItem}>
      {iconName ? (
        <Icon
          name={iconName}
          size={16}
          color={iconColor || '#4F4F4F'}
          style={styles.icon}
        />
      ) : null}
      <ThemeText category="bodyParagraph" style={styles.center}>
        {text}
      </ThemeText>
    </Layout>
  );
};

const styles = StyleSheet.create({
  listItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#ffffff',
    paddingVertical: 2,
    paddingHorizontal: 12,
  },
  text: {
    textAlign: 'center',
  },
  icon: {
    marginEnd: 4,
    marginTop: 3,
  },
});

export default TextList;
