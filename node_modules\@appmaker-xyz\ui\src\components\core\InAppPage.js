import React from 'react';
import { Layout } from '@appmaker-xyz/ui';
import { useInAppPage } from '@appmaker-xyz/core';

function InAppPage({ attributes, BlocksView, currentAction, onAction }) {
  const { pageId } = attributes;
  const [{ inAppPage, loading: inAppPageLoading }, refetch] = useInAppPage({
    pageId,
    action: currentAction,
  });
  const loadingLayout =
    pageId === 'home' ? 'home' : inAppPage?.attributes?.loadingLayout;

  return (
    <Layout
      loadingLayout={loadingLayout}
      loading={inAppPageLoading && !inAppPage?.blocks}>
      <BlocksView
        inAppPage={inAppPage}
        currentAction={currentAction}
        onAction={onAction}
        onRefresh={inAppPage?.attributes?.enablePullRefresh ? refetch : null}
        isRefreshing={inAppPageLoading}
      />
    </Layout>
  );
}

export default React.memo(InAppPage);
