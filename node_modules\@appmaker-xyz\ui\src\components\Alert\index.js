import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ThemeText, AppTouchable } from '../atoms';
import Icon from 'react-native-vector-icons/Feather';
import { AppModal } from '../coreComponents';
import { useAlertStore, ModalAlert } from './alertStore';

const ModalAlertProvider = () => {
  const alertStore = useAlertStore();
  const { title, message, leftButton, rightButton } = alertStore.alterDataState;
  return (
    <Layout>
      <AppModal
        isVisible={alertStore.alertShown}
        onBackdropPress={alertStore.hide}
        style={styles.modal}>
        <Layout style={styles.modalContainer}>
          <Layout style={styles.modalHeader}>
            <ThemeText size="xl" fontFamily="medium">
              {title}
            </ThemeText>
            <Icon
              name="x"
              size={28}
              color="#000"
              style={styles.closeIcon}
              onPress={alertStore.hide}
            />
          </Layout>
          <ThemeText style={styles.text} color="#8391A1">
            {message}
          </ThemeText>
          <Layout style={styles.cardActionsContainer}>
            <AppTouchable
              style={styles.loginButton}
              onPress={leftButton?.leftButton}>
              <ThemeText fontFamily="medium">{leftButton?.title}</ThemeText>
            </AppTouchable>
            <AppTouchable
              style={styles.guestButton}
              onPress={leftButton?.rightButton}>
              <ThemeText color="#fff" fontFamily="medium">
                {rightButton?.title}
              </ThemeText>
            </AppTouchable>
          </Layout>
        </Layout>
      </AppModal>
    </Layout>
  );
};
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'center',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 18,
    overflow: 'hidden',
    padding: 18,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  closeIcon: {
    padding: 6,
  },
  text: {
    marginTop: 6,
    marginBottom: 18,
  },
  cardActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  loginButton: {
    borderWidth: 1,
    borderColor: '#000',
    paddingVertical: 12,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
    overflow: 'hidden',
  },
  guestButton: {
    borderWidth: 1,
    borderColor: '#000',
    backgroundColor: '#000000',
    paddingVertical: 12,
    flex: 1,
    marginLeft: 6,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
    overflow: 'hidden',
  },
});

export default ModalAlertProvider;
export { ModalAlert };
