import React from 'react';
import { StyleSheet } from 'react-native';
import { ProfileCard, Layout, AppmakerText, ActionBar } from '../..';
import { useApThemeState } from '../../../theme/ThemeContext';
import { ToSignIn } from './components';

const Account = (props) => {
  const {
    signedIn,
    userName,
    imageUrl,
    logoutAction,
    loginAction,
    createAccountAction,
    socialLogin,
  } = props;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  return (
    <Layout style={styles.container}>
      {signedIn ? (
        <>
          <ProfileCard
            attributes={{
              image:
                imageUrl ||
                'https://gravatar.com/avatar/edd206844f3ebd31870791fcbb8798c1?s=400&d=mp&r=r',
              title: userName || '',
            }}
          />
          <Layout style={styles.actions}>
            <AppmakerText category="bodyParagraph" status="grey">
              Settings
            </AppmakerText>
            <ActionBar
              title="Logout"
              leftIcon="log-out"
              onPress={logoutAction}
            />
          </Layout>
        </>
      ) : (
        <ToSignIn
          loginAction={loginAction}
          createAccountAction={createAccountAction}
          showSocialLogin={socialLogin}
        />
      )}
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      flex: 1,
      padding: spacing.small,
    },
    actions: {
      marginTop: spacing.base,
    },
    notSigned: {
      alignItems: 'center',
    },
    row: {
      backgroundColor: color.white,
      flexDirection: 'row',
      width: '100%',
      marginTop: spacing.md,
    },
    column: {
      width: '50%',
      paddingHorizontal: spacing.nano,
    },
  });

export default Account;
