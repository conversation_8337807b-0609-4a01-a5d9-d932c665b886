{"version": 3, "names": ["whitespace", "require", "parens", "_t", "FLIPPED_ALIAS_KEYS", "isCallExpression", "isExpressionStatement", "isMemberExpression", "isNewExpression", "expandAliases", "obj", "newObj", "add", "type", "func", "fn", "node", "parent", "stack", "result", "Object", "keys", "aliases", "alias", "expandedParens", "expandedWhitespaceNodes", "nodes", "find", "printStack", "isOrHasCallExpression", "object", "needsWhitespace", "expression", "flag", "needsWhitespaceBefore", "needsWhitespaceAfter", "needsParens", "callee"], "sources": ["../../src/node/index.ts"], "sourcesContent": ["import * as whitespace from \"./whitespace\";\nimport * as parens from \"./parentheses\";\nimport {\n  FLIPPED_ALIAS_KEYS,\n  isCallExpression,\n  isExpressionStatement,\n  isMemberExpression,\n  isNewExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nimport type { WhitespaceFlag } from \"./whitespace\";\n\nexport type NodeHandlers<R> = {\n  [K in string]?: (\n    node: K extends t.Node[\"type\"] ? Extract<t.Node, { type: K }> : t.Node,\n    // todo:\n    // node: K extends keyof typeof t\n    //   ? Extract<typeof t[K], { type: \"string\" }>\n    //   : t.Node,\n    parent: t.Node,\n    stack: t.Node[],\n  ) => R;\n};\n\nfunction expandAliases<R>(obj: NodeHandlers<R>) {\n  const newObj: NodeHandlers<R> = {};\n\n  function add(\n    type: string,\n    func: (node: t.Node, parent: t.Node, stack: t.Node[]) => R,\n  ) {\n    const fn = newObj[type];\n    newObj[type] = fn\n      ? function (node, parent, stack) {\n          const result = fn(node, parent, stack);\n\n          return result == null ? func(node, parent, stack) : result;\n        }\n      : func;\n  }\n\n  for (const type of Object.keys(obj)) {\n    const aliases = FLIPPED_ALIAS_KEYS[type];\n    if (aliases) {\n      for (const alias of aliases) {\n        add(alias, obj[type]);\n      }\n    } else {\n      add(type, obj[type]);\n    }\n  }\n\n  return newObj;\n}\n\n// Rather than using `t.is` on each object property, we pre-expand any type aliases\n// into concrete types so that the 'find' call below can be as fast as possible.\nconst expandedParens = expandAliases(parens);\nconst expandedWhitespaceNodes = expandAliases(whitespace.nodes);\n\nfunction find<R>(\n  obj: NodeHandlers<R>,\n  node: t.Node,\n  parent: t.Node,\n  printStack?: t.Node[],\n): R | null {\n  const fn = obj[node.type];\n  return fn ? fn(node, parent, printStack) : null;\n}\n\nfunction isOrHasCallExpression(node: t.Node): boolean {\n  if (isCallExpression(node)) {\n    return true;\n  }\n\n  return isMemberExpression(node) && isOrHasCallExpression(node.object);\n}\n\nexport function needsWhitespace(\n  node: t.Node,\n  parent: t.Node,\n  type: WhitespaceFlag,\n): boolean {\n  if (!node) return false;\n\n  if (isExpressionStatement(node)) {\n    node = node.expression;\n  }\n\n  const flag = find(expandedWhitespaceNodes, node, parent);\n\n  if (typeof flag === \"number\") {\n    return (flag & type) !== 0;\n  }\n\n  return false;\n}\n\nexport function needsWhitespaceBefore(node: t.Node, parent: t.Node) {\n  return needsWhitespace(node, parent, 1);\n}\n\nexport function needsWhitespaceAfter(node: t.Node, parent: t.Node) {\n  return needsWhitespace(node, parent, 2);\n}\n\nexport function needsParens(\n  node: t.Node,\n  parent: t.Node,\n  printStack?: t.Node[],\n) {\n  if (!parent) return false;\n\n  if (isNewExpression(parent) && parent.callee === node) {\n    if (isOrHasCallExpression(node)) return true;\n  }\n\n  return find(expandedParens, node, parent, printStack);\n}\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,EAAA,GAAAF,OAAA;AAMsB;EALpBG,kBAAkB;EAClBC,gBAAgB;EAChBC,qBAAqB;EACrBC,kBAAkB;EAClBC;AAAe,IAAAL,EAAA;AAkBjB,SAASM,aAAaA,CAAIC,GAAoB,EAAE;EAC9C,MAAMC,MAAuB,GAAG,CAAC,CAAC;EAElC,SAASC,GAAGA,CACVC,IAAY,EACZC,IAA0D,EAC1D;IACA,MAAMC,EAAE,GAAGJ,MAAM,CAACE,IAAI,CAAC;IACvBF,MAAM,CAACE,IAAI,CAAC,GAAGE,EAAE,GACb,UAAUC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE;MAC7B,MAAMC,MAAM,GAAGJ,EAAE,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,CAAC;MAEtC,OAAOC,MAAM,IAAI,IAAI,GAAGL,IAAI,CAACE,IAAI,EAAEC,MAAM,EAAEC,KAAK,CAAC,GAAGC,MAAM;IAC5D,CAAC,GACDL,IAAI;EACV;EAEA,KAAK,MAAMD,IAAI,IAAIO,MAAM,CAACC,IAAI,CAACX,GAAG,CAAC,EAAE;IACnC,MAAMY,OAAO,GAAGlB,kBAAkB,CAACS,IAAI,CAAC;IACxC,IAAIS,OAAO,EAAE;MACX,KAAK,MAAMC,KAAK,IAAID,OAAO,EAAE;QAC3BV,GAAG,CAACW,KAAK,EAAEb,GAAG,CAACG,IAAI,CAAC,CAAC;MACvB;IACF,CAAC,MAAM;MACLD,GAAG,CAACC,IAAI,EAAEH,GAAG,CAACG,IAAI,CAAC,CAAC;IACtB;EACF;EAEA,OAAOF,MAAM;AACf;AAIA,MAAMa,cAAc,GAAGf,aAAa,CAACP,MAAM,CAAC;AAC5C,MAAMuB,uBAAuB,GAAGhB,aAAa,CAACT,UAAU,CAAC0B,KAAK,CAAC;AAE/D,SAASC,IAAIA,CACXjB,GAAoB,EACpBM,IAAY,EACZC,MAAc,EACdW,UAAqB,EACX;EACV,MAAMb,EAAE,GAAGL,GAAG,CAACM,IAAI,CAACH,IAAI,CAAC;EACzB,OAAOE,EAAE,GAAGA,EAAE,CAACC,IAAI,EAAEC,MAAM,EAAEW,UAAU,CAAC,GAAG,IAAI;AACjD;AAEA,SAASC,qBAAqBA,CAACb,IAAY,EAAW;EACpD,IAAIX,gBAAgB,CAACW,IAAI,CAAC,EAAE;IAC1B,OAAO,IAAI;EACb;EAEA,OAAOT,kBAAkB,CAACS,IAAI,CAAC,IAAIa,qBAAqB,CAACb,IAAI,CAACc,MAAM,CAAC;AACvE;AAEO,SAASC,eAAeA,CAC7Bf,IAAY,EACZC,MAAc,EACdJ,IAAoB,EACX;EACT,IAAI,CAACG,IAAI,EAAE,OAAO,KAAK;EAEvB,IAAIV,qBAAqB,CAACU,IAAI,CAAC,EAAE;IAC/BA,IAAI,GAAGA,IAAI,CAACgB,UAAU;EACxB;EAEA,MAAMC,IAAI,GAAGN,IAAI,CAACF,uBAAuB,EAAET,IAAI,EAAEC,MAAM,CAAC;EAExD,IAAI,OAAOgB,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAO,CAACA,IAAI,GAAGpB,IAAI,MAAM,CAAC;EAC5B;EAEA,OAAO,KAAK;AACd;AAEO,SAASqB,qBAAqBA,CAAClB,IAAY,EAAEC,MAAc,EAAE;EAClE,OAAOc,eAAe,CAACf,IAAI,EAAEC,MAAM,EAAE,CAAC,CAAC;AACzC;AAEO,SAASkB,oBAAoBA,CAACnB,IAAY,EAAEC,MAAc,EAAE;EACjE,OAAOc,eAAe,CAACf,IAAI,EAAEC,MAAM,EAAE,CAAC,CAAC;AACzC;AAEO,SAASmB,WAAWA,CACzBpB,IAAY,EACZC,MAAc,EACdW,UAAqB,EACrB;EACA,IAAI,CAACX,MAAM,EAAE,OAAO,KAAK;EAEzB,IAAIT,eAAe,CAACS,MAAM,CAAC,IAAIA,MAAM,CAACoB,MAAM,KAAKrB,IAAI,EAAE;IACrD,IAAIa,qBAAqB,CAACb,IAAI,CAAC,EAAE,OAAO,IAAI;EAC9C;EAEA,OAAOW,IAAI,CAACH,cAAc,EAAER,IAAI,EAAEC,MAAM,EAAEW,UAAU,CAAC;AACvD"}