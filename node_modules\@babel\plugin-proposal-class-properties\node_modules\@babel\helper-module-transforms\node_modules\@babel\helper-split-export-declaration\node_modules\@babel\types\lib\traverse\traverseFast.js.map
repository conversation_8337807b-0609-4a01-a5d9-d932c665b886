{"version": 3, "names": ["_definitions", "require", "traverseFast", "node", "enter", "opts", "keys", "VISITOR_KEYS", "type", "key", "subNode", "Array", "isArray"], "sources": ["../../src/traverse/traverseFast.ts"], "sourcesContent": ["import { VISITOR_KEYS } from \"../definitions\";\nimport type * as t from \"..\";\n\n/**\n * A prefix AST traversal implementation meant for simple searching\n * and processing.\n */\nexport default function traverseFast<Options = {}>(\n  node: t.Node | null | undefined,\n  enter: (node: t.Node, opts?: Options) => void,\n  opts?: Options,\n): void {\n  if (!node) return;\n\n  const keys = VISITOR_KEYS[node.type];\n  if (!keys) return;\n\n  opts = opts || ({} as Options);\n  enter(node, opts);\n\n  for (const key of keys) {\n    const subNode: t.Node | undefined | null =\n      // @ts-expect-error key must present in node\n      node[key];\n\n    if (Array.isArray(subNode)) {\n      for (const node of subNode) {\n        traverseFast(node, enter, opts);\n      }\n    } else {\n      traverseFast(subNode, enter, opts);\n    }\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAOe,SAASC,YAAYA,CAClCC,IAA+B,EAC/BC,KAA6C,EAC7CC,IAAc,EACR;EACN,IAAI,CAACF,IAAI,EAAE;EAEX,MAAMG,IAAI,GAAGC,yBAAY,CAACJ,IAAI,CAACK,IAAI,CAAC;EACpC,IAAI,CAACF,IAAI,EAAE;EAEXD,IAAI,GAAGA,IAAI,IAAK,CAAC,CAAa;EAC9BD,KAAK,CAACD,IAAI,EAAEE,IAAI,CAAC;EAEjB,KAAK,MAAMI,GAAG,IAAIH,IAAI,EAAE;IACtB,MAAMI,OAAkC,GAEtCP,IAAI,CAACM,GAAG,CAAC;IAEX,IAAIE,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;MAC1B,KAAK,MAAMP,IAAI,IAAIO,OAAO,EAAE;QAC1BR,YAAY,CAACC,IAAI,EAAEC,KAAK,EAAEC,IAAI,CAAC;MACjC;IACF,CAAC,MAAM;MACLH,YAAY,CAACQ,OAAO,EAAEN,KAAK,EAAEC,IAAI,CAAC;IACpC;EACF;AACF"}