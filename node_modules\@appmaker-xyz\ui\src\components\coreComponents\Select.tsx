import React, { useEffect, useMemo, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import Icon from 'react-native-vector-icons/Feather';
import { usePageState } from '@appmaker-xyz/core';
import { ThemeText } from '../atoms';
import { styles as themeStyles } from '../../styles';

interface SelectProps {
  label: string;
  leftIconName: string;
  name: string;
  placeholder?: string;
  options?: any[];
  onChange: (value: string) => void;
  value: string;
  helperText?: string;
  error?: string;
  style?: any;
  selectBoxStyle?: any;
}

const Select: React.FC<SelectProps> = ({
  label,
  leftIconName,
  name,
  placeholder,
  options = [],
  onChange = () => {},
  style,
  helperText,
  error,
  value,
  selectBoxStyle,
  ...props
}) => {
  const [isFocus, setIsFocus] = useState(false);
  const item = useMemo(
    () => options.find((item) => item.value === value),
    [options, value],
  );

  return (
    <View style={styles.container}>
      <View style={style}>
        {label && <ThemeText size="sm">{label}</ThemeText>}
        <Dropdown
          style={[
            styles.dropdown,
            isFocus && { borderBottomColor: '#212121' },
            selectBoxStyle,
          ]}
          placeholderStyle={styles.placeholderStyle}
          selectedTextStyle={styles.selectedTextStyle}
          inputSearchStyle={styles.inputSearchStyle}
          iconStyle={styles.iconStyle}
          data={options}
          search={true}
          maxHeight={300}
          value={item}
          labelField="label"
          valueField="label"
          placeholder={!isFocus ? `Select ${placeholder}` : '...'}
          searchPlaceholder="Search..."
          onFocus={() => setIsFocus(true)}
          onBlur={() => setIsFocus(false)}
          onChange={(item) => {
            onChange(item.value);
          }}
          renderLeftIcon={() => (
            <Icon
              style={styles.icon}
              color={isFocus ? '#212121' : '#9E9E9E'}
              name={leftIconName}
              size={20}
            />
          )}
          {...props}
        />
      </View>
      {helperText && (
        <ThemeText size="sm" color="#868E96">
          {helperText}
        </ThemeText>
      )}
      {error && (
        <ThemeText size="sm" color="#E03131">
          {error}
        </ThemeText>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    marginBottom: 16,
    marginTop: 4,
    position: 'relative',
  },
  dropdown: {
    height: 40,
    borderWidth: 1,
    borderColor: '#E8ECF4',
    borderRadius: 6,
    paddingHorizontal: 4,
  },
  icon: {
    marginRight: 5,
  },
  selectedTextStyle: {
    fontSize: 16,
    fontFamily: themeStyles.fontFamily.regular,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontFamily: themeStyles.fontFamily.regular,
  },
  placeholderStyle: {
    fontFamily: themeStyles.fontFamily.regular,
    fontSize: 15,
  },
});

export default Select;
