import { analytics } from '@appmaker-xyz/core';
import { logEvent } from './lib';
import { shopifyIdHelper } from '@appmaker-xyz/shopify';
let facebook_content_type = 'product';

export function setContentType(_type) {
  facebook_content_type = _type;
}

function getFacebookContentType() {
  return facebook_content_type || 'product';
}
const eventsHandler = {
  product_added_to_cart: (event, params, context) => {
    const contentType = getFacebookContentType();
    logEvent(
      'fb_mobile_add_to_cart',
      params?.price,
      {
        item_name: params?.item_name,
        fb_description: params?.item_name,
        fb_currency: params?.currency,
        value: params?.price,
        fb_content_type: contentType,
        fb_content_id:
          contentType === 'product'
            ? shopifyIdHelper(params?.variant_id, true)
            : shopifyIdHelper(params?.item_id, true),
      },
      context.product,
    );
  },
  product_added_to_wishlist: (event, params, context) => {
    const contentType = getFacebookContentType();
    logEvent(
      'fb_mobile_add_to_wishlist',
      params?.price,
      {
        item_name: params?.item_name,
        fb_description: params?.item_name,
        fb_currency: params?.currency,
        value: params?.price,
        fb_content_type: contentType,
        fb_content_id:
          contentType === 'product'
            ? shopifyIdHelper(params?.variant_id, true)
            : shopifyIdHelper(params?.item_id, true),
      },
      context.product,
    );
  },
  product_search: (event, params, context) => {
    const contentType = getFacebookContentType();
    logEvent('fb_mobile_search', null, {
      fb_search_string: params?.query,
      fb_content_type: contentType,
    });
  },
  product_viewed: (event, params, context) => {
    const contentType = getFacebookContentType();
    logEvent(
      'fb_mobile_content_view',
      params?.price,
      {
        item_name: params?.item_name,
        fb_description: params?.item_name,
        fb_currency: params?.currency,
        value: params?.price,
        fb_content_type: contentType,
        fb_content_id:
          contentType === 'product'
            ? shopifyIdHelper(params?.variant_id, true)
            : shopifyIdHelper(params?.item_id, true),
      },
      context?.product,
    );
  },
  checkout_started: (event, params, context) => {
    const contentType = getFacebookContentType();
    const contentIds = JSON.stringify(
      context?.cart?.lineItems?.edges.map(({ node }) =>
        contentType === 'product'
          ? shopifyIdHelper(node?.variant?.id, true)
          : shopifyIdHelper(node?.variant?.product?.id, true),
      ),
    );
    logEvent(
      'fb_mobile_initiated_checkout',
      params?.total_amount,
      {
        fb_content_type: contentType,
        fb_currency: params?.currency,
        value: params?.total_amount,
        fb_content_id: contentIds,
        fb_num_items: params?.products_count,
      },
      context?.cart,
    );
  },
  checkout_completed: (event, params, context) => {
    const contentType = getFacebookContentType();
    const contentIds = JSON.stringify(
      context?.cart?.lineItems?.edges.map(({ node }) =>
        contentType === 'product'
          ? shopifyIdHelper(node?.variant?.id, true)
          : shopifyIdHelper(node?.variant?.product?.id, true),
      ),
    );
    logEvent(
      'fb_mobile_purchase',
      params?.total_amount,
      {
        fb_content_type: contentType,
        fb_currency: params?.currency,
        value: params?.total_amount,
        fb_content_id: contentIds,
        fb_num_items: params?.products_count,
      },
      context?.cart,
    );
  },
  track: (event, params, context) => {
    // console.log('tracking-tracking-tracking-tracking-tracking-tracking');
    // logEvent(event, params, context);
  },
};

export function activateAnalyticsEvents() {
  analytics.onTrack((event, params, context) => {
    const eventFn = eventsHandler[event] || eventsHandler.track;
    if (eventFn) {
      eventFn(event, params, context);
    }
  }, 'facebook-analytics');
}
