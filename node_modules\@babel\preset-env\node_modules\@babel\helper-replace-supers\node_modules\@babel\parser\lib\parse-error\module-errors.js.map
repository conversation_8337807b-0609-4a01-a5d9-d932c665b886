{"version": 3, "names": ["_parseError", "require", "_default", "ImportMetaOutsideModule", "message", "code", "ParseErrorCode", "SourceTypeModuleError", "ImportOutsideModule", "exports", "default"], "sources": ["../../src/parse-error/module-errors.ts"], "sourcesContent": ["import { ParseErrorCode } from \"../parse-error\";\n\nexport default {\n  ImportMetaOutsideModule: {\n    message: `import.meta may appear only with 'sourceType: \"module\"'`,\n    code: ParseErrorCode.SourceTypeModuleError,\n  },\n  ImportOutsideModule: {\n    message: `'import' and 'export' may appear only with 'sourceType: \"module\"'`,\n    code: ParseErrorCode.SourceTypeModuleError,\n  },\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,WAAA,GAAAC,OAAA;AAAgD,IAAAC,QAAA,GAEjC;EACbC,uBAAuB,EAAE;IACvBC,OAAO,EAAG,yDAAwD;IAClEC,IAAI,EAAEC,0BAAc,CAACC;EACvB,CAAC;EACDC,mBAAmB,EAAE;IACnBJ,OAAO,EAAG,mEAAkE;IAC5EC,IAAI,EAAEC,0BAAc,CAACC;EACvB;AACF,CAAC;AAAAE,OAAA,CAAAC,OAAA,GAAAR,QAAA"}