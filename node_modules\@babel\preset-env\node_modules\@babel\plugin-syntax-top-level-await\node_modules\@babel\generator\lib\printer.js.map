{"version": 3, "names": ["_buffer", "require", "n", "_t", "generatorFunctions", "isFunction", "isStatement", "isClassBody", "isTSInterfaceBody", "isTSEnumDeclaration", "SCIENTIFIC_NOTATION", "ZERO_DECIMAL_INTEGER", "NON_DECIMAL_LITERAL", "PURE_ANNOTATION_RE", "HAS_NEWLINE", "HAS_BlOCK_COMMENT_END", "needsParens", "Printer", "constructor", "format", "map", "inForStatementInitCounter", "_printStack", "_indent", "_indentChar", "_indentRepeat", "_insideAux", "_parenPushNewlineState", "_noLineTerminator", "_printAuxAfterOnNextUserNode", "_printedComments", "Set", "_endsWithInteger", "_endsWithWord", "_lastCommentLine", "_endsWithInnerRaw", "_indentInnerComments", "_buf", "<PERSON><PERSON><PERSON>", "indent", "style", "charCodeAt", "length", "_inputMap", "generate", "ast", "print", "_maybe<PERSON>dd<PERSON>uxComment", "get", "compact", "concise", "dedent", "semicolon", "force", "_appendChar", "_queue", "rightBrace", "minified", "removeLastSemicolon", "token", "space", "_space", "<PERSON><PERSON><PERSON><PERSON>", "lastCp", "getLastChar", "word", "str", "noLineTerminatorAfter", "_maybePrintInnerComments", "endsWith", "_append", "number", "Number", "isInteger", "test", "maybeNewline", "lastChar", "str<PERSON><PERSON><PERSON>", "tokenChar", "char", "newline", "i", "retainLines", "getNewlineCount", "j", "_newline", "endsWithCharAndNewline", "removeTrailingNewline", "exactSource", "loc", "cb", "_catchUp", "source", "prop", "sourceWithOffset", "lineOffset", "columnOffset", "withSource", "sourceIdentifierName", "identifierName", "pos", "_canMarkIdName", "sourcePosition", "_sourcePosition", "identifierNamePos", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_maybeIndent", "append", "_maybeAddParenChar", "appendChar", "queue", "firstChar", "queueIndentation", "_getIndent", "_shouldIndent", "parenPushNewlineState", "printed", "len", "cha", "chaPost", "slice", "catchUp", "line", "count", "getCurrentLine", "printTerminatorless", "node", "parent", "isLabel", "terminatorState", "trailingCommentsLineOffset", "forceParens", "nodeType", "type", "oldConcise", "_compact", "printMethod", "undefined", "ReferenceError", "JSON", "stringify", "name", "push", "oldInAux", "shouldPrintParens", "retainFunctionParens", "extra", "parenthesized", "_printLeadingComments", "bind", "_printTrailingComments", "pop", "enteredPositionlessNode", "_printAuxBeforeComment", "_printAuxAfterComment", "comment", "auxiliaryCommentBefore", "_printComment", "value", "auxiliaryCommentAfter", "getPossibleRaw", "raw", "rawValue", "printJoin", "nodes", "opts", "newlineOpts", "addNewlines", "nextNodeStartLine", "separator", "statement", "_printNewline", "iterator", "_nextNode$loc", "nextNode", "start", "printAndIndentOnComments", "leadingComments", "printBlock", "body", "innerComments", "trailingComments", "_printComments", "comments", "printInnerComments", "hasSpace", "printedCommentsCount", "size", "noIndentInnerCommentsHere", "printSequence", "printList", "items", "commaSeparator", "newLine", "startLine", "lastCommentLine", "offset", "_should<PERSON>rintComment", "ignore", "has", "add", "shouldPrintComment", "skipNewLines", "noLineTerminator", "isBlockComment", "printNewLines", "lastCharCode", "val", "adjustMultilineComment", "_comment$loc", "column", "newlineRegex", "RegExp", "replace", "indentSize", "getCurrentColumn", "repeat", "nodeLoc", "hasLoc", "nodeStartLine", "nodeEndLine", "end", "lastLine", "leadingCommentNewline", "should<PERSON><PERSON>t", "commentStartLine", "commentEndLine", "Math", "max", "min", "singleLine", "shouldSkipNewline", "properties", "Object", "assign", "prototype", "Noop", "_default", "exports", "default"], "sources": ["../src/printer.ts"], "sourcesContent": ["import Buffer, { type Po<PERSON> } from \"./buffer\";\nimport type { Loc } from \"./buffer\";\nimport * as n from \"./node\";\nimport type * as t from \"@babel/types\";\nimport {\n  isFunction,\n  isStatement,\n  isClassBody,\n  isTSInterfaceBody,\n  isTSEnumDeclaration,\n} from \"@babel/types\";\nimport type {\n  RecordAndTuplePluginOptions,\n  PipelineOperatorPluginOptions,\n} from \"@babel/parser\";\nimport type { Opts as jsescOptions } from \"jsesc\";\n\nimport * as generatorFunctions from \"./generators\";\nimport type SourceMap from \"./source-map\";\nimport * as charCodes from \"charcodes\";\nimport { type TraceMap } from \"@jridgewell/trace-mapping\";\n\nconst SCIENTIFIC_NOTATION = /e/i;\nconst ZERO_DECIMAL_INTEGER = /\\.0+$/;\nconst NON_DECIMAL_LITERAL = /^0[box]/;\nconst PURE_ANNOTATION_RE = /^\\s*[@#]__PURE__\\s*$/;\nconst HAS_NEWLINE = /[\\n\\r\\u2028\\u2029]/;\nconst HAS_BlOCK_COMMENT_END = /\\*\\//;\n\nconst { needsParens } = n;\n\nconst enum COMMENT_TYPE {\n  LEADING,\n  INNER,\n  TRAILING,\n}\n\nconst enum COMMENT_SKIP_NEWLINE {\n  DEFAULT,\n  ALL,\n  LEADING,\n  TRAILING,\n}\n\nconst enum PRINT_COMMENT_HINT {\n  SKIP,\n  ALLOW,\n  DEFER,\n}\n\nexport type Format = {\n  shouldPrintComment: (comment: string) => boolean;\n  retainLines: boolean;\n  retainFunctionParens: boolean;\n  comments: boolean;\n  auxiliaryCommentBefore: string;\n  auxiliaryCommentAfter: string;\n  compact: boolean | \"auto\";\n  minified: boolean;\n  concise: boolean;\n  indent: {\n    adjustMultilineComment: boolean;\n    style: string;\n  };\n  recordAndTupleSyntaxType: RecordAndTuplePluginOptions[\"syntaxType\"];\n  jsescOption: jsescOptions;\n  /**\n   * @deprecated Removed in Babel 8, use `jsescOption` instead\n   */\n  jsonCompatibleStrings?: boolean;\n  /**\n   * For use with the Hack-style pipe operator.\n   * Changes what token is used for pipe bodies’ topic references.\n   */\n  topicToken?: PipelineOperatorPluginOptions[\"topicToken\"];\n  /**\n   * @deprecated Removed in Babel 8\n   */\n  decoratorsBeforeExport?: boolean;\n};\n\ninterface AddNewlinesOptions {\n  addNewlines(leading: boolean, node: t.Node): number;\n  nextNodeStartLine: number;\n}\n\ninterface PrintSequenceOptions extends Partial<AddNewlinesOptions> {\n  statement?: boolean;\n  indent?: boolean;\n  trailingCommentsLineOffset?: number;\n}\n\ninterface PrintListOptions {\n  separator?: (this: Printer) => void;\n  iterator?: (node: t.Node, index: number) => void;\n  statement?: boolean;\n  indent?: boolean;\n}\n\nexport type PrintJoinOptions = PrintListOptions & PrintSequenceOptions;\nclass Printer {\n  constructor(format: Format, map: SourceMap) {\n    this.format = format;\n    this._buf = new Buffer(map);\n\n    this._indentChar = format.indent.style.charCodeAt(0);\n    this._indentRepeat = format.indent.style.length;\n\n    this._inputMap = map?._inputMap;\n  }\n  declare _inputMap: TraceMap;\n\n  declare format: Format;\n  inForStatementInitCounter: number = 0;\n\n  declare _buf: Buffer;\n  _printStack: Array<t.Node> = [];\n  _indent: number = 0;\n  _indentChar: number = 0;\n  _indentRepeat: number = 0;\n  _insideAux: boolean = false;\n  _parenPushNewlineState: { printed: boolean } | null = null;\n  _noLineTerminator: boolean = false;\n  _printAuxAfterOnNextUserNode: boolean = false;\n  _printedComments = new Set<t.Comment>();\n  _endsWithInteger = false;\n  _endsWithWord = false;\n  _lastCommentLine = 0;\n  _endsWithInnerRaw: boolean = false;\n  _indentInnerComments: boolean = true;\n\n  generate(ast: t.Node) {\n    this.print(ast);\n    this._maybeAddAuxComment();\n\n    return this._buf.get();\n  }\n\n  /**\n   * Increment indent size.\n   */\n\n  indent(): void {\n    if (this.format.compact || this.format.concise) return;\n\n    this._indent++;\n  }\n\n  /**\n   * Decrement indent size.\n   */\n\n  dedent(): void {\n    if (this.format.compact || this.format.concise) return;\n\n    this._indent--;\n  }\n\n  /**\n   * Add a semicolon to the buffer.\n   */\n\n  semicolon(force: boolean = false): void {\n    this._maybeAddAuxComment();\n    if (force) {\n      this._appendChar(charCodes.semicolon);\n    } else {\n      this._queue(charCodes.semicolon);\n    }\n    this._noLineTerminator = false;\n  }\n\n  /**\n   * Add a right brace to the buffer.\n   */\n\n  rightBrace(): void {\n    if (this.format.minified) {\n      this._buf.removeLastSemicolon();\n    }\n    this.token(\"}\");\n  }\n\n  /**\n   * Add a space to the buffer unless it is compact.\n   */\n\n  space(force: boolean = false): void {\n    if (this.format.compact) return;\n\n    if (force) {\n      this._space();\n    } else if (this._buf.hasContent()) {\n      const lastCp = this.getLastChar();\n      if (lastCp !== charCodes.space && lastCp !== charCodes.lineFeed) {\n        this._space();\n      }\n    }\n  }\n\n  /**\n   * Writes a token that can't be safely parsed without taking whitespace into account.\n   */\n\n  word(str: string, noLineTerminatorAfter: boolean = false): void {\n    this._maybePrintInnerComments();\n\n    // prevent concatenating words and creating // comment out of division and regex\n    if (\n      this._endsWithWord ||\n      (str.charCodeAt(0) === charCodes.slash && this.endsWith(charCodes.slash))\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._append(str, false);\n\n    this._endsWithWord = true;\n    this._noLineTerminator = noLineTerminatorAfter;\n  }\n\n  /**\n   * Writes a number token so that we can validate if it is an integer.\n   */\n\n  number(str: string): void {\n    this.word(str);\n\n    // Integer tokens need special handling because they cannot have '.'s inserted\n    // immediately after them.\n    this._endsWithInteger =\n      Number.isInteger(+str) &&\n      !NON_DECIMAL_LITERAL.test(str) &&\n      !SCIENTIFIC_NOTATION.test(str) &&\n      !ZERO_DECIMAL_INTEGER.test(str) &&\n      str.charCodeAt(str.length - 1) !== charCodes.dot;\n  }\n\n  /**\n   * Writes a simple token.\n   */\n\n  token(str: string, maybeNewline = false): void {\n    this._maybePrintInnerComments();\n\n    // space is mandatory to avoid outputting <!--\n    // http://javascript.spec.whatwg.org/#comment-syntax\n    const lastChar = this.getLastChar();\n    const strFirst = str.charCodeAt(0);\n    if (\n      (lastChar === charCodes.exclamationMark && str === \"--\") ||\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (strFirst === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (strFirst === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (strFirst === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._append(str, maybeNewline);\n    this._noLineTerminator = false;\n  }\n\n  tokenChar(char: number): void {\n    this._maybePrintInnerComments();\n\n    // space is mandatory to avoid outputting <!--\n    // http://javascript.spec.whatwg.org/#comment-syntax\n    const lastChar = this.getLastChar();\n    if (\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (char === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (char === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (char === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._appendChar(char);\n    this._noLineTerminator = false;\n  }\n\n  /**\n   * Add a newline (or many newlines), maintaining formatting.\n   * This function checks the number of newlines in the queue and subtracts them.\n   * It currently has some limitations.\n   * @see {Buffer#getNewlineCount}\n   */\n  newline(i: number = 1, force?: boolean): void {\n    if (i <= 0) return;\n\n    if (!force) {\n      if (this.format.retainLines || this.format.compact) return;\n\n      if (this.format.concise) {\n        this.space();\n        return;\n      }\n    }\n\n    if (i > 2) i = 2; // Max two lines\n\n    i -= this._buf.getNewlineCount();\n\n    for (let j = 0; j < i; j++) {\n      this._newline();\n    }\n\n    return;\n  }\n\n  endsWith(char: number): boolean {\n    return this.getLastChar() === char;\n  }\n\n  getLastChar(): number {\n    return this._buf.getLastChar();\n  }\n\n  endsWithCharAndNewline(): number {\n    return this._buf.endsWithCharAndNewline();\n  }\n\n  removeTrailingNewline(): void {\n    this._buf.removeTrailingNewline();\n  }\n\n  exactSource(loc: Loc | undefined, cb: () => void) {\n    if (!loc) {\n      cb();\n      return;\n    }\n\n    this._catchUp(\"start\", loc);\n\n    this._buf.exactSource(loc, cb);\n  }\n\n  source(prop: \"start\" | \"end\", loc: Loc | undefined): void {\n    if (!loc) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.source(prop, loc);\n  }\n\n  sourceWithOffset(\n    prop: \"start\" | \"end\",\n    loc: Loc | undefined,\n    lineOffset: number,\n    columnOffset: number,\n  ): void {\n    if (!loc) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.sourceWithOffset(prop, loc, lineOffset, columnOffset);\n  }\n\n  withSource(\n    prop: \"start\" | \"end\",\n    loc: Loc | undefined,\n    cb: () => void,\n  ): void {\n    if (!loc) {\n      cb();\n      return;\n    }\n\n    this._catchUp(prop, loc);\n\n    this._buf.withSource(prop, loc, cb);\n  }\n\n  sourceIdentifierName(identifierName: string, pos?: Pos): void {\n    if (!this._buf._canMarkIdName) return;\n\n    const sourcePosition = this._buf._sourcePosition;\n    sourcePosition.identifierNamePos = pos;\n    sourcePosition.identifierName = identifierName;\n  }\n\n  _space(): void {\n    this._queue(charCodes.space);\n  }\n\n  _newline(): void {\n    this._queue(charCodes.lineFeed);\n  }\n\n  _append(str: string, maybeNewline: boolean): void {\n    this._maybeAddParen(str);\n    this._maybeIndent(str.charCodeAt(0));\n\n    this._buf.append(str, maybeNewline);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _appendChar(char: number): void {\n    this._maybeAddParenChar(char);\n    this._maybeIndent(char);\n\n    this._buf.appendChar(char);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _queue(char: number) {\n    this._maybeAddParenChar(char);\n    this._maybeIndent(char);\n\n    this._buf.queue(char);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _maybeIndent(firstChar: number): void {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      this._buf.queueIndentation(this._indentChar, this._getIndent());\n    }\n  }\n\n  _shouldIndent(firstChar: number) {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      return true;\n    }\n  }\n\n  _maybeAddParenChar(char: number): void {\n    // see startTerminatorless() instance method\n    const parenPushNewlineState = this._parenPushNewlineState;\n    if (!parenPushNewlineState) return;\n\n    // This function does two things:\n    // - If needed, prints a parenthesis\n    // - If the currently printed string removes the need for the paren,\n    //   it resets the _parenPushNewlineState field.\n    //   Almost everything removes the need for a paren, except for\n    //   comments and whitespaces.\n\n    if (char === charCodes.space) {\n      // Whitespaces only, the parentheses might still be needed.\n      return;\n    }\n\n    // Check for newline or comment.\n    if (char !== charCodes.lineFeed) {\n      this._parenPushNewlineState = null;\n      return;\n    }\n\n    this.token(\"(\");\n    this.indent();\n    parenPushNewlineState.printed = true;\n  }\n\n  _maybeAddParen(str: string): void {\n    // see startTerminatorless() instance method\n    const parenPushNewlineState = this._parenPushNewlineState;\n    if (!parenPushNewlineState) return;\n\n    // This function does two things:\n    // - If needed, prints a parenthesis\n    // - If the currently printed string removes the need for the paren,\n    //   it resets the _parenPushNewlineState field.\n    //   Almost everything removes the need for a paren, except for\n    //   comments and whitespaces.\n\n    const len = str.length;\n\n    let i;\n    for (i = 0; i < len && str.charCodeAt(i) === charCodes.space; i++) continue;\n    if (i === len) {\n      // Whitespaces only, the parentheses might still be needed.\n      return;\n    }\n\n    // Check for newline or comment.\n    const cha = str.charCodeAt(i);\n    if (cha !== charCodes.lineFeed) {\n      if (\n        // This is not a comment (it doesn't start with /)\n        cha !== charCodes.slash ||\n        // This is not a comment (it's a / operator)\n        i + 1 === len\n      ) {\n        // After a normal token, the parentheses aren't needed anymore\n        this._parenPushNewlineState = null;\n        return;\n      }\n\n      const chaPost = str.charCodeAt(i + 1);\n\n      if (chaPost === charCodes.asterisk) {\n        // This is a block comment\n\n        if (PURE_ANNOTATION_RE.test(str.slice(i + 2, len - 2))) {\n          // We avoid printing newlines after #__PURE__ comments (we treat\n          // then as unary operators), but we must keep the old\n          // parenPushNewlineState because, if a newline was forbidden, it is\n          // still forbidden after the comment.\n          return;\n        }\n\n        // NOTE: code flow continues from here to after these if/elses\n      } else if (chaPost !== charCodes.slash) {\n        // This is neither a block comment, nor a line comment.\n        // After a normal token, the parentheses aren't needed anymore\n        this._parenPushNewlineState = null;\n        return;\n      }\n    }\n\n    this.token(\"(\");\n    this.indent();\n    parenPushNewlineState.printed = true;\n  }\n\n  catchUp(line: number) {\n    if (!this.format.retainLines) return;\n\n    // catch up to this nodes newline if we're behind\n    const count = line - this._buf.getCurrentLine();\n\n    for (let i = 0; i < count; i++) {\n      this._newline();\n    }\n  }\n\n  _catchUp(prop: \"start\" | \"end\", loc?: Loc) {\n    if (!this.format.retainLines) return;\n\n    // catch up to this nodes newline if we're behind\n    const pos = loc ? loc[prop] : null;\n    if (pos?.line != null) {\n      const count = pos.line - this._buf.getCurrentLine();\n\n      for (let i = 0; i < count; i++) {\n        this._newline();\n      }\n    }\n  }\n\n  /**\n   * Get the current indent.\n   */\n\n  _getIndent(): number {\n    return this._indentRepeat * this._indent;\n  }\n\n  printTerminatorless(node: t.Node, parent: t.Node, isLabel: boolean) {\n    /**\n     * Set some state that will be modified if a newline has been inserted before any\n     * non-space characters.\n     *\n     * This is to prevent breaking semantics for terminatorless separator nodes. eg:\n     *\n     *   return foo;\n     *\n     * returns `foo`. But if we do:\n     *\n     *   return\n     *   foo;\n     *\n     *  `undefined` will be returned and not `foo` due to the terminator.\n     */\n    if (isLabel) {\n      this._noLineTerminator = true;\n      this.print(node, parent);\n    } else {\n      const terminatorState = {\n        printed: false,\n      };\n      this._parenPushNewlineState = terminatorState;\n      this.print(node, parent);\n      /**\n       * Print an ending parentheses if a starting one has been printed.\n       */\n      if (terminatorState.printed) {\n        this.dedent();\n        this.newline();\n        this.token(\")\");\n      }\n    }\n  }\n\n  print(\n    node: t.Node | null,\n    parent?: t.Node,\n    noLineTerminatorAfter?: boolean,\n    // trailingCommentsLineOffset also used to check if called from printJoin\n    // it will be ignored if `noLineTerminatorAfter||this._noLineTerminator`\n    trailingCommentsLineOffset?: number,\n    forceParens?: boolean,\n  ) {\n    if (!node) return;\n\n    this._endsWithInnerRaw = false;\n\n    const nodeType = node.type;\n    const format = this.format;\n\n    const oldConcise = format.concise;\n    if (\n      // @ts-expect-error document _compact AST properties\n      node._compact\n    ) {\n      format.concise = true;\n    }\n\n    const printMethod =\n      this[\n        nodeType as Exclude<\n          t.Node[\"type\"],\n          // removed\n          | \"Noop\"\n          // renamed\n          | t.DeprecatedAliases[\"type\"]\n        >\n      ];\n    if (printMethod === undefined) {\n      throw new ReferenceError(\n        `unknown node of type ${JSON.stringify(\n          nodeType,\n        )} with constructor ${JSON.stringify(node.constructor.name)}`,\n      );\n    }\n\n    this._printStack.push(node);\n\n    const oldInAux = this._insideAux;\n    this._insideAux = node.loc == undefined;\n    this._maybeAddAuxComment(this._insideAux && !oldInAux);\n\n    let shouldPrintParens = false;\n    if (forceParens) {\n      shouldPrintParens = true;\n    } else if (\n      format.retainFunctionParens &&\n      nodeType === \"FunctionExpression\" &&\n      node.extra &&\n      node.extra.parenthesized\n    ) {\n      shouldPrintParens = true;\n    } else {\n      shouldPrintParens = needsParens(node, parent, this._printStack);\n    }\n    if (shouldPrintParens) {\n      this.token(\"(\");\n      this._endsWithInnerRaw = false;\n    }\n\n    this._lastCommentLine = 0;\n\n    this._printLeadingComments(node, parent);\n\n    const loc = nodeType === \"Program\" || nodeType === \"File\" ? null : node.loc;\n\n    this.exactSource(loc, printMethod.bind(this, node, parent));\n\n    if (shouldPrintParens) {\n      this._printTrailingComments(node, parent);\n      this.token(\")\");\n      this._noLineTerminator = noLineTerminatorAfter;\n    } else if (noLineTerminatorAfter && !this._noLineTerminator) {\n      this._noLineTerminator = true;\n      this._printTrailingComments(node, parent);\n    } else {\n      this._printTrailingComments(node, parent, trailingCommentsLineOffset);\n    }\n\n    // end\n    this._printStack.pop();\n\n    format.concise = oldConcise;\n    this._insideAux = oldInAux;\n\n    this._endsWithInnerRaw = false;\n  }\n\n  _maybeAddAuxComment(enteredPositionlessNode?: boolean) {\n    if (enteredPositionlessNode) this._printAuxBeforeComment();\n    if (!this._insideAux) this._printAuxAfterComment();\n  }\n\n  _printAuxBeforeComment() {\n    if (this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = true;\n\n    const comment = this.format.auxiliaryCommentBefore;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  _printAuxAfterComment() {\n    if (!this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = false;\n\n    const comment = this.format.auxiliaryCommentAfter;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  getPossibleRaw(\n    node:\n      | t.StringLiteral\n      | t.NumericLiteral\n      | t.BigIntLiteral\n      | t.DecimalLiteral\n      | t.DirectiveLiteral\n      | t.JSXText,\n  ): string | undefined {\n    const extra = node.extra;\n    if (\n      extra &&\n      extra.raw != null &&\n      extra.rawValue != null &&\n      node.value === extra.rawValue\n    ) {\n      // @ts-expect-error: The extra.raw of these AST node types must be a string\n      return extra.raw;\n    }\n  }\n\n  printJoin(\n    nodes: Array<t.Node> | undefined | null,\n    parent: t.Node,\n    opts: PrintJoinOptions = {},\n  ) {\n    if (!nodes?.length) return;\n\n    if (opts.indent) this.indent();\n\n    const newlineOpts: AddNewlinesOptions = {\n      addNewlines: opts.addNewlines,\n      nextNodeStartLine: 0,\n    };\n\n    const separator = opts.separator ? opts.separator.bind(this) : null;\n\n    const len = nodes.length;\n    for (let i = 0; i < len; i++) {\n      const node = nodes[i];\n      if (!node) continue;\n\n      if (opts.statement) this._printNewline(i === 0, newlineOpts);\n\n      this.print(node, parent, undefined, opts.trailingCommentsLineOffset || 0);\n\n      opts.iterator?.(node, i);\n\n      if (i < len - 1) separator?.();\n\n      if (opts.statement) {\n        if (i + 1 === len) {\n          this.newline(1);\n        } else {\n          const nextNode = nodes[i + 1];\n          newlineOpts.nextNodeStartLine = nextNode.loc?.start.line || 0;\n\n          this._printNewline(true, newlineOpts);\n        }\n      }\n    }\n\n    if (opts.indent) this.dedent();\n  }\n\n  printAndIndentOnComments(node: t.Node, parent: t.Node) {\n    const indent = node.leadingComments && node.leadingComments.length > 0;\n    if (indent) this.indent();\n    this.print(node, parent);\n    if (indent) this.dedent();\n  }\n\n  printBlock(parent: Extract<t.Node, { body: t.Statement }>) {\n    const node = parent.body;\n\n    if (node.type !== \"EmptyStatement\") {\n      this.space();\n    }\n\n    this.print(node, parent);\n  }\n\n  _printTrailingComments(node: t.Node, parent?: t.Node, lineOffset?: number) {\n    const { innerComments, trailingComments } = node;\n    // We print inner comments here, so that if for some reason they couldn't\n    // be printed in earlier locations they are still printed *somewhere*,\n    // even if at the end of the node.\n    if (innerComments?.length) {\n      this._printComments(\n        COMMENT_TYPE.TRAILING,\n        innerComments,\n        node,\n        parent,\n        lineOffset,\n      );\n    }\n    if (trailingComments?.length) {\n      this._printComments(\n        COMMENT_TYPE.TRAILING,\n        trailingComments,\n        node,\n        parent,\n        lineOffset,\n      );\n    }\n  }\n\n  _printLeadingComments(node: t.Node, parent: t.Node) {\n    const comments = node.leadingComments;\n    if (!comments?.length) return;\n    this._printComments(COMMENT_TYPE.LEADING, comments, node, parent);\n  }\n\n  _maybePrintInnerComments() {\n    if (this._endsWithInnerRaw) this.printInnerComments();\n    this._endsWithInnerRaw = true;\n    this._indentInnerComments = true;\n  }\n\n  printInnerComments() {\n    const node = this._printStack[this._printStack.length - 1];\n    const comments = node.innerComments;\n    if (!comments?.length) return;\n\n    const hasSpace = this.endsWith(charCodes.space);\n    const indent = this._indentInnerComments;\n    const printedCommentsCount = this._printedComments.size;\n    if (indent) this.indent();\n    this._printComments(COMMENT_TYPE.INNER, comments, node);\n    if (hasSpace && printedCommentsCount !== this._printedComments.size) {\n      this.space();\n    }\n    if (indent) this.dedent();\n  }\n\n  noIndentInnerCommentsHere() {\n    this._indentInnerComments = false;\n  }\n\n  printSequence(\n    nodes: t.Node[],\n    parent: t.Node,\n    opts: PrintSequenceOptions = {},\n  ) {\n    opts.statement = true;\n    this.printJoin(nodes, parent, opts);\n  }\n\n  printList(items: t.Node[], parent: t.Node, opts: PrintListOptions = {}) {\n    if (opts.separator == null) {\n      opts.separator = commaSeparator;\n    }\n\n    this.printJoin(items, parent, opts);\n  }\n\n  _printNewline(newLine: boolean, opts: AddNewlinesOptions) {\n    // Fast path since 'this.newline' does nothing when not tracking lines.\n    if (this.format.retainLines || this.format.compact) return;\n\n    // Fast path for concise since 'this.newline' just inserts a space when\n    // concise formatting is in use.\n    if (this.format.concise) {\n      this.space();\n      return;\n    }\n\n    if (!newLine) {\n      return;\n    }\n\n    const startLine = opts.nextNodeStartLine;\n    const lastCommentLine = this._lastCommentLine;\n    if (startLine > 0 && lastCommentLine > 0) {\n      const offset = startLine - lastCommentLine;\n      if (offset >= 0) {\n        this.newline(offset || 1);\n        return;\n      }\n    }\n\n    // don't add newlines at the beginning of the file\n    if (this._buf.hasContent()) {\n      // Here is the logic of the original line wrapping according to the node layout, we are not using it now.\n      // We currently add at most one newline to each node in the list, ignoring `opts.addNewlines`.\n\n      // let lines = 0;\n      // if (!leading) lines++; // always include at least a single line after\n      // if (opts.addNewlines) lines += opts.addNewlines(leading, node) || 0;\n\n      // const needs = leading ? needsWhitespaceBefore : needsWhitespaceAfter;\n      // if (needs(node, parent)) lines++;\n\n      // this.newline(Math.min(2, lines));\n\n      this.newline(1);\n    }\n  }\n\n  // Returns `PRINT_COMMENT_HINT.DEFER` if the comment cannot be printed in this position due to\n  // line terminators, signaling that the print comments loop can stop and\n  // resume printing comments at the next possible position. This happens when\n  // printing inner comments, since if we have an inner comment with a multiline\n  // there is at least one inner position where line terminators are allowed.\n  _shouldPrintComment(comment: t.Comment): PRINT_COMMENT_HINT {\n    // Some plugins (such as flow-strip-types) use this to mark comments as removed using the AST-root 'comments' property,\n    // where they can't manually mutate the AST node comment lists.\n    if (comment.ignore) return PRINT_COMMENT_HINT.SKIP;\n\n    if (this._printedComments.has(comment)) return PRINT_COMMENT_HINT.SKIP;\n\n    if (\n      this._noLineTerminator &&\n      (HAS_NEWLINE.test(comment.value) ||\n        HAS_BlOCK_COMMENT_END.test(comment.value))\n    ) {\n      return PRINT_COMMENT_HINT.DEFER;\n    }\n\n    this._printedComments.add(comment);\n\n    if (!this.format.shouldPrintComment(comment.value)) {\n      return PRINT_COMMENT_HINT.SKIP;\n    }\n\n    return PRINT_COMMENT_HINT.ALLOW;\n  }\n\n  _printComment(comment: t.Comment, skipNewLines: COMMENT_SKIP_NEWLINE) {\n    const noLineTerminator = this._noLineTerminator;\n    const isBlockComment = comment.type === \"CommentBlock\";\n\n    // Add a newline before and after a block comment, unless explicitly\n    // disallowed\n    const printNewLines =\n      isBlockComment &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.ALL &&\n      !this._noLineTerminator;\n\n    if (\n      printNewLines &&\n      this._buf.hasContent() &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.LEADING\n    ) {\n      this.newline(1);\n    }\n\n    const lastCharCode = this.getLastChar();\n    if (\n      lastCharCode !== charCodes.leftSquareBracket &&\n      lastCharCode !== charCodes.leftCurlyBrace\n    ) {\n      this.space();\n    }\n\n    let val;\n    if (isBlockComment) {\n      val = `/*${comment.value}*/`;\n      if (this.format.indent.adjustMultilineComment) {\n        const offset = comment.loc?.start.column;\n        if (offset) {\n          const newlineRegex = new RegExp(\"\\\\n\\\\s{1,\" + offset + \"}\", \"g\");\n          val = val.replace(newlineRegex, \"\\n\");\n        }\n\n        let indentSize = this.format.retainLines\n          ? 0\n          : this._buf.getCurrentColumn();\n\n        if (this._shouldIndent(charCodes.slash) || this.format.retainLines) {\n          indentSize += this._getIndent();\n        }\n\n        val = val.replace(/\\n(?!$)/g, `\\n${\" \".repeat(indentSize)}`);\n      }\n    } else if (!noLineTerminator) {\n      val = `//${comment.value}`;\n    } else {\n      // It was a single-line comment, so it's guaranteed to not\n      // contain newlines and it can be safely printed as a block\n      // comment.\n      val = `/*${comment.value}*/`;\n    }\n\n    // Avoid creating //* comments\n    if (this.endsWith(charCodes.slash)) this._space();\n\n    this.source(\"start\", comment.loc);\n    this._append(val, isBlockComment);\n\n    if (!isBlockComment && !noLineTerminator) {\n      this.newline(1, true);\n    }\n\n    if (printNewLines && skipNewLines !== COMMENT_SKIP_NEWLINE.TRAILING) {\n      this.newline(1);\n    }\n  }\n\n  _printComments(\n    type: COMMENT_TYPE,\n    comments: readonly t.Comment[],\n    node: t.Node,\n    parent?: t.Node,\n    lineOffset: number = 0,\n  ) {\n    const nodeLoc = node.loc;\n    const len = comments.length;\n    let hasLoc = !!nodeLoc;\n    const nodeStartLine = hasLoc ? nodeLoc.start.line : 0;\n    const nodeEndLine = hasLoc ? nodeLoc.end.line : 0;\n    let lastLine = 0;\n    let leadingCommentNewline = 0;\n\n    const maybeNewline = this._noLineTerminator\n      ? function () {}\n      : this.newline.bind(this);\n\n    for (let i = 0; i < len; i++) {\n      const comment = comments[i];\n\n      const shouldPrint = this._shouldPrintComment(comment);\n      if (shouldPrint === PRINT_COMMENT_HINT.DEFER) {\n        hasLoc = false;\n        break;\n      }\n      if (hasLoc && comment.loc && shouldPrint === PRINT_COMMENT_HINT.ALLOW) {\n        const commentStartLine = comment.loc.start.line;\n        const commentEndLine = comment.loc.end.line;\n        if (type === COMMENT_TYPE.LEADING) {\n          let offset = 0;\n          if (i === 0) {\n            // Because currently we cannot handle blank lines before leading comments,\n            // we always wrap before and after multi-line comments.\n            if (\n              this._buf.hasContent() &&\n              (comment.type === \"CommentLine\" ||\n                commentStartLine != commentEndLine)\n            ) {\n              offset = leadingCommentNewline = 1;\n            }\n          } else {\n            offset = commentStartLine - lastLine;\n          }\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n\n          if (i + 1 === len) {\n            maybeNewline(\n              Math.max(nodeStartLine - lastLine, leadingCommentNewline),\n            );\n            lastLine = nodeStartLine;\n          }\n        } else if (type === COMMENT_TYPE.INNER) {\n          const offset =\n            commentStartLine - (i === 0 ? nodeStartLine : lastLine);\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n\n          if (i + 1 === len) {\n            maybeNewline(Math.min(1, nodeEndLine - lastLine)); // TODO: Improve here when inner comments processing is stronger\n            lastLine = nodeEndLine;\n          }\n        } else {\n          const offset =\n            commentStartLine - (i === 0 ? nodeEndLine - lineOffset : lastLine);\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n        }\n      } else {\n        hasLoc = false;\n        if (shouldPrint !== PRINT_COMMENT_HINT.ALLOW) {\n          continue;\n        }\n\n        if (len === 1) {\n          const singleLine = comment.loc\n            ? comment.loc.start.line === comment.loc.end.line\n            : !HAS_NEWLINE.test(comment.value);\n\n          const shouldSkipNewline =\n            singleLine &&\n            !isStatement(node) &&\n            !isClassBody(parent) &&\n            !isTSInterfaceBody(parent) &&\n            !isTSEnumDeclaration(parent);\n\n          if (type === COMMENT_TYPE.LEADING) {\n            this._printComment(\n              comment,\n              (shouldSkipNewline && node.type !== \"ObjectExpression\") ||\n                (singleLine && isFunction(parent, { body: node }))\n                ? COMMENT_SKIP_NEWLINE.ALL\n                : COMMENT_SKIP_NEWLINE.DEFAULT,\n            );\n          } else if (shouldSkipNewline && type === COMMENT_TYPE.TRAILING) {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n          } else {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n          }\n        } else if (\n          type === COMMENT_TYPE.INNER &&\n          !(node.type === \"ObjectExpression\" && node.properties.length > 1) &&\n          node.type !== \"ClassBody\" &&\n          node.type !== \"TSInterfaceBody\"\n        ) {\n          // class X {\n          //   /*:: a: number*/\n          //   /*:: b: ?string*/\n          // }\n\n          this._printComment(\n            comment,\n            i === 0\n              ? COMMENT_SKIP_NEWLINE.LEADING\n              : i === len - 1\n              ? COMMENT_SKIP_NEWLINE.TRAILING\n              : COMMENT_SKIP_NEWLINE.DEFAULT,\n          );\n        } else {\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n        }\n      }\n    }\n\n    if (type === COMMENT_TYPE.TRAILING && hasLoc && lastLine) {\n      this._lastCommentLine = lastLine;\n    }\n  }\n}\n\n// Expose the node type functions and helpers on the prototype for easy usage.\nObject.assign(Printer.prototype, generatorFunctions);\n\nif (!process.env.BABEL_8_BREAKING) {\n  // @ts-ignore(Babel 7 vs Babel 8) Babel 7 has Noop print method\n  Printer.prototype.Noop = function Noop(this: Printer) {};\n}\n\ntype GeneratorFunctions = typeof generatorFunctions;\ninterface Printer extends GeneratorFunctions {}\nexport default Printer;\n\nfunction commaSeparator(this: Printer) {\n  this.token(\",\");\n  this.space();\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,CAAA,GAAAD,OAAA;AAEA,IAAAE,EAAA,GAAAF,OAAA;AAaA,IAAAG,kBAAA,GAAAH,OAAA;AAGAA,OAAA;AAA0D;EAfxDI,UAAU;EACVC,WAAW;EACXC,WAAW;EACXC,iBAAiB;EACjBC;AAAmB,IAAAN,EAAA;AAarB,MAAMO,mBAAmB,GAAG,IAAI;AAChC,MAAMC,oBAAoB,GAAG,OAAO;AACpC,MAAMC,mBAAmB,GAAG,SAAS;AACrC,MAAMC,kBAAkB,GAAG,sBAAsB;AACjD,MAAMC,WAAW,GAAG,oBAAoB;AACxC,MAAMC,qBAAqB,GAAG,MAAM;AAEpC,MAAM;EAAEC;AAAY,CAAC,GAAGd,CAAC;AAuEzB,MAAMe,OAAO,CAAC;EACZC,WAAWA,CAACC,MAAc,EAAEC,GAAc,EAAE;IAAA,KAY5CC,yBAAyB,GAAW,CAAC;IAAA,KAGrCC,WAAW,GAAkB,EAAE;IAAA,KAC/BC,OAAO,GAAW,CAAC;IAAA,KACnBC,WAAW,GAAW,CAAC;IAAA,KACvBC,aAAa,GAAW,CAAC;IAAA,KACzBC,UAAU,GAAY,KAAK;IAAA,KAC3BC,sBAAsB,GAAgC,IAAI;IAAA,KAC1DC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,4BAA4B,GAAY,KAAK;IAAA,KAC7CC,gBAAgB,GAAG,IAAIC,GAAG,EAAa;IAAA,KACvCC,gBAAgB,GAAG,KAAK;IAAA,KACxBC,aAAa,GAAG,KAAK;IAAA,KACrBC,gBAAgB,GAAG,CAAC;IAAA,KACpBC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,oBAAoB,GAAY,IAAI;IA3BlC,IAAI,CAACjB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACkB,IAAI,GAAG,IAAIC,eAAM,CAAClB,GAAG,CAAC;IAE3B,IAAI,CAACI,WAAW,GAAGL,MAAM,CAACoB,MAAM,CAACC,KAAK,CAACC,UAAU,CAAC,CAAC,CAAC;IACpD,IAAI,CAAChB,aAAa,GAAGN,MAAM,CAACoB,MAAM,CAACC,KAAK,CAACE,MAAM;IAE/C,IAAI,CAACC,SAAS,GAAGvB,GAAG,oBAAHA,GAAG,CAAEuB,SAAS;EACjC;EAsBAC,QAAQA,CAACC,GAAW,EAAE;IACpB,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC;IACf,IAAI,CAACE,mBAAmB,EAAE;IAE1B,OAAO,IAAI,CAACV,IAAI,CAACW,GAAG,EAAE;EACxB;EAMAT,MAAMA,CAAA,EAAS;IACb,IAAI,IAAI,CAACpB,MAAM,CAAC8B,OAAO,IAAI,IAAI,CAAC9B,MAAM,CAAC+B,OAAO,EAAE;IAEhD,IAAI,CAAC3B,OAAO,EAAE;EAChB;EAMA4B,MAAMA,CAAA,EAAS;IACb,IAAI,IAAI,CAAChC,MAAM,CAAC8B,OAAO,IAAI,IAAI,CAAC9B,MAAM,CAAC+B,OAAO,EAAE;IAEhD,IAAI,CAAC3B,OAAO,EAAE;EAChB;EAMA6B,SAASA,CAACC,KAAc,GAAG,KAAK,EAAQ;IACtC,IAAI,CAACN,mBAAmB,EAAE;IAC1B,IAAIM,KAAK,EAAE;MACT,IAAI,CAACC,WAAW,IAAqB;IACvC,CAAC,MAAM;MACL,IAAI,CAACC,MAAM,IAAqB;IAClC;IACA,IAAI,CAAC3B,iBAAiB,GAAG,KAAK;EAChC;EAMA4B,UAAUA,CAAA,EAAS;IACjB,IAAI,IAAI,CAACrC,MAAM,CAACsC,QAAQ,EAAE;MACxB,IAAI,CAACpB,IAAI,CAACqB,mBAAmB,EAAE;IACjC;IACA,IAAI,CAACC,SAAK,KAAK;EACjB;EAMAC,KAAKA,CAACP,KAAc,GAAG,KAAK,EAAQ;IAClC,IAAI,IAAI,CAAClC,MAAM,CAAC8B,OAAO,EAAE;IAEzB,IAAII,KAAK,EAAE;MACT,IAAI,CAACQ,MAAM,EAAE;IACf,CAAC,MAAM,IAAI,IAAI,CAACxB,IAAI,CAACyB,UAAU,EAAE,EAAE;MACjC,MAAMC,MAAM,GAAG,IAAI,CAACC,WAAW,EAAE;MACjC,IAAID,MAAM,OAAoB,IAAIA,MAAM,OAAuB,EAAE;QAC/D,IAAI,CAACF,MAAM,EAAE;MACf;IACF;EACF;EAMAI,IAAIA,CAACC,GAAW,EAAEC,qBAA8B,GAAG,KAAK,EAAQ;IAC9D,IAAI,CAACC,wBAAwB,EAAE;IAG/B,IACE,IAAI,CAACnC,aAAa,IACjBiC,GAAG,CAACzB,UAAU,CAAC,CAAC,CAAC,OAAoB,IAAI,IAAI,CAAC4B,QAAQ,IAAkB,EACzE;MACA,IAAI,CAACR,MAAM,EAAE;IACf;IAEA,IAAI,CAACd,mBAAmB,EAAE;IAC1B,IAAI,CAACuB,OAAO,CAACJ,GAAG,EAAE,KAAK,CAAC;IAExB,IAAI,CAACjC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACL,iBAAiB,GAAGuC,qBAAqB;EAChD;EAMAI,MAAMA,CAACL,GAAW,EAAQ;IACxB,IAAI,CAACD,IAAI,CAACC,GAAG,CAAC;IAId,IAAI,CAAClC,gBAAgB,GACnBwC,MAAM,CAACC,SAAS,CAAC,CAACP,GAAG,CAAC,IACtB,CAACtD,mBAAmB,CAAC8D,IAAI,CAACR,GAAG,CAAC,IAC9B,CAACxD,mBAAmB,CAACgE,IAAI,CAACR,GAAG,CAAC,IAC9B,CAACvD,oBAAoB,CAAC+D,IAAI,CAACR,GAAG,CAAC,IAC/BA,GAAG,CAACzB,UAAU,CAACyB,GAAG,CAACxB,MAAM,GAAG,CAAC,CAAC,OAAkB;EACpD;EAMAiB,KAAKA,CAACO,GAAW,EAAES,YAAY,GAAG,KAAK,EAAQ;IAC7C,IAAI,CAACP,wBAAwB,EAAE;IAI/B,MAAMQ,QAAQ,GAAG,IAAI,CAACZ,WAAW,EAAE;IACnC,MAAMa,QAAQ,GAAGX,GAAG,CAACzB,UAAU,CAAC,CAAC,CAAC;IAClC,IACGmC,QAAQ,OAA8B,IAAIV,GAAG,KAAK,IAAI,IAEtDW,QAAQ,OAAuB,IAAID,QAAQ,OAAwB,IACnEC,QAAQ,OAAmB,IAAID,QAAQ,OAAoB,IAE3DC,QAAQ,OAAkB,IAAI,IAAI,CAAC7C,gBAAiB,EACrD;MACA,IAAI,CAAC6B,MAAM,EAAE;IACf;IAEA,IAAI,CAACd,mBAAmB,EAAE;IAC1B,IAAI,CAACuB,OAAO,CAACJ,GAAG,EAAES,YAAY,CAAC;IAC/B,IAAI,CAAC/C,iBAAiB,GAAG,KAAK;EAChC;EAEAkD,SAASA,CAACC,IAAY,EAAQ;IAC5B,IAAI,CAACX,wBAAwB,EAAE;IAI/B,MAAMQ,QAAQ,GAAG,IAAI,CAACZ,WAAW,EAAE;IACnC,IAEGe,IAAI,OAAuB,IAAIH,QAAQ,OAAuB,IAC9DG,IAAI,OAAmB,IAAIH,QAAQ,OAAoB,IAEvDG,IAAI,OAAkB,IAAI,IAAI,CAAC/C,gBAAiB,EACjD;MACA,IAAI,CAAC6B,MAAM,EAAE;IACf;IAEA,IAAI,CAACd,mBAAmB,EAAE;IAC1B,IAAI,CAACO,WAAW,CAACyB,IAAI,CAAC;IACtB,IAAI,CAACnD,iBAAiB,GAAG,KAAK;EAChC;EAQAoD,OAAOA,CAACC,CAAS,GAAG,CAAC,EAAE5B,KAAe,EAAQ;IAC5C,IAAI4B,CAAC,IAAI,CAAC,EAAE;IAEZ,IAAI,CAAC5B,KAAK,EAAE;MACV,IAAI,IAAI,CAAClC,MAAM,CAAC+D,WAAW,IAAI,IAAI,CAAC/D,MAAM,CAAC8B,OAAO,EAAE;MAEpD,IAAI,IAAI,CAAC9B,MAAM,CAAC+B,OAAO,EAAE;QACvB,IAAI,CAACU,KAAK,EAAE;QACZ;MACF;IACF;IAEA,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC;IAEhBA,CAAC,IAAI,IAAI,CAAC5C,IAAI,CAAC8C,eAAe,EAAE;IAEhC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MAC1B,IAAI,CAACC,QAAQ,EAAE;IACjB;IAEA;EACF;EAEAhB,QAAQA,CAACU,IAAY,EAAW;IAC9B,OAAO,IAAI,CAACf,WAAW,EAAE,KAAKe,IAAI;EACpC;EAEAf,WAAWA,CAAA,EAAW;IACpB,OAAO,IAAI,CAAC3B,IAAI,CAAC2B,WAAW,EAAE;EAChC;EAEAsB,sBAAsBA,CAAA,EAAW;IAC/B,OAAO,IAAI,CAACjD,IAAI,CAACiD,sBAAsB,EAAE;EAC3C;EAEAC,qBAAqBA,CAAA,EAAS;IAC5B,IAAI,CAAClD,IAAI,CAACkD,qBAAqB,EAAE;EACnC;EAEAC,WAAWA,CAACC,GAAoB,EAAEC,EAAc,EAAE;IAChD,IAAI,CAACD,GAAG,EAAE;MACRC,EAAE,EAAE;MACJ;IACF;IAEA,IAAI,CAACC,QAAQ,CAAC,OAAO,EAAEF,GAAG,CAAC;IAE3B,IAAI,CAACpD,IAAI,CAACmD,WAAW,CAACC,GAAG,EAAEC,EAAE,CAAC;EAChC;EAEAE,MAAMA,CAACC,IAAqB,EAAEJ,GAAoB,EAAQ;IACxD,IAAI,CAACA,GAAG,EAAE;IAEV,IAAI,CAACE,QAAQ,CAACE,IAAI,EAAEJ,GAAG,CAAC;IAExB,IAAI,CAACpD,IAAI,CAACuD,MAAM,CAACC,IAAI,EAAEJ,GAAG,CAAC;EAC7B;EAEAK,gBAAgBA,CACdD,IAAqB,EACrBJ,GAAoB,EACpBM,UAAkB,EAClBC,YAAoB,EACd;IACN,IAAI,CAACP,GAAG,EAAE;IAEV,IAAI,CAACE,QAAQ,CAACE,IAAI,EAAEJ,GAAG,CAAC;IAExB,IAAI,CAACpD,IAAI,CAACyD,gBAAgB,CAACD,IAAI,EAAEJ,GAAG,EAAEM,UAAU,EAAEC,YAAY,CAAC;EACjE;EAEAC,UAAUA,CACRJ,IAAqB,EACrBJ,GAAoB,EACpBC,EAAc,EACR;IACN,IAAI,CAACD,GAAG,EAAE;MACRC,EAAE,EAAE;MACJ;IACF;IAEA,IAAI,CAACC,QAAQ,CAACE,IAAI,EAAEJ,GAAG,CAAC;IAExB,IAAI,CAACpD,IAAI,CAAC4D,UAAU,CAACJ,IAAI,EAAEJ,GAAG,EAAEC,EAAE,CAAC;EACrC;EAEAQ,oBAAoBA,CAACC,cAAsB,EAAEC,GAAS,EAAQ;IAC5D,IAAI,CAAC,IAAI,CAAC/D,IAAI,CAACgE,cAAc,EAAE;IAE/B,MAAMC,cAAc,GAAG,IAAI,CAACjE,IAAI,CAACkE,eAAe;IAChDD,cAAc,CAACE,iBAAiB,GAAGJ,GAAG;IACtCE,cAAc,CAACH,cAAc,GAAGA,cAAc;EAChD;EAEAtC,MAAMA,CAAA,EAAS;IACb,IAAI,CAACN,MAAM,IAAiB;EAC9B;EAEA8B,QAAQA,CAAA,EAAS;IACf,IAAI,CAAC9B,MAAM,IAAoB;EACjC;EAEAe,OAAOA,CAACJ,GAAW,EAAES,YAAqB,EAAQ;IAChD,IAAI,CAAC8B,cAAc,CAACvC,GAAG,CAAC;IACxB,IAAI,CAACwC,YAAY,CAACxC,GAAG,CAACzB,UAAU,CAAC,CAAC,CAAC,CAAC;IAEpC,IAAI,CAACJ,IAAI,CAACsE,MAAM,CAACzC,GAAG,EAAES,YAAY,CAAC;IAEnC,IAAI,CAAC1C,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;EAC/B;EAEAsB,WAAWA,CAACyB,IAAY,EAAQ;IAC9B,IAAI,CAAC6B,kBAAkB,CAAC7B,IAAI,CAAC;IAC7B,IAAI,CAAC2B,YAAY,CAAC3B,IAAI,CAAC;IAEvB,IAAI,CAAC1C,IAAI,CAACwE,UAAU,CAAC9B,IAAI,CAAC;IAE1B,IAAI,CAAC9C,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;EAC/B;EAEAuB,MAAMA,CAACwB,IAAY,EAAE;IACnB,IAAI,CAAC6B,kBAAkB,CAAC7B,IAAI,CAAC;IAC7B,IAAI,CAAC2B,YAAY,CAAC3B,IAAI,CAAC;IAEvB,IAAI,CAAC1C,IAAI,CAACyE,KAAK,CAAC/B,IAAI,CAAC;IAErB,IAAI,CAAC9C,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;EAC/B;EAEA0E,YAAYA,CAACK,SAAiB,EAAQ;IAEpC,IACE,IAAI,CAACxF,OAAO,IACZwF,SAAS,OAAuB,IAChC,IAAI,CAAC1C,QAAQ,IAAoB,EACjC;MACA,IAAI,CAAChC,IAAI,CAAC2E,gBAAgB,CAAC,IAAI,CAACxF,WAAW,EAAE,IAAI,CAACyF,UAAU,EAAE,CAAC;IACjE;EACF;EAEAC,aAAaA,CAACH,SAAiB,EAAE;IAE/B,IACE,IAAI,CAACxF,OAAO,IACZwF,SAAS,OAAuB,IAChC,IAAI,CAAC1C,QAAQ,IAAoB,EACjC;MACA,OAAO,IAAI;IACb;EACF;EAEAuC,kBAAkBA,CAAC7B,IAAY,EAAQ;IAErC,MAAMoC,qBAAqB,GAAG,IAAI,CAACxF,sBAAsB;IACzD,IAAI,CAACwF,qBAAqB,EAAE;IAS5B,IAAIpC,IAAI,OAAoB,EAAE;MAE5B;IACF;IAGA,IAAIA,IAAI,OAAuB,EAAE;MAC/B,IAAI,CAACpD,sBAAsB,GAAG,IAAI;MAClC;IACF;IAEA,IAAI,CAACgC,SAAK,IAAK;IACf,IAAI,CAACpB,MAAM,EAAE;IACb4E,qBAAqB,CAACC,OAAO,GAAG,IAAI;EACtC;EAEAX,cAAcA,CAACvC,GAAW,EAAQ;IAEhC,MAAMiD,qBAAqB,GAAG,IAAI,CAACxF,sBAAsB;IACzD,IAAI,CAACwF,qBAAqB,EAAE;IAS5B,MAAME,GAAG,GAAGnD,GAAG,CAACxB,MAAM;IAEtB,IAAIuC,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,GAAG,IAAInD,GAAG,CAACzB,UAAU,CAACwC,CAAC,CAAC,OAAoB,EAAEA,CAAC,EAAE,EAAE;IACnE,IAAIA,CAAC,KAAKoC,GAAG,EAAE;MAEb;IACF;IAGA,MAAMC,GAAG,GAAGpD,GAAG,CAACzB,UAAU,CAACwC,CAAC,CAAC;IAC7B,IAAIqC,GAAG,OAAuB,EAAE;MAC9B,IAEEA,GAAG,OAAoB,IAEvBrC,CAAC,GAAG,CAAC,KAAKoC,GAAG,EACb;QAEA,IAAI,CAAC1F,sBAAsB,GAAG,IAAI;QAClC;MACF;MAEA,MAAM4F,OAAO,GAAGrD,GAAG,CAACzB,UAAU,CAACwC,CAAC,GAAG,CAAC,CAAC;MAErC,IAAIsC,OAAO,OAAuB,EAAE;QAGlC,IAAI1G,kBAAkB,CAAC6D,IAAI,CAACR,GAAG,CAACsD,KAAK,CAACvC,CAAC,GAAG,CAAC,EAAEoC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE;UAKtD;QACF;MAGF,CAAC,MAAM,IAAIE,OAAO,OAAoB,EAAE;QAGtC,IAAI,CAAC5F,sBAAsB,GAAG,IAAI;QAClC;MACF;IACF;IAEA,IAAI,CAACgC,SAAK,IAAK;IACf,IAAI,CAACpB,MAAM,EAAE;IACb4E,qBAAqB,CAACC,OAAO,GAAG,IAAI;EACtC;EAEAK,OAAOA,CAACC,IAAY,EAAE;IACpB,IAAI,CAAC,IAAI,CAACvG,MAAM,CAAC+D,WAAW,EAAE;IAG9B,MAAMyC,KAAK,GAAGD,IAAI,GAAG,IAAI,CAACrF,IAAI,CAACuF,cAAc,EAAE;IAE/C,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,KAAK,EAAE1C,CAAC,EAAE,EAAE;MAC9B,IAAI,CAACI,QAAQ,EAAE;IACjB;EACF;EAEAM,QAAQA,CAACE,IAAqB,EAAEJ,GAAS,EAAE;IACzC,IAAI,CAAC,IAAI,CAACtE,MAAM,CAAC+D,WAAW,EAAE;IAG9B,MAAMkB,GAAG,GAAGX,GAAG,GAAGA,GAAG,CAACI,IAAI,CAAC,GAAG,IAAI;IAClC,IAAI,CAAAO,GAAG,oBAAHA,GAAG,CAAEsB,IAAI,KAAI,IAAI,EAAE;MACrB,MAAMC,KAAK,GAAGvB,GAAG,CAACsB,IAAI,GAAG,IAAI,CAACrF,IAAI,CAACuF,cAAc,EAAE;MAEnD,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,KAAK,EAAE1C,CAAC,EAAE,EAAE;QAC9B,IAAI,CAACI,QAAQ,EAAE;MACjB;IACF;EACF;EAMA4B,UAAUA,CAAA,EAAW;IACnB,OAAO,IAAI,CAACxF,aAAa,GAAG,IAAI,CAACF,OAAO;EAC1C;EAEAsG,mBAAmBA,CAACC,IAAY,EAAEC,MAAc,EAAEC,OAAgB,EAAE;IAgBlE,IAAIA,OAAO,EAAE;MACX,IAAI,CAACpG,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACkB,KAAK,CAACgF,IAAI,EAAEC,MAAM,CAAC;IAC1B,CAAC,MAAM;MACL,MAAME,eAAe,GAAG;QACtBb,OAAO,EAAE;MACX,CAAC;MACD,IAAI,CAACzF,sBAAsB,GAAGsG,eAAe;MAC7C,IAAI,CAACnF,KAAK,CAACgF,IAAI,EAAEC,MAAM,CAAC;MAIxB,IAAIE,eAAe,CAACb,OAAO,EAAE;QAC3B,IAAI,CAACjE,MAAM,EAAE;QACb,IAAI,CAAC6B,OAAO,EAAE;QACd,IAAI,CAACrB,SAAK,IAAK;MACjB;IACF;EACF;EAEAb,KAAKA,CACHgF,IAAmB,EACnBC,MAAe,EACf5D,qBAA+B,EAG/B+D,0BAAmC,EACnCC,WAAqB,EACrB;IACA,IAAI,CAACL,IAAI,EAAE;IAEX,IAAI,CAAC3F,iBAAiB,GAAG,KAAK;IAE9B,MAAMiG,QAAQ,GAAGN,IAAI,CAACO,IAAI;IAC1B,MAAMlH,MAAM,GAAG,IAAI,CAACA,MAAM;IAE1B,MAAMmH,UAAU,GAAGnH,MAAM,CAAC+B,OAAO;IACjC,IAEE4E,IAAI,CAACS,QAAQ,EACb;MACApH,MAAM,CAAC+B,OAAO,GAAG,IAAI;IACvB;IAEA,MAAMsF,WAAW,GACf,IAAI,CACFJ,QAAQ,CAOT;IACH,IAAII,WAAW,KAAKC,SAAS,EAAE;MAC7B,MAAM,IAAIC,cAAc,CACrB,wBAAuBC,IAAI,CAACC,SAAS,CACpCR,QAAQ,CACR,qBAAoBO,IAAI,CAACC,SAAS,CAACd,IAAI,CAAC5G,WAAW,CAAC2H,IAAI,CAAE,EAAC,CAC9D;IACH;IAEA,IAAI,CAACvH,WAAW,CAACwH,IAAI,CAAChB,IAAI,CAAC;IAE3B,MAAMiB,QAAQ,GAAG,IAAI,CAACrH,UAAU;IAChC,IAAI,CAACA,UAAU,GAAGoG,IAAI,CAACrC,GAAG,IAAIgD,SAAS;IACvC,IAAI,CAAC1F,mBAAmB,CAAC,IAAI,CAACrB,UAAU,IAAI,CAACqH,QAAQ,CAAC;IAEtD,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIb,WAAW,EAAE;MACfa,iBAAiB,GAAG,IAAI;IAC1B,CAAC,MAAM,IACL7H,MAAM,CAAC8H,oBAAoB,IAC3Bb,QAAQ,KAAK,oBAAoB,IACjCN,IAAI,CAACoB,KAAK,IACVpB,IAAI,CAACoB,KAAK,CAACC,aAAa,EACxB;MACAH,iBAAiB,GAAG,IAAI;IAC1B,CAAC,MAAM;MACLA,iBAAiB,GAAGhI,WAAW,CAAC8G,IAAI,EAAEC,MAAM,EAAE,IAAI,CAACzG,WAAW,CAAC;IACjE;IACA,IAAI0H,iBAAiB,EAAE;MACrB,IAAI,CAACrF,SAAK,IAAK;MACf,IAAI,CAACxB,iBAAiB,GAAG,KAAK;IAChC;IAEA,IAAI,CAACD,gBAAgB,GAAG,CAAC;IAEzB,IAAI,CAACkH,qBAAqB,CAACtB,IAAI,EAAEC,MAAM,CAAC;IAExC,MAAMtC,GAAG,GAAG2C,QAAQ,KAAK,SAAS,IAAIA,QAAQ,KAAK,MAAM,GAAG,IAAI,GAAGN,IAAI,CAACrC,GAAG;IAE3E,IAAI,CAACD,WAAW,CAACC,GAAG,EAAE+C,WAAW,CAACa,IAAI,CAAC,IAAI,EAAEvB,IAAI,EAAEC,MAAM,CAAC,CAAC;IAE3D,IAAIiB,iBAAiB,EAAE;MACrB,IAAI,CAACM,sBAAsB,CAACxB,IAAI,EAAEC,MAAM,CAAC;MACzC,IAAI,CAACpE,SAAK,IAAK;MACf,IAAI,CAAC/B,iBAAiB,GAAGuC,qBAAqB;IAChD,CAAC,MAAM,IAAIA,qBAAqB,IAAI,CAAC,IAAI,CAACvC,iBAAiB,EAAE;MAC3D,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAAC0H,sBAAsB,CAACxB,IAAI,EAAEC,MAAM,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAACuB,sBAAsB,CAACxB,IAAI,EAAEC,MAAM,EAAEG,0BAA0B,CAAC;IACvE;IAGA,IAAI,CAAC5G,WAAW,CAACiI,GAAG,EAAE;IAEtBpI,MAAM,CAAC+B,OAAO,GAAGoF,UAAU;IAC3B,IAAI,CAAC5G,UAAU,GAAGqH,QAAQ;IAE1B,IAAI,CAAC5G,iBAAiB,GAAG,KAAK;EAChC;EAEAY,mBAAmBA,CAACyG,uBAAiC,EAAE;IACrD,IAAIA,uBAAuB,EAAE,IAAI,CAACC,sBAAsB,EAAE;IAC1D,IAAI,CAAC,IAAI,CAAC/H,UAAU,EAAE,IAAI,CAACgI,qBAAqB,EAAE;EACpD;EAEAD,sBAAsBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAC5H,4BAA4B,EAAE;IACvC,IAAI,CAACA,4BAA4B,GAAG,IAAI;IAExC,MAAM8H,OAAO,GAAG,IAAI,CAACxI,MAAM,CAACyI,sBAAsB;IAClD,IAAID,OAAO,EAAE;MACX,IAAI,CAACE,aAAa,CAChB;QACExB,IAAI,EAAE,cAAc;QACpByB,KAAK,EAAEH;MACT,CAAC,IAEF;IACH;EACF;EAEAD,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAAC7H,4BAA4B,EAAE;IACxC,IAAI,CAACA,4BAA4B,GAAG,KAAK;IAEzC,MAAM8H,OAAO,GAAG,IAAI,CAACxI,MAAM,CAAC4I,qBAAqB;IACjD,IAAIJ,OAAO,EAAE;MACX,IAAI,CAACE,aAAa,CAChB;QACExB,IAAI,EAAE,cAAc;QACpByB,KAAK,EAAEH;MACT,CAAC,IAEF;IACH;EACF;EAEAK,cAAcA,CACZlC,IAMa,EACO;IACpB,MAAMoB,KAAK,GAAGpB,IAAI,CAACoB,KAAK;IACxB,IACEA,KAAK,IACLA,KAAK,CAACe,GAAG,IAAI,IAAI,IACjBf,KAAK,CAACgB,QAAQ,IAAI,IAAI,IACtBpC,IAAI,CAACgC,KAAK,KAAKZ,KAAK,CAACgB,QAAQ,EAC7B;MAEA,OAAOhB,KAAK,CAACe,GAAG;IAClB;EACF;EAEAE,SAASA,CACPC,KAAuC,EACvCrC,MAAc,EACdsC,IAAsB,GAAG,CAAC,CAAC,EAC3B;IACA,IAAI,EAACD,KAAK,YAALA,KAAK,CAAE1H,MAAM,GAAE;IAEpB,IAAI2H,IAAI,CAAC9H,MAAM,EAAE,IAAI,CAACA,MAAM,EAAE;IAE9B,MAAM+H,WAA+B,GAAG;MACtCC,WAAW,EAAEF,IAAI,CAACE,WAAW;MAC7BC,iBAAiB,EAAE;IACrB,CAAC;IAED,MAAMC,SAAS,GAAGJ,IAAI,CAACI,SAAS,GAAGJ,IAAI,CAACI,SAAS,CAACpB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;IAEnE,MAAMhC,GAAG,GAAG+C,KAAK,CAAC1H,MAAM;IACxB,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,GAAG,EAAEpC,CAAC,EAAE,EAAE;MAC5B,MAAM6C,IAAI,GAAGsC,KAAK,CAACnF,CAAC,CAAC;MACrB,IAAI,CAAC6C,IAAI,EAAE;MAEX,IAAIuC,IAAI,CAACK,SAAS,EAAE,IAAI,CAACC,aAAa,CAAC1F,CAAC,KAAK,CAAC,EAAEqF,WAAW,CAAC;MAE5D,IAAI,CAACxH,KAAK,CAACgF,IAAI,EAAEC,MAAM,EAAEU,SAAS,EAAE4B,IAAI,CAACnC,0BAA0B,IAAI,CAAC,CAAC;MAEzEmC,IAAI,CAACO,QAAQ,oBAAbP,IAAI,CAACO,QAAQ,CAAG9C,IAAI,EAAE7C,CAAC,CAAC;MAExB,IAAIA,CAAC,GAAGoC,GAAG,GAAG,CAAC,EAAEoD,SAAS,oBAATA,SAAS,EAAI;MAE9B,IAAIJ,IAAI,CAACK,SAAS,EAAE;QAClB,IAAIzF,CAAC,GAAG,CAAC,KAAKoC,GAAG,EAAE;UACjB,IAAI,CAACrC,OAAO,CAAC,CAAC,CAAC;QACjB,CAAC,MAAM;UAAA,IAAA6F,aAAA;UACL,MAAMC,QAAQ,GAAGV,KAAK,CAACnF,CAAC,GAAG,CAAC,CAAC;UAC7BqF,WAAW,CAACE,iBAAiB,GAAG,EAAAK,aAAA,GAAAC,QAAQ,CAACrF,GAAG,qBAAZoF,aAAA,CAAcE,KAAK,CAACrD,IAAI,KAAI,CAAC;UAE7D,IAAI,CAACiD,aAAa,CAAC,IAAI,EAAEL,WAAW,CAAC;QACvC;MACF;IACF;IAEA,IAAID,IAAI,CAAC9H,MAAM,EAAE,IAAI,CAACY,MAAM,EAAE;EAChC;EAEA6H,wBAAwBA,CAAClD,IAAY,EAAEC,MAAc,EAAE;IACrD,MAAMxF,MAAM,GAAGuF,IAAI,CAACmD,eAAe,IAAInD,IAAI,CAACmD,eAAe,CAACvI,MAAM,GAAG,CAAC;IACtE,IAAIH,MAAM,EAAE,IAAI,CAACA,MAAM,EAAE;IACzB,IAAI,CAACO,KAAK,CAACgF,IAAI,EAAEC,MAAM,CAAC;IACxB,IAAIxF,MAAM,EAAE,IAAI,CAACY,MAAM,EAAE;EAC3B;EAEA+H,UAAUA,CAACnD,MAA8C,EAAE;IACzD,MAAMD,IAAI,GAAGC,MAAM,CAACoD,IAAI;IAExB,IAAIrD,IAAI,CAACO,IAAI,KAAK,gBAAgB,EAAE;MAClC,IAAI,CAACzE,KAAK,EAAE;IACd;IAEA,IAAI,CAACd,KAAK,CAACgF,IAAI,EAAEC,MAAM,CAAC;EAC1B;EAEAuB,sBAAsBA,CAACxB,IAAY,EAAEC,MAAe,EAAEhC,UAAmB,EAAE;IACzE,MAAM;MAAEqF,aAAa;MAAEC;IAAiB,CAAC,GAAGvD,IAAI;IAIhD,IAAIsD,aAAa,YAAbA,aAAa,CAAE1I,MAAM,EAAE;MACzB,IAAI,CAAC4I,cAAc,IAEjBF,aAAa,EACbtD,IAAI,EACJC,MAAM,EACNhC,UAAU,CACX;IACH;IACA,IAAIsF,gBAAgB,YAAhBA,gBAAgB,CAAE3I,MAAM,EAAE;MAC5B,IAAI,CAAC4I,cAAc,IAEjBD,gBAAgB,EAChBvD,IAAI,EACJC,MAAM,EACNhC,UAAU,CACX;IACH;EACF;EAEAqD,qBAAqBA,CAACtB,IAAY,EAAEC,MAAc,EAAE;IAClD,MAAMwD,QAAQ,GAAGzD,IAAI,CAACmD,eAAe;IACrC,IAAI,EAACM,QAAQ,YAARA,QAAQ,CAAE7I,MAAM,GAAE;IACvB,IAAI,CAAC4I,cAAc,IAAuBC,QAAQ,EAAEzD,IAAI,EAAEC,MAAM,CAAC;EACnE;EAEA3D,wBAAwBA,CAAA,EAAG;IACzB,IAAI,IAAI,CAACjC,iBAAiB,EAAE,IAAI,CAACqJ,kBAAkB,EAAE;IACrD,IAAI,CAACrJ,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,oBAAoB,GAAG,IAAI;EAClC;EAEAoJ,kBAAkBA,CAAA,EAAG;IACnB,MAAM1D,IAAI,GAAG,IAAI,CAACxG,WAAW,CAAC,IAAI,CAACA,WAAW,CAACoB,MAAM,GAAG,CAAC,CAAC;IAC1D,MAAM6I,QAAQ,GAAGzD,IAAI,CAACsD,aAAa;IACnC,IAAI,EAACG,QAAQ,YAARA,QAAQ,CAAE7I,MAAM,GAAE;IAEvB,MAAM+I,QAAQ,GAAG,IAAI,CAACpH,QAAQ,IAAiB;IAC/C,MAAM9B,MAAM,GAAG,IAAI,CAACH,oBAAoB;IACxC,MAAMsJ,oBAAoB,GAAG,IAAI,CAAC5J,gBAAgB,CAAC6J,IAAI;IACvD,IAAIpJ,MAAM,EAAE,IAAI,CAACA,MAAM,EAAE;IACzB,IAAI,CAAC+I,cAAc,IAAqBC,QAAQ,EAAEzD,IAAI,CAAC;IACvD,IAAI2D,QAAQ,IAAIC,oBAAoB,KAAK,IAAI,CAAC5J,gBAAgB,CAAC6J,IAAI,EAAE;MACnE,IAAI,CAAC/H,KAAK,EAAE;IACd;IACA,IAAIrB,MAAM,EAAE,IAAI,CAACY,MAAM,EAAE;EAC3B;EAEAyI,yBAAyBA,CAAA,EAAG;IAC1B,IAAI,CAACxJ,oBAAoB,GAAG,KAAK;EACnC;EAEAyJ,aAAaA,CACXzB,KAAe,EACfrC,MAAc,EACdsC,IAA0B,GAAG,CAAC,CAAC,EAC/B;IACAA,IAAI,CAACK,SAAS,GAAG,IAAI;IACrB,IAAI,CAACP,SAAS,CAACC,KAAK,EAAErC,MAAM,EAAEsC,IAAI,CAAC;EACrC;EAEAyB,SAASA,CAACC,KAAe,EAAEhE,MAAc,EAAEsC,IAAsB,GAAG,CAAC,CAAC,EAAE;IACtE,IAAIA,IAAI,CAACI,SAAS,IAAI,IAAI,EAAE;MAC1BJ,IAAI,CAACI,SAAS,GAAGuB,cAAc;IACjC;IAEA,IAAI,CAAC7B,SAAS,CAAC4B,KAAK,EAAEhE,MAAM,EAAEsC,IAAI,CAAC;EACrC;EAEAM,aAAaA,CAACsB,OAAgB,EAAE5B,IAAwB,EAAE;IAExD,IAAI,IAAI,CAAClJ,MAAM,CAAC+D,WAAW,IAAI,IAAI,CAAC/D,MAAM,CAAC8B,OAAO,EAAE;IAIpD,IAAI,IAAI,CAAC9B,MAAM,CAAC+B,OAAO,EAAE;MACvB,IAAI,CAACU,KAAK,EAAE;MACZ;IACF;IAEA,IAAI,CAACqI,OAAO,EAAE;MACZ;IACF;IAEA,MAAMC,SAAS,GAAG7B,IAAI,CAACG,iBAAiB;IACxC,MAAM2B,eAAe,GAAG,IAAI,CAACjK,gBAAgB;IAC7C,IAAIgK,SAAS,GAAG,CAAC,IAAIC,eAAe,GAAG,CAAC,EAAE;MACxC,MAAMC,MAAM,GAAGF,SAAS,GAAGC,eAAe;MAC1C,IAAIC,MAAM,IAAI,CAAC,EAAE;QACf,IAAI,CAACpH,OAAO,CAACoH,MAAM,IAAI,CAAC,CAAC;QACzB;MACF;IACF;IAGA,IAAI,IAAI,CAAC/J,IAAI,CAACyB,UAAU,EAAE,EAAE;MAa1B,IAAI,CAACkB,OAAO,CAAC,CAAC,CAAC;IACjB;EACF;EAOAqH,mBAAmBA,CAAC1C,OAAkB,EAAsB;IAG1D,IAAIA,OAAO,CAAC2C,MAAM,EAAE;IAEpB,IAAI,IAAI,CAACxK,gBAAgB,CAACyK,GAAG,CAAC5C,OAAO,CAAC,EAAE;IAExC,IACE,IAAI,CAAC/H,iBAAiB,KACrBd,WAAW,CAAC4D,IAAI,CAACiF,OAAO,CAACG,KAAK,CAAC,IAC9B/I,qBAAqB,CAAC2D,IAAI,CAACiF,OAAO,CAACG,KAAK,CAAC,CAAC,EAC5C;MACA;IACF;IAEA,IAAI,CAAChI,gBAAgB,CAAC0K,GAAG,CAAC7C,OAAO,CAAC;IAElC,IAAI,CAAC,IAAI,CAACxI,MAAM,CAACsL,kBAAkB,CAAC9C,OAAO,CAACG,KAAK,CAAC,EAAE;MAClD;IACF;IAEA;EACF;EAEAD,aAAaA,CAACF,OAAkB,EAAE+C,YAAkC,EAAE;IACpE,MAAMC,gBAAgB,GAAG,IAAI,CAAC/K,iBAAiB;IAC/C,MAAMgL,cAAc,GAAGjD,OAAO,CAACtB,IAAI,KAAK,cAAc;IAItD,MAAMwE,aAAa,GACjBD,cAAc,IACdF,YAAY,MAA6B,IACzC,CAAC,IAAI,CAAC9K,iBAAiB;IAEzB,IACEiL,aAAa,IACb,IAAI,CAACxK,IAAI,CAACyB,UAAU,EAAE,IACtB4I,YAAY,MAAiC,EAC7C;MACA,IAAI,CAAC1H,OAAO,CAAC,CAAC,CAAC;IACjB;IAEA,MAAM8H,YAAY,GAAG,IAAI,CAAC9I,WAAW,EAAE;IACvC,IACE8I,YAAY,OAAgC,IAC5CA,YAAY,QAA6B,EACzC;MACA,IAAI,CAAClJ,KAAK,EAAE;IACd;IAEA,IAAImJ,GAAG;IACP,IAAIH,cAAc,EAAE;MAClBG,GAAG,GAAI,KAAIpD,OAAO,CAACG,KAAM,IAAG;MAC5B,IAAI,IAAI,CAAC3I,MAAM,CAACoB,MAAM,CAACyK,sBAAsB,EAAE;QAAA,IAAAC,YAAA;QAC7C,MAAMb,MAAM,IAAAa,YAAA,GAAGtD,OAAO,CAAClE,GAAG,qBAAXwH,YAAA,CAAalC,KAAK,CAACmC,MAAM;QACxC,IAAId,MAAM,EAAE;UACV,MAAMe,YAAY,GAAG,IAAIC,MAAM,CAAC,WAAW,GAAGhB,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC;UAChEW,GAAG,GAAGA,GAAG,CAACM,OAAO,CAACF,YAAY,EAAE,IAAI,CAAC;QACvC;QAEA,IAAIG,UAAU,GAAG,IAAI,CAACnM,MAAM,CAAC+D,WAAW,GACpC,CAAC,GACD,IAAI,CAAC7C,IAAI,CAACkL,gBAAgB,EAAE;QAEhC,IAAI,IAAI,CAACrG,aAAa,IAAiB,IAAI,IAAI,CAAC/F,MAAM,CAAC+D,WAAW,EAAE;UAClEoI,UAAU,IAAI,IAAI,CAACrG,UAAU,EAAE;QACjC;QAEA8F,GAAG,GAAGA,GAAG,CAACM,OAAO,CAAC,UAAU,EAAG,KAAI,GAAG,CAACG,MAAM,CAACF,UAAU,CAAE,EAAC,CAAC;MAC9D;IACF,CAAC,MAAM,IAAI,CAACX,gBAAgB,EAAE;MAC5BI,GAAG,GAAI,KAAIpD,OAAO,CAACG,KAAM,EAAC;IAC5B,CAAC,MAAM;MAILiD,GAAG,GAAI,KAAIpD,OAAO,CAACG,KAAM,IAAG;IAC9B;IAGA,IAAI,IAAI,CAACzF,QAAQ,IAAiB,EAAE,IAAI,CAACR,MAAM,EAAE;IAEjD,IAAI,CAAC+B,MAAM,CAAC,OAAO,EAAE+D,OAAO,CAAClE,GAAG,CAAC;IACjC,IAAI,CAACnB,OAAO,CAACyI,GAAG,EAAEH,cAAc,CAAC;IAEjC,IAAI,CAACA,cAAc,IAAI,CAACD,gBAAgB,EAAE;MACxC,IAAI,CAAC3H,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;IACvB;IAEA,IAAI6H,aAAa,IAAIH,YAAY,MAAkC,EAAE;MACnE,IAAI,CAAC1H,OAAO,CAAC,CAAC,CAAC;IACjB;EACF;EAEAsG,cAAcA,CACZjD,IAAkB,EAClBkD,QAA8B,EAC9BzD,IAAY,EACZC,MAAe,EACfhC,UAAkB,GAAG,CAAC,EACtB;IACA,MAAM0H,OAAO,GAAG3F,IAAI,CAACrC,GAAG;IACxB,MAAM4B,GAAG,GAAGkE,QAAQ,CAAC7I,MAAM;IAC3B,IAAIgL,MAAM,GAAG,CAAC,CAACD,OAAO;IACtB,MAAME,aAAa,GAAGD,MAAM,GAAGD,OAAO,CAAC1C,KAAK,CAACrD,IAAI,GAAG,CAAC;IACrD,MAAMkG,WAAW,GAAGF,MAAM,GAAGD,OAAO,CAACI,GAAG,CAACnG,IAAI,GAAG,CAAC;IACjD,IAAIoG,QAAQ,GAAG,CAAC;IAChB,IAAIC,qBAAqB,GAAG,CAAC;IAE7B,MAAMpJ,YAAY,GAAG,IAAI,CAAC/C,iBAAiB,GACvC,YAAY,CAAC,CAAC,GACd,IAAI,CAACoD,OAAO,CAACqE,IAAI,CAAC,IAAI,CAAC;IAE3B,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,GAAG,EAAEpC,CAAC,EAAE,EAAE;MAC5B,MAAM0E,OAAO,GAAG4B,QAAQ,CAACtG,CAAC,CAAC;MAE3B,MAAM+I,WAAW,GAAG,IAAI,CAAC3B,mBAAmB,CAAC1C,OAAO,CAAC;MACrD,IAAIqE,WAAW,MAA6B,EAAE;QAC5CN,MAAM,GAAG,KAAK;QACd;MACF;MACA,IAAIA,MAAM,IAAI/D,OAAO,CAAClE,GAAG,IAAIuI,WAAW,MAA6B,EAAE;QACrE,MAAMC,gBAAgB,GAAGtE,OAAO,CAAClE,GAAG,CAACsF,KAAK,CAACrD,IAAI;QAC/C,MAAMwG,cAAc,GAAGvE,OAAO,CAAClE,GAAG,CAACoI,GAAG,CAACnG,IAAI;QAC3C,IAAIW,IAAI,MAAyB,EAAE;UACjC,IAAI+D,MAAM,GAAG,CAAC;UACd,IAAInH,CAAC,KAAK,CAAC,EAAE;YAGX,IACE,IAAI,CAAC5C,IAAI,CAACyB,UAAU,EAAE,KACrB6F,OAAO,CAACtB,IAAI,KAAK,aAAa,IAC7B4F,gBAAgB,IAAIC,cAAc,CAAC,EACrC;cACA9B,MAAM,GAAG2B,qBAAqB,GAAG,CAAC;YACpC;UACF,CAAC,MAAM;YACL3B,MAAM,GAAG6B,gBAAgB,GAAGH,QAAQ;UACtC;UACAA,QAAQ,GAAGI,cAAc;UAEzBvJ,YAAY,CAACyH,MAAM,CAAC;UACpB,IAAI,CAACvC,aAAa,CAACF,OAAO,IAA2B;UAErD,IAAI1E,CAAC,GAAG,CAAC,KAAKoC,GAAG,EAAE;YACjB1C,YAAY,CACVwJ,IAAI,CAACC,GAAG,CAACT,aAAa,GAAGG,QAAQ,EAAEC,qBAAqB,CAAC,CAC1D;YACDD,QAAQ,GAAGH,aAAa;UAC1B;QACF,CAAC,MAAM,IAAItF,IAAI,MAAuB,EAAE;UACtC,MAAM+D,MAAM,GACV6B,gBAAgB,IAAIhJ,CAAC,KAAK,CAAC,GAAG0I,aAAa,GAAGG,QAAQ,CAAC;UACzDA,QAAQ,GAAGI,cAAc;UAEzBvJ,YAAY,CAACyH,MAAM,CAAC;UACpB,IAAI,CAACvC,aAAa,CAACF,OAAO,IAA2B;UAErD,IAAI1E,CAAC,GAAG,CAAC,KAAKoC,GAAG,EAAE;YACjB1C,YAAY,CAACwJ,IAAI,CAACE,GAAG,CAAC,CAAC,EAAET,WAAW,GAAGE,QAAQ,CAAC,CAAC;YACjDA,QAAQ,GAAGF,WAAW;UACxB;QACF,CAAC,MAAM;UACL,MAAMxB,MAAM,GACV6B,gBAAgB,IAAIhJ,CAAC,KAAK,CAAC,GAAG2I,WAAW,GAAG7H,UAAU,GAAG+H,QAAQ,CAAC;UACpEA,QAAQ,GAAGI,cAAc;UAEzBvJ,YAAY,CAACyH,MAAM,CAAC;UACpB,IAAI,CAACvC,aAAa,CAACF,OAAO,IAA2B;QACvD;MACF,CAAC,MAAM;QACL+D,MAAM,GAAG,KAAK;QACd,IAAIM,WAAW,MAA6B,EAAE;UAC5C;QACF;QAEA,IAAI3G,GAAG,KAAK,CAAC,EAAE;UACb,MAAMiH,UAAU,GAAG3E,OAAO,CAAClE,GAAG,GAC1BkE,OAAO,CAAClE,GAAG,CAACsF,KAAK,CAACrD,IAAI,KAAKiC,OAAO,CAAClE,GAAG,CAACoI,GAAG,CAACnG,IAAI,GAC/C,CAAC5G,WAAW,CAAC4D,IAAI,CAACiF,OAAO,CAACG,KAAK,CAAC;UAEpC,MAAMyE,iBAAiB,GACrBD,UAAU,IACV,CAAChO,WAAW,CAACwH,IAAI,CAAC,IAClB,CAACvH,WAAW,CAACwH,MAAM,CAAC,IACpB,CAACvH,iBAAiB,CAACuH,MAAM,CAAC,IAC1B,CAACtH,mBAAmB,CAACsH,MAAM,CAAC;UAE9B,IAAIM,IAAI,MAAyB,EAAE;YACjC,IAAI,CAACwB,aAAa,CAChBF,OAAO,EACN4E,iBAAiB,IAAIzG,IAAI,CAACO,IAAI,KAAK,kBAAkB,IACnDiG,UAAU,IAAIjO,UAAU,CAAC0H,MAAM,EAAE;cAAEoD,IAAI,EAAErD;YAAK,CAAC,CAAE,QAEpB,CACjC;UACH,CAAC,MAAM,IAAIyG,iBAAiB,IAAIlG,IAAI,MAA0B,EAAE;YAC9D,IAAI,CAACwB,aAAa,CAACF,OAAO,IAA2B;UACvD,CAAC,MAAM;YACL,IAAI,CAACE,aAAa,CAACF,OAAO,IAA+B;UAC3D;QACF,CAAC,MAAM,IACLtB,IAAI,MAAuB,IAC3B,EAAEP,IAAI,CAACO,IAAI,KAAK,kBAAkB,IAAIP,IAAI,CAAC0G,UAAU,CAAC9L,MAAM,GAAG,CAAC,CAAC,IACjEoF,IAAI,CAACO,IAAI,KAAK,WAAW,IACzBP,IAAI,CAACO,IAAI,KAAK,iBAAiB,EAC/B;UAMA,IAAI,CAACwB,aAAa,CAChBF,OAAO,EACP1E,CAAC,KAAK,CAAC,OAEHA,CAAC,KAAKoC,GAAG,GAAG,CAAC,QAEe,CACjC;QACH,CAAC,MAAM;UACL,IAAI,CAACwC,aAAa,CAACF,OAAO,IAA+B;QAC3D;MACF;IACF;IAEA,IAAItB,IAAI,MAA0B,IAAIqF,MAAM,IAAII,QAAQ,EAAE;MACxD,IAAI,CAAC5L,gBAAgB,GAAG4L,QAAQ;IAClC;EACF;AACF;AAGAW,MAAM,CAACC,MAAM,CAACzN,OAAO,CAAC0N,SAAS,EAAEvO,kBAAkB,CAAC;AAEjB;EAEjCa,OAAO,CAAC0N,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAgB,CAAC,CAAC;AAC1D;AAAC,IAAAC,QAAA,GAIc5N,OAAO;AAAA6N,OAAA,CAAAC,OAAA,GAAAF,QAAA;AAEtB,SAAS7C,cAAcA,CAAA,EAAgB;EACrC,IAAI,CAACrI,SAAK,IAAK;EACf,IAAI,CAACC,KAAK,EAAE;AACd"}