{"name": "@appmaker-xyz/react-native", "version": "0.4.46-expo-v2-build-test-25-04-e5e5c0b.0", "description": "", "main": "build-output/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rollup --config rollup.config.js", "watch": "rollup --config rollup.config.js --watch", "prepublishOnly": "npm run build"}, "keywords": [], "author": "", "license": "UNLICENSED", "files": ["build-output"], "dependencies": {"@miblanchard/react-native-slider": "^2.3.1", "@native-html/iframe-plugin": "^1.1.2", "@rollup/plugin-typescript": "^11.1.2", "react-native-render-html": "^5.1.0", "react-native-simple-store": "^2.0.2", "react-native-swiper-flatlist": "^3.0.16", "react-native-uuid": "^2.0.2", "striptags": "^3.2.0"}, "devDependencies": {"@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-image": "^3.0.1", "@rollup/plugin-typescript": "^11.1.2", "rollup": "^2.79.0", "rollup-plugin-local-resolve": "^1.0.7", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-uglify": "^6.0.4"}, "peerDependencies": {"@appmaker-xyz/remote-bundle": "*", "@react-native-community/async-storage": "*", "@react-native-firebase/analytics": "*", "@react-native-firebase/auth": "*", "@react-native-firebase/messaging": "*", "@react-navigation/bottom-tabs": "*", "@react-navigation/drawer": "*", "@react-navigation/native": "*", "@react-navigation/stack": "*", "@shopify/flash-list": "*", "@tanstack/react-query": "*", "axios": "*", "i18next": "*", "immer": "*", "lodash": "*", "react": "*", "react-i18next": "*", "react-native": "*", "react-native-localize": "*", "react-native-restart": "*", "react-native-svg": "*", "react-native-vector-icons/Feather": "*", "react-native-webview": "*", "zustand/shallow": "*"}, "repository": "https://github.com/Appmaker-xyz/starter-app", "publishConfig": {"registry": "https://flash.appmaker.xyz", "directory": "build-output"}}