import { customStyles } from '../../styles/custom';
const page = {
  blocks: [
    {
      name: 'appmaker/CartCard',
      contextValues: true,
      attributes: {
        appmakerAction: {
          productId: '{{blockItem.node.merchandise.product.id}}',
          action: 'OPEN_PRODUCT',
        },
        product: '{{blockItem.node}}',
        regularPrice:
          '{{currencyHelper(blockItem.node.estimatedCost.subtotalAmount.amount,blockItem.node.estimatedCost.subtotalAmount.currencyCode)}}',
        salePrice:
          '{{currencyHelper(blockItem.node.estimatedCost.totalAmount.amount,blockItem.node.estimatedCost.totalAmount.currencyCode)}}',
        title: '{{blockItem.node.merchandise.product.title }}',
        count: '{{parseFloat(blockItem.node.quantity)}}',
        featureImg: '{{blockItem.node.merchandise.image.src}}',
        variation:
          '{{blockItem.node.merchandise.title == "Default Title" ? "" : blockItem.node.merchandise.title }}',
        hasPages: false,
        onSale:
          '{{blockItem.node.estimatedCost.subtotalAmount.amount > blockItem.node.estimatedCost.totalAmount.amount ? 1 : 0 }}',
        priceSale:
          '{{currencyHelper(blockItem.node.estimatedCost.subtotalAmount.amount,blockItem.node.estimatedCost.subtotalAmount.currencyCode)}}',
        currency:
          '{{getCurrency(blockItem.node.estimatedCost.totalAmount.currencyCode)}}',
        discountTotal: '{{blockItem.node.discountAllocations}}',
        aditionalInfo:
          '{{blockItem.node.sellingPlanAllocation ? blockItem.node.sellingPlanAllocation.sellingPlan.name : "" }}',
        __appmakerStylesClassName: 'cartCardCustomStyle',
        dataSource: {
          responseType: 'replace',
          repeatable: 'Yes',
          attributes: {
            params: '{{blockData.lines.edges}}',
          },
          source: 'variables',
          repeatItem: 'DataVariable',
        },
      },
    },
    {
      name: 'appmaker/order-notes',
      attributes: {
        __display:
          '{{checkIfTrueFalse(plugins.shopify.settings.show_order_notes)}}',
        orderNote: '{{blockData.note}}',
      },
    },
    {
      name: 'appmaker/CartCoupon',
      attributes: {
        plugin: '{{getObjectOfAnArray(plugins,"app-only-coupons")}}',
        __display:
          '{{checkIfTrueFalse(plugins.shopify.settings.show_apply_coupon)}}',
        enableAutoApplyCoupon:
          '{{checkIfTrueFalse(getObjectOfAnArray(plugins,"app-only-coupons").settings.enable_auto_apply_coupon)}}',
        couponDiscountedLength: '{{blockData.discountCodes.length}}',
        autoApplyCoupon:
          '{{getObjectOfAnArray(plugins,"app-only-coupons").settings.auto_apply_coupon}}',
        couponDiscounted: '{{blockData.discountCodes}}', // to be done
        __appmakerStylesClassName: 'cartCouponCustomStyle',
      },
    },
  ],

  fixedFooter: {
    clientId: 'checkout-sumbit',
    name: 'appmaker/checkout-button',
    attributes: {
      show: true,
      viewCartText: 'Checkout',
      itemCount: '{{blockData.lines.edges.length}}',
      totalPrice:
        '{{currencyHelper(blockItem.estimatedCost.subtotalAmount.amount,blockItem.estimatedCost.subtotalAmount.currencyCode)}}',
      showSubTotalAmount:
        '{{blockItem.estimatedCost.subtotalAmount.amount > blockItem.estimatedCost.totalAmount.amount ? 1 : 0 }}',
      subtotalAmount:
        '{{currencyHelper(blockItem.estimatedCost.totalAmount.amount,blockItem.estimatedCost.totalAmount.currencyCode)}}',
      appmakerAction: {
        action: 'OPEN_CHECKOUT',
      },
      __appmakerStylesClassName: 'checkoutCustomStyle',
    },
  },

  dataSource: {
    source: 'shopify',
    attributes: {
      mapping: {
        items: 'data.data.cart',
      },
      methodName: 'cartById',
      params: '{{appStorageStateApi.cart.id}}',
    },
    // dependencies: {
    //   // appStorageState: ['cart'],
    // },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
  },
  attributes: {
    insideSafeAreaView: true,
    loadingLayout: 'product',
    showLoadingTillData: true,
    reloadOnFocus: true,
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      // paddingHorizontal: 16,
    },
  },
  title: 'Cart',
};
export default page;
