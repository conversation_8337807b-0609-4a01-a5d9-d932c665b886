import { isEmpty } from 'lodash';
import { ShopifyDataSourceConfig } from '../../datasource/shopify';
import { useInfiniteDataSource } from '@appmaker-xyz/core';
type QueryInputCollectionId = {
  collectionId: string;
  searchQuery?: string;
};
type QueryInputCollectionHandle = {
  collectionHandle: string;
  searchQuery?: string;
};
type QueryInputSearch = {
  searchQuery: string;
};

type QueryInputProduct = {
  productIds: string[];
};
type QueryInputProductRecommendation = {
  productRecommendationId: string;
};
type QueryInputHandles = {
  handles: string[];
};
type QueryInput =
  | QueryInputCollectionId
  | QueryInputCollectionHandle
  | QueryInputSearch
  | QueryInputProduct
  | QueryInputProductRecommendation
  | QueryInputHandles;
type filters = { [key: string]: any }; // Replace 'any' with more specific type if possible
type sort = { [key: string]: any }; // Replace 'any' with more specific type if possible
type limit = number; // not required number;
type surface = string; // not required string;

type ProductData = {
  data: any; // Replace 'any' with the actual data type
  isLoading: boolean;
  isError: boolean;
  hasNextPage: boolean;
  fetchNextPage: () => void;
  isFetchingNextPage: boolean;
  productList: any[]; // Replace 'any[]' with the actual product list type
  isFetching: boolean;
};
const useProducts = ({
  query,
  filters,
  sort,
  limit,
  surface,
  ...restProps
}: {
  query: QueryInput;
  filters?: filters;
  sort?: sort;
  limit?: limit;
  surface?: surface;
  [key: string]: any;
}): ProductData => {
  let dataSource: any; // Replace 'any' with the correct type of dataSource
  if (query?.productIds) {
    dataSource = ShopifyDataSourceConfig.ProductsByIdHandles({
      ids: query.productIds,
      handles: query.handles,
      // FIXME: Need to have a prop or something that decides whether to show hidden products or not instead of this hardcoded string
      showHiddenProducts: true,
      ...limit && { productsLimit: limit },
      ...surface && { surface: surface },
      ...restProps
    });
  } else if (query?.searchQuery) {
    dataSource = ShopifyDataSourceConfig.ProductsBySearch({
      searchQuery: query.searchQuery,
      ...limit && { productsLimit: limit },
      ...surface && { surface: surface },
      ...restProps
    });
  } else if (query?.collectionHandle) {
    dataSource = ShopifyDataSourceConfig.ProductsByCollection({
      collectionHandle: query.collectionHandle,
      ...limit && { productsLimit: limit },
      ...surface && { surface: surface },
      ...restProps
    });
  } else if (query?.collectionId) {
    dataSource = ShopifyDataSourceConfig.ProductsByCollection({
      collectionId: query.collectionId,
      ...limit && { productsLimit: limit },
      ...surface && { surface: surface },
      ...restProps
    });
  } else if (query?.productRecommendationId) {
    dataSource = ShopifyDataSourceConfig.ProductRecommendations({
      productId: query.productRecommendationId,
      ...limit && { limit: limit },
      ...surface && { surface: surface },
      ...restProps
    });
  } else if(query?.handles) {
    dataSource = ShopifyDataSourceConfig.ProductsByHandles({
      handles: query.handles,
      ...limit && { productsLimit: limit },
      ...surface && { surface: surface },
      ...restProps
    });
  }
  const [productData, { list }] = useInfiniteDataSource({
    dataSource,
    enabled: !isEmpty(dataSource),
    filterParams: {
      ...(!isEmpty(filters) && { filter: filters }),
      ...(!isEmpty(sort) && { sort: sort }),
      ...(!isEmpty(query?.searchQuery) && { searchQuery: query.searchQuery }),
    },
    ...(dataSource?.pageInfoExtractor && { pageInfoExtractor: dataSource?.pageInfoExtractor }),
  });

  return { ...productData, productList: list };
};

export { useProducts };
