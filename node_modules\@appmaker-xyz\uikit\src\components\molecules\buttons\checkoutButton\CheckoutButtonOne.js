import React from 'react';
import { Button, AppmakerText } from '../../../../components';
import { StyleSheet, View, I18nManager } from 'react-native';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { useApThemeState } from '../../../../theme/ThemeContext';
import { testProps } from '@appmaker-xyz/core';
import { colors as configColors } from '@appmaker-xyz/app-config/newConfig';
import { useState } from 'react';
import { useCart } from '@appmaker-xyz/shopify';
const CheckoutButtonOne = ({
  clientId,
  show,
  totalPrice,
  subtotalAmount,
  showSubTotalAmount = false,
  onPress,
  viewCartText = 'View Cart',
  __appmakerCustomStyles: customStyles = {},
}) => {
  const { totalQuantity, cartTotalPayableWithCurrency } = useCart();
  const [loading, setLoading] = useState(false);
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });

  const ChevronRight = (props) => (
    <Icon name={`chevron-${I18nManager.isRTL ? 'left' : 'right'}`} {...props} />
  );
  const containerStyles = [styles.checkoutButtoncontainer];
  if (show === true) {
    containerStyles.push({ bottom: 0 });
  }
  if (customStyles.container) {
    containerStyles.push(customStyles?.container);
  }
  async function _onPress() {
    setLoading(true);
    await onPress();
    setLoading(false);
  }
  return (
    <View style={containerStyles} {...testProps(`${clientId}-container`)}>
      <Button
        testId={'checkout-button'}
        loading={loading}
        status={customStyles?.button ? null : 'dark'}
        style={styles.checkoutButton}
        onPress={_onPress}
        clientId={clientId}
        wholeContainerStyle={customStyles?.button}>
        <View>
          <AppmakerText
            testId={'checkout-button-items-count'}
            category="highlighter1"
            status={customStyles?.itemCountText ? null : 'white'}
            style={customStyles?.itemCountText}>
            {totalQuantity + ' Items'}
          </AppmakerText>
          <View style={styles.priceDetails}>
            <AppmakerText
              testId={'checkout-button-total-amount'}
              category="bodyParagraphBold"
              status={customStyles?.itemCountText ? null : 'white'}
              style={customStyles?.itemCountText}>
              {cartTotalPayableWithCurrency}
            </AppmakerText>
            {showSubTotalAmount == true && totalPrice != subtotalAmount ? (
              <AppmakerText
                category="bodySubText"
                status={customStyles?.itemCountText ? null : 'white'}
                style={
                  ([styles.subtotalAmountText],
                  {
                    ...customStyles?.itemCountText,
                    ...styles.subtotalAmountText,
                  })
                }>
                {subtotalAmount}
              </AppmakerText>
            ) : null}
          </View>
        </View>
        <AppmakerText
          category="actionTitle"
          status={customStyles?.ctaText ? null : 'white'}
          clientId={`${clientId}-btn`}
          style={customStyles?.ctaText}>
          {viewCartText} <ChevronRight style={customStyles?.ctaText_icon} />
        </AppmakerText>
      </Button>
    </View>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    checkoutButtoncontainer: {
      width: '100%',
      backgroundColor: color.white,
      paddingVertical: spacing.small,
      paddingHorizontal: spacing.base,
      borderTopWidth: 1,
      borderTopColor: color.light,
      justifyContent: 'flex-end',
    },
    checkoutButton: {
      justifyContent: 'space-between',
      width: '98%',
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: -6,
    },
    priceDetails: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    subtotalAmountText: {
      marginLeft: 10,
      textDecorationLine: 'line-through',
      textDecorationStyle: 'solid',
    },
    fontColor: {},
  });

export default CheckoutButtonOne;
