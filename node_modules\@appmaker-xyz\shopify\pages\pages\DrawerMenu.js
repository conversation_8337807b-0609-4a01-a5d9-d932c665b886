import ICONS from '../../ToolBarIcons';
import { appSettings } from '@appmaker-xyz/core';

const home = {
  // toolBarItems: [
  //   ICONS.SEARCH,
  //   ICONS.WISHLIST,
  //   ICONS.CART,
  //   // ICONS.LOGIN_PAGE,
  //   // ICONS.LOGOUT,
  //   // ICONS.MY_ACCOUNT,
  // ],
  toolbarIcon: {
    attributes: {
      enable: true,
    },
  },
  attributes: {
    removeClippedSubviewsForIOS: false,
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'shopify/drawer-header',
      clientId: 'drawer-header',
      attributes: {
        viewSingle: true,
        showLoginRegister: !appSettings.getOptionAsBoolean('hide_login'),
        textColor: appSettings.getOption('drawer_header_title_color'),
      },
      dependencies: {
        appStorageState: ['user'],
      },
    },
    // {
    //   attributes: {
    //     blockData: '{{blockData.data}}',
    //     dataSource: {
    //       source: 'shopify',
    //       attributes: {
    //         mapping: 'data',
    //         methodName: 'collections',
    //       },
    //       // repeatable: 'Yes',
    //       repeatItem: 'DataSource',
    //     },
    //     // numColumns: '2',
    //   },
    //   name: 'appmaker/json',
    // },

    {
      name: 'appmaker/drawer-menu',
      clientId: 'normal-btn',
      attributes: {
        items: '{{blockData.data}}',
        title: 'Thank you page',
        viewSingle: true,
        drawerItem: true,
        featureImg: 'https://img.icons8.com/material/4ac144/256/user-male.png',
        imageResize: 'contain',
        __appmakerAttributes: {
          errorViewAttribute: {
            message: 'Oops !',
          },
          emptyViewAttribute: {
            message: 'Empty',
          },
        },
      },
    },
  ],
  stickyFooter: {
    blocks: [
      // {
      //   name: 'appmaker/social-link-bar',
      //   attributes: {},
      // },
      {
        name: 'appmaker/actionbar',
        clientId: 'appmaker-app-settings',
        attributes: {
          __display:
            appSettings.getOptionAsBoolean('enable_currency_switcher') ||
            appSettings.getOptionAsBoolean('show_language_switcher'),
          appmakerAction: {
            action: 'OPEN_APP_SETTINGS',
          },
          title: 'App Settings',
          leftIcon: 'settings',
          drawerItem: true,
        },
      },
    ],
  },
  dataSource: {
    source: 'shopify',
    attributes: {
      // mapping: 'data',
      methodName: 'getNavigationMenu',
    },
    // repeatable: 'Yes',
    // repeatItem: 'DataSource',
  },
};

export default home;
