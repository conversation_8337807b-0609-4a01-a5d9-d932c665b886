import { appSettings } from '@appmaker-xyz/core';
const page = {
  type: 'normal',
  title: 'Settings',
  attributes: {
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      backgroundColor: '#ffffff',
    },
  },
  blocks: [
    {
      name: 'appmaker/blocksView',
      attributes: {
        renderType: 'normal',
        rootContainerStyle: {
          flex: 2,
        },
        contentContainerStyle: {
          flex: 1,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/actionbar',
          attributes: {
            __display: appSettings.getOptionAsBoolean('show_language_switcher'),
            appmakerAction: {
              action: 'OPEN_LANGUAGES_PAGE',
            },
            title: 'Change App Language',
            leftIcon: 'globe',
          },
        },
      ],
    },
  ],
};
export default page;
