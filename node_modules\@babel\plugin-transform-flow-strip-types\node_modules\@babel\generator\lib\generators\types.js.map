{"version": 3, "names": ["_t", "require", "_jsesc", "isAssignmentPattern", "isIdentifier", "Identifier", "node", "_node$loc", "sourceIdentifierName", "loc", "identifierName", "name", "word", "ArgumentPlaceholder", "token", "RestElement", "print", "argument", "ObjectExpression", "props", "properties", "length", "space", "printList", "indent", "statement", "sourceWithOffset", "ObjectMethod", "printJoin", "decorators", "_methodHead", "body", "ObjectProperty", "computed", "key", "value", "left", "shorthand", "ArrayExpression", "elems", "elements", "len", "i", "elem", "RecordExpression", "startToken", "endToken", "format", "recordAndTupleSyntaxType", "Error", "JSON", "stringify", "TupleExpression", "RegExpLiteral", "pattern", "flags", "<PERSON>olean<PERSON>iter<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NumericLiteral", "raw", "getPossibleRaw", "opts", "jsescOption", "numbers", "number", "jsesc", "minified", "StringLiteral", "undefined", "val", "BigIntLiteral", "DecimalLiteral", "validTopicTokenSet", "Set", "TopicReference", "topicToken", "has", "givenTopicTokenJSON", "validTopics", "Array", "from", "v", "join", "PipelineTopicExpression", "expression", "PipelineBareFunction", "callee", "PipelinePrimaryTopicReference"], "sources": ["../../src/generators/types.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport { isAssignmentPattern, isIdentifier } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport jsesc from \"jsesc\";\n\nexport function Identifier(this: Printer, node: t.Identifier) {\n  this.sourceIdentifierName(node.loc?.identifierName || node.name);\n  this.word(node.name);\n}\n\nexport function ArgumentPlaceholder(this: Printer) {\n  this.token(\"?\");\n}\n\nexport function RestElement(this: Printer, node: t.RestElement) {\n  this.token(\"...\");\n  this.print(node.argument, node);\n}\n\nexport { RestElement as SpreadElement };\n\nexport function ObjectExpression(this: Printer, node: t.ObjectExpression) {\n  const props = node.properties;\n\n  this.token(\"{\");\n\n  if (props.length) {\n    this.space();\n    this.printList(props, node, { indent: true, statement: true });\n    this.space();\n  }\n\n  this.sourceWithOffset(\"end\", node.loc, -1);\n\n  this.token(\"}\");\n}\n\nexport { ObjectExpression as ObjectPattern };\n\nexport function ObjectMethod(this: Printer, node: t.ObjectMethod) {\n  this.printJoin(node.decorators, node);\n  this._methodHead(node);\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function ObjectProperty(this: Printer, node: t.ObjectProperty) {\n  this.printJoin(node.decorators, node);\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(node.key, node);\n    this.token(\"]\");\n  } else {\n    // print `({ foo: foo = 5 } = {})` as `({ foo = 5 } = {});`\n    if (\n      isAssignmentPattern(node.value) &&\n      isIdentifier(node.key) &&\n      // @ts-expect-error todo(flow->ts) `.name` does not exist on some types in union\n      node.key.name === node.value.left.name\n    ) {\n      this.print(node.value, node);\n      return;\n    }\n\n    this.print(node.key, node);\n\n    // shorthand!\n    if (\n      node.shorthand &&\n      isIdentifier(node.key) &&\n      isIdentifier(node.value) &&\n      node.key.name === node.value.name\n    ) {\n      return;\n    }\n  }\n\n  this.token(\":\");\n  this.space();\n  this.print(node.value, node);\n}\n\nexport function ArrayExpression(this: Printer, node: t.ArrayExpression) {\n  const elems = node.elements;\n  const len = elems.length;\n\n  this.token(\"[\");\n\n  for (let i = 0; i < elems.length; i++) {\n    const elem = elems[i];\n    if (elem) {\n      if (i > 0) this.space();\n      this.print(elem, node);\n      if (i < len - 1) this.token(\",\");\n    } else {\n      // If the array expression ends with a hole, that hole\n      // will be ignored by the interpreter, but if it ends with\n      // two (or more) holes, we need to write out two (or more)\n      // commas so that the resulting code is interpreted with\n      // both (all) of the holes.\n      this.token(\",\");\n    }\n  }\n\n  this.token(\"]\");\n}\n\nexport { ArrayExpression as ArrayPattern };\n\nexport function RecordExpression(this: Printer, node: t.RecordExpression) {\n  const props = node.properties;\n\n  let startToken;\n  let endToken;\n  if (this.format.recordAndTupleSyntaxType === \"bar\") {\n    startToken = \"{|\";\n    endToken = \"|}\";\n  } else if (\n    this.format.recordAndTupleSyntaxType !== \"hash\" &&\n    this.format.recordAndTupleSyntaxType != null\n  ) {\n    throw new Error(\n      `The \"recordAndTupleSyntaxType\" generator option must be \"bar\" or \"hash\" (${JSON.stringify(\n        this.format.recordAndTupleSyntaxType,\n      )} received).`,\n    );\n  } else {\n    startToken = \"#{\";\n    endToken = \"}\";\n  }\n\n  this.token(startToken);\n\n  if (props.length) {\n    this.space();\n    this.printList(props, node, { indent: true, statement: true });\n    this.space();\n  }\n  this.token(endToken);\n}\n\nexport function TupleExpression(this: Printer, node: t.TupleExpression) {\n  const elems = node.elements;\n  const len = elems.length;\n\n  let startToken;\n  let endToken;\n  if (this.format.recordAndTupleSyntaxType === \"bar\") {\n    startToken = \"[|\";\n    endToken = \"|]\";\n  } else if (this.format.recordAndTupleSyntaxType === \"hash\") {\n    startToken = \"#[\";\n    endToken = \"]\";\n  } else {\n    throw new Error(\n      `${this.format.recordAndTupleSyntaxType} is not a valid recordAndTuple syntax type`,\n    );\n  }\n\n  this.token(startToken);\n\n  for (let i = 0; i < elems.length; i++) {\n    const elem = elems[i];\n    if (elem) {\n      if (i > 0) this.space();\n      this.print(elem, node);\n      if (i < len - 1) this.token(\",\");\n    }\n  }\n\n  this.token(endToken);\n}\n\nexport function RegExpLiteral(this: Printer, node: t.RegExpLiteral) {\n  this.word(`/${node.pattern}/${node.flags}`);\n}\n\nexport function BooleanLiteral(this: Printer, node: t.BooleanLiteral) {\n  this.word(node.value ? \"true\" : \"false\");\n}\n\nexport function NullLiteral(this: Printer) {\n  this.word(\"null\");\n}\n\nexport function NumericLiteral(this: Printer, node: t.NumericLiteral) {\n  const raw = this.getPossibleRaw(node);\n  const opts = this.format.jsescOption;\n  const value = node.value + \"\";\n  if (opts.numbers) {\n    this.number(jsesc(node.value, opts));\n  } else if (raw == null) {\n    this.number(value); // normalize\n  } else if (this.format.minified) {\n    this.number(raw.length < value.length ? raw : value);\n  } else {\n    this.number(raw);\n  }\n}\n\nexport function StringLiteral(this: Printer, node: t.StringLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.token(raw);\n    return;\n  }\n\n  const val = jsesc(node.value, this.format.jsescOption);\n\n  this.token(val);\n}\n\nexport function BigIntLiteral(this: Printer, node: t.BigIntLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.word(raw);\n    return;\n  }\n  this.word(node.value + \"n\");\n}\n\nexport function DecimalLiteral(this: Printer, node: t.DecimalLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.word(raw);\n    return;\n  }\n  this.word(node.value + \"m\");\n}\n\n// Hack pipe operator\nconst validTopicTokenSet = new Set([\"^^\", \"@@\", \"^\", \"%\", \"#\"]);\nexport function TopicReference(this: Printer) {\n  const { topicToken } = this.format;\n\n  if (validTopicTokenSet.has(topicToken)) {\n    this.token(topicToken);\n  } else {\n    const givenTopicTokenJSON = JSON.stringify(topicToken);\n    const validTopics = Array.from(validTopicTokenSet, v => JSON.stringify(v));\n    throw new Error(\n      `The \"topicToken\" generator option must be one of ` +\n        `${validTopics.join(\", \")} (${givenTopicTokenJSON} received instead).`,\n    );\n  }\n}\n\n// Smart-mix pipe operator\nexport function PipelineTopicExpression(\n  this: Printer,\n  node: t.PipelineTopicExpression,\n) {\n  this.print(node.expression, node);\n}\n\nexport function PipelineBareFunction(\n  this: Printer,\n  node: t.PipelineBareFunction,\n) {\n  this.print(node.callee, node);\n}\n\nexport function PipelinePrimaryTopicReference(this: Printer) {\n  this.token(\"#\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAA0B;EAFjBE,mBAAmB;EAAEC;AAAY,IAAAJ,EAAA;AAInC,SAASK,UAAUA,CAAgBC,IAAkB,EAAE;EAAA,IAAAC,SAAA;EAC5D,IAAI,CAACC,oBAAoB,CAAC,EAAAD,SAAA,GAAAD,IAAI,CAACG,GAAG,qBAARF,SAAA,CAAUG,cAAc,KAAIJ,IAAI,CAACK,IAAI,CAAC;EAChE,IAAI,CAACC,IAAI,CAACN,IAAI,CAACK,IAAI,CAAC;AACtB;AAEO,SAASE,mBAAmBA,CAAA,EAAgB;EACjD,IAAI,CAACC,SAAK,GAAI,CAAC;AACjB;AAEO,SAASC,WAAWA,CAAgBT,IAAmB,EAAE;EAC9D,IAAI,CAACQ,KAAK,CAAC,KAAK,CAAC;EACjB,IAAI,CAACE,KAAK,CAACV,IAAI,CAACW,QAAQ,EAAEX,IAAI,CAAC;AACjC;AAIO,SAASY,gBAAgBA,CAAgBZ,IAAwB,EAAE;EACxE,MAAMa,KAAK,GAAGb,IAAI,CAACc,UAAU;EAE7B,IAAI,CAACN,SAAK,IAAI,CAAC;EAEf,IAAIK,KAAK,CAACE,MAAM,EAAE;IAChB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,SAAS,CAACJ,KAAK,EAAEb,IAAI,EAAE;MAAEkB,MAAM,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC9D,IAAI,CAACH,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAACI,gBAAgB,CAAC,KAAK,EAAEpB,IAAI,CAACG,GAAG,EAAE,CAAC,CAAC,CAAC;EAE1C,IAAI,CAACK,SAAK,IAAI,CAAC;AACjB;AAIO,SAASa,YAAYA,CAAgBrB,IAAoB,EAAE;EAChE,IAAI,CAACsB,SAAS,CAACtB,IAAI,CAACuB,UAAU,EAAEvB,IAAI,CAAC;EACrC,IAAI,CAACwB,WAAW,CAACxB,IAAI,CAAC;EACtB,IAAI,CAACgB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACN,KAAK,CAACV,IAAI,CAACyB,IAAI,EAAEzB,IAAI,CAAC;AAC7B;AAEO,SAAS0B,cAAcA,CAAgB1B,IAAsB,EAAE;EACpE,IAAI,CAACsB,SAAS,CAACtB,IAAI,CAACuB,UAAU,EAAEvB,IAAI,CAAC;EAErC,IAAIA,IAAI,CAAC2B,QAAQ,EAAE;IACjB,IAAI,CAACnB,SAAK,GAAI,CAAC;IACf,IAAI,CAACE,KAAK,CAACV,IAAI,CAAC4B,GAAG,EAAE5B,IAAI,CAAC;IAC1B,IAAI,CAACQ,SAAK,GAAI,CAAC;EACjB,CAAC,MAAM;IAEL,IACEX,mBAAmB,CAACG,IAAI,CAAC6B,KAAK,CAAC,IAC/B/B,YAAY,CAACE,IAAI,CAAC4B,GAAG,CAAC,IAEtB5B,IAAI,CAAC4B,GAAG,CAACvB,IAAI,KAAKL,IAAI,CAAC6B,KAAK,CAACC,IAAI,CAACzB,IAAI,EACtC;MACA,IAAI,CAACK,KAAK,CAACV,IAAI,CAAC6B,KAAK,EAAE7B,IAAI,CAAC;MAC5B;IACF;IAEA,IAAI,CAACU,KAAK,CAACV,IAAI,CAAC4B,GAAG,EAAE5B,IAAI,CAAC;IAG1B,IACEA,IAAI,CAAC+B,SAAS,IACdjC,YAAY,CAACE,IAAI,CAAC4B,GAAG,CAAC,IACtB9B,YAAY,CAACE,IAAI,CAAC6B,KAAK,CAAC,IACxB7B,IAAI,CAAC4B,GAAG,CAACvB,IAAI,KAAKL,IAAI,CAAC6B,KAAK,CAACxB,IAAI,EACjC;MACA;IACF;EACF;EAEA,IAAI,CAACG,SAAK,GAAI,CAAC;EACf,IAAI,CAACQ,KAAK,CAAC,CAAC;EACZ,IAAI,CAACN,KAAK,CAACV,IAAI,CAAC6B,KAAK,EAAE7B,IAAI,CAAC;AAC9B;AAEO,SAASgC,eAAeA,CAAgBhC,IAAuB,EAAE;EACtE,MAAMiC,KAAK,GAAGjC,IAAI,CAACkC,QAAQ;EAC3B,MAAMC,GAAG,GAAGF,KAAK,CAAClB,MAAM;EAExB,IAAI,CAACP,SAAK,GAAI,CAAC;EAEf,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAAClB,MAAM,EAAEqB,CAAC,EAAE,EAAE;IACrC,MAAMC,IAAI,GAAGJ,KAAK,CAACG,CAAC,CAAC;IACrB,IAAIC,IAAI,EAAE;MACR,IAAID,CAAC,GAAG,CAAC,EAAE,IAAI,CAACpB,KAAK,CAAC,CAAC;MACvB,IAAI,CAACN,KAAK,CAAC2B,IAAI,EAAErC,IAAI,CAAC;MACtB,IAAIoC,CAAC,GAAGD,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC3B,SAAK,GAAI,CAAC;IAClC,CAAC,MAAM;MAML,IAAI,CAACA,SAAK,GAAI,CAAC;IACjB;EACF;EAEA,IAAI,CAACA,SAAK,GAAI,CAAC;AACjB;AAIO,SAAS8B,gBAAgBA,CAAgBtC,IAAwB,EAAE;EACxE,MAAMa,KAAK,GAAGb,IAAI,CAACc,UAAU;EAE7B,IAAIyB,UAAU;EACd,IAAIC,QAAQ;EACZ,IAAI,IAAI,CAACC,MAAM,CAACC,wBAAwB,KAAK,KAAK,EAAE;IAClDH,UAAU,GAAG,IAAI;IACjBC,QAAQ,GAAG,IAAI;EACjB,CAAC,MAAM,IACL,IAAI,CAACC,MAAM,CAACC,wBAAwB,KAAK,MAAM,IAC/C,IAAI,CAACD,MAAM,CAACC,wBAAwB,IAAI,IAAI,EAC5C;IACA,MAAM,IAAIC,KAAK,CACZ,4EAA2EC,IAAI,CAACC,SAAS,CACxF,IAAI,CAACJ,MAAM,CAACC,wBACd,CAAE,aACJ,CAAC;EACH,CAAC,MAAM;IACLH,UAAU,GAAG,IAAI;IACjBC,QAAQ,GAAG,GAAG;EAChB;EAEA,IAAI,CAAChC,KAAK,CAAC+B,UAAU,CAAC;EAEtB,IAAI1B,KAAK,CAACE,MAAM,EAAE;IAChB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,SAAS,CAACJ,KAAK,EAAEb,IAAI,EAAE;MAAEkB,MAAM,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC9D,IAAI,CAACH,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACR,KAAK,CAACgC,QAAQ,CAAC;AACtB;AAEO,SAASM,eAAeA,CAAgB9C,IAAuB,EAAE;EACtE,MAAMiC,KAAK,GAAGjC,IAAI,CAACkC,QAAQ;EAC3B,MAAMC,GAAG,GAAGF,KAAK,CAAClB,MAAM;EAExB,IAAIwB,UAAU;EACd,IAAIC,QAAQ;EACZ,IAAI,IAAI,CAACC,MAAM,CAACC,wBAAwB,KAAK,KAAK,EAAE;IAClDH,UAAU,GAAG,IAAI;IACjBC,QAAQ,GAAG,IAAI;EACjB,CAAC,MAAM,IAAI,IAAI,CAACC,MAAM,CAACC,wBAAwB,KAAK,MAAM,EAAE;IAC1DH,UAAU,GAAG,IAAI;IACjBC,QAAQ,GAAG,GAAG;EAChB,CAAC,MAAM;IACL,MAAM,IAAIG,KAAK,CACZ,GAAE,IAAI,CAACF,MAAM,CAACC,wBAAyB,4CAC1C,CAAC;EACH;EAEA,IAAI,CAAClC,KAAK,CAAC+B,UAAU,CAAC;EAEtB,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAAClB,MAAM,EAAEqB,CAAC,EAAE,EAAE;IACrC,MAAMC,IAAI,GAAGJ,KAAK,CAACG,CAAC,CAAC;IACrB,IAAIC,IAAI,EAAE;MACR,IAAID,CAAC,GAAG,CAAC,EAAE,IAAI,CAACpB,KAAK,CAAC,CAAC;MACvB,IAAI,CAACN,KAAK,CAAC2B,IAAI,EAAErC,IAAI,CAAC;MACtB,IAAIoC,CAAC,GAAGD,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC3B,SAAK,GAAI,CAAC;IAClC;EACF;EAEA,IAAI,CAACA,KAAK,CAACgC,QAAQ,CAAC;AACtB;AAEO,SAASO,aAAaA,CAAgB/C,IAAqB,EAAE;EAClE,IAAI,CAACM,IAAI,CAAE,IAAGN,IAAI,CAACgD,OAAQ,IAAGhD,IAAI,CAACiD,KAAM,EAAC,CAAC;AAC7C;AAEO,SAASC,cAAcA,CAAgBlD,IAAsB,EAAE;EACpE,IAAI,CAACM,IAAI,CAACN,IAAI,CAAC6B,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;AAC1C;AAEO,SAASsB,WAAWA,CAAA,EAAgB;EACzC,IAAI,CAAC7C,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAAS8C,cAAcA,CAAgBpD,IAAsB,EAAE;EACpE,MAAMqD,GAAG,GAAG,IAAI,CAACC,cAAc,CAACtD,IAAI,CAAC;EACrC,MAAMuD,IAAI,GAAG,IAAI,CAACd,MAAM,CAACe,WAAW;EACpC,MAAM3B,KAAK,GAAG7B,IAAI,CAAC6B,KAAK,GAAG,EAAE;EAC7B,IAAI0B,IAAI,CAACE,OAAO,EAAE;IAChB,IAAI,CAACC,MAAM,CAACC,MAAK,CAAC3D,IAAI,CAAC6B,KAAK,EAAE0B,IAAI,CAAC,CAAC;EACtC,CAAC,MAAM,IAAIF,GAAG,IAAI,IAAI,EAAE;IACtB,IAAI,CAACK,MAAM,CAAC7B,KAAK,CAAC;EACpB,CAAC,MAAM,IAAI,IAAI,CAACY,MAAM,CAACmB,QAAQ,EAAE;IAC/B,IAAI,CAACF,MAAM,CAACL,GAAG,CAACtC,MAAM,GAAGc,KAAK,CAACd,MAAM,GAAGsC,GAAG,GAAGxB,KAAK,CAAC;EACtD,CAAC,MAAM;IACL,IAAI,CAAC6B,MAAM,CAACL,GAAG,CAAC;EAClB;AACF;AAEO,SAASQ,aAAaA,CAAgB7D,IAAqB,EAAE;EAClE,MAAMqD,GAAG,GAAG,IAAI,CAACC,cAAc,CAACtD,IAAI,CAAC;EACrC,IAAI,CAAC,IAAI,CAACyC,MAAM,CAACmB,QAAQ,IAAIP,GAAG,KAAKS,SAAS,EAAE;IAC9C,IAAI,CAACtD,KAAK,CAAC6C,GAAG,CAAC;IACf;EACF;EAEA,MAAMU,GAAG,GAAGJ,MAAK,CAAC3D,IAAI,CAAC6B,KAAK,EAAE,IAAI,CAACY,MAAM,CAACe,WAAW,CAAC;EAEtD,IAAI,CAAChD,KAAK,CAACuD,GAAG,CAAC;AACjB;AAEO,SAASC,aAAaA,CAAgBhE,IAAqB,EAAE;EAClE,MAAMqD,GAAG,GAAG,IAAI,CAACC,cAAc,CAACtD,IAAI,CAAC;EACrC,IAAI,CAAC,IAAI,CAACyC,MAAM,CAACmB,QAAQ,IAAIP,GAAG,KAAKS,SAAS,EAAE;IAC9C,IAAI,CAACxD,IAAI,CAAC+C,GAAG,CAAC;IACd;EACF;EACA,IAAI,CAAC/C,IAAI,CAACN,IAAI,CAAC6B,KAAK,GAAG,GAAG,CAAC;AAC7B;AAEO,SAASoC,cAAcA,CAAgBjE,IAAsB,EAAE;EACpE,MAAMqD,GAAG,GAAG,IAAI,CAACC,cAAc,CAACtD,IAAI,CAAC;EACrC,IAAI,CAAC,IAAI,CAACyC,MAAM,CAACmB,QAAQ,IAAIP,GAAG,KAAKS,SAAS,EAAE;IAC9C,IAAI,CAACxD,IAAI,CAAC+C,GAAG,CAAC;IACd;EACF;EACA,IAAI,CAAC/C,IAAI,CAACN,IAAI,CAAC6B,KAAK,GAAG,GAAG,CAAC;AAC7B;AAGA,MAAMqC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxD,SAASC,cAAcA,CAAA,EAAgB;EAC5C,MAAM;IAAEC;EAAW,CAAC,GAAG,IAAI,CAAC5B,MAAM;EAElC,IAAIyB,kBAAkB,CAACI,GAAG,CAACD,UAAU,CAAC,EAAE;IACtC,IAAI,CAAC7D,KAAK,CAAC6D,UAAU,CAAC;EACxB,CAAC,MAAM;IACL,MAAME,mBAAmB,GAAG3B,IAAI,CAACC,SAAS,CAACwB,UAAU,CAAC;IACtD,MAAMG,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACR,kBAAkB,EAAES,CAAC,IAAI/B,IAAI,CAACC,SAAS,CAAC8B,CAAC,CAAC,CAAC;IAC1E,MAAM,IAAIhC,KAAK,CACZ,mDAAkD,GAChD,GAAE6B,WAAW,CAACI,IAAI,CAAC,IAAI,CAAE,KAAIL,mBAAoB,qBACtD,CAAC;EACH;AACF;AAGO,SAASM,uBAAuBA,CAErC7E,IAA+B,EAC/B;EACA,IAAI,CAACU,KAAK,CAACV,IAAI,CAAC8E,UAAU,EAAE9E,IAAI,CAAC;AACnC;AAEO,SAAS+E,oBAAoBA,CAElC/E,IAA4B,EAC5B;EACA,IAAI,CAACU,KAAK,CAACV,IAAI,CAACgF,MAAM,EAAEhF,IAAI,CAAC;AAC/B;AAEO,SAASiF,6BAA6BA,CAAA,EAAgB;EAC3D,IAAI,CAACzE,SAAK,GAAI,CAAC;AACjB"}