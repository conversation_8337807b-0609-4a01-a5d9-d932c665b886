declare module '@appmaker-xyz/shopify' {
  export interface Product {
    node?: {
      images?: {
        edges: Array<{
          node: {
            src: string;
            width: number;
            height: number;
            altText?: string;
          };
        }>;
      };
      title?: string;
      onlineStoreUrl?: string;
      media?: {
        edges: Array<{
          node: {
            mediaContentType: string;
            image?: {
              originalSrc: string;
            };
            previewImage?: {
              originalSrc: string;
            };
            sources?: Array<{
              url: string;
            }>;
          };
        }>;
      };
    };
  }

  export interface Variant {
    node?: {
      image?: {
        src: string;
      };
      sku?: string;
    };
  }

  export interface Image {
    thumbnail: string;
    width: number;
    height: number;
    uri: string;
    altText?: string;
  }
}