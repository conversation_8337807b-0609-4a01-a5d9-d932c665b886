import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ThemeText, UiKitButton } from '@appmaker-xyz/ui';
import WebView from 'react-native-webview';
import Modal from 'react-native-modal';
import Icon from 'react-native-vector-icons/Feather';
import { shopifyIdHelper } from '@appmaker-xyz/shopify';
import { usePageState, usePluginStore } from '@appmaker-xyz/core';

function formatDataForProduct(jsonData) {
  const formated = {};
  Object.keys(jsonData).forEach((key) => {
    if (jsonData[key] === '' || !key.match(/properties/)) {
    } else {
      const newKey = key.replace('properties[', '').replace(']', '');
      formated[newKey] = jsonData[key];
    }
  });
  return formated;
}
function getBookingUrl({ shopId, productId, variantId }) {
  const BASE_URL = 'https://app-static-pages.appmaker.xyz/shopify/sesami/';
  return `${BASE_URL}?shopId=${shopId}&productId=${productId}&variantId=${variantId}`;
}
const TimeSlotBooking = ({ attributes, onPress, onAction }) => {
  const [timeSlot, setTimeSlot] = useState('');
  const [visible, setVisibility] = useState(false);
  const open = () => setVisibility(true);
  const close = () => setVisibility(false);
  const { appmakerAction } = attributes;

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  const setpageState = usePageState((state) => state.setState);
  const onMessage = (event) => {
    const { data } = event.nativeEvent;

    const jsonData = JSON.parse(data);
    if (jsonData['properties[Date]']) {
      setTimeSlot(
        `${jsonData['properties[Date]']} Time ${jsonData['properties[Time]']}`,
      );
    }
    setVisibility(false);
    const productAttributes = formatDataForProduct(jsonData);
    setpageState((state) => {
      if (!state.productAttributes) {
        state.productAttributes = {};
      }
      state.productAttributes = {
        ...state.productAttributes,
        ...productAttributes,
      };
    });
  };
  const { productId, variantId } = attributes;
  const shopId = usePluginStore(
    (state) => state.plugins['sesami-booking'].settings.shopId,
  );
  let normalProductId = '',
    normalVariantId = '';
  try {
    normalProductId = shopifyIdHelper(productId, true);
    normalVariantId = shopifyIdHelper(variantId, true);
  } catch (error) {
    console.log(error);
  }
  console.log('timeSlot', timeSlot);
  return attributes?.tags.match('sesami-service') ? (
    <Layout style={styles.container}>
      <Layout style={styles.barContainer}>
        <ThemeText size="lg" fontFamily="medium" style={styles.title}>
          Appointment Time
        </ThemeText>
        <Layout style={styles.barContainer}>
          {timeSlot ? (
            <ThemeText fontFamily="bold" style={styles.timeText}>
              {timeSlot.replace(/:/, ' ')}
            </ThemeText>
          ) : (
            <UiKitButton onPress={open} small status="dark">
              Pick a time
            </UiKitButton>
          )}
          {timeSlot ? (
            <Icon
              name="x"
              size={12}
              onPress={() => setTimeSlot('')}
              style={styles.icon}
            />
          ) : null}
        </Layout>
      </Layout>
      <Modal
        testID={'modal'}
        isVisible={visible}
        onSwipeComplete={close}
        onBackButtonPress={close}
        backdropTransitionOutTiming={0}
        onBackdropPress={close}
        style={styles.view}>
        <WebView
          source={{
            uri: getBookingUrl({
              shopId,
              productId: normalProductId,
              variantId: normalVariantId,
            }),
          }}
          style={{ width: '100%' }}
          onMessage={onMessage}
        />
      </Modal>
    </Layout>
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    padding: 12,
    flex: 1,
    marginBottom: 4,
  },
  barContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: { flex: 1 },
  view: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  icon: {
    marginLeft: 4,
    padding: 6,
  },
  timeText: {
    backgroundColor: '#E9EDF1',
    padding: 4,
    borderRadius: 4,
  },
});

export default TimeSlotBooking;
