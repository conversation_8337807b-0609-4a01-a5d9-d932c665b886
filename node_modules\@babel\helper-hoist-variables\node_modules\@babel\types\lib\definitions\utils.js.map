{"version": 3, "names": ["_is", "require", "_validate", "VISITOR_KEYS", "exports", "ALIAS_KEYS", "FLIPPED_ALIAS_KEYS", "NODE_FIELDS", "BUILDER_KEYS", "DEPRECATED_KEYS", "NODE_PARENT_VALIDATIONS", "getType", "val", "Array", "isArray", "validate", "typeIs", "typeName", "assertNodeType", "validateType", "validateOptional", "optional", "validateOptionalType", "arrayOf", "elementType", "chain", "assertValueType", "assertEach", "arrayOfType", "validateArrayOfType", "callback", "validator", "node", "key", "i", "length", "subkey", "v", "process", "env", "BABEL_TYPES_8_BREAKING", "validate<PERSON><PERSON><PERSON>", "each", "assertOneOf", "values", "indexOf", "TypeError", "JSON", "stringify", "oneOf", "types", "type", "is", "oneOfNodeTypes", "assertNodeOrValueType", "oneOfNodeOrValueTypes", "valid", "assertShape", "shape", "errors", "property", "Object", "keys", "validateField", "error", "push", "message", "join", "shapeOf", "assertOptionalChainStart", "_current", "current", "callee", "object", "fns", "args", "fn", "chainOf", "Error", "validTypeOpts", "valid<PERSON>ield<PERSON>eys", "store", "defineAliasedType", "aliases", "opts", "defined", "_store$opts$inherits$", "_defined", "inherits", "slice", "additional", "filter", "a", "includes", "unshift", "defineType", "fields", "getOwnPropertyNames", "field", "def", "default", "deprecated", "visitor", "builder", "k", "depre<PERSON><PERSON><PERSON><PERSON>", "concat", "undefined", "for<PERSON>ach", "alias"], "sources": ["../../src/definitions/utils.ts"], "sourcesContent": ["import is from \"../validators/is\";\nimport { validateField, validateChild } from \"../validators/validate\";\nimport type * as t from \"..\";\n\nexport const VISITOR_KEYS: Record<string, string[]> = {};\nexport const ALIAS_KEYS: Partial<Record<NodeTypesWithoutComment, string[]>> =\n  {};\nexport const FLIPPED_ALIAS_KEYS: Record<string, NodeTypesWithoutComment[]> = {};\nexport const NODE_FIELDS: Record<string, FieldDefinitions> = {};\nexport const BUILDER_KEYS: Record<string, string[]> = {};\nexport const DEPRECATED_KEYS: Record<string, NodeTypesWithoutComment> = {};\nexport const NODE_PARENT_VALIDATIONS: Record<string, Validator> = {};\n\nfunction getType(val: any) {\n  if (Array.isArray(val)) {\n    return \"array\";\n  } else if (val === null) {\n    return \"null\";\n  } else {\n    return typeof val;\n  }\n}\n\ntype NodeTypesWithoutComment = t.Node[\"type\"] | keyof t.Aliases;\n\ntype NodeTypes = NodeTypesWithoutComment | t.Comment[\"type\"];\n\ntype PrimitiveTypes = ReturnType<typeof getType>;\n\ntype FieldDefinitions = {\n  [x: string]: FieldOptions;\n};\n\ntype DefineTypeOpts = {\n  fields?: FieldDefinitions;\n  visitor?: Array<string>;\n  aliases?: Array<string>;\n  builder?: Array<string>;\n  inherits?: NodeTypes;\n  deprecatedAlias?: string;\n  validate?: Validator;\n};\n\nexport type Validator = (\n  | { type: PrimitiveTypes }\n  | { each: Validator }\n  | { chainOf: Validator[] }\n  | { oneOf: any[] }\n  | { oneOfNodeTypes: NodeTypes[] }\n  | { oneOfNodeOrValueTypes: (NodeTypes | PrimitiveTypes)[] }\n  | { shapeOf: { [x: string]: FieldOptions } }\n  | {}\n) &\n  ((node: t.Node, key: string, val: any) => void);\n\nexport type FieldOptions = {\n  default?: string | number | boolean | [];\n  optional?: boolean;\n  deprecated?: boolean;\n  validate?: Validator;\n};\n\nexport function validate(validate: Validator): FieldOptions {\n  return { validate };\n}\n\nexport function typeIs(typeName: NodeTypes | NodeTypes[]) {\n  return typeof typeName === \"string\"\n    ? assertNodeType(typeName)\n    : assertNodeType(...typeName);\n}\n\nexport function validateType(typeName: NodeTypes | NodeTypes[]) {\n  return validate(typeIs(typeName));\n}\n\nexport function validateOptional(validate: Validator): FieldOptions {\n  return { validate, optional: true };\n}\n\nexport function validateOptionalType(\n  typeName: NodeTypes | NodeTypes[],\n): FieldOptions {\n  return { validate: typeIs(typeName), optional: true };\n}\n\nexport function arrayOf(elementType: Validator): Validator {\n  return chain(assertValueType(\"array\"), assertEach(elementType));\n}\n\nexport function arrayOfType(typeName: NodeTypes | NodeTypes[]) {\n  return arrayOf(typeIs(typeName));\n}\n\nexport function validateArrayOfType(typeName: NodeTypes | NodeTypes[]) {\n  return validate(arrayOfType(typeName));\n}\n\nexport function assertEach(callback: Validator): Validator {\n  function validator(node: t.Node, key: string, val: any) {\n    if (!Array.isArray(val)) return;\n\n    for (let i = 0; i < val.length; i++) {\n      const subkey = `${key}[${i}]`;\n      const v = val[i];\n      callback(node, subkey, v);\n      if (process.env.BABEL_TYPES_8_BREAKING) validateChild(node, subkey, v);\n    }\n  }\n  validator.each = callback;\n  return validator;\n}\n\nexport function assertOneOf(...values: Array<any>): Validator {\n  function validate(node: any, key: string, val: any) {\n    if (values.indexOf(val) < 0) {\n      throw new TypeError(\n        `Property ${key} expected value to be one of ${JSON.stringify(\n          values,\n        )} but got ${JSON.stringify(val)}`,\n      );\n    }\n  }\n\n  validate.oneOf = values;\n\n  return validate;\n}\n\nexport function assertNodeType(...types: NodeTypes[]): Validator {\n  function validate(node: t.Node, key: string, val: any) {\n    for (const type of types) {\n      if (is(type, val)) {\n        validateChild(node, key, val);\n        return;\n      }\n    }\n\n    throw new TypeError(\n      `Property ${key} of ${\n        node.type\n      } expected node to be of a type ${JSON.stringify(\n        types,\n      )} but instead got ${JSON.stringify(val?.type)}`,\n    );\n  }\n\n  validate.oneOfNodeTypes = types;\n\n  return validate;\n}\n\nexport function assertNodeOrValueType(\n  ...types: (NodeTypes | PrimitiveTypes)[]\n): Validator {\n  function validate(node: t.Node, key: string, val: any) {\n    for (const type of types) {\n      if (getType(val) === type || is(type, val)) {\n        validateChild(node, key, val);\n        return;\n      }\n    }\n\n    throw new TypeError(\n      `Property ${key} of ${\n        node.type\n      } expected node to be of a type ${JSON.stringify(\n        types,\n      )} but instead got ${JSON.stringify(val?.type)}`,\n    );\n  }\n\n  validate.oneOfNodeOrValueTypes = types;\n\n  return validate;\n}\n\nexport function assertValueType(type: PrimitiveTypes): Validator {\n  function validate(node: t.Node, key: string, val: any) {\n    const valid = getType(val) === type;\n\n    if (!valid) {\n      throw new TypeError(\n        `Property ${key} expected type of ${type} but got ${getType(val)}`,\n      );\n    }\n  }\n\n  validate.type = type;\n\n  return validate;\n}\n\nexport function assertShape(shape: { [x: string]: FieldOptions }): Validator {\n  function validate(node: t.Node, key: string, val: any) {\n    const errors = [];\n    for (const property of Object.keys(shape)) {\n      try {\n        validateField(node, property, val[property], shape[property]);\n      } catch (error) {\n        if (error instanceof TypeError) {\n          errors.push(error.message);\n          continue;\n        }\n        throw error;\n      }\n    }\n    if (errors.length) {\n      throw new TypeError(\n        `Property ${key} of ${\n          node.type\n        } expected to have the following:\\n${errors.join(\"\\n\")}`,\n      );\n    }\n  }\n\n  validate.shapeOf = shape;\n\n  return validate;\n}\n\nexport function assertOptionalChainStart(): Validator {\n  function validate(node: t.Node) {\n    let current = node;\n    while (node) {\n      const { type } = current;\n      if (type === \"OptionalCallExpression\") {\n        if (current.optional) return;\n        current = current.callee;\n        continue;\n      }\n\n      if (type === \"OptionalMemberExpression\") {\n        if (current.optional) return;\n        current = current.object;\n        continue;\n      }\n\n      break;\n    }\n\n    throw new TypeError(\n      `Non-optional ${node.type} must chain from an optional OptionalMemberExpression or OptionalCallExpression. Found chain from ${current?.type}`,\n    );\n  }\n\n  return validate;\n}\n\nexport function chain(...fns: Array<Validator>): Validator {\n  function validate(...args: Parameters<Validator>) {\n    for (const fn of fns) {\n      fn(...args);\n    }\n  }\n  validate.chainOf = fns;\n\n  if (\n    fns.length >= 2 &&\n    \"type\" in fns[0] &&\n    fns[0].type === \"array\" &&\n    !(\"each\" in fns[1])\n  ) {\n    throw new Error(\n      `An assertValueType(\"array\") validator can only be followed by an assertEach(...) validator.`,\n    );\n  }\n\n  return validate;\n}\n\nconst validTypeOpts = [\n  \"aliases\",\n  \"builder\",\n  \"deprecatedAlias\",\n  \"fields\",\n  \"inherits\",\n  \"visitor\",\n  \"validate\",\n];\nconst validFieldKeys = [\"default\", \"optional\", \"deprecated\", \"validate\"];\n\nconst store = {} as Record<string, DefineTypeOpts>;\n\n// Wraps defineType to ensure these aliases are included.\nexport function defineAliasedType(...aliases: string[]) {\n  return (type: string, opts: DefineTypeOpts = {}) => {\n    let defined = opts.aliases;\n    if (!defined) {\n      if (opts.inherits) defined = store[opts.inherits].aliases?.slice();\n      defined ??= [];\n      opts.aliases = defined;\n    }\n    const additional = aliases.filter(a => !defined.includes(a));\n    defined.unshift(...additional);\n    defineType(type, opts);\n  };\n}\n\nexport default function defineType(type: string, opts: DefineTypeOpts = {}) {\n  const inherits = (opts.inherits && store[opts.inherits]) || {};\n\n  let fields = opts.fields;\n  if (!fields) {\n    fields = {};\n    if (inherits.fields) {\n      const keys = Object.getOwnPropertyNames(inherits.fields);\n      for (const key of keys) {\n        const field = inherits.fields[key];\n        const def = field.default;\n        if (\n          Array.isArray(def) ? def.length > 0 : def && typeof def === \"object\"\n        ) {\n          throw new Error(\n            \"field defaults can only be primitives or empty arrays currently\",\n          );\n        }\n        fields[key] = {\n          default: Array.isArray(def) ? [] : def,\n          optional: field.optional,\n          deprecated: field.deprecated,\n          validate: field.validate,\n        };\n      }\n    }\n  }\n\n  const visitor: Array<string> = opts.visitor || inherits.visitor || [];\n  const aliases: Array<string> = opts.aliases || inherits.aliases || [];\n  const builder: Array<string> =\n    opts.builder || inherits.builder || opts.visitor || [];\n\n  for (const k of Object.keys(opts)) {\n    if (validTypeOpts.indexOf(k) === -1) {\n      throw new Error(`Unknown type option \"${k}\" on ${type}`);\n    }\n  }\n\n  if (opts.deprecatedAlias) {\n    DEPRECATED_KEYS[opts.deprecatedAlias] = type as NodeTypesWithoutComment;\n  }\n\n  // ensure all field keys are represented in `fields`\n  for (const key of visitor.concat(builder)) {\n    fields[key] = fields[key] || {};\n  }\n\n  for (const key of Object.keys(fields)) {\n    const field = fields[key];\n\n    if (field.default !== undefined && builder.indexOf(key) === -1) {\n      field.optional = true;\n    }\n    if (field.default === undefined) {\n      field.default = null;\n    } else if (!field.validate && field.default != null) {\n      field.validate = assertValueType(getType(field.default));\n    }\n\n    for (const k of Object.keys(field)) {\n      if (validFieldKeys.indexOf(k) === -1) {\n        throw new Error(`Unknown field key \"${k}\" on ${type}.${key}`);\n      }\n    }\n  }\n\n  VISITOR_KEYS[type] = opts.visitor = visitor;\n  BUILDER_KEYS[type] = opts.builder = builder;\n  NODE_FIELDS[type] = opts.fields = fields;\n  ALIAS_KEYS[type as NodeTypesWithoutComment] = opts.aliases = aliases;\n  aliases.forEach(alias => {\n    FLIPPED_ALIAS_KEYS[alias] = FLIPPED_ALIAS_KEYS[alias] || [];\n    FLIPPED_ALIAS_KEYS[alias].push(type as NodeTypesWithoutComment);\n  });\n\n  if (opts.validate) {\n    NODE_PARENT_VALIDATIONS[type] = opts.validate;\n  }\n\n  store[type] = opts;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,GAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AAGO,MAAME,YAAsC,GAAG,CAAC,CAAC;AAACC,OAAA,CAAAD,YAAA,GAAAA,YAAA;AAClD,MAAME,UAA8D,GACzE,CAAC,CAAC;AAACD,OAAA,CAAAC,UAAA,GAAAA,UAAA;AACE,MAAMC,kBAA6D,GAAG,CAAC,CAAC;AAACF,OAAA,CAAAE,kBAAA,GAAAA,kBAAA;AACzE,MAAMC,WAA6C,GAAG,CAAC,CAAC;AAACH,OAAA,CAAAG,WAAA,GAAAA,WAAA;AACzD,MAAMC,YAAsC,GAAG,CAAC,CAAC;AAACJ,OAAA,CAAAI,YAAA,GAAAA,YAAA;AAClD,MAAMC,eAAwD,GAAG,CAAC,CAAC;AAACL,OAAA,CAAAK,eAAA,GAAAA,eAAA;AACpE,MAAMC,uBAAkD,GAAG,CAAC,CAAC;AAACN,OAAA,CAAAM,uBAAA,GAAAA,uBAAA;AAErE,SAASC,OAAOA,CAACC,GAAQ,EAAE;EACzB,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;IACtB,OAAO,OAAO;EAChB,CAAC,MAAM,IAAIA,GAAG,KAAK,IAAI,EAAE;IACvB,OAAO,MAAM;EACf,CAAC,MAAM;IACL,OAAO,OAAOA,GAAG;EACnB;AACF;AAyCO,SAASG,QAAQA,CAACA,QAAmB,EAAgB;EAC1D,OAAO;IAAEA;EAAS,CAAC;AACrB;AAEO,SAASC,MAAMA,CAACC,QAAiC,EAAE;EACxD,OAAO,OAAOA,QAAQ,KAAK,QAAQ,GAC/BC,cAAc,CAACD,QAAQ,CAAC,GACxBC,cAAc,CAAC,GAAGD,QAAQ,CAAC;AACjC;AAEO,SAASE,YAAYA,CAACF,QAAiC,EAAE;EAC9D,OAAOF,QAAQ,CAACC,MAAM,CAACC,QAAQ,CAAC,CAAC;AACnC;AAEO,SAASG,gBAAgBA,CAACL,QAAmB,EAAgB;EAClE,OAAO;IAAEA,QAAQ;IAAEM,QAAQ,EAAE;EAAK,CAAC;AACrC;AAEO,SAASC,oBAAoBA,CAClCL,QAAiC,EACnB;EACd,OAAO;IAAEF,QAAQ,EAAEC,MAAM,CAACC,QAAQ,CAAC;IAAEI,QAAQ,EAAE;EAAK,CAAC;AACvD;AAEO,SAASE,OAAOA,CAACC,WAAsB,EAAa;EACzD,OAAOC,KAAK,CAACC,eAAe,CAAC,OAAO,CAAC,EAAEC,UAAU,CAACH,WAAW,CAAC,CAAC;AACjE;AAEO,SAASI,WAAWA,CAACX,QAAiC,EAAE;EAC7D,OAAOM,OAAO,CAACP,MAAM,CAACC,QAAQ,CAAC,CAAC;AAClC;AAEO,SAASY,mBAAmBA,CAACZ,QAAiC,EAAE;EACrE,OAAOF,QAAQ,CAACa,WAAW,CAACX,QAAQ,CAAC,CAAC;AACxC;AAEO,SAASU,UAAUA,CAACG,QAAmB,EAAa;EACzD,SAASC,SAASA,CAACC,IAAY,EAAEC,GAAW,EAAErB,GAAQ,EAAE;IACtD,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;IAEzB,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,GAAG,CAACuB,MAAM,EAAED,CAAC,EAAE,EAAE;MACnC,MAAME,MAAM,GAAI,GAAEH,GAAI,IAAGC,CAAE,GAAE;MAC7B,MAAMG,CAAC,GAAGzB,GAAG,CAACsB,CAAC,CAAC;MAChBJ,QAAQ,CAACE,IAAI,EAAEI,MAAM,EAAEC,CAAC,CAAC;MACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE,IAAAC,uBAAa,EAACT,IAAI,EAAEI,MAAM,EAAEC,CAAC,CAAC;IACxE;EACF;EACAN,SAAS,CAACW,IAAI,GAAGZ,QAAQ;EACzB,OAAOC,SAAS;AAClB;AAEO,SAASY,WAAWA,CAAC,GAAGC,MAAkB,EAAa;EAC5D,SAAS7B,QAAQA,CAACiB,IAAS,EAAEC,GAAW,EAAErB,GAAQ,EAAE;IAClD,IAAIgC,MAAM,CAACC,OAAO,CAACjC,GAAG,CAAC,GAAG,CAAC,EAAE;MAC3B,MAAM,IAAIkC,SAAS,CAChB,YAAWb,GAAI,gCAA+Bc,IAAI,CAACC,SAAS,CAC3DJ,MACF,CAAE,YAAWG,IAAI,CAACC,SAAS,CAACpC,GAAG,CAAE,EACnC,CAAC;IACH;EACF;EAEAG,QAAQ,CAACkC,KAAK,GAAGL,MAAM;EAEvB,OAAO7B,QAAQ;AACjB;AAEO,SAASG,cAAcA,CAAC,GAAGgC,KAAkB,EAAa;EAC/D,SAASnC,QAAQA,CAACiB,IAAY,EAAEC,GAAW,EAAErB,GAAQ,EAAE;IACrD,KAAK,MAAMuC,IAAI,IAAID,KAAK,EAAE;MACxB,IAAI,IAAAE,WAAE,EAACD,IAAI,EAAEvC,GAAG,CAAC,EAAE;QACjB,IAAA6B,uBAAa,EAACT,IAAI,EAAEC,GAAG,EAAErB,GAAG,CAAC;QAC7B;MACF;IACF;IAEA,MAAM,IAAIkC,SAAS,CAChB,YAAWb,GAAI,OACdD,IAAI,CAACmB,IACN,kCAAiCJ,IAAI,CAACC,SAAS,CAC9CE,KACF,CAAE,oBAAmBH,IAAI,CAACC,SAAS,CAACpC,GAAG,oBAAHA,GAAG,CAAEuC,IAAI,CAAE,EACjD,CAAC;EACH;EAEApC,QAAQ,CAACsC,cAAc,GAAGH,KAAK;EAE/B,OAAOnC,QAAQ;AACjB;AAEO,SAASuC,qBAAqBA,CACnC,GAAGJ,KAAqC,EAC7B;EACX,SAASnC,QAAQA,CAACiB,IAAY,EAAEC,GAAW,EAAErB,GAAQ,EAAE;IACrD,KAAK,MAAMuC,IAAI,IAAID,KAAK,EAAE;MACxB,IAAIvC,OAAO,CAACC,GAAG,CAAC,KAAKuC,IAAI,IAAI,IAAAC,WAAE,EAACD,IAAI,EAAEvC,GAAG,CAAC,EAAE;QAC1C,IAAA6B,uBAAa,EAACT,IAAI,EAAEC,GAAG,EAAErB,GAAG,CAAC;QAC7B;MACF;IACF;IAEA,MAAM,IAAIkC,SAAS,CAChB,YAAWb,GAAI,OACdD,IAAI,CAACmB,IACN,kCAAiCJ,IAAI,CAACC,SAAS,CAC9CE,KACF,CAAE,oBAAmBH,IAAI,CAACC,SAAS,CAACpC,GAAG,oBAAHA,GAAG,CAAEuC,IAAI,CAAE,EACjD,CAAC;EACH;EAEApC,QAAQ,CAACwC,qBAAqB,GAAGL,KAAK;EAEtC,OAAOnC,QAAQ;AACjB;AAEO,SAASW,eAAeA,CAACyB,IAAoB,EAAa;EAC/D,SAASpC,QAAQA,CAACiB,IAAY,EAAEC,GAAW,EAAErB,GAAQ,EAAE;IACrD,MAAM4C,KAAK,GAAG7C,OAAO,CAACC,GAAG,CAAC,KAAKuC,IAAI;IAEnC,IAAI,CAACK,KAAK,EAAE;MACV,MAAM,IAAIV,SAAS,CAChB,YAAWb,GAAI,qBAAoBkB,IAAK,YAAWxC,OAAO,CAACC,GAAG,CAAE,EACnE,CAAC;IACH;EACF;EAEAG,QAAQ,CAACoC,IAAI,GAAGA,IAAI;EAEpB,OAAOpC,QAAQ;AACjB;AAEO,SAAS0C,WAAWA,CAACC,KAAoC,EAAa;EAC3E,SAAS3C,QAAQA,CAACiB,IAAY,EAAEC,GAAW,EAAErB,GAAQ,EAAE;IACrD,MAAM+C,MAAM,GAAG,EAAE;IACjB,KAAK,MAAMC,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACJ,KAAK,CAAC,EAAE;MACzC,IAAI;QACF,IAAAK,uBAAa,EAAC/B,IAAI,EAAE4B,QAAQ,EAAEhD,GAAG,CAACgD,QAAQ,CAAC,EAAEF,KAAK,CAACE,QAAQ,CAAC,CAAC;MAC/D,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd,IAAIA,KAAK,YAAYlB,SAAS,EAAE;UAC9Ba,MAAM,CAACM,IAAI,CAACD,KAAK,CAACE,OAAO,CAAC;UAC1B;QACF;QACA,MAAMF,KAAK;MACb;IACF;IACA,IAAIL,MAAM,CAACxB,MAAM,EAAE;MACjB,MAAM,IAAIW,SAAS,CAChB,YAAWb,GAAI,OACdD,IAAI,CAACmB,IACN,qCAAoCQ,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAE,EACzD,CAAC;IACH;EACF;EAEApD,QAAQ,CAACqD,OAAO,GAAGV,KAAK;EAExB,OAAO3C,QAAQ;AACjB;AAEO,SAASsD,wBAAwBA,CAAA,EAAc;EACpD,SAAStD,QAAQA,CAACiB,IAAY,EAAE;IAAA,IAAAsC,QAAA;IAC9B,IAAIC,OAAO,GAAGvC,IAAI;IAClB,OAAOA,IAAI,EAAE;MACX,MAAM;QAAEmB;MAAK,CAAC,GAAGoB,OAAO;MACxB,IAAIpB,IAAI,KAAK,wBAAwB,EAAE;QACrC,IAAIoB,OAAO,CAAClD,QAAQ,EAAE;QACtBkD,OAAO,GAAGA,OAAO,CAACC,MAAM;QACxB;MACF;MAEA,IAAIrB,IAAI,KAAK,0BAA0B,EAAE;QACvC,IAAIoB,OAAO,CAAClD,QAAQ,EAAE;QACtBkD,OAAO,GAAGA,OAAO,CAACE,MAAM;QACxB;MACF;MAEA;IACF;IAEA,MAAM,IAAI3B,SAAS,CAChB,gBAAed,IAAI,CAACmB,IAAK,qGAAkG,CAAAmB,QAAA,GAAEC,OAAO,qBAAPD,QAAA,CAASnB,IAAK,EAC9I,CAAC;EACH;EAEA,OAAOpC,QAAQ;AACjB;AAEO,SAASU,KAAKA,CAAC,GAAGiD,GAAqB,EAAa;EACzD,SAAS3D,QAAQA,CAAC,GAAG4D,IAA2B,EAAE;IAChD,KAAK,MAAMC,EAAE,IAAIF,GAAG,EAAE;MACpBE,EAAE,CAAC,GAAGD,IAAI,CAAC;IACb;EACF;EACA5D,QAAQ,CAAC8D,OAAO,GAAGH,GAAG;EAEtB,IACEA,GAAG,CAACvC,MAAM,IAAI,CAAC,IACf,MAAM,IAAIuC,GAAG,CAAC,CAAC,CAAC,IAChBA,GAAG,CAAC,CAAC,CAAC,CAACvB,IAAI,KAAK,OAAO,IACvB,EAAE,MAAM,IAAIuB,GAAG,CAAC,CAAC,CAAC,CAAC,EACnB;IACA,MAAM,IAAII,KAAK,CACZ,6FACH,CAAC;EACH;EAEA,OAAO/D,QAAQ;AACjB;AAEA,MAAMgE,aAAa,GAAG,CACpB,SAAS,EACT,SAAS,EACT,iBAAiB,EACjB,QAAQ,EACR,UAAU,EACV,SAAS,EACT,UAAU,CACX;AACD,MAAMC,cAAc,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC;AAExE,MAAMC,KAAK,GAAG,CAAC,CAAmC;AAG3C,SAASC,iBAAiBA,CAAC,GAAGC,OAAiB,EAAE;EACtD,OAAO,CAAChC,IAAY,EAAEiC,IAAoB,GAAG,CAAC,CAAC,KAAK;IAClD,IAAIC,OAAO,GAAGD,IAAI,CAACD,OAAO;IAC1B,IAAI,CAACE,OAAO,EAAE;MAAA,IAAAC,qBAAA,EAAAC,QAAA;MACZ,IAAIH,IAAI,CAACI,QAAQ,EAAEH,OAAO,IAAAC,qBAAA,GAAGL,KAAK,CAACG,IAAI,CAACI,QAAQ,CAAC,CAACL,OAAO,qBAA5BG,qBAAA,CAA8BG,KAAK,CAAC,CAAC;MAClE,CAAAF,QAAA,GAAAF,OAAO,YAAAE,QAAA,GAAPF,OAAO,GAAK,EAAE;MACdD,IAAI,CAACD,OAAO,GAAGE,OAAO;IACxB;IACA,MAAMK,UAAU,GAAGP,OAAO,CAACQ,MAAM,CAACC,CAAC,IAAI,CAACP,OAAO,CAACQ,QAAQ,CAACD,CAAC,CAAC,CAAC;IAC5DP,OAAO,CAACS,OAAO,CAAC,GAAGJ,UAAU,CAAC;IAC9BK,UAAU,CAAC5C,IAAI,EAAEiC,IAAI,CAAC;EACxB,CAAC;AACH;AAEe,SAASW,UAAUA,CAAC5C,IAAY,EAAEiC,IAAoB,GAAG,CAAC,CAAC,EAAE;EAC1E,MAAMI,QAAQ,GAAIJ,IAAI,CAACI,QAAQ,IAAIP,KAAK,CAACG,IAAI,CAACI,QAAQ,CAAC,IAAK,CAAC,CAAC;EAE9D,IAAIQ,MAAM,GAAGZ,IAAI,CAACY,MAAM;EACxB,IAAI,CAACA,MAAM,EAAE;IACXA,MAAM,GAAG,CAAC,CAAC;IACX,IAAIR,QAAQ,CAACQ,MAAM,EAAE;MACnB,MAAMlC,IAAI,GAAGD,MAAM,CAACoC,mBAAmB,CAACT,QAAQ,CAACQ,MAAM,CAAC;MACxD,KAAK,MAAM/D,GAAG,IAAI6B,IAAI,EAAE;QACtB,MAAMoC,KAAK,GAAGV,QAAQ,CAACQ,MAAM,CAAC/D,GAAG,CAAC;QAClC,MAAMkE,GAAG,GAAGD,KAAK,CAACE,OAAO;QACzB,IACEvF,KAAK,CAACC,OAAO,CAACqF,GAAG,CAAC,GAAGA,GAAG,CAAChE,MAAM,GAAG,CAAC,GAAGgE,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EACpE;UACA,MAAM,IAAIrB,KAAK,CACb,iEACF,CAAC;QACH;QACAkB,MAAM,CAAC/D,GAAG,CAAC,GAAG;UACZmE,OAAO,EAAEvF,KAAK,CAACC,OAAO,CAACqF,GAAG,CAAC,GAAG,EAAE,GAAGA,GAAG;UACtC9E,QAAQ,EAAE6E,KAAK,CAAC7E,QAAQ;UACxBgF,UAAU,EAAEH,KAAK,CAACG,UAAU;UAC5BtF,QAAQ,EAAEmF,KAAK,CAACnF;QAClB,CAAC;MACH;IACF;EACF;EAEA,MAAMuF,OAAsB,GAAGlB,IAAI,CAACkB,OAAO,IAAId,QAAQ,CAACc,OAAO,IAAI,EAAE;EACrE,MAAMnB,OAAsB,GAAGC,IAAI,CAACD,OAAO,IAAIK,QAAQ,CAACL,OAAO,IAAI,EAAE;EACrE,MAAMoB,OAAsB,GAC1BnB,IAAI,CAACmB,OAAO,IAAIf,QAAQ,CAACe,OAAO,IAAInB,IAAI,CAACkB,OAAO,IAAI,EAAE;EAExD,KAAK,MAAME,CAAC,IAAI3C,MAAM,CAACC,IAAI,CAACsB,IAAI,CAAC,EAAE;IACjC,IAAIL,aAAa,CAAClC,OAAO,CAAC2D,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MACnC,MAAM,IAAI1B,KAAK,CAAE,wBAAuB0B,CAAE,QAAOrD,IAAK,EAAC,CAAC;IAC1D;EACF;EAEA,IAAIiC,IAAI,CAACqB,eAAe,EAAE;IACxBhG,eAAe,CAAC2E,IAAI,CAACqB,eAAe,CAAC,GAAGtD,IAA+B;EACzE;EAGA,KAAK,MAAMlB,GAAG,IAAIqE,OAAO,CAACI,MAAM,CAACH,OAAO,CAAC,EAAE;IACzCP,MAAM,CAAC/D,GAAG,CAAC,GAAG+D,MAAM,CAAC/D,GAAG,CAAC,IAAI,CAAC,CAAC;EACjC;EAEA,KAAK,MAAMA,GAAG,IAAI4B,MAAM,CAACC,IAAI,CAACkC,MAAM,CAAC,EAAE;IACrC,MAAME,KAAK,GAAGF,MAAM,CAAC/D,GAAG,CAAC;IAEzB,IAAIiE,KAAK,CAACE,OAAO,KAAKO,SAAS,IAAIJ,OAAO,CAAC1D,OAAO,CAACZ,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9DiE,KAAK,CAAC7E,QAAQ,GAAG,IAAI;IACvB;IACA,IAAI6E,KAAK,CAACE,OAAO,KAAKO,SAAS,EAAE;MAC/BT,KAAK,CAACE,OAAO,GAAG,IAAI;IACtB,CAAC,MAAM,IAAI,CAACF,KAAK,CAACnF,QAAQ,IAAImF,KAAK,CAACE,OAAO,IAAI,IAAI,EAAE;MACnDF,KAAK,CAACnF,QAAQ,GAAGW,eAAe,CAACf,OAAO,CAACuF,KAAK,CAACE,OAAO,CAAC,CAAC;IAC1D;IAEA,KAAK,MAAMI,CAAC,IAAI3C,MAAM,CAACC,IAAI,CAACoC,KAAK,CAAC,EAAE;MAClC,IAAIlB,cAAc,CAACnC,OAAO,CAAC2D,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACpC,MAAM,IAAI1B,KAAK,CAAE,sBAAqB0B,CAAE,QAAOrD,IAAK,IAAGlB,GAAI,EAAC,CAAC;MAC/D;IACF;EACF;EAEA9B,YAAY,CAACgD,IAAI,CAAC,GAAGiC,IAAI,CAACkB,OAAO,GAAGA,OAAO;EAC3C9F,YAAY,CAAC2C,IAAI,CAAC,GAAGiC,IAAI,CAACmB,OAAO,GAAGA,OAAO;EAC3ChG,WAAW,CAAC4C,IAAI,CAAC,GAAGiC,IAAI,CAACY,MAAM,GAAGA,MAAM;EACxC3F,UAAU,CAAC8C,IAAI,CAA4B,GAAGiC,IAAI,CAACD,OAAO,GAAGA,OAAO;EACpEA,OAAO,CAACyB,OAAO,CAACC,KAAK,IAAI;IACvBvG,kBAAkB,CAACuG,KAAK,CAAC,GAAGvG,kBAAkB,CAACuG,KAAK,CAAC,IAAI,EAAE;IAC3DvG,kBAAkB,CAACuG,KAAK,CAAC,CAAC5C,IAAI,CAACd,IAA+B,CAAC;EACjE,CAAC,CAAC;EAEF,IAAIiC,IAAI,CAACrE,QAAQ,EAAE;IACjBL,uBAAuB,CAACyC,IAAI,CAAC,GAAGiC,IAAI,CAACrE,QAAQ;EAC/C;EAEAkE,KAAK,CAAC9B,IAAI,CAAC,GAAGiC,IAAI;AACpB"}