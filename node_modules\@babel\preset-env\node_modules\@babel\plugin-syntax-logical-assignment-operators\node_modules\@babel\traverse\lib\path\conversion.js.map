{"version": 3, "names": ["_t", "require", "_helperEnvironmentVisitor", "_helperFunctionName", "_visitors", "arrowFunctionExpression", "assignmentExpression", "binaryExpression", "blockStatement", "callExpression", "conditionalExpression", "expressionStatement", "identifier", "isIdentifier", "jsxIdentifier", "logicalExpression", "LOGICAL_OPERATORS", "memberExpression", "metaProperty", "numericLiteral", "objectExpression", "restElement", "returnStatement", "sequenceExpression", "spreadElement", "stringLiteral", "super", "_super", "thisExpression", "toExpression", "unaryExpression", "toCom<PERSON><PERSON>ey", "key", "isMemberExpression", "node", "property", "isProperty", "isMethod", "ReferenceError", "computed", "name", "ensureBlock", "body", "get", "bodyNode", "Array", "isArray", "Error", "isBlockStatement", "statements", "stringPath", "<PERSON><PERSON><PERSON>", "isStatement", "push", "isFunction", "parentPath", "setup", "arrowFunctionToShadowed", "isArrowFunctionExpression", "arrowFunctionToExpression", "unwrapFunctionEnvironment", "isFunctionExpression", "isFunctionDeclaration", "buildCodeFrameError", "hoistFunctionEnvironment", "setType", "path", "type", "allowInsertArrow", "allowInsertArrowWithRest", "specCompliant", "noNewArrows", "thisBinding", "fnPath", "fn", "checkBinding", "scope", "generateUidIdentifier", "id", "init", "unshiftContainer", "hub", "addHelper", "replaceWith", "nameFunction", "getSuperCallsVisitor", "mergeVisitors", "CallExpression", "child", "allSuperCalls", "is<PERSON><PERSON><PERSON>", "environmentVisitor", "arrowParent", "thisEnvFn", "findParent", "p", "_arrowParent", "isProgram", "isClassProperty", "static", "isClassPrivateProperty", "inConstructor", "isClassMethod", "kind", "thisPaths", "argumentsPaths", "newTargetPaths", "superProps", "superCalls", "getScopeInformation", "length", "traverse", "superBinding", "getSuper<PERSON><PERSON><PERSON>", "for<PERSON>ach", "superCall", "callee", "loc", "argumentsBinding", "getBinding", "args", "buildUndefinedNode", "<PERSON><PERSON><PERSON><PERSON>", "argsRef", "newTargetBinding", "targetChild", "targetRef", "flatSuperProps", "reduce", "acc", "superProp", "concat", "standardizeSuperProperty", "superParent<PERSON>ath", "isAssignment", "isAssignmentExpression", "left", "isCall", "isCallExpression", "isTaggedTemplate", "isTaggedTemplateExpression", "tag", "getSuperPropBinding", "value", "right", "call", "getThis<PERSON><PERSON>ing", "hasSuperClass", "<PERSON><PERSON><PERSON><PERSON>", "thisRef", "isJSX", "isLogicalOp", "op", "includes", "operator", "assignment<PERSON><PERSON>", "slice", "isLogicalAssignment", "tmp", "generateDeclaredUidIdentifier", "object", "rightExpression", "isUpdateExpression", "updateExpr", "computedKey", "parts", "prefix", "superClass", "assignSuperThisVisitor", "supers", "has", "add", "replaceWithMultiple", "WeakSet", "args<PERSON><PERSON><PERSON>", "propName", "argsList", "fnBody", "method", "unshift", "valueIdent", "cache<PERSON>ey", "data", "getData", "setData", "getScopeInformationVisitor", "ThisExpression", "JSXIdentifier", "isJSXMemberExpression", "isJSXOpeningElement", "MemberExpression", "Identifier", "isReferencedIdentifier", "curr", "hasOwnBinding", "rename", "parent", "MetaProperty"], "sources": ["../../src/path/conversion.ts"], "sourcesContent": ["// This file contains methods that convert the path node into another node or some other type of data.\n\nimport {\n  arrowFunctionExpression,\n  assignmentExpression,\n  binaryExpression,\n  blockStatement,\n  callExpression,\n  conditionalExpression,\n  expressionStatement,\n  identifier,\n  isIdentifier,\n  jsxIdentifier,\n  logicalExpression,\n  LOGICAL_OPERATORS,\n  memberExpression,\n  metaProperty,\n  numericLiteral,\n  objectExpression,\n  restElement,\n  returnStatement,\n  sequenceExpression,\n  spreadElement,\n  stringLiteral,\n  super as _super,\n  thisExpression,\n  toExpression,\n  unaryExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\nimport nameFunction from \"@babel/helper-function-name\";\nimport { merge as mergeVisitors } from \"../visitors\";\nimport type NodePath from \"./index\";\n\nexport function toComputedKey(this: NodePath) {\n  let key;\n  if (this.isMemberExpression()) {\n    key = this.node.property;\n  } else if (this.isProperty() || this.isMethod()) {\n    key = this.node.key;\n  } else {\n    throw new ReferenceError(\"todo\");\n  }\n\n  // @ts-expect-error todo(flow->ts) computed does not exist in ClassPrivateProperty\n  if (!this.node.computed) {\n    if (isIdentifier(key)) key = stringLiteral(key.name);\n  }\n\n  return key;\n}\n\nexport function ensureBlock(\n  this: NodePath<\n    t.Loop | t.WithStatement | t.Function | t.LabeledStatement | t.CatchClause\n  >,\n) {\n  const body = this.get(\"body\");\n  const bodyNode = body.node;\n\n  if (Array.isArray(body)) {\n    throw new Error(\"Can't convert array path to a block statement\");\n  }\n  if (!bodyNode) {\n    throw new Error(\"Can't convert node without a body\");\n  }\n\n  if (body.isBlockStatement()) {\n    return bodyNode;\n  }\n\n  const statements: Array<t.Statement> = [];\n\n  let stringPath = \"body\";\n  let key;\n  let listKey;\n  if (body.isStatement()) {\n    listKey = \"body\";\n    key = 0;\n    statements.push(body.node);\n  } else {\n    stringPath += \".body.0\";\n    if (this.isFunction()) {\n      key = \"argument\";\n      statements.push(returnStatement(body.node as t.Expression));\n    } else {\n      key = \"expression\";\n      statements.push(expressionStatement(body.node as t.Expression));\n    }\n  }\n\n  this.node.body = blockStatement(statements);\n  const parentPath = this.get(stringPath) as NodePath;\n  body.setup(\n    parentPath,\n    listKey\n      ? // @ts-expect-error listKey must present in parent path\n        parentPath.node[listKey]\n      : parentPath.node,\n    listKey,\n    key,\n  );\n\n  return this.node;\n}\n\n/**\n * Keeping this for backward-compatibility. You should use arrowFunctionToExpression() for >=7.x.\n */\n// TODO(Babel 8): Remove this\nexport function arrowFunctionToShadowed(this: NodePath) {\n  if (!this.isArrowFunctionExpression()) return;\n\n  this.arrowFunctionToExpression();\n}\n\n/**\n * Given an arbitrary function, process its content as if it were an arrow function, moving references\n * to \"this\", \"arguments\", \"super\", and such into the function's parent scope. This method is useful if\n * you have wrapped some set of items in an IIFE or other function, but want \"this\", \"arguments\", and super\"\n * to continue behaving as expected.\n */\nexport function unwrapFunctionEnvironment(this: NodePath) {\n  if (\n    !this.isArrowFunctionExpression() &&\n    !this.isFunctionExpression() &&\n    !this.isFunctionDeclaration()\n  ) {\n    throw this.buildCodeFrameError(\n      \"Can only unwrap the environment of a function.\",\n    );\n  }\n\n  hoistFunctionEnvironment(this);\n}\n\nfunction setType<N extends t.Node, T extends N[\"type\"]>(\n  path: NodePath<N>,\n  type: T,\n): asserts path is NodePath<Extract<N, { type: T }>> {\n  path.node.type = type;\n}\n\n/**\n * Convert a given arrow function into a normal ES5 function expression.\n */\nexport function arrowFunctionToExpression(\n  this: NodePath<t.ArrowFunctionExpression>,\n  {\n    allowInsertArrow = true,\n    allowInsertArrowWithRest = allowInsertArrow,\n    /** @deprecated Use `noNewArrows` instead */\n    specCompliant = false,\n    // TODO(Babel 8): Consider defaulting to `false` for spec compliancy\n    noNewArrows = !specCompliant,\n  }: {\n    allowInsertArrow?: boolean | void;\n    allowInsertArrowWithRest?: boolean | void;\n    specCompliant?: boolean | void;\n    noNewArrows?: boolean;\n  } = {},\n): NodePath<\n  Exclude<t.Function, t.Method | t.ArrowFunctionExpression> | t.CallExpression\n> {\n  if (!this.isArrowFunctionExpression()) {\n    throw (this as NodePath).buildCodeFrameError(\n      \"Cannot convert non-arrow function to a function expression.\",\n    );\n  }\n\n  const { thisBinding, fnPath: fn } = hoistFunctionEnvironment(\n    this,\n    noNewArrows,\n    allowInsertArrow,\n    allowInsertArrowWithRest,\n  );\n\n  // @ts-expect-error TS requires explicit fn type annotation\n  fn.ensureBlock();\n  setType(fn, \"FunctionExpression\");\n\n  if (!noNewArrows) {\n    const checkBinding = thisBinding\n      ? null\n      : fn.scope.generateUidIdentifier(\"arrowCheckId\");\n    if (checkBinding) {\n      fn.parentPath.scope.push({\n        id: checkBinding,\n        init: objectExpression([]),\n      });\n    }\n\n    fn.get(\"body\").unshiftContainer(\n      \"body\",\n      expressionStatement(\n        callExpression(this.hub.addHelper(\"newArrowCheck\"), [\n          thisExpression(),\n          checkBinding\n            ? identifier(checkBinding.name)\n            : identifier(thisBinding),\n        ]),\n      ),\n    );\n\n    fn.replaceWith(\n      callExpression(\n        memberExpression(\n          // @ts-expect-error TS can't infer nameFunction returns CallExpression | ArrowFunctionExpression here\n          nameFunction(this, true) || fn.node,\n          identifier(\"bind\"),\n        ),\n        [checkBinding ? identifier(checkBinding.name) : thisExpression()],\n      ),\n    );\n\n    return fn.get(\"callee.object\");\n  }\n\n  return fn;\n}\n\nconst getSuperCallsVisitor = mergeVisitors<{\n  allSuperCalls: NodePath<t.CallExpression>[];\n}>([\n  {\n    CallExpression(child, { allSuperCalls }) {\n      if (!child.get(\"callee\").isSuper()) return;\n      allSuperCalls.push(child);\n    },\n  },\n  environmentVisitor,\n]);\n\n/**\n * Given a function, traverse its contents, and if there are references to \"this\", \"arguments\", \"super\",\n * or \"new.target\", ensure that these references reference the parent environment around this function.\n *\n * @returns `thisBinding`: the name of the injected reference to `this`; for example \"_this\"\n * @returns `fnPath`: the new path to the function node. This is different from the fnPath\n *                    parameter when the function node is wrapped in another node.\n */\nfunction hoistFunctionEnvironment(\n  fnPath: NodePath<t.Function>,\n  // TODO(Babel 8): Consider defaulting to `false` for spec compliancy\n  noNewArrows: boolean | void = true,\n  allowInsertArrow: boolean | void = true,\n  allowInsertArrowWithRest: boolean | void = true,\n): { thisBinding: string; fnPath: NodePath<t.Function> } {\n  let arrowParent;\n  let thisEnvFn: NodePath<t.Function> = fnPath.findParent(p => {\n    if (p.isArrowFunctionExpression()) {\n      arrowParent ??= p;\n      return false;\n    }\n    return (\n      p.isFunction() ||\n      p.isProgram() ||\n      p.isClassProperty({ static: false }) ||\n      p.isClassPrivateProperty({ static: false })\n    );\n  }) as NodePath<t.Function>;\n  const inConstructor = thisEnvFn.isClassMethod({ kind: \"constructor\" });\n\n  if (thisEnvFn.isClassProperty() || thisEnvFn.isClassPrivateProperty()) {\n    if (arrowParent) {\n      thisEnvFn = arrowParent;\n    } else if (allowInsertArrow) {\n      // It's safe to wrap this function in another and not hoist to the\n      // top level because the 'this' binding is constant in class\n      // properties (since 'super()' has already been called), so we don't\n      // need to capture/reassign it at the top level.\n      fnPath.replaceWith(\n        callExpression(\n          arrowFunctionExpression([], toExpression(fnPath.node)),\n          [],\n        ),\n      );\n      thisEnvFn = fnPath.get(\"callee\") as NodePath<t.ArrowFunctionExpression>;\n      fnPath = thisEnvFn.get(\"body\") as NodePath<t.FunctionExpression>;\n    } else {\n      throw fnPath.buildCodeFrameError(\n        \"Unable to transform arrow inside class property\",\n      );\n    }\n  }\n\n  const { thisPaths, argumentsPaths, newTargetPaths, superProps, superCalls } =\n    getScopeInformation(fnPath);\n\n  // Convert all super() calls in the constructor, if super is used in an arrow.\n  if (inConstructor && superCalls.length > 0) {\n    if (!allowInsertArrow) {\n      throw superCalls[0].buildCodeFrameError(\n        \"When using '@babel/plugin-transform-arrow-functions', \" +\n          \"it's not possible to compile `super()` in an arrow function without compiling classes.\\n\" +\n          \"Please add '@babel/plugin-transform-classes' to your Babel configuration.\",\n      );\n    }\n    if (!allowInsertArrowWithRest) {\n      // preset-env with target `since 2017` enables `transform-parameters` without `transform-classes`.\n      throw superCalls[0].buildCodeFrameError(\n        \"When using '@babel/plugin-transform-parameters', \" +\n          \"it's not possible to compile `super()` in an arrow function with default or rest parameters without compiling classes.\\n\" +\n          \"Please add '@babel/plugin-transform-classes' to your Babel configuration.\",\n      );\n    }\n    const allSuperCalls: NodePath<t.CallExpression>[] = [];\n    thisEnvFn.traverse(getSuperCallsVisitor, { allSuperCalls });\n    const superBinding = getSuperBinding(thisEnvFn);\n    allSuperCalls.forEach(superCall => {\n      const callee = identifier(superBinding);\n      callee.loc = superCall.node.callee.loc;\n\n      superCall.get(\"callee\").replaceWith(callee);\n    });\n  }\n\n  // Convert all \"arguments\" references in the arrow to point at the alias.\n  if (argumentsPaths.length > 0) {\n    const argumentsBinding = getBinding(thisEnvFn, \"arguments\", () => {\n      const args = () => identifier(\"arguments\");\n      if (thisEnvFn.scope.path.isProgram()) {\n        return conditionalExpression(\n          binaryExpression(\n            \"===\",\n            unaryExpression(\"typeof\", args()),\n            stringLiteral(\"undefined\"),\n          ),\n          thisEnvFn.scope.buildUndefinedNode(),\n          args(),\n        );\n      } else {\n        return args();\n      }\n    });\n\n    argumentsPaths.forEach(argumentsChild => {\n      const argsRef = identifier(argumentsBinding);\n      argsRef.loc = argumentsChild.node.loc;\n\n      argumentsChild.replaceWith(argsRef);\n    });\n  }\n\n  // Convert all \"new.target\" references in the arrow to point at the alias.\n  if (newTargetPaths.length > 0) {\n    const newTargetBinding = getBinding(thisEnvFn, \"newtarget\", () =>\n      metaProperty(identifier(\"new\"), identifier(\"target\")),\n    );\n\n    newTargetPaths.forEach(targetChild => {\n      const targetRef = identifier(newTargetBinding);\n      targetRef.loc = targetChild.node.loc;\n\n      targetChild.replaceWith(targetRef);\n    });\n  }\n\n  // Convert all \"super.prop\" references to point at aliases.\n  if (superProps.length > 0) {\n    if (!allowInsertArrow) {\n      throw superProps[0].buildCodeFrameError(\n        \"When using '@babel/plugin-transform-arrow-functions', \" +\n          \"it's not possible to compile `super.prop` in an arrow function without compiling classes.\\n\" +\n          \"Please add '@babel/plugin-transform-classes' to your Babel configuration.\",\n      );\n    }\n\n    const flatSuperProps: NodePath<t.MemberExpression>[] = superProps.reduce(\n      (acc, superProp) => acc.concat(standardizeSuperProperty(superProp)),\n      [],\n    );\n\n    flatSuperProps.forEach(superProp => {\n      const key = superProp.node.computed\n        ? \"\"\n        : // @ts-expect-error super property must not contain private name\n          superProp.get(\"property\").node.name;\n\n      const superParentPath = superProp.parentPath;\n\n      const isAssignment = superParentPath.isAssignmentExpression({\n        left: superProp.node,\n      });\n      const isCall = superParentPath.isCallExpression({\n        callee: superProp.node,\n      });\n      const isTaggedTemplate = superParentPath.isTaggedTemplateExpression({\n        tag: superProp.node,\n      });\n      const superBinding = getSuperPropBinding(thisEnvFn, isAssignment, key);\n\n      const args: t.Expression[] = [];\n      if (superProp.node.computed) {\n        // SuperProperty must not be a private name\n        args.push(superProp.get(\"property\").node as t.Expression);\n      }\n\n      if (isAssignment) {\n        const value = superParentPath.node.right;\n        args.push(value);\n      }\n\n      const call = callExpression(identifier(superBinding), args);\n\n      if (isCall) {\n        superParentPath.unshiftContainer(\"arguments\", thisExpression());\n        superProp.replaceWith(memberExpression(call, identifier(\"call\")));\n\n        thisPaths.push(\n          superParentPath.get(\"arguments.0\") as NodePath<t.ThisExpression>,\n        );\n      } else if (isAssignment) {\n        // Replace not only the super.prop, but the whole assignment\n        superParentPath.replaceWith(call);\n      } else if (isTaggedTemplate) {\n        superProp.replaceWith(\n          callExpression(memberExpression(call, identifier(\"bind\"), false), [\n            thisExpression(),\n          ]),\n        );\n\n        thisPaths.push(\n          superProp.get(\"arguments.0\") as NodePath<t.ThisExpression>,\n        );\n      } else {\n        superProp.replaceWith(call);\n      }\n    });\n  }\n\n  // Convert all \"this\" references in the arrow to point at the alias.\n  let thisBinding: string | null;\n  if (thisPaths.length > 0 || !noNewArrows) {\n    thisBinding = getThisBinding(thisEnvFn, inConstructor);\n\n    if (\n      noNewArrows ||\n      // In subclass constructors, still need to rewrite because \"this\" can't be bound in spec mode\n      // because it might not have been initialized yet.\n      (inConstructor && hasSuperClass(thisEnvFn))\n    ) {\n      thisPaths.forEach(thisChild => {\n        const thisRef = thisChild.isJSX()\n          ? jsxIdentifier(thisBinding)\n          : identifier(thisBinding);\n\n        thisRef.loc = thisChild.node.loc;\n        thisChild.replaceWith(thisRef);\n      });\n\n      if (!noNewArrows) thisBinding = null;\n    }\n  }\n\n  return { thisBinding, fnPath };\n}\n\ntype LogicalOp = Parameters<typeof logicalExpression>[0];\ntype BinaryOp = Parameters<typeof binaryExpression>[0];\n\nfunction isLogicalOp(op: string): op is LogicalOp {\n  return LOGICAL_OPERATORS.includes(op);\n}\n\nfunction standardizeSuperProperty(\n  superProp: NodePath<t.MemberExpression>,\n):\n  | [NodePath<t.MemberExpression>]\n  | [NodePath<t.MemberExpression>, NodePath<t.MemberExpression>] {\n  if (\n    superProp.parentPath.isAssignmentExpression() &&\n    superProp.parentPath.node.operator !== \"=\"\n  ) {\n    const assignmentPath = superProp.parentPath;\n\n    const op = assignmentPath.node.operator.slice(0, -1) as\n      | LogicalOp\n      | BinaryOp;\n\n    const value = assignmentPath.node.right;\n\n    const isLogicalAssignment = isLogicalOp(op);\n\n    if (superProp.node.computed) {\n      // from: super[foo] **= 4;\n      // to:   super[tmp = foo] = super[tmp] ** 4;\n\n      // from: super[foo] ??= 4;\n      // to:   super[tmp = foo] ?? super[tmp] = 4;\n\n      const tmp = superProp.scope.generateDeclaredUidIdentifier(\"tmp\");\n\n      const object = superProp.node.object;\n      const property = superProp.node.property as t.Expression;\n\n      assignmentPath\n        .get(\"left\")\n        .replaceWith(\n          memberExpression(\n            object,\n            assignmentExpression(\"=\", tmp, property),\n            true /* computed */,\n          ),\n        );\n\n      assignmentPath\n        .get(\"right\")\n        .replaceWith(\n          rightExpression(\n            isLogicalAssignment ? \"=\" : op,\n            memberExpression(object, identifier(tmp.name), true /* computed */),\n            value,\n          ),\n        );\n    } else {\n      // from: super.foo **= 4;\n      // to:   super.foo = super.foo ** 4;\n\n      // from: super.foo ??= 4;\n      // to:   super.foo ?? super.foo = 4;\n\n      const object = superProp.node.object;\n      const property = superProp.node.property as t.Identifier;\n\n      assignmentPath\n        .get(\"left\")\n        .replaceWith(memberExpression(object, property));\n\n      assignmentPath\n        .get(\"right\")\n        .replaceWith(\n          rightExpression(\n            isLogicalAssignment ? \"=\" : op,\n            memberExpression(object, identifier(property.name)),\n            value,\n          ),\n        );\n    }\n\n    if (isLogicalAssignment) {\n      assignmentPath.replaceWith(\n        logicalExpression(\n          op,\n          assignmentPath.node.left as t.MemberExpression,\n          assignmentPath.node.right,\n        ),\n      );\n    } else {\n      assignmentPath.node.operator = \"=\";\n    }\n\n    return [\n      assignmentPath.get(\"left\") as NodePath<t.MemberExpression>,\n      assignmentPath.get(\"right\").get(\"left\"),\n    ];\n  } else if (superProp.parentPath.isUpdateExpression()) {\n    const updateExpr = superProp.parentPath;\n\n    const tmp = superProp.scope.generateDeclaredUidIdentifier(\"tmp\");\n    const computedKey = superProp.node.computed\n      ? superProp.scope.generateDeclaredUidIdentifier(\"prop\")\n      : null;\n\n    const parts: t.Expression[] = [\n      assignmentExpression(\n        \"=\",\n        tmp,\n        memberExpression(\n          superProp.node.object,\n          computedKey\n            ? assignmentExpression(\n                \"=\",\n                computedKey,\n                superProp.node.property as t.Expression,\n              )\n            : superProp.node.property,\n          superProp.node.computed,\n        ),\n      ),\n      assignmentExpression(\n        \"=\",\n        memberExpression(\n          superProp.node.object,\n          computedKey ? identifier(computedKey.name) : superProp.node.property,\n          superProp.node.computed,\n        ),\n        binaryExpression(\n          // map `++` to `+`, and `--` to `-`\n          superProp.parentPath.node.operator[0] as \"+\" | \"-\",\n          identifier(tmp.name),\n          numericLiteral(1),\n        ),\n      ),\n    ];\n\n    if (!superProp.parentPath.node.prefix) {\n      parts.push(identifier(tmp.name));\n    }\n\n    updateExpr.replaceWith(sequenceExpression(parts));\n\n    const left = updateExpr.get(\n      \"expressions.0.right\",\n    ) as NodePath<t.MemberExpression>;\n    const right = updateExpr.get(\n      \"expressions.1.left\",\n    ) as NodePath<t.MemberExpression>;\n    return [left, right];\n  }\n\n  return [superProp];\n\n  function rightExpression(\n    op: BinaryOp | \"=\",\n    left: t.MemberExpression,\n    right: t.Expression,\n  ) {\n    if (op === \"=\") {\n      return assignmentExpression(\"=\", left, right);\n    } else {\n      return binaryExpression(op, left, right);\n    }\n  }\n}\n\nfunction hasSuperClass(thisEnvFn: NodePath<t.Function>) {\n  return (\n    thisEnvFn.isClassMethod() &&\n    !!(thisEnvFn.parentPath.parentPath.node as t.Class).superClass\n  );\n}\n\nconst assignSuperThisVisitor = mergeVisitors<{\n  supers: WeakSet<t.CallExpression>;\n  thisBinding: string;\n}>([\n  {\n    CallExpression(child, { supers, thisBinding }) {\n      if (!child.get(\"callee\").isSuper()) return;\n      if (supers.has(child.node)) return;\n      supers.add(child.node);\n\n      child.replaceWithMultiple([\n        child.node,\n        assignmentExpression(\"=\", identifier(thisBinding), identifier(\"this\")),\n      ]);\n    },\n  },\n  environmentVisitor,\n]);\n\n// Create a binding that evaluates to the \"this\" of the given function.\nfunction getThisBinding(\n  thisEnvFn: NodePath<t.Function>,\n  inConstructor: boolean,\n) {\n  return getBinding(thisEnvFn, \"this\", thisBinding => {\n    if (!inConstructor || !hasSuperClass(thisEnvFn)) return thisExpression();\n\n    thisEnvFn.traverse(assignSuperThisVisitor, {\n      supers: new WeakSet(),\n      thisBinding,\n    });\n  });\n}\n\n// Create a binding for a function that will call \"super()\" with arguments passed through.\nfunction getSuperBinding(thisEnvFn: NodePath<t.Function>) {\n  return getBinding(thisEnvFn, \"supercall\", () => {\n    const argsBinding = thisEnvFn.scope.generateUidIdentifier(\"args\");\n    return arrowFunctionExpression(\n      [restElement(argsBinding)],\n      callExpression(_super(), [spreadElement(identifier(argsBinding.name))]),\n    );\n  });\n}\n\n// Create a binding for a function that will call \"super.foo\" or \"super[foo]\".\nfunction getSuperPropBinding(\n  thisEnvFn: NodePath<t.Function>,\n  isAssignment: boolean,\n  propName: string,\n) {\n  const op = isAssignment ? \"set\" : \"get\";\n\n  return getBinding(thisEnvFn, `superprop_${op}:${propName || \"\"}`, () => {\n    const argsList = [];\n\n    let fnBody;\n    if (propName) {\n      // () => super.foo\n      fnBody = memberExpression(_super(), identifier(propName));\n    } else {\n      const method = thisEnvFn.scope.generateUidIdentifier(\"prop\");\n      // (method) => super[method]\n      argsList.unshift(method);\n      fnBody = memberExpression(\n        _super(),\n        identifier(method.name),\n        true /* computed */,\n      );\n    }\n\n    if (isAssignment) {\n      const valueIdent = thisEnvFn.scope.generateUidIdentifier(\"value\");\n      argsList.push(valueIdent);\n\n      fnBody = assignmentExpression(\"=\", fnBody, identifier(valueIdent.name));\n    }\n\n    return arrowFunctionExpression(argsList, fnBody);\n  });\n}\n\nfunction getBinding(\n  thisEnvFn: NodePath,\n  key: string,\n  init: (name: string) => t.Expression,\n) {\n  const cacheKey = \"binding:\" + key;\n  let data: string | undefined = thisEnvFn.getData(cacheKey);\n  if (!data) {\n    const id = thisEnvFn.scope.generateUidIdentifier(key);\n    data = id.name;\n    thisEnvFn.setData(cacheKey, data);\n\n    thisEnvFn.scope.push({\n      id: id,\n      init: init(data),\n    });\n  }\n\n  return data;\n}\n\ntype ScopeInfo = {\n  thisPaths: NodePath<t.ThisExpression | t.JSXIdentifier>[];\n  superCalls: NodePath<t.CallExpression>[];\n  superProps: NodePath<t.MemberExpression>[];\n  argumentsPaths: NodePath<t.Identifier | t.JSXIdentifier>[];\n  newTargetPaths: NodePath<t.MetaProperty>[];\n};\n\nconst getScopeInformationVisitor = mergeVisitors<ScopeInfo>([\n  {\n    ThisExpression(child, { thisPaths }) {\n      thisPaths.push(child);\n    },\n    JSXIdentifier(child, { thisPaths }) {\n      if (child.node.name !== \"this\") return;\n      if (\n        !child.parentPath.isJSXMemberExpression({ object: child.node }) &&\n        !child.parentPath.isJSXOpeningElement({ name: child.node })\n      ) {\n        return;\n      }\n\n      thisPaths.push(child);\n    },\n    CallExpression(child, { superCalls }) {\n      if (child.get(\"callee\").isSuper()) superCalls.push(child);\n    },\n    MemberExpression(child, { superProps }) {\n      if (child.get(\"object\").isSuper()) superProps.push(child);\n    },\n    Identifier(child, { argumentsPaths }) {\n      if (!child.isReferencedIdentifier({ name: \"arguments\" })) return;\n\n      let curr = child.scope;\n      do {\n        if (curr.hasOwnBinding(\"arguments\")) {\n          curr.rename(\"arguments\");\n          return;\n        }\n        if (curr.path.isFunction() && !curr.path.isArrowFunctionExpression()) {\n          break;\n        }\n      } while ((curr = curr.parent));\n\n      argumentsPaths.push(child);\n    },\n    MetaProperty(child, { newTargetPaths }) {\n      if (!child.get(\"meta\").isIdentifier({ name: \"new\" })) return;\n      if (!child.get(\"property\").isIdentifier({ name: \"target\" })) return;\n\n      newTargetPaths.push(child);\n    },\n  },\n  environmentVisitor,\n]);\n\nfunction getScopeInformation(fnPath: NodePath) {\n  const thisPaths: ScopeInfo[\"thisPaths\"] = [];\n  const argumentsPaths: ScopeInfo[\"argumentsPaths\"] = [];\n  const newTargetPaths: ScopeInfo[\"newTargetPaths\"] = [];\n  const superProps: ScopeInfo[\"superProps\"] = [];\n  const superCalls: ScopeInfo[\"superCalls\"] = [];\n\n  fnPath.traverse(getScopeInformationVisitor, {\n    thisPaths,\n    argumentsPaths,\n    newTargetPaths,\n    superProps,\n    superCalls,\n  });\n\n  return {\n    thisPaths,\n    argumentsPaths,\n    newTargetPaths,\n    superProps,\n    superCalls,\n  };\n}\n"], "mappings": ";;;;;;;;;;AAEA,IAAAA,EAAA,GAAAC,OAAA;AA4BA,IAAAC,yBAAA,GAAAD,OAAA;AACA,IAAAE,mBAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AAAqD;EA7BnDI,uBAAuB;EACvBC,oBAAoB;EACpBC,gBAAgB;EAChBC,cAAc;EACdC,cAAc;EACdC,qBAAqB;EACrBC,mBAAmB;EACnBC,UAAU;EACVC,YAAY;EACZC,aAAa;EACbC,iBAAiB;EACjBC,iBAAiB;EACjBC,gBAAgB;EAChBC,YAAY;EACZC,cAAc;EACdC,gBAAgB;EAChBC,WAAW;EACXC,eAAe;EACfC,kBAAkB;EAClBC,aAAa;EACbC,aAAa;EACbC,KAAK,EAAIC,MAAM;EACfC,cAAc;EACdC,YAAY;EACZC;AAAe,IAAA9B,EAAA;AAQV,SAAS+B,aAAaA,CAAA,EAAiB;EAC5C,IAAIC,GAAG;EACP,IAAI,IAAI,CAACC,kBAAkB,EAAE,EAAE;IAC7BD,GAAG,GAAG,IAAI,CAACE,IAAI,CAACC,QAAQ;EAC1B,CAAC,MAAM,IAAI,IAAI,CAACC,UAAU,EAAE,IAAI,IAAI,CAACC,QAAQ,EAAE,EAAE;IAC/CL,GAAG,GAAG,IAAI,CAACE,IAAI,CAACF,GAAG;EACrB,CAAC,MAAM;IACL,MAAM,IAAIM,cAAc,CAAC,MAAM,CAAC;EAClC;EAGA,IAAI,CAAC,IAAI,CAACJ,IAAI,CAACK,QAAQ,EAAE;IACvB,IAAI1B,YAAY,CAACmB,GAAG,CAAC,EAAEA,GAAG,GAAGP,aAAa,CAACO,GAAG,CAACQ,IAAI,CAAC;EACtD;EAEA,OAAOR,GAAG;AACZ;AAEO,SAASS,WAAWA,CAAA,EAIzB;EACA,MAAMC,IAAI,GAAG,IAAI,CAACC,GAAG,CAAC,MAAM,CAAC;EAC7B,MAAMC,QAAQ,GAAGF,IAAI,CAACR,IAAI;EAE1B,IAAIW,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE;IACvB,MAAM,IAAIK,KAAK,CAAC,+CAA+C,CAAC;EAClE;EACA,IAAI,CAACH,QAAQ,EAAE;IACb,MAAM,IAAIG,KAAK,CAAC,mCAAmC,CAAC;EACtD;EAEA,IAAIL,IAAI,CAACM,gBAAgB,EAAE,EAAE;IAC3B,OAAOJ,QAAQ;EACjB;EAEA,MAAMK,UAA8B,GAAG,EAAE;EAEzC,IAAIC,UAAU,GAAG,MAAM;EACvB,IAAIlB,GAAG;EACP,IAAImB,OAAO;EACX,IAAIT,IAAI,CAACU,WAAW,EAAE,EAAE;IACtBD,OAAO,GAAG,MAAM;IAChBnB,GAAG,GAAG,CAAC;IACPiB,UAAU,CAACI,IAAI,CAACX,IAAI,CAACR,IAAI,CAAC;EAC5B,CAAC,MAAM;IACLgB,UAAU,IAAI,SAAS;IACvB,IAAI,IAAI,CAACI,UAAU,EAAE,EAAE;MACrBtB,GAAG,GAAG,UAAU;MAChBiB,UAAU,CAACI,IAAI,CAAC/B,eAAe,CAACoB,IAAI,CAACR,IAAI,CAAiB,CAAC;IAC7D,CAAC,MAAM;MACLF,GAAG,GAAG,YAAY;MAClBiB,UAAU,CAACI,IAAI,CAAC1C,mBAAmB,CAAC+B,IAAI,CAACR,IAAI,CAAiB,CAAC;IACjE;EACF;EAEA,IAAI,CAACA,IAAI,CAACQ,IAAI,GAAGlC,cAAc,CAACyC,UAAU,CAAC;EAC3C,MAAMM,UAAU,GAAG,IAAI,CAACZ,GAAG,CAACO,UAAU,CAAa;EACnDR,IAAI,CAACc,KAAK,CACRD,UAAU,EACVJ,OAAO,GAEHI,UAAU,CAACrB,IAAI,CAACiB,OAAO,CAAC,GACxBI,UAAU,CAACrB,IAAI,EACnBiB,OAAO,EACPnB,GAAG,CACJ;EAED,OAAO,IAAI,CAACE,IAAI;AAClB;AAMO,SAASuB,uBAAuBA,CAAA,EAAiB;EACtD,IAAI,CAAC,IAAI,CAACC,yBAAyB,EAAE,EAAE;EAEvC,IAAI,CAACC,yBAAyB,EAAE;AAClC;AAQO,SAASC,yBAAyBA,CAAA,EAAiB;EACxD,IACE,CAAC,IAAI,CAACF,yBAAyB,EAAE,IACjC,CAAC,IAAI,CAACG,oBAAoB,EAAE,IAC5B,CAAC,IAAI,CAACC,qBAAqB,EAAE,EAC7B;IACA,MAAM,IAAI,CAACC,mBAAmB,CAC5B,gDAAgD,CACjD;EACH;EAEAC,wBAAwB,CAAC,IAAI,CAAC;AAChC;AAEA,SAASC,OAAOA,CACdC,IAAiB,EACjBC,IAAO,EAC4C;EACnDD,IAAI,CAAChC,IAAI,CAACiC,IAAI,GAAGA,IAAI;AACvB;AAKO,SAASR,yBAAyBA,CAEvC;EACES,gBAAgB,GAAG,IAAI;EACvBC,wBAAwB,GAAGD,gBAAgB;EAE3CE,aAAa,GAAG,KAAK;EAErBC,WAAW,GAAG,CAACD;AAMjB,CAAC,GAAG,CAAC,CAAC,EAGN;EACA,IAAI,CAAC,IAAI,CAACZ,yBAAyB,EAAE,EAAE;IACrC,MAAO,IAAI,CAAcK,mBAAmB,CAC1C,6DAA6D,CAC9D;EACH;EAEA,MAAM;IAAES,WAAW;IAAEC,MAAM,EAAEC;EAAG,CAAC,GAAGV,wBAAwB,CAC1D,IAAI,EACJO,WAAW,EACXH,gBAAgB,EAChBC,wBAAwB,CACzB;EAGDK,EAAE,CAACjC,WAAW,EAAE;EAChBwB,OAAO,CAACS,EAAE,EAAE,oBAAoB,CAAC;EAEjC,IAAI,CAACH,WAAW,EAAE;IAChB,MAAMI,YAAY,GAAGH,WAAW,GAC5B,IAAI,GACJE,EAAE,CAACE,KAAK,CAACC,qBAAqB,CAAC,cAAc,CAAC;IAClD,IAAIF,YAAY,EAAE;MAChBD,EAAE,CAACnB,UAAU,CAACqB,KAAK,CAACvB,IAAI,CAAC;QACvByB,EAAE,EAAEH,YAAY;QAChBI,IAAI,EAAE3D,gBAAgB,CAAC,EAAE;MAC3B,CAAC,CAAC;IACJ;IAEAsD,EAAE,CAAC/B,GAAG,CAAC,MAAM,CAAC,CAACqC,gBAAgB,CAC7B,MAAM,EACNrE,mBAAmB,CACjBF,cAAc,CAAC,IAAI,CAACwE,GAAG,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE,CAClDtD,cAAc,EAAE,EAChB+C,YAAY,GACR/D,UAAU,CAAC+D,YAAY,CAACnC,IAAI,CAAC,GAC7B5B,UAAU,CAAC4D,WAAW,CAAC,CAC5B,CAAC,CACH,CACF;IAEDE,EAAE,CAACS,WAAW,CACZ1E,cAAc,CACZQ,gBAAgB,CAEd,IAAAmE,2BAAY,EAAC,IAAI,EAAE,IAAI,CAAC,IAAIV,EAAE,CAACxC,IAAI,EACnCtB,UAAU,CAAC,MAAM,CAAC,CACnB,EACD,CAAC+D,YAAY,GAAG/D,UAAU,CAAC+D,YAAY,CAACnC,IAAI,CAAC,GAAGZ,cAAc,EAAE,CAAC,CAClE,CACF;IAED,OAAO8C,EAAE,CAAC/B,GAAG,CAAC,eAAe,CAAC;EAChC;EAEA,OAAO+B,EAAE;AACX;AAEA,MAAMW,oBAAoB,GAAG,IAAAC,eAAa,EAEvC,CACD;EACEC,cAAcA,CAACC,KAAK,EAAE;IAAEC;EAAc,CAAC,EAAE;IACvC,IAAI,CAACD,KAAK,CAAC7C,GAAG,CAAC,QAAQ,CAAC,CAAC+C,OAAO,EAAE,EAAE;IACpCD,aAAa,CAACpC,IAAI,CAACmC,KAAK,CAAC;EAC3B;AACF,CAAC,EACDG,iCAAkB,CACnB,CAAC;AAUF,SAAS3B,wBAAwBA,CAC/BS,MAA4B,EAE5BF,WAA2B,GAAG,IAAI,EAClCH,gBAAgC,GAAG,IAAI,EACvCC,wBAAwC,GAAG,IAAI,EACQ;EACvD,IAAIuB,WAAW;EACf,IAAIC,SAA+B,GAAGpB,MAAM,CAACqB,UAAU,CAACC,CAAC,IAAI;IAC3D,IAAIA,CAAC,CAACrC,yBAAyB,EAAE,EAAE;MAAA,IAAAsC,YAAA;MACjC,CAAAA,YAAA,GAAAJ,WAAW,YAAAI,YAAA,GAAXJ,WAAW,GAAKG,CAAC;MACjB,OAAO,KAAK;IACd;IACA,OACEA,CAAC,CAACzC,UAAU,EAAE,IACdyC,CAAC,CAACE,SAAS,EAAE,IACbF,CAAC,CAACG,eAAe,CAAC;MAAEC,MAAM,EAAE;IAAM,CAAC,CAAC,IACpCJ,CAAC,CAACK,sBAAsB,CAAC;MAAED,MAAM,EAAE;IAAM,CAAC,CAAC;EAE/C,CAAC,CAAyB;EAC1B,MAAME,aAAa,GAAGR,SAAS,CAACS,aAAa,CAAC;IAAEC,IAAI,EAAE;EAAc,CAAC,CAAC;EAEtE,IAAIV,SAAS,CAACK,eAAe,EAAE,IAAIL,SAAS,CAACO,sBAAsB,EAAE,EAAE;IACrE,IAAIR,WAAW,EAAE;MACfC,SAAS,GAAGD,WAAW;IACzB,CAAC,MAAM,IAAIxB,gBAAgB,EAAE;MAK3BK,MAAM,CAACU,WAAW,CAChB1E,cAAc,CACZJ,uBAAuB,CAAC,EAAE,EAAEwB,YAAY,CAAC4C,MAAM,CAACvC,IAAI,CAAC,CAAC,EACtD,EAAE,CACH,CACF;MACD2D,SAAS,GAAGpB,MAAM,CAAC9B,GAAG,CAAC,QAAQ,CAAwC;MACvE8B,MAAM,GAAGoB,SAAS,CAAClD,GAAG,CAAC,MAAM,CAAmC;IAClE,CAAC,MAAM;MACL,MAAM8B,MAAM,CAACV,mBAAmB,CAC9B,iDAAiD,CAClD;IACH;EACF;EAEA,MAAM;IAAEyC,SAAS;IAAEC,cAAc;IAAEC,cAAc;IAAEC,UAAU;IAAEC;EAAW,CAAC,GACzEC,mBAAmB,CAACpC,MAAM,CAAC;EAG7B,IAAI4B,aAAa,IAAIO,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;IAC1C,IAAI,CAAC1C,gBAAgB,EAAE;MACrB,MAAMwC,UAAU,CAAC,CAAC,CAAC,CAAC7C,mBAAmB,CACrC,wDAAwD,GACtD,0FAA0F,GAC1F,2EAA2E,CAC9E;IACH;IACA,IAAI,CAACM,wBAAwB,EAAE;MAE7B,MAAMuC,UAAU,CAAC,CAAC,CAAC,CAAC7C,mBAAmB,CACrC,mDAAmD,GACjD,0HAA0H,GAC1H,2EAA2E,CAC9E;IACH;IACA,MAAM0B,aAA2C,GAAG,EAAE;IACtDI,SAAS,CAACkB,QAAQ,CAAC1B,oBAAoB,EAAE;MAAEI;IAAc,CAAC,CAAC;IAC3D,MAAMuB,YAAY,GAAGC,eAAe,CAACpB,SAAS,CAAC;IAC/CJ,aAAa,CAACyB,OAAO,CAACC,SAAS,IAAI;MACjC,MAAMC,MAAM,GAAGxG,UAAU,CAACoG,YAAY,CAAC;MACvCI,MAAM,CAACC,GAAG,GAAGF,SAAS,CAACjF,IAAI,CAACkF,MAAM,CAACC,GAAG;MAEtCF,SAAS,CAACxE,GAAG,CAAC,QAAQ,CAAC,CAACwC,WAAW,CAACiC,MAAM,CAAC;IAC7C,CAAC,CAAC;EACJ;EAGA,IAAIX,cAAc,CAACK,MAAM,GAAG,CAAC,EAAE;IAC7B,MAAMQ,gBAAgB,GAAGC,UAAU,CAAC1B,SAAS,EAAE,WAAW,EAAE,MAAM;MAChE,MAAM2B,IAAI,GAAGA,CAAA,KAAM5G,UAAU,CAAC,WAAW,CAAC;MAC1C,IAAIiF,SAAS,CAACjB,KAAK,CAACV,IAAI,CAAC+B,SAAS,EAAE,EAAE;QACpC,OAAOvF,qBAAqB,CAC1BH,gBAAgB,CACd,KAAK,EACLuB,eAAe,CAAC,QAAQ,EAAE0F,IAAI,EAAE,CAAC,EACjC/F,aAAa,CAAC,WAAW,CAAC,CAC3B,EACDoE,SAAS,CAACjB,KAAK,CAAC6C,kBAAkB,EAAE,EACpCD,IAAI,EAAE,CACP;MACH,CAAC,MAAM;QACL,OAAOA,IAAI,EAAE;MACf;IACF,CAAC,CAAC;IAEFf,cAAc,CAACS,OAAO,CAACQ,cAAc,IAAI;MACvC,MAAMC,OAAO,GAAG/G,UAAU,CAAC0G,gBAAgB,CAAC;MAC5CK,OAAO,CAACN,GAAG,GAAGK,cAAc,CAACxF,IAAI,CAACmF,GAAG;MAErCK,cAAc,CAACvC,WAAW,CAACwC,OAAO,CAAC;IACrC,CAAC,CAAC;EACJ;EAGA,IAAIjB,cAAc,CAACI,MAAM,GAAG,CAAC,EAAE;IAC7B,MAAMc,gBAAgB,GAAGL,UAAU,CAAC1B,SAAS,EAAE,WAAW,EAAE,MAC1D3E,YAAY,CAACN,UAAU,CAAC,KAAK,CAAC,EAAEA,UAAU,CAAC,QAAQ,CAAC,CAAC,CACtD;IAED8F,cAAc,CAACQ,OAAO,CAACW,WAAW,IAAI;MACpC,MAAMC,SAAS,GAAGlH,UAAU,CAACgH,gBAAgB,CAAC;MAC9CE,SAAS,CAACT,GAAG,GAAGQ,WAAW,CAAC3F,IAAI,CAACmF,GAAG;MAEpCQ,WAAW,CAAC1C,WAAW,CAAC2C,SAAS,CAAC;IACpC,CAAC,CAAC;EACJ;EAGA,IAAInB,UAAU,CAACG,MAAM,GAAG,CAAC,EAAE;IACzB,IAAI,CAAC1C,gBAAgB,EAAE;MACrB,MAAMuC,UAAU,CAAC,CAAC,CAAC,CAAC5C,mBAAmB,CACrC,wDAAwD,GACtD,6FAA6F,GAC7F,2EAA2E,CAC9E;IACH;IAEA,MAAMgE,cAA8C,GAAGpB,UAAU,CAACqB,MAAM,CACtE,CAACC,GAAG,EAAEC,SAAS,KAAKD,GAAG,CAACE,MAAM,CAACC,wBAAwB,CAACF,SAAS,CAAC,CAAC,EACnE,EAAE,CACH;IAEDH,cAAc,CAACb,OAAO,CAACgB,SAAS,IAAI;MAClC,MAAMlG,GAAG,GAAGkG,SAAS,CAAChG,IAAI,CAACK,QAAQ,GAC/B,EAAE,GAEF2F,SAAS,CAACvF,GAAG,CAAC,UAAU,CAAC,CAACT,IAAI,CAACM,IAAI;MAEvC,MAAM6F,eAAe,GAAGH,SAAS,CAAC3E,UAAU;MAE5C,MAAM+E,YAAY,GAAGD,eAAe,CAACE,sBAAsB,CAAC;QAC1DC,IAAI,EAAEN,SAAS,CAAChG;MAClB,CAAC,CAAC;MACF,MAAMuG,MAAM,GAAGJ,eAAe,CAACK,gBAAgB,CAAC;QAC9CtB,MAAM,EAAEc,SAAS,CAAChG;MACpB,CAAC,CAAC;MACF,MAAMyG,gBAAgB,GAAGN,eAAe,CAACO,0BAA0B,CAAC;QAClEC,GAAG,EAAEX,SAAS,CAAChG;MACjB,CAAC,CAAC;MACF,MAAM8E,YAAY,GAAG8B,mBAAmB,CAACjD,SAAS,EAAEyC,YAAY,EAAEtG,GAAG,CAAC;MAEtE,MAAMwF,IAAoB,GAAG,EAAE;MAC/B,IAAIU,SAAS,CAAChG,IAAI,CAACK,QAAQ,EAAE;QAE3BiF,IAAI,CAACnE,IAAI,CAAC6E,SAAS,CAACvF,GAAG,CAAC,UAAU,CAAC,CAACT,IAAI,CAAiB;MAC3D;MAEA,IAAIoG,YAAY,EAAE;QAChB,MAAMS,KAAK,GAAGV,eAAe,CAACnG,IAAI,CAAC8G,KAAK;QACxCxB,IAAI,CAACnE,IAAI,CAAC0F,KAAK,CAAC;MAClB;MAEA,MAAME,IAAI,GAAGxI,cAAc,CAACG,UAAU,CAACoG,YAAY,CAAC,EAAEQ,IAAI,CAAC;MAE3D,IAAIiB,MAAM,EAAE;QACVJ,eAAe,CAACrD,gBAAgB,CAAC,WAAW,EAAEpD,cAAc,EAAE,CAAC;QAC/DsG,SAAS,CAAC/C,WAAW,CAAClE,gBAAgB,CAACgI,IAAI,EAAErI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QAEjE4F,SAAS,CAACnD,IAAI,CACZgF,eAAe,CAAC1F,GAAG,CAAC,aAAa,CAAC,CACnC;MACH,CAAC,MAAM,IAAI2F,YAAY,EAAE;QAEvBD,eAAe,CAAClD,WAAW,CAAC8D,IAAI,CAAC;MACnC,CAAC,MAAM,IAAIN,gBAAgB,EAAE;QAC3BT,SAAS,CAAC/C,WAAW,CACnB1E,cAAc,CAACQ,gBAAgB,CAACgI,IAAI,EAAErI,UAAU,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE,CAChEgB,cAAc,EAAE,CACjB,CAAC,CACH;QAED4E,SAAS,CAACnD,IAAI,CACZ6E,SAAS,CAACvF,GAAG,CAAC,aAAa,CAAC,CAC7B;MACH,CAAC,MAAM;QACLuF,SAAS,CAAC/C,WAAW,CAAC8D,IAAI,CAAC;MAC7B;IACF,CAAC,CAAC;EACJ;EAGA,IAAIzE,WAA0B;EAC9B,IAAIgC,SAAS,CAACM,MAAM,GAAG,CAAC,IAAI,CAACvC,WAAW,EAAE;IACxCC,WAAW,GAAG0E,cAAc,CAACrD,SAAS,EAAEQ,aAAa,CAAC;IAEtD,IACE9B,WAAW,IAGV8B,aAAa,IAAI8C,aAAa,CAACtD,SAAS,CAAE,EAC3C;MACAW,SAAS,CAACU,OAAO,CAACkC,SAAS,IAAI;QAC7B,MAAMC,OAAO,GAAGD,SAAS,CAACE,KAAK,EAAE,GAC7BxI,aAAa,CAAC0D,WAAW,CAAC,GAC1B5D,UAAU,CAAC4D,WAAW,CAAC;QAE3B6E,OAAO,CAAChC,GAAG,GAAG+B,SAAS,CAAClH,IAAI,CAACmF,GAAG;QAChC+B,SAAS,CAACjE,WAAW,CAACkE,OAAO,CAAC;MAChC,CAAC,CAAC;MAEF,IAAI,CAAC9E,WAAW,EAAEC,WAAW,GAAG,IAAI;IACtC;EACF;EAEA,OAAO;IAAEA,WAAW;IAAEC;EAAO,CAAC;AAChC;AAKA,SAAS8E,WAAWA,CAACC,EAAU,EAAmB;EAChD,OAAOxI,iBAAiB,CAACyI,QAAQ,CAACD,EAAE,CAAC;AACvC;AAEA,SAASpB,wBAAwBA,CAC/BF,SAAuC,EAGwB;EAC/D,IACEA,SAAS,CAAC3E,UAAU,CAACgF,sBAAsB,EAAE,IAC7CL,SAAS,CAAC3E,UAAU,CAACrB,IAAI,CAACwH,QAAQ,KAAK,GAAG,EAC1C;IACA,MAAMC,cAAc,GAAGzB,SAAS,CAAC3E,UAAU;IAE3C,MAAMiG,EAAE,GAAGG,cAAc,CAACzH,IAAI,CAACwH,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAEvC;IAEZ,MAAMb,KAAK,GAAGY,cAAc,CAACzH,IAAI,CAAC8G,KAAK;IAEvC,MAAMa,mBAAmB,GAAGN,WAAW,CAACC,EAAE,CAAC;IAE3C,IAAItB,SAAS,CAAChG,IAAI,CAACK,QAAQ,EAAE;MAO3B,MAAMuH,GAAG,GAAG5B,SAAS,CAACtD,KAAK,CAACmF,6BAA6B,CAAC,KAAK,CAAC;MAEhE,MAAMC,MAAM,GAAG9B,SAAS,CAAChG,IAAI,CAAC8H,MAAM;MACpC,MAAM7H,QAAQ,GAAG+F,SAAS,CAAChG,IAAI,CAACC,QAAwB;MAExDwH,cAAc,CACXhH,GAAG,CAAC,MAAM,CAAC,CACXwC,WAAW,CACVlE,gBAAgB,CACd+I,MAAM,EACN1J,oBAAoB,CAAC,GAAG,EAAEwJ,GAAG,EAAE3H,QAAQ,CAAC,EACxC,IAAI,CACL,CACF;MAEHwH,cAAc,CACXhH,GAAG,CAAC,OAAO,CAAC,CACZwC,WAAW,CACV8E,eAAe,CACbJ,mBAAmB,GAAG,GAAG,GAAGL,EAAE,EAC9BvI,gBAAgB,CAAC+I,MAAM,EAAEpJ,UAAU,CAACkJ,GAAG,CAACtH,IAAI,CAAC,EAAE,IAAI,CAAgB,EACnEuG,KAAK,CACN,CACF;IACL,CAAC,MAAM;MAOL,MAAMiB,MAAM,GAAG9B,SAAS,CAAChG,IAAI,CAAC8H,MAAM;MACpC,MAAM7H,QAAQ,GAAG+F,SAAS,CAAChG,IAAI,CAACC,QAAwB;MAExDwH,cAAc,CACXhH,GAAG,CAAC,MAAM,CAAC,CACXwC,WAAW,CAAClE,gBAAgB,CAAC+I,MAAM,EAAE7H,QAAQ,CAAC,CAAC;MAElDwH,cAAc,CACXhH,GAAG,CAAC,OAAO,CAAC,CACZwC,WAAW,CACV8E,eAAe,CACbJ,mBAAmB,GAAG,GAAG,GAAGL,EAAE,EAC9BvI,gBAAgB,CAAC+I,MAAM,EAAEpJ,UAAU,CAACuB,QAAQ,CAACK,IAAI,CAAC,CAAC,EACnDuG,KAAK,CACN,CACF;IACL;IAEA,IAAIc,mBAAmB,EAAE;MACvBF,cAAc,CAACxE,WAAW,CACxBpE,iBAAiB,CACfyI,EAAE,EACFG,cAAc,CAACzH,IAAI,CAACsG,IAAI,EACxBmB,cAAc,CAACzH,IAAI,CAAC8G,KAAK,CAC1B,CACF;IACH,CAAC,MAAM;MACLW,cAAc,CAACzH,IAAI,CAACwH,QAAQ,GAAG,GAAG;IACpC;IAEA,OAAO,CACLC,cAAc,CAAChH,GAAG,CAAC,MAAM,CAAC,EAC1BgH,cAAc,CAAChH,GAAG,CAAC,OAAO,CAAC,CAACA,GAAG,CAAC,MAAM,CAAC,CACxC;EACH,CAAC,MAAM,IAAIuF,SAAS,CAAC3E,UAAU,CAAC2G,kBAAkB,EAAE,EAAE;IACpD,MAAMC,UAAU,GAAGjC,SAAS,CAAC3E,UAAU;IAEvC,MAAMuG,GAAG,GAAG5B,SAAS,CAACtD,KAAK,CAACmF,6BAA6B,CAAC,KAAK,CAAC;IAChE,MAAMK,WAAW,GAAGlC,SAAS,CAAChG,IAAI,CAACK,QAAQ,GACvC2F,SAAS,CAACtD,KAAK,CAACmF,6BAA6B,CAAC,MAAM,CAAC,GACrD,IAAI;IAER,MAAMM,KAAqB,GAAG,CAC5B/J,oBAAoB,CAClB,GAAG,EACHwJ,GAAG,EACH7I,gBAAgB,CACdiH,SAAS,CAAChG,IAAI,CAAC8H,MAAM,EACrBI,WAAW,GACP9J,oBAAoB,CAClB,GAAG,EACH8J,WAAW,EACXlC,SAAS,CAAChG,IAAI,CAACC,QAAQ,CACxB,GACD+F,SAAS,CAAChG,IAAI,CAACC,QAAQ,EAC3B+F,SAAS,CAAChG,IAAI,CAACK,QAAQ,CACxB,CACF,EACDjC,oBAAoB,CAClB,GAAG,EACHW,gBAAgB,CACdiH,SAAS,CAAChG,IAAI,CAAC8H,MAAM,EACrBI,WAAW,GAAGxJ,UAAU,CAACwJ,WAAW,CAAC5H,IAAI,CAAC,GAAG0F,SAAS,CAAChG,IAAI,CAACC,QAAQ,EACpE+F,SAAS,CAAChG,IAAI,CAACK,QAAQ,CACxB,EACDhC,gBAAgB,CAEd2H,SAAS,CAAC3E,UAAU,CAACrB,IAAI,CAACwH,QAAQ,CAAC,CAAC,CAAC,EACrC9I,UAAU,CAACkJ,GAAG,CAACtH,IAAI,CAAC,EACpBrB,cAAc,CAAC,CAAC,CAAC,CAClB,CACF,CACF;IAED,IAAI,CAAC+G,SAAS,CAAC3E,UAAU,CAACrB,IAAI,CAACoI,MAAM,EAAE;MACrCD,KAAK,CAAChH,IAAI,CAACzC,UAAU,CAACkJ,GAAG,CAACtH,IAAI,CAAC,CAAC;IAClC;IAEA2H,UAAU,CAAChF,WAAW,CAAC5D,kBAAkB,CAAC8I,KAAK,CAAC,CAAC;IAEjD,MAAM7B,IAAI,GAAG2B,UAAU,CAACxH,GAAG,CACzB,qBAAqB,CACU;IACjC,MAAMqG,KAAK,GAAGmB,UAAU,CAACxH,GAAG,CAC1B,oBAAoB,CACW;IACjC,OAAO,CAAC6F,IAAI,EAAEQ,KAAK,CAAC;EACtB;EAEA,OAAO,CAACd,SAAS,CAAC;EAElB,SAAS+B,eAAeA,CACtBT,EAAkB,EAClBhB,IAAwB,EACxBQ,KAAmB,EACnB;IACA,IAAIQ,EAAE,KAAK,GAAG,EAAE;MACd,OAAOlJ,oBAAoB,CAAC,GAAG,EAAEkI,IAAI,EAAEQ,KAAK,CAAC;IAC/C,CAAC,MAAM;MACL,OAAOzI,gBAAgB,CAACiJ,EAAE,EAAEhB,IAAI,EAAEQ,KAAK,CAAC;IAC1C;EACF;AACF;AAEA,SAASG,aAAaA,CAACtD,SAA+B,EAAE;EACtD,OACEA,SAAS,CAACS,aAAa,EAAE,IACzB,CAAC,CAAET,SAAS,CAACtC,UAAU,CAACA,UAAU,CAACrB,IAAI,CAAaqI,UAAU;AAElE;AAEA,MAAMC,sBAAsB,GAAG,IAAAlF,eAAa,EAGzC,CACD;EACEC,cAAcA,CAACC,KAAK,EAAE;IAAEiF,MAAM;IAAEjG;EAAY,CAAC,EAAE;IAC7C,IAAI,CAACgB,KAAK,CAAC7C,GAAG,CAAC,QAAQ,CAAC,CAAC+C,OAAO,EAAE,EAAE;IACpC,IAAI+E,MAAM,CAACC,GAAG,CAAClF,KAAK,CAACtD,IAAI,CAAC,EAAE;IAC5BuI,MAAM,CAACE,GAAG,CAACnF,KAAK,CAACtD,IAAI,CAAC;IAEtBsD,KAAK,CAACoF,mBAAmB,CAAC,CACxBpF,KAAK,CAACtD,IAAI,EACV5B,oBAAoB,CAAC,GAAG,EAAEM,UAAU,CAAC4D,WAAW,CAAC,EAAE5D,UAAU,CAAC,MAAM,CAAC,CAAC,CACvE,CAAC;EACJ;AACF,CAAC,EACD+E,iCAAkB,CACnB,CAAC;AAGF,SAASuD,cAAcA,CACrBrD,SAA+B,EAC/BQ,aAAsB,EACtB;EACA,OAAOkB,UAAU,CAAC1B,SAAS,EAAE,MAAM,EAAErB,WAAW,IAAI;IAClD,IAAI,CAAC6B,aAAa,IAAI,CAAC8C,aAAa,CAACtD,SAAS,CAAC,EAAE,OAAOjE,cAAc,EAAE;IAExEiE,SAAS,CAACkB,QAAQ,CAACyD,sBAAsB,EAAE;MACzCC,MAAM,EAAE,IAAII,OAAO,EAAE;MACrBrG;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAGA,SAASyC,eAAeA,CAACpB,SAA+B,EAAE;EACxD,OAAO0B,UAAU,CAAC1B,SAAS,EAAE,WAAW,EAAE,MAAM;IAC9C,MAAMiF,WAAW,GAAGjF,SAAS,CAACjB,KAAK,CAACC,qBAAqB,CAAC,MAAM,CAAC;IACjE,OAAOxE,uBAAuB,CAC5B,CAACgB,WAAW,CAACyJ,WAAW,CAAC,CAAC,EAC1BrK,cAAc,CAACkB,MAAM,EAAE,EAAE,CAACH,aAAa,CAACZ,UAAU,CAACkK,WAAW,CAACtI,IAAI,CAAC,CAAC,CAAC,CAAC,CACxE;EACH,CAAC,CAAC;AACJ;AAGA,SAASsG,mBAAmBA,CAC1BjD,SAA+B,EAC/ByC,YAAqB,EACrByC,QAAgB,EAChB;EACA,MAAMvB,EAAE,GAAGlB,YAAY,GAAG,KAAK,GAAG,KAAK;EAEvC,OAAOf,UAAU,CAAC1B,SAAS,EAAG,aAAY2D,EAAG,IAAGuB,QAAQ,IAAI,EAAG,EAAC,EAAE,MAAM;IACtE,MAAMC,QAAQ,GAAG,EAAE;IAEnB,IAAIC,MAAM;IACV,IAAIF,QAAQ,EAAE;MAEZE,MAAM,GAAGhK,gBAAgB,CAACU,MAAM,EAAE,EAAEf,UAAU,CAACmK,QAAQ,CAAC,CAAC;IAC3D,CAAC,MAAM;MACL,MAAMG,MAAM,GAAGrF,SAAS,CAACjB,KAAK,CAACC,qBAAqB,CAAC,MAAM,CAAC;MAE5DmG,QAAQ,CAACG,OAAO,CAACD,MAAM,CAAC;MACxBD,MAAM,GAAGhK,gBAAgB,CACvBU,MAAM,EAAE,EACRf,UAAU,CAACsK,MAAM,CAAC1I,IAAI,CAAC,EACvB,IAAI,CACL;IACH;IAEA,IAAI8F,YAAY,EAAE;MAChB,MAAM8C,UAAU,GAAGvF,SAAS,CAACjB,KAAK,CAACC,qBAAqB,CAAC,OAAO,CAAC;MACjEmG,QAAQ,CAAC3H,IAAI,CAAC+H,UAAU,CAAC;MAEzBH,MAAM,GAAG3K,oBAAoB,CAAC,GAAG,EAAE2K,MAAM,EAAErK,UAAU,CAACwK,UAAU,CAAC5I,IAAI,CAAC,CAAC;IACzE;IAEA,OAAOnC,uBAAuB,CAAC2K,QAAQ,EAAEC,MAAM,CAAC;EAClD,CAAC,CAAC;AACJ;AAEA,SAAS1D,UAAUA,CACjB1B,SAAmB,EACnB7D,GAAW,EACX+C,IAAoC,EACpC;EACA,MAAMsG,QAAQ,GAAG,UAAU,GAAGrJ,GAAG;EACjC,IAAIsJ,IAAwB,GAAGzF,SAAS,CAAC0F,OAAO,CAACF,QAAQ,CAAC;EAC1D,IAAI,CAACC,IAAI,EAAE;IACT,MAAMxG,EAAE,GAAGe,SAAS,CAACjB,KAAK,CAACC,qBAAqB,CAAC7C,GAAG,CAAC;IACrDsJ,IAAI,GAAGxG,EAAE,CAACtC,IAAI;IACdqD,SAAS,CAAC2F,OAAO,CAACH,QAAQ,EAAEC,IAAI,CAAC;IAEjCzF,SAAS,CAACjB,KAAK,CAACvB,IAAI,CAAC;MACnByB,EAAE,EAAEA,EAAE;MACNC,IAAI,EAAEA,IAAI,CAACuG,IAAI;IACjB,CAAC,CAAC;EACJ;EAEA,OAAOA,IAAI;AACb;AAUA,MAAMG,0BAA0B,GAAG,IAAAnG,eAAa,EAAY,CAC1D;EACEoG,cAAcA,CAAClG,KAAK,EAAE;IAAEgB;EAAU,CAAC,EAAE;IACnCA,SAAS,CAACnD,IAAI,CAACmC,KAAK,CAAC;EACvB,CAAC;EACDmG,aAAaA,CAACnG,KAAK,EAAE;IAAEgB;EAAU,CAAC,EAAE;IAClC,IAAIhB,KAAK,CAACtD,IAAI,CAACM,IAAI,KAAK,MAAM,EAAE;IAChC,IACE,CAACgD,KAAK,CAACjC,UAAU,CAACqI,qBAAqB,CAAC;MAAE5B,MAAM,EAAExE,KAAK,CAACtD;IAAK,CAAC,CAAC,IAC/D,CAACsD,KAAK,CAACjC,UAAU,CAACsI,mBAAmB,CAAC;MAAErJ,IAAI,EAAEgD,KAAK,CAACtD;IAAK,CAAC,CAAC,EAC3D;MACA;IACF;IAEAsE,SAAS,CAACnD,IAAI,CAACmC,KAAK,CAAC;EACvB,CAAC;EACDD,cAAcA,CAACC,KAAK,EAAE;IAAEoB;EAAW,CAAC,EAAE;IACpC,IAAIpB,KAAK,CAAC7C,GAAG,CAAC,QAAQ,CAAC,CAAC+C,OAAO,EAAE,EAAEkB,UAAU,CAACvD,IAAI,CAACmC,KAAK,CAAC;EAC3D,CAAC;EACDsG,gBAAgBA,CAACtG,KAAK,EAAE;IAAEmB;EAAW,CAAC,EAAE;IACtC,IAAInB,KAAK,CAAC7C,GAAG,CAAC,QAAQ,CAAC,CAAC+C,OAAO,EAAE,EAAEiB,UAAU,CAACtD,IAAI,CAACmC,KAAK,CAAC;EAC3D,CAAC;EACDuG,UAAUA,CAACvG,KAAK,EAAE;IAAEiB;EAAe,CAAC,EAAE;IACpC,IAAI,CAACjB,KAAK,CAACwG,sBAAsB,CAAC;MAAExJ,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE;IAE1D,IAAIyJ,IAAI,GAAGzG,KAAK,CAACZ,KAAK;IACtB,GAAG;MACD,IAAIqH,IAAI,CAACC,aAAa,CAAC,WAAW,CAAC,EAAE;QACnCD,IAAI,CAACE,MAAM,CAAC,WAAW,CAAC;QACxB;MACF;MACA,IAAIF,IAAI,CAAC/H,IAAI,CAACZ,UAAU,EAAE,IAAI,CAAC2I,IAAI,CAAC/H,IAAI,CAACR,yBAAyB,EAAE,EAAE;QACpE;MACF;IACF,CAAC,QAASuI,IAAI,GAAGA,IAAI,CAACG,MAAM;IAE5B3F,cAAc,CAACpD,IAAI,CAACmC,KAAK,CAAC;EAC5B,CAAC;EACD6G,YAAYA,CAAC7G,KAAK,EAAE;IAAEkB;EAAe,CAAC,EAAE;IACtC,IAAI,CAAClB,KAAK,CAAC7C,GAAG,CAAC,MAAM,CAAC,CAAC9B,YAAY,CAAC;MAAE2B,IAAI,EAAE;IAAM,CAAC,CAAC,EAAE;IACtD,IAAI,CAACgD,KAAK,CAAC7C,GAAG,CAAC,UAAU,CAAC,CAAC9B,YAAY,CAAC;MAAE2B,IAAI,EAAE;IAAS,CAAC,CAAC,EAAE;IAE7DkE,cAAc,CAACrD,IAAI,CAACmC,KAAK,CAAC;EAC5B;AACF,CAAC,EACDG,iCAAkB,CACnB,CAAC;AAEF,SAASkB,mBAAmBA,CAACpC,MAAgB,EAAE;EAC7C,MAAM+B,SAAiC,GAAG,EAAE;EAC5C,MAAMC,cAA2C,GAAG,EAAE;EACtD,MAAMC,cAA2C,GAAG,EAAE;EACtD,MAAMC,UAAmC,GAAG,EAAE;EAC9C,MAAMC,UAAmC,GAAG,EAAE;EAE9CnC,MAAM,CAACsC,QAAQ,CAAC0E,0BAA0B,EAAE;IAC1CjF,SAAS;IACTC,cAAc;IACdC,cAAc;IACdC,UAAU;IACVC;EACF,CAAC,CAAC;EAEF,OAAO;IACLJ,SAAS;IACTC,cAAc;IACdC,cAAc;IACdC,UAAU;IACVC;EACF,CAAC;AACH"}