import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import React, { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useAction } from "@appmaker-xyz/react-native";
import { appmaker } from "@appmaker-xyz/core";
const schema = z
    .object({
        email: z.string({
            required_error: 'Please enter your email address',
        })
            .trim().
            email("Please enter a valid email address"),
        password: z.string({
            required_error: 'Please enter a password',
        }),
    });
type FormType = z.infer<typeof schema>;
type SocialButton = {
    name: string;
    component: React.Component
}
export function useLogin(
    {
        defaultValues
    }: {
        defaultValues?: Partial<FormType>
    } = {}
) {
    const socialButtons: SocialButton[] = useMemo(() => appmaker.applyFilters('social-login-buttons', []) as any[], []);
    const onAction = useAction();
    const {
        control,
        handleSubmit,
        formState,
        setError,
        setFocus
    } = useForm({
        resolver: zodResolver(schema),
        defaultValues,
    });
    const [loading, setLoading] = useState(false);
    const submitLogin = handleSubmit(async (data) => {
        setLoading(true);
        const resp = await onAction({
            action: 'LOGIN_USER',
            params: {
                username: data.email,
                password: data.password,
            },
        });
        setLoading(false);
    })
    return {
        control,
        handleSubmit,
        formState,
        submitLogin,
        isLoading: loading,
        socialButtons,
        setFocus
    }
}