import { StyleSheet, Platform } from 'react-native';
import Config from '../../../../Config';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: 20,
    marginHorizontal: 20,
    marginVertical: 20,
  },
  fieldContainer: {
    marginVertical: 5,
  },
  submit: {
    marginTop: 30,
    paddingTop: 10,
    paddingBottom: 10,
    borderWidth: 1,
    elevation: 2,
    flex: 1,
  },
  closeImage: {
    alignItems: 'flex-end',
    marginRight: 10,
    marginTop: Platform.OS === 'ios' ? 50 : 10,
    width: 25,
    height: 25,
    opacity: 0.5,
  },
  heading: {
    fontWeight: 'bold',
    color: Config.toolbarColor,
    textAlign: 'center',
    fontSize: 20,
    marginBottom: 40,
  },
  comment: {
    marginBottom: 5,
  },
  inputComment: {
    borderColor: '#b0bec5',
    borderWidth: 0.8,
    borderRadius: 5,
  },
  name: {
    marginBottom: 5,
  },
  inputName: {
    borderColor: '#b0bec5',
    borderWidth: 0.8,
    borderRadius: 5,
  },
  email: {
    marginBottom: 5,
  },
  inputEmail: {
    borderColor: '#b0bec5',
    borderWidth: 0.8,
    borderRadius: 5,
  },
  postCommentText: {
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',,
  },
});

export default styles;
