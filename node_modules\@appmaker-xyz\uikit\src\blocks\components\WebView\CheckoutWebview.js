import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { Button } from '@appmaker-xyz/uikit/src/components/molecules/index';
import { WebView } from 'react-native-webview';

function CartPage({ setCheckoutVisible, checkoutVisible }) {
  return (
    <View style={{ flex: 1, backgroundColor: 'yellow' }}>
      <View style={{ flex: 1 }}>
        <Text>Cart</Text>
      </View>
      <Button
        onPress={() => {
          console.log('sss');
          setCheckoutVisible(!checkoutVisible);
        }}
        baseSize>
        Submit
      </Button>
    </View>
  );
}
function CheckoutPage({ checkoutVisible, setCheckoutVisible }) {
  return (
    <View
      style={{
        backgroundColor: 'red',
        position: 'absolute',
        zIndex: checkoutVisible ? 100000 : -1,
        width: '100%',
        height: '100%',
      }}>
      <View style={{ flex: 1 }}>
        <Text>CheckoutWebview</Text>
        <WebView
          cacheEnabled={true}
          source={{
            uri: 'https://www.chumbak.com/60110930098/checkouts/fd162564df486da93ba2cbb77c8fcbf3',
          }}
        />
      </View>
      <Button
        onPress={() => {
          console.log('sss');
          setCheckoutVisible(!checkoutVisible);
        }}
        baseSize>
        Submit
      </Button>
    </View>
  );
}
export default function CheckoutWebview() {
  const [checkoutVisible, setCheckoutVisible] = React.useState(true);

  return (
    <View style={{ flex: 1 }}>
      <CheckoutPage
        setCheckoutVisible={setCheckoutVisible}
        checkoutVisible={checkoutVisible}
      />
      <CartPage
        setCheckoutVisible={setCheckoutVisible}
        checkoutVisible={checkoutVisible}
      />
    </View>
  );
}

const styles = StyleSheet.create({});
