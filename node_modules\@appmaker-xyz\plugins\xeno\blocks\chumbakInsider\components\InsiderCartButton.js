import React from 'react';
import { StyleSheet } from 'react-native';
import { ThemeText, AppImage, AppTouchable, Layout } from '@appmaker-xyz/ui';
import Icon from 'react-native-vector-icons/Feather';

const ChumbakInsiderCartButton = ({ attributes, onPress, onAction }) => {
  const { appmakerAction } = attributes;

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      onAction(appmakerAction);
    };
  }

  return (
    <AppTouchable style={styles.container} onPress={onPressHandle}>
      <Layout style={styles.containerInner}>
        <ThemeText>Add</ThemeText>
        <AppImage
          uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
          style={styles.insiderImage}
        />
        <ThemeText>membership</ThemeText>
      </Layout>
      <Icon name="plus" size={20} color={'#1B1B1B'} style={styles.icon} />
    </AppTouchable>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-evenly',
    padding: 12,
    backgroundColor: '#B6DCC9',
    width: '100%',
  },
  containerInner: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  insiderImage: {
    width: 60,
    height: 30,
    resizeMode: 'contain',
    marginHorizontal: 6,
  },
  icon: {
    paddingHorizontal: 24,
  },
});

export default ChumbakInsiderCartButton;
