import { appmaker } from '@appmaker-xyz/core';
import { getExtensionConfig } from '@appmaker-xyz/core';

export const addBlockInOrderDetail = () => {
  appmaker.addFilter(
    'inapp-page-data-response',
    'response-data',
    (data, { pageId }) => {
      if (pageId === 'orderDetail') {
        let shop_name = getExtensionConfig(
          'shopify',
          'shopifyStoreDomain',
          false,
        );
        if (shop_name) {
          const orderDetailActionBar = {
            clientId: 'orderify-action-bar',
            name: 'appmaker/actionbar',
            attributes: {
              title: 'Edit Order',
              appmakerAction: {
                params: {
                  url: `https://shopify-order-edit.herokuapp.com/order-editor/${shop_name}/{{shopifyIdHelper(blockItem.node.id, true)}}`,
                },
                action: 'OPEN_WEBVIEW',
              },
            },
          };

          var foundIndex = data.blocks.findIndex(
            (x) => x.clientId === 'order-detail-header',
          );
          var duplicateIndex = data.blocks.findIndex(
            (x) => x.clientId === orderDetailActionBar?.clientId,
          );
          if (foundIndex !== -1 && duplicateIndex === -1) {
            data.blocks.splice(foundIndex + 1, 0, orderDetailActionBar);
          }
        }
      }
      return data;
    },
  );
};
