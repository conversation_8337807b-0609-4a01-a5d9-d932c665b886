import React from 'react';
import { AppTouchable, ThemeText } from '../atoms';
import { useStyles } from '@appmaker-xyz/react-native';
import { usePluginStore } from '@appmaker-xyz/core';

const RichText = ({ attributes = {}, onPress, onAction }) => {
  const {
    appmakerAction,
    containerStyles,
    textStyles,
    html = false,
    themeFontFamily,
    text,
    tagStyles,
  } = attributes;

  const isSystemThemeEnabled = usePluginStore(
    (state) =>
      state?.plugins['app-branding']?.settings?.isSystemThemeSwitchEnabled,
  );
  const { theme } = useStyles();

  const defaultTextColor = isSystemThemeEnabled ? theme.colors.text : '#000';

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  if (!text) {
    return null;
  }

  return (
    <AppTouchable onPress={onPressHandle} style={containerStyles}>
      <ThemeText
        html={html}
        style={textStyles}
        fontFamily={themeFontFamily}
        color={defaultTextColor}
        customHtmlTagsStyles={html ? tagStyles : null}>
        {text}
      </ThemeText>
    </AppTouchable>
  );
};

export default RichText;
