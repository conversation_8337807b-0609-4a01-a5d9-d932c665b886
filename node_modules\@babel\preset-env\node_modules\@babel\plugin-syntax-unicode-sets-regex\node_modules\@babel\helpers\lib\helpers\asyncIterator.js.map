{"version": 3, "names": ["_asyncIterator", "iterable", "method", "async", "sync", "retry", "Symbol", "asyncIterator", "iterator", "call", "AsyncFromSyncIterator", "TypeError", "s", "n", "next", "prototype", "AsyncFromSyncIteratorContinuation", "apply", "arguments", "return", "value", "ret", "undefined", "Promise", "resolve", "done", "throw", "thr", "reject", "r", "Object", "then"], "sources": ["../../src/helpers/asyncIterator.js"], "sourcesContent": ["/* @minVersion 7.15.9 */\n\nexport default function _asyncIterator(iterable) {\n  var method,\n    async,\n    sync,\n    retry = 2;\n\n  if (typeof Symbol !== \"undefined\") {\n    async = Symbol.asyncIterator;\n    sync = Symbol.iterator;\n  }\n\n  while (retry--) {\n    if (async && (method = iterable[async]) != null) {\n      return method.call(iterable);\n    }\n    if (sync && (method = iterable[sync]) != null) {\n      return new AsyncFromSyncIterator(method.call(iterable));\n    }\n\n    async = \"@@asyncIterator\";\n    sync = \"@@iterator\";\n  }\n\n  throw new TypeError(\"Object is not async iterable\");\n}\n\nfunction AsyncFromSyncIterator(s) {\n  AsyncFromSyncIterator = function (s) {\n    this.s = s;\n    this.n = s.next;\n  };\n  AsyncFromSyncIterator.prototype = {\n    /* SyncIterator */ s: null,\n    /* SyncIterator.[[Next]] */ n: null,\n    next: function () {\n      return AsyncFromSyncIteratorContinuation(this.n.apply(this.s, arguments));\n    },\n    return: function (value) {\n      var ret = this.s.return;\n      if (ret === undefined) {\n        return Promise.resolve({ value: value, done: true });\n      }\n      return AsyncFromSyncIteratorContinuation(ret.apply(this.s, arguments));\n    },\n    throw: function (value) {\n      var thr = this.s.return;\n      if (thr === undefined) return Promise.reject(value);\n      return AsyncFromSyncIteratorContinuation(thr.apply(this.s, arguments));\n    },\n  };\n\n  function AsyncFromSyncIteratorContinuation(r) {\n    // This step is _before_ calling AsyncFromSyncIteratorContinuation in the spec.\n    if (Object(r) !== r) {\n      return Promise.reject(new TypeError(r + \" is not an object.\"));\n    }\n\n    var done = r.done;\n    return Promise.resolve(r.value).then(function (value) {\n      return { value: value, done: done };\n    });\n  }\n\n  return new AsyncFromSyncIterator(s);\n}\n"], "mappings": ";;;;;;AAEe,SAASA,cAAc,CAACC,QAAQ,EAAE;EAC/C,IAAIC,MAAM;IACRC,KAAK;IACLC,IAAI;IACJC,KAAK,GAAG,CAAC;EAEX,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjCH,KAAK,GAAGG,MAAM,CAACC,aAAa;IAC5BH,IAAI,GAAGE,MAAM,CAACE,QAAQ;EACxB;EAEA,OAAOH,KAAK,EAAE,EAAE;IACd,IAAIF,KAAK,IAAI,CAACD,MAAM,GAAGD,QAAQ,CAACE,KAAK,CAAC,KAAK,IAAI,EAAE;MAC/C,OAAOD,MAAM,CAACO,IAAI,CAACR,QAAQ,CAAC;IAC9B;IACA,IAAIG,IAAI,IAAI,CAACF,MAAM,GAAGD,QAAQ,CAACG,IAAI,CAAC,KAAK,IAAI,EAAE;MAC7C,OAAO,IAAIM,qBAAqB,CAACR,MAAM,CAACO,IAAI,CAACR,QAAQ,CAAC,CAAC;IACzD;IAEAE,KAAK,GAAG,iBAAiB;IACzBC,IAAI,GAAG,YAAY;EACrB;EAEA,MAAM,IAAIO,SAAS,CAAC,8BAA8B,CAAC;AACrD;AAEA,SAASD,qBAAqB,CAACE,CAAC,EAAE;EAChCF,qBAAqB,GAAG,UAAUE,CAAC,EAAE;IACnC,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGD,CAAC,CAACE,IAAI;EACjB,CAAC;EACDJ,qBAAqB,CAACK,SAAS,GAAG;IACbH,CAAC,EAAE,IAAI;IACEC,CAAC,EAAE,IAAI;IACnCC,IAAI,EAAE,YAAY;MAChB,OAAOE,iCAAiC,CAAC,IAAI,CAACH,CAAC,CAACI,KAAK,CAAC,IAAI,CAACL,CAAC,EAAEM,SAAS,CAAC,CAAC;IAC3E,CAAC;IACDC,MAAM,EAAE,UAAUC,KAAK,EAAE;MACvB,IAAIC,GAAG,GAAG,IAAI,CAACT,CAAC,CAACO,MAAM;MACvB,IAAIE,GAAG,KAAKC,SAAS,EAAE;QACrB,OAAOC,OAAO,CAACC,OAAO,CAAC;UAAEJ,KAAK,EAAEA,KAAK;UAAEK,IAAI,EAAE;QAAK,CAAC,CAAC;MACtD;MACA,OAAOT,iCAAiC,CAACK,GAAG,CAACJ,KAAK,CAAC,IAAI,CAACL,CAAC,EAAEM,SAAS,CAAC,CAAC;IACxE,CAAC;IACDQ,KAAK,EAAE,UAAUN,KAAK,EAAE;MACtB,IAAIO,GAAG,GAAG,IAAI,CAACf,CAAC,CAACO,MAAM;MACvB,IAAIQ,GAAG,KAAKL,SAAS,EAAE,OAAOC,OAAO,CAACK,MAAM,CAACR,KAAK,CAAC;MACnD,OAAOJ,iCAAiC,CAACW,GAAG,CAACV,KAAK,CAAC,IAAI,CAACL,CAAC,EAAEM,SAAS,CAAC,CAAC;IACxE;EACF,CAAC;EAED,SAASF,iCAAiC,CAACa,CAAC,EAAE;IAE5C,IAAIC,MAAM,CAACD,CAAC,CAAC,KAAKA,CAAC,EAAE;MACnB,OAAON,OAAO,CAACK,MAAM,CAAC,IAAIjB,SAAS,CAACkB,CAAC,GAAG,oBAAoB,CAAC,CAAC;IAChE;IAEA,IAAIJ,IAAI,GAAGI,CAAC,CAACJ,IAAI;IACjB,OAAOF,OAAO,CAACC,OAAO,CAACK,CAAC,CAACT,KAAK,CAAC,CAACW,IAAI,CAAC,UAAUX,KAAK,EAAE;MACpD,OAAO;QAAEA,KAAK,EAAEA,KAAK;QAAEK,IAAI,EAAEA;MAAK,CAAC;IACrC,CAAC,CAAC;EACJ;EAEA,OAAO,IAAIf,qBAAqB,CAACE,CAAC,CAAC;AACrC"}