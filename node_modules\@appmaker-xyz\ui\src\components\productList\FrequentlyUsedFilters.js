import React, { useState } from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { ThemeText, AppTouchable, Layout } from '../atoms';
import { Button } from '../coreComponents';

const filters = [
  'S',
  'M',
  'L',
  'Black',
  'White',
  'Shirt',
  'T-Shirt',
  'Jean<PERSON>',
  'Dress',
  'Sneakers',
  'Boots',
  'Formal',
  'Casual',
];

function FilterItem(props) {
  const [active, setActive] = useState(false);
  const { filter, themeColor } = props;
  const filterItemContainer = [styles.filterItem];

  if (themeColor) {
    filterItemContainer.push({
      backgroundColor: `${themeColor}12`,
      borderColor: `${themeColor}40`,
    });
  }
  if (active) {
    filterItemContainer.push({
      borderColor: themeColor,
    });
  }

  return (
    <AppTouchable
      style={filterItemContainer}
      onPress={() => setActive(!active)}>
      <Icon
        name="check"
        size={16}
        color={active ? themeColor : `${themeColor}40`}
        style={styles.checkIcon}
      />
      <ThemeText color={themeColor}>{filter}</ThemeText>
    </AppTouchable>
  );
}

const FrequentlyUsedFilters = ({ attributes = {} }) => {
  const [selected, setSelected] = useState(true);
  const { themeColor = '#000000' } = attributes;
  const containerStyles = [styles.container];

  if (themeColor) {
    containerStyles.push({
      backgroundColor: `${themeColor}05`,
    });
  }
  return (
    <Layout style={containerStyles}>
      <Layout style={styles.titleContainer}>
        <ThemeText size="md" fontFamily="medium">
          Frequently Used Filters
        </ThemeText>
        <AppTouchable style={styles.clearButton}>
          <ThemeText color={themeColor} size="sm" fontFamily="medium">
            {selected ? 'Clear All' : 'Select All'}
          </ThemeText>
        </AppTouchable>
      </Layout>
      <Layout style={styles.innerContainer}>
        <ScrollView
          contentContainerStyle={styles.scrollerContainer}
          horizontal={true}
          showsHorizontalScrollIndicator={false}>
          <ThemeText style={styles.filterTitle}>Filter name</ThemeText>
          {filters.map((filter) => (
            <FilterItem themeColor={themeColor} filter={filter} />
          ))}
        </ScrollView>
        <ScrollView
          contentContainerStyle={styles.scrollerContainer}
          horizontal={true}
          showsHorizontalScrollIndicator={false}>
          <ThemeText style={styles.filterTitle}>Filter name</ThemeText>
          {filters.map((filter) => (
            <FilterItem themeColor={themeColor} filter={filter} />
          ))}
        </ScrollView>
      </Layout>

      {selected ? (
        <Button
          title="Press me!"
          onPress={() => {}}
          isOutline={false}
          color={themeColor}
          buttonStyle={styles.button}
        />
      ) : null}
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {},
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingTop: 12,
  },
  clearButton: {
    paddingVertical: 4,
  },
  innerContainer: {
    paddingVertical: 12,
  },
  scrollerContainer: {
    marginVertical: 2,
    paddingHorizontal: 12,
    alignItems: 'center',
  },
  filterTitle: {
    marginRight: 8,
  },
  filterItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 6,
    borderWidth: 1,
    margin: 2,
  },
  checkIcon: {
    marginRight: 4,
  },
  button: {
    marginBottom: 12,
    alignSelf: 'center',
  },
});

export default FrequentlyUsedFilters;
