{"version": 3, "names": ["_matchesPattern", "require", "buildMatchMemberExpression", "match", "allowPartial", "parts", "split", "member", "matchesPattern"], "sources": ["../../src/validators/buildMatchMemberExpression.ts"], "sourcesContent": ["import matchesPattern from \"./matchesPattern\";\nimport type * as t from \"..\";\n\n/**\n * Build a function that when called will return whether or not the\n * input `node` `MemberExpression` matches the input `match`.\n *\n * For example, given the match `React.createClass` it would match the\n * parsed nodes of `React.createClass` and `React[\"createClass\"]`.\n */\nexport default function buildMatchMemberExpression(\n  match: string,\n  allowPartial?: boolean,\n) {\n  const parts = match.split(\".\");\n\n  return (member: t.Node) => matchesPattern(member, parts, allowPartial);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,eAAA,GAAAC,OAAA;AAUe,SAASC,0BAA0BA,CAChDC,KAAa,EACbC,YAAsB,EACtB;EACA,MAAMC,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC;EAE9B,OAAQC,MAAc,IAAK,IAAAC,uBAAc,EAACD,MAAM,EAAEF,KAAK,EAAED,YAAY,CAAC;AACxE"}