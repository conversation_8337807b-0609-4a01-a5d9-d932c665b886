import { appmaker, emitEvent } from '@appmaker-xyz/core';
import { useState, useEffect } from 'react';
import { usePluginStore } from '@appmaker-xyz/core';
import { useTranslation } from 'react-i18next';
import { currencyHelper, stripHtmlTags } from '../helper';
import {
  calculateLineItemDiscountAmout,
  calculateLineItemSavings,
} from '../helper/cart';
import { getExtensionConfig, usePageState } from '@appmaker-xyz/core';
import { getCurrency } from '../hooks';
import { CurrentCartQuery } from '../datasource/api-react-query';
import { isCartApiEnabled, convertLineItems  } from '../helper/helper';
import { useCurrentCart } from './cart/useCurrentCart';
type Cart = CurrentCartQuery['cart'];
type Lines = NonNullable<Cart>['lines']['nodes'];
type LineItem = NonNullable<Lines>[number];

function getLineItemSavings(
  lineItem,
  includeComparePrice = true,
  display_decimals_in_cart_page,
) {
  let discountAmount = 0;
  discountAmount =
    discountAmount + calculateLineItemSavings(lineItem, includeComparePrice);
  return display_decimals_in_cart_page
    ? parseFloat(discountAmount).toFixed(2)
    : parseFloat(discountAmount).toFixed(0);
}

function getAttributesLegacy({ attributes = {}, blockData }) {
  const lineItem = blockData;
  let newAttributes = {
    title: lineItem?.node?.title,
    featureImg: lineItem?.node?.variant?.image?.url,
    variantTitle: lineItem?.node?.variant?.title,
    // product: lineItem?.node?.variant?.product,
    product: lineItem?.node,
    variant: lineItem?.node?.variant,
    lineItem,
    unitRegularPrice: currencyHelper(
      lineItem?.node?.variant?.compareAtPrice?.amount,
      lineItem?.node?.variant?.compareAtPrice?.currencyCode,
    ),
    regularPrice: currencyHelper(
      parseFloat(lineItem?.node?.quantity) *
      parseFloat(lineItem?.node?.variant?.compareAtPrice?.amount),
      lineItem?.node?.variant?.compareAtPrice?.currencyCode,
    ),
    salePrice: currencyHelper(
      parseFloat(lineItem?.node?.quantity) *
        parseFloat(lineItem?.node?.variant?.price?.amount),
      lineItem?.node?.variant?.price?.currencyCode,
    ),
    discountAllocations: lineItem?.node?.discountAllocations,
    unitSalePrice: currencyHelper(
      lineItem?.node?.variant?.price?.amount,
      lineItem?.node?.variant?.price?.currencyCode,
    ),
    currency: getCurrency(
      lineItem?.node?.estimatedCost?.totalAmount?.currencyCode,
    ),
    quantity: lineItem?.node?.quantity,
    onSale:
      parseFloat(lineItem?.node?.variant?.price?.amount) <
      parseFloat(lineItem?.node?.variant?.compareAtPrice?.amount),
  };

  if (newAttributes.variantTitle === 'Default Title') {
    newAttributes.variantTitle = '';
  }
  return {
    ...newAttributes,
  };
}
function getAttributes({ attributes = {}, blockData }: {
  attributes: any,
  blockData: LineItem
}) {

  const lineItem = blockData;
  const variant = lineItem?.merchandise;
  let newAttributes = {
    title: lineItem?.merchandise.product?.title,
    featureImg: variant.image?.thumbnail,
    variantTitle: variant.title,
    // product: lineItem?.node?.variant?.product,
    product: lineItem?.merchandise.product,
    variant: lineItem?.merchandise,
    lineItem,
    unitRegularPrice: currencyHelper(
      // lineItem?.node?.variant?.compareAtPrice?.amount,
      lineItem?.estimatedCost?.compareAtAmount?.amount,
      lineItem?.estimatedCost?.compareAtAmount?.currencyCode,
      // lineItem?.node?.variant?.compareAtPrice?.currencyCode,
    ),
    regularPrice: currencyHelper(
      // lineItem?.node?.variant?.compareAtPrice?.amount,
      lineItem.quantity *
      parseFloat(lineItem?.estimatedCost?.compareAtAmount?.amount),
      // lineItem?.node?.variant?.compareAtPrice?.currencyCode,
      lineItem?.estimatedCost?.compareAtAmount?.currencyCode,
    ),
    salePrice: currencyHelper(
      parseFloat(lineItem?.estimatedCost?.totalAmount?.amount),
      lineItem?.estimatedCost?.amount?.currencyCode,
    ),
    discountAllocations: lineItem?.discountAllocations,
    unitSalePrice: currencyHelper(
      lineItem?.estimatedCost?.amount?.amount,
      lineItem?.estimatedCost?.amount?.currencyCode,
    ),
    currency: getCurrency(
      lineItem?.estimatedCost?.totalAmount?.currencyCode,
    ),
    quantity: lineItem.quantity,
    onSale:
    parseFloat(lineItem?.estimatedCost?.amount?.amount) <
    parseFloat(lineItem?.estimatedCost?.compareAtAmount?.amount),
  };

  // blockItem.node.variant.title == "Default Title" ? "" : blockItem.node.variant.title
  if (newAttributes.variantTitle === 'Default Title') {
    newAttributes.variantTitle = '';
  }

  // };
  // }
  return {
    // ...attributes,
    ...newAttributes,
  };
}
export function useCartProduct(props) {
  const { onAction, pageDispatch, blockData } = props;
  const isCartApi = isCartApiEnabled();
  const [quantityLoading, setQuantityLoading] = useState(false);
  const [removeLoading, setRemoveLoading] = useState(false);
  const [variantUpdateLoading, setVariantUpdateLoading] = useState(false);
  let getData = isCartApi ?  getAttributes : getAttributesLegacy;
  const {
    title,
    featureImg,
    variantTitle,
    product,
    regularPrice,
    salePrice,
    onSale = false,
    discountAllocations,
    unitRegularPrice,
    unitSalePrice,
    currency,
    quantity,
    lineItem,
    __appmakerCustomStyles,
    variant,
    specialText,
    ...others
  } = getData({ attributes: props.attributes, blockData });
  const display_decimals_in_cart_page = usePluginStore(
    (state) => state?.plugins?.shopify?.settings?.display_decimals_in_cart_page,
  );
  const maxQuantity = appmaker.applyFilters('max-quantity-cart', 1000, {
    lineItem,
  });
  const currentCartLegacy = usePageState((state) => state?.blockData);

  const currentCart = useCurrentCart();
  const cartLimitErrorMessages = appmaker.applyFilters(
    'cart-limit-line-item-validate-messages',
    [],
    {
      lineItem: isCartApi ? convertLineItems(lineItem) : lineItem,
      currentCart: isCartApi ? currentCart : currentCartLegacy,
    },
  );
  const maxFreeGiftPrice = getExtensionConfig(
    'shopify',
    'max_price_for_free_gift',
    0,
  );
  const toFixedValue = getExtensionConfig(
    'shopify',
    'free_gift_price_to_fixed_value',
    0,
  );
  let isFreeGift = false;
  let isLineItemFree = false;
  // console.log(currencyHelper(123, 'INR'));
  const { t } = useTranslation();
  // console.log(discountAmount,'````');

  const updateLineItemVariant = async ({
    variantId,
    customAttributes,
  }: {
    variantId: string;
    customAttributes?: any;
  }) => {
    try {
      const action = {
        action: 'MANAGE_CART',
        params: {
          updateCartPageStateRequired: true,
          lineItemsToUpdate: [
            {
              id: isCartApi ? lineItem?.id : lineItem?.node?.id,
              variantId: variantId,
              ...(customAttributes && { customAttributes }),
            },
          ],
        },
      };
      setVariantUpdateLoading(true);
      const cartResp = await onAction(action);
      return cartResp;
    } catch (e) {
      console.log(e, 'error');
    } finally {
      setVariantUpdateLoading(false);
    }
  };

  const onAddtoCart = async ({ _quantity }) => {
    // console.log('onAddtoCart', _quantity);
    try {
      const updateAction = {
        action: 'UPDATE_CART_V2',
        params: {
          lineItemId: isCartApi ? lineItem.id : lineItem?.node.id,
          product: isCartApi ? lineItem?.merchandise?.product : lineItem?.node?.variant?.product,
          variant: isCartApi ? lineItem?.merchandise : lineItem?.node?.variant,
          quantity: _quantity,
          updateCartPageStateRequired: true,
          // product: data,
        },
      };
      const cartResp = await onAction(updateAction);
    } catch (e) {
      console.log(e, 'error');
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: t('Something went wrong, please try again'),
        },
      });
    }
  };
  const openProduct = ({ extraParams } = {}) => {
    if (
      (isCartApi && lineItem?.merchandise?.product?.id) ||
      lineItem?.node?.variant?.product?.id
    ) {
      const openProductActionParams = {
        action: 'OPEN_PRODUCT',
        productId: isCartApi
          ? lineItem?.merchandise?.product?.id
          : lineItem?.node?.variant?.product?.id,
        params: {
          ...(extraParams && { extraParams }),
        },
      };
      onAction && onAction(openProductActionParams);
    }
  };

  const onQuantityChange = async (_quantity) => {
    // console.log('onQuantityChange', _quantity)
    if (quantityLoading) {
      return;
    }
    setQuantityLoading(true);
    await onAddtoCart({ _quantity: _quantity });
    setQuantityLoading(false);
  };
  const showDiscountTitle = appmaker.applyFilters(
    'condition-for-script-title-showing-in-cart',
    true,
  );
  let discountTitle = [];
  let lineItemDiscountCodes = [];
  if (Array.isArray(discountAllocations)) {
    if (showDiscountTitle == true) {
      discountTitle = discountAllocations.map((item) => {
        return isCartApi ? item?.title : item?.discountApplication?.title;
      });
      // filter non empty values
      discountTitle = discountTitle.filter((v) => v);
    }
    lineItemDiscountCodes = discountAllocations.map((item) => {
      return item?.discountApplication?.code;
    });
    // filter non empty values
    lineItemDiscountCodes = lineItemDiscountCodes.filter((v) => v);
  }
  const onRemoveItem = async () => {
    emitEvent('removeFromCart', {
      currency: product?.estimatedCost?.totalAmount?.currencyCode,
      title,
      price: product?.estimatedCost?.totalAmount?.amount,
      id: product.id,
      variant: variant,
    });
    setRemoveLoading(true);
    await onAddtoCart({ _quantity: 0 });
    setRemoveLoading(false);
  };
  const includeComparePrice = appmaker.applyFilters(
    'shopify-cart-include-compare-price-for-savings',
    true,
  );
  const itemSavingsAmount = getLineItemSavings(
    lineItem,
    includeComparePrice,
    display_decimals_in_cart_page,
  );
  const variantLineItemRegularPrice = onSale
    ? variant?.compareAtPrice?.amount
    : variant?.price?.amount * quantity;
  const variantLineItemSalePrice = variant?.price?.amount * quantity;
  const lineItemDiscount = calculateLineItemDiscountAmout(lineItem);
  let _lineItemSubTotalPrice =
    (parseFloat(isCartApi ? lineItem?.estimatedCost?.amount?.amount : variant?.price?.amount) * quantity) - lineItemDiscount;
  let lineItemSubTotalPrice = display_decimals_in_cart_page
    ? _lineItemSubTotalPrice.toFixed(2)
    : _lineItemSubTotalPrice.toFixed(parseInt(toFixedValue, 10));
  isFreeGift =
    _lineItemSubTotalPrice < parseInt(maxFreeGiftPrice, 10) ||
    _lineItemSubTotalPrice === 0;
  isLineItemFree =
    _lineItemSubTotalPrice < parseInt(maxFreeGiftPrice, 10) ||
    _lineItemSubTotalPrice === 0;
  if (!isCartApi && product?.customAttributes?.length > 0) {
    isFreeGift =
      product.customAttributes?.find(
        (x) =>
          (x?.key === 'appmaker_free_gift' ||
            x?.key === '_appmaker_free_gift') &&
          x?.value === 'Free Gift',
      ) && isFreeGift;
  }
  if(isCartApi && lineItem?.attributes?.length > 0) {
    isFreeGift = lineItem?.attributes?.find(
      (x) =>
        (x?.key === 'appmaker_free_gift' ||
          x?.key === '_appmaker_free_gift') &&
        x?.value === 'Free Gift',
    ) && isFreeGift;
  }
  const canRemoveItem = appmaker.applyFilters(
    'shopify-cart-can-remove-item',
    true,
    {
      lineItem: isCartApi ? lineItem : lineItem?.node,
    },
  );
  const canUpdateQuantity = appmaker.applyFilters(
    'shopify-cart-can-update-line-item',
    true,
    {
      lineItem: isCartApi ? lineItem : lineItem?.node,
    },
  );
  const singleLineItemRegularPrice =
    parseFloat(isCartApi ? lineItem?.estimatedCost?.compareAtAmount?.amount : variant?.compareAtPrice?.amount) ||
    parseFloat(isCartApi ? lineItem?.estimatedCost?.amount?.amount : variant?.price?.amount);
  const lineItemRegularPrice = singleLineItemRegularPrice * quantity;
  const lineItemRegularPriceWithCurrency = !isNaN(lineItemRegularPrice)
    ? currencyHelper(lineItemRegularPrice, currency)
    : '';
  const lineItemSubTotalPriceWithCurrency = currencyHelper(
    lineItemSubTotalPrice,
    currency,
  );
  const hasDiscount = lineItemDiscount > 0;
  const hasSavings = itemSavingsAmount > 0;
  const increaseQuantity = (_step = 1) => {
    if (quantity + _step <= maxQuantity) {
      onQuantityChange(quantity + _step);
    } else if (quantity + _step > maxQuantity) {
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'The maximum quantity for this product has been reached',
        },
      });
    }
  };
  const quantityAvailable = variant?.quantityAvailable;

  return {
    lineItemErrorMessages: cartLimitErrorMessages,
    openProduct,
    title,
    isFreeGift,
    imageUri: featureImg,
    variationText: variantTitle,
    salePrice: stripHtmlTags(salePrice),
    regularPrice: stripHtmlTags(regularPrice),
    unitRegularPrice,
    unitSalePrice,
    onRemoveItem,
    removeLoading,
    onSale,
    quantity,
    __appmakerCustomStyles: __appmakerCustomStyles?.stepper,
    onQuantityChange,
    quantityLoading,
    itemSavingsAmount,
    itemSavingsWithCurrency: currencyHelper(itemSavingsAmount, currency),
    regularPriceWithCurrency: currencyHelper(regularPrice, currency),
    variantLineItemRegularPriceWithCurrency: currencyHelper(
      variantLineItemRegularPrice,
      currency,
    ),
    variantLineItemSalePriceWithCurrency: currencyHelper(
      variantLineItemSalePrice,
      currency,
    ),
    discountTitle: discountTitle.length > 0 ? discountTitle.join('\n') : null,
    currencyHelper,
    variantTitle,
    lineItemTotalPrice: lineItemSubTotalPrice,
    variant,
    increaseQuantity,
    decreaseQuantity: (_step = 1) => onQuantityChange(quantity - _step),
    canRemoveItem,
    canUpdateQuantity,
    lineItemRegularPrice,
    lineItemRegularPriceWithCurrency,
    lineItemSubTotalPriceWithCurrency,
    hasDiscount,
    hasSavings,
    lineItemDiscountCodes,
    isLineItemFree,
    sku: variant?.sku,
    customAttributes: lineItem.attributes,
    maxQuantity,
    isMaxQuantityReached: quantity === maxQuantity,
    lineItem,
    updateLineItemVariant,
    variantUpdateLoading,
    quantityAvailable,
  };
}