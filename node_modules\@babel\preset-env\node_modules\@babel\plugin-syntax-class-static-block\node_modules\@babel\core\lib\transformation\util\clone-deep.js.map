{"version": 3, "names": ["deepClone", "value", "cache", "has", "get", "cloned", "Array", "isArray", "length", "set", "i", "keys", "Object", "key", "_default", "Map"], "sources": ["../../../src/transformation/util/clone-deep.ts"], "sourcesContent": ["//https://github.com/babel/babel/pull/14583#discussion_r882828856\nfunction deepClone(value: any, cache: Map<any, any>): any {\n  if (value !== null) {\n    if (cache.has(value)) return cache.get(value);\n    let cloned: any;\n    if (Array.isArray(value)) {\n      cloned = new Array(value.length);\n      cache.set(value, cloned);\n      for (let i = 0; i < value.length; i++) {\n        cloned[i] =\n          typeof value[i] !== \"object\" ? value[i] : deepClone(value[i], cache);\n      }\n    } else {\n      cloned = {};\n      cache.set(value, cloned);\n      const keys = Object.keys(value);\n      for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        cloned[key] =\n          typeof value[key] !== \"object\"\n            ? value[key]\n            : deepClone(value[key], cache);\n      }\n    }\n    return cloned;\n  }\n  return value;\n}\n\nexport default function <T>(value: T): T {\n  if (typeof value !== \"object\") return value;\n  return deepClone(value, new Map());\n}\n"], "mappings": ";;;;;;AACA,SAASA,SAASA,CAACC,KAAU,EAAEC,KAAoB,EAAO;EACxD,IAAID,KAAK,KAAK,IAAI,EAAE;IAClB,IAAIC,KAAK,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE,OAAOC,KAAK,CAACE,GAAG,CAACH,KAAK,CAAC;IAC7C,IAAII,MAAW;IACf,IAAIC,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,EAAE;MACxBI,MAAM,GAAG,IAAIC,KAAK,CAACL,KAAK,CAACO,MAAM,CAAC;MAChCN,KAAK,CAACO,GAAG,CAACR,KAAK,EAAEI,MAAM,CAAC;MACxB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAACO,MAAM,EAAEE,CAAC,EAAE,EAAE;QACrCL,MAAM,CAACK,CAAC,CAAC,GACP,OAAOT,KAAK,CAACS,CAAC,CAAC,KAAK,QAAQ,GAAGT,KAAK,CAACS,CAAC,CAAC,GAAGV,SAAS,CAACC,KAAK,CAACS,CAAC,CAAC,EAAER,KAAK,CAAC;MACxE;IACF,CAAC,MAAM;MACLG,MAAM,GAAG,CAAC,CAAC;MACXH,KAAK,CAACO,GAAG,CAACR,KAAK,EAAEI,MAAM,CAAC;MACxB,MAAMM,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACV,KAAK,CAAC;MAC/B,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,IAAI,CAACH,MAAM,EAAEE,CAAC,EAAE,EAAE;QACpC,MAAMG,GAAG,GAAGF,IAAI,CAACD,CAAC,CAAC;QACnBL,MAAM,CAACQ,GAAG,CAAC,GACT,OAAOZ,KAAK,CAACY,GAAG,CAAC,KAAK,QAAQ,GAC1BZ,KAAK,CAACY,GAAG,CAAC,GACVb,SAAS,CAACC,KAAK,CAACY,GAAG,CAAC,EAAEX,KAAK,CAAC;MACpC;IACF;IACA,OAAOG,MAAM;EACf;EACA,OAAOJ,KAAK;AACd;AAEe,SAAAa,SAAab,KAAQ,EAAK;EACvC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;EAC3C,OAAOD,SAAS,CAACC,KAAK,EAAE,IAAIc,GAAG,EAAE,CAAC;AACpC;AAAC"}