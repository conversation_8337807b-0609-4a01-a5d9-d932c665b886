# GraphQL Injections System

This system allows you to extend GraphQL operations in your AppMaker application without modifying the core operations. It automatically scans for extended GraphQL operations, merges them with the core operations, and generates the TypeScript types.

## How It Works

1. You create GraphQL fragments or operations in specific directories (see below)
2. The script scans these directories and generates a configuration file
3. The codegen process merges your extended operations with core operations
4. TypeScript types are generated for the merged operations

## Directory Structure

Extended GraphQL operations should be placed in an `extended-graphql` directory within your package:

```
packages-dev/your-theme/
├── extended-graphql/
│   ├── fragments/
│   │   ├── cart/
│   │   │   └── extendedCartResponse.gql
│   ├── queries/
│   │   └── yourExtendedQuery.gql
│   └── mutations/
│       └── yourExtendedMutation.gql
```

## Usage

### Running the Script

```bash
# Generate config and run codegen
node scripts/gql-injections/index.js

# Generate config only
node scripts/gql-injections/index.js --config-only

# Specify a custom config path
node scripts/gql-injections/index.js --config-path path/to/config.json
```

### Example: Extending a Cart Fragment

Here's an example of extending the `cartResponse` fragment to include custom metafields:

```graphql
# In packages-dev/your-theme/extended-graphql/fragments/cart/extendedCartResponse.gql
fragment cartResponse on Cart {
  lines(first: 250) {
    nodes {
      merchandise {
        ... on ProductVariant {
          quantityAvailable
          product {
            custom_category: metafield(key: "category", namespace: "custom") {
              value
            }
            custom_pet_type: metafield(key: "pet_type", namespace: "custom") {
              value
            }
          }
        }
      }
    }
  }
}
```

> **Note:** The fragment name must match exactly the name of the fragment you want to extend.

### Migration from Programmatic Extensions

This system replaces the previous approach of programmatically adding fields. For example, instead of using:

```javascript
fieldsHelper.addFieldsToCartLineItem({
  fields: {
    variant: {
      quantityAvailable: true,
      product: {
        custom_category: {
          __aliasFor: 'metafield',
          __args: {
            key: 'category',
            namespace: 'custom',
          },
          value: true,
        },
        // More fields...
      },
    },
  },
});
```

You now create a GraphQL fragment file as shown in the example above.

## Supported Paths

The script scans the following paths for extended GraphQL operations:

- `packages/**/extended-graphql/**/*.gql`
- `packages-dev/**/extended-graphql/**/*.gql`
- `node_modules/@appmaker-packages/**/extended-graphql/**/*.gql`

## How It's Generated

1. The script scans for extended GraphQL operations
2. It generates a configuration file at `appmaker/gql-codegen.json`
3. It creates temporary merged operation files
4. GraphQL codegen runs to generate TypeScript types

## Troubleshooting

### No Extended GraphQL Paths Found

If you see a warning about no extended GraphQL paths found, check that:

1. Your files are in an `extended-graphql` directory
2. Your files have the `.gql` extension
3. The directory structure matches one of the supported paths

### Operation Names Don't Match

Make sure the operation names in your extended files match exactly the names of the operations you want to extend.

### Type Generation Errors

If you encounter errors during type generation, check:

1. Your GraphQL syntax is valid
2. The fields you're trying to access exist in the schema
3. You're not trying to extend operations that don't exist in the core 