import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, I18nManager } from 'react-native';
import { emitEvent, analytics } from '@appmaker-xyz/core';
import {
  Layout,
  AppmakerText,
  AppTouchable,
  AppImage,
} from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { testProps } from '@appmaker-xyz/core';
import Badge from './Badge';

const getAnalyticsContentType = (action) => {
  switch (action.type) {
    case 'OPEN_COLLECTION':
      return {
        content_type: 'collection',
        content_id:
          action?.collectionId ||
          action.params?.collectionHandle ||
          action.params?.value,
      };
    case 'OPEN_PRODUCT':
      return {
        content_type: 'product',
        content_id: action?.productId || action.params?.productId,
      };
    case 'OPEN_IN_APP_PAGE':
    case 'OPEN_INAPP_PAGE':
      return {
        content_type: 'page',
        content_id: action.params.pageId || action.params.id,
      };
    case 'OPEN_IN_WEBVIEW':
      return {
        content_type: 'webview',
        content_id: action.url || action.params.title,
      };
    default:
      return {
        content_type: 'page',
      };
  }
};
const DrawerItem = ({
  clientId,
  title,
  featureImg,
  appmakerAction,
  type,
  nodes,
  badge,
  badgeText = 'Badge',
  badgeColor = '#212121',
  badgeTextColor = '#ffffff',
  dotIndicator,
  dotIndicatorColor = '#BC002D',
  dotSize = 10,
  fontColor = '#212121',
  onPressHandle,
}) => {
  const { color, spacing, fonts, font } = useApThemeState();
  const styles = allStyles({
    color,
    spacing,
    fonts,
    font,
  });
  return (
    <AppTouchable
      onPress={() => {
        if (appmakerAction?.type === 'OPEN_COLLECTION' && !nodes) {
          emitEvent('drawerCategoryClick', {
            title,
            action: 'OPEN_COLLECTION',
          });
          analytics.track('drawerCategoryClick', {
            title,
            action: 'OPEN_COLLECTION',
          });
        }
        // emitEvent('drawerMenuClick', {});
        const finalTitle =
          appmakerAction?.params?.title || appmakerAction?.title || title;
        onPressHandle(appmakerAction, nodes, finalTitle, type);
      }}
      clientId={clientId}>
      <View style={[styles.container, type === 'title' && styles.topBorder]}>
        <Layout style={styles.titleContainer}>
          {featureImg ? (
            <Layout style={styles.leftImage}>
              <AppImage
                uri={featureImg}
                style={styles.image}
                resizeMode="contain"
              />
            </Layout>
          ) : null}
          <Layout style={styles.titleContainer}>
            <AppmakerText
              {...testProps(`${clientId}-title`)}
              category={'bodyParagraph'}
              status={type === 'title' ? 'grey' : 'dark'}
              numberOfLines={1}
              style={{ flexWrap: 'wrap', color: fontColor }}>
              {title}
            </AppmakerText>
            {badge ? (
              <Badge
                text={badgeText}
                customStyles={{
                  containerStyle: {
                    backgroundColor: badgeColor,
                    marginLeft: 4,
                  },
                  textStyle: {
                    color: badgeTextColor,
                  },
                }}
              />
            ) : null}
            {dotIndicator ? (
              <Layout
                style={[
                  styles.dotIndicator,
                  {
                    width: dotSize,
                    height: dotSize,
                    backgroundColor: dotIndicatorColor,
                  },
                ]}
              />
            ) : null}
          </Layout>
        </Layout>
        {type !== 'title' && nodes?.length != 0 && (
          <Icon
            name={`chevron-${I18nManager.isRTL ? 'left' : 'right'}`}
            size={font.size.lg}
            color={color.dark}
          />
        )}
      </View>
    </AppTouchable>
  );
};

const DrawerMenu = ({ attributes, onPress, onAction, clientId }) => {
  let dataSet = {};
  let { items } = attributes;
  if (attributes?.items?.blocks) {
    dataSet = attributes.items.blocks;
    items = dataSet
      ? dataSet[0]?.data
        ? dataSet[0]?.data
        : dataSet
      : undefined;
  }
  const [contents, setContents] = useState(items);
  const [stack, setStack] = useState([]); // stack structure used for the navigation
  const [loading, setLoading] = useState(true);
  const { color, spacing, fonts, font } = useApThemeState();
  const styles = allStyles({
    color,
    spacing,
    fonts,
    font,
  });
  const resetItems = () => {
    // for reseting navigation of menu items to initial.
    setContents(items);
    setStack([{ nodes: items, title: '' }]);
  };
  useEffect(() => {
    if (items) {
      setLoading(false);
      resetItems();
    } // used because there is a slight delay in loading data from props. ie, items is undefined at first
  }, [items]);
  const onPressHandle = (action, nodes, title, type) => {
    if (nodes && nodes.length > 0) {
      stack.push({ nodes, title });
      setContents(stack[stack.length - 1].nodes);
    } else if (type !== 'title') {
      if (!action?.params?.title || !action?.params?.label) {
        action.params.title = title;
        action.params.label = title;
      }
      if (action.type === 'OPEN_IN_APP_PAGE' && action.params.id === 'home') {
        onAction({ action: 'OPEN_HOME', params: { replacePage: true } });
      } else {
        analytics.track(
          'select_menu_content',
          {
            ...getAnalyticsContentType(action),
            title: action.params.title || action.params.label,
          },
          {
            action,
          },
        );
        onAction(action);
      }
      onAction({ action: 'TOGGLE_DRAWER' });
      resetItems();
    }
  };

  if (loading) {
    return <Layout loading={true} />;
  }
  return (
    <FlatList
      data={contents}
      ListHeaderComponent={() => {
        if (stack[stack.length - 1].title) {
          return (
            <AppTouchable
              onPress={() => {
                stack.pop();
                setContents(stack[stack.length - 1].nodes);
              }}>
              <Layout style={[styles.titleContainer, styles.goBackStyle]}>
                <Icon
                  name="arrow-left"
                  size={font.size.lg}
                  color={color.demiDark}
                  style={{
                    paddingHorizontal: spacing.small,
                  }}
                />
                <AppmakerText
                  category={'pageSubHeading'}
                  status={'demiDark'}
                  numberOfLines={1}
                  style={{ flexShrink: 1, flexWrap: 'wrap' }}>
                  {stack[stack.length - 1].title}
                </AppmakerText>
              </Layout>
            </AppTouchable>
          );
        } else {
          return <View />;
        }
      }}
      keyExtractor={(item, index) => index.toString()}
      renderItem={({ item, key }) => (
        <DrawerItem
          key={key}
          clientId={clientId}
          title={item.title}
          featureImg={item.icon}
          badge={item.badge}
          badgeColor={item.badgeColor}
          badgeText={item.badgeText}
          badgeTextColor={item.badgeTextColor}
          fontColor={item.fontColor}
          dotIndicator={item.dotIndicator}
          dotIndicatorColor={item.dotIndicatorColor}
          dotSize={item.dotSize}
          nodes={item.nodes}
          appmakerAction={item.action}
          type={item.type}
          onPressHandle={item.type == 'title' ? () => {} : onPressHandle}
        />
      )}
    />
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      padding: spacing.small,
      justifyContent: 'space-between',
      backgroundColor: color.white,
      alignItems: 'center',
      marginBottom: spacing.nano,
      flex: 1,
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    leftImage: {
      width: 30,
      height: 30,
      marginRight: spacing.base,
    },
    image: {
      width: '100%',
      height: '100%',
      resizeMode: 'cover',
    },
    topBorder: {
      borderTopColor: color.light,
      borderTopWidth: 1,
    },
    goBackStyle: {
      paddingVertical: spacing.base,
      marginTop: 0.5,
      marginBottom: spacing.nano,
    },
    dotIndicator: {
      borderRadius: 20,
      marginLeft: 6,
      overflow: 'hidden',
    },
  });

export default DrawerMenu;
