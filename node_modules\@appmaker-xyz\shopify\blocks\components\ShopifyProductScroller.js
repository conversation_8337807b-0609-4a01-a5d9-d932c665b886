import React from 'react';
import { I18nManager } from 'react-native';
import ProductList from './product/ProductList';
import { BlockCard } from '@appmaker-xyz/ui';

export const ShopifyProductScroller = (props) => {
  const { attributes } = props;
  const collectionId =
    attributes?.source?.params?.id || attributes?.config?.collectionId;
  const numberOfProducts = attributes?.numberOfProducts || 10;
  const layout = attributes?.layout;
  const loadAllProducts = attributes?.loadAllProducts || false;
  const backgroundColor = attributes?.backgroundColor || '#FFFFFF';

  const finalAttributes = {
    productType:
      '{{blockItem.node.variants.edges.length > 1 ? "variable" : "normal"}}',
    wishList: true,
    isInsideCartPage: !!attributes?.isInsideCartPage,
    __appmakerStylesClassName: 'productGridWidgetCustomStyles',
    ...(attributes && attributes),
  };

  return (
    <BlockCard
      {...props}
      attributes={{
        ...attributes,
        accessButton: attributes.showViewMoreButton ? attributes.ctaText : '',
        childContainerStyle: { paddingBottom: 12 },
        wholeContainerStyle: { backgroundColor: backgroundColor },
      }}>
      <ProductList
        blockAttributes={finalAttributes}
        collectionQuery={{
          id: collectionId,
        }}
        useFlatlist={true}
        horizontal={layout === 'grid' ? false : true}
        disablePagination={!loadAllProducts}
        limit={numberOfProducts}
        surface={'product-scroller'}
        referrer={{
          name: 'appmaker-shopify-product-scroller',
          type: 'product-scroller',
          title: attributes?.title,
        }}
        flashListProps={{
          style: {
            backgroundColor: backgroundColor,
          },
          showsHorizontalScrollIndicator: false,
          ...(I18nManager.isRTL && {
            maintainVisibleContentPosition: {
              minIndexForVisible: 0,
              autoscrollToTopThreshold: 0,
            },
          }),
        }}
        layoutProps={{
          loadingLayout:
            layout === 'grid' ? 'product-grid' : 'product-scroller',
        }}
        {...props}
      />
    </BlockCard>
  );
};
