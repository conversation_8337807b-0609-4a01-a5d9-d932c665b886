import React, { useState } from 'react';
import { AppmakerText, AppTouchable, Layout } from '../../../../components';
import { StyleSheet } from 'react-native';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { useApThemeState } from '../../../../theme/ThemeContext';

const FloatingButton = ({ attributes, onPress, onAction }) => {
  const {
    label,
    iconName,
    visibilityStatus = false,
    type,
    appmakerAction,
  } = attributes;
  const { color, spacing, font } = useApThemeState();
  const [visible, setVisible] = useState(visibilityStatus);
  const styles = allStyles({ color, spacing });
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  if (!visible) {
    return null;
  }
  if (type === 'iconButton') {
    return (
      <AppTouchable style={styles.iconButtonContainer} onPress={onPressHandle}>
        <Icon name={iconName} size={24} color={color.dark} />
      </AppTouchable>
    );
  }
  return (
    <Layout style={styles.container}>
      <AppTouchable style={styles.textContainer} onPress={onPress}>
        <Icon
          name={iconName}
          color={color.white}
          size={font.size.semi}
          style={{ marginEnd: spacing.mini }}
        />
        <AppmakerText category="smallButtonText" status="white">
          {label}
        </AppmakerText>
      </AppTouchable>
      <AppTouchable
        style={styles.closeButton}
        onPress={() => {
          setVisible(false);
        }}>
        <Icon name="x" color={color.white} />
      </AppTouchable>
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      position: 'absolute',
      alignSelf: 'center',
      bottom: spacing.xxxl,
      flexDirection: 'row',
      zIndex: 10,
    },
    textContainer: {
      paddingVertical: spacing.small,
      paddingStart: spacing.base,
      paddingEnd: spacing.mini,
      flexDirection: 'row',
      backgroundColor: color.primary,
      borderTopLeftRadius: spacing.lg,
      borderBottomLeftRadius: spacing.lg,
      justifyContent: 'center',
    },
    closeButton: {
      paddingVertical: spacing.small,
      paddingStart: spacing.mini,
      paddingEnd: spacing.base,
      backgroundColor: `${color.primary}E6`,
      borderTopRightRadius: spacing.lg,
      borderBottomRightRadius: spacing.lg,
      justifyContent: 'center',
    },
    iconButtonContainer: {
      position: 'absolute',
      bottom: 24,
      right: 24,
      flexDirection: 'row',
      zIndex: 100,
      backgroundColor: color.white,
      borderRadius: spacing.xl,
      overflow: 'hidden',
      elevation: 5,
      padding: spacing.md,
    },
  });

export default FloatingButton;
