import './languages';
import { addFilter } from '@appmaker-xyz/core';
import { allPages } from './pages/index';
import './tranform';
import './blocks';
import './events';
import './hooks';
import './souces';
import './filter';
import './filters/webview';
import './routes';
import * as actions from './actions';
import { registerReusableBlocks } from './register-reusable-blocks';
import { registerToolBarIcons } from './toolBarIconsv2';
import { countries, getShipsToCountries } from './data/countries';
import { currencyHelper, stripHtmlTags } from './helper/index';
import {
  hasVariantInCart,
  getCartSum,
  setCheckoutId,
} from './helper/cartHelper';
import { getShopifyConfig } from './helper/getShopifyConfig';
import ProductBadge from './components/ProductBadge';
import AddReview from './blocks/addReview/index';
import ShopifyFilterProviderWrapper from './blocks/filter/FilterProviderWrapper';
import ProductList from './blocks/components/product/ProductList';
import OrderList from './blocks/components/orders/OrderList';
import { ShopifyDataSourceConfig } from './datasource/shopify';
import SlotBlock from './blocks/SlotBlock';
import * as hooks from './hooks/all';

addFilter('appmaker-app-pages', 'local-config', (pages) => {
  return { ...allPages, ...pages };
});
addFilter('appmaker-actions', 'local-config', (oldActions) => {
  return { ...actions, ...oldActions };
});
registerReusableBlocks();
registerToolBarIcons();
export { allPages, initialPages, styles } from './pages/index';
export { default as ToolBarIcons } from './ToolBarIcons';
export { metafieldsHelper } from './helper/metafields';
export { fieldsHelper } from './helper/fields';
export { shopifyIdHelper } from './helper/shopifyIdHelper';
export { countries, getShipsToCountries };
export { currencyHelper, stripHtmlTags, getShopifyConfig };
export { productsStore } from './store/productsStore';
export { pagesStore } from './store/pagesStore';
export {
  wishlistStoreApi,
  useWishlistStore,
  useWishlistProductIds,
  useWishlistProducts,
  getWishlistProductIds,
} from './store/wishlistStore';
export { ShopifyCouponList } from './blocks/components/ShopifyCartBlocks';
export { syncCartFromId } from './actions/helper/cart';
export { ProductBadge };
export { hasVariantInCart, getCartSum };
export { AddressModalContent } from './blocks/components/Address/ShopifyAddressChooserWrapper';
export { ShopifyFilterProviderWrapper, ProductList, OrderList };
export {
  singleProductParams,
  singleProductContext,
  getCartParams,
  trackAddToCartWithoutData,
  multipleProductParams,
} from './helper/analytics';
export { NavigationAction, ShopifyPages } from './navigation';
export { getActionFromURL } from './helper/deeplink';
export { setCheckoutId };
export { ShopifyDataSourceConfig };
export * from './datasource/types';
export * from './souces/shopify';
export * from './hooks/all';
// export * from './souces/checkout/index';
export * from './datasource/api-react-query';
export {
  imageResizeHelper,
  imageResize as shopifyCdnImageResizeHelper,
} from './helper/image';
export { ShopifyNavigation } from './shopifyNavigation';
export { FragmentRegistry } from './souces/shopify/fragments';
export { logoutUserLocally } from './helper/index';
export { SlotBlock };
export { hooks };
export { richToHTML, renderRichText } from './hooks';
export * from './datasource/fetcher';
export { getMyShopifyDomainName } from './helper/shop';
export { fetchUser } from './helper/user';
export {
  clearCart,
  getCart,
  isCartApiEnabled,
  checkShopifyCartValidity,
  shopifyCartToLegacyCart,
  isShopifyCheckoutSheetEnabled,
  convertCartLineItemsToLegacyLineItems,
  migrateCheckoutToCart,
} from './helper/helper';
