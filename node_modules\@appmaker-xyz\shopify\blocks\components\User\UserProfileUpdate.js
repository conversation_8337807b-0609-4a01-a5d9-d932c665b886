import React from 'react';
import { View, TextInput, StyleSheet } from 'react-native';
import { ThemeText, Button } from '@appmaker-xyz/ui';
import { useUserProfile } from '../../../hooks/user/useUserProfile';

function Input(props) {
  return (
    <View>
      <ThemeText category="bodySubText">{props.label}</ThemeText>
      <TextInput
        placeholder={props.placeholder}
        value={props.value}
        onChangeText={props.onChangeText}
        {...props}
      />
    </View>
  );
}
const UserProfileUpdate = (props) => {
  const {
    setNewFirstName,
    setNewLastName,
    setNewEmail,
    setNewPhone,
    firstName,
    lastName,
    email,
    phone,
    isLoading,
    updateProfile,
  } = useUserProfile(props);

  return (
    <View>
      <Input
        label="First Name"
        placeholder="Enter your First Name"
        onChangeText={setNewFirstName}
        value={firstName}
        style={[styles.input, styles.inputHeight]}
      />
      <Input
        label="Last Name"
        placeholder="Enter your Last Name"
        onChangeText={setNewLastName}
        value={lastName}
        // editable={user?.email ? false : true}
        style={[styles.input, styles.inputHeight]}
      />
      <Input
        label="Email"
        placeholder="Email"
        onChangeText={setNewEmail}
        value={email}
        style={[styles.input, styles.inputHeight]}
      />
      <Input
        label="Phone"
        placeholder="Phone"
        onChangeText={setNewPhone}
        value={phone}
        style={[styles.input, styles.inputHeight]}
      />
      <Button
        onPress={updateProfile}
        isLoading={isLoading}
        status="dark"
        title="Update Profile"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
    marginTop: 2,
  },
  inputHeight: {
    height: 46,
  },
  textAreaHeight: {
    height: 100,
    textAlignVertical: 'top',
  },
  starRatingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    marginTop: 2,
  },
  starIcon: {
    marginRight: 5,
    padding: 5,
  },
});

export default UserProfileUpdate;
