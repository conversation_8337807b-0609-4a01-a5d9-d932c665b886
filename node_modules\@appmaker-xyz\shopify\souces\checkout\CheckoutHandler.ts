import { shopifyCheckout } from './index';
import {
  CheckoutEvent,
  CheckoutEventCallback,
  CheckoutCompletedEvent,
} from '@shopify/checkout-sheet-kit';
import { handleAction } from '@appmaker-xyz/react-native';
import { onEvent, addFilter, appStorageApi } from '@appmaker-xyz/core';
import { isShopifyCheckoutSheetKitPreloadEnabled } from '../../helper/helper';
enum CHECKOUT_EVENTS {
  CLOSE = 'close',
  COMPLETE = 'completed',
  PIXEL = 'pixel',
}

type checkoutHandler = {
  initialize: () => void;
  cleanup: () => void;
  handleClose: CheckoutEventCallback;
  handleComplete: CheckoutEventCallback;
};
const getRandomNumber = () => {
  return Math.floor(10000000 + Math.random() * 90000000);
};

const handleClose: CheckoutEventCallback = () => {
  // This is to force the cart to reload when the checkout is closed
  appStorageApi().setState({
    refetchCart: getRandomNumber(),
  });
  invalidateCheckoutCache();
  const shopifyCart = appStorageApi().getState().shopifyCart;
  if(!shopifyCart){
    handleAction && handleAction({ action: 'GO_TO_HOME'});
  }
};

const handleComplete: CheckoutEventCallback = (
  data: CheckoutCompletedEvent,
) => {
  handleAction &&
    handleAction({
      action: 'SET_ORDER_COMPLETE',
      params: {
        ...data,
      },
    });
};

const onPixel = (data) => {};

const preloadCheckout = async () => {
  await shopifyCheckout?.preload();
};

const invalidateCheckoutCache = async () => {
  await shopifyCheckout?.invalidate();
};

// Preload checkout when cart is opened
const cartChangeListener = () => {
  onEvent('open.cart', async () => {
    if (isShopifyCheckoutSheetKitPreloadEnabled()) {
      await preloadCheckout();
    }
  });
  addFilter('cart-after-response', 'cart-after-response', async () => {
    if (isShopifyCheckoutSheetKitPreloadEnabled()) {
      await invalidateCheckoutCache();
    }
  });
};

const checkoutHandler = {
  initialize: function () {
    shopifyCheckout.addEventListener(CHECKOUT_EVENTS.CLOSE, handleClose);
    shopifyCheckout.addEventListener(CHECKOUT_EVENTS.COMPLETE, handleComplete);
    shopifyCheckout.addEventListener(CHECKOUT_EVENTS.PIXEL, onPixel);
    cartChangeListener();
  },

  cleanup: function () {
    shopifyCheckout.removeEventListener(CHECKOUT_EVENTS.CLOSE);
    shopifyCheckout.removeEventListener(CHECKOUT_EVENTS.COMPLETE);
  },
};

export default checkoutHandler;
