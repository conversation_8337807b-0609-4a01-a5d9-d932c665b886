import { appSettings } from '@appmaker-xyz/core';
// import { colors as configColors } from '@appmaker-xyz/app-config/newConfig';

const page = {
  title: '',
  blocks: [
    {
      attributes: {
        filter: '{{pageState.filter}}',
        product: '{{blockItem}}',
      },
      isValid: true,
      innerBlocks: [],
      name: 'appmaker/ShopifyVariationListner',
      clientId: 'de9b066c-ae0d-4f7c-a3dd-123',
      dependencies: {
        pageState: ['filter'],
      },
    },
    // {
    //   name: 'appmaker/product-image',
    //   attributes: {
    //     // imageList:
    //     //   '<%let b=[];blockItem.node.images.edges.map(function(item){b.push(item.node.src)}) %><%=b%>',
    //     imageList: '{{blockItem}}',
    //     index: 0,
    //     // style: {
    //     //   width: 200,
    //     //   height: 200,
    //     //   alignSelf: 'center',
    //     //   resizeMode: 'contain',
    //     //   // marginBottom: spacing.lg,
    //     // },
    //     indexImage: '{{pageState.variant.node.image.src}}',
    //     title: '{{blockItem.node.title}}',
    //     uri: '{{blockItem.node.onlineStoreUrl}}',
    //     displayWishlist: true,
    //   },
    //   dependencies: {
    //     pageState: ['variant'],
    //   },
    // },
    {
      name: 'appmaker/product-data',
      clientId: 'product-data',
      attributes: {
        savedItemIds: '{{(pageState.metaData.savedItem.data)}}',
        in_stock: '{{pageState.variant.node.availableForSale}}',
        id: '{{blockItem.node.id}}',
        title: '{{blockItem.node.title}}',
        currency_symbol:
          '{{ blockItem.node.priceRange.maxVariantPrice.currencyCode}}',
        salePercentage:
          '<%if(pageState.variant.node.compareAtPrice&& parseFloat(pageState.variant.node.compareAtPrice.amount)>parseFloat(pageState.variant.node.price.amount)){ %><%=Math.round(100-(parseFloat(pageState.variant.node.price.amount)/parseFloat(pageState.variant.node.compareAtPrice.amount)*100))%> % Off<% }else{ %><%=""%><% }%>',
        onSale:
          '<%if(pageState.variant.node.compareAtPrice && parseFloat(pageState.variant.node.compareAtPrice.amount)>parseFloat(pageState.variant.node.price.amount)){ %><%=true%><% }%><%else{ %><%=false%><% }%>',
        salePrice:
          '{{currencyHelper(pageState.variant.node.price.amount, blockItem.node.priceRange.maxVariantPrice.currencyCode)}}',
        regular_price_value:
          '<%if(pageState.variant.node.compareAtPrice && pageState.variant.node.compareAtPrice.amount && parseFloat(pageState.variant.node.compareAtPrice.amount)>parseFloat(pageState.variant.node.price.amount)){ %><%=pageState.variant.node.compareAtPrice.amount%><% }%>',
        saved_amount:
          '<%=differenceAmountCalculator(pageState.variant.node.compareAtPrice.amount, pageState.variant.node.price.amount,blockItem.node.priceRange.maxVariantPrice.currencyCode)%>',
        price_value: '{{ pageState.variant.node.price.amount }}',
        regularPrice:
          '<%if(pageState.variant.node.compareAtPrice && pageState.variant.node.compareAtPrice.amount && parseFloat(pageState.variant.node.compareAtPrice.amount)>parseFloat(pageState.variant.node.price.amount)){ %><%=echo(currencyHelper(pageState.variant.node.compareAtPrice.amount, pageState.variant.node.compareAtPrice.currencyCode))%><% }%>',
        priceDetailModal: false,
      },
      dependencies: {
        pageState: ['variant', 'metaData'],
      },
    },
    {
      name: 'appmaker/product-counter',
      attributes: {
        counter: true,
      },
    },
    {
      name: 'appmaker/product-variation',
      attributes: {
        __appmakerAttributes: {
          emptyViewAttribute: {
            showEmptyView: false,
            message: 'No var',
          },
        },
        dataSource: {
          repeatable: 'Yes',
          attributes: {
            params: '{{blockData.node.options}}',
            transformKey: 'shopify-detail-variations',
          },
          source: 'variables',
          repeatItem: 'DataVariable',
        },
        blockTitle: '{{blockItem.name}}',
        title: '{{blockItem.name}}',
        variations: '{{blockItem.options}}',

        // variations: "{{blockItem.values}}",
        variationKey: '{{blockItem.key}}',
      },
    },
    {
      name: 'appmaker/buttons',
      clientId: 'product-detail-buttons',
      attributes: {
        showInReverseOrder: appSettings.getOptionAsBoolean(
          'display_product_detail_buttons_in_reverse_order',
        ),
        addCartButtonStyle: {
          backgroundColor: appSettings.getOption('primary_button_color'),
          borderWidth: 0,
        },
        addCartFontColor: appSettings.getOption('primary_button_text_color'),
        buyNowButtonStyle: {
          backgroundColor: appSettings.getOption('secondary_button_color'),
          borderWidth: 0,
        },
        buyNowFontColor: appSettings.getOption('secondary_button_text_color'),
        in_stock: '{{pageState.variant.node.availableForSale}}',
        addCartText: 'Add to Cart',
        // buyNowText: 'Buy',
        buyNowAction: {
          action: 'BUY_NOW',
          type: 'normal',
        },
      },
      dependencies: {
        pageState: ['variant'],
      },
    },
    // {
    //   name: 'appmaker/radio',
    //   attributes: {
    //     __display:
    //       '{{pageState.variant.node.sellingPlanAllocations.edges.length>0}}',
    //     text: 'Hello there sellingPlanAllocations',
    //     hideDefault: '{{blockItem.node.requiresSellingPlan}}',
    //     data: '{{pageState.variant.node.sellingPlanAllocations}}',
    //   },
    //   dependencies: {
    //     pageState: ['variant', 'metaData'],
    //   },
    // },
    // {
    //   clientId: 'my-account-menu',
    //   name: 'appmaker/actionbar',
    //   attributes: {
    //     // __display: '<%=containsIframe(blockItem.node.descriptionHtml)%>',
    //     containerStyle: {
    //       flex: 1,
    //     },
    //     title: 'Description',

    //     appmakerAction: {
    //       action: 'OPEN_IN_WEBVIEW',
    //       params: {
    //         attributes: {
    //           preHtml: `<meta name="viewport" content="width=device-width, initial-scale=1.0">
    //             <style type="text/css">${constants.SHOPIFY_PRODUCT_DESCRIPTION_CSS}</style>`,
    //         },
    //         html: '{{blockItem.node.descriptionHtml}}',
    //       },
    //     },
    //   },
    // },
    // {
    //   clientId: 'my-account-menu',
    //   name: 'appmaker/actionbar',
    //   attributes: {
    //     __display: '<%=containsIframe(blockItem.node.descriptionHtml)%>',
    //     containerStyle: {
    //       flex: 1,
    //     },
    //     title: 'Description',
    //     type: 'title',
    //     appmakerAction: {
    //       action: 'NO_ACTION',
    //       params: {},
    //     },
    //   },
    // },
    // {
    //   name: 'appmaker/webview',
    //   isValid: true,
    //   clientId: 'f496b61a-56c9-4862-b4a5-d5438bb530aa',
    //   attributes: {
    //     __display: '<%=containsIframe(blockItem.node.descriptionHtml)%>',
    //     url: '',
    //     autoHeight: true,
    //     source: {
    //       html: `<meta name="viewport" content="width=device-width, initial-scale=1.0"><style type="text/css">${constants.SHOPIFY_PRODUCT_DESCRIPTION_CSS}</style>{{blockItem.node.descriptionHtml}}`,
    //     },
    //   },
    // },
    // {
    //   name: 'appmaker/expandable-text-block',
    //   clientId: 'widget-description',
    //   attributes: {
    //     __display: '<%=!containsIframe(blockItem.node.descriptionHtml)%>',
    //     blockTitle: 'Description',
    //     content: '{{blockItem.node.descriptionHtml}}',
    //     accessButton: false,
    //     expanded: true,
    //     expandable: false,
    //   },
    // },
    // {
    //   name: 'appmaker/related-product-scroller',
    //   clientId: 'widget-description',
    //   attributes: {
    //     __display: true,
    //     title: 'You may also like',
    //     productId: '{{blockItem.node.id}}',
    //     accessButton: false,
    //     expanded: true,
    //     expandable: false,
    //   },
    // },
  ],
  metaDataSource: {
    savedItem: {
      source: 'savedProducts',
      attributes: {
        transformKey: 'product-list-save-items',
        methodName: 'allItems',
        params: {},
      },
    },
  },
  attributes: {
    loadingLayout: 'product',
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      // paddingHorizontal: 16,
    },
    // headerShown: false,
  },
  dataSource: {
    runByDefault: false,
    source: 'shopify',
    attributes: {
      mapping: {
        items: 'data.data',
      },
      methodName: 'product',
      params: { productId: '{{currentAction.params.productId}}' },
    },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
  },
};
export default page;
