declare module '@appmaker-xyz/shopify' {
  export interface SortReturn {
    applySort: (sortValue: string) => void;
    selectedSort: string;
  }

  export function useSortValue(): SortReturn;

  export interface SortOption {
    label: string;
    value: string;
  }

  export interface ProductSortReturn {
    sort: SortOption[] | null;
    isLoading: boolean;
  }

  export function useProductSort(): ProductSortReturn;

  export interface FilterValue {
    count?: number;
    id: string;
    input: string;
    label: string;
  }

  export type FilterType = 'LIST' | 'PRICE_RANGE';

  export interface Filter {
    id: string;
    label: string;
    type: FilterType;
    values: FilterValue[];
  }

  export type Filters = Filter[];
  export interface CollectionFilterReturn {
    availableFilters: Filters;
    applyFilters: () => void;
  }
  export function useCollectionFilter(): CollectionFilterReturn;

  // useFilterOptions
  export interface SelectedFilterValue {
    count: number;
    id: string;
    input: string;
    label: string;
  }

  export interface PriceRangeFilterValue {
    type: 'PRICE_RANGE';
    min: number;
    max: number;
  }

  export interface SelectedFilterGroup {
    [valueId: string]: SelectedFilterValue | PriceRangeFilterValue;
  }

  export interface SelectedFilters {
    [filterKey: string]: SelectedFilterGroup;
  }

  export interface FilterOptionsReturn {
    selectFilter: (
      filterKey: string,
      filterOptionKey: string,
      filterOption: FilterValue,
    ) => void;
    removeFilter: (filterKey: string, filterOptionKey: string) => void;
    clearSelectedFilters: () => void;
    selectedFilters: SelectedFilters | null;
    hasAnyFilterSelected: boolean;
  }

  export function useFilterOptions(): FilterOptionsReturn;
}
