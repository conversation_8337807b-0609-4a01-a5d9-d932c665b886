import React from 'react';
import { Platform, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { ThemeText, Layout } from '../atoms';

const Badge = ({
  color,
  iconName,
  style,
  text,
  full = false,
  customStyles,
  testId = '',
}) => {
  const containerStyles = [styles.container];
  const textStyles = [styles.text];
  if (color) {
    containerStyles.push({ backgroundColor: color });
  }
  if (style) {
    containerStyles.push(style);
  }
  if (!text) {
    return null;
  }
  if (customStyles) {
    containerStyles.push(customStyles?.containerStyle);
    textStyles.push(customStyles?.textStyle);
  }
  if (full) {
    containerStyles.push({ justifyContent: 'center' });
    textStyles.push({ width: '100%' });
  }
  return (
    <Layout style={containerStyles}>
      {iconName ? (
        <Icon name={iconName} color="#fff" size={10} style={styles.leftIcon} />
      ) : null}
      <ThemeText testId={testId} size="sm" color="#FFFFFF" style={textStyles}>
        {text?.toString().split('\\n')?.join('\n')}
      </ThemeText>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#000000',
    paddingHorizontal: 4,
    paddingBottom: 2,
    paddingTop: Platform.OS === 'ios' ? 4 : 1,
    borderRadius: 3,
    flexDirection: 'row',
    alignItems: 'center',
    textAlign: 'center',
  },
  leftIcon: {
    marginRight: 4,
  },
  text: {
    textAlign: 'center',
  },
});

export default Badge;
export function BadgeBlock({ attributes, ...props }) {
  return (
    <Badge
      text={attributes.text}
      status={attributes.status}
      iconName={attributes.iconName}
    />
  );
}
