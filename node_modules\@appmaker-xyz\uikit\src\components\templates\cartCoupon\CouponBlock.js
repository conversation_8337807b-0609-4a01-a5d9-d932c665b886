import { toUpper } from 'lodash';
import React from 'react';
import { StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { Layout, AppmakerText, Button } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';

const CouponBlock = ({ attributes, onPress, onAction }) => {
  const {
    appmakerAction,
    couponCode,
    description,
    amount,
    free_shipping,
    min_amount,
    max_amount,
    expiry_date,
    discount_type,
    usage_limit_per_user,
  } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  // const coupon = {
  //   coupon: 'appmaker',
  //   amount: '45',
  //   free_shipping: true,
  //   min_amount: '20',
  //   max_amount: '',
  //   expiry_date: {
  //     date: '2022-03-24 00:00:00.000000',
  //     timezone_type: 1,
  //     timezone: '+00:00',
  //   },
  //   discount_type: 'fixed_cart',
  //   description: '',
  //   usage_limit_per_user: 0,
  // };

  return (
    <Layout style={styles.container}>
      <Layout style={styles.couponContainer}>
        <Layout style={styles.coupon}>
          <Icon
            name="tag"
            size={18}
            color={color.primary}
            style={styles.icon}
          />
          <AppmakerText category="h1Heading" status="primary">
            {toUpper(couponCode)}
          </AppmakerText>
        </Layout>
        <Button onPress={() => onPressHandle()} link status="primary">
          Apply
        </Button>
      </Layout>
      <Layout style={styles.mainText}>
        {amount ? (
          <AppmakerText style={styles.mb}>Get {amount} discount</AppmakerText>
        ) : null}
        {description ? (
          <AppmakerText style={styles.mb}>{description}</AppmakerText>
        ) : null}
        {min_amount ? (
          <AppmakerText style={styles.mb}>
            Minimum Spend {min_amount}
          </AppmakerText>
        ) : null}
        {max_amount ? (
          <AppmakerText style={styles.mb}>
            Maximum Discount {max_amount}
          </AppmakerText>
        ) : null}
      </Layout>
      <Layout
        style={expiry_date || free_shipping ? styles.extraContents : null}>
        {expiry_date ? (
          <AppmakerText category="bodySubText" status="demiDark">
            Valid until {expiry_date.date}
          </AppmakerText>
        ) : null}
        {free_shipping ? (
          <Layout style={styles.shipping}>
            <Icon name="truck" color={color.success} style={styles.icon} />
            <AppmakerText status="success" category="bodySubText">
              Free Shipping
            </AppmakerText>
          </Layout>
        ) : null}
      </Layout>
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      marginTop: spacing.small,
      //   borderColor: color.grey,
      //   borderWidth: 1,
      backgroundColor: `${color.primary}0F`,
      borderRadius: spacing.mini,
      padding: spacing.small,
    },
    coupon: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: `${color.primary}1b`,
      paddingHorizontal: spacing.small,
      borderRadius: spacing.nano,
    },
    couponContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: spacing.small,
    },
    mainText: {},
    extraContents: {
      paddingTop: spacing.small,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      borderTopColor: color.light,
      borderTopWidth: 1,
    },
    mb: {
      marginBottom: spacing.mini,
    },
    shipping: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    icon: {
      marginRight: spacing.mini,
    },
    tableCell: {
      backgroundColor: 'transparent',
    },
  });

export default CouponBlock;
