{"version": 3, "names": ["_getBindingIdentifiers", "require", "isBinding", "node", "parent", "grandparent", "type", "keys", "getBindingIdentifiers", "i", "length", "key", "val", "Array", "isArray", "indexOf"], "sources": ["../../src/validators/isBinding.ts"], "sourcesContent": ["import getBindingIdentifiers from \"../retrievers/getBindingIdentifiers\";\nimport type * as t from \"..\";\n/**\n * Check if the input `node` is a binding identifier.\n */\nexport default function isBinding(\n  node: t.Node,\n  parent: t.Node,\n  grandparent?: t.Node,\n): boolean {\n  if (\n    grandparent &&\n    node.type === \"Identifier\" &&\n    parent.type === \"ObjectProperty\" &&\n    grandparent.type === \"ObjectExpression\"\n  ) {\n    // We need to special-case this, because getBindingIdentifiers\n    // has an ObjectProperty->value entry for destructuring patterns.\n    return false;\n  }\n\n  const keys =\n    // @ts-expect-error getBindingIdentifiers.keys does not cover all AST types\n    getBindingIdentifiers.keys[parent.type];\n  if (keys) {\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const val =\n        // @ts-expect-error key must present in parent\n        parent[key];\n      if (Array.isArray(val)) {\n        if (val.indexOf(node) >= 0) return true;\n      } else {\n        if (val === node) return true;\n      }\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAKe,SAASC,SAASA,CAC/BC,IAAY,EACZC,MAAc,EACdC,WAAoB,EACX;EACT,IACEA,WAAW,IACXF,IAAI,CAACG,IAAI,KAAK,YAAY,IAC1BF,MAAM,CAACE,IAAI,KAAK,gBAAgB,IAChCD,WAAW,CAACC,IAAI,KAAK,kBAAkB,EACvC;IAGA,OAAO,KAAK;EACd;EAEA,MAAMC,IAAI,GAERC,8BAAqB,CAACD,IAAI,CAACH,MAAM,CAACE,IAAI,CAAC;EACzC,IAAIC,IAAI,EAAE;IACR,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,MAAME,GAAG,GAAGJ,IAAI,CAACE,CAAC,CAAC;MACnB,MAAMG,GAAG,GAEPR,MAAM,CAACO,GAAG,CAAC;MACb,IAAIE,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;QACtB,IAAIA,GAAG,CAACG,OAAO,CAACZ,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,IAAI;MACzC,CAAC,MAAM;QACL,IAAIS,GAAG,KAAKT,IAAI,EAAE,OAAO,IAAI;MAC/B;IACF;EACF;EAEA,OAAO,KAAK;AACd"}