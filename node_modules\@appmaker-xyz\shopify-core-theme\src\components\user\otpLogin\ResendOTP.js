import React, { useState, useEffect } from 'react';
import { StyleSheet } from 'react-native';
import { UiKitButton } from '@appmaker-xyz/ui';

const ResendOTP = ({ attributes, onAction, coreDispatch }, props) => {
  const { timerLimit } = attributes;
  const [time, setTime] = useState(timerLimit);
  const [disableButton, setDisableButton] = useState(true);

  useEffect(() => {
    let interval;
    if (disableButton === true) {
      interval = setInterval(() => {
        setTime((t) => {
          if (t - 1 === 0) {
            clearInterval(interval);
            setDisableButton(false);
            return timerLimit;
          }
          return t - 1;
        });
      }, 1000);
    }
    return () => interval && clearInterval(interval);
  }, [disableButton]);
  const onPress = async () => {
    coreDispatch({
      type: 'SET_VALUE',
      name: 'loadingButton',
      value: true,
    });
    onAction && (await onAction(attributes.appmakerAction));
    coreDispatch({
      type: 'SET_VALUE',
      name: 'loadingButton',
      value: false,
    });
    setDisableButton(true);
  };
  return (
    <UiKitButton
      disabled={disableButton}
      {...attributes}
      small={true}
      wholeContainerStyle={styles.recentOtpButton}
      onPress={onPress}>{`Resend OTP ${
      time !== timerLimit ? `in ${time}s` : ''
    }`}</UiKitButton>
  );
};
const styles = StyleSheet.create({
  recentOtpButton: {
    backgroundColor: 'transparent',
    marginTop: 12,
  },
});

export default ResendOTP;
