import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  Pressable,
  Keyboard,
  ActivityIndicator,
} from 'react-native';
import CustomOtpBar from './components/CustomOtpBar';
import { Button } from '../coreComponents';
import { ThemeText } from '../atoms';

const OtpInputCard = ({
  verifyCode,
  verifyErrorMessage,
  onEditNumber,
  phoneNumber,
  resendCode,
  resendLoading,
  failedAttemptCount,
  otpLength,
  type = 'mobile',
}) => {
  const [isValidOtp, setOtpValid] = useState(true);
  const [isOtpResent, setResendOtp] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [timer, setTimer] = useState(30);
  const [canResend, setCanResend] = useState(false);
  const [otpCode, setOtpCode] = useState('');
  const [enableSubmit, setEnableSubmit] = useState(false);

  useEffect(() => {
    setOtpValid(!verifyErrorMessage?.message);
  }, [verifyErrorMessage]);

  useEffect(() => {
    if (!isValidOtp && failedAttemptCount > 2) {
      onEditNumber();
    }
  }, [isValidOtp, failedAttemptCount, onEditNumber]);

  useEffect(() => {
    let countdown;
    if (isOtpResent || !canResend) {
      setCanResend(false);
      countdown = setInterval(() => {
        setTimer((prevTimer) => {
          if (prevTimer === 1) {
            clearInterval(countdown);
            setCanResend(true);
            setResendOtp(false);
            return 30;
          }
          return prevTimer - 1;
        });
      }, 1000);
    }
    return () => clearInterval(countdown);
  }, [isOtpResent, canResend]);

  const doCheckOtp = useCallback(
    (pin) => {
      return pin.trim().length === otpLength;
    },
    [otpLength],
  );

  const handleVerifyOtp = useCallback(
    async (otpCode) => {
      setIsLoading(true);
      Keyboard.dismiss();
      try {
        await verifyCode(otpCode);
        setIsLoading(false);
      } catch (err) {
        setIsLoading(false);
      }
    },
    [verifyCode],
  );

  const handleOtpChange = useCallback(
    (item) => {
      setOtpCode(item);
      const isValid = doCheckOtp(item);
      setEnableSubmit(isValid);
      if (isValid) {
        handleVerifyOtp(item);
      }
    },
    [doCheckOtp, handleVerifyOtp],
  );

  return (
    <View style={styles.container}>
      <ThemeText style={styles.textStyle1}>
        {'You will receive OTP on '}
        <ThemeText fontFamily="bold">{phoneNumber}</ThemeText>
      </ThemeText>
      <Pressable style={styles.marginV5} onPress={onEditNumber}>
        <View style={styles.titleBorder}>
          <ThemeText style={styles.textStyle3}>
            {type === 'email' ? 'Edit email address' : 'Edit phone number'}
          </ThemeText>
        </View>
      </Pressable>

      <View style={styles.viewCenterOtp}>
        {isOtpResent ? (
          <ThemeText style={styles.resendOtpTextStyle}>
            OTP has been resent
          </ThemeText>
        ) : !isValidOtp ? (
          <ThemeText style={styles.otpErrorTextStyle}>
            {verifyErrorMessage?.message}
          </ThemeText>
        ) : null}
        <View style={styles.otpInputContainer}>
          <CustomOtpBar
            showError={!isValidOtp}
            isOtpResent={isOtpResent}
            onFinalText={handleOtpChange}
            lengthInput={otpLength}
          />
        </View>
      </View>
      <Button
        activityIndicatorColor={'#fff'}
        size="default"
        title={'Submit'}
        buttonStyle={styles.submitButton}
        color={enableSubmit ? false : 'grey'}
        isLoading={isLoading}
        onPress={() => {
          if (doCheckOtp(otpCode)) {
            handleVerifyOtp(otpCode);
          }
        }}
      />
      <View style={styles.resendContainer}>
        {resendLoading === 'loading' ? (
          <ActivityIndicator
            size="small"
            color={'black'}
            style={styles.loadingIndicatorStyle}
          />
        ) : null}

        <Pressable
          onPress={() => {
            if (canResend) {
              setResendOtp(true);
              resendCode(phoneNumber);
              setOtpValid(true);
            }
          }}
          disabled={!canResend}>
          <View>
            <ThemeText style={styles.textStyle4}>
              {canResend
                ? "Didn't Receive OTP? Resend"
                : `Resend OTP in ${timer}s`}
            </ThemeText>
          </View>
        </Pressable>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingVertical: 18,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  submitButton: {
    marginVertical: 10,
  },
  textStyle1: {
    color: '#374151',
  },
  marginV5: {
    marginVertical: 2,
  },
  textStyle3: {
    color: '#374151',
    marginTop: 5,
  },
  viewCenterOtp: {
    marginTop: 30,
  },
  titleBorder: {
    borderBottomWidth: 1,
    borderColor: '#374151',
  },
  resendContainer: {
    marginTop: 10,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  loadingIndicatorStyle: {
    marginRight: 18,
  },
  textStyle4: {
    color: '#374151',
    fontSize: 14,
  },
  otpErrorTextStyle: {
    fontSize: 15,
    marginTop: 5,
    marginHorizontal: 5,
    color: '#374151',
  },
  resendOtpTextStyle: {
    fontSize: 15,
    marginTop: 5,
    textAlign: 'center',
    color: '#374151',
  },
  otpInputContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default OtpInputCard;
