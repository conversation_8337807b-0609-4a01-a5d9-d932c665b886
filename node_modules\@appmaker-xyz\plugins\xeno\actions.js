import { shopifyIdHelper } from '@appmaker-xyz/shopify';

export async function OPEN_CHUMBAK_INSIDER_PAGE(
  { action, params },
  { handleAction, pageState },
) {
  handleAction({
    pageId: 'ChumbakInsiderPage',
    params,
    action: 'OPEN_INAPP_PAGE',
  });
}

export async function OPEN_CHUMBAK_INSIDER_PAGE_CART(
  { action, params },
  { handleAction, pageState },
) {
  handleAction({
    pageId: 'ChumbakInsiderCartPage',
    params,
    openInModal: true,
    closeButton: true,
    handleAction,
    action: 'OPEN_INAPP_PAGE',
  });
}
const startsWith = (arr, str) => {
  return arr.some((item) => item.startsWith(str));
};
export async function ADD_TO_CART_XENO_PRODUCT(
  { action, variantId },
  { handleAction, coreDispatch, appState, runDataSource, appStorageState },
) {
  try {
    const { checkout, user } = appStorageState;
    // TODO get tag from plugin settings
    const isInsider = user ? startsWith(user.tags, 'Xeno Tier:Member') : false;
    if (!isInsider) {
      const dataSource = {
        attributes: {},
        source: 'shopify',
      };
      const { appId } = appState;
      let hasXenoProduct = checkout?.lineItems?.edges.some(
        (x) => x?.node?.variant?.product?.productType === 'membership',
      );
      if (hasXenoProduct) {
        const node = checkout?.lineItems?.edges.find(
          (x) => x?.node?.variant?.product?.productType === 'membership',
        )?.node;
        const deleteParams = {
          action: 'UPDATE_CART_V2',
          params: {
            product: node?.variant?.product,
            variant: node?.variant,
            quantity: 0,
            lineItemId: node?.id,
            // product: data,
          },
        };
        const cartResp = await handleAction(deleteParams, {
          handleAction,
          coreDispatch,
          appState,
          runDataSource,
          appStorageState,
        });
      }
      let params = {
        action: 'ADD_TO_CART_V2',
        params: {
          variant: { node: { id: shopifyIdHelper(variantId) } },
          quantity: 1,
          customAttributes: { key: '_membership', value: '_membership' },
        },
        dependencies: {
          appStorageState,
        },
      };
      const cartResp = await handleAction(params, {
        handleAction,
        coreDispatch,
        appState,
        runDataSource,
        appStorageState,
      });
      if (cartResp.status === 'success') {
        handleAction({
          action: 'SHOW_MESSAGE',
          params: { title: cartResp?.message || 'Cart Updated' },
        });
      } else {
        handleAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: 'Something went wrong, Please try again',
          },
        });
      }
    } else {
      handleAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'You are already an Insider Member',
        },
      });
    }
  } catch (error) {
    alert(error);
  }
}
