import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ThemeText, AppImage } from '@appmaker-xyz/ui';

const ChumakInsider = ({ attributes, onPress, pageDispatch, onAction }) => {
  return (
    <Layout style={styles.container}>
      <Layout style={styles.infoContainer}>
        <AppImage
          uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/popup_logo-removebg-preview_1024x1024.png"
          style={styles.image}
        />
        <Layout style={styles.insiderBadge}>
          <AppImage
            uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
            style={styles.insiderImage}
          />
          <ThemeText>BENEFITS</ThemeText>
        </Layout>
        <Layout style={styles.content}>
          <ThemeText fontFamily="bold">Free Priority Shipping</ThemeText>
          <ThemeText size="sm" color="#4F4F4F">
            Enjoy priority shipping, free of any additional cost on every order
          </ThemeText>
        </Layout>
        <Layout style={styles.content}>
          <ThemeText fontFamily="bold">Additional Discounts</ThemeText>
          <ThemeText size="sm" color="#4F4F4F">
            Enjoy a guaranteed 10% discount on all future purchases (over &
            above; no conditions)
          </ThemeText>
        </Layout>
        <Layout style={styles.content}>
          <ThemeText fontFamily="bold">Exclusive Access</ThemeText>
          <ThemeText size="sm" color="#4F4F4F">
            Get special access to preview all upcoming sales and events.
          </ThemeText>
        </Layout>
        <Layout style={styles.content}>
          <ThemeText fontFamily="bold">Gifts & More</ThemeText>
          <ThemeText size="sm" color="#4F4F4F">
            Get your Chumbak free gift on every 3rd purchase.
          </ThemeText>
        </Layout>
      </Layout>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#B6DCC9',
  },
  infoContainer: {
    paddingHorizontal: 12,
    paddingBottom: 16,
  },
  image: {
    width: 120,
    height: 80,
    resizeMode: 'contain',
    marginBottom: 32,
    alignSelf: 'center',
  },
  insiderBadge: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    alignSelf: 'flex-start',
    alignItems: 'center',
    padding: 6,
    marginBottom: 12,
  },
  insiderImage: {
    width: 90,
    height: 30,
    resizeMode: 'contain',
    marginRight: 6,
  },
  content: {
    marginBottom: 12,
  },
});

export default ChumakInsider;
