import { useCallback } from 'react';
import { appStorageApi } from '@appmaker-xyz/core';

export const useOrderNote = ({ onAction, fromCart = false }) => {
  const cart = appStorageApi()?.getState()?.shopifyCart;
  const note = cart?.note || '';

  const setOrderNote = useCallback(
    async (newNote) => {
      await onAction({
        action: 'ADD_ORDER_NOTE',
        params: {
          orderNote: newNote,
          shouldUpdateBlockData: fromCart,
        },
      });
    },
    [onAction],
  );

  return { note, setOrderNote };
};
