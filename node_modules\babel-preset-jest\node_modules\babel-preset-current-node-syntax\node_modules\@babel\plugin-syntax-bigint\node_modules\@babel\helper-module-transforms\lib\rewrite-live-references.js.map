{"version": 3, "names": ["_assert", "require", "_core", "_helperSimpleAccess", "assignmentExpression", "callExpression", "cloneNode", "expressionStatement", "getOuterBindingIdentifiers", "identifier", "isMemberExpression", "isVariableDeclaration", "jsxIdentifier", "jsxMemberExpression", "memberExpression", "numericLiteral", "sequenceExpression", "stringLiteral", "variableDeclaration", "variableDeclarator", "t", "isInType", "path", "parent", "type", "parentPath", "exportKind", "isStatement", "isExpression", "rewriteLiveReferences", "programPath", "metadata", "imported", "Map", "exported", "requeueInParent", "requeue", "source", "data", "localName", "importName", "imports", "set", "importsNamespace", "local", "exportMeta", "get", "push", "names", "rewriteBindingInitVisitorState", "scope", "traverse", "rewriteBindingInitVisitor", "bindingNames", "Set", "Array", "from", "keys", "simplifyAccess", "rewriteReferencesVisitorState", "seen", "WeakSet", "buildImportReference", "identNode", "meta", "referenced", "lazy", "namespace", "name", "interop", "computed", "stringSpecifiers", "has", "rewriteReferencesVisitor", "<PERSON><PERSON>", "skip", "ClassDeclaration", "id", "node", "Error", "exportNames", "length", "statement", "buildBindingExportAssignmentExpression", "_blockHoist", "insertAfter", "VariableDeclaration", "Object", "for<PERSON>ach", "localExpr", "exportsObjectName", "exportName", "currentScope", "hasOwnBinding", "rename", "reduce", "expr", "buildImportThrow", "template", "expression", "ast", "ReferencedIdentifier", "add", "importData", "buildCodeFrameError", "localBinding", "getBinding", "rootBinding", "ref", "loc", "isCallExpression", "callee", "isOptionalCallExpression", "isTaggedTemplateExpression", "tag", "replaceWith", "isJSXIdentifier", "object", "property", "UpdateExpression", "arg", "update", "isIdentifier", "exportedNames", "operator", "prefix", "generateDeclaredUidIdentifier", "AssignmentExpression", "exit", "left", "assert", "assignment", "right", "ids", "programScopeIds", "filter", "find", "items", "isExpressionStatement", "ForOfStatement|ForInStatement", "programScope", "didTransformExport", "importConstViolationName", "loopBodyScope", "ensureBlock", "bodyPath", "newLoopId", "generateUidIdentifierBasedOnNode", "registerDeclaration", "unshiftContainer"], "sources": ["../src/rewrite-live-references.ts"], "sourcesContent": ["import assert from \"assert\";\nimport { template, types as t } from \"@babel/core\";\nimport type { NodePath, Visitor, Scope } from \"@babel/traverse\";\nimport simplifyAccess from \"@babel/helper-simple-access\";\n\nimport type { ModuleMetadata } from \"./normalize-and-load-metadata.ts\";\n\nconst {\n  assignmentExpression,\n  callExpression,\n  cloneNode,\n  expressionStatement,\n  getOuterBindingIdentifiers,\n  identifier,\n  isMemberExpression,\n  isVariableDeclaration,\n  jsxIdentifier,\n  jsxMemberExpression,\n  memberExpression,\n  numericLiteral,\n  sequenceExpression,\n  stringLiteral,\n  variableDeclaration,\n  variableDeclarator,\n} = t;\n\ninterface RewriteReferencesVisitorState {\n  exported: Map<any, any>;\n  metadata: ModuleMetadata;\n  requeueInParent: (path: NodePath) => void;\n  scope: Scope;\n  imported: Map<any, any>;\n  buildImportReference: (\n    [source, importName, localName]: readonly [string, string, string],\n    identNode: t.Identifier | t.CallExpression | t.JSXIdentifier,\n  ) => any;\n  seen: WeakSet<object>;\n}\n\ninterface RewriteBindingInitVisitorState {\n  exported: Map<any, any>;\n  metadata: ModuleMetadata;\n  requeueInParent: (path: NodePath) => void;\n  scope: Scope;\n}\n\nfunction isInType(path: NodePath) {\n  do {\n    switch (path.parent.type) {\n      case \"TSTypeAnnotation\":\n      case \"TSTypeAliasDeclaration\":\n      case \"TSTypeReference\":\n      case \"TypeAnnotation\":\n      case \"TypeAlias\":\n        return true;\n      case \"ExportSpecifier\":\n        return (\n          (\n            path.parentPath.parent as\n              | t.ExportDefaultDeclaration\n              | t.ExportNamedDeclaration\n          ).exportKind === \"type\"\n        );\n      default:\n        if (path.parentPath.isStatement() || path.parentPath.isExpression()) {\n          return false;\n        }\n    }\n  } while ((path = path.parentPath));\n}\n\nexport default function rewriteLiveReferences(\n  programPath: NodePath<t.Program>,\n  metadata: ModuleMetadata,\n) {\n  const imported = new Map();\n  const exported = new Map();\n  const requeueInParent = (path: NodePath) => {\n    // Manually re-queue `exports.default =` expressions so that the ES3\n    // transform has an opportunity to convert them. Ideally this would\n    // happen automatically from the replaceWith above. See #4140 for\n    // more info.\n    programPath.requeue(path);\n  };\n\n  for (const [source, data] of metadata.source) {\n    for (const [localName, importName] of data.imports) {\n      imported.set(localName, [source, importName, null]);\n    }\n    for (const localName of data.importsNamespace) {\n      imported.set(localName, [source, null, localName]);\n    }\n  }\n\n  for (const [local, data] of metadata.local) {\n    let exportMeta = exported.get(local);\n    if (!exportMeta) {\n      exportMeta = [];\n      exported.set(local, exportMeta);\n    }\n\n    exportMeta.push(...data.names);\n  }\n\n  // Rewrite initialization of bindings to update exports.\n  const rewriteBindingInitVisitorState: RewriteBindingInitVisitorState = {\n    metadata,\n    requeueInParent,\n    scope: programPath.scope,\n    exported, // local name => exported name list\n  };\n  programPath.traverse(\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    rewriteBindingInitVisitor,\n    rewriteBindingInitVisitorState,\n  );\n\n  // NOTE(logan): The 'Array.from' calls are to make this code with in loose mode.\n  const bindingNames = new Set([\n    ...Array.from(imported.keys()),\n    ...Array.from(exported.keys()),\n  ]);\n  if (process.env.BABEL_8_BREAKING) {\n    simplifyAccess(programPath, bindingNames);\n  } else {\n    // @ts-ignore(Babel 7 vs Babel 8) The third param has been removed in Babel 8.\n    simplifyAccess(programPath, bindingNames, false);\n  }\n\n  // Rewrite reads/writes from imports and exports to have the correct behavior.\n  const rewriteReferencesVisitorState: RewriteReferencesVisitorState = {\n    seen: new WeakSet(),\n    metadata,\n    requeueInParent,\n    scope: programPath.scope,\n    imported, // local / import\n    exported, // local name => exported name list\n    buildImportReference: ([source, importName, localName], identNode) => {\n      const meta = metadata.source.get(source);\n      meta.referenced = true;\n\n      if (localName) {\n        if (meta.lazy) {\n          identNode = callExpression(\n            // @ts-expect-error Fixme: we should handle the case when identNode is a JSXIdentifier\n            identNode,\n            [],\n          );\n        }\n        return identNode;\n      }\n\n      let namespace: t.Expression = identifier(meta.name);\n      if (meta.lazy) namespace = callExpression(namespace, []);\n\n      if (importName === \"default\" && meta.interop === \"node-default\") {\n        return namespace;\n      }\n\n      const computed = metadata.stringSpecifiers.has(importName);\n\n      return memberExpression(\n        namespace,\n        computed ? stringLiteral(importName) : identifier(importName),\n        computed,\n      );\n    },\n  };\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  programPath.traverse(rewriteReferencesVisitor, rewriteReferencesVisitorState);\n}\n\n/**\n * A visitor to inject export update statements during binding initialization.\n */\nconst rewriteBindingInitVisitor: Visitor<RewriteBindingInitVisitorState> = {\n  Scope(path) {\n    path.skip();\n  },\n  ClassDeclaration(path) {\n    const { requeueInParent, exported, metadata } = this;\n\n    const { id } = path.node;\n    if (!id) throw new Error(\"Expected class to have a name\");\n    const localName = id.name;\n\n    const exportNames = exported.get(localName) || [];\n    if (exportNames.length > 0) {\n      const statement = expressionStatement(\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        buildBindingExportAssignmentExpression(\n          metadata,\n          exportNames,\n          identifier(localName),\n          path.scope,\n        ),\n      );\n      // @ts-expect-error todo(flow->ts): avoid mutations\n      statement._blockHoist = path.node._blockHoist;\n\n      requeueInParent(path.insertAfter(statement)[0]);\n    }\n  },\n  VariableDeclaration(path) {\n    const { requeueInParent, exported, metadata } = this;\n\n    Object.keys(path.getOuterBindingIdentifiers()).forEach(localName => {\n      const exportNames = exported.get(localName) || [];\n\n      if (exportNames.length > 0) {\n        const statement = expressionStatement(\n          // eslint-disable-next-line @typescript-eslint/no-use-before-define\n          buildBindingExportAssignmentExpression(\n            metadata,\n            exportNames,\n            identifier(localName),\n            path.scope,\n          ),\n        );\n        // @ts-expect-error todo(flow->ts): avoid mutations\n        statement._blockHoist = path.node._blockHoist;\n\n        requeueInParent(path.insertAfter(statement)[0]);\n      }\n    });\n  },\n};\n\nconst buildBindingExportAssignmentExpression = (\n  metadata: ModuleMetadata,\n  exportNames: string[],\n  localExpr: t.Expression,\n  scope: Scope,\n) => {\n  const exportsObjectName = metadata.exportName;\n  for (\n    let currentScope = scope;\n    currentScope != null;\n    currentScope = currentScope.parent\n  ) {\n    if (currentScope.hasOwnBinding(exportsObjectName)) {\n      currentScope.rename(exportsObjectName);\n    }\n  }\n  return (exportNames || []).reduce((expr, exportName) => {\n    // class Foo {} export { Foo, Foo as Bar };\n    // as\n    // class Foo {} exports.Foo = exports.Bar = Foo;\n    const { stringSpecifiers } = metadata;\n    const computed = stringSpecifiers.has(exportName);\n    return assignmentExpression(\n      \"=\",\n      memberExpression(\n        identifier(exportsObjectName),\n        computed ? stringLiteral(exportName) : identifier(exportName),\n        /* computed */ computed,\n      ),\n      expr,\n    );\n  }, localExpr);\n};\n\nconst buildImportThrow = (localName: string) => {\n  return template.expression.ast`\n    (function() {\n      throw new Error('\"' + '${localName}' + '\" is read-only.');\n    })()\n  `;\n};\n\nconst rewriteReferencesVisitor: Visitor<RewriteReferencesVisitorState> = {\n  ReferencedIdentifier(path) {\n    const { seen, buildImportReference, scope, imported, requeueInParent } =\n      this;\n    if (seen.has(path.node)) return;\n    seen.add(path.node);\n\n    const localName = path.node.name;\n\n    const importData = imported.get(localName);\n    if (importData) {\n      if (isInType(path)) {\n        throw path.buildCodeFrameError(\n          `Cannot transform the imported binding \"${localName}\" since it's also used in a type annotation. ` +\n            `Please strip type annotations using @babel/preset-typescript or @babel/preset-flow.`,\n        );\n      }\n\n      const localBinding = path.scope.getBinding(localName);\n      const rootBinding = scope.getBinding(localName);\n\n      // redeclared in this scope\n      if (rootBinding !== localBinding) return;\n\n      const ref = buildImportReference(importData, path.node);\n\n      // Preserve the binding location so that sourcemaps are nicer.\n      ref.loc = path.node.loc;\n\n      if (\n        (path.parentPath.isCallExpression({ callee: path.node }) ||\n          path.parentPath.isOptionalCallExpression({ callee: path.node }) ||\n          path.parentPath.isTaggedTemplateExpression({ tag: path.node })) &&\n        isMemberExpression(ref)\n      ) {\n        path.replaceWith(sequenceExpression([numericLiteral(0), ref]));\n      } else if (path.isJSXIdentifier() && isMemberExpression(ref)) {\n        const { object, property } = ref;\n        path.replaceWith(\n          jsxMemberExpression(\n            // @ts-expect-error todo(flow->ts): possible bug `object` might not have a name\n            jsxIdentifier(object.name),\n            // @ts-expect-error todo(flow->ts): possible bug `property` might not have a name\n            jsxIdentifier(property.name),\n          ),\n        );\n      } else {\n        path.replaceWith(ref);\n      }\n\n      requeueInParent(path);\n\n      // The path could have been replaced with an identifier that would\n      // otherwise be re-visited, so we skip processing its children.\n      path.skip();\n    }\n  },\n\n  UpdateExpression(path) {\n    const {\n      scope,\n      seen,\n      imported,\n      exported,\n      requeueInParent,\n      buildImportReference,\n    } = this;\n\n    if (seen.has(path.node)) return;\n\n    seen.add(path.node);\n\n    const arg = path.get(\"argument\");\n\n    // No change needed\n    if (arg.isMemberExpression()) return;\n\n    const update = path.node;\n\n    if (arg.isIdentifier()) {\n      const localName = arg.node.name;\n\n      // redeclared in this scope\n      if (scope.getBinding(localName) !== path.scope.getBinding(localName)) {\n        return;\n      }\n\n      const exportedNames = exported.get(localName);\n      const importData = imported.get(localName);\n\n      if (exportedNames?.length > 0 || importData) {\n        if (importData) {\n          path.replaceWith(\n            assignmentExpression(\n              update.operator[0] + \"=\",\n              buildImportReference(importData, arg.node),\n              buildImportThrow(localName),\n            ),\n          );\n        } else if (update.prefix) {\n          // ++foo\n          // =>   exports.foo = ++foo\n          path.replaceWith(\n            buildBindingExportAssignmentExpression(\n              this.metadata,\n              exportedNames,\n              cloneNode(update),\n              path.scope,\n            ),\n          );\n        } else {\n          // foo++\n          // =>   (ref = i++, exports.i = i, ref)\n          const ref = scope.generateDeclaredUidIdentifier(localName);\n\n          path.replaceWith(\n            sequenceExpression([\n              assignmentExpression(\"=\", cloneNode(ref), cloneNode(update)),\n              buildBindingExportAssignmentExpression(\n                this.metadata,\n                exportedNames,\n                identifier(localName),\n                path.scope,\n              ),\n              cloneNode(ref),\n            ]),\n          );\n        }\n      }\n    }\n\n    requeueInParent(path);\n    path.skip();\n  },\n\n  AssignmentExpression: {\n    exit(path) {\n      const {\n        scope,\n        seen,\n        imported,\n        exported,\n        requeueInParent,\n        buildImportReference,\n      } = this;\n\n      if (seen.has(path.node)) return;\n      seen.add(path.node);\n\n      const left = path.get(\"left\");\n\n      // No change needed\n      if (left.isMemberExpression()) return;\n\n      if (left.isIdentifier()) {\n        // Simple update-assign foo += 1; export { foo };\n        // =>   exports.foo =  (foo += 1);\n        const localName = left.node.name;\n\n        // redeclared in this scope\n        if (scope.getBinding(localName) !== path.scope.getBinding(localName)) {\n          return;\n        }\n\n        const exportedNames = exported.get(localName);\n        const importData = imported.get(localName);\n        if (exportedNames?.length > 0 || importData) {\n          assert(path.node.operator === \"=\", \"Path was not simplified\");\n\n          const assignment = path.node;\n\n          if (importData) {\n            assignment.left = buildImportReference(importData, left.node);\n\n            assignment.right = sequenceExpression([\n              assignment.right,\n              buildImportThrow(localName),\n            ]);\n          }\n\n          path.replaceWith(\n            buildBindingExportAssignmentExpression(\n              this.metadata,\n              exportedNames,\n              assignment,\n              path.scope,\n            ),\n          );\n          requeueInParent(path);\n        }\n      } else {\n        const ids = left.getOuterBindingIdentifiers();\n        const programScopeIds = Object.keys(ids).filter(\n          localName =>\n            scope.getBinding(localName) === path.scope.getBinding(localName),\n        );\n        const id = programScopeIds.find(localName => imported.has(localName));\n\n        if (id) {\n          path.node.right = sequenceExpression([\n            path.node.right,\n            buildImportThrow(id),\n          ]);\n        }\n\n        // Complex ({a, b, c} = {}); export { a, c };\n        // =>   ({a, b, c} = {}), (exports.a = a, exports.c = c);\n        const items: t.Expression[] = [];\n        programScopeIds.forEach(localName => {\n          const exportedNames = exported.get(localName) || [];\n          if (exportedNames.length > 0) {\n            items.push(\n              buildBindingExportAssignmentExpression(\n                this.metadata,\n                exportedNames,\n                identifier(localName),\n                path.scope,\n              ),\n            );\n          }\n        });\n\n        if (items.length > 0) {\n          let node: t.Node = sequenceExpression(items);\n          if (path.parentPath.isExpressionStatement()) {\n            node = expressionStatement(node);\n            // @ts-expect-error todo(flow->ts): avoid mutations\n            node._blockHoist = path.parentPath.node._blockHoist;\n          }\n\n          const statement = path.insertAfter(node)[0];\n          requeueInParent(statement);\n        }\n      }\n    },\n  },\n  \"ForOfStatement|ForInStatement\"(\n    path: NodePath<t.ForOfStatement | t.ForInStatement>,\n  ) {\n    const { scope, node } = path;\n    const { left } = node;\n    const { exported, imported, scope: programScope } = this;\n\n    if (!isVariableDeclaration(left)) {\n      let didTransformExport = false,\n        importConstViolationName;\n      const loopBodyScope = path.get(\"body\").scope;\n      for (const name of Object.keys(getOuterBindingIdentifiers(left))) {\n        if (programScope.getBinding(name) === scope.getBinding(name)) {\n          if (exported.has(name)) {\n            didTransformExport = true;\n            if (loopBodyScope.hasOwnBinding(name)) {\n              loopBodyScope.rename(name);\n            }\n          }\n          if (imported.has(name) && !importConstViolationName) {\n            importConstViolationName = name;\n          }\n        }\n      }\n      if (!didTransformExport && !importConstViolationName) {\n        return;\n      }\n\n      path.ensureBlock();\n      const bodyPath = path.get(\"body\");\n\n      const newLoopId = scope.generateUidIdentifierBasedOnNode(left);\n      path\n        .get(\"left\")\n        .replaceWith(\n          variableDeclaration(\"let\", [\n            variableDeclarator(cloneNode(newLoopId)),\n          ]),\n        );\n      scope.registerDeclaration(path.get(\"left\"));\n\n      if (didTransformExport) {\n        bodyPath.unshiftContainer(\n          \"body\",\n          expressionStatement(assignmentExpression(\"=\", left, newLoopId)),\n        );\n      }\n      if (importConstViolationName) {\n        bodyPath.unshiftContainer(\n          \"body\",\n          expressionStatement(buildImportThrow(importConstViolationName)),\n        );\n      }\n    }\n  },\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAAE,mBAAA,GAAAF,OAAA;AAIA,MAAM;EACJG,oBAAoB;EACpBC,cAAc;EACdC,SAAS;EACTC,mBAAmB;EACnBC,0BAA0B;EAC1BC,UAAU;EACVC,kBAAkB;EAClBC,qBAAqB;EACrBC,aAAa;EACbC,mBAAmB;EACnBC,gBAAgB;EAChBC,cAAc;EACdC,kBAAkB;EAClBC,aAAa;EACbC,mBAAmB;EACnBC;AACF,CAAC,GAAGC,WAAC;AAsBL,SAASC,QAAQA,CAACC,IAAc,EAAE;EAChC,GAAG;IACD,QAAQA,IAAI,CAACC,MAAM,CAACC,IAAI;MACtB,KAAK,kBAAkB;MACvB,KAAK,wBAAwB;MAC7B,KAAK,iBAAiB;MACtB,KAAK,gBAAgB;MACrB,KAAK,WAAW;QACd,OAAO,IAAI;MACb,KAAK,iBAAiB;QACpB,OAEIF,IAAI,CAACG,UAAU,CAACF,MAAM,CAGtBG,UAAU,KAAK,MAAM;MAE3B;QACE,IAAIJ,IAAI,CAACG,UAAU,CAACE,WAAW,CAAC,CAAC,IAAIL,IAAI,CAACG,UAAU,CAACG,YAAY,CAAC,CAAC,EAAE;UACnE,OAAO,KAAK;QACd;IACJ;EACF,CAAC,QAASN,IAAI,GAAGA,IAAI,CAACG,UAAU;AAClC;AAEe,SAASI,qBAAqBA,CAC3CC,WAAgC,EAChCC,QAAwB,EACxB;EACA,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC1B,MAAMC,QAAQ,GAAG,IAAID,GAAG,CAAC,CAAC;EAC1B,MAAME,eAAe,GAAIb,IAAc,IAAK;IAK1CQ,WAAW,CAACM,OAAO,CAACd,IAAI,CAAC;EAC3B,CAAC;EAED,KAAK,MAAM,CAACe,MAAM,EAAEC,IAAI,CAAC,IAAIP,QAAQ,CAACM,MAAM,EAAE;IAC5C,KAAK,MAAM,CAACE,SAAS,EAAEC,UAAU,CAAC,IAAIF,IAAI,CAACG,OAAO,EAAE;MAClDT,QAAQ,CAACU,GAAG,CAACH,SAAS,EAAE,CAACF,MAAM,EAAEG,UAAU,EAAE,IAAI,CAAC,CAAC;IACrD;IACA,KAAK,MAAMD,SAAS,IAAID,IAAI,CAACK,gBAAgB,EAAE;MAC7CX,QAAQ,CAACU,GAAG,CAACH,SAAS,EAAE,CAACF,MAAM,EAAE,IAAI,EAAEE,SAAS,CAAC,CAAC;IACpD;EACF;EAEA,KAAK,MAAM,CAACK,KAAK,EAAEN,IAAI,CAAC,IAAIP,QAAQ,CAACa,KAAK,EAAE;IAC1C,IAAIC,UAAU,GAAGX,QAAQ,CAACY,GAAG,CAACF,KAAK,CAAC;IACpC,IAAI,CAACC,UAAU,EAAE;MACfA,UAAU,GAAG,EAAE;MACfX,QAAQ,CAACQ,GAAG,CAACE,KAAK,EAAEC,UAAU,CAAC;IACjC;IAEAA,UAAU,CAACE,IAAI,CAAC,GAAGT,IAAI,CAACU,KAAK,CAAC;EAChC;EAGA,MAAMC,8BAA8D,GAAG;IACrElB,QAAQ;IACRI,eAAe;IACfe,KAAK,EAAEpB,WAAW,CAACoB,KAAK;IACxBhB;EACF,CAAC;EACDJ,WAAW,CAACqB,QAAQ,CAElBC,yBAAyB,EACzBH,8BACF,CAAC;EAGD,MAAMI,YAAY,GAAG,IAAIC,GAAG,CAAC,CAC3B,GAAGC,KAAK,CAACC,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC,CAAC,CAAC,EAC9B,GAAGF,KAAK,CAACC,IAAI,CAACtB,QAAQ,CAACuB,IAAI,CAAC,CAAC,CAAC,CAC/B,CAAC;EAGK;IAEL,IAAAC,2BAAc,EAAC5B,WAAW,EAAEuB,YAAY,EAAE,KAAK,CAAC;EAClD;EAGA,MAAMM,6BAA4D,GAAG;IACnEC,IAAI,EAAE,IAAIC,OAAO,CAAC,CAAC;IACnB9B,QAAQ;IACRI,eAAe;IACfe,KAAK,EAAEpB,WAAW,CAACoB,KAAK;IACxBlB,QAAQ;IACRE,QAAQ;IACR4B,oBAAoB,EAAEA,CAAC,CAACzB,MAAM,EAAEG,UAAU,EAAED,SAAS,CAAC,EAAEwB,SAAS,KAAK;MACpE,MAAMC,IAAI,GAAGjC,QAAQ,CAACM,MAAM,CAACS,GAAG,CAACT,MAAM,CAAC;MACxC2B,IAAI,CAACC,UAAU,GAAG,IAAI;MAEtB,IAAI1B,SAAS,EAAE;QACb,IAAIyB,IAAI,CAACE,IAAI,EAAE;UACbH,SAAS,GAAG1D,cAAc,CAExB0D,SAAS,EACT,EACF,CAAC;QACH;QACA,OAAOA,SAAS;MAClB;MAEA,IAAII,SAAuB,GAAG1D,UAAU,CAACuD,IAAI,CAACI,IAAI,CAAC;MACnD,IAAIJ,IAAI,CAACE,IAAI,EAAEC,SAAS,GAAG9D,cAAc,CAAC8D,SAAS,EAAE,EAAE,CAAC;MAExD,IAAI3B,UAAU,KAAK,SAAS,IAAIwB,IAAI,CAACK,OAAO,KAAK,cAAc,EAAE;QAC/D,OAAOF,SAAS;MAClB;MAEA,MAAMG,QAAQ,GAAGvC,QAAQ,CAACwC,gBAAgB,CAACC,GAAG,CAAChC,UAAU,CAAC;MAE1D,OAAO1B,gBAAgB,CACrBqD,SAAS,EACTG,QAAQ,GAAGrD,aAAa,CAACuB,UAAU,CAAC,GAAG/B,UAAU,CAAC+B,UAAU,CAAC,EAC7D8B,QACF,CAAC;IACH;EACF,CAAC;EAEDxC,WAAW,CAACqB,QAAQ,CAACsB,wBAAwB,EAAEd,6BAA6B,CAAC;AAC/E;AAKA,MAAMP,yBAAkE,GAAG;EACzEsB,KAAKA,CAACpD,IAAI,EAAE;IACVA,IAAI,CAACqD,IAAI,CAAC,CAAC;EACb,CAAC;EACDC,gBAAgBA,CAACtD,IAAI,EAAE;IACrB,MAAM;MAAEa,eAAe;MAAED,QAAQ;MAAEH;IAAS,CAAC,GAAG,IAAI;IAEpD,MAAM;MAAE8C;IAAG,CAAC,GAAGvD,IAAI,CAACwD,IAAI;IACxB,IAAI,CAACD,EAAE,EAAE,MAAM,IAAIE,KAAK,CAAC,+BAA+B,CAAC;IACzD,MAAMxC,SAAS,GAAGsC,EAAE,CAACT,IAAI;IAEzB,MAAMY,WAAW,GAAG9C,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,IAAI,EAAE;IACjD,IAAIyC,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1B,MAAMC,SAAS,GAAG3E,mBAAmB,CAEnC4E,sCAAsC,CACpCpD,QAAQ,EACRiD,WAAW,EACXvE,UAAU,CAAC8B,SAAS,CAAC,EACrBjB,IAAI,CAAC4B,KACP,CACF,CAAC;MAEDgC,SAAS,CAACE,WAAW,GAAG9D,IAAI,CAACwD,IAAI,CAACM,WAAW;MAE7CjD,eAAe,CAACb,IAAI,CAAC+D,WAAW,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD;EACF,CAAC;EACDI,mBAAmBA,CAAChE,IAAI,EAAE;IACxB,MAAM;MAAEa,eAAe;MAAED,QAAQ;MAAEH;IAAS,CAAC,GAAG,IAAI;IAEpDwD,MAAM,CAAC9B,IAAI,CAACnC,IAAI,CAACd,0BAA0B,CAAC,CAAC,CAAC,CAACgF,OAAO,CAACjD,SAAS,IAAI;MAClE,MAAMyC,WAAW,GAAG9C,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,IAAI,EAAE;MAEjD,IAAIyC,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;QAC1B,MAAMC,SAAS,GAAG3E,mBAAmB,CAEnC4E,sCAAsC,CACpCpD,QAAQ,EACRiD,WAAW,EACXvE,UAAU,CAAC8B,SAAS,CAAC,EACrBjB,IAAI,CAAC4B,KACP,CACF,CAAC;QAEDgC,SAAS,CAACE,WAAW,GAAG9D,IAAI,CAACwD,IAAI,CAACM,WAAW;QAE7CjD,eAAe,CAACb,IAAI,CAAC+D,WAAW,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAED,MAAMC,sCAAsC,GAAGA,CAC7CpD,QAAwB,EACxBiD,WAAqB,EACrBS,SAAuB,EACvBvC,KAAY,KACT;EACH,MAAMwC,iBAAiB,GAAG3D,QAAQ,CAAC4D,UAAU;EAC7C,KACE,IAAIC,YAAY,GAAG1C,KAAK,EACxB0C,YAAY,IAAI,IAAI,EACpBA,YAAY,GAAGA,YAAY,CAACrE,MAAM,EAClC;IACA,IAAIqE,YAAY,CAACC,aAAa,CAACH,iBAAiB,CAAC,EAAE;MACjDE,YAAY,CAACE,MAAM,CAACJ,iBAAiB,CAAC;IACxC;EACF;EACA,OAAO,CAACV,WAAW,IAAI,EAAE,EAAEe,MAAM,CAAC,CAACC,IAAI,EAAEL,UAAU,KAAK;IAItD,MAAM;MAAEpB;IAAiB,CAAC,GAAGxC,QAAQ;IACrC,MAAMuC,QAAQ,GAAGC,gBAAgB,CAACC,GAAG,CAACmB,UAAU,CAAC;IACjD,OAAOvF,oBAAoB,CACzB,GAAG,EACHU,gBAAgB,CACdL,UAAU,CAACiF,iBAAiB,CAAC,EAC7BpB,QAAQ,GAAGrD,aAAa,CAAC0E,UAAU,CAAC,GAAGlF,UAAU,CAACkF,UAAU,CAAC,EAC9CrB,QACjB,CAAC,EACD0B,IACF,CAAC;EACH,CAAC,EAAEP,SAAS,CAAC;AACf,CAAC;AAED,MAAMQ,gBAAgB,GAAI1D,SAAiB,IAAK;EAC9C,OAAO2D,cAAQ,CAACC,UAAU,CAACC,GAAI;AACjC;AACA,+BAA+B7D,SAAU;AACzC;AACA,GAAG;AACH,CAAC;AAED,MAAMkC,wBAAgE,GAAG;EACvE4B,oBAAoBA,CAAC/E,IAAI,EAAE;IACzB,MAAM;MAAEsC,IAAI;MAAEE,oBAAoB;MAAEZ,KAAK;MAAElB,QAAQ;MAAEG;IAAgB,CAAC,GACpE,IAAI;IACN,IAAIyB,IAAI,CAACY,GAAG,CAAClD,IAAI,CAACwD,IAAI,CAAC,EAAE;IACzBlB,IAAI,CAAC0C,GAAG,CAAChF,IAAI,CAACwD,IAAI,CAAC;IAEnB,MAAMvC,SAAS,GAAGjB,IAAI,CAACwD,IAAI,CAACV,IAAI;IAEhC,MAAMmC,UAAU,GAAGvE,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;IAC1C,IAAIgE,UAAU,EAAE;MACd,IAAIlF,QAAQ,CAACC,IAAI,CAAC,EAAE;QAClB,MAAMA,IAAI,CAACkF,mBAAmB,CAC3B,0CAAyCjE,SAAU,+CAA8C,GAC/F,qFACL,CAAC;MACH;MAEA,MAAMkE,YAAY,GAAGnF,IAAI,CAAC4B,KAAK,CAACwD,UAAU,CAACnE,SAAS,CAAC;MACrD,MAAMoE,WAAW,GAAGzD,KAAK,CAACwD,UAAU,CAACnE,SAAS,CAAC;MAG/C,IAAIoE,WAAW,KAAKF,YAAY,EAAE;MAElC,MAAMG,GAAG,GAAG9C,oBAAoB,CAACyC,UAAU,EAAEjF,IAAI,CAACwD,IAAI,CAAC;MAGvD8B,GAAG,CAACC,GAAG,GAAGvF,IAAI,CAACwD,IAAI,CAAC+B,GAAG;MAEvB,IACE,CAACvF,IAAI,CAACG,UAAU,CAACqF,gBAAgB,CAAC;QAAEC,MAAM,EAAEzF,IAAI,CAACwD;MAAK,CAAC,CAAC,IACtDxD,IAAI,CAACG,UAAU,CAACuF,wBAAwB,CAAC;QAAED,MAAM,EAAEzF,IAAI,CAACwD;MAAK,CAAC,CAAC,IAC/DxD,IAAI,CAACG,UAAU,CAACwF,0BAA0B,CAAC;QAAEC,GAAG,EAAE5F,IAAI,CAACwD;MAAK,CAAC,CAAC,KAChEpE,kBAAkB,CAACkG,GAAG,CAAC,EACvB;QACAtF,IAAI,CAAC6F,WAAW,CAACnG,kBAAkB,CAAC,CAACD,cAAc,CAAC,CAAC,CAAC,EAAE6F,GAAG,CAAC,CAAC,CAAC;MAChE,CAAC,MAAM,IAAItF,IAAI,CAAC8F,eAAe,CAAC,CAAC,IAAI1G,kBAAkB,CAACkG,GAAG,CAAC,EAAE;QAC5D,MAAM;UAAES,MAAM;UAAEC;QAAS,CAAC,GAAGV,GAAG;QAChCtF,IAAI,CAAC6F,WAAW,CACdtG,mBAAmB,CAEjBD,aAAa,CAACyG,MAAM,CAACjD,IAAI,CAAC,EAE1BxD,aAAa,CAAC0G,QAAQ,CAAClD,IAAI,CAC7B,CACF,CAAC;MACH,CAAC,MAAM;QACL9C,IAAI,CAAC6F,WAAW,CAACP,GAAG,CAAC;MACvB;MAEAzE,eAAe,CAACb,IAAI,CAAC;MAIrBA,IAAI,CAACqD,IAAI,CAAC,CAAC;IACb;EACF,CAAC;EAED4C,gBAAgBA,CAACjG,IAAI,EAAE;IACrB,MAAM;MACJ4B,KAAK;MACLU,IAAI;MACJ5B,QAAQ;MACRE,QAAQ;MACRC,eAAe;MACf2B;IACF,CAAC,GAAG,IAAI;IAER,IAAIF,IAAI,CAACY,GAAG,CAAClD,IAAI,CAACwD,IAAI,CAAC,EAAE;IAEzBlB,IAAI,CAAC0C,GAAG,CAAChF,IAAI,CAACwD,IAAI,CAAC;IAEnB,MAAM0C,GAAG,GAAGlG,IAAI,CAACwB,GAAG,CAAC,UAAU,CAAC;IAGhC,IAAI0E,GAAG,CAAC9G,kBAAkB,CAAC,CAAC,EAAE;IAE9B,MAAM+G,MAAM,GAAGnG,IAAI,CAACwD,IAAI;IAExB,IAAI0C,GAAG,CAACE,YAAY,CAAC,CAAC,EAAE;MACtB,MAAMnF,SAAS,GAAGiF,GAAG,CAAC1C,IAAI,CAACV,IAAI;MAG/B,IAAIlB,KAAK,CAACwD,UAAU,CAACnE,SAAS,CAAC,KAAKjB,IAAI,CAAC4B,KAAK,CAACwD,UAAU,CAACnE,SAAS,CAAC,EAAE;QACpE;MACF;MAEA,MAAMoF,aAAa,GAAGzF,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC;MAC7C,MAAMgE,UAAU,GAAGvE,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;MAE1C,IAAI,CAAAoF,aAAa,oBAAbA,aAAa,CAAE1C,MAAM,IAAG,CAAC,IAAIsB,UAAU,EAAE;QAC3C,IAAIA,UAAU,EAAE;UACdjF,IAAI,CAAC6F,WAAW,CACd/G,oBAAoB,CAClBqH,MAAM,CAACG,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,EACxB9D,oBAAoB,CAACyC,UAAU,EAAEiB,GAAG,CAAC1C,IAAI,CAAC,EAC1CmB,gBAAgB,CAAC1D,SAAS,CAC5B,CACF,CAAC;QACH,CAAC,MAAM,IAAIkF,MAAM,CAACI,MAAM,EAAE;UAGxBvG,IAAI,CAAC6F,WAAW,CACdhC,sCAAsC,CACpC,IAAI,CAACpD,QAAQ,EACb4F,aAAa,EACbrH,SAAS,CAACmH,MAAM,CAAC,EACjBnG,IAAI,CAAC4B,KACP,CACF,CAAC;QACH,CAAC,MAAM;UAGL,MAAM0D,GAAG,GAAG1D,KAAK,CAAC4E,6BAA6B,CAACvF,SAAS,CAAC;UAE1DjB,IAAI,CAAC6F,WAAW,CACdnG,kBAAkB,CAAC,CACjBZ,oBAAoB,CAAC,GAAG,EAAEE,SAAS,CAACsG,GAAG,CAAC,EAAEtG,SAAS,CAACmH,MAAM,CAAC,CAAC,EAC5DtC,sCAAsC,CACpC,IAAI,CAACpD,QAAQ,EACb4F,aAAa,EACblH,UAAU,CAAC8B,SAAS,CAAC,EACrBjB,IAAI,CAAC4B,KACP,CAAC,EACD5C,SAAS,CAACsG,GAAG,CAAC,CACf,CACH,CAAC;QACH;MACF;IACF;IAEAzE,eAAe,CAACb,IAAI,CAAC;IACrBA,IAAI,CAACqD,IAAI,CAAC,CAAC;EACb,CAAC;EAEDoD,oBAAoB,EAAE;IACpBC,IAAIA,CAAC1G,IAAI,EAAE;MACT,MAAM;QACJ4B,KAAK;QACLU,IAAI;QACJ5B,QAAQ;QACRE,QAAQ;QACRC,eAAe;QACf2B;MACF,CAAC,GAAG,IAAI;MAER,IAAIF,IAAI,CAACY,GAAG,CAAClD,IAAI,CAACwD,IAAI,CAAC,EAAE;MACzBlB,IAAI,CAAC0C,GAAG,CAAChF,IAAI,CAACwD,IAAI,CAAC;MAEnB,MAAMmD,IAAI,GAAG3G,IAAI,CAACwB,GAAG,CAAC,MAAM,CAAC;MAG7B,IAAImF,IAAI,CAACvH,kBAAkB,CAAC,CAAC,EAAE;MAE/B,IAAIuH,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;QAGvB,MAAMnF,SAAS,GAAG0F,IAAI,CAACnD,IAAI,CAACV,IAAI;QAGhC,IAAIlB,KAAK,CAACwD,UAAU,CAACnE,SAAS,CAAC,KAAKjB,IAAI,CAAC4B,KAAK,CAACwD,UAAU,CAACnE,SAAS,CAAC,EAAE;UACpE;QACF;QAEA,MAAMoF,aAAa,GAAGzF,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC;QAC7C,MAAMgE,UAAU,GAAGvE,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;QAC1C,IAAI,CAAAoF,aAAa,oBAAbA,aAAa,CAAE1C,MAAM,IAAG,CAAC,IAAIsB,UAAU,EAAE;UAC3C2B,OAAM,CAAC5G,IAAI,CAACwD,IAAI,CAAC8C,QAAQ,KAAK,GAAG,EAAE,yBAAyB,CAAC;UAE7D,MAAMO,UAAU,GAAG7G,IAAI,CAACwD,IAAI;UAE5B,IAAIyB,UAAU,EAAE;YACd4B,UAAU,CAACF,IAAI,GAAGnE,oBAAoB,CAACyC,UAAU,EAAE0B,IAAI,CAACnD,IAAI,CAAC;YAE7DqD,UAAU,CAACC,KAAK,GAAGpH,kBAAkB,CAAC,CACpCmH,UAAU,CAACC,KAAK,EAChBnC,gBAAgB,CAAC1D,SAAS,CAAC,CAC5B,CAAC;UACJ;UAEAjB,IAAI,CAAC6F,WAAW,CACdhC,sCAAsC,CACpC,IAAI,CAACpD,QAAQ,EACb4F,aAAa,EACbQ,UAAU,EACV7G,IAAI,CAAC4B,KACP,CACF,CAAC;UACDf,eAAe,CAACb,IAAI,CAAC;QACvB;MACF,CAAC,MAAM;QACL,MAAM+G,GAAG,GAAGJ,IAAI,CAACzH,0BAA0B,CAAC,CAAC;QAC7C,MAAM8H,eAAe,GAAG/C,MAAM,CAAC9B,IAAI,CAAC4E,GAAG,CAAC,CAACE,MAAM,CAC7ChG,SAAS,IACPW,KAAK,CAACwD,UAAU,CAACnE,SAAS,CAAC,KAAKjB,IAAI,CAAC4B,KAAK,CAACwD,UAAU,CAACnE,SAAS,CACnE,CAAC;QACD,MAAMsC,EAAE,GAAGyD,eAAe,CAACE,IAAI,CAACjG,SAAS,IAAIP,QAAQ,CAACwC,GAAG,CAACjC,SAAS,CAAC,CAAC;QAErE,IAAIsC,EAAE,EAAE;UACNvD,IAAI,CAACwD,IAAI,CAACsD,KAAK,GAAGpH,kBAAkB,CAAC,CACnCM,IAAI,CAACwD,IAAI,CAACsD,KAAK,EACfnC,gBAAgB,CAACpB,EAAE,CAAC,CACrB,CAAC;QACJ;QAIA,MAAM4D,KAAqB,GAAG,EAAE;QAChCH,eAAe,CAAC9C,OAAO,CAACjD,SAAS,IAAI;UACnC,MAAMoF,aAAa,GAAGzF,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,IAAI,EAAE;UACnD,IAAIoF,aAAa,CAAC1C,MAAM,GAAG,CAAC,EAAE;YAC5BwD,KAAK,CAAC1F,IAAI,CACRoC,sCAAsC,CACpC,IAAI,CAACpD,QAAQ,EACb4F,aAAa,EACblH,UAAU,CAAC8B,SAAS,CAAC,EACrBjB,IAAI,CAAC4B,KACP,CACF,CAAC;UACH;QACF,CAAC,CAAC;QAEF,IAAIuF,KAAK,CAACxD,MAAM,GAAG,CAAC,EAAE;UACpB,IAAIH,IAAY,GAAG9D,kBAAkB,CAACyH,KAAK,CAAC;UAC5C,IAAInH,IAAI,CAACG,UAAU,CAACiH,qBAAqB,CAAC,CAAC,EAAE;YAC3C5D,IAAI,GAAGvE,mBAAmB,CAACuE,IAAI,CAAC;YAEhCA,IAAI,CAACM,WAAW,GAAG9D,IAAI,CAACG,UAAU,CAACqD,IAAI,CAACM,WAAW;UACrD;UAEA,MAAMF,SAAS,GAAG5D,IAAI,CAAC+D,WAAW,CAACP,IAAI,CAAC,CAAC,CAAC,CAAC;UAC3C3C,eAAe,CAAC+C,SAAS,CAAC;QAC5B;MACF;IACF;EACF,CAAC;EACD,+BAA+ByD,CAC7BrH,IAAmD,EACnD;IACA,MAAM;MAAE4B,KAAK;MAAE4B;IAAK,CAAC,GAAGxD,IAAI;IAC5B,MAAM;MAAE2G;IAAK,CAAC,GAAGnD,IAAI;IACrB,MAAM;MAAE5C,QAAQ;MAAEF,QAAQ;MAAEkB,KAAK,EAAE0F;IAAa,CAAC,GAAG,IAAI;IAExD,IAAI,CAACjI,qBAAqB,CAACsH,IAAI,CAAC,EAAE;MAChC,IAAIY,kBAAkB,GAAG,KAAK;QAC5BC,wBAAwB;MAC1B,MAAMC,aAAa,GAAGzH,IAAI,CAACwB,GAAG,CAAC,MAAM,CAAC,CAACI,KAAK;MAC5C,KAAK,MAAMkB,IAAI,IAAImB,MAAM,CAAC9B,IAAI,CAACjD,0BAA0B,CAACyH,IAAI,CAAC,CAAC,EAAE;QAChE,IAAIW,YAAY,CAAClC,UAAU,CAACtC,IAAI,CAAC,KAAKlB,KAAK,CAACwD,UAAU,CAACtC,IAAI,CAAC,EAAE;UAC5D,IAAIlC,QAAQ,CAACsC,GAAG,CAACJ,IAAI,CAAC,EAAE;YACtByE,kBAAkB,GAAG,IAAI;YACzB,IAAIE,aAAa,CAAClD,aAAa,CAACzB,IAAI,CAAC,EAAE;cACrC2E,aAAa,CAACjD,MAAM,CAAC1B,IAAI,CAAC;YAC5B;UACF;UACA,IAAIpC,QAAQ,CAACwC,GAAG,CAACJ,IAAI,CAAC,IAAI,CAAC0E,wBAAwB,EAAE;YACnDA,wBAAwB,GAAG1E,IAAI;UACjC;QACF;MACF;MACA,IAAI,CAACyE,kBAAkB,IAAI,CAACC,wBAAwB,EAAE;QACpD;MACF;MAEAxH,IAAI,CAAC0H,WAAW,CAAC,CAAC;MAClB,MAAMC,QAAQ,GAAG3H,IAAI,CAACwB,GAAG,CAAC,MAAM,CAAC;MAEjC,MAAMoG,SAAS,GAAGhG,KAAK,CAACiG,gCAAgC,CAAClB,IAAI,CAAC;MAC9D3G,IAAI,CACDwB,GAAG,CAAC,MAAM,CAAC,CACXqE,WAAW,CACVjG,mBAAmB,CAAC,KAAK,EAAE,CACzBC,kBAAkB,CAACb,SAAS,CAAC4I,SAAS,CAAC,CAAC,CACzC,CACH,CAAC;MACHhG,KAAK,CAACkG,mBAAmB,CAAC9H,IAAI,CAACwB,GAAG,CAAC,MAAM,CAAC,CAAC;MAE3C,IAAI+F,kBAAkB,EAAE;QACtBI,QAAQ,CAACI,gBAAgB,CACvB,MAAM,EACN9I,mBAAmB,CAACH,oBAAoB,CAAC,GAAG,EAAE6H,IAAI,EAAEiB,SAAS,CAAC,CAChE,CAAC;MACH;MACA,IAAIJ,wBAAwB,EAAE;QAC5BG,QAAQ,CAACI,gBAAgB,CACvB,MAAM,EACN9I,mBAAmB,CAAC0F,gBAAgB,CAAC6C,wBAAwB,CAAC,CAChE,CAAC;MACH;IACF;EACF;AACF,CAAC"}