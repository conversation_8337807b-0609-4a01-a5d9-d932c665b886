{"version": 3, "names": ["_importMetaResolve", "require", "importMetaResolve", "polyfill", "resolve", "specifier", "parent"], "sources": ["../../../src/config/files/import-meta-resolve.ts"], "sourcesContent": ["import { resolve as polyfill } from \"../../vendor/import-meta-resolve.ts\";\n\nlet importMetaResolve: (specifier: string, parent: string) => string;\n\nif (USE_ESM) {\n  // Node.js < 20, when using the `--experimental-import-meta-resolve` flag,\n  // have an asynchronous implementation of import.meta.resolve.\n  if (\n    typeof import.meta.resolve === \"function\" &&\n    typeof import.meta.resolve(import.meta.url) === \"string\"\n  ) {\n    // @ts-expect-error: TS defines import.meta as returning a promise\n    importMetaResolve = import.meta.resolve;\n  } else {\n    importMetaResolve = polyfill;\n  }\n} else {\n  importMetaResolve = polyfill;\n}\n\nexport default function resolve(\n  specifier: string,\n  parent?: string | URL,\n): string {\n  // @ts-expect-error: TS defines import.meta.resolve as returning a promises\n  return importMetaResolve(specifier, parent);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAEA,IAAIC,iBAAgE;AAc7D;EACLA,iBAAiB,GAAGC,0BAAQ;AAC9B;AAEe,SAASC,OAAOA,CAC7BC,SAAiB,EACjBC,MAAqB,EACb;EAER,OAAOJ,iBAAiB,CAACG,SAAS,EAAEC,MAAM,CAAC;AAC7C;AAAC"}