import { SearchBar } from '@appmaker-xyz/uikit';
import React from 'react';
import { emitEvent } from '@appmaker-xyz/core';
import { analytics } from '@appmaker-xyz/core';
import { isEmpty, trim } from 'lodash';

export function SearchTapSearchBar({ attributes, onAction, coreDispatch }) {
  return (
    <SearchBar
      label={attributes.label}
      topBarView={attributes.topBarView}
      onBack={() => onAction({ action: 'GO_BACK' })}
      debounceInterval={attributes.debounceInterval}
      debounceOnChange={(text) => {
        if (!isEmpty(text)) {
          analytics.track('product_search_inline', {
            query: trim(text),
            extenstion: 'searchtap',
          });
        }
        coreDispatch({
          type: 'SET_VALUE',
          name: attributes.name || 'searchSuggestion',
          value: text,
        });
      }}
      onPress={async (searchQuery) => {
        analytics.track('product_search', {
          query: trim(searchQuery),
          extenstion: 'searchtap',
        });
        emitEvent('product.search', searchQuery);
        onAction({
          ...attributes.appmakerAction,
          params: {
            ...attributes.appmakerAction?.params,
            title: searchQuery || '',
            searchQuery,
          },
        });
      }}
    />
  );
}
