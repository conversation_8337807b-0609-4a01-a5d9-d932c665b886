{"version": 3, "names": ["_asyncGeneratorDelegate", "inner", "iter", "waiting", "pump", "key", "value", "Promise", "resolve", "done", "OverloadYield", "Symbol", "iterator", "next", "throw", "return"], "sources": ["../../src/helpers/asyncGeneratorDelegate.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport <PERSON><PERSON><PERSON><PERSON> from \"OverloadYield\";\n\nexport default function _asyncGeneratorDelegate(inner) {\n  var iter = {},\n    // See the comment in AsyncGenerator to understand what this is.\n    waiting = false;\n\n  function pump(key, value) {\n    waiting = true;\n    value = new Promise(function (resolve) {\n      resolve(inner[key](value));\n    });\n    return {\n      done: false,\n      value: new OverloadYield(value, /* kind: delegate */ 1),\n    };\n  }\n\n  iter[(typeof Symbol !== \"undefined\" && Symbol.iterator) || \"@@iterator\"] =\n    function () {\n      return this;\n    };\n\n  iter.next = function (value) {\n    if (waiting) {\n      waiting = false;\n      return value;\n    }\n    return pump(\"next\", value);\n  };\n\n  if (typeof inner.throw === \"function\") {\n    iter.throw = function (value) {\n      if (waiting) {\n        waiting = false;\n        throw value;\n      }\n      return pump(\"throw\", value);\n    };\n  }\n\n  if (typeof inner.return === \"function\") {\n    iter.return = function (value) {\n      if (waiting) {\n        waiting = false;\n        return value;\n      }\n      return pump(\"return\", value);\n    };\n  }\n\n  return iter;\n}\n"], "mappings": ";;;;;;AAEA;AAEe,SAASA,uBAAuB,CAACC,KAAK,EAAE;EACrD,IAAIC,IAAI,GAAG,CAAC,CAAC;IAEXC,OAAO,GAAG,KAAK;EAEjB,SAASC,IAAI,CAACC,GAAG,EAAEC,KAAK,EAAE;IACxBH,OAAO,GAAG,IAAI;IACdG,KAAK,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;MACrCA,OAAO,CAACP,KAAK,CAACI,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,OAAO;MACLG,IAAI,EAAE,KAAK;MACXH,KAAK,EAAE,IAAII,cAAa,CAACJ,KAAK,EAAuB,CAAC;IACxD,CAAC;EACH;EAEAJ,IAAI,CAAE,OAAOS,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAK,YAAY,CAAC,GACtE,YAAY;IACV,OAAO,IAAI;EACb,CAAC;EAEHV,IAAI,CAACW,IAAI,GAAG,UAAUP,KAAK,EAAE;IAC3B,IAAIH,OAAO,EAAE;MACXA,OAAO,GAAG,KAAK;MACf,OAAOG,KAAK;IACd;IACA,OAAOF,IAAI,CAAC,MAAM,EAAEE,KAAK,CAAC;EAC5B,CAAC;EAED,IAAI,OAAOL,KAAK,CAACa,KAAK,KAAK,UAAU,EAAE;IACrCZ,IAAI,CAACY,KAAK,GAAG,UAAUR,KAAK,EAAE;MAC5B,IAAIH,OAAO,EAAE;QACXA,OAAO,GAAG,KAAK;QACf,MAAMG,KAAK;MACb;MACA,OAAOF,IAAI,CAAC,OAAO,EAAEE,KAAK,CAAC;IAC7B,CAAC;EACH;EAEA,IAAI,OAAOL,KAAK,CAACc,MAAM,KAAK,UAAU,EAAE;IACtCb,IAAI,CAACa,MAAM,GAAG,UAAUT,KAAK,EAAE;MAC7B,IAAIH,OAAO,EAAE;QACXA,OAAO,GAAG,KAAK;QACf,OAAOG,KAAK;MACd;MACA,OAAOF,IAAI,CAAC,QAAQ,EAAEE,KAAK,CAAC;IAC9B,CAAC;EACH;EAEA,OAAOJ,IAAI;AACb"}