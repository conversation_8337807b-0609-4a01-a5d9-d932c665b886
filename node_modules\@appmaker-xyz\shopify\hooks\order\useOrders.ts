import { useInfiniteDataSource } from '@appmaker-xyz/core';
import { isEmpty } from 'lodash';
import { ShopifyDataSourceConfig } from '../../datasource/shopify';
import { useAppStorage } from '@appmaker-xyz/core';

type OrderProps = {
  limit?: number,
};
export type OrderList = OrderItem[]

export interface OrderItem {
  cursor: string
  node: Order
}

  export interface Order {
  orderNumber: number
  name: string
  id: string
  processedAt: string
  financialStatus: string
  cancelReason: any
  fulfillmentStatus: string
  shippingAddress: ShippingAddress
  totalPrice: TotalPrice
  successfulFulfillments: any[]
  currencyCode: string
  discountApplications: DiscountApplications
  lineItems: LineItems
}

export interface ShippingAddress {
  id: string
  firstName: string
  lastName: string
  address1: string
  address2: any
  phone: any
  city: string
  country: string
  zip: string
}

export interface TotalPrice {
  amount: string
  currencyCode: string
}

export interface DiscountApplications {
  edges: any[]
}

export interface LineItems {
  edges: Edge[]
}

export interface Edge {
  node: Node2
}

export interface Node2 {
  currentQuantity: number
  originalTotalPrice: OriginalTotalPrice
  customAttributes: CustomAttribute[]
  title: string
  discountedTotalPrice: DiscountedTotalPrice
  variant: Variant
  quantity: number
}

export interface OriginalTotalPrice {
  amount: string
  currencyCode: string
}

export interface CustomAttribute {
  key: string
  value: string
}

export interface DiscountedTotalPrice {
  amount: string
  currencyCode: string
}

export interface Variant {
  id: string
  title: string
  sku: string
  price: Price
  compareAtPrice?: CompareAtPrice
  image: Image
  product: Product
}

export interface Price {
  amount: string
  currencyCode: string
}

export interface CompareAtPrice {
  amount: string
  currencyCode: string
}

export interface Image {
  url: string
}

export interface Product {
  id: string
  vendor: string
  productType: string
}

type useOrderReturn = {
  isLoading: boolean;
  orderList: OrderList;
}
const useOrders = (props: OrderProps): useOrderReturn => {
  const user = useAppStorage((state) => state.user);
  const dataSource = ShopifyDataSourceConfig?.Orders({
    accessToken: user?.accessToken,
    ...(props?.limit && { ordersLimit: props?.limit })
  });

  const [ordersData, { list }] = useInfiniteDataSource({
    dataSource,
    enabled: !isEmpty(dataSource),
  });
  return { ...ordersData, orderList: list };
};

export { useOrders };
