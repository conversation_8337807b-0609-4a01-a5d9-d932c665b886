{"version": 3, "names": ["AsyncGenerator", "gen", "front", "back", "send", "key", "arg", "Promise", "resolve", "reject", "request", "next", "resume", "result", "value", "overloaded", "OverloadYield", "v", "then", "<PERSON><PERSON><PERSON>", "k", "done", "settle", "err", "type", "_invoke", "return", "undefined", "prototype", "Symbol", "asyncIterator", "throw"], "sources": ["../../src/helpers/AsyncGenerator.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport <PERSON>load<PERSON><PERSON> from \"OverloadYield\";\n\nexport default function AsyncGenerator(gen) {\n  var front, back;\n\n  function send(key, arg) {\n    return new Promise(function (resolve, reject) {\n      var request = {\n        key: key,\n        arg: arg,\n        resolve: resolve,\n        reject: reject,\n        next: null,\n      };\n\n      if (back) {\n        back = back.next = request;\n      } else {\n        front = back = request;\n        resume(key, arg);\n      }\n    });\n  }\n\n  function resume(key, arg) {\n    try {\n      var result = gen[key](arg);\n      var value = result.value;\n      var overloaded = value instanceof OverloadYield;\n\n      Promise.resolve(overloaded ? value.v : value).then(\n        function (arg) {\n          if (overloaded) {\n            // Overloaded yield requires calling into the generator twice:\n            //  - first we get the iterator result wrapped in a promise\n            //    (the gen[key](arg) call above)\n            //  - then we await it (the Promise.resolve call above)\n            //  - then we give the result back to the iterator, so that it can:\n            //    * if it was an await, use its result\n            //    * if it was a yield*, possibly return the `done: true` signal\n            //      so that yield* knows that the iterator is finished.\n            //      This needs to happen in the second call, because in the\n            //      first one `done: true` was hidden in the promise and thus\n            //      not visible to the (sync) yield*.\n            //      The other part of this implementation is in asyncGeneratorDelegate.\n            var nextKey = key === \"return\" ? \"return\" : \"next\";\n            if (!value.k || arg.done) {\n              // await or end of yield*\n              return resume(nextKey, arg);\n            } else {\n              // yield*, not done\n              arg = gen[nextKey](arg).value;\n            }\n          }\n\n          settle(result.done ? \"return\" : \"normal\", arg);\n        },\n        function (err) {\n          resume(\"throw\", err);\n        }\n      );\n    } catch (err) {\n      settle(\"throw\", err);\n    }\n  }\n\n  function settle(type, value) {\n    switch (type) {\n      case \"return\":\n        front.resolve({ value: value, done: true });\n        break;\n      case \"throw\":\n        front.reject(value);\n        break;\n      default:\n        front.resolve({ value: value, done: false });\n        break;\n    }\n\n    front = front.next;\n    if (front) {\n      resume(front.key, front.arg);\n    } else {\n      back = null;\n    }\n  }\n\n  this._invoke = send;\n\n  // Hide \"return\" method if generator return is not supported\n  if (typeof gen.return !== \"function\") {\n    this.return = undefined;\n  }\n}\n\nAsyncGenerator.prototype[\n  (typeof Symbol === \"function\" && Symbol.asyncIterator) || \"@@asyncIterator\"\n] = function () {\n  return this;\n};\n\nAsyncGenerator.prototype.next = function (arg) {\n  return this._invoke(\"next\", arg);\n};\nAsyncGenerator.prototype.throw = function (arg) {\n  return this._invoke(\"throw\", arg);\n};\nAsyncGenerator.prototype.return = function (arg) {\n  return this._invoke(\"return\", arg);\n};\n"], "mappings": ";;;;;;AAEA;AAEe,SAASA,cAAc,CAACC,GAAG,EAAE;EAC1C,IAAIC,KAAK,EAAEC,IAAI;EAEf,SAASC,IAAI,CAACC,GAAG,EAAEC,GAAG,EAAE;IACtB,OAAO,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;MAC5C,IAAIC,OAAO,GAAG;QACZL,GAAG,EAAEA,GAAG;QACRC,GAAG,EAAEA,GAAG;QACRE,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAEA,MAAM;QACdE,IAAI,EAAE;MACR,CAAC;MAED,IAAIR,IAAI,EAAE;QACRA,IAAI,GAAGA,IAAI,CAACQ,IAAI,GAAGD,OAAO;MAC5B,CAAC,MAAM;QACLR,KAAK,GAAGC,IAAI,GAAGO,OAAO;QACtBE,MAAM,CAACP,GAAG,EAAEC,GAAG,CAAC;MAClB;IACF,CAAC,CAAC;EACJ;EAEA,SAASM,MAAM,CAACP,GAAG,EAAEC,GAAG,EAAE;IACxB,IAAI;MACF,IAAIO,MAAM,GAAGZ,GAAG,CAACI,GAAG,CAAC,CAACC,GAAG,CAAC;MAC1B,IAAIQ,KAAK,GAAGD,MAAM,CAACC,KAAK;MACxB,IAAIC,UAAU,GAAGD,KAAK,YAAYE,cAAa;MAE/CT,OAAO,CAACC,OAAO,CAACO,UAAU,GAAGD,KAAK,CAACG,CAAC,GAAGH,KAAK,CAAC,CAACI,IAAI,CAChD,UAAUZ,GAAG,EAAE;QACb,IAAIS,UAAU,EAAE;UAad,IAAII,OAAO,GAAGd,GAAG,KAAK,QAAQ,GAAG,QAAQ,GAAG,MAAM;UAClD,IAAI,CAACS,KAAK,CAACM,CAAC,IAAId,GAAG,CAACe,IAAI,EAAE;YAExB,OAAOT,MAAM,CAACO,OAAO,EAAEb,GAAG,CAAC;UAC7B,CAAC,MAAM;YAELA,GAAG,GAAGL,GAAG,CAACkB,OAAO,CAAC,CAACb,GAAG,CAAC,CAACQ,KAAK;UAC/B;QACF;QAEAQ,MAAM,CAACT,MAAM,CAACQ,IAAI,GAAG,QAAQ,GAAG,QAAQ,EAAEf,GAAG,CAAC;MAChD,CAAC,EACD,UAAUiB,GAAG,EAAE;QACbX,MAAM,CAAC,OAAO,EAAEW,GAAG,CAAC;MACtB,CAAC,CACF;IACH,CAAC,CAAC,OAAOA,GAAG,EAAE;MACZD,MAAM,CAAC,OAAO,EAAEC,GAAG,CAAC;IACtB;EACF;EAEA,SAASD,MAAM,CAACE,IAAI,EAAEV,KAAK,EAAE;IAC3B,QAAQU,IAAI;MACV,KAAK,QAAQ;QACXtB,KAAK,CAACM,OAAO,CAAC;UAAEM,KAAK,EAAEA,KAAK;UAAEO,IAAI,EAAE;QAAK,CAAC,CAAC;QAC3C;MACF,KAAK,OAAO;QACVnB,KAAK,CAACO,MAAM,CAACK,KAAK,CAAC;QACnB;MACF;QACEZ,KAAK,CAACM,OAAO,CAAC;UAAEM,KAAK,EAAEA,KAAK;UAAEO,IAAI,EAAE;QAAM,CAAC,CAAC;QAC5C;IAAM;IAGVnB,KAAK,GAAGA,KAAK,CAACS,IAAI;IAClB,IAAIT,KAAK,EAAE;MACTU,MAAM,CAACV,KAAK,CAACG,GAAG,EAAEH,KAAK,CAACI,GAAG,CAAC;IAC9B,CAAC,MAAM;MACLH,IAAI,GAAG,IAAI;IACb;EACF;EAEA,IAAI,CAACsB,OAAO,GAAGrB,IAAI;EAGnB,IAAI,OAAOH,GAAG,CAACyB,MAAM,KAAK,UAAU,EAAE;IACpC,IAAI,CAACA,MAAM,GAAGC,SAAS;EACzB;AACF;AAEA3B,cAAc,CAAC4B,SAAS,CACrB,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,aAAa,IAAK,iBAAiB,CAC5E,GAAG,YAAY;EACd,OAAO,IAAI;AACb,CAAC;AAED9B,cAAc,CAAC4B,SAAS,CAACjB,IAAI,GAAG,UAAUL,GAAG,EAAE;EAC7C,OAAO,IAAI,CAACmB,OAAO,CAAC,MAAM,EAAEnB,GAAG,CAAC;AAClC,CAAC;AACDN,cAAc,CAAC4B,SAAS,CAACG,KAAK,GAAG,UAAUzB,GAAG,EAAE;EAC9C,OAAO,IAAI,CAACmB,OAAO,CAAC,OAAO,EAAEnB,GAAG,CAAC;AACnC,CAAC;AACDN,cAAc,CAAC4B,SAAS,CAACF,MAAM,GAAG,UAAUpB,GAAG,EAAE;EAC/C,OAAO,IAAI,CAACmB,OAAO,CAAC,QAAQ,EAAEnB,GAAG,CAAC;AACpC,CAAC"}