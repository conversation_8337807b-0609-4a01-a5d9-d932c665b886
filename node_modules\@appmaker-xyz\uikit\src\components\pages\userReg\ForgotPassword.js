import React, { useState } from 'react';
import { StyleSheet, KeyboardAvoidingView, Platform } from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import {
  Layout,
  Input,
  Button,
  AppmakerText,
  AppImage,
} from '../../../components';

const ForgotPassword = (props) => {
  const {
    onEmailChange,
    isSuccessSubmit,
    onEmailSubmit,
    onLogin,
    logoUrl,
    submitButtonLeft,
    submitButtonText,
  } = props;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  const [submit, setSubmit] = useState(false);

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS == 'ios' ? 'padding' : 'height'}>
      <Layout style={styles.forgotPass}>
        {logoUrl && <AppImage uri={logoUrl} style={styles.logo} />}
        {isSuccessSubmit === false ? (
          <>
            <Input
              label="Email Id"
              leftIcon="mail"
              captionIcon="info"
              caption="Enter your registered email id, You'll Receive an email with instructions to reset password."
              onChangeText={(e) => onEmailChange && onEmailChange(e)}
            />
            <Button
              status="dark"
              accessoryLeft={submitButtonLeft}
              onPress={onEmailSubmit}>
              {submitButtonText || 'Submit'}
            </Button>
          </>
        ) : (
          <>
            <AppmakerText
              category="bodyParagraph"
              style={styles.instruction}
              status="demiDark">
              Instructions to reset password has been send to your email
              Address. Please check your email.
            </AppmakerText>
            <Button status="dark" onPress={onLogin}>
              Login
            </Button>
          </>
        )}
      </Layout>
    </KeyboardAvoidingView>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      padding: spacing.base,
      flex: 1,
      alignItems: 'center',
    },
    forgotPass: {
      position: 'relative',
      width: '100%',
      flex: 1,
    },
    logo: {
      width: '50%',
      height: 80,
      alignSelf: 'center',
      resizeMode: 'contain',
      marginVertical: spacing.md,
    },
    instruction: {
      paddingVertical: spacing.lg,
      textAlign: 'center',
    },
  });

export default ForgotPassword;
