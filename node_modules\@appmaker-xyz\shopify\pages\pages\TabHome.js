import { appSettings } from '@appmaker-xyz/core';
const TabHomePage = {
  blocks: [
    {
      name: 'appmaker/tabs',
      innerBlocks: [
        {
          name: 'appmaker/tab-item',
          attributes: {
            __visibility: () => true,
            title: 'Home',
            icon: 'home',
          },
          innerBlocks: [
            {
              name: 'appmaker/actionBlock',
              attributes: {
                action: {
                  action: 'OPEN_INAPP_PAGE',
                  params: {
                    value: 'home',
                    category: appSettings.getOption('open_category_id'),
                  },
                },
              },
            },
          ],
        },
        {
          name: 'appmaker/tab-item',
          attributes: {
            __visibility: () => !appSettings.getOptionAsBoolean('hide_cart'),
            title: 'Cart',
            icon: 'shopping-cart',
          },
          innerBlocks: [
            {
              name: 'appmaker/actionBlock',
              attributes: {
                action: {
                  action: 'OPEN_INAPP_PAGE',
                  params: { value: 'cartPageV2' },
                },
              },
            },
          ],
        },
        {
          name: 'appmaker/tab-item',
          attributes: {
            __visibility: () => false, // TODO wishlist not working in tab
            // __visibility: () => displayHelper.shouldDisplayWishlist,
            title: 'Wishlist',
            icon: 'heart',
          },
          innerBlocks: [
            {
              name: 'appmaker/actionBlock',
              attributes: {
                action: {
                  action: 'OPEN_INAPP_PAGE',
                  params: { value: 'WishList' },
                },
              },
            },
          ],
        },
        // {
        //   name: 'appmaker/tab-item',
        //   attributes: {
        //     __visibility: () => displayHelper.shouldDisplayNotificationsTab,
        //     title: 'Notifications',
        //     icon: 'bell',
        //   },
        //   innerBlocks: [
        //     {
        //       name: 'appmaker/actionBlock',
        //       attributes: {
        //         action: {
        //           action: 'OPEN_INAPP_PAGE',
        //           params: { value: 'NotificationsHistory' },
        //         },
        //       },
        //     },
        //   ],
        // },
        {
          name: 'appmaker/tab-item',
          attributes: {
            __visibility: (user) => !!user?.accessToken,
            title: 'My Account',
            icon: 'user',
          },
          innerBlocks: [
            {
              name: 'appmaker/actionBlock',
              attributes: {
                action: {
                  action: 'OPEN_INAPP_PAGE',
                  params: { value: 'MyAccount' },
                },
              },
            },
          ],
        },
        {
          name: 'appmaker/tab-item',
          attributes: {
            __visibility: (user) => !user?.accessToken,
            title: 'Login',
            icon: 'user',
          },
          innerBlocks: [
            {
              name: 'appmaker/actionBlock',
              attributes: {
                action: {
                  action: 'OPEN_INAPP_PAGE',
                  params: { value: 'LoginOptions' },
                },
              },
            },
          ],
        },
      ],
      attributes: {
        tabBarPosition: 'bottom',
        swipeEnabled: false,
      },
    },
  ],
  attributes: {
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      // paddingHorizontal: 16,
    },
  },
};
export default TabHomePage;
