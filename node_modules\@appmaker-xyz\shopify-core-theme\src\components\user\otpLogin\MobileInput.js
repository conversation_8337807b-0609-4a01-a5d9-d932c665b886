import React, { useEffect, useState } from 'react';
import { MobileInputCard, OtpInputCard } from '@appmaker-xyz/ui';
import { useOtpLogin } from '@appmaker-xyz/shopify';

const MobileInput = (props) => {
  const [formattedValue, setFormattedValue] = useState('+91');
  const [textMobile, setMobileNumber] = useState('');
  const { attributes } = props;

  const {
    currentStep,
    sendCode,
    verifyCode,
    sendStatus,
    verifyErrorMessage,
    resendStatus,
    resendErrorMessage,
    resendCode,
    reset,
    failedAttemptCount,
    otpDigitCount,
    phoneDigitCount,
    autoSubmitPhone = true,
    countryCodes,
  } = useOtpLogin({
    handleAction: props?.onAction,
  });

  useEffect(() => {
    if (autoSubmitPhone && textMobile.length === Number(phoneDigitCount)) {
      onPressReceiveOtp();
    }
  }, [textMobile, autoSubmitPhone, phoneDigitCount]);

  const onPressReceiveOtp = () => {
    const checkValid = phoneDigitCount
      ? textMobile.length === Number(phoneDigitCount)
      : true;
    if (textMobile && checkValid) {
      sendCode(formattedValue, textMobile);
    } else {
      props?.onAction &&
        props?.onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: 'Please enter a valid mobile number',
          },
        });
    }
  };
  const inputLabel = attributes?.label || 'Login With OTP';
  return currentStep === 'send' || sendStatus === 'loading' ? (
    <MobileInputCard
      defaultValue={textMobile}
      onPress={onPressReceiveOtp}
      isLoading={sendStatus === 'loading'}
      onChangeText={(text) => {
        if (text.length > Number(phoneDigitCount)) {
          return;
        }
        setMobileNumber(text);
      }}
      onChangeFormattedText={(text) => {
        if (text?.length - 3 > Number(phoneDigitCount)) {
          return;
        }
        setFormattedValue(text);
      }}
      countryCodes={countryCodes}
      label={inputLabel}
    />
  ) : (
    <OtpInputCard
      verifyCode={verifyCode}
      phoneNumber={formattedValue}
      onEditNumber={reset}
      verifyErrorMessage={verifyErrorMessage}
      failedAttemptCount={failedAttemptCount}
      resendErrorMessage={resendErrorMessage}
      resendCode={resendCode}
      resendLoading={resendStatus}
      otpLength={Number(otpDigitCount)}
    />
  );
};

export default MobileInput;
