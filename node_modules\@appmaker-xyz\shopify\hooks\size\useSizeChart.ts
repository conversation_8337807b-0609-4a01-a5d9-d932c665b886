import { useEffect, useState } from 'react';
import { applyFilters } from '@appmaker-xyz/core';

type SizeChartProp = {
  product: any;
};
const useSizeChart = (props: SizeChartProp) => {
  const [sizeChartUrl, setSizeChartUrl] = useState('');

  const getSizeChartUrl = async () => {
    const Url = await applyFilters('custom-size-chart-url', null, {
      product: props?.product,
    });
    setSizeChartUrl(Url);
  };

  useEffect(() => {
    getSizeChartUrl();
  }, []);
  return {
    sizeChartUrl,
  };
};

export { useSizeChart };
