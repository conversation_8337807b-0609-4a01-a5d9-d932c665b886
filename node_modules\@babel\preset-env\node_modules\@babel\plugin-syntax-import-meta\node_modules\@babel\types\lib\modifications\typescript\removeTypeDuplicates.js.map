{"version": 3, "names": ["_generated", "require", "getQualifiedName", "node", "isIdentifier", "name", "right", "left", "removeTypeDuplicates", "nodes", "generics", "Map", "bases", "typeGroups", "Set", "types", "i", "length", "indexOf", "isTSAnyKeyword", "isTSBaseType", "set", "type", "isTSUnionType", "has", "push", "add", "isTSTypeReference", "typeParameters", "typeName", "existing", "get", "params", "concat", "baseType", "genericName"], "sources": ["../../../src/modifications/typescript/removeTypeDuplicates.ts"], "sourcesContent": ["import {\n  isIdentifier,\n  isTSAny<PERSON>eyword,\n  isTSTypeReference,\n  isTSUnionType,\n  isTSBaseType,\n} from \"../../validators/generated\";\nimport type * as t from \"../..\";\n\nfunction getQualifiedName(node: t.TSTypeReference[\"typeName\"]): string {\n  return isIdentifier(node)\n    ? node.name\n    : `${node.right.name}.${getQualifiedName(node.left)}`;\n}\n\n/**\n * Dedupe type annotations.\n */\nexport default function removeTypeDuplicates(\n  nodes: Array<t.TSType>,\n): Array<t.TSType> {\n  const generics = new Map<string, t.TSTypeReference>();\n  const bases = new Map<t.TSBaseType[\"type\"], t.TSBaseType>();\n\n  // store union type groups to circular references\n  const typeGroups = new Set<t.TSType[]>();\n\n  const types: t.TSType[] = [];\n\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if (!node) continue;\n\n    // detect duplicates\n    if (types.indexOf(node) >= 0) {\n      continue;\n    }\n\n    // this type matches anything\n    if (isTSAnyKeyword(node)) {\n      return [node];\n    }\n\n    // Analogue of FlowBaseAnnotation\n    if (isTSBaseType(node)) {\n      bases.set(node.type, node);\n      continue;\n    }\n\n    if (isTSUnionType(node)) {\n      if (!typeGroups.has(node.types)) {\n        nodes.push(...node.types);\n        typeGroups.add(node.types);\n      }\n      continue;\n    }\n\n    // todo: support merging tuples: number[]\n    if (isTSTypeReference(node) && node.typeParameters) {\n      const name = getQualifiedName(node.typeName);\n\n      if (generics.has(name)) {\n        let existing: t.TypeScript = generics.get(name);\n        if (existing.typeParameters) {\n          if (node.typeParameters) {\n            existing.typeParameters.params = removeTypeDuplicates(\n              existing.typeParameters.params.concat(node.typeParameters.params),\n            );\n          }\n        } else {\n          existing = node.typeParameters;\n        }\n      } else {\n        generics.set(name, node);\n      }\n\n      continue;\n    }\n\n    types.push(node);\n  }\n\n  // add back in bases\n  for (const [, baseType] of bases) {\n    types.push(baseType);\n  }\n\n  // add back in generics\n  for (const [, genericName] of generics) {\n    types.push(genericName);\n  }\n\n  return types;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AASA,SAASC,gBAAgBA,CAACC,IAAmC,EAAU;EACrE,OAAO,IAAAC,uBAAY,EAACD,IAAI,CAAC,GACrBA,IAAI,CAACE,IAAI,GACR,GAAEF,IAAI,CAACG,KAAK,CAACD,IAAK,IAAGH,gBAAgB,CAACC,IAAI,CAACI,IAAI,CAAE,EAAC;AACzD;AAKe,SAASC,oBAAoBA,CAC1CC,KAAsB,EACL;EACjB,MAAMC,QAAQ,GAAG,IAAIC,GAAG,EAA6B;EACrD,MAAMC,KAAK,GAAG,IAAID,GAAG,EAAsC;EAG3D,MAAME,UAAU,GAAG,IAAIC,GAAG,EAAc;EAExC,MAAMC,KAAiB,GAAG,EAAE;EAE5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,KAAK,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAMb,IAAI,GAAGM,KAAK,CAACO,CAAC,CAAC;IACrB,IAAI,CAACb,IAAI,EAAE;IAGX,IAAIY,KAAK,CAACG,OAAO,CAACf,IAAI,CAAC,IAAI,CAAC,EAAE;MAC5B;IACF;IAGA,IAAI,IAAAgB,yBAAc,EAAChB,IAAI,CAAC,EAAE;MACxB,OAAO,CAACA,IAAI,CAAC;IACf;IAGA,IAAI,IAAAiB,uBAAY,EAACjB,IAAI,CAAC,EAAE;MACtBS,KAAK,CAACS,GAAG,CAAClB,IAAI,CAACmB,IAAI,EAAEnB,IAAI,CAAC;MAC1B;IACF;IAEA,IAAI,IAAAoB,wBAAa,EAACpB,IAAI,CAAC,EAAE;MACvB,IAAI,CAACU,UAAU,CAACW,GAAG,CAACrB,IAAI,CAACY,KAAK,CAAC,EAAE;QAC/BN,KAAK,CAACgB,IAAI,CAAC,GAAGtB,IAAI,CAACY,KAAK,CAAC;QACzBF,UAAU,CAACa,GAAG,CAACvB,IAAI,CAACY,KAAK,CAAC;MAC5B;MACA;IACF;IAGA,IAAI,IAAAY,4BAAiB,EAACxB,IAAI,CAAC,IAAIA,IAAI,CAACyB,cAAc,EAAE;MAClD,MAAMvB,IAAI,GAAGH,gBAAgB,CAACC,IAAI,CAAC0B,QAAQ,CAAC;MAE5C,IAAInB,QAAQ,CAACc,GAAG,CAACnB,IAAI,CAAC,EAAE;QACtB,IAAIyB,QAAsB,GAAGpB,QAAQ,CAACqB,GAAG,CAAC1B,IAAI,CAAC;QAC/C,IAAIyB,QAAQ,CAACF,cAAc,EAAE;UAC3B,IAAIzB,IAAI,CAACyB,cAAc,EAAE;YACvBE,QAAQ,CAACF,cAAc,CAACI,MAAM,GAAGxB,oBAAoB,CACnDsB,QAAQ,CAACF,cAAc,CAACI,MAAM,CAACC,MAAM,CAAC9B,IAAI,CAACyB,cAAc,CAACI,MAAM,CAAC,CAClE;UACH;QACF,CAAC,MAAM;UACLF,QAAQ,GAAG3B,IAAI,CAACyB,cAAc;QAChC;MACF,CAAC,MAAM;QACLlB,QAAQ,CAACW,GAAG,CAAChB,IAAI,EAAEF,IAAI,CAAC;MAC1B;MAEA;IACF;IAEAY,KAAK,CAACU,IAAI,CAACtB,IAAI,CAAC;EAClB;EAGA,KAAK,MAAM,GAAG+B,QAAQ,CAAC,IAAItB,KAAK,EAAE;IAChCG,KAAK,CAACU,IAAI,CAACS,QAAQ,CAAC;EACtB;EAGA,KAAK,MAAM,GAAGC,WAAW,CAAC,IAAIzB,QAAQ,EAAE;IACtCK,KAAK,CAACU,IAAI,CAACU,WAAW,CAAC;EACzB;EAEA,OAAOpB,KAAK;AACd"}