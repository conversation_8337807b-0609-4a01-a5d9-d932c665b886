import React from 'react';
import { StyleSheet } from 'react-native';
import { AppImage, Layout, ThemeText } from '../atoms';
import { AppmakerRemoteImage } from '@appmaker-xyz/react-native';
import { Button } from '../coreComponents';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const Onboarding = ({ attributes, onPress, onAction }) => {
  const {
    bgImgSrc = '',
    logoSrc = '',
    text = ' ',
    skippable = true,
    buttonColor,
    roundedButton = false,
    appmakerAction,
    skipAction,
  } = attributes;

  const buttonBgColor = buttonColor ? buttonColor : '#1B1B1B';

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      onAction(appmakerAction);
    };
  }

  const insets = useSafeAreaInsets();

  return (
    <Layout style={styles.container}>
      <AppmakerRemoteImage
        name="SPLASH_IMAGE"
        background={true}
        resizeMode="cover"
        style={styles.bgImage}>
        {skippable ? (
          <Button
            title="Skip"
            onPress={() => {
              console.log(skipAction);
              if (skipAction) {
                onAction(skipAction);
              }
            }}
            buttonStyle={[styles.skipButton, { marginTop: insets.top }]}
            size="sm"
            textColor="#475569"
            color="#F8FAFC"
          />
        ) : null}
        {logoSrc ? (
          <AppImage src={logoSrc} style={styles.image} resizeMode="contain" />
        ) : null}
        {text ? <ThemeText style={styles.text}>{text}</ThemeText> : null}
        <Button
          title="Login"
          onPress={onPressHandle}
          activeOpacity={1}
          buttonStyle={[
            styles.button,
            { borderRadius: roundedButton ? 40 : 0 },
          ]}
          color={buttonBgColor}
        />
      </AppmakerRemoteImage>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  bgImage: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    padding: 24,
    position: 'relative',
  },
  skipButton: {
    position: 'absolute',
    right: 12,
    top: 12,
    paddingVertical: 3,
  },
  button: {
    marginTop: 16,
    width: '100%',
  },
  image: {
    width: 200,
    height: 60,
    marginBottom: 16,
  },
  text: {
    textAlign: 'center',
  },
});

export default Onboarding;
