const blocks = [
  {
    name: 'appmaker/remoteImage',
    attributes: {
      style: {
        width: 200,
        height: 100,
        alignSelf: 'center',
        marginTop: 24,
        marginBottom: 18,
        resizeMode: 'contain',
      },
      name: 'LOGIN_LOGO',
    },
  },
  {
    name: 'appmaker/email-otp-login',
    attributes: {
      content: 'Email OTP Login',
      category: 'h1Heading',
      style: {
        marginBottom: 4,
      },
    },
  },
];
const EmailOtpRequest = {
  type: 'normal',
  title: '',
  attributes: {
    renderInScroll: true,
    renderType: 'normal',
    showLoadingTillData: true,
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
      backgroundColor: '#ffffff',
    },
    contentContainerStyle: {
      flex: 1,
      justifyContent: 'center',
      paddingHorizontal: 12,
      paddingTop: 64,
    },
  },
  blocks: blocks,
  // fixedFooter: true,
};
export default EmailOtpRequest;
