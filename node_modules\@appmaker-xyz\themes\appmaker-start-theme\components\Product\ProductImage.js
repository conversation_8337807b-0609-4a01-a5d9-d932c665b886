import React from 'react';
import { StyleSheet, Image, useWindowDimensions } from 'react-native';
import { Layout } from '../../uikit/index';
import useProductImages from '@appmaker-xyz/shopify/hooks/useProductImages';
import useProductWishList from '@appmaker-xyz/shopify/hooks/useProductWishList';
import { SwiperFlatList } from 'react-native-swiper-flatlist';
import Icon from 'react-native-vector-icons/AntDesign';

function BottomRightContainer({ children }) {
  return <Layout style={styles.bottomRightContainer}>{children}</Layout>;
}

const ProductImage = (props) => {
  const {
    onAction,
    index,
    imageList,
    appmakerAction,
    customDotColor,
    imageRatio,
    shareProduct,
  } = useProductImages(props);
  const { toggleWishList, isSaved } = useProductWishList(props);
  const { width } = useWindowDimensions();
  const renderItem = ({ item }) => {
    return (
      <Image
        source={{ uri: item.uri }}
        style={[styles.image, { width: width }]}
      />
    );
  };
  return (
    <Layout style={[styles.container, { aspectRatio: 1 / imageRatio }]}>
      <SwiperFlatList
        autoplay
        autoplayDelay={2}
        autoplayLoop
        index={2}
        showPagination={true}
        paginationStyle={[
          styles.dotContainer,
          imageList.length >= 24 && styles.maxWidth,
        ]}
        paginationStyleItem={styles.dotStyle}
        paginationStyleItemInactive={styles.inactiveDotStyle}
        paginationStyleItemActive={styles.activeDotStyle}
        data={imageList}
        renderItem={renderItem}
      />
      <BottomRightContainer>
        <Icon
          name={'sharealt'}
          size={18}
          color="#0F172A"
          style={[styles.icon, styles.mb]}
          onPress={shareProduct}
        />
        <Icon
          name={isSaved ? 'heart' : 'hearto'}
          size={18}
          style={styles.icon}
          color="#0F172A"
          onPress={toggleWishList}
        />
      </BottomRightContainer>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: { position: 'relative' },
  image: { height: '100%', marginRight: 10 },
  inactiveDotStyle: {
    backgroundColor: 'rgba(15, 23, 42, 0.3)',
  },
  activeDotStyle: {
    backgroundColor: 'rgba(15, 23, 42, 0.8)',
    width: 16,
    height: 8,
    borderRadius: 6,
  },
  dotStyle: {
    backgroundColor: 'rgba(15, 23, 42, 0.8)',
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: -4,
  },
  dotContainer: {
    left: 4,
  },
  maxWidth: {
    maxWidth: '90%',
    overflow: 'hidden',
  },
  bottomRightContainer: {
    position: 'absolute',
    bottom: 16,
    right: 16,
  },
  icon: {
    padding: 10,
    backgroundColor: 'rgba(255,255,255,0.6)',
    borderRadius: 20,
  },
  mb: {
    marginBottom: 12,
  },
});

export default ProductImage;
