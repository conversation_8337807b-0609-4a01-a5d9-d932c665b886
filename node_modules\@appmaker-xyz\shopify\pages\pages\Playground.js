const onUrlChange = (url, onAction) => {
  console.log('url change callbck - ', url, onAction);
  const regexCheckout = /checkouts\/([0-9 a-z A-Z]*)\/thank_you/;
  const regexOrder = /orders\/([0-9 a-z A-Z]*)/;

  if (regexCheckout.test(url) || regexOrder.test(url)) {
    return onAction({
      action: 'ORDER_COMPLETE_REDIRECT',
    });
  }
};
const request_body =
  'attributes%5Bcollection_products_per_page%5D=&attributes%5Bcollection_layout%5D=&csrf_bold_token=f3654572aaec190e05a5475db0d95ad6&frequency_type=null&billing_plan=null&frequency_num=1&frequency_type_text=&shopify_customer_id=&email=&address1=&address2=&city=&company=&country=&first_name=Saleeh%20&last_name=&phone=&province=&zip=&pickup_enabled=0&shopify_cart%5Bnote%5D=&shopify_cart%5Bitems%5D%5B0%5D%5Bproperties%5D%5Bfrequency_num%5D=1&shopify_cart%5Bitems%5D%5B0%5D%5Bproperties%5D%5Bfrequency_type%5D=1&shopify_cart%5Bitems%5D%5B0%5D%5Bproperties%5D%5Bgroup_id%5D=157872&shopify_cart%5Bitems%5D%5B0%5D%5Bquantity%5D=1&shopify_cart%5Bitems%5D%5B0%5D%5Bvariant_id%5D=36186223181986&shopify_cart%5Bitems%5D%5B0%5D%5Btitle%5D=GuiFri%2B%26%2BSat%2B4yrs%2B%26%2Babove%2B%2811.00am%2B-12.00pm%29&shopify_cart%5Bitems%5D%5B0%5D%5Bproduct_id%5D=5723742077090&shopify_cart%5Bitems%5D%5B0%5D%5Bimage%5D=https%3A%2F%2Fcdn.shopify.com%2Fs%2Ffiles%2F1%2F0466%2F4079%2F1714%2Fproducts%2FLerio-F.-Tungul_ec89a518-be6c-4acd-8731-6329d391b17c.jpg%3Fv%3D1600686615&convertible_products=undefined';

// const getURL()
const testSource = {
  uri: 'https://de4596c158.to.intercept.rest/',
  method: 'POST',
  body: request_body,
  headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
};
const page = {
  id: 'checkout',
  status: 'active',
  title: 'Checkout',
  attributes: {
    renderType: 'normal',
    contentContainerStyle: { flex: 1 },
    rootContainerStyle: { flex: 1 },
  },
  blocks: [
    {
      clientId: 'my-account-menu',
      name: 'appmaker/actionbar',
      attributes: {
        title: 'Playground',
        appmakerAction: {
          action: 'OPEN_INAPP_PAGE',
          pageId: 'Playground',
        },
      },
    },
    {
      name: 'appmaker/sal-play',
    },
   
  ],
};
export default page;
