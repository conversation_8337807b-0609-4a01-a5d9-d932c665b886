import React, { useCallback, useState } from 'react';
import { useAddressZone } from '@appmaker-xyz/shopify';
import { ThemeText } from '@appmaker-xyz/ui';
// import DropDownPicker from 'react-native-dropdown-picker';
import ModalSelector from '../components/ModalSelector';
import { getExtensionAsBoolean, getExtensionConfig } from '@appmaker-xyz/core';
import { StyleSheet } from 'react-native';
import { Controller } from 'react-hook-form';

const RegionSelect = ({ control, labels, name }) => {
  const hide_province = getExtensionConfig(
    'address-customization',
    'disableProvince',
    false,
  );

  const useExtensionSchema = getExtensionAsBoolean(
    'address-customization',
    'use_custom_schema',
    false,
  );

  const hide_country =
    !!getExtensionConfig('address-customization', 'country', {})?.ui_disabled &&
    useExtensionSchema;

  const new_hide_province =
    !!getExtensionConfig('address-customization', 'province', {})
      ?.ui_disabled && useExtensionSchema;

  const [open, setOpen] = useState(false);
  const {
    zoneList,
    countryList,
    selectedCountry,
    selectedZone,
    zoneLabel,
    countryLabel,
    setCountry,
    setZone,
    zoneInputType,
  } = useAddressZone({
    control,
    countryName: 'country',
    zoneName: 'province',
  });
  const [countryOpen, setCountryOpen] = useState(false);
  const [cityOpen, setCityOpen] = useState(false);
  const onCountryOpen = useCallback(() => {
    setCityOpen(false);
  }, []);

  const onCityOpen = useCallback(() => {
    setCountryOpen(false);
  }, []);

  if(hide_country && useExtensionSchema) return null;

  return (
    <>
      <ThemeText size="lg" style={styles.sectionHeader}>
        Country and Province
      </ThemeText>
      {/* <DropDownPicker
        open={countryOpen}
        onOpen={onCountryOpen}
        setOpen={setCountryOpen}
        value={selectedCountry}
        items={countryList}
        setValue={setCountry}
        searchable={true}
        zIndex={2000}
        zIndexInverse={1000}
        maxHeight={height / 2}
      />
      <DropDownPicker
        open={cityOpen}
        onOpen={onCityOpen}
        setOpen={setCityOpen}
        value={selectedZone}
        items={zoneList}
        setValue={setZone}
        searchable={true}
        zIndex={1000}
        zIndexInverse={2000}
        maxHeight={height / 2}
      /> */}
      {hide_country ? null : (
        <Controller
          control={control}
          name="country"
          render={({ fieldState }) => (
            <ModalSelector
              open={countryOpen}
              onOpen={onCountryOpen}
              setOpen={setCountryOpen}
              value={selectedCountry}
              items={countryList}
              setValue={setCountry}
              label={countryLabel}
              error={fieldState?.error?.message}
            />
          )}
        />
      )}
      {!(hide_province || new_hide_province) ? (
        <Controller
          control={control}
          name="province"
          render={({ fieldState }) => (
            <ModalSelector
              open={cityOpen}
              onOpen={onCityOpen}
              setOpen={setCityOpen}
              value={selectedZone}
              items={zoneList}
              setValue={setZone}
              label={zoneLabel}
              error={fieldState?.error?.message}
            />
          )}
        />
      ) : null}
    </>
  );
};

const styles = StyleSheet.create({
  sectionHeader: {
    marginBottom: 0,
  },
});

export default RegionSelect;