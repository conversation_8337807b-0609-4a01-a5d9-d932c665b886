import { appmaker, usePageState } from '@appmaker-xyz/core';
import { useState, useMemo } from 'react';
import { appPluginStoreApi } from '@appmaker-xyz/core';
// import  from 'packages/appmaker-core/src/extend';
import { calculateCartSavings } from '../../helper/cart';
import { currencyHelper } from '../../helper/index';
import { isEmpty } from 'lodash';
import { shopifyIdHelper } from '../../helper/shopifyIdHelper';
import { useStoreDetails } from '../../hooks/useStoreDetails';
import { addCartDeliveryAddress } from '../../actions/ShopifyCartApi/mutations';
import { isCartApiEnabled, shopifyCartToLegacyCart, convertCartLineItemsToLegacyLineItems } from '../../helper/helper';
function toParseFloatOrInt(value) {
  const { display_decimals_in_cart_page } =
    appPluginStoreApi().getState().plugins?.shopify?.settings;
  try {
    if (display_decimals_in_cart_page) {
      return parseFloat(value);
    }
    return Math.round(parseFloat(value));
  } catch (error) {
    return parseInt(value, 10);
  }
}
const _addCartDeliveryAddress = async (address, setPageState) => {
  try {
    const response = await addCartDeliveryAddress(address);
    if (response?.cart?.id) {
      setPageState({
        blockData: response?.cart,
      });
    }
    return response;
  } catch (e) {}
  return null;
};

export function useCart({ onAction } = {}) {
  const isCheckoutButtonLoading = usePageState(
    (state) => state.isCheckoutButtonLoading,
  );
  const isCartApi = isCartApiEnabled();
  const [checkoutLoading, setCheckoutLoading] = useState(false);
  const cartState = usePageState((state) => state.blockData);
  const setPageState = usePageState((state) => state.setPageState);
  const cartLimitErrorMessages = appmaker.applyFilters(
    'cart-limit-validate-messages',
    [],
    {
      currentCart: isCartApi ? shopifyCartToLegacyCart(cartState) : cartState,
    },
  );
  const { storeId, storeUrl, shopDomain } = useStoreDetails();
  const setPageStateVar = usePageState((_state) => _state.setPageStateVar);
  const canCheckout = usePageState((state) => state.canCheckout);

  const lineItemsEdge = isCartApi ? cartState?.lines : cartState?.lineItems;
  const totalPrice = isCartApi
    ? cartState?.estimatedCost?.totalAmount
    : cartState?.totalPrice;
  const lineItems = isCartApi
    ? lineItemsEdge?.nodes
    : lineItemsEdge?.edges || [];
  const cartTotalSavings = toParseFloatOrInt(
    calculateCartSavings(lineItems, { includeDiscountAllocations: true }, cartState) || 0,
  );
  const cartTotalSavingWithoutDiscount = toParseFloatOrInt(
    calculateCartSavings(lineItems, { includeDiscountAllocations: false }, cartState) || 0,
  );
  const minimum_order_amount = appPluginStoreApi().getState().plugins?.shopify?.settings?.free_shipping_settings?.minimum_order_amount;
  const shipping_fee = appPluginStoreApi().getState().plugins?.shopify?.settings?.free_shipping_settings?.shipping_fee;

  const shippingFeeConfig = appmaker.applyFilters('cart-shipping-fee-config', {
    minOrderAmount: isEmpty(minimum_order_amount) ? 0 : parseFloat(minimum_order_amount),
    shippingFee: isEmpty(shipping_fee) ? 0 : parseFloat(shipping_fee),
  });

  let cartDiscountSavings = cartTotalSavings - cartTotalSavingWithoutDiscount;
  if (cartDiscountSavings > 0) {
    cartDiscountSavings = cartDiscountSavings?.toFixed(0);
  }
  const cartTotal =
    toParseFloatOrInt(totalPrice?.amount || 0) + cartTotalSavings;
  const cartDiscountSavingsWithCurrency = currencyHelper(
    cartDiscountSavings,
    totalPrice?.currencyCode,
  );
  const cartTotalSavingWithoutDiscountWithCurrency = currencyHelper(
    cartTotalSavingWithoutDiscount,
    totalPrice?.currencyCode,
  );
  const cartTotalPricelwithCurrency = currencyHelper(
    cartTotal,
    totalPrice?.currencyCode,
  );
  const openCheckout = async (extraParams) => {
    if (onAction) {
      setCheckoutLoading(true);
      try {
        await onAction({
          action: 'START_CHECKOUT',
          params: extraParams,
        });
      } catch (error) {
      } finally {
        setCheckoutLoading(false);
      }
    } else {
      console.log('onAction is not defined=>');
    }
  };
  const setCanCheckout = function (value) {
    setPageStateVar('canCheckout', value);
  };
  let totalQuantity = 0;
  if (isCartApi) {
    totalQuantity = cartState?.totalQuantity;
  } else {
    lineItems?.forEach((item) => {
      totalQuantity += item?.node?.quantity;
    });
  }
  let cartTotalPayable = toParseFloatOrInt(totalPrice?.amount || 0);
  let isFreeShipping = false;
  if (
    totalQuantity > 0 &&
    cartTotalPayable < shippingFeeConfig.minOrderAmount
  ) {
    cartTotalPayable += shippingFeeConfig.shippingFee;
  } else {
    isFreeShipping = true;
  }
  const cartTotalPayableWithCurrency = currencyHelper(
    cartTotalPayable,
    totalPrice?.currencyCode,
  );
  const shippingFee = shippingFeeConfig.shippingFee;
  const shippingFeeWithCurrency = currencyHelper(
    shippingFee,
    totalPrice?.currencyCode,
  );

  const shareCart = ({ message }) => {
    onAction({
      action: 'SHARE_CART',
      params: { message: message },
    });
  };
  const cartData = useMemo(() => {
    return isCartApi ? shopifyCartToLegacyCart(cartState) : cartState;
  }, [cartState]);
  const lineItemsLegacy = useMemo(() => {
    return isCartApi
      ? convertCartLineItemsToLegacyLineItems(lineItems)
      : lineItems;
  }, [lineItems]);
  const firstProductId = lineItems?.[0]?.merchandise?.product?.id;

  async function addCartDeliveryAddress(address) {
    return await _addCartDeliveryAddress(address, setPageState);
  }
  return {
    addCartDeliveryAddress,
    deliveryGroups: cartData?.deliveryGroups,
    discountApplications: cartData?.discountApplications,
    shareCart,
    cartErrorMessages: cartLimitErrorMessages,
    totalQuantity: totalQuantity,
    cart: cartData,
    lines: lineItems,
    lineItems: lineItemsLegacy,
    cartTotalPrice: cartTotal,
    cartTotalPricelwithCurrency,
    cartSubTotalAmount: toParseFloatOrInt(totalPrice?.amount || 0),
    cartTotalSavings,
    cartTotalSavingsWithCurrency: currencyHelper(
      cartTotalSavings,
      totalPrice?.currencyCode,
    ),
    cartTotalPayable: currencyHelper(
      totalPrice?.amount || 0,
      totalPrice?.currencyCode,
    ),
    cartTotalPayableWithCurrency,
    cartDiscountSavings,
    cartDiscountSavingsWithCurrency,
    cartTotalSavingWithoutDiscount,
    cartTotalSavingWithoutDiscountWithCurrency,
    shippingFee,
    shippingFeeWithCurrency,
    isFreeShipping,
    openCheckout,
    canCheckout,
    setCanCheckout,
    checkoutLoading,
    isCheckoutButtonLoading,
    firstProductId,
  };
}
