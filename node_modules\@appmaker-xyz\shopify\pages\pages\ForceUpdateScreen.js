import { Dimensions } from 'react-native';

const windowWidth = Dimensions.get('window').width || 360;
const windowHeight = Dimensions.get('window').height || 720;

const ForceUpdateScreen = {
  parentID: 'XSkcagmn7H',
  id: 'XSkcagmn7H',
  title: 'New Page',
  blocks: [
    {
      name: 'appmaker/remoteTouchableImage',
      attributes: {
        name: 'newversionavailable',
        appmakerAction: {
          action: 'OPEN_APP_STORE_PLAYSTORE',
        },
        style: {
          width: windowWidth,
          height: windowHeight,
          alignSelf: 'center',
          resizeMode: 'contain',
        },
      },
    },
  ],
  language: 'default',
  requiredSyncWithPlugin: false,
  attributes: {},
};
export default ForceUpdateScreen;
