import React, { useState } from 'react';
import { StyleSheet, TextInput, View, I18nManager } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { testProps } from '@appmaker-xyz/core';
import { useTranslation } from 'react-i18next';
import { color, font, spacing, fonts } from './UiKitStyles';
import { ThemeText } from '../../atoms';

/**
 * DEPRECIATED
 * DO NOT use this component anymore.
 */
const UiKitInput = (props) => {
  const { attributes = {}, pageDispatch, clientId } = props;
  const onChangeText = props?.onChangeText;
  const labelProp = props?.label;
  const typeProp = props?.type;
  const __appmakerCustomStylesProp = props?.__appmakerCustomStyles;
  const valueProp = props?.value;
  const leftIconProp = props?.leftIcon;
  const captionProp = props?.caption;
  const captionIconProp = props?.captionIcon;
  const rightIconProp = props?.rightIcon;
  const statusProp = props?.rightIcon;

  const {
    name,
    type = typeProp,
    editable,
    leftIcon = leftIconProp,
    rightIcon = rightIconProp,
    label = labelProp,
    status = statusProp,
    caption = captionProp,
    captionIcon = captionIconProp,
    value = valueProp,
    onChange = () => null,
    autoCompleteType,
    __appmakerCustomStyles = __appmakerCustomStylesProp,
  } = attributes;
  const onChangeTextDefault = (text) => {
    text = text || '';
    if (pageDispatch && name) {
      pageDispatch({
        type: 'SET_VALUE',
        name,
        value: text.trim(),
      });
    }
  };
  const { t } = useTranslation();
  const [focus, setFocus] = useState(false);
  const [hidePass, setHidePass] = useState(true);
  const mainContainerStyles = [styles.container];
  const containerStyles = [styles.inputContainer];
  const iconStyles = [styles.icon];
  const inputTextStyle = [styles.inputArea];
  const floatingLabelStyles = [styles.floatingLabel];
  let keyboard_type = 'default';
  if (type === 'number') {
    keyboard_type = 'phone-pad';
  } else if (type === 'email-address') {
    keyboard_type = 'email-address';
  }

  if (status && color[status]) {
    containerStyles.push({ borderColor: color[status] });
    iconStyles.push({ color: color[status] });
  }

  if (editable === false) {
    containerStyles.push({
      backgroundColor: color.light,
      borderColor: color.grey,
    });
    iconStyles.push({ color: color.grey });
    inputTextStyle.push({ color: color.grey });
  }

  if (I18nManager.isRTL) {
    inputTextStyle.push({ textAlign: 'right' });
  }

  if (type === 'noContainer') {
    mainContainerStyles.push({
      paddingTop: 0,
      marginBottom: 0,
    });
  }

  if (__appmakerCustomStyles) {
    mainContainerStyles.push(__appmakerCustomStyles?.main_container);
    containerStyles.push(__appmakerCustomStyles?.container);
    iconStyles.push(__appmakerCustomStyles?.icon);
    inputTextStyle.push(__appmakerCustomStyles?.input_text);
    floatingLabelStyles.push(__appmakerCustomStyles?.floating_label);
  }

  return (
    <View style={mainContainerStyles}>
      <View style={containerStyles}>
        {leftIcon && <Icon name={leftIcon} style={iconStyles} />}
        {label && focus === true && (
          <View style={floatingLabelStyles}>
            <ThemeText
              size="sm"
              color={status ? color[status] : '#1B1B1B'}
              style={__appmakerCustomStyles?.floating_label_text}>
              {label}
            </ThemeText>
          </View>
        )}
        <TextInput
          {...testProps(clientId)}
          style={inputTextStyle}
          textContentType={type}
          autoCompleteType={autoCompleteType ? autoCompleteType : 'off'}
          keyboardType={keyboard_type}
          secureTextEntry={type && type === 'password' && hidePass}
          placeholder={t(label)}
          autoFocus={false}
          autoCapitalize="none"
          editable={editable}
          value={value}
          onChangeText={onChangeText ? onChangeText : onChangeTextDefault}
          onChange={onChange}
          onFocus={() => {
            setFocus(true);
          }}
        />
        {rightIcon && <Icon name={rightIcon} style={iconStyles} />}
        {type && type === 'password' && (
          <Icon
            {...testProps('password-show')}
            name={hidePass === false ? 'eye-off' : 'eye'}
            style={iconStyles}
            onPress={() => {
              setHidePass(!hidePass);
            }}
          />
        )}
      </View>
      {caption && (
        <ThemeText
          size="xs"
          color={status ? color[status] : '#1B1B1B'}
          style={styles.caption}>
          {caption && captionIcon && <Icon name={captionIcon} />} {caption}
        </ThemeText>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: spacing.small,
    position: 'relative',
    marginBottom: spacing.md,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 50,
    backgroundColor: color.white,
    borderWidth: 1,
    borderColor: color.demiDark,
    borderRadius: spacing.nano,
    paddingHorizontal: spacing.small,
  },
  inputArea: {
    flex: 1,
    paddingHorizontal: spacing.small,
    ...fonts.bodyParagraph,
    color: color.dark,
    fontWeight: 'normal',
  },
  floatingLabel: {
    position: 'absolute',
    top: -11,
    left: 8,
    backgroundColor: color.white,
    paddingHorizontal: spacing.nano,
  },
  icon: {
    fontSize: font.size.lg,
    color: color.dark,
    padding: spacing.nano,
  },
  caption: {
    marginTop: spacing.nano,
  },
});

export default UiKitInput;
