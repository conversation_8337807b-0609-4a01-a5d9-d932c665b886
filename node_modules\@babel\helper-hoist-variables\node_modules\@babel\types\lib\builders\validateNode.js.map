{"version": 3, "names": ["_validate", "require", "_", "validateNode", "node", "keys", "BUILDER_KEYS", "type", "key", "validate"], "sources": ["../../src/builders/validateNode.ts"], "sourcesContent": ["import validate from \"../validators/validate\";\nimport type * as t from \"..\";\nimport { BUILDER_KEYS } from \"..\";\n\nexport default function validateNode<N extends t.Node>(node: N) {\n  // todo: because keys not in BUILDER_KEYS are not validated - this actually allows invalid nodes in some cases\n  const keys = BUILDER_KEYS[node.type] as (keyof N & string)[];\n  for (const key of keys) {\n    validate(node, key, node[key]);\n  }\n  return node;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AAEA,IAAAC,CAAA,GAAAD,OAAA;AAEe,SAASE,YAAYA,CAAmBC,IAAO,EAAE;EAE9D,MAAMC,IAAI,GAAGC,cAAY,CAACF,IAAI,CAACG,IAAI,CAAyB;EAC5D,KAAK,MAAMC,GAAG,IAAIH,IAAI,EAAE;IACtB,IAAAI,iBAAQ,EAACL,IAAI,EAAEI,GAAG,EAAEJ,IAAI,CAACI,GAAG,CAAC,CAAC;EAChC;EACA,OAAOJ,IAAI;AACb"}