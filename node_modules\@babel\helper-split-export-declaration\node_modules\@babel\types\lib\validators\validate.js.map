{"version": 3, "names": ["_definitions", "require", "validate", "node", "key", "val", "fields", "NODE_FIELDS", "type", "field", "validateField", "validate<PERSON><PERSON><PERSON>", "optional", "NODE_PARENT_VALIDATIONS"], "sources": ["../../src/validators/validate.ts"], "sourcesContent": ["import {\n  NODE_FIELDS,\n  NODE_PARENT_VALIDATIONS,\n  type FieldOptions,\n} from \"../definitions\";\nimport type * as t from \"..\";\n\nexport default function validate(\n  node: t.Node | undefined | null,\n  key: string,\n  val: any,\n): void {\n  if (!node) return;\n\n  const fields = NODE_FIELDS[node.type];\n  if (!fields) return;\n\n  const field = fields[key];\n  validateField(node, key, val, field);\n  validateChild(node, key, val);\n}\n\nexport function validateField(\n  node: t.Node | undefined | null,\n  key: string,\n  val: any,\n  field: FieldOptions | undefined | null,\n): void {\n  if (!field?.validate) return;\n  if (field.optional && val == null) return;\n\n  field.validate(node, key, val);\n}\n\nexport function validateChild(\n  node: t.Node | undefined | null,\n  key: string,\n  val?: t.Node | undefined | null,\n) {\n  if (val == null) return;\n  const validate = NODE_PARENT_VALIDATIONS[val.type];\n  if (!validate) return;\n  validate(node, key, val);\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAOe,SAASC,QAAQA,CAC9BC,IAA+B,EAC/BC,GAAW,EACXC,GAAQ,EACF;EACN,IAAI,CAACF,IAAI,EAAE;EAEX,MAAMG,MAAM,GAAGC,wBAAW,CAACJ,IAAI,CAACK,IAAI,CAAC;EACrC,IAAI,CAACF,MAAM,EAAE;EAEb,MAAMG,KAAK,GAAGH,MAAM,CAACF,GAAG,CAAC;EACzBM,aAAa,CAACP,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEI,KAAK,CAAC;EACpCE,aAAa,CAACR,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;AAC/B;AAEO,SAASK,aAAaA,CAC3BP,IAA+B,EAC/BC,GAAW,EACXC,GAAQ,EACRI,KAAsC,EAChC;EACN,IAAI,EAACA,KAAK,YAALA,KAAK,CAAEP,QAAQ,GAAE;EACtB,IAAIO,KAAK,CAACG,QAAQ,IAAIP,GAAG,IAAI,IAAI,EAAE;EAEnCI,KAAK,CAACP,QAAQ,CAACC,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;AAChC;AAEO,SAASM,aAAaA,CAC3BR,IAA+B,EAC/BC,GAAW,EACXC,GAA+B,EAC/B;EACA,IAAIA,GAAG,IAAI,IAAI,EAAE;EACjB,MAAMH,QAAQ,GAAGW,oCAAuB,CAACR,GAAG,CAACG,IAAI,CAAC;EAClD,IAAI,CAACN,QAAQ,EAAE;EACfA,QAAQ,CAACC,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;AAC1B"}