import React from 'react';
import {StyleSheet} from 'react-native';
import {
  Layout,
  AppTouchable,
  AppmakerText,
  AppImage,
} from '../../../components';
import {useApThemeState} from '../../../theme/ThemeContext';

const PostHeader = ({attributes, onPress, onAction}) => {
  const {
    featureImg,
    title,
    category,
    timeStamp,
    authorImg,
    authorName,
    appmakerAction,
  } = attributes;
  const {color, spacing} = useApThemeState();
  const styles = allStyles({color, spacing});

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  return (
    <Layout style={styles.container}>
      <AppImage uri={featureImg} style={styles.imgStyle} />
      <Layout style={styles.shade} />
      <Layout style={styles.content}>
        <AppmakerText
          category="pageHeading"
          status="white"
          style={styles.title}>
          {title}
        </AppmakerText>
        <AppTouchable style={styles.meta} onPress={onPressHandle}>
          <Layout style={styles.profileContainer}>
            <AppImage uri={authorImg} style={styles.authorImage} />
            <AppmakerText status="light">{authorName}</AppmakerText>
          </Layout>
          <Layout>
            <AppmakerText
              status="light"
              category="highlighter1"
              style={styles.title}>
              {category} | {timeStamp}
            </AppmakerText>
          </Layout>
        </AppTouchable>
      </Layout>
    </Layout>
  );
};

const allStyles = ({spacing, color}) =>
  StyleSheet.create({
    container: {
      width: '100%',
      minHeight: 260,
      position: 'relative',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: color.dark,
    },
    imgStyle: {
      width: '100%',
      height: '100%',
      resizeMode: 'cover',
      position: 'absolute',
    },
    shade: {
      width: '100%',
      height: '100%',
      position: 'absolute',
      backgroundColor: `${color.dark}90`,
    },
    content: {
      elevation: 5,
      paddingHorizontal: spacing.base,
      paddingVertical: spacing.xl,
    },
    title: {
      textAlign: 'center',
    },
    profileContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: spacing.base,
      padding: spacing.base,
    },
    authorImage: {
      width: spacing.xl,
      height: spacing.xl,
      borderRadius: spacing.xl,
      marginRight: spacing.base,
    },
  });

export default PostHeader;
