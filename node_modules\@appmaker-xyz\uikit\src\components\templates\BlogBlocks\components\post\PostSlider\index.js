/**
 * Post list Template View for inAppView
 */
import PropTypes from 'prop-types';
import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Swiper } from '@appmaker-xyz/uikit/Views';
import ListTemplateTwo from '../ListTemplateTwo';
// import TemplateFour from '../TemplateFour';
import TemplateFive from '../TemplateFive';
import TemplateSix from '../TemplateSix';

const { width } = Dimensions.get('window');

// import { shadeColor } from '../../../common/Utils';

const styles = StyleSheet.create({
  flatlist: {},
  listItemContainer: {
    flex: 1,
    marginVertical: 0,
    backgroundColor: '#fff',
    borderRadius: 5,
  },
  dot: {
    backgroundColor: 'rgba(0,0,0,.2)',
    width: 6,
    height: 6,
    borderRadius: 3,
    marginLeft: 2,
    marginRight: 2,
    marginTop: 2,
    marginBottom: 2,
  },
  wideIndicator: {
    backgroundColor: '#00000017',
    height: 1,
    marginRight: 7,
    width: 14,
  },
  activeWideIndicator: {
    backgroundColor: 'black',
    height: 1,
    marginRight: 7,
    width: 14,
  },
  activeDot: {
    backgroundColor: '#007aff',
    width: 6,
    height: 6,
    borderRadius: 3,
    marginLeft: 2,
    marginRight: 2,
    marginTop: 2,
    marginBottom: 2,
  },
  paginationStyle: {
    position: 'absolute',
    bottom: 10,
  },
});

/**
 * Template one for Post list item
 */

const SliderTemplate = ({ attributes, data }) => {
  console.log('slider tempalte core =- ', attributes, data);
  const listItem = data
    ? data.map((data, key) => {
        const constProps = {
          // onClick: props.onClick,
          // parentWidth: props.parentWidth,
        };

        switch (data.template) {
          case 'template-2':
            return (
              <View key={key} style={styles.listItemContainer}>
                <ListTemplateTwo
                  {...constProps}
                  attributes={attributes}
                  data={data}
                  title={data.title}
                />
              </View>
            );
          case 'template-5':
            return (
              <View key={key} style={styles.listItemContainer}>
                <TemplateFive
                  {...constProps}
                  attributes={attributes}
                  data={data}
                  title={data.title}
                />
              </View>
            );
          case 'template-6':
            return (
              <View key={key} style={styles.listItemContainer}>
                <TemplateSix
                  {...constProps}
                  attributes={attributes}
                  data={data}
                  title={data.title}
                />
              </View>
            );
          default:
            return (
              <View key={key} style={styles.listItemContainer}>
                <ListTemplateTwo
                  {...constProps}
                  attributes={attributes}
                  data={data}
                  title={data.title}
                />
              </View>
            );
        }

        // return (
        //         <View key={key} style={styles.listItemContainer}>
        //             <ListTemplateTwo
        //                 {...constProps} data={data} title={data.title} />
        //         </View>
        // );
      })
    : null;

  const templateID =
    data && data[0] ? data[0].template || 'template-2' : 'template-2';
  const imageHeight = width * 0.54;

  /**
   * Swiper needs to set static height
   * so we are comparing template and returning static height
   */
  const height =
    templateID === 'template-2'
      ? 250
      : templateID === 'template-5'
      ? imageHeight + 150
      : templateID === 'template-6'
      ? imageHeight
      : 250;
  const wideIndicator = {
    backgroundColor: templateID === 'template-5' ? '#00000017' : '#5E5E5E',
    height: 1,
    marginRight: 7,
    width: 14,
  };

  const activeWideIndicator = {
    backgroundColor: templateID === 'template-5' ? 'black' : 'white',
    height: 1,
    marginRight: 7,
    width: 14,
  };
  // console.log('Slider template id and height = ', templateID, height);
  return (
    <View style={{ flex: 1 }}>
      {data && (
        <Swiper
          style={{ height }}
          dot={<View style={wideIndicator} />}
          activeDot={<View style={activeWideIndicator} />}
          paginationStyle={styles.paginationStyle}
          autoplay={true}
          autoplayTimeout={5}
          showsButtons={false}
          loop>
          {listItem}
        </Swiper>
      )}
    </View>
  );
};
SliderTemplate.propTypes = {
  data: PropTypes.any.isRequired,
  title: PropTypes.string,
  onClick: PropTypes.func,
  parentWidth: PropTypes.number,
};

SliderTemplate.defaultProps = {};

export default SliderTemplate;
