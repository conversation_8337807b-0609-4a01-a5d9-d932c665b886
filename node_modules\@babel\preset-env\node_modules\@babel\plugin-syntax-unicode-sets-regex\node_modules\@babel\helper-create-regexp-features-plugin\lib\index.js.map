{"version": 3, "names": ["version", "split", "reduce", "v", "x", "version<PERSON>ey", "createRegExpFeaturePlugin", "name", "feature", "options", "manipulateOptions", "pre", "file", "features", "get", "<PERSON><PERSON><PERSON>", "newFeatures", "enableFeature", "FEATURES", "useUnicodeFlag", "runtime", "unicodeFlag", "set", "undefined", "has", "runtimeKey", "hasFeature", "duplicateNamedCaptureGroups", "Error", "visitor", "RegExpLiteral", "path", "node", "regexpuOptions", "generateRegexpuOptions", "pattern", "canSkipRegexpu", "namedCaptureGroups", "__proto__", "namedGroups", "onNamedGroup", "index", "prev", "Array", "isArray", "push", "newFlags", "modifiers", "onNewFlags", "flags", "rewritePattern", "Object", "keys", "length", "isRegExpTest", "call", "t", "callExpression", "addHelper", "valueToNode", "annotateAsPure", "replaceWith", "transformFlags", "parentPath", "isMemberExpression", "object", "computed", "isIdentifier"], "sources": ["../src/index.ts"], "sourcesContent": ["import rewritePattern from \"regexpu-core\";\nimport {\n  features<PERSON>ey,\n  FEATURES,\n  enableFeature,\n  runtimeKey,\n  hasFeature,\n} from \"./features\";\nimport { generateRegexpuOptions, canSkipRegexpu, transformFlags } from \"./util\";\nimport type { NodePath } from \"@babel/traverse\";\n\nimport { types as t } from \"@babel/core\";\nimport type { PluginObject } from \"@babel/core\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\n\ndeclare const PACKAGE_JSON: { name: string; version: string };\n\n// Note: Versions are represented as an integer. e.g. 7.1.5 is represented\n//       as 70000100005. This method is easier than using a semver-parsing\n//       package, but it breaks if we release x.y.z where x, y or z are\n//       greater than 99_999.\nconst version = PACKAGE_JSON.version\n  .split(\".\")\n  .reduce((v, x) => v * 1e5 + +x, 0);\nconst versionKey = \"@babel/plugin-regexp-features/version\";\n\nexport interface Options {\n  name: string;\n  feature: keyof typeof FEATURES;\n  options?: {\n    useUnicodeFlag?: boolean;\n    runtime?: boolean;\n  };\n  manipulateOptions?: PluginObject[\"manipulateOptions\"];\n}\n\nexport function createRegExpFeaturePlugin({\n  name,\n  feature,\n  options = {},\n  manipulateOptions = () => {},\n}: Options): PluginObject {\n  return {\n    name,\n\n    manipulateOptions,\n\n    pre() {\n      const { file } = this;\n      const features = file.get(featuresKey) ?? 0;\n      let newFeatures = enableFeature(features, FEATURES[feature]);\n\n      const { useUnicodeFlag, runtime } = options;\n      if (useUnicodeFlag === false) {\n        newFeatures = enableFeature(newFeatures, FEATURES.unicodeFlag);\n      }\n      if (newFeatures !== features) {\n        file.set(featuresKey, newFeatures);\n      }\n\n      if (runtime !== undefined) {\n        if (\n          file.has(runtimeKey) &&\n          file.get(runtimeKey) !== runtime &&\n          // TODO(Babel 8): Remove this check. It's necessary because in Babel 7\n          // we allow multiple copies of transform-named-capturing-groups-regex\n          // with conflicting 'runtime' options.\n          hasFeature(newFeatures, FEATURES.duplicateNamedCaptureGroups)\n        ) {\n          throw new Error(\n            `The 'runtime' option must be the same for ` +\n              `'@babel/plugin-transform-named-capturing-groups-regex' and ` +\n              `'@babel/plugin-proposal-duplicate-named-capturing-groups-regex'.`,\n          );\n        }\n        // TODO(Babel 8): Remove this check and always set it.\n        // It's necessary because in Babel 7 we allow multiple copies of\n        // transform-named-capturing-groups-regex with conflicting 'runtime' options.\n        if (feature === \"namedCaptureGroups\") {\n          if (!runtime || !file.has(runtimeKey)) file.set(runtimeKey, runtime);\n        } else {\n          file.set(runtimeKey, runtime);\n        }\n      }\n\n      if (!file.has(versionKey) || file.get(versionKey) < version) {\n        file.set(versionKey, version);\n      }\n    },\n\n    visitor: {\n      RegExpLiteral(path) {\n        const { node } = path;\n        const { file } = this;\n        const features = file.get(featuresKey);\n        const runtime = file.get(runtimeKey) ?? true;\n\n        const regexpuOptions = generateRegexpuOptions(node.pattern, features);\n        if (canSkipRegexpu(node, regexpuOptions)) {\n          return;\n        }\n\n        const namedCaptureGroups: Record<string, number | number[]> = {\n          __proto__: null,\n        };\n        if (regexpuOptions.namedGroups === \"transform\") {\n          regexpuOptions.onNamedGroup = (name, index) => {\n            const prev = namedCaptureGroups[name];\n            if (typeof prev === \"number\") {\n              namedCaptureGroups[name] = [prev, index];\n            } else if (Array.isArray(prev)) {\n              prev.push(index);\n            } else {\n              namedCaptureGroups[name] = index;\n            }\n          };\n        }\n\n        let newFlags;\n        if (regexpuOptions.modifiers === \"transform\") {\n          regexpuOptions.onNewFlags = flags => {\n            newFlags = flags;\n          };\n        }\n\n        node.pattern = rewritePattern(node.pattern, node.flags, regexpuOptions);\n\n        if (\n          regexpuOptions.namedGroups === \"transform\" &&\n          Object.keys(namedCaptureGroups).length > 0 &&\n          runtime &&\n          !isRegExpTest(path)\n        ) {\n          const call = t.callExpression(this.addHelper(\"wrapRegExp\"), [\n            node,\n            t.valueToNode(namedCaptureGroups),\n          ]);\n          annotateAsPure(call);\n\n          path.replaceWith(call);\n        }\n\n        node.flags = transformFlags(regexpuOptions, newFlags ?? node.flags);\n      },\n    },\n  };\n}\n\nfunction isRegExpTest(path: NodePath<t.RegExpLiteral>) {\n  return (\n    path.parentPath.isMemberExpression({\n      object: path.node,\n      computed: false,\n    }) && path.parentPath.get(\"property\").isIdentifier({ name: \"test\" })\n  );\n}\n"], "mappings": ";;;;;;AAAA;AACA;AAOA;AAGA;AAEA;AAQA,MAAMA,OAAO,GAAG,SACbC,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAG,GAAG,GAAG,CAACC,CAAC,EAAE,CAAC,CAAC;AACpC,MAAMC,UAAU,GAAG,uCAAuC;AAYnD,SAASC,yBAAyB,CAAC;EACxCC,IAAI;EACJC,OAAO;EACPC,OAAO,GAAG,CAAC,CAAC;EACZC,iBAAiB,GAAG,MAAM,CAAC;AACpB,CAAC,EAAgB;EACxB,OAAO;IACLH,IAAI;IAEJG,iBAAiB;IAEjBC,GAAG,GAAG;MAAA;MACJ,MAAM;QAAEC;MAAK,CAAC,GAAG,IAAI;MACrB,MAAMC,QAAQ,gBAAGD,IAAI,CAACE,GAAG,CAACC,qBAAW,CAAC,wBAAI,CAAC;MAC3C,IAAIC,WAAW,GAAG,IAAAC,uBAAa,EAACJ,QAAQ,EAAEK,kBAAQ,CAACV,OAAO,CAAC,CAAC;MAE5D,MAAM;QAAEW,cAAc;QAAEC;MAAQ,CAAC,GAAGX,OAAO;MAC3C,IAAIU,cAAc,KAAK,KAAK,EAAE;QAC5BH,WAAW,GAAG,IAAAC,uBAAa,EAACD,WAAW,EAAEE,kBAAQ,CAACG,WAAW,CAAC;MAChE;MACA,IAAIL,WAAW,KAAKH,QAAQ,EAAE;QAC5BD,IAAI,CAACU,GAAG,CAACP,qBAAW,EAAEC,WAAW,CAAC;MACpC;MAEA,IAAII,OAAO,KAAKG,SAAS,EAAE;QACzB,IACEX,IAAI,CAACY,GAAG,CAACC,oBAAU,CAAC,IACpBb,IAAI,CAACE,GAAG,CAACW,oBAAU,CAAC,KAAKL,OAAO,IAIhC,IAAAM,oBAAU,EAACV,WAAW,EAAEE,kBAAQ,CAACS,2BAA2B,CAAC,EAC7D;UACA,MAAM,IAAIC,KAAK,CACZ,4CAA2C,GACzC,6DAA4D,GAC5D,kEAAiE,CACrE;QACH;QAIA,IAAIpB,OAAO,KAAK,oBAAoB,EAAE;UACpC,IAAI,CAACY,OAAO,IAAI,CAACR,IAAI,CAACY,GAAG,CAACC,oBAAU,CAAC,EAAEb,IAAI,CAACU,GAAG,CAACG,oBAAU,EAAEL,OAAO,CAAC;QACtE,CAAC,MAAM;UACLR,IAAI,CAACU,GAAG,CAACG,oBAAU,EAAEL,OAAO,CAAC;QAC/B;MACF;MAEA,IAAI,CAACR,IAAI,CAACY,GAAG,CAACnB,UAAU,CAAC,IAAIO,IAAI,CAACE,GAAG,CAACT,UAAU,CAAC,GAAGL,OAAO,EAAE;QAC3DY,IAAI,CAACU,GAAG,CAACjB,UAAU,EAAEL,OAAO,CAAC;MAC/B;IACF,CAAC;IAED6B,OAAO,EAAE;MACPC,aAAa,CAACC,IAAI,EAAE;QAAA;QAClB,MAAM;UAAEC;QAAK,CAAC,GAAGD,IAAI;QACrB,MAAM;UAAEnB;QAAK,CAAC,GAAG,IAAI;QACrB,MAAMC,QAAQ,GAAGD,IAAI,CAACE,GAAG,CAACC,qBAAW,CAAC;QACtC,MAAMK,OAAO,iBAAGR,IAAI,CAACE,GAAG,CAACW,oBAAU,CAAC,yBAAI,IAAI;QAE5C,MAAMQ,cAAc,GAAG,IAAAC,4BAAsB,EAACF,IAAI,CAACG,OAAO,EAAEtB,QAAQ,CAAC;QACrE,IAAI,IAAAuB,oBAAc,EAACJ,IAAI,EAAEC,cAAc,CAAC,EAAE;UACxC;QACF;QAEA,MAAMI,kBAAqD,GAAG;UAC5DC,SAAS,EAAE;QACb,CAAC;QACD,IAAIL,cAAc,CAACM,WAAW,KAAK,WAAW,EAAE;UAC9CN,cAAc,CAACO,YAAY,GAAG,CAACjC,IAAI,EAAEkC,KAAK,KAAK;YAC7C,MAAMC,IAAI,GAAGL,kBAAkB,CAAC9B,IAAI,CAAC;YACrC,IAAI,OAAOmC,IAAI,KAAK,QAAQ,EAAE;cAC5BL,kBAAkB,CAAC9B,IAAI,CAAC,GAAG,CAACmC,IAAI,EAAED,KAAK,CAAC;YAC1C,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;cAC9BA,IAAI,CAACG,IAAI,CAACJ,KAAK,CAAC;YAClB,CAAC,MAAM;cACLJ,kBAAkB,CAAC9B,IAAI,CAAC,GAAGkC,KAAK;YAClC;UACF,CAAC;QACH;QAEA,IAAIK,QAAQ;QACZ,IAAIb,cAAc,CAACc,SAAS,KAAK,WAAW,EAAE;UAC5Cd,cAAc,CAACe,UAAU,GAAGC,KAAK,IAAI;YACnCH,QAAQ,GAAGG,KAAK;UAClB,CAAC;QACH;QAEAjB,IAAI,CAACG,OAAO,GAAGe,YAAc,CAAClB,IAAI,CAACG,OAAO,EAAEH,IAAI,CAACiB,KAAK,EAAEhB,cAAc,CAAC;QAEvE,IACEA,cAAc,CAACM,WAAW,KAAK,WAAW,IAC1CY,MAAM,CAACC,IAAI,CAACf,kBAAkB,CAAC,CAACgB,MAAM,GAAG,CAAC,IAC1CjC,OAAO,IACP,CAACkC,YAAY,CAACvB,IAAI,CAAC,EACnB;UACA,MAAMwB,IAAI,GAAGC,WAAC,CAACC,cAAc,CAAC,IAAI,CAACC,SAAS,CAAC,YAAY,CAAC,EAAE,CAC1D1B,IAAI,EACJwB,WAAC,CAACG,WAAW,CAACtB,kBAAkB,CAAC,CAClC,CAAC;UACF,IAAAuB,6BAAc,EAACL,IAAI,CAAC;UAEpBxB,IAAI,CAAC8B,WAAW,CAACN,IAAI,CAAC;QACxB;QAEAvB,IAAI,CAACiB,KAAK,GAAG,IAAAa,oBAAc,EAAC7B,cAAc,eAAEa,QAAQ,wBAAId,IAAI,CAACiB,KAAK,CAAC;MACrE;IACF;EACF,CAAC;AACH;AAEA,SAASK,YAAY,CAACvB,IAA+B,EAAE;EACrD,OACEA,IAAI,CAACgC,UAAU,CAACC,kBAAkB,CAAC;IACjCC,MAAM,EAAElC,IAAI,CAACC,IAAI;IACjBkC,QAAQ,EAAE;EACZ,CAAC,CAAC,IAAInC,IAAI,CAACgC,UAAU,CAACjD,GAAG,CAAC,UAAU,CAAC,CAACqD,YAAY,CAAC;IAAE5D,IAAI,EAAE;EAAO,CAAC,CAAC;AAExE"}