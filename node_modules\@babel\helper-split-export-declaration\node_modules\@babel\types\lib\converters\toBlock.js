"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = toBlock;
var _generated = require("../validators/generated");
var _generated2 = require("../builders/generated");
function toBlock(node, parent) {
  if ((0, _generated.isBlockStatement)(node)) {
    return node;
  }
  let blockNodes = [];
  if ((0, _generated.isEmptyStatement)(node)) {
    blockNodes = [];
  } else {
    if (!(0, _generated.isStatement)(node)) {
      if ((0, _generated.isFunction)(parent)) {
        node = (0, _generated2.returnStatement)(node);
      } else {
        node = (0, _generated2.expressionStatement)(node);
      }
    }
    blockNodes = [node];
  }
  return (0, _generated2.blockStatement)(blockNodes);
}

//# sourceMappingURL=toBlock.js.map
