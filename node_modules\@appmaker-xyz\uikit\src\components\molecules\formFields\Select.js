import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import Icon from 'react-native-vector-icons/Feather';
import { AppmakerText } from '../../../components';
import { usePageState } from '@appmaker-xyz/core';
import { useStyles, createStyleSheet } from '@appmaker-xyz/react-native';

// const countryByCode = (code) =>
//   data[data.findIndex((item) => item.value === code)] || {};
// const countryByLabel = (label) =>
//   data[data.findIndex((item) => item.label === label)] || {};
const Select = ({ attributes, coreDispatch }) => {
  const {
    status = 'grey',
    label = 'Label',
    leftIconName,
    name,
    selectList = [],
    selectListFn,
    setValueIfOnlyOne,
    defaultValue,
    avilableCountries,
  } = attributes;
  // console.log(selectListFn(),'selectListFn');
  const listData = selectListFn ? selectListFn() : Object.values(selectList);
  const [isFocus, setIsFocus] = useState(false);
  const parentName = attributes.parentName || '_formData';
  const inputValue = usePageState((state) => {
    return state[parentName] && state[parentName][name]
      ? state[parentName][name]
      : '';
  });
  const { styles } = useStyles(stylesheet);
  useEffect(() => {
    if (listData.length === 1 && setValueIfOnlyOne) {
      onChangeInput(listData[0].label);
    }
  }, [listData, setValueIfOnlyOne]);
  useEffect(() => {
    onChangeInput(defaultValue);
  }, [defaultValue]);
  function onChangeInput(valueText) {
    coreDispatch({
      type: 'SET_VALUE',
      name,
      value: valueText,
      parent: parentName,
    });
  }
  return (
    <View style={styles.container}>
      {/* {label && (value || isFocus) ? ( */}
      <AppmakerText
        category="highlighter2"
        status={status}
        style={styles.floatingLabel}>
        {label}
      </AppmakerText>
      {/* ) : null} */}
      <Dropdown
        style={[styles.dropdown, isFocus && { borderBottomColor: '#1b1b1b' }]}
        placeholderStyle={styles.placeholderStyle}
        selectedTextStyle={styles.selectedTextStyle}
        inputSearchStyle={styles.inputSearchStyle}
        iconStyle={styles.iconStyle}
        data={listData}
        search={true}
        maxHeight={300}
        labelField="label"
        valueField="label"
        placeholder={!isFocus ? `Select ${label}` : '...'}
        searchPlaceholder="Search..."
        value={inputValue}
        onFocus={() => setIsFocus(true)}
        onBlur={() => setIsFocus(false)}
        onChange={(item) => {
          onChangeInput(item.label);
        }}
        renderLeftIcon={() => (
          <Icon
            style={styles.icon}
            color={isFocus ? '#1B1B1B' : '#4f4f4f'}
            name={leftIconName}
            size={20}
          />
        )}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: 'white',
    marginBottom: 16,
    marginTop: 4,
    position: 'relative',
  },
  dropdown: {
    height: 50,
    borderBottomColor: '#4f4f4f',
    borderBottomWidth: 0.5,
  },
  icon: {
    marginRight: 5,
  },
  floatingLabel: {
    position: 'absolute',
    backgroundColor: 'white',
    left: 1,
    top: -6,
    zIndex: 999,
  },
  selectedTextStyle: {
    fontFamily: theme.fontFamily.regular,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontFamily: theme.fontFamily.regular,
  },
  placeholderStyle: {
    fontFamily: theme.fontFamily.regular,
  },
}));

export default Select;
