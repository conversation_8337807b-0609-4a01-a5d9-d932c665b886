import React from 'react';
import { StyleSheet, FlatList } from 'react-native';
import { AppmakerText, AppTouchable, Layout, AppImage, Badge } from '../..';
import { useApThemeState } from '../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';

const Card = ({ attributes, children, onPress, onAction }) => {
  const {
    imgSrc,
    featureImg,
    titleMeta,
    title,
    excerpt,
    desc,
    meta,
    type,
    reverse,
    appmakerAction,
    htmlExcerpt,
    stripTagsExcerpt,
    unescape = true,
    postBlock,
    status,
    restrictLines = true,
    extraText,
    strikeText,
    message,
    extraTextTwo,
  } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  const mainContainer = [styles.container];
  const cardContainerStyle = [styles.cardContainer];
  const imageContainerStyle = [styles.imgContainer];
  const contentContainerStyle = [styles.contentContainer];
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  if (status) {
    mainContainer.push({ borderColor: color[status] });
  }
  if (type === 'type-1') {
    cardContainerStyle.push({ flexDirection: 'row', padding: spacing.mini });
    imageContainerStyle.push({ width: 150, height: 150 });
    contentContainerStyle.push({ paddingVertical: 0 });
  }
  if (type === 'type-2') {
    cardContainerStyle.push({ flexDirection: 'row', padding: spacing.mini });
    imageContainerStyle.push({ width: 105, height: 105 });
    contentContainerStyle.push({ paddingVertical: 0 });
  }
  if (type === 'review') {
    cardContainerStyle.push({ flexDirection: 'row', padding: spacing.mini });
    imageContainerStyle.push({ width: 48, height: 48 });
    contentContainerStyle.push({ paddingVertical: 0 });
    mainContainer.push({ borderWidth: 0, borderBottomWidth: 1 });
  }
  if (postBlock && reverse) {
    cardContainerStyle.push({ flexDirection: 'row-reverse' });
    contentContainerStyle.push({
      paddingHorizontal: spacing.nano,
      marginRight: spacing.nano,
    });
  }

  const numLines = restrictLines ? 1 : null;

  const CardExcerpt = () => {
    if (type === 'review') {
      const ratingBadgeColor = () => {
        let rating = excerpt;
        switch (true) {
          case rating >= '3.5':
            return 'success';
          case rating >= '2.5':
            return 'warining';
          default:
            return 'dark';
        }
      };
      return (
        <Layout flexDirection="row">
          <Badge text={excerpt} iconName="star" status={ratingBadgeColor()} />
        </Layout>
      );
    } else {
      return (
        <Layout style={styles.descriptionContainer}>
          <AppmakerText
            unescape={unescape}
            html={htmlExcerpt}
            striptags={stripTagsExcerpt}
            category="smallButtonText"
            status="dark"
            numberOfLines={type === 'type-1' ? 3 : 2}>
            {`${excerpt}`}
          </AppmakerText>
          {strikeText ? (
            <AppmakerText
              unescape={unescape}
              html={htmlExcerpt}
              striptags={stripTagsExcerpt}
              style={styles.strike}
              category="bodySubText"
              status="dark"
              numberOfLines={type === 'type-1' ? 3 : 2}>
              {`${strikeText}`}
            </AppmakerText>
          ) : null}
          {desc ? (
            <AppmakerText
              unescape={unescape}
              html={htmlExcerpt}
              striptags={stripTagsExcerpt}
              category="bodySubText"
              status="demiDark"
              numberOfLines={type === 'type-1' ? 3 : 2}>
              {`${desc}`}
            </AppmakerText>
          ) : null}
        </Layout>
      );
    }
  };

  const CardImage = () => {
    if (featureImg) {
      return (
        <Layout style={imageContainerStyle}>
          <AppImage uri={featureImg} style={styles.imgStyle} />
        </Layout>
      );
    }
    if (imgSrc) {
      return (
        <Layout style={imageContainerStyle}>
          <AppImage src={imgSrc} style={styles.imgStyle} />
        </Layout>
      );
    }
    if (postBlock && !featureImg) {
      return (
        <Layout style={imageContainerStyle}>
          <Layout style={[styles.imgStyle, styles.letterCover]}>
            <AppmakerText status="grey" category="largeHeading">
              {title.charAt(0).toUpperCase()}
            </AppmakerText>
          </Layout>
        </Layout>
      );
    }
    return null;
  };

  return (
    <AppTouchable onPress={onPressHandle}>
      <Layout style={mainContainer}>
        <Layout style={cardContainerStyle}>
          <CardImage />
          <Layout style={contentContainerStyle}>
            {titleMeta ? (
              <AppmakerText category="smallButtonText" status="primary">
                {(titleMeta.toUpperCase && titleMeta.toUpperCase()) ||
                  titleMeta}
              </AppmakerText>
            ) : null}
            {title ? (
              <AppmakerText
                category="h1Heading"
                status={type === 'review' ? 'grey' : 'dark'}
                numberOfLines={type === 'type-2' ? numLines : 2}>
                {title}
              </AppmakerText>
            ) : null}
            {excerpt ? <CardExcerpt /> : null}
            {meta && (
              <Layout>
                {/* map metaItem with */}
                <FlatList
                  columnWrapperStyle={styles.metaContainer}
                  numColumns={type === 'type-1' ? 2 : 3}
                  data={meta}
                  keyExtractor={(item) => item.id}
                  renderItem={({ item }) => (
                    <Layout style={styles.metaItem}>
                      <AppmakerText category="highlighter2" status="grey">
                        <Icon name={item.iconName} /> {item.title}
                      </AppmakerText>
                    </Layout>
                  )}
                />
              </Layout>
            )}
            {extraText ? (
              <AppmakerText category="smallButtonText" status="demiDark">
                {extraText}
              </AppmakerText>
            ) : null}
            {extraTextTwo ? (
              <AppmakerText category="smallButtonText" status="demiDark">
                {extraTextTwo}
              </AppmakerText>
            ) : null}
          </Layout>
        </Layout>
        {message?.title ? (
          <Layout style={styles.messageContainerStyle}>
            <Icon
              name="info"
              size={18}
              color={color[attributes.message.status]}
              style={{
                backgroundColor: `${color[attributes.message.status]}40`,
                borderRadius: 20,
                padding: spacing.mini,
                marginRight: spacing.mini,
              }}
            />
            <AppmakerText>{attributes.message.title}</AppmakerText>
          </Layout>
        ) : null}
        {children && (
          <Layout style={styles.childrenContainer}>{children}</Layout>
        )}
      </Layout>
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    messageContainerStyle: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 10,
      backgroundColor: '#f5f5f5',
      marginHorizontal: 6,
      marginBottom: 6,
    },
    container: {
      backgroundColor: color.white,
      borderRadius: spacing.nano,
      borderWidth: 1,
      borderColor: color.light,
      marginBottom: spacing.nano,
    },
    strike: {
      textDecorationLine: 'line-through',
    },
    cardContainer: {},
    imgContainer: {
      width: '100%',
      height: 185,
    },
    imgStyle: {
      width: '100%',
      height: '100%',
      borderRadius: spacing.nano,
      resizeMode: 'cover',
    },
    letterCover: {
      backgroundColor: color.dark,
      justifyContent: 'center',
      alignItems: 'center',
    },
    contentContainer: {
      padding: spacing.base,
      flex: 1,
    },
    metaContainer: {
      flexWrap: 'wrap',
    },
    metaItem: {
      marginRight: spacing.lg,
      marginTop: spacing.small,
    },
    childrenContainer: {
      paddingHorizontal: spacing.mini,
      paddingBottom: spacing.mini,
    },
    descriptionContainer: {
      flex: 1,
    },
  });

export default Card;
