import { simpleLocalStore as store } from '@appmaker-xyz/react-native';
import { runDataSource } from '@appmaker-xyz/core';
class WishlistLocalStorage {
  constructor(config) {
    this.config = config;
    this.key = 'wishlist-v2'; // config.key;

    // this.saveLimit = parseInt(config.saveLimit) || 5;
  }
  async getAllItemsIds() {
    const items = (await store.get(this.key)) || {};
    return items;
  }
  async saveItem({ key = 'default', id }) {
    try {
      let allData = await this.getAllItemsIds();
      //node 15
      // allData[key] ??= { products: [] };
      if (!allData[key]) {
        allData[key] = { products: [] };
      }
      let isExist = allData[key]?.products?.some((p) => p === id);
      !isExist && allData[key]?.products?.push(id);
      await store.save(this.key, allData);
      return allData;
    } catch (e) {
      console.log('wishlist something went wrong');
    }
  }
  async removeAllItems() {
    try {
      await store.save(this.key, {});
      return true;
    } catch (e) {}
  }
  async removeItem({ id, key = 'default' }) {
    let allItems = await this.getAllItemsIds();
    if (allItems[key]?.products?.length > 0) {
      allItems[key].products = allItems[key].products.filter((i) => i !== id);
      await store.save(this.key, allItems);
    }
    return allItems;
  }
  async getAllProducts({ key = 'default' }) {
    try {
      let items = await this.getAllItemsIds();
      if (items[key]?.products?.length > 0) {
        items = items[key]?.products?.filter((i) => !!i);
        const [response] = await runDataSource(
          {
            dataSource: {
              source: 'shopify',
              attributes: {},
            },
          },
          {
            methodName: 'products',
            params: {
              ids: items,
            },
          },
        );
        if (response?.status == 200 && response?.data?.data) {
          return response;
          // const data = response.data.data.map((item) => ({
          //   node: item,
          // }));
          // console.log('before return - ', data);
          // return data;
        }
        throw new Error(response);
      }
    } catch (e) {
      console.log(' ASW something went wrong in datasource -- ', e);
    }
  }
}

export default WishlistLocalStorage;
