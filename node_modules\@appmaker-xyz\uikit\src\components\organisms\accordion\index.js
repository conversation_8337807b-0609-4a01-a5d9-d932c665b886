import React, { useState } from 'react';
import {
  StyleSheet,
  LayoutAnimation,
  Platform,
  UIManager,
  ActivityIndicator,
} from 'react-native';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { AppTouchable, Layout, AppmakerText } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';

const Accordion = ({ attributes }) => {
  const { data, title, loading, type, filter } = attributes;
  const { color, font, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  const [expand, setExpand] = useState(false);
  if (Platform.OS === 'android') {
    if (UIManager.setLayoutAnimationEnabledExperimental) {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }
  }
  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpand(!expand);
  };

  return (
    <Layout style={styles.container}>
      <AppTouchable style={styles.row} onPress={toggleExpand}>
        <AppmakerText
          category="actionTitle"
          style={filter ? styles.accordionTitle : null}>
          {title}
        </AppmakerText>
        {loading ? (
          <ActivityIndicator color={color.dark} />
        ) : (
          <Icon
            name={expand ? 'minus' : 'plus'}
            size={font.size.lg}
            color={color.dark}
          />
        )}
      </AppTouchable>
      {expand && (
        <Layout style={styles.child}>
          {typeof data === 'string' ? (
            <AppmakerText
              html={type && type == 'html' ? true : false}
              category="bodyParagraph">
              {data}
            </AppmakerText>
          ) : (
            <Layout>{data}</Layout>
          )}
        </Layout>
      )}
    </Layout>
  );
};

const allStyles = ({ color, spacing }) =>
  StyleSheet.create({
    container: {
      marginBottom: spacing.nano,
      borderBottomColor: color.light,
      borderBottomWidth: 1,
    },
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      backgroundColor: color.white,
      paddingHorizontal: spacing.small,
      paddingTop: spacing.small,
      paddingBottom: spacing.base,
    },
    accordionTitle: { fontSize: 17 },
    child: {
      padding: spacing.small,
      backgroundColor: color.white,
    },
  });

export default Accordion;
