import React from 'react';
import { Card } from '../../../../components';

const PostCard = ({ attributes, onAction, ...props }) => {
  console.log(attributes.excerpt);
  const { authorName, timeStamp, appmakerAction } = attributes;
  const action =
    appmakerAction && onAction
      ? { onPress: () => onAction(appmakerAction) }
      : {};
  return (
    <Card
      attributes={{
        meta: [
          authorName && { iconName: 'user', title: authorName },
          // timeStamp && { iconName: 'clock', title: timeStamp },
        ],
        ...attributes,
      }}
      {...action}
      {...props}
    />
  );
  // return null;
};

export default PostCard;
