{"version": 3, "names": ["mergeOptions", "target", "source", "k", "Object", "keys", "parserOpts", "targetObj", "mergeDefaultFields", "val", "undefined", "isIterableIterator", "value", "next", "Symbol", "iterator"], "sources": ["../../src/config/util.ts"], "sourcesContent": ["import type {\n  ValidatedOptions,\n  NormalizedOptions,\n} from \"./validation/options.ts\";\n\nexport function mergeOptions(\n  target: ValidatedOptions,\n  source: ValidatedOptions | NormalizedOptions,\n): void {\n  for (const k of Object.keys(source)) {\n    if (\n      (k === \"parserOpts\" || k === \"generatorOpts\" || k === \"assumptions\") &&\n      source[k]\n    ) {\n      const parserOpts = source[k];\n      const targetObj = target[k] || (target[k] = {});\n      mergeDefaultFields(targetObj, parserOpts);\n    } else {\n      //@ts-expect-error k must index source\n      const val = source[k];\n      //@ts-expect-error assigning source to target\n      if (val !== undefined) target[k] = val as any;\n    }\n  }\n}\n\nfunction mergeDefaultFields<T extends {}>(target: T, source: T) {\n  for (const k of Object.keys(source) as (keyof T)[]) {\n    const val = source[k];\n    if (val !== undefined) target[k] = val;\n  }\n}\n\nexport function isIterableIterator(value: any): value is IterableIterator<any> {\n  return (\n    !!value &&\n    typeof value.next === \"function\" &&\n    typeof value[Symbol.iterator] === \"function\"\n  );\n}\n"], "mappings": ";;;;;;;AAKO,SAASA,YAAYA,CAC1BC,MAAwB,EACxBC,MAA4C,EACtC;EACN,KAAK,MAAMC,CAAC,IAAIC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,EAAE;IACnC,IACE,CAACC,CAAC,KAAK,YAAY,IAAIA,CAAC,KAAK,eAAe,IAAIA,CAAC,KAAK,aAAa,KACnED,MAAM,CAACC,CAAC,CAAC,EACT;MACA,MAAMG,UAAU,GAAGJ,MAAM,CAACC,CAAC,CAAC;MAC5B,MAAMI,SAAS,GAAGN,MAAM,CAACE,CAAC,CAAC,KAAKF,MAAM,CAACE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;MAC/CK,kBAAkB,CAACD,SAAS,EAAED,UAAU,CAAC;IAC3C,CAAC,MAAM;MAEL,MAAMG,GAAG,GAAGP,MAAM,CAACC,CAAC,CAAC;MAErB,IAAIM,GAAG,KAAKC,SAAS,EAAET,MAAM,CAACE,CAAC,CAAC,GAAGM,GAAU;IAC/C;EACF;AACF;AAEA,SAASD,kBAAkBA,CAAeP,MAAS,EAAEC,MAAS,EAAE;EAC9D,KAAK,MAAMC,CAAC,IAAIC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,EAAiB;IAClD,MAAMO,GAAG,GAAGP,MAAM,CAACC,CAAC,CAAC;IACrB,IAAIM,GAAG,KAAKC,SAAS,EAAET,MAAM,CAACE,CAAC,CAAC,GAAGM,GAAG;EACxC;AACF;AAEO,SAASE,kBAAkBA,CAACC,KAAU,EAAkC;EAC7E,OACE,CAAC,CAACA,KAAK,IACP,OAAOA,KAAK,CAACC,IAAI,KAAK,UAAU,IAChC,OAAOD,KAAK,CAACE,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU;AAEhD;AAAC"}