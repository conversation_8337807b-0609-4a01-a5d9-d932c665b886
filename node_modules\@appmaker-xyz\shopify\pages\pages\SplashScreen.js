import { appSettings } from '@appmaker-xyz/core';

const page = {
  attributes: {
    headerShown: false,
  },
  blocks: [
    {
      name: 'appmaker/splashScreen',
      clientId: 'splash-image',
      attributes: {
        appmakerAction: {
          action: 'OPEN_INITIAL_INAPPPAGE',
          params: { replacePage: true },
        },
        homeAction: { action: 'OPEN_HOME', params: { replacePage: true } },
        // timeout: constants.SPLASH_SCREEN_TIMEOUT,
        timeout: appSettings.getOption('splash_screen_timeout'),
      },
    },
  ],

  title: '',
};
export default page;
