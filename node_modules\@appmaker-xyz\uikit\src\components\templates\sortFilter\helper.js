export function converToShopifyParams(appliedFilter, avilableFilters) {
  const avilableFiltersObject = avilableFilters.reduce((acc, item) => {
    acc[item.id] = item;
    return acc;
  }, {});
  //   console.log(avilableFiltersObject);
  //   console.log(appliedFilter);
  const filterKeys = Object.keys(appliedFilter);
  //   console.log(filterKeys);
  const filterParam = [];
  filterKeys.forEach((key) => {
    const value = appliedFilter[key];
    const currentFilter = avilableFiltersObject[key];
    // console.log(currentFilter.type, key);
    if (currentFilter.type === 'PRICE_RANGE') {
      //   return {};
      filterParam.push({ price: value });
    }
    if (currentFilter.type === 'LIST') {
      // filterParam.push(value)
      const filterValueKeys = Object.keys(value);
      filterValueKeys.forEach((filterValueKey) => {
        const filterValue = value[filterValueKey];
        if (filterValue.state) {
          try {
            filterParam.push(JSON.parse(filterValue.value));
          } catch (error) {}
        }
      });
    }
  });
  return filterParam;
}
