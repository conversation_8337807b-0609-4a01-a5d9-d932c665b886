/* eslint-disable react-native/no-inline-styles */
import { View, ActivityIndicator } from 'react-native';
import React, { useEffect, useState } from 'react';
import { ActionBar } from '@appmaker-xyz/ui';
import { usePageState } from '@appmaker-xyz/core';
import { styles } from '@appmaker-xyz/shopify';
const { color, spacing } = styles;

export default function GoogleLoginComponent({ attributes, onAction }) {
  const googleLoginLoading = usePageState((state) => state.googleLoginLoading);
  const [buttonLoading, setButtonLoading] = useState(false);
  useEffect(() => {
    setButtonLoading(googleLoginLoading);
  }, [googleLoginLoading]);
  return (
    <ActionBar
      onPress={() => {
        onAction({
          action: 'LOGIN_USER_VIA_GOOGLE',
        });
      }}
      attributes={{
        featureImg:
          'https://storage.googleapis.com/site-cdn.appmaker.xyz/2022/06/d45ea601-image-5.png',
        imageResize: 'contain',
        imageSize: 28,
        title: 'Continue with Google',
        loading: buttonLoading,
        appmakerAction: {
          action: 'LOGIN_USER_VIA_GOOGLE',
        },
        fontColor: '#ffffff',
        containerStyle: {
          backgroundColor: '#0099FF',
          marginHorizontal: spacing.base,
          marginBottom: spacing.mini,
          borderRadius: 0,
        },
      }}
    />
  );
}
