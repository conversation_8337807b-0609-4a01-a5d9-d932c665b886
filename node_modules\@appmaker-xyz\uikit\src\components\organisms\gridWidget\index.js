import React from 'react';
import ProductGridWidget from './ProductGridWidget';
import ProductGridTwo from './ProductGridTwo';
import { appmaker } from '@appmaker-xyz/core';
import ProductGridThree from './ProductGridThree';

const ProductGridBlock = ({ attributes, ...props }) => {
  const productListDesign = appmaker.applyFilters(
    'product-list-type',
    'type-1',
  );
  // console.log(productListDesign, 'shopifyConfig--');

  if (productListDesign === 'type-2') {
    return <ProductGridTwo attributes={attributes} {...props} />;
  }
  if (productListDesign === 'type-3') {
    return <ProductGridThree attributes={attributes} {...props} />;
  }
  return <ProductGridWidget attributes={attributes} {...props} />;
};

export default ProductGridBlock;
