import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  I18nManager,
  ActivityIndicator,
  LayoutAnimation,
  Platform,
  UIManager,
} from 'react-native';
import {
  Layout,
  AppmakerText,
  AppTouchable,
  AppImage,
  Confirm,
} from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { testProps } from '@appmaker-xyz/core';

const ActionBar = ({ attributes, onPress, onAction, clientId }) => {
  const {
    featureImg,
    imageResize = 'cover',
    leftIcon,
    title,
    subTitle,
    rightIcon,
    drawerItem,
    type,
    appmakerAction,
    text,
    loading = false,
    image,
    noMargin = false,
    containerStyle,
    fontColor,
  } = attributes;
  const [confirm, setConfirm] = useState(false);
  const { color, spacing, fonts, font } = useApThemeState();
  const styles = allStyles({
    color,
    spacing,
    fonts,
    font,
    imageResize,
    drawerItem,
    noMargin,
  });
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      onAction(appmakerAction);
    };
  }
  if (Platform.OS === 'android') {
    if (UIManager.setLayoutAnimationEnabledExperimental) {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }
  }
  const toggleConfirm = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setConfirm(!confirm);
  };
  const finalText = title || text;
  const finalFeatureImg = featureImg || (image && image.url);
  const containerStyles = [styles.container];
  if (type === 'title') {
    containerStyles.push(styles.topBorder);
  }
  if (containerStyle) {
    containerStyles.push(containerStyle);
  }
  return (
    <Layout style={styles.wholeContainer}>
      <AppTouchable
        onPress={onPressHandle}
        clientId={clientId}
        disabled={loading ? true : false}>
        <View style={containerStyles}>
          <Layout style={styles.titleContainer}>
            {leftIcon && (
              <Icon
                name={leftIcon}
                size={font.size.lg}
                color={loading ? color.grey : color.demiDark}
                style={{ marginRight: spacing.base }}
              />
            )}
            {finalFeatureImg ? (
              <Layout style={styles.leftImage}>
                <AppImage
                  uri={finalFeatureImg}
                  style={styles.image}
                  resizeMode="contain"
                />
              </Layout>
            ) : null}
            <Layout flex={1}>
              <AppmakerText
                {...testProps(`${clientId}-title`)}
                category={drawerItem ? 'pageSubHeading' : 'actionTitle'}
                status={loading ? 'grey' : 'dark'}
                // status={type === 'title' ? 'grey' : 'dark'}
                fontColor={fontColor ? fontColor : null}
                numberOfLines={1}>
                {finalText}
              </AppmakerText>
              {subTitle ? (
                <AppmakerText category="highlighter1" status="grey">
                  {subTitle}
                </AppmakerText>
              ) : null}
            </Layout>
          </Layout>
          {loading && (
            <ActivityIndicator
              size="small"
              color={fontColor ? fontColor : color.demiDark}
            />
          )}
          {type !== 'title' && !loading && (
            <Icon
              name={
                rightIcon
                  ? rightIcon
                  : `chevron-${I18nManager.isRTL ? 'left' : 'right'}`
              }
              size={font.size.lg}
              color={fontColor ? fontColor : color.dark}
            />
          )}
        </View>
      </AppTouchable>
      {/* {confirm ? (
        <Confirm
          attributes={{
            title: 'Are you sure?',
            confirmText: 'OK',
            denyText: 'Dont do it',
          }}
        />
      ) : null} */}
    </Layout>
  );
};

const allStyles = ({ spacing, color, imageResize, drawerItem, noMargin }) =>
  StyleSheet.create({
    wholeContainer: {
      marginBottom: noMargin ? 0 : spacing.nano,
    },
    container: {
      flexDirection: 'row',
      padding: drawerItem ? spacing.small : spacing.base,
      justifyContent: 'space-between',
      backgroundColor: color.white,
      alignItems: 'center',
      borderRadius: spacing.nano,
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    leftImage: {
      width: 30,
      height: 30,
      marginRight: spacing.base,
    },
    image: {
      width: '100%',
      height: '100%',
      resizeMode: imageResize,
    },
    topBorder: {
      borderTopColor: color.light,
      borderTopWidth: 1,
    },
    confirmContainer: {
      paddingHorizontal: spacing.base,
      paddingVertical: spacing.base,
      backgroundColor: color.white,
      borderTopWidth: 0.5,
      borderTopColor: color.gray,
    },
    confirm: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: spacing.small,
    },
    confirmButtons: {
      flex: 1,
    },
    mr: {
      marginRight: spacing.base,
    },
  });

export default ActionBar;
