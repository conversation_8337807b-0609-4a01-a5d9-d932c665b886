{"version": 3, "names": ["_index", "require", "_index2", "isLet", "node", "isVariableDeclaration", "kind", "BLOCK_SCOPED_SYMBOL"], "sources": ["../../src/validators/isLet.ts"], "sourcesContent": ["import { isVariableDeclaration } from \"./generated/index.ts\";\nimport { BLOCK_SCOPED_SYMBOL } from \"../constants/index.ts\";\nimport type * as t from \"../index.ts\";\n\n/**\n * Check if the input `node` is a `let` variable declaration.\n */\nexport default function isLet(node: t.Node): boolean {\n  return (\n    isVariableDeclaration(node) &&\n    (node.kind !== \"var\" ||\n      // @ts-expect-error Fixme: document private properties\n      node[BLOCK_SCOPED_SYMBOL])\n  );\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAMe,SAASE,KAAKA,CAACC,IAAY,EAAW;EACnD,OACE,IAAAC,4BAAqB,EAACD,IAAI,CAAC,KAC1BA,IAAI,CAACE,IAAI,KAAK,KAAK,IAElBF,IAAI,CAACG,2BAAmB,CAAC,CAAC;AAEhC"}