import React, { useState } from 'react';
import { ImageBackground, StyleSheet, Dimensions, Pressable } from 'react-native';
import { Layout, ThemeText } from '@appmaker-xyz/ui';
import { CountdownTimer } from './index';

const CountDownTimerBlock = ({ attributes, onPress, onAction }) => {
  const {
    date,
    expiredText,
    dangerBgColor,
    dangerTextColor,
    bgColor,
    textColor,
    title,
    titleColor,
    backgroundColor,
    featureImg,
    stacked,
    appmakerAction,
  } = attributes;
  const [hide, setHide] = useState(false);
  const image = { uri: featureImg?.url };
  const ratio = featureImg?.width / featureImg?.height;
  const fullWidth = Dimensions.get('window').width;
  const blockHeight = fullWidth / ratio;
  const finalHeight = blockHeight <= 110 ? 110 : blockHeight;

  const onEndTimer = () => {
    setHide(true);
  };
  if (hide) {
    return null;
  }
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  return (
    <Pressable onPress={onPressHandle}>
      <Layout
        style={[styles.container, { height: featureImg ? finalHeight : null }]}>
        <ImageBackground
          source={image}
          resizeMode="cover"
          style={[
            styles.image,
            {
              backgroundColor: backgroundColor,
            },
          ]}>
          {title ? (
            <ThemeText
              size="lg"
              fontFamily="medium"
              style={styles.text}
              color={titleColor}>
              {title}
            </ThemeText>
          ) : null}
          <CountdownTimer
            attributes={{
              targetDate: date,
              onEndTimer,
              expiredText: expiredText,
              dangerBgColor: dangerBgColor,
              dangerTextColor: dangerTextColor,
              bgColor: bgColor,
              textColor: textColor,
              size: 'lg',
              stacked: stacked,
            }}
          />
        </ImageBackground>
      </Layout>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  image: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
  },
  text: {
    marginBottom: 12,
    textAlign: 'center',
  },
});

export default CountDownTimerBlock;
