import { useEffect } from 'react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

export function useProductHook({
  attributes,
  onAction,
  blockData,
  pageDispatch,
}) {
  const {
    savedItemIds = {},
    id,
    productType,
    cartQuantity,
    cartCountMaps = {},
    appmakerAction,
  } = attributes;
  const saved = savedItemIds[id] === true;
  const [addwish, setAddwish] = useState(saved);
  const quatity = cartCountMaps[id] || cartQuantity || 0;
  const [addingTocartLoading, setCartLoading] = useState(false);
  const [quantityLoading, setQuantityLoading] = useState(false);
  const [currentQuantity, setCurrentQuantity] = useState(quatity);
  const { t } = useTranslation();
  useEffect(() => {
    setCurrentQuantity(quatity);
  }, [quatity]);
  const onAddtoCart = async ({ quantity }) => {
    if (productType === 'variable') {
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'Please select a product variant',
        },
      });
      return onAction(appmakerAction);
    }
    setCartLoading(true);
    try {
      // if(count > quantity) {}
      let params =
        currentQuantity < quantity
          ? {
              action: 'ADD_TO_CART',
              params: {
                product: blockData,
                quantity: 1,
                fromList: true,
                // product: data,
              },
            }
          : {
              action: 'UPDATE_CART_QUANTITY',
              params: {
                product: blockData,
                quantity,
                fromList: true,
                // product: data,
              },
            };
      const cartResp = await onAction(params);
      if (cartResp?.status === 'fail') {
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: cartResp?.message,
          },
        });
      } else {
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: t('Cart updated'),
            buttonTitle: t('View Cart'),
            buttonAction: {
              action: 'OPEN_CART',
            },
          },
        });
        pageDispatch({
          type: 'set_value',
          name: 'cartResponse',
          value: cartResp,
        });
        setCartLoading(false);
        setCurrentQuantity(quantity);
      }
    } catch (e) {
      console.log(e);
    }
  };
  const onSaved = (saved) => {
    let action;
    if (saved) {
      action = 'SAVE_ITEM';
    } else {
      action = 'REMOVE_ITEM';
    }
    onAction({ action, params: { item: blockData, id } });
  };
  const onQuantityChange = async (quantity) => {
    setQuantityLoading(true);
    await onAddtoCart({ quantity });
    setQuantityLoading(false);
  };
  useEffect(() => {
    setAddwish(saved);
  }, [saved]);
  return {
    onAddtoCart,
    onSaved,
    onQuantityChange,
    setCurrentQuantity,
    currentQuantity,
    addingTocartLoading,
    quantityLoading,
    saved,
    addwish,
    setAddwish,
    quatity,
  };
}
