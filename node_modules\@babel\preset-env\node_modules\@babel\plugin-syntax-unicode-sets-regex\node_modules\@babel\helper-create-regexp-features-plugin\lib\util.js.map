{"version": 3, "names": ["generateRegexpuOptions", "pattern", "toTransform", "feat", "name", "ok", "hasFeature", "FEATURES", "featDuplicateNamedGroups", "regex", "seen", "Set", "match", "exec", "add", "has", "unicodeFlag", "unicodeSetsFlag", "dotAllFlag", "unicodePropertyEscapes", "namedGroups", "onNamedGroup", "modifiers", "canSkipRegexpu", "node", "options", "flags", "includes", "test", "transformFlags", "regexpuOptions", "replace"], "sources": ["../src/util.ts"], "sourcesContent": ["import type { types as t } from \"@babel/core\";\nimport { FEATURES, hasFeature } from \"./features\";\n\nimport type { RegexpuOptions } from \"regexpu-core\";\n\nexport function generateRegexpuOptions(\n  pattern: string,\n  toTransform: number,\n): RegexpuOptions {\n  type Experimental = 1;\n\n  const feat = <Stability extends 0 | 1 = 0>(\n    name: keyof typeof FEATURES,\n    ok: \"transform\" | (Stability extends 0 ? never : \"parse\") = \"transform\",\n  ) => {\n    return hasFeature(toTransform, FEATURES[name]) ? ok : false;\n  };\n\n  const featDuplicateNamedGroups = (): \"transform\" | false => {\n    if (!feat(\"duplicateNamedCaptureGroups\")) return false;\n\n    // This can return false positive, for example for /\\(?<a>\\)/.\n    // However, it's such a rare occurrence that it's ok to compile\n    // the regexp even if we only need to compile regexps with\n    // duplicate named capturing groups.\n    const regex = /\\(\\?<([^>]+)>/g;\n    const seen = new Set();\n    for (let match; (match = regex.exec(pattern)); seen.add(match[1])) {\n      if (seen.has(match[1])) return \"transform\";\n    }\n    return false;\n  };\n\n  return {\n    unicodeFlag: feat(\"unicodeFlag\"),\n    unicodeSetsFlag:\n      feat<Experimental>(\"unicodeSetsFlag\") ||\n      feat<Experimental>(\"unicodeSetsFlag_syntax\", \"parse\"),\n    dotAllFlag: feat(\"dotAllFlag\"),\n    unicodePropertyEscapes: feat(\"unicodePropertyEscape\"),\n    namedGroups: feat(\"namedCaptureGroups\") || featDuplicateNamedGroups(),\n    onNamedGroup: () => {},\n    modifiers: feat(\"modifiers\"),\n  };\n}\n\nexport function canSkipRegexpu(\n  node: t.RegExpLiteral,\n  options: RegexpuOptions,\n): boolean {\n  const { flags, pattern } = node;\n\n  if (flags.includes(\"v\")) {\n    if (options.unicodeSetsFlag === \"transform\") return false;\n  }\n\n  if (flags.includes(\"u\")) {\n    if (options.unicodeFlag === \"transform\") return false;\n    if (\n      options.unicodePropertyEscapes === \"transform\" &&\n      /\\\\[pP]{/.test(pattern)\n    ) {\n      return false;\n    }\n  }\n\n  if (flags.includes(\"s\")) {\n    if (options.dotAllFlag === \"transform\") return false;\n  }\n\n  if (options.namedGroups === \"transform\" && /\\(\\?<(?![=!])/.test(pattern)) {\n    return false;\n  }\n\n  if (options.modifiers === \"transform\" && /\\(\\?[\\w-]+:/.test(pattern)) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function transformFlags(regexpuOptions: RegexpuOptions, flags: string) {\n  if (regexpuOptions.unicodeSetsFlag === \"transform\") {\n    flags = flags.replace(\"v\", \"u\");\n  }\n  if (regexpuOptions.unicodeFlag === \"transform\") {\n    flags = flags.replace(\"u\", \"\");\n  }\n  if (regexpuOptions.dotAllFlag === \"transform\") {\n    flags = flags.replace(\"s\", \"\");\n  }\n  return flags;\n}\n"], "mappings": ";;;;;;;;AACA;AAIO,SAASA,sBAAsB,CACpCC,OAAe,EACfC,WAAmB,EACH;EAGhB,MAAMC,IAAI,GAAG,CACXC,IAA2B,EAC3BC,EAAyD,GAAG,WAAW,KACpE;IACH,OAAO,IAAAC,oBAAU,EAACJ,WAAW,EAAEK,kBAAQ,CAACH,IAAI,CAAC,CAAC,GAAGC,EAAE,GAAG,KAAK;EAC7D,CAAC;EAED,MAAMG,wBAAwB,GAAG,MAA2B;IAC1D,IAAI,CAACL,IAAI,CAAC,6BAA6B,CAAC,EAAE,OAAO,KAAK;IAMtD,MAAMM,KAAK,GAAG,gBAAgB;IAC9B,MAAMC,IAAI,GAAG,IAAIC,GAAG,EAAE;IACtB,KAAK,IAAIC,KAAK,EAAGA,KAAK,GAAGH,KAAK,CAACI,IAAI,CAACZ,OAAO,CAAC,EAAGS,IAAI,CAACI,GAAG,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MACjE,IAAIF,IAAI,CAACK,GAAG,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,WAAW;IAC5C;IACA,OAAO,KAAK;EACd,CAAC;EAED,OAAO;IACLI,WAAW,EAAEb,IAAI,CAAC,aAAa,CAAC;IAChCc,eAAe,EACbd,IAAI,CAAe,iBAAiB,CAAC,IACrCA,IAAI,CAAe,wBAAwB,EAAE,OAAO,CAAC;IACvDe,UAAU,EAAEf,IAAI,CAAC,YAAY,CAAC;IAC9BgB,sBAAsB,EAAEhB,IAAI,CAAC,uBAAuB,CAAC;IACrDiB,WAAW,EAAEjB,IAAI,CAAC,oBAAoB,CAAC,IAAIK,wBAAwB,EAAE;IACrEa,YAAY,EAAE,MAAM,CAAC,CAAC;IACtBC,SAAS,EAAEnB,IAAI,CAAC,WAAW;EAC7B,CAAC;AACH;AAEO,SAASoB,cAAc,CAC5BC,IAAqB,EACrBC,OAAuB,EACd;EACT,MAAM;IAAEC,KAAK;IAAEzB;EAAQ,CAAC,GAAGuB,IAAI;EAE/B,IAAIE,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACvB,IAAIF,OAAO,CAACR,eAAe,KAAK,WAAW,EAAE,OAAO,KAAK;EAC3D;EAEA,IAAIS,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACvB,IAAIF,OAAO,CAACT,WAAW,KAAK,WAAW,EAAE,OAAO,KAAK;IACrD,IACES,OAAO,CAACN,sBAAsB,KAAK,WAAW,IAC9C,SAAS,CAACS,IAAI,CAAC3B,OAAO,CAAC,EACvB;MACA,OAAO,KAAK;IACd;EACF;EAEA,IAAIyB,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACvB,IAAIF,OAAO,CAACP,UAAU,KAAK,WAAW,EAAE,OAAO,KAAK;EACtD;EAEA,IAAIO,OAAO,CAACL,WAAW,KAAK,WAAW,IAAI,eAAe,CAACQ,IAAI,CAAC3B,OAAO,CAAC,EAAE;IACxE,OAAO,KAAK;EACd;EAEA,IAAIwB,OAAO,CAACH,SAAS,KAAK,WAAW,IAAI,aAAa,CAACM,IAAI,CAAC3B,OAAO,CAAC,EAAE;IACpE,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb;AAEO,SAAS4B,cAAc,CAACC,cAA8B,EAAEJ,KAAa,EAAE;EAC5E,IAAII,cAAc,CAACb,eAAe,KAAK,WAAW,EAAE;IAClDS,KAAK,GAAGA,KAAK,CAACK,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EACjC;EACA,IAAID,cAAc,CAACd,WAAW,KAAK,WAAW,EAAE;IAC9CU,KAAK,GAAGA,KAAK,CAACK,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;EAChC;EACA,IAAID,cAAc,CAACZ,UAAU,KAAK,WAAW,EAAE;IAC7CQ,KAAK,GAAGA,KAAK,CAACK,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;EAChC;EACA,OAAOL,KAAK;AACd"}