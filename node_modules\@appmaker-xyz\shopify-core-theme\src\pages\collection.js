const pagesData = {
  title: 'Product list',
  version: '2',
  attributes: {
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      backgroundColor: 'white',
    },
  },
  blocks: [
    {
      attributes: {
        // defaultLayoutConfig: {
        //   id: 'default',
        //   numColumns: 2,
        //   spanEvery: 5,
        // },
      },
      name: 'shopify/products-collection',
      innerBlocks: [],
      clientId: 'product-list',
      isValid: true,
    },
  ],

  stickyFooter: {
    blocks: [],
  },
  _id: 'collection',
  uid: 'odRhv94hf4S52SysvvNoxPyRx682',
  // dataSource: ,
//   toolBarItems: [ICONS.SEARCH, ICONS.WISHLIST, ICONS.CART],
};
export default pagesData;
