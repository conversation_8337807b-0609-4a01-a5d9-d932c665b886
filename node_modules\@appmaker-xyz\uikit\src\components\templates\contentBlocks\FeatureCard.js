import React from 'react';
import { ScrollView, StyleSheet } from 'react-native';
import { useApThemeState } from '@appmaker-xyz/uikit/src/theme/ThemeContext';
import {
  AppTouchable,
  Layout,
  AppmakerText,
} from '@appmaker-xyz/uikit/src/components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { AppImage } from '../../molecules/index';

const FeatureCard = ({ onAction, onPress, attributes }) => {
  const { color, spacing } = useApThemeState();

  const {
    title,
    subTitle,
    iconName,
    iconSize,
    imgSrc,
    imgSize,
    appmakerAction,
  } = attributes;

  const styles = allStyles({ color, spacing, imgSize });

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      onAction(appmakerAction);
    };
  }

  return (
    <AppTouchable onPress={onPressHandle}>
      <Layout style={styles.container}>
        {imgSrc ? <AppImage uri={imgSrc} style={styles.image} /> : null}
        {iconName ? (
          <Icon name={iconName} size={parseInt(iconSize) || 18} color="black" />
        ) : null}
        <AppmakerText category="bodyParagraphBold" style={styles.center}>
          {title}
        </AppmakerText>
        <AppmakerText category="bodySubText" style={styles.center}>
          {subTitle}
        </AppmakerText>
      </Layout>
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color, imgSize }) =>
  StyleSheet.create({
    container: {
      textAlign: 'center',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: spacing.small,
      paddingHorizontal: spacing.lg,
      backgroundColor: color.white,
      marginHorizontal: 0,
      borderRightColor: color.light,
      borderRightWidth: 1,
    },
    scroll: {
      flexGrow: 0,
    },
    image: {
      width: imgSize,
      resizeMode: 'contain',
      aspectRatio: 1 / 1,
      marginBottom: spacing.nano,
    },
    center: {
      textAlign: 'center',
    },
  });

export default FeatureCard;
