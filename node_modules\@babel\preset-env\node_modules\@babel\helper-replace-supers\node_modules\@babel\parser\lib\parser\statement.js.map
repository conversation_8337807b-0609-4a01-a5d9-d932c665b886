{"version": 3, "names": ["_types", "require", "_expression", "_parseError", "_identifier", "_scopeflags", "_util", "_productionParameter", "_expressionScope", "_tokenizer", "_location", "_node", "_lval", "loopLabel", "kind", "switchLabel", "ParseFunctionFlag", "Expression", "Declaration", "HangingDeclaration", "NullableId", "Async", "exports", "ParseStatementFlag", "StatementOnly", "AllowImportExport", "AllowDeclaration", "AllowFunctionDeclaration", "AllowLabeledFunction", "loneSurrogate", "keywordRelationalOperator", "babel7CompatTokens", "tokens", "input", "i", "length", "token", "type", "loc", "start", "value", "end", "hashEndPos", "hashEndLoc", "createPositionWithColumnOffset", "splice", "Token", "getExportedToken", "startLoc", "endLoc", "tokenIsTemplate", "backquoteEnd", "backquoteEndLoc", "startToken", "charCodeAt", "templateValue", "templateElementEnd", "templateElementEndLoc", "endToken", "slice", "Statement<PERSON><PERSON>er", "ExpressionParser", "parseTopLevel", "file", "program", "parseProgram", "comments", "state", "options", "finishNode", "sourceType", "interpreter", "parseInterpreterDirective", "parseBlockBody", "inModule", "allowUndeclaredExports", "scope", "undefinedExports", "size", "localName", "at", "Array", "from", "raise", "Errors", "ModuleExportUndefined", "finishedProgram", "finishNodeAt", "stmtToDirective", "stmt", "directive", "expression", "directiveLiteral", "expressionValue", "raw", "val", "addExtra", "match", "node", "startNode", "next", "isLet", "isContextual", "hasFollowingBindingAtom", "chStartsBindingIdentifier", "ch", "pos", "isIdentifierStart", "lastIndex", "test", "endCh", "codePointAtPos", "isIdentifierChar", "chStartsBindingPattern", "nextTokenStart", "nextCh", "hasInLineFollowingBindingIdentifier", "nextTokenInLineStart", "startsUsingForOf", "containsEsc", "<PERSON><PERSON><PERSON>", "tokenIsIdentifier", "hasFollowingLineBreak", "expectPlugin", "startsAwaitUsing", "isUnparsedContextual", "nextTokenInLineStartSince", "parseModuleItem", "parseStatementLike", "parseStatementListItem", "annexB", "strict", "parseStatementOrSloppyAnnexBFunctionDeclaration", "allowLabeledFunction", "flags", "parseStatement", "decorators", "parseDecorators", "parseStatementContent", "starttype", "allowDeclaration", "allowFunctionDeclaration", "topLevel", "parseBreakContinueStatement", "parseDebuggerStatement", "parseDoWhileStatement", "parseForStatement", "lookaheadCharCode", "StrictFunction", "SloppyFunctionAnnexB", "SloppyFunction", "parseFunctionStatement", "unexpected", "parseClass", "maybeTakeDecorators", "parseIfStatement", "parseReturnStatement", "parseSwitchStatement", "parseThrowStatement", "parseTryStatement", "isAwaitAllowed", "AwaitUsingNotInAsyncContext", "UnexpectedLexicalDeclaration", "parseVarStatement", "inTopLevel", "UnexpectedUsingDeclaration", "parseWhileStatement", "parseWithStatement", "parseBlock", "parseEmptyStatement", "nextTokenCharCode", "allowImportExportEverywhere", "UnexpectedImportExport", "result", "parseImport", "importKind", "sawUnambiguousESM", "parseExport", "exportKind", "assertModuleNodeAllowed", "isAsyncFunction", "AsyncFunctionInSingleStatementContext", "maybe<PERSON><PERSON>", "expr", "parseExpression", "eat", "parseLabeledStatement", "parseExpressionStatement", "ImportOutsideModule", "decoratorsEnabledBeforeExport", "hasPlugin", "getPluginOption", "maybeDecorators", "classNode", "exportNode", "DecoratorsBeforeAfterExport", "unshift", "resetStartLocationFromNode", "canHaveLeadingDecorator", "allowExport", "push", "parseDecorator", "DecoratorExportClass", "UnexpectedLeadingDecorator", "expectOnePlugin", "expect", "wrapParenthesis", "paramsStartLoc", "parseMaybeDecoratorArguments", "DecoratorArgumentsOutsideParentheses", "parseIdentifier", "startNodeAt", "object", "classScope", "usePrivateName", "property", "parsePrivateName", "computed", "parseExprSubscripts", "startNodeAtNode", "callee", "arguments", "parseCallExpressionArguments", "toReferencedList", "isBreak", "isLineTerminator", "label", "semicolon", "verifyBreakContinue", "labels", "lab", "name", "IllegalBreakContinue", "parseHeaderExpression", "body", "withSmartMixTopicForbiddingContext", "pop", "await<PERSON>t", "eatContextual", "lastTokStartLoc", "enter", "SCOPE_OTHER", "parseFor", "startsWithLet", "startsWithAwaitUsing", "starsWithUsingDeclaration", "isLetOrUsing", "initNode", "parseVar", "init", "isForIn", "ForInUsing", "declarations", "parseForIn", "startsWithAsync", "refExpressionErrors", "ExpressionErrors", "isForOf", "ForOfLet", "ForOfAsync", "checkDestructuringPrivate", "toAssignable", "checkLVal", "in", "checkExpressionErrors", "isAsync", "isHangingDeclaration", "parseFunction", "consequent", "alternate", "prodParam", "hasReturn", "allowReturnOutsideFunction", "IllegalReturn", "argument", "discriminant", "cases", "cur", "<PERSON><PERSON><PERSON><PERSON>", "isCase", "MultipleDefaultsInSwitch", "exit", "hasPrecedingLineBreak", "NewlineAfterThrow", "lastTokEndLoc", "parseCatchClauseParam", "param", "parseBindingAtom", "SCOPE_SIMPLE_CATCH", "binding", "BIND_CATCH_PARAM", "block", "handler", "clause", "finalizer", "NoCatchOrFinally", "allowMissingInitializer", "StrictWith", "LabelRedeclaration", "labelName", "tokenIsLoop", "statementStart", "allowDirectives", "createNewLexicalScope", "afterBlockParse", "strictErrors", "clear", "isValidDirective", "extra", "parenthesized", "directives", "parseBlockOrModuleBlockBody", "undefined", "oldStrict", "hasStrictModeDirective", "parsedNonDirective", "setStrict", "call", "update", "await", "id", "ForInOfLoopInitializer", "InvalidLhs", "ancestor", "left", "right", "parseMaybeAssignAllowIn", "isFor", "decl", "parseVarId", "parseMaybeAssignDisallowIn", "DeclarationMissingInitializer", "BIND_VAR", "BIND_LEXICAL", "parseAsyncFunctionExpression", "hangingDeclaration", "isDeclaration", "requireId", "initFunction", "GeneratorInSingleStatementContext", "generator", "parseFunctionId", "oldMaybeInArrowParameters", "maybeInArrowParameters", "SCOPE_FUNCTION", "functionFlags", "parseFunctionParams", "parseFunctionBodyAndFinish", "registerFunctionStatementId", "isConstructor", "expressionScope", "newParameterDeclarationScope", "params", "parseBindingList", "ParseBindingListFlags", "IS_FUNCTION_PARAMS", "IS_CONSTRUCTOR_PARAMS", "declareName", "async", "treatFunctionsAsVar", "BIND_FUNCTION", "isStatement", "optionalId", "parseClassId", "parseClassSuper", "parseClassBody", "superClass", "isClassProperty", "isClassMethod", "isNonstaticConstructor", "method", "static", "key", "hadSuperClass", "hadConstructor", "classBody", "DecoratorSemicolon", "member", "parseClassMember", "DecoratorConstructor", "TrailingDecorator", "parseClassMemberFromModifier", "pushClassMethod", "prop", "parseClassProperty", "resetPreviousNodeTrailingComments", "isStatic", "parseClassStaticBlock", "parseClassMemberWithIsStatic", "publicMethod", "privateMethod", "publicProp", "privateProp", "accessorProp", "publicMember", "parsePropertyNamePrefixOperator", "isPrivateName", "parseClassElementName", "pushClassPrivateMethod", "ConstructorIsGenerator", "isPrivate", "maybeQuestionTokenStartLoc", "parsePostMemberNameModifiers", "allowsDirectSuper", "DuplicateConstructor", "override", "OverrideOnConstructor", "pushClassPrivateProperty", "pushClassProperty", "isGenerator", "optional", "ConstructorIsAsync", "ConstructorIsAccessor", "checkGetterSetterParams", "pushClassAccessorProperty", "StaticPrototype", "ConstructorClassPrivateField", "parsePropertyName", "_member$decorators", "SCOPE_CLASS", "SCOPE_STATIC_BLOCK", "SCOPE_SUPER", "<PERSON><PERSON><PERSON><PERSON>", "PARAM", "DecoratorStaticBlock", "ConstructorClassField", "parseClassPrivateProperty", "declarePrivateName", "getPrivateNameSV", "CLASS_ELEMENT_OTHER", "parseClassAccessorProperty", "parseMethod", "CLASS_ELEMENT_STATIC_GETTER", "CLASS_ELEMENT_INSTANCE_GETTER", "CLASS_ELEMENT_STATIC_SETTER", "CLASS_ELEMENT_INSTANCE_SETTER", "declareClassPrivateMethodInScope", "methodOrProp", "parseInitializer", "newExpressionScope", "bindingType", "BIND_CLASS", "declareNameFromIdentifier", "MissingClassName", "maybeDefaultIdentifier", "parseMaybeImportPhase", "<PERSON><PERSON><PERSON><PERSON>", "maybeParseExportDefaultSpecifier", "parseAfterDefault", "hasStar", "eatExportStar", "hasNamespace", "maybeParseExportNamespaceSpecifier", "parseAfterNamespace", "isFromRequired", "UnsupportedDecoratorExport", "parseExportFrom", "hasSpecifiers", "maybeParseExportNamedSpecifiers", "hasDeclaration", "maybeParseExportDeclaration", "_node2$declaration", "node2", "checkExport", "source", "declaration", "parseExportDefaultExpression", "isExportDefaultSpecifier", "specifier", "exported", "specifiers", "parseModuleExportName", "isTypeExport", "parseExportSpecifiers", "assertions", "shouldParseExportDeclaration", "parseExportDeclaration", "DecoratorBeforeExport", "UnsupportedDefaultExport", "res", "nextType", "has<PERSON>rom", "nextAfterFrom", "nextTokenStartSince", "parseImportSource", "maybeParseImportAttributes", "checkJSONModuleImport", "checkNames", "isDefault", "isFrom", "checkDuplicateExports", "_declaration$extra", "ExportDefaultFromAsIdentifier", "exportName", "local", "ExportBindingIsString", "checkReservedWord", "checkLocalExport", "Error", "checkDeclaration", "properties", "elem", "elements", "exportedIdentifiers", "has", "DuplicateDefaultExport", "DuplicateExport", "add", "isInTypeExport", "nodes", "first", "isMaybeTypeOnly", "isString", "parseExportSpecifier", "cloneStringLiteral", "cloneIdentifier", "parseStringLiteral", "surrogate", "ModuleExportNameHasLoneSurrogate", "surrogateCharCode", "isJSONModuleImport", "some", "checkImportReflection", "module", "_node$assertions", "ImportReflectionNotBinding", "ImportReflectionHasAssertion", "nonDefaultNamedSpecifier", "find", "imported", "ImportJSONBindingNotDefault", "isPotentialImportPhase", "isExport", "applyImportPhase", "phase", "phaseIdentifier", "isImportPhase", "tokenIsKeywordOrIdentifier", "resetPreviousIdentifierLeadingComments", "isPrecedingIdImportPhase", "parseImportSourceAndAttributes", "parseImportSpecifiersAndAfter", "maybeParseDefaultImportSpecifier", "parseNext", "maybeParseStarImportSpecifier", "parseNamedImportSpecifiers", "expectContextual", "_node$specifiers", "parseExprAtom", "parseImportSpecifierLocal", "finishImportSpecifier", "parseImportAttributes", "attrs", "attrNames", "Set", "keyName", "ModuleAttributesWithDuplicateKeys", "ModuleAttributeInvalidValue", "parseModuleAttributes", "attributes", "ModuleAttributeDifferentFromType", "useWith", "expectImportAttributesPlugin", "ImportAttributesUseAssert", "DestructureNamedImport", "importedIsString", "importSpecifier", "parseImportSpecifier", "isInTypeOnlyImport", "ImportBindingIsString", "importName", "isThisParam", "default"], "sources": ["../../src/parser/statement.ts"], "sourcesContent": ["import type * as N from \"../types\";\nimport {\n  tokenIsIdentifier,\n  tokenIsKeywordOrIdentifier,\n  tokenIsLoop,\n  tokenIsTemplate,\n  tt,\n  type TokenType,\n  getExportedToken,\n} from \"../tokenizer/types\";\nimport ExpressionParser from \"./expression\";\nimport { Errors } from \"../parse-error\";\nimport { isIdentifierChar, isIdentifierStart } from \"../util/identifier\";\nimport * as charCodes from \"charcodes\";\nimport {\n  BIND_CLASS,\n  BIND_LEXICAL,\n  BIND_VAR,\n  BIND_FUNCTION,\n  SCOPE_CLASS,\n  SCOPE_FUNCTION,\n  SCOPE_OTHER,\n  SCOPE_SIMPLE_CATCH,\n  SCOPE_STATIC_BLOCK,\n  SCOPE_SUPER,\n  CLASS_ELEMENT_OTHER,\n  CLASS_ELEMENT_INSTANCE_GETTER,\n  CLASS_ELEMENT_INSTANCE_SETTER,\n  CLASS_ELEMENT_STATIC_GETTER,\n  CLASS_ELEMENT_STATIC_SETTER,\n  type BindingTypes,\n  BIND_CATCH_PARAM,\n} from \"../util/scopeflags\";\nimport { ExpressionErrors } from \"./util\";\nimport { PARAM, functionFlags } from \"../util/production-parameter\";\nimport {\n  newExpressionScope,\n  newParameterDeclarationScope,\n} from \"../util/expression-scope\";\nimport type { SourceType } from \"../options\";\nimport { Token } from \"../tokenizer\";\nimport type { Position } from \"../util/location\";\nimport { createPositionWithColumnOffset } from \"../util/location\";\nimport { cloneStringLiteral, cloneIdentifier, type Undone } from \"./node\";\nimport type Parser from \"./index\";\nimport { ParseBindingListFlags } from \"./lval\";\n\nconst loopLabel = { kind: \"loop\" } as const,\n  switchLabel = { kind: \"switch\" } as const;\n\nexport const enum ParseFunctionFlag {\n  Expression = 0b0000,\n  Declaration = 0b0001,\n  HangingDeclaration = 0b0010,\n  NullableId = 0b0100,\n  Async = 0b1000,\n}\n\nexport const enum ParseStatementFlag {\n  StatementOnly = 0b0000,\n  AllowImportExport = 0b0001,\n  AllowDeclaration = 0b0010,\n  AllowFunctionDeclaration = 0b0100,\n  AllowLabeledFunction = 0b1000,\n}\n\nconst loneSurrogate = /[\\uD800-\\uDFFF]/u;\n\nconst keywordRelationalOperator = /in(?:stanceof)?/y;\n\n/**\n * Convert tokens for backward Babel 7 compat.\n * tt.privateName => tt.hash + tt.name\n * tt.templateTail => tt.backquote/tt.braceR + tt.template + tt.backquote\n * tt.templateNonTail => tt.backquote/tt.braceR + tt.template + tt.dollarBraceL\n * For performance reasons this routine mutates `tokens`, it is okay\n * here since we execute `parseTopLevel` once for every file.\n */\nfunction babel7CompatTokens(tokens: (Token | N.Comment)[], input: string) {\n  for (let i = 0; i < tokens.length; i++) {\n    const token = tokens[i];\n    const { type } = token;\n    if (typeof type === \"number\") {\n      if (!process.env.BABEL_8_BREAKING) {\n        if (type === tt.privateName) {\n          const { loc, start, value, end } = token;\n          const hashEndPos = start + 1;\n          const hashEndLoc = createPositionWithColumnOffset(loc.start, 1);\n          tokens.splice(\n            i,\n            1,\n            new Token({\n              // @ts-expect-error: hacky way to create token\n              type: getExportedToken(tt.hash),\n              value: \"#\",\n              start: start,\n              end: hashEndPos,\n              startLoc: loc.start,\n              endLoc: hashEndLoc,\n            }),\n            new Token({\n              // @ts-expect-error: hacky way to create token\n              type: getExportedToken(tt.name),\n              value: value,\n              start: hashEndPos,\n              end: end,\n              startLoc: hashEndLoc,\n              endLoc: loc.end,\n            }),\n          );\n          i++;\n          continue;\n        }\n\n        if (tokenIsTemplate(type)) {\n          const { loc, start, value, end } = token;\n          const backquoteEnd = start + 1;\n          const backquoteEndLoc = createPositionWithColumnOffset(loc.start, 1);\n          let startToken;\n          if (input.charCodeAt(start) === charCodes.graveAccent) {\n            startToken = new Token({\n              // @ts-expect-error: hacky way to create token\n              type: getExportedToken(tt.backQuote),\n              value: \"`\",\n              start: start,\n              end: backquoteEnd,\n              startLoc: loc.start,\n              endLoc: backquoteEndLoc,\n            });\n          } else {\n            startToken = new Token({\n              // @ts-expect-error: hacky way to create token\n              type: getExportedToken(tt.braceR),\n              value: \"}\",\n              start: start,\n              end: backquoteEnd,\n              startLoc: loc.start,\n              endLoc: backquoteEndLoc,\n            });\n          }\n          let templateValue,\n            templateElementEnd,\n            templateElementEndLoc,\n            endToken;\n          if (type === tt.templateTail) {\n            // ends with '`'\n            templateElementEnd = end - 1;\n            templateElementEndLoc = createPositionWithColumnOffset(loc.end, -1);\n            templateValue = value === null ? null : value.slice(1, -1);\n            endToken = new Token({\n              // @ts-expect-error: hacky way to create token\n              type: getExportedToken(tt.backQuote),\n              value: \"`\",\n              start: templateElementEnd,\n              end: end,\n              startLoc: templateElementEndLoc,\n              endLoc: loc.end,\n            });\n          } else {\n            // ends with `${`\n            templateElementEnd = end - 2;\n            templateElementEndLoc = createPositionWithColumnOffset(loc.end, -2);\n            templateValue = value === null ? null : value.slice(1, -2);\n            endToken = new Token({\n              // @ts-expect-error: hacky way to create token\n              type: getExportedToken(tt.dollarBraceL),\n              value: \"${\",\n              start: templateElementEnd,\n              end: end,\n              startLoc: templateElementEndLoc,\n              endLoc: loc.end,\n            });\n          }\n          tokens.splice(\n            i,\n            1,\n            startToken,\n            new Token({\n              // @ts-expect-error: hacky way to create token\n              type: getExportedToken(tt.template),\n              value: templateValue,\n              start: backquoteEnd,\n              end: templateElementEnd,\n              startLoc: backquoteEndLoc,\n              endLoc: templateElementEndLoc,\n            }),\n            endToken,\n          );\n          i += 2;\n          continue;\n        }\n      }\n      // @ts-expect-error: we manipulate `token` for performance reasons\n      token.type = getExportedToken(type);\n    }\n  }\n  return tokens;\n}\nexport default abstract class StatementParser extends ExpressionParser {\n  // ### Statement parsing\n\n  // Parse a program. Initializes the parser, reads any number of\n  // statements, and wraps them in a Program node.  Optionally takes a\n  // `program` argument.  If present, the statements will be appended\n  // to its body instead of creating a new node.\n\n  parseTopLevel(this: Parser, file: N.File, program: N.Program): N.File {\n    file.program = this.parseProgram(program);\n    file.comments = this.state.comments;\n\n    if (this.options.tokens) {\n      file.tokens = babel7CompatTokens(this.tokens, this.input);\n    }\n\n    return this.finishNode(file, \"File\");\n  }\n\n  parseProgram(\n    this: Parser,\n    program: Undone<N.Program>,\n    end: TokenType = tt.eof,\n    sourceType: SourceType = this.options.sourceType,\n  ): N.Program {\n    program.sourceType = sourceType;\n    program.interpreter = this.parseInterpreterDirective();\n    this.parseBlockBody(program, true, true, end);\n    if (\n      this.inModule &&\n      !this.options.allowUndeclaredExports &&\n      this.scope.undefinedExports.size > 0\n    ) {\n      for (const [localName, at] of Array.from(this.scope.undefinedExports)) {\n        this.raise(Errors.ModuleExportUndefined, { at, localName });\n      }\n    }\n    let finishedProgram: N.Program;\n    if (end === tt.eof) {\n      // finish at eof for top level program\n      finishedProgram = this.finishNode(program, \"Program\");\n    } else {\n      // finish immediately before the end token\n      finishedProgram = this.finishNodeAt(\n        program,\n        \"Program\",\n        createPositionWithColumnOffset(this.state.startLoc, -1),\n      );\n    }\n    return finishedProgram;\n  }\n\n  /**\n   * cast a Statement to a Directive. This method mutates input statement.\n   */\n  stmtToDirective(stmt: N.Statement): N.Directive {\n    const directive = stmt as any;\n    directive.type = \"Directive\";\n    directive.value = directive.expression;\n    delete directive.expression;\n\n    const directiveLiteral = directive.value;\n    const expressionValue = directiveLiteral.value;\n    const raw = this.input.slice(directiveLiteral.start, directiveLiteral.end);\n    const val = (directiveLiteral.value = raw.slice(1, -1)); // remove quotes\n\n    this.addExtra(directiveLiteral, \"raw\", raw);\n    this.addExtra(directiveLiteral, \"rawValue\", val);\n    this.addExtra(directiveLiteral, \"expressionValue\", expressionValue);\n\n    directiveLiteral.type = \"DirectiveLiteral\";\n\n    return directive;\n  }\n\n  parseInterpreterDirective(): N.InterpreterDirective | null {\n    if (!this.match(tt.interpreterDirective)) {\n      return null;\n    }\n\n    const node = this.startNode<N.InterpreterDirective>();\n    node.value = this.state.value;\n    this.next();\n    return this.finishNode(node, \"InterpreterDirective\");\n  }\n\n  isLet(): boolean {\n    if (!this.isContextual(tt._let)) {\n      return false;\n    }\n    return this.hasFollowingBindingAtom();\n  }\n\n  chStartsBindingIdentifier(ch: number, pos: number) {\n    if (isIdentifierStart(ch)) {\n      keywordRelationalOperator.lastIndex = pos;\n      if (keywordRelationalOperator.test(this.input)) {\n        // We have seen `in` or `instanceof` so far, now check if the identifier\n        // ends here\n        const endCh = this.codePointAtPos(keywordRelationalOperator.lastIndex);\n        if (!isIdentifierChar(endCh) && endCh !== charCodes.backslash) {\n          return false;\n        }\n      }\n      return true;\n    } else if (ch === charCodes.backslash) {\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  chStartsBindingPattern(ch: number) {\n    return (\n      ch === charCodes.leftSquareBracket || ch === charCodes.leftCurlyBrace\n    );\n  }\n\n  /**\n   * Assuming we have seen a contextual `let` and declaration is allowed, check if it\n   * starts a variable declaration so that it should be interpreted as a keyword.\n   */\n  hasFollowingBindingAtom(): boolean {\n    const next = this.nextTokenStart();\n    const nextCh = this.codePointAtPos(next);\n    return (\n      this.chStartsBindingPattern(nextCh) ||\n      this.chStartsBindingIdentifier(nextCh, next)\n    );\n  }\n\n  /**\n   * Assuming we have seen a contextual `using` and declaration is allowed, check if it\n   * starts a variable declaration in the same line so that it should be interpreted as\n   * a keyword.\n   */\n  hasInLineFollowingBindingIdentifier(): boolean {\n    const next = this.nextTokenInLineStart();\n    const nextCh = this.codePointAtPos(next);\n    return this.chStartsBindingIdentifier(nextCh, next);\n  }\n\n  startsUsingForOf(): boolean {\n    const { type, containsEsc } = this.lookahead();\n    if (type === tt._of && !containsEsc) {\n      // `using of` must start a for-lhs-of statement\n      return false;\n    } else if (tokenIsIdentifier(type) && !this.hasFollowingLineBreak()) {\n      this.expectPlugin(\"explicitResourceManagement\");\n      return true;\n    }\n  }\n\n  startsAwaitUsing(): boolean {\n    let next = this.nextTokenInLineStart();\n    if (this.isUnparsedContextual(next, \"using\")) {\n      next = this.nextTokenInLineStartSince(next + 5);\n      const nextCh = this.codePointAtPos(next);\n      if (this.chStartsBindingIdentifier(nextCh, next)) {\n        this.expectPlugin(\"explicitResourceManagement\");\n        return true;\n      }\n    }\n    return false;\n  }\n\n  // https://tc39.es/ecma262/#prod-ModuleItem\n  parseModuleItem(this: Parser) {\n    return this.parseStatementLike(\n      ParseStatementFlag.AllowImportExport |\n        ParseStatementFlag.AllowDeclaration |\n        ParseStatementFlag.AllowFunctionDeclaration |\n        // This function is actually also used to parse StatementItems,\n        // which with Annex B enabled allows labeled functions.\n        ParseStatementFlag.AllowLabeledFunction,\n    );\n  }\n\n  // https://tc39.es/ecma262/#prod-StatementListItem\n  parseStatementListItem(this: Parser) {\n    return this.parseStatementLike(\n      ParseStatementFlag.AllowDeclaration |\n        ParseStatementFlag.AllowFunctionDeclaration |\n        (!this.options.annexB || this.state.strict\n          ? 0\n          : ParseStatementFlag.AllowLabeledFunction),\n    );\n  }\n\n  parseStatementOrSloppyAnnexBFunctionDeclaration(\n    this: Parser,\n    allowLabeledFunction: boolean = false,\n  ) {\n    let flags: ParseStatementFlag = ParseStatementFlag.StatementOnly;\n    if (this.options.annexB && !this.state.strict) {\n      flags |= ParseStatementFlag.AllowFunctionDeclaration;\n      if (allowLabeledFunction) {\n        flags |= ParseStatementFlag.AllowLabeledFunction;\n      }\n    }\n    return this.parseStatementLike(flags);\n  }\n\n  // Parse a single statement.\n  //\n  // If expecting a statement and finding a slash operator, parse a\n  // regular expression literal. This is to handle cases like\n  // `if (foo) /blah/.exec(foo)`, where looking at the previous token\n  // does not help.\n  // https://tc39.es/ecma262/#prod-Statement\n  parseStatement(this: Parser) {\n    return this.parseStatementLike(ParseStatementFlag.StatementOnly);\n  }\n\n  // ImportDeclaration and ExportDeclaration are also handled here so we can throw recoverable errors\n  // when they are not at the top level\n  parseStatementLike(\n    this: Parser,\n    flags: ParseStatementFlag,\n  ):\n    | N.Statement\n    | N.Declaration\n    | N.ImportDeclaration\n    | N.ExportDefaultDeclaration\n    | N.ExportNamedDeclaration\n    | N.ExportAllDeclaration {\n    let decorators: N.Decorator[] | null = null;\n\n    if (this.match(tt.at)) {\n      decorators = this.parseDecorators(true);\n    }\n    return this.parseStatementContent(flags, decorators);\n  }\n\n  parseStatementContent(\n    this: Parser,\n    flags: ParseStatementFlag,\n    decorators?: N.Decorator[] | null,\n  ): N.Statement {\n    const starttype = this.state.type;\n    const node = this.startNode();\n    const allowDeclaration = !!(flags & ParseStatementFlag.AllowDeclaration);\n    const allowFunctionDeclaration = !!(\n      flags & ParseStatementFlag.AllowFunctionDeclaration\n    );\n    const topLevel = flags & ParseStatementFlag.AllowImportExport;\n\n    // Most types of statements are recognized by the keyword they\n    // start with. Many are trivial to parse, some require a bit of\n    // complexity.\n\n    switch (starttype) {\n      case tt._break:\n        return this.parseBreakContinueStatement(node, /* isBreak */ true);\n      case tt._continue:\n        return this.parseBreakContinueStatement(node, /* isBreak */ false);\n      case tt._debugger:\n        return this.parseDebuggerStatement(node as Undone<N.DebuggerStatement>);\n      case tt._do:\n        return this.parseDoWhileStatement(node as Undone<N.DoWhileStatement>);\n      case tt._for:\n        return this.parseForStatement(node as Undone<N.ForStatement>);\n      case tt._function:\n        if (this.lookaheadCharCode() === charCodes.dot) break;\n        if (!allowFunctionDeclaration) {\n          this.raise(\n            this.state.strict\n              ? Errors.StrictFunction\n              : this.options.annexB\n              ? Errors.SloppyFunctionAnnexB\n              : Errors.SloppyFunction,\n            { at: this.state.startLoc },\n          );\n        }\n        return this.parseFunctionStatement(\n          node as Undone<N.FunctionDeclaration>,\n          false,\n          !allowDeclaration && allowFunctionDeclaration,\n        );\n      case tt._class:\n        if (!allowDeclaration) this.unexpected();\n        return this.parseClass(\n          this.maybeTakeDecorators(\n            decorators,\n            node as Undone<N.ClassDeclaration>,\n          ),\n          true,\n        );\n\n      case tt._if:\n        return this.parseIfStatement(node as Undone<N.IfStatement>);\n      case tt._return:\n        return this.parseReturnStatement(node as Undone<N.ReturnStatement>);\n      case tt._switch:\n        return this.parseSwitchStatement(node as Undone<N.SwitchStatement>);\n      case tt._throw:\n        return this.parseThrowStatement(node as Undone<N.ThrowStatement>);\n      case tt._try:\n        return this.parseTryStatement(node as Undone<N.TryStatement>);\n\n      case tt._await:\n        // [+Await] await [no LineTerminator here] using [no LineTerminator here] BindingList[+Using]\n        if (!this.state.containsEsc && this.startsAwaitUsing()) {\n          if (!this.isAwaitAllowed()) {\n            this.raise(Errors.AwaitUsingNotInAsyncContext, { at: node });\n          } else if (!allowDeclaration) {\n            this.raise(Errors.UnexpectedLexicalDeclaration, {\n              at: node,\n            });\n          }\n          this.next(); // eat 'await'\n          return this.parseVarStatement(\n            node as Undone<N.VariableDeclaration>,\n            \"await using\",\n          );\n        }\n        break;\n      case tt._using:\n        // using [no LineTerminator here] BindingList[+Using]\n        if (\n          this.state.containsEsc ||\n          !this.hasInLineFollowingBindingIdentifier()\n        ) {\n          break;\n        }\n        this.expectPlugin(\"explicitResourceManagement\");\n        if (!this.scope.inModule && this.scope.inTopLevel) {\n          this.raise(Errors.UnexpectedUsingDeclaration, {\n            at: this.state.startLoc,\n          });\n        } else if (!allowDeclaration) {\n          this.raise(Errors.UnexpectedLexicalDeclaration, {\n            at: this.state.startLoc,\n          });\n        }\n        return this.parseVarStatement(\n          node as Undone<N.VariableDeclaration>,\n          \"using\",\n        );\n      case tt._let: {\n        if (this.state.containsEsc) {\n          break;\n        }\n        // `let [` is an explicit negative lookahead for\n        // ExpressionStatement, so special-case it first.\n        const next = this.nextTokenStart();\n        const nextCh = this.codePointAtPos(next);\n        if (nextCh !== charCodes.leftSquareBracket) {\n          if (!allowDeclaration && this.hasFollowingLineBreak()) break;\n          if (\n            !this.chStartsBindingIdentifier(nextCh, next) &&\n            nextCh !== charCodes.leftCurlyBrace\n          ) {\n            break;\n          }\n        }\n      }\n      // fall through\n      case tt._const: {\n        if (!allowDeclaration) {\n          this.raise(Errors.UnexpectedLexicalDeclaration, {\n            at: this.state.startLoc,\n          });\n        }\n      }\n      // fall through\n      case tt._var: {\n        const kind = this.state.value;\n        return this.parseVarStatement(\n          node as Undone<N.VariableDeclaration>,\n          kind,\n        );\n      }\n      case tt._while:\n        return this.parseWhileStatement(node as Undone<N.WhileStatement>);\n      case tt._with:\n        return this.parseWithStatement(node as Undone<N.WithStatement>);\n      case tt.braceL:\n        return this.parseBlock();\n      case tt.semi:\n        return this.parseEmptyStatement(node as Undone<N.EmptyStatement>);\n      case tt._import: {\n        const nextTokenCharCode = this.lookaheadCharCode();\n        if (\n          nextTokenCharCode === charCodes.leftParenthesis || // import()\n          nextTokenCharCode === charCodes.dot // import.meta\n        ) {\n          break;\n        }\n      }\n      // fall through\n      case tt._export: {\n        if (!this.options.allowImportExportEverywhere && !topLevel) {\n          this.raise(Errors.UnexpectedImportExport, {\n            at: this.state.startLoc,\n          });\n        }\n\n        this.next(); // eat `import`/`export`\n\n        let result;\n        if (starttype === tt._import) {\n          result = this.parseImport(node as Undone<N.ImportDeclaration>);\n\n          if (\n            result.type === \"ImportDeclaration\" &&\n            (!result.importKind || result.importKind === \"value\")\n          ) {\n            this.sawUnambiguousESM = true;\n          }\n        } else {\n          result = this.parseExport(\n            node as Undone<\n              | N.ExportAllDeclaration\n              | N.ExportDefaultDeclaration\n              | N.ExportDefaultDeclaration\n            >,\n            decorators,\n          );\n\n          if (\n            (result.type === \"ExportNamedDeclaration\" &&\n              (!result.exportKind || result.exportKind === \"value\")) ||\n            (result.type === \"ExportAllDeclaration\" &&\n              (!result.exportKind || result.exportKind === \"value\")) ||\n            result.type === \"ExportDefaultDeclaration\"\n          ) {\n            this.sawUnambiguousESM = true;\n          }\n        }\n\n        this.assertModuleNodeAllowed(result);\n\n        return result;\n      }\n\n      default: {\n        if (this.isAsyncFunction()) {\n          if (!allowDeclaration) {\n            this.raise(Errors.AsyncFunctionInSingleStatementContext, {\n              at: this.state.startLoc,\n            });\n          }\n          this.next(); // eat 'async'\n          return this.parseFunctionStatement(\n            node as Undone<N.FunctionDeclaration>,\n            true,\n            !allowDeclaration && allowFunctionDeclaration,\n          );\n        }\n      }\n    }\n\n    // If the statement does not start with a statement keyword or a\n    // brace, it's an ExpressionStatement or LabeledStatement. We\n    // simply start parsing an expression, and afterwards, if the\n    // next token is a colon and the expression was a simple\n    // Identifier node, we switch to interpreting it as a label.\n    const maybeName = this.state.value;\n    const expr = this.parseExpression();\n\n    if (\n      tokenIsIdentifier(starttype) &&\n      expr.type === \"Identifier\" &&\n      this.eat(tt.colon)\n    ) {\n      return this.parseLabeledStatement(\n        node as Undone<N.LabeledStatement>,\n        maybeName,\n        // @ts-expect-error migrate to Babel types\n        expr,\n        flags,\n      );\n    } else {\n      return this.parseExpressionStatement(\n        node as Undone<N.ExpressionStatement>,\n        expr,\n        decorators,\n      );\n    }\n  }\n\n  assertModuleNodeAllowed(node: N.Node): void {\n    if (!this.options.allowImportExportEverywhere && !this.inModule) {\n      this.raise(Errors.ImportOutsideModule, { at: node });\n    }\n  }\n\n  decoratorsEnabledBeforeExport(): boolean {\n    if (this.hasPlugin(\"decorators-legacy\")) return true;\n    return (\n      this.hasPlugin(\"decorators\") &&\n      this.getPluginOption(\"decorators\", \"decoratorsBeforeExport\") !== false\n    );\n  }\n\n  // Attach the decorators to the given class.\n  // NOTE: This method changes the .start location of the class, and thus\n  // can affect comment attachment. Calling it before or after finalizing\n  // the class node (and thus finalizing its comments) changes how comments\n  // before the `class` keyword or before the final .start location of the\n  // class are attached.\n  maybeTakeDecorators<T extends Undone<N.Class>>(\n    maybeDecorators: N.Decorator[] | null,\n    classNode: T,\n    exportNode?: Undone<N.ExportDefaultDeclaration | N.ExportNamedDeclaration>,\n  ): T {\n    if (maybeDecorators) {\n      if (classNode.decorators && classNode.decorators.length > 0) {\n        // Note: decorators attachment is only attempred multiple times\n        // when the class is part of an export declaration.\n        if (\n          typeof this.getPluginOption(\n            \"decorators\",\n            \"decoratorsBeforeExport\",\n          ) !== \"boolean\"\n        ) {\n          // If `decoratorsBeforeExport` was set to `true` or `false`, we\n          // already threw an error about decorators not being in a valid\n          // position.\n          this.raise(Errors.DecoratorsBeforeAfterExport, {\n            at: classNode.decorators[0],\n          });\n        }\n        classNode.decorators.unshift(...maybeDecorators);\n      } else {\n        classNode.decorators = maybeDecorators;\n      }\n      this.resetStartLocationFromNode(classNode, maybeDecorators[0]);\n      if (exportNode) this.resetStartLocationFromNode(exportNode, classNode);\n    }\n    return classNode;\n  }\n\n  canHaveLeadingDecorator(): boolean {\n    return this.match(tt._class);\n  }\n\n  parseDecorators(this: Parser, allowExport?: boolean): N.Decorator[] {\n    const decorators = [];\n    do {\n      decorators.push(this.parseDecorator());\n    } while (this.match(tt.at));\n\n    if (this.match(tt._export)) {\n      if (!allowExport) {\n        this.unexpected();\n      }\n\n      if (!this.decoratorsEnabledBeforeExport()) {\n        this.raise(Errors.DecoratorExportClass, { at: this.state.startLoc });\n      }\n    } else if (!this.canHaveLeadingDecorator()) {\n      throw this.raise(Errors.UnexpectedLeadingDecorator, {\n        at: this.state.startLoc,\n      });\n    }\n\n    return decorators;\n  }\n\n  parseDecorator(this: Parser): N.Decorator {\n    this.expectOnePlugin([\"decorators\", \"decorators-legacy\"]);\n\n    const node = this.startNode<N.Decorator>();\n    this.next();\n\n    if (this.hasPlugin(\"decorators\")) {\n      const startLoc = this.state.startLoc;\n      let expr: N.Expression;\n\n      if (this.match(tt.parenL)) {\n        const startLoc = this.state.startLoc;\n        this.next(); // eat '('\n        expr = this.parseExpression();\n        this.expect(tt.parenR);\n        expr = this.wrapParenthesis(startLoc, expr);\n\n        const paramsStartLoc = this.state.startLoc;\n        node.expression = this.parseMaybeDecoratorArguments(expr);\n        if (\n          this.getPluginOption(\"decorators\", \"allowCallParenthesized\") ===\n            false &&\n          node.expression !== expr\n        ) {\n          this.raise(Errors.DecoratorArgumentsOutsideParentheses, {\n            at: paramsStartLoc,\n          });\n        }\n      } else {\n        expr = this.parseIdentifier(false);\n\n        while (this.eat(tt.dot)) {\n          const node = this.startNodeAt(startLoc);\n          node.object = expr;\n          if (this.match(tt.privateName)) {\n            this.classScope.usePrivateName(\n              this.state.value,\n              this.state.startLoc,\n            );\n            node.property = this.parsePrivateName();\n          } else {\n            node.property = this.parseIdentifier(true);\n          }\n          node.computed = false;\n          expr = this.finishNode(node, \"MemberExpression\");\n        }\n\n        node.expression = this.parseMaybeDecoratorArguments(expr);\n      }\n    } else {\n      node.expression = this.parseExprSubscripts();\n    }\n    return this.finishNode(node, \"Decorator\");\n  }\n\n  parseMaybeDecoratorArguments(this: Parser, expr: N.Expression): N.Expression {\n    if (this.eat(tt.parenL)) {\n      const node = this.startNodeAtNode(expr);\n      node.callee = expr;\n      node.arguments = this.parseCallExpressionArguments(tt.parenR, false);\n      this.toReferencedList(node.arguments);\n      return this.finishNode(node, \"CallExpression\");\n    }\n\n    return expr;\n  }\n\n  parseBreakContinueStatement(\n    node: Undone<N.Node>,\n    isBreak: true,\n  ): N.BreakStatement;\n  parseBreakContinueStatement(\n    node: Undone<N.Node>,\n    isBreak: false,\n  ): N.ContinueStatement;\n  parseBreakContinueStatement(\n    node: Undone<N.BreakStatement | N.ContinueStatement>,\n    isBreak: boolean,\n  ): N.BreakStatement | N.ContinueStatement {\n    this.next();\n\n    if (this.isLineTerminator()) {\n      node.label = null;\n    } else {\n      node.label = this.parseIdentifier();\n      this.semicolon();\n    }\n\n    this.verifyBreakContinue(node, isBreak);\n\n    return this.finishNode(\n      node,\n      isBreak ? \"BreakStatement\" : \"ContinueStatement\",\n    );\n  }\n\n  verifyBreakContinue(\n    node: Undone<N.BreakStatement | N.ContinueStatement>,\n    isBreak: boolean,\n  ) {\n    let i;\n    for (i = 0; i < this.state.labels.length; ++i) {\n      const lab = this.state.labels[i];\n      if (node.label == null || lab.name === node.label.name) {\n        if (lab.kind != null && (isBreak || lab.kind === \"loop\")) break;\n        if (node.label && isBreak) break;\n      }\n    }\n    if (i === this.state.labels.length) {\n      const type = isBreak ? \"BreakStatement\" : \"ContinueStatement\";\n      this.raise(Errors.IllegalBreakContinue, { at: node, type });\n    }\n  }\n\n  parseDebuggerStatement(\n    node: Undone<N.DebuggerStatement>,\n  ): N.DebuggerStatement {\n    this.next();\n    this.semicolon();\n    return this.finishNode(node, \"DebuggerStatement\");\n  }\n\n  parseHeaderExpression(this: Parser): N.Expression {\n    this.expect(tt.parenL);\n    const val = this.parseExpression();\n    this.expect(tt.parenR);\n    return val;\n  }\n\n  // https://tc39.es/ecma262/#prod-DoWhileStatement\n  parseDoWhileStatement(\n    this: Parser,\n    node: Undone<N.DoWhileStatement>,\n  ): N.DoWhileStatement {\n    this.next();\n    this.state.labels.push(loopLabel);\n\n    // Parse the loop body's body.\n    node.body =\n      // For the smartPipelines plugin: Disable topic references from outer\n      // contexts within the loop body. They are permitted in test expressions,\n      // outside of the loop body.\n      this.withSmartMixTopicForbiddingContext(() =>\n        // Parse the loop body's body.\n        this.parseStatement(),\n      );\n\n    this.state.labels.pop();\n\n    this.expect(tt._while);\n    node.test = this.parseHeaderExpression();\n    this.eat(tt.semi);\n    return this.finishNode(node, \"DoWhileStatement\");\n  }\n\n  // Disambiguating between a `for` and a `for`/`in` or `for`/`of`\n  // loop is non-trivial. Basically, we have to parse the init `var`\n  // statement or expression, disallowing the `in` operator (see\n  // the second parameter to `parseExpression`), and then check\n  // whether the next token is `in` or `of`. When there is no init\n  // part (semicolon immediately after the opening parenthesis), it\n  // is a regular `for` loop.\n\n  parseForStatement(\n    this: Parser,\n    node: Undone<N.ForStatement | N.ForInOf>,\n  ): N.ForLike {\n    this.next();\n    this.state.labels.push(loopLabel);\n\n    let awaitAt = null;\n\n    if (this.isAwaitAllowed() && this.eatContextual(tt._await)) {\n      awaitAt = this.state.lastTokStartLoc;\n    }\n    this.scope.enter(SCOPE_OTHER);\n    this.expect(tt.parenL);\n\n    if (this.match(tt.semi)) {\n      if (awaitAt !== null) {\n        this.unexpected(awaitAt);\n      }\n      return this.parseFor(node as Undone<N.ForStatement>, null);\n    }\n\n    const startsWithLet = this.isContextual(tt._let);\n    {\n      const startsWithAwaitUsing =\n        this.isContextual(tt._await) && this.startsAwaitUsing();\n      const starsWithUsingDeclaration =\n        startsWithAwaitUsing ||\n        (this.isContextual(tt._using) && this.startsUsingForOf());\n      const isLetOrUsing =\n        (startsWithLet && this.hasFollowingBindingAtom()) ||\n        starsWithUsingDeclaration;\n\n      if (this.match(tt._var) || this.match(tt._const) || isLetOrUsing) {\n        const initNode = this.startNode<N.VariableDeclaration>();\n        let kind;\n        if (startsWithAwaitUsing) {\n          kind = \"await using\";\n          if (!this.isAwaitAllowed()) {\n            this.raise(Errors.AwaitUsingNotInAsyncContext, {\n              at: this.state.startLoc,\n            });\n          }\n          this.next(); // eat 'await'\n        } else {\n          kind = this.state.value;\n        }\n        this.next();\n        this.parseVar(initNode, true, kind);\n        const init = this.finishNode(initNode, \"VariableDeclaration\");\n\n        const isForIn = this.match(tt._in);\n        if (isForIn && starsWithUsingDeclaration) {\n          this.raise(Errors.ForInUsing, { at: init });\n        }\n        if (\n          (isForIn || this.isContextual(tt._of)) &&\n          init.declarations.length === 1\n        ) {\n          return this.parseForIn(node as Undone<N.ForInOf>, init, awaitAt);\n        }\n        if (awaitAt !== null) {\n          this.unexpected(awaitAt);\n        }\n        return this.parseFor(node as Undone<N.ForStatement>, init);\n      }\n    }\n\n    // Check whether the first token is possibly a contextual keyword, so that\n    // we can forbid `for (async of` if this turns out to be a for-of loop.\n    const startsWithAsync = this.isContextual(tt._async);\n\n    const refExpressionErrors = new ExpressionErrors();\n    const init = this.parseExpression(true, refExpressionErrors);\n    const isForOf = this.isContextual(tt._of);\n    if (isForOf) {\n      // Check for leading tokens that are forbidden in for-of loops:\n      if (startsWithLet) {\n        this.raise(Errors.ForOfLet, { at: init });\n      }\n\n      if (\n        // `for await (async of []);` is allowed.\n        awaitAt === null &&\n        startsWithAsync &&\n        init.type === \"Identifier\"\n      ) {\n        // This catches the case where the `async` in `for (async of` was\n        // parsed as an identifier. If it was parsed as the start of an async\n        // arrow function (e.g. `for (async of => {} of []);`), the LVal check\n        // further down will raise a more appropriate error.\n        this.raise(Errors.ForOfAsync, { at: init });\n      }\n    }\n    if (isForOf || this.match(tt._in)) {\n      this.checkDestructuringPrivate(refExpressionErrors);\n      this.toAssignable(init, /* isLHS */ true);\n      const type = isForOf ? \"ForOfStatement\" : \"ForInStatement\";\n      this.checkLVal(init, { in: { type } });\n      return this.parseForIn(\n        node as Undone<N.ForInStatement | N.ForOfStatement>,\n        // @ts-expect-error init has been transformed to an assignable\n        init,\n        awaitAt,\n      );\n    } else {\n      this.checkExpressionErrors(refExpressionErrors, true);\n    }\n    if (awaitAt !== null) {\n      this.unexpected(awaitAt);\n    }\n    return this.parseFor(node as Undone<N.ForStatement>, init);\n  }\n\n  // https://tc39.es/ecma262/#prod-HoistableDeclaration\n  parseFunctionStatement(\n    this: Parser,\n    node: Undone<N.FunctionDeclaration>,\n    isAsync: boolean,\n    isHangingDeclaration: boolean,\n  ): N.FunctionDeclaration {\n    this.next(); // eat 'function'\n    return this.parseFunction(\n      node,\n      ParseFunctionFlag.Declaration |\n        (isHangingDeclaration ? ParseFunctionFlag.HangingDeclaration : 0) |\n        (isAsync ? ParseFunctionFlag.Async : 0),\n    );\n  }\n\n  // https://tc39.es/ecma262/#prod-IfStatement\n  parseIfStatement(this: Parser, node: Undone<N.IfStatement>) {\n    this.next();\n    node.test = this.parseHeaderExpression();\n    // Annex B.3.3\n    // https://tc39.es/ecma262/#sec-functiondeclarations-in-ifstatement-statement-clauses\n    node.consequent = this.parseStatementOrSloppyAnnexBFunctionDeclaration();\n    node.alternate = this.eat(tt._else)\n      ? this.parseStatementOrSloppyAnnexBFunctionDeclaration()\n      : null;\n    return this.finishNode(node, \"IfStatement\");\n  }\n\n  parseReturnStatement(this: Parser, node: Undone<N.ReturnStatement>) {\n    if (!this.prodParam.hasReturn && !this.options.allowReturnOutsideFunction) {\n      this.raise(Errors.IllegalReturn, { at: this.state.startLoc });\n    }\n\n    this.next();\n\n    // In `return` (and `break`/`continue`), the keywords with\n    // optional arguments, we eagerly look for a semicolon or the\n    // possibility to insert one.\n\n    if (this.isLineTerminator()) {\n      node.argument = null;\n    } else {\n      node.argument = this.parseExpression();\n      this.semicolon();\n    }\n\n    return this.finishNode(node, \"ReturnStatement\");\n  }\n\n  // https://tc39.es/ecma262/#prod-SwitchStatement\n  parseSwitchStatement(this: Parser, node: Undone<N.SwitchStatement>) {\n    this.next();\n    node.discriminant = this.parseHeaderExpression();\n    const cases: N.SwitchStatement[\"cases\"] = (node.cases = []);\n    this.expect(tt.braceL);\n    this.state.labels.push(switchLabel);\n    this.scope.enter(SCOPE_OTHER);\n\n    // Statements under must be grouped (by label) in SwitchCase\n    // nodes. `cur` is used to keep the node that we are currently\n    // adding statements to.\n\n    let cur;\n    for (let sawDefault; !this.match(tt.braceR); ) {\n      if (this.match(tt._case) || this.match(tt._default)) {\n        const isCase = this.match(tt._case);\n        if (cur) this.finishNode(cur, \"SwitchCase\");\n        // @ts-expect-error Fixme\n        cases.push((cur = this.startNode()));\n        cur.consequent = [];\n        this.next();\n        if (isCase) {\n          cur.test = this.parseExpression();\n        } else {\n          if (sawDefault) {\n            this.raise(Errors.MultipleDefaultsInSwitch, {\n              at: this.state.lastTokStartLoc,\n            });\n          }\n          sawDefault = true;\n          cur.test = null;\n        }\n        this.expect(tt.colon);\n      } else {\n        if (cur) {\n          cur.consequent.push(this.parseStatementListItem());\n        } else {\n          this.unexpected();\n        }\n      }\n    }\n    this.scope.exit();\n    if (cur) this.finishNode(cur, \"SwitchCase\");\n    this.next(); // Closing brace\n    this.state.labels.pop();\n    return this.finishNode(node, \"SwitchStatement\");\n  }\n\n  parseThrowStatement(this: Parser, node: Undone<N.ThrowStatement>) {\n    this.next();\n    if (this.hasPrecedingLineBreak()) {\n      this.raise(Errors.NewlineAfterThrow, { at: this.state.lastTokEndLoc });\n    }\n    node.argument = this.parseExpression();\n    this.semicolon();\n    return this.finishNode(node, \"ThrowStatement\");\n  }\n\n  parseCatchClauseParam(this: Parser): N.Pattern {\n    const param = this.parseBindingAtom();\n\n    this.scope.enter(\n      this.options.annexB && param.type === \"Identifier\"\n        ? SCOPE_SIMPLE_CATCH\n        : 0,\n    );\n    this.checkLVal(param, {\n      in: { type: \"CatchClause\" },\n      binding: BIND_CATCH_PARAM,\n    });\n\n    return param;\n  }\n\n  parseTryStatement(\n    this: Parser,\n    node: Undone<N.TryStatement>,\n  ): N.TryStatement {\n    this.next();\n\n    node.block = this.parseBlock();\n    node.handler = null;\n\n    if (this.match(tt._catch)) {\n      const clause = this.startNode<N.CatchClause>();\n      this.next();\n      if (this.match(tt.parenL)) {\n        this.expect(tt.parenL);\n        clause.param = this.parseCatchClauseParam();\n        this.expect(tt.parenR);\n      } else {\n        clause.param = null;\n        this.scope.enter(SCOPE_OTHER);\n      }\n\n      // Parse the catch clause's body.\n      clause.body =\n        // For the smartPipelines plugin: Disable topic references from outer\n        // contexts within the catch clause's body.\n        this.withSmartMixTopicForbiddingContext(() =>\n          // Parse the catch clause's body.\n          this.parseBlock(false, false),\n        );\n\n      this.scope.exit();\n      node.handler = this.finishNode(clause, \"CatchClause\");\n    }\n\n    node.finalizer = this.eat(tt._finally) ? this.parseBlock() : null;\n\n    if (!node.handler && !node.finalizer) {\n      this.raise(Errors.NoCatchOrFinally, { at: node });\n    }\n\n    return this.finishNode(node, \"TryStatement\");\n  }\n\n  // https://tc39.es/ecma262/#prod-VariableStatement\n  // https://tc39.es/ecma262/#prod-LexicalDeclaration\n  parseVarStatement(\n    this: Parser,\n    node: Undone<N.VariableDeclaration>,\n    kind: \"var\" | \"let\" | \"const\" | \"using\" | \"await using\",\n    allowMissingInitializer: boolean = false,\n  ): N.VariableDeclaration {\n    this.next();\n    this.parseVar(node, false, kind, allowMissingInitializer);\n    this.semicolon();\n    return this.finishNode(node, \"VariableDeclaration\");\n  }\n\n  // https://tc39.es/ecma262/#prod-WhileStatement\n  parseWhileStatement(\n    this: Parser,\n    node: Undone<N.WhileStatement>,\n  ): N.WhileStatement {\n    this.next();\n    node.test = this.parseHeaderExpression();\n    this.state.labels.push(loopLabel);\n\n    // Parse the loop body.\n    node.body =\n      // For the smartPipelines plugin:\n      // Disable topic references from outer contexts within the loop body.\n      // They are permitted in test expressions, outside of the loop body.\n      this.withSmartMixTopicForbiddingContext(() =>\n        // Parse loop body.\n        this.parseStatement(),\n      );\n\n    this.state.labels.pop();\n\n    return this.finishNode(node, \"WhileStatement\");\n  }\n\n  parseWithStatement(\n    this: Parser,\n    node: Undone<N.WithStatement>,\n  ): N.WithStatement {\n    if (this.state.strict) {\n      this.raise(Errors.StrictWith, { at: this.state.startLoc });\n    }\n    this.next();\n    node.object = this.parseHeaderExpression();\n\n    // Parse the statement body.\n    node.body =\n      // For the smartPipelines plugin:\n      // Disable topic references from outer contexts within the with statement's body.\n      // They are permitted in function default-parameter expressions, which are\n      // part of the outer context, outside of the with statement's body.\n      this.withSmartMixTopicForbiddingContext(() =>\n        // Parse the statement body.\n        this.parseStatement(),\n      );\n\n    return this.finishNode(node, \"WithStatement\");\n  }\n\n  parseEmptyStatement(node: Undone<N.EmptyStatement>): N.EmptyStatement {\n    this.next();\n    return this.finishNode(node, \"EmptyStatement\");\n  }\n\n  // https://tc39.es/ecma262/#prod-LabelledStatement\n  parseLabeledStatement(\n    this: Parser,\n    node: Undone<N.LabeledStatement>,\n    maybeName: string,\n    expr: N.Identifier,\n    flags: ParseStatementFlag,\n  ): N.LabeledStatement {\n    for (const label of this.state.labels) {\n      if (label.name === maybeName) {\n        this.raise(Errors.LabelRedeclaration, {\n          at: expr,\n          labelName: maybeName,\n        });\n      }\n    }\n\n    const kind = tokenIsLoop(this.state.type)\n      ? \"loop\"\n      : this.match(tt._switch)\n      ? \"switch\"\n      : null;\n    for (let i = this.state.labels.length - 1; i >= 0; i--) {\n      const label = this.state.labels[i];\n      if (label.statementStart === node.start) {\n        label.statementStart = this.state.start;\n        label.kind = kind;\n      } else {\n        break;\n      }\n    }\n\n    this.state.labels.push({\n      name: maybeName,\n      kind: kind,\n      statementStart: this.state.start,\n    });\n    // https://tc39.es/ecma262/#prod-LabelledItem\n    node.body =\n      flags & ParseStatementFlag.AllowLabeledFunction\n        ? this.parseStatementOrSloppyAnnexBFunctionDeclaration(true)\n        : this.parseStatement();\n\n    this.state.labels.pop();\n    node.label = expr;\n    return this.finishNode(node, \"LabeledStatement\");\n  }\n\n  parseExpressionStatement(\n    node: Undone<N.ExpressionStatement>,\n    expr: N.Expression,\n    /* eslint-disable-next-line @typescript-eslint/no-unused-vars -- used in TypeScript parser */\n    decorators: N.Decorator[] | null,\n  ) {\n    node.expression = expr;\n    this.semicolon();\n    return this.finishNode(node, \"ExpressionStatement\");\n  }\n\n  // Parse a semicolon-enclosed block of statements, handling `\"use\n  // strict\"` declarations when `allowDirectives` is true (used for\n  // function bodies).\n\n  parseBlock(\n    this: Parser,\n    allowDirectives: boolean = false,\n    createNewLexicalScope: boolean = true,\n    afterBlockParse?: (hasStrictModeDirective: boolean) => void,\n  ): N.BlockStatement {\n    const node = this.startNode<N.BlockStatement>();\n    if (allowDirectives) {\n      this.state.strictErrors.clear();\n    }\n    this.expect(tt.braceL);\n    if (createNewLexicalScope) {\n      this.scope.enter(SCOPE_OTHER);\n    }\n    this.parseBlockBody(\n      node,\n      allowDirectives,\n      false,\n      tt.braceR,\n      afterBlockParse,\n    );\n    if (createNewLexicalScope) {\n      this.scope.exit();\n    }\n    return this.finishNode(node, \"BlockStatement\");\n  }\n\n  isValidDirective(stmt: N.Statement): boolean {\n    return (\n      stmt.type === \"ExpressionStatement\" &&\n      stmt.expression.type === \"StringLiteral\" &&\n      !stmt.expression.extra.parenthesized\n    );\n  }\n\n  parseBlockBody(\n    this: Parser,\n    node: Undone<N.BlockStatementLike>,\n    allowDirectives: boolean | undefined | null,\n    topLevel: boolean,\n    end: TokenType,\n    afterBlockParse?: (hasStrictModeDirective: boolean) => void,\n  ): void {\n    const body: N.BlockStatementLike[\"body\"] = (node.body = []);\n    const directives: N.BlockStatementLike[\"directives\"] = (node.directives =\n      []);\n    this.parseBlockOrModuleBlockBody(\n      body,\n      allowDirectives ? directives : undefined,\n      topLevel,\n      end,\n      afterBlockParse,\n    );\n  }\n\n  // Undefined directives means that directives are not allowed.\n  // https://tc39.es/ecma262/#prod-Block\n  // https://tc39.es/ecma262/#prod-ModuleBody\n  parseBlockOrModuleBlockBody(\n    this: Parser,\n    body: N.Statement[],\n    directives: N.Directive[] | undefined | null,\n    topLevel: boolean,\n    end: TokenType,\n    afterBlockParse?: (hasStrictModeDirective: boolean) => void,\n  ): void {\n    const oldStrict = this.state.strict;\n    let hasStrictModeDirective = false;\n    let parsedNonDirective = false;\n\n    while (!this.match(end)) {\n      const stmt = topLevel\n        ? this.parseModuleItem()\n        : this.parseStatementListItem();\n\n      if (directives && !parsedNonDirective) {\n        if (this.isValidDirective(stmt)) {\n          const directive = this.stmtToDirective(stmt);\n          directives.push(directive);\n\n          if (\n            !hasStrictModeDirective &&\n            directive.value.value === \"use strict\"\n          ) {\n            hasStrictModeDirective = true;\n            this.setStrict(true);\n          }\n\n          continue;\n        }\n        parsedNonDirective = true;\n        // clear strict errors since the strict mode will not change within the block\n        this.state.strictErrors.clear();\n      }\n      body.push(stmt);\n    }\n\n    if (afterBlockParse) {\n      afterBlockParse.call(this, hasStrictModeDirective);\n    }\n\n    if (!oldStrict) {\n      this.setStrict(false);\n    }\n\n    this.next();\n  }\n\n  // Parse a regular `for` loop. The disambiguation code in\n  // `parseStatement` will already have parsed the init statement or\n  // expression.\n\n  parseFor(\n    this: Parser,\n    node: Undone<N.ForStatement>,\n    init?: N.VariableDeclaration | N.Expression | null,\n  ): N.ForStatement {\n    node.init = init;\n    this.semicolon(/* allowAsi */ false);\n    node.test = this.match(tt.semi) ? null : this.parseExpression();\n    this.semicolon(/* allowAsi */ false);\n    node.update = this.match(tt.parenR) ? null : this.parseExpression();\n    this.expect(tt.parenR);\n\n    // Parse the loop body.\n    node.body =\n      // For the smartPipelines plugin: Disable topic references from outer\n      // contexts within the loop body. They are permitted in test expressions,\n      // outside of the loop body.\n      this.withSmartMixTopicForbiddingContext(() =>\n        // Parse the loop body.\n        this.parseStatement(),\n      );\n\n    this.scope.exit();\n    this.state.labels.pop();\n\n    return this.finishNode(node, \"ForStatement\");\n  }\n\n  // Parse a `for`/`in` and `for`/`of` loop, which are almost\n  // same from parser's perspective.\n\n  parseForIn(\n    this: Parser,\n    node: Undone<N.ForInOf>,\n    init: N.VariableDeclaration | N.AssignmentPattern,\n    awaitAt?: Position | null,\n  ): N.ForInOf {\n    const isForIn = this.match(tt._in);\n    this.next();\n\n    if (isForIn) {\n      if (awaitAt !== null) this.unexpected(awaitAt);\n    } else {\n      node.await = awaitAt !== null;\n    }\n\n    if (\n      init.type === \"VariableDeclaration\" &&\n      init.declarations[0].init != null &&\n      (!isForIn ||\n        !this.options.annexB ||\n        this.state.strict ||\n        init.kind !== \"var\" ||\n        init.declarations[0].id.type !== \"Identifier\")\n    ) {\n      this.raise(Errors.ForInOfLoopInitializer, {\n        at: init,\n        type: isForIn ? \"ForInStatement\" : \"ForOfStatement\",\n      });\n    }\n\n    if (init.type === \"AssignmentPattern\") {\n      this.raise(Errors.InvalidLhs, {\n        at: init,\n        ancestor: { type: \"ForStatement\" },\n      });\n    }\n\n    node.left = init;\n    node.right = isForIn\n      ? this.parseExpression()\n      : this.parseMaybeAssignAllowIn();\n    this.expect(tt.parenR);\n\n    // Parse the loop body.\n    node.body =\n      // For the smartPipelines plugin:\n      // Disable topic references from outer contexts within the loop body.\n      // They are permitted in test expressions, outside of the loop body.\n      this.withSmartMixTopicForbiddingContext(() =>\n        // Parse loop body.\n        this.parseStatement(),\n      );\n\n    this.scope.exit();\n    this.state.labels.pop();\n\n    return this.finishNode(node, isForIn ? \"ForInStatement\" : \"ForOfStatement\");\n  }\n\n  // Parse a list of variable declarations.\n\n  parseVar(\n    this: Parser,\n    node: Undone<N.VariableDeclaration>,\n    isFor: boolean,\n    kind: \"var\" | \"let\" | \"const\" | \"using\" | \"await using\",\n    allowMissingInitializer: boolean = false,\n  ): Undone<N.VariableDeclaration> {\n    const declarations: N.VariableDeclarator[] = (node.declarations = []);\n    node.kind = kind;\n    for (;;) {\n      const decl = this.startNode<N.VariableDeclarator>();\n      this.parseVarId(decl, kind);\n      decl.init = !this.eat(tt.eq)\n        ? null\n        : isFor\n        ? this.parseMaybeAssignDisallowIn()\n        : this.parseMaybeAssignAllowIn();\n\n      if (decl.init === null && !allowMissingInitializer) {\n        if (\n          decl.id.type !== \"Identifier\" &&\n          !(isFor && (this.match(tt._in) || this.isContextual(tt._of)))\n        ) {\n          this.raise(Errors.DeclarationMissingInitializer, {\n            at: this.state.lastTokEndLoc,\n            kind: \"destructuring\",\n          });\n        } else if (\n          kind === \"const\" &&\n          !(this.match(tt._in) || this.isContextual(tt._of))\n        ) {\n          this.raise(Errors.DeclarationMissingInitializer, {\n            at: this.state.lastTokEndLoc,\n            kind: \"const\",\n          });\n        }\n      }\n      declarations.push(this.finishNode(decl, \"VariableDeclarator\"));\n      if (!this.eat(tt.comma)) break;\n    }\n    return node;\n  }\n\n  parseVarId(\n    this: Parser,\n    decl: Undone<N.VariableDeclarator>,\n    kind: \"var\" | \"let\" | \"const\" | \"using\" | \"await using\",\n  ): void {\n    const id = this.parseBindingAtom();\n    this.checkLVal(id, {\n      in: { type: \"VariableDeclarator\" },\n      binding: kind === \"var\" ? BIND_VAR : BIND_LEXICAL,\n    });\n    decl.id = id;\n  }\n\n  // https://tc39.es/ecma262/#prod-AsyncFunctionExpression\n  parseAsyncFunctionExpression(\n    this: Parser,\n    node: Undone<N.FunctionExpression>,\n  ): N.FunctionExpression {\n    return this.parseFunction(node, ParseFunctionFlag.Async);\n  }\n\n  // Parse a function declaration or expression (depending on the\n  // ParseFunctionFlag.Declaration flag).\n\n  parseFunction<T extends N.NormalFunction>(\n    this: Parser,\n    node: Undone<T>,\n    flags: ParseFunctionFlag = ParseFunctionFlag.Expression,\n  ): T {\n    const hangingDeclaration = flags & ParseFunctionFlag.HangingDeclaration;\n    const isDeclaration = !!(flags & ParseFunctionFlag.Declaration);\n    const requireId = isDeclaration && !(flags & ParseFunctionFlag.NullableId);\n    const isAsync = !!(flags & ParseFunctionFlag.Async);\n\n    this.initFunction(node, isAsync);\n\n    if (this.match(tt.star)) {\n      if (hangingDeclaration) {\n        this.raise(Errors.GeneratorInSingleStatementContext, {\n          at: this.state.startLoc,\n        });\n      }\n      this.next(); // eat *\n      node.generator = true;\n    }\n\n    if (isDeclaration) {\n      node.id = this.parseFunctionId(requireId);\n    }\n\n    const oldMaybeInArrowParameters = this.state.maybeInArrowParameters;\n    this.state.maybeInArrowParameters = false;\n    this.scope.enter(SCOPE_FUNCTION);\n    this.prodParam.enter(functionFlags(isAsync, node.generator));\n\n    if (!isDeclaration) {\n      node.id = this.parseFunctionId();\n    }\n\n    this.parseFunctionParams(node, /* isConstructor */ false);\n\n    // For the smartPipelines plugin: Disable topic references from outer\n    // contexts within the function body. They are permitted in function\n    // default-parameter expressions, outside of the function body.\n    this.withSmartMixTopicForbiddingContext(() => {\n      // Parse the function body.\n      this.parseFunctionBodyAndFinish(\n        node,\n        isDeclaration ? \"FunctionDeclaration\" : \"FunctionExpression\",\n      );\n    });\n\n    this.prodParam.exit();\n    this.scope.exit();\n\n    if (isDeclaration && !hangingDeclaration) {\n      // We need to register this _after_ parsing the function body\n      // because of TypeScript body-less function declarations,\n      // which shouldn't be added to the scope.\n      this.registerFunctionStatementId(node as T);\n    }\n\n    this.state.maybeInArrowParameters = oldMaybeInArrowParameters;\n    return node as T;\n  }\n\n  parseFunctionId(requireId?: boolean): N.Identifier | undefined | null {\n    return requireId || tokenIsIdentifier(this.state.type)\n      ? this.parseIdentifier()\n      : null;\n  }\n\n  parseFunctionParams(\n    this: Parser,\n    node: Undone<N.Function>,\n    isConstructor?: boolean,\n  ): void {\n    this.expect(tt.parenL);\n    this.expressionScope.enter(newParameterDeclarationScope());\n    node.params = this.parseBindingList(\n      tt.parenR,\n      charCodes.rightParenthesis,\n      ParseBindingListFlags.IS_FUNCTION_PARAMS |\n        (isConstructor ? ParseBindingListFlags.IS_CONSTRUCTOR_PARAMS : 0),\n    );\n\n    this.expressionScope.exit();\n  }\n\n  registerFunctionStatementId(node: N.Function): void {\n    if (!node.id) return;\n\n    // If it is a regular function declaration in sloppy mode, then it is\n    // subject to Annex B semantics (BIND_FUNCTION). Otherwise, the binding\n    // mode depends on properties of the current scope (see\n    // treatFunctionsAsVar).\n    this.scope.declareName(\n      node.id.name,\n      !this.options.annexB || this.state.strict || node.generator || node.async\n        ? this.scope.treatFunctionsAsVar\n          ? BIND_VAR\n          : BIND_LEXICAL\n        : BIND_FUNCTION,\n      node.id.loc.start,\n    );\n  }\n\n  // Parse a class declaration or literal (depending on the\n  // `isStatement` parameter).\n\n  parseClass<T extends N.Class>(\n    this: Parser,\n    node: Undone<T>,\n    isStatement: /* T === ClassDeclaration */ boolean,\n    optionalId?: boolean,\n  ): T {\n    this.next(); // 'class'\n\n    // A class definition is always strict mode code.\n    const oldStrict = this.state.strict;\n    this.state.strict = true;\n\n    this.parseClassId(node, isStatement, optionalId);\n    this.parseClassSuper(node);\n    // this.state.strict is restored in parseClassBody\n    node.body = this.parseClassBody(!!node.superClass, oldStrict);\n\n    return this.finishNode(\n      node,\n      isStatement ? \"ClassDeclaration\" : \"ClassExpression\",\n    );\n  }\n\n  isClassProperty(): boolean {\n    return this.match(tt.eq) || this.match(tt.semi) || this.match(tt.braceR);\n  }\n\n  isClassMethod(): boolean {\n    return this.match(tt.parenL);\n  }\n\n  isNonstaticConstructor(method: N.ClassMethod | N.ClassProperty): boolean {\n    return (\n      !method.computed &&\n      !method.static &&\n      (method.key.name === \"constructor\" || // Identifier\n        method.key.value === \"constructor\") // String literal\n    );\n  }\n\n  // https://tc39.es/ecma262/#prod-ClassBody\n  parseClassBody(\n    this: Parser,\n    hadSuperClass: boolean,\n    oldStrict: boolean,\n  ): N.ClassBody {\n    this.classScope.enter();\n\n    const state: N.ParseClassMemberState = {\n      hadConstructor: false,\n      hadSuperClass,\n    };\n    let decorators: N.Decorator[] = [];\n    const classBody = this.startNode<N.ClassBody>();\n    classBody.body = [];\n\n    this.expect(tt.braceL);\n\n    // For the smartPipelines plugin: Disable topic references from outer\n    // contexts within the class body.\n    this.withSmartMixTopicForbiddingContext(() => {\n      // Parse the contents within the braces.\n      while (!this.match(tt.braceR)) {\n        if (this.eat(tt.semi)) {\n          if (decorators.length > 0) {\n            throw this.raise(Errors.DecoratorSemicolon, {\n              at: this.state.lastTokEndLoc,\n            });\n          }\n          continue;\n        }\n\n        if (this.match(tt.at)) {\n          decorators.push(this.parseDecorator());\n          continue;\n        }\n\n        const member = this.startNode<N.ClassMember>();\n\n        // steal the decorators if there are any\n        if (decorators.length) {\n          // @ts-expect-error Fixme\n          member.decorators = decorators;\n          this.resetStartLocationFromNode(member, decorators[0]);\n          decorators = [];\n        }\n\n        this.parseClassMember(classBody, member, state);\n\n        if (\n          // @ts-expect-error Fixme\n          member.kind === \"constructor\" &&\n          // @ts-expect-error Fixme\n          member.decorators &&\n          // @ts-expect-error Fixme\n          member.decorators.length > 0\n        ) {\n          this.raise(Errors.DecoratorConstructor, { at: member });\n        }\n      }\n    });\n\n    this.state.strict = oldStrict;\n\n    this.next(); // eat `}`\n\n    if (decorators.length) {\n      throw this.raise(Errors.TrailingDecorator, { at: this.state.startLoc });\n    }\n\n    this.classScope.exit();\n\n    return this.finishNode(classBody, \"ClassBody\");\n  }\n\n  // returns true if the current identifier is a method/field name,\n  // false if it is a modifier\n  parseClassMemberFromModifier(\n    this: Parser,\n    classBody: Undone<N.ClassBody>,\n    member: Undone<N.ClassMember>,\n  ): boolean {\n    const key = this.parseIdentifier(true); // eats the modifier\n\n    if (this.isClassMethod()) {\n      const method: N.ClassMethod = member as any;\n\n      // a method named like the modifier\n      method.kind = \"method\";\n      method.computed = false;\n      method.key = key;\n      method.static = false;\n      this.pushClassMethod(\n        classBody,\n        method,\n        false,\n        false,\n        /* isConstructor */ false,\n        false,\n      );\n      return true;\n    } else if (this.isClassProperty()) {\n      const prop: N.ClassProperty = member as any;\n\n      // a property named like the modifier\n      prop.computed = false;\n      prop.key = key;\n      prop.static = false;\n      classBody.body.push(this.parseClassProperty(prop));\n      return true;\n    }\n    this.resetPreviousNodeTrailingComments(key);\n    return false;\n  }\n\n  parseClassMember(\n    this: Parser,\n    classBody: Undone<N.ClassBody>,\n    member: Undone<N.ClassMember>,\n    state: N.ParseClassMemberState,\n  ): void {\n    const isStatic = this.isContextual(tt._static);\n\n    if (isStatic) {\n      if (this.parseClassMemberFromModifier(classBody, member)) {\n        // a class element named 'static'\n        return;\n      }\n      if (this.eat(tt.braceL)) {\n        this.parseClassStaticBlock(classBody, member as any as N.StaticBlock);\n        return;\n      }\n    }\n\n    this.parseClassMemberWithIsStatic(classBody, member, state, isStatic);\n  }\n\n  parseClassMemberWithIsStatic(\n    this: Parser,\n    classBody: Undone<N.ClassBody>,\n    member: Undone<N.ClassMember>,\n    state: N.ParseClassMemberState,\n    isStatic: boolean,\n  ) {\n    const publicMethod = member as N.ClassMethod;\n    const privateMethod = member as N.ClassPrivateMethod;\n    const publicProp = member as N.ClassProperty;\n    const privateProp = member as N.ClassPrivateProperty;\n    const accessorProp = member as N.ClassAccessorProperty;\n\n    const method: typeof publicMethod | typeof privateMethod = publicMethod;\n    const publicMember: typeof publicMethod | typeof publicProp = publicMethod;\n\n    member.static = isStatic;\n    this.parsePropertyNamePrefixOperator(member);\n\n    if (this.eat(tt.star)) {\n      // a generator\n      method.kind = \"method\";\n      const isPrivateName = this.match(tt.privateName);\n      this.parseClassElementName(method);\n\n      if (isPrivateName) {\n        // Private generator method\n        this.pushClassPrivateMethod(classBody, privateMethod, true, false);\n        return;\n      }\n\n      if (this.isNonstaticConstructor(publicMethod)) {\n        this.raise(Errors.ConstructorIsGenerator, {\n          at: publicMethod.key,\n        });\n      }\n\n      this.pushClassMethod(\n        classBody,\n        publicMethod,\n        true,\n        false,\n        /* isConstructor */ false,\n        false,\n      );\n\n      return;\n    }\n\n    const isContextual =\n      tokenIsIdentifier(this.state.type) && !this.state.containsEsc;\n    const isPrivate = this.match(tt.privateName);\n    const key = this.parseClassElementName(member);\n    const maybeQuestionTokenStartLoc = this.state.startLoc;\n\n    this.parsePostMemberNameModifiers(publicMember);\n\n    if (this.isClassMethod()) {\n      method.kind = \"method\";\n\n      if (isPrivate) {\n        this.pushClassPrivateMethod(classBody, privateMethod, false, false);\n        return;\n      }\n\n      // a normal method\n      const isConstructor = this.isNonstaticConstructor(publicMethod);\n      let allowsDirectSuper = false;\n      if (isConstructor) {\n        publicMethod.kind = \"constructor\";\n\n        // TypeScript allows multiple overloaded constructor declarations.\n        if (state.hadConstructor && !this.hasPlugin(\"typescript\")) {\n          this.raise(Errors.DuplicateConstructor, { at: key });\n        }\n        if (isConstructor && this.hasPlugin(\"typescript\") && member.override) {\n          this.raise(Errors.OverrideOnConstructor, { at: key });\n        }\n        state.hadConstructor = true;\n        allowsDirectSuper = state.hadSuperClass;\n      }\n\n      this.pushClassMethod(\n        classBody,\n        publicMethod,\n        false,\n        false,\n        isConstructor,\n        allowsDirectSuper,\n      );\n    } else if (this.isClassProperty()) {\n      if (isPrivate) {\n        this.pushClassPrivateProperty(classBody, privateProp);\n      } else {\n        this.pushClassProperty(classBody, publicProp);\n      }\n    } else if (\n      isContextual &&\n      key.name === \"async\" &&\n      !this.isLineTerminator()\n    ) {\n      // an async method\n      this.resetPreviousNodeTrailingComments(key);\n      const isGenerator = this.eat(tt.star);\n\n      if (publicMember.optional) {\n        this.unexpected(maybeQuestionTokenStartLoc);\n      }\n\n      method.kind = \"method\";\n      // The so-called parsed name would have been \"async\": get the real name.\n      const isPrivate = this.match(tt.privateName);\n      this.parseClassElementName(method);\n      this.parsePostMemberNameModifiers(publicMember);\n\n      if (isPrivate) {\n        // private async method\n        this.pushClassPrivateMethod(\n          classBody,\n          privateMethod,\n          isGenerator,\n          true,\n        );\n      } else {\n        if (this.isNonstaticConstructor(publicMethod)) {\n          this.raise(Errors.ConstructorIsAsync, { at: publicMethod.key });\n        }\n\n        this.pushClassMethod(\n          classBody,\n          publicMethod,\n          isGenerator,\n          true,\n          /* isConstructor */ false,\n          false,\n        );\n      }\n    } else if (\n      isContextual &&\n      (key.name === \"get\" || key.name === \"set\") &&\n      !(this.match(tt.star) && this.isLineTerminator())\n    ) {\n      // `get\\n*` is an uninitialized property named 'get' followed by a generator.\n      // a getter or setter\n      this.resetPreviousNodeTrailingComments(key);\n      method.kind = key.name;\n      // The so-called parsed name would have been \"get/set\": get the real name.\n      const isPrivate = this.match(tt.privateName);\n      this.parseClassElementName(publicMethod);\n\n      if (isPrivate) {\n        // private getter/setter\n        this.pushClassPrivateMethod(classBody, privateMethod, false, false);\n      } else {\n        if (this.isNonstaticConstructor(publicMethod)) {\n          this.raise(Errors.ConstructorIsAccessor, { at: publicMethod.key });\n        }\n        this.pushClassMethod(\n          classBody,\n          publicMethod,\n          false,\n          false,\n          /* isConstructor */ false,\n          false,\n        );\n      }\n\n      this.checkGetterSetterParams(publicMethod);\n    } else if (\n      isContextual &&\n      key.name === \"accessor\" &&\n      !this.isLineTerminator()\n    ) {\n      this.expectPlugin(\"decoratorAutoAccessors\");\n      this.resetPreviousNodeTrailingComments(key);\n\n      // The so-called parsed name would have been \"accessor\": get the real name.\n      const isPrivate = this.match(tt.privateName);\n      this.parseClassElementName(publicProp);\n      this.pushClassAccessorProperty(classBody, accessorProp, isPrivate);\n    } else if (this.isLineTerminator()) {\n      // an uninitialized class property (due to ASI, since we don't otherwise recognize the next token)\n      if (isPrivate) {\n        this.pushClassPrivateProperty(classBody, privateProp);\n      } else {\n        this.pushClassProperty(classBody, publicProp);\n      }\n    } else {\n      this.unexpected();\n    }\n  }\n\n  // https://tc39.es/ecma262/#prod-ClassElementName\n  parseClassElementName(\n    this: Parser,\n    member: Undone<N.ClassMember>,\n  ): N.Expression | N.Identifier {\n    const { type, value } = this.state;\n    if (\n      (type === tt.name || type === tt.string) &&\n      member.static &&\n      value === \"prototype\"\n    ) {\n      this.raise(Errors.StaticPrototype, { at: this.state.startLoc });\n    }\n\n    if (type === tt.privateName) {\n      if (value === \"constructor\") {\n        this.raise(Errors.ConstructorClassPrivateField, {\n          at: this.state.startLoc,\n        });\n      }\n      const key = this.parsePrivateName();\n      member.key = key;\n      return key;\n    }\n\n    return this.parsePropertyName(member);\n  }\n\n  parseClassStaticBlock(\n    this: Parser,\n    classBody: Undone<N.ClassBody>,\n    member: Undone<\n      N.StaticBlock & {\n        decorators?: Array<N.Decorator>;\n      }\n    >,\n  ) {\n    // Start a new lexical scope\n    this.scope.enter(SCOPE_CLASS | SCOPE_STATIC_BLOCK | SCOPE_SUPER);\n    // Start a new scope with regard to loop labels\n    const oldLabels = this.state.labels;\n    this.state.labels = [];\n    // ClassStaticBlockStatementList:\n    //   StatementList[~Yield, ~Await, ~Return] opt\n    this.prodParam.enter(PARAM);\n    const body: N.Node[] = (member.body = []);\n    this.parseBlockOrModuleBlockBody(body, undefined, false, tt.braceR);\n    this.prodParam.exit();\n    this.scope.exit();\n    this.state.labels = oldLabels;\n    classBody.body.push(this.finishNode<N.StaticBlock>(member, \"StaticBlock\"));\n    if (member.decorators?.length) {\n      this.raise(Errors.DecoratorStaticBlock, { at: member });\n    }\n  }\n\n  pushClassProperty(\n    this: Parser,\n    classBody: Undone<N.ClassBody>,\n    prop: N.ClassProperty,\n  ) {\n    if (\n      !prop.computed &&\n      (prop.key.name === \"constructor\" || prop.key.value === \"constructor\")\n    ) {\n      // Non-computed field, which is either an identifier named \"constructor\"\n      // or a string literal named \"constructor\"\n      this.raise(Errors.ConstructorClassField, { at: prop.key });\n    }\n\n    classBody.body.push(this.parseClassProperty(prop));\n  }\n\n  pushClassPrivateProperty(\n    this: Parser,\n    classBody: Undone<N.ClassBody>,\n    prop: Undone<N.ClassPrivateProperty>,\n  ) {\n    const node = this.parseClassPrivateProperty(prop);\n    classBody.body.push(node);\n\n    this.classScope.declarePrivateName(\n      this.getPrivateNameSV(node.key),\n      CLASS_ELEMENT_OTHER,\n      node.key.loc.start,\n    );\n  }\n\n  pushClassAccessorProperty(\n    this: Parser,\n    classBody: Undone<N.ClassBody>,\n    prop: N.ClassAccessorProperty,\n    isPrivate: boolean,\n  ) {\n    if (!isPrivate && !prop.computed) {\n      // Not private, so not node is not a PrivateName and we can safely cast\n      const key = prop.key as N.Expression;\n\n      if (key.name === \"constructor\" || key.value === \"constructor\") {\n        // Non-computed field, which is either an identifier named \"constructor\"\n        // or a string literal named \"constructor\"\n        this.raise(Errors.ConstructorClassField, { at: key });\n      }\n    }\n\n    const node = this.parseClassAccessorProperty(prop);\n    classBody.body.push(node);\n\n    if (isPrivate) {\n      this.classScope.declarePrivateName(\n        this.getPrivateNameSV(node.key),\n        CLASS_ELEMENT_OTHER,\n        node.key.loc.start,\n      );\n    }\n  }\n\n  pushClassMethod(\n    this: Parser,\n    classBody: Undone<N.ClassBody>,\n    method: Undone<N.ClassMethod>,\n    isGenerator: boolean,\n    isAsync: boolean,\n    isConstructor: boolean,\n    allowsDirectSuper: boolean,\n  ): void {\n    classBody.body.push(\n      this.parseMethod(\n        method,\n        isGenerator,\n        isAsync,\n        isConstructor,\n        allowsDirectSuper,\n        \"ClassMethod\",\n        true,\n      ),\n    );\n  }\n\n  pushClassPrivateMethod(\n    this: Parser,\n    classBody: Undone<N.ClassBody>,\n    method: Undone<N.ClassPrivateMethod>,\n    isGenerator: boolean,\n    isAsync: boolean,\n  ): void {\n    const node = this.parseMethod(\n      method,\n      isGenerator,\n      isAsync,\n      /* isConstructor */ false,\n      false,\n      \"ClassPrivateMethod\",\n      true,\n    );\n    classBody.body.push(node);\n\n    const kind =\n      node.kind === \"get\"\n        ? node.static\n          ? CLASS_ELEMENT_STATIC_GETTER\n          : CLASS_ELEMENT_INSTANCE_GETTER\n        : node.kind === \"set\"\n        ? node.static\n          ? CLASS_ELEMENT_STATIC_SETTER\n          : CLASS_ELEMENT_INSTANCE_SETTER\n        : CLASS_ELEMENT_OTHER;\n    this.declareClassPrivateMethodInScope(node, kind);\n  }\n\n  declareClassPrivateMethodInScope(\n    node: Undone<\n      N.ClassPrivateMethod | N.EstreeMethodDefinition | N.TSDeclareMethod\n    >,\n    kind: number,\n  ) {\n    this.classScope.declarePrivateName(\n      this.getPrivateNameSV(node.key),\n      kind,\n      node.key.loc.start,\n    );\n  }\n\n  // Overridden in typescript.js\n  parsePostMemberNameModifiers(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    methodOrProp: Undone<N.ClassMethod | N.ClassProperty>,\n  ): void {}\n\n  // https://tc39.es/ecma262/#prod-FieldDefinition\n  parseClassPrivateProperty(\n    this: Parser,\n    node: Undone<N.ClassPrivateProperty>,\n  ): N.ClassPrivateProperty {\n    this.parseInitializer(node);\n    this.semicolon();\n    return this.finishNode(node, \"ClassPrivateProperty\");\n  }\n\n  // https://tc39.es/ecma262/#prod-FieldDefinition\n  parseClassProperty(this: Parser, node: N.ClassProperty): N.ClassProperty {\n    this.parseInitializer(node);\n    this.semicolon();\n    return this.finishNode(node, \"ClassProperty\");\n  }\n\n  parseClassAccessorProperty(\n    this: Parser,\n    node: N.ClassAccessorProperty,\n  ): N.ClassAccessorProperty {\n    this.parseInitializer(node);\n    this.semicolon();\n    return this.finishNode(node, \"ClassAccessorProperty\");\n  }\n\n  // https://tc39.es/ecma262/#prod-Initializer\n  parseInitializer(\n    this: Parser,\n    node: Undone<\n      N.ClassProperty | N.ClassPrivateProperty | N.ClassAccessorProperty\n    >,\n  ): void {\n    this.scope.enter(SCOPE_CLASS | SCOPE_SUPER);\n    this.expressionScope.enter(newExpressionScope());\n    this.prodParam.enter(PARAM);\n    node.value = this.eat(tt.eq) ? this.parseMaybeAssignAllowIn() : null;\n    this.expressionScope.exit();\n    this.prodParam.exit();\n    this.scope.exit();\n  }\n\n  parseClassId(\n    node: Undone<N.Class>,\n    isStatement: boolean,\n    optionalId?: boolean | null,\n    bindingType: BindingTypes = BIND_CLASS,\n  ): void {\n    if (tokenIsIdentifier(this.state.type)) {\n      node.id = this.parseIdentifier();\n      if (isStatement) {\n        this.declareNameFromIdentifier(node.id, bindingType);\n      }\n    } else {\n      if (optionalId || !isStatement) {\n        node.id = null;\n      } else {\n        throw this.raise(Errors.MissingClassName, { at: this.state.startLoc });\n      }\n    }\n  }\n\n  // https://tc39.es/ecma262/#prod-ClassHeritage\n  parseClassSuper(this: Parser, node: Undone<N.Class>): void {\n    node.superClass = this.eat(tt._extends) ? this.parseExprSubscripts() : null;\n  }\n\n  // Parses module export declaration.\n  // https://tc39.es/ecma262/#prod-ExportDeclaration\n\n  parseExport(\n    this: Parser,\n    node: Undone<\n      | N.ExportDefaultDeclaration\n      | N.ExportAllDeclaration\n      | N.ExportNamedDeclaration\n    >,\n    decorators: N.Decorator[] | null,\n  ): N.AnyExport {\n    const maybeDefaultIdentifier = this.parseMaybeImportPhase(\n      node,\n      /* isExport */ true,\n    );\n    const hasDefault = this.maybeParseExportDefaultSpecifier(\n      node,\n      maybeDefaultIdentifier,\n    );\n    const parseAfterDefault = !hasDefault || this.eat(tt.comma);\n    const hasStar =\n      parseAfterDefault &&\n      this.eatExportStar(\n        // @ts-expect-error todo(flow->ts)\n        node,\n      );\n    const hasNamespace =\n      hasStar &&\n      this.maybeParseExportNamespaceSpecifier(\n        // @ts-expect-error todo(flow->ts)\n        node,\n      );\n    const parseAfterNamespace =\n      parseAfterDefault && (!hasNamespace || this.eat(tt.comma));\n    const isFromRequired = hasDefault || hasStar;\n\n    if (hasStar && !hasNamespace) {\n      if (hasDefault) this.unexpected();\n      if (decorators) {\n        throw this.raise(Errors.UnsupportedDecoratorExport, { at: node });\n      }\n      this.parseExportFrom(node as Undone<N.ExportNamedDeclaration>, true);\n\n      return this.finishNode(node, \"ExportAllDeclaration\");\n    }\n\n    const hasSpecifiers = this.maybeParseExportNamedSpecifiers(\n      // @ts-expect-error todo(flow->ts)\n      node,\n    );\n\n    if (hasDefault && parseAfterDefault && !hasStar && !hasSpecifiers) {\n      this.unexpected(null, tt.braceL);\n    }\n\n    if (hasNamespace && parseAfterNamespace) {\n      this.unexpected(null, tt._from);\n    }\n\n    let hasDeclaration;\n    if (isFromRequired || hasSpecifiers) {\n      hasDeclaration = false;\n      if (decorators) {\n        throw this.raise(Errors.UnsupportedDecoratorExport, { at: node });\n      }\n      this.parseExportFrom(\n        node as Undone<N.ExportNamedDeclaration>,\n        isFromRequired,\n      );\n    } else {\n      hasDeclaration = this.maybeParseExportDeclaration(\n        node as Undone<N.ExportNamedDeclaration>,\n      );\n    }\n\n    if (isFromRequired || hasSpecifiers || hasDeclaration) {\n      const node2 = node as Undone<N.ExportNamedDeclaration>;\n      this.checkExport(node2, true, false, !!node2.source);\n      if (node2.declaration?.type === \"ClassDeclaration\") {\n        this.maybeTakeDecorators(decorators, node2.declaration, node2);\n      } else if (decorators) {\n        throw this.raise(Errors.UnsupportedDecoratorExport, { at: node });\n      }\n      return this.finishNode(node2, \"ExportNamedDeclaration\");\n    }\n\n    if (this.eat(tt._default)) {\n      const node2 = node as Undone<N.ExportDefaultDeclaration>;\n      // export default ...\n      const decl = this.parseExportDefaultExpression();\n      node2.declaration = decl;\n\n      if (decl.type === \"ClassDeclaration\") {\n        this.maybeTakeDecorators(decorators, decl as N.ClassDeclaration, node2);\n      } else if (decorators) {\n        throw this.raise(Errors.UnsupportedDecoratorExport, { at: node });\n      }\n\n      this.checkExport(node2, true, true);\n\n      return this.finishNode(node2, \"ExportDefaultDeclaration\");\n    }\n\n    this.unexpected(null, tt.braceL);\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  eatExportStar(node: N.Node): boolean {\n    return this.eat(tt.star);\n  }\n\n  maybeParseExportDefaultSpecifier(\n    node: Undone<\n      | N.ExportDefaultDeclaration\n      | N.ExportAllDeclaration\n      | N.ExportNamedDeclaration\n    >,\n    maybeDefaultIdentifier: N.Identifier | null,\n  ): node is Undone<N.ExportNamedDeclaration> {\n    if (maybeDefaultIdentifier || this.isExportDefaultSpecifier()) {\n      // export defaultObj ...\n      this.expectPlugin(\"exportDefaultFrom\", maybeDefaultIdentifier?.loc.start);\n      const id = maybeDefaultIdentifier || this.parseIdentifier(true);\n      const specifier = this.startNodeAtNode<N.ExportDefaultSpecifier>(id);\n      specifier.exported = id;\n      (node as Undone<N.ExportNamedDeclaration>).specifiers = [\n        this.finishNode(specifier, \"ExportDefaultSpecifier\"),\n      ];\n      return true;\n    }\n    return false;\n  }\n\n  maybeParseExportNamespaceSpecifier(node: N.Node): boolean {\n    if (this.isContextual(tt._as)) {\n      if (!node.specifiers) node.specifiers = [];\n\n      const specifier = this.startNodeAt(this.state.lastTokStartLoc);\n\n      this.next();\n\n      specifier.exported = this.parseModuleExportName();\n      node.specifiers.push(\n        this.finishNode(specifier, \"ExportNamespaceSpecifier\"),\n      );\n      return true;\n    }\n    return false;\n  }\n\n  maybeParseExportNamedSpecifiers(node: N.Node): boolean {\n    if (this.match(tt.braceL)) {\n      if (!node.specifiers) node.specifiers = [];\n      const isTypeExport = node.exportKind === \"type\";\n      node.specifiers.push(...this.parseExportSpecifiers(isTypeExport));\n\n      node.source = null;\n      node.declaration = null;\n      if (this.hasPlugin(\"importAssertions\")) {\n        node.assertions = [];\n      }\n\n      return true;\n    }\n    return false;\n  }\n\n  maybeParseExportDeclaration(\n    this: Parser,\n    node: Undone<N.ExportNamedDeclaration>,\n  ): boolean {\n    if (this.shouldParseExportDeclaration()) {\n      node.specifiers = [];\n      node.source = null;\n      if (this.hasPlugin(\"importAssertions\")) {\n        node.assertions = [];\n      }\n      node.declaration = this.parseExportDeclaration(node);\n      return true;\n    }\n    return false;\n  }\n\n  isAsyncFunction(): boolean {\n    if (!this.isContextual(tt._async)) return false;\n    const next = this.nextTokenInLineStart();\n    return this.isUnparsedContextual(next, \"function\");\n  }\n\n  parseExportDefaultExpression(this: Parser): N.Expression | N.Declaration {\n    const expr = this.startNode();\n\n    if (this.match(tt._function)) {\n      this.next();\n      return this.parseFunction(\n        expr as Undone<N.FunctionDeclaration>,\n        ParseFunctionFlag.Declaration | ParseFunctionFlag.NullableId,\n      );\n    } else if (this.isAsyncFunction()) {\n      this.next(); // eat 'async'\n      this.next(); // eat 'function'\n      return this.parseFunction(\n        expr as Undone<N.FunctionDeclaration>,\n        ParseFunctionFlag.Declaration |\n          ParseFunctionFlag.NullableId |\n          ParseFunctionFlag.Async,\n      );\n    }\n\n    if (this.match(tt._class)) {\n      return this.parseClass(expr as Undone<N.ClassExpression>, true, true);\n    }\n\n    if (this.match(tt.at)) {\n      if (\n        this.hasPlugin(\"decorators\") &&\n        this.getPluginOption(\"decorators\", \"decoratorsBeforeExport\") === true\n      ) {\n        this.raise(Errors.DecoratorBeforeExport, { at: this.state.startLoc });\n      }\n      return this.parseClass(\n        this.maybeTakeDecorators(\n          this.parseDecorators(false),\n          this.startNode<N.ClassDeclaration>(),\n        ),\n        true,\n        true,\n      );\n    }\n\n    if (this.match(tt._const) || this.match(tt._var) || this.isLet()) {\n      throw this.raise(Errors.UnsupportedDefaultExport, {\n        at: this.state.startLoc,\n      });\n    }\n\n    const res = this.parseMaybeAssignAllowIn();\n    this.semicolon();\n    return res;\n  }\n\n  // https://tc39.es/ecma262/#prod-ExportDeclaration\n  parseExportDeclaration(\n    this: Parser,\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    node: Undone<N.ExportNamedDeclaration>,\n  ): N.Declaration | undefined | null {\n    if (this.match(tt._class)) {\n      const node = this.parseClass(\n        this.startNode<N.ClassDeclaration>(),\n        true,\n        false,\n      );\n      return node;\n    }\n    return this.parseStatementListItem() as N.Declaration;\n  }\n\n  isExportDefaultSpecifier(): boolean {\n    const { type } = this.state;\n    if (tokenIsIdentifier(type)) {\n      if ((type === tt._async && !this.state.containsEsc) || type === tt._let) {\n        return false;\n      }\n      if (\n        (type === tt._type || type === tt._interface) &&\n        !this.state.containsEsc\n      ) {\n        const { type: nextType } = this.lookahead();\n        // If we see any variable name other than `from` after `type` keyword,\n        // we consider it as flow/typescript type exports\n        // note that this approach may fail on some pedantic cases\n        // export type from = number\n        if (\n          (tokenIsIdentifier(nextType) && nextType !== tt._from) ||\n          nextType === tt.braceL\n        ) {\n          this.expectOnePlugin([\"flow\", \"typescript\"]);\n          return false;\n        }\n      }\n    } else if (!this.match(tt._default)) {\n      return false;\n    }\n\n    const next = this.nextTokenStart();\n    const hasFrom = this.isUnparsedContextual(next, \"from\");\n    if (\n      this.input.charCodeAt(next) === charCodes.comma ||\n      (tokenIsIdentifier(this.state.type) && hasFrom)\n    ) {\n      return true;\n    }\n    // lookahead again when `export default from` is seen\n    if (this.match(tt._default) && hasFrom) {\n      const nextAfterFrom = this.input.charCodeAt(\n        this.nextTokenStartSince(next + 4),\n      );\n      return (\n        nextAfterFrom === charCodes.quotationMark ||\n        nextAfterFrom === charCodes.apostrophe\n      );\n    }\n    return false;\n  }\n\n  parseExportFrom(\n    this: Parser,\n    node: Undone<N.ExportNamedDeclaration>,\n    expect?: boolean,\n  ): void {\n    if (this.eatContextual(tt._from)) {\n      node.source = this.parseImportSource();\n      this.checkExport(node);\n      this.maybeParseImportAttributes(node);\n      this.checkJSONModuleImport(node);\n    } else if (expect) {\n      this.unexpected();\n    }\n\n    this.semicolon();\n  }\n\n  shouldParseExportDeclaration(): boolean {\n    const { type } = this.state;\n    if (type === tt.at) {\n      this.expectOnePlugin([\"decorators\", \"decorators-legacy\"]);\n      if (this.hasPlugin(\"decorators\")) {\n        if (\n          this.getPluginOption(\"decorators\", \"decoratorsBeforeExport\") === true\n        ) {\n          this.raise(Errors.DecoratorBeforeExport, {\n            at: this.state.startLoc,\n          });\n        }\n\n        return true;\n      }\n    }\n\n    return (\n      type === tt._var ||\n      type === tt._const ||\n      type === tt._function ||\n      type === tt._class ||\n      this.isLet() ||\n      this.isAsyncFunction()\n    );\n  }\n\n  checkExport(\n    node: Undone<N.ExportNamedDeclaration | N.ExportDefaultDeclaration>,\n    checkNames?: boolean,\n    isDefault?: boolean,\n    isFrom?: boolean,\n  ): void {\n    if (checkNames) {\n      // Check for duplicate exports\n      if (isDefault) {\n        // Default exports\n        this.checkDuplicateExports(node, \"default\");\n        if (this.hasPlugin(\"exportDefaultFrom\")) {\n          const declaration = (node as any as N.ExportDefaultDeclaration)\n            .declaration;\n          if (\n            declaration.type === \"Identifier\" &&\n            declaration.name === \"from\" &&\n            declaration.end - declaration.start === 4 && // does not contain escape\n            !declaration.extra?.parenthesized\n          ) {\n            this.raise(Errors.ExportDefaultFromAsIdentifier, {\n              at: declaration,\n            });\n          }\n        }\n        // @ts-expect-error node.specifiers may not exist\n      } else if (node.specifiers && node.specifiers.length) {\n        // Named exports\n        // @ts-expect-error node.specifiers may not exist\n        for (const specifier of node.specifiers) {\n          const { exported } = specifier;\n          const exportName =\n            exported.type === \"Identifier\" ? exported.name : exported.value;\n          this.checkDuplicateExports(specifier, exportName);\n          if (!isFrom && specifier.local) {\n            const { local } = specifier;\n            if (local.type !== \"Identifier\") {\n              this.raise(Errors.ExportBindingIsString, {\n                at: specifier,\n                localName: local.value,\n                exportName,\n              });\n            } else {\n              // check for keywords used as local names\n              this.checkReservedWord(local.name, local.loc.start, true, false);\n              // check if export is defined\n              this.scope.checkLocalExport(local);\n            }\n          }\n        }\n      } else if (node.declaration) {\n        // Exported declarations\n        if (\n          node.declaration.type === \"FunctionDeclaration\" ||\n          node.declaration.type === \"ClassDeclaration\"\n        ) {\n          const id = node.declaration.id;\n          if (!id) throw new Error(\"Assertion failure\");\n\n          this.checkDuplicateExports(node, id.name);\n        } else if (node.declaration.type === \"VariableDeclaration\") {\n          for (const declaration of node.declaration.declarations) {\n            this.checkDeclaration(declaration.id);\n          }\n        }\n      }\n    }\n  }\n\n  checkDeclaration(node: N.Pattern | N.ObjectProperty): void {\n    if (node.type === \"Identifier\") {\n      this.checkDuplicateExports(node, node.name);\n    } else if (node.type === \"ObjectPattern\") {\n      for (const prop of node.properties) {\n        this.checkDeclaration(prop);\n      }\n    } else if (node.type === \"ArrayPattern\") {\n      for (const elem of node.elements) {\n        if (elem) {\n          this.checkDeclaration(elem);\n        }\n      }\n    } else if (node.type === \"ObjectProperty\") {\n      // @ts-expect-error migrate to Babel types\n      this.checkDeclaration(node.value);\n    } else if (node.type === \"RestElement\") {\n      this.checkDeclaration(node.argument);\n    } else if (node.type === \"AssignmentPattern\") {\n      this.checkDeclaration(node.left);\n    }\n  }\n\n  checkDuplicateExports(\n    node: Undone<\n      | N.Identifier\n      | N.StringLiteral\n      | N.ExportNamedDeclaration\n      | N.ExportSpecifier\n      | N.ExportDefaultSpecifier\n    >,\n    exportName: string,\n  ): void {\n    if (this.exportedIdentifiers.has(exportName)) {\n      if (exportName === \"default\") {\n        this.raise(Errors.DuplicateDefaultExport, { at: node });\n      } else {\n        this.raise(Errors.DuplicateExport, { at: node, exportName });\n      }\n    }\n    this.exportedIdentifiers.add(exportName);\n  }\n\n  // Parses a comma-separated list of module exports.\n\n  parseExportSpecifiers(isInTypeExport: boolean): Array<N.ExportSpecifier> {\n    const nodes = [];\n    let first = true;\n\n    // export { x, y as z } [from '...']\n    this.expect(tt.braceL);\n\n    while (!this.eat(tt.braceR)) {\n      if (first) {\n        first = false;\n      } else {\n        this.expect(tt.comma);\n        if (this.eat(tt.braceR)) break;\n      }\n      const isMaybeTypeOnly = this.isContextual(tt._type);\n      const isString = this.match(tt.string);\n      const node = this.startNode();\n      node.local = this.parseModuleExportName();\n      nodes.push(\n        this.parseExportSpecifier(\n          node,\n          isString,\n          isInTypeExport,\n          isMaybeTypeOnly,\n        ),\n      );\n    }\n\n    return nodes;\n  }\n\n  parseExportSpecifier(\n    node: any,\n    isString: boolean,\n    /* eslint-disable @typescript-eslint/no-unused-vars -- used in TypeScript parser */\n    isInTypeExport: boolean,\n    isMaybeTypeOnly: boolean,\n    /* eslint-enable @typescript-eslint/no-unused-vars */\n  ): N.ExportSpecifier {\n    if (this.eatContextual(tt._as)) {\n      node.exported = this.parseModuleExportName();\n    } else if (isString) {\n      node.exported = cloneStringLiteral(node.local);\n    } else if (!node.exported) {\n      node.exported = cloneIdentifier(node.local);\n    }\n    return this.finishNode<N.ExportSpecifier>(node, \"ExportSpecifier\");\n  }\n\n  // https://tc39.es/ecma262/#prod-ModuleExportName\n  parseModuleExportName(): N.StringLiteral | N.Identifier {\n    if (this.match(tt.string)) {\n      const result = this.parseStringLiteral(this.state.value);\n      const surrogate = result.value.match(loneSurrogate);\n      if (surrogate) {\n        this.raise(Errors.ModuleExportNameHasLoneSurrogate, {\n          at: result,\n          surrogateCharCode: surrogate[0].charCodeAt(0),\n        });\n      }\n      return result;\n    }\n    return this.parseIdentifier(true);\n  }\n\n  isJSONModuleImport(\n    node: Undone<\n      N.ExportAllDeclaration | N.ExportNamedDeclaration | N.ImportDeclaration\n    >,\n  ): boolean {\n    if (node.assertions != null) {\n      return node.assertions.some(({ key, value }) => {\n        return (\n          value.value === \"json\" &&\n          (key.type === \"Identifier\"\n            ? key.name === \"type\"\n            : key.value === \"type\")\n        );\n      });\n    }\n    return false;\n  }\n\n  checkImportReflection(node: Undone<N.ImportDeclaration>) {\n    if (node.module) {\n      if (\n        node.specifiers.length !== 1 ||\n        node.specifiers[0].type !== \"ImportDefaultSpecifier\"\n      ) {\n        this.raise(Errors.ImportReflectionNotBinding, {\n          at: node.specifiers[0].loc.start,\n        });\n      }\n      if (node.assertions?.length > 0) {\n        this.raise(Errors.ImportReflectionHasAssertion, {\n          at: node.specifiers[0].loc.start,\n        });\n      }\n    }\n  }\n\n  checkJSONModuleImport(\n    node: Undone<\n      N.ExportAllDeclaration | N.ExportNamedDeclaration | N.ImportDeclaration\n    >,\n  ) {\n    // @ts-expect-error Fixme: node.type must be undefined because they are undone\n    if (this.isJSONModuleImport(node) && node.type !== \"ExportAllDeclaration\") {\n      // @ts-expect-error specifiers may not index node\n      const { specifiers } = node;\n      if (specifiers != null) {\n        // @ts-expect-error refine specifier types\n        const nonDefaultNamedSpecifier = specifiers.find(specifier => {\n          let imported;\n          if (specifier.type === \"ExportSpecifier\") {\n            imported = specifier.local;\n          } else if (specifier.type === \"ImportSpecifier\") {\n            imported = specifier.imported;\n          }\n          if (imported !== undefined) {\n            return imported.type === \"Identifier\"\n              ? imported.name !== \"default\"\n              : imported.value !== \"default\";\n          }\n        });\n        if (nonDefaultNamedSpecifier !== undefined) {\n          this.raise(Errors.ImportJSONBindingNotDefault, {\n            at: nonDefaultNamedSpecifier.loc.start,\n          });\n        }\n      }\n    }\n  }\n\n  isPotentialImportPhase(isExport: boolean): boolean {\n    return !isExport && this.isContextual(tt._module);\n  }\n\n  applyImportPhase(\n    node: Undone<N.ImportDeclaration | N.ExportNamedDeclaration>,\n    isExport: boolean,\n    phase: string | null,\n    loc?: Position,\n  ): void {\n    if (isExport) {\n      if (!process.env.IS_PUBLISH) {\n        if (phase === \"module\") {\n          throw new Error(\n            \"Assertion failure: export declarations do not support the 'module' phase.\",\n          );\n        }\n      }\n      return;\n    }\n    if (phase === \"module\") {\n      this.expectPlugin(\"importReflection\", loc);\n      (node as N.ImportDeclaration).module = true;\n    } else if (this.hasPlugin(\"importReflection\")) {\n      (node as N.ImportDeclaration).module = false;\n    }\n  }\n\n  /*\n   * Parse `module` in `import module x fro \"x\"`, disambiguating\n   * `import module from \"x\"` and `import module from from \"x\"`.\n   *\n   * This function might return an identifier representing the `module`\n   * if it eats `module` and then discovers that it was the default import\n   * binding and not the import reflection.\n   *\n   * This function is also used to parse `import type` and `import typeof`\n   * in the TS and Flow plugins.\n   *\n   * Note: the proposal has been updated to use `source` instead of `module`,\n   * but it has not been implemented yet.\n   */\n  parseMaybeImportPhase(\n    node: Undone<N.ImportDeclaration | N.TsImportEqualsDeclaration>,\n    isExport: boolean,\n  ): N.Identifier | null {\n    if (!this.isPotentialImportPhase(isExport)) {\n      this.applyImportPhase(\n        node as Undone<N.ImportDeclaration>,\n        isExport,\n        null,\n      );\n      return null;\n    }\n\n    const phaseIdentifier = this.parseIdentifier(true);\n\n    const { type } = this.state;\n    const isImportPhase = tokenIsKeywordOrIdentifier(type)\n      ? // OK: import <phase> x from \"foo\";\n        // OK: import <phase> from from \"foo\";\n        // NO: import <phase> from \"foo\";\n        // NO: import <phase> from 'foo';\n        // With the module declarations proposals, we will need further disambiguation\n        // for `import module from from;`.\n        type !== tt._from || this.lookaheadCharCode() === charCodes.lowercaseF\n      : // OK: import <phase> { x } from \"foo\";\n        // OK: import <phase> x from \"foo\";\n        // OK: import <phase> * as T from \"foo\";\n        // NO: import <phase> from \"foo\";\n        // OK: import <phase> \"foo\";\n        // The last one is invalid, we will continue parsing and throw\n        // an error later\n        type !== tt.comma;\n\n    if (isImportPhase) {\n      this.resetPreviousIdentifierLeadingComments(phaseIdentifier);\n      this.applyImportPhase(\n        node as Undone<N.ImportDeclaration>,\n        isExport,\n        phaseIdentifier.name,\n        phaseIdentifier.loc.start,\n      );\n      return null;\n    } else {\n      this.applyImportPhase(\n        node as Undone<N.ImportDeclaration>,\n        isExport,\n        null,\n      );\n      // `<phase>` is a default binding, return it to the main import declaration parser\n      return phaseIdentifier;\n    }\n  }\n\n  isPrecedingIdImportPhase(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    phase: string,\n  ) {\n    const { type } = this.state;\n    return tokenIsIdentifier(type)\n      ? // OK: import <phase> x from \"foo\";\n        // OK: import <phase> from from \"foo\";\n        // NO: import <phase> from \"foo\";\n        // NO: import <phase> from 'foo';\n        // With the module declarations proposals, we will need further disambiguation\n        // for `import module from from;`.\n        type !== tt._from || this.lookaheadCharCode() === charCodes.lowercaseF\n      : // OK: import <phase> { x } from \"foo\";\n        // OK: import <phase> x from \"foo\";\n        // OK: import <phase> * as T from \"foo\";\n        // NO: import <phase> from \"foo\";\n        // OK: import <phase> \"foo\";\n        // The last one is invalid, we will continue parsing and throw\n        // an error later\n        type !== tt.comma;\n  }\n\n  // Parses import declaration.\n  // https://tc39.es/ecma262/#prod-ImportDeclaration\n\n  parseImport(this: Parser, node: Undone<N.ImportDeclaration>): N.AnyImport {\n    if (this.match(tt.string)) {\n      // import '...'\n      return this.parseImportSourceAndAttributes(node);\n    }\n\n    return this.parseImportSpecifiersAndAfter(\n      node,\n      this.parseMaybeImportPhase(node, /* isExport */ false),\n    );\n  }\n\n  parseImportSpecifiersAndAfter(\n    this: Parser,\n    node: Undone<N.ImportDeclaration>,\n    maybeDefaultIdentifier: N.Identifier | null,\n  ): N.AnyImport {\n    node.specifiers = [];\n\n    // check if we have a default import like\n    // import React from \"react\";\n    const hasDefault = this.maybeParseDefaultImportSpecifier(\n      node,\n      maybeDefaultIdentifier,\n    );\n    /* we are checking if we do not have a default import, then it is obvious that we need named imports\n     * import { get } from \"axios\";\n     * but if we do have a default import\n     * we need to check if we have a comma after that and\n     * that is where this `|| this.eat` condition comes into play\n     */\n    const parseNext = !hasDefault || this.eat(tt.comma);\n    // if we do have to parse the next set of specifiers, we first check for star imports\n    // import React, * from \"react\";\n    const hasStar = parseNext && this.maybeParseStarImportSpecifier(node);\n    // now we check if we need to parse the next imports\n    // but only if they are not importing * (everything)\n    if (parseNext && !hasStar) this.parseNamedImportSpecifiers(node);\n    this.expectContextual(tt._from);\n\n    return this.parseImportSourceAndAttributes(node);\n  }\n\n  parseImportSourceAndAttributes(\n    this: Parser,\n    node: Undone<N.ImportDeclaration>,\n  ): N.AnyImport {\n    node.specifiers ??= [];\n    node.source = this.parseImportSource();\n    this.maybeParseImportAttributes(node);\n    this.checkImportReflection(node);\n    this.checkJSONModuleImport(node);\n\n    this.semicolon();\n    return this.finishNode(node, \"ImportDeclaration\");\n  }\n\n  parseImportSource(this: Parser): N.StringLiteral {\n    if (!this.match(tt.string)) this.unexpected();\n    return this.parseExprAtom() as N.StringLiteral;\n  }\n\n  parseImportSpecifierLocal<\n    T extends\n      | N.ImportSpecifier\n      | N.ImportDefaultSpecifier\n      | N.ImportNamespaceSpecifier,\n  >(\n    node: Undone<N.ImportDeclaration>,\n    specifier: Undone<T>,\n    type: T[\"type\"],\n  ): void {\n    specifier.local = this.parseIdentifier();\n    node.specifiers.push(this.finishImportSpecifier(specifier, type));\n  }\n\n  finishImportSpecifier<\n    T extends\n      | N.ImportSpecifier\n      | N.ImportDefaultSpecifier\n      | N.ImportNamespaceSpecifier,\n  >(specifier: Undone<T>, type: T[\"type\"], bindingType = BIND_LEXICAL) {\n    this.checkLVal(specifier.local, {\n      in: { type },\n      binding: bindingType,\n    });\n    return this.finishNode(specifier, type);\n  }\n\n  /**\n   * parse assert entries\n   *\n   * @see {@link https://tc39.es/proposal-import-attributes/#prod-WithEntries WithEntries}\n   */\n  parseImportAttributes(): N.ImportAttribute[] {\n    this.expect(tt.braceL);\n\n    const attrs = [];\n    const attrNames = new Set();\n\n    do {\n      if (this.match(tt.braceR)) {\n        break;\n      }\n\n      const node = this.startNode<N.ImportAttribute>();\n\n      // parse AssertionKey : IdentifierName, StringLiteral\n      const keyName = this.state.value;\n      // check if we already have an entry for an attribute\n      // if a duplicate entry is found, throw an error\n      // for now this logic will come into play only when someone declares `type` twice\n      if (attrNames.has(keyName)) {\n        this.raise(Errors.ModuleAttributesWithDuplicateKeys, {\n          at: this.state.startLoc,\n          key: keyName,\n        });\n      }\n      attrNames.add(keyName);\n      if (this.match(tt.string)) {\n        node.key = this.parseStringLiteral(keyName);\n      } else {\n        node.key = this.parseIdentifier(true);\n      }\n      this.expect(tt.colon);\n\n      if (!this.match(tt.string)) {\n        throw this.raise(Errors.ModuleAttributeInvalidValue, {\n          at: this.state.startLoc,\n        });\n      }\n      node.value = this.parseStringLiteral(this.state.value);\n      attrs.push(this.finishNode(node, \"ImportAttribute\"));\n    } while (this.eat(tt.comma));\n\n    this.expect(tt.braceR);\n\n    return attrs;\n  }\n\n  /**\n   * parse module attributes\n   * @deprecated It will be removed in Babel 8\n   */\n  parseModuleAttributes() {\n    const attrs: N.ImportAttribute[] = [];\n    const attributes = new Set();\n    do {\n      const node = this.startNode<N.ImportAttribute>();\n      node.key = this.parseIdentifier(true);\n\n      if (node.key.name !== \"type\") {\n        this.raise(Errors.ModuleAttributeDifferentFromType, {\n          at: node.key,\n        });\n      }\n\n      if (attributes.has(node.key.name)) {\n        this.raise(Errors.ModuleAttributesWithDuplicateKeys, {\n          at: node.key,\n          key: node.key.name,\n        });\n      }\n      attributes.add(node.key.name);\n      this.expect(tt.colon);\n      if (!this.match(tt.string)) {\n        throw this.raise(Errors.ModuleAttributeInvalidValue, {\n          at: this.state.startLoc,\n        });\n      }\n      node.value = this.parseStringLiteral(this.state.value);\n      attrs.push(this.finishNode(node, \"ImportAttribute\"));\n    } while (this.eat(tt.comma));\n\n    return attrs;\n  }\n\n  maybeParseImportAttributes(\n    node: Undone<N.ImportDeclaration | N.ExportNamedDeclaration>,\n  ) {\n    let attributes: N.ImportAttribute[];\n    let useWith = false;\n\n    // https://tc39.es/proposal-import-attributes/#prod-WithClause\n    if (this.match(tt._with)) {\n      if (\n        this.hasPrecedingLineBreak() &&\n        this.lookaheadCharCode() === charCodes.leftParenthesis\n      ) {\n        // This will be parsed as a with statement, and we will throw a\n        // better error about it not being supported in strict mode.\n        return;\n      }\n\n      this.next(); // eat `with`\n\n      if (!process.env.BABEL_8_BREAKING) {\n        if (this.hasPlugin(\"moduleAttributes\")) {\n          attributes = this.parseModuleAttributes();\n        } else {\n          this.expectImportAttributesPlugin();\n          attributes = this.parseImportAttributes();\n        }\n      } else {\n        this.expectImportAttributesPlugin();\n        attributes = this.parseImportAttributes();\n      }\n      useWith = true;\n    } else if (this.isContextual(tt._assert) && !this.hasPrecedingLineBreak()) {\n      if (this.hasPlugin(\"importAttributes\")) {\n        if (\n          this.getPluginOption(\"importAttributes\", \"deprecatedAssertSyntax\") !==\n          true\n        ) {\n          this.raise(Errors.ImportAttributesUseAssert, {\n            at: this.state.startLoc,\n          });\n        }\n        this.addExtra(node, \"deprecatedAssertSyntax\", true);\n      } else {\n        this.expectOnePlugin([\"importAttributes\", \"importAssertions\"]);\n      }\n      this.next(); // eat `assert`\n      attributes = this.parseImportAttributes();\n    } else if (\n      this.hasPlugin(\"importAttributes\") ||\n      this.hasPlugin(\"importAssertions\")\n    ) {\n      attributes = [];\n    } else if (!process.env.BABEL_8_BREAKING) {\n      if (this.hasPlugin(\"moduleAttributes\")) {\n        attributes = [];\n      } else return;\n    } else return;\n\n    if (!useWith && this.hasPlugin(\"importAssertions\")) {\n      node.assertions = attributes;\n    } else {\n      node.attributes = attributes;\n    }\n  }\n\n  maybeParseDefaultImportSpecifier(\n    node: Undone<N.ImportDeclaration>,\n    maybeDefaultIdentifier: N.Identifier | null,\n  ): boolean {\n    // import defaultObj, { x, y as z } from '...'\n    if (maybeDefaultIdentifier) {\n      const specifier = this.startNodeAtNode<N.ImportDefaultSpecifier>(\n        maybeDefaultIdentifier,\n      );\n      specifier.local = maybeDefaultIdentifier;\n      node.specifiers.push(\n        this.finishImportSpecifier(specifier, \"ImportDefaultSpecifier\"),\n      );\n      return true;\n    } else if (\n      // We allow keywords, and parseImportSpecifierLocal will report a recoverable error\n      tokenIsKeywordOrIdentifier(this.state.type)\n    ) {\n      this.parseImportSpecifierLocal(\n        node,\n        this.startNode<N.ImportDefaultSpecifier>(),\n        \"ImportDefaultSpecifier\",\n      );\n      return true;\n    }\n    return false;\n  }\n\n  maybeParseStarImportSpecifier(node: Undone<N.ImportDeclaration>): boolean {\n    if (this.match(tt.star)) {\n      const specifier = this.startNode<N.ImportNamespaceSpecifier>();\n      this.next();\n      this.expectContextual(tt._as);\n\n      this.parseImportSpecifierLocal(\n        node,\n        specifier,\n        \"ImportNamespaceSpecifier\",\n      );\n      return true;\n    }\n    return false;\n  }\n\n  parseNamedImportSpecifiers(node: Undone<N.ImportDeclaration>) {\n    let first = true;\n    this.expect(tt.braceL);\n    while (!this.eat(tt.braceR)) {\n      if (first) {\n        first = false;\n      } else {\n        // Detect an attempt to deep destructure\n        if (this.eat(tt.colon)) {\n          throw this.raise(Errors.DestructureNamedImport, {\n            at: this.state.startLoc,\n          });\n        }\n\n        this.expect(tt.comma);\n        if (this.eat(tt.braceR)) break;\n      }\n\n      const specifier = this.startNode<N.ImportSpecifier>();\n      const importedIsString = this.match(tt.string);\n      const isMaybeTypeOnly = this.isContextual(tt._type);\n      specifier.imported = this.parseModuleExportName();\n      const importSpecifier = this.parseImportSpecifier(\n        specifier,\n        importedIsString,\n        node.importKind === \"type\" || node.importKind === \"typeof\",\n        isMaybeTypeOnly,\n        undefined,\n      );\n      node.specifiers.push(importSpecifier);\n    }\n  }\n\n  // https://tc39.es/ecma262/#prod-ImportSpecifier\n  parseImportSpecifier(\n    specifier: Undone<N.ImportSpecifier>,\n    importedIsString: boolean,\n    /* eslint-disable @typescript-eslint/no-unused-vars -- used in TypeScript and Flow parser */\n    isInTypeOnlyImport: boolean,\n    isMaybeTypeOnly: boolean,\n    bindingType: BindingTypes | undefined,\n    /* eslint-enable @typescript-eslint/no-unused-vars */\n  ): N.ImportSpecifier {\n    if (this.eatContextual(tt._as)) {\n      specifier.local = this.parseIdentifier();\n    } else {\n      const { imported } = specifier;\n      if (importedIsString) {\n        throw this.raise(Errors.ImportBindingIsString, {\n          at: specifier,\n          importName: (imported as N.StringLiteral).value,\n        });\n      }\n      this.checkReservedWord(\n        (imported as N.Identifier).name,\n        specifier.loc.start,\n        true,\n        true,\n      );\n      if (!specifier.local) {\n        specifier.local = cloneIdentifier(imported);\n      }\n    }\n    return this.finishImportSpecifier(\n      specifier,\n      \"ImportSpecifier\",\n      bindingType,\n    );\n  }\n\n  // This is used in flow and typescript plugin\n  // Determine whether a parameter is a this param\n  isThisParam(\n    param: N.Pattern | N.Identifier | N.TSParameterProperty,\n  ): boolean {\n    return param.type === \"Identifier\" && param.name === \"this\";\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AASA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;AAEA,IAAAI,WAAA,GAAAJ,OAAA;AAmBA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,oBAAA,GAAAN,OAAA;AACA,IAAAO,gBAAA,GAAAP,OAAA;AAKA,IAAAQ,UAAA,GAAAR,OAAA;AAEA,IAAAS,SAAA,GAAAT,OAAA;AACA,IAAAU,KAAA,GAAAV,OAAA;AAEA,IAAAW,KAAA,GAAAX,OAAA;AAEA,MAAMY,SAAS,GAAG;IAAEC,IAAI,EAAE;EAAO,CAAU;EACzCC,WAAW,GAAG;IAAED,IAAI,EAAE;EAAS,CAAU;AAAC,IAE1BE,iBAAiB;EAAAC,UAAA;EAAAC,WAAA;EAAAC,kBAAA;EAAAC,UAAA;EAAAC,KAAA;AAAA;AAAAC,OAAA,CAAAN,iBAAA,GAAAA,iBAAA;AAAA,IAQjBO,kBAAkB;EAAAC,aAAA;EAAAC,iBAAA;EAAAC,gBAAA;EAAAC,wBAAA;EAAAC,oBAAA;AAAA;AAAAN,OAAA,CAAAC,kBAAA,GAAAA,kBAAA;AAQpC,MAAMM,aAAa,GAAG,kBAAkB;AAExC,MAAMC,yBAAyB,GAAG,kBAAkB;AAUpD,SAASC,kBAAkBA,CAACC,MAA6B,EAAEC,KAAa,EAAE;EACxE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,MAAME,KAAK,GAAGJ,MAAM,CAACE,CAAC,CAAC;IACvB,MAAM;MAAEG;IAAK,CAAC,GAAGD,KAAK;IACtB,IAAI,OAAOC,IAAI,KAAK,QAAQ,EAAE;MACO;QACjC,IAAIA,IAAI,QAAmB,EAAE;UAC3B,MAAM;YAAEC,GAAG;YAAEC,KAAK;YAAEC,KAAK;YAAEC;UAAI,CAAC,GAAGL,KAAK;UACxC,MAAMM,UAAU,GAAGH,KAAK,GAAG,CAAC;UAC5B,MAAMI,UAAU,GAAG,IAAAC,wCAA8B,EAACN,GAAG,CAACC,KAAK,EAAE,CAAC,CAAC;UAC/DP,MAAM,CAACa,MAAM,CACXX,CAAC,EACD,CAAC,EACD,IAAIY,gBAAK,CAAC;YAERT,IAAI,EAAE,IAAAU,uBAAgB,IAAQ,CAAC;YAC/BP,KAAK,EAAE,GAAG;YACVD,KAAK,EAAEA,KAAK;YACZE,GAAG,EAAEC,UAAU;YACfM,QAAQ,EAAEV,GAAG,CAACC,KAAK;YACnBU,MAAM,EAAEN;UACV,CAAC,CAAC,EACF,IAAIG,gBAAK,CAAC;YAERT,IAAI,EAAE,IAAAU,uBAAgB,KAAQ,CAAC;YAC/BP,KAAK,EAAEA,KAAK;YACZD,KAAK,EAAEG,UAAU;YACjBD,GAAG,EAAEA,GAAG;YACRO,QAAQ,EAAEL,UAAU;YACpBM,MAAM,EAAEX,GAAG,CAACG;UACd,CAAC,CACH,CAAC;UACDP,CAAC,EAAE;UACH;QACF;QAEA,IAAI,IAAAgB,sBAAe,EAACb,IAAI,CAAC,EAAE;UACzB,MAAM;YAAEC,GAAG;YAAEC,KAAK;YAAEC,KAAK;YAAEC;UAAI,CAAC,GAAGL,KAAK;UACxC,MAAMe,YAAY,GAAGZ,KAAK,GAAG,CAAC;UAC9B,MAAMa,eAAe,GAAG,IAAAR,wCAA8B,EAACN,GAAG,CAACC,KAAK,EAAE,CAAC,CAAC;UACpE,IAAIc,UAAU;UACd,IAAIpB,KAAK,CAACqB,UAAU,CAACf,KAAK,CAAC,OAA0B,EAAE;YACrDc,UAAU,GAAG,IAAIP,gBAAK,CAAC;cAErBT,IAAI,EAAE,IAAAU,uBAAgB,IAAa,CAAC;cACpCP,KAAK,EAAE,GAAG;cACVD,KAAK,EAAEA,KAAK;cACZE,GAAG,EAAEU,YAAY;cACjBH,QAAQ,EAAEV,GAAG,CAACC,KAAK;cACnBU,MAAM,EAAEG;YACV,CAAC,CAAC;UACJ,CAAC,MAAM;YACLC,UAAU,GAAG,IAAIP,gBAAK,CAAC;cAErBT,IAAI,EAAE,IAAAU,uBAAgB,GAAU,CAAC;cACjCP,KAAK,EAAE,GAAG;cACVD,KAAK,EAAEA,KAAK;cACZE,GAAG,EAAEU,YAAY;cACjBH,QAAQ,EAAEV,GAAG,CAACC,KAAK;cACnBU,MAAM,EAAEG;YACV,CAAC,CAAC;UACJ;UACA,IAAIG,aAAa,EACfC,kBAAkB,EAClBC,qBAAqB,EACrBC,QAAQ;UACV,IAAIrB,IAAI,OAAoB,EAAE;YAE5BmB,kBAAkB,GAAGf,GAAG,GAAG,CAAC;YAC5BgB,qBAAqB,GAAG,IAAAb,wCAA8B,EAACN,GAAG,CAACG,GAAG,EAAE,CAAC,CAAC,CAAC;YACnEc,aAAa,GAAGf,KAAK,KAAK,IAAI,GAAG,IAAI,GAAGA,KAAK,CAACmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1DD,QAAQ,GAAG,IAAIZ,gBAAK,CAAC;cAEnBT,IAAI,EAAE,IAAAU,uBAAgB,IAAa,CAAC;cACpCP,KAAK,EAAE,GAAG;cACVD,KAAK,EAAEiB,kBAAkB;cACzBf,GAAG,EAAEA,GAAG;cACRO,QAAQ,EAAES,qBAAqB;cAC/BR,MAAM,EAAEX,GAAG,CAACG;YACd,CAAC,CAAC;UACJ,CAAC,MAAM;YAELe,kBAAkB,GAAGf,GAAG,GAAG,CAAC;YAC5BgB,qBAAqB,GAAG,IAAAb,wCAA8B,EAACN,GAAG,CAACG,GAAG,EAAE,CAAC,CAAC,CAAC;YACnEc,aAAa,GAAGf,KAAK,KAAK,IAAI,GAAG,IAAI,GAAGA,KAAK,CAACmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1DD,QAAQ,GAAG,IAAIZ,gBAAK,CAAC;cAEnBT,IAAI,EAAE,IAAAU,uBAAgB,IAAgB,CAAC;cACvCP,KAAK,EAAE,IAAI;cACXD,KAAK,EAAEiB,kBAAkB;cACzBf,GAAG,EAAEA,GAAG;cACRO,QAAQ,EAAES,qBAAqB;cAC/BR,MAAM,EAAEX,GAAG,CAACG;YACd,CAAC,CAAC;UACJ;UACAT,MAAM,CAACa,MAAM,CACXX,CAAC,EACD,CAAC,EACDmB,UAAU,EACV,IAAIP,gBAAK,CAAC;YAERT,IAAI,EAAE,IAAAU,uBAAgB,IAAY,CAAC;YACnCP,KAAK,EAAEe,aAAa;YACpBhB,KAAK,EAAEY,YAAY;YACnBV,GAAG,EAAEe,kBAAkB;YACvBR,QAAQ,EAAEI,eAAe;YACzBH,MAAM,EAAEQ;UACV,CAAC,CAAC,EACFC,QACF,CAAC;UACDxB,CAAC,IAAI,CAAC;UACN;QACF;MACF;MAEAE,KAAK,CAACC,IAAI,GAAG,IAAAU,uBAAgB,EAACV,IAAI,CAAC;IACrC;EACF;EACA,OAAOL,MAAM;AACf;AACe,MAAe4B,eAAe,SAASC,mBAAgB,CAAC;EAQrEC,aAAaA,CAAeC,IAAY,EAAEC,OAAkB,EAAU;IACpED,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,YAAY,CAACD,OAAO,CAAC;IACzCD,IAAI,CAACG,QAAQ,GAAG,IAAI,CAACC,KAAK,CAACD,QAAQ;IAEnC,IAAI,IAAI,CAACE,OAAO,CAACpC,MAAM,EAAE;MACvB+B,IAAI,CAAC/B,MAAM,GAAGD,kBAAkB,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,KAAK,CAAC;IAC3D;IAEA,OAAO,IAAI,CAACoC,UAAU,CAACN,IAAI,EAAE,MAAM,CAAC;EACtC;EAEAE,YAAYA,CAEVD,OAA0B,EAC1BvB,GAAc,MAAS,EACvB6B,UAAsB,GAAG,IAAI,CAACF,OAAO,CAACE,UAAU,EACrC;IACXN,OAAO,CAACM,UAAU,GAAGA,UAAU;IAC/BN,OAAO,CAACO,WAAW,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACtD,IAAI,CAACC,cAAc,CAACT,OAAO,EAAE,IAAI,EAAE,IAAI,EAAEvB,GAAG,CAAC;IAC7C,IACE,IAAI,CAACiC,QAAQ,IACb,CAAC,IAAI,CAACN,OAAO,CAACO,sBAAsB,IACpC,IAAI,CAACC,KAAK,CAACC,gBAAgB,CAACC,IAAI,GAAG,CAAC,EACpC;MACA,KAAK,MAAM,CAACC,SAAS,EAAEC,EAAE,CAAC,IAAIC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACN,KAAK,CAACC,gBAAgB,CAAC,EAAE;QACrE,IAAI,CAACM,KAAK,CAACC,kBAAM,CAACC,qBAAqB,EAAE;UAAEL,EAAE;UAAED;QAAU,CAAC,CAAC;MAC7D;IACF;IACA,IAAIO,eAA0B;IAC9B,IAAI7C,GAAG,QAAW,EAAE;MAElB6C,eAAe,GAAG,IAAI,CAACjB,UAAU,CAACL,OAAO,EAAE,SAAS,CAAC;IACvD,CAAC,MAAM;MAELsB,eAAe,GAAG,IAAI,CAACC,YAAY,CACjCvB,OAAO,EACP,SAAS,EACT,IAAApB,wCAA8B,EAAC,IAAI,CAACuB,KAAK,CAACnB,QAAQ,EAAE,CAAC,CAAC,CACxD,CAAC;IACH;IACA,OAAOsC,eAAe;EACxB;EAKAE,eAAeA,CAACC,IAAiB,EAAe;IAC9C,MAAMC,SAAS,GAAGD,IAAW;IAC7BC,SAAS,CAACrD,IAAI,GAAG,WAAW;IAC5BqD,SAAS,CAAClD,KAAK,GAAGkD,SAAS,CAACC,UAAU;IACtC,OAAOD,SAAS,CAACC,UAAU;IAE3B,MAAMC,gBAAgB,GAAGF,SAAS,CAAClD,KAAK;IACxC,MAAMqD,eAAe,GAAGD,gBAAgB,CAACpD,KAAK;IAC9C,MAAMsD,GAAG,GAAG,IAAI,CAAC7D,KAAK,CAAC0B,KAAK,CAACiC,gBAAgB,CAACrD,KAAK,EAAEqD,gBAAgB,CAACnD,GAAG,CAAC;IAC1E,MAAMsD,GAAG,GAAIH,gBAAgB,CAACpD,KAAK,GAAGsD,GAAG,CAACnC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE;IAEvD,IAAI,CAACqC,QAAQ,CAACJ,gBAAgB,EAAE,KAAK,EAAEE,GAAG,CAAC;IAC3C,IAAI,CAACE,QAAQ,CAACJ,gBAAgB,EAAE,UAAU,EAAEG,GAAG,CAAC;IAChD,IAAI,CAACC,QAAQ,CAACJ,gBAAgB,EAAE,iBAAiB,EAAEC,eAAe,CAAC;IAEnED,gBAAgB,CAACvD,IAAI,GAAG,kBAAkB;IAE1C,OAAOqD,SAAS;EAClB;EAEAlB,yBAAyBA,CAAA,EAAkC;IACzD,IAAI,CAAC,IAAI,CAACyB,KAAK,GAAwB,CAAC,EAAE;MACxC,OAAO,IAAI;IACb;IAEA,MAAMC,IAAI,GAAG,IAAI,CAACC,SAAS,CAAyB,CAAC;IACrDD,IAAI,CAAC1D,KAAK,GAAG,IAAI,CAAC2B,KAAK,CAAC3B,KAAK;IAC7B,IAAI,CAAC4D,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAAC/B,UAAU,CAAC6B,IAAI,EAAE,sBAAsB,CAAC;EACtD;EAEAG,KAAKA,CAAA,EAAY;IACf,IAAI,CAAC,IAAI,CAACC,YAAY,GAAQ,CAAC,EAAE;MAC/B,OAAO,KAAK;IACd;IACA,OAAO,IAAI,CAACC,uBAAuB,CAAC,CAAC;EACvC;EAEAC,yBAAyBA,CAACC,EAAU,EAAEC,GAAW,EAAE;IACjD,IAAI,IAAAC,6BAAiB,EAACF,EAAE,CAAC,EAAE;MACzB3E,yBAAyB,CAAC8E,SAAS,GAAGF,GAAG;MACzC,IAAI5E,yBAAyB,CAAC+E,IAAI,CAAC,IAAI,CAAC5E,KAAK,CAAC,EAAE;QAG9C,MAAM6E,KAAK,GAAG,IAAI,CAACC,cAAc,CAACjF,yBAAyB,CAAC8E,SAAS,CAAC;QACtE,IAAI,CAAC,IAAAI,4BAAgB,EAACF,KAAK,CAAC,IAAIA,KAAK,OAAwB,EAAE;UAC7D,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIL,EAAE,OAAwB,EAAE;MACrC,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF;EAEAQ,sBAAsBA,CAACR,EAAU,EAAE;IACjC,OACEA,EAAE,OAAgC,IAAIA,EAAE,QAA6B;EAEzE;EAMAF,uBAAuBA,CAAA,EAAY;IACjC,MAAMH,IAAI,GAAG,IAAI,CAACc,cAAc,CAAC,CAAC;IAClC,MAAMC,MAAM,GAAG,IAAI,CAACJ,cAAc,CAACX,IAAI,CAAC;IACxC,OACE,IAAI,CAACa,sBAAsB,CAACE,MAAM,CAAC,IACnC,IAAI,CAACX,yBAAyB,CAACW,MAAM,EAAEf,IAAI,CAAC;EAEhD;EAOAgB,mCAAmCA,CAAA,EAAY;IAC7C,MAAMhB,IAAI,GAAG,IAAI,CAACiB,oBAAoB,CAAC,CAAC;IACxC,MAAMF,MAAM,GAAG,IAAI,CAACJ,cAAc,CAACX,IAAI,CAAC;IACxC,OAAO,IAAI,CAACI,yBAAyB,CAACW,MAAM,EAAEf,IAAI,CAAC;EACrD;EAEAkB,gBAAgBA,CAAA,EAAY;IAC1B,MAAM;MAAEjF,IAAI;MAAEkF;IAAY,CAAC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAC9C,IAAInF,IAAI,QAAW,IAAI,CAACkF,WAAW,EAAE;MAEnC,OAAO,KAAK;IACd,CAAC,MAAM,IAAI,IAAAE,wBAAiB,EAACpF,IAAI,CAAC,IAAI,CAAC,IAAI,CAACqF,qBAAqB,CAAC,CAAC,EAAE;MACnE,IAAI,CAACC,YAAY,CAAC,4BAA4B,CAAC;MAC/C,OAAO,IAAI;IACb;EACF;EAEAC,gBAAgBA,CAAA,EAAY;IAC1B,IAAIxB,IAAI,GAAG,IAAI,CAACiB,oBAAoB,CAAC,CAAC;IACtC,IAAI,IAAI,CAACQ,oBAAoB,CAACzB,IAAI,EAAE,OAAO,CAAC,EAAE;MAC5CA,IAAI,GAAG,IAAI,CAAC0B,yBAAyB,CAAC1B,IAAI,GAAG,CAAC,CAAC;MAC/C,MAAMe,MAAM,GAAG,IAAI,CAACJ,cAAc,CAACX,IAAI,CAAC;MACxC,IAAI,IAAI,CAACI,yBAAyB,CAACW,MAAM,EAAEf,IAAI,CAAC,EAAE;QAChD,IAAI,CAACuB,YAAY,CAAC,4BAA4B,CAAC;QAC/C,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;EAGAI,eAAeA,CAAA,EAAe;IAC5B,OAAO,IAAI,CAACC,kBAAkB,CAC5BzG,kBAAkB,CAACE,iBAAiB,GAClCF,kBAAkB,CAACG,gBAAgB,GACnCH,kBAAkB,CAACI,wBAAwB,GAG3CJ,kBAAkB,CAACK,oBACvB,CAAC;EACH;EAGAqG,sBAAsBA,CAAA,EAAe;IACnC,OAAO,IAAI,CAACD,kBAAkB,CAC5BzG,kBAAkB,CAACG,gBAAgB,GACjCH,kBAAkB,CAACI,wBAAwB,IAC1C,CAAC,IAAI,CAACyC,OAAO,CAAC8D,MAAM,IAAI,IAAI,CAAC/D,KAAK,CAACgE,MAAM,GACtC,CAAC,GACD5G,kBAAkB,CAACK,oBAAoB,CAC/C,CAAC;EACH;EAEAwG,+CAA+CA,CAE7CC,oBAA6B,GAAG,KAAK,EACrC;IACA,IAAIC,KAAyB,GAAG/G,kBAAkB,CAACC,aAAa;IAChE,IAAI,IAAI,CAAC4C,OAAO,CAAC8D,MAAM,IAAI,CAAC,IAAI,CAAC/D,KAAK,CAACgE,MAAM,EAAE;MAC7CG,KAAK,IAAI/G,kBAAkB,CAACI,wBAAwB;MACpD,IAAI0G,oBAAoB,EAAE;QACxBC,KAAK,IAAI/G,kBAAkB,CAACK,oBAAoB;MAClD;IACF;IACA,OAAO,IAAI,CAACoG,kBAAkB,CAACM,KAAK,CAAC;EACvC;EASAC,cAAcA,CAAA,EAAe;IAC3B,OAAO,IAAI,CAACP,kBAAkB,CAACzG,kBAAkB,CAACC,aAAa,CAAC;EAClE;EAIAwG,kBAAkBA,CAEhBM,KAAyB,EAOA;IACzB,IAAIE,UAAgC,GAAG,IAAI;IAE3C,IAAI,IAAI,CAACvC,KAAK,GAAM,CAAC,EAAE;MACrBuC,UAAU,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAAC;IACzC;IACA,OAAO,IAAI,CAACC,qBAAqB,CAACJ,KAAK,EAAEE,UAAU,CAAC;EACtD;EAEAE,qBAAqBA,CAEnBJ,KAAyB,EACzBE,UAAiC,EACpB;IACb,MAAMG,SAAS,GAAG,IAAI,CAACxE,KAAK,CAAC9B,IAAI;IACjC,MAAM6D,IAAI,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAC7B,MAAMyC,gBAAgB,GAAG,CAAC,EAAEN,KAAK,GAAG/G,kBAAkB,CAACG,gBAAgB,CAAC;IACxE,MAAMmH,wBAAwB,GAAG,CAAC,EAChCP,KAAK,GAAG/G,kBAAkB,CAACI,wBAAwB,CACpD;IACD,MAAMmH,QAAQ,GAAGR,KAAK,GAAG/G,kBAAkB,CAACE,iBAAiB;IAM7D,QAAQkH,SAAS;MACf;QACE,OAAO,IAAI,CAACI,2BAA2B,CAAC7C,IAAI,EAAgB,IAAI,CAAC;MACnE;QACE,OAAO,IAAI,CAAC6C,2BAA2B,CAAC7C,IAAI,EAAgB,KAAK,CAAC;MACpE;QACE,OAAO,IAAI,CAAC8C,sBAAsB,CAAC9C,IAAmC,CAAC;MACzE;QACE,OAAO,IAAI,CAAC+C,qBAAqB,CAAC/C,IAAkC,CAAC;MACvE;QACE,OAAO,IAAI,CAACgD,iBAAiB,CAAChD,IAA8B,CAAC;MAC/D;QACE,IAAI,IAAI,CAACiD,iBAAiB,CAAC,CAAC,OAAkB,EAAE;QAChD,IAAI,CAACN,wBAAwB,EAAE;UAC7B,IAAI,CAAC1D,KAAK,CACR,IAAI,CAAChB,KAAK,CAACgE,MAAM,GACb/C,kBAAM,CAACgE,cAAc,GACrB,IAAI,CAAChF,OAAO,CAAC8D,MAAM,GACnB9C,kBAAM,CAACiE,oBAAoB,GAC3BjE,kBAAM,CAACkE,cAAc,EACzB;YAAEtE,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;UAAS,CAC5B,CAAC;QACH;QACA,OAAO,IAAI,CAACuG,sBAAsB,CAChCrD,IAAI,EACJ,KAAK,EACL,CAAC0C,gBAAgB,IAAIC,wBACvB,CAAC;MACH;QACE,IAAI,CAACD,gBAAgB,EAAE,IAAI,CAACY,UAAU,CAAC,CAAC;QACxC,OAAO,IAAI,CAACC,UAAU,CACpB,IAAI,CAACC,mBAAmB,CACtBlB,UAAU,EACVtC,IACF,CAAC,EACD,IACF,CAAC;MAEH;QACE,OAAO,IAAI,CAACyD,gBAAgB,CAACzD,IAA6B,CAAC;MAC7D;QACE,OAAO,IAAI,CAAC0D,oBAAoB,CAAC1D,IAAiC,CAAC;MACrE;QACE,OAAO,IAAI,CAAC2D,oBAAoB,CAAC3D,IAAiC,CAAC;MACrE;QACE,OAAO,IAAI,CAAC4D,mBAAmB,CAAC5D,IAAgC,CAAC;MACnE;QACE,OAAO,IAAI,CAAC6D,iBAAiB,CAAC7D,IAA8B,CAAC;MAE/D;QAEE,IAAI,CAAC,IAAI,CAAC/B,KAAK,CAACoD,WAAW,IAAI,IAAI,CAACK,gBAAgB,CAAC,CAAC,EAAE;UACtD,IAAI,CAAC,IAAI,CAACoC,cAAc,CAAC,CAAC,EAAE;YAC1B,IAAI,CAAC7E,KAAK,CAACC,kBAAM,CAAC6E,2BAA2B,EAAE;cAAEjF,EAAE,EAAEkB;YAAK,CAAC,CAAC;UAC9D,CAAC,MAAM,IAAI,CAAC0C,gBAAgB,EAAE;YAC5B,IAAI,CAACzD,KAAK,CAACC,kBAAM,CAAC8E,4BAA4B,EAAE;cAC9ClF,EAAE,EAAEkB;YACN,CAAC,CAAC;UACJ;UACA,IAAI,CAACE,IAAI,CAAC,CAAC;UACX,OAAO,IAAI,CAAC+D,iBAAiB,CAC3BjE,IAAI,EACJ,aACF,CAAC;QACH;QACA;MACF;QAEE,IACE,IAAI,CAAC/B,KAAK,CAACoD,WAAW,IACtB,CAAC,IAAI,CAACH,mCAAmC,CAAC,CAAC,EAC3C;UACA;QACF;QACA,IAAI,CAACO,YAAY,CAAC,4BAA4B,CAAC;QAC/C,IAAI,CAAC,IAAI,CAAC/C,KAAK,CAACF,QAAQ,IAAI,IAAI,CAACE,KAAK,CAACwF,UAAU,EAAE;UACjD,IAAI,CAACjF,KAAK,CAACC,kBAAM,CAACiF,0BAA0B,EAAE;YAC5CrF,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;UACjB,CAAC,CAAC;QACJ,CAAC,MAAM,IAAI,CAAC4F,gBAAgB,EAAE;UAC5B,IAAI,CAACzD,KAAK,CAACC,kBAAM,CAAC8E,4BAA4B,EAAE;YAC9ClF,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;UACjB,CAAC,CAAC;QACJ;QACA,OAAO,IAAI,CAACmH,iBAAiB,CAC3BjE,IAAI,EACJ,OACF,CAAC;MACH;QAAc;UACZ,IAAI,IAAI,CAAC/B,KAAK,CAACoD,WAAW,EAAE;YAC1B;UACF;UAGA,MAAMnB,IAAI,GAAG,IAAI,CAACc,cAAc,CAAC,CAAC;UAClC,MAAMC,MAAM,GAAG,IAAI,CAACJ,cAAc,CAACX,IAAI,CAAC;UACxC,IAAIe,MAAM,OAAgC,EAAE;YAC1C,IAAI,CAACyB,gBAAgB,IAAI,IAAI,CAAClB,qBAAqB,CAAC,CAAC,EAAE;YACvD,IACE,CAAC,IAAI,CAAClB,yBAAyB,CAACW,MAAM,EAAEf,IAAI,CAAC,IAC7Ce,MAAM,QAA6B,EACnC;cACA;YACF;UACF;QACF;MAEA;QAAgB;UACd,IAAI,CAACyB,gBAAgB,EAAE;YACrB,IAAI,CAACzD,KAAK,CAACC,kBAAM,CAAC8E,4BAA4B,EAAE;cAC9ClF,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;YACjB,CAAC,CAAC;UACJ;QACF;MAEA;QAAc;UACZ,MAAMlC,IAAI,GAAG,IAAI,CAACqD,KAAK,CAAC3B,KAAK;UAC7B,OAAO,IAAI,CAAC2H,iBAAiB,CAC3BjE,IAAI,EACJpF,IACF,CAAC;QACH;MACA;QACE,OAAO,IAAI,CAACwJ,mBAAmB,CAACpE,IAAgC,CAAC;MACnE;QACE,OAAO,IAAI,CAACqE,kBAAkB,CAACrE,IAA+B,CAAC;MACjE;QACE,OAAO,IAAI,CAACsE,UAAU,CAAC,CAAC;MAC1B;QACE,OAAO,IAAI,CAACC,mBAAmB,CAACvE,IAAgC,CAAC;MACnE;QAAiB;UACf,MAAMwE,iBAAiB,GAAG,IAAI,CAACvB,iBAAiB,CAAC,CAAC;UAClD,IACEuB,iBAAiB,OAA8B,IAC/CA,iBAAiB,OAAkB,EACnC;YACA;UACF;QACF;MAEA;QAAiB;UACf,IAAI,CAAC,IAAI,CAACtG,OAAO,CAACuG,2BAA2B,IAAI,CAAC7B,QAAQ,EAAE;YAC1D,IAAI,CAAC3D,KAAK,CAACC,kBAAM,CAACwF,sBAAsB,EAAE;cACxC5F,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;YACjB,CAAC,CAAC;UACJ;UAEA,IAAI,CAACoD,IAAI,CAAC,CAAC;UAEX,IAAIyE,MAAM;UACV,IAAIlC,SAAS,OAAe,EAAE;YAC5BkC,MAAM,GAAG,IAAI,CAACC,WAAW,CAAC5E,IAAmC,CAAC;YAE9D,IACE2E,MAAM,CAACxI,IAAI,KAAK,mBAAmB,KAClC,CAACwI,MAAM,CAACE,UAAU,IAAIF,MAAM,CAACE,UAAU,KAAK,OAAO,CAAC,EACrD;cACA,IAAI,CAACC,iBAAiB,GAAG,IAAI;YAC/B;UACF,CAAC,MAAM;YACLH,MAAM,GAAG,IAAI,CAACI,WAAW,CACvB/E,IAAI,EAKJsC,UACF,CAAC;YAED,IACGqC,MAAM,CAACxI,IAAI,KAAK,wBAAwB,KACtC,CAACwI,MAAM,CAACK,UAAU,IAAIL,MAAM,CAACK,UAAU,KAAK,OAAO,CAAC,IACtDL,MAAM,CAACxI,IAAI,KAAK,sBAAsB,KACpC,CAACwI,MAAM,CAACK,UAAU,IAAIL,MAAM,CAACK,UAAU,KAAK,OAAO,CAAE,IACxDL,MAAM,CAACxI,IAAI,KAAK,0BAA0B,EAC1C;cACA,IAAI,CAAC2I,iBAAiB,GAAG,IAAI;YAC/B;UACF;UAEA,IAAI,CAACG,uBAAuB,CAACN,MAAM,CAAC;UAEpC,OAAOA,MAAM;QACf;MAEA;QAAS;UACP,IAAI,IAAI,CAACO,eAAe,CAAC,CAAC,EAAE;YAC1B,IAAI,CAACxC,gBAAgB,EAAE;cACrB,IAAI,CAACzD,KAAK,CAACC,kBAAM,CAACiG,qCAAqC,EAAE;gBACvDrG,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;cACjB,CAAC,CAAC;YACJ;YACA,IAAI,CAACoD,IAAI,CAAC,CAAC;YACX,OAAO,IAAI,CAACmD,sBAAsB,CAChCrD,IAAI,EACJ,IAAI,EACJ,CAAC0C,gBAAgB,IAAIC,wBACvB,CAAC;UACH;QACF;IACF;IAOA,MAAMyC,SAAS,GAAG,IAAI,CAACnH,KAAK,CAAC3B,KAAK;IAClC,MAAM+I,IAAI,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IAEnC,IACE,IAAA/D,wBAAiB,EAACkB,SAAS,CAAC,IAC5B4C,IAAI,CAAClJ,IAAI,KAAK,YAAY,IAC1B,IAAI,CAACoJ,GAAG,GAAS,CAAC,EAClB;MACA,OAAO,IAAI,CAACC,qBAAqB,CAC/BxF,IAAI,EACJoF,SAAS,EAETC,IAAI,EACJjD,KACF,CAAC;IACH,CAAC,MAAM;MACL,OAAO,IAAI,CAACqD,wBAAwB,CAClCzF,IAAI,EACJqF,IAAI,EACJ/C,UACF,CAAC;IACH;EACF;EAEA2C,uBAAuBA,CAACjF,IAAY,EAAQ;IAC1C,IAAI,CAAC,IAAI,CAAC9B,OAAO,CAACuG,2BAA2B,IAAI,CAAC,IAAI,CAACjG,QAAQ,EAAE;MAC/D,IAAI,CAACS,KAAK,CAACC,kBAAM,CAACwG,mBAAmB,EAAE;QAAE5G,EAAE,EAAEkB;MAAK,CAAC,CAAC;IACtD;EACF;EAEA2F,6BAA6BA,CAAA,EAAY;IACvC,IAAI,IAAI,CAACC,SAAS,CAAC,mBAAmB,CAAC,EAAE,OAAO,IAAI;IACpD,OACE,IAAI,CAACA,SAAS,CAAC,YAAY,CAAC,IAC5B,IAAI,CAACC,eAAe,CAAC,YAAY,EAAE,wBAAwB,CAAC,KAAK,KAAK;EAE1E;EAQArC,mBAAmBA,CACjBsC,eAAqC,EACrCC,SAAY,EACZC,UAA0E,EACvE;IACH,IAAIF,eAAe,EAAE;MACnB,IAAIC,SAAS,CAACzD,UAAU,IAAIyD,SAAS,CAACzD,UAAU,CAACrG,MAAM,GAAG,CAAC,EAAE;QAG3D,IACE,OAAO,IAAI,CAAC4J,eAAe,CACzB,YAAY,EACZ,wBACF,CAAC,KAAK,SAAS,EACf;UAIA,IAAI,CAAC5G,KAAK,CAACC,kBAAM,CAAC+G,2BAA2B,EAAE;YAC7CnH,EAAE,EAAEiH,SAAS,CAACzD,UAAU,CAAC,CAAC;UAC5B,CAAC,CAAC;QACJ;QACAyD,SAAS,CAACzD,UAAU,CAAC4D,OAAO,CAAC,GAAGJ,eAAe,CAAC;MAClD,CAAC,MAAM;QACLC,SAAS,CAACzD,UAAU,GAAGwD,eAAe;MACxC;MACA,IAAI,CAACK,0BAA0B,CAACJ,SAAS,EAAED,eAAe,CAAC,CAAC,CAAC,CAAC;MAC9D,IAAIE,UAAU,EAAE,IAAI,CAACG,0BAA0B,CAACH,UAAU,EAAED,SAAS,CAAC;IACxE;IACA,OAAOA,SAAS;EAClB;EAEAK,uBAAuBA,CAAA,EAAY;IACjC,OAAO,IAAI,CAACrG,KAAK,GAAU,CAAC;EAC9B;EAEAwC,eAAeA,CAAe8D,WAAqB,EAAiB;IAClE,MAAM/D,UAAU,GAAG,EAAE;IACrB,GAAG;MACDA,UAAU,CAACgE,IAAI,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IACxC,CAAC,QAAQ,IAAI,CAACxG,KAAK,GAAM,CAAC;IAE1B,IAAI,IAAI,CAACA,KAAK,GAAW,CAAC,EAAE;MAC1B,IAAI,CAACsG,WAAW,EAAE;QAChB,IAAI,CAAC/C,UAAU,CAAC,CAAC;MACnB;MAEA,IAAI,CAAC,IAAI,CAACqC,6BAA6B,CAAC,CAAC,EAAE;QACzC,IAAI,CAAC1G,KAAK,CAACC,kBAAM,CAACsH,oBAAoB,EAAE;UAAE1H,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;QAAS,CAAC,CAAC;MACtE;IACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAACsJ,uBAAuB,CAAC,CAAC,EAAE;MAC1C,MAAM,IAAI,CAACnH,KAAK,CAACC,kBAAM,CAACuH,0BAA0B,EAAE;QAClD3H,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;MACjB,CAAC,CAAC;IACJ;IAEA,OAAOwF,UAAU;EACnB;EAEAiE,cAAcA,CAAA,EAA4B;IACxC,IAAI,CAACG,eAAe,CAAC,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC;IAEzD,MAAM1G,IAAI,GAAG,IAAI,CAACC,SAAS,CAAc,CAAC;IAC1C,IAAI,CAACC,IAAI,CAAC,CAAC;IAEX,IAAI,IAAI,CAAC0F,SAAS,CAAC,YAAY,CAAC,EAAE;MAChC,MAAM9I,QAAQ,GAAG,IAAI,CAACmB,KAAK,CAACnB,QAAQ;MACpC,IAAIuI,IAAkB;MAEtB,IAAI,IAAI,CAACtF,KAAK,GAAU,CAAC,EAAE;QACzB,MAAMjD,QAAQ,GAAG,IAAI,CAACmB,KAAK,CAACnB,QAAQ;QACpC,IAAI,CAACoD,IAAI,CAAC,CAAC;QACXmF,IAAI,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;QAC7B,IAAI,CAACqB,MAAM,GAAU,CAAC;QACtBtB,IAAI,GAAG,IAAI,CAACuB,eAAe,CAAC9J,QAAQ,EAAEuI,IAAI,CAAC;QAE3C,MAAMwB,cAAc,GAAG,IAAI,CAAC5I,KAAK,CAACnB,QAAQ;QAC1CkD,IAAI,CAACP,UAAU,GAAG,IAAI,CAACqH,4BAA4B,CAACzB,IAAI,CAAC;QACzD,IACE,IAAI,CAACQ,eAAe,CAAC,YAAY,EAAE,wBAAwB,CAAC,KAC1D,KAAK,IACP7F,IAAI,CAACP,UAAU,KAAK4F,IAAI,EACxB;UACA,IAAI,CAACpG,KAAK,CAACC,kBAAM,CAAC6H,oCAAoC,EAAE;YACtDjI,EAAE,EAAE+H;UACN,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACLxB,IAAI,GAAG,IAAI,CAAC2B,eAAe,CAAC,KAAK,CAAC;QAElC,OAAO,IAAI,CAACzB,GAAG,GAAO,CAAC,EAAE;UACvB,MAAMvF,IAAI,GAAG,IAAI,CAACiH,WAAW,CAACnK,QAAQ,CAAC;UACvCkD,IAAI,CAACkH,MAAM,GAAG7B,IAAI;UAClB,IAAI,IAAI,CAACtF,KAAK,IAAe,CAAC,EAAE;YAC9B,IAAI,CAACoH,UAAU,CAACC,cAAc,CAC5B,IAAI,CAACnJ,KAAK,CAAC3B,KAAK,EAChB,IAAI,CAAC2B,KAAK,CAACnB,QACb,CAAC;YACDkD,IAAI,CAACqH,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;UACzC,CAAC,MAAM;YACLtH,IAAI,CAACqH,QAAQ,GAAG,IAAI,CAACL,eAAe,CAAC,IAAI,CAAC;UAC5C;UACAhH,IAAI,CAACuH,QAAQ,GAAG,KAAK;UACrBlC,IAAI,GAAG,IAAI,CAAClH,UAAU,CAAC6B,IAAI,EAAE,kBAAkB,CAAC;QAClD;QAEAA,IAAI,CAACP,UAAU,GAAG,IAAI,CAACqH,4BAA4B,CAACzB,IAAI,CAAC;MAC3D;IACF,CAAC,MAAM;MACLrF,IAAI,CAACP,UAAU,GAAG,IAAI,CAAC+H,mBAAmB,CAAC,CAAC;IAC9C;IACA,OAAO,IAAI,CAACrJ,UAAU,CAAC6B,IAAI,EAAE,WAAW,CAAC;EAC3C;EAEA8G,4BAA4BA,CAAezB,IAAkB,EAAgB;IAC3E,IAAI,IAAI,CAACE,GAAG,GAAU,CAAC,EAAE;MACvB,MAAMvF,IAAI,GAAG,IAAI,CAACyH,eAAe,CAACpC,IAAI,CAAC;MACvCrF,IAAI,CAAC0H,MAAM,GAAGrC,IAAI;MAClBrF,IAAI,CAAC2H,SAAS,GAAG,IAAI,CAACC,4BAA4B,KAAY,KAAK,CAAC;MACpE,IAAI,CAACC,gBAAgB,CAAC7H,IAAI,CAAC2H,SAAS,CAAC;MACrC,OAAO,IAAI,CAACxJ,UAAU,CAAC6B,IAAI,EAAE,gBAAgB,CAAC;IAChD;IAEA,OAAOqF,IAAI;EACb;EAUAxC,2BAA2BA,CACzB7C,IAAoD,EACpD8H,OAAgB,EACwB;IACxC,IAAI,CAAC5H,IAAI,CAAC,CAAC;IAEX,IAAI,IAAI,CAAC6H,gBAAgB,CAAC,CAAC,EAAE;MAC3B/H,IAAI,CAACgI,KAAK,GAAG,IAAI;IACnB,CAAC,MAAM;MACLhI,IAAI,CAACgI,KAAK,GAAG,IAAI,CAAChB,eAAe,CAAC,CAAC;MACnC,IAAI,CAACiB,SAAS,CAAC,CAAC;IAClB;IAEA,IAAI,CAACC,mBAAmB,CAAClI,IAAI,EAAE8H,OAAO,CAAC;IAEvC,OAAO,IAAI,CAAC3J,UAAU,CACpB6B,IAAI,EACJ8H,OAAO,GAAG,gBAAgB,GAAG,mBAC/B,CAAC;EACH;EAEAI,mBAAmBA,CACjBlI,IAAoD,EACpD8H,OAAgB,EAChB;IACA,IAAI9L,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACiC,KAAK,CAACkK,MAAM,CAAClM,MAAM,EAAE,EAAED,CAAC,EAAE;MAC7C,MAAMoM,GAAG,GAAG,IAAI,CAACnK,KAAK,CAACkK,MAAM,CAACnM,CAAC,CAAC;MAChC,IAAIgE,IAAI,CAACgI,KAAK,IAAI,IAAI,IAAII,GAAG,CAACC,IAAI,KAAKrI,IAAI,CAACgI,KAAK,CAACK,IAAI,EAAE;QACtD,IAAID,GAAG,CAACxN,IAAI,IAAI,IAAI,KAAKkN,OAAO,IAAIM,GAAG,CAACxN,IAAI,KAAK,MAAM,CAAC,EAAE;QAC1D,IAAIoF,IAAI,CAACgI,KAAK,IAAIF,OAAO,EAAE;MAC7B;IACF;IACA,IAAI9L,CAAC,KAAK,IAAI,CAACiC,KAAK,CAACkK,MAAM,CAAClM,MAAM,EAAE;MAClC,MAAME,IAAI,GAAG2L,OAAO,GAAG,gBAAgB,GAAG,mBAAmB;MAC7D,IAAI,CAAC7I,KAAK,CAACC,kBAAM,CAACoJ,oBAAoB,EAAE;QAAExJ,EAAE,EAAEkB,IAAI;QAAE7D;MAAK,CAAC,CAAC;IAC7D;EACF;EAEA2G,sBAAsBA,CACpB9C,IAAiC,EACZ;IACrB,IAAI,CAACE,IAAI,CAAC,CAAC;IACX,IAAI,CAAC+H,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI,CAAC9J,UAAU,CAAC6B,IAAI,EAAE,mBAAmB,CAAC;EACnD;EAEAuI,qBAAqBA,CAAA,EAA6B;IAChD,IAAI,CAAC5B,MAAM,GAAU,CAAC;IACtB,MAAM9G,GAAG,GAAG,IAAI,CAACyF,eAAe,CAAC,CAAC;IAClC,IAAI,CAACqB,MAAM,GAAU,CAAC;IACtB,OAAO9G,GAAG;EACZ;EAGAkD,qBAAqBA,CAEnB/C,IAAgC,EACZ;IACpB,IAAI,CAACE,IAAI,CAAC,CAAC;IACX,IAAI,CAACjC,KAAK,CAACkK,MAAM,CAAC7B,IAAI,CAAC3L,SAAS,CAAC;IAGjCqF,IAAI,CAACwI,IAAI,GAIP,IAAI,CAACC,kCAAkC,CAAC,MAEtC,IAAI,CAACpG,cAAc,CAAC,CACtB,CAAC;IAEH,IAAI,CAACpE,KAAK,CAACkK,MAAM,CAACO,GAAG,CAAC,CAAC;IAEvB,IAAI,CAAC/B,MAAM,GAAU,CAAC;IACtB3G,IAAI,CAACW,IAAI,GAAG,IAAI,CAAC4H,qBAAqB,CAAC,CAAC;IACxC,IAAI,CAAChD,GAAG,GAAQ,CAAC;IACjB,OAAO,IAAI,CAACpH,UAAU,CAAC6B,IAAI,EAAE,kBAAkB,CAAC;EAClD;EAUAgD,iBAAiBA,CAEfhD,IAAwC,EAC7B;IACX,IAAI,CAACE,IAAI,CAAC,CAAC;IACX,IAAI,CAACjC,KAAK,CAACkK,MAAM,CAAC7B,IAAI,CAAC3L,SAAS,CAAC;IAEjC,IAAIgO,OAAO,GAAG,IAAI;IAElB,IAAI,IAAI,CAAC7E,cAAc,CAAC,CAAC,IAAI,IAAI,CAAC8E,aAAa,GAAU,CAAC,EAAE;MAC1DD,OAAO,GAAG,IAAI,CAAC1K,KAAK,CAAC4K,eAAe;IACtC;IACA,IAAI,CAACnK,KAAK,CAACoK,KAAK,CAACC,uBAAW,CAAC;IAC7B,IAAI,CAACpC,MAAM,GAAU,CAAC;IAEtB,IAAI,IAAI,CAAC5G,KAAK,GAAQ,CAAC,EAAE;MACvB,IAAI4I,OAAO,KAAK,IAAI,EAAE;QACpB,IAAI,CAACrF,UAAU,CAACqF,OAAO,CAAC;MAC1B;MACA,OAAO,IAAI,CAACK,QAAQ,CAAChJ,IAAI,EAA4B,IAAI,CAAC;IAC5D;IAEA,MAAMiJ,aAAa,GAAG,IAAI,CAAC7I,YAAY,GAAQ,CAAC;IAChD;MACE,MAAM8I,oBAAoB,GACxB,IAAI,CAAC9I,YAAY,GAAU,CAAC,IAAI,IAAI,CAACsB,gBAAgB,CAAC,CAAC;MACzD,MAAMyH,yBAAyB,GAC7BD,oBAAoB,IACnB,IAAI,CAAC9I,YAAY,IAAU,CAAC,IAAI,IAAI,CAACgB,gBAAgB,CAAC,CAAE;MAC3D,MAAMgI,YAAY,GACfH,aAAa,IAAI,IAAI,CAAC5I,uBAAuB,CAAC,CAAC,IAChD8I,yBAAyB;MAE3B,IAAI,IAAI,CAACpJ,KAAK,GAAQ,CAAC,IAAI,IAAI,CAACA,KAAK,GAAU,CAAC,IAAIqJ,YAAY,EAAE;QAChE,MAAMC,QAAQ,GAAG,IAAI,CAACpJ,SAAS,CAAwB,CAAC;QACxD,IAAIrF,IAAI;QACR,IAAIsO,oBAAoB,EAAE;UACxBtO,IAAI,GAAG,aAAa;UACpB,IAAI,CAAC,IAAI,CAACkJ,cAAc,CAAC,CAAC,EAAE;YAC1B,IAAI,CAAC7E,KAAK,CAACC,kBAAM,CAAC6E,2BAA2B,EAAE;cAC7CjF,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;YACjB,CAAC,CAAC;UACJ;UACA,IAAI,CAACoD,IAAI,CAAC,CAAC;QACb,CAAC,MAAM;UACLtF,IAAI,GAAG,IAAI,CAACqD,KAAK,CAAC3B,KAAK;QACzB;QACA,IAAI,CAAC4D,IAAI,CAAC,CAAC;QACX,IAAI,CAACoJ,QAAQ,CAACD,QAAQ,EAAE,IAAI,EAAEzO,IAAI,CAAC;QACnC,MAAM2O,IAAI,GAAG,IAAI,CAACpL,UAAU,CAACkL,QAAQ,EAAE,qBAAqB,CAAC;QAE7D,MAAMG,OAAO,GAAG,IAAI,CAACzJ,KAAK,GAAO,CAAC;QAClC,IAAIyJ,OAAO,IAAIL,yBAAyB,EAAE;UACxC,IAAI,CAAClK,KAAK,CAACC,kBAAM,CAACuK,UAAU,EAAE;YAAE3K,EAAE,EAAEyK;UAAK,CAAC,CAAC;QAC7C;QACA,IACE,CAACC,OAAO,IAAI,IAAI,CAACpJ,YAAY,IAAO,CAAC,KACrCmJ,IAAI,CAACG,YAAY,CAACzN,MAAM,KAAK,CAAC,EAC9B;UACA,OAAO,IAAI,CAAC0N,UAAU,CAAC3J,IAAI,EAAuBuJ,IAAI,EAAEZ,OAAO,CAAC;QAClE;QACA,IAAIA,OAAO,KAAK,IAAI,EAAE;UACpB,IAAI,CAACrF,UAAU,CAACqF,OAAO,CAAC;QAC1B;QACA,OAAO,IAAI,CAACK,QAAQ,CAAChJ,IAAI,EAA4BuJ,IAAI,CAAC;MAC5D;IACF;IAIA,MAAMK,eAAe,GAAG,IAAI,CAACxJ,YAAY,GAAU,CAAC;IAEpD,MAAMyJ,mBAAmB,GAAG,IAAIC,sBAAgB,CAAC,CAAC;IAClD,MAAMP,IAAI,GAAG,IAAI,CAACjE,eAAe,CAAC,IAAI,EAAEuE,mBAAmB,CAAC;IAC5D,MAAME,OAAO,GAAG,IAAI,CAAC3J,YAAY,IAAO,CAAC;IACzC,IAAI2J,OAAO,EAAE;MAEX,IAAId,aAAa,EAAE;QACjB,IAAI,CAAChK,KAAK,CAACC,kBAAM,CAAC8K,QAAQ,EAAE;UAAElL,EAAE,EAAEyK;QAAK,CAAC,CAAC;MAC3C;MAEA,IAEEZ,OAAO,KAAK,IAAI,IAChBiB,eAAe,IACfL,IAAI,CAACpN,IAAI,KAAK,YAAY,EAC1B;QAKA,IAAI,CAAC8C,KAAK,CAACC,kBAAM,CAAC+K,UAAU,EAAE;UAAEnL,EAAE,EAAEyK;QAAK,CAAC,CAAC;MAC7C;IACF;IACA,IAAIQ,OAAO,IAAI,IAAI,CAAChK,KAAK,GAAO,CAAC,EAAE;MACjC,IAAI,CAACmK,yBAAyB,CAACL,mBAAmB,CAAC;MACnD,IAAI,CAACM,YAAY,CAACZ,IAAI,EAAc,IAAI,CAAC;MACzC,MAAMpN,IAAI,GAAG4N,OAAO,GAAG,gBAAgB,GAAG,gBAAgB;MAC1D,IAAI,CAACK,SAAS,CAACb,IAAI,EAAE;QAAEc,EAAE,EAAE;UAAElO;QAAK;MAAE,CAAC,CAAC;MACtC,OAAO,IAAI,CAACwN,UAAU,CACpB3J,IAAI,EAEJuJ,IAAI,EACJZ,OACF,CAAC;IACH,CAAC,MAAM;MACL,IAAI,CAAC2B,qBAAqB,CAACT,mBAAmB,EAAE,IAAI,CAAC;IACvD;IACA,IAAIlB,OAAO,KAAK,IAAI,EAAE;MACpB,IAAI,CAACrF,UAAU,CAACqF,OAAO,CAAC;IAC1B;IACA,OAAO,IAAI,CAACK,QAAQ,CAAChJ,IAAI,EAA4BuJ,IAAI,CAAC;EAC5D;EAGAlG,sBAAsBA,CAEpBrD,IAAmC,EACnCuK,OAAgB,EAChBC,oBAA6B,EACN;IACvB,IAAI,CAACtK,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACuK,aAAa,CACvBzK,IAAI,EACJlF,iBAAiB,CAACE,WAAW,IAC1BwP,oBAAoB,GAAG1P,iBAAiB,CAACG,kBAAkB,GAAG,CAAC,CAAC,IAChEsP,OAAO,GAAGzP,iBAAiB,CAACK,KAAK,GAAG,CAAC,CAC1C,CAAC;EACH;EAGAsI,gBAAgBA,CAAezD,IAA2B,EAAE;IAC1D,IAAI,CAACE,IAAI,CAAC,CAAC;IACXF,IAAI,CAACW,IAAI,GAAG,IAAI,CAAC4H,qBAAqB,CAAC,CAAC;IAGxCvI,IAAI,CAAC0K,UAAU,GAAG,IAAI,CAACxI,+CAA+C,CAAC,CAAC;IACxElC,IAAI,CAAC2K,SAAS,GAAG,IAAI,CAACpF,GAAG,GAAS,CAAC,GAC/B,IAAI,CAACrD,+CAA+C,CAAC,CAAC,GACtD,IAAI;IACR,OAAO,IAAI,CAAC/D,UAAU,CAAC6B,IAAI,EAAE,aAAa,CAAC;EAC7C;EAEA0D,oBAAoBA,CAAe1D,IAA+B,EAAE;IAClE,IAAI,CAAC,IAAI,CAAC4K,SAAS,CAACC,SAAS,IAAI,CAAC,IAAI,CAAC3M,OAAO,CAAC4M,0BAA0B,EAAE;MACzE,IAAI,CAAC7L,KAAK,CAACC,kBAAM,CAAC6L,aAAa,EAAE;QAAEjM,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;MAAS,CAAC,CAAC;IAC/D;IAEA,IAAI,CAACoD,IAAI,CAAC,CAAC;IAMX,IAAI,IAAI,CAAC6H,gBAAgB,CAAC,CAAC,EAAE;MAC3B/H,IAAI,CAACgL,QAAQ,GAAG,IAAI;IACtB,CAAC,MAAM;MACLhL,IAAI,CAACgL,QAAQ,GAAG,IAAI,CAAC1F,eAAe,CAAC,CAAC;MACtC,IAAI,CAAC2C,SAAS,CAAC,CAAC;IAClB;IAEA,OAAO,IAAI,CAAC9J,UAAU,CAAC6B,IAAI,EAAE,iBAAiB,CAAC;EACjD;EAGA2D,oBAAoBA,CAAe3D,IAA+B,EAAE;IAClE,IAAI,CAACE,IAAI,CAAC,CAAC;IACXF,IAAI,CAACiL,YAAY,GAAG,IAAI,CAAC1C,qBAAqB,CAAC,CAAC;IAChD,MAAM2C,KAAiC,GAAIlL,IAAI,CAACkL,KAAK,GAAG,EAAG;IAC3D,IAAI,CAACvE,MAAM,EAAU,CAAC;IACtB,IAAI,CAAC1I,KAAK,CAACkK,MAAM,CAAC7B,IAAI,CAACzL,WAAW,CAAC;IACnC,IAAI,CAAC6D,KAAK,CAACoK,KAAK,CAACC,uBAAW,CAAC;IAM7B,IAAIoC,GAAG;IACP,KAAK,IAAIC,UAAU,EAAE,CAAC,IAAI,CAACrL,KAAK,EAAU,CAAC,GAAI;MAC7C,IAAI,IAAI,CAACA,KAAK,GAAS,CAAC,IAAI,IAAI,CAACA,KAAK,GAAY,CAAC,EAAE;QACnD,MAAMsL,MAAM,GAAG,IAAI,CAACtL,KAAK,GAAS,CAAC;QACnC,IAAIoL,GAAG,EAAE,IAAI,CAAChN,UAAU,CAACgN,GAAG,EAAE,YAAY,CAAC;QAE3CD,KAAK,CAAC5E,IAAI,CAAE6E,GAAG,GAAG,IAAI,CAAClL,SAAS,CAAC,CAAE,CAAC;QACpCkL,GAAG,CAACT,UAAU,GAAG,EAAE;QACnB,IAAI,CAACxK,IAAI,CAAC,CAAC;QACX,IAAImL,MAAM,EAAE;UACVF,GAAG,CAACxK,IAAI,GAAG,IAAI,CAAC2E,eAAe,CAAC,CAAC;QACnC,CAAC,MAAM;UACL,IAAI8F,UAAU,EAAE;YACd,IAAI,CAACnM,KAAK,CAACC,kBAAM,CAACoM,wBAAwB,EAAE;cAC1CxM,EAAE,EAAE,IAAI,CAACb,KAAK,CAAC4K;YACjB,CAAC,CAAC;UACJ;UACAuC,UAAU,GAAG,IAAI;UACjBD,GAAG,CAACxK,IAAI,GAAG,IAAI;QACjB;QACA,IAAI,CAACgG,MAAM,GAAS,CAAC;MACvB,CAAC,MAAM;QACL,IAAIwE,GAAG,EAAE;UACPA,GAAG,CAACT,UAAU,CAACpE,IAAI,CAAC,IAAI,CAACvE,sBAAsB,CAAC,CAAC,CAAC;QACpD,CAAC,MAAM;UACL,IAAI,CAACuB,UAAU,CAAC,CAAC;QACnB;MACF;IACF;IACA,IAAI,CAAC5E,KAAK,CAAC6M,IAAI,CAAC,CAAC;IACjB,IAAIJ,GAAG,EAAE,IAAI,CAAChN,UAAU,CAACgN,GAAG,EAAE,YAAY,CAAC;IAC3C,IAAI,CAACjL,IAAI,CAAC,CAAC;IACX,IAAI,CAACjC,KAAK,CAACkK,MAAM,CAACO,GAAG,CAAC,CAAC;IACvB,OAAO,IAAI,CAACvK,UAAU,CAAC6B,IAAI,EAAE,iBAAiB,CAAC;EACjD;EAEA4D,mBAAmBA,CAAe5D,IAA8B,EAAE;IAChE,IAAI,CAACE,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAACsL,qBAAqB,CAAC,CAAC,EAAE;MAChC,IAAI,CAACvM,KAAK,CAACC,kBAAM,CAACuM,iBAAiB,EAAE;QAAE3M,EAAE,EAAE,IAAI,CAACb,KAAK,CAACyN;MAAc,CAAC,CAAC;IACxE;IACA1L,IAAI,CAACgL,QAAQ,GAAG,IAAI,CAAC1F,eAAe,CAAC,CAAC;IACtC,IAAI,CAAC2C,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI,CAAC9J,UAAU,CAAC6B,IAAI,EAAE,gBAAgB,CAAC;EAChD;EAEA2L,qBAAqBA,CAAA,EAA0B;IAC7C,MAAMC,KAAK,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAErC,IAAI,CAACnN,KAAK,CAACoK,KAAK,CACd,IAAI,CAAC5K,OAAO,CAAC8D,MAAM,IAAI4J,KAAK,CAACzP,IAAI,KAAK,YAAY,GAC9C2P,8BAAkB,GAClB,CACN,CAAC;IACD,IAAI,CAAC1B,SAAS,CAACwB,KAAK,EAAE;MACpBvB,EAAE,EAAE;QAAElO,IAAI,EAAE;MAAc,CAAC;MAC3B4P,OAAO,EAAEC;IACX,CAAC,CAAC;IAEF,OAAOJ,KAAK;EACd;EAEA/H,iBAAiBA,CAEf7D,IAA4B,EACZ;IAChB,IAAI,CAACE,IAAI,CAAC,CAAC;IAEXF,IAAI,CAACiM,KAAK,GAAG,IAAI,CAAC3H,UAAU,CAAC,CAAC;IAC9BtE,IAAI,CAACkM,OAAO,GAAG,IAAI;IAEnB,IAAI,IAAI,CAACnM,KAAK,GAAU,CAAC,EAAE;MACzB,MAAMoM,MAAM,GAAG,IAAI,CAAClM,SAAS,CAAgB,CAAC;MAC9C,IAAI,CAACC,IAAI,CAAC,CAAC;MACX,IAAI,IAAI,CAACH,KAAK,GAAU,CAAC,EAAE;QACzB,IAAI,CAAC4G,MAAM,GAAU,CAAC;QACtBwF,MAAM,CAACP,KAAK,GAAG,IAAI,CAACD,qBAAqB,CAAC,CAAC;QAC3C,IAAI,CAAChF,MAAM,GAAU,CAAC;MACxB,CAAC,MAAM;QACLwF,MAAM,CAACP,KAAK,GAAG,IAAI;QACnB,IAAI,CAAClN,KAAK,CAACoK,KAAK,CAACC,uBAAW,CAAC;MAC/B;MAGAoD,MAAM,CAAC3D,IAAI,GAGT,IAAI,CAACC,kCAAkC,CAAC,MAEtC,IAAI,CAACnE,UAAU,CAAC,KAAK,EAAE,KAAK,CAC9B,CAAC;MAEH,IAAI,CAAC5F,KAAK,CAAC6M,IAAI,CAAC,CAAC;MACjBvL,IAAI,CAACkM,OAAO,GAAG,IAAI,CAAC/N,UAAU,CAACgO,MAAM,EAAE,aAAa,CAAC;IACvD;IAEAnM,IAAI,CAACoM,SAAS,GAAG,IAAI,CAAC7G,GAAG,GAAY,CAAC,GAAG,IAAI,CAACjB,UAAU,CAAC,CAAC,GAAG,IAAI;IAEjE,IAAI,CAACtE,IAAI,CAACkM,OAAO,IAAI,CAAClM,IAAI,CAACoM,SAAS,EAAE;MACpC,IAAI,CAACnN,KAAK,CAACC,kBAAM,CAACmN,gBAAgB,EAAE;QAAEvN,EAAE,EAAEkB;MAAK,CAAC,CAAC;IACnD;IAEA,OAAO,IAAI,CAAC7B,UAAU,CAAC6B,IAAI,EAAE,cAAc,CAAC;EAC9C;EAIAiE,iBAAiBA,CAEfjE,IAAmC,EACnCpF,IAAuD,EACvD0R,uBAAgC,GAAG,KAAK,EACjB;IACvB,IAAI,CAACpM,IAAI,CAAC,CAAC;IACX,IAAI,CAACoJ,QAAQ,CAACtJ,IAAI,EAAE,KAAK,EAAEpF,IAAI,EAAE0R,uBAAuB,CAAC;IACzD,IAAI,CAACrE,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI,CAAC9J,UAAU,CAAC6B,IAAI,EAAE,qBAAqB,CAAC;EACrD;EAGAoE,mBAAmBA,CAEjBpE,IAA8B,EACZ;IAClB,IAAI,CAACE,IAAI,CAAC,CAAC;IACXF,IAAI,CAACW,IAAI,GAAG,IAAI,CAAC4H,qBAAqB,CAAC,CAAC;IACxC,IAAI,CAACtK,KAAK,CAACkK,MAAM,CAAC7B,IAAI,CAAC3L,SAAS,CAAC;IAGjCqF,IAAI,CAACwI,IAAI,GAIP,IAAI,CAACC,kCAAkC,CAAC,MAEtC,IAAI,CAACpG,cAAc,CAAC,CACtB,CAAC;IAEH,IAAI,CAACpE,KAAK,CAACkK,MAAM,CAACO,GAAG,CAAC,CAAC;IAEvB,OAAO,IAAI,CAACvK,UAAU,CAAC6B,IAAI,EAAE,gBAAgB,CAAC;EAChD;EAEAqE,kBAAkBA,CAEhBrE,IAA6B,EACZ;IACjB,IAAI,IAAI,CAAC/B,KAAK,CAACgE,MAAM,EAAE;MACrB,IAAI,CAAChD,KAAK,CAACC,kBAAM,CAACqN,UAAU,EAAE;QAAEzN,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;MAAS,CAAC,CAAC;IAC5D;IACA,IAAI,CAACoD,IAAI,CAAC,CAAC;IACXF,IAAI,CAACkH,MAAM,GAAG,IAAI,CAACqB,qBAAqB,CAAC,CAAC;IAG1CvI,IAAI,CAACwI,IAAI,GAKP,IAAI,CAACC,kCAAkC,CAAC,MAEtC,IAAI,CAACpG,cAAc,CAAC,CACtB,CAAC;IAEH,OAAO,IAAI,CAAClE,UAAU,CAAC6B,IAAI,EAAE,eAAe,CAAC;EAC/C;EAEAuE,mBAAmBA,CAACvE,IAA8B,EAAoB;IACpE,IAAI,CAACE,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAAC/B,UAAU,CAAC6B,IAAI,EAAE,gBAAgB,CAAC;EAChD;EAGAwF,qBAAqBA,CAEnBxF,IAAgC,EAChCoF,SAAiB,EACjBC,IAAkB,EAClBjD,KAAyB,EACL;IACpB,KAAK,MAAM4F,KAAK,IAAI,IAAI,CAAC/J,KAAK,CAACkK,MAAM,EAAE;MACrC,IAAIH,KAAK,CAACK,IAAI,KAAKjD,SAAS,EAAE;QAC5B,IAAI,CAACnG,KAAK,CAACC,kBAAM,CAACsN,kBAAkB,EAAE;UACpC1N,EAAE,EAAEuG,IAAI;UACRoH,SAAS,EAAErH;QACb,CAAC,CAAC;MACJ;IACF;IAEA,MAAMxK,IAAI,GAAG,IAAA8R,kBAAW,EAAC,IAAI,CAACzO,KAAK,CAAC9B,IAAI,CAAC,GACrC,MAAM,GACN,IAAI,CAAC4D,KAAK,GAAW,CAAC,GACtB,QAAQ,GACR,IAAI;IACR,KAAK,IAAI/D,CAAC,GAAG,IAAI,CAACiC,KAAK,CAACkK,MAAM,CAAClM,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACtD,MAAMgM,KAAK,GAAG,IAAI,CAAC/J,KAAK,CAACkK,MAAM,CAACnM,CAAC,CAAC;MAClC,IAAIgM,KAAK,CAAC2E,cAAc,KAAK3M,IAAI,CAAC3D,KAAK,EAAE;QACvC2L,KAAK,CAAC2E,cAAc,GAAG,IAAI,CAAC1O,KAAK,CAAC5B,KAAK;QACvC2L,KAAK,CAACpN,IAAI,GAAGA,IAAI;MACnB,CAAC,MAAM;QACL;MACF;IACF;IAEA,IAAI,CAACqD,KAAK,CAACkK,MAAM,CAAC7B,IAAI,CAAC;MACrB+B,IAAI,EAAEjD,SAAS;MACfxK,IAAI,EAAEA,IAAI;MACV+R,cAAc,EAAE,IAAI,CAAC1O,KAAK,CAAC5B;IAC7B,CAAC,CAAC;IAEF2D,IAAI,CAACwI,IAAI,GACPpG,KAAK,GAAG/G,kBAAkB,CAACK,oBAAoB,GAC3C,IAAI,CAACwG,+CAA+C,CAAC,IAAI,CAAC,GAC1D,IAAI,CAACG,cAAc,CAAC,CAAC;IAE3B,IAAI,CAACpE,KAAK,CAACkK,MAAM,CAACO,GAAG,CAAC,CAAC;IACvB1I,IAAI,CAACgI,KAAK,GAAG3C,IAAI;IACjB,OAAO,IAAI,CAAClH,UAAU,CAAC6B,IAAI,EAAE,kBAAkB,CAAC;EAClD;EAEAyF,wBAAwBA,CACtBzF,IAAmC,EACnCqF,IAAkB,EAElB/C,UAAgC,EAChC;IACAtC,IAAI,CAACP,UAAU,GAAG4F,IAAI;IACtB,IAAI,CAAC4C,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI,CAAC9J,UAAU,CAAC6B,IAAI,EAAE,qBAAqB,CAAC;EACrD;EAMAsE,UAAUA,CAERsI,eAAwB,GAAG,KAAK,EAChCC,qBAA8B,GAAG,IAAI,EACrCC,eAA2D,EACzC;IAClB,MAAM9M,IAAI,GAAG,IAAI,CAACC,SAAS,CAAmB,CAAC;IAC/C,IAAI2M,eAAe,EAAE;MACnB,IAAI,CAAC3O,KAAK,CAAC8O,YAAY,CAACC,KAAK,CAAC,CAAC;IACjC;IACA,IAAI,CAACrG,MAAM,EAAU,CAAC;IACtB,IAAIkG,qBAAqB,EAAE;MACzB,IAAI,CAACnO,KAAK,CAACoK,KAAK,CAACC,uBAAW,CAAC;IAC/B;IACA,IAAI,CAACxK,cAAc,CACjByB,IAAI,EACJ4M,eAAe,EACf,KAAK,KAELE,eACF,CAAC;IACD,IAAID,qBAAqB,EAAE;MACzB,IAAI,CAACnO,KAAK,CAAC6M,IAAI,CAAC,CAAC;IACnB;IACA,OAAO,IAAI,CAACpN,UAAU,CAAC6B,IAAI,EAAE,gBAAgB,CAAC;EAChD;EAEAiN,gBAAgBA,CAAC1N,IAAiB,EAAW;IAC3C,OACEA,IAAI,CAACpD,IAAI,KAAK,qBAAqB,IACnCoD,IAAI,CAACE,UAAU,CAACtD,IAAI,KAAK,eAAe,IACxC,CAACoD,IAAI,CAACE,UAAU,CAACyN,KAAK,CAACC,aAAa;EAExC;EAEA5O,cAAcA,CAEZyB,IAAkC,EAClC4M,eAA2C,EAC3ChK,QAAiB,EACjBrG,GAAc,EACduQ,eAA2D,EACrD;IACN,MAAMtE,IAAkC,GAAIxI,IAAI,CAACwI,IAAI,GAAG,EAAG;IAC3D,MAAM4E,UAA8C,GAAIpN,IAAI,CAACoN,UAAU,GACrE,EAAG;IACL,IAAI,CAACC,2BAA2B,CAC9B7E,IAAI,EACJoE,eAAe,GAAGQ,UAAU,GAAGE,SAAS,EACxC1K,QAAQ,EACRrG,GAAG,EACHuQ,eACF,CAAC;EACH;EAKAO,2BAA2BA,CAEzB7E,IAAmB,EACnB4E,UAA4C,EAC5CxK,QAAiB,EACjBrG,GAAc,EACduQ,eAA2D,EACrD;IACN,MAAMS,SAAS,GAAG,IAAI,CAACtP,KAAK,CAACgE,MAAM;IACnC,IAAIuL,sBAAsB,GAAG,KAAK;IAClC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,OAAO,CAAC,IAAI,CAAC1N,KAAK,CAACxD,GAAG,CAAC,EAAE;MACvB,MAAMgD,IAAI,GAAGqD,QAAQ,GACjB,IAAI,CAACf,eAAe,CAAC,CAAC,GACtB,IAAI,CAACE,sBAAsB,CAAC,CAAC;MAEjC,IAAIqL,UAAU,IAAI,CAACK,kBAAkB,EAAE;QACrC,IAAI,IAAI,CAACR,gBAAgB,CAAC1N,IAAI,CAAC,EAAE;UAC/B,MAAMC,SAAS,GAAG,IAAI,CAACF,eAAe,CAACC,IAAI,CAAC;UAC5C6N,UAAU,CAAC9G,IAAI,CAAC9G,SAAS,CAAC;UAE1B,IACE,CAACgO,sBAAsB,IACvBhO,SAAS,CAAClD,KAAK,CAACA,KAAK,KAAK,YAAY,EACtC;YACAkR,sBAAsB,GAAG,IAAI;YAC7B,IAAI,CAACE,SAAS,CAAC,IAAI,CAAC;UACtB;UAEA;QACF;QACAD,kBAAkB,GAAG,IAAI;QAEzB,IAAI,CAACxP,KAAK,CAAC8O,YAAY,CAACC,KAAK,CAAC,CAAC;MACjC;MACAxE,IAAI,CAAClC,IAAI,CAAC/G,IAAI,CAAC;IACjB;IAEA,IAAIuN,eAAe,EAAE;MACnBA,eAAe,CAACa,IAAI,CAAC,IAAI,EAAEH,sBAAsB,CAAC;IACpD;IAEA,IAAI,CAACD,SAAS,EAAE;MACd,IAAI,CAACG,SAAS,CAAC,KAAK,CAAC;IACvB;IAEA,IAAI,CAACxN,IAAI,CAAC,CAAC;EACb;EAMA8I,QAAQA,CAENhJ,IAA4B,EAC5BuJ,IAAkD,EAClC;IAChBvJ,IAAI,CAACuJ,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACtB,SAAS,CAAgB,KAAK,CAAC;IACpCjI,IAAI,CAACW,IAAI,GAAG,IAAI,CAACZ,KAAK,GAAQ,CAAC,GAAG,IAAI,GAAG,IAAI,CAACuF,eAAe,CAAC,CAAC;IAC/D,IAAI,CAAC2C,SAAS,CAAgB,KAAK,CAAC;IACpCjI,IAAI,CAAC4N,MAAM,GAAG,IAAI,CAAC7N,KAAK,GAAU,CAAC,GAAG,IAAI,GAAG,IAAI,CAACuF,eAAe,CAAC,CAAC;IACnE,IAAI,CAACqB,MAAM,GAAU,CAAC;IAGtB3G,IAAI,CAACwI,IAAI,GAIP,IAAI,CAACC,kCAAkC,CAAC,MAEtC,IAAI,CAACpG,cAAc,CAAC,CACtB,CAAC;IAEH,IAAI,CAAC3D,KAAK,CAAC6M,IAAI,CAAC,CAAC;IACjB,IAAI,CAACtN,KAAK,CAACkK,MAAM,CAACO,GAAG,CAAC,CAAC;IAEvB,OAAO,IAAI,CAACvK,UAAU,CAAC6B,IAAI,EAAE,cAAc,CAAC;EAC9C;EAKA2J,UAAUA,CAER3J,IAAuB,EACvBuJ,IAAiD,EACjDZ,OAAyB,EACd;IACX,MAAMa,OAAO,GAAG,IAAI,CAACzJ,KAAK,GAAO,CAAC;IAClC,IAAI,CAACG,IAAI,CAAC,CAAC;IAEX,IAAIsJ,OAAO,EAAE;MACX,IAAIb,OAAO,KAAK,IAAI,EAAE,IAAI,CAACrF,UAAU,CAACqF,OAAO,CAAC;IAChD,CAAC,MAAM;MACL3I,IAAI,CAAC6N,KAAK,GAAGlF,OAAO,KAAK,IAAI;IAC/B;IAEA,IACEY,IAAI,CAACpN,IAAI,KAAK,qBAAqB,IACnCoN,IAAI,CAACG,YAAY,CAAC,CAAC,CAAC,CAACH,IAAI,IAAI,IAAI,KAChC,CAACC,OAAO,IACP,CAAC,IAAI,CAACtL,OAAO,CAAC8D,MAAM,IACpB,IAAI,CAAC/D,KAAK,CAACgE,MAAM,IACjBsH,IAAI,CAAC3O,IAAI,KAAK,KAAK,IACnB2O,IAAI,CAACG,YAAY,CAAC,CAAC,CAAC,CAACoE,EAAE,CAAC3R,IAAI,KAAK,YAAY,CAAC,EAChD;MACA,IAAI,CAAC8C,KAAK,CAACC,kBAAM,CAAC6O,sBAAsB,EAAE;QACxCjP,EAAE,EAAEyK,IAAI;QACRpN,IAAI,EAAEqN,OAAO,GAAG,gBAAgB,GAAG;MACrC,CAAC,CAAC;IACJ;IAEA,IAAID,IAAI,CAACpN,IAAI,KAAK,mBAAmB,EAAE;MACrC,IAAI,CAAC8C,KAAK,CAACC,kBAAM,CAAC8O,UAAU,EAAE;QAC5BlP,EAAE,EAAEyK,IAAI;QACR0E,QAAQ,EAAE;UAAE9R,IAAI,EAAE;QAAe;MACnC,CAAC,CAAC;IACJ;IAEA6D,IAAI,CAACkO,IAAI,GAAG3E,IAAI;IAChBvJ,IAAI,CAACmO,KAAK,GAAG3E,OAAO,GAChB,IAAI,CAAClE,eAAe,CAAC,CAAC,GACtB,IAAI,CAAC8I,uBAAuB,CAAC,CAAC;IAClC,IAAI,CAACzH,MAAM,GAAU,CAAC;IAGtB3G,IAAI,CAACwI,IAAI,GAIP,IAAI,CAACC,kCAAkC,CAAC,MAEtC,IAAI,CAACpG,cAAc,CAAC,CACtB,CAAC;IAEH,IAAI,CAAC3D,KAAK,CAAC6M,IAAI,CAAC,CAAC;IACjB,IAAI,CAACtN,KAAK,CAACkK,MAAM,CAACO,GAAG,CAAC,CAAC;IAEvB,OAAO,IAAI,CAACvK,UAAU,CAAC6B,IAAI,EAAEwJ,OAAO,GAAG,gBAAgB,GAAG,gBAAgB,CAAC;EAC7E;EAIAF,QAAQA,CAENtJ,IAAmC,EACnCqO,KAAc,EACdzT,IAAuD,EACvD0R,uBAAgC,GAAG,KAAK,EACT;IAC/B,MAAM5C,YAAoC,GAAI1J,IAAI,CAAC0J,YAAY,GAAG,EAAG;IACrE1J,IAAI,CAACpF,IAAI,GAAGA,IAAI;IAChB,SAAS;MACP,MAAM0T,IAAI,GAAG,IAAI,CAACrO,SAAS,CAAuB,CAAC;MACnD,IAAI,CAACsO,UAAU,CAACD,IAAI,EAAE1T,IAAI,CAAC;MAC3B0T,IAAI,CAAC/E,IAAI,GAAG,CAAC,IAAI,CAAChE,GAAG,GAAM,CAAC,GACxB,IAAI,GACJ8I,KAAK,GACL,IAAI,CAACG,0BAA0B,CAAC,CAAC,GACjC,IAAI,CAACJ,uBAAuB,CAAC,CAAC;MAElC,IAAIE,IAAI,CAAC/E,IAAI,KAAK,IAAI,IAAI,CAAC+C,uBAAuB,EAAE;QAClD,IACEgC,IAAI,CAACR,EAAE,CAAC3R,IAAI,KAAK,YAAY,IAC7B,EAAEkS,KAAK,KAAK,IAAI,CAACtO,KAAK,GAAO,CAAC,IAAI,IAAI,CAACK,YAAY,IAAO,CAAC,CAAC,CAAC,EAC7D;UACA,IAAI,CAACnB,KAAK,CAACC,kBAAM,CAACuP,6BAA6B,EAAE;YAC/C3P,EAAE,EAAE,IAAI,CAACb,KAAK,CAACyN,aAAa;YAC5B9Q,IAAI,EAAE;UACR,CAAC,CAAC;QACJ,CAAC,MAAM,IACLA,IAAI,KAAK,OAAO,IAChB,EAAE,IAAI,CAACmF,KAAK,GAAO,CAAC,IAAI,IAAI,CAACK,YAAY,IAAO,CAAC,CAAC,EAClD;UACA,IAAI,CAACnB,KAAK,CAACC,kBAAM,CAACuP,6BAA6B,EAAE;YAC/C3P,EAAE,EAAE,IAAI,CAACb,KAAK,CAACyN,aAAa;YAC5B9Q,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;MACF;MACA8O,YAAY,CAACpD,IAAI,CAAC,IAAI,CAACnI,UAAU,CAACmQ,IAAI,EAAE,oBAAoB,CAAC,CAAC;MAC9D,IAAI,CAAC,IAAI,CAAC/I,GAAG,GAAS,CAAC,EAAE;IAC3B;IACA,OAAOvF,IAAI;EACb;EAEAuO,UAAUA,CAERD,IAAkC,EAClC1T,IAAuD,EACjD;IACN,MAAMkT,EAAE,GAAG,IAAI,CAACjC,gBAAgB,CAAC,CAAC;IAClC,IAAI,CAACzB,SAAS,CAAC0D,EAAE,EAAE;MACjBzD,EAAE,EAAE;QAAElO,IAAI,EAAE;MAAqB,CAAC;MAClC4P,OAAO,EAAEnR,IAAI,KAAK,KAAK,GAAG8T,oBAAQ,GAAGC;IACvC,CAAC,CAAC;IACFL,IAAI,CAACR,EAAE,GAAGA,EAAE;EACd;EAGAc,4BAA4BA,CAE1B5O,IAAkC,EACZ;IACtB,OAAO,IAAI,CAACyK,aAAa,CAACzK,IAAI,EAAElF,iBAAiB,CAACK,KAAK,CAAC;EAC1D;EAKAsP,aAAaA,CAEXzK,IAAe,EACfoC,KAAwB,GAAGtH,iBAAiB,CAACC,UAAU,EACpD;IACH,MAAM8T,kBAAkB,GAAGzM,KAAK,GAAGtH,iBAAiB,CAACG,kBAAkB;IACvE,MAAM6T,aAAa,GAAG,CAAC,EAAE1M,KAAK,GAAGtH,iBAAiB,CAACE,WAAW,CAAC;IAC/D,MAAM+T,SAAS,GAAGD,aAAa,IAAI,EAAE1M,KAAK,GAAGtH,iBAAiB,CAACI,UAAU,CAAC;IAC1E,MAAMqP,OAAO,GAAG,CAAC,EAAEnI,KAAK,GAAGtH,iBAAiB,CAACK,KAAK,CAAC;IAEnD,IAAI,CAAC6T,YAAY,CAAChP,IAAI,EAAEuK,OAAO,CAAC;IAEhC,IAAI,IAAI,CAACxK,KAAK,GAAQ,CAAC,EAAE;MACvB,IAAI8O,kBAAkB,EAAE;QACtB,IAAI,CAAC5P,KAAK,CAACC,kBAAM,CAAC+P,iCAAiC,EAAE;UACnDnQ,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;QACjB,CAAC,CAAC;MACJ;MACA,IAAI,CAACoD,IAAI,CAAC,CAAC;MACXF,IAAI,CAACkP,SAAS,GAAG,IAAI;IACvB;IAEA,IAAIJ,aAAa,EAAE;MACjB9O,IAAI,CAAC8N,EAAE,GAAG,IAAI,CAACqB,eAAe,CAACJ,SAAS,CAAC;IAC3C;IAEA,MAAMK,yBAAyB,GAAG,IAAI,CAACnR,KAAK,CAACoR,sBAAsB;IACnE,IAAI,CAACpR,KAAK,CAACoR,sBAAsB,GAAG,KAAK;IACzC,IAAI,CAAC3Q,KAAK,CAACoK,KAAK,CAACwG,0BAAc,CAAC;IAChC,IAAI,CAAC1E,SAAS,CAAC9B,KAAK,CAAC,IAAAyG,kCAAa,EAAChF,OAAO,EAAEvK,IAAI,CAACkP,SAAS,CAAC,CAAC;IAE5D,IAAI,CAACJ,aAAa,EAAE;MAClB9O,IAAI,CAAC8N,EAAE,GAAG,IAAI,CAACqB,eAAe,CAAC,CAAC;IAClC;IAEA,IAAI,CAACK,mBAAmB,CAACxP,IAAI,EAAsB,KAAK,CAAC;IAKzD,IAAI,CAACyI,kCAAkC,CAAC,MAAM;MAE5C,IAAI,CAACgH,0BAA0B,CAC7BzP,IAAI,EACJ8O,aAAa,GAAG,qBAAqB,GAAG,oBAC1C,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAAClE,SAAS,CAACW,IAAI,CAAC,CAAC;IACrB,IAAI,CAAC7M,KAAK,CAAC6M,IAAI,CAAC,CAAC;IAEjB,IAAIuD,aAAa,IAAI,CAACD,kBAAkB,EAAE;MAIxC,IAAI,CAACa,2BAA2B,CAAC1P,IAAS,CAAC;IAC7C;IAEA,IAAI,CAAC/B,KAAK,CAACoR,sBAAsB,GAAGD,yBAAyB;IAC7D,OAAOpP,IAAI;EACb;EAEAmP,eAAeA,CAACJ,SAAmB,EAAmC;IACpE,OAAOA,SAAS,IAAI,IAAAxN,wBAAiB,EAAC,IAAI,CAACtD,KAAK,CAAC9B,IAAI,CAAC,GAClD,IAAI,CAAC6K,eAAe,CAAC,CAAC,GACtB,IAAI;EACV;EAEAwI,mBAAmBA,CAEjBxP,IAAwB,EACxB2P,aAAuB,EACjB;IACN,IAAI,CAAChJ,MAAM,GAAU,CAAC;IACtB,IAAI,CAACiJ,eAAe,CAAC9G,KAAK,CAAC,IAAA+G,6CAA4B,EAAC,CAAC,CAAC;IAC1D7P,IAAI,CAAC8P,MAAM,GAAG,IAAI,CAACC,gBAAgB,SAGjCC,2BAAqB,CAACC,kBAAkB,IACrCN,aAAa,GAAGK,2BAAqB,CAACE,qBAAqB,GAAG,CAAC,CACpE,CAAC;IAED,IAAI,CAACN,eAAe,CAACrE,IAAI,CAAC,CAAC;EAC7B;EAEAmE,2BAA2BA,CAAC1P,IAAgB,EAAQ;IAClD,IAAI,CAACA,IAAI,CAAC8N,EAAE,EAAE;IAMd,IAAI,CAACpP,KAAK,CAACyR,WAAW,CACpBnQ,IAAI,CAAC8N,EAAE,CAACzF,IAAI,EACZ,CAAC,IAAI,CAACnK,OAAO,CAAC8D,MAAM,IAAI,IAAI,CAAC/D,KAAK,CAACgE,MAAM,IAAIjC,IAAI,CAACkP,SAAS,IAAIlP,IAAI,CAACoQ,KAAK,GACrE,IAAI,CAAC1R,KAAK,CAAC2R,mBAAmB,GAC5B3B,oBAAQ,GACRC,wBAAY,GACd2B,yBAAa,EACjBtQ,IAAI,CAAC8N,EAAE,CAAC1R,GAAG,CAACC,KACd,CAAC;EACH;EAKAkH,UAAUA,CAERvD,IAAe,EACfuQ,WAAiD,EACjDC,UAAoB,EACjB;IACH,IAAI,CAACtQ,IAAI,CAAC,CAAC;IAGX,MAAMqN,SAAS,GAAG,IAAI,CAACtP,KAAK,CAACgE,MAAM;IACnC,IAAI,CAAChE,KAAK,CAACgE,MAAM,GAAG,IAAI;IAExB,IAAI,CAACwO,YAAY,CAACzQ,IAAI,EAAEuQ,WAAW,EAAEC,UAAU,CAAC;IAChD,IAAI,CAACE,eAAe,CAAC1Q,IAAI,CAAC;IAE1BA,IAAI,CAACwI,IAAI,GAAG,IAAI,CAACmI,cAAc,CAAC,CAAC,CAAC3Q,IAAI,CAAC4Q,UAAU,EAAErD,SAAS,CAAC;IAE7D,OAAO,IAAI,CAACpP,UAAU,CACpB6B,IAAI,EACJuQ,WAAW,GAAG,kBAAkB,GAAG,iBACrC,CAAC;EACH;EAEAM,eAAeA,CAAA,EAAY;IACzB,OAAO,IAAI,CAAC9Q,KAAK,GAAM,CAAC,IAAI,IAAI,CAACA,KAAK,GAAQ,CAAC,IAAI,IAAI,CAACA,KAAK,EAAU,CAAC;EAC1E;EAEA+Q,aAAaA,CAAA,EAAY;IACvB,OAAO,IAAI,CAAC/Q,KAAK,GAAU,CAAC;EAC9B;EAEAgR,sBAAsBA,CAACC,MAAuC,EAAW;IACvE,OACE,CAACA,MAAM,CAACzJ,QAAQ,IAChB,CAACyJ,MAAM,CAACC,MAAM,KACbD,MAAM,CAACE,GAAG,CAAC7I,IAAI,KAAK,aAAa,IAChC2I,MAAM,CAACE,GAAG,CAAC5U,KAAK,KAAK,aAAa,CAAC;EAEzC;EAGAqU,cAAcA,CAEZQ,aAAsB,EACtB5D,SAAkB,EACL;IACb,IAAI,CAACpG,UAAU,CAAC2B,KAAK,CAAC,CAAC;IAEvB,MAAM7K,KAA8B,GAAG;MACrCmT,cAAc,EAAE,KAAK;MACrBD;IACF,CAAC;IACD,IAAI7O,UAAyB,GAAG,EAAE;IAClC,MAAM+O,SAAS,GAAG,IAAI,CAACpR,SAAS,CAAc,CAAC;IAC/CoR,SAAS,CAAC7I,IAAI,GAAG,EAAE;IAEnB,IAAI,CAAC7B,MAAM,EAAU,CAAC;IAItB,IAAI,CAAC8B,kCAAkC,CAAC,MAAM;MAE5C,OAAO,CAAC,IAAI,CAAC1I,KAAK,EAAU,CAAC,EAAE;QAC7B,IAAI,IAAI,CAACwF,GAAG,GAAQ,CAAC,EAAE;UACrB,IAAIjD,UAAU,CAACrG,MAAM,GAAG,CAAC,EAAE;YACzB,MAAM,IAAI,CAACgD,KAAK,CAACC,kBAAM,CAACoS,kBAAkB,EAAE;cAC1CxS,EAAE,EAAE,IAAI,CAACb,KAAK,CAACyN;YACjB,CAAC,CAAC;UACJ;UACA;QACF;QAEA,IAAI,IAAI,CAAC3L,KAAK,GAAM,CAAC,EAAE;UACrBuC,UAAU,CAACgE,IAAI,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;UACtC;QACF;QAEA,MAAMgL,MAAM,GAAG,IAAI,CAACtR,SAAS,CAAgB,CAAC;QAG9C,IAAIqC,UAAU,CAACrG,MAAM,EAAE;UAErBsV,MAAM,CAACjP,UAAU,GAAGA,UAAU;UAC9B,IAAI,CAAC6D,0BAA0B,CAACoL,MAAM,EAAEjP,UAAU,CAAC,CAAC,CAAC,CAAC;UACtDA,UAAU,GAAG,EAAE;QACjB;QAEA,IAAI,CAACkP,gBAAgB,CAACH,SAAS,EAAEE,MAAM,EAAEtT,KAAK,CAAC;QAE/C,IAEEsT,MAAM,CAAC3W,IAAI,KAAK,aAAa,IAE7B2W,MAAM,CAACjP,UAAU,IAEjBiP,MAAM,CAACjP,UAAU,CAACrG,MAAM,GAAG,CAAC,EAC5B;UACA,IAAI,CAACgD,KAAK,CAACC,kBAAM,CAACuS,oBAAoB,EAAE;YAAE3S,EAAE,EAAEyS;UAAO,CAAC,CAAC;QACzD;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACtT,KAAK,CAACgE,MAAM,GAAGsL,SAAS;IAE7B,IAAI,CAACrN,IAAI,CAAC,CAAC;IAEX,IAAIoC,UAAU,CAACrG,MAAM,EAAE;MACrB,MAAM,IAAI,CAACgD,KAAK,CAACC,kBAAM,CAACwS,iBAAiB,EAAE;QAAE5S,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;MAAS,CAAC,CAAC;IACzE;IAEA,IAAI,CAACqK,UAAU,CAACoE,IAAI,CAAC,CAAC;IAEtB,OAAO,IAAI,CAACpN,UAAU,CAACkT,SAAS,EAAE,WAAW,CAAC;EAChD;EAIAM,4BAA4BA,CAE1BN,SAA8B,EAC9BE,MAA6B,EACpB;IACT,MAAML,GAAG,GAAG,IAAI,CAAClK,eAAe,CAAC,IAAI,CAAC;IAEtC,IAAI,IAAI,CAAC8J,aAAa,CAAC,CAAC,EAAE;MACxB,MAAME,MAAqB,GAAGO,MAAa;MAG3CP,MAAM,CAACpW,IAAI,GAAG,QAAQ;MACtBoW,MAAM,CAACzJ,QAAQ,GAAG,KAAK;MACvByJ,MAAM,CAACE,GAAG,GAAGA,GAAG;MAChBF,MAAM,CAACC,MAAM,GAAG,KAAK;MACrB,IAAI,CAACW,eAAe,CAClBP,SAAS,EACTL,MAAM,EACN,KAAK,EACL,KAAK,EACe,KAAK,EACzB,KACF,CAAC;MACD,OAAO,IAAI;IACb,CAAC,MAAM,IAAI,IAAI,CAACH,eAAe,CAAC,CAAC,EAAE;MACjC,MAAMgB,IAAqB,GAAGN,MAAa;MAG3CM,IAAI,CAACtK,QAAQ,GAAG,KAAK;MACrBsK,IAAI,CAACX,GAAG,GAAGA,GAAG;MACdW,IAAI,CAACZ,MAAM,GAAG,KAAK;MACnBI,SAAS,CAAC7I,IAAI,CAAClC,IAAI,CAAC,IAAI,CAACwL,kBAAkB,CAACD,IAAI,CAAC,CAAC;MAClD,OAAO,IAAI;IACb;IACA,IAAI,CAACE,iCAAiC,CAACb,GAAG,CAAC;IAC3C,OAAO,KAAK;EACd;EAEAM,gBAAgBA,CAEdH,SAA8B,EAC9BE,MAA6B,EAC7BtT,KAA8B,EACxB;IACN,MAAM+T,QAAQ,GAAG,IAAI,CAAC5R,YAAY,IAAW,CAAC;IAE9C,IAAI4R,QAAQ,EAAE;MACZ,IAAI,IAAI,CAACL,4BAA4B,CAACN,SAAS,EAAEE,MAAM,CAAC,EAAE;QAExD;MACF;MACA,IAAI,IAAI,CAAChM,GAAG,EAAU,CAAC,EAAE;QACvB,IAAI,CAAC0M,qBAAqB,CAACZ,SAAS,EAAEE,MAA8B,CAAC;QACrE;MACF;IACF;IAEA,IAAI,CAACW,4BAA4B,CAACb,SAAS,EAAEE,MAAM,EAAEtT,KAAK,EAAE+T,QAAQ,CAAC;EACvE;EAEAE,4BAA4BA,CAE1Bb,SAA8B,EAC9BE,MAA6B,EAC7BtT,KAA8B,EAC9B+T,QAAiB,EACjB;IACA,MAAMG,YAAY,GAAGZ,MAAuB;IAC5C,MAAMa,aAAa,GAAGb,MAA8B;IACpD,MAAMc,UAAU,GAAGd,MAAyB;IAC5C,MAAMe,WAAW,GAAGf,MAAgC;IACpD,MAAMgB,YAAY,GAAGhB,MAAiC;IAEtD,MAAMP,MAAkD,GAAGmB,YAAY;IACvE,MAAMK,YAAqD,GAAGL,YAAY;IAE1EZ,MAAM,CAACN,MAAM,GAAGe,QAAQ;IACxB,IAAI,CAACS,+BAA+B,CAAClB,MAAM,CAAC;IAE5C,IAAI,IAAI,CAAChM,GAAG,GAAQ,CAAC,EAAE;MAErByL,MAAM,CAACpW,IAAI,GAAG,QAAQ;MACtB,MAAM8X,aAAa,GAAG,IAAI,CAAC3S,KAAK,IAAe,CAAC;MAChD,IAAI,CAAC4S,qBAAqB,CAAC3B,MAAM,CAAC;MAElC,IAAI0B,aAAa,EAAE;QAEjB,IAAI,CAACE,sBAAsB,CAACvB,SAAS,EAAEe,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC;QAClE;MACF;MAEA,IAAI,IAAI,CAACrB,sBAAsB,CAACoB,YAAY,CAAC,EAAE;QAC7C,IAAI,CAAClT,KAAK,CAACC,kBAAM,CAAC2T,sBAAsB,EAAE;UACxC/T,EAAE,EAAEqT,YAAY,CAACjB;QACnB,CAAC,CAAC;MACJ;MAEA,IAAI,CAACU,eAAe,CAClBP,SAAS,EACTc,YAAY,EACZ,IAAI,EACJ,KAAK,EACe,KAAK,EACzB,KACF,CAAC;MAED;IACF;IAEA,MAAM/R,YAAY,GAChB,IAAAmB,wBAAiB,EAAC,IAAI,CAACtD,KAAK,CAAC9B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC8B,KAAK,CAACoD,WAAW;IAC/D,MAAMyR,SAAS,GAAG,IAAI,CAAC/S,KAAK,IAAe,CAAC;IAC5C,MAAMmR,GAAG,GAAG,IAAI,CAACyB,qBAAqB,CAACpB,MAAM,CAAC;IAC9C,MAAMwB,0BAA0B,GAAG,IAAI,CAAC9U,KAAK,CAACnB,QAAQ;IAEtD,IAAI,CAACkW,4BAA4B,CAACR,YAAY,CAAC;IAE/C,IAAI,IAAI,CAAC1B,aAAa,CAAC,CAAC,EAAE;MACxBE,MAAM,CAACpW,IAAI,GAAG,QAAQ;MAEtB,IAAIkY,SAAS,EAAE;QACb,IAAI,CAACF,sBAAsB,CAACvB,SAAS,EAAEe,aAAa,EAAE,KAAK,EAAE,KAAK,CAAC;QACnE;MACF;MAGA,MAAMzC,aAAa,GAAG,IAAI,CAACoB,sBAAsB,CAACoB,YAAY,CAAC;MAC/D,IAAIc,iBAAiB,GAAG,KAAK;MAC7B,IAAItD,aAAa,EAAE;QACjBwC,YAAY,CAACvX,IAAI,GAAG,aAAa;QAGjC,IAAIqD,KAAK,CAACmT,cAAc,IAAI,CAAC,IAAI,CAACxL,SAAS,CAAC,YAAY,CAAC,EAAE;UACzD,IAAI,CAAC3G,KAAK,CAACC,kBAAM,CAACgU,oBAAoB,EAAE;YAAEpU,EAAE,EAAEoS;UAAI,CAAC,CAAC;QACtD;QACA,IAAIvB,aAAa,IAAI,IAAI,CAAC/J,SAAS,CAAC,YAAY,CAAC,IAAI2L,MAAM,CAAC4B,QAAQ,EAAE;UACpE,IAAI,CAAClU,KAAK,CAACC,kBAAM,CAACkU,qBAAqB,EAAE;YAAEtU,EAAE,EAAEoS;UAAI,CAAC,CAAC;QACvD;QACAjT,KAAK,CAACmT,cAAc,GAAG,IAAI;QAC3B6B,iBAAiB,GAAGhV,KAAK,CAACkT,aAAa;MACzC;MAEA,IAAI,CAACS,eAAe,CAClBP,SAAS,EACTc,YAAY,EACZ,KAAK,EACL,KAAK,EACLxC,aAAa,EACbsD,iBACF,CAAC;IACH,CAAC,MAAM,IAAI,IAAI,CAACpC,eAAe,CAAC,CAAC,EAAE;MACjC,IAAIiC,SAAS,EAAE;QACb,IAAI,CAACO,wBAAwB,CAAChC,SAAS,EAAEiB,WAAW,CAAC;MACvD,CAAC,MAAM;QACL,IAAI,CAACgB,iBAAiB,CAACjC,SAAS,EAAEgB,UAAU,CAAC;MAC/C;IACF,CAAC,MAAM,IACLjS,YAAY,IACZ8Q,GAAG,CAAC7I,IAAI,KAAK,OAAO,IACpB,CAAC,IAAI,CAACN,gBAAgB,CAAC,CAAC,EACxB;MAEA,IAAI,CAACgK,iCAAiC,CAACb,GAAG,CAAC;MAC3C,MAAMqC,WAAW,GAAG,IAAI,CAAChO,GAAG,GAAQ,CAAC;MAErC,IAAIiN,YAAY,CAACgB,QAAQ,EAAE;QACzB,IAAI,CAAClQ,UAAU,CAACyP,0BAA0B,CAAC;MAC7C;MAEA/B,MAAM,CAACpW,IAAI,GAAG,QAAQ;MAEtB,MAAMkY,SAAS,GAAG,IAAI,CAAC/S,KAAK,IAAe,CAAC;MAC5C,IAAI,CAAC4S,qBAAqB,CAAC3B,MAAM,CAAC;MAClC,IAAI,CAACgC,4BAA4B,CAACR,YAAY,CAAC;MAE/C,IAAIM,SAAS,EAAE;QAEb,IAAI,CAACF,sBAAsB,CACzBvB,SAAS,EACTe,aAAa,EACbmB,WAAW,EACX,IACF,CAAC;MACH,CAAC,MAAM;QACL,IAAI,IAAI,CAACxC,sBAAsB,CAACoB,YAAY,CAAC,EAAE;UAC7C,IAAI,CAAClT,KAAK,CAACC,kBAAM,CAACuU,kBAAkB,EAAE;YAAE3U,EAAE,EAAEqT,YAAY,CAACjB;UAAI,CAAC,CAAC;QACjE;QAEA,IAAI,CAACU,eAAe,CAClBP,SAAS,EACTc,YAAY,EACZoB,WAAW,EACX,IAAI,EACgB,KAAK,EACzB,KACF,CAAC;MACH;IACF,CAAC,MAAM,IACLnT,YAAY,KACX8Q,GAAG,CAAC7I,IAAI,KAAK,KAAK,IAAI6I,GAAG,CAAC7I,IAAI,KAAK,KAAK,CAAC,IAC1C,EAAE,IAAI,CAACtI,KAAK,GAAQ,CAAC,IAAI,IAAI,CAACgI,gBAAgB,CAAC,CAAC,CAAC,EACjD;MAGA,IAAI,CAACgK,iCAAiC,CAACb,GAAG,CAAC;MAC3CF,MAAM,CAACpW,IAAI,GAAGsW,GAAG,CAAC7I,IAAI;MAEtB,MAAMyK,SAAS,GAAG,IAAI,CAAC/S,KAAK,IAAe,CAAC;MAC5C,IAAI,CAAC4S,qBAAqB,CAACR,YAAY,CAAC;MAExC,IAAIW,SAAS,EAAE;QAEb,IAAI,CAACF,sBAAsB,CAACvB,SAAS,EAAEe,aAAa,EAAE,KAAK,EAAE,KAAK,CAAC;MACrE,CAAC,MAAM;QACL,IAAI,IAAI,CAACrB,sBAAsB,CAACoB,YAAY,CAAC,EAAE;UAC7C,IAAI,CAAClT,KAAK,CAACC,kBAAM,CAACwU,qBAAqB,EAAE;YAAE5U,EAAE,EAAEqT,YAAY,CAACjB;UAAI,CAAC,CAAC;QACpE;QACA,IAAI,CAACU,eAAe,CAClBP,SAAS,EACTc,YAAY,EACZ,KAAK,EACL,KAAK,EACe,KAAK,EACzB,KACF,CAAC;MACH;MAEA,IAAI,CAACwB,uBAAuB,CAACxB,YAAY,CAAC;IAC5C,CAAC,MAAM,IACL/R,YAAY,IACZ8Q,GAAG,CAAC7I,IAAI,KAAK,UAAU,IACvB,CAAC,IAAI,CAACN,gBAAgB,CAAC,CAAC,EACxB;MACA,IAAI,CAACtG,YAAY,CAAC,wBAAwB,CAAC;MAC3C,IAAI,CAACsQ,iCAAiC,CAACb,GAAG,CAAC;MAG3C,MAAM4B,SAAS,GAAG,IAAI,CAAC/S,KAAK,IAAe,CAAC;MAC5C,IAAI,CAAC4S,qBAAqB,CAACN,UAAU,CAAC;MACtC,IAAI,CAACuB,yBAAyB,CAACvC,SAAS,EAAEkB,YAAY,EAAEO,SAAS,CAAC;IACpE,CAAC,MAAM,IAAI,IAAI,CAAC/K,gBAAgB,CAAC,CAAC,EAAE;MAElC,IAAI+K,SAAS,EAAE;QACb,IAAI,CAACO,wBAAwB,CAAChC,SAAS,EAAEiB,WAAW,CAAC;MACvD,CAAC,MAAM;QACL,IAAI,CAACgB,iBAAiB,CAACjC,SAAS,EAAEgB,UAAU,CAAC;MAC/C;IACF,CAAC,MAAM;MACL,IAAI,CAAC/O,UAAU,CAAC,CAAC;IACnB;EACF;EAGAqP,qBAAqBA,CAEnBpB,MAA6B,EACA;IAC7B,MAAM;MAAEpV,IAAI;MAAEG;IAAM,CAAC,GAAG,IAAI,CAAC2B,KAAK;IAClC,IACE,CAAC9B,IAAI,QAAY,IAAIA,IAAI,QAAc,KACvCoV,MAAM,CAACN,MAAM,IACb3U,KAAK,KAAK,WAAW,EACrB;MACA,IAAI,CAAC2C,KAAK,CAACC,kBAAM,CAAC2U,eAAe,EAAE;QAAE/U,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;MAAS,CAAC,CAAC;IACjE;IAEA,IAAIX,IAAI,QAAmB,EAAE;MAC3B,IAAIG,KAAK,KAAK,aAAa,EAAE;QAC3B,IAAI,CAAC2C,KAAK,CAACC,kBAAM,CAAC4U,4BAA4B,EAAE;UAC9ChV,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;QACjB,CAAC,CAAC;MACJ;MACA,MAAMoU,GAAG,GAAG,IAAI,CAAC5J,gBAAgB,CAAC,CAAC;MACnCiK,MAAM,CAACL,GAAG,GAAGA,GAAG;MAChB,OAAOA,GAAG;IACZ;IAEA,OAAO,IAAI,CAAC6C,iBAAiB,CAACxC,MAAM,CAAC;EACvC;EAEAU,qBAAqBA,CAEnBZ,SAA8B,EAC9BE,MAIC,EACD;IAAA,IAAAyC,kBAAA;IAEA,IAAI,CAACtV,KAAK,CAACoK,KAAK,CAACmL,uBAAW,GAAGC,8BAAkB,GAAGC,uBAAW,CAAC;IAEhE,MAAMC,SAAS,GAAG,IAAI,CAACnW,KAAK,CAACkK,MAAM;IACnC,IAAI,CAAClK,KAAK,CAACkK,MAAM,GAAG,EAAE;IAGtB,IAAI,CAACyC,SAAS,CAAC9B,KAAK,CAACuL,0BAAK,CAAC;IAC3B,MAAM7L,IAAc,GAAI+I,MAAM,CAAC/I,IAAI,GAAG,EAAG;IACzC,IAAI,CAAC6E,2BAA2B,CAAC7E,IAAI,EAAE8E,SAAS,EAAE,KAAK,GAAW,CAAC;IACnE,IAAI,CAAC1C,SAAS,CAACW,IAAI,CAAC,CAAC;IACrB,IAAI,CAAC7M,KAAK,CAAC6M,IAAI,CAAC,CAAC;IACjB,IAAI,CAACtN,KAAK,CAACkK,MAAM,GAAGiM,SAAS;IAC7B/C,SAAS,CAAC7I,IAAI,CAAClC,IAAI,CAAC,IAAI,CAACnI,UAAU,CAAgBoT,MAAM,EAAE,aAAa,CAAC,CAAC;IAC1E,KAAAyC,kBAAA,GAAIzC,MAAM,CAACjP,UAAU,aAAjB0R,kBAAA,CAAmB/X,MAAM,EAAE;MAC7B,IAAI,CAACgD,KAAK,CAACC,kBAAM,CAACoV,oBAAoB,EAAE;QAAExV,EAAE,EAAEyS;MAAO,CAAC,CAAC;IACzD;EACF;EAEA+B,iBAAiBA,CAEfjC,SAA8B,EAC9BQ,IAAqB,EACrB;IACA,IACE,CAACA,IAAI,CAACtK,QAAQ,KACbsK,IAAI,CAACX,GAAG,CAAC7I,IAAI,KAAK,aAAa,IAAIwJ,IAAI,CAACX,GAAG,CAAC5U,KAAK,KAAK,aAAa,CAAC,EACrE;MAGA,IAAI,CAAC2C,KAAK,CAACC,kBAAM,CAACqV,qBAAqB,EAAE;QAAEzV,EAAE,EAAE+S,IAAI,CAACX;MAAI,CAAC,CAAC;IAC5D;IAEAG,SAAS,CAAC7I,IAAI,CAAClC,IAAI,CAAC,IAAI,CAACwL,kBAAkB,CAACD,IAAI,CAAC,CAAC;EACpD;EAEAwB,wBAAwBA,CAEtBhC,SAA8B,EAC9BQ,IAAoC,EACpC;IACA,MAAM7R,IAAI,GAAG,IAAI,CAACwU,yBAAyB,CAAC3C,IAAI,CAAC;IACjDR,SAAS,CAAC7I,IAAI,CAAClC,IAAI,CAACtG,IAAI,CAAC;IAEzB,IAAI,CAACmH,UAAU,CAACsN,kBAAkB,CAChC,IAAI,CAACC,gBAAgB,CAAC1U,IAAI,CAACkR,GAAG,CAAC,EAC/ByD,+BAAmB,EACnB3U,IAAI,CAACkR,GAAG,CAAC9U,GAAG,CAACC,KACf,CAAC;EACH;EAEAuX,yBAAyBA,CAEvBvC,SAA8B,EAC9BQ,IAA6B,EAC7BiB,SAAkB,EAClB;IACA,IAAI,CAACA,SAAS,IAAI,CAACjB,IAAI,CAACtK,QAAQ,EAAE;MAEhC,MAAM2J,GAAG,GAAGW,IAAI,CAACX,GAAmB;MAEpC,IAAIA,GAAG,CAAC7I,IAAI,KAAK,aAAa,IAAI6I,GAAG,CAAC5U,KAAK,KAAK,aAAa,EAAE;QAG7D,IAAI,CAAC2C,KAAK,CAACC,kBAAM,CAACqV,qBAAqB,EAAE;UAAEzV,EAAE,EAAEoS;QAAI,CAAC,CAAC;MACvD;IACF;IAEA,MAAMlR,IAAI,GAAG,IAAI,CAAC4U,0BAA0B,CAAC/C,IAAI,CAAC;IAClDR,SAAS,CAAC7I,IAAI,CAAClC,IAAI,CAACtG,IAAI,CAAC;IAEzB,IAAI8S,SAAS,EAAE;MACb,IAAI,CAAC3L,UAAU,CAACsN,kBAAkB,CAChC,IAAI,CAACC,gBAAgB,CAAC1U,IAAI,CAACkR,GAAG,CAAC,EAC/ByD,+BAAmB,EACnB3U,IAAI,CAACkR,GAAG,CAAC9U,GAAG,CAACC,KACf,CAAC;IACH;EACF;EAEAuV,eAAeA,CAEbP,SAA8B,EAC9BL,MAA6B,EAC7BuC,WAAoB,EACpBhJ,OAAgB,EAChBoF,aAAsB,EACtBsD,iBAA0B,EACpB;IACN5B,SAAS,CAAC7I,IAAI,CAAClC,IAAI,CACjB,IAAI,CAACuO,WAAW,CACd7D,MAAM,EACNuC,WAAW,EACXhJ,OAAO,EACPoF,aAAa,EACbsD,iBAAiB,EACjB,aAAa,EACb,IACF,CACF,CAAC;EACH;EAEAL,sBAAsBA,CAEpBvB,SAA8B,EAC9BL,MAAoC,EACpCuC,WAAoB,EACpBhJ,OAAgB,EACV;IACN,MAAMvK,IAAI,GAAG,IAAI,CAAC6U,WAAW,CAC3B7D,MAAM,EACNuC,WAAW,EACXhJ,OAAO,EACa,KAAK,EACzB,KAAK,EACL,oBAAoB,EACpB,IACF,CAAC;IACD8G,SAAS,CAAC7I,IAAI,CAAClC,IAAI,CAACtG,IAAI,CAAC;IAEzB,MAAMpF,IAAI,GACRoF,IAAI,CAACpF,IAAI,KAAK,KAAK,GACfoF,IAAI,CAACiR,MAAM,GACT6D,uCAA2B,GAC3BC,yCAA6B,GAC/B/U,IAAI,CAACpF,IAAI,KAAK,KAAK,GACnBoF,IAAI,CAACiR,MAAM,GACT+D,uCAA2B,GAC3BC,yCAA6B,GAC/BN,+BAAmB;IACzB,IAAI,CAACO,gCAAgC,CAAClV,IAAI,EAAEpF,IAAI,CAAC;EACnD;EAEAsa,gCAAgCA,CAC9BlV,IAEC,EACDpF,IAAY,EACZ;IACA,IAAI,CAACuM,UAAU,CAACsN,kBAAkB,CAChC,IAAI,CAACC,gBAAgB,CAAC1U,IAAI,CAACkR,GAAG,CAAC,EAC/BtW,IAAI,EACJoF,IAAI,CAACkR,GAAG,CAAC9U,GAAG,CAACC,KACf,CAAC;EACH;EAGA2W,4BAA4BA,CAE1BmC,YAAqD,EAC/C,CAAC;EAGTX,yBAAyBA,CAEvBxU,IAAoC,EACZ;IACxB,IAAI,CAACoV,gBAAgB,CAACpV,IAAI,CAAC;IAC3B,IAAI,CAACiI,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI,CAAC9J,UAAU,CAAC6B,IAAI,EAAE,sBAAsB,CAAC;EACtD;EAGA8R,kBAAkBA,CAAe9R,IAAqB,EAAmB;IACvE,IAAI,CAACoV,gBAAgB,CAACpV,IAAI,CAAC;IAC3B,IAAI,CAACiI,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI,CAAC9J,UAAU,CAAC6B,IAAI,EAAE,eAAe,CAAC;EAC/C;EAEA4U,0BAA0BA,CAExB5U,IAA6B,EACJ;IACzB,IAAI,CAACoV,gBAAgB,CAACpV,IAAI,CAAC;IAC3B,IAAI,CAACiI,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI,CAAC9J,UAAU,CAAC6B,IAAI,EAAE,uBAAuB,CAAC;EACvD;EAGAoV,gBAAgBA,CAEdpV,IAEC,EACK;IACN,IAAI,CAACtB,KAAK,CAACoK,KAAK,CAACmL,uBAAW,GAAGE,uBAAW,CAAC;IAC3C,IAAI,CAACvE,eAAe,CAAC9G,KAAK,CAAC,IAAAuM,mCAAkB,EAAC,CAAC,CAAC;IAChD,IAAI,CAACzK,SAAS,CAAC9B,KAAK,CAACuL,0BAAK,CAAC;IAC3BrU,IAAI,CAAC1D,KAAK,GAAG,IAAI,CAACiJ,GAAG,GAAM,CAAC,GAAG,IAAI,CAAC6I,uBAAuB,CAAC,CAAC,GAAG,IAAI;IACpE,IAAI,CAACwB,eAAe,CAACrE,IAAI,CAAC,CAAC;IAC3B,IAAI,CAACX,SAAS,CAACW,IAAI,CAAC,CAAC;IACrB,IAAI,CAAC7M,KAAK,CAAC6M,IAAI,CAAC,CAAC;EACnB;EAEAkF,YAAYA,CACVzQ,IAAqB,EACrBuQ,WAAoB,EACpBC,UAA2B,EAC3B8E,WAAyB,GAAGC,sBAAU,EAChC;IACN,IAAI,IAAAhU,wBAAiB,EAAC,IAAI,CAACtD,KAAK,CAAC9B,IAAI,CAAC,EAAE;MACtC6D,IAAI,CAAC8N,EAAE,GAAG,IAAI,CAAC9G,eAAe,CAAC,CAAC;MAChC,IAAIuJ,WAAW,EAAE;QACf,IAAI,CAACiF,yBAAyB,CAACxV,IAAI,CAAC8N,EAAE,EAAEwH,WAAW,CAAC;MACtD;IACF,CAAC,MAAM;MACL,IAAI9E,UAAU,IAAI,CAACD,WAAW,EAAE;QAC9BvQ,IAAI,CAAC8N,EAAE,GAAG,IAAI;MAChB,CAAC,MAAM;QACL,MAAM,IAAI,CAAC7O,KAAK,CAACC,kBAAM,CAACuW,gBAAgB,EAAE;UAAE3W,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;QAAS,CAAC,CAAC;MACxE;IACF;EACF;EAGA4T,eAAeA,CAAe1Q,IAAqB,EAAQ;IACzDA,IAAI,CAAC4Q,UAAU,GAAG,IAAI,CAACrL,GAAG,GAAY,CAAC,GAAG,IAAI,CAACiC,mBAAmB,CAAC,CAAC,GAAG,IAAI;EAC7E;EAKAzC,WAAWA,CAET/E,IAIC,EACDsC,UAAgC,EACnB;IACb,MAAMoT,sBAAsB,GAAG,IAAI,CAACC,qBAAqB,CACvD3V,IAAI,EACW,IACjB,CAAC;IACD,MAAM4V,UAAU,GAAG,IAAI,CAACC,gCAAgC,CACtD7V,IAAI,EACJ0V,sBACF,CAAC;IACD,MAAMI,iBAAiB,GAAG,CAACF,UAAU,IAAI,IAAI,CAACrQ,GAAG,GAAS,CAAC;IAC3D,MAAMwQ,OAAO,GACXD,iBAAiB,IACjB,IAAI,CAACE,aAAa,CAEhBhW,IACF,CAAC;IACH,MAAMiW,YAAY,GAChBF,OAAO,IACP,IAAI,CAACG,kCAAkC,CAErClW,IACF,CAAC;IACH,MAAMmW,mBAAmB,GACvBL,iBAAiB,KAAK,CAACG,YAAY,IAAI,IAAI,CAAC1Q,GAAG,GAAS,CAAC,CAAC;IAC5D,MAAM6Q,cAAc,GAAGR,UAAU,IAAIG,OAAO;IAE5C,IAAIA,OAAO,IAAI,CAACE,YAAY,EAAE;MAC5B,IAAIL,UAAU,EAAE,IAAI,CAACtS,UAAU,CAAC,CAAC;MACjC,IAAIhB,UAAU,EAAE;QACd,MAAM,IAAI,CAACrD,KAAK,CAACC,kBAAM,CAACmX,0BAA0B,EAAE;UAAEvX,EAAE,EAAEkB;QAAK,CAAC,CAAC;MACnE;MACA,IAAI,CAACsW,eAAe,CAACtW,IAAI,EAAsC,IAAI,CAAC;MAEpE,OAAO,IAAI,CAAC7B,UAAU,CAAC6B,IAAI,EAAE,sBAAsB,CAAC;IACtD;IAEA,MAAMuW,aAAa,GAAG,IAAI,CAACC,+BAA+B,CAExDxW,IACF,CAAC;IAED,IAAI4V,UAAU,IAAIE,iBAAiB,IAAI,CAACC,OAAO,IAAI,CAACQ,aAAa,EAAE;MACjE,IAAI,CAACjT,UAAU,CAAC,IAAI,GAAW,CAAC;IAClC;IAEA,IAAI2S,YAAY,IAAIE,mBAAmB,EAAE;MACvC,IAAI,CAAC7S,UAAU,CAAC,IAAI,IAAU,CAAC;IACjC;IAEA,IAAImT,cAAc;IAClB,IAAIL,cAAc,IAAIG,aAAa,EAAE;MACnCE,cAAc,GAAG,KAAK;MACtB,IAAInU,UAAU,EAAE;QACd,MAAM,IAAI,CAACrD,KAAK,CAACC,kBAAM,CAACmX,0BAA0B,EAAE;UAAEvX,EAAE,EAAEkB;QAAK,CAAC,CAAC;MACnE;MACA,IAAI,CAACsW,eAAe,CAClBtW,IAAI,EACJoW,cACF,CAAC;IACH,CAAC,MAAM;MACLK,cAAc,GAAG,IAAI,CAACC,2BAA2B,CAC/C1W,IACF,CAAC;IACH;IAEA,IAAIoW,cAAc,IAAIG,aAAa,IAAIE,cAAc,EAAE;MAAA,IAAAE,kBAAA;MACrD,MAAMC,KAAK,GAAG5W,IAAwC;MACtD,IAAI,CAAC6W,WAAW,CAACD,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAACA,KAAK,CAACE,MAAM,CAAC;MACpD,IAAI,EAAAH,kBAAA,GAAAC,KAAK,CAACG,WAAW,qBAAjBJ,kBAAA,CAAmBxa,IAAI,MAAK,kBAAkB,EAAE;QAClD,IAAI,CAACqH,mBAAmB,CAAClB,UAAU,EAAEsU,KAAK,CAACG,WAAW,EAAEH,KAAK,CAAC;MAChE,CAAC,MAAM,IAAItU,UAAU,EAAE;QACrB,MAAM,IAAI,CAACrD,KAAK,CAACC,kBAAM,CAACmX,0BAA0B,EAAE;UAAEvX,EAAE,EAAEkB;QAAK,CAAC,CAAC;MACnE;MACA,OAAO,IAAI,CAAC7B,UAAU,CAACyY,KAAK,EAAE,wBAAwB,CAAC;IACzD;IAEA,IAAI,IAAI,CAACrR,GAAG,GAAY,CAAC,EAAE;MACzB,MAAMqR,KAAK,GAAG5W,IAA0C;MAExD,MAAMsO,IAAI,GAAG,IAAI,CAAC0I,4BAA4B,CAAC,CAAC;MAChDJ,KAAK,CAACG,WAAW,GAAGzI,IAAI;MAExB,IAAIA,IAAI,CAACnS,IAAI,KAAK,kBAAkB,EAAE;QACpC,IAAI,CAACqH,mBAAmB,CAAClB,UAAU,EAAEgM,IAAI,EAAwBsI,KAAK,CAAC;MACzE,CAAC,MAAM,IAAItU,UAAU,EAAE;QACrB,MAAM,IAAI,CAACrD,KAAK,CAACC,kBAAM,CAACmX,0BAA0B,EAAE;UAAEvX,EAAE,EAAEkB;QAAK,CAAC,CAAC;MACnE;MAEA,IAAI,CAAC6W,WAAW,CAACD,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;MAEnC,OAAO,IAAI,CAACzY,UAAU,CAACyY,KAAK,EAAE,0BAA0B,CAAC;IAC3D;IAEA,IAAI,CAACtT,UAAU,CAAC,IAAI,GAAW,CAAC;EAClC;EAGA0S,aAAaA,CAAChW,IAAY,EAAW;IACnC,OAAO,IAAI,CAACuF,GAAG,GAAQ,CAAC;EAC1B;EAEAsQ,gCAAgCA,CAC9B7V,IAIC,EACD0V,sBAA2C,EACD;IAC1C,IAAIA,sBAAsB,IAAI,IAAI,CAACuB,wBAAwB,CAAC,CAAC,EAAE;MAE7D,IAAI,CAACxV,YAAY,CAAC,mBAAmB,EAAEiU,sBAAsB,oBAAtBA,sBAAsB,CAAEtZ,GAAG,CAACC,KAAK,CAAC;MACzE,MAAMyR,EAAE,GAAG4H,sBAAsB,IAAI,IAAI,CAAC1O,eAAe,CAAC,IAAI,CAAC;MAC/D,MAAMkQ,SAAS,GAAG,IAAI,CAACzP,eAAe,CAA2BqG,EAAE,CAAC;MACpEoJ,SAAS,CAACC,QAAQ,GAAGrJ,EAAE;MACtB9N,IAAI,CAAsCoX,UAAU,GAAG,CACtD,IAAI,CAACjZ,UAAU,CAAC+Y,SAAS,EAAE,wBAAwB,CAAC,CACrD;MACD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAhB,kCAAkCA,CAAClW,IAAY,EAAW;IACxD,IAAI,IAAI,CAACI,YAAY,GAAO,CAAC,EAAE;MAC7B,IAAI,CAACJ,IAAI,CAACoX,UAAU,EAAEpX,IAAI,CAACoX,UAAU,GAAG,EAAE;MAE1C,MAAMF,SAAS,GAAG,IAAI,CAACjQ,WAAW,CAAC,IAAI,CAAChJ,KAAK,CAAC4K,eAAe,CAAC;MAE9D,IAAI,CAAC3I,IAAI,CAAC,CAAC;MAEXgX,SAAS,CAACC,QAAQ,GAAG,IAAI,CAACE,qBAAqB,CAAC,CAAC;MACjDrX,IAAI,CAACoX,UAAU,CAAC9Q,IAAI,CAClB,IAAI,CAACnI,UAAU,CAAC+Y,SAAS,EAAE,0BAA0B,CACvD,CAAC;MACD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAV,+BAA+BA,CAACxW,IAAY,EAAW;IACrD,IAAI,IAAI,CAACD,KAAK,EAAU,CAAC,EAAE;MACzB,IAAI,CAACC,IAAI,CAACoX,UAAU,EAAEpX,IAAI,CAACoX,UAAU,GAAG,EAAE;MAC1C,MAAME,YAAY,GAAGtX,IAAI,CAACgF,UAAU,KAAK,MAAM;MAC/ChF,IAAI,CAACoX,UAAU,CAAC9Q,IAAI,CAAC,GAAG,IAAI,CAACiR,qBAAqB,CAACD,YAAY,CAAC,CAAC;MAEjEtX,IAAI,CAAC8W,MAAM,GAAG,IAAI;MAClB9W,IAAI,CAAC+W,WAAW,GAAG,IAAI;MACvB,IAAI,IAAI,CAACnR,SAAS,CAAC,kBAAkB,CAAC,EAAE;QACtC5F,IAAI,CAACwX,UAAU,GAAG,EAAE;MACtB;MAEA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAd,2BAA2BA,CAEzB1W,IAAsC,EAC7B;IACT,IAAI,IAAI,CAACyX,4BAA4B,CAAC,CAAC,EAAE;MACvCzX,IAAI,CAACoX,UAAU,GAAG,EAAE;MACpBpX,IAAI,CAAC8W,MAAM,GAAG,IAAI;MAClB,IAAI,IAAI,CAAClR,SAAS,CAAC,kBAAkB,CAAC,EAAE;QACtC5F,IAAI,CAACwX,UAAU,GAAG,EAAE;MACtB;MACAxX,IAAI,CAAC+W,WAAW,GAAG,IAAI,CAACW,sBAAsB,CAAC1X,IAAI,CAAC;MACpD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAkF,eAAeA,CAAA,EAAY;IACzB,IAAI,CAAC,IAAI,CAAC9E,YAAY,GAAU,CAAC,EAAE,OAAO,KAAK;IAC/C,MAAMF,IAAI,GAAG,IAAI,CAACiB,oBAAoB,CAAC,CAAC;IACxC,OAAO,IAAI,CAACQ,oBAAoB,CAACzB,IAAI,EAAE,UAAU,CAAC;EACpD;EAEA8W,4BAA4BA,CAAA,EAA6C;IACvE,MAAM3R,IAAI,GAAG,IAAI,CAACpF,SAAS,CAAC,CAAC;IAE7B,IAAI,IAAI,CAACF,KAAK,GAAa,CAAC,EAAE;MAC5B,IAAI,CAACG,IAAI,CAAC,CAAC;MACX,OAAO,IAAI,CAACuK,aAAa,CACvBpF,IAAI,EACJvK,iBAAiB,CAACE,WAAW,GAAGF,iBAAiB,CAACI,UACpD,CAAC;IACH,CAAC,MAAM,IAAI,IAAI,CAACgK,eAAe,CAAC,CAAC,EAAE;MACjC,IAAI,CAAChF,IAAI,CAAC,CAAC;MACX,IAAI,CAACA,IAAI,CAAC,CAAC;MACX,OAAO,IAAI,CAACuK,aAAa,CACvBpF,IAAI,EACJvK,iBAAiB,CAACE,WAAW,GAC3BF,iBAAiB,CAACI,UAAU,GAC5BJ,iBAAiB,CAACK,KACtB,CAAC;IACH;IAEA,IAAI,IAAI,CAAC4E,KAAK,GAAU,CAAC,EAAE;MACzB,OAAO,IAAI,CAACwD,UAAU,CAAC8B,IAAI,EAA+B,IAAI,EAAE,IAAI,CAAC;IACvE;IAEA,IAAI,IAAI,CAACtF,KAAK,GAAM,CAAC,EAAE;MACrB,IACE,IAAI,CAAC6F,SAAS,CAAC,YAAY,CAAC,IAC5B,IAAI,CAACC,eAAe,CAAC,YAAY,EAAE,wBAAwB,CAAC,KAAK,IAAI,EACrE;QACA,IAAI,CAAC5G,KAAK,CAACC,kBAAM,CAACyY,qBAAqB,EAAE;UAAE7Y,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;QAAS,CAAC,CAAC;MACvE;MACA,OAAO,IAAI,CAACyG,UAAU,CACpB,IAAI,CAACC,mBAAmB,CACtB,IAAI,CAACjB,eAAe,CAAC,KAAK,CAAC,EAC3B,IAAI,CAACtC,SAAS,CAAqB,CACrC,CAAC,EACD,IAAI,EACJ,IACF,CAAC;IACH;IAEA,IAAI,IAAI,CAACF,KAAK,GAAU,CAAC,IAAI,IAAI,CAACA,KAAK,GAAQ,CAAC,IAAI,IAAI,CAACI,KAAK,CAAC,CAAC,EAAE;MAChE,MAAM,IAAI,CAAClB,KAAK,CAACC,kBAAM,CAAC0Y,wBAAwB,EAAE;QAChD9Y,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;MACjB,CAAC,CAAC;IACJ;IAEA,MAAM+a,GAAG,GAAG,IAAI,CAACzJ,uBAAuB,CAAC,CAAC;IAC1C,IAAI,CAACnG,SAAS,CAAC,CAAC;IAChB,OAAO4P,GAAG;EACZ;EAGAH,sBAAsBA,CAGpB1X,IAAsC,EACJ;IAClC,IAAI,IAAI,CAACD,KAAK,GAAU,CAAC,EAAE;MACzB,MAAMC,IAAI,GAAG,IAAI,CAACuD,UAAU,CAC1B,IAAI,CAACtD,SAAS,CAAqB,CAAC,EACpC,IAAI,EACJ,KACF,CAAC;MACD,OAAOD,IAAI;IACb;IACA,OAAO,IAAI,CAAC+B,sBAAsB,CAAC,CAAC;EACtC;EAEAkV,wBAAwBA,CAAA,EAAY;IAClC,MAAM;MAAE9a;IAAK,CAAC,GAAG,IAAI,CAAC8B,KAAK;IAC3B,IAAI,IAAAsD,wBAAiB,EAACpF,IAAI,CAAC,EAAE;MAC3B,IAAKA,IAAI,OAAc,IAAI,CAAC,IAAI,CAAC8B,KAAK,CAACoD,WAAW,IAAKlF,IAAI,OAAY,EAAE;QACvE,OAAO,KAAK;MACd;MACA,IACE,CAACA,IAAI,QAAa,IAAIA,IAAI,QAAkB,KAC5C,CAAC,IAAI,CAAC8B,KAAK,CAACoD,WAAW,EACvB;QACA,MAAM;UAAElF,IAAI,EAAE2b;QAAS,CAAC,GAAG,IAAI,CAACxW,SAAS,CAAC,CAAC;QAK3C,IACG,IAAAC,wBAAiB,EAACuW,QAAQ,CAAC,IAAIA,QAAQ,OAAa,IACrDA,QAAQ,MAAc,EACtB;UACA,IAAI,CAACpR,eAAe,CAAC,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;UAC5C,OAAO,KAAK;QACd;MACF;IACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC3G,KAAK,GAAY,CAAC,EAAE;MACnC,OAAO,KAAK;IACd;IAEA,MAAMG,IAAI,GAAG,IAAI,CAACc,cAAc,CAAC,CAAC;IAClC,MAAM+W,OAAO,GAAG,IAAI,CAACpW,oBAAoB,CAACzB,IAAI,EAAE,MAAM,CAAC;IACvD,IACE,IAAI,CAACnE,KAAK,CAACqB,UAAU,CAAC8C,IAAI,CAAC,OAAoB,IAC9C,IAAAqB,wBAAiB,EAAC,IAAI,CAACtD,KAAK,CAAC9B,IAAI,CAAC,IAAI4b,OAAQ,EAC/C;MACA,OAAO,IAAI;IACb;IAEA,IAAI,IAAI,CAAChY,KAAK,GAAY,CAAC,IAAIgY,OAAO,EAAE;MACtC,MAAMC,aAAa,GAAG,IAAI,CAACjc,KAAK,CAACqB,UAAU,CACzC,IAAI,CAAC6a,mBAAmB,CAAC/X,IAAI,GAAG,CAAC,CACnC,CAAC;MACD,OACE8X,aAAa,OAA4B,IACzCA,aAAa,OAAyB;IAE1C;IACA,OAAO,KAAK;EACd;EAEA1B,eAAeA,CAEbtW,IAAsC,EACtC2G,MAAgB,EACV;IACN,IAAI,IAAI,CAACiC,aAAa,GAAS,CAAC,EAAE;MAChC5I,IAAI,CAAC8W,MAAM,GAAG,IAAI,CAACoB,iBAAiB,CAAC,CAAC;MACtC,IAAI,CAACrB,WAAW,CAAC7W,IAAI,CAAC;MACtB,IAAI,CAACmY,0BAA0B,CAACnY,IAAI,CAAC;MACrC,IAAI,CAACoY,qBAAqB,CAACpY,IAAI,CAAC;IAClC,CAAC,MAAM,IAAI2G,MAAM,EAAE;MACjB,IAAI,CAACrD,UAAU,CAAC,CAAC;IACnB;IAEA,IAAI,CAAC2E,SAAS,CAAC,CAAC;EAClB;EAEAwP,4BAA4BA,CAAA,EAAY;IACtC,MAAM;MAAEtb;IAAK,CAAC,GAAG,IAAI,CAAC8B,KAAK;IAC3B,IAAI9B,IAAI,OAAU,EAAE;MAClB,IAAI,CAACuK,eAAe,CAAC,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC;MACzD,IAAI,IAAI,CAACd,SAAS,CAAC,YAAY,CAAC,EAAE;QAChC,IACE,IAAI,CAACC,eAAe,CAAC,YAAY,EAAE,wBAAwB,CAAC,KAAK,IAAI,EACrE;UACA,IAAI,CAAC5G,KAAK,CAACC,kBAAM,CAACyY,qBAAqB,EAAE;YACvC7Y,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;UACjB,CAAC,CAAC;QACJ;QAEA,OAAO,IAAI;MACb;IACF;IAEA,OACEX,IAAI,OAAY,IAChBA,IAAI,OAAc,IAClBA,IAAI,OAAiB,IACrBA,IAAI,OAAc,IAClB,IAAI,CAACgE,KAAK,CAAC,CAAC,IACZ,IAAI,CAAC+E,eAAe,CAAC,CAAC;EAE1B;EAEA2R,WAAWA,CACT7W,IAAmE,EACnEqY,UAAoB,EACpBC,SAAmB,EACnBC,MAAgB,EACV;IACN,IAAIF,UAAU,EAAE;MAEd,IAAIC,SAAS,EAAE;QAEb,IAAI,CAACE,qBAAqB,CAACxY,IAAI,EAAE,SAAS,CAAC;QAC3C,IAAI,IAAI,CAAC4F,SAAS,CAAC,mBAAmB,CAAC,EAAE;UAAA,IAAA6S,kBAAA;UACvC,MAAM1B,WAAW,GAAI/W,IAAI,CACtB+W,WAAW;UACd,IACEA,WAAW,CAAC5a,IAAI,KAAK,YAAY,IACjC4a,WAAW,CAAC1O,IAAI,KAAK,MAAM,IAC3B0O,WAAW,CAACxa,GAAG,GAAGwa,WAAW,CAAC1a,KAAK,KAAK,CAAC,IACzC,GAAAoc,kBAAA,GAAC1B,WAAW,CAAC7J,KAAK,aAAjBuL,kBAAA,CAAmBtL,aAAa,GACjC;YACA,IAAI,CAAClO,KAAK,CAACC,kBAAM,CAACwZ,6BAA6B,EAAE;cAC/C5Z,EAAE,EAAEiY;YACN,CAAC,CAAC;UACJ;QACF;MAEF,CAAC,MAAM,IAAI/W,IAAI,CAACoX,UAAU,IAAIpX,IAAI,CAACoX,UAAU,CAACnb,MAAM,EAAE;QAGpD,KAAK,MAAMib,SAAS,IAAIlX,IAAI,CAACoX,UAAU,EAAE;UACvC,MAAM;YAAED;UAAS,CAAC,GAAGD,SAAS;UAC9B,MAAMyB,UAAU,GACdxB,QAAQ,CAAChb,IAAI,KAAK,YAAY,GAAGgb,QAAQ,CAAC9O,IAAI,GAAG8O,QAAQ,CAAC7a,KAAK;UACjE,IAAI,CAACkc,qBAAqB,CAACtB,SAAS,EAAEyB,UAAU,CAAC;UACjD,IAAI,CAACJ,MAAM,IAAIrB,SAAS,CAAC0B,KAAK,EAAE;YAC9B,MAAM;cAAEA;YAAM,CAAC,GAAG1B,SAAS;YAC3B,IAAI0B,KAAK,CAACzc,IAAI,KAAK,YAAY,EAAE;cAC/B,IAAI,CAAC8C,KAAK,CAACC,kBAAM,CAAC2Z,qBAAqB,EAAE;gBACvC/Z,EAAE,EAAEoY,SAAS;gBACbrY,SAAS,EAAE+Z,KAAK,CAACtc,KAAK;gBACtBqc;cACF,CAAC,CAAC;YACJ,CAAC,MAAM;cAEL,IAAI,CAACG,iBAAiB,CAACF,KAAK,CAACvQ,IAAI,EAAEuQ,KAAK,CAACxc,GAAG,CAACC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;cAEhE,IAAI,CAACqC,KAAK,CAACqa,gBAAgB,CAACH,KAAK,CAAC;YACpC;UACF;QACF;MACF,CAAC,MAAM,IAAI5Y,IAAI,CAAC+W,WAAW,EAAE;QAE3B,IACE/W,IAAI,CAAC+W,WAAW,CAAC5a,IAAI,KAAK,qBAAqB,IAC/C6D,IAAI,CAAC+W,WAAW,CAAC5a,IAAI,KAAK,kBAAkB,EAC5C;UACA,MAAM2R,EAAE,GAAG9N,IAAI,CAAC+W,WAAW,CAACjJ,EAAE;UAC9B,IAAI,CAACA,EAAE,EAAE,MAAM,IAAIkL,KAAK,CAAC,mBAAmB,CAAC;UAE7C,IAAI,CAACR,qBAAqB,CAACxY,IAAI,EAAE8N,EAAE,CAACzF,IAAI,CAAC;QAC3C,CAAC,MAAM,IAAIrI,IAAI,CAAC+W,WAAW,CAAC5a,IAAI,KAAK,qBAAqB,EAAE;UAC1D,KAAK,MAAM4a,WAAW,IAAI/W,IAAI,CAAC+W,WAAW,CAACrN,YAAY,EAAE;YACvD,IAAI,CAACuP,gBAAgB,CAAClC,WAAW,CAACjJ,EAAE,CAAC;UACvC;QACF;MACF;IACF;EACF;EAEAmL,gBAAgBA,CAACjZ,IAAkC,EAAQ;IACzD,IAAIA,IAAI,CAAC7D,IAAI,KAAK,YAAY,EAAE;MAC9B,IAAI,CAACqc,qBAAqB,CAACxY,IAAI,EAAEA,IAAI,CAACqI,IAAI,CAAC;IAC7C,CAAC,MAAM,IAAIrI,IAAI,CAAC7D,IAAI,KAAK,eAAe,EAAE;MACxC,KAAK,MAAM0V,IAAI,IAAI7R,IAAI,CAACkZ,UAAU,EAAE;QAClC,IAAI,CAACD,gBAAgB,CAACpH,IAAI,CAAC;MAC7B;IACF,CAAC,MAAM,IAAI7R,IAAI,CAAC7D,IAAI,KAAK,cAAc,EAAE;MACvC,KAAK,MAAMgd,IAAI,IAAInZ,IAAI,CAACoZ,QAAQ,EAAE;QAChC,IAAID,IAAI,EAAE;UACR,IAAI,CAACF,gBAAgB,CAACE,IAAI,CAAC;QAC7B;MACF;IACF,CAAC,MAAM,IAAInZ,IAAI,CAAC7D,IAAI,KAAK,gBAAgB,EAAE;MAEzC,IAAI,CAAC8c,gBAAgB,CAACjZ,IAAI,CAAC1D,KAAK,CAAC;IACnC,CAAC,MAAM,IAAI0D,IAAI,CAAC7D,IAAI,KAAK,aAAa,EAAE;MACtC,IAAI,CAAC8c,gBAAgB,CAACjZ,IAAI,CAACgL,QAAQ,CAAC;IACtC,CAAC,MAAM,IAAIhL,IAAI,CAAC7D,IAAI,KAAK,mBAAmB,EAAE;MAC5C,IAAI,CAAC8c,gBAAgB,CAACjZ,IAAI,CAACkO,IAAI,CAAC;IAClC;EACF;EAEAsK,qBAAqBA,CACnBxY,IAMC,EACD2Y,UAAkB,EACZ;IACN,IAAI,IAAI,CAACU,mBAAmB,CAACC,GAAG,CAACX,UAAU,CAAC,EAAE;MAC5C,IAAIA,UAAU,KAAK,SAAS,EAAE;QAC5B,IAAI,CAAC1Z,KAAK,CAACC,kBAAM,CAACqa,sBAAsB,EAAE;UAAEza,EAAE,EAAEkB;QAAK,CAAC,CAAC;MACzD,CAAC,MAAM;QACL,IAAI,CAACf,KAAK,CAACC,kBAAM,CAACsa,eAAe,EAAE;UAAE1a,EAAE,EAAEkB,IAAI;UAAE2Y;QAAW,CAAC,CAAC;MAC9D;IACF;IACA,IAAI,CAACU,mBAAmB,CAACI,GAAG,CAACd,UAAU,CAAC;EAC1C;EAIApB,qBAAqBA,CAACmC,cAAuB,EAA4B;IACvE,MAAMC,KAAK,GAAG,EAAE;IAChB,IAAIC,KAAK,GAAG,IAAI;IAGhB,IAAI,CAACjT,MAAM,EAAU,CAAC;IAEtB,OAAO,CAAC,IAAI,CAACpB,GAAG,EAAU,CAAC,EAAE;MAC3B,IAAIqU,KAAK,EAAE;QACTA,KAAK,GAAG,KAAK;MACf,CAAC,MAAM;QACL,IAAI,CAACjT,MAAM,GAAS,CAAC;QACrB,IAAI,IAAI,CAACpB,GAAG,EAAU,CAAC,EAAE;MAC3B;MACA,MAAMsU,eAAe,GAAG,IAAI,CAACzZ,YAAY,IAAS,CAAC;MACnD,MAAM0Z,QAAQ,GAAG,IAAI,CAAC/Z,KAAK,IAAU,CAAC;MACtC,MAAMC,IAAI,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;MAC7BD,IAAI,CAAC4Y,KAAK,GAAG,IAAI,CAACvB,qBAAqB,CAAC,CAAC;MACzCsC,KAAK,CAACrT,IAAI,CACR,IAAI,CAACyT,oBAAoB,CACvB/Z,IAAI,EACJ8Z,QAAQ,EACRJ,cAAc,EACdG,eACF,CACF,CAAC;IACH;IAEA,OAAOF,KAAK;EACd;EAEAI,oBAAoBA,CAClB/Z,IAAS,EACT8Z,QAAiB,EAEjBJ,cAAuB,EACvBG,eAAwB,EAEL;IACnB,IAAI,IAAI,CAACjR,aAAa,GAAO,CAAC,EAAE;MAC9B5I,IAAI,CAACmX,QAAQ,GAAG,IAAI,CAACE,qBAAqB,CAAC,CAAC;IAC9C,CAAC,MAAM,IAAIyC,QAAQ,EAAE;MACnB9Z,IAAI,CAACmX,QAAQ,GAAG,IAAA6C,wBAAkB,EAACha,IAAI,CAAC4Y,KAAK,CAAC;IAChD,CAAC,MAAM,IAAI,CAAC5Y,IAAI,CAACmX,QAAQ,EAAE;MACzBnX,IAAI,CAACmX,QAAQ,GAAG,IAAA8C,qBAAe,EAACja,IAAI,CAAC4Y,KAAK,CAAC;IAC7C;IACA,OAAO,IAAI,CAACza,UAAU,CAAoB6B,IAAI,EAAE,iBAAiB,CAAC;EACpE;EAGAqX,qBAAqBA,CAAA,EAAmC;IACtD,IAAI,IAAI,CAACtX,KAAK,IAAU,CAAC,EAAE;MACzB,MAAM4E,MAAM,GAAG,IAAI,CAACuV,kBAAkB,CAAC,IAAI,CAACjc,KAAK,CAAC3B,KAAK,CAAC;MACxD,MAAM6d,SAAS,GAAGxV,MAAM,CAACrI,KAAK,CAACyD,KAAK,CAACpE,aAAa,CAAC;MACnD,IAAIwe,SAAS,EAAE;QACb,IAAI,CAAClb,KAAK,CAACC,kBAAM,CAACkb,gCAAgC,EAAE;UAClDtb,EAAE,EAAE6F,MAAM;UACV0V,iBAAiB,EAAEF,SAAS,CAAC,CAAC,CAAC,CAAC/c,UAAU,CAAC,CAAC;QAC9C,CAAC,CAAC;MACJ;MACA,OAAOuH,MAAM;IACf;IACA,OAAO,IAAI,CAACqC,eAAe,CAAC,IAAI,CAAC;EACnC;EAEAsT,kBAAkBA,CAChBta,IAEC,EACQ;IACT,IAAIA,IAAI,CAACwX,UAAU,IAAI,IAAI,EAAE;MAC3B,OAAOxX,IAAI,CAACwX,UAAU,CAAC+C,IAAI,CAAC,CAAC;QAAErJ,GAAG;QAAE5U;MAAM,CAAC,KAAK;QAC9C,OACEA,KAAK,CAACA,KAAK,KAAK,MAAM,KACrB4U,GAAG,CAAC/U,IAAI,KAAK,YAAY,GACtB+U,GAAG,CAAC7I,IAAI,KAAK,MAAM,GACnB6I,GAAG,CAAC5U,KAAK,KAAK,MAAM,CAAC;MAE7B,CAAC,CAAC;IACJ;IACA,OAAO,KAAK;EACd;EAEAke,qBAAqBA,CAACxa,IAAiC,EAAE;IACvD,IAAIA,IAAI,CAACya,MAAM,EAAE;MAAA,IAAAC,gBAAA;MACf,IACE1a,IAAI,CAACoX,UAAU,CAACnb,MAAM,KAAK,CAAC,IAC5B+D,IAAI,CAACoX,UAAU,CAAC,CAAC,CAAC,CAACjb,IAAI,KAAK,wBAAwB,EACpD;QACA,IAAI,CAAC8C,KAAK,CAACC,kBAAM,CAACyb,0BAA0B,EAAE;UAC5C7b,EAAE,EAAEkB,IAAI,CAACoX,UAAU,CAAC,CAAC,CAAC,CAAChb,GAAG,CAACC;QAC7B,CAAC,CAAC;MACJ;MACA,IAAI,EAAAqe,gBAAA,GAAA1a,IAAI,CAACwX,UAAU,qBAAfkD,gBAAA,CAAiBze,MAAM,IAAG,CAAC,EAAE;QAC/B,IAAI,CAACgD,KAAK,CAACC,kBAAM,CAAC0b,4BAA4B,EAAE;UAC9C9b,EAAE,EAAEkB,IAAI,CAACoX,UAAU,CAAC,CAAC,CAAC,CAAChb,GAAG,CAACC;QAC7B,CAAC,CAAC;MACJ;IACF;EACF;EAEA+b,qBAAqBA,CACnBpY,IAEC,EACD;IAEA,IAAI,IAAI,CAACsa,kBAAkB,CAACta,IAAI,CAAC,IAAIA,IAAI,CAAC7D,IAAI,KAAK,sBAAsB,EAAE;MAEzE,MAAM;QAAEib;MAAW,CAAC,GAAGpX,IAAI;MAC3B,IAAIoX,UAAU,IAAI,IAAI,EAAE;QAEtB,MAAMyD,wBAAwB,GAAGzD,UAAU,CAAC0D,IAAI,CAAC5D,SAAS,IAAI;UAC5D,IAAI6D,QAAQ;UACZ,IAAI7D,SAAS,CAAC/a,IAAI,KAAK,iBAAiB,EAAE;YACxC4e,QAAQ,GAAG7D,SAAS,CAAC0B,KAAK;UAC5B,CAAC,MAAM,IAAI1B,SAAS,CAAC/a,IAAI,KAAK,iBAAiB,EAAE;YAC/C4e,QAAQ,GAAG7D,SAAS,CAAC6D,QAAQ;UAC/B;UACA,IAAIA,QAAQ,KAAKzN,SAAS,EAAE;YAC1B,OAAOyN,QAAQ,CAAC5e,IAAI,KAAK,YAAY,GACjC4e,QAAQ,CAAC1S,IAAI,KAAK,SAAS,GAC3B0S,QAAQ,CAACze,KAAK,KAAK,SAAS;UAClC;QACF,CAAC,CAAC;QACF,IAAIue,wBAAwB,KAAKvN,SAAS,EAAE;UAC1C,IAAI,CAACrO,KAAK,CAACC,kBAAM,CAAC8b,2BAA2B,EAAE;YAC7Clc,EAAE,EAAE+b,wBAAwB,CAACze,GAAG,CAACC;UACnC,CAAC,CAAC;QACJ;MACF;IACF;EACF;EAEA4e,sBAAsBA,CAACC,QAAiB,EAAW;IACjD,OAAO,CAACA,QAAQ,IAAI,IAAI,CAAC9a,YAAY,IAAW,CAAC;EACnD;EAEA+a,gBAAgBA,CACdnb,IAA4D,EAC5Dkb,QAAiB,EACjBE,KAAoB,EACpBhf,GAAc,EACR;IACN,IAAI8e,QAAQ,EAAE;MAAA;MAQZ;IACF;IACA,IAAIE,KAAK,KAAK,QAAQ,EAAE;MACtB,IAAI,CAAC3Z,YAAY,CAAC,kBAAkB,EAAErF,GAAG,CAAC;MACzC4D,IAAI,CAAyBya,MAAM,GAAG,IAAI;IAC7C,CAAC,MAAM,IAAI,IAAI,CAAC7U,SAAS,CAAC,kBAAkB,CAAC,EAAE;MAC5C5F,IAAI,CAAyBya,MAAM,GAAG,KAAK;IAC9C;EACF;EAgBA9E,qBAAqBA,CACnB3V,IAA+D,EAC/Dkb,QAAiB,EACI;IACrB,IAAI,CAAC,IAAI,CAACD,sBAAsB,CAACC,QAAQ,CAAC,EAAE;MAC1C,IAAI,CAACC,gBAAgB,CACnBnb,IAAI,EACJkb,QAAQ,EACR,IACF,CAAC;MACD,OAAO,IAAI;IACb;IAEA,MAAMG,eAAe,GAAG,IAAI,CAACrU,eAAe,CAAC,IAAI,CAAC;IAElD,MAAM;MAAE7K;IAAK,CAAC,GAAG,IAAI,CAAC8B,KAAK;IAC3B,MAAMqd,aAAa,GAAG,IAAAC,iCAA0B,EAACpf,IAAI,CAAC,GAOlDA,IAAI,OAAa,IAAI,IAAI,CAAC8G,iBAAiB,CAAC,CAAC,QAAyB,GAQtE9G,IAAI,OAAa;IAErB,IAAImf,aAAa,EAAE;MACjB,IAAI,CAACE,sCAAsC,CAACH,eAAe,CAAC;MAC5D,IAAI,CAACF,gBAAgB,CACnBnb,IAAI,EACJkb,QAAQ,EACRG,eAAe,CAAChT,IAAI,EACpBgT,eAAe,CAACjf,GAAG,CAACC,KACtB,CAAC;MACD,OAAO,IAAI;IACb,CAAC,MAAM;MACL,IAAI,CAAC8e,gBAAgB,CACnBnb,IAAI,EACJkb,QAAQ,EACR,IACF,CAAC;MAED,OAAOG,eAAe;IACxB;EACF;EAEAI,wBAAwBA,CAEtBL,KAAa,EACb;IACA,MAAM;MAAEjf;IAAK,CAAC,GAAG,IAAI,CAAC8B,KAAK;IAC3B,OAAO,IAAAsD,wBAAiB,EAACpF,IAAI,CAAC,GAO1BA,IAAI,OAAa,IAAI,IAAI,CAAC8G,iBAAiB,CAAC,CAAC,QAAyB,GAQtE9G,IAAI,OAAa;EACvB;EAKAyI,WAAWA,CAAe5E,IAAiC,EAAe;IACxE,IAAI,IAAI,CAACD,KAAK,IAAU,CAAC,EAAE;MAEzB,OAAO,IAAI,CAAC2b,8BAA8B,CAAC1b,IAAI,CAAC;IAClD;IAEA,OAAO,IAAI,CAAC2b,6BAA6B,CACvC3b,IAAI,EACJ,IAAI,CAAC2V,qBAAqB,CAAC3V,IAAI,EAAiB,KAAK,CACvD,CAAC;EACH;EAEA2b,6BAA6BA,CAE3B3b,IAAiC,EACjC0V,sBAA2C,EAC9B;IACb1V,IAAI,CAACoX,UAAU,GAAG,EAAE;IAIpB,MAAMxB,UAAU,GAAG,IAAI,CAACgG,gCAAgC,CACtD5b,IAAI,EACJ0V,sBACF,CAAC;IAOD,MAAMmG,SAAS,GAAG,CAACjG,UAAU,IAAI,IAAI,CAACrQ,GAAG,GAAS,CAAC;IAGnD,MAAMwQ,OAAO,GAAG8F,SAAS,IAAI,IAAI,CAACC,6BAA6B,CAAC9b,IAAI,CAAC;IAGrE,IAAI6b,SAAS,IAAI,CAAC9F,OAAO,EAAE,IAAI,CAACgG,0BAA0B,CAAC/b,IAAI,CAAC;IAChE,IAAI,CAACgc,gBAAgB,GAAS,CAAC;IAE/B,OAAO,IAAI,CAACN,8BAA8B,CAAC1b,IAAI,CAAC;EAClD;EAEA0b,8BAA8BA,CAE5B1b,IAAiC,EACpB;IAAA,IAAAic,gBAAA;IACb,CAAAA,gBAAA,GAAAjc,IAAI,CAACoX,UAAU,YAAA6E,gBAAA,GAAfjc,IAAI,CAACoX,UAAU,GAAK,EAAE;IACtBpX,IAAI,CAAC8W,MAAM,GAAG,IAAI,CAACoB,iBAAiB,CAAC,CAAC;IACtC,IAAI,CAACC,0BAA0B,CAACnY,IAAI,CAAC;IACrC,IAAI,CAACwa,qBAAqB,CAACxa,IAAI,CAAC;IAChC,IAAI,CAACoY,qBAAqB,CAACpY,IAAI,CAAC;IAEhC,IAAI,CAACiI,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI,CAAC9J,UAAU,CAAC6B,IAAI,EAAE,mBAAmB,CAAC;EACnD;EAEAkY,iBAAiBA,CAAA,EAAgC;IAC/C,IAAI,CAAC,IAAI,CAACnY,KAAK,IAAU,CAAC,EAAE,IAAI,CAACuD,UAAU,CAAC,CAAC;IAC7C,OAAO,IAAI,CAAC4Y,aAAa,CAAC,CAAC;EAC7B;EAEAC,yBAAyBA,CAMvBnc,IAAiC,EACjCkX,SAAoB,EACpB/a,IAAe,EACT;IACN+a,SAAS,CAAC0B,KAAK,GAAG,IAAI,CAAC5R,eAAe,CAAC,CAAC;IACxChH,IAAI,CAACoX,UAAU,CAAC9Q,IAAI,CAAC,IAAI,CAAC8V,qBAAqB,CAAClF,SAAS,EAAE/a,IAAI,CAAC,CAAC;EACnE;EAEAigB,qBAAqBA,CAKnBlF,SAAoB,EAAE/a,IAAe,EAAEmZ,WAAW,GAAG3G,wBAAY,EAAE;IACnE,IAAI,CAACvE,SAAS,CAAC8M,SAAS,CAAC0B,KAAK,EAAE;MAC9BvO,EAAE,EAAE;QAAElO;MAAK,CAAC;MACZ4P,OAAO,EAAEuJ;IACX,CAAC,CAAC;IACF,OAAO,IAAI,CAACnX,UAAU,CAAC+Y,SAAS,EAAE/a,IAAI,CAAC;EACzC;EAOAkgB,qBAAqBA,CAAA,EAAwB;IAC3C,IAAI,CAAC1V,MAAM,EAAU,CAAC;IAEtB,MAAM2V,KAAK,GAAG,EAAE;IAChB,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAE3B,GAAG;MACD,IAAI,IAAI,CAACzc,KAAK,EAAU,CAAC,EAAE;QACzB;MACF;MAEA,MAAMC,IAAI,GAAG,IAAI,CAACC,SAAS,CAAoB,CAAC;MAGhD,MAAMwc,OAAO,GAAG,IAAI,CAACxe,KAAK,CAAC3B,KAAK;MAIhC,IAAIigB,SAAS,CAACjD,GAAG,CAACmD,OAAO,CAAC,EAAE;QAC1B,IAAI,CAACxd,KAAK,CAACC,kBAAM,CAACwd,iCAAiC,EAAE;UACnD5d,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB,QAAQ;UACvBoU,GAAG,EAAEuL;QACP,CAAC,CAAC;MACJ;MACAF,SAAS,CAAC9C,GAAG,CAACgD,OAAO,CAAC;MACtB,IAAI,IAAI,CAAC1c,KAAK,IAAU,CAAC,EAAE;QACzBC,IAAI,CAACkR,GAAG,GAAG,IAAI,CAACgJ,kBAAkB,CAACuC,OAAO,CAAC;MAC7C,CAAC,MAAM;QACLzc,IAAI,CAACkR,GAAG,GAAG,IAAI,CAAClK,eAAe,CAAC,IAAI,CAAC;MACvC;MACA,IAAI,CAACL,MAAM,GAAS,CAAC;MAErB,IAAI,CAAC,IAAI,CAAC5G,KAAK,IAAU,CAAC,EAAE;QAC1B,MAAM,IAAI,CAACd,KAAK,CAACC,kBAAM,CAACyd,2BAA2B,EAAE;UACnD7d,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;QACjB,CAAC,CAAC;MACJ;MACAkD,IAAI,CAAC1D,KAAK,GAAG,IAAI,CAAC4d,kBAAkB,CAAC,IAAI,CAACjc,KAAK,CAAC3B,KAAK,CAAC;MACtDggB,KAAK,CAAChW,IAAI,CAAC,IAAI,CAACnI,UAAU,CAAC6B,IAAI,EAAE,iBAAiB,CAAC,CAAC;IACtD,CAAC,QAAQ,IAAI,CAACuF,GAAG,GAAS,CAAC;IAE3B,IAAI,CAACoB,MAAM,EAAU,CAAC;IAEtB,OAAO2V,KAAK;EACd;EAMAM,qBAAqBA,CAAA,EAAG;IACtB,MAAMN,KAA0B,GAAG,EAAE;IACrC,MAAMO,UAAU,GAAG,IAAIL,GAAG,CAAC,CAAC;IAC5B,GAAG;MACD,MAAMxc,IAAI,GAAG,IAAI,CAACC,SAAS,CAAoB,CAAC;MAChDD,IAAI,CAACkR,GAAG,GAAG,IAAI,CAAClK,eAAe,CAAC,IAAI,CAAC;MAErC,IAAIhH,IAAI,CAACkR,GAAG,CAAC7I,IAAI,KAAK,MAAM,EAAE;QAC5B,IAAI,CAACpJ,KAAK,CAACC,kBAAM,CAAC4d,gCAAgC,EAAE;UAClDhe,EAAE,EAAEkB,IAAI,CAACkR;QACX,CAAC,CAAC;MACJ;MAEA,IAAI2L,UAAU,CAACvD,GAAG,CAACtZ,IAAI,CAACkR,GAAG,CAAC7I,IAAI,CAAC,EAAE;QACjC,IAAI,CAACpJ,KAAK,CAACC,kBAAM,CAACwd,iCAAiC,EAAE;UACnD5d,EAAE,EAAEkB,IAAI,CAACkR,GAAG;UACZA,GAAG,EAAElR,IAAI,CAACkR,GAAG,CAAC7I;QAChB,CAAC,CAAC;MACJ;MACAwU,UAAU,CAACpD,GAAG,CAACzZ,IAAI,CAACkR,GAAG,CAAC7I,IAAI,CAAC;MAC7B,IAAI,CAAC1B,MAAM,GAAS,CAAC;MACrB,IAAI,CAAC,IAAI,CAAC5G,KAAK,IAAU,CAAC,EAAE;QAC1B,MAAM,IAAI,CAACd,KAAK,CAACC,kBAAM,CAACyd,2BAA2B,EAAE;UACnD7d,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;QACjB,CAAC,CAAC;MACJ;MACAkD,IAAI,CAAC1D,KAAK,GAAG,IAAI,CAAC4d,kBAAkB,CAAC,IAAI,CAACjc,KAAK,CAAC3B,KAAK,CAAC;MACtDggB,KAAK,CAAChW,IAAI,CAAC,IAAI,CAACnI,UAAU,CAAC6B,IAAI,EAAE,iBAAiB,CAAC,CAAC;IACtD,CAAC,QAAQ,IAAI,CAACuF,GAAG,GAAS,CAAC;IAE3B,OAAO+W,KAAK;EACd;EAEAnE,0BAA0BA,CACxBnY,IAA4D,EAC5D;IACA,IAAI6c,UAA+B;IACnC,IAAIE,OAAO,GAAG,KAAK;IAGnB,IAAI,IAAI,CAAChd,KAAK,GAAS,CAAC,EAAE;MACxB,IACE,IAAI,CAACyL,qBAAqB,CAAC,CAAC,IAC5B,IAAI,CAACvI,iBAAiB,CAAC,CAAC,OAA8B,EACtD;QAGA;MACF;MAEA,IAAI,CAAC/C,IAAI,CAAC,CAAC;MAEwB;QACjC,IAAI,IAAI,CAAC0F,SAAS,CAAC,kBAAkB,CAAC,EAAE;UACtCiX,UAAU,GAAG,IAAI,CAACD,qBAAqB,CAAC,CAAC;QAC3C,CAAC,MAAM;UACL,IAAI,CAACI,4BAA4B,CAAC,CAAC;UACnCH,UAAU,GAAG,IAAI,CAACR,qBAAqB,CAAC,CAAC;QAC3C;MACF;MAIAU,OAAO,GAAG,IAAI;IAChB,CAAC,MAAM,IAAI,IAAI,CAAC3c,YAAY,GAAW,CAAC,IAAI,CAAC,IAAI,CAACoL,qBAAqB,CAAC,CAAC,EAAE;MACzE,IAAI,IAAI,CAAC5F,SAAS,CAAC,kBAAkB,CAAC,EAAE;QACtC,IACE,IAAI,CAACC,eAAe,CAAC,kBAAkB,EAAE,wBAAwB,CAAC,KAClE,IAAI,EACJ;UACA,IAAI,CAAC5G,KAAK,CAACC,kBAAM,CAAC+d,yBAAyB,EAAE;YAC3Cne,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;UACjB,CAAC,CAAC;QACJ;QACA,IAAI,CAACgD,QAAQ,CAACE,IAAI,EAAE,wBAAwB,EAAE,IAAI,CAAC;MACrD,CAAC,MAAM;QACL,IAAI,CAAC0G,eAAe,CAAC,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;MAChE;MACA,IAAI,CAACxG,IAAI,CAAC,CAAC;MACX2c,UAAU,GAAG,IAAI,CAACR,qBAAqB,CAAC,CAAC;IAC3C,CAAC,MAAM,IACL,IAAI,CAACzW,SAAS,CAAC,kBAAkB,CAAC,IAClC,IAAI,CAACA,SAAS,CAAC,kBAAkB,CAAC,EAClC;MACAiX,UAAU,GAAG,EAAE;IACjB,CAAC,MAAyC;MACxC,IAAI,IAAI,CAACjX,SAAS,CAAC,kBAAkB,CAAC,EAAE;QACtCiX,UAAU,GAAG,EAAE;MACjB,CAAC,MAAM;IACT;IAEA,IAAI,CAACE,OAAO,IAAI,IAAI,CAACnX,SAAS,CAAC,kBAAkB,CAAC,EAAE;MAClD5F,IAAI,CAACwX,UAAU,GAAGqF,UAAU;IAC9B,CAAC,MAAM;MACL7c,IAAI,CAAC6c,UAAU,GAAGA,UAAU;IAC9B;EACF;EAEAjB,gCAAgCA,CAC9B5b,IAAiC,EACjC0V,sBAA2C,EAClC;IAET,IAAIA,sBAAsB,EAAE;MAC1B,MAAMwB,SAAS,GAAG,IAAI,CAACzP,eAAe,CACpCiO,sBACF,CAAC;MACDwB,SAAS,CAAC0B,KAAK,GAAGlD,sBAAsB;MACxC1V,IAAI,CAACoX,UAAU,CAAC9Q,IAAI,CAClB,IAAI,CAAC8V,qBAAqB,CAAClF,SAAS,EAAE,wBAAwB,CAChE,CAAC;MACD,OAAO,IAAI;IACb,CAAC,MAAM,IAEL,IAAAqE,iCAA0B,EAAC,IAAI,CAACtd,KAAK,CAAC9B,IAAI,CAAC,EAC3C;MACA,IAAI,CAACggB,yBAAyB,CAC5Bnc,IAAI,EACJ,IAAI,CAACC,SAAS,CAA2B,CAAC,EAC1C,wBACF,CAAC;MACD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEA6b,6BAA6BA,CAAC9b,IAAiC,EAAW;IACxE,IAAI,IAAI,CAACD,KAAK,GAAQ,CAAC,EAAE;MACvB,MAAMmX,SAAS,GAAG,IAAI,CAACjX,SAAS,CAA6B,CAAC;MAC9D,IAAI,CAACC,IAAI,CAAC,CAAC;MACX,IAAI,CAAC8b,gBAAgB,GAAO,CAAC;MAE7B,IAAI,CAACG,yBAAyB,CAC5Bnc,IAAI,EACJkX,SAAS,EACT,0BACF,CAAC;MACD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEA6E,0BAA0BA,CAAC/b,IAAiC,EAAE;IAC5D,IAAI4Z,KAAK,GAAG,IAAI;IAChB,IAAI,CAACjT,MAAM,EAAU,CAAC;IACtB,OAAO,CAAC,IAAI,CAACpB,GAAG,EAAU,CAAC,EAAE;MAC3B,IAAIqU,KAAK,EAAE;QACTA,KAAK,GAAG,KAAK;MACf,CAAC,MAAM;QAEL,IAAI,IAAI,CAACrU,GAAG,GAAS,CAAC,EAAE;UACtB,MAAM,IAAI,CAACtG,KAAK,CAACC,kBAAM,CAACge,sBAAsB,EAAE;YAC9Cpe,EAAE,EAAE,IAAI,CAACb,KAAK,CAACnB;UACjB,CAAC,CAAC;QACJ;QAEA,IAAI,CAAC6J,MAAM,GAAS,CAAC;QACrB,IAAI,IAAI,CAACpB,GAAG,EAAU,CAAC,EAAE;MAC3B;MAEA,MAAM2R,SAAS,GAAG,IAAI,CAACjX,SAAS,CAAoB,CAAC;MACrD,MAAMkd,gBAAgB,GAAG,IAAI,CAACpd,KAAK,IAAU,CAAC;MAC9C,MAAM8Z,eAAe,GAAG,IAAI,CAACzZ,YAAY,IAAS,CAAC;MACnD8W,SAAS,CAAC6D,QAAQ,GAAG,IAAI,CAAC1D,qBAAqB,CAAC,CAAC;MACjD,MAAM+F,eAAe,GAAG,IAAI,CAACC,oBAAoB,CAC/CnG,SAAS,EACTiG,gBAAgB,EAChBnd,IAAI,CAAC6E,UAAU,KAAK,MAAM,IAAI7E,IAAI,CAAC6E,UAAU,KAAK,QAAQ,EAC1DgV,eAAe,EACfvM,SACF,CAAC;MACDtN,IAAI,CAACoX,UAAU,CAAC9Q,IAAI,CAAC8W,eAAe,CAAC;IACvC;EACF;EAGAC,oBAAoBA,CAClBnG,SAAoC,EACpCiG,gBAAyB,EAEzBG,kBAA2B,EAC3BzD,eAAwB,EACxBvE,WAAqC,EAElB;IACnB,IAAI,IAAI,CAAC1M,aAAa,GAAO,CAAC,EAAE;MAC9BsO,SAAS,CAAC0B,KAAK,GAAG,IAAI,CAAC5R,eAAe,CAAC,CAAC;IAC1C,CAAC,MAAM;MACL,MAAM;QAAE+T;MAAS,CAAC,GAAG7D,SAAS;MAC9B,IAAIiG,gBAAgB,EAAE;QACpB,MAAM,IAAI,CAACle,KAAK,CAACC,kBAAM,CAACqe,qBAAqB,EAAE;UAC7Cze,EAAE,EAAEoY,SAAS;UACbsG,UAAU,EAAGzC,QAAQ,CAAqBze;QAC5C,CAAC,CAAC;MACJ;MACA,IAAI,CAACwc,iBAAiB,CACnBiC,QAAQ,CAAkB1S,IAAI,EAC/B6O,SAAS,CAAC9a,GAAG,CAACC,KAAK,EACnB,IAAI,EACJ,IACF,CAAC;MACD,IAAI,CAAC6a,SAAS,CAAC0B,KAAK,EAAE;QACpB1B,SAAS,CAAC0B,KAAK,GAAG,IAAAqB,qBAAe,EAACc,QAAQ,CAAC;MAC7C;IACF;IACA,OAAO,IAAI,CAACqB,qBAAqB,CAC/BlF,SAAS,EACT,iBAAiB,EACjB5B,WACF,CAAC;EACH;EAIAmI,WAAWA,CACT7R,KAAuD,EAC9C;IACT,OAAOA,KAAK,CAACzP,IAAI,KAAK,YAAY,IAAIyP,KAAK,CAACvD,IAAI,KAAK,MAAM;EAC7D;AACF;AAACjN,OAAA,CAAAsiB,OAAA,GAAAhgB,eAAA"}