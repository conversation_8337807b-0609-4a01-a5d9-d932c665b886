import React, { Component, useState } from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
// @ts-ignore
import Modal from 'react-native-modal';
import { spacing, color } from '../../../styles';
import { AppmakerText, <PERSON><PERSON>, <PERSON>per, VariationListing } from '../../../';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
const CloseIcon = (props) => <Icon name="x" color="#000" {...props} />;

const DefaultModalContent = ({
  attributes = {},
  BlocksView,
  innerBlocks,
  ...props
}) => {
  const { title, subTitle } = attributes;
  return (
    <View style={styles.modalBody}>
      <View style={styles.modalHeader}>
        <View>
          <AppmakerText category="actionTitle" status="dark">
            {title}
          </AppmakerText>
          <AppmakerText category="highlighter1" status="grey">
            {subTitle}
          </AppmakerText>
        </View>
        <Button
          onPress={props.onPress}
          status="white"
          accessoryLeft={CloseIcon}
          small
        />
      </View>
      <ScrollView
        style={{
          flex: 1,
          width: '100%',
        }}>
        <BlocksView template={innerBlocks} {...props} blockData={props.data} />
      </ScrollView>
      {/*  */}
    </View>
  );
};

const BottomSheet = (props) => {
  const [visible, setVisibility] = useState(true);
  const open = () => setVisibility(true);
  const close = () => setVisibility(false);
  return (
    <View style={styles.view}>
      <Button onPress={open} small>
        Open modal
      </Button>
      <Modal
        testID={'modal'}
        isVisible={visible}
        onSwipeComplete={close}
        onBackButtonPress={close}
        backdropTransitionOutTiming={0}
        onBackdropPress={close}
        style={styles.view}>
        <DefaultModalContent onPress={close} {...props} />
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  view: {
    justifyContent: 'flex-end',
    marginVertical: 0,
    marginHorizontal: 1,
  },
  modalBody: {
    backgroundColor: color.white,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
    flexDirection: 'column',
    maxHeight: '80%',
    width: '100%',
    flex: 1,
    padding: spacing.md,
  },
  modalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: color.light,
    paddingBottom: spacing.small,
  },
  modalContent: {
    paddingVertical: spacing.base,
    flexDirection: 'column',
    width: '100%',
  },
  modalStepperContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
    marginBottom: spacing.md,
    borderBottomColor: color.light,
    borderBottomWidth: 1,
    borderTopColor: color.light,
    borderTopWidth: 1,
  },
  modalFooter: {
    width: '100%',
    backgroundColor: color.white,
  },
});

export default BottomSheet;
