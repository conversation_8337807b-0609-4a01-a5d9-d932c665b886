{"version": 3, "names": ["_context", "require", "_t", "VISITOR_KEYS", "traverseNode", "node", "opts", "scope", "state", "path", "<PERSON><PERSON><PERSON><PERSON>", "keys", "type", "context", "TraversalContext", "key", "visit"], "sources": ["../src/traverse-node.ts"], "sourcesContent": ["import TraversalContext from \"./context\";\nimport type { TraverseOptions } from \"./index\";\nimport type <PERSON>de<PERSON><PERSON> from \"./path\";\nimport type Scope from \"./scope\";\nimport type * as t from \"@babel/types\";\nimport { VISITOR_KEYS } from \"@babel/types\";\n\n/**\n * Traverse the children of given node\n * @param {Node} node\n * @param {TraverseOptions} opts The traverse options used to create a new traversal context\n * @param {scope} scope A traversal scope used to create a new traversal context. When opts.noScope is true, scope should not be provided\n * @param {any} state A user data storage provided as the second callback argument for traversal visitors\n * @param {NodePath} path A NodePath of given node\n * @param {Record<string, boolean>} skipKeys A map from key names to whether that should be skipped during traversal. The skipKeys are applied to every descendants\n * @returns {boolean} Whether the traversal stops early\n\n * @note This function does not visit the given `node`.\n */\nexport function traverseNode(\n  node: t.Node,\n  opts: TraverseOptions,\n  scope?: Scope,\n  state?: any,\n  path?: NodePath,\n  skipKeys?: Record<string, boolean>,\n): boolean {\n  const keys = VISITOR_KEYS[node.type];\n  if (!keys) return false;\n\n  const context = new TraversalContext(scope, opts, state, path);\n  for (const key of keys) {\n    if (skipKeys && skipKeys[key]) continue;\n    if (context.visit(node, key)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAKA,IAAAC,EAAA,GAAAD,OAAA;AAA4C;EAAnCE;AAAY,IAAAD,EAAA;AAcd,SAASE,YAAYA,CAC1BC,IAAY,EACZC,IAAqB,EACrBC,KAAa,EACbC,KAAW,EACXC,IAAe,EACfC,QAAkC,EACzB;EACT,MAAMC,IAAI,GAAGR,YAAY,CAACE,IAAI,CAACO,IAAI,CAAC;EACpC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAME,OAAO,GAAG,IAAIC,gBAAgB,CAACP,KAAK,EAAED,IAAI,EAAEE,KAAK,EAAEC,IAAI,CAAC;EAC9D,KAAK,MAAMM,GAAG,IAAIJ,IAAI,EAAE;IACtB,IAAID,QAAQ,IAAIA,QAAQ,CAACK,GAAG,CAAC,EAAE;IAC/B,IAAIF,OAAO,CAACG,KAAK,CAACX,IAAI,EAAEU,GAAG,CAAC,EAAE;MAC5B,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd"}