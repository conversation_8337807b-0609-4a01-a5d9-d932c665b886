import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { Layout, AppmakerText, AppTouchable } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
const PageHead = ({
  attributes,
  onPress,
  onAction,
  clientId,
  coreDispatch,
  ...blockItem
}) => {
  const {
    title,
    subTitle,
    layoutSelector = true,
    description = false,
  } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({
    color,
    spacing,
    layoutSelector,
  });
  // const setPageState = usePageState(state => state.numColumns);
  function setNumColumns(numColumns) {
    coreDispatch({
      type: 'SET_VALUE',
      name: 'numColumns',
      value: numColumns,
    });
  }
  return (
    <View style={styles.container}>
      <Layout style={styles.titleContainer}>
        {title ? (
          <AppmakerText category="actionTitle" status="dark" numberOfLines={1}>
            {title}
          </AppmakerText>
        ) : null}
        {subTitle ? (
          <AppmakerText
            category="highlighter1"
            status="grey"
            style={styles.subTitle}>
            {subTitle}
          </AppmakerText>
        ) : null}
        {description ? (
          <AppmakerText status="demiDark" style={styles.description}>
            {description}
          </AppmakerText>
        ) : null}
      </Layout>
      {layoutSelector ? (
        <Layout style={styles.row}>
          <AppTouchable
            style={[styles.icon, styles.active]}
            onPress={() => setNumColumns(1)}>
            <Icon name="square" size={18} color={color.dark} />
          </AppTouchable>
          <AppTouchable
            style={[styles.icon, styles.active]}
            onPress={() => setNumColumns(2)}>
            <Icon name="grid" size={18} color={color.dark} />
          </AppTouchable>
        </Layout>
      ) : null}
    </View>
  );
};

const allStyles = ({ spacing, color, layoutSelector }) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      padding: spacing.base,
      justifyContent: 'space-between',
      backgroundColor: color.white,
      alignItems: 'center',
      borderRadius: spacing.nano,
    },
    titleContainer: {
      alignItems: layoutSelector ? 'flex-start' : 'center',
      flex: 1,
    },
    icon: {
      padding: spacing.nano,
      marginStart: spacing.small,
      borderRadius: spacing.nano,
    },
    active: {
      backgroundColor: color.light,
    },
    row: {
      flexDirection: 'row',
    },
    subTitle: {
      marginTop: spacing.nano,
    },
    description: {
      marginTop: spacing.base,
      marginBottom: spacing.small,
      textAlign: 'center',
      fontSize: 13,
    },
  });

export default PageHead;
