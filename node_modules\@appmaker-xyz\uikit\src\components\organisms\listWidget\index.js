import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { testProps } from '@appmaker-xyz/core';
import {
  AppmakerText,
  Layout,
  ListImage,
  Button,
  Stepper,
  AppTouchable,
} from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import { Badge } from '../../molecules';

const ListItem = ({
  clientId,
  uri,
  title,
  salePrice,
  regularPrice,
  saleBadge,
  outOfStock = false,
  staticTexts,
  quatityInfo,
  discription,
  onClick,
  attribute,
  quatity,
  quantityLoading,
  onQuantityChange,
  onPress,
  saved,
  onSaved,
}) => {
  const { color, spacing } = useApThemeState();
  const [addwish, setAddwish] = useState(saved);
  const styles = allStyles({ color, spacing });

  useEffect(() => {
    setAddwish(saved);
  }, [saved]);

  let attributeView = null;
  if (attribute) {
    attributeView = attribute.map((value) => {
      return (
        <View style={styles.attributesListing}>
          <Badge
            text={value.label}
            status="demiDark"
            style={styles.attributeBadgeStyle}
          />
        </View>
      );
    });
  }

  return (
    <AppTouchable onPress={onPress} {...testProps(clientId)}>
      <Layout style={styles.container}>
        <View style={styles.imgContainer}>
          <ListImage uri={uri} />

          <Badge
            style={styles.saleBadgeStyle}
            status="success"
            text={saleBadge}
          />
          <Badge
            style={styles.stockOutBadgeStyle}
            status="danger"
            text={outOfStock}
          />
        </View>
        <View style={styles.textContainer}>
          <View>
            <AppmakerText category="bodyParagraph" status="dark">
              {title}
            </AppmakerText>
            {/* <AppmakerText category="highlighter1" status="demiDark">
              Quantity Info
            </AppmakerText> */}
          </View>
          <View>
            <View style={styles.priceDataContainer}>
              <AppmakerText category="h1Heading">{regularPrice}</AppmakerText>
              <AppmakerText
                category="h1SubHeading"
                status="success"
                hideText={!saleBadge}
                style={styles.offerPrice}>
                {salePrice}
              </AppmakerText>
            </View>
            {/* <AppmakerText category="highlighter2" status="demiDark">
              Lorem ipsum, dolor sit amet cons...
            </AppmakerText> */}
          </View>
          {attribute && (
            <View style={styles.attributesContainer}>{attributeView}</View>
          )}
        </View>
        <View style={styles.stepperContainer}>
          {!outOfStock ? (
            <Stepper
              count={quatity}
              customizable={false}
              buttonText={staticTexts.groceryButtonText}
              buttonLoadingText={staticTexts.groceryButtonLoadingText}
              adding={quantityLoading}
              onChange={onQuantityChange}
            />
          ) : (
            <Button
              small
              outline
              status={addwish ? 'light' : 'grey'}
              onPress={() => {
                setAddwish(!addwish);
                saved = !addwish;
                onSaved(!addwish);
              }}>
              {addwish
                ? staticTexts.addedToWishlistText
                : staticTexts.addToWishlistText}
            </Button>
          )}
        </View>
      </Layout>
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      padding: spacing.base,
      backgroundColor: color.white,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    imgContainer: {
      position: 'relative',
    },
    saleBadgeStyle: {
      position: 'absolute',
      left: spacing.nano,
      top: spacing.nano,
      opacity: 0.8,
    },
    stockOutBadgeStyle: {
      position: 'absolute',
      left: spacing.nano,
      right: spacing.nano,
      bottom: spacing.nano,
      opacity: 0.8,
    },
    textContainer: {
      flex: 1,
      paddingHorizontal: spacing.base,
    },
    priceDataContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: spacing.nano,
      flexWrap: 'wrap',
    },
    stepperContainer: {
      maxWidth: 115,
    },
    attributesContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    attributesListing: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: spacing.nano,
    },
    attributeBadgeStyle: {
      marginRight: spacing.nano,
      marginBottom: spacing.nano,
    },
    offerPrice: {
      textDecorationLine: 'line-through',
      marginLeft: spacing.nano,
    },
  });

export default ListItem;
