/**
 * Created by saleeh on 14/08/16.
 */
import t from '@appmaker/tcomb-form-native';
import moment from 'moment';

function getValidateObject(validate) {
  //validate
  var regex = false;
  switch (validate.type) {
    case 'email':
      regex = /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
      break;
    case 'phone':
      regex = /[\s\#0-9_\-\+\(\)]/;
      break;
    case 'regex':
      regex = new RegExp(validate.regex);
      break;
  }
  var Type = t.refinement(t.String, function (value) {
    if (regex) {
      return regex.test(value);
    }
    return true;
  });
  Type.getValidationErrorMessage = function (value, path, context) {
    return validate.message;
  };
  return Type;
}

/***
 *  Convert a single Field
 * @param item
 * @returns {*}
 */
var toField = function (item, fieldOptions) {
  var defaultOpt = {
    label: item.label,
    help: item.description,
    placeholder: item.placeholder,
    underlineColorAndroid: 'transparent',
    onFocus: item.onFocus ? item.onFocus : () => {},
    onCollapseChange: item.onCollapseChange ? item.onCollapseChange : () => {},
  };
  if (fieldOptions.hasOwnProperty(item.type)) {
    defaultOpt = { ...defaultOpt, ...fieldOptions[item.type] };
  }
  switch (item.type) {
    case 'select':
      return {
        type: item.required
          ? t.enums(item.options)
          : t.maybe(t.enums(item.options)),
        options: {
          ...defaultOpt,
          // selectType: 'radio',
        },
        value: item.default_value,
      };

    case 'datepicker':
      var dateTimeFormat =
        item.mode == 'time' ? 'h:mm a' : 'dddd, MMMM Do YYYY';
      const default_value = moment(item.default_value, 'DD-MM-YYYY').toDate();
      const minimum_value = item.minDate
        ? moment(item.minDate, 'DD-MM-YYYY').toDate()
        : '';
      const maximum_value = item.maxDate
        ? moment(item.maxDate, 'DD-MM-YYYY').toDate()
        : '';
      return {
        type: item.required ? t.Date : t.maybe(t.Date),
        options: {
          ...defaultOpt,
          config: {
            format: (date) => String(moment(date).format(dateTimeFormat)),
          },
          mode: item.mode || 'date',
          minimumDate: minimum_value,
          maximumDate: maximum_value,
        },
        value: default_value,
      };

    case 'number':
      return {
        type: item.required ? t.Number : t.maybe(t.Number),
        options: {
          ...defaultOpt,
          keyboardType: 'numeric',
        },
        value: item.default_value,
      };
    case 'checkbox':
      return {
        type: item.required ? t.Boolean : t.maybe(t.Boolean),
        options: {
          ...defaultOpt,
        },
        value: item.default_value,
      };
    case 'dependent-select':
      return {
        type: item.required ? t.String : t.maybe(t.String),
        options: {
          ...defaultOpt,
        },
        value: item.default_value,
      };
    case 'textarea':
      const option = {
        ...defaultOpt,
        keyboardType: item.keyboardType,
        multiline: true,
        numberOfLines: item.options.line_count,
        maxLength: item.maxLength,
        minLength: item.minLength,
        stylesheet: {
          ...t.form.Form.stylesheet,
          textbox: {
            ...t.form.Form.stylesheet.textbox,
            normal: {
              ...t.form.Form.stylesheet.textbox.normal,
              height: 100,
            },
            error: {
              ...t.form.Form.stylesheet.textbox.error,
              height: 100,
            },
          },
        },
      };
      return {
        type: item.required
          ? getValidateObject(item.validate)
          : t.maybe(getValidateObject(item.validate)),
        options: {
          ...defaultOpt,
          keyboardType: item.keyboardType,
          multiline: true,
          numberOfLines: item.options.line_count,
          maxLength: item.maxLength,
          minLength: item.minLength,
          stylesheet: {
            ...t.form.Form.stylesheet,
            textbox: {
              ...t.form.Form.stylesheet.textbox,
              normal: {
                ...t.form.Form.stylesheet.textbox.normal,
                height: 100,
              },
              error: {
                ...t.form.Form.stylesheet.textbox.error,
                height: 100,
              },
            },
          },
        },
        value: item.default_value,
      };
    case 'hidden':
      return {
        type: t.maybe(t.Any),
        options: {
          hidden: true,
        },
        value: item.default_value,
      };
    case 'password':
      return {
        type: item.required
          ? getValidateObject(item.validate)
          : t.maybe(getValidateObject(item.validate)),
        options: {
          ...defaultOpt,
          keyboardType: item.keyboardType,
          maxLength: item.maxLength,
          minLength: item.minLength,
          secureTextEntry: true,
          autoCapitalize: 'none',
        },
        value: item.default_value,
      };
    case 'email':
    case 'text':
    case 'tel':
    default:
      return {
        type: item.required
          ? getValidateObject(item.validate)
          : t.maybe(getValidateObject(item.validate)),
        options: {
          ...defaultOpt,
          keyboardType: item.keyboardType,
          maxLength: item.maxLength,
          minLength: item.minLength,
          autoCapitalize: 'none',
          returnKeyType: item.returnKeyType,
        },
        value: item.default_value,
      };
  }
};
/***
 * Convert JSON form Objects to T-comp forms with {type,options,values}
 * @param items
 * @param dependencies
 * @param order
 * @param fieldOptions
 * @returns {{types: {}, options: {fields: {}, order: string[]}, values: {}}}
 */
var toForm = function (
  items,
  dependencies,
  order = Object.keys(items),
  fieldOptions = {},
) {
  /***
   * Default values
   * @type {{types: {}, options: {fields: {}, order: string[]}, values: {}}}
   */
  var res = {
    types: {},
    options: {
      fields: {},
      order: order,
    },
    values: {},
  };
  /***
   * Go thorough all fields
   */
  Object.keys(items).map((key) => {
    /***
     * Getting One field
     */
    var item = items[key];
    /***
     * Convecting to  field
     * @type {{type, options, value}}
     */
    var field = toField(item, fieldOptions);
    if (field) {
      /***
       * Only add if not dependent
       */
      if (!item.dependent) {
        res.types[key] = field.type;
      }
      /***
       * Pushing options and values
       *
       */
      if (field.options) {
        res.options.fields[key] = field.options;
      }
      if (field.value) {
        res.values[key] = field.value;
      }
    }
  });
  /***
   *  Creating Root Struct
   */
  var ParentItem = t.struct(res.types);
  const RootItem = t.union([ParentItem, t.enums({})]);
  /***
   * Calls on every Value change
   * @param value
   * @returns {*}
   */
  RootItem.dispatch = (value) => {
    /***
     * In first call value will be undefined
     */
    if (value == undefined) {
      return ParentItem;
    }
    /***
     * for new Form elements
     * @type {{}}
     */
    var fields = {};
    /***
         * Loop through each dependency item
         * @example  {
                       billing_state: {
                            on: "billing_country",
                            onEmpty: {
                                "id": "billing_phone"
                            }
                        },
                        billing_city: {
                            on: "billing_state"
                        },
                        billing_address_2: {
                            on: "billing_city"
                        }
         */
    if (dependencies) {
      Object.keys(dependencies).map((key) => {
        var config = dependencies[key];
        /***
         * Load dependent Field Value
         * @example Load selected country for selecting its state
         */
        var dependentFieldValue = value[config.on];

        if (dependentFieldValue) {
          /***
           * check match value equals dependent Field Value
           */
          if (
            config.hasOwnProperty('matchValue') &&
            config.matchValue != dependentFieldValue
          ) {
            return;
          }

          if (
            config.hasOwnProperty('notMatchValue') &&
            config.notMatchValue == dependentFieldValue
          ) {
            return;
          }

          /***
           * Copy current Item to newItem
           * @type {{}}
           */
          var newItem = { ...items[key] };
          /***
           * Check dependency is select
           */
          if (newItem.type == 'dependent-select') {
            /**
             *Checks Subvalues
             * @example if India selected check IN exist in states option
             */
            if (
              newItem.options[dependentFieldValue] &&
              newItem.options[dependentFieldValue].items != null
            ) {
              /***
               * convert it as normal select
               * @type {string}
               */
              newItem.type = 'select';
              /***
               * Load its options
               */
              var optionItems = newItem.options[dependentFieldValue].items;
              /***
               * Clear value if  value not  in options
               * @example error if California is selected and changed Country to India
               */
              if (!optionItems.hasOwnProperty(value[key])) {
                value[key] = '';
              }
              newItem.options = optionItems;
            } else if (config.hasOwnProperty('onEmpty')) {
              /***
               *Create onEmpty field
               */
              newItem = { ...items[config.onEmpty.id] };
            }
          }
          fields[key] = toField(newItem, fieldOptions).type;
        }
      });
    }
    //return ParentItem;
    return ParentItem.extend(fields);
  };
  res.types = RootItem;

  return res;
};
var toRequest = function (ob) {
  var toReturn = {};

  for (var i in ob) {
    if (!ob.hasOwnProperty(i)) {
      continue;
    }

    if (
      typeof ob[i] === 'object' &&
      !(ob[i] != null && typeof ob[i].getMonth === 'function')
    ) {
      var flatObject = toRequest(ob[i]);
      for (var x in flatObject) {
        if (!flatObject.hasOwnProperty(x)) {
          continue;
        }

        toReturn[x] = flatObject[x];
      }
    } else {
      if (typeof ob[i] === 'boolean' || typeof ob[i] === 'string') {
        toReturn[i] = ob[i];
      } else {
        toReturn[i] = String(ob[i]);
      }
    }
  }
  return toReturn;
};
export { toField, toForm, toRequest, getValidateObject };
