import React, { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import { Layout, AppTouchable, AppmakerText } from '../..';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { useApThemeState } from '../../../theme/ThemeContext';

const CheckBox = (props) => {
  const {
    noMargin,
    checkedStatus,
    value,
    containerStyle,
    onValueChange,
    label,
    small = false,
    activeColor: activeColorProp,
    labelStyles,
    testId,
  } = props;
  const [checked, setChecked] = useState(checkedStatus || value);
  const { color, spacing, font } = useApThemeState();
  const activeColor = activeColorProp || color.success;
  const styles = allStyles({
    color,
    spacing,
    checked,
    activeColor,
    noMargin,
    small,
  });
  useEffect(() => {
    setChecked(checkedStatus || value);
  }, [checkedStatus || value]);
  return (
    <AppTouchable
      testId={testId}
      style={containerStyle}
      onPress={() => {
        setChecked(!checked);
        onValueChange && onValueChange(!checked);
      }}>
      <Layout style={styles.checkBoxContainer}>
        <Layout style={styles.checkBox}>
          <Icon
            name="check"
            size={small ? font.size.base : font.size.md}
            color="#ffffff"
          />
        </Layout>
        <AppmakerText
          category="bodyParagraph"
          style={{ ...styles.labelTextStyles, ...labelStyles }}>
          {label}
        </AppmakerText>
      </Layout>
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color, checked, activeColor, noMargin, small }) =>
  StyleSheet.create({
    checkBoxContainer: {
      flexDirection: 'row',
      marginBottom: noMargin ? 0 : spacing.base,
      alignItems: 'flex-start',
    },
    checkBox: {
      backgroundColor: checked ? activeColor : color.white,
      height: small ? 18 : 20,
      width: small ? 18 : 20,
      marginRight: spacing.nano + spacing.mini,
      borderColor: checked ? activeColor : color.demiDark,
      borderWidth: spacing.nano / 4,
      borderRadius: spacing.nano,
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 1,
    },
    labelTextStyles: {
      flexWrap: 'wrap',
      flexShrink: 1,
    },
  });

export default CheckBox;
