{"version": 3, "names": ["_debug", "data", "require", "_fs", "_path", "_json", "_gensync", "_caching", "_configApi", "_utils", "_moduleTypes", "_patternToRegex", "_configError", "fs", "_rewriteStackTrace", "debug", "buildDebug", "ROOT_CONFIG_FILENAMES", "exports", "RELATIVE_CONFIG_FILENAMES", "BABELIGNORE_FILENAME", "LOADING_CONFIGS", "Set", "readConfigCode", "makeStrongCache", "filepath", "cache", "nodeFs", "existsSync", "never", "has", "dirname", "path", "options", "add", "loadCodeDefault", "delete", "assertCache", "endHiddenCallStack", "makeConfigAPI", "Array", "isArray", "ConfigError", "then", "configured", "throwConfigError", "packageToBabelConfig", "makeWeakCacheSync", "file", "babel", "readConfigJSON5", "makeStaticFileCache", "content", "json5", "parse", "err", "message", "readIgnoreConfig", "ignoreDir", "ignorePatterns", "split", "map", "line", "replace", "trim", "filter", "pattern", "ignore", "pathPatternToRegex", "findConfigUpwards", "rootDir", "filename", "join", "nextDir", "findRelativeConfig", "packageData", "envName", "caller", "config", "loc", "directories", "_packageData$pkg", "loadOneConfig", "pkg", "ignoreLoc", "findRootConfig", "names", "previousConfig", "configs", "gens<PERSON>", "all", "readConfig", "reduce", "basename", "loadConfig", "name", "v", "w", "process", "versions", "node", "resolve", "r", "paths", "b", "M", "f", "_findPath", "_nodeModulePaths", "concat", "Error", "code", "conf", "ext", "extname", "resolveShowConfigPath", "targetPath", "env", "BABEL_SHOW_CONFIG_FOR", "absolutePath", "stats", "stat", "isFile"], "sources": ["../../../src/config/files/configuration.ts"], "sourcesContent": ["import buildDebug from \"debug\";\nimport nodeFs from \"fs\";\nimport path from \"path\";\nimport json5 from \"json5\";\nimport gensync from \"gensync\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport { makeStrongCache, makeWeakCacheSync } from \"../caching\";\nimport type { CacheConfigurator } from \"../caching\";\nimport { makeConfigAPI } from \"../helpers/config-api\";\nimport type { ConfigAPI } from \"../helpers/config-api\";\nimport { makeStaticFileCache } from \"./utils\";\nimport loadCodeDefault from \"./module-types\";\nimport pathPatternToRegex from \"../pattern-to-regex\";\nimport type { FilePackageData, RelativeConfig, ConfigFile } from \"./types\";\nimport type { CallerMetadata } from \"../validation/options\";\nimport ConfigError from \"../../errors/config-error\";\n\nimport * as fs from \"../../gensync-utils/fs\";\n\nimport { createRequire } from \"module\";\nimport { endHiddenCallStack } from \"../../errors/rewrite-stack-trace\";\nconst require = createRequire(import.meta.url);\n\nconst debug = buildDebug(\"babel:config:loading:files:configuration\");\n\nexport const ROOT_CONFIG_FILENAMES = [\n  \"babel.config.js\",\n  \"babel.config.cjs\",\n  \"babel.config.mjs\",\n  \"babel.config.json\",\n  \"babel.config.cts\",\n];\nconst RELATIVE_CONFIG_FILENAMES = [\n  \".babelrc\",\n  \".babelrc.js\",\n  \".babelrc.cjs\",\n  \".babelrc.mjs\",\n  \".babelrc.json\",\n  \".babelrc.cts\",\n];\n\nconst BABELIGNORE_FILENAME = \".babelignore\";\n\nconst LOADING_CONFIGS = new Set();\n\nconst readConfigCode = makeStrongCache(function* readConfigCode(\n  filepath: string,\n  cache: CacheConfigurator<{\n    envName: string;\n    caller: CallerMetadata | undefined;\n  }>,\n): Handler<ConfigFile | null> {\n  if (!nodeFs.existsSync(filepath)) {\n    cache.never();\n    return null;\n  }\n\n  // The `require()` call below can make this code reentrant if a require hook like @babel/register has been\n  // loaded into the system. That would cause Babel to attempt to compile the `.babelrc.js` file as it loads\n  // below. To cover this case, we auto-ignore re-entrant config processing.\n  if (LOADING_CONFIGS.has(filepath)) {\n    cache.never();\n\n    debug(\"Auto-ignoring usage of config %o.\", filepath);\n    return {\n      filepath,\n      dirname: path.dirname(filepath),\n      options: {},\n    };\n  }\n\n  let options: unknown;\n  try {\n    LOADING_CONFIGS.add(filepath);\n    options = yield* loadCodeDefault(\n      filepath,\n      \"You appear to be using a native ECMAScript module configuration \" +\n        \"file, which is only supported when running Babel asynchronously.\",\n    );\n  } finally {\n    LOADING_CONFIGS.delete(filepath);\n  }\n\n  let assertCache = false;\n  if (typeof options === \"function\") {\n    // @ts-expect-error - if we want to make it possible to use async configs\n    yield* [];\n\n    options = endHiddenCallStack(options as any as (api: ConfigAPI) => {})(\n      makeConfigAPI(cache),\n    );\n\n    assertCache = true;\n  }\n\n  if (!options || typeof options !== \"object\" || Array.isArray(options)) {\n    throw new ConfigError(\n      `Configuration should be an exported JavaScript object.`,\n      filepath,\n    );\n  }\n\n  // @ts-expect-error todo(flow->ts)\n  if (typeof options.then === \"function\") {\n    throw new ConfigError(\n      `You appear to be using an async configuration, ` +\n        `which your current version of Babel does not support. ` +\n        `We may add support for this in the future, ` +\n        `but if you're on the most recent version of @babel/core and still ` +\n        `seeing this error, then you'll need to synchronously return your config.`,\n      filepath,\n    );\n  }\n\n  if (assertCache && !cache.configured()) throwConfigError(filepath);\n\n  return {\n    filepath,\n    dirname: path.dirname(filepath),\n    options,\n  };\n});\n\nconst packageToBabelConfig = makeWeakCacheSync(\n  (file: ConfigFile): ConfigFile | null => {\n    const babel: unknown = file.options[\"babel\"];\n\n    if (typeof babel === \"undefined\") return null;\n\n    if (typeof babel !== \"object\" || Array.isArray(babel) || babel === null) {\n      throw new ConfigError(`.babel property must be an object`, file.filepath);\n    }\n\n    return {\n      filepath: file.filepath,\n      dirname: file.dirname,\n      options: babel,\n    };\n  },\n);\n\nconst readConfigJSON5 = makeStaticFileCache((filepath, content): ConfigFile => {\n  let options;\n  try {\n    options = json5.parse(content);\n  } catch (err) {\n    throw new ConfigError(\n      `Error while parsing config - ${err.message}`,\n      filepath,\n    );\n  }\n\n  if (!options) throw new ConfigError(`No config detected`, filepath);\n\n  if (typeof options !== \"object\") {\n    throw new ConfigError(`Config returned typeof ${typeof options}`, filepath);\n  }\n  if (Array.isArray(options)) {\n    throw new ConfigError(`Expected config object but found array`, filepath);\n  }\n\n  delete options[\"$schema\"];\n\n  return {\n    filepath,\n    dirname: path.dirname(filepath),\n    options,\n  };\n});\n\nconst readIgnoreConfig = makeStaticFileCache((filepath, content) => {\n  const ignoreDir = path.dirname(filepath);\n  const ignorePatterns = content\n    .split(\"\\n\")\n    .map<string>(line => line.replace(/#(.*?)$/, \"\").trim())\n    .filter(line => !!line);\n\n  for (const pattern of ignorePatterns) {\n    if (pattern[0] === \"!\") {\n      throw new ConfigError(\n        `Negation of file paths is not supported.`,\n        filepath,\n      );\n    }\n  }\n\n  return {\n    filepath,\n    dirname: path.dirname(filepath),\n    ignore: ignorePatterns.map(pattern =>\n      pathPatternToRegex(pattern, ignoreDir),\n    ),\n  };\n});\n\nexport function findConfigUpwards(rootDir: string): string | null {\n  let dirname = rootDir;\n  for (;;) {\n    for (const filename of ROOT_CONFIG_FILENAMES) {\n      if (nodeFs.existsSync(path.join(dirname, filename))) {\n        return dirname;\n      }\n    }\n\n    const nextDir = path.dirname(dirname);\n    if (dirname === nextDir) break;\n    dirname = nextDir;\n  }\n\n  return null;\n}\n\nexport function* findRelativeConfig(\n  packageData: FilePackageData,\n  envName: string,\n  caller: CallerMetadata | undefined,\n): Handler<RelativeConfig> {\n  let config = null;\n  let ignore = null;\n\n  const dirname = path.dirname(packageData.filepath);\n\n  for (const loc of packageData.directories) {\n    if (!config) {\n      config = yield* loadOneConfig(\n        RELATIVE_CONFIG_FILENAMES,\n        loc,\n        envName,\n        caller,\n        packageData.pkg?.dirname === loc\n          ? packageToBabelConfig(packageData.pkg)\n          : null,\n      );\n    }\n\n    if (!ignore) {\n      const ignoreLoc = path.join(loc, BABELIGNORE_FILENAME);\n      ignore = yield* readIgnoreConfig(ignoreLoc);\n\n      if (ignore) {\n        debug(\"Found ignore %o from %o.\", ignore.filepath, dirname);\n      }\n    }\n  }\n\n  return { config, ignore };\n}\n\nexport function findRootConfig(\n  dirname: string,\n  envName: string,\n  caller: CallerMetadata | undefined,\n): Handler<ConfigFile | null> {\n  return loadOneConfig(ROOT_CONFIG_FILENAMES, dirname, envName, caller);\n}\n\nfunction* loadOneConfig(\n  names: string[],\n  dirname: string,\n  envName: string,\n  caller: CallerMetadata | undefined,\n  previousConfig: ConfigFile | null = null,\n): Handler<ConfigFile | null> {\n  const configs = yield* gensync.all(\n    names.map(filename =>\n      readConfig(path.join(dirname, filename), envName, caller),\n    ),\n  );\n  const config = configs.reduce((previousConfig: ConfigFile | null, config) => {\n    if (config && previousConfig) {\n      throw new ConfigError(\n        `Multiple configuration files found. Please remove one:\\n` +\n          ` - ${path.basename(previousConfig.filepath)}\\n` +\n          ` - ${config.filepath}\\n` +\n          `from ${dirname}`,\n      );\n    }\n\n    return config || previousConfig;\n  }, previousConfig);\n\n  if (config) {\n    debug(\"Found configuration %o from %o.\", config.filepath, dirname);\n  }\n  return config;\n}\n\nexport function* loadConfig(\n  name: string,\n  dirname: string,\n  envName: string,\n  caller: CallerMetadata | undefined,\n): Handler<ConfigFile> {\n  const filepath = require.resolve(name, { paths: [dirname] });\n\n  const conf = yield* readConfig(filepath, envName, caller);\n  if (!conf) {\n    throw new ConfigError(\n      `Config file contains no configuration data`,\n      filepath,\n    );\n  }\n\n  debug(\"Loaded config %o from %o.\", name, dirname);\n  return conf;\n}\n\n/**\n * Read the given config file, returning the result. Returns null if no config was found, but will\n * throw if there are parsing errors while loading a config.\n */\nfunction readConfig(\n  filepath: string,\n  envName: string,\n  caller: CallerMetadata | undefined,\n): Handler<ConfigFile | null> {\n  const ext = path.extname(filepath);\n  switch (ext) {\n    case \".js\":\n    case \".cjs\":\n    case \".mjs\":\n    case \".cts\":\n      return readConfigCode(filepath, { envName, caller });\n    default:\n      return readConfigJSON5(filepath);\n  }\n}\n\nexport function* resolveShowConfigPath(\n  dirname: string,\n): Handler<string | null> {\n  const targetPath = process.env.BABEL_SHOW_CONFIG_FOR;\n  if (targetPath != null) {\n    const absolutePath = path.resolve(dirname, targetPath);\n    const stats = yield* fs.stat(absolutePath);\n    if (!stats.isFile()) {\n      throw new Error(\n        `${absolutePath}: BABEL_SHOW_CONFIG_FOR must refer to a regular file, directories are not supported.`,\n      );\n    }\n    return absolutePath;\n  }\n  return null;\n}\n\nfunction throwConfigError(filepath: string): never {\n  throw new ConfigError(\n    `\\\nCaching was left unconfigured. Babel's plugins, presets, and .babelrc.js files can be configured\nfor various types of caching, using the first param of their handler functions:\n\nmodule.exports = function(api) {\n  // The API exposes the following:\n\n  // Cache the returned value forever and don't call this function again.\n  api.cache(true);\n\n  // Don't cache at all. Not recommended because it will be very slow.\n  api.cache(false);\n\n  // Cached based on the value of some function. If this function returns a value different from\n  // a previously-encountered value, the plugins will re-evaluate.\n  var env = api.cache(() => process.env.NODE_ENV);\n\n  // If testing for a specific env, we recommend specifics to avoid instantiating a plugin for\n  // any possible NODE_ENV value that might come up during plugin execution.\n  var isProd = api.cache(() => process.env.NODE_ENV === \"production\");\n\n  // .cache(fn) will perform a linear search though instances to find the matching plugin based\n  // based on previous instantiated plugins. If you want to recreate the plugin and discard the\n  // previous instance whenever something changes, you may use:\n  var isProd = api.cache.invalidate(() => process.env.NODE_ENV === \"production\");\n\n  // Note, we also expose the following more-verbose versions of the above examples:\n  api.cache.forever(); // api.cache(true)\n  api.cache.never();   // api.cache(false)\n  api.cache.using(fn); // api.cache(fn)\n\n  // Return the value that will be cached.\n  return { };\n};`,\n    filepath,\n  );\n}\n"], "mappings": ";;;;;;;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,IAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,SAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,QAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAM,QAAA,GAAAL,OAAA;AAEA,IAAAM,UAAA,GAAAN,OAAA;AAEA,IAAAO,MAAA,GAAAP,OAAA;AACA,IAAAQ,YAAA,GAAAR,OAAA;AACA,IAAAS,eAAA,GAAAT,OAAA;AAGA,IAAAU,YAAA,GAAAV,OAAA;AAEA,IAAAW,EAAA,GAAAX,OAAA;AAGA,IAAAY,kBAAA,GAAAZ,OAAA;AAGA,MAAMa,KAAK,GAAGC,QAAU,CAAC,0CAA0C,CAAC;AAE7D,MAAMC,qBAAqB,GAAG,CACnC,iBAAiB,EACjB,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,EACnB,kBAAkB,CACnB;AAACC,OAAA,CAAAD,qBAAA,GAAAA,qBAAA;AACF,MAAME,yBAAyB,GAAG,CAChC,UAAU,EACV,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,cAAc,CACf;AAED,MAAMC,oBAAoB,GAAG,cAAc;AAE3C,MAAMC,eAAe,GAAG,IAAIC,GAAG,EAAE;AAEjC,MAAMC,cAAc,GAAG,IAAAC,wBAAe,EAAC,UAAUD,cAAcA,CAC7DE,QAAgB,EAChBC,KAGE,EAC0B;EAC5B,IAAI,CAACC,KAAM,CAACC,UAAU,CAACH,QAAQ,CAAC,EAAE;IAChCC,KAAK,CAACG,KAAK,EAAE;IACb,OAAO,IAAI;EACb;EAKA,IAAIR,eAAe,CAACS,GAAG,CAACL,QAAQ,CAAC,EAAE;IACjCC,KAAK,CAACG,KAAK,EAAE;IAEbd,KAAK,CAAC,mCAAmC,EAAEU,QAAQ,CAAC;IACpD,OAAO;MACLA,QAAQ;MACRM,OAAO,EAAEC,OAAI,CAACD,OAAO,CAACN,QAAQ,CAAC;MAC/BQ,OAAO,EAAE,CAAC;IACZ,CAAC;EACH;EAEA,IAAIA,OAAgB;EACpB,IAAI;IACFZ,eAAe,CAACa,GAAG,CAACT,QAAQ,CAAC;IAC7BQ,OAAO,GAAG,OAAO,IAAAE,oBAAe,EAC9BV,QAAQ,EACR,kEAAkE,GAChE,kEAAkE,CACrE;EACH,CAAC,SAAS;IACRJ,eAAe,CAACe,MAAM,CAACX,QAAQ,CAAC;EAClC;EAEA,IAAIY,WAAW,GAAG,KAAK;EACvB,IAAI,OAAOJ,OAAO,KAAK,UAAU,EAAE;IAEjC,OAAO,EAAE;IAETA,OAAO,GAAG,IAAAK,qCAAkB,EAACL,OAAO,CAAkC,CACpE,IAAAM,wBAAa,EAACb,KAAK,CAAC,CACrB;IAEDW,WAAW,GAAG,IAAI;EACpB;EAEA,IAAI,CAACJ,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIO,KAAK,CAACC,OAAO,CAACR,OAAO,CAAC,EAAE;IACrE,MAAM,IAAIS,oBAAW,CAClB,wDAAuD,EACxDjB,QAAQ,CACT;EACH;EAGA,IAAI,OAAOQ,OAAO,CAACU,IAAI,KAAK,UAAU,EAAE;IACtC,MAAM,IAAID,oBAAW,CAClB,iDAAgD,GAC9C,wDAAuD,GACvD,6CAA4C,GAC5C,oEAAmE,GACnE,0EAAyE,EAC5EjB,QAAQ,CACT;EACH;EAEA,IAAIY,WAAW,IAAI,CAACX,KAAK,CAACkB,UAAU,EAAE,EAAEC,gBAAgB,CAACpB,QAAQ,CAAC;EAElE,OAAO;IACLA,QAAQ;IACRM,OAAO,EAAEC,OAAI,CAACD,OAAO,CAACN,QAAQ,CAAC;IAC/BQ;EACF,CAAC;AACH,CAAC,CAAC;AAEF,MAAMa,oBAAoB,GAAG,IAAAC,0BAAiB,EAC3CC,IAAgB,IAAwB;EACvC,MAAMC,KAAc,GAAGD,IAAI,CAACf,OAAO,CAAC,OAAO,CAAC;EAE5C,IAAI,OAAOgB,KAAK,KAAK,WAAW,EAAE,OAAO,IAAI;EAE7C,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIT,KAAK,CAACC,OAAO,CAACQ,KAAK,CAAC,IAAIA,KAAK,KAAK,IAAI,EAAE;IACvE,MAAM,IAAIP,oBAAW,CAAE,mCAAkC,EAAEM,IAAI,CAACvB,QAAQ,CAAC;EAC3E;EAEA,OAAO;IACLA,QAAQ,EAAEuB,IAAI,CAACvB,QAAQ;IACvBM,OAAO,EAAEiB,IAAI,CAACjB,OAAO;IACrBE,OAAO,EAAEgB;EACX,CAAC;AACH,CAAC,CACF;AAED,MAAMC,eAAe,GAAG,IAAAC,0BAAmB,EAAC,CAAC1B,QAAQ,EAAE2B,OAAO,KAAiB;EAC7E,IAAInB,OAAO;EACX,IAAI;IACFA,OAAO,GAAGoB,OAAK,CAACC,KAAK,CAACF,OAAO,CAAC;EAChC,CAAC,CAAC,OAAOG,GAAG,EAAE;IACZ,MAAM,IAAIb,oBAAW,CAClB,gCAA+Ba,GAAG,CAACC,OAAQ,EAAC,EAC7C/B,QAAQ,CACT;EACH;EAEA,IAAI,CAACQ,OAAO,EAAE,MAAM,IAAIS,oBAAW,CAAE,oBAAmB,EAAEjB,QAAQ,CAAC;EAEnE,IAAI,OAAOQ,OAAO,KAAK,QAAQ,EAAE;IAC/B,MAAM,IAAIS,oBAAW,CAAE,0BAAyB,OAAOT,OAAQ,EAAC,EAAER,QAAQ,CAAC;EAC7E;EACA,IAAIe,KAAK,CAACC,OAAO,CAACR,OAAO,CAAC,EAAE;IAC1B,MAAM,IAAIS,oBAAW,CAAE,wCAAuC,EAAEjB,QAAQ,CAAC;EAC3E;EAEA,OAAOQ,OAAO,CAAC,SAAS,CAAC;EAEzB,OAAO;IACLR,QAAQ;IACRM,OAAO,EAAEC,OAAI,CAACD,OAAO,CAACN,QAAQ,CAAC;IAC/BQ;EACF,CAAC;AACH,CAAC,CAAC;AAEF,MAAMwB,gBAAgB,GAAG,IAAAN,0BAAmB,EAAC,CAAC1B,QAAQ,EAAE2B,OAAO,KAAK;EAClE,MAAMM,SAAS,GAAG1B,OAAI,CAACD,OAAO,CAACN,QAAQ,CAAC;EACxC,MAAMkC,cAAc,GAAGP,OAAO,CAC3BQ,KAAK,CAAC,IAAI,CAAC,CACXC,GAAG,CAASC,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAACC,IAAI,EAAE,CAAC,CACvDC,MAAM,CAACH,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC;EAEzB,KAAK,MAAMI,OAAO,IAAIP,cAAc,EAAE;IACpC,IAAIO,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACtB,MAAM,IAAIxB,oBAAW,CAClB,0CAAyC,EAC1CjB,QAAQ,CACT;IACH;EACF;EAEA,OAAO;IACLA,QAAQ;IACRM,OAAO,EAAEC,OAAI,CAACD,OAAO,CAACN,QAAQ,CAAC;IAC/B0C,MAAM,EAAER,cAAc,CAACE,GAAG,CAACK,OAAO,IAChC,IAAAE,uBAAkB,EAACF,OAAO,EAAER,SAAS,CAAC;EAE1C,CAAC;AACH,CAAC,CAAC;AAEK,SAASW,iBAAiBA,CAACC,OAAe,EAAiB;EAChE,IAAIvC,OAAO,GAAGuC,OAAO;EACrB,SAAS;IACP,KAAK,MAAMC,QAAQ,IAAItD,qBAAqB,EAAE;MAC5C,IAAIU,KAAM,CAACC,UAAU,CAACI,OAAI,CAACwC,IAAI,CAACzC,OAAO,EAAEwC,QAAQ,CAAC,CAAC,EAAE;QACnD,OAAOxC,OAAO;MAChB;IACF;IAEA,MAAM0C,OAAO,GAAGzC,OAAI,CAACD,OAAO,CAACA,OAAO,CAAC;IACrC,IAAIA,OAAO,KAAK0C,OAAO,EAAE;IACzB1C,OAAO,GAAG0C,OAAO;EACnB;EAEA,OAAO,IAAI;AACb;AAEO,UAAUC,kBAAkBA,CACjCC,WAA4B,EAC5BC,OAAe,EACfC,MAAkC,EACT;EACzB,IAAIC,MAAM,GAAG,IAAI;EACjB,IAAIX,MAAM,GAAG,IAAI;EAEjB,MAAMpC,OAAO,GAAGC,OAAI,CAACD,OAAO,CAAC4C,WAAW,CAAClD,QAAQ,CAAC;EAElD,KAAK,MAAMsD,GAAG,IAAIJ,WAAW,CAACK,WAAW,EAAE;IACzC,IAAI,CAACF,MAAM,EAAE;MAAA,IAAAG,gBAAA;MACXH,MAAM,GAAG,OAAOI,aAAa,CAC3B/D,yBAAyB,EACzB4D,GAAG,EACHH,OAAO,EACPC,MAAM,EACN,EAAAI,gBAAA,GAAAN,WAAW,CAACQ,GAAG,qBAAfF,gBAAA,CAAiBlD,OAAO,MAAKgD,GAAG,GAC5BjC,oBAAoB,CAAC6B,WAAW,CAACQ,GAAG,CAAC,GACrC,IAAI,CACT;IACH;IAEA,IAAI,CAAChB,MAAM,EAAE;MACX,MAAMiB,SAAS,GAAGpD,OAAI,CAACwC,IAAI,CAACO,GAAG,EAAE3D,oBAAoB,CAAC;MACtD+C,MAAM,GAAG,OAAOV,gBAAgB,CAAC2B,SAAS,CAAC;MAE3C,IAAIjB,MAAM,EAAE;QACVpD,KAAK,CAAC,0BAA0B,EAAEoD,MAAM,CAAC1C,QAAQ,EAAEM,OAAO,CAAC;MAC7D;IACF;EACF;EAEA,OAAO;IAAE+C,MAAM;IAAEX;EAAO,CAAC;AAC3B;AAEO,SAASkB,cAAcA,CAC5BtD,OAAe,EACf6C,OAAe,EACfC,MAAkC,EACN;EAC5B,OAAOK,aAAa,CAACjE,qBAAqB,EAAEc,OAAO,EAAE6C,OAAO,EAAEC,MAAM,CAAC;AACvE;AAEA,UAAUK,aAAaA,CACrBI,KAAe,EACfvD,OAAe,EACf6C,OAAe,EACfC,MAAkC,EAClCU,cAAiC,GAAG,IAAI,EACZ;EAC5B,MAAMC,OAAO,GAAG,OAAOC,UAAO,CAACC,GAAG,CAChCJ,KAAK,CAACzB,GAAG,CAACU,QAAQ,IAChBoB,UAAU,CAAC3D,OAAI,CAACwC,IAAI,CAACzC,OAAO,EAAEwC,QAAQ,CAAC,EAAEK,OAAO,EAAEC,MAAM,CAAC,CAC1D,CACF;EACD,MAAMC,MAAM,GAAGU,OAAO,CAACI,MAAM,CAAC,CAACL,cAAiC,EAAET,MAAM,KAAK;IAC3E,IAAIA,MAAM,IAAIS,cAAc,EAAE;MAC5B,MAAM,IAAI7C,oBAAW,CAClB,0DAAyD,GACvD,MAAKV,OAAI,CAAC6D,QAAQ,CAACN,cAAc,CAAC9D,QAAQ,CAAE,IAAG,GAC/C,MAAKqD,MAAM,CAACrD,QAAS,IAAG,GACxB,QAAOM,OAAQ,EAAC,CACpB;IACH;IAEA,OAAO+C,MAAM,IAAIS,cAAc;EACjC,CAAC,EAAEA,cAAc,CAAC;EAElB,IAAIT,MAAM,EAAE;IACV/D,KAAK,CAAC,iCAAiC,EAAE+D,MAAM,CAACrD,QAAQ,EAAEM,OAAO,CAAC;EACpE;EACA,OAAO+C,MAAM;AACf;AAEO,UAAUgB,UAAUA,CACzBC,IAAY,EACZhE,OAAe,EACf6C,OAAe,EACfC,MAAkC,EACb;EACrB,MAAMpD,QAAQ,GAAG,GAAAuE,CAAA,EAAAC,CAAA,MAAAD,CAAA,GAAAA,CAAA,CAAApC,KAAA,OAAAqC,CAAA,GAAAA,CAAA,CAAArC,KAAA,QAAAoC,CAAA,OAAAC,CAAA,OAAAD,CAAA,OAAAC,CAAA,QAAAD,CAAA,QAAAC,CAAA,MAAAC,OAAA,CAAAC,QAAA,CAAAC,IAAA,WAAAlG,OAAA,CAAAmG,OAAA,IAAAC,CAAA;IAAAC,KAAA,GAAAC,CAAA;EAAA,GAAAC,CAAA,GAAAvG,OAAA;IAAA,IAAAwG,CAAA,GAAAD,CAAA,CAAAE,SAAA,CAAAL,CAAA,EAAAG,CAAA,CAAAG,gBAAA,CAAAJ,CAAA,EAAAK,MAAA,CAAAL,CAAA;IAAA,IAAAE,CAAA,SAAAA,CAAA;IAAAA,CAAA,OAAAI,KAAA,2BAAAR,CAAA;IAAAI,CAAA,CAAAK,IAAA;IAAA,MAAAL,CAAA;EAAA,GAAgBX,IAAI,EAAE;IAAEQ,KAAK,EAAE,CAACxE,OAAO;EAAE,CAAC,CAAC;EAE5D,MAAMiF,IAAI,GAAG,OAAOrB,UAAU,CAAClE,QAAQ,EAAEmD,OAAO,EAAEC,MAAM,CAAC;EACzD,IAAI,CAACmC,IAAI,EAAE;IACT,MAAM,IAAItE,oBAAW,CAClB,4CAA2C,EAC5CjB,QAAQ,CACT;EACH;EAEAV,KAAK,CAAC,2BAA2B,EAAEgF,IAAI,EAAEhE,OAAO,CAAC;EACjD,OAAOiF,IAAI;AACb;AAMA,SAASrB,UAAUA,CACjBlE,QAAgB,EAChBmD,OAAe,EACfC,MAAkC,EACN;EAC5B,MAAMoC,GAAG,GAAGjF,OAAI,CAACkF,OAAO,CAACzF,QAAQ,CAAC;EAClC,QAAQwF,GAAG;IACT,KAAK,KAAK;IACV,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;MACT,OAAO1F,cAAc,CAACE,QAAQ,EAAE;QAAEmD,OAAO;QAAEC;MAAO,CAAC,CAAC;IACtD;MACE,OAAO3B,eAAe,CAACzB,QAAQ,CAAC;EAAC;AAEvC;AAEO,UAAU0F,qBAAqBA,CACpCpF,OAAe,EACS;EACxB,MAAMqF,UAAU,GAAGlB,OAAO,CAACmB,GAAG,CAACC,qBAAqB;EACpD,IAAIF,UAAU,IAAI,IAAI,EAAE;IACtB,MAAMG,YAAY,GAAGvF,OAAI,CAACqE,OAAO,CAACtE,OAAO,EAAEqF,UAAU,CAAC;IACtD,MAAMI,KAAK,GAAG,OAAO3G,EAAE,CAAC4G,IAAI,CAACF,YAAY,CAAC;IAC1C,IAAI,CAACC,KAAK,CAACE,MAAM,EAAE,EAAE;MACnB,MAAM,IAAIZ,KAAK,CACZ,GAAES,YAAa,sFAAqF,CACtG;IACH;IACA,OAAOA,YAAY;EACrB;EACA,OAAO,IAAI;AACb;AAEA,SAAS1E,gBAAgBA,CAACpB,QAAgB,EAAS;EACjD,MAAM,IAAIiB,oBAAW,CAClB;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,EACCjB,QAAQ,CACT;AACH;AAAC"}