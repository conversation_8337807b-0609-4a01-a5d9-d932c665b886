import {
  addFunction,
  functionRunner,
  runFunctionWithArgs,
} from '../functionRunner';
describe('Function runner', () => {
  test('validate input and dependencies', async () => {
    const functionDefinition = `
    return {input,dependencies};
    `;
    const dependencies = { dependencies: 'dependencies' };
    const input = { input: 'input' };
    const result = await functionRunner({
      args: [input, dependencies],
      functionDefinition,
    });
    expect(result).toEqual({ dependencies, input });
  });
});
describe('Function Store', () => {
  test('addFunction', async () => {
    const functionDefinition = `
        return {input,dependencies};
        `;
    const dependencies = { dependencies: 'dependencies' };
    const input = { input: 'input' };
    addFunction('test', functionDefinition);
    const result = await runFunctionWithArgs({
      args: [input, dependencies],
      functionName: 'test',
    });
    expect(result).toEqual({ dependencies, input });
  });
});
