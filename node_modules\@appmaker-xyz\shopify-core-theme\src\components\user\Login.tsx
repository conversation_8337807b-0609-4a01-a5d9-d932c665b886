import React from 'react';
import { StyleSheet, KeyboardAvoidingView, Platform } from 'react-native';
import { Layout, ThemeText, AppTouchable, Button } from '@appmaker-xyz/ui';
import { useLogin, useNavigationActions } from '@appmaker-xyz/shopify';
import SocialLogin from './SocialLogin';
import { InputFormItem } from './components/InputFormItem';

const Login = () => {
  const { control, isLoading, submitLogin, socialButtons, setFocus } = useLogin(
    {
      defaultValues: {},
    },
  );
  const shopifyActions = useNavigationActions();
  return (
    <KeyboardAvoidingView keyboardVerticalOffset={80} behavior={'padding'}>
      <Layout style={styles.container}>
        <InputFormItem
          autoFocus={true}
          control={control}
          name="email"
          label={'Email'}
          style={styles.input}
          onSubmitEditing={() => setFocus('password')}
          returnKeyType="next"
        />
        <InputFormItem
          control={control}
          name="password"
          label={'Password'}
          style={styles.input}
          secureTextEntry={true}
          onSubmitEditing={submitLogin}
          returnKeyType="done"
        />
        <AppTouchable
          style={styles.forgotButton}
          onPress={shopifyActions.openResetPassword}>
          <ThemeText size="sm" color="#868E96">
            Forgot Password?
          </ThemeText>
        </AppTouchable>
        <Button
          title="Login"
          onPress={submitLogin}
          isLoading={isLoading}
          activityIndicatorColor="#FFFFFF"
          size="md"
          color="#1E232C"
        />
        <SocialLogin socialButtons={socialButtons} />
        <Layout style={styles.signUpButton}>
          <AppTouchable
            onPress={shopifyActions.openRegister}
            style={styles.signupTextContainer}>
            <ThemeText color="#1E232C">Don't have an account?</ThemeText>
            <ThemeText
              color="#427FD1"
              fontFamily="medium"
              style={styles.signupText}>
              Sign up
            </ThemeText>
          </AppTouchable>
        </Layout>
      </Layout>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {},
  input: {
    backgroundColor: '#E8ECF4',
    borderRadius: 6,
    overflow: 'hidden',
    position: 'relative',
  },
  eyeIcon: {
    position: 'absolute',
    right: 12,
    top: 10,
  },
  forgotButton: {
    alignSelf: 'flex-end',
    marginBottom: 16,
    marginTop: -6,
  },
  signUpButton: {
    marginTop: 32,
    alignItems: 'center',
  },
  signupTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  signupText: {
    marginLeft: 4,
  },
});

export default Login;
