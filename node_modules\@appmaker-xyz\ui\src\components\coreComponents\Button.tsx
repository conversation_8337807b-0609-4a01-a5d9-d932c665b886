import React from 'react';
import {
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
  View,
} from 'react-native';
import { ThemeText, AppTouchable } from '../atoms';
import { testProps } from '@appmaker-xyz/core';

interface ButtonProps {
  title: string;
  onPress: () => void;
  buttonStyle?: ViewStyle;
  textStyle?: TextStyle;
  isLoading: boolean;
  isOutline: boolean;
  activityIndicatorColor: string;
  size: 'sm' | 'lg' | 'xl' | 'default'; // Replace 'default' with a more specific size if needed
  color: string | undefined; // You can replace 'undefined' with the actual color type
  textColor?: string;
  fontType: string;
  testId?: string;
  disabled?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  buttonStyle,
  textStyle,
  isLoading,
  isOutline,
  activityIndicatorColor,
  size,
  color,
  textColor,
  fontType,
  testId,
  disabled,
  leftIcon,
  rightIcon,
}) => {
  let buttonPaddingStyles: ViewStyle;
  let backgroundColor: string;
  let borderColor: string | undefined;

  switch (size) {
    case 'sm':
      buttonPaddingStyles = styles.buttonSmPadding;
      break;
    case 'lg':
      buttonPaddingStyles = styles.buttonLgPadding;
      break;
    case 'xl':
      buttonPaddingStyles = styles.buttonXlPadding;
      break;
    default:
      buttonPaddingStyles = styles.buttonBasePadding;
  }

  if (isOutline) {
    backgroundColor = 'transparent';
    borderColor = color || 'transparent';
  } else if (color) {
    backgroundColor = color;
  } else {
    backgroundColor = '#000000';
  }

  let buttonTextColor = isOutline ? color : textColor || '#FFFFFF';

  return (
    <AppTouchable
      {...testProps(testId)}
      style={[
        styles.button,
        isOutline ? styles.outlineButton : styles.filledButton,
        buttonPaddingStyles,
        buttonStyle,
        { backgroundColor },
        isOutline ? { borderColor } : undefined,
        disabled ? { opacity: 0.7 } : undefined,
      ]}
      onPress={onPress}
      disabled={isLoading || disabled}>
      {isLoading ? (
        <ActivityIndicator color={activityIndicatorColor || buttonTextColor} />
      ) : (
        <View style={styles.contentContainer}>
          {leftIcon && <View style={styles.iconLeftContainer}>{leftIcon}</View>}
          <ThemeText
            size={size}
            color={buttonTextColor}
            fontFamily={fontType || 'medium'}
            style={textStyle}>
            {title}
          </ThemeText>
          {rightIcon && (
            <View style={styles.iconRightContainer}>{rightIcon}</View>
          )}
        </View>
      )}
    </AppTouchable>
  );
};

const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
  },
  filledButton: {
    backgroundColor: '#007AFF',
  },
  outlineButton: {
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  buttonBasePadding: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 6,
  },
  buttonSmPadding: {
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 5,
  },
  buttonLgPadding: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  buttonXlPadding: {
    paddingVertical: 20,
    paddingHorizontal: 25,
    borderRadius: 10,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconLeftContainer: {
    marginEnd: 5,
  },
  iconRightContainer: {
    marginStart: 5,
  },
});

export default Button;
