import { useCountdown } from '@appmaker-xyz/uikit/src/hooks/useCountdown';
import React from 'react';
import { View } from 'react-native';
import { AppmakerText } from '../../index';

const ExpiredNotice = () => {
  return (
    <View>
      <AppmakerText>Expired!!!</AppmakerText>
      <AppmakerText>Please select a future date and time.</AppmakerText>
    </View>
  );
};

const DateTimeDisplay = ({ value, type, isDanger }) => {
  return (
    <View>
      <AppmakerText>{value}</AppmakerText>
      <AppmakerText>{type}</AppmakerText>
    </View>
  );
};

const ShowCounter = ({ days, hours, minutes, seconds }) => {
  return (
    <View>
      <DateTimeDisplay value={days} type={'Days'} isDanger={days <= 3} />
      <AppmakerText>:</AppmakerText>
      <DateTimeDisplay value={hours} type={'Hours'} isDanger={false} />
      <AppmakerText>:</AppmakerText>
      <DateTimeDisplay value={minutes} type={'Mins'} isDanger={false} />
      <AppmakerText>:</AppmakerText>
      <DateTimeDisplay value={seconds} type={'Seconds'} isDanger={false} />
    </View>
  );
};

const CountdownTimer = ({ attributes }) => {
  const { targetDate } = attributes;
  const [days, hours, minutes, seconds] = useCountdown(targetDate);

  if (days + hours + minutes + seconds <= 0) {
    return <ExpiredNotice />;
  } else {
    return (
      <ShowCounter
        days={days}
        hours={hours}
        minutes={minutes}
        seconds={seconds}
      />
    );
  }
};
export { CountdownTimer };

export default function CountdownTimerDemo() {
  const THREE_DAYS_IN_MS = 3 * 24 * 60 * 60 * 1000;
  const NOW_IN_MS = new Date().getTime();

  const dateTimeAfterThreeDays = NOW_IN_MS + THREE_DAYS_IN_MS;

  return (
    <View>
      <AppmakerText>Countdown Timer</AppmakerText>
      <CountdownTimer
        attributes={{
          targetDate: dateTimeAfterThreeDays,
          showExpiredNotice: true,
          // expi
        }}
      />
    </View>
  );
}
