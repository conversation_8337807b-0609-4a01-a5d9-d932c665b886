const page = {
  title: 'Apply Coupon',
  attributes: {
    // headerShown: false,
    rootContainerStyle: {
      backgroundColor: '#FFFFFF',
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      paddingHorizontal: -12,
      paddingTop: 12,
    },
  },
  blocks: [
    {
      name: 'appmaker/CartCoupon',
      attributes: {
        __display:
          '{{checkIfTrueFalse(plugins.shopify.settings.show_apply_coupon)}}',
        defaultEditMode: true,
        couponDiscounted: '{{blockData.discountCodes}}', // to be done

        __appmakerStylesClassName: 'cartCouponCustomStyle',
      },
    },
    {
      name: 'appmaker/blocksView',
      attributes: {
        headerShown: false,
        rootContainerStyle: {
          marginTop: 12,
          paddingHorizontal: 12,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/text',
          attributes: {
            content: 'Available Coupons',
            category: 'bodyParagraph',
            status: 'demiDark',
          },
        },
        {
          name: 'appmaker/coupon-block',
          attributes: {
            couponCode: 'j9pgpv93dhc6fyhc',
            description: '10000 INR gift card',
            appmakerAction: {
              action: 'CHECKOUT_APPLY_GIFT_CARD',
              params: {
                couponCode: 'j9pgpv93dhc6fyhc',
              },
            },
          },
        },
      ],
    },
  ],
};
export default page;
