import { appSettings } from '@appmaker-xyz/core';
const page = {
  blocks: [
    {
      name: 'appmaker/CartCard',
      contextValues: true,
      attributes: {
        product: '{{blockItem.node}}',
        salePrice:
          '{{currencyHelper(blockItem.node.variant.price.amount,blockItem.node.variant.price.currencyCode)}}',
        title: '{{blockItem.node.title }}',
        count: '{{parseFloat(blockItem.node.quantity)}}',
        featureImg: '{{blockItem.node.variant.image.src}}',
        variation:
          '{{blockItem.node.variant.title == "Default Title" ? "" : blockItem.node.variant.title }}',
        hasPages: false,
        dataSource: {
          responseType: 'replace',
          repeatable: 'Yes',
          attributes: {
            params: '{{blockData.lineItems.edges}}',
          },
          source: 'variables',
          repeatItem: 'DataVariable',
        },
      },
    },
    {
      name: 'appmaker/CartCoupon',
      attributes: {
        __display: !appSettings.getOptionAsBoolean('hide_cart_coupon'),
        couponDiscounted: '{{blockData.discountApplications.edges}}',
      },
    },
  ],

  fixedFooter: {
    clientId: 'checkout-sumbit',
    name: 'appmaker/checkout-button',
    attributes: {
      show: true,
      viewCartText: 'Checkout',
      itemCount: '{{blockData.lineItems.edges.length}}',
      totalPrice:
        '{{currencyHelper(blockItem.lineItemsSubtotalPrice.amount,blockItem.lineItemsSubtotalPrice.currencyCode)}}',
      appmakerAction: {
        action: 'OPEN_CHECKOUT',
      },
    },
  },

  dataSource: {
    source: 'shopify',
    attributes: {
      mapping: {
        items: 'data.data.node',
      },
      methodName: 'checkoutById',
      params: '{{appStorageState.checkout.id}}',
    },
    dependencies: {
      appStorageState: ['checkout'],
    },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
  },
  attributes: {
    showLoadingTillData: true,
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      // paddingHorizontal: 16,
    },
  },
  title: 'Cart',
};
export default page;
