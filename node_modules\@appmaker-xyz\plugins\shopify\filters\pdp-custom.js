import { appmaker } from '@appmaker-xyz/core';

// appmaker
//find index of block
function findBlockIndex(blocks, clientId) {
  return blocks.findIndex((block) => block.clientId === clientId);
}
const vendorBlock = {
  clientId: 'cusotm-vendor-block',
  name: 'appmaker/table-cell',
  attributes: {
    title: 'Vendor',
    value: '{{blockItem.node.vendor}}',
  },
};
const skuBlock = {
  name: 'appmaker/text',
  clientId: 'custom-sku-block',
  attributes: {
    content: 'SKU - {{pageState.variant.node.sku}}',
    category: 'highlighter1',
    status: 'demiDark',
    style: {
      backgroundColor: '#ffffff',
      paddingHorizontal: 12,
      paddingVertical: 2,
    },
  },
};

export function addWidgetinProductDetailPage(settings) {
  // Adding extra blocks
  appmaker.addFilter(
    'inapp-page-data-response',
    'vendor-block',
    (data, { pageId }) => {
      // console.log('settings', JSON.stringify(settings, null, 2));
      if (pageId === 'productDetail') {
        if (settings.show_vendor_pdp === '1') {
          const clientId = 'product-data';
          const index = findBlockIndex(data.blocks, clientId);
          data.blocks[index].attributes.brandName = '{{blockItem.node.vendor}}';
        }
        if (settings?.showSKUonPDP === '1') {
          const clientId = 'slot-before-product-counter';
          const index = findBlockIndex(data.blocks, clientId);
          const currentBlockIndex = findBlockIndex(
            data.blocks,
            skuBlock.clientId,
          );
          if (currentBlockIndex === -1) {
            data.blocks.splice(index, 0, skuBlock);
          }
        }
      }
      return data;
    },
  );
}
