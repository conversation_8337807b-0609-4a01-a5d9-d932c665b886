/* eslint-disable react-native/no-inline-styles */
import { View, Platform } from 'react-native';
import React, { useEffect, useState } from 'react';
import { ActionBar } from '@appmaker-xyz/ui';
import { usePageState } from '@appmaker-xyz/core';
import { styles } from '@appmaker-xyz/shopify';
const { color, spacing } = styles;
export default function AppleLoginComponent({ attributes, onAction }) {
  const appleLoginLoading = usePageState((state) => state.appleLoginLoading);
  const [buttonLoading, setButtonLoading] = useState(false);
  useEffect(() => {
    setButtonLoading(appleLoginLoading);
  }, [appleLoginLoading]);
  return (
    <ActionBar
      onPress={() => {
        onAction({
          action: 'LOGIN_USER_VIA_APPLE',
        });
      }}
      attributes={{
        __display: Platform.OS === 'ios',
        featureImg:
          'https://storage.googleapis.com/site-cdn.appmaker.xyz/2022/06/2513ae19-image-4.png',
        imageResize: 'contain',
        title: 'Continue with Apple',
        appmakerAction: {
          action: 'LOGIN_USER_VIA_APPLE',
        },
        loading: buttonLoading,
        fontColor: '#ffffff',
        containerStyle: {
          backgroundColor: '#000000',
          marginHorizontal: spacing.base,
          marginBottom: spacing.mini,
          borderRadius: 0,
        },
      }}
    />
  );
}
