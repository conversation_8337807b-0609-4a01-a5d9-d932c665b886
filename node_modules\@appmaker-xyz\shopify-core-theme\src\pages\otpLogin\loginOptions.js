const login = {
  parentID: 'LoginOptions',
  id: 'LoginOptions',
  title: '',
  blocks: [
    {
      name: 'appmaker/remoteImage',
      attributes: {
        name: 'LOGIN_LOGO',
        resizeMode: 'contain',
        style: {
          width: 200,
          height: 100,
          alignSelf: 'center',
          marginTop: 24,
          marginBottom: 18,
        },
      },
      innerBlocks: [],
    },
    {
      name: 'appmaker/otp-login',
      clientId: 'social-login',
      attributes: {
        otpDigitCount:
          "{{plugins['lucent-simple-otp'].settings.total_otp_digit_count}}",
        phoneDigitCount:
          "{{plugins['lucent-simple-otp'].settings.phone_number_digit_count}}",
      },
      innerBlocks: [],
    },
    {
      name: 'appmaker/layout',
      clientId: 'login-layout',
      attributes: {
        style: {
          width: '100%',
        },
      },
      innerBlocks: [],
    },
  ],
  attributes: {
    insideSafeAreaView: true,
    backgroundColor: '#ffffff',
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
};

export default login;
