import ICONS from '../../ToolBarIcons';

const postTitleList = {
  title: 'Blog Posts Category',
  blocks: [
    {
      clientId: 'my-account-menu',
      name: 'appmaker/actionbar',
      attributes: {
        title: '{{blockItem.node.title}}',
        appmakerAction: {
          params: {
            id: '{{blockItem.node.id}}',
            title: '{{blockItem.node.title}}',
          },
          action: 'OPEN_BLOG_LIST',
        },
        dataSource: {
          source: 'shopify',
          attributes: {
            mapping: {
              items: 'data.data.blogs.edges',
            },
            methodName: 'posts',
            params: '{{currentAction.params}}',
          },
          repeatable: 'Yes',
          repeatItem: 'DataSource',
        },
      },
    },
    // {
    //   attributes: {
    //     id: '{{blockItem.node.id}}',
    //     hasPages: true,
    //     appmakerAction: {
    //       pageId: 'productDetail',
    //       params: {
    //         pageData: '{{blockItem}}',
    //       },
    //       action: 'OPEN_INAPP_PAGE',
    //     },
    //     dataSource: {
    //       source: 'shopify',
    //       attributes: {
    //         mapping: {
    //           items: 'data.data.products.edges',
    //         },
    //         methodName: 'products',
    //         params: '{{currentAction.params}}',
    //       },
    //       repeatable: 'Yes',
    //       repeatItem: 'DataSource',
    //     },
    //     title: '{{blockItem.node.title }}',
    //     // featureImg: '{{blockItem.node.image.src}}',
    //     // htmlExcerpt: '{{blockItem.node.excerptHtml}}',
    //     // contentHtml: '{{blockItem.node.contentHtml}}',
    //     blockItem: '{{blockItem}}',
    //   },
    //   name: 'appmaker/post-item',
    //   innerBlocks: [],
    //   clientId: '973a144a-2b29-4bb3-869f-467f16f37a1c',
    //   isValid: true,
    // },
  ],
  _id: 'postList',
  uid: 'odRhv94hf4S52SysvvNoxPyRx682',
  dataSource: {},
  toolBarItems: [ICONS.SEARCH, ICONS.WISHLIST, ICONS.CART],
};
export default postTitleList;
