import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import {
  Layout,
  ThemeText,
  AppTouchable,
  Badge,
  TableCell,
} from '@appmaker-xyz/ui';
import Icon from 'react-native-vector-icons/Feather';
import Modal from 'react-native-modal';
import { appmaker } from '@appmaker-xyz/core';
import { SlotBlock, useProductDetail } from '@appmaker-xyz/shopify';
import { testProps } from '@appmaker-xyz/core';

const ProductData = (props) => {
  const { attributes, clientId } = props;
  const {
    priceSale = '0',
    currency_symbol,
    regular_price_value,
    price_value,
    vendorName,
    stockPhrase,
    saved_amount,
    priceDetailModal = true,
    customStyles,
    brandName,
    __appmakerCustomStyles = {},
  } = attributes;
  const {
    title,
    regularPrice,
    salePercentage,
    tax_included_text = false,
    onSale,
    salePrice,
    average_rating,
    currentVariantInStock: in_stock,
    savedAmount,
    product,
    preOrderEnabled,
  } = useProductDetail({ attributes, props, clientId });
  const [visible, setVisible] = useState(false);

  let toggle = () => setVisible(!visible);
  const ratingBadgeColor = () => {
    let rating = average_rating;
    switch (true) {
      case rating >= '3.5':
        return 'success';
      case rating >= '2.5':
        return 'warining';
      default:
        return 'dark';
    }
  };
  // var saved_amount = Math.abs((regular_price_value - price_value).toFixed(2));
  const canShowSavedText =
    regular_price_value &&
    price_value &&
    saved_amount != 0 &&
    saved_amount != '';

  const savedInitialText = 'Total savings for this product is';
  const displayPriceDetailModal =
    priceDetailModal && priceDetailModal != 'false';
  const savedAmountText = ` ${
    savedAmount ? savedAmount : ''
  } (${salePercentage} OFF) `;
  const ReviewsComp = appmaker.applyFilters(
    'appmaker-product-data-reviews',
    () => null,
  );
  return (
    <Layout style={styles.productDetails}>
      <Layout style={styles.metaData}>
        <Layout flexDirection="row" alignItems="center">
          {in_stock != 'hide' ? (
            <>
              {brandName ? (
                <ThemeText size="sm" fontFamily="bold" style={styles.brandName}>
                  {brandName}
                </ThemeText>
              ) : null}
              {preOrderEnabled ? null : (
                <ThemeText
                  testId={'stock-status'}
                  clientId={`${clientId}-stock-text`}
                  color={
                    customStyles?.brandColor?.primary ||
                    (in_stock === false
                    ? __appmakerCustomStyles?.product_data?.out_of_stock?.color
                    : __appmakerCustomStyles?.product_data?.in_stock?.color) ||
                    (in_stock === false ? '#DC2626' : '#059669')
                  }>
                  {in_stock === false ? 'Out of Stock' : 'In-Stock'}
                </ThemeText>
              )}
            </>
          ) : null}
          {stockPhrase ? (
            <ThemeText testId={'stock-phrase'} color="#DC2626" size="sm">
              ({stockPhrase})
            </ThemeText>
          ) : null}
        </Layout>
        {ReviewsComp ? (
          <ReviewsComp
            from="product-data"
            pageDispatch={props?.pageDispatch}
            attributes={attributes}
          />
        ) : null}

        {average_rating && average_rating > 0 ? (
          <Badge
            testId={'average_rating'}
            text={average_rating}
            status={ratingBadgeColor()}
            customStyles={
              customStyles?.reviewBadgeStyle ||
              __appmakerCustomStyles?.product_data?.rating_badge
            }
            iconName="star"
          />
        ) : null}
      </Layout>
      <ThemeText
        testId={'product-title'}
        clientId={`${clientId}-product-name`}
        size="md"
        color="#1B1B1B"
        style={styles.title}>
        {title || product.node.title}
      </ThemeText>
      <AppTouchable
        {...testProps('price-detail-button')}
        style={styles.priceData}
        onPress={() => onSale && displayPriceDetailModal && toggle()}>
        <ThemeText
          testId={'sale-price'}
          clientId={`${clientId}-sale-price`}
          size="lg"
          fontFamily="bold">
          {salePrice}
        </ThemeText>
        {onSale && !!priceSale ? (
          <ThemeText
            testId={'regular-price'}
            clientId={`${clientId}-regular-price`}
            size="sm"
            style={styles.strikePrice}
            color={
              __appmakerCustomStyles?.product_data?.strike_price?.color ||
              '#A9AEB7'
            }>
            {regularPrice}
          </ThemeText>
        ) : null}
        {onSale ? (
          <ThemeText
            testId={'sale-percentage'}
            clientId={`${clientId}-sale-percentage`}
            size="sm"
            color={
              (customStyles ? customStyles?.brandColor?.secondary : null) ||
              __appmakerCustomStyles?.product_data?.offer_percent?.color ||
              '#16A34A'
            }
            style={styles.offer}>
            {salePercentage}{' '}
            {displayPriceDetailModal ? (
              <Icon
                name="info"
                style={__appmakerCustomStyles?.product_data?.more_info_icon}
              />
            ) : null}
          </ThemeText>
        ) : null}
      </AppTouchable>
      <SlotBlock slotId="pdp-below-price" {...props} />
      {tax_included_text && tax_included_text !== '' ? (
        <ThemeText testId={'tax-included'} size={11} color="#A9AEB7">
          {tax_included_text}
        </ThemeText>
      ) : null}
      <Modal
        isVisible={visible}
        onSwipeComplete={toggle}
        onBackButtonPress={toggle}
        backdropTransitionOutTiming={0}
        onBackdropPress={toggle}
        style={styles.modal}>
        <Layout style={styles.modalBody}>
          <Layout style={styles.modalHeader}>
            <Layout>
              <ThemeText size="lg" fontFamily="medium" color="#1B1B1B">
                Price details
              </ThemeText>
            </Layout>
            <AppTouchable {...testProps('price-detail-close')} onPress={toggle}>
              <Icon name="x" size={18} />
            </AppTouchable>
          </Layout>
          <Layout style={styles.modalContent}>
            {regularPrice ? (
              <TableCell
                attributes={{
                  title: 'Maximum Retail Price',
                  subTitle: 'Including all Taxes',
                  value: regularPrice,
                }}
              />
            ) : null}
            {onSale ? (
              <TableCell
                attributes={{ title: 'Selling Price', value: salePrice }}
              />
            ) : null}
            {regularPrice ? (
              <Layout style={styles.savedPriceData}>
                <ThemeText
                  size="sm"
                  color={
                    __appmakerCustomStyles?.product_data?.offer_percent
                      ?.color || '#16A34A'
                  }>
                  {savedInitialText}{' '}
                </ThemeText>
                <ThemeText
                  size="sm"
                  html={true}
                  color={
                    __appmakerCustomStyles?.product_data?.offer_percent
                      ?.color || '#16A34A'
                  }>
                  {savedAmountText}
                </ThemeText>
              </Layout>
            ) : null}
          </Layout>
        </Layout>
      </Modal>
      {vendorName && vendorName !== '' ? (
        <ThemeText status="grey" category="bodySubText">
          Sold By:{' '}
          <ThemeText status="primary" category="smallButtonText">
            {vendorName}
          </ThemeText>
        </ThemeText>
      ) : null}
    </Layout>
  );
};

const styles = StyleSheet.create({
  productDetails: {
    padding: 12,
    backgroundColor: '#FFFFFF',
  },
  metaData: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: {
    marginTop: 6,
    marginBottom: 8,
  },
  priceData: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  strikePrice: {
    marginHorizontal: 4,
    textDecorationLine: 'line-through',
  },
  offer: {
    marginHorizontal: 4,
  },
  modal: {
    justifyContent: 'flex-end',
    marginVertical: 0,
    marginHorizontal: 1,
  },
  modalBody: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    borderTopStartRadius: 16,
    borderTopEndRadius: 16,
    flexDirection: 'column',
    maxHeight: '80%',
  },
  modalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E9EDF1',
    paddingBottom: 12,
  },
  modalContent: {
    paddingVertical: 12,
    flexDirection: 'column',
    width: '100%',
  },
  savedPriceData: {
    textAlign: 'center',
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  brandName: {
    marginRight: 6,
    paddingRight: 6,
    borderRightWidth: 1,
    borderRightColor: '#E9EDF1',
  },
});

export default ProductData;
