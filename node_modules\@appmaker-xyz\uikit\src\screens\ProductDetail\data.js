import * as testProduct from './product';

// const product = testProduct.hxp;
const getDetailBlocks = (product) => {
  const widgets = product.product_widgets.map((widget) => {
    if (widget.type === 'reviews') {
      return {
        name: 'appmaker/product-info',
        attributes: {
          blockTitle: widget.title,
          // variationType: 'largeText',
          // accessButton: '',
          // text: variation.name || null,
          productReview: widget.items.data,
        },
      };
    }

    // console.log(widget.content);
    return {
      name: 'appmaker/product-info',
      attributes: {
        blockTitle: widget.title,
        // accessButton: '',
        text: widget.content || null,
      },
    };
  });

  const variations = product.attributes.map((variation) => {
    // console.log(widget.content);

    return {
      name: 'appmaker/product-info',
      contextValues: true,

      attributes: {
        blockTitle: variation.name,
        // variationType: 'largeText',
        // accessButton: '',
        // text: variation.name || null,
        variationKey: variation.id,
        variations: variation.options.map((item) => ({
          id: item.slug,
          variationName: item.name,
        })),
      },
    };
  });

  const detailPage = {
    status: 'active',
    version: 'v1.1',
    blocks: [
      {
        clientId: 'c2b0ef4e-6bc3-4510-a5cc-81fd148a85e6',
        attributes: {},
        innerBlocks: [],
        name: 'appmaker/woocommerce-prodict-detail-listener',
        contextValues: true,
      },

      {
        clientId: 'c2b0ef4e-6bc3-4510-a5cc-81fd148a85e6',
        attributes: {
          imageList: [],
          // index: 3,
          mapValues: {
            imageList: 'images',
          },
          contextValues: {
            index: 'imageIndex',
          },
        },

        innerBlocks: [],
        isValid: true,
        name: 'appmaker/product-image',
      },

      {
        name: 'appmaker/product-data',
        attributes: {
          outOfStock: false,
          // title: productXYZ.title,
          // regularPrice: productXYZ.mrp,
          // salePrice: productXYZ.sellingPrice,
          // salePercentage: '20% OFF',
          // vendorName: productXYZ.seller,
          mapValues: {
            title: 'name',
            regularPrice: 'regular_price_display',
            outOfStock: 'in_stock',
            salePercentage: 'sale_percentage',
            salePrice: 'sale_price_display',
            vendorName: 'vendorName',
            average_rating: 'average_rating',
          },
          contextValues: {
            regularPrice: 'product.id',
          },
        },
        contextValues: true,
      },
      {
        name: 'appmaker/product-info',
        attributes: {
          counter: true,
        },
        contextValues: true,
      },
      ...variations,
      // {
      //   name: 'appmaker/product-info',
      //   attributes: {
      //     blockTitle: 'Select Size (If >14 Items)',
      //     accessButton: 'Size Chart',
      //     variations: productXYZ.variations.size,
      //     mapValues: {
      //       // blockTitle: 'name',
      //     },
      //   },
      // },

      ...widgets,
      // {
      //   name: 'appmaker/product-info',
      //   attributes: {
      //     blockTitle: 'Select Size',
      //     accessButton: 'Size Chart',
      //     variations: productXYZ.variations.size,
      //   },
      // },
      // {
      //   name: 'appmaker/product-info',
      //   attributes: {
      //     blockTitle: 'Select Color',
      //     accessButton: '',
      //     variations: productXYZ.variations.color,
      //   },
      // },
    ],
    fixedFooter: {
      name: 'appmaker/buttons',
      attributes: {
        addCartText: 'Add to Cart',
        buyNowText: 'Buy',
        outOfStock: false,
      },
    },
    type: 'NORMAL',
    attributes: false,
    title: 'Home',
    language: 'default',
    parentID: 'home',
    id: 'home',
  };
  return detailPage;
};
export {getDetailBlocks};
