import React from 'react';
import { StyleSheet, ScrollView, FlatList, SafeAreaView } from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import {
  Layout,
  BlockCard,
  TableCell,
  Button,
  CartCard,
} from '../../../components';

const WooCommerceCart = ({ attributes, onPress, onAction }) => {
  const { products, totalAmount, appmakerAction } = attributes;
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });

  return (
    <SafeAreaView flex={1}>
      <ScrollView flex={1}>
        <FlatList
          data={products}
          renderItem={(item) => (
            <CartCard
              attributes={{ title: item.title, featureImg: item.featureImg }}
            />
          )}
        />
      </ScrollView>
      <Layout style={styles.bottomPart}>
        <BlockCard
          attributes={{
            title: `Total: ${totalAmount}`,
            accessButton: 'Expand',
            expandable: true,
            expanded: false,
            childContainerStyle: { paddingHorizontal: spacing.base },
          }}>
          <TableCell
            attributes={{
              title: 'Cart Total',
              value: 'Rs.2000.00',
            }}
          />
          <TableCell
            attributes={{
              title: 'Coupon Discount',
              value: 'Rs.200.00',
            }}
          />
          <TableCell
            attributes={{
              type: 'total',
              title: 'Amount Payable',
              value: 'Rs.1800.00',
            }}
          />
        </BlockCard>
        <Layout style={styles.checkoutButton}>
          <Layout flex={1}>
            <Button block>Checkout</Button>
          </Layout>
          <Layout flex={1}>
            <Button block status="dark">
              Apple Pay
            </Button>
          </Layout>
        </Layout>
      </Layout>
    </SafeAreaView>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    productCard: {
      backgroundColor: '#ffffff',
      padding: 12,
      borderBottomColor: color.light,
      borderBottomWidth: 1,
    },
    imageStyles: {
      width: 100,
      height: 100,
      borderRadius: 4,
      marginRight: spacing.base,
    },
    cardContent: {
      flex: 1,
      marginBottom: 4,
    },
    metaContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 4,
    },
    metaItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: spacing.base,
    },
    metaIcon: {
      marginRight: 4,
    },
    bottomPart: {
      backgroundColor: '#ffffff',
      borderTopColor: '#E9EDF1',
      borderTopWidth: 1,
    },
    checkoutButton: {
      paddingHorizontal: spacing.base,
      paddingBottom: spacing.base,
      flexDirection: 'row',
    },
  });

export default WooCommerceCart;
