import React, { useState, useEffect } from 'react';
import { applyFilters } from '@appmaker-xyz/core';
import { useApThemeState } from '../../../../../theme/ThemeContext';
import { AppTouchable, Layout, AppmakerText } from '../../../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { allStyles } from './ProductVariations';
import { usePageState } from '@appmaker-xyz/core';
import { ImageBackground } from 'react-native';
import { AppImage } from '@appmaker-xyz/uikit';

export const ScrollBlocks = ({
  attributes,
  clientId,
  onChange,
  testId = '',
}) => {
  let {
    key,
    variation,
    variationType,
    selectedItem,
    disabled,
    __appmakerCustomStyles = {},
  } = attributes;
  const { spacing, color } = useApThemeState();
  // const currentValue = usePageState((state) => state.filter[va]);
  const styles = allStyles({ spacing, color });
  const selected = selectedItem === key ? true : false;
  // console.log(key);
  const variationItemSelectedStyle = [styles.variationItemSelected];

  if (__appmakerCustomStyles) {
    variationItemSelectedStyle.push(
      __appmakerCustomStyles?.variation_block
        ?.variation_item_selected_container,
    );
  }
  useEffect(() => {
    if (attributes.selectedItem != attributes.key && selected) {
      // setSelected(false);
    }
  }, [selectedItem]);
  if (variationType === 'color') {
    let variationColor = variation.toLowerCase();
    return (
      <AppTouchable
        testId={testId}
        clientId={`${clientId}-${key}`}
        key={key}
        style={[styles.colorSelector, { backgroundColor: variationColor }]}
        onPress={() => {
          if (!selected) {
            // setSelected(!selected);
            onChange(attributes);
          }
        }}>
        {selected && (
          <Icon
            name="check"
            size={spacing.lg}
            color={variationColor === 'white' ? color.dark : color.white}
          />
        )}
      </AppTouchable>
    );
  }
  if (variationType === 'image') {
    return (
      <AppTouchable
        testId={testId}
        clientId={`${clientId}-${key}`}
        key={key}
        style={[styles.variationItem, selected && variationItemSelectedStyle]}
        onPress={() => {
          if (!selected) {
            // setSelected(!selected);
            onChange(attributes);
          }
        }}>
        <AppImage uri={'https://picsum.photos/200/300'} style={styles.image} />
      </AppTouchable>
    );
  }
  return (
    <AppTouchable
      testId={testId}
      clientId={`${clientId}-${key}`}
      key={`key-${key}-${selected}`}
      style={[
        styles.variationItem,
        selected && variationItemSelectedStyle,
        disabled && styles.muted,
      ]}
      onPress={() => {
        if (!selected) {
          // setSelected(!selected);
          onChange(attributes);
        }
      }}>
      <AppmakerText
        category="h1SubHeading"
        status={selected ? 'primary' : 'dark'}
        fontColor={
          selected
            ? __appmakerCustomStyles?.variation_block
                ?.variation_item_text_selected?.color
            : '#1B1B1B'
        }
        style={styles.variationText}>
        {applyFilters('product-detail-page-variation-title', variation)}
      </AppmakerText>
      {disabled ? (
        <AppImage
          src={require('./img/cross-line.png')}
          style={styles.muteImage}
        />
      ) : null}
    </AppTouchable>
  );
};
