/**
 * Banner View for inAppView
 */
// import {useImageDimensions} from '@react-native-community/hooks';

import React, { useEffect, useState } from 'react';
import { Image, StyleSheet, TouchableOpacity, View } from 'react-native';
// import AutoHeightImage from 'react-native-auto-height-image';
import { emitEvent, analytics } from '@appmaker-xyz/core';
import { AppmakerImage } from '@appmaker-xyz/react-native';
import { Dimensions } from 'react-native';
import { appSettings, testProps } from '@appmaker-xyz/core';

// const { width } = Dimensions.get('window');

const useOrientation = () => {
  const [screenSize, setScreenSize] = useState({
    screenWidth: Dimensions.get('screen').width,
    screenHeight: Dimensions.get('screen').height,
  });
  // const [screenHeight, setScreenHeight] = useState(
  //   Dimensions.get('screen').width,
  // );

  useEffect(() => {
    Dimensions.addEventListener('change', ({ window: { width, height } }) => {
      setScreenSize({ screenWidth: width, screenHeight: height });
    });
  }, []);

  return screenSize;
};

/**
 * Simple Banner view wrapping an Image and an optional inline title.
 * @param { image, title, height, width } props.
 */
function getImageSize(attributes) {
  const { imageContainerStyle, thumbnail_meta } = attributes;
  if (thumbnail_meta) {
    return thumbnail_meta;
  } else if (imageContainerStyle) {
    return imageContainerStyle;
  } else {
    return {};
  }
}
const Banner = ({ attributes, onPress, onAction, clientId }) => {
  // default image heigh if response doesn't have dimensions
  let newHeight = 200;
  const {
    forceSize,
    parentWidth,
    dimensions,
    action,
    image,
    appmakerAction,
    __appmaker_analytics_parent_id,
    __appmaker_analytics_parent_name,
  } = attributes;
  let { screenWidth } = useOrientation();
  // width of parent component
  let width = parentWidth ? parentWidth : screenWidth;
  let newWidth = forceSize && dimensions?.width ? dimensions?.width : width;
  newWidth = parseInt(newWidth);
  // checks if response has image dimensions
  // check if the heights are null to avoid any possible infinity issue

  const imageUrl = typeof image === 'object' ? image.url : image;

  // const {dimensions: imageDim, loading, error} = useImageDimensions(source);
  // console.log(imageDim, loading, error);
  // checking if there is any config in props
  // and if there any sets primary color as background color
  // return ;
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      emitEvent('bannerClick', {
        action: appmakerAction,
        bannerName: appmakerAction?.title,
        item_id: clientId,
      });
      analytics.track(
        'appmaker_block_click',
        {
          blockType: 'appmaker_banner',
          blockId: clientId,
          blockLabel: attributes?.__appmaker_analytics?.blockLabel,
          ...(image.id && {
            resourceId: attributes?.__appmaker_analytics?.resourceId,
          }),
          ...(__appmaker_analytics_parent_id && {
            parentId: __appmaker_analytics_parent_id,
          }),
          ...(__appmaker_analytics_parent_name && {
            parentName: __appmaker_analytics_parent_name,
          }),
          resourceUrl: imageUrl,
          resourceName: attributes?.__appmaker_analytics?.resourceName,
        },
        {
          blockAttributes: attributes,
          blockClientId: clientId,
          blockAction: appmakerAction,
          __appmaker_analytics_parent_id,
          __appmaker_analytics_parent_name,
          resourceName: attributes?.__appmaker_analytics?.resourceName,
        },
      );
      onAction(appmakerAction);
    };
  }
  const [imageLoading, setImageLoading] = useState(true);
  const imageSize = getImageSize(attributes);
  const { height: imageHeight, width: imageWidth } = imageSize;
  const customHeightWithAspectRatio = (imageHeight / imageWidth) * newWidth;
  const [customImageHeight, setCustomImageHeight] = useState(0);
  // console.log({
  //   imageUrl,
  //   width: newWidth,
  //   height: customHeightWithAspectRatio,
  //   customImageHeight,
  // });
  return (
    <>
      {/* {imageUrl && imageLoading && <ImageLoading />} */}
      <TouchableOpacity
        onPress={onPressHandle}
        {...testProps(`appmaker_banner-${clientId}`)}
        style={attributes.style}
        activeOpacity={0.9}>
        <AppmakerImage
          style={{
            width: newWidth,
            height: customHeightWithAspectRatio || customImageHeight,
          }}
          blockName='appmaker/banner'
          onLoad={(e) => {
            // console.log(
            //   `(${e.nativeEvent.height} / ${e.nativeEvent.width}) * ${newWidth} => ${imageUrl}`,
            // );
            setCustomImageHeight(
              (e.nativeEvent.height / e.nativeEvent.width) * newWidth,
            );
          }}
          source={{
            uri: imageUrl,
            // uri: 'https://assets.myntassets.com/f_webp,w_122,c_limit,fl_progressive,dpr_2.0/assets/images/2022/2/23/c8946606-d520-4ba6-a9df-2e6a6ad70fe91645602339490-Sarees.jpg',
          }}
          resizeMode={'contain'}
        />
      </TouchableOpacity>
    </>
  );
};

// Banner.propTypes = {
//   data: PropTypes.any.isRequired,
//   onClick: PropTypes.func,
//   action: PropTypes.any,
//   config: PropTypes.object,
//   parentWidth: PropTypes.number,
// };

// Banner.defaultProps = {};

export default Banner;
