import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { View, StyleSheet, Pressable } from 'react-native';
import { testProps } from '@appmaker-xyz/core';
import { ThemeText } from '../atoms';

const RadioItem = ({
  label,
  value,
  color,
  selectedValue,
  textStyles,
  onPress,
  testId,
}) => {
  const isActive =
    selectedValue === value ||
    (selectedValue?.id && selectedValue?.id === value?.id);

  const handlePress = () => {
    if (onPress) {
      onPress(value, label);
    }
  };

  const themeColor = color || '#000000';
  const borderStyle = {
    borderColor: isActive ? themeColor : `${themeColor}40`,
  };

  const activeBg = { backgroundColor: themeColor };

  return (
    <Pressable
      {...testProps(testId)}
      style={styles.radioItem}
      onPress={handlePress}>
      <View style={[styles.radioCircle, borderStyle]}>
        {isActive && <View style={[styles.selectedRb, activeBg]} />}
      </View>
      <ThemeText style={[styles.radioText, textStyles]}>{label}</ThemeText>
    </Pressable>
  );
};

// Prop types for RadioItem
RadioItem.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired,
  color: PropTypes.string,
  selectedValue: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  textStyles: PropTypes.object,
  onPress: PropTypes.func.isRequired,
  testId: PropTypes.string,
};

// Default props for RadioItem
RadioItem.defaultProps = {
  color: '#000000',
  textStyles: {},
  testId: '',
};

const Radio = ({
  color,
  options,
  onPress,
  initialSelectedValue,
  textStyles,
}) => {
  const [selectedValue, setSelectedValue] = useState(
    initialSelectedValue || (options.length > 0 && options[0].value),
  );

  const handleSelect = (value, label) => {
    setSelectedValue(value);
    if (onPress) {
      onPress(value, label);
    }
  };

  return (
    <View>
      {options.map((item, id) => (
        <RadioItem
          testId={`radio-option-${id}`}
          key={item.id || id}
          selectedValue={selectedValue}
          {...item}
          color={color}
          textStyles={textStyles}
          onPress={handleSelect}
        />
      ))}
    </View>
  );
};

// Prop types for Radio
Radio.propTypes = {
  color: PropTypes.string,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.object])
        .isRequired,
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    }),
  ).isRequired,
  onPress: PropTypes.func.isRequired,
  initialSelectedValue: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object,
  ]),
  textStyles: PropTypes.object,
};

// Default props for Radio
Radio.defaultProps = {
  color: '#000000',
  initialSelectedValue: null,
  textStyles: {},
};

const styles = StyleSheet.create({
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingVertical: 6,
  },
  radioCircle: {
    height: 20,
    width: 20,
    borderRadius: 100,
    borderWidth: 1.5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedRb: {
    width: 12.5,
    height: 12.5,
    borderRadius: 50,
  },
  radioText: {
    marginLeft: 8,
    fontSize: 16,
    lineHeight: 18,
  },
});

export default Radio;
