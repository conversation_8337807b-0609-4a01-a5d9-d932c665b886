{"version": 3, "names": ["_generated", "require", "matchesPattern", "member", "match", "allowPartial", "isMemberExpression", "parts", "Array", "isArray", "split", "nodes", "node", "object", "push", "property", "length", "i", "j", "value", "isIdentifier", "name", "isStringLiteral", "isThisExpression"], "sources": ["../../src/validators/matchesPattern.ts"], "sourcesContent": ["import {\n  isIdentifier,\n  isMemberExpression,\n  isStringLiteral,\n  isThisExpression,\n} from \"./generated\";\nimport type * as t from \"..\";\n\n/**\n * Determines whether or not the input node `member` matches the\n * input `match`.\n *\n * For example, given the match `React.createClass` it would match the\n * parsed nodes of `React.createClass` and `React[\"createClass\"]`.\n */\nexport default function matchesPattern(\n  member: t.Node | null | undefined,\n  match: string | string[],\n  allowPartial?: boolean,\n): boolean {\n  // not a member expression\n  if (!isMemberExpression(member)) return false;\n\n  const parts = Array.isArray(match) ? match : match.split(\".\");\n  const nodes = [];\n\n  let node;\n  for (node = member; isMemberExpression(node); node = node.object) {\n    nodes.push(node.property);\n  }\n  nodes.push(node);\n\n  if (nodes.length < parts.length) return false;\n  if (!allowPartial && nodes.length > parts.length) return false;\n\n  for (let i = 0, j = nodes.length - 1; i < parts.length; i++, j--) {\n    const node = nodes[j];\n    let value;\n    if (isIdentifier(node)) {\n      value = node.name;\n    } else if (isStringLiteral(node)) {\n      value = node.value;\n    } else if (isThisExpression(node)) {\n      value = \"this\";\n    } else {\n      return false;\n    }\n\n    if (parts[i] !== value) return false;\n  }\n\n  return true;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAee,SAASC,cAAcA,CACpCC,MAAiC,EACjCC,KAAwB,EACxBC,YAAsB,EACb;EAET,IAAI,CAAC,IAAAC,6BAAkB,EAACH,MAAM,CAAC,EAAE,OAAO,KAAK;EAE7C,MAAMI,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,GAAGA,KAAK,GAAGA,KAAK,CAACM,KAAK,CAAC,GAAG,CAAC;EAC7D,MAAMC,KAAK,GAAG,EAAE;EAEhB,IAAIC,IAAI;EACR,KAAKA,IAAI,GAAGT,MAAM,EAAE,IAAAG,6BAAkB,EAACM,IAAI,CAAC,EAAEA,IAAI,GAAGA,IAAI,CAACC,MAAM,EAAE;IAChEF,KAAK,CAACG,IAAI,CAACF,IAAI,CAACG,QAAQ,CAAC;EAC3B;EACAJ,KAAK,CAACG,IAAI,CAACF,IAAI,CAAC;EAEhB,IAAID,KAAK,CAACK,MAAM,GAAGT,KAAK,CAACS,MAAM,EAAE,OAAO,KAAK;EAC7C,IAAI,CAACX,YAAY,IAAIM,KAAK,CAACK,MAAM,GAAGT,KAAK,CAACS,MAAM,EAAE,OAAO,KAAK;EAE9D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGP,KAAK,CAACK,MAAM,GAAG,CAAC,EAAEC,CAAC,GAAGV,KAAK,CAACS,MAAM,EAAEC,CAAC,EAAE,EAAEC,CAAC,EAAE,EAAE;IAChE,MAAMN,IAAI,GAAGD,KAAK,CAACO,CAAC,CAAC;IACrB,IAAIC,KAAK;IACT,IAAI,IAAAC,uBAAY,EAACR,IAAI,CAAC,EAAE;MACtBO,KAAK,GAAGP,IAAI,CAACS,IAAI;IACnB,CAAC,MAAM,IAAI,IAAAC,0BAAe,EAACV,IAAI,CAAC,EAAE;MAChCO,KAAK,GAAGP,IAAI,CAACO,KAAK;IACpB,CAAC,MAAM,IAAI,IAAAI,2BAAgB,EAACX,IAAI,CAAC,EAAE;MACjCO,KAAK,GAAG,MAAM;IAChB,CAAC,MAAM;MACL,OAAO,KAAK;IACd;IAEA,IAAIZ,KAAK,CAACU,CAAC,CAAC,KAAKE,KAAK,EAAE,OAAO,KAAK;EACtC;EAEA,OAAO,IAAI;AACb"}