import { addFilter } from '@appmaker-xyz/core';
import { getAvailableOptions } from './helper/helper';

addFilter(
  'response-tranform-shopify-detail-variations',
  'shopify-product-variations',
  (variations) => {
    const finalItems = variations?.map
      ? variations?.map((attribute) => {
          return {
            name: attribute.name,
            key: attribute.name,
            options: attribute.values.map((option) => ({
              //   value: option,
              id: option,
              variationName: option,
            })),
            defaultValue: attribute.values[0],
          };
        })
      : [];
    return finalItems?.length === 1 && finalItems[0].options.length === 1
      ? []
      : finalItems;
    // return finalItems;
  },
);
addFilter(
  'response-tranform-product-list-save-items',
  'shopify-product-list-save-items',
  (items) => {
    const newItems = {};
    items.map((item) => {
      console.log(item?.node?.id);
      newItems[item?.node?.id] = true;
    });
    return newItems;
  },
);

addFilter(
  'response-tranform-shopify-detail-variations-v2',
  'shopify-product-variations',
  (product) => {
    const availableOptions = getAvailableOptions({
      productData: product,
    });
    const { options } = product.node;
    const finalItems = options?.map
      ? options?.map((attribute) => {
          const defaultValue =
            availableOptions &&
            availableOptions[attribute?.name] &&
            availableOptions[attribute?.name][0];
          return {
            name: attribute.name,
            key: attribute.name,
            options: attribute.values.map((option) => ({
              //   value: option,
              id: option,
              variationName: option,
            })),
            defaultValue,
          };
        })
      : [];
    return finalItems?.length === 1 && finalItems[0].options.length === 1
      ? []
      : finalItems;
    // return finalItems;
  },
);
