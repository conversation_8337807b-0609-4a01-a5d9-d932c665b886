import React from 'react';
import BlockCard from '@appmaker-xyz/uikit/src/components/organisms/card/BlockCard';

const ImageScroller = ({
  attributes,
  BlockItem,
  BlocksView,
  innerBlocks,
  onPress,
  onAction,
  clientId,
}) => {
  const defaultHeight = innerBlocks.length > 0 ? 130 : 0;
  const PARENT_HEIGHT = attributes.customBlockHeight
    ? attributes.customBlockHeight
    : defaultHeight;
  const ENABLE_GAP = attributes?.enableGap;
  const finalInnerBlocks = innerBlocks.map((block) => ({
    ...block,
    attributes: {
      __appmaker_analytics_parent_id: clientId,
      __appmaker_analytics_parent_name:
        attributes?.__appmaker_analytics?.blockLabel,
      ...block.attributes,
      PARENT_HEIGHT,
      ENABLE_GAP,
    },
  }));
  return (
    <BlockCard
      onAction={onAction}
      clientId={`appmaker_imagescroller-${clientId}`}
      attributes={{
        ...attributes,
        title: attributes.showTitle ? attributes.title : '',
        accessButton: attributes.showViewMoreButton ? attributes.ctaText : '',
        childContainerStyle: { height: PARENT_HEIGHT },
        wholeContainerStyle: { marginBottom: 0 },
      }}>
      <BlocksView
        onAction={onAction}
        inAppPage={{
          blocks: finalInnerBlocks,
          attributes: {
            horizontal: true,
          },
        }}
      />
    </BlockCard>
  );
};

export default ImageScroller;
