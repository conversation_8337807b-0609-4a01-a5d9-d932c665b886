{"version": 3, "names": ["literalTemplate", "formatter", "tpl", "opts", "metadata", "names", "buildLiteralData", "arg", "defaultReplacements", "for<PERSON>ach", "replacement", "i", "replacements", "normalizeReplacements", "Object", "keys", "key", "prototype", "hasOwnProperty", "call", "Error", "unwrap", "populatePlaceholders", "assign", "nameSet", "prefix", "result", "buildTemplateCode", "Set", "parseAndBuildMetadata", "code", "parser", "placeholder<PERSON><PERSON><PERSON><PERSON>", "concat", "Array", "from", "placeholder<PERSON><PERSON><PERSON>", "preserveComments", "syntacticPlaceholders", "placeholders", "some", "placeholder", "isDuplicate", "has", "name", "length", "value", "push"], "sources": ["../src/literal.ts"], "sourcesContent": ["import type { Formatter } from \"./formatters\";\nimport type { TemplateReplacements, TemplateOpts } from \"./options\";\nimport { normalizeReplacements } from \"./options\";\nimport parseAndBuildMetadata from \"./parse\";\nimport populatePlaceholders from \"./populate\";\n\nexport default function literalTemplate<T>(\n  formatter: Formatter<T>,\n  tpl: Array<string>,\n  opts: TemplateOpts,\n): (_: Array<unknown>) => (_: unknown) => T {\n  const { metadata, names } = buildLiteralData(formatter, tpl, opts);\n\n  return arg => {\n    const defaultReplacements: TemplateReplacements = {};\n    arg.forEach((replacement, i) => {\n      defaultReplacements[names[i]] = replacement;\n    });\n\n    return (arg: unknown) => {\n      const replacements = normalizeReplacements(arg);\n\n      if (replacements) {\n        Object.keys(replacements).forEach(key => {\n          if (Object.prototype.hasOwnProperty.call(defaultReplacements, key)) {\n            throw new Error(\"Unexpected replacement overlap.\");\n          }\n        });\n      }\n\n      return formatter.unwrap(\n        populatePlaceholders(\n          metadata,\n          replacements\n            ? Object.assign(replacements, defaultReplacements)\n            : defaultReplacements,\n        ),\n      );\n    };\n  };\n}\n\nfunction buildLiteralData<T>(\n  formatter: Formatter<T>,\n  tpl: Array<string>,\n  opts: TemplateOpts,\n) {\n  let names;\n  let nameSet: Set<string>;\n  let metadata;\n  let prefix = \"\";\n\n  do {\n    // If there are cases where the template already contains $0 or any other\n    // matching pattern, we keep adding \"$\" characters until a unique prefix\n    // is found.\n    prefix += \"$\";\n    const result = buildTemplateCode(tpl, prefix);\n\n    names = result.names;\n    nameSet = new Set(names);\n    metadata = parseAndBuildMetadata(formatter, formatter.code(result.code), {\n      parser: opts.parser,\n\n      // Explicitly include our generated names in the whitelist so users never\n      // have to think about whether their placeholder pattern will match.\n      placeholderWhitelist: new Set(\n        result.names.concat(\n          opts.placeholderWhitelist\n            ? Array.from(opts.placeholderWhitelist)\n            : [],\n        ),\n      ),\n      placeholderPattern: opts.placeholderPattern,\n      preserveComments: opts.preserveComments,\n      syntacticPlaceholders: opts.syntacticPlaceholders,\n    });\n  } while (\n    metadata.placeholders.some(\n      placeholder => placeholder.isDuplicate && nameSet.has(placeholder.name),\n    )\n  );\n\n  return { metadata, names };\n}\n\nfunction buildTemplateCode(\n  tpl: Array<string>,\n  prefix: string,\n): { names: Array<string>; code: string } {\n  const names = [];\n\n  let code = tpl[0];\n\n  for (let i = 1; i < tpl.length; i++) {\n    const value = `${prefix}${i - 1}`;\n    names.push(value);\n\n    code += value + tpl[i];\n  }\n\n  return { names, code };\n}\n"], "mappings": ";;;;;;AAEA;AACA;AACA;AAEe,SAASA,eAAe,CACrCC,SAAuB,EACvBC,GAAkB,EAClBC,IAAkB,EACwB;EAC1C,MAAM;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAGC,gBAAgB,CAACL,SAAS,EAAEC,GAAG,EAAEC,IAAI,CAAC;EAElE,OAAOI,GAAG,IAAI;IACZ,MAAMC,mBAAyC,GAAG,CAAC,CAAC;IACpDD,GAAG,CAACE,OAAO,CAAC,CAACC,WAAW,EAAEC,CAAC,KAAK;MAC9BH,mBAAmB,CAACH,KAAK,CAACM,CAAC,CAAC,CAAC,GAAGD,WAAW;IAC7C,CAAC,CAAC;IAEF,OAAQH,GAAY,IAAK;MACvB,MAAMK,YAAY,GAAG,IAAAC,8BAAqB,EAACN,GAAG,CAAC;MAE/C,IAAIK,YAAY,EAAE;QAChBE,MAAM,CAACC,IAAI,CAACH,YAAY,CAAC,CAACH,OAAO,CAACO,GAAG,IAAI;UACvC,IAAIF,MAAM,CAACG,SAAS,CAACC,cAAc,CAACC,IAAI,CAACX,mBAAmB,EAAEQ,GAAG,CAAC,EAAE;YAClE,MAAM,IAAII,KAAK,CAAC,iCAAiC,CAAC;UACpD;QACF,CAAC,CAAC;MACJ;MAEA,OAAOnB,SAAS,CAACoB,MAAM,CACrB,IAAAC,iBAAoB,EAClBlB,QAAQ,EACRQ,YAAY,GACRE,MAAM,CAACS,MAAM,CAACX,YAAY,EAAEJ,mBAAmB,CAAC,GAChDA,mBAAmB,CACxB,CACF;IACH,CAAC;EACH,CAAC;AACH;AAEA,SAASF,gBAAgB,CACvBL,SAAuB,EACvBC,GAAkB,EAClBC,IAAkB,EAClB;EACA,IAAIE,KAAK;EACT,IAAImB,OAAoB;EACxB,IAAIpB,QAAQ;EACZ,IAAIqB,MAAM,GAAG,EAAE;EAEf,GAAG;IAIDA,MAAM,IAAI,GAAG;IACb,MAAMC,MAAM,GAAGC,iBAAiB,CAACzB,GAAG,EAAEuB,MAAM,CAAC;IAE7CpB,KAAK,GAAGqB,MAAM,CAACrB,KAAK;IACpBmB,OAAO,GAAG,IAAII,GAAG,CAACvB,KAAK,CAAC;IACxBD,QAAQ,GAAG,IAAAyB,cAAqB,EAAC5B,SAAS,EAAEA,SAAS,CAAC6B,IAAI,CAACJ,MAAM,CAACI,IAAI,CAAC,EAAE;MACvEC,MAAM,EAAE5B,IAAI,CAAC4B,MAAM;MAInBC,oBAAoB,EAAE,IAAIJ,GAAG,CAC3BF,MAAM,CAACrB,KAAK,CAAC4B,MAAM,CACjB9B,IAAI,CAAC6B,oBAAoB,GACrBE,KAAK,CAACC,IAAI,CAAChC,IAAI,CAAC6B,oBAAoB,CAAC,GACrC,EAAE,CACP,CACF;MACDI,kBAAkB,EAAEjC,IAAI,CAACiC,kBAAkB;MAC3CC,gBAAgB,EAAElC,IAAI,CAACkC,gBAAgB;MACvCC,qBAAqB,EAAEnC,IAAI,CAACmC;IAC9B,CAAC,CAAC;EACJ,CAAC,QACClC,QAAQ,CAACmC,YAAY,CAACC,IAAI,CACxBC,WAAW,IAAIA,WAAW,CAACC,WAAW,IAAIlB,OAAO,CAACmB,GAAG,CAACF,WAAW,CAACG,IAAI,CAAC,CACxE;EAGH,OAAO;IAAExC,QAAQ;IAAEC;EAAM,CAAC;AAC5B;AAEA,SAASsB,iBAAiB,CACxBzB,GAAkB,EAClBuB,MAAc,EAC0B;EACxC,MAAMpB,KAAK,GAAG,EAAE;EAEhB,IAAIyB,IAAI,GAAG5B,GAAG,CAAC,CAAC,CAAC;EAEjB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,GAAG,CAAC2C,MAAM,EAAElC,CAAC,EAAE,EAAE;IACnC,MAAMmC,KAAK,GAAI,GAAErB,MAAO,GAAEd,CAAC,GAAG,CAAE,EAAC;IACjCN,KAAK,CAAC0C,IAAI,CAACD,KAAK,CAAC;IAEjBhB,IAAI,IAAIgB,KAAK,GAAG5C,GAAG,CAACS,CAAC,CAAC;EACxB;EAEA,OAAO;IAAEN,KAAK;IAAEyB;EAAK,CAAC;AACxB"}