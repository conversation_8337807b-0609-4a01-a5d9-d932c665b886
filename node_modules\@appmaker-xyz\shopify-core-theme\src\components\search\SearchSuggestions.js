import React from 'react';
import { StyleSheet } from 'react-native';
import { emitEvent } from '@appmaker-xyz/core';
import { Layout, ThemeText, AppImage, AppTouchable } from '@appmaker-xyz/ui';
import Icon from 'react-native-vector-icons/Feather';
import { ShopifyNavigation } from '@appmaker-xyz/shopify';

const Item = ({ image, title, onPress, html, onPressArrow }) => {
  const tagStyles = {
    mark: { color: '#212121', fontSize: 16 },
    span: { color: '#A1A1AA', fontSize: 16 },
  };
  return (
    <AppTouchable style={styles.item} onPress={onPress}>
      {image ? (
        <AppImage uri={image} style={styles.image} resizeMode="cover" />
      ) : (
        <Icon name="search" size={16} style={styles.icon} color="#898989" />
      )}
      <Layout style={styles.text}>
        <ThemeText
          size="md"
          numberOfLines={1}
          html={html ? true : false}
          customHtmlTagsStyles={tagStyles}>
          {html ? html : title}
        </ThemeText>
      </Layout>
      {image ? null : (
        <Icon
          name="arrow-up-left"
          size={16}
          style={styles.icon}
          color="#898989"
          onPress={onPressArrow}
        />
      )}
    </AppTouchable>
  );
};

const SearchSuggestions = ({ queries, products, setValue }) => {
  return (
    <Layout style={styles.container}>
      {queries?.map((result, index) => (
        <Item
          key={index}
          title={result.text}
          html={result.styledText}
          onPressArrow={() => {
            setValue(result.text);
          }}
          onPress={() => {
            emitEvent('product.search', result.text);
            ShopifyNavigation.openProductSearchResult({
              query: result.text,
              pageTitle: result.text,
            });
          }}
        />
      ))}
      {products?.map((result, index) => (
        <Item
          key={index}
          image={result?.images?.edges?.[0]?.node?.url}
          title={result.title}
          onPress={() =>
            ShopifyNavigation.openProduct({
              handle: result.handle,
            })
          }
        />
      ))}
    </Layout>
  );
};

export default SearchSuggestions;

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  text: {
    flex: 1,
    marginHorizontal: 6,
  },
  image: {
    width: 24,
    height: 24,
    borderRadius: 10,
    borderWidth: 0.5,
    borderColor: '#EFEFEF',
  },
});
