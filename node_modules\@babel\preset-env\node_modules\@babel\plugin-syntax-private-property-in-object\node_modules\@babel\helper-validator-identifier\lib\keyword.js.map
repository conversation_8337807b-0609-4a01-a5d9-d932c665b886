{"version": 3, "names": ["reservedWords", "keyword", "strict", "strictBind", "keywords", "Set", "reservedWordsStrictSet", "reservedWordsStrictBindSet", "isReservedWord", "word", "inModule", "isStrictReservedWord", "has", "isStrictBindOnlyReservedWord", "isStrictBindReservedWord", "isKeyword"], "sources": ["../src/keyword.ts"], "sourcesContent": ["const reservedWords = {\n  keyword: [\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"continue\",\n    \"debugger\",\n    \"default\",\n    \"do\",\n    \"else\",\n    \"finally\",\n    \"for\",\n    \"function\",\n    \"if\",\n    \"return\",\n    \"switch\",\n    \"throw\",\n    \"try\",\n    \"var\",\n    \"const\",\n    \"while\",\n    \"with\",\n    \"new\",\n    \"this\",\n    \"super\",\n    \"class\",\n    \"extends\",\n    \"export\",\n    \"import\",\n    \"null\",\n    \"true\",\n    \"false\",\n    \"in\",\n    \"instanceof\",\n    \"typeof\",\n    \"void\",\n    \"delete\",\n  ],\n  strict: [\n    \"implements\",\n    \"interface\",\n    \"let\",\n    \"package\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"static\",\n    \"yield\",\n  ],\n  strictBind: [\"eval\", \"arguments\"],\n};\nconst keywords = new Set(reservedWords.keyword);\nconst reservedWordsStrictSet = new Set(reservedWords.strict);\nconst reservedWordsStrictBindSet = new Set(reservedWords.strictBind);\n\n/**\n * Checks if word is a reserved word in non-strict mode\n */\nexport function isReservedWord(word: string, inModule: boolean): boolean {\n  return (inModule && word === \"await\") || word === \"enum\";\n}\n\n/**\n * Checks if word is a reserved word in non-binding strict mode\n *\n * Includes non-strict reserved words\n */\nexport function isStrictReservedWord(word: string, inModule: boolean): boolean {\n  return isReservedWord(word, inModule) || reservedWordsStrictSet.has(word);\n}\n\n/**\n * Checks if word is a reserved word in binding strict mode, but it is allowed as\n * a normal identifier.\n */\nexport function isStrictBindOnlyReservedWord(word: string): boolean {\n  return reservedWordsStrictBindSet.has(word);\n}\n\n/**\n * Checks if word is a reserved word in binding strict mode\n *\n * Includes non-strict reserved words and non-binding strict reserved words\n */\nexport function isStrictBindReservedWord(\n  word: string,\n  inModule: boolean,\n): boolean {\n  return (\n    isStrictReservedWord(word, inModule) || isStrictBindOnlyReservedWord(word)\n  );\n}\n\nexport function isKeyword(word: string): boolean {\n  return keywords.has(word);\n}\n"], "mappings": ";;;;;;;;;;AAAA,MAAMA,aAAa,GAAG;EACpBC,OAAO,EAAE,CACP,OADO,EAEP,MAFO,EAGP,OAHO,EAIP,UAJO,EAKP,UALO,EAMP,SANO,EAOP,IAPO,EAQP,MARO,EASP,SATO,EAUP,KAVO,EAWP,UAXO,EAYP,IAZO,EAaP,QAbO,EAcP,QAdO,EAeP,OAfO,EAgBP,KAhBO,EAiBP,KAjBO,EAkBP,OAlBO,EAmBP,OAnBO,EAoBP,MApBO,EAqBP,KArBO,EAsBP,MAtBO,EAuBP,OAvBO,EAwBP,OAxBO,EAyBP,SAzBO,EA0BP,QA1BO,EA2BP,QA3BO,EA4BP,MA5BO,EA6BP,MA7BO,EA8BP,OA9BO,EA+BP,IA/BO,EAgCP,YAhCO,EAiCP,QAjCO,EAkCP,MAlCO,EAmCP,QAnCO,CADW;EAsCpBC,MAAM,EAAE,CACN,YADM,EAEN,WAFM,EAGN,KAHM,EAIN,SAJM,EAKN,SALM,EAMN,WANM,EAON,QAPM,EAQN,QARM,EASN,OATM,CAtCY;EAiDpBC,UAAU,EAAE,CAAC,MAAD,EAAS,WAAT;AAjDQ,CAAtB;AAmDA,MAAMC,QAAQ,GAAG,IAAIC,GAAJ,CAAQL,aAAa,CAACC,OAAtB,CAAjB;AACA,MAAMK,sBAAsB,GAAG,IAAID,GAAJ,CAAQL,aAAa,CAACE,MAAtB,CAA/B;AACA,MAAMK,0BAA0B,GAAG,IAAIF,GAAJ,CAAQL,aAAa,CAACG,UAAtB,CAAnC;;AAKO,SAASK,cAAT,CAAwBC,IAAxB,EAAsCC,QAAtC,EAAkE;EACvE,OAAQA,QAAQ,IAAID,IAAI,KAAK,OAAtB,IAAkCA,IAAI,KAAK,MAAlD;AACD;;AAOM,SAASE,oBAAT,CAA8BF,IAA9B,EAA4CC,QAA5C,EAAwE;EAC7E,OAAOF,cAAc,CAACC,IAAD,EAAOC,QAAP,CAAd,IAAkCJ,sBAAsB,CAACM,GAAvB,CAA2BH,IAA3B,CAAzC;AACD;;AAMM,SAASI,4BAAT,CAAsCJ,IAAtC,EAA6D;EAClE,OAAOF,0BAA0B,CAACK,GAA3B,CAA+BH,IAA/B,CAAP;AACD;;AAOM,SAASK,wBAAT,CACLL,IADK,EAELC,QAFK,EAGI;EACT,OACEC,oBAAoB,CAACF,IAAD,EAAOC,QAAP,CAApB,IAAwCG,4BAA4B,CAACJ,IAAD,CADtE;AAGD;;AAEM,SAASM,SAAT,CAAmBN,IAAnB,EAA0C;EAC/C,OAAOL,QAAQ,CAACQ,GAAT,CAAaH,IAAb,CAAP;AACD"}