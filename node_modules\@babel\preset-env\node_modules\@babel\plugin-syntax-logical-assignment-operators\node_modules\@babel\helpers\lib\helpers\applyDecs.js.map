{"version": 3, "names": ["old_createMetadataMethodsForProperty", "metadataMap", "kind", "property", "decoratorFinishedRef", "getMetadata", "key", "old_assertNotFinished", "old_assertMetadataKey", "metadataForKey", "pub", "public", "priv", "private", "get", "Object", "hasOwnProperty", "call", "constructor", "setMetadata", "value", "Map", "set", "old_convertMetadataMapToFinal", "obj", "parentMetadataMap", "Symbol", "metadata", "for", "metadataKeys", "getOwnPropertySymbols", "length", "i", "metaForKey", "parentMetaForKey", "parentPub", "setPrototypeOf", "privArr", "Array", "from", "values", "parentPriv", "concat", "old_createAddInitializerMethod", "initializers", "addInitializer", "initializer", "old_assertCallable", "push", "old_memberDec", "dec", "name", "desc", "isStatic", "isPrivate", "kindStr", "ctx", "v", "metadataKind", "metadataName", "access", "assign", "fnName", "Error", "TypeError", "fn", "hint", "old_assertValidReturnValue", "type", "undefined", "init", "old_getInit", "console", "warn", "old_applyMemberDec", "ret", "base", "decInfo", "decs", "getOwnPropertyDescriptor", "newValue", "newInit", "instance", "ownInitializers", "originalInitializer", "args", "defineProperty", "old_applyMemberDecs", "Class", "protoMetadataMap", "staticMetadataMap", "decInfos", "protoInitializers", "staticInitializers", "existingProtoNonFields", "existingStaticNonFields", "isArray", "prototype", "existingNonFields", "existingKind", "old_pushInitializers", "old_applyClassDecs", "targetClass", "classDecs", "newClass", "nextNewClass", "applyDecs", "memberDecs"], "sources": ["../../src/helpers/applyDecs.js"], "sourcesContent": ["/* @minVersion 7.17.8 */\n\n/**\n * NOTE: This is an old version of the helper, used for 2021-12 decorators.\n * Updates should be done in applyDecs2203R.js.\n */\n\n/**\n  Enums are used in this file, but not assigned to vars to avoid non-hoistable values\n\n  CONSTRUCTOR = 0;\n  PUBLIC = 1;\n  PRIVATE = 2;\n\n  FIELD = 0;\n  ACCESSOR = 1;\n  METHOD = 2;\n  GETTER = 3;\n  SETTER = 4;\n\n  STATIC = 5;\n\n  CLASS = 10; // only used in assertValidReturnValue\n*/\n\nfunction old_createMetadataMethodsForProperty(\n  metadataMap,\n  kind,\n  property,\n  decoratorFinishedRef\n) {\n  return {\n    getMetadata: function (key) {\n      old_assertNotFinished(decoratorFinishedRef, \"getMetadata\");\n      old_assertMetadataKey(key);\n\n      var metadataForKey = metadataMap[key];\n\n      if (metadataForKey === void 0) return void 0;\n\n      if (kind === 1 /* PUBLIC */) {\n        var pub = metadataForKey.public;\n        if (pub !== void 0) {\n          return pub[property];\n        }\n      } else if (kind === 2 /* PRIVATE */) {\n        var priv = metadataForKey.private;\n        if (priv !== void 0) {\n          return priv.get(property);\n        }\n      } else if (Object.hasOwnProperty.call(metadataForKey, \"constructor\")) {\n        return metadataForKey.constructor;\n      }\n    },\n    setMetadata: function (key, value) {\n      old_assertNotFinished(decoratorFinishedRef, \"setMetadata\");\n      old_assertMetadataKey(key);\n\n      var metadataForKey = metadataMap[key];\n\n      if (metadataForKey === void 0) {\n        metadataForKey = metadataMap[key] = {};\n      }\n\n      if (kind === 1 /* PUBLIC */) {\n        var pub = metadataForKey.public;\n\n        if (pub === void 0) {\n          pub = metadataForKey.public = {};\n        }\n\n        pub[property] = value;\n      } else if (kind === 2 /* PRIVATE */) {\n        var priv = metadataForKey.priv;\n\n        if (priv === void 0) {\n          priv = metadataForKey.private = new Map();\n        }\n\n        priv.set(property, value);\n      } else {\n        metadataForKey.constructor = value;\n      }\n    },\n  };\n}\n\nfunction old_convertMetadataMapToFinal(obj, metadataMap) {\n  var parentMetadataMap = obj[Symbol.metadata || Symbol.for(\"Symbol.metadata\")];\n  var metadataKeys = Object.getOwnPropertySymbols(metadataMap);\n\n  if (metadataKeys.length === 0) return;\n\n  for (var i = 0; i < metadataKeys.length; i++) {\n    var key = metadataKeys[i];\n    var metaForKey = metadataMap[key];\n    var parentMetaForKey = parentMetadataMap ? parentMetadataMap[key] : null;\n\n    var pub = metaForKey.public;\n    var parentPub = parentMetaForKey ? parentMetaForKey.public : null;\n\n    if (pub && parentPub) {\n      Object.setPrototypeOf(pub, parentPub);\n    }\n\n    var priv = metaForKey.private;\n\n    if (priv) {\n      var privArr = Array.from(priv.values());\n      var parentPriv = parentMetaForKey ? parentMetaForKey.private : null;\n\n      if (parentPriv) {\n        privArr = privArr.concat(parentPriv);\n      }\n\n      metaForKey.private = privArr;\n    }\n\n    if (parentMetaForKey) {\n      Object.setPrototypeOf(metaForKey, parentMetaForKey);\n    }\n  }\n\n  if (parentMetadataMap) {\n    Object.setPrototypeOf(metadataMap, parentMetadataMap);\n  }\n\n  obj[Symbol.metadata || Symbol.for(\"Symbol.metadata\")] = metadataMap;\n}\n\nfunction old_createAddInitializerMethod(initializers, decoratorFinishedRef) {\n  return function addInitializer(initializer) {\n    old_assertNotFinished(decoratorFinishedRef, \"addInitializer\");\n    old_assertCallable(initializer, \"An initializer\");\n    initializers.push(initializer);\n  };\n}\n\nfunction old_memberDec(\n  dec,\n  name,\n  desc,\n  metadataMap,\n  initializers,\n  kind,\n  isStatic,\n  isPrivate,\n  value\n) {\n  var kindStr;\n\n  switch (kind) {\n    case 1 /* ACCESSOR */:\n      kindStr = \"accessor\";\n      break;\n    case 2 /* METHOD */:\n      kindStr = \"method\";\n      break;\n    case 3 /* GETTER */:\n      kindStr = \"getter\";\n      break;\n    case 4 /* SETTER */:\n      kindStr = \"setter\";\n      break;\n    default:\n      kindStr = \"field\";\n  }\n\n  var ctx = {\n    kind: kindStr,\n    name: isPrivate ? \"#\" + name : name,\n    isStatic: isStatic,\n    isPrivate: isPrivate,\n  };\n\n  var decoratorFinishedRef = { v: false };\n\n  if (kind !== 0 /* FIELD */) {\n    ctx.addInitializer = old_createAddInitializerMethod(\n      initializers,\n      decoratorFinishedRef\n    );\n  }\n\n  var metadataKind, metadataName;\n\n  if (isPrivate) {\n    metadataKind = 2 /* PRIVATE */;\n    metadataName = Symbol(name);\n\n    var access = {};\n\n    if (kind === 0 /* FIELD */) {\n      access.get = desc.get;\n      access.set = desc.set;\n    } else if (kind === 2 /* METHOD */) {\n      access.get = function () {\n        return desc.value;\n      };\n    } else {\n      // replace with values that will go through the final getter and setter\n      if (kind === 1 /* ACCESSOR */ || kind === 3 /* GETTER */) {\n        access.get = function () {\n          return desc.get.call(this);\n        };\n      }\n\n      if (kind === 1 /* ACCESSOR */ || kind === 4 /* SETTER */) {\n        access.set = function (v) {\n          desc.set.call(this, v);\n        };\n      }\n    }\n\n    ctx.access = access;\n  } else {\n    metadataKind = 1 /* PUBLIC */;\n    metadataName = name;\n  }\n\n  try {\n    return dec(\n      value,\n      Object.assign(\n        ctx,\n        old_createMetadataMethodsForProperty(\n          metadataMap,\n          metadataKind,\n          metadataName,\n          decoratorFinishedRef\n        )\n      )\n    );\n  } finally {\n    decoratorFinishedRef.v = true;\n  }\n}\n\nfunction old_assertNotFinished(decoratorFinishedRef, fnName) {\n  if (decoratorFinishedRef.v) {\n    throw new Error(\n      \"attempted to call \" + fnName + \" after decoration was finished\"\n    );\n  }\n}\n\nfunction old_assertMetadataKey(key) {\n  if (typeof key !== \"symbol\") {\n    throw new TypeError(\"Metadata keys must be symbols, received: \" + key);\n  }\n}\n\nfunction old_assertCallable(fn, hint) {\n  if (typeof fn !== \"function\") {\n    throw new TypeError(hint + \" must be a function\");\n  }\n}\n\nfunction old_assertValidReturnValue(kind, value) {\n  var type = typeof value;\n\n  if (kind === 1 /* ACCESSOR */) {\n    if (type !== \"object\" || value === null) {\n      throw new TypeError(\n        \"accessor decorators must return an object with get, set, or init properties or void 0\"\n      );\n    }\n    if (value.get !== undefined) {\n      old_assertCallable(value.get, \"accessor.get\");\n    }\n    if (value.set !== undefined) {\n      old_assertCallable(value.set, \"accessor.set\");\n    }\n    if (value.init !== undefined) {\n      old_assertCallable(value.init, \"accessor.init\");\n    }\n    if (value.initializer !== undefined) {\n      old_assertCallable(value.initializer, \"accessor.initializer\");\n    }\n  } else if (type !== \"function\") {\n    var hint;\n    if (kind === 0 /* FIELD */) {\n      hint = \"field\";\n    } else if (kind === 10 /* CLASS */) {\n      hint = \"class\";\n    } else {\n      hint = \"method\";\n    }\n    throw new TypeError(hint + \" decorators must return a function or void 0\");\n  }\n}\n\nfunction old_getInit(desc) {\n  var initializer;\n  if (\n    (initializer = desc.init) == null &&\n    (initializer = desc.initializer) &&\n    typeof console !== \"undefined\"\n  ) {\n    console.warn(\".initializer has been renamed to .init as of March 2022\");\n  }\n  return initializer;\n}\n\nfunction old_applyMemberDec(\n  ret,\n  base,\n  decInfo,\n  name,\n  kind,\n  isStatic,\n  isPrivate,\n  metadataMap,\n  initializers\n) {\n  var decs = decInfo[0];\n\n  var desc, initializer, value;\n\n  if (isPrivate) {\n    if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n      desc = {\n        get: decInfo[3],\n        set: decInfo[4],\n      };\n    } else if (kind === 3 /* GETTER */) {\n      desc = {\n        get: decInfo[3],\n      };\n    } else if (kind === 4 /* SETTER */) {\n      desc = {\n        set: decInfo[3],\n      };\n    } else {\n      desc = {\n        value: decInfo[3],\n      };\n    }\n  } else if (kind !== 0 /* FIELD */) {\n    desc = Object.getOwnPropertyDescriptor(base, name);\n  }\n\n  if (kind === 1 /* ACCESSOR */) {\n    value = {\n      get: desc.get,\n      set: desc.set,\n    };\n  } else if (kind === 2 /* METHOD */) {\n    value = desc.value;\n  } else if (kind === 3 /* GETTER */) {\n    value = desc.get;\n  } else if (kind === 4 /* SETTER */) {\n    value = desc.set;\n  }\n\n  var newValue, get, set;\n\n  if (typeof decs === \"function\") {\n    newValue = old_memberDec(\n      decs,\n      name,\n      desc,\n      metadataMap,\n      initializers,\n      kind,\n      isStatic,\n      isPrivate,\n      value\n    );\n\n    if (newValue !== void 0) {\n      old_assertValidReturnValue(kind, newValue);\n\n      if (kind === 0 /* FIELD */) {\n        initializer = newValue;\n      } else if (kind === 1 /* ACCESSOR */) {\n        initializer = old_getInit(newValue);\n        get = newValue.get || value.get;\n        set = newValue.set || value.set;\n\n        value = { get: get, set: set };\n      } else {\n        value = newValue;\n      }\n    }\n  } else {\n    for (var i = decs.length - 1; i >= 0; i--) {\n      var dec = decs[i];\n\n      newValue = old_memberDec(\n        dec,\n        name,\n        desc,\n        metadataMap,\n        initializers,\n        kind,\n        isStatic,\n        isPrivate,\n        value\n      );\n\n      if (newValue !== void 0) {\n        old_assertValidReturnValue(kind, newValue);\n        var newInit;\n\n        if (kind === 0 /* FIELD */) {\n          newInit = newValue;\n        } else if (kind === 1 /* ACCESSOR */) {\n          newInit = old_getInit(newValue);\n          get = newValue.get || value.get;\n          set = newValue.set || value.set;\n\n          value = { get: get, set: set };\n        } else {\n          value = newValue;\n        }\n\n        if (newInit !== void 0) {\n          if (initializer === void 0) {\n            initializer = newInit;\n          } else if (typeof initializer === \"function\") {\n            initializer = [initializer, newInit];\n          } else {\n            initializer.push(newInit);\n          }\n        }\n      }\n    }\n  }\n\n  if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n    if (initializer === void 0) {\n      // If the initializer was void 0, sub in a dummy initializer\n      initializer = function (instance, init) {\n        return init;\n      };\n    } else if (typeof initializer !== \"function\") {\n      var ownInitializers = initializer;\n\n      initializer = function (instance, init) {\n        var value = init;\n\n        for (var i = 0; i < ownInitializers.length; i++) {\n          value = ownInitializers[i].call(instance, value);\n        }\n\n        return value;\n      };\n    } else {\n      var originalInitializer = initializer;\n\n      initializer = function (instance, init) {\n        return originalInitializer.call(instance, init);\n      };\n    }\n\n    ret.push(initializer);\n  }\n\n  if (kind !== 0 /* FIELD */) {\n    if (kind === 1 /* ACCESSOR */) {\n      desc.get = value.get;\n      desc.set = value.set;\n    } else if (kind === 2 /* METHOD */) {\n      desc.value = value;\n    } else if (kind === 3 /* GETTER */) {\n      desc.get = value;\n    } else if (kind === 4 /* SETTER */) {\n      desc.set = value;\n    }\n\n    if (isPrivate) {\n      if (kind === 1 /* ACCESSOR */) {\n        ret.push(function (instance, args) {\n          return value.get.call(instance, args);\n        });\n        ret.push(function (instance, args) {\n          return value.set.call(instance, args);\n        });\n      } else if (kind === 2 /* METHOD */) {\n        ret.push(value);\n      } else {\n        ret.push(function (instance, args) {\n          return value.call(instance, args);\n        });\n      }\n    } else {\n      Object.defineProperty(base, name, desc);\n    }\n  }\n}\n\nfunction old_applyMemberDecs(\n  ret,\n  Class,\n  protoMetadataMap,\n  staticMetadataMap,\n  decInfos\n) {\n  var protoInitializers;\n  var staticInitializers;\n\n  var existingProtoNonFields = new Map();\n  var existingStaticNonFields = new Map();\n\n  for (var i = 0; i < decInfos.length; i++) {\n    var decInfo = decInfos[i];\n\n    // skip computed property names\n    if (!Array.isArray(decInfo)) continue;\n\n    var kind = decInfo[1];\n    var name = decInfo[2];\n    var isPrivate = decInfo.length > 3;\n\n    var isStatic = kind >= 5; /* STATIC */\n    var base;\n    var metadataMap;\n    var initializers;\n\n    if (isStatic) {\n      base = Class;\n      metadataMap = staticMetadataMap;\n      kind = kind - 5 /* STATIC */;\n      // initialize staticInitializers when we see a non-field static member\n      if (kind !== 0 /* FIELD */) {\n        staticInitializers = staticInitializers || [];\n        initializers = staticInitializers;\n      }\n    } else {\n      base = Class.prototype;\n      metadataMap = protoMetadataMap;\n      // initialize protoInitializers when we see a non-field member\n      if (kind !== 0 /* FIELD */) {\n        protoInitializers = protoInitializers || [];\n        initializers = protoInitializers;\n      }\n    }\n\n    if (kind !== 0 /* FIELD */ && !isPrivate) {\n      var existingNonFields = isStatic\n        ? existingStaticNonFields\n        : existingProtoNonFields;\n\n      var existingKind = existingNonFields.get(name) || 0;\n\n      if (\n        existingKind === true ||\n        (existingKind === 3 /* GETTER */ && kind !== 4) /* SETTER */ ||\n        (existingKind === 4 /* SETTER */ && kind !== 3) /* GETTER */\n      ) {\n        throw new Error(\n          \"Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: \" +\n            name\n        );\n      } else if (!existingKind && kind > 2 /* METHOD */) {\n        existingNonFields.set(name, kind);\n      } else {\n        existingNonFields.set(name, true);\n      }\n    }\n\n    old_applyMemberDec(\n      ret,\n      base,\n      decInfo,\n      name,\n      kind,\n      isStatic,\n      isPrivate,\n      metadataMap,\n      initializers\n    );\n  }\n\n  old_pushInitializers(ret, protoInitializers);\n  old_pushInitializers(ret, staticInitializers);\n}\n\nfunction old_pushInitializers(ret, initializers) {\n  if (initializers) {\n    ret.push(function (instance) {\n      for (var i = 0; i < initializers.length; i++) {\n        initializers[i].call(instance);\n      }\n      return instance;\n    });\n  }\n}\n\nfunction old_applyClassDecs(ret, targetClass, metadataMap, classDecs) {\n  if (classDecs.length > 0) {\n    var initializers = [];\n    var newClass = targetClass;\n    var name = targetClass.name;\n\n    for (var i = classDecs.length - 1; i >= 0; i--) {\n      var decoratorFinishedRef = { v: false };\n\n      try {\n        var ctx = Object.assign(\n          {\n            kind: \"class\",\n            name: name,\n            addInitializer: old_createAddInitializerMethod(\n              initializers,\n              decoratorFinishedRef\n            ),\n          },\n          old_createMetadataMethodsForProperty(\n            metadataMap,\n            0 /* CONSTRUCTOR */,\n            name,\n            decoratorFinishedRef\n          )\n        );\n        var nextNewClass = classDecs[i](newClass, ctx);\n      } finally {\n        decoratorFinishedRef.v = true;\n      }\n\n      if (nextNewClass !== undefined) {\n        old_assertValidReturnValue(10 /* CLASS */, nextNewClass);\n        newClass = nextNewClass;\n      }\n    }\n\n    ret.push(newClass, function () {\n      for (var i = 0; i < initializers.length; i++) {\n        initializers[i].call(newClass);\n      }\n    });\n  }\n}\n\n/**\n  Basic usage:\n\n  applyDecs(\n    Class,\n    [\n      // member decorators\n      [\n        dec,                // dec or array of decs\n        0,                  // kind of value being decorated\n        'prop',             // name of public prop on class containing the value being decorated,\n        '#p',               // the name of the private property (if is private, void 0 otherwise),\n      ]\n    ],\n    [\n      // class decorators\n      dec1, dec2\n    ]\n  )\n  ```\n\n  Fully transpiled example:\n\n  ```js\n  @dec\n  class Class {\n    @dec\n    a = 123;\n\n    @dec\n    #a = 123;\n\n    @dec\n    @dec2\n    accessor b = 123;\n\n    @dec\n    accessor #b = 123;\n\n    @dec\n    c() { console.log('c'); }\n\n    @dec\n    #c() { console.log('privC'); }\n\n    @dec\n    get d() { console.log('d'); }\n\n    @dec\n    get #d() { console.log('privD'); }\n\n    @dec\n    set e(v) { console.log('e'); }\n\n    @dec\n    set #e(v) { console.log('privE'); }\n  }\n\n\n  // becomes\n  let initializeInstance;\n  let initializeClass;\n\n  let initA;\n  let initPrivA;\n\n  let initB;\n  let initPrivB, getPrivB, setPrivB;\n\n  let privC;\n  let privD;\n  let privE;\n\n  let Class;\n  class _Class {\n    static {\n      let ret = applyDecs(\n        this,\n        [\n          [dec, 0, 'a'],\n          [dec, 0, 'a', (i) => i.#a, (i, v) => i.#a = v],\n          [[dec, dec2], 1, 'b'],\n          [dec, 1, 'b', (i) => i.#privBData, (i, v) => i.#privBData = v],\n          [dec, 2, 'c'],\n          [dec, 2, 'c', () => console.log('privC')],\n          [dec, 3, 'd'],\n          [dec, 3, 'd', () => console.log('privD')],\n          [dec, 4, 'e'],\n          [dec, 4, 'e', () => console.log('privE')],\n        ],\n        [\n          dec\n        ]\n      )\n\n      initA = ret[0];\n\n      initPrivA = ret[1];\n\n      initB = ret[2];\n\n      initPrivB = ret[3];\n      getPrivB = ret[4];\n      setPrivB = ret[5];\n\n      privC = ret[6];\n\n      privD = ret[7];\n\n      privE = ret[8];\n\n      initializeInstance = ret[9];\n\n      Class = ret[10]\n\n      initializeClass = ret[11];\n    }\n\n    a = (initializeInstance(this), initA(this, 123));\n\n    #a = initPrivA(this, 123);\n\n    #bData = initB(this, 123);\n    get b() { return this.#bData }\n    set b(v) { this.#bData = v }\n\n    #privBData = initPrivB(this, 123);\n    get #b() { return getPrivB(this); }\n    set #b(v) { setPrivB(this, v); }\n\n    c() { console.log('c'); }\n\n    #c(...args) { return privC(this, ...args) }\n\n    get d() { console.log('d'); }\n\n    get #d() { return privD(this); }\n\n    set e(v) { console.log('e'); }\n\n    set #e(v) { privE(this, v); }\n  }\n\n  initializeClass(Class);\n */\nexport default function applyDecs(targetClass, memberDecs, classDecs) {\n  var ret = [];\n  var staticMetadataMap = {};\n\n  var protoMetadataMap = {};\n\n  old_applyMemberDecs(\n    ret,\n    targetClass,\n    protoMetadataMap,\n    staticMetadataMap,\n    memberDecs\n  );\n\n  old_convertMetadataMapToFinal(targetClass.prototype, protoMetadataMap);\n\n  old_applyClassDecs(ret, targetClass, staticMetadataMap, classDecs);\n\n  old_convertMetadataMapToFinal(targetClass, staticMetadataMap);\n\n  return ret;\n}\n"], "mappings": ";;;;;;AAyBA,SAASA,oCAAoC,CAC3CC,WAAW,EACXC,IAAI,EACJC,QAAQ,EACRC,oBAAoB,EACpB;EACA,OAAO;IACLC,WAAW,EAAE,UAAUC,GAAG,EAAE;MAC1BC,qBAAqB,CAACH,oBAAoB,EAAE,aAAa,CAAC;MAC1DI,qBAAqB,CAACF,GAAG,CAAC;MAE1B,IAAIG,cAAc,GAAGR,WAAW,CAACK,GAAG,CAAC;MAErC,IAAIG,cAAc,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC;MAE5C,IAAIP,IAAI,KAAK,CAAC,EAAe;QAC3B,IAAIQ,GAAG,GAAGD,cAAc,CAACE,MAAM;QAC/B,IAAID,GAAG,KAAK,KAAK,CAAC,EAAE;UAClB,OAAOA,GAAG,CAACP,QAAQ,CAAC;QACtB;MACF,CAAC,MAAM,IAAID,IAAI,KAAK,CAAC,EAAgB;QACnC,IAAIU,IAAI,GAAGH,cAAc,CAACI,OAAO;QACjC,IAAID,IAAI,KAAK,KAAK,CAAC,EAAE;UACnB,OAAOA,IAAI,CAACE,GAAG,CAACX,QAAQ,CAAC;QAC3B;MACF,CAAC,MAAM,IAAIY,MAAM,CAACC,cAAc,CAACC,IAAI,CAACR,cAAc,EAAE,aAAa,CAAC,EAAE;QACpE,OAAOA,cAAc,CAACS,WAAW;MACnC;IACF,CAAC;IACDC,WAAW,EAAE,UAAUb,GAAG,EAAEc,KAAK,EAAE;MACjCb,qBAAqB,CAACH,oBAAoB,EAAE,aAAa,CAAC;MAC1DI,qBAAqB,CAACF,GAAG,CAAC;MAE1B,IAAIG,cAAc,GAAGR,WAAW,CAACK,GAAG,CAAC;MAErC,IAAIG,cAAc,KAAK,KAAK,CAAC,EAAE;QAC7BA,cAAc,GAAGR,WAAW,CAACK,GAAG,CAAC,GAAG,CAAC,CAAC;MACxC;MAEA,IAAIJ,IAAI,KAAK,CAAC,EAAe;QAC3B,IAAIQ,GAAG,GAAGD,cAAc,CAACE,MAAM;QAE/B,IAAID,GAAG,KAAK,KAAK,CAAC,EAAE;UAClBA,GAAG,GAAGD,cAAc,CAACE,MAAM,GAAG,CAAC,CAAC;QAClC;QAEAD,GAAG,CAACP,QAAQ,CAAC,GAAGiB,KAAK;MACvB,CAAC,MAAM,IAAIlB,IAAI,KAAK,CAAC,EAAgB;QACnC,IAAIU,IAAI,GAAGH,cAAc,CAACG,IAAI;QAE9B,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;UACnBA,IAAI,GAAGH,cAAc,CAACI,OAAO,GAAG,IAAIQ,GAAG,EAAE;QAC3C;QAEAT,IAAI,CAACU,GAAG,CAACnB,QAAQ,EAAEiB,KAAK,CAAC;MAC3B,CAAC,MAAM;QACLX,cAAc,CAACS,WAAW,GAAGE,KAAK;MACpC;IACF;EACF,CAAC;AACH;AAEA,SAASG,6BAA6B,CAACC,GAAG,EAAEvB,WAAW,EAAE;EACvD,IAAIwB,iBAAiB,GAAGD,GAAG,CAACE,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACE,GAAG,CAAC,iBAAiB,CAAC,CAAC;EAC7E,IAAIC,YAAY,GAAGd,MAAM,CAACe,qBAAqB,CAAC7B,WAAW,CAAC;EAE5D,IAAI4B,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;EAE/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,YAAY,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC5C,IAAI1B,GAAG,GAAGuB,YAAY,CAACG,CAAC,CAAC;IACzB,IAAIC,UAAU,GAAGhC,WAAW,CAACK,GAAG,CAAC;IACjC,IAAI4B,gBAAgB,GAAGT,iBAAiB,GAAGA,iBAAiB,CAACnB,GAAG,CAAC,GAAG,IAAI;IAExE,IAAII,GAAG,GAAGuB,UAAU,CAACtB,MAAM;IAC3B,IAAIwB,SAAS,GAAGD,gBAAgB,GAAGA,gBAAgB,CAACvB,MAAM,GAAG,IAAI;IAEjE,IAAID,GAAG,IAAIyB,SAAS,EAAE;MACpBpB,MAAM,CAACqB,cAAc,CAAC1B,GAAG,EAAEyB,SAAS,CAAC;IACvC;IAEA,IAAIvB,IAAI,GAAGqB,UAAU,CAACpB,OAAO;IAE7B,IAAID,IAAI,EAAE;MACR,IAAIyB,OAAO,GAAGC,KAAK,CAACC,IAAI,CAAC3B,IAAI,CAAC4B,MAAM,EAAE,CAAC;MACvC,IAAIC,UAAU,GAAGP,gBAAgB,GAAGA,gBAAgB,CAACrB,OAAO,GAAG,IAAI;MAEnE,IAAI4B,UAAU,EAAE;QACdJ,OAAO,GAAGA,OAAO,CAACK,MAAM,CAACD,UAAU,CAAC;MACtC;MAEAR,UAAU,CAACpB,OAAO,GAAGwB,OAAO;IAC9B;IAEA,IAAIH,gBAAgB,EAAE;MACpBnB,MAAM,CAACqB,cAAc,CAACH,UAAU,EAAEC,gBAAgB,CAAC;IACrD;EACF;EAEA,IAAIT,iBAAiB,EAAE;IACrBV,MAAM,CAACqB,cAAc,CAACnC,WAAW,EAAEwB,iBAAiB,CAAC;EACvD;EAEAD,GAAG,CAACE,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACE,GAAG,CAAC,iBAAiB,CAAC,CAAC,GAAG3B,WAAW;AACrE;AAEA,SAAS0C,8BAA8B,CAACC,YAAY,EAAExC,oBAAoB,EAAE;EAC1E,OAAO,SAASyC,cAAc,CAACC,WAAW,EAAE;IAC1CvC,qBAAqB,CAACH,oBAAoB,EAAE,gBAAgB,CAAC;IAC7D2C,kBAAkB,CAACD,WAAW,EAAE,gBAAgB,CAAC;IACjDF,YAAY,CAACI,IAAI,CAACF,WAAW,CAAC;EAChC,CAAC;AACH;AAEA,SAASG,aAAa,CACpBC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJnD,WAAW,EACX2C,YAAY,EACZ1C,IAAI,EACJmD,QAAQ,EACRC,SAAS,EACTlC,KAAK,EACL;EACA,IAAImC,OAAO;EAEX,QAAQrD,IAAI;IACV,KAAK,CAAC;MACJqD,OAAO,GAAG,UAAU;MACpB;IACF,KAAK,CAAC;MACJA,OAAO,GAAG,QAAQ;MAClB;IACF,KAAK,CAAC;MACJA,OAAO,GAAG,QAAQ;MAClB;IACF,KAAK,CAAC;MACJA,OAAO,GAAG,QAAQ;MAClB;IACF;MACEA,OAAO,GAAG,OAAO;EAAC;EAGtB,IAAIC,GAAG,GAAG;IACRtD,IAAI,EAAEqD,OAAO;IACbJ,IAAI,EAAEG,SAAS,GAAG,GAAG,GAAGH,IAAI,GAAGA,IAAI;IACnCE,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEA;EACb,CAAC;EAED,IAAIlD,oBAAoB,GAAG;IAAEqD,CAAC,EAAE;EAAM,CAAC;EAEvC,IAAIvD,IAAI,KAAK,CAAC,EAAc;IAC1BsD,GAAG,CAACX,cAAc,GAAGF,8BAA8B,CACjDC,YAAY,EACZxC,oBAAoB,CACrB;EACH;EAEA,IAAIsD,YAAY,EAAEC,YAAY;EAE9B,IAAIL,SAAS,EAAE;IACbI,YAAY,GAAG,CAAC;IAChBC,YAAY,GAAGjC,MAAM,CAACyB,IAAI,CAAC;IAE3B,IAAIS,MAAM,GAAG,CAAC,CAAC;IAEf,IAAI1D,IAAI,KAAK,CAAC,EAAc;MAC1B0D,MAAM,CAAC9C,GAAG,GAAGsC,IAAI,CAACtC,GAAG;MACrB8C,MAAM,CAACtC,GAAG,GAAG8B,IAAI,CAAC9B,GAAG;IACvB,CAAC,MAAM,IAAIpB,IAAI,KAAK,CAAC,EAAe;MAClC0D,MAAM,CAAC9C,GAAG,GAAG,YAAY;QACvB,OAAOsC,IAAI,CAAChC,KAAK;MACnB,CAAC;IACH,CAAC,MAAM;MAEL,IAAIlB,IAAI,KAAK,CAAC,IAAmBA,IAAI,KAAK,CAAC,EAAe;QACxD0D,MAAM,CAAC9C,GAAG,GAAG,YAAY;UACvB,OAAOsC,IAAI,CAACtC,GAAG,CAACG,IAAI,CAAC,IAAI,CAAC;QAC5B,CAAC;MACH;MAEA,IAAIf,IAAI,KAAK,CAAC,IAAmBA,IAAI,KAAK,CAAC,EAAe;QACxD0D,MAAM,CAACtC,GAAG,GAAG,UAAUmC,CAAC,EAAE;UACxBL,IAAI,CAAC9B,GAAG,CAACL,IAAI,CAAC,IAAI,EAAEwC,CAAC,CAAC;QACxB,CAAC;MACH;IACF;IAEAD,GAAG,CAACI,MAAM,GAAGA,MAAM;EACrB,CAAC,MAAM;IACLF,YAAY,GAAG,CAAC;IAChBC,YAAY,GAAGR,IAAI;EACrB;EAEA,IAAI;IACF,OAAOD,GAAG,CACR9B,KAAK,EACLL,MAAM,CAAC8C,MAAM,CACXL,GAAG,EACHxD,oCAAoC,CAClCC,WAAW,EACXyD,YAAY,EACZC,YAAY,EACZvD,oBAAoB,CACrB,CACF,CACF;EACH,CAAC,SAAS;IACRA,oBAAoB,CAACqD,CAAC,GAAG,IAAI;EAC/B;AACF;AAEA,SAASlD,qBAAqB,CAACH,oBAAoB,EAAE0D,MAAM,EAAE;EAC3D,IAAI1D,oBAAoB,CAACqD,CAAC,EAAE;IAC1B,MAAM,IAAIM,KAAK,CACb,oBAAoB,GAAGD,MAAM,GAAG,gCAAgC,CACjE;EACH;AACF;AAEA,SAAStD,qBAAqB,CAACF,GAAG,EAAE;EAClC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3B,MAAM,IAAI0D,SAAS,CAAC,2CAA2C,GAAG1D,GAAG,CAAC;EACxE;AACF;AAEA,SAASyC,kBAAkB,CAACkB,EAAE,EAAEC,IAAI,EAAE;EACpC,IAAI,OAAOD,EAAE,KAAK,UAAU,EAAE;IAC5B,MAAM,IAAID,SAAS,CAACE,IAAI,GAAG,qBAAqB,CAAC;EACnD;AACF;AAEA,SAASC,0BAA0B,CAACjE,IAAI,EAAEkB,KAAK,EAAE;EAC/C,IAAIgD,IAAI,GAAG,OAAOhD,KAAK;EAEvB,IAAIlB,IAAI,KAAK,CAAC,EAAiB;IAC7B,IAAIkE,IAAI,KAAK,QAAQ,IAAIhD,KAAK,KAAK,IAAI,EAAE;MACvC,MAAM,IAAI4C,SAAS,CACjB,uFAAuF,CACxF;IACH;IACA,IAAI5C,KAAK,CAACN,GAAG,KAAKuD,SAAS,EAAE;MAC3BtB,kBAAkB,CAAC3B,KAAK,CAACN,GAAG,EAAE,cAAc,CAAC;IAC/C;IACA,IAAIM,KAAK,CAACE,GAAG,KAAK+C,SAAS,EAAE;MAC3BtB,kBAAkB,CAAC3B,KAAK,CAACE,GAAG,EAAE,cAAc,CAAC;IAC/C;IACA,IAAIF,KAAK,CAACkD,IAAI,KAAKD,SAAS,EAAE;MAC5BtB,kBAAkB,CAAC3B,KAAK,CAACkD,IAAI,EAAE,eAAe,CAAC;IACjD;IACA,IAAIlD,KAAK,CAAC0B,WAAW,KAAKuB,SAAS,EAAE;MACnCtB,kBAAkB,CAAC3B,KAAK,CAAC0B,WAAW,EAAE,sBAAsB,CAAC;IAC/D;EACF,CAAC,MAAM,IAAIsB,IAAI,KAAK,UAAU,EAAE;IAC9B,IAAIF,IAAI;IACR,IAAIhE,IAAI,KAAK,CAAC,EAAc;MAC1BgE,IAAI,GAAG,OAAO;IAChB,CAAC,MAAM,IAAIhE,IAAI,KAAK,EAAE,EAAc;MAClCgE,IAAI,GAAG,OAAO;IAChB,CAAC,MAAM;MACLA,IAAI,GAAG,QAAQ;IACjB;IACA,MAAM,IAAIF,SAAS,CAACE,IAAI,GAAG,8CAA8C,CAAC;EAC5E;AACF;AAEA,SAASK,WAAW,CAACnB,IAAI,EAAE;EACzB,IAAIN,WAAW;EACf,IACE,CAACA,WAAW,GAAGM,IAAI,CAACkB,IAAI,KAAK,IAAI,KAChCxB,WAAW,GAAGM,IAAI,CAACN,WAAW,CAAC,IAChC,OAAO0B,OAAO,KAAK,WAAW,EAC9B;IACAA,OAAO,CAACC,IAAI,CAAC,yDAAyD,CAAC;EACzE;EACA,OAAO3B,WAAW;AACpB;AAEA,SAAS4B,kBAAkB,CACzBC,GAAG,EACHC,IAAI,EACJC,OAAO,EACP1B,IAAI,EACJjD,IAAI,EACJmD,QAAQ,EACRC,SAAS,EACTrD,WAAW,EACX2C,YAAY,EACZ;EACA,IAAIkC,IAAI,GAAGD,OAAO,CAAC,CAAC,CAAC;EAErB,IAAIzB,IAAI,EAAEN,WAAW,EAAE1B,KAAK;EAE5B,IAAIkC,SAAS,EAAE;IACb,IAAIpD,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;MACvDkD,IAAI,GAAG;QACLtC,GAAG,EAAE+D,OAAO,CAAC,CAAC,CAAC;QACfvD,GAAG,EAAEuD,OAAO,CAAC,CAAC;MAChB,CAAC;IACH,CAAC,MAAM,IAAI3E,IAAI,KAAK,CAAC,EAAe;MAClCkD,IAAI,GAAG;QACLtC,GAAG,EAAE+D,OAAO,CAAC,CAAC;MAChB,CAAC;IACH,CAAC,MAAM,IAAI3E,IAAI,KAAK,CAAC,EAAe;MAClCkD,IAAI,GAAG;QACL9B,GAAG,EAAEuD,OAAO,CAAC,CAAC;MAChB,CAAC;IACH,CAAC,MAAM;MACLzB,IAAI,GAAG;QACLhC,KAAK,EAAEyD,OAAO,CAAC,CAAC;MAClB,CAAC;IACH;EACF,CAAC,MAAM,IAAI3E,IAAI,KAAK,CAAC,EAAc;IACjCkD,IAAI,GAAGrC,MAAM,CAACgE,wBAAwB,CAACH,IAAI,EAAEzB,IAAI,CAAC;EACpD;EAEA,IAAIjD,IAAI,KAAK,CAAC,EAAiB;IAC7BkB,KAAK,GAAG;MACNN,GAAG,EAAEsC,IAAI,CAACtC,GAAG;MACbQ,GAAG,EAAE8B,IAAI,CAAC9B;IACZ,CAAC;EACH,CAAC,MAAM,IAAIpB,IAAI,KAAK,CAAC,EAAe;IAClCkB,KAAK,GAAGgC,IAAI,CAAChC,KAAK;EACpB,CAAC,MAAM,IAAIlB,IAAI,KAAK,CAAC,EAAe;IAClCkB,KAAK,GAAGgC,IAAI,CAACtC,GAAG;EAClB,CAAC,MAAM,IAAIZ,IAAI,KAAK,CAAC,EAAe;IAClCkB,KAAK,GAAGgC,IAAI,CAAC9B,GAAG;EAClB;EAEA,IAAI0D,QAAQ,EAAElE,GAAG,EAAEQ,GAAG;EAEtB,IAAI,OAAOwD,IAAI,KAAK,UAAU,EAAE;IAC9BE,QAAQ,GAAG/B,aAAa,CACtB6B,IAAI,EACJ3B,IAAI,EACJC,IAAI,EACJnD,WAAW,EACX2C,YAAY,EACZ1C,IAAI,EACJmD,QAAQ,EACRC,SAAS,EACTlC,KAAK,CACN;IAED,IAAI4D,QAAQ,KAAK,KAAK,CAAC,EAAE;MACvBb,0BAA0B,CAACjE,IAAI,EAAE8E,QAAQ,CAAC;MAE1C,IAAI9E,IAAI,KAAK,CAAC,EAAc;QAC1B4C,WAAW,GAAGkC,QAAQ;MACxB,CAAC,MAAM,IAAI9E,IAAI,KAAK,CAAC,EAAiB;QACpC4C,WAAW,GAAGyB,WAAW,CAACS,QAAQ,CAAC;QACnClE,GAAG,GAAGkE,QAAQ,CAAClE,GAAG,IAAIM,KAAK,CAACN,GAAG;QAC/BQ,GAAG,GAAG0D,QAAQ,CAAC1D,GAAG,IAAIF,KAAK,CAACE,GAAG;QAE/BF,KAAK,GAAG;UAAEN,GAAG,EAAEA,GAAG;UAAEQ,GAAG,EAAEA;QAAI,CAAC;MAChC,CAAC,MAAM;QACLF,KAAK,GAAG4D,QAAQ;MAClB;IACF;EACF,CAAC,MAAM;IACL,KAAK,IAAIhD,CAAC,GAAG8C,IAAI,CAAC/C,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzC,IAAIkB,GAAG,GAAG4B,IAAI,CAAC9C,CAAC,CAAC;MAEjBgD,QAAQ,GAAG/B,aAAa,CACtBC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJnD,WAAW,EACX2C,YAAY,EACZ1C,IAAI,EACJmD,QAAQ,EACRC,SAAS,EACTlC,KAAK,CACN;MAED,IAAI4D,QAAQ,KAAK,KAAK,CAAC,EAAE;QACvBb,0BAA0B,CAACjE,IAAI,EAAE8E,QAAQ,CAAC;QAC1C,IAAIC,OAAO;QAEX,IAAI/E,IAAI,KAAK,CAAC,EAAc;UAC1B+E,OAAO,GAAGD,QAAQ;QACpB,CAAC,MAAM,IAAI9E,IAAI,KAAK,CAAC,EAAiB;UACpC+E,OAAO,GAAGV,WAAW,CAACS,QAAQ,CAAC;UAC/BlE,GAAG,GAAGkE,QAAQ,CAAClE,GAAG,IAAIM,KAAK,CAACN,GAAG;UAC/BQ,GAAG,GAAG0D,QAAQ,CAAC1D,GAAG,IAAIF,KAAK,CAACE,GAAG;UAE/BF,KAAK,GAAG;YAAEN,GAAG,EAAEA,GAAG;YAAEQ,GAAG,EAAEA;UAAI,CAAC;QAChC,CAAC,MAAM;UACLF,KAAK,GAAG4D,QAAQ;QAClB;QAEA,IAAIC,OAAO,KAAK,KAAK,CAAC,EAAE;UACtB,IAAInC,WAAW,KAAK,KAAK,CAAC,EAAE;YAC1BA,WAAW,GAAGmC,OAAO;UACvB,CAAC,MAAM,IAAI,OAAOnC,WAAW,KAAK,UAAU,EAAE;YAC5CA,WAAW,GAAG,CAACA,WAAW,EAAEmC,OAAO,CAAC;UACtC,CAAC,MAAM;YACLnC,WAAW,CAACE,IAAI,CAACiC,OAAO,CAAC;UAC3B;QACF;MACF;IACF;EACF;EAEA,IAAI/E,IAAI,KAAK,CAAC,IAAgBA,IAAI,KAAK,CAAC,EAAiB;IACvD,IAAI4C,WAAW,KAAK,KAAK,CAAC,EAAE;MAE1BA,WAAW,GAAG,UAAUoC,QAAQ,EAAEZ,IAAI,EAAE;QACtC,OAAOA,IAAI;MACb,CAAC;IACH,CAAC,MAAM,IAAI,OAAOxB,WAAW,KAAK,UAAU,EAAE;MAC5C,IAAIqC,eAAe,GAAGrC,WAAW;MAEjCA,WAAW,GAAG,UAAUoC,QAAQ,EAAEZ,IAAI,EAAE;QACtC,IAAIlD,KAAK,GAAGkD,IAAI;QAEhB,KAAK,IAAItC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmD,eAAe,CAACpD,MAAM,EAAEC,CAAC,EAAE,EAAE;UAC/CZ,KAAK,GAAG+D,eAAe,CAACnD,CAAC,CAAC,CAACf,IAAI,CAACiE,QAAQ,EAAE9D,KAAK,CAAC;QAClD;QAEA,OAAOA,KAAK;MACd,CAAC;IACH,CAAC,MAAM;MACL,IAAIgE,mBAAmB,GAAGtC,WAAW;MAErCA,WAAW,GAAG,UAAUoC,QAAQ,EAAEZ,IAAI,EAAE;QACtC,OAAOc,mBAAmB,CAACnE,IAAI,CAACiE,QAAQ,EAAEZ,IAAI,CAAC;MACjD,CAAC;IACH;IAEAK,GAAG,CAAC3B,IAAI,CAACF,WAAW,CAAC;EACvB;EAEA,IAAI5C,IAAI,KAAK,CAAC,EAAc;IAC1B,IAAIA,IAAI,KAAK,CAAC,EAAiB;MAC7BkD,IAAI,CAACtC,GAAG,GAAGM,KAAK,CAACN,GAAG;MACpBsC,IAAI,CAAC9B,GAAG,GAAGF,KAAK,CAACE,GAAG;IACtB,CAAC,MAAM,IAAIpB,IAAI,KAAK,CAAC,EAAe;MAClCkD,IAAI,CAAChC,KAAK,GAAGA,KAAK;IACpB,CAAC,MAAM,IAAIlB,IAAI,KAAK,CAAC,EAAe;MAClCkD,IAAI,CAACtC,GAAG,GAAGM,KAAK;IAClB,CAAC,MAAM,IAAIlB,IAAI,KAAK,CAAC,EAAe;MAClCkD,IAAI,CAAC9B,GAAG,GAAGF,KAAK;IAClB;IAEA,IAAIkC,SAAS,EAAE;MACb,IAAIpD,IAAI,KAAK,CAAC,EAAiB;QAC7ByE,GAAG,CAAC3B,IAAI,CAAC,UAAUkC,QAAQ,EAAEG,IAAI,EAAE;UACjC,OAAOjE,KAAK,CAACN,GAAG,CAACG,IAAI,CAACiE,QAAQ,EAAEG,IAAI,CAAC;QACvC,CAAC,CAAC;QACFV,GAAG,CAAC3B,IAAI,CAAC,UAAUkC,QAAQ,EAAEG,IAAI,EAAE;UACjC,OAAOjE,KAAK,CAACE,GAAG,CAACL,IAAI,CAACiE,QAAQ,EAAEG,IAAI,CAAC;QACvC,CAAC,CAAC;MACJ,CAAC,MAAM,IAAInF,IAAI,KAAK,CAAC,EAAe;QAClCyE,GAAG,CAAC3B,IAAI,CAAC5B,KAAK,CAAC;MACjB,CAAC,MAAM;QACLuD,GAAG,CAAC3B,IAAI,CAAC,UAAUkC,QAAQ,EAAEG,IAAI,EAAE;UACjC,OAAOjE,KAAK,CAACH,IAAI,CAACiE,QAAQ,EAAEG,IAAI,CAAC;QACnC,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLtE,MAAM,CAACuE,cAAc,CAACV,IAAI,EAAEzB,IAAI,EAAEC,IAAI,CAAC;IACzC;EACF;AACF;AAEA,SAASmC,mBAAmB,CAC1BZ,GAAG,EACHa,KAAK,EACLC,gBAAgB,EAChBC,iBAAiB,EACjBC,QAAQ,EACR;EACA,IAAIC,iBAAiB;EACrB,IAAIC,kBAAkB;EAEtB,IAAIC,sBAAsB,GAAG,IAAIzE,GAAG,EAAE;EACtC,IAAI0E,uBAAuB,GAAG,IAAI1E,GAAG,EAAE;EAEvC,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2D,QAAQ,CAAC5D,MAAM,EAAEC,CAAC,EAAE,EAAE;IACxC,IAAI6C,OAAO,GAAGc,QAAQ,CAAC3D,CAAC,CAAC;IAGzB,IAAI,CAACM,KAAK,CAAC0D,OAAO,CAACnB,OAAO,CAAC,EAAE;IAE7B,IAAI3E,IAAI,GAAG2E,OAAO,CAAC,CAAC,CAAC;IACrB,IAAI1B,IAAI,GAAG0B,OAAO,CAAC,CAAC,CAAC;IACrB,IAAIvB,SAAS,GAAGuB,OAAO,CAAC9C,MAAM,GAAG,CAAC;IAElC,IAAIsB,QAAQ,GAAGnD,IAAI,IAAI,CAAC;IACxB,IAAI0E,IAAI;IACR,IAAI3E,WAAW;IACf,IAAI2C,YAAY;IAEhB,IAAIS,QAAQ,EAAE;MACZuB,IAAI,GAAGY,KAAK;MACZvF,WAAW,GAAGyF,iBAAiB;MAC/BxF,IAAI,GAAGA,IAAI,GAAG,CAAC;MAEf,IAAIA,IAAI,KAAK,CAAC,EAAc;QAC1B2F,kBAAkB,GAAGA,kBAAkB,IAAI,EAAE;QAC7CjD,YAAY,GAAGiD,kBAAkB;MACnC;IACF,CAAC,MAAM;MACLjB,IAAI,GAAGY,KAAK,CAACS,SAAS;MACtBhG,WAAW,GAAGwF,gBAAgB;MAE9B,IAAIvF,IAAI,KAAK,CAAC,EAAc;QAC1B0F,iBAAiB,GAAGA,iBAAiB,IAAI,EAAE;QAC3ChD,YAAY,GAAGgD,iBAAiB;MAClC;IACF;IAEA,IAAI1F,IAAI,KAAK,CAAC,IAAgB,CAACoD,SAAS,EAAE;MACxC,IAAI4C,iBAAiB,GAAG7C,QAAQ,GAC5B0C,uBAAuB,GACvBD,sBAAsB;MAE1B,IAAIK,YAAY,GAAGD,iBAAiB,CAACpF,GAAG,CAACqC,IAAI,CAAC,IAAI,CAAC;MAEnD,IACEgD,YAAY,KAAK,IAAI,IACpBA,YAAY,KAAK,CAAC,IAAiBjG,IAAI,KAAK,CAAE,IAC9CiG,YAAY,KAAK,CAAC,IAAiBjG,IAAI,KAAK,CAAE,EAC/C;QACA,MAAM,IAAI6D,KAAK,CACb,uMAAuM,GACrMZ,IAAI,CACP;MACH,CAAC,MAAM,IAAI,CAACgD,YAAY,IAAIjG,IAAI,GAAG,CAAC,EAAe;QACjDgG,iBAAiB,CAAC5E,GAAG,CAAC6B,IAAI,EAAEjD,IAAI,CAAC;MACnC,CAAC,MAAM;QACLgG,iBAAiB,CAAC5E,GAAG,CAAC6B,IAAI,EAAE,IAAI,CAAC;MACnC;IACF;IAEAuB,kBAAkB,CAChBC,GAAG,EACHC,IAAI,EACJC,OAAO,EACP1B,IAAI,EACJjD,IAAI,EACJmD,QAAQ,EACRC,SAAS,EACTrD,WAAW,EACX2C,YAAY,CACb;EACH;EAEAwD,oBAAoB,CAACzB,GAAG,EAAEiB,iBAAiB,CAAC;EAC5CQ,oBAAoB,CAACzB,GAAG,EAAEkB,kBAAkB,CAAC;AAC/C;AAEA,SAASO,oBAAoB,CAACzB,GAAG,EAAE/B,YAAY,EAAE;EAC/C,IAAIA,YAAY,EAAE;IAChB+B,GAAG,CAAC3B,IAAI,CAAC,UAAUkC,QAAQ,EAAE;MAC3B,KAAK,IAAIlD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,YAAY,CAACb,MAAM,EAAEC,CAAC,EAAE,EAAE;QAC5CY,YAAY,CAACZ,CAAC,CAAC,CAACf,IAAI,CAACiE,QAAQ,CAAC;MAChC;MACA,OAAOA,QAAQ;IACjB,CAAC,CAAC;EACJ;AACF;AAEA,SAASmB,kBAAkB,CAAC1B,GAAG,EAAE2B,WAAW,EAAErG,WAAW,EAAEsG,SAAS,EAAE;EACpE,IAAIA,SAAS,CAACxE,MAAM,GAAG,CAAC,EAAE;IACxB,IAAIa,YAAY,GAAG,EAAE;IACrB,IAAI4D,QAAQ,GAAGF,WAAW;IAC1B,IAAInD,IAAI,GAAGmD,WAAW,CAACnD,IAAI;IAE3B,KAAK,IAAInB,CAAC,GAAGuE,SAAS,CAACxE,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9C,IAAI5B,oBAAoB,GAAG;QAAEqD,CAAC,EAAE;MAAM,CAAC;MAEvC,IAAI;QACF,IAAID,GAAG,GAAGzC,MAAM,CAAC8C,MAAM,CACrB;UACE3D,IAAI,EAAE,OAAO;UACbiD,IAAI,EAAEA,IAAI;UACVN,cAAc,EAAEF,8BAA8B,CAC5CC,YAAY,EACZxC,oBAAoB;QAExB,CAAC,EACDJ,oCAAoC,CAClCC,WAAW,EACX,CAAC,EACDkD,IAAI,EACJ/C,oBAAoB,CACrB,CACF;QACD,IAAIqG,YAAY,GAAGF,SAAS,CAACvE,CAAC,CAAC,CAACwE,QAAQ,EAAEhD,GAAG,CAAC;MAChD,CAAC,SAAS;QACRpD,oBAAoB,CAACqD,CAAC,GAAG,IAAI;MAC/B;MAEA,IAAIgD,YAAY,KAAKpC,SAAS,EAAE;QAC9BF,0BAA0B,CAAC,EAAE,EAAcsC,YAAY,CAAC;QACxDD,QAAQ,GAAGC,YAAY;MACzB;IACF;IAEA9B,GAAG,CAAC3B,IAAI,CAACwD,QAAQ,EAAE,YAAY;MAC7B,KAAK,IAAIxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,YAAY,CAACb,MAAM,EAAEC,CAAC,EAAE,EAAE;QAC5CY,YAAY,CAACZ,CAAC,CAAC,CAACf,IAAI,CAACuF,QAAQ,CAAC;MAChC;IACF,CAAC,CAAC;EACJ;AACF;AAmJe,SAASE,SAAS,CAACJ,WAAW,EAAEK,UAAU,EAAEJ,SAAS,EAAE;EACpE,IAAI5B,GAAG,GAAG,EAAE;EACZ,IAAIe,iBAAiB,GAAG,CAAC,CAAC;EAE1B,IAAID,gBAAgB,GAAG,CAAC,CAAC;EAEzBF,mBAAmB,CACjBZ,GAAG,EACH2B,WAAW,EACXb,gBAAgB,EAChBC,iBAAiB,EACjBiB,UAAU,CACX;EAEDpF,6BAA6B,CAAC+E,WAAW,CAACL,SAAS,EAAER,gBAAgB,CAAC;EAEtEY,kBAAkB,CAAC1B,GAAG,EAAE2B,WAAW,EAAEZ,iBAAiB,EAAEa,SAAS,CAAC;EAElEhF,6BAA6B,CAAC+E,WAAW,EAAEZ,iBAAiB,CAAC;EAE7D,OAAOf,GAAG;AACZ"}