import { addFilter } from '@appmaker-xyz/core';

export function registerReusableBlocks() {
  const reusableBlocks = {
    'shopify-product-list-item': {
      blocks: [
        {
          name: 'appmaker/shopify-product-list',
          attributes: {},
          clientId: '80745141-c2d9-4a6b-85fa-e70581a9f761',
        },
      ],
    },
  };
  addFilter('appmaker-reusableBlocks', 'shopify-default', (blocks) => {
    return { ...blocks, ...reusableBlocks };
  });
}
