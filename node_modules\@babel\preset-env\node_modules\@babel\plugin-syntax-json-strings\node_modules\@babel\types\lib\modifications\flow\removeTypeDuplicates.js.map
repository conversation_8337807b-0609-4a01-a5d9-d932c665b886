{"version": 3, "names": ["_generated", "require", "getQualifiedName", "node", "isIdentifier", "name", "id", "qualification", "removeTypeDuplicates", "nodes", "generics", "Map", "bases", "typeGroups", "Set", "types", "i", "length", "indexOf", "isAnyTypeAnnotation", "isFlowBaseAnnotation", "set", "type", "isUnionTypeAnnotation", "has", "concat", "add", "isGenericTypeAnnotation", "existing", "get", "typeParameters", "params", "push", "baseType", "genericName"], "sources": ["../../../src/modifications/flow/removeTypeDuplicates.ts"], "sourcesContent": ["import {\n  isAnyTypeAnnotation,\n  isGenericTypeAnnotation,\n  isUnionTypeAnnotation,\n  isFlowBaseAnnotation,\n  isIdentifier,\n} from \"../../validators/generated\";\nimport type * as t from \"../..\";\n\nfunction getQualifiedName(node: t.GenericTypeAnnotation[\"id\"]): string {\n  return isIdentifier(node)\n    ? node.name\n    : `${node.id.name}.${getQualifiedName(node.qualification)}`;\n}\n\n/**\n * Dedupe type annotations.\n */\nexport default function removeTypeDuplicates(\n  // todo(babel-8): change type to Array<...>\n  nodes: ReadonlyArray<t.FlowType | false | null | undefined>,\n): t.FlowType[] {\n  const generics = new Map<string, t.GenericTypeAnnotation>();\n  const bases = new Map<t.FlowBaseAnnotation[\"type\"], t.FlowBaseAnnotation>();\n\n  // store union type groups to circular references\n  const typeGroups = new Set<t.FlowType[]>();\n\n  const types: t.FlowType[] = [];\n\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if (!node) continue;\n\n    // detect duplicates\n    if (types.indexOf(node) >= 0) {\n      continue;\n    }\n\n    // this type matches anything\n    if (isAnyTypeAnnotation(node)) {\n      return [node];\n    }\n\n    if (isFlowBaseAnnotation(node)) {\n      bases.set(node.type, node);\n      continue;\n    }\n\n    if (isUnionTypeAnnotation(node)) {\n      if (!typeGroups.has(node.types)) {\n        // todo(babel-8): use .push when nodes is mutable\n        nodes = nodes.concat(node.types);\n        typeGroups.add(node.types);\n      }\n      continue;\n    }\n\n    // find a matching generic type and merge and deduplicate the type parameters\n    if (isGenericTypeAnnotation(node)) {\n      const name = getQualifiedName(node.id);\n\n      if (generics.has(name)) {\n        let existing: t.Flow = generics.get(name);\n        if (existing.typeParameters) {\n          if (node.typeParameters) {\n            existing.typeParameters.params = removeTypeDuplicates(\n              existing.typeParameters.params.concat(node.typeParameters.params),\n            );\n          }\n        } else {\n          existing = node.typeParameters;\n        }\n      } else {\n        generics.set(name, node);\n      }\n\n      continue;\n    }\n\n    types.push(node);\n  }\n\n  // add back in bases\n  for (const [, baseType] of bases) {\n    types.push(baseType);\n  }\n\n  // add back in generics\n  for (const [, genericName] of generics) {\n    types.push(genericName);\n  }\n\n  return types;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AASA,SAASC,gBAAgBA,CAACC,IAAmC,EAAU;EACrE,OAAO,IAAAC,uBAAY,EAACD,IAAI,CAAC,GACrBA,IAAI,CAACE,IAAI,GACR,GAAEF,IAAI,CAACG,EAAE,CAACD,IAAK,IAAGH,gBAAgB,CAACC,IAAI,CAACI,aAAa,CAAE,EAAC;AAC/D;AAKe,SAASC,oBAAoBA,CAE1CC,KAA2D,EAC7C;EACd,MAAMC,QAAQ,GAAG,IAAIC,GAAG,EAAmC;EAC3D,MAAMC,KAAK,GAAG,IAAID,GAAG,EAAsD;EAG3E,MAAME,UAAU,GAAG,IAAIC,GAAG,EAAgB;EAE1C,MAAMC,KAAmB,GAAG,EAAE;EAE9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,KAAK,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAMb,IAAI,GAAGM,KAAK,CAACO,CAAC,CAAC;IACrB,IAAI,CAACb,IAAI,EAAE;IAGX,IAAIY,KAAK,CAACG,OAAO,CAACf,IAAI,CAAC,IAAI,CAAC,EAAE;MAC5B;IACF;IAGA,IAAI,IAAAgB,8BAAmB,EAAChB,IAAI,CAAC,EAAE;MAC7B,OAAO,CAACA,IAAI,CAAC;IACf;IAEA,IAAI,IAAAiB,+BAAoB,EAACjB,IAAI,CAAC,EAAE;MAC9BS,KAAK,CAACS,GAAG,CAAClB,IAAI,CAACmB,IAAI,EAAEnB,IAAI,CAAC;MAC1B;IACF;IAEA,IAAI,IAAAoB,gCAAqB,EAACpB,IAAI,CAAC,EAAE;MAC/B,IAAI,CAACU,UAAU,CAACW,GAAG,CAACrB,IAAI,CAACY,KAAK,CAAC,EAAE;QAE/BN,KAAK,GAAGA,KAAK,CAACgB,MAAM,CAACtB,IAAI,CAACY,KAAK,CAAC;QAChCF,UAAU,CAACa,GAAG,CAACvB,IAAI,CAACY,KAAK,CAAC;MAC5B;MACA;IACF;IAGA,IAAI,IAAAY,kCAAuB,EAACxB,IAAI,CAAC,EAAE;MACjC,MAAME,IAAI,GAAGH,gBAAgB,CAACC,IAAI,CAACG,EAAE,CAAC;MAEtC,IAAII,QAAQ,CAACc,GAAG,CAACnB,IAAI,CAAC,EAAE;QACtB,IAAIuB,QAAgB,GAAGlB,QAAQ,CAACmB,GAAG,CAACxB,IAAI,CAAC;QACzC,IAAIuB,QAAQ,CAACE,cAAc,EAAE;UAC3B,IAAI3B,IAAI,CAAC2B,cAAc,EAAE;YACvBF,QAAQ,CAACE,cAAc,CAACC,MAAM,GAAGvB,oBAAoB,CACnDoB,QAAQ,CAACE,cAAc,CAACC,MAAM,CAACN,MAAM,CAACtB,IAAI,CAAC2B,cAAc,CAACC,MAAM,CAAC,CAClE;UACH;QACF,CAAC,MAAM;UACLH,QAAQ,GAAGzB,IAAI,CAAC2B,cAAc;QAChC;MACF,CAAC,MAAM;QACLpB,QAAQ,CAACW,GAAG,CAAChB,IAAI,EAAEF,IAAI,CAAC;MAC1B;MAEA;IACF;IAEAY,KAAK,CAACiB,IAAI,CAAC7B,IAAI,CAAC;EAClB;EAGA,KAAK,MAAM,GAAG8B,QAAQ,CAAC,IAAIrB,KAAK,EAAE;IAChCG,KAAK,CAACiB,IAAI,CAACC,QAAQ,CAAC;EACtB;EAGA,KAAK,MAAM,GAAGC,WAAW,CAAC,IAAIxB,QAAQ,EAAE;IACtCK,KAAK,CAACiB,IAAI,CAACE,WAAW,CAAC;EACzB;EAEA,OAAOnB,KAAK;AACd"}