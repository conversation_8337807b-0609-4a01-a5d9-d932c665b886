import React from 'react';
import { Layout } from '../../../../components';
import { appmaker } from '@appmaker-xyz/core';
import { StyleSheet } from 'react-native';
import { ProductSlotBlock } from './ProductSlotBlock';
function getBlocksBySlotId(innerBlocks, slotId) {
  const slotBlocks = innerBlocks.find((block) => {
    if (block.attributes.slotId === slotId) {
      return block.innerBlocks;
    }
  });
  if (slotBlocks) {
    return slotBlocks.innerBlocks;
  }
  return [];
}
export function InnerBlocks({ innerBlocks, BlockItem, blockData, ...props }) {
  const ReviewsComp = appmaker.applyFilters(
    'appmaker-product-grid-reviews',
    () => null,
  );
  const topRightslotBlocks = getBlocksBySlotId(innerBlocks, 'top-left') || [];
  const topleftslotBlocks = getBlocksBySlotId(innerBlocks, 'top-right') || [];
  // console.log(JSON.stringify(slotBlocks, null, 2), 'slotBlocks');
  // return null;
  return (
    <>
      <Layout style={styles.topLeftBlocks}>
        <ProductSlotBlock
          innerBlocks={innerBlocks}
          BlockItem={BlockItem}
          blockData={blockData}
          slotId="top-left"
          {...props}
        />
      </Layout>
      <Layout style={styles.topRightBlocks}>
        <ProductSlotBlock
          innerBlocks={innerBlocks}
          BlockItem={BlockItem}
          blockData={blockData}
          slotId="top-right"
          {...props}
        />
      </Layout>
    </>
  );
}
const styles = StyleSheet.create({
  topLeftBlocks: {
    flexDirection: 'row',
    position: 'absolute',
    flexWrap: 'wrap',
    left: 2,
    top: 4,
  },
  topRightBlocks: {
    flexDirection: 'row',
    position: 'absolute',
    flexWrap: 'wrap',
    right: 2,
    top: 4,
  },
});
