import { View, Text, Button } from 'react-native';
import React from 'react';
import { usePageState } from '@appmaker-xyz/core';

export default function VariationSelectCointainer({ variationKey, onChange }) {
  const currentValue = usePageState(
    (state) => state.filter && state.filter[variationKey],
  );
  const filter = usePageState((state) => state.filter);
  //   console.log(currentValue, filter);
  return (
    <View>
      <Text>
        VariationSelectCointainer:{variationKey} => {currentValue}
        <Button
          title="Chanage"
          onPress={() => {
            onChange('Light Blue');
            // pageDispatch({
            //     type: 'set_filter',
            //     variationKey: attributes.variationKey,
            //     variationValue: value,
            //     filter: variation,
            //   });
          }}
        />
      </Text>
    </View>
  );
}
