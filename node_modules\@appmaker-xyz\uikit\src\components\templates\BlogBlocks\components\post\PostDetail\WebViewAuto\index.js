/**
 * Custom WebView with autoHeight feature
 *
 * @prop source: Same as WebView
 * @prop autoHeight: true|false
 * @prop defaultHeight: 100
 * @prop width: device Width
 * @prop ...props
 * @version v1.0.2
 */

import React, {Component} from 'react';
import {Dimensions, Platform} from 'react-native';
// import { WebView } from 'react-native-webview';

const {height} = Dimensions.get('window');

const injectedScriptIOS = () => {
  function waitForBridge() {
    if (window.ReactNativeWebView.postMessage.length !== 1) {
      setTimeout(waitForBridge, 200);
    } else {
      window.ReactNativeWebView.postMessage(
        Math.max(
          // document.documentElement.clientHeight,
          // document.documentElement.scrollHeight,
          document.body.clientHeight,
          document.body.scrollHeight,
        ),
      );
    }
  }
  waitForBridge();
};

const injectedScriptAndroid = () => {
  function waitForBridge() {
    // console.log('which', window.postMessage, window.ReactNativeWebView.postMessage);

    if (window.postMessage.length < 1) {
      setTimeout(waitForBridge, 200);
    } else {
      window.ReactNativeWebView.postMessage(
        Math.max(
          // document.documentElement.clientHeight,
          // document.documentElement.scrollHeight,
          document.body.clientHeight,
          document.body.scrollHeight,
        ),
      );
    }
  }
  waitForBridge();
};

/**
 * ## AutoHeightWebView
 * WebView inject a script to determine the height of a webpage dynamically
 */
export default class AutoHeightWebView extends Component {
  state = {
    webViewHeight: Number,
  };

  static defaultProps = {
    autoHeight: true,
  };

  constructor(props) {
    super(props);
    this.state = {
      webViewHeight: this.props.defaultHeight,
    };

    this._onMessage = this._onMessage.bind(this);
  }

  _onMessage(e) {
    // console.log(
    //   "******* onMessage **********",
    //   e.nativeEvent.data,
    //   e.nativeEvent
    // );
    this.setState({
      webViewHeight: parseInt(e.nativeEvent.data),
    });
  }

  onError(e) {
    console.log('onError  - ', e);
  }

  stopLoading() {
    this.webview.stopLoading();
  }

  reload() {
    this.webview.reload();
  }

  onLoadFineshed() {
    console.log(' Webview loading end');
  }

  // renderLoadingView = () => {
  //   return (
  //     <ActivityIndicator
  //       color = {Config.toolbarColor}
  //       size="large"
  //       style={{
  //         flex: 1,
  //         left: 0,
  //         right: 0,
  //         top: 0,
  //         bottom: 0,
  //         position: 'absolute',
  //         alignItems: 'center',
  //         justifyContent: 'center' }}
  //     />
  //   );
  // };

  render() {
    const _w = this.props.width || Dimensions.get('window').width;
    let _h = this.props.autoHeight
      ? this.state.webViewHeight
      : this.props.defaultHeight;

    // console.log('redner - w, h, autoheght = ', _w, _h, this.props.autoHeight, this.state.webViewHeight, this.props.defaultHeight);

    const androidScript =
      "window.ReactNativeWebView.postMessage = String(Object.hasOwnProperty).replace('hasOwnProperty', 'postMessage');" +
      '(' +
      String(injectedScriptAndroid) +
      ')();';
    const iosScript =
      '(' +
      String(injectedScriptIOS) +
      ')();' +
      "window.ReactNativeWebView.postMessage = String(Object.hasOwnProperty).replace('hasOwnProperty', 'postMessage');";

    // const testScript =
    // if autoheight is null set height to be screenHeight
    if (!_h) {
      // console.log(' webview _h is null ')
      _h = height;
    }
    // console.log("redner - h, autoheght = ", _h, this.props.autoHeight);

    return (
      <WebView
        ref={ref => {
          this.webview = ref;
        }}
        scalesPageToFit={false}
        injectedJavaScript={Platform.OS === 'ios' ? iosScript : androidScript}
        scrollEnabled={this.props.scrollEnabled || false}
        onMessage={this._onMessage}
        onError={this.onError}
        javaScriptEnabled={true}
        onLoadEnd={this.props.onLoadEnd}
        // renderLoading={this.renderLoadingView}
        automaticallyAdjustContentInsets={true}
        {...this.props}
        style={[{width: _w, flex: 1}, this.props.style, {height: _h}]}
      />
    );
  }
}
//     console.log('redner - w, h, autoheght = ', _w, _h, this.props.autoHeight);
// style={[{ width: _w }, this.props.style, { height: _h }]}
