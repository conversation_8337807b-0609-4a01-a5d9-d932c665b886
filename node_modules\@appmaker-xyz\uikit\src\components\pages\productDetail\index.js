import React from 'react';
import { StyleSheet, ScrollView, SafeAreaView } from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import {
  ActionButtons,
  ProductData,
  ProductImage,
  ProductInfo,
} from './components';
import { ActionBar } from '../../../components';

const ProductDetail = () => {
  const { spacing, color } = useApThemeState();
  const styles = allStyles({ spacing, color });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.container}>
        <ProductImage attributes={{ imageList: productXYZ.images }} />
        <ProductData
          attributes={{
            outOfStock: false,
            title: productXYZ.title,
            regularPrice: productXYZ.mrp,
            salePrice: productXYZ.sellingPrice,
            salePercentage: '20% OFF',
            vendorName: productXYZ.seller,
            stockPhrase: 'Only 1 Left in Stock',
          }}
        />
        <ProductInfo
          attributes={{
            blockTitle: 'About Product',
            accessButton: '',
            text: productXYZ.description,
          }}
        />
        <ProductInfo
          attributes={{
            blockTitle: 'Select Color',
            accessButton: '',
            variations: productXYZ.variations.color,
            variationType: 'color',
          }}
        />
        <ProductInfo
          attributes={{
            blockTitle: 'Select Color',
            accessButton: '',
            variations: productXYZ.variations.color,
          }}
        />
        <ProductInfo
          attributes={{
            blockTitle: '',
            accessButton: '',
            counter: true,
          }}
        />
        <ProductInfo
          attributes={{
            blockTitle: 'Select Payment Method',
            accessButton: '',
            paymentOptions: productXYZ.paymentOptions,
          }}
        />
        <ProductInfo
          attributes={{
            blockTitle: 'Select Size (If >14 Items)',
            accessButton: 'Size Chart',
            variations: productXYZ.variations.size,
          }}
        />
        <ProductInfo
          attributes={{
            blockTitle: 'Large text variations',
            accessButton: 'More action',
            variations: productXYZ.variations.largeVariations,
            variationType: 'largeText',
          }}
        />
        <ProductInfo
          attributes={{
            blockTitle: 'Check Shipping Cost',
            accessButton: '',
            pinCheck: true,
          }}
        />
        <ProductInfo
          attributes={{
            blockTitle: 'Reviews',
            accessButton: 'Read all reviews',
            productReview: productXYZ.productReview,
          }}
        />
        <ProductInfo
          attributes={{
            blockTitle: 'Categories',
            accessButton: '',
            linkCloud: productXYZ.categories,
          }}
        />
        <ProductInfo
          attributes={{
            blockTitle: 'Tags',
            accessButton: '',
            linkCloud: productXYZ.categories,
          }}
        />
        <ProductInfo
          attributes={{
            blockTitle: 'Pick a date',
            accessButton: '',
            datePicker: true,
          }}
        />

        <ActionBar
          attributes={{ title: 'Go Somewhere' }}
          onPress={() => console.log('Go somewhre')}
        />
        <ActionBar
          attributes={{ title: 'Go Somewhere', leftIcon: 'settings' }}
          onPress={() => console.log('Go somewhre')}
        />
      </ScrollView>
      <ActionButtons
        attributes={{
          addCartText: 'Add to Cart',
          buyNowText: 'Buy Now',
          outOfStock: false,
          loading: false,
          disabled: false,
        }}
      />
    </SafeAreaView>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.light,
    },
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: spacing.base,
    },
  });

const productXYZ = {
  title: "Adidas Unisex's Wucht P3 Badminton Shoes",
  mrp: 'Rs.2,999.00',
  sellingPrice: 'Rs. 1999.00',
  seller: 'PARAMA HEARTBEAT SPORTS (INDIA) PRIVATE LIMITED',
  description:
    'High-tech meets super comfort in these performance driven adidas badminton shoes. These unisex shoes have a snug-fitting synthetic upper with a comfortable Cloudfoam midsole. The High wrap Rubber outsole provide maximum flexibility to your feet.',
  images: [
    {
      key: 1,
      uri:
        'https://images-na.ssl-images-amazon.com/images/I/61v4YzYwZ0L._UL1024_.jpg',
    },
    {
      key: 2,
      uri:
        'https://images-na.ssl-images-amazon.com/images/I/51EkiEkT8OL._UL1000_.jpg',
    },
    {
      key: 3,
      uri:
        'https://images-na.ssl-images-amazon.com/images/I/61zCVZyBFXL._UL1280_.jpg',
    },
    {
      key: 4,
      uri:
        'https://images-na.ssl-images-amazon.com/images/I/51CyqAI6ZsL._UL1024_.jpg',
    },
  ],
  variations: {
    size: [
      {
        id: '1',
        variationName: '6 UK',
      },
      {
        id: '2',
        variationName: '7 UK',
      },
      {
        id: '3',
        variationName: '8 UK',
      },
      {
        id: '4',
        variationName: '9 UK',
      },
      {
        id: '5',
        variationName: '10 UK',
      },
      {
        id: '6',
        variationName: '11 UK',
      },
      {
        id: '7',
        variationName: '12 UK',
      },
      {
        id: '1',
        variationName: '6 UK',
      },
      {
        id: '2',
        variationName: '7 UK',
      },
      {
        id: '3',
        variationName: '8 UK',
      },
      {
        id: '4',
        variationName: '9 UK',
      },
      {
        id: '5',
        variationName: '10 UK',
      },
      {
        id: '6',
        variationName: '11 UK',
      },
      {
        id: '7',
        variationName: '12 UK',
      },
      {
        id: '7',
        variationName: '12 UK',
      },
    ],
    color: [
      {
        id: '1',
        variationName: 'Red',
      },
      {
        id: '2',
        variationName: 'Blue',
      },
      {
        id: '3',
        variationName: 'Yellow',
      },
      {
        id: '4',
        variationName: 'Black',
      },
      {
        id: '5',
        variationName: 'Pink',
      },
      {
        id: '6',
        variationName: 'Teal',
      },
      {
        id: '7',
        variationName: 'Orange',
      },
      {
        id: '8',
        variationName: 'Grey',
      },
      {
        id: '9',
        variationName: 'Maroon',
      },
      {
        id: '10',
        variationName: 'Green',
      },
      {
        id: '10',
        variationName: 'White',
      },
    ],
    largeVariations: [
      {
        id: '1',
        variationName:
          'Lorem ipsum dolor sit amet, consectetur adipisicing elit.',
      },
      {
        id: '2',
        variationName:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit',
      },
      {
        id: '3',
        variationName:
          'Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam',
      },
      {
        id: '4',
        variationName:
          'Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium',
      },
      {
        id: '5',
        variationName:
          'architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam',
      },
      {
        id: '6',
        variationName:
          'Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
      },
      {
        id: '7',
        variationName:
          'At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident',
      },
      {
        id: '8',
        variationName:
          'Architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam',
      },
    ],
  },
  productReview: [
    {
      key: 1,
      userName: '<EMAIL>',
      userImage:
        'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_960_720.png',
      rating: '4.0',
      review:
        'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Fugiat quas',
    },
    {
      key: 2,
      userName: 'User Two',
      userImage:
        'https://images.generated.photos/MmIA9OJle_-xI4Ygh656UsXOLl8HG_wfAX0mBHBPonI/rs:fit:512:512/Z3M6Ly9nZW5lcmF0/ZWQtcGhvdG9zL3Yy/XzA3ODQxNTEuanBn.jpg',
      rating: '3.0',
      review:
        'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Fugiat quas',
    },
    {
      key: 3,
      userName: 'User Three',
      userImage:
        'https://images.generated.photos/KAt2IDBV1j5xNK0AnWE9GACDGz5C7xNnfSx0q8OZ-7c/rs:fit:512:512/Z3M6Ly9nZW5lcmF0/ZWQtcGhvdG9zL3Ry/YW5zcGFyZW50X3Yy/L3YyXzA2MjgxMjku/cG5n.png',
      rating: '2.0',
      review:
        'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Fugiat quas',
    },
  ],
  categories: [
    {
      key: 1,
      category: 'Health',
    },
    {
      key: 2,
      category: 'Shoes',
    },
    {
      key: 3,
      category: 'Footwear',
    },
    {
      key: 4,
      category: 'Sports',
    },
    {
      key: 5,
      category: 'Badminton',
    },
    {
      key: 6,
      category: 'Activity',
    },
    {
      key: 7,
      category: 'Unisex',
    },
    {
      key: 8,
      category: 'Adidas',
    },
  ],
  paymentOptions: [
    {
      key: 1,
      option: 'Pay in full',
    },
    {
      key: 2,
      option: 'Payment with plan',
    },
  ],
};

export default ProductDetail;
