import React from 'react';
import {
  Layout,
  AppmakerText,
  AppTouchable,
  Badge,
} from '../../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/AntDesign';

export function BottomRight(props) {
  return (
    <Layout
      style={[
        props.styles.overlayContainer,
        props?.__appmakerCustomStyles?.overlay_container,
      ]}>
      {props.wishList == true ? (
        <AppTouchable
          onPress={() => {
            props.setAddwish(!props.addwish);
            props.onSaved(!props.addwish);
          }}
          style={[
            props.styles.wishListIcon,
            props?.__appmakerCustomStyles?.wish_list_icon_container,
          ]}>
          <Icon
            name={props.addwish === false ? 'hearto' : 'heart'}
            color={
              props.addwish === false ? props.color.demiDark : props.color.dark
            }
            size={props.md}
            style={props?.__appmakerCustomStyles?.wish_list_icon}
          />
        </AppTouchable>
      ) : null}
    </Layout>
  );
}
export function ProductData(props) {
  return (
    <Layout style={props.productTitle}>
      {props.show_last_few_remaining &&
      (props.show_last_few_remaining === 'true' ||
        props.show_last_few_remaining === true) ? (
        <AppmakerText
          category="highlighter2"
          fontColor={props.brandColor?.secondary}
          style={props?.__appmakerCustomStyles?.last_few_remaining_text}>
          {props.last_few_remaining_text}
        </AppmakerText>
      ) : null}
      {props.in_stock == true ? null : (
        <AppmakerText
          category="highlighter2"
          fontColor={props.brandColor?.secondary}
          style={props?.__appmakerCustomStyles?.out_of_stock_text}>
          Out of Stock
        </AppmakerText>
      )}
      <AppmakerText
        style={props?.__appmakerCustomStyles?.product_title}
        numberOfLines={props.titleNumberOfLines}
        category="bodySubText">
        {props.title}
      </AppmakerText>
    </Layout>
  );
}
export function PriceData(props) {
  return (
    <Layout
      style={[
        props.styles.priceDataContainer,
        props?.__appmakerCustomStyles?.price_data_container,
      ]}>
      <AppmakerText
        category="bodySubText"
        style={props?.__appmakerCustomStyles?.sale_price}>
        {props.salePrice}
      </AppmakerText>
      <AppmakerText
        category="highlighter2"
        fontColor={
          props?.__appmakerCustomStyles?.offer_price?.color
            ? props?.__appmakerCustomStyles?.offer_price?.color
            : props.brandColor?.primary
        }
        status="demiDark"
        hideText={!props.onSale && !props.priceSale}
        style={props.styles.offerPrice}>
        {props.regularPrice}
      </AppmakerText>
      {props.onSale && (
        <Badge
          style={props.styles.saleBadgeStyle}
          status="success"
          text={props.salePercentage}
          customStyles={props?.__appmakerCustomStyles?.sale_badge}
        />
      )}
    </Layout>
  );
}
export function ExtraContents(props) {
  return (
    <Layout style={props.styles.extraContentContainer}>
      {props.new_product ? (
        <AppmakerText
          category="highlighter2"
          fontColor="#884A49"
          style={props?.__appmakerCustomStyles?.new_product_text}>
          {props.new_product.value}
        </AppmakerText>
      ) : null}
      {props.customisable ? (
        <AppmakerText
          category="highlighter2"
          fontColor="#884A49"
          style={props?.__appmakerCustomStyles?.customisable_text}>
          Customisable
        </AppmakerText>
      ) : null}
    </Layout>
  );
}
