{"version": 3, "names": ["getEnv", "defaultValue", "process", "env", "BABEL_ENV", "NODE_ENV"], "sources": ["../../../src/config/helpers/environment.ts"], "sourcesContent": ["export function getEnv(defaultValue: string = \"development\"): string {\n  return process.env.BABEL_ENV || process.env.NODE_ENV || defaultValue;\n}\n"], "mappings": ";;;;;;AAAO,SAASA,MAAMA,CAACC,YAAoB,GAAG,aAAa,EAAU;EACnE,OAAOC,OAAO,CAACC,GAAG,CAACC,SAAS,IAAIF,OAAO,CAACC,GAAG,CAACE,QAAQ,IAAIJ,YAAY;AACtE;AAAC"}