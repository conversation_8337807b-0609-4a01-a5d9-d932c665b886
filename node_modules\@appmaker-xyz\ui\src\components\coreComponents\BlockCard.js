import React, { useState } from 'react';
import { LayoutAnimation, Platform, UIManager } from 'react-native';
import { AppTouchable, ThemeText, Layout } from '../atoms';
import { testProps } from '@appmaker-xyz/core';
import Icon from 'react-native-vector-icons/Feather';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';

const BlockCard = ({ clientId, attributes, children, onPress, onAction }) => {
  const {
    title,
    showTitle,
    accessButton,
    expandable = false,
    expanded = true,
    hiddenText = <Icon name="plus" size={18} />,
    expandedText = <Icon name="plus" size={18} />,
    childContainerStyle,
    appmakerAction,
    accessButtonColor,
    accessButtonStyle,
    wholeContainerStyle,
    __display = true,
  } = attributes;
  const [visibility, setVisibility] = useState(expanded);

  const { styles, theme } = useStyles(stylesheet);

  if (Platform.OS === 'android') {
    if (UIManager.setLayoutAnimationEnabledExperimental) {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }
  }
  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setVisibility(!visibility);
  };
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  if (__display === false || __display === 'false') {
    return null;
  }

  return (
    <AppTouchable
      onPress={() => (expandable ? toggleExpand() : null)}
      disableTouch={!expandable}>
      <Layout
        style={[
          title || accessButton ? styles.container : null,
          wholeContainerStyle,
        ]}
        {...testProps(clientId)}>
        {(showTitle !== false && title) || accessButton ? (
          <Layout style={styles.titleContainer}>
            <ThemeText size="md" fontFamily="medium" color={theme.colors.text}>
              {title}
            </ThemeText>
            {accessButton !== '' && (
              <AppTouchable
                onPress={() => {
                  onPressHandle && onPressHandle();
                  expandable && toggleExpand();
                }}
                accessoryLeft={() =>
                  expandedText == '' && hiddenText == '' ? (
                    <Icon name={visibility ? 'minus' : 'plus'} size={18} />
                  ) : null
                }
                style={accessButtonStyle}>
                <ThemeText
                  size="sm"
                  fontFamily="medium"
                  color={accessButtonColor}>
                  {expandable
                    ? visibility
                      ? expandedText
                      : hiddenText
                    : accessButton}
                </ThemeText>
              </AppTouchable>
            )}
          </Layout>
        ) : null}
        {visibility && <Layout style={childContainerStyle}>{children}</Layout>}
      </Layout>
    </AppTouchable>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.background,
    marginBottom: 4,
    paddingTop: 8,
  },
  titleContainer: {
    paddingHorizontal: 12,
    paddingBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  noPadding: {
    paddingHorizontal: 0,
  },
}));

export default BlockCard;
