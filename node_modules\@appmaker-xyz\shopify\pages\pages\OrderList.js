const page = {
  blocks: [
    {
      name: 'shopify/order-card',
      contextValues: true,
      clientId: 'order-list-card',
      attributes: {
        data: '{{blockItem}}',
        orderId: '{{blockItem.node.name.replace("#","")}}',
        items: '{{blockItem.node.lineItems.edges.length}}',
        status: '{{blockItem.node.fulfillmentStatus}}',
        firstItemTitle: '{{blockItem.node.lineItems.edges[0].node.title}}',
        paymentMethod: '{{blockItem.node.financialStatus}}',
        orderDate: "{{momentJS(blockItem.node.processedAt,'MMM Do YY')}}",
        appmakerAction: {
          pageId: 'orderDetail',
          params: {
            pageData: '{{blockItem}}',
          },
          action: 'OPEN_INAPP_PAGE',
        },
        featureImg:
          '{{ blockItem.node && blockItem.node.lineItems && blockItem.node.lineItems.edges[0] && blockItem.node.lineItems.edges[0].node && blockItem.node.lineItems.edges[0].node.variant ?  blockItem.node.lineItems.edges[0].node.variant.image.url : "" }}',
        totalAmount:
          '{{blockItem.node.totalPrice.amount}} {{blockItem.node.totalPrice.currencyCode}}',
        dataSource: {
          source: 'shopify',
          attributes: {
            mapping: {
              items: 'data.data.customer.orders.edges',
            },
            methodName: 'orders',
            params: '{{blockData}}',
          },
          repeatable: 'Yes',
          repeatItem: 'DataSource',
        },
      },
    },
  ],
  attributes: {
    showLoadingTillData: true,
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  title: 'Orders',
};
export default page;
