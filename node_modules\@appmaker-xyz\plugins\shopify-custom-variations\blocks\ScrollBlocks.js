import React from 'react';
import { AppImage } from '@appmaker-xyz/uikit';
import { useApThemeState } from '@appmaker-xyz/uikit/src/index';
import { AppTouchable, Layout } from '@appmaker-xyz/uikit/src/components/index';
import { ScrollView, StyleSheet, FlatList } from 'react-native';
// import { FlatList } from 'react-navigation';

function ImageItem({ selected, imageUrl, appmakerAction, onAction }) {
  const { spacing, color } = useApThemeState();
  const styles = allStyles({ spacing, color });
  const variationItemSelectedStyle = [styles.variationItemSelected];
  return (
    <AppTouchable
      style={[styles.variationItem, selected && variationItemSelectedStyle]}
      onPress={() => onAction(appmakerAction)}>
      <AppImage uri={imageUrl} style={styles.image} />
    </AppTouchable>
  );
}

export const ScrollBlocks = ({ attributes, onAction }) => {
  let { products, __appmakerCustomStyles = {} } = attributes;
  const { spacing, color } = useApThemeState();
  const styles = allStyles({ spacing, color });
  const variationItemSelectedStyle = [styles.variationItemSelected];

  if (__appmakerCustomStyles) {
    variationItemSelectedStyle.push(
      __appmakerCustomStyles?.variation_block
        ?.variation_item_selected_container,
    );
  }
  function renderItem({ item }) {
    return (
      <ImageItem
        imageUrl={item.imageUrl}
        appmakerAction={item.appmakerAction}
        onAction={onAction}
      />
    );
  }
  // console.log(products);
  return (
    <ScrollView
      horizontal={true}
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}
      style={styles.container}>
      <FlatList
        horizontal={true}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        data={products}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
      />
      <Layout style={{ marginRight: spacing.base }} />
    </ScrollView>
  );
};

export const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      paddingBottom: spacing.small,
    },
    variationItem: {
      position: 'relative',
      backgroundColor: color.light,
      borderRadius: spacing.nano,
      marginLeft: spacing.base,
      borderColor: color.light,
      borderWidth: 1,
      marginVertical: spacing.nano,
    },
    variationText: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.base,
    },
    variationItemSelected: {
      borderColor: color.primary,
      overflow: 'hidden',
    },
    listVariationItem: {
      width: '100%',
      marginLeft: 0,
      marginVertical: spacing.mini,
    },
    selectBlock: {
      marginHorizontal: spacing.base,
      marginBottom: spacing.base,
      backgroundColor: color.light,
      padding: spacing.base,
      borderRadius: spacing.nano,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    colorSelector: {
      width: spacing.lg * 2,
      height: spacing.lg * 2,
      borderRadius: spacing.xl,
      borderColor: color.light,
      borderWidth: spacing.nano / 2,
      marginLeft: spacing.base,
      justifyContent: 'center',
      alignItems: 'center',
    },
    colorSelectedIndicator: {
      paddingBottom: spacing.base,
      paddingHorizontal: spacing.base,
    },
    modal: {
      justifyContent: 'flex-end',
      marginVertical: 0,
      marginHorizontal: 1,
    },
    modalBody: {
      backgroundColor: color.white,
      padding: spacing.md,
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      borderTopStartRadius: spacing.md,
      borderTopEndRadius: spacing.md,
      flexDirection: 'column',
      maxHeight: '80%',
    },
    modalHeader: {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderBottomWidth: 1,
      borderBottomColor: color.light,
      paddingBottom: spacing.base,
    },
    modalContent: {
      paddingVertical: spacing.base,
      flexDirection: 'column',
      width: '100%',
    },
    muted: {
      backgroundColor: color.white,
      borderColor: color.grey,
    },
    muteImage: {
      width: '100%',
      height: '100%',
      position: 'absolute',
      zIndex: 10,
      resizeMode: 'stretch',
    },
    imageContainer: {
      position: 'relative',
      backgroundColor: color.light,
      borderRadius: spacing.nano,
      marginLeft: spacing.base,
      borderColor: color.light,
      borderWidth: 1,
      marginVertical: spacing.nano,
    },
    image: {
      height: 75,
      width: 75,
      resizeMode: 'cover',
    },
  });
