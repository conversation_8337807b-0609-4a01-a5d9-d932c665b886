import { useEffect } from 'react';
import { useState } from 'react';

export function useProductDetail({
  attributes,
  onAction,
  blockData,
  pageDispatch,
}) {
  const {
    priceSale = '0',
    in_stock,
    title,
    regularPrice,
    salePrice,
    onSale,
    salePercentage,
    currency_symbol,
    regular_price_value,
    price_value,
    vendorName,
    stockPhrase,
    average_rating,
    saved_amount,
    priceDetailModal = true,
    customStyles,
    tax_included_text = false,
    brandName,
    product_subtitle,
    titleNumberOfLines,
    __appmakerCustomStyles = {},
    reviews_count,
  } = attributes;
  return {
    title,
    product_subtitle,
    salePrice,
    regularPrice,
    average_rating,
    reviews_count,
  };
}
