import React from 'react';
import {
  Image,
  StyleSheet,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { appSettings } from '@appmaker-xyz/core';
import { spacing } from '../../../styles';
import { useState } from 'react';
import { AppmakerImage } from '@appmaker-xyz/react-native';
import { AppmakerRemoteImage } from '@appmaker-xyz/react-native';

const ImageLoading = ({ style }) => {
  return (
    <View style={[styles.loadingContainer, style]}>
      <AppmakerRemoteImage name="APP_TITLE_IMAGE" style={styles.loadingImage} />
    </View>
  );
};

const AppImage = ({ uri, style, src, fastImage, ...props }) => {
  const [imageLoading, setImageLoading] = useState(true);
  return (
    <>
      {/* {uri && imageLoading && <ImageLoading />} */}
      {fastImage ? (
        <AppmakerImage
          source={uri ? { uri: uri } : src}
          style={style}
          onLoad={() => setImageLoading(false)}
          {...props}
        />
      ) : (
        <Image
          source={uri ? { uri: uri } : src}
          style={style}
          onLoad={() => setImageLoading(false)}
          {...props}
        />
      )}
    </>
  );
};

const ListImage = ({ uri }) => {
  return <Image source={{ uri: uri }} style={styles.listImage} />;
};

const TouchableImage = ({ attributes, onAction }) => {
  const {
    conditionalClick,
    conditionalClickCount,
    conditionalClickAction,
    appmakerAction,
    style,
    src,
    uri,
  } = attributes;
  let clickCount = 0;
  return (
    <TouchableWithoutFeedback
      onPress={() => {
        clickCount++;
        if (conditionalClick && clickCount === conditionalClickCount) {
          clickCount = 0;
          onAction(conditionalClickAction);
        } else if (appmakerAction) {
          onAction(appmakerAction);
        }
      }}>
      <Image source={uri ? { uri: uri } : src} style={{ ...style }} />
    </TouchableWithoutFeedback>
  );
};

const ProductScrollerImage = ({ uri }) => {
  return <Image source={{ uri: uri }} style={styles.scrollerImage} />;
};

const LargeImage = ({ uri }) => {
  return <Image source={{ uri: uri }} style={styles.largeImage} />;
};

const styles = StyleSheet.create({
  listImage: {
    width: 85,
    height: 85,
    resizeMode: appSettings.getOptionAsBoolean('product_list_type')
      ? 'contain'
      : 'cover',
    borderRadius: spacing.nano,
  },
  scrollerImage: {
    width: 170,
    height: 180,
    borderRadius: spacing.nano,
  },
  largeImage: {
    width: '100%',
    height: 130,
    resizeMode: 'cover',
    borderRadius: spacing.nano,
  },
  loadingContainer: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fafafa',
  },
  loadingImage: {
    width: 120,
    height: 60,
    resizeMode: 'contain',
    opacity: 0.2,
  },
});

export {
  ListImage,
  ProductScrollerImage,
  LargeImage,
  AppImage,
  TouchableImage,
};
