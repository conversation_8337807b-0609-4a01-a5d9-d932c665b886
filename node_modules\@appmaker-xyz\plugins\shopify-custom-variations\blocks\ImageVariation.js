import { View, Text } from 'react-native';
import React, { useEffect } from 'react';
import { runDataSource, appPluginStoreApi } from '@appmaker-xyz/core';
import { ScrollBlocks } from './ScrollBlocks';
import { useState } from 'react';
import BlockCard from '@appmaker-xyz/uikit/src/components/organisms/card/BlockCard';
function imageResize(imageURL, size) {
  if (imageURL && size) {
    let separatorIndex = imageURL.lastIndexOf('.');
    const resizedImage = `${imageURL.slice(
      0,
      separatorIndex,
    )}_${size.toLowerCase()}${imageURL.slice(separatorIndex)}`;
    return resizedImage;
  }
}
async function loadProducts(ids) {
  const dataSource = {
    attributes: {},
    source: 'shopify',
  };
  const [response] = await runDataSource(
    {
      dataSource,
    },
    {
      methodName: 'products',
      params: {
        ids,
      },
    },
  );
  const products = response.data.data.nodes;
  const hideOutOfStock =
    appPluginStoreApi()?.getState()?.plugins?.['shopify-custom-variations']
      ?.settings?.hide_out_of_stock || false;
  let finalProducts = [];
  products.map((product) => {
    if (!product?.availableForSale && hideOutOfStock) {
      return null;
    }
    finalProducts.push({
      id: product.id,
      appmakerAction: {
        pageId: 'productDetail',
        params: {
          pageData: { node: product },
        },
        action: 'OPEN_PRODUCT_DETAIL',
      },
      imageUrl: imageResize(product.images.edges[0].node.src, 'x150'),
    });
  });
  return finalProducts;
}
export default function ImageVariation({ attributes, onAction }) {
  const { productIds: productIdString, title } = attributes;
  const [products, setProducts] = useState([]);
  useEffect(() => {
    async function loadData() {
      try {
        const productsArray = await loadProducts(JSON.parse(productIdString));
        setProducts(productsArray);
      } catch (error) {
        console.log(error);
      }
    }
    loadData();
  }, [productIdString]);

  return products.length === 0 ? null : (
    <BlockCard
      attributes={{
        title,
      }}>
      <ScrollBlocks
        onAction={onAction}
        attributes={{
          products,
        }}
      />
    </BlockCard>
  );
}
