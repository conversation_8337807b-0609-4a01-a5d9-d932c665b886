{"version": 3, "names": ["_generated", "require", "_constants", "isLet", "node", "isVariableDeclaration", "kind", "BLOCK_SCOPED_SYMBOL"], "sources": ["../../src/validators/isLet.ts"], "sourcesContent": ["import { isVariableDeclaration } from \"./generated\";\nimport { BLOCK_SCOPED_SYMBOL } from \"../constants\";\nimport type * as t from \"..\";\n\n/**\n * Check if the input `node` is a `let` variable declaration.\n */\nexport default function isLet(node: t.Node): boolean {\n  return (\n    isVariableDeclaration(node) &&\n    (node.kind !== \"var\" ||\n      // @ts-expect-error Fixme: document private properties\n      node[BLOCK_SCOPED_SYMBOL])\n  );\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AAMe,SAASE,KAAKA,CAACC,IAAY,EAAW;EACnD,OACE,IAAAC,gCAAqB,EAACD,IAAI,CAAC,KAC1BA,IAAI,CAACE,IAAI,KAAK,KAAK,IAElBF,IAAI,CAACG,8BAAmB,CAAC,CAAC;AAEhC"}