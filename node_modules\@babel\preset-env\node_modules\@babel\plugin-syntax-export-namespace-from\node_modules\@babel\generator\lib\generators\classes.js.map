{"version": 3, "names": ["_t", "require", "isExportDefaultDeclaration", "isExportNamedDeclaration", "ClassDeclaration", "node", "parent", "inExport", "_shouldPrintDecoratorsBeforeExport", "printJoin", "decorators", "declare", "word", "space", "abstract", "id", "print", "typeParameters", "superClass", "superTypeParameters", "implements", "printList", "body", "ClassBody", "token", "length", "newline", "indent", "printSequence", "dedent", "endsWith", "sourceWithOffset", "loc", "rightBrace", "ClassProperty", "_node$key$loc", "_node$key$loc$end", "endLine", "key", "end", "line", "catchUp", "tsPrintClassMemberModifiers", "computed", "_variance", "optional", "definite", "typeAnnotation", "value", "semicolon", "ClassAccessorProperty", "_node$key$loc2", "_node$key$loc2$end", "ClassPrivateProperty", "static", "ClassMethod", "_classMethodHead", "ClassPrivateMethod", "_node$key$loc3", "_node$key$loc3$end", "_methodHead", "StaticBlock"], "sources": ["../../src/generators/classes.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport {\n  isExportDefaultDeclaration,\n  isExportNamedDeclaration,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport * as charCodes from \"charcodes\";\n\nexport function ClassDeclaration(\n  this: Printer,\n  node: t.ClassDeclaration,\n  parent: t.Node,\n) {\n  const inExport =\n    isExportDefaultDeclaration(parent) || isExportNamedDeclaration(parent);\n\n  if (\n    !inExport ||\n    !this._shouldPrintDecoratorsBeforeExport(\n      parent as t.ExportDeclaration & { declaration: t.ClassDeclaration },\n    )\n  ) {\n    this.printJoin(node.decorators, node);\n  }\n\n  if (node.declare) {\n    // TS\n    this.word(\"declare\");\n    this.space();\n  }\n\n  if (node.abstract) {\n    // TS\n    this.word(\"abstract\");\n    this.space();\n  }\n\n  this.word(\"class\");\n\n  if (node.id) {\n    this.space();\n    this.print(node.id, node);\n  }\n\n  this.print(node.typeParameters, node);\n\n  if (node.superClass) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.print(node.superClass, node);\n    this.print(node.superTypeParameters, node);\n  }\n\n  if (node.implements) {\n    this.space();\n    this.word(\"implements\");\n    this.space();\n    this.printList(node.implements, node);\n  }\n\n  this.space();\n  this.print(node.body, node);\n}\n\nexport { ClassDeclaration as ClassExpression };\n\nexport function ClassBody(this: Printer, node: t.ClassBody) {\n  this.token(\"{\");\n  if (node.body.length === 0) {\n    this.token(\"}\");\n  } else {\n    this.newline();\n\n    this.indent();\n    this.printSequence(node.body, node);\n    this.dedent();\n\n    if (!this.endsWith(charCodes.lineFeed)) this.newline();\n\n    this.sourceWithOffset(\"end\", node.loc, 0, -1);\n\n    this.rightBrace();\n  }\n}\n\nexport function ClassProperty(this: Printer, node: t.ClassProperty) {\n  this.printJoin(node.decorators, node);\n\n  // catch up to property key, avoid line break\n  // between member modifiers and the property key.\n  const endLine = node.key.loc?.end?.line;\n  if (endLine) this.catchUp(endLine);\n\n  this.tsPrintClassMemberModifiers(node);\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(node.key, node);\n    this.token(\"]\");\n  } else {\n    this._variance(node);\n    this.print(node.key, node);\n  }\n\n  // TS\n  if (node.optional) {\n    this.token(\"?\");\n  }\n  if (node.definite) {\n    this.token(\"!\");\n  }\n\n  this.print(node.typeAnnotation, node);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value, node);\n  }\n  this.semicolon();\n}\n\nexport function ClassAccessorProperty(\n  this: Printer,\n  node: t.ClassAccessorProperty,\n) {\n  this.printJoin(node.decorators, node);\n\n  // catch up to property key, avoid line break\n  // between member modifiers and the property key.\n  const endLine = node.key.loc?.end?.line;\n  if (endLine) this.catchUp(endLine);\n\n  // TS does not support class accessor property yet\n  this.tsPrintClassMemberModifiers(node);\n\n  this.word(\"accessor\", true);\n  this.space();\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(node.key, node);\n    this.token(\"]\");\n  } else {\n    // Todo: Flow does not support class accessor property yet.\n    this._variance(node);\n    this.print(node.key, node);\n  }\n\n  // TS\n  if (node.optional) {\n    this.token(\"?\");\n  }\n  if (node.definite) {\n    this.token(\"!\");\n  }\n\n  this.print(node.typeAnnotation, node);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value, node);\n  }\n  this.semicolon();\n}\n\nexport function ClassPrivateProperty(\n  this: Printer,\n  node: t.ClassPrivateProperty,\n) {\n  this.printJoin(node.decorators, node);\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  this.print(node.key, node);\n  this.print(node.typeAnnotation, node);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value, node);\n  }\n  this.semicolon();\n}\n\nexport function ClassMethod(this: Printer, node: t.ClassMethod) {\n  this._classMethodHead(node);\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function ClassPrivateMethod(this: Printer, node: t.ClassPrivateMethod) {\n  this._classMethodHead(node);\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function _classMethodHead(\n  this: Printer,\n  node: t.ClassMethod | t.ClassPrivateMethod | t.TSDeclareMethod,\n) {\n  this.printJoin(node.decorators, node);\n\n  // catch up to method key, avoid line break\n  // between member modifiers/method heads and the method key.\n  const endLine = node.key.loc?.end?.line;\n  if (endLine) this.catchUp(endLine);\n\n  this.tsPrintClassMemberModifiers(node);\n  this._methodHead(node);\n}\n\nexport function StaticBlock(this: Printer, node: t.StaticBlock) {\n  this.word(\"static\");\n  this.space();\n  this.token(\"{\");\n  if (node.body.length === 0) {\n    this.token(\"}\");\n  } else {\n    this.newline();\n    this.printSequence(node.body, node, {\n      indent: true,\n    });\n\n    this.sourceWithOffset(\"end\", node.loc, 0, -1);\n\n    this.rightBrace();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAGsB;EAFpBC,0BAA0B;EAC1BC;AAAwB,IAAAH,EAAA;AAKnB,SAASI,gBAAgBA,CAE9BC,IAAwB,EACxBC,MAAc,EACd;EACA,MAAMC,QAAQ,GACZL,0BAA0B,CAACI,MAAM,CAAC,IAAIH,wBAAwB,CAACG,MAAM,CAAC;EAExE,IACE,CAACC,QAAQ,IACT,CAAC,IAAI,CAACC,kCAAkC,CACtCF,MAAM,CACP,EACD;IACA,IAAI,CAACG,SAAS,CAACJ,IAAI,CAACK,UAAU,EAAEL,IAAI,CAAC;EACvC;EAEA,IAAIA,IAAI,CAACM,OAAO,EAAE;IAEhB,IAAI,CAACC,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACC,KAAK,EAAE;EACd;EAEA,IAAIR,IAAI,CAACS,QAAQ,EAAE;IAEjB,IAAI,CAACF,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACC,KAAK,EAAE;EACd;EAEA,IAAI,CAACD,IAAI,CAAC,OAAO,CAAC;EAElB,IAAIP,IAAI,CAACU,EAAE,EAAE;IACX,IAAI,CAACF,KAAK,EAAE;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACU,EAAE,EAAEV,IAAI,CAAC;EAC3B;EAEA,IAAI,CAACW,KAAK,CAACX,IAAI,CAACY,cAAc,EAAEZ,IAAI,CAAC;EAErC,IAAIA,IAAI,CAACa,UAAU,EAAE;IACnB,IAAI,CAACL,KAAK,EAAE;IACZ,IAAI,CAACD,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACC,KAAK,EAAE;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACa,UAAU,EAAEb,IAAI,CAAC;IACjC,IAAI,CAACW,KAAK,CAACX,IAAI,CAACc,mBAAmB,EAAEd,IAAI,CAAC;EAC5C;EAEA,IAAIA,IAAI,CAACe,UAAU,EAAE;IACnB,IAAI,CAACP,KAAK,EAAE;IACZ,IAAI,CAACD,IAAI,CAAC,YAAY,CAAC;IACvB,IAAI,CAACC,KAAK,EAAE;IACZ,IAAI,CAACQ,SAAS,CAAChB,IAAI,CAACe,UAAU,EAAEf,IAAI,CAAC;EACvC;EAEA,IAAI,CAACQ,KAAK,EAAE;EACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACiB,IAAI,EAAEjB,IAAI,CAAC;AAC7B;AAIO,SAASkB,SAASA,CAAgBlB,IAAiB,EAAE;EAC1D,IAAI,CAACmB,SAAK,KAAK;EACf,IAAInB,IAAI,CAACiB,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;IAC1B,IAAI,CAACD,SAAK,KAAK;EACjB,CAAC,MAAM;IACL,IAAI,CAACE,OAAO,EAAE;IAEd,IAAI,CAACC,MAAM,EAAE;IACb,IAAI,CAACC,aAAa,CAACvB,IAAI,CAACiB,IAAI,EAAEjB,IAAI,CAAC;IACnC,IAAI,CAACwB,MAAM,EAAE;IAEb,IAAI,CAAC,IAAI,CAACC,QAAQ,IAAoB,EAAE,IAAI,CAACJ,OAAO,EAAE;IAEtD,IAAI,CAACK,gBAAgB,CAAC,KAAK,EAAE1B,IAAI,CAAC2B,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7C,IAAI,CAACC,UAAU,EAAE;EACnB;AACF;AAEO,SAASC,aAAaA,CAAgB7B,IAAqB,EAAE;EAAA,IAAA8B,aAAA,EAAAC,iBAAA;EAClE,IAAI,CAAC3B,SAAS,CAACJ,IAAI,CAACK,UAAU,EAAEL,IAAI,CAAC;EAIrC,MAAMgC,OAAO,IAAAF,aAAA,GAAG9B,IAAI,CAACiC,GAAG,CAACN,GAAG,sBAAAI,iBAAA,GAAZD,aAAA,CAAcI,GAAG,qBAAjBH,iBAAA,CAAmBI,IAAI;EACvC,IAAIH,OAAO,EAAE,IAAI,CAACI,OAAO,CAACJ,OAAO,CAAC;EAElC,IAAI,CAACK,2BAA2B,CAACrC,IAAI,CAAC;EAEtC,IAAIA,IAAI,CAACsC,QAAQ,EAAE;IACjB,IAAI,CAACnB,SAAK,IAAK;IACf,IAAI,CAACR,KAAK,CAACX,IAAI,CAACiC,GAAG,EAAEjC,IAAI,CAAC;IAC1B,IAAI,CAACmB,SAAK,IAAK;EACjB,CAAC,MAAM;IACL,IAAI,CAACoB,SAAS,CAACvC,IAAI,CAAC;IACpB,IAAI,CAACW,KAAK,CAACX,IAAI,CAACiC,GAAG,EAAEjC,IAAI,CAAC;EAC5B;EAGA,IAAIA,IAAI,CAACwC,QAAQ,EAAE;IACjB,IAAI,CAACrB,SAAK,IAAK;EACjB;EACA,IAAInB,IAAI,CAACyC,QAAQ,EAAE;IACjB,IAAI,CAACtB,SAAK,IAAK;EACjB;EAEA,IAAI,CAACR,KAAK,CAACX,IAAI,CAAC0C,cAAc,EAAE1C,IAAI,CAAC;EACrC,IAAIA,IAAI,CAAC2C,KAAK,EAAE;IACd,IAAI,CAACnC,KAAK,EAAE;IACZ,IAAI,CAACW,SAAK,IAAK;IACf,IAAI,CAACX,KAAK,EAAE;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAAC2C,KAAK,EAAE3C,IAAI,CAAC;EAC9B;EACA,IAAI,CAAC4C,SAAS,EAAE;AAClB;AAEO,SAASC,qBAAqBA,CAEnC7C,IAA6B,EAC7B;EAAA,IAAA8C,cAAA,EAAAC,kBAAA;EACA,IAAI,CAAC3C,SAAS,CAACJ,IAAI,CAACK,UAAU,EAAEL,IAAI,CAAC;EAIrC,MAAMgC,OAAO,IAAAc,cAAA,GAAG9C,IAAI,CAACiC,GAAG,CAACN,GAAG,sBAAAoB,kBAAA,GAAZD,cAAA,CAAcZ,GAAG,qBAAjBa,kBAAA,CAAmBZ,IAAI;EACvC,IAAIH,OAAO,EAAE,IAAI,CAACI,OAAO,CAACJ,OAAO,CAAC;EAGlC,IAAI,CAACK,2BAA2B,CAACrC,IAAI,CAAC;EAEtC,IAAI,CAACO,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC3B,IAAI,CAACC,KAAK,EAAE;EAEZ,IAAIR,IAAI,CAACsC,QAAQ,EAAE;IACjB,IAAI,CAACnB,SAAK,IAAK;IACf,IAAI,CAACR,KAAK,CAACX,IAAI,CAACiC,GAAG,EAAEjC,IAAI,CAAC;IAC1B,IAAI,CAACmB,SAAK,IAAK;EACjB,CAAC,MAAM;IAEL,IAAI,CAACoB,SAAS,CAACvC,IAAI,CAAC;IACpB,IAAI,CAACW,KAAK,CAACX,IAAI,CAACiC,GAAG,EAAEjC,IAAI,CAAC;EAC5B;EAGA,IAAIA,IAAI,CAACwC,QAAQ,EAAE;IACjB,IAAI,CAACrB,SAAK,IAAK;EACjB;EACA,IAAInB,IAAI,CAACyC,QAAQ,EAAE;IACjB,IAAI,CAACtB,SAAK,IAAK;EACjB;EAEA,IAAI,CAACR,KAAK,CAACX,IAAI,CAAC0C,cAAc,EAAE1C,IAAI,CAAC;EACrC,IAAIA,IAAI,CAAC2C,KAAK,EAAE;IACd,IAAI,CAACnC,KAAK,EAAE;IACZ,IAAI,CAACW,SAAK,IAAK;IACf,IAAI,CAACX,KAAK,EAAE;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAAC2C,KAAK,EAAE3C,IAAI,CAAC;EAC9B;EACA,IAAI,CAAC4C,SAAS,EAAE;AAClB;AAEO,SAASI,oBAAoBA,CAElChD,IAA4B,EAC5B;EACA,IAAI,CAACI,SAAS,CAACJ,IAAI,CAACK,UAAU,EAAEL,IAAI,CAAC;EACrC,IAAIA,IAAI,CAACiD,MAAM,EAAE;IACf,IAAI,CAAC1C,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACC,KAAK,EAAE;EACd;EACA,IAAI,CAACG,KAAK,CAACX,IAAI,CAACiC,GAAG,EAAEjC,IAAI,CAAC;EAC1B,IAAI,CAACW,KAAK,CAACX,IAAI,CAAC0C,cAAc,EAAE1C,IAAI,CAAC;EACrC,IAAIA,IAAI,CAAC2C,KAAK,EAAE;IACd,IAAI,CAACnC,KAAK,EAAE;IACZ,IAAI,CAACW,SAAK,IAAK;IACf,IAAI,CAACX,KAAK,EAAE;IACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAAC2C,KAAK,EAAE3C,IAAI,CAAC;EAC9B;EACA,IAAI,CAAC4C,SAAS,EAAE;AAClB;AAEO,SAASM,WAAWA,CAAgBlD,IAAmB,EAAE;EAC9D,IAAI,CAACmD,gBAAgB,CAACnD,IAAI,CAAC;EAC3B,IAAI,CAACQ,KAAK,EAAE;EACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACiB,IAAI,EAAEjB,IAAI,CAAC;AAC7B;AAEO,SAASoD,kBAAkBA,CAAgBpD,IAA0B,EAAE;EAC5E,IAAI,CAACmD,gBAAgB,CAACnD,IAAI,CAAC;EAC3B,IAAI,CAACQ,KAAK,EAAE;EACZ,IAAI,CAACG,KAAK,CAACX,IAAI,CAACiB,IAAI,EAAEjB,IAAI,CAAC;AAC7B;AAEO,SAASmD,gBAAgBA,CAE9BnD,IAA8D,EAC9D;EAAA,IAAAqD,cAAA,EAAAC,kBAAA;EACA,IAAI,CAAClD,SAAS,CAACJ,IAAI,CAACK,UAAU,EAAEL,IAAI,CAAC;EAIrC,MAAMgC,OAAO,IAAAqB,cAAA,GAAGrD,IAAI,CAACiC,GAAG,CAACN,GAAG,sBAAA2B,kBAAA,GAAZD,cAAA,CAAcnB,GAAG,qBAAjBoB,kBAAA,CAAmBnB,IAAI;EACvC,IAAIH,OAAO,EAAE,IAAI,CAACI,OAAO,CAACJ,OAAO,CAAC;EAElC,IAAI,CAACK,2BAA2B,CAACrC,IAAI,CAAC;EACtC,IAAI,CAACuD,WAAW,CAACvD,IAAI,CAAC;AACxB;AAEO,SAASwD,WAAWA,CAAgBxD,IAAmB,EAAE;EAC9D,IAAI,CAACO,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACW,SAAK,KAAK;EACf,IAAInB,IAAI,CAACiB,IAAI,CAACG,MAAM,KAAK,CAAC,EAAE;IAC1B,IAAI,CAACD,SAAK,KAAK;EACjB,CAAC,MAAM;IACL,IAAI,CAACE,OAAO,EAAE;IACd,IAAI,CAACE,aAAa,CAACvB,IAAI,CAACiB,IAAI,EAAEjB,IAAI,EAAE;MAClCsB,MAAM,EAAE;IACV,CAAC,CAAC;IAEF,IAAI,CAACI,gBAAgB,CAAC,KAAK,EAAE1B,IAAI,CAAC2B,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7C,IAAI,CAACC,UAAU,EAAE;EACnB;AACF"}