{"version": 3, "names": ["_cloneNode", "require", "cloneDeepWithoutLoc", "node", "cloneNode"], "sources": ["../../src/clone/cloneDeepWithoutLoc.ts"], "sourcesContent": ["import cloneNode from \"./cloneNode\";\nimport type * as t from \"..\";\n/**\n * Create a deep clone of a `node` and all of it's child nodes\n * including only properties belonging to the node.\n * excluding `_private` and location properties.\n */\nexport default function cloneDeepWithoutLoc<T extends t.Node>(node: T): T {\n  return cloneNode(node, /* deep */ true, /* withoutLoc */ true);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAOe,SAASC,mBAAmBA,CAAmBC,IAAO,EAAK;EACxE,OAAO,IAAAC,kBAAS,EAACD,IAAI,EAAa,IAAI,EAAmB,IAAI,CAAC;AAChE"}