import React from 'react';
import { Layout, AppmakerText, BlockCard } from '@appmaker-xyz/uikit';
import { StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { useApThemeState } from '@appmaker-xyz/uikit/src/theme/ThemeContext';

const TextList = ({ attributes = {}, clientId }) => {
  const { text, iconName, iconColor } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });

  return (
    <Layout style={styles.listItem}>
      {iconName ? (
        <Icon
          name={iconName}
          size={spacing.md}
          color={iconColor || color.demiDark}
          style={styles.icon}
        />
      ) : null}
      <AppmakerText category="bodyParagraph" style={styles.center}>
        {text}
      </AppmakerText>
    </Layout>
  );
};

const allStyles = ({ color, spacing }) =>
  StyleSheet.create({
    listItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      backgroundColor: color.white,
      paddingVertical: 2,
      paddingHorizontal: spacing.base,
    },
    text: {
      textAlign: 'center',
    },
    icon: {
      marginEnd: spacing.nano,
      marginTop: 3,
    },
  });

export default TextList;
