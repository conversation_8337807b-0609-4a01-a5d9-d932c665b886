import { StyleSheet, ActivityIndicator } from 'react-native';
import React from 'react';
import Icon from 'react-native-vector-icons/Feather';
import { testProps } from '@appmaker-xyz/core';
import { AppTouchable, Layout, ThemeText } from '../atoms';

function DecrementComponent(props) {
  let testId = 'quantity-decrement';
  if (props?.testId) {
    testId = `${props?.testId}-${testId}`;
  }
  return (
    <AppTouchable
      {...testProps(testId)}
      onPress={() => props.decrement(1)}
      style={props.style}
      disabled={props.isLoading}>
      <Icon name="minus" size={14} color={props.color} />
    </AppTouchable>
  );
}

function IncrementComponent(props) {
  let testId = 'quantity-increment';
  if (props?.testId) {
    testId = `${props?.testId}-${testId}`;
  }
  return (
    <AppTouchable
      {...testProps(testId)}
      onPress={() => props.increment(1)}
      style={props.style}
      disabled={props.isLoading}>
      <Icon name="plus" size={14} color={props.color} />
    </AppTouchable>
  );
}

export default function StepperButton({
  min = 1,
  max = 10000000,
  quantity = 1,
  increaseQuantity = () => {},
  decreaseQuantity = () => {},
  isLoading = false,
  containerStyle,
  size,
  fullWidth,
  groceryMode,
  onAddToCart,
  maxReached = false,
  textColor = '#000000',
  testId,
  extensionStyles = {},
}) {
  const containerStyles = [styles.container];
  const buttonStyles = [styles.button];
  if (containerStyle) {
    containerStyles.push(containerStyle);
  }
  if (size === 'lg') {
    buttonStyles.push({ padding: 8 });
  }
  if (extensionStyles) {
    containerStyles.push(extensionStyles?.stepper_container);
    buttonStyles.push(extensionStyles?.iconButton);
  }
  if (groceryMode && quantity < min) {
    return (
      <Layout style={containerStyles}>
        <AppTouchable onPress={onAddToCart} style={styles.addCartButton}>
          {isLoading ? (
            <ActivityIndicator size={12} color={textColor} />
          ) : (
            <ThemeText size="sm" style={styles.addText} color={textColor}>
              ADD
            </ThemeText>
          )}
        </AppTouchable>
      </Layout>
    );
  }
  return (
    <Layout style={containerStyles}>
      <DecrementComponent
        testId={testId}
        decrement={decreaseQuantity}
        isLoading={isLoading}
        style={buttonStyles}
        color={extensionStyles?.iconButton?.color || textColor}
      />
      <Layout style={fullWidth ? styles.grow : styles.middleContainer}>
        {isLoading ? (
          <ActivityIndicator size={12} color={textColor} />
        ) : (
          <ThemeText
            testId={`${testId}-quantity`}
            size="sm"
            style={styles.text}
            fontFamily="bold"
            color={textColor}>
            {quantity}
          </ThemeText>
        )}
      </Layout>
      <IncrementComponent
        testId={testId}
        increment={increaseQuantity}
        isLoading={isLoading}
        style={buttonStyles}
        color={extensionStyles?.iconButton?.color || textColor}
        isDisabled={maxReached}
      />
    </Layout>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#000',
    borderRadius: 6,
  },
  button: {
    padding: 6,
  },
  middleContainer: {
    minWidth: 26,
    maxWidth: 26,
  },
  grow: {
    flexGrow: 1,
  },
  text: {
    textAlign: 'center',
  },
  addCartButton: {
    flexGrow: 1,
    alignSelf: 'center',
    padding: 6,
  },
  addText: {
    textAlign: 'center',
  },
});
