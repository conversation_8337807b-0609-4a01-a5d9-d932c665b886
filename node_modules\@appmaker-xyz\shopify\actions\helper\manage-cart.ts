import {
  appmakerFunctions,
  applyFilters,
  analytics,
  appSettings,
  appStorageApi,
} from '@appmaker-xyz/core';
import { Platform } from 'react-native';
import { isNativeCheckout, saveCartToLocal } from './cart';
import produce from 'immer';
import { isEqual } from 'lodash';
import { runDataSource as runDataSourceFn } from '@appmaker-xyz/core';

import {
  singleProductParams,
  singleProductContext,
  getCartParams,
  trackAddToCartWithoutData,
} from '../../helper/analytics';
import { cartHelpers } from '../../helper/cartHelper';

const CART_STORAGE_KEY = 'shopifyCart';

type ManageCartHandlerContext = {
  updateCartPageStateRequired?: boolean
}

import { manageShopifyCartMutation } from '../cartApi';
import { isCartApiEnabled, getCurrentCart, shopifyCartToLegacyCart } from '../../helper/helper';
async function runManageCartGql(params, { runDataSource }) {
  const {
    checkoutId,
    lineItemsToAdd,
    lineItemsToUpdate,
    cartCustomAttributes,
    applyDiscountCode,
    removeDiscountCode,
    lineItemsToRemove,
    messages,
  } = params;
  const dataSource = {
    source: 'shopify',
    attributes: {},
  };
  if (
    lineItemsToAdd?.length > 0 ||
    lineItemsToUpdate?.length > 0 ||
    lineItemsToRemove?.length > 0 ||
    cartCustomAttributes?.length > 0 ||
    applyDiscountCode ||
    removeDiscountCode
  ) {
    const [response] = await runDataSourceFn(
      {
        dataSource,
      },
      {
        methodName: 'manageCheckout',
        params: {
          checkoutId,
          lineItemsToAdd,
          lineItemsToUpdate,
          cartCustomAttributes,
          applyDiscountCode,
          removeDiscountCode,
          lineItemsToRemove,
        },
      },
    );
    return response.data;
  }

  return messages?.length > 0
    ? {
        errors: messages,
      }
    : {};
}
function formatLineItem(lineItem) {
  const newLinItem = {
    // id: lineItem.id,
    quantity: lineItem?.quantity,
    variantId: lineItem.variantId,
    customAttributes: lineItem.customAttributes,
  };
  if (lineItem.id) {
    newLinItem.id = lineItem.id;
  }
  return newLinItem;
}
export function getCheckoutFromResponse(cartResponse) {
  const { data: response } = cartResponse;
  let cartData;
  if(cartResponse?.cart) {
    cartData = cartResponse.cart;
  } else if (response?.node) {
    cartData = response.node;
  } else if (response?.checkoutCreate?.checkout) {
    cartData = response.checkoutCreate.checkout;
  } else if (response?.checkoutDiscountCodeRemove?.checkout) {
    cartData = response.checkoutDiscountCodeRemove.checkout;
  } else if (response?.checkoutDiscountCodeApplyV2?.checkout) {
    cartData = response.checkoutDiscountCodeApplyV2.checkout;
  } else if (response?.checkoutAttributesUpdateV2?.checkout) {
    cartData = response.checkoutAttributesUpdateV2.checkout;
  } else if (response?.checkoutEmailUpdateV2?.checkout) {
    cartData = response.checkoutEmailUpdateV2.checkout;
  } else if (response?.checkoutShippingAddressUpdateV2?.checkout) {
    cartData = response.checkoutShippingAddressUpdateV2.checkout;
  } else if (response?.checkoutLineItemsRemove?.checkout) {
    cartData = response.checkoutLineItemsRemove.checkout;
  } else if (response?.checkoutLineItemsUpdate?.checkout) {
    cartData = response.checkoutLineItemsUpdate.checkout;
  } else if (response?.checkoutLineItemsAdd?.checkout) {
    cartData = response.checkoutLineItemsAdd.checkout;
  }
  applyFilters('shopify-checkout-cart-page-response', cartData);
  return cartData;
}

async function updateCartPageState(cartData, dependencies) {
  const { coreDispatch = false } = dependencies;
  if (cartData && cartData.id && coreDispatch) {
    coreDispatch({
      type: 'SET_BLOCK_DATA',
      blockData: cartData,
    });
  }
}
async function clearCart(dependencies) {
  appStorageApi()?.setState?.({
    checkout: null,
  });
  appStorageApi()?.setState?.({
    [CART_STORAGE_KEY]: null,
  });
}
async function ensureCartBelongsToCustomer(cartResponse, dependencies) {
  const { runDataSource } = dependencies;
  if (cartResponse?.data?.checkoutCreate?.checkout) {
    const checkoutData = cartResponse?.data?.checkoutCreate?.checkout;
    const user = appStorageApi().getState().user;
    if (user?.accessToken && checkoutData?.id) {
      const newCheckoutAttributes = {
        methodName: 'checkoutCustomerAssociateV2',
        params: {
          checkoutId: checkoutData?.id,
          customerAccessToken: user.accessToken,
        },
      };
      const dataSource = {
        source: 'shopify',
        attributes: {},
      };
      const [assosiateRespponse] = await runDataSourceFn(
        { dataSource },
        newCheckoutAttributes,
      );
      await cartHelpers.saveCart(
        assosiateRespponse?.data?.data?.checkoutCustomerAssociateV2?.checkout,
        dependencies,
      );
    }
  }
}
function isEmpty(array) {
  return !array || array.length === 0;
}

async function cartPostProcess(cartResponse, dependencies) {
  const cartInput = {
    currentCart: isCartApiEnabled() ? shopifyCartToLegacyCart(cartResponse?.cart) : getCheckoutFromResponse(cartResponse),
    lineItemsToAdd: [],
    lineItemsToUpdate: [],
  };
  const newCartOutput = await appmakerFunctions.runAppmakerFunction(
    'manage-cart-after-response',
    cartInput,
    { ...dependencies, cartHelpers },
  );
  // const newCartOutput = cartInput
  const { lineItemsToAdd, lineItemsToUpdate, lineItemsToRemove, cartCustomAttributes  } =
    newCartOutput;
  if (
    isEmpty(lineItemsToAdd) &&
    isEmpty(lineItemsToUpdate) &&
    isEmpty(lineItemsToRemove) &&
    isEmpty(cartCustomAttributes)
  ) {
    return cartResponse;
  }
  const newCartResponse = await updateCartHandler(
    newCartOutput,
    dependencies,
    true,
  );
  return newCartResponse;
}
function validateCheckout(cartResponse, currentCart = {}) {
  const { data, errors } = cartResponse;
  let hasCheckoutErrors = false;
  let _clearCart = false;
  if (
    data?.checkoutLineItemsAdd?.checkoutUserErrors &&
    data?.checkoutLineItemsAdd?.checkoutUserErrors?.length > 0
  ) {
    hasCheckoutErrors = true;
    analytics.track('cart_error', {
      errorLocation: 'checkoutLineItemsAdd',
      errors: data?.checkoutLineItemsAdd?.checkoutUserErrors,
      cartResponse,
      currentCart,
    });
    data?.checkoutLineItemsAdd?.checkoutUserErrors?.forEach((error) => {
      if (
        error.message === 'Checkout does not exist' ||
        error?.code === 'INVALID'
      ) {
        _clearCart = true;
      }
    });
  }
  if (errors?.length > 0) {
    analytics.track('cart_error', {
      errorLocation: 'rootErrors',
      errors: errors,
      cartResponse,
      currentCart,
    });
    hasCheckoutErrors = true;
    errors.forEach((error) => {
      if (
        error.message.includes('Checkout is already completed.') ||
        error?.code === 'already_completed'
      ) {
        _clearCart = true;
      }
    });

    // hasCheckoutErrors  = errors.filter()
  }
  return [hasCheckoutErrors, _clearCart];
}

export async function updateCartHandler(
  input,
  dependencies,
  isPostProcessing = false,
) {
  const appStorageState = appStorageApi().getState();
  let currentCart = appStorageState?.checkout || input?.currentCart || {};
  if (isCartApiEnabled()) {
    currentCart = getCurrentCart();
  }
  // const currentCart = checkout;
  /**
   * On app first open checkout will be empty in storage
   */
  let defaultAttributes = applyFilters('cart-custom-attributes', [
    { key: 'appmaker_platform', value: Platform.OS },
    { key: 'appmaker_checkout_version', value: 'v3' },
    {
      key: 'app_version',
      value: appSettings.getOption('app_version_name'),
    },
    {
      key: 'appmaker_sdk_version',
      value: appSettings.getOption('sdk_version'),
    },
  ]);

  if (isNativeCheckout()) {
    defaultAttributes.push({
      key: 'appmaker_native_checkout',
      value: 'true',
    });
  }

  const cartCustomAttributes = [
    ...(input?.cartCustomAttributes || []),
    ...defaultAttributes,
  ];

  const deleteKeys = [];
  let finalCustomAttributes =
    cartCustomAttributes.filter((attribute) => {
      const existingAttribute = !!currentCart?.customAttributes?.find(
        (cartItem) =>
          cartItem.key === attribute.key && cartItem.value === attribute.value,
      );
      const shouldDelete = attribute?.delete === true;
      if (shouldDelete) {
        deleteKeys.push(attribute.key);
      }
      // remove if existingAttribute = true & shouldDelete = false :- Element already exists and not marked for delete
      // remove if existingAttribute = false & shouldDelete = true :- Element does not exists and marked for delete
      // do not remove if existingAttribute = true & shouldDelete = true  :- Element exists and marked for delete
      return (
        !(existingAttribute && !shouldDelete) &&
        !(shouldDelete && !existingAttribute)
      );
    }) || [];

  if (finalCustomAttributes.length > 0) {
    finalCustomAttributes = [
      ...(currentCart?.customAttributes || []),
      ...finalCustomAttributes,
    ];
  }


  let cartInput = {
    currentCart,
    ...input,
    cartCustomAttributes: finalCustomAttributes,
  };

  if (!isPostProcessing) {
    cartInput = await appmakerFunctions.runAppmakerFunction(
      'manage-cart-before-update',
      {
        ...cartInput,
        currentCart: isCartApiEnabled() ? shopifyCartToLegacyCart(currentCart) : currentCart,
      },
      { ...dependencies, cartHelpers },
    );
  }
  const {
    lineItemsToAdd,
    lineItemsToUpdate,
    cartCustomAttributes: cartCustomAttributesInput,
  } = cartInput;
  // remove all items with deleteKeys
  finalCustomAttributes = cartCustomAttributesInput?.filter?.(
    (attribute) => !deleteKeys.includes(attribute.key),
  );
  let cartResponse = null;
  if (isCartApiEnabled()) {
    cartResponse = await manageShopifyCartMutation({
      lineItemsToAdd: lineItemsToAdd?.map(formatLineItem) || [],
      lineItemsToUpdate: lineItemsToUpdate?.map(formatLineItem) || [],
      cartCustomAttributes: finalCustomAttributes || [],
      applyDiscountCode: cartInput.applyDiscountCode || '',
      removeDiscountCode: cartInput.removeDiscountCode || false,
      lineItemsToRemove: cartInput.lineItemsToRemove || [],
    });
  } else {
    cartResponse = await runManageCartGql(
      {
        checkoutId: currentCart?.id,
        lineItemsToAdd: lineItemsToAdd?.map(formatLineItem) || [],
        lineItemsToUpdate: lineItemsToUpdate?.map(formatLineItem) || [],
        cartCustomAttributes: finalCustomAttributes || [],
        applyDiscountCode: cartInput.applyDiscountCode || false,
        removeDiscountCode: cartInput.removeDiscountCode || false,
        lineItemsToRemove: cartInput.lineItemsToRemove || [],
        ...(cartInput?.messages && { messages: cartInput?.messages }),
      },
      dependencies || { runDataSource: runDataSourceFn },
    );
  }

  // return false if cartResponse is empty object or false
  if (!cartResponse || isEqual(cartResponse, {})) {
    return false;
  }
  if (isEmpty(currentCart?.id)) {
    analytics.track(
      'cart_created',
      getCartParams(getCheckoutFromResponse(cartResponse)),
    );
  } else {
    analytics.track(
      'cart_updated',
      getCartParams(getCheckoutFromResponse(cartResponse)),
    );
  }
  const [hasCheckoutErrors, requiresClearLocalCart] = validateCheckout(
    cartResponse,
    currentCart,
  );
  if (isCartApiEnabled()) {
    cartResponse = await cartPostProcess(cartResponse, dependencies);
    // TODO added basic post response filter for prefetching checkout
    await appmakerFunctions.runAppmakerFunction(
      'cart-after-response',
      cartInput,
      { ...dependencies, cartHelpers },
    );
  } else {
    // TODO - cart post processing
    if (hasCheckoutErrors && requiresClearLocalCart) {
      await clearCart(dependencies);
    } else {
      if (!isPostProcessing) {
        cartResponse = await cartPostProcess(cartResponse, dependencies);
        await cartHelpers.saveCart(
          getCheckoutFromResponse(cartResponse),
          dependencies,
        );
        ensureCartBelongsToCustomer(cartResponse, dependencies);
      }
    }
  }
  return cartResponse;
}

export async function manageCartHandler(
  input,
  dependencies,
  isPostProcessing = false,
  context: ManageCartHandlerContext = {},
) {
  const updateCartPageStateRequired = context?.updateCartPageStateRequired;
  analytics?.track(
    'update_cart_multiple',
    {},
    {
      ...input,
    },
  );
  let currentCart = appStorageApi().getState().checkout;
  if (isCartApiEnabled()) {
    currentCart = getCurrentCart();
  }
  let cartInput = {
    ...input,
  };

  if (!isPostProcessing) {
    cartInput = await appmakerFunctions.runAppmakerFunction(
      'manage-cart-before-update',
      cartInput,
      { ...dependencies, cartHelpers },
    );
  }
  const { lineItemsToAdd, lineItemsToUpdate } = cartInput;
  let cartResponse;
  if (isCartApiEnabled()) {
    cartResponse = await manageShopifyCartMutation({
      lineItemsToAdd: lineItemsToAdd?.map(formatLineItem) || [],
      lineItemsToUpdate: lineItemsToUpdate?.map(formatLineItem) || [],
      cartCustomAttributes: [],
      applyDiscountCode: cartInput.applyDiscountCode || '',
      removeDiscountCode: cartInput.removeDiscountCode || false,
      lineItemsToRemove: cartInput.lineItemsToRemove || [],
      cartNoteToUpdate: cartInput.cartNoteToUpdate || '',
    });
  } else {
    cartResponse = await runManageCartGql(
      {
        checkoutId: currentCart?.id,
        lineItemsToAdd: lineItemsToAdd?.map(formatLineItem) || [],
        lineItemsToUpdate: lineItemsToUpdate?.map(formatLineItem) || [],
        cartCustomAttributes: [],
        applyDiscountCode: cartInput.applyDiscountCode || false,
        removeDiscountCode: cartInput.removeDiscountCode || false,
        lineItemsToRemove: cartInput.lineItemsToRemove || [],
        ...(cartInput?.messages && { messages: cartInput?.messages }),
      },
      {
        runDataSource: runDataSourceFn,
      },
    );
  }
  // return false if cartResponse is empty object or false
  if (!cartResponse || isEqual(cartResponse, {})) {
    return false;
  }
  if (isEmpty(currentCart?.id)) {
    analytics.track(
      'cart_created',
      getCartParams(getCheckoutFromResponse(cartResponse)),
    );
  } else {
    analytics.track(
      'cart_updated',
      getCartParams(getCheckoutFromResponse(cartResponse)),
    );
  }
  const [hasCheckoutErrors, requiresClearLocalCart] = validateCheckout(
    cartResponse,
    currentCart,
  );
  if (!isCartApiEnabled()) {
  if (hasCheckoutErrors && requiresClearLocalCart) {
    await clearCart(dependencies);
  } else {
    if (!isPostProcessing) {
      cartResponse = await cartPostProcess(cartResponse, dependencies);
      await cartHelpers.saveCart(
        getCheckoutFromResponse(cartResponse),
        dependencies,
      );
      ensureCartBelongsToCustomer(cartResponse, {
        runDataSource: runDataSourceFn,
      });
    }
  }
}
  if (updateCartPageStateRequired) {
    updateCartPageState(getCheckoutFromResponse(cartResponse), dependencies);
  }
  return cartResponse;
}

export async function addToCart(
  {
    variantId,
    quantity,
    customAttributes = [],
    product,
    pageId,
    variant,
    updateCartPageStateRequired = false,
    referrer,
  },
  dependencies,
) {
  // emitEvent('cart.add', {
  //   ...product?.node,
  //   pageId,
  //   variant,
  //   quantity,
  // });
  // if(customAttributes)
  let finalCustomAttributes = customAttributes;
  try {
    finalCustomAttributes.push({
      key: '_appmaker',
      value: 'true',
    });
  } catch (error) {
    console.log('error', error);
  }
  try {
    finalCustomAttributes = applyFilters(
      'cart-line-item-custom-attributes',
      customAttributes,
      {
        variantId,
        quantity,
        product,
        variant,
      },
    );
  } catch (error) {
    console.log('error', error);
  }
  const lineItemsToAdd = [
    {
      variantId,
      quantity,
      customAttributes: finalCustomAttributes,
      product,
      variant,
    },
  ];
  let cartResponse;
  // if (false) {
  //   cartResponse = await manageShopifyCartMutation({
  //     lineItemsToAdd,
  //   });
  // } else {
    cartResponse = await updateCartHandler(
      {
        lineItemsToAdd,
      },
      dependencies,
    );
  // }
  if (
    product &&
    (product?.node?.title || product?.title) &&
    variant &&
    (variant?.node?.title || variant?.title)
  ) {
    analytics.track(
      'product_added_to_cart',
      singleProductParams(product, variant, { quantity }),
      singleProductContext(
        product,
        variant,
        { pageId },
        {
          customAttributes: finalCustomAttributes,
          ...(referrer && { referrer }),
        },
      ),
    );
  } else if (product?.id || product?.node?.id) {
    trackAddToCartWithoutData(
      product?.id || product?.node?.id,
      variantId,
      quantity,
    );
  }
  if (updateCartPageStateRequired) {
    updateCartPageState(getCheckoutFromResponse(cartResponse), dependencies);
  }
  return cartResponse;
}
export async function updateCart(
  {
    lineItemId,
    variantId,
    quantity,
    customAttributes,
    product,
    updateCartPageStateRequired,
    pageId,
    variant,
  },
  dependencies,
) {
  let cartResponse;
  const lineItemsToUpdate = [
    {
      id: lineItemId,
      variantId,
      quantity,
      customAttributes,
      product,
      variant,
    },
  ];
    cartResponse = await updateCartHandler(
      {
        lineItemsToUpdate,
      },
      dependencies,
    );
  if (
    product &&
    variant &&
    (product?.node?.title || product?.title) &&
    (variant?.title || variant?.node?.title)
  ) {
    if (quantity <= 0) {
      analytics.track(
        'remove_from_cart',
        singleProductParams(product, variant),
        singleProductContext(product, variant, { pageId }),
      );
    } else {
      analytics.track(
        'update_cart',
        singleProductParams(product, variant, {
          quantity,
        }),
        singleProductContext(product, variant, { pageId }),
      );
    }
  }

  if (updateCartPageStateRequired) {
    updateCartPageState(getCheckoutFromResponse(cartResponse), dependencies);
  }
  return cartResponse;
}

export async function removeCartLineItem(
  { lineItemId, product, variant, pageId, updateCartPageStateRequired = true },
  dependencies,
) {
  let cartResponse;
    cartResponse = await updateCartHandler(
      {
        lineItemsToRemove: [lineItemId],
      },
      dependencies,
    );
  if (
    product &&
    variant &&
    (product?.node?.title || product?.title) &&
    (variant?.title || variant?.node?.title)
  ) {
    analytics.track(
      'remove_from_cart',
      singleProductParams(product, variant),
      singleProductContext(
        product,
        variant,
        { pageId },
        { prevCart: dependencies?.appStorageState?.checkout, currentLineItemId: lineItemId },
      ),
    );
  }

  if (updateCartPageStateRequired) {
    updateCartPageState(getCheckoutFromResponse(cartResponse), dependencies);
  }
}
