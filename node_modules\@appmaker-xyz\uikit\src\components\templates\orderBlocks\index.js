import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout, AppmakerText, Card, Button } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import { Badge } from '../../molecules/index';

const OrderCard = ({ attributes, onPress, onAction }) => {
  const {
    featureImg,
    orderId,
    items,
    numberOfItems,
    totalAmount,
    appmakerAction,
    topNotice,
    status,
    can_repeat_order = false,
    orderDate,
    deliveryDate,
    paymentMethod,
  } = attributes;

  function cardStatus() {
    switch (status) {
      case 'SCHEDULED':
        return 'primary';
      case 'UNFULFILLED':
        return 'warning';
      case 'FULFILLED':
        return 'success';
      case 'ON_HOLD':
        return 'dark';
      case 'PARTIALLY_FULFILLED':
        return 'danger';
      default:
        return 'demiDark';
    }
  }
  let cardColor = cardStatus();
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing, cardColor });
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  const orderAgain = () => {
    onAction({
      type: 'GET_REDIRECT_URL',
      params: { url: 'payment', params: orderId },
    }).then((url) => {
      onAction({
        type: 'OPEN_IN_WEB_VIEW',
        params: { title: 'Order Again', url: url },
      });
    });
  };

  return (
    <Layout style={styles.container}>
      <Layout style={styles.statusContainer}>
        <AppmakerText status="white" category="bodyParagraph">
          Order Status:{' '}
          <AppmakerText status="white" category="bodyParagraph">
            {status && status.toUpperCase()}
          </AppmakerText>
        </AppmakerText>
        <AppmakerText status="white" category="bodyParagraphBold">
          #{orderId}
        </AppmakerText>
      </Layout>
      <Card
        onPress={onPressHandle}
        attributes={{
          type: 'type-2',
          status: cardStatus(),
          title: `Order Total: ${totalAmount}`,
          featureImg: featureImg,
          excerpt: `Item(s): ${items}`,
        }}>
        {numberOfItems >= 2 && (
          <Badge text={numberOfItems} style={styles.countBadge} />
        )}
        <Layout style={styles.orderExtraData}>
          {orderDate && (
            <AppmakerText category="bodySubText" status="grey">
              Placed on{' '}
              <AppmakerText category="bodySubText" status="demiDark">
                {orderDate}
              </AppmakerText>
            </AppmakerText>
          )}
          <AppmakerText category="bodySubText" status="grey">
            Payment status{' '}
            <AppmakerText category="bodyParagraphBold" status="demiDark">
              {paymentMethod}
            </AppmakerText>
          </AppmakerText>
          {topNotice &&
            topNotice.map((item, key) => (
              <AppmakerText category="bodySubText" status="grey">
                {item.message}
                <AppmakerText category="bodySubText" status="demiDark">
                  {deliveryDate}
                </AppmakerText>
              </AppmakerText>
            ))}
        </Layout>
        <Layout flexDirection="row">
          {(status === 'cancelled' ||
            status === 'failed' ||
            status === 'refunded' ||
            status === 'pending' ||
            status === 'completed') &&
            can_repeat_order && (
              <Layout flex={1}>
                <Button small status="light" onPress={() => orderAgain()}>
                  Order Again
                </Button>
              </Layout>
            )}
          {topNotice &&
            topNotice.map(
              (item, key) =>
                item.button && (
                  <Layout flex={1}>
                    <Button
                      small
                      status="light"
                      onPress={() => onAction(item.button.action)}>
                      {item.button.text}
                    </Button>
                  </Layout>
                ),
            )}
        </Layout>
      </Card>
    </Layout>
  );
};

const allStyles = ({ spacing, color, cardColor }) =>
  StyleSheet.create({
    container: {
      backgroundColor: `${color[cardColor]}CC`,
      borderColor: color[cardColor],
      borderWidth: 1,
      borderRadius: spacing.nano,
      marginBottom: spacing.mini,
    },
    statusContainer: {
      paddingVertical: spacing.mini,
      paddingHorizontal: spacing.small,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    orderExtraData: {
      marginBottom: spacing.base,
    },
    countBadge: {
      position: 'absolute',
      top: -104,
      left: 12,
    },
  });

export default OrderCard;
