import { uglify } from 'rollup-plugin-uglify';
import localResolve from 'rollup-plugin-local-resolve';
import babel from '@rollup/plugin-babel';
import json from '@rollup/plugin-json';

export default {
    input: 'index.js',
    plugins: [
        json(),
        localResolve(),
        uglify(),
        babel({
            presets: ["@babel/preset-react"],
        }),
    ],
    output: {
        dir: 'build-output',
        format: 'cjs',
        preserveModules: true, 
        preserveModulesRoot: './',
    },
    minifyInternalExports: true,
};