const page = {
  title: 'Apply Coupon',
  attributes: {
    showLoadingTillData: true,
    // headerShown: false,
    rootContainerStyle: {
      backgroundColor: '#FFFFFF',
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      paddingHorizontal: -12,
      paddingTop: 12,
    },
  },
  blocks: [
    // {
    //   name: 'appmaker/actionbar',
    //   attributes: {
    //     title: 'Apply Coupon ',
    //     subTitle: '{{ blockData.id }}',
    //     leftIcon: 'scissors',
    //   },
    // },
    {
      name: 'appmaker/CartCoupon',
      attributes: {
        defaultEditMode: true,
        couponDiscounted: '{{blockData.discountApplications.edges}}', // to be done
        couponDiscountedLength:
          '{{blockData.discountApplications.edges.length}}',
        autoApplyCoupon:
          '{{getObjectOfAnArray(plugins,"app-only-coupons").settings.auto_apply_coupon}}',
        enableAutoApplyCoupon:
          '{{checkIfTrueFalse(getObjectOfAnArray(plugins,"app-only-coupons").settings.enable_auto_apply_coupon)}}',
        gobackAfterApply: true,
        __appmakerStylesClassName: 'cartCouponCustomStyle',
      },
    },
    {
      name: 'appmaker/blocksView',
      attributes: {
        headerShown: false,
        rootContainerStyle: {
          marginTop: 8,
          paddingHorizontal: 12,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/text',
          attributes: {
            content: 'Available Coupons',
            category: 'bodyParagraph',
            status: 'demiDark',
          },
        },
        {
          name: 'appmaker/coupon-block',
          attributes: {
            couponCode: '{{blockItem.coupon}}',
            description: '{{blockItem.description}}',
            appmakerAction: {
              action: 'APPLY_COUPON',
              params: {
                coupon: '{{blockItem.coupon}}',
                goBackAfterApply: true,
              },
            },
            dataSource: {
              source: 'appCouponsList',
              attributes: {
                methodName: 'getCouponList',
              },
              responseType: 'replace',
              repeatable: 'Yes',
              repeatItem: 'DataSource',
            },
          },
        },
      ],
    },
  ],
  dataSource: {
    source: 'shopify',
    attributes: {
      mapping: {
        items: 'data.data.node',
      },
      reloadOnFocus: true,
      methodName: 'checkoutById',
      params: '{{appStorageStateApi.checkout.id}}',
    },
    // dependencies: {
    //   // appStorageState: ['cart'],
    // },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
  },
};
export default page;
