{"version": 3, "names": ["_isReactComponent", "require", "_isCompatTag", "_buildC<PERSON><PERSON>n", "_assertNode", "_generated", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "_createTypeAnnotationBasedOnTypeof", "_createFlowUnionType", "_createTSUnionType", "_generated2", "_uppercase", "_cloneNode", "_clone", "_cloneDeep", "_cloneDeepWithoutLoc", "_cloneWithoutLoc", "_addComment", "_addComments", "_inheritInnerComments", "_inheritLeadingComments", "_inheritsComments", "_inheritTrailingComments", "_removeComments", "_generated3", "_constants", "_ensureBlock", "_toBindingIdentifierName", "_toBlock", "_toCom<PERSON><PERSON>ey", "_toExpression", "_toIdentifier", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toSequenceExpression", "_toStatement", "_valueToNode", "_definitions", "_appendToMemberExpression", "_inherits", "_prependToMemberExpression", "_removeProperties", "_removePropertiesDeep", "_removeTypeDuplicates", "_getBindingIdentifiers", "_getOuterBindingIdentifiers", "_traverse", "_traverseFast", "_shallowEqual", "_is", "_isBinding", "_isBlockScoped", "_isImmutable", "_isLet", "_isNode", "_isNodesEquivalent", "_isPlaceholderType", "_isReferenced", "_isScope", "_isSpecifierDefault", "_isType", "_isValidES3Identifier", "_isValidIdentifier", "_isVar", "_matchesPattern", "_validate", "_buildMatchMemberExpression", "_generated4", "_deprecationWarning", "react", "isReactComponent", "isCompatTag", "buildChildren"], "sources": ["../src/index.ts"], "sourcesContent": ["import isReactComponent from \"./validators/react/isReactComponent\";\nimport isCompatTag from \"./validators/react/isCompatTag\";\nimport buildChildren from \"./builders/react/buildChildren\";\n\n// asserts\nexport { default as assertNode } from \"./asserts/assertNode\";\nexport * from \"./asserts/generated\";\n\n// builders\nexport { default as createTypeAnnotationBasedOnTypeof } from \"./builders/flow/createTypeAnnotationBasedOnTypeof\";\n/** @deprecated use createFlowUnionType instead */\nexport { default as createUnionTypeAnnotation } from \"./builders/flow/createFlowUnionType\";\nexport { default as createFlowUnionType } from \"./builders/flow/createFlowUnionType\";\nexport { default as createTSUnionType } from \"./builders/typescript/createTSUnionType\";\nexport * from \"./builders/generated\";\nexport * from \"./builders/generated/uppercase\";\n\n// clone\nexport { default as cloneNode } from \"./clone/cloneNode\";\nexport { default as clone } from \"./clone/clone\";\nexport { default as cloneDeep } from \"./clone/cloneDeep\";\nexport { default as cloneDeepWithoutLoc } from \"./clone/cloneDeepWithoutLoc\";\nexport { default as cloneWithoutLoc } from \"./clone/cloneWithoutLoc\";\n\n// comments\nexport { default as addComment } from \"./comments/addComment\";\nexport { default as addComments } from \"./comments/addComments\";\nexport { default as inheritInnerComments } from \"./comments/inheritInnerComments\";\nexport { default as inheritLeadingComments } from \"./comments/inheritLeadingComments\";\nexport { default as inheritsComments } from \"./comments/inheritsComments\";\nexport { default as inheritTrailingComments } from \"./comments/inheritTrailingComments\";\nexport { default as removeComments } from \"./comments/removeComments\";\n\n// constants\nexport * from \"./constants/generated\";\nexport * from \"./constants\";\n\n// converters\nexport { default as ensureBlock } from \"./converters/ensureBlock\";\nexport { default as toBindingIdentifierName } from \"./converters/toBindingIdentifierName\";\nexport { default as toBlock } from \"./converters/toBlock\";\nexport { default as toComputedKey } from \"./converters/toComputedKey\";\nexport { default as toExpression } from \"./converters/toExpression\";\nexport { default as toIdentifier } from \"./converters/toIdentifier\";\nexport { default as toKeyAlias } from \"./converters/toKeyAlias\";\nexport { default as toSequenceExpression } from \"./converters/toSequenceExpression\";\nexport { default as toStatement } from \"./converters/toStatement\";\nexport { default as valueToNode } from \"./converters/valueToNode\";\n\n// definitions\nexport * from \"./definitions\";\n\n// modifications\nexport { default as appendToMemberExpression } from \"./modifications/appendToMemberExpression\";\nexport { default as inherits } from \"./modifications/inherits\";\nexport { default as prependToMemberExpression } from \"./modifications/prependToMemberExpression\";\nexport {\n  default as removeProperties,\n  type Options as RemovePropertiesOptions,\n} from \"./modifications/removeProperties\";\nexport { default as removePropertiesDeep } from \"./modifications/removePropertiesDeep\";\nexport { default as removeTypeDuplicates } from \"./modifications/flow/removeTypeDuplicates\";\n\n// retrievers\nexport { default as getBindingIdentifiers } from \"./retrievers/getBindingIdentifiers\";\nexport { default as getOuterBindingIdentifiers } from \"./retrievers/getOuterBindingIdentifiers\";\n\n// traverse\nexport { default as traverse } from \"./traverse/traverse\";\nexport * from \"./traverse/traverse\";\nexport { default as traverseFast } from \"./traverse/traverseFast\";\n\n// utils\nexport { default as shallowEqual } from \"./utils/shallowEqual\";\n\n// validators\nexport { default as is } from \"./validators/is\";\nexport { default as isBinding } from \"./validators/isBinding\";\nexport { default as isBlockScoped } from \"./validators/isBlockScoped\";\nexport { default as isImmutable } from \"./validators/isImmutable\";\nexport { default as isLet } from \"./validators/isLet\";\nexport { default as isNode } from \"./validators/isNode\";\nexport { default as isNodesEquivalent } from \"./validators/isNodesEquivalent\";\nexport { default as isPlaceholderType } from \"./validators/isPlaceholderType\";\nexport { default as isReferenced } from \"./validators/isReferenced\";\nexport { default as isScope } from \"./validators/isScope\";\nexport { default as isSpecifierDefault } from \"./validators/isSpecifierDefault\";\nexport { default as isType } from \"./validators/isType\";\nexport { default as isValidES3Identifier } from \"./validators/isValidES3Identifier\";\nexport { default as isValidIdentifier } from \"./validators/isValidIdentifier\";\nexport { default as isVar } from \"./validators/isVar\";\nexport { default as matchesPattern } from \"./validators/matchesPattern\";\nexport { default as validate } from \"./validators/validate\";\nexport { default as buildMatchMemberExpression } from \"./validators/buildMatchMemberExpression\";\nexport * from \"./validators/generated\";\n\n// react\nexport const react = {\n  isReactComponent,\n  isCompatTag,\n  buildChildren,\n};\n\nexport type * from \"./ast-types/generated\";\n\n// this is used by @babel/traverse to warn about deprecated visitors\nexport { default as __internal__deprecationWarning } from \"./utils/deprecationWarning\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AAGA,IAAAG,WAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AAAAK,MAAA,CAAAC,IAAA,CAAAF,UAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAJ,UAAA,CAAAI,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAZ,UAAA,CAAAI,GAAA;IAAA;EAAA;AAAA;AAGA,IAAAS,kCAAA,GAAAjB,OAAA;AAEA,IAAAkB,oBAAA,GAAAlB,OAAA;AAEA,IAAAmB,kBAAA,GAAAnB,OAAA;AACA,IAAAoB,WAAA,GAAApB,OAAA;AAAAK,MAAA,CAAAC,IAAA,CAAAc,WAAA,EAAAb,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAY,WAAA,CAAAZ,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAI,WAAA,CAAAZ,GAAA;IAAA;EAAA;AAAA;AACA,IAAAa,UAAA,GAAArB,OAAA;AAAAK,MAAA,CAAAC,IAAA,CAAAe,UAAA,EAAAd,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAa,UAAA,CAAAb,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAK,UAAA,CAAAb,GAAA;IAAA;EAAA;AAAA;AAGA,IAAAc,UAAA,GAAAtB,OAAA;AACA,IAAAuB,MAAA,GAAAvB,OAAA;AACA,IAAAwB,UAAA,GAAAxB,OAAA;AACA,IAAAyB,oBAAA,GAAAzB,OAAA;AACA,IAAA0B,gBAAA,GAAA1B,OAAA;AAGA,IAAA2B,WAAA,GAAA3B,OAAA;AACA,IAAA4B,YAAA,GAAA5B,OAAA;AACA,IAAA6B,qBAAA,GAAA7B,OAAA;AACA,IAAA8B,uBAAA,GAAA9B,OAAA;AACA,IAAA+B,iBAAA,GAAA/B,OAAA;AACA,IAAAgC,wBAAA,GAAAhC,OAAA;AACA,IAAAiC,eAAA,GAAAjC,OAAA;AAGA,IAAAkC,WAAA,GAAAlC,OAAA;AAAAK,MAAA,CAAAC,IAAA,CAAA4B,WAAA,EAAA3B,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAA0B,WAAA,CAAA1B,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAkB,WAAA,CAAA1B,GAAA;IAAA;EAAA;AAAA;AACA,IAAA2B,UAAA,GAAAnC,OAAA;AAAAK,MAAA,CAAAC,IAAA,CAAA6B,UAAA,EAAA5B,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAA2B,UAAA,CAAA3B,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAmB,UAAA,CAAA3B,GAAA;IAAA;EAAA;AAAA;AAGA,IAAA4B,YAAA,GAAApC,OAAA;AACA,IAAAqC,wBAAA,GAAArC,OAAA;AACA,IAAAsC,QAAA,GAAAtC,OAAA;AACA,IAAAuC,cAAA,GAAAvC,OAAA;AACA,IAAAwC,aAAA,GAAAxC,OAAA;AACA,IAAAyC,aAAA,GAAAzC,OAAA;AACA,IAAA0C,WAAA,GAAA1C,OAAA;AACA,IAAA2C,qBAAA,GAAA3C,OAAA;AACA,IAAA4C,YAAA,GAAA5C,OAAA;AACA,IAAA6C,YAAA,GAAA7C,OAAA;AAGA,IAAA8C,YAAA,GAAA9C,OAAA;AAAAK,MAAA,CAAAC,IAAA,CAAAwC,YAAA,EAAAvC,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAsC,YAAA,CAAAtC,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAA8B,YAAA,CAAAtC,GAAA;IAAA;EAAA;AAAA;AAGA,IAAAuC,yBAAA,GAAA/C,OAAA;AACA,IAAAgD,SAAA,GAAAhD,OAAA;AACA,IAAAiD,0BAAA,GAAAjD,OAAA;AACA,IAAAkD,iBAAA,GAAAlD,OAAA;AAIA,IAAAmD,qBAAA,GAAAnD,OAAA;AACA,IAAAoD,qBAAA,GAAApD,OAAA;AAGA,IAAAqD,sBAAA,GAAArD,OAAA;AACA,IAAAsD,2BAAA,GAAAtD,OAAA;AAGA,IAAAuD,SAAA,GAAAvD,OAAA;AACAK,MAAA,CAAAC,IAAA,CAAAiD,SAAA,EAAAhD,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAA+C,SAAA,CAAA/C,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAuC,SAAA,CAAA/C,GAAA;IAAA;EAAA;AAAA;AACA,IAAAgD,aAAA,GAAAxD,OAAA;AAGA,IAAAyD,aAAA,GAAAzD,OAAA;AAGA,IAAA0D,GAAA,GAAA1D,OAAA;AACA,IAAA2D,UAAA,GAAA3D,OAAA;AACA,IAAA4D,cAAA,GAAA5D,OAAA;AACA,IAAA6D,YAAA,GAAA7D,OAAA;AACA,IAAA8D,MAAA,GAAA9D,OAAA;AACA,IAAA+D,OAAA,GAAA/D,OAAA;AACA,IAAAgE,kBAAA,GAAAhE,OAAA;AACA,IAAAiE,kBAAA,GAAAjE,OAAA;AACA,IAAAkE,aAAA,GAAAlE,OAAA;AACA,IAAAmE,QAAA,GAAAnE,OAAA;AACA,IAAAoE,mBAAA,GAAApE,OAAA;AACA,IAAAqE,OAAA,GAAArE,OAAA;AACA,IAAAsE,qBAAA,GAAAtE,OAAA;AACA,IAAAuE,kBAAA,GAAAvE,OAAA;AACA,IAAAwE,MAAA,GAAAxE,OAAA;AACA,IAAAyE,eAAA,GAAAzE,OAAA;AACA,IAAA0E,SAAA,GAAA1E,OAAA;AACA,IAAA2E,2BAAA,GAAA3E,OAAA;AACA,IAAA4E,WAAA,GAAA5E,OAAA;AAAAK,MAAA,CAAAC,IAAA,CAAAsE,WAAA,EAAArE,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAoE,WAAA,CAAApE,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAA4D,WAAA,CAAApE,GAAA;IAAA;EAAA;AAAA;AAYA,IAAAqE,mBAAA,GAAA7E,OAAA;AATO,MAAM8E,KAAK,GAAG;EACnBC,gBAAgB,EAAhBA,yBAAgB;EAChBC,WAAW,EAAXA,oBAAW;EACXC,aAAa,EAAbA;AACF,CAAC;AAACpE,OAAA,CAAAiE,KAAA,GAAAA,KAAA"}