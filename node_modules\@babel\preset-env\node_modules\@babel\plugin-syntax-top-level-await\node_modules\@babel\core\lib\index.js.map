{"version": 3, "names": ["_file", "require", "_buildExternalHelpers", "_files", "_environment", "_types", "data", "Object", "defineProperty", "exports", "enumerable", "get", "_parser", "_traverse", "_template", "_config", "_transform", "_transformFile", "_transformAst", "_parse", "thisFile", "version", "DEFAULT_EXTENSIONS", "freeze", "OptionManager", "init", "opts", "loadOptionsSync", "Plugin", "alias", "Error", "types", "traverse", "tokTypes", "template"], "sources": ["../src/index.ts"], "sourcesContent": ["declare const PACKAGE_JSON: { name: string; version: string };\ndeclare const USE_ESM: boolean, IS_STANDALONE: boolean;\n\nexport const version = PACKAGE_JSON.version;\n\nexport { default as File } from \"./transformation/file/file\";\nexport type { default as PluginPass } from \"./transformation/plugin-pass\";\nexport { default as buildExternalHelpers } from \"./tools/build-external-helpers\";\nexport { resolvePlugin, resolvePreset } from \"./config/files\";\n\nexport { getEnv } from \"./config/helpers/environment\";\n\n// NOTE: Lazy re-exports aren't detected by the Node.js CJS-ESM interop.\n// These are handled by pluginInjectNodeReexportsHints in our babel.config.js\n// so that they can work well.\nexport * as types from \"@babel/types\";\nexport { tokTypes } from \"@babel/parser\";\nexport { default as traverse } from \"@babel/traverse\";\nexport { default as template } from \"@babel/template\";\n\nexport {\n  createConfigItem,\n  createConfigItemSync,\n  createConfigItemAsync,\n} from \"./config\";\n\nexport {\n  loadPartialConfig,\n  loadPartialConfigSync,\n  loadPartialConfigAsync,\n  loadOptions,\n  loadOptionsSync,\n  loadOptionsAsync,\n} from \"./config\";\n\nexport type {\n  CallerMetadata,\n  InputOptions,\n  PluginAPI,\n  PluginObject,\n  PresetAPI,\n  PresetObject,\n} from \"./config\";\n\nexport {\n  transform,\n  transformSync,\n  transformAsync,\n  type FileResult,\n} from \"./transform\";\nexport {\n  transformFile,\n  transformFileSync,\n  transformFileAsync,\n} from \"./transform-file\";\nexport {\n  transformFromAst,\n  transformFromAstSync,\n  transformFromAstAsync,\n} from \"./transform-ast\";\nexport { parse, parseSync, parseAsync } from \"./parse\";\n\n/**\n * Recommended set of compilable extensions. Not used in @babel/core directly, but meant as\n * as an easy source for tooling making use of @babel/core.\n */\nexport const DEFAULT_EXTENSIONS = Object.freeze([\n  \".js\",\n  \".jsx\",\n  \".es6\",\n  \".es\",\n  \".mjs\",\n  \".cjs\",\n] as const);\n\n// For easier backward-compatibility, provide an API like the one we exposed in Babel 6.\n// TODO(Babel 8): Remove this.\nimport { loadOptionsSync } from \"./config\";\nexport class OptionManager {\n  init(opts: {}) {\n    return loadOptionsSync(opts);\n  }\n}\n\n// TODO(Babel 8): Remove this.\nexport function Plugin(alias: string) {\n  throw new Error(\n    `The (${alias}) Babel 5 plugin is being run with an unsupported Babel version.`,\n  );\n}\n\nimport Module from \"module\";\nimport * as thisFile from \"./index\";\nif (USE_ESM) {\n  if (!IS_STANDALONE) {\n    // Pass this module to the CJS proxy, so that it can be synchronously accessed.\n    const cjsProxy = Module.createRequire(import.meta.url)(\"../cjs-proxy.cjs\");\n    cjsProxy[\"__ initialize @babel/core cjs proxy __\"] = thisFile;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,qBAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAEA,IAAAG,YAAA,GAAAH,OAAA;AAAsD,SAAAI,OAAA;EAAA,MAAAC,IAAA,GAAAL,OAAA;EAAAI,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAAC,MAAA,CAAAC,cAAA,KAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAN,MAAA;EAAA;AAAA;AAMtD,SAAAO,QAAA;EAAA,MAAAN,IAAA,GAAAL,OAAA;EAAAW,OAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,UAAA;EAAA,MAAAP,IAAA,GAAAL,OAAA;EAAAY,SAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,UAAA;EAAA,MAAAR,IAAA,GAAAL,OAAA;EAAAa,SAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAS,OAAA,GAAAd,OAAA;AAwBA,IAAAe,UAAA,GAAAf,OAAA;AAMA,IAAAgB,cAAA,GAAAhB,OAAA;AAKA,IAAAiB,aAAA,GAAAjB,OAAA;AAKA,IAAAkB,MAAA,GAAAlB,OAAA;AAgCA,IAAAmB,QAAA,GAAAnB,OAAA;AAzFO,MAAMoB,OAAO,WAAuB;AAACZ,OAAA,CAAAY,OAAA,GAAAA,OAAA;AA+DrC,MAAMC,kBAAkB,GAAGf,MAAM,CAACgB,MAAM,CAAC,CAC9C,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,CACP,CAAU;AAACd,OAAA,CAAAa,kBAAA,GAAAA,kBAAA;AAKL,MAAME,aAAa,CAAC;EACzBC,IAAIA,CAACC,IAAQ,EAAE;IACb,OAAO,IAAAC,uBAAe,EAACD,IAAI,CAAC;EAC9B;AACF;AAACjB,OAAA,CAAAe,aAAA,GAAAA,aAAA;AAGM,SAASI,MAAMA,CAACC,KAAa,EAAE;EACpC,MAAM,IAAIC,KAAK,CACZ,QAAOD,KAAM,kEAAiE,CAChF;AACH;AAAC;AAAA,MAAApB,OAAA,CAAAsB,KAAA,GAAAtB,OAAA,CAAAuB,QAAA,GAAAvB,OAAA,CAAAwB,QAAA,GAAAxB,OAAA,CAAAyB,QAAA"}