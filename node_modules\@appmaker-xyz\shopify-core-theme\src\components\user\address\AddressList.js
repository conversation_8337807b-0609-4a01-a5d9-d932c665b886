import React, { useState } from 'react';
import { StyleSheet, FlatList, Pressable } from 'react-native';
import { Button, ThemeText, Layout } from '@appmaker-xyz/ui';
import { useAddressActions, useAddressList } from '@appmaker-xyz/shopify';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { useFocusEffect } from '@react-navigation/native';

const Card = ({ address, defaultAddress, actions, refetch, onAction }) => {
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  return (
    <Pressable key={address.id} style={styles.addressCard} onPress={() => {}}>
      {defaultAddress ? (
        <Layout style={styles.defaultItem}>
          <ThemeText color="#ffffff" size="sm">
            Default
          </ThemeText>
        </Layout>
      ) : null}
      <Layout style={styles.addressDetails}>
        <ThemeText size="md" fontFamily="medium">
          {address.firstName}
          {address.lastName}
        </ThemeText>
        {address.address1 ? (
          <ThemeText size="sm">{address.address1}</ThemeText>
        ) : null}
        {address.address2 ? (
          <ThemeText size="sm">{address.address2}</ThemeText>
        ) : null}
        {address.city ? (
          <ThemeText size="sm">
            {address.city}, {address.province}
          </ThemeText>
        ) : null}
        {address.country ? (
          <ThemeText size="sm">
            {address.country}-{address.zip}
          </ThemeText>
        ) : null}
        {address.phone ? <ThemeText>Phone: {address.phone}</ThemeText> : null}
      </Layout>
      <Layout style={styles.buttonsContainer}>
        {!defaultAddress ? (
          <>
            <Button
              title="Set as default"
              size="sm"
              // onPress={async () => {
              //   await actions.setDefaultAddress(address.id);
              //   refetch();
              // }}
              isLoading={loading}
              activityIndicatorColor="#ffffff"
              onPress={() => {
                setLoading(true);
                actions
                  .setDefaultAddress(address.id)
                  .then(() => {
                    refetch();
                    setLoading(false);
                  })
                  .catch(() => {
                    setLoading(false);
                  });
              }}
            />
            <Layout style={styles.gap} />
          </>
        ) : null}
        <Button
          title="Edit"
          size="sm"
          onPress={() =>
            onAction({
              action: 'EDIT_SHIPPING_ADDRESS_CUSTOMER',
              params: {
                pageData: { node: address },
              },
            })
          }
        />
        <Layout style={styles.gap} />
        <Button
          title="Delete"
          size="sm"
          // onPress={async () => {
          //   await actions.deleteAddress(address.id);
          //   refetch();
          // }}
          activityIndicatorColor="#ffffff"
          isLoading={deleteLoading}
          onPress={() => {
            setDeleteLoading(true);
            actions
              .deleteAddress(address.id)
              .then(() => {
                refetch();
                setDeleteLoading(false);
              })
              .catch(() => {
                setDeleteLoading(false);
              });
          }}
        />
      </Layout>
    </Pressable>
  );
};

const AddressList = ({ onAction }) => {
  const { addressList, isFetching, refetch, defaultAddressId } =
    useAddressList();
  const actions = useAddressActions();

  useFocusEffect(
    React.useCallback(() => {
      refetch();
    }, []),
  );

  return (
    <Layout style={styles.container}>
      <Layout style={styles.header}>
        <Button
          title="Add New Address"
          color="#212121"
          isOutline={true}
          onPress={() => onAction({ action: 'OPEN_SHIPPING_ADDRESS_CREATE' })}
        />
      </Layout>
      <Layout style={styles.contentContainer}>
        <ThemeText fontFamily="medium" color="#64748B">
          Saved Addresses
        </ThemeText>
        {isFetching ? (
          <SkeletonPlaceholder borderRadius={4} marginTop={12}>
            <SkeletonPlaceholder.Item
              width={'auto'}
              height={100}
              marginBottom={12}
            />
            <SkeletonPlaceholder.Item
              width={'auto'}
              height={100}
              marginBottom={12}
            />
            <SkeletonPlaceholder.Item
              width={'auto'}
              height={100}
              marginBottom={12}
            />
            <SkeletonPlaceholder.Item
              width={'auto'}
              height={100}
              marginBottom={12}
            />
          </SkeletonPlaceholder>
        ) : (
          <FlatList
            data={addressList}
            renderItem={({ item }) => {
              const address = item;
              const defaultAddress = address.id === defaultAddressId;
              return (
                <Card
                  address={address}
                  defaultAddress={defaultAddress}
                  actions={actions}
                  refetch={refetch}
                  onAction={onAction}
                />
              );
            }}
            keyExtractor={(item) => item.id}
          />
        )}
      </Layout>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 12,
  },
  header: {
    marginVertical: 12,
  },
  contentContainer: {
    paddingBottom: 16,
  },
  addressCard: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#EAEAEA',
    borderRadius: 6,
    marginTop: 8,
    position: 'relative',
    overflow: 'hidden',
  },
  defaultItem: {
    borderBottomLeftRadius: 6,
    paddingHorizontal: 6,
    backgroundColor: '#64748B',
    position: 'absolute',
    right: 0,
    top: 0,
  },
  defaultAddress: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#212121',
  },
  addressDetails: {
    flex: 1,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  gap: {
    width: 6,
  },
});

export default AddressList;
