{"version": 3, "names": ["_t", "require", "isArrayTypeAnnotation", "isArrowFunctionExpression", "isAssignmentExpression", "isAwaitExpression", "isBinary", "isBinaryExpression", "isUpdateExpression", "isCallExpression", "isClass", "isClassExpression", "isConditional", "isConditionalExpression", "isExportDeclaration", "isExportDefaultDeclaration", "isExpressionStatement", "isFor", "isForInStatement", "isForOfStatement", "isForStatement", "isFunctionExpression", "isIfStatement", "isIndexedAccessType", "isIntersectionTypeAnnotation", "isLogicalExpression", "isMemberExpression", "isNewExpression", "isNullableTypeAnnotation", "isObjectPattern", "isOptionalCallExpression", "isOptionalMemberExpression", "isReturnStatement", "isSequenceExpression", "isSwitchStatement", "isTSArrayType", "isTSAsExpression", "isTSInstantiationExpression", "isTSIntersectionType", "isTSNonNullExpression", "isTSOptionalType", "isTSRestType", "isTSTypeAssertion", "isTSUnionType", "isTaggedTemplateExpression", "isThrowStatement", "isTypeAnnotation", "isUnaryLike", "isUnionTypeAnnotation", "isVariableDeclarator", "isWhileStatement", "isYieldExpression", "isTSSatisfiesExpression", "PRECEDENCE", "in", "instanceof", "isTSTypeExpression", "node", "isClassExtendsClause", "parent", "superClass", "hasPostfixPart", "object", "callee", "tag", "NullableTypeAnnotation", "FunctionTypeAnnotation", "printStack", "length", "UpdateExpression", "ObjectExpression", "isFirstInContext", "DoExpression", "async", "Binary", "operator", "left", "parentOp", "parentPos", "nodeOp", "nodePos", "right", "UnionTypeAnnotation", "OptionalIndexedAccessType", "objectType", "TSAsExpression", "TSUnionType", "TSInferType", "TSInstantiationExpression", "typeParameters", "BinaryExpression", "SequenceExpression", "test", "discriminant", "expression", "YieldExpression", "ClassExpression", "UnaryLike", "FunctionExpression", "ArrowFunctionExpression", "ConditionalExpression", "OptionalMemberExpression", "AssignmentExpression", "LogicalExpression", "Identifier", "_node$extra", "extra", "parenthesized", "id", "name", "isFollowedByBracket", "computed", "optional", "checkParam", "expressionStatement", "arrowBody", "exportDefault", "forHead", "forInHead", "forOfHead", "i", "declaration", "body", "init", "expressions", "prefix"], "sources": ["../../src/node/parentheses.ts"], "sourcesContent": ["import {\n  isArrayTypeAnnotation,\n  isArrowFunctionExpression,\n  isAssignmentExpression,\n  isAwaitExpression,\n  isBinary,\n  isBinaryExpression,\n  isUpdateExpression,\n  isCallExpression,\n  isClass,\n  isClassExpression,\n  isConditional,\n  isConditionalExpression,\n  isExportDeclaration,\n  isExportDefaultDeclaration,\n  isExpressionStatement,\n  isFor,\n  isForInStatement,\n  isForOfStatement,\n  isForStatement,\n  isFunctionExpression,\n  isIfStatement,\n  isIndexedAccessType,\n  isIntersectionTypeAnnotation,\n  isLogicalExpression,\n  isMemberExpression,\n  isNewExpression,\n  isNullableTypeAnnotation,\n  isObjectPattern,\n  isOptionalCallExpression,\n  isOptionalMemberExpression,\n  isReturnStatement,\n  isSequenceExpression,\n  isSwitchStatement,\n  isTSArrayType,\n  isTSAsExpression,\n  isTSInstantiationExpression,\n  isTSIntersectionType,\n  isTSNonNullExpression,\n  isTSOptionalType,\n  isTSRestType,\n  isTSTypeAssertion,\n  isTSUnionType,\n  isTaggedTemplateExpression,\n  isThrowStatement,\n  isTypeAnnotation,\n  isUnaryLike,\n  isUnionTypeAnnotation,\n  isVariableDeclarator,\n  isWhileStatement,\n  isYieldExpression,\n  isTSSatisfiesExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nconst PRECEDENCE = {\n  \"||\": 0,\n  \"??\": 0,\n  \"|>\": 0,\n  \"&&\": 1,\n  \"|\": 2,\n  \"^\": 3,\n  \"&\": 4,\n  \"==\": 5,\n  \"===\": 5,\n  \"!=\": 5,\n  \"!==\": 5,\n  \"<\": 6,\n  \">\": 6,\n  \"<=\": 6,\n  \">=\": 6,\n  in: 6,\n  instanceof: 6,\n  \">>\": 7,\n  \"<<\": 7,\n  \">>>\": 7,\n  \"+\": 8,\n  \"-\": 8,\n  \"*\": 9,\n  \"/\": 9,\n  \"%\": 9,\n  \"**\": 10,\n};\n\nconst enum CheckParam {\n  expressionStatement = 1 << 0,\n  arrowBody = 1 << 1,\n  exportDefault = 1 << 2,\n  forHead = 1 << 3,\n  forInHead = 1 << 4,\n  forOfHead = 1 << 5,\n}\n\nfunction isTSTypeExpression(node: t.Node) {\n  return (\n    isTSAsExpression(node) ||\n    isTSSatisfiesExpression(node) ||\n    isTSTypeAssertion(node)\n  );\n}\n\nconst isClassExtendsClause = (\n  node: t.Node,\n  parent: t.Node,\n): parent is t.Class => isClass(parent, { superClass: node });\n\nconst hasPostfixPart = (node: t.Node, parent: t.Node) =>\n  ((isMemberExpression(parent) || isOptionalMemberExpression(parent)) &&\n    parent.object === node) ||\n  ((isCallExpression(parent) ||\n    isOptionalCallExpression(parent) ||\n    isNewExpression(parent)) &&\n    parent.callee === node) ||\n  (isTaggedTemplateExpression(parent) && parent.tag === node) ||\n  isTSNonNullExpression(parent);\n\nexport function NullableTypeAnnotation(\n  node: t.NullableTypeAnnotation,\n  parent: t.Node,\n): boolean {\n  return isArrayTypeAnnotation(parent);\n}\n\nexport function FunctionTypeAnnotation(\n  node: t.FunctionTypeAnnotation,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  if (printStack.length < 3) return;\n\n  return (\n    // (() => A) | (() => B)\n    isUnionTypeAnnotation(parent) ||\n    // (() => A) & (() => B)\n    isIntersectionTypeAnnotation(parent) ||\n    // (() => A)[]\n    isArrayTypeAnnotation(parent) ||\n    // <T>(A: T): (T => T[]) => B => [A, B]\n    (isTypeAnnotation(parent) &&\n      // Check grandparent\n      isArrowFunctionExpression(printStack[printStack.length - 3]))\n  );\n}\n\nexport function UpdateExpression(\n  node: t.UpdateExpression,\n  parent: t.Node,\n): boolean {\n  return hasPostfixPart(node, parent) || isClassExtendsClause(node, parent);\n}\n\nexport function ObjectExpression(\n  node: t.ObjectExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  return isFirstInContext(\n    printStack,\n    CheckParam.expressionStatement | CheckParam.arrowBody,\n  );\n}\n\nexport function DoExpression(\n  node: t.DoExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  // `async do` can start an expression statement\n  return (\n    !node.async && isFirstInContext(printStack, CheckParam.expressionStatement)\n  );\n}\n\nexport function Binary(node: t.BinaryExpression, parent: t.Node): boolean {\n  if (\n    node.operator === \"**\" &&\n    isBinaryExpression(parent, { operator: \"**\" })\n  ) {\n    return parent.left === node;\n  }\n\n  if (isClassExtendsClause(node, parent)) {\n    return true;\n  }\n\n  if (\n    hasPostfixPart(node, parent) ||\n    isUnaryLike(parent) ||\n    isAwaitExpression(parent)\n  ) {\n    return true;\n  }\n\n  if (isBinary(parent)) {\n    const parentOp = parent.operator;\n    const parentPos = PRECEDENCE[parentOp];\n\n    const nodeOp = node.operator;\n    const nodePos = PRECEDENCE[nodeOp];\n\n    if (\n      // Logical expressions with the same precedence don't need parens.\n      (parentPos === nodePos &&\n        parent.right === node &&\n        !isLogicalExpression(parent)) ||\n      parentPos > nodePos\n    ) {\n      return true;\n    }\n  }\n}\n\nexport function UnionTypeAnnotation(\n  node: t.UnionTypeAnnotation,\n  parent: t.Node,\n): boolean {\n  return (\n    isArrayTypeAnnotation(parent) ||\n    isNullableTypeAnnotation(parent) ||\n    isIntersectionTypeAnnotation(parent) ||\n    isUnionTypeAnnotation(parent)\n  );\n}\n\nexport { UnionTypeAnnotation as IntersectionTypeAnnotation };\n\nexport function OptionalIndexedAccessType(\n  node: t.OptionalIndexedAccessType,\n  parent: t.Node,\n): boolean {\n  return isIndexedAccessType(parent, { objectType: node });\n}\n\nexport function TSAsExpression() {\n  return true;\n}\n\nexport {\n  TSAsExpression as TSSatisfiesExpression,\n  TSAsExpression as TSTypeAssertion,\n};\n\nexport function TSUnionType(node: t.TSUnionType, parent: t.Node): boolean {\n  return (\n    isTSArrayType(parent) ||\n    isTSOptionalType(parent) ||\n    isTSIntersectionType(parent) ||\n    isTSUnionType(parent) ||\n    isTSRestType(parent)\n  );\n}\n\nexport { TSUnionType as TSIntersectionType };\n\nexport function TSInferType(node: t.TSInferType, parent: t.Node): boolean {\n  return isTSArrayType(parent) || isTSOptionalType(parent);\n}\n\nexport function TSInstantiationExpression(\n  node: t.TSInstantiationExpression,\n  parent: t.Node,\n) {\n  return (\n    (isCallExpression(parent) ||\n      isOptionalCallExpression(parent) ||\n      isNewExpression(parent) ||\n      isTSInstantiationExpression(parent)) &&\n    !!parent.typeParameters\n  );\n}\n\nexport function BinaryExpression(\n  node: t.BinaryExpression,\n  parent: t.Node,\n): boolean {\n  // let i = (1 in []);\n  // for ((1 in []);;);\n  return (\n    node.operator === \"in\" && (isVariableDeclarator(parent) || isFor(parent))\n  );\n}\n\nexport function SequenceExpression(\n  node: t.SequenceExpression,\n  parent: t.Node,\n): boolean {\n  if (\n    // Although parentheses wouldn't hurt around sequence\n    // expressions in the head of for loops, traditional style\n    // dictates that e.g. i++, j++ should not be wrapped with\n    // parentheses.\n    isForStatement(parent) ||\n    isThrowStatement(parent) ||\n    isReturnStatement(parent) ||\n    (isIfStatement(parent) && parent.test === node) ||\n    (isWhileStatement(parent) && parent.test === node) ||\n    (isForInStatement(parent) && parent.right === node) ||\n    (isSwitchStatement(parent) && parent.discriminant === node) ||\n    (isExpressionStatement(parent) && parent.expression === node)\n  ) {\n    return false;\n  }\n\n  // Otherwise err on the side of overparenthesization, adding\n  // explicit exceptions above if this proves overzealous.\n  return true;\n}\n\nexport function YieldExpression(\n  node: t.YieldExpression,\n  parent: t.Node,\n): boolean {\n  return (\n    isBinary(parent) ||\n    isUnaryLike(parent) ||\n    hasPostfixPart(node, parent) ||\n    (isAwaitExpression(parent) && isYieldExpression(node)) ||\n    (isConditionalExpression(parent) && node === parent.test) ||\n    isClassExtendsClause(node, parent)\n  );\n}\n\nexport { YieldExpression as AwaitExpression };\n\nexport function ClassExpression(\n  node: t.ClassExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  return isFirstInContext(\n    printStack,\n    CheckParam.expressionStatement | CheckParam.exportDefault,\n  );\n}\n\nexport function UnaryLike(\n  node:\n    | t.UnaryLike\n    | t.ArrowFunctionExpression\n    | t.ConditionalExpression\n    | t.AssignmentExpression,\n  parent: t.Node,\n): boolean {\n  return (\n    hasPostfixPart(node, parent) ||\n    isBinaryExpression(parent, { operator: \"**\", left: node }) ||\n    isClassExtendsClause(node, parent)\n  );\n}\n\nexport function FunctionExpression(\n  node: t.FunctionExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  return isFirstInContext(\n    printStack,\n    CheckParam.expressionStatement | CheckParam.exportDefault,\n  );\n}\n\nexport function ArrowFunctionExpression(\n  node: t.ArrowFunctionExpression,\n  parent: t.Node,\n): boolean {\n  return isExportDeclaration(parent) || ConditionalExpression(node, parent);\n}\n\nexport function ConditionalExpression(\n  node:\n    | t.ConditionalExpression\n    | t.ArrowFunctionExpression\n    | t.AssignmentExpression,\n  parent?: t.Node,\n): boolean {\n  if (\n    isUnaryLike(parent) ||\n    isBinary(parent) ||\n    isConditionalExpression(parent, { test: node }) ||\n    isAwaitExpression(parent) ||\n    isTSTypeExpression(parent)\n  ) {\n    return true;\n  }\n\n  return UnaryLike(node, parent);\n}\n\nexport function OptionalMemberExpression(\n  node: t.OptionalMemberExpression,\n  parent: t.Node,\n): boolean {\n  return (\n    isCallExpression(parent, { callee: node }) ||\n    isMemberExpression(parent, { object: node })\n  );\n}\n\nexport { OptionalMemberExpression as OptionalCallExpression };\n\nexport function AssignmentExpression(\n  node: t.AssignmentExpression,\n  parent: t.Node,\n): boolean {\n  if (isObjectPattern(node.left)) {\n    return true;\n  } else {\n    return ConditionalExpression(node, parent);\n  }\n}\n\nexport function LogicalExpression(\n  node: t.LogicalExpression,\n  parent: t.Node,\n): boolean {\n  if (isTSTypeExpression(parent)) return true;\n  switch (node.operator) {\n    case \"||\":\n      if (!isLogicalExpression(parent)) return false;\n      return parent.operator === \"??\" || parent.operator === \"&&\";\n    case \"&&\":\n      return isLogicalExpression(parent, { operator: \"??\" });\n    case \"??\":\n      return isLogicalExpression(parent) && parent.operator !== \"??\";\n  }\n}\n\nexport function Identifier(\n  node: t.Identifier,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  // 13.15.2 AssignmentExpression RS: Evaluation\n  // (fn) = function () {};\n  if (\n    node.extra?.parenthesized &&\n    isAssignmentExpression(parent, { left: node }) &&\n    (isFunctionExpression(parent.right) || isClassExpression(parent.right)) &&\n    parent.right.id == null\n  ) {\n    return true;\n  }\n  // Non-strict code allows the identifier `let`, but it cannot occur as-is in\n  // certain contexts to avoid ambiguity with contextual keyword `let`.\n  if (node.name === \"let\") {\n    // Some contexts only forbid `let [`, so check if the next token would\n    // be the left bracket of a computed member expression.\n    const isFollowedByBracket =\n      isMemberExpression(parent, {\n        object: node,\n        computed: true,\n      }) ||\n      isOptionalMemberExpression(parent, {\n        object: node,\n        computed: true,\n        optional: false,\n      });\n    return isFirstInContext(\n      printStack,\n      isFollowedByBracket\n        ? CheckParam.expressionStatement |\n            CheckParam.forHead |\n            CheckParam.forInHead |\n            CheckParam.forOfHead\n        : CheckParam.forOfHead,\n    );\n  }\n\n  // ECMAScript specifically forbids a for-of loop from starting with the\n  // token sequence `for (async of`, because it would be ambiguous with\n  // `for (async of => {};;)`, so we need to add extra parentheses.\n  //\n  // If the parent is a for-await-of loop (i.e. parent.await === true), the\n  // parentheses aren't strictly needed, but we add them anyway because\n  // some tools (including earlier Babel versions) can't parse\n  // `for await (async of [])` without them.\n  return (\n    node.name === \"async\" && isForOfStatement(parent) && node === parent.left\n  );\n}\n\n// Walk up the print stack to determine if our node can come first\n// in a particular context.\nfunction isFirstInContext(\n  printStack: Array<t.Node>,\n  checkParam: CheckParam,\n): boolean {\n  const expressionStatement = checkParam & CheckParam.expressionStatement;\n  const arrowBody = checkParam & CheckParam.arrowBody;\n  const exportDefault = checkParam & CheckParam.exportDefault;\n  const forHead = checkParam & CheckParam.forHead;\n  const forInHead = checkParam & CheckParam.forInHead;\n  const forOfHead = checkParam & CheckParam.forOfHead;\n\n  let i = printStack.length - 1;\n  if (i <= 0) return;\n  let node = printStack[i];\n  i--;\n  let parent = printStack[i];\n  while (i >= 0) {\n    if (\n      (expressionStatement &&\n        isExpressionStatement(parent, { expression: node })) ||\n      (exportDefault &&\n        isExportDefaultDeclaration(parent, { declaration: node })) ||\n      (arrowBody && isArrowFunctionExpression(parent, { body: node })) ||\n      (forHead && isForStatement(parent, { init: node })) ||\n      (forInHead && isForInStatement(parent, { left: node })) ||\n      (forOfHead && isForOfStatement(parent, { left: node }))\n    ) {\n      return true;\n    }\n\n    if (\n      i > 0 &&\n      ((hasPostfixPart(node, parent) && !isNewExpression(parent)) ||\n        (isSequenceExpression(parent) && parent.expressions[0] === node) ||\n        (isUpdateExpression(parent) && !parent.prefix) ||\n        isConditional(parent, { test: node }) ||\n        isBinary(parent, { left: node }) ||\n        isAssignmentExpression(parent, { left: node }))\n    ) {\n      node = parent;\n      i--;\n      parent = printStack[i];\n    } else {\n      return false;\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAoDsB;EAnDpBC,qBAAqB;EACrBC,yBAAyB;EACzBC,sBAAsB;EACtBC,iBAAiB;EACjBC,QAAQ;EACRC,kBAAkB;EAClBC,kBAAkB;EAClBC,gBAAgB;EAChBC,OAAO;EACPC,iBAAiB;EACjBC,aAAa;EACbC,uBAAuB;EACvBC,mBAAmB;EACnBC,0BAA0B;EAC1BC,qBAAqB;EACrBC,KAAK;EACLC,gBAAgB;EAChBC,gBAAgB;EAChBC,cAAc;EACdC,oBAAoB;EACpBC,aAAa;EACbC,mBAAmB;EACnBC,4BAA4B;EAC5BC,mBAAmB;EACnBC,kBAAkB;EAClBC,eAAe;EACfC,wBAAwB;EACxBC,eAAe;EACfC,wBAAwB;EACxBC,0BAA0B;EAC1BC,iBAAiB;EACjBC,oBAAoB;EACpBC,iBAAiB;EACjBC,aAAa;EACbC,gBAAgB;EAChBC,2BAA2B;EAC3BC,oBAAoB;EACpBC,qBAAqB;EACrBC,gBAAgB;EAChBC,YAAY;EACZC,iBAAiB;EACjBC,aAAa;EACbC,0BAA0B;EAC1BC,gBAAgB;EAChBC,gBAAgB;EAChBC,WAAW;EACXC,qBAAqB;EACrBC,oBAAoB;EACpBC,gBAAgB;EAChBC,iBAAiB;EACjBC;AAAuB,IAAApD,EAAA;AAGzB,MAAMqD,UAAU,GAAG;EACjB,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,IAAI,EAAE,CAAC;EACP,KAAK,EAAE,CAAC;EACR,IAAI,EAAE,CAAC;EACP,KAAK,EAAE,CAAC;EACR,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACPC,EAAE,EAAE,CAAC;EACLC,UAAU,EAAE,CAAC;EACb,IAAI,EAAE,CAAC;EACP,IAAI,EAAE,CAAC;EACP,KAAK,EAAE,CAAC;EACR,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,GAAG,EAAE,CAAC;EACN,IAAI,EAAE;AACR,CAAC;AAWD,SAASC,kBAAkBA,CAACC,IAAY,EAAE;EACxC,OACErB,gBAAgB,CAACqB,IAAI,CAAC,IACtBL,uBAAuB,CAACK,IAAI,CAAC,IAC7Bf,iBAAiB,CAACe,IAAI,CAAC;AAE3B;AAEA,MAAMC,oBAAoB,GAAGA,CAC3BD,IAAY,EACZE,MAAc,KACQjD,OAAO,CAACiD,MAAM,EAAE;EAAEC,UAAU,EAAEH;AAAK,CAAC,CAAC;AAE7D,MAAMI,cAAc,GAAGA,CAACJ,IAAY,EAAEE,MAAc,KACjD,CAACjC,kBAAkB,CAACiC,MAAM,CAAC,IAAI5B,0BAA0B,CAAC4B,MAAM,CAAC,KAChEA,MAAM,CAACG,MAAM,KAAKL,IAAI,IACvB,CAAChD,gBAAgB,CAACkD,MAAM,CAAC,IACxB7B,wBAAwB,CAAC6B,MAAM,CAAC,IAChChC,eAAe,CAACgC,MAAM,CAAC,KACvBA,MAAM,CAACI,MAAM,KAAKN,IAAK,IACxBb,0BAA0B,CAACe,MAAM,CAAC,IAAIA,MAAM,CAACK,GAAG,KAAKP,IAAK,IAC3DlB,qBAAqB,CAACoB,MAAM,CAAC;AAExB,SAASM,sBAAsBA,CACpCR,IAA8B,EAC9BE,MAAc,EACL;EACT,OAAOzD,qBAAqB,CAACyD,MAAM,CAAC;AACtC;AAEO,SAASO,sBAAsBA,CACpCT,IAA8B,EAC9BE,MAAc,EACdQ,UAAyB,EAChB;EACT,IAAIA,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;EAE3B,OAEEpB,qBAAqB,CAACW,MAAM,CAAC,IAE7BnC,4BAA4B,CAACmC,MAAM,CAAC,IAEpCzD,qBAAqB,CAACyD,MAAM,CAAC,IAE5Bb,gBAAgB,CAACa,MAAM,CAAC,IAEvBxD,yBAAyB,CAACgE,UAAU,CAACA,UAAU,CAACC,MAAM,GAAG,CAAC,CAAC,CAAE;AAEnE;AAEO,SAASC,gBAAgBA,CAC9BZ,IAAwB,EACxBE,MAAc,EACL;EACT,OAAOE,cAAc,CAACJ,IAAI,EAAEE,MAAM,CAAC,IAAID,oBAAoB,CAACD,IAAI,EAAEE,MAAM,CAAC;AAC3E;AAEO,SAASW,gBAAgBA,CAC9Bb,IAAwB,EACxBE,MAAc,EACdQ,UAAyB,EAChB;EACT,OAAOI,gBAAgB,CACrBJ,UAAU,EACV,KAAqD,CACtD;AACH;AAEO,SAASK,YAAYA,CAC1Bf,IAAoB,EACpBE,MAAc,EACdQ,UAAyB,EAChB;EAET,OACE,CAACV,IAAI,CAACgB,KAAK,IAAIF,gBAAgB,CAACJ,UAAU,IAAiC;AAE/E;AAEO,SAASO,MAAMA,CAACjB,IAAwB,EAAEE,MAAc,EAAW;EACxE,IACEF,IAAI,CAACkB,QAAQ,KAAK,IAAI,IACtBpE,kBAAkB,CAACoD,MAAM,EAAE;IAAEgB,QAAQ,EAAE;EAAK,CAAC,CAAC,EAC9C;IACA,OAAOhB,MAAM,CAACiB,IAAI,KAAKnB,IAAI;EAC7B;EAEA,IAAIC,oBAAoB,CAACD,IAAI,EAAEE,MAAM,CAAC,EAAE;IACtC,OAAO,IAAI;EACb;EAEA,IACEE,cAAc,CAACJ,IAAI,EAAEE,MAAM,CAAC,IAC5BZ,WAAW,CAACY,MAAM,CAAC,IACnBtD,iBAAiB,CAACsD,MAAM,CAAC,EACzB;IACA,OAAO,IAAI;EACb;EAEA,IAAIrD,QAAQ,CAACqD,MAAM,CAAC,EAAE;IACpB,MAAMkB,QAAQ,GAAGlB,MAAM,CAACgB,QAAQ;IAChC,MAAMG,SAAS,GAAGzB,UAAU,CAACwB,QAAQ,CAAC;IAEtC,MAAME,MAAM,GAAGtB,IAAI,CAACkB,QAAQ;IAC5B,MAAMK,OAAO,GAAG3B,UAAU,CAAC0B,MAAM,CAAC;IAElC,IAEGD,SAAS,KAAKE,OAAO,IACpBrB,MAAM,CAACsB,KAAK,KAAKxB,IAAI,IACrB,CAAChC,mBAAmB,CAACkC,MAAM,CAAC,IAC9BmB,SAAS,GAAGE,OAAO,EACnB;MACA,OAAO,IAAI;IACb;EACF;AACF;AAEO,SAASE,mBAAmBA,CACjCzB,IAA2B,EAC3BE,MAAc,EACL;EACT,OACEzD,qBAAqB,CAACyD,MAAM,CAAC,IAC7B/B,wBAAwB,CAAC+B,MAAM,CAAC,IAChCnC,4BAA4B,CAACmC,MAAM,CAAC,IACpCX,qBAAqB,CAACW,MAAM,CAAC;AAEjC;AAIO,SAASwB,yBAAyBA,CACvC1B,IAAiC,EACjCE,MAAc,EACL;EACT,OAAOpC,mBAAmB,CAACoC,MAAM,EAAE;IAAEyB,UAAU,EAAE3B;EAAK,CAAC,CAAC;AAC1D;AAEO,SAAS4B,cAAcA,CAAA,EAAG;EAC/B,OAAO,IAAI;AACb;AAOO,SAASC,WAAWA,CAAC7B,IAAmB,EAAEE,MAAc,EAAW;EACxE,OACExB,aAAa,CAACwB,MAAM,CAAC,IACrBnB,gBAAgB,CAACmB,MAAM,CAAC,IACxBrB,oBAAoB,CAACqB,MAAM,CAAC,IAC5BhB,aAAa,CAACgB,MAAM,CAAC,IACrBlB,YAAY,CAACkB,MAAM,CAAC;AAExB;AAIO,SAAS4B,WAAWA,CAAC9B,IAAmB,EAAEE,MAAc,EAAW;EACxE,OAAOxB,aAAa,CAACwB,MAAM,CAAC,IAAInB,gBAAgB,CAACmB,MAAM,CAAC;AAC1D;AAEO,SAAS6B,yBAAyBA,CACvC/B,IAAiC,EACjCE,MAAc,EACd;EACA,OACE,CAAClD,gBAAgB,CAACkD,MAAM,CAAC,IACvB7B,wBAAwB,CAAC6B,MAAM,CAAC,IAChChC,eAAe,CAACgC,MAAM,CAAC,IACvBtB,2BAA2B,CAACsB,MAAM,CAAC,KACrC,CAAC,CAACA,MAAM,CAAC8B,cAAc;AAE3B;AAEO,SAASC,gBAAgBA,CAC9BjC,IAAwB,EACxBE,MAAc,EACL;EAGT,OACEF,IAAI,CAACkB,QAAQ,KAAK,IAAI,KAAK1B,oBAAoB,CAACU,MAAM,CAAC,IAAI1C,KAAK,CAAC0C,MAAM,CAAC,CAAC;AAE7E;AAEO,SAASgC,kBAAkBA,CAChClC,IAA0B,EAC1BE,MAAc,EACL;EACT,IAKEvC,cAAc,CAACuC,MAAM,CAAC,IACtBd,gBAAgB,CAACc,MAAM,CAAC,IACxB3B,iBAAiB,CAAC2B,MAAM,CAAC,IACxBrC,aAAa,CAACqC,MAAM,CAAC,IAAIA,MAAM,CAACiC,IAAI,KAAKnC,IAAK,IAC9CP,gBAAgB,CAACS,MAAM,CAAC,IAAIA,MAAM,CAACiC,IAAI,KAAKnC,IAAK,IACjDvC,gBAAgB,CAACyC,MAAM,CAAC,IAAIA,MAAM,CAACsB,KAAK,KAAKxB,IAAK,IAClDvB,iBAAiB,CAACyB,MAAM,CAAC,IAAIA,MAAM,CAACkC,YAAY,KAAKpC,IAAK,IAC1DzC,qBAAqB,CAAC2C,MAAM,CAAC,IAAIA,MAAM,CAACmC,UAAU,KAAKrC,IAAK,EAC7D;IACA,OAAO,KAAK;EACd;EAIA,OAAO,IAAI;AACb;AAEO,SAASsC,eAAeA,CAC7BtC,IAAuB,EACvBE,MAAc,EACL;EACT,OACErD,QAAQ,CAACqD,MAAM,CAAC,IAChBZ,WAAW,CAACY,MAAM,CAAC,IACnBE,cAAc,CAACJ,IAAI,EAAEE,MAAM,CAAC,IAC3BtD,iBAAiB,CAACsD,MAAM,CAAC,IAAIR,iBAAiB,CAACM,IAAI,CAAE,IACrD5C,uBAAuB,CAAC8C,MAAM,CAAC,IAAIF,IAAI,KAAKE,MAAM,CAACiC,IAAK,IACzDlC,oBAAoB,CAACD,IAAI,EAAEE,MAAM,CAAC;AAEtC;AAIO,SAASqC,eAAeA,CAC7BvC,IAAuB,EACvBE,MAAc,EACdQ,UAAyB,EAChB;EACT,OAAOI,gBAAgB,CACrBJ,UAAU,EACV,KAAyD,CAC1D;AACH;AAEO,SAAS8B,SAASA,CACvBxC,IAI0B,EAC1BE,MAAc,EACL;EACT,OACEE,cAAc,CAACJ,IAAI,EAAEE,MAAM,CAAC,IAC5BpD,kBAAkB,CAACoD,MAAM,EAAE;IAAEgB,QAAQ,EAAE,IAAI;IAAEC,IAAI,EAAEnB;EAAK,CAAC,CAAC,IAC1DC,oBAAoB,CAACD,IAAI,EAAEE,MAAM,CAAC;AAEtC;AAEO,SAASuC,kBAAkBA,CAChCzC,IAA0B,EAC1BE,MAAc,EACdQ,UAAyB,EAChB;EACT,OAAOI,gBAAgB,CACrBJ,UAAU,EACV,KAAyD,CAC1D;AACH;AAEO,SAASgC,uBAAuBA,CACrC1C,IAA+B,EAC/BE,MAAc,EACL;EACT,OAAO7C,mBAAmB,CAAC6C,MAAM,CAAC,IAAIyC,qBAAqB,CAAC3C,IAAI,EAAEE,MAAM,CAAC;AAC3E;AAEO,SAASyC,qBAAqBA,CACnC3C,IAG0B,EAC1BE,MAAe,EACN;EACT,IACEZ,WAAW,CAACY,MAAM,CAAC,IACnBrD,QAAQ,CAACqD,MAAM,CAAC,IAChB9C,uBAAuB,CAAC8C,MAAM,EAAE;IAAEiC,IAAI,EAAEnC;EAAK,CAAC,CAAC,IAC/CpD,iBAAiB,CAACsD,MAAM,CAAC,IACzBH,kBAAkB,CAACG,MAAM,CAAC,EAC1B;IACA,OAAO,IAAI;EACb;EAEA,OAAOsC,SAAS,CAACxC,IAAI,EAAEE,MAAM,CAAC;AAChC;AAEO,SAAS0C,wBAAwBA,CACtC5C,IAAgC,EAChCE,MAAc,EACL;EACT,OACElD,gBAAgB,CAACkD,MAAM,EAAE;IAAEI,MAAM,EAAEN;EAAK,CAAC,CAAC,IAC1C/B,kBAAkB,CAACiC,MAAM,EAAE;IAAEG,MAAM,EAAEL;EAAK,CAAC,CAAC;AAEhD;AAIO,SAAS6C,oBAAoBA,CAClC7C,IAA4B,EAC5BE,MAAc,EACL;EACT,IAAI9B,eAAe,CAAC4B,IAAI,CAACmB,IAAI,CAAC,EAAE;IAC9B,OAAO,IAAI;EACb,CAAC,MAAM;IACL,OAAOwB,qBAAqB,CAAC3C,IAAI,EAAEE,MAAM,CAAC;EAC5C;AACF;AAEO,SAAS4C,iBAAiBA,CAC/B9C,IAAyB,EACzBE,MAAc,EACL;EACT,IAAIH,kBAAkB,CAACG,MAAM,CAAC,EAAE,OAAO,IAAI;EAC3C,QAAQF,IAAI,CAACkB,QAAQ;IACnB,KAAK,IAAI;MACP,IAAI,CAAClD,mBAAmB,CAACkC,MAAM,CAAC,EAAE,OAAO,KAAK;MAC9C,OAAOA,MAAM,CAACgB,QAAQ,KAAK,IAAI,IAAIhB,MAAM,CAACgB,QAAQ,KAAK,IAAI;IAC7D,KAAK,IAAI;MACP,OAAOlD,mBAAmB,CAACkC,MAAM,EAAE;QAAEgB,QAAQ,EAAE;MAAK,CAAC,CAAC;IACxD,KAAK,IAAI;MACP,OAAOlD,mBAAmB,CAACkC,MAAM,CAAC,IAAIA,MAAM,CAACgB,QAAQ,KAAK,IAAI;EAAC;AAErE;AAEO,SAAS6B,UAAUA,CACxB/C,IAAkB,EAClBE,MAAc,EACdQ,UAAyB,EAChB;EAAA,IAAAsC,WAAA;EAGT,IACE,CAAAA,WAAA,GAAAhD,IAAI,CAACiD,KAAK,aAAVD,WAAA,CAAYE,aAAa,IACzBvG,sBAAsB,CAACuD,MAAM,EAAE;IAAEiB,IAAI,EAAEnB;EAAK,CAAC,CAAC,KAC7CpC,oBAAoB,CAACsC,MAAM,CAACsB,KAAK,CAAC,IAAItE,iBAAiB,CAACgD,MAAM,CAACsB,KAAK,CAAC,CAAC,IACvEtB,MAAM,CAACsB,KAAK,CAAC2B,EAAE,IAAI,IAAI,EACvB;IACA,OAAO,IAAI;EACb;EAGA,IAAInD,IAAI,CAACoD,IAAI,KAAK,KAAK,EAAE;IAGvB,MAAMC,mBAAmB,GACvBpF,kBAAkB,CAACiC,MAAM,EAAE;MACzBG,MAAM,EAAEL,IAAI;MACZsD,QAAQ,EAAE;IACZ,CAAC,CAAC,IACFhF,0BAA0B,CAAC4B,MAAM,EAAE;MACjCG,MAAM,EAAEL,IAAI;MACZsD,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACJ,OAAOzC,gBAAgB,CACrBJ,UAAU,EACV2C,mBAAmB,GACf,KACoB,KACE,KACA,KACF,CACzB;EACH;EAUA,OACErD,IAAI,CAACoD,IAAI,KAAK,OAAO,IAAI1F,gBAAgB,CAACwC,MAAM,CAAC,IAAIF,IAAI,KAAKE,MAAM,CAACiB,IAAI;AAE7E;AAIA,SAASL,gBAAgBA,CACvBJ,UAAyB,EACzB8C,UAAsB,EACb;EACT,MAAMC,mBAAmB,GAAGD,UAAU,IAAiC;EACvE,MAAME,SAAS,GAAGF,UAAU,IAAuB;EACnD,MAAMG,aAAa,GAAGH,UAAU,IAA2B;EAC3D,MAAMI,OAAO,GAAGJ,UAAU,IAAqB;EAC/C,MAAMK,SAAS,GAAGL,UAAU,KAAuB;EACnD,MAAMM,SAAS,GAAGN,UAAU,KAAuB;EAEnD,IAAIO,CAAC,GAAGrD,UAAU,CAACC,MAAM,GAAG,CAAC;EAC7B,IAAIoD,CAAC,IAAI,CAAC,EAAE;EACZ,IAAI/D,IAAI,GAAGU,UAAU,CAACqD,CAAC,CAAC;EACxBA,CAAC,EAAE;EACH,IAAI7D,MAAM,GAAGQ,UAAU,CAACqD,CAAC,CAAC;EAC1B,OAAOA,CAAC,IAAI,CAAC,EAAE;IACb,IACGN,mBAAmB,IAClBlG,qBAAqB,CAAC2C,MAAM,EAAE;MAAEmC,UAAU,EAAErC;IAAK,CAAC,CAAC,IACpD2D,aAAa,IACZrG,0BAA0B,CAAC4C,MAAM,EAAE;MAAE8D,WAAW,EAAEhE;IAAK,CAAC,CAAE,IAC3D0D,SAAS,IAAIhH,yBAAyB,CAACwD,MAAM,EAAE;MAAE+D,IAAI,EAAEjE;IAAK,CAAC,CAAE,IAC/D4D,OAAO,IAAIjG,cAAc,CAACuC,MAAM,EAAE;MAAEgE,IAAI,EAAElE;IAAK,CAAC,CAAE,IAClD6D,SAAS,IAAIpG,gBAAgB,CAACyC,MAAM,EAAE;MAAEiB,IAAI,EAAEnB;IAAK,CAAC,CAAE,IACtD8D,SAAS,IAAIpG,gBAAgB,CAACwC,MAAM,EAAE;MAAEiB,IAAI,EAAEnB;IAAK,CAAC,CAAE,EACvD;MACA,OAAO,IAAI;IACb;IAEA,IACE+D,CAAC,GAAG,CAAC,KACH3D,cAAc,CAACJ,IAAI,EAAEE,MAAM,CAAC,IAAI,CAAChC,eAAe,CAACgC,MAAM,CAAC,IACvD1B,oBAAoB,CAAC0B,MAAM,CAAC,IAAIA,MAAM,CAACiE,WAAW,CAAC,CAAC,CAAC,KAAKnE,IAAK,IAC/DjD,kBAAkB,CAACmD,MAAM,CAAC,IAAI,CAACA,MAAM,CAACkE,MAAO,IAC9CjH,aAAa,CAAC+C,MAAM,EAAE;MAAEiC,IAAI,EAAEnC;IAAK,CAAC,CAAC,IACrCnD,QAAQ,CAACqD,MAAM,EAAE;MAAEiB,IAAI,EAAEnB;IAAK,CAAC,CAAC,IAChCrD,sBAAsB,CAACuD,MAAM,EAAE;MAAEiB,IAAI,EAAEnB;IAAK,CAAC,CAAC,CAAC,EACjD;MACAA,IAAI,GAAGE,MAAM;MACb6D,CAAC,EAAE;MACH7D,MAAM,GAAGQ,UAAU,CAACqD,CAAC,CAAC;IACxB,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF;EAEA,OAAO,KAAK;AACd"}