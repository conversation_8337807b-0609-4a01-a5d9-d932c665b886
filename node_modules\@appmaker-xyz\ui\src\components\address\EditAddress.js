import React, { useState } from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import { Layout, ThemeText, AppTouchable } from '../atoms';

import Icon from 'react-native-vector-icons/Feather';
import { Input, DropSelect } from '../coreComponents';

const EditAddress = (props) => {
  const [value, setValue] = useState('');
  const onPress = props?.onSubmit;
  return (
    <Layout style={styles.modalContainer}>
      <ScrollView contentContainerStyle={styles.inputsContainer}>
        <ThemeText color="#868E96" size="sm">
          *Required fields.
        </ThemeText>
        <Layout style={styles.heading}>
          <Layout style={styles.numberBox}>
            <ThemeText color="#3280FE">1</ThemeText>
          </Layout>
          <ThemeText color="#3280FE" fontFamily="medium">
            Shipping Address
          </ThemeText>
        </Layout>
        <Input
          label="First Name"
          placeholder="Enter First Name"
          value={value}
          onChange={setValue}
        />
        <Input
          label="Last Name"
          placeholder="Enter Last Name"
          value={value}
          onChange={setValue}
        />
        <Input
          label="Address Line 1"
          placeholder="Enter Address Line 1"
          value={value}
          onChange={setValue}
        />
        <AppTouchable style={styles.addNewLineButton}>
          <Icon name="plus" size={12} color="#3280FE" style={styles.plusIcon} />
          <ThemeText color="#3280FE">Street Address 2 (Optional)</ThemeText>
        </AppTouchable>
        <Input
          label="Address Line 2"
          placeholder="Enter Address Line 2"
          value={value}
          onChange={setValue}
        />
        <Input
          label="Email"
          placeholder="Enter Email"
          value={value}
          onChange={setValue}
        />
        <Input
          label="Phone"
          placeholder="Enter Phone"
          value={value}
          onChange={setValue}
        />
        <Input
          label="City"
          placeholder="Enter City"
          value={value}
          onChange={setValue}
        />
        <DropSelect attributes={{ label: 'State' }} />
        <DropSelect attributes={{ label: 'Country' }} />
        <Input
          label="Pin Code"
          placeholder="Enter Pin Code"
          value={value}
          onChange={setValue}
        />
        <AppTouchable style={styles.deliverButton} onPress={onPress}>
          <ThemeText size="lg" color="#fff">
            Deliver Here
          </ThemeText>
        </AppTouchable>
      </ScrollView>
    </Layout>
  );
};

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
  },
  heading: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
    marginBottom: 12,
  },
  numberBox: {
    borderWidth: 1,
    borderColor: '#3280FE',
    color: '#3280FE',
    borderRadius: 4,
    height: 24,
    width: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  closeIcon: {
    padding: 6,
  },
  inputsContainer: {
    paddingHorizontal: 16,
    paddingBottom: 18,
    paddingTop: 12,
  },
  addNewLineButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    marginBottom: 12,
  },
  plusIcon: {
    marginRight: 4,
  },
  deliverButton: {
    flexDirection: 'row',
    backgroundColor: '#1E232C',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 6,
  },
});

export default EditAddress;
