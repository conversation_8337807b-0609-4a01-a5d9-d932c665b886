import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ThemeText, AppTouchable } from '../atoms';
import { Button } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { AppModal } from '../coreComponents';

const PopupLogin = ({ attributes = {} }) => {
  const { onLogin, onGuest } = attributes;
  const [isModalVisible, setModalVisible] = useState(false);

  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };

  return (
    <Layout>
      <Button title="LOGIN POPUP" onPress={toggleModal} />

      <AppModal
        isVisible={isModalVisible}
        onBackdropPress={() => setModalVisible(false)}
        style={styles.modal}>
        <Layout style={styles.modalContainer}>
          <Layout style={styles.modalHeader}>
            <ThemeText size="xl" fontFamily="medium">
              {'Welcome! Glad to see\nyou!'}
            </ThemeText>
            <Icon
              name="x"
              size={28}
              color="#000"
              style={styles.closeIcon}
              onPress={toggleModal}
            />
          </Layout>
          <ThemeText style={styles.text} color="#8391A1">
            Log in & finish your order!
          </ThemeText>
          <Layout style={styles.cardActionsContainer}>
            <AppTouchable style={styles.loginButton} onPress={onLogin}>
              <ThemeText fontFamily="medium">Sign in / Sign up</ThemeText>
            </AppTouchable>
            <AppTouchable style={styles.guestButton} onPress={onGuest}>
              <ThemeText color="#fff" fontFamily="medium">
                Continue as Guest
              </ThemeText>
            </AppTouchable>
          </Layout>
        </Layout>
      </AppModal>
    </Layout>
  );
};
const styles = StyleSheet.create({
  modal: {
    justifyContent: 'center',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 18,
    overflow: 'hidden',
    padding: 18,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  closeIcon: {
    padding: 6,
  },
  text: {
    marginTop: 6,
    marginBottom: 18,
  },
  cardActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  loginButton: {
    borderWidth: 1,
    borderColor: '#000',
    paddingVertical: 12,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
    overflow: 'hidden',
  },
  guestButton: {
    borderWidth: 1,
    borderColor: '#000',
    backgroundColor: '#000000',
    paddingVertical: 12,
    flex: 1,
    marginLeft: 6,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
    overflow: 'hidden',
  },
});

export default PopupLogin;
