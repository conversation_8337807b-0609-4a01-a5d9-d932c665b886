import React from 'react';
import { View, StyleSheet } from 'react-native';
import { BlocksView } from '@appmaker-xyz/react-native';
import { useApThemeState } from '../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import AppmakerText from '../atoms/AppmakerText';
import Button from '../molecules/buttons';
import ActivityIndicator from '../molecules/ActivityIndicator';
const Layout = ({
  onAction,
  children,
  style,
  loading = false,
  error = false,
  errorText = 'Something went wrong',
  empty = false,
  emptyText = 'No items',
  blank = false,
  iconName,
  message,
  emptyRetry,
  showEmptyView = true,
  onErrorRetry,
  loadingLayout = 'normal',
  ...props
}) => {
  const { color } = useApThemeState();
  // console.log();
  if (loading) {
    return (
      <View {...props}>
        <ActivityIndicator
          color={color?.primary}
          size="large"
          type={loadingLayout}
        />
      </View>
    );
  }
  if (error || empty || blank) {
    return (
      <BlocksView
        onAction={
          onAction
            ? onAction
            : () => {
                console.log('Action cannot be performed');
              }
        }
        inAppPage={{
          blocks: [
            {
              name: 'appmaker/error-block',
              attributes: {
                error,
                errorText,
                iconName,
                empty,
                showEmptyView,
                onErrorRetry,
                blank,
                message,
                emptyText,
                emptyRetry,
                otherProps: props,
              },
            },
          ],
        }}
      />
    );
  }
  return (
    <View style={style} {...props}>
      {children}
    </View>
  );
};

const allStyles = ({ spacing }) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
      paddingTop: spacing.lg * 2,
    },
    cicleContainer: {
      padding: spacing?.lg,
      width: 76,
      height: 76,
      borderRadius: 76 / 2,
    },
    innerIcon: {
      opacity: 1,
      alignSelf: 'center',
      justifyContent: 'center',
    },
    text: {
      marginTop: spacing?.base,
      marginBottom: spacing?.lg,
    },
  });

export default Layout;
