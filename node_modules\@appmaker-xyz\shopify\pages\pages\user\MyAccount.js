// appmaker/profile-card-item
// 'appmaker/actionbar'
// {"access_token": "token_98ce61dd8afde157aa3776f898b99a0572ab2da7", "status": 1, "user": {"avatar": "https://secure.gravatar.com/avatar/4c502bb0e4c66f5c50062eb19d9aecc2?s=96&d=mm&r=g", "display_name": "wooapp", "email": "<EMAIL>", "id": "3", "nicename": "wooapp", "status": "0"}
const MyAccount = {
  blocks: [
    {
      name: 'appmaker/shopify-access-token-checker',
      attributes: {
        user: '{{appStorageState.user}}',
      },
    },
    {
      name: 'shopify/profile-card-item',
      attributes: {},
    },
    {
      clientId: 'my-account-menu-orders',
      name: 'appmaker/actionbar',
      attributes: {
        title: 'Orders',
        appmakerAction: {
          pageId: 'OrderList',
          params: {
            pageData: '{{appStorageState.user}}',
          },
          action: 'OPEN_INAPP_PAGE',
        },
      },
      dependencies: {
        appStorageState: ['user'],
      },
    },
    {
      clientId: 'address-list',
      name: 'appmaker/actionbar',
      attributes: {
        title: 'Address',
        __display: '{{!checkIfTrueFalse(plugins.shopify.settings.hide_myaccount_address)}}',
        appmakerAction: {
          params: {},
          action: 'OPEN_ADDRESS_LIST_PAGE',
        },
      },
    },
    {
      clientId: 'my-account-menu',
      name: 'appmaker/actionbar',
      attributes: {
        title: 'Delete account',
        __display: '{{__platformOS === "ios" }}',
        appmakerAction: {
          params: {},
          action: 'OPEN_INAPP_PAGE',
          pageId: 'DeleteAccountConfirmation',
        },
      },
    },

    {
      clientId: 'my-account-menu',
      name: 'appmaker/actionbar',
      attributes: {
        title: 'Log out',
        appmakerAction: {
          params: {},
          action: 'LOGOUT',
        },
      },
    },
  ],
  attributes: {
    renderType: 'normal',
    renderInScroll: true,
    backgroundColor: '#FFFFFF',
    scrollViewContentContainerStyle: {
      paddingHorizontal: 6,
      paddingTop: 8,
    },
    backgroundColor: '#FFFFFF',
  },
  title: 'My Account',
};
export default MyAccount;
