{"version": 3, "names": ["Plug<PERSON><PERSON><PERSON>", "constructor", "file", "key", "options", "_map", "Map", "opts", "cwd", "filename", "set", "val", "get", "availableHelper", "name", "versionRange", "addHelper", "addImport", "buildCodeFrameError", "node", "msg", "_Error", "exports", "default", "prototype", "getModuleName"], "sources": ["../../src/transformation/plugin-pass.ts"], "sourcesContent": ["import type File from \"./file/file\";\nimport type { NodeLocation } from \"./file/file\";\n\nexport default class PluginPass {\n  _map: Map<unknown, unknown> = new Map();\n  key: string | undefined | null;\n  file: File;\n  opts: any;\n\n  // The working directory that Babel's programmatic options are loaded\n  // relative to.\n  cwd: string;\n\n  // The absolute path of the file being compiled.\n  filename: string | void;\n\n  constructor(file: File, key?: string | null, options?: any | null) {\n    this.key = key;\n    this.file = file;\n    this.opts = options || {};\n    this.cwd = file.opts.cwd;\n    this.filename = file.opts.filename;\n  }\n\n  set(key: unknown, val: unknown) {\n    this._map.set(key, val);\n  }\n\n  get(key: unknown): any {\n    return this._map.get(key);\n  }\n\n  availableHelper(name: string, versionRange?: string | null) {\n    return this.file.availableHelper(name, versionRange);\n  }\n\n  addHelper(name: string) {\n    return this.file.addHelper(name);\n  }\n\n  // TODO: Remove this in Babel 8\n  addImport() {\n    this.file.addImport();\n  }\n\n  buildCodeFrameError(\n    node: NodeLocation | undefined | null,\n    msg: string,\n    _Error?: typeof Error,\n  ) {\n    return this.file.buildCodeFrameError(node, msg, _Error);\n  }\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  (PluginPass as any).prototype.getModuleName = function getModuleName():\n    | string\n    | undefined {\n    return this.file.getModuleName();\n  };\n}\n"], "mappings": ";;;;;;AAGe,MAAMA,UAAU,CAAC;EAa9BC,WAAWA,CAACC,IAAU,EAAEC,GAAmB,EAAEC,OAAoB,EAAE;IAAA,KAZnEC,IAAI,GAA0B,IAAIC,GAAG,EAAE;IAAA,KACvCH,GAAG;IAAA,KACHD,IAAI;IAAA,KACJK,IAAI;IAAA,KAIJC,GAAG;IAAA,KAGHC,QAAQ;IAGN,IAAI,CAACN,GAAG,GAAGA,GAAG;IACd,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACK,IAAI,GAAGH,OAAO,IAAI,CAAC,CAAC;IACzB,IAAI,CAACI,GAAG,GAAGN,IAAI,CAACK,IAAI,CAACC,GAAG;IACxB,IAAI,CAACC,QAAQ,GAAGP,IAAI,CAACK,IAAI,CAACE,QAAQ;EACpC;EAEAC,GAAGA,CAACP,GAAY,EAAEQ,GAAY,EAAE;IAC9B,IAAI,CAACN,IAAI,CAACK,GAAG,CAACP,GAAG,EAAEQ,GAAG,CAAC;EACzB;EAEAC,GAAGA,CAACT,GAAY,EAAO;IACrB,OAAO,IAAI,CAACE,IAAI,CAACO,GAAG,CAACT,GAAG,CAAC;EAC3B;EAEAU,eAAeA,CAACC,IAAY,EAAEC,YAA4B,EAAE;IAC1D,OAAO,IAAI,CAACb,IAAI,CAACW,eAAe,CAACC,IAAI,EAAEC,YAAY,CAAC;EACtD;EAEAC,SAASA,CAACF,IAAY,EAAE;IACtB,OAAO,IAAI,CAACZ,IAAI,CAACc,SAAS,CAACF,IAAI,CAAC;EAClC;EAGAG,SAASA,CAAA,EAAG;IACV,IAAI,CAACf,IAAI,CAACe,SAAS,EAAE;EACvB;EAEAC,mBAAmBA,CACjBC,IAAqC,EACrCC,GAAW,EACXC,MAAqB,EACrB;IACA,OAAO,IAAI,CAACnB,IAAI,CAACgB,mBAAmB,CAACC,IAAI,EAAEC,GAAG,EAAEC,MAAM,CAAC;EACzD;AACF;AAACC,OAAA,CAAAC,OAAA,GAAAvB,UAAA;AAEkC;EAChCA,UAAU,CAASwB,SAAS,CAACC,aAAa,GAAG,SAASA,aAAaA,CAAA,EAEtD;IACZ,OAAO,IAAI,CAACvB,IAAI,CAACuB,aAAa,EAAE;EAClC,CAAC;AACH;AAAC"}