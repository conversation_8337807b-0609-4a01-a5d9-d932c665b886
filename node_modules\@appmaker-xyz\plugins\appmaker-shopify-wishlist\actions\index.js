export async function ADD_WISHLIST(action, { runDataSource }) {
  const { id, key = 'default' } = action?.params;
  if (id) {
    const dataSource = {
      attributes: {},
      source: 'appmaker-shopify-wishlist',
    };
    const [response] = await runDataSource(
      {
        dataSource,
      },
      {
        methodName: 'saveItem',
        params: {
          id,
          key,
        },
      },
    );
  }
}

export async function REMOVE_WISHLIST(action, { runDataSource }) {
  const { id, key = 'default' } = action?.params;
  const dataSource = {
    attributes: {},
    source: 'appmaker-shopify-wishlist',
  };
  if (!!id) {
    const [response] = await runDataSource(
      {
        dataSource,
      },
      {
        methodName: 'removeItem',
        params: {
          id,
          key,
        },
      },
    );
  } else {
  }
}

// Sync wishlist between remote and local upon login
export function SYNC_WISHLIST(action, {}) {}
