import { useDataSourceV2 } from '@appmaker-xyz/core';
interface MenuItem {}
interface NavigationMenuResponse {
  data: {
    blocks: MenuItem[];
  };
}
type MenuItems = {
  blocks: Array<any>;
};

const useMenuItems = (props): MenuItems => {
  const dataSource = {
    source: 'shopify',
    methodName: 'getNavigationMenu',
    params: {},
  };
  const [queryResult, { item }] = useDataSourceV2<
    NavigationMenuResponse,
    NavigationMenuResponse
  >({
    dataSource,
    enabled: true,
  });
  return {
    blocks: item?.data?.blocks,
    ...queryResult
  };
};

export { useMenuItems };
