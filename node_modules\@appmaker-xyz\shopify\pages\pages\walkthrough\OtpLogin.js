import { color, spacing } from '../../styles';

const Onboarding = {
  type: 'normal',
  title: 'Register Page',
  attributes: {
    headerShown: false,
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
      paddingHorizontal: spacing.base,
    },
  },
  blocks: [
    {
      name: 'appmaker/appImage',
      attributes: {
        uri: 'https://storage.googleapis.com/site-cdn.appmaker.xyz/2022/06/6ecad864-peachmode_logo_390x.webp',
        resizeMode: 'contain',
        style: {
          width: 250,
          height: 100,
          marginTop: spacing.xl,
          marginBottom: spacing.lg,
        },
      },
    },
    {
      name: 'appmaker/layout',
      attributes: {
        style: {
          marginBottom: spacing.lg,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/text',
          attributes: {
            content: 'Login with OTP',
            category: 'h1Heading',
          },
        },
        {
          name: 'appmaker/text',
          attributes: {
            content: 'Enter your mobile number to get the OTP.',
            category: 'bodySubText',
          },
        },
      ],
    },
    {
      name: 'appmaker/floating-label-input',
      attributes: {
        label: 'Enter Phone Number',
      },
    },
    {
      name: 'appmaker/button',
      attributes: {
        content: 'Get OTP',
        baseSize: true,
        wholeContainerStyle: {
          borderRadius: 40,
          marginTop: spacing.base,
          backgroundColor: '#ee3d63',
          borderWidth: 0,
        },
        appmakerAction: {
          action: 'OPEN_INAPP_PAGE',
          pageId: 'OtpLoginVerify',
        },
      },
    },
  ],
};
export default Onboarding;
