import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ImageSwiper } from '../../../components';
import { AppTouchable, AppmakerText, AppImage } from '../../../components';

import { useApThemeState } from '../../../theme/ThemeContext';

const CarouselBlock = ({ children }) => {
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  return (
    <Layout style={styles.container}>
      <AnySwiper
        // children={children}
        attributes={{
          imageList: postset.posts,
          title: postset.posts,
          autoplay: true,
          resizeMode: 'cover',
        }}>
        {/* {children} */}
        {postset.posts.map((item) => {
          const uri = typeof item === 'string' ? item : item.uri;
          return (
            <AppTouchable>
              <AppImage uri={uri} resizeMode={'cover'} />

              <Layout>
                <AppmakerText
                  category="h1Heading"
                  status="white"
                  numberOfLines={3}>
                  {item.title}
                </AppmakerText>
                <AppmakerText status="light" category="highlighter1">
                  {item.authorName} | {item.timeStamp}
                </AppmakerText>
              </Layout>
            </AppTouchable>
          );
        })}
      </AnySwiper>
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      width: '100%',
      height: 280,
      backgroundColor: color.dark,
    },
  });

export default CarouselBlock;

const postset = {
  posts: [
    {
      titleMeta: 'title-meta',
      title: 'Lorem ipsum dolor sit amet consectetur adipisicing elit.',
      uri:
        'https://images.unsplash.com/photo-1598418031169-4cbc3bee87e1?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=375&q=80',
      //   coverLetter: true,
      excerpt:
        'Lorem ipsum dolor sit amet consectetur adipisicing elit. Corporis beatae quibusdam est saepe quia in dicta cupiditate reiciendis nihil facere!',
      authorName: 'John Doe',
      timeStamp: 'Yesterday',
    },
    {
      titleMeta: 'title-meta',
      title: 'Corporis beatae quibusdam est saepe quia in dicta.',
      uri:
        'https://images.unsplash.com/photo-1549486047-80c486539c06?ixlib=rb-1.2.1&auto=format&fit=crop&w=750&q=80',
      //   coverLetter: true,
      excerpt:
        'Lorem ipsum dolor sit amet consectetur adipisicing elit. Corporis beatae quibusdam est saepe quia in dicta cupiditate reiciendis nihil facere!',
      authorName: 'John Doe',
      timeStamp: 'Yesterday',
    },
    {
      titleMeta: 'title-meta',
      title: 'Lorem ipsum dolor sit amet consectetur adipisicing elit.',
      uri:
        'https://images.unsplash.com/photo-1540563567510-6155d809c3f9?ixlib=rb-1.2.1&auto=format&fit=crop&w=750&q=80',
      //   coverLetter: true,
      excerpt:
        'Lorem ipsum dolor sit amet consectetur adipisicing elit. Corporis beatae quibusdam est saepe quia in dicta cupiditate reiciendis nihil facere!',
      authorName: 'John Doe',
      timeStamp: 'Yesterday',
    },
    {
      titleMeta: 'title-meta',
      title: 'Corporis beatae quibusdam est saepe quia in dicta.',
      uri:
        'https://storage.googleapis.com/stateless-blog-appmaker-xyz/2020/08/6e176818-best-multi-vendor-plugin-for-woocommerce-768x432.png',
      // coverLetter: true,
      excerpt:
        'Lorem ipsum dolor sit amet consectetur adipisicing elit. Corporis beatae quibusdam est saepe quia in dicta cupiditate reiciendis nihil facere!',
      authorName: 'John Doe',
      timeStamp: 'Yesterday',
    },
  ],
};
