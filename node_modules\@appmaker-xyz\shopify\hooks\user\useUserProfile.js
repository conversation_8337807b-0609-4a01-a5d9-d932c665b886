import { useState } from 'react';
import { currencyHelper } from '../../helper';
import { appStorageApi, runDataSource } from '@appmaker-xyz/core';
// customerUpdate
export function useUserProfile(props) {
  const { attributes, onAction, pageDispatch, coreDispatch } = props;
  const user = appStorageApi().getState()?.user;
  const [newFirstName, setNewFirstName] = useState(user?.firstName || '');
  const [newLastName, setNewLastName] = useState(user?.lastName || '');
  const [newEmail, setNewEmail] = useState(user?.email || '');
  const [newPhone, setNewPhone] = useState(user?.phone || '');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const shopifyDatasource = {
    dataSource: {
      source: 'shopify',
      attributes: {},
    },
  };
  const updateProfile = async () => {
    setIsLoading(true);
    try {
      const userData = {
        firstName: newFirstName,
        lastName: newLastName,
        email: newEmail,
        phone: newPhone,
        accessToken: user?.accessToken,
      };
      const [response] = await runDataSource(shopifyDatasource, {
        methodName: 'customerUpdate',
        params: userData,
      });
      if (response.status === 200) {
        setIsLoading(false);
        const customerUpdateData = response?.data?.data?.customerUpdate;
        if (
          !(customerUpdateData?.customerUserErrors?.length > 0) &&
          customerUpdateData?.customer
        ) {
          coreDispatch({
            type: 'SET_APP_VAR_LOCAL',
            name: 'user',
            // value: response.data.data[].checkout,
            value: {
              accessToken: user?.accessToken,
              ...customerUpdateData?.customer,
            },
          });
          onAction?.({
            action: 'SHOW_MESSAGE',
            params: {
              title: 'Profile updated successfully',
            },
          });

          onAction?.({
            action: 'GO_BACK',
          });
          return { status: 'success', data: response };
        } else if (customerUpdateData?.customerUserErrors?.length > 0) {
          showShopifyErrorMessage(customerUpdateData?.customerUserErrors);
        }
      } else {
        onAction?.({
          action: 'SHOW_MESSAGE',
          params: {
            title: 'Something went wrong, please try again.',
          },
        });
        setIsLoading(false);
        return {
          status: 'fail',
          data: response,
          error: true,
          message: 'Something went wrong',
        };
      }
    } catch (e) {
      setIsLoading(false);
      onAction?.({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'Something went wrong, please try again.',
        },
      });
      return {
        status: 'fail',
        error: true,
        message: 'Something went wrong',
      };
    }
  };

  const changePassword = async () => {
    // vhhgfg
    if (newPassword && confirmPassword && newPassword === confirmPassword) {
      setIsLoading(true);
      try {
        const [response] = await runDataSource(shopifyDatasource, {
          methodName: 'customerUpdate',
          params: { password: newPassword, accessToken: user?.accessToken },
        });
        setIsLoading(false);
        if (response.status === 200) {
          setIsLoading(false);
          const customerUpdateData = response?.data?.data?.customerUpdate;
          if (
            !(customerUpdateData?.customerUserErrors?.length > 0) &&
            customerUpdateData?.customer
          ) {
            coreDispatch({
              type: 'SET_APP_VAR_LOCAL',
              name: 'user',
              // value: response.data.data[].checkout,
              value: {
                accessToken:
                  customerUpdateData?.customerAccessToken?.accessToken ||
                  user?.accessToken,
                ...customerUpdateData?.customer,
              },
            });
            onAction?.({
              action: 'SHOW_MESSAGE',
              params: {
                title: 'Password Changed Successfully',
              },
            });

            onAction?.({
              action: 'GO_BACK',
            });
            return { status: 'success', data: response };
          } else if (customerUpdateData?.customerUserErrors?.length > 0) {
            showShopifyErrorMessage(customerUpdateData?.customerUserErrors);
          }
        } else {
          setIsLoading(false);
          onAction?.({
            action: 'SHOW_MESSAGE',
            params: {
              title: 'Something went wrong please try again',
            },
          });
          return {
            status: 'fail',
            data: response,
            error: true,
            message: 'Something went wrong',
          };
        }
      } catch (e) {
        setIsLoading(false);
      }
    } else {
      onAction?.({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'Passwords do not match please try again',
        },
      });
      return {
        status: 'fail',
        // data: response,
        error: true,
        message: 'Password do not match',
      };
    }
  };

  const showShopifyErrorMessage = (errors) => {
    if (errors?.length > 0) {
      const errorMessage = errors[0]?.message;
      onAction?.({
        action: 'SHOW_MESSAGE',
        params: {
          title: errorMessage,
        },
      });
    }
  };

  return {
    user,
    firstName: newFirstName,
    lastName: newLastName,
    email: newEmail,
    phone: newPhone,
    setNewFirstName,
    setNewLastName,
    setNewEmail,
    setNewPhone,
    updateProfile,
    setNewPassword,
    setConfirmPassword,
    changePassword,
    isLoading,
  };
}
