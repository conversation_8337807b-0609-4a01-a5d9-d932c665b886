declare module '@appmaker-xyz/shopify' {
  export interface UseProductListItemAttributes {
    gridViewListing?: boolean;
    numColumns?: number;
    appmakerAction?: any;
    isInsideCartPage?: boolean;
    average_rating?: number;
    reviews_count?: number;
    cartActionParams?: {
      fromCart?: boolean;
      [key: string]: any;
    };
    surface?: string;
    referrer?: string;
    isWishlist?: boolean;
    [key: string]: any;
  }

  export interface UseProductListItemProps {
    attributes: UseProductListItemAttributes;
    onAction: (action: any) => Promise<any>;
    blockData: any;
    pageDispatch: (action: any) => void;
    blocksViewItemIndex?: number;
    swipeNavigationEnabled?: boolean;
    showAddedToCartMessage?: boolean;
  }

  export interface UseProductListItemResult {
    surface: string;
    isWishlistPage: boolean;
    isBuyNowOnly: () => boolean;
    isDisablePurchase: () => boolean;
    thumbnail_meta: any;
    wishList: any;
    productType: string;
    firstAvailableVariant: any;
    product: any;
    containerWidth: number;
    onQuantityChange: (quantity?: number, variant?: any) => Promise<void>;
    addingTocartLoading: boolean;
    openProduct: (params?: any) => Promise<any>;
    onOpenProductDetail: (params?: any) => Promise<any>;
    outOfStock: boolean;
    imageAspectRatio: number;
    imageRatio: number;
    isProductListPage: boolean;
    hasTag: (tagName: string) => boolean;
    onSaved: (saved: boolean) => void;
    title: string;
    imageUrl: string;
    featureImage: any;
    salePercentage: string | null;
    salePrice: string;
    regularPrice: string;
    onAddToCart: () => Promise<void>;
    isAddToCartLoading: boolean;
    average_rating: number;
    reviews_count: number;
    gridViewListing: boolean;
    addToCart: (params: { appmakerAction?: boolean }) => Promise<any>;
    count: number;
    updateCart: (params: {
      quantity?: number;
      variant?: any;
      cartLineItemId?: string;
      updateCartPageStateRequired?: boolean;
    }) => Promise<void>;
    adding: boolean;
    setAdding: (adding: boolean) => void;
    show_last_few_remaining: boolean;
    last_few_remaining_text: string;
    brandColor: string;
    numColumns: number;
    onSale: boolean;
    tags: string[];
    salePriceValue: number;
    regularPriceValue: number;
    hasReviews: boolean;
    custom_fields: any;
    productNode: any;
    vendorName: string;
    showVendorName: boolean;
    showVariations: boolean;
    preOrderEnabled: boolean;
    productIndex: number;
  }

  export function useProductListItem(props: UseProductListItemProps): UseProductListItemResult;
}