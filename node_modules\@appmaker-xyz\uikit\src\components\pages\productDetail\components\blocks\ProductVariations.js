import React, { useState, useEffect } from 'react';
import { StyleSheet, ScrollView, FlatList } from 'react-native';
import { useApThemeState } from '../../../../../theme/ThemeContext';
import { Layout } from '../../../../../components';
import { ScrollBlocks } from './ScrollBlocks';
import { SelectBlock } from './ListBlocks';

const ProductVariations = ({
  attributes,
  onChange,
  data,
  selectedItem = '',
  testId,
}) => {
  let {
    variations,
    title,
    variationType = '',
    __appmakerCustomStyles = {},
    in_stock,
    availableOptions,
  } = attributes;
  function inStock(item, variationName) {
    return (
      availableOptions && !availableOptions[variationName]?.includes(item.id)
    );
  }
  const { spacing, color } = useApThemeState();
  const styles = allStyles({ spacing, color });
  useEffect(() => {
    onChange(selectedItem);
  }, [selectedItem]);
  const renderItem = ({ item, index }) => (
    <ScrollBlocks
      testId={`${testId}-value-${index}`}
      clientId={`variation-select-item-${attributes.variationKey}`}
      variationKey={attributes.variationKey}
      onChange={(res) => {
        onChange(res.key);
      }}
      attributes={{
        selectedItem,
        variation: item.variationName,
        key: item.id,
        variationType: variationType,
        // disabled: in_stock === 'true' ? false : true,
        disabled: inStock(item, attributes.variationKey),
        __appmakerCustomStyles: __appmakerCustomStyles,
      }}
    />
  );
  return (
    <>
      {variationType === 'largeText' ? (
        <SelectBlock
          onChange={(res) => {
            onChange(res);
          }}
          attributes={{ selectedItem, variation: variations }}
        />
      ) : (
        <Layout>
          <ScrollView
            horizontal={true}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
            style={styles.container}>
            <FlatList
              horizontal={variations.length <= 14 && true}
              numColumns={
                variations.length > 14 && Math.ceil(variations.length / 2)
              }
              showsVerticalScrollIndicator={false}
              showsHorizontalScrollIndicator={false}
              data={variations}
              renderItem={renderItem}
              keyExtractor={(item) => item.id}
            />
            <Layout style={{ marginRight: spacing.base }} />
          </ScrollView>
          {variationType === 'color' && (
            <Layout style={styles.colorSelectedIndicator}>
              {/* <AppmakerText category="bodyParagraph" status="demiDark">
                Selected: <AppmakerText>Red</AppmakerText>
              </AppmakerText> */}
            </Layout>
          )}
        </Layout>
      )}
    </>
  );
};

export const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      paddingBottom: spacing.small,
    },
    variationItem: {
      position: 'relative',
      backgroundColor: color.light,
      borderRadius: spacing.nano,
      marginLeft: spacing.base,
      borderColor: color.light,
      borderWidth: 1,
      marginVertical: spacing.nano,
    },
    variationText: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.base,
    },
    variationItemSelected: {
      borderColor: color.primary,
      overflow: 'hidden',
    },
    listVariationItem: {
      width: '100%',
      marginLeft: 0,
      marginVertical: spacing.mini,
    },
    selectBlock: {
      marginHorizontal: spacing.base,
      marginBottom: spacing.base,
      backgroundColor: color.light,
      padding: spacing.base,
      borderRadius: spacing.nano,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    colorSelector: {
      width: spacing.lg * 2,
      height: spacing.lg * 2,
      borderRadius: spacing.xl,
      borderColor: color.light,
      borderWidth: spacing.nano / 2,
      marginLeft: spacing.base,
      justifyContent: 'center',
      alignItems: 'center',
    },
    colorSelectedIndicator: {
      paddingBottom: spacing.base,
      paddingHorizontal: spacing.base,
    },
    modal: {
      justifyContent: 'flex-end',
      marginVertical: 0,
      marginHorizontal: 1,
    },
    modalBody: {
      backgroundColor: color.white,
      padding: spacing.md,
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      borderTopStartRadius: spacing.md,
      borderTopEndRadius: spacing.md,
      flexDirection: 'column',
      maxHeight: '80%',
    },
    modalHeader: {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderBottomWidth: 1,
      borderBottomColor: color.light,
      paddingBottom: spacing.base,
    },
    modalContent: {
      paddingVertical: spacing.base,
      flexDirection: 'column',
      width: '100%',
    },
    muted: {
      backgroundColor: color.white,
      borderColor: color.grey,
    },
    muteImage: {
      width: '100%',
      height: '100%',
      position: 'absolute',
      zIndex: 10,
      resizeMode: 'stretch',
    },
    imageContainer: {
      position: 'relative',
      backgroundColor: color.light,
      borderRadius: spacing.nano,
      marginLeft: spacing.base,
      borderColor: color.light,
      borderWidth: 1,
      marginVertical: spacing.nano,
    },
    image: {
      height: 75,
      width: 75,
      resizeMode: 'cover',
    },
  });

export default ProductVariations;
