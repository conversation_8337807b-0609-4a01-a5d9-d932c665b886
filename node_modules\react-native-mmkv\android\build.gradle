import com.android.Version

def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
def agpVersionMajor = agpVersion.tokenize('.')[0].toInteger()
def androidManifestPath = agpVersionMajor >= 7 ? 'src/main/AndroidManifest.xml' : 'src/hasNamespace/AndroidManifest.xml'

buildscript {
  repositories {
    maven {
      url "https://plugins.gradle.org/m2/"
    }
    mavenCentral()
    google()
  }

  dependencies {
    classpath("com.android.tools.build:gradle:8.2.2")
  }
}

def resolveBuildType() {
    Gradle gradle = getGradle()
    String tskReqStr = gradle.getStartParameter().getTaskRequests()['args'].toString()

    return tskReqStr.contains('Release') ? 'release' : 'debug'
}

def isNewArchitectureEnabled() {
  // To opt-in for the New Architecture, you can either:
  // - Set `newArchEnabled` to true inside the `gradle.properties` file
  // - Invoke gradle with `-newArchEnabled=true`
  // - Set an environment variable `ORG_GRADLE_PROJECT_newArchEnabled=true`
  return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

if (isNewArchitectureEnabled()) {
  apply plugin: 'com.facebook.react'
}
apply plugin: 'com.android.library'

def safeExtGet(prop, fallback) {
  rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

def reactNativeArchitectures() {
  def value = project.getProperties().get("reactNativeArchitectures")
  return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

repositories {
  mavenCentral()
}

android {
  compileSdkVersion safeExtGet("compileSdkVersion", 28)
  if (agpVersionMajor >= 7) {
    namespace = "com.reactnativemmkv"
  }

  if (agpVersionMajor >= 8) {
      buildFeatures {
          buildConfig = true
      }
  }

  sourceSets {
    main {
      manifest.srcFile androidManifestPath
    }
  }

  // Used to override the NDK path/version on internal CI or by allowing
  // users to customize the NDK path/version from their root project (e.g. for M1 support)
  if (rootProject.hasProperty("ndkPath")) {
    ndkPath rootProject.ext.ndkPath
  }
  if (rootProject.hasProperty("ndkVersion")) {
    ndkVersion rootProject.ext.ndkVersion
  }

  buildFeatures {
    prefab true
  }

  defaultConfig {
    minSdkVersion safeExtGet('minSdkVersion', 16)
    targetSdkVersion safeExtGet('targetSdkVersion', 28)
    versionCode 1
    versionName "1.0"
    buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString()
    externalNativeBuild {
      cmake {
        cppFlags "-O2 -frtti -fexceptions -Wall -Wno-unused-variable -fstack-protector-all"
        arguments "-DANDROID_STL=c++_shared"
        abiFilters (*reactNativeArchitectures())
      }
    }
  }

  compileOptions {
    sourceCompatibility JavaVersion.VERSION_1_8
    targetCompatibility JavaVersion.VERSION_1_8
  }

  externalNativeBuild {
    cmake {
      path "CMakeLists.txt"
    }
  }
  packagingOptions {
    doNotStrip resolveBuildType() == 'debug' ? "**/**/*.so" : ''
    excludes = [
            "META-INF",
            "META-INF/**",
            "**/libjsi.so",
            "**/libc++_shared.so"
    ]
  }
}

dependencies {
  //noinspection GradleDynamicVersion
  implementation 'com.facebook.react:react-android:+'
}

// Resolves "LOCAL_SRC_FILES points to a missing file, Check that libfb.so exists or that its path is correct".
tasks.whenTaskAdded { task ->
  if (task.name.contains("configureCMakeDebug")) {
    rootProject.getTasksByName("packageReactNdkDebugLibs", true).forEach {
      task.dependsOn(it)
    }
  }
  // We want to add a dependency for both configureCMakeRelease and configureCMakeRelWithDebInfo
  if (task.name.contains("configureCMakeRel")) {
    rootProject.getTasksByName("packageReactNdkReleaseLibs", true).forEach {
      task.dependsOn(it)
    }
  }
}
