import { usePluginStore } from '@appmaker-xyz/core';
import { ActionBar } from '@appmaker-xyz/uikit/src/components/molecules/index';
import React, { useEffect } from 'react';
import base64 from 'base-64';
import { View } from 'react-native';
import { cancelOrder } from '../api';
import { emitEvent } from '@appmaker-xyz/core';

function Reorder({ attributes, onAction }) {
  const returnPrimeStore = usePluginStore(
    (state) => state.plugins['order-ease'],
  );
  const shopifyStore = returnPrimeStore.settings.shopifyStore;
  const order_id = base64
    .decode(attributes.blockItem.node.id)
    .match(/Order\/(\d+)/i)[1];
  return (
    <View>
      <ActionBar
        attributes={{
          title: 'Re order',
        }}
        onPress={async () => {
          onAction({
            action: 'OPEN_IN_WEB_VIEW',
            params: {
              title: 'Re order',
              url: `https://orderease.appsdart.com/reorder/${shopifyStore}/${order_id}`,
            },
          });
        }}
      />
    </View>
  );
}

function CancelOrder({ attributes, onAction }) {
  const [loading, setLoading] = React.useState(false);
  const returnPrimeStore = usePluginStore(
    (state) => state.plugins['order-ease'],
  );
  const shopifyStore = returnPrimeStore.settings.shopifyStore;
  const order_id = base64
    .decode(attributes.blockItem.node.id)
    .match(/Order\/(\d+)/i)[1];
  const [showCancelButton, setShowCancelButton] = React.useState(true);
  useEffect(() => {
    if (
      attributes.blockItem.node.cancelReason !== null ||
      attributes?.blockItem?.node?.fulfillmentStatus === 'FULFILLED'
    ) {
      setShowCancelButton(false);
    } else {
      setShowCancelButton(true);
    }
  }, [attributes]);
  return showCancelButton ? (
    <ActionBar
      attributes={{
        title: 'Cancel Order',
        loading,
      }}
      onPress={async () => {
        emitEvent('cancelOrder', { orderId: attributes.blockItem.node.id });
        setLoading(true);
        await cancelOrder({
          order_number: order_id,
          store: shopifyStore,
        });
        setShowCancelButton(false);
        setLoading(false);
      }}
    />
  ) : null;
}
export { Reorder, CancelOrder };
