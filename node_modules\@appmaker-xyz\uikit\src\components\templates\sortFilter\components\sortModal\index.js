import { StyleSheet, Text } from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import SortModalContent from './SortModalContent';
import { emitEvent } from '@appmaker-xyz/core';

export default function SortModal({
  sortVisible,
  closeSort,
  avilableFilters,
  coreDispatch,
  attributes = {},
}) {
  const { bottomPlacement } = attributes;
  const modalStyles = [styles.centerView];
  if (bottomPlacement) {
    modalStyles.push({ justifyContent: 'flex-end', marginBottom: 0 });
  }
  return (
    <Modal
      testID={'modal'}
      isVisible={sortVisible}
      onSwipeComplete={closeSort}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      onBackButtonPress={closeSort}
      backdropTransitionOutTiming={1}
      onBackdropPress={closeSort}
      style={modalStyles}>
      <SortModalContent
        onSelect={(item) => {
          emitEvent('sortApply', {
            action: 'SORT',
            label: item.label,
            value: item.value,
          });
          coreDispatch({
            type: 'SET_PAGE_VAR',
            name: 'sort',
            value: item,
          });
          closeSort();
        }}
        closeSort={closeSort}
        attributes={attributes}
      />
    </Modal>
  );
}

const styles = StyleSheet.create({
  view: {
    justifyContent: 'flex-end',
    marginVertical: 0,
    marginHorizontal: 12,
  },
  centerView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});
