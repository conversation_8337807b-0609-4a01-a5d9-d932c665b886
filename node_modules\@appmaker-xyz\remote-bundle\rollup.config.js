import { uglify } from 'rollup-plugin-uglify';
import localResolve from 'rollup-plugin-local-resolve';
import babel from '@rollup/plugin-babel';
// import babel from '@rollup/plugin-babel';

const config = {
  input: 'bundle-demo/src/index.js',
  plugins: [
    localResolve(),
    babel({
      presets: ['@babel/preset-react'],
    }),
    //  uglify()
  ],
  output: {
    file: 'bundle-demo/build-output/index.js',
    format: 'cjs',
  },
  minifyInternalExports: true,
};
export default config;