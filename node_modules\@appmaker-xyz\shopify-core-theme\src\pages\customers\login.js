import { appSettings } from '@appmaker-xyz/core';

const LoginPage = {
  type: 'normal',
  title: 'Login',
  attributes: {
    renderType: 'normal',
    renderInScroll: true,
    rootContainerStyle: {
      flex: 1,
      backgroundColor: '#FFFFFF',
    },
    contentContainerStyle: {
      flex: 1,
    },
    scrollViewContentContainerStyle: {
      justifyContent: 'center',
      padding: 12,
      flexGrow: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/remoteImage',
      attributes: {
        name: 'LOGIN_LOGO',
        style: {
          width: 200,
          height: 60,
          alignSelf: 'center',
          resizeMode: 'contain',
          marginBottom: 18,
        },
      },
    },
    {
      name: 'shopify/login-block',
      attributes: {},
    },
  ],
};

export default LoginPage;
