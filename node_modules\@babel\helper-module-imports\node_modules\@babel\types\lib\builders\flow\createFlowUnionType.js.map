{"version": 3, "names": ["_index", "require", "_removeTypeDuplicates", "createFlowUnionType", "types", "flattened", "removeTypeDuplicates", "length", "unionTypeAnnotation"], "sources": ["../../../src/builders/flow/createFlowUnionType.ts"], "sourcesContent": ["import { unionTypeAnnotation } from \"../generated/index.ts\";\nimport removeTypeDuplicates from \"../../modifications/flow/removeTypeDuplicates.ts\";\nimport type * as t from \"../../index.ts\";\n\n/**\n * Takes an array of `types` and flattens them, removing duplicates and\n * returns a `UnionTypeAnnotation` node containing them.\n */\nexport default function createFlowUnionType<T extends t.FlowType>(\n  types: [T] | Array<T>,\n): T | t.UnionTypeAnnotation {\n  const flattened = removeTypeDuplicates(types);\n\n  if (flattened.length === 1) {\n    return flattened[0] as T;\n  } else {\n    return unionTypeAnnotation(flattened);\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AAOe,SAASE,mBAAmBA,CACzCC,KAAqB,EACM;EAC3B,MAAMC,SAAS,GAAG,IAAAC,6BAAoB,EAACF,KAAK,CAAC;EAE7C,IAAIC,SAAS,CAACE,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAOF,SAAS,CAAC,CAAC,CAAC;EACrB,CAAC,MAAM;IACL,OAAO,IAAAG,0BAAmB,EAACH,SAAS,CAAC;EACvC;AACF"}