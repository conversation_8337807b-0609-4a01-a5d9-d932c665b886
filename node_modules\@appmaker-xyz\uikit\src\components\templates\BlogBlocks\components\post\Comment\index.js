import PropTypes from "prop-types";
import React, { Component } from "react";
import { View, FlatList } from "react-native";
import CommentItem from "./CommentItem";

/**
 * ## Comment
 * A container component used
 * to list all the comments of the posts
 */
export default class Comment extends Component {
  constructor() {
    super();
    this.state = {
      isWebViewLoaded: false
    };
  }
  commentItem = (item, index) => {
    return (
      <View key={index}>
        <CommentItem data={item} />
      </View>
    );
  };
  render() {
    return (
      <View>
        <FlatList
          data={this.props.comments}
          renderItem={({ item, index }) => this.commentItem(item, index)}
          keyExtractor={(item, index) => index.toString()}
        />
      </View>
    );
  }
}

Comment.propTypes = {
  comments: PropTypes.object
};
