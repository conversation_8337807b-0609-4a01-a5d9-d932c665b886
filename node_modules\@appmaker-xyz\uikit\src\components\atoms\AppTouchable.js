/**
 * @providesModule AppTouchable
 * @flow
 */

import React from 'react';
import { TouchableOpacity, Platform } from 'react-native';
import { testProps } from '@appmaker-xyz/core';
function AppTouchable(props) {
  return (
    <TouchableOpacity
      accessibilityTraits="button"
      underlayColor="white"
      activeOpacity={
        props.disableTouch ? 1 : Platform.OS === 'android' ? 0.9 : 0.85
      }
      {...props}
      {...testProps(props.testId || props.clientId)}
    />
  );
}

// module.exports = ;
export default AppTouchable;
