import { appmaker } from '@appmaker-xyz/core';
// import './actions';
// import './pages';
import activateHelper, {
  addExtraMetaFields,
  addReviewsAttributes,
  addProductReviewButtonPDP,
} from './helper';
// import ReviewList from './pages/ReviewList';
export function activate({ settings }) {
  activateHelper();
  addExtraMetaFields();
  addReviewsAttributes();
  addProductReviewButtonPDP(settings);
}
const Judgeme = {
  id: 'judgeme-reviews',
  name: 'Judgeme Reviews',
  activate,
};
export default Judgeme;
