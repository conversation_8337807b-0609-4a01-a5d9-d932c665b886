import * as React from 'react';
import {View, StyleSheet, Dimensions, Text} from 'react-native';
import {TabView, TabBar} from 'react-native-tab-view';

import GroceryListing from './GroceryListing.js';
import {color, spacing, fonts} from '../styles/index.js';

const SecondRoute = ({category}) => (
  <View style={styles.scene}>
    {/* <Text>{JSON.stringify(category)}</Text> */}
    <GroceryListing params={{category}} />
  </View>
);
const TabItemView = React.memo(SecondRoute);
const categories = [
  {
    id: 101,
    label: 'Bakery Items',
  },
  {
    id: 122,
    label: 'Batter',
  },
  {
    id: 125,
    label: 'Beverages',
  },
  {
    id: 102,
    label: 'Biscuits/Cookies',
  },
  {
    id: 99,
    label: 'Blended Spices',
  },
  // {
  //   id: 5358,
  //   label: 'Cafe',
  // },
  // {
  //   id: 2910,
  //   label: 'Canned Food',
  // },
  // {
  //   id: 89,
  //   label: 'Chips',
  // },
  // {
  //   id: 415,
  //   label: 'Chutney',
  // },
  // {
  //   id: 95,
  //   label: 'Daily needs',
  // },
  // {
  //   id: 75,
  //   label: 'Dairy Products',
  // },
  // {
  //   id: 5115,
  //   label: 'Diwali Hampers',
  // },
  // {
  //   id: 5058,
  //   label: 'Diwali Items',
  // },
  // {
  //   id: 108,
  //   label: 'Dry Fruits',
  // },
  // {
  //   id: 421,
  //   label: 'Egg and Bread',
  // },
  // {
  //   id: 123,
  //   label: 'Essence and Colours',
  // },
  // {
  //   id: 4379,
  //   label: 'Events',
  // },
  // {
  //   id: 5429,
  //   label: 'Fasting Items',
  // },
  // {
  //   id: 70,
  //   label: 'Flour',
  // },
  // {
  //   id: 991,
  //   label: 'Frozen Items',
  // },
  // {
  //   id: 76,
  //   label: 'Frozen Paratha',
  // },
  // {
  //   id: 100,
  //   label: 'Frozen Snacks',
  // },
  // {
  //   id: 3016,
  //   label: 'Frozen Sweets',
  // },
  // {
  //   id: 992,
  //   label: 'Frozen Vegetables',
  // },
  // {
  //   id: 2890,
  //   label: 'General',
  // },
  // {
  //   id: 97,
  //   label: 'Ground Spices',
  // },
  // {
  //   id: 127,
  //   label: 'Home Care',
  // },
  // {
  //   id: 72,
  //   label: 'Instant Mix',
  // },
  // {
  //   id: 111,
  //   label: 'Jaggery',
  // },
  // {
  //   id: 417,
  //   label: 'Jam',
  // },
  // {
  //   id: 124,
  //   label: 'Jelly/Candy/Mint',
  // },
  // {
  //   id: 90,
  //   label: 'Khakhra',
  // },
  // {
  //   id: 104,
  //   label: 'Khari',
  // },
  // {
  //   id: 69,
  //   label: 'Lentils(Dal, Pulse)',
  // },
  // {
  //   id: 119,
  //   label: 'Mamara and Poha',
  // },
  // {
  //   id: 420,
  //   label: 'Milk and Yoghurt',
  // },
  // {
  //   id: 4118,
  //   label: 'Miscellaneous',
  // },
  // {
  //   id: 88,
  //   label: 'Mukhvas',
  // },
  // {
  //   id: 91,
  //   label: 'Namkeen',
  // },
  // {
  //   id: 120,
  //   label: 'Noodles',
  // },
  // {
  //   id: 71,
  //   label: 'Oil and Ghee',
  // },
  // {
  //   id: 542,
  //   label: 'Other Flour',
  // },
  // {
  //   id: 84,
  //   label: 'Others',
  // },
  // {
  //   id: 92,
  //   label: 'Papad',
  // },
  // {
  //   id: 79,
  //   label: 'Party Items',
  // },
  // {
  //   id: 416,
  //   label: 'Paste',
  // },
  // {
  //   id: 126,
  //   label: 'Personal Care',
  // },
  // {
  //   id: 414,
  //   label: 'Pickles',
  // },
  // {
  //   id: 413,
  //   label: 'Pickles/Chutney/Paste',
  // },
  // {
  //   id: 80,
  //   label: 'Puja Items',
  // },
  // {
  //   id: 118,
  //   label: 'Quick Meals',
  // },
  // {
  //   id: 4401,
  //   label: 'Rakhi',
  // },
  // {
  //   id: 105,
  //   label: 'Ready to Eat',
  // },
  // {
  //   id: 106,
  //   label: 'Ready to Fry',
  // },
  // {
  //   id: 68,
  //   label: 'Rice',
  // },
  // {
  //   id: 731,
  //   label: 'Rusk and Cake',
  // },
  // {
  //   id: 112,
  //   label: 'Salt and Sugar',
  // },
  // {
  //   id: 419,
  //   label: 'Sauce',
  // },
  // {
  //   id: 83,
  //   label: 'Snacks',
  // },
  // {
  //   id: 418,
  //   label: 'Soup',
  // },
  // {
  //   id: 96,
  //   label: 'Spices',
  // },
  // {
  //   id: 82,
  //   label: 'Sweets',
  // },
  // {
  //   id: 114,
  //   label: 'Tea and Coffee',
  // },
  // {
  //   id: 412,
  //   label: 'Uncategorized',
  // },
  // {
  //   id: 128,
  //   label: 'Utensils',
  // },
  // {
  //   id: 2896,
  //   label: 'Vegetables and Fruits',
  // },
  // {
  //   id: 121,
  //   label: 'Vermicelli',
  // },
  // {
  //   id: 98,
  //   label: 'Whole Spices',
  // },
];
export default class DynamicWidthTabBarExample extends React.Component {
  static title = 'Scrollable tab bar (auto width)';
  static backgroundColor = '#3f51b5';
  static appbarElevation = 0;

  state = {
    index: 0,
    routes: categories.map((item) => ({
      key: item.id + '-tab',
      title: item.label,
      id: item.id,
    })),
    // [
    //   {key: 'daily', title: 'Daily Fresh'},
    //   {key: 'contacts', title: 'Veggies'},
    //   {key: 'albums', title: 'fruits'},
    //   {key: 'chat', title: 'Fish'},
    //   {key: 'long', title: 'Meat'},
    //   {key: 'medium', title: 'Frozen food'},
    // ],
  };

  handleIndexChange = (index) =>
    this.setState({
      index,
    });

  renderTabBar = (props) => {
    return (
      <TabBar
        {...props}
        scrollEnabled
        indicatorStyle={styles.indicator}
        style={styles.tabbar}
        labelStyle={styles.label}
        tabStyle={styles.tabStyle}
      />
    );
  };

  renderScene = ({route}) => {
    // if (Math.abs(index - this.state.routes.indexOf(route)) > 2) {
    //   return <View />;
    // }

    return <TabItemView name={route.title} category={route.id} />;
  };

  render() {
    return (
      <TabView
        lazy
        navigationState={this.state}
        renderScene={this.renderScene}
        renderTabBar={this.renderTabBar}
        onIndexChange={this.handleIndexChange}
      />
    );
  }
}

const styles = StyleSheet.create({
  scene: {
    flex: 1,
  },
  tabbar: {
    backgroundColor: color.white,
  },
  indicator: {
    backgroundColor: color.primary,
    borderTopEndRadius: spacing.small,
    borderTopStartRadius: spacing.small,
  },
  label: {
    ...fonts.actionTitle,
    color: color.primary,
    textTransform: 'capitalize',
  },
  tabStyle: {
    width: 'auto',
  },
});
