{"name": "@appmaker-xyz/plugins", "version": "0.2.87", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "echo \"Error: no build specified\"", "build-old": "rollup --config rollup.config.js", "watch": "rollup --config rollup.config.js --watch"}, "keywords": [], "author": "", "license": "UNLICENSED", "dependencies": {}, "devDependencies": {"@rollup/plugin-babel": "^5.3.1", "rollup": "^2.79.0", "rollup-plugin-uglify": "^6.0.4", "rollup-plugin-local-resolve": "^1.0.7", "@rollup/plugin-typescript": "^11.1.2", "rollup-plugin-peer-deps-external": "^2.2.4"}, "peerDependencies": {"react": "16.13.1", "react-dom": "16.13.1", "react-native": "0.63.5"}, "repository": "https://github.com/Appmaker-xyz/starter-app", "publishConfig": {"registry": "https://flash.appmaker.xyz", "directory": "build-output"}}