import { useAppStorage } from '@appmaker-xyz/core';
import { useShopifyProduct } from '@appmaker-xyz/shopify';
import { isEmpty } from 'lodash';
import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout, AppmakerText, AppImage } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';

function FreeGiftProduct({ product, styles }) {
  const { title, imageUrl, regularPriceWithCurrency } = useShopifyProduct({
    product,
  });
  // const imagesUrl = product?.images?.edges[0]?.node?.url;
  return isEmpty(title) ? null : (
    <Layout style={styles.cardBody}>
      <AppImage uri={imageUrl} style={styles.image} />
      <Layout style={styles.cardBodyText}>
        <AppmakerText category="smallButtonText">{title}</AppmakerText>
        <Layout style={styles.cardPrice}>
          <AppmakerText
            category="bodyParagraphBold"
            fontColor="#3AB75B"
            style={styles.freeText}>
            FREE
          </AppmakerText>
          <AppmakerText
            category="smallButtonText"
            fontColor="#595959"
            style={styles.strike}>
            {regularPriceWithCurrency}
          </AppmakerText>
        </Layout>
      </Layout>
    </Layout>
  );
}

const CartFreeGift = ({ attributes = {}, onPress, onAction }) => {
  const { appmakerAction, message } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  const nextFreeGiftProduct = useAppStorage(
    (state) => state?.next_free_gift_product?.productData,
  );
  return isEmpty(message) ? null : (
    <Layout style={styles.container}>
      <Layout style={styles.cardContainer}>
        <Layout style={styles.cardHeader}>
          <AppmakerText status="white" category="bodySubText">
            {message}
          </AppmakerText>
        </Layout>
        <FreeGiftProduct styles={styles} product={nextFreeGiftProduct} />
      </Layout>
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      //   backgroundColor: color.white,
      padding: spacing.base,
    },
    cardContainer: {
      backgroundColor: color.white,
      borderRadius: spacing.base,
      overflow: 'hidden',
    },
    cardHeader: {
      backgroundColor: '#3AB75B',
      paddingHorizontal: spacing.small,
      paddingVertical: spacing.nano,
    },
    cardBody: {
      flexDirection: 'row',
      padding: spacing.small,
    },
    cardBodyText: {
      flex: 1,
      justifyContent: 'space-between',
    },
    cardPrice: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    image: {
      width: 65,
      height: 65,
      marginRight: spacing.small,
      borderRadius: spacing.small,
      overflow: 'hidden',
      borderColor: '#E9EDF1',
      borderWidth: 1,
    },
    freeText: {
      marginRight: spacing.mini,
    },
    strike: {
      textDecorationLine: 'line-through',
    },
  });

export default CartFreeGift;
