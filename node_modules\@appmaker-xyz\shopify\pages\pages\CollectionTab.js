const collectionTab = {
  attributes: {
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      // paddingHorizontal: 16,
    },
    // headerShown: false,
  },
  blocks: [
    {
      name: 'appmaker/tab-block',
      innerBlocks: [
        {
          name: 'appmaker/in-app-page',
          attributes: {
            label: 'BSC',
            pageId: 'home',
          },
        },
        {
          name: 'appmaker/in-app-page',
          attributes: {
            label: 'City of _',
            pageId: 'M0aBM4s6R1',
          },
        },
        {
          name: 'appmaker/in-app-page',
          attributes: {
            label: 'Pause',
            pageId: 'j3EvFVVeTb',
          },
        },
        // {
        //   name: 'appmaker/in-app-page',
        //   attributes: {
        //     label: 'sarees',
        //     pageId: 'productList',
        //     actionParams: {
        //       params: {
        //         collectionHandle: 'sarees',
        //       },
        //     },
        //   },
        // },
        // {
        //   name: 'appmaker/in-app-page',
        //   attributes: {
        //     label: 'western',
        //     pageId: 'productList',
        //     actionParams: {
        //       params: {
        //         collectionHandle: 'western',
        //       },
        //     },
        //   },
        // },
        // {
        //   name: 'appmaker/in-app-page',
        //   attributes: {
        //     label: 'lehengas',
        //     pageId: 'productList',
        //     actionParams: {
        //       params: {
        //         collectionHandle: 'lehengas',
        //       },
        //     },

        //   },
        // },
      ],
    },
  ],
};
export default collectionTab;
