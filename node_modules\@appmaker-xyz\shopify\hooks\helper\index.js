import { shopConfig } from '../../store/shopifyStore';

export function differenceAmountCalculator(
  regular_price_value,
  price_value,
  currency,
) {
  const amount = Math.abs((regular_price_value - price_value).toFixed(2));
  const shopData = shopConfig.get();
  try {
    const { moneyFormat } = shopData?.shop;
    return `${moneyFormat}`.replace(
      /{\s?{\s?(amount|amount_no_decimals)\s?}\s?}/g,
      amount,
    );
  } catch (error) {
    return `${currency} ${amount}`;
  }
}
