import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { ThemeText, AppTouchable, Layout } from '@appmaker-xyz/ui';
import Icon from 'react-native-vector-icons/Feather';

const FloatingButton = ({ attributes, onPress, onAction }) => {
  const {
    label,
    iconName,
    visibilityStatus = false,
    type,
    appmakerAction,
  } = attributes;
  const [visible, setVisible] = useState(visibilityStatus);
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  if (!visible) {
    return null;
  }
  if (type === 'iconButton') {
    return (
      <AppTouchable style={styles.iconButtonContainer} onPress={onPressHandle}>
        <Icon name={iconName} size={24} color="#1B1B1B" />
      </AppTouchable>
    );
  }
  return (
    <Layout style={styles.container}>
      <AppTouchable style={styles.textContainer} onPress={onPress}>
        <Icon name={iconName} color="#fff" size={15} style={{ marginEnd: 6 }} />
        <ThemeText size="sm" color="#fff">
          {label}
        </ThemeText>
      </AppTouchable>
      <AppTouchable
        style={styles.closeButton}
        onPress={() => {
          setVisible(false);
        }}>
        <Icon name="x" color="#fff" />
      </AppTouchable>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    alignSelf: 'center',
    bottom: 72,
    flexDirection: 'row',
    zIndex: 10,
  },
  textContainer: {
    paddingVertical: 8,
    paddingStart: 12,
    paddingEnd: 6,
    flexDirection: 'row',
    backgroundColor: '#212121',
    borderTopLeftRadius: 24,
    borderBottomLeftRadius: 24,
    justifyContent: 'center',
  },
  closeButton: {
    paddingVertical: 8,
    paddingStart: 6,
    paddingEnd: 12,
    backgroundColor: '#212121E6',
    borderTopRightRadius: 24,
    borderBottomRightRadius: 24,
    justifyContent: 'center',
  },
  iconButtonContainer: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    flexDirection: 'row',
    zIndex: 100,
    backgroundColor: '#fff',
    borderRadius: 32,
    overflow: 'hidden',
    elevation: 5,
    padding: 16,
  },
});

export default FloatingButton;
