import React, { useEffect } from 'react';
import SearchBar from './SearchBar';
import { usePredictiveSearch } from '@appmaker-xyz/shopify';
import { useSearchQuery } from '@appmaker-xyz/shopify';
const SearchBlock = (props) => {
  const { searchQuery, setSearchQuery } = useSearchQuery();
  const { isLoading } = usePredictiveSearch(searchQuery);
  return (
    <>
      <SearchBar
        value={searchQuery}
        setValue={setSearchQuery}
        isLoading={isLoading}
        {...props}
      />
    </>
  );
};

export default SearchBlock;
