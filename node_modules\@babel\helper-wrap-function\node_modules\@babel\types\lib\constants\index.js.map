{"version": 3, "names": ["STATEMENT_OR_BLOCK_KEYS", "exports", "FLATTENABLE_KEYS", "FOR_INIT_KEYS", "COMMENT_KEYS", "LOGICAL_OPERATORS", "UPDATE_OPERATORS", "BOOLEAN_NUMBER_BINARY_OPERATORS", "EQUALITY_BINARY_OPERATORS", "COMPARISON_BINARY_OPERATORS", "BOOLEAN_BINARY_OPERATORS", "NUMBER_BINARY_OPERATORS", "BINARY_OPERATORS", "ASSIGNMENT_OPERATORS", "map", "op", "BOOLEAN_UNARY_OPERATORS", "NUMBER_UNARY_OPERATORS", "STRING_UNARY_OPERATORS", "UNARY_OPERATORS", "INHERIT_KEYS", "optional", "force", "BLOCK_SCOPED_SYMBOL", "Symbol", "for", "NOT_LOCAL_BINDING"], "sources": ["../../src/constants/index.ts"], "sourcesContent": ["export const STATEMENT_OR_BLOCK_KEYS = [\"consequent\", \"body\", \"alternate\"];\nexport const FLATTENABLE_KEYS = [\"body\", \"expressions\"];\nexport const FOR_INIT_KEYS = [\"left\", \"init\"];\nexport const COMMENT_KEYS = [\n  \"leadingComments\",\n  \"trailingComments\",\n  \"innerComments\",\n] as const;\n\nexport const LOGICAL_OPERATORS = [\"||\", \"&&\", \"??\"];\nexport const UPDATE_OPERATORS = [\"++\", \"--\"];\n\nexport const BOOLEAN_NUMBER_BINARY_OPERATORS = [\">\", \"<\", \">=\", \"<=\"];\nexport const EQUALITY_BINARY_OPERATORS = [\"==\", \"===\", \"!=\", \"!==\"];\nexport const COMPARISON_BINARY_OPERATORS = [\n  ...EQUALITY_BINARY_OPERATORS,\n  \"in\",\n  \"instanceof\",\n];\nexport const BOOLEAN_BINARY_OPERATORS = [\n  ...COMPARISON_BINARY_OPERATORS,\n  ...BOOLEAN_NUMBER_BINARY_OPERATORS,\n];\nexport const NUMBER_BINARY_OPERATORS = [\n  \"-\",\n  \"/\",\n  \"%\",\n  \"*\",\n  \"**\",\n  \"&\",\n  \"|\",\n  \">>\",\n  \">>>\",\n  \"<<\",\n  \"^\",\n];\nexport const BINARY_OPERATORS = [\n  \"+\",\n  ...NUMBER_BINARY_OPERATORS,\n  ...BOOLEAN_BINARY_OPERATORS,\n  \"|>\",\n];\n\nexport const ASSIGNMENT_OPERATORS = [\n  \"=\",\n  \"+=\",\n  ...NUMBER_BINARY_OPERATORS.map(op => op + \"=\"),\n  ...LOGICAL_OPERATORS.map(op => op + \"=\"),\n];\n\nexport const BOOLEAN_UNARY_OPERATORS = [\"delete\", \"!\"];\nexport const NUMBER_UNARY_OPERATORS = [\"+\", \"-\", \"~\"];\nexport const STRING_UNARY_OPERATORS = [\"typeof\"];\nexport const UNARY_OPERATORS = [\n  \"void\",\n  \"throw\",\n  ...BOOLEAN_UNARY_OPERATORS,\n  ...NUMBER_UNARY_OPERATORS,\n  ...STRING_UNARY_OPERATORS,\n];\n\nexport const INHERIT_KEYS = {\n  optional: [\"typeAnnotation\", \"typeParameters\", \"returnType\"],\n  force: [\"start\", \"loc\", \"end\"],\n} as const;\n\nexport const BLOCK_SCOPED_SYMBOL = Symbol.for(\"var used to be block scoped\");\nexport const NOT_LOCAL_BINDING = Symbol.for(\n  \"should not be considered a local binding\",\n);\n"], "mappings": ";;;;;;AAAO,MAAMA,uBAAuB,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,WAAW,CAAC;AAACC,OAAA,CAAAD,uBAAA,GAAAA,uBAAA;AACpE,MAAME,gBAAgB,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC;AAACD,OAAA,CAAAC,gBAAA,GAAAA,gBAAA;AACjD,MAAMC,aAAa,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;AAACF,OAAA,CAAAE,aAAA,GAAAA,aAAA;AACvC,MAAMC,YAAY,GAAG,CAC1B,iBAAiB,EACjB,kBAAkB,EAClB,eAAe,CACP;AAACH,OAAA,CAAAG,YAAA,GAAAA,YAAA;AAEJ,MAAMC,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AAACJ,OAAA,CAAAI,iBAAA,GAAAA,iBAAA;AAC7C,MAAMC,gBAAgB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;AAACL,OAAA,CAAAK,gBAAA,GAAAA,gBAAA;AAEtC,MAAMC,+BAA+B,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;AAACN,OAAA,CAAAM,+BAAA,GAAAA,+BAAA;AAC/D,MAAMC,yBAAyB,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;AAACP,OAAA,CAAAO,yBAAA,GAAAA,yBAAA;AAC7D,MAAMC,2BAA2B,GAAG,CACzC,GAAGD,yBAAyB,EAC5B,IAAI,EACJ,YAAY,CACb;AAACP,OAAA,CAAAQ,2BAAA,GAAAA,2BAAA;AACK,MAAMC,wBAAwB,GAAG,CACtC,GAAGD,2BAA2B,EAC9B,GAAGF,+BAA+B,CACnC;AAACN,OAAA,CAAAS,wBAAA,GAAAA,wBAAA;AACK,MAAMC,uBAAuB,GAAG,CACrC,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,IAAI,EACJ,GAAG,EACH,GAAG,EACH,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,GAAG,CACJ;AAACV,OAAA,CAAAU,uBAAA,GAAAA,uBAAA;AACK,MAAMC,gBAAgB,GAAG,CAC9B,GAAG,EACH,GAAGD,uBAAuB,EAC1B,GAAGD,wBAAwB,EAC3B,IAAI,CACL;AAACT,OAAA,CAAAW,gBAAA,GAAAA,gBAAA;AAEK,MAAMC,oBAAoB,GAAG,CAClC,GAAG,EACH,IAAI,EACJ,GAAGF,uBAAuB,CAACG,GAAG,CAACC,EAAE,IAAIA,EAAE,GAAG,GAAG,CAAC,EAC9C,GAAGV,iBAAiB,CAACS,GAAG,CAACC,EAAE,IAAIA,EAAE,GAAG,GAAG,CAAC,CACzC;AAACd,OAAA,CAAAY,oBAAA,GAAAA,oBAAA;AAEK,MAAMG,uBAAuB,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;AAACf,OAAA,CAAAe,uBAAA,GAAAA,uBAAA;AAChD,MAAMC,sBAAsB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAAChB,OAAA,CAAAgB,sBAAA,GAAAA,sBAAA;AAC/C,MAAMC,sBAAsB,GAAG,CAAC,QAAQ,CAAC;AAACjB,OAAA,CAAAiB,sBAAA,GAAAA,sBAAA;AAC1C,MAAMC,eAAe,GAAG,CAC7B,MAAM,EACN,OAAO,EACP,GAAGH,uBAAuB,EAC1B,GAAGC,sBAAsB,EACzB,GAAGC,sBAAsB,CAC1B;AAACjB,OAAA,CAAAkB,eAAA,GAAAA,eAAA;AAEK,MAAMC,YAAY,GAAG;EAC1BC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,YAAY,CAAC;EAC5DC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK;AAC/B,CAAU;AAACrB,OAAA,CAAAmB,YAAA,GAAAA,YAAA;AAEJ,MAAMG,mBAAmB,GAAGC,MAAM,CAACC,GAAG,CAAC,6BAA6B,CAAC;AAACxB,OAAA,CAAAsB,mBAAA,GAAAA,mBAAA;AACtE,MAAMG,iBAAiB,GAAGF,MAAM,CAACC,GAAG,CACzC,0CACF,CAAC;AAACxB,OAAA,CAAAyB,iBAAA,GAAAA,iBAAA"}