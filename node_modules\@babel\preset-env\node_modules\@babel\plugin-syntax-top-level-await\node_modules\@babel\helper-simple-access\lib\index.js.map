{"version": 3, "names": ["LOGICAL_OPERATORS", "assignmentExpression", "binaryExpression", "cloneNode", "identifier", "logicalExpression", "numericLiteral", "sequenceExpression", "unaryExpression", "simpleAssignmentVisitor", "UpdateExpression", "exit", "path", "scope", "bindingNames", "includeUpdateExpression", "arg", "get", "isIdentifier", "localName", "node", "name", "has", "getBinding", "parentPath", "isExpressionStatement", "isCompletionRecord", "operator", "replaceWith", "prefix", "old", "generateUidIdentifierBasedOnNode", "varName", "push", "id", "binary", "AssignmentExpression", "seen", "add", "left", "slice", "includes", "right", "simplifyAccess", "traverse", "WeakSet"], "sources": ["../src/index.ts"], "sourcesContent": ["import {\n  LOGICAL_OPERATORS,\n  assignmentExpression,\n  binaryExpression,\n  cloneNode,\n  identifier,\n  logicalExpression,\n  numericLiteral,\n  sequenceExpression,\n  unaryExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type { Node<PERSON><PERSON>, Scope, Visitor } from \"@babel/traverse\";\n\ntype State = {\n  scope: Scope;\n  bindingNames: Set<string>;\n  seen: WeakSet<t.Node>;\n  includeUpdateExpression: boolean;\n};\n\nconst simpleAssignmentVisitor: Visitor<State> = {\n  // TODO(Babel 8): Remove UpdateExpression\n  UpdateExpression: {\n    exit(path) {\n      const { scope, bindingNames, includeUpdateExpression } = this;\n      if (!includeUpdateExpression) {\n        return;\n      }\n\n      const arg = path.get(\"argument\");\n      if (!arg.isIdentifier()) return;\n      const localName = arg.node.name;\n\n      if (!bindingNames.has(localName)) return;\n\n      // redeclared in this scope\n      if (scope.getBinding(localName) !== path.scope.getBinding(localName)) {\n        return;\n      }\n\n      if (\n        path.parentPath.isExpressionStatement() &&\n        !path.isCompletionRecord()\n      ) {\n        // ++i => (i += 1);\n        const operator = path.node.operator == \"++\" ? \"+=\" : \"-=\";\n        path.replaceWith(\n          assignmentExpression(operator, arg.node, numericLiteral(1)),\n        );\n      } else if (path.node.prefix) {\n        // ++i => (i = (+i) + 1);\n        path.replaceWith(\n          assignmentExpression(\n            \"=\",\n            identifier(localName),\n            binaryExpression(\n              path.node.operator[0] as \"+\" | \"-\",\n              unaryExpression(\"+\", arg.node),\n              numericLiteral(1),\n            ),\n          ),\n        );\n      } else {\n        const old = path.scope.generateUidIdentifierBasedOnNode(\n          arg.node,\n          \"old\",\n        );\n        const varName = old.name;\n        path.scope.push({ id: old });\n\n        const binary = binaryExpression(\n          path.node.operator[0] as \"+\" | \"-\",\n          identifier(varName),\n          // todo: support bigint\n          numericLiteral(1),\n        );\n\n        // i++ => (_old = (+i), i = _old + 1, _old)\n        path.replaceWith(\n          sequenceExpression([\n            assignmentExpression(\n              \"=\",\n              identifier(varName),\n              unaryExpression(\"+\", arg.node),\n            ),\n            assignmentExpression(\"=\", cloneNode(arg.node), binary),\n            identifier(varName),\n          ]),\n        );\n      }\n    },\n  },\n\n  AssignmentExpression: {\n    exit(path) {\n      const { scope, seen, bindingNames } = this;\n\n      if (path.node.operator === \"=\") return;\n\n      if (seen.has(path.node)) return;\n      seen.add(path.node);\n\n      const left = path.get(\"left\");\n      if (!left.isIdentifier()) return;\n\n      // Simple update-assign foo += 1;\n      // =>   exports.foo =  (foo += 1);\n      const localName = left.node.name;\n\n      if (!bindingNames.has(localName)) return;\n\n      // redeclared in this scope\n      if (scope.getBinding(localName) !== path.scope.getBinding(localName)) {\n        return;\n      }\n\n      const operator = path.node.operator.slice(0, -1);\n      if (LOGICAL_OPERATORS.includes(operator)) {\n        // &&, ||, ??\n        // (foo &&= bar) => (foo && foo = bar)\n        path.replaceWith(\n          logicalExpression(\n            // @ts-expect-error Guarded by LOGICAL_OPERATORS.includes\n            operator,\n            path.node.left,\n            assignmentExpression(\n              \"=\",\n              cloneNode(path.node.left),\n              path.node.right,\n            ),\n          ),\n        );\n      } else {\n        // (foo += bar) => (foo = foo + bar)\n        path.node.right = binaryExpression(\n          // @ts-expect-error An assignment expression operator removing \"=\" must\n          // be a valid binary operator\n          operator,\n          cloneNode(path.node.left),\n          path.node.right,\n        );\n        path.node.operator = \"=\";\n      }\n    },\n  },\n};\n\nexport default function simplifyAccess(\n  path: NodePath,\n  bindingNames: Set<string>,\n  // TODO(Babel 8): Remove this\n  includeUpdateExpression: boolean = true,\n) {\n  path.traverse(simpleAssignmentVisitor, {\n    scope: path.scope,\n    bindingNames,\n    seen: new WeakSet(),\n    includeUpdateExpression,\n  });\n}\n"], "mappings": ";;;;;;AAAA;AAUsB;EATpBA,iBAAiB;EACjBC,oBAAoB;EACpBC,gBAAgB;EAChBC,SAAS;EACTC,UAAU;EACVC,iBAAiB;EACjBC,cAAc;EACdC,kBAAkB;EAClBC;AAAe;AAYjB,MAAMC,uBAAuC,GAAG;EAE9CC,gBAAgB,EAAE;IAChBC,IAAI,CAACC,IAAI,EAAE;MACT,MAAM;QAAEC,KAAK;QAAEC,YAAY;QAAEC;MAAwB,CAAC,GAAG,IAAI;MAC7D,IAAI,CAACA,uBAAuB,EAAE;QAC5B;MACF;MAEA,MAAMC,GAAG,GAAGJ,IAAI,CAACK,GAAG,CAAC,UAAU,CAAC;MAChC,IAAI,CAACD,GAAG,CAACE,YAAY,EAAE,EAAE;MACzB,MAAMC,SAAS,GAAGH,GAAG,CAACI,IAAI,CAACC,IAAI;MAE/B,IAAI,CAACP,YAAY,CAACQ,GAAG,CAACH,SAAS,CAAC,EAAE;;MAGlC,IAAIN,KAAK,CAACU,UAAU,CAACJ,SAAS,CAAC,KAAKP,IAAI,CAACC,KAAK,CAACU,UAAU,CAACJ,SAAS,CAAC,EAAE;QACpE;MACF;MAEA,IACEP,IAAI,CAACY,UAAU,CAACC,qBAAqB,EAAE,IACvC,CAACb,IAAI,CAACc,kBAAkB,EAAE,EAC1B;QAEA,MAAMC,QAAQ,GAAGf,IAAI,CAACQ,IAAI,CAACO,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI;QACzDf,IAAI,CAACgB,WAAW,CACd3B,oBAAoB,CAAC0B,QAAQ,EAAEX,GAAG,CAACI,IAAI,EAAEd,cAAc,CAAC,CAAC,CAAC,CAAC,CAC5D;MACH,CAAC,MAAM,IAAIM,IAAI,CAACQ,IAAI,CAACS,MAAM,EAAE;QAE3BjB,IAAI,CAACgB,WAAW,CACd3B,oBAAoB,CAClB,GAAG,EACHG,UAAU,CAACe,SAAS,CAAC,EACrBjB,gBAAgB,CACdU,IAAI,CAACQ,IAAI,CAACO,QAAQ,CAAC,CAAC,CAAC,EACrBnB,eAAe,CAAC,GAAG,EAAEQ,GAAG,CAACI,IAAI,CAAC,EAC9Bd,cAAc,CAAC,CAAC,CAAC,CAClB,CACF,CACF;MACH,CAAC,MAAM;QACL,MAAMwB,GAAG,GAAGlB,IAAI,CAACC,KAAK,CAACkB,gCAAgC,CACrDf,GAAG,CAACI,IAAI,EACR,KAAK,CACN;QACD,MAAMY,OAAO,GAAGF,GAAG,CAACT,IAAI;QACxBT,IAAI,CAACC,KAAK,CAACoB,IAAI,CAAC;UAAEC,EAAE,EAAEJ;QAAI,CAAC,CAAC;QAE5B,MAAMK,MAAM,GAAGjC,gBAAgB,CAC7BU,IAAI,CAACQ,IAAI,CAACO,QAAQ,CAAC,CAAC,CAAC,EACrBvB,UAAU,CAAC4B,OAAO,CAAC;QAEnB1B,cAAc,CAAC,CAAC,CAAC,CAClB;;QAGDM,IAAI,CAACgB,WAAW,CACdrB,kBAAkB,CAAC,CACjBN,oBAAoB,CAClB,GAAG,EACHG,UAAU,CAAC4B,OAAO,CAAC,EACnBxB,eAAe,CAAC,GAAG,EAAEQ,GAAG,CAACI,IAAI,CAAC,CAC/B,EACDnB,oBAAoB,CAAC,GAAG,EAAEE,SAAS,CAACa,GAAG,CAACI,IAAI,CAAC,EAAEe,MAAM,CAAC,EACtD/B,UAAU,CAAC4B,OAAO,CAAC,CACpB,CAAC,CACH;MACH;IACF;EACF,CAAC;EAEDI,oBAAoB,EAAE;IACpBzB,IAAI,CAACC,IAAI,EAAE;MACT,MAAM;QAAEC,KAAK;QAAEwB,IAAI;QAAEvB;MAAa,CAAC,GAAG,IAAI;MAE1C,IAAIF,IAAI,CAACQ,IAAI,CAACO,QAAQ,KAAK,GAAG,EAAE;MAEhC,IAAIU,IAAI,CAACf,GAAG,CAACV,IAAI,CAACQ,IAAI,CAAC,EAAE;MACzBiB,IAAI,CAACC,GAAG,CAAC1B,IAAI,CAACQ,IAAI,CAAC;MAEnB,MAAMmB,IAAI,GAAG3B,IAAI,CAACK,GAAG,CAAC,MAAM,CAAC;MAC7B,IAAI,CAACsB,IAAI,CAACrB,YAAY,EAAE,EAAE;;MAI1B,MAAMC,SAAS,GAAGoB,IAAI,CAACnB,IAAI,CAACC,IAAI;MAEhC,IAAI,CAACP,YAAY,CAACQ,GAAG,CAACH,SAAS,CAAC,EAAE;;MAGlC,IAAIN,KAAK,CAACU,UAAU,CAACJ,SAAS,CAAC,KAAKP,IAAI,CAACC,KAAK,CAACU,UAAU,CAACJ,SAAS,CAAC,EAAE;QACpE;MACF;MAEA,MAAMQ,QAAQ,GAAGf,IAAI,CAACQ,IAAI,CAACO,QAAQ,CAACa,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChD,IAAIxC,iBAAiB,CAACyC,QAAQ,CAACd,QAAQ,CAAC,EAAE;QAGxCf,IAAI,CAACgB,WAAW,CACdvB,iBAAiB;QAEfsB,QAAQ,EACRf,IAAI,CAACQ,IAAI,CAACmB,IAAI,EACdtC,oBAAoB,CAClB,GAAG,EACHE,SAAS,CAACS,IAAI,CAACQ,IAAI,CAACmB,IAAI,CAAC,EACzB3B,IAAI,CAACQ,IAAI,CAACsB,KAAK,CAChB,CACF,CACF;MACH,CAAC,MAAM;QAEL9B,IAAI,CAACQ,IAAI,CAACsB,KAAK,GAAGxC,gBAAgB;QAGhCyB,QAAQ,EACRxB,SAAS,CAACS,IAAI,CAACQ,IAAI,CAACmB,IAAI,CAAC,EACzB3B,IAAI,CAACQ,IAAI,CAACsB,KAAK,CAChB;QACD9B,IAAI,CAACQ,IAAI,CAACO,QAAQ,GAAG,GAAG;MAC1B;IACF;EACF;AACF,CAAC;AAEc,SAASgB,cAAc,CACpC/B,IAAc,EACdE,YAAyB;AAEzBC,uBAAgC,GAAG,IAAI,EACvC;EACAH,IAAI,CAACgC,QAAQ,CAACnC,uBAAuB,EAAE;IACrCI,KAAK,EAAED,IAAI,CAACC,KAAK;IACjBC,YAAY;IACZuB,IAAI,EAAE,IAAIQ,OAAO,EAAE;IACnB9B;EACF,CAAC,CAAC;AACJ"}