import React from 'react';
import { BlockCard } from '@appmaker-xyz/ui';

const ShopifyProductRecommendation = ({
  attributes,
  BlocksView,
  innerBlocks,
  blockData,
  ...props
}) => {
  const pagesData = {
    blocks: [
      {
        dependencies: {
          pageState: ['metaData'],
        },
        attributes: {
          customDataSource: {
            source: 'shopify',
            attributes: {
              mapping: {
                items: 'data.data.productRecommendations',
              },
              methodName: 'productRecommendations',
              params: {
                ...attributes?.dataSourceParams,
                // productId: productId,
              },
            },
            repeatable: 'Yes',
            repeatItem: 'DataSource',
          },
          surface: 'appmaker-related-product-scroller',
          hasPages: false,
          horizontal: true,
          gridViewListing: false,
          isInsideCartPage: attributes?.isInsideCartPage === true,
          loadingLayout: 'product-scroller',
        },
        name: 'appmaker/shopify-product-list',
        innerBlocks: [],
      },
    ],
  };
  return (
    <BlockCard
      attributes={{
        ...attributes,
        accessButton: attributes.showViewMoreButton ? attributes.ctaText : '',
        childContainerStyle: { paddingBottom: 12 },
      }}
      {...props}>
      <BlocksView inAppPage={pagesData} {...props} blockData={props.data} />
    </BlockCard>
  );
};

export default ShopifyProductRecommendation;
