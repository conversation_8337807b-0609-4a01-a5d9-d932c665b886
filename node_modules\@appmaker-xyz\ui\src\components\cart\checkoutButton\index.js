import React from 'react';
import CheckoutButtonOne from './CheckoutButtonOne';
import CheckoutButtonTwo from './CheckoutButtonTwo';

const CheckoutButtonTypes = ({ ...props }) => {
  //   const productListDesign = appmaker.applyFilters(
  //     'product-list-type',
  //     'type-1',
  //   );

  if (props.type === 'type-2') {
    return <CheckoutButtonTwo {...props} />;
  }
  return <CheckoutButtonOne {...props} />;
};
const CheckoutButton = ({ onAction, attributes, pageState, clientId, t }) => {
  const { appmakerAction } = attributes;
  return (
    <CheckoutButtonTypes
      clientId={clientId}
      wholeContainerStyle={attributes.wholeContainerStyle}
      fontColor={attributes.fontColor}
      onPress={() => onAction({ ...appmakerAction, t, onAction })}
      {...attributes}
    />
  );
  // return <CheckoutButtonTypes {...props} />;
};
export default CheckoutButton;
