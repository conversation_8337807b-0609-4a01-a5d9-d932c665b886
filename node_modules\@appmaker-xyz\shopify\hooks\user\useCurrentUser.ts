import { useAppStorage } from '@appmaker-xyz/core';
import { useGetCurrentUserQuery } from '@appmaker-xyz/shopify';

type Metafield = {
  namespace: string;
  key: string;
};

interface Props {
  metafields?: Metafield[];
}
type ResponseMetafield = {
  namespace: string;
  key: string;
  value: string;
};

type Customer = {
  id: string;
  firstName: string | null;
  email: string | null;
  tags: string[];
  phone: string | null;
  numberOfOrders: string;
  metafields?: Metafield[];
};

type Data = {
  customer: Customer;
};

type Response = {
  status: string;
  fetchStatus: string;
  isLoading: boolean;
  isSuccess: boolean;
  isError: boolean;
  isInitialLoading: boolean;
  data: Data;
  dataUpdatedAt: number;
  error: any;
  errorUpdatedAt: number;
  failureCount: number;
  failureReason: any;
  errorUpdateCount: number;
  isFetched: boolean;
  isFetchedAfterMount: boolean;
  isFetching: boolean;
  isRefetching: boolean;
  isLoadingError: boolean;
  isPaused: boolean;
  isPlaceholderData: boolean;
  isPreviousData: boolean;
  isRefetchError: boolean;
  isStale: boolean;
  id: string;
  firstName: string;
  email: string;
  tags: string[];
  phone: string | null;
  numberOfOrders: string;
  metafields: ResponseMetafield[];
};

/**
 * Hook to get the current user information.
 *
 * @param {Props} props - The parameters for the hook.
 * @param {Metadata} [props.metadata] - An optional array of metadata objects.
 * @param {string} props.metadata[].namespace - The namespace of the metadata.
 * @param {string} props.metadata[].key - The key of the metadata.
 * @returns {Object} The current user information.
 */
const useCurrentUser = (props: Props): Response => {
  const user = useAppStorage((state) => state?.user);
  const accessToken = user?.accessToken;
  let currentUser = useGetCurrentUserQuery({
    customerAccessToken: accessToken,
    identifiers: props?.metafields || [],
  });

  currentUser = {
    ...currentUser,
    ...currentUser?.data?.customer,
  };

  // Fix types for the response
  return currentUser as unknown as Response;
};

export { useCurrentUser };