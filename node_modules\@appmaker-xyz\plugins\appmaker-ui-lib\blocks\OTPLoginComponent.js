import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, Keyboard } from 'react-native';
import { appmaker } from '@appmaker-xyz/core';
import PhoneInput from 'react-native-phone-number-input';
import {
  Layout,
  Input,
  Button,
  useApThemeState,
  AppmakerText,
} from '@appmaker-xyz/uikit';
import ResendOTPButton from './OTPResendButton';

const OtpLogin = ({ attributes, onAction }) => {
  const { onSubmitPhoneNumberAction } = attributes;
  let {
    enableResendOTP,
    otpMaxLength = 6,
    defaultCountryCode,
    onVerifyOTPAction,
    __appmakerCustomStyles = {
      button: {
        backgroundColor: '#ee3d63',
      },
    },
  } = attributes;
  let otpMaxCount =
    typeof otpMaxLength === 'string' ? parseInt(otpMaxLength) : otpMaxLength;

  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  const [confirm, setConfirm] = useState(null);
  const [phone, setPhone] = useState('');
  const [loading, setLoading] = useState(false);
  const [formatedPhone, setFormatedPhone] = useState('');
  const [otp, setOTP] = useState('');
  const [validNumber, setNumberValidStatus] = useState(true);

  const phoneInput = useRef(null);

  const onSubmitPhoneNumber = async () => {
    Keyboard.dismiss();
    if (!phone || !formatedPhone) {
      onAction({
        action: 'SHOW_MESSAGE',
        params: { title: 'Enter your mobile number to continue' },
      });
      return;
    }
    if (!confirm) {
      let checkValid = phoneInput.current?.isValidNumber(formatedPhone);
      const customCheckValid = await appmaker.applyFilters(
        'otp-phone-validator',
        (formatedPhone) => {
          return true;
        },
      );
      checkValid = checkValid && customCheckValid(formatedPhone);
      setNumberValidStatus(checkValid);
      if (!checkValid) {
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: 'Please enter a valid mobile number.',
          },
        });
        return;
      }
    }
    setLoading(true);
    onSubmitPhoneNumberAction.params = { phone, formatedPhone };
    try {
      await onAction(onSubmitPhoneNumberAction);
      setConfirm(true);
    } catch (error) {}
    setLoading(false);
  };

  const onSubmitOTP = async () => {
    if (!otp) {
      onAction({
        action: 'SHOW_MESSAGE',
        params: { title: 'Please enter OTP' },
      });
      return;
    }
    setLoading(true);
    onVerifyOTPAction.params = { phone, formatedPhone, otp };
    Keyboard.dismiss();
    try {
      await onAction(onVerifyOTPAction);
    } catch (error) {
      onAction({
        action: 'SHOW_MESSAGE',
        params: { title: error.message },
      });
      setOTP('');
    }

    setLoading(false);
  };

  useEffect(() => {
    otp.length === otpMaxCount && onSubmitOTP();
  }, [otp]);
  return (
    <>
      <Layout style={styles.otpview}>
        {!confirm ? (
          <>
            <PhoneInput
              ref={phoneInput}
              defaultValue={phone}
              defaultCode={
                // firebaseOTPStore.settings.default_country_code || 'IN'
                'IN'
              }
              layout="first"
              containerStyle={[
                validNumber
                  ? styles.phoneNumberContainer
                  : styles.phoneNumberContainerError,
                __appmakerCustomStyles?.input?.container,
              ]}
              textInputStyle={{ height: 50 }}
              onChangeText={(text) => {
                setPhone(text);
              }}
              onChangeFormattedText={(text) => {
                setFormatedPhone(text);
              }}
              autoFocus
            />
            <AppmakerText
              status={validNumber ? 'demiDark' : 'danger'}
              category="bodySubText"
              style={styles.helperText}>
              {validNumber
                ? 'Enter your mobile number to get the OTP.'
                : 'Please enter a valid mobile number & try again. '}
            </AppmakerText>
            <Button
              status="dark"
              loading={loading}
              onPress={onSubmitPhoneNumber}
              __appmakerCustomStyles={__appmakerCustomStyles?.button}>
              Send OTP
            </Button>
          </>
        ) : (
          <>
            <Input
              onChangeText={(v) => setOTP(v)}
              label="Enter OTP"
              leftIcon="key"
              type="number"
              value={otp}
              maxLength={otpMaxCount || 6}
              caption="Enter OTP sent to your Mobile Number"
              __appmakerCustomStyles={__appmakerCustomStyles?.input}
              autoFocus={true}
            />
            <Button
              status="dark"
              loading={loading}
              onPress={onSubmitOTP}
              wholeContainerStyle={attributes.buttonWholeContainerStyle}
              __appmakerCustomStyles={__appmakerCustomStyles?.button}>
              Login
            </Button>
            {enableResendOTP && (
              <ResendOTPButton
                small={true}
                loading={loading}
                onPress={onSubmitPhoneNumber}
                wholeContainerStyle={styles.recentOtpButton}
                fontColor={color.dark}>
                Resend OTP
              </ResendOTPButton>
            )}
            {/* <Button onPress={onSubmitPhoneNumber} small status="white">
              Resend OTP
            </Button> */}
          </>
        )}
      </Layout>
    </>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      padding: spacing.base,
      flex: 1,
      alignItems: 'center',
    },
    phoneNumberContainer: {
      height: 50,
      // marginBottom: 20,
      borderWidth: 1,
      borderRadius: 5,
      borderColor: '#212121',
      width: '100%',
      overflow: 'hidden',
    },
    phoneNumberContainerError: {
      height: 50,
      // marginBottom: 20,
      borderWidth: 1,
      borderRadius: 5,
      borderColor: 'red',
      width: '100%',
      overflow: 'hidden',
    },
    helperText: {
      marginTop: spacing.sm,
      marginBottom: spacing.lg,
    },
    otpview: {
      position: 'relative',
      width: '100%',
    },
    logo: {
      width: '50%',
      height: 80,
      alignSelf: 'center',
      resizeMode: 'contain',
      marginVertical: spacing.md,
    },
    signIn: {
      width: '100%',
      alignItems: 'center',
    },
    signInQue: {
      marginBottom: spacing.mini,
    },
    recentOtpButton: {
      backgroundColor: 'transparent',
      marginTop: spacing.base,
    },
  });

export default OtpLogin;
