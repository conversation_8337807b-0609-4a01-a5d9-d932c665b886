import { useCountdown } from '@appmaker-xyz/uikit/src/hooks/useCountdown';
import React from 'react';
import { StyleSheet, View } from 'react-native';
// import { AppmakerText } from '../../index';
import { AppmakerText } from '@appmaker-xyz/uikit';

const ExpiredNotice = ({ expiredText = 'Expired!!' }) => {
  return (
    <View>
      <AppmakerText>{expiredText}</AppmakerText>
    </View>
  );
};

const DateTimeDisplay = ({ value, type, isDanger, attributes }) => {
  const {
    dangerBgColor = '#ff0000',
    dangerTextColor = '#fff',
    bgColor = '#212121',
    textColor = '#F8FAFC',
    size,
    stacked,
  } = attributes;
  return (
    <View
      style={[
        styles.card,
        {
          backgroundColor: bgColor,
          paddingVertical: size === 'lg' ? 10 : 2,
          borderRadius: size === 'lg' ? 6 : 4,
          flexDirection: stacked ? 'column' : 'row',
          paddingHorizontal: stacked ? 12 : 6,
        },
      ]}>
      <AppmakerText
        category={size === 'lg' ? 'h1Heading' : 'smallButtonText'}
        fontColor={textColor}>
        {value}
      </AppmakerText>
      <AppmakerText
        category={size === 'lg' ? 'highlighter2' : 'smallButtonText'}
        fontColor={textColor}
        style={{ marginStart: 2 }}>
        {type}
      </AppmakerText>
    </View>
  );
};

const ShowCounter = ({ days, hours, minutes, seconds, attributes = {} }) => {
  return (
    <View style={styles.wholeContainer}>
      {days !== 0 ? (
        <>
          <DateTimeDisplay
            value={days}
            type={attributes.size === 'lg' ? 'DAYS' : 'D'}
            isDanger={days <= 1}
            attributes={attributes}
          />
          <AppmakerText style={styles.colon}>:</AppmakerText>
        </>
      ) : null}
      <DateTimeDisplay
        value={hours}
        type={attributes.size === 'lg' ? 'HOURS' : 'H'}
        isDanger={days <= 1}
        attributes={attributes}
      />
      <AppmakerText style={styles.colon}>:</AppmakerText>
      <DateTimeDisplay
        value={minutes}
        type={attributes.size === 'lg' ? 'MINUTES' : 'M'}
        isDanger={days <= 1}
        attributes={attributes}
      />
      <AppmakerText style={styles.colon}>:</AppmakerText>
      <DateTimeDisplay
        value={seconds}
        type={attributes.size === 'lg' ? 'SECONDS' : 'S'}
        isDanger={days <= 1}
        attributes={attributes}
      />
    </View>
  );
};

const CountdownTimer = ({ attributes }) => {
  const {
    targetDate,
    expiredText,
    dangerBgColor,
    dangerTextColor,
    bgColor,
    textColor,
    size,
    stacked,
    onEndTimer,
  } = attributes;
  const [days, hours, minutes, seconds] = useCountdown(targetDate);

  if (days + hours + minutes + seconds <= 0) {
    onEndTimer(expiredText);
    return <ExpiredNotice expiredText={expiredText} />;
  } else {
    return (
      <ShowCounter
        days={days}
        hours={hours}
        minutes={minutes}
        seconds={seconds}
        attributes={{
          dangerBgColor: dangerBgColor,
          dangerTextColor: dangerTextColor,
          bgColor: bgColor,
          textColor: textColor,
          size: size,
          stacked: stacked,
        }}
      />
    );
  }
};
export { CountdownTimer };

export default function CountdownTimerDemo() {
  const THREE_DAYS_IN_MS = 3 * 24 * 60 * 60 * 1000;
  const NOW_IN_MS = new Date().getTime();

  const dateTimeAfterThreeDays = NOW_IN_MS + THREE_DAYS_IN_MS;

  return (
    <View>
      <AppmakerText>Countdown Timer</AppmakerText>
      <CountdownTimer
        attributes={{
          targetDate: dateTimeAfterThreeDays,
          showExpiredNotice: true,
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  wholeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  card: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  colon: {
    marginHorizontal: 4,
  },
});
