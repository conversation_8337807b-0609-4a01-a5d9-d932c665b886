{"version": 3, "names": ["_t", "require", "isClassDeclaration", "isExportDefaultSpecifier", "isExportNamespaceSpecifier", "isImportDefaultSpecifier", "isImportNamespaceSpecifier", "isStatement", "ImportSpecifier", "node", "importKind", "word", "space", "print", "imported", "local", "name", "ImportDefaultSpecifier", "ExportDefaultSpecifier", "exported", "ExportSpecifier", "exportKind", "ExportNamespaceSpecifier", "token", "_printAssertions", "printList", "assertions", "ExportAllDeclaration", "_node$assertions", "length", "source", "semicolon", "maybePrintDecoratorsBeforeExport", "printer", "declaration", "_shouldPrintDecoratorsBeforeExport", "printJoin", "decorators", "ExportNamedDeclaration", "declar", "specifiers", "slice", "hasSpecial", "first", "shift", "_node$assertions2", "ExportDefaultDeclaration", "noIndentInnerCommentsHere", "ImportDeclaration", "_node$assertions3", "isTypeKind", "module", "hasSpecifiers", "_node$attributes", "attributes", "ImportAttribute", "key", "value", "ImportNamespaceSpecifier"], "sources": ["../../src/generators/modules.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport {\n  isClassDeclaration,\n  isExportDefaultSpecifier,\n  isExportNamespaceSpecifier,\n  isImportDefaultSpecifier,\n  isImportNamespaceSpecifier,\n  isStatement,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nexport function ImportSpecifier(this: Printer, node: t.ImportSpecifier) {\n  if (node.importKind === \"type\" || node.importKind === \"typeof\") {\n    this.word(node.importKind);\n    this.space();\n  }\n\n  this.print(node.imported, node);\n  // @ts-expect-error todo(flow-ts) maybe check node type instead of relying on name to be undefined on t.StringLiteral\n  if (node.local && node.local.name !== node.imported.name) {\n    this.space();\n    this.word(\"as\");\n    this.space();\n    this.print(node.local, node);\n  }\n}\n\nexport function ImportDefaultSpecifier(\n  this: Printer,\n  node: t.ImportDefaultSpecifier,\n) {\n  this.print(node.local, node);\n}\n\nexport function ExportDefaultSpecifier(\n  this: Printer,\n  node: t.ExportDefaultSpecifier,\n) {\n  this.print(node.exported, node);\n}\n\nexport function ExportSpecifier(this: Printer, node: t.ExportSpecifier) {\n  if (node.exportKind === \"type\") {\n    this.word(\"type\");\n    this.space();\n  }\n\n  this.print(node.local, node);\n  // @ts-expect-error todo(flow-ts) maybe check node type instead of relying on name to be undefined on t.StringLiteral\n  if (node.exported && node.local.name !== node.exported.name) {\n    this.space();\n    this.word(\"as\");\n    this.space();\n    this.print(node.exported, node);\n  }\n}\n\nexport function ExportNamespaceSpecifier(\n  this: Printer,\n  node: t.ExportNamespaceSpecifier,\n) {\n  this.token(\"*\");\n  this.space();\n  this.word(\"as\");\n  this.space();\n  this.print(node.exported, node);\n}\n\nexport function _printAssertions(\n  this: Printer,\n  node: Extract<t.Node, { assertions?: t.ImportAttribute[] }>,\n) {\n  this.word(\"assert\");\n  this.space();\n  this.token(\"{\");\n  this.space();\n  this.printList(node.assertions, node);\n  this.space();\n  this.token(\"}\");\n}\n\nexport function ExportAllDeclaration(\n  this: Printer,\n  node: t.ExportAllDeclaration | t.DeclareExportAllDeclaration,\n) {\n  this.word(\"export\");\n  this.space();\n  if (node.exportKind === \"type\") {\n    this.word(\"type\");\n    this.space();\n  }\n  this.token(\"*\");\n  this.space();\n  this.word(\"from\");\n  this.space();\n  // @ts-expect-error Fixme: assertions is not defined in DeclareExportAllDeclaration\n  if (node.assertions?.length) {\n    this.print(node.source, node, true);\n    this.space();\n    // @ts-expect-error Fixme: assertions is not defined in DeclareExportAllDeclaration\n    this._printAssertions(node);\n  } else {\n    this.print(node.source, node);\n  }\n\n  this.semicolon();\n}\n\nfunction maybePrintDecoratorsBeforeExport(\n  printer: Printer,\n  node: t.ExportNamedDeclaration | t.ExportDefaultDeclaration,\n) {\n  if (\n    isClassDeclaration(node.declaration) &&\n    printer._shouldPrintDecoratorsBeforeExport(\n      node as t.ExportNamedDeclaration & { declaration: t.ClassDeclaration },\n    )\n  ) {\n    printer.printJoin(node.declaration.decorators, node);\n  }\n}\n\nexport function ExportNamedDeclaration(\n  this: Printer,\n  node: t.ExportNamedDeclaration,\n) {\n  maybePrintDecoratorsBeforeExport(this, node);\n\n  this.word(\"export\");\n  this.space();\n  if (node.declaration) {\n    const declar = node.declaration;\n    this.print(declar, node);\n    if (!isStatement(declar)) this.semicolon();\n  } else {\n    if (node.exportKind === \"type\") {\n      this.word(\"type\");\n      this.space();\n    }\n\n    const specifiers = node.specifiers.slice(0);\n\n    // print \"special\" specifiers first\n    let hasSpecial = false;\n    for (;;) {\n      const first = specifiers[0];\n      if (\n        isExportDefaultSpecifier(first) ||\n        isExportNamespaceSpecifier(first)\n      ) {\n        hasSpecial = true;\n        this.print(specifiers.shift(), node);\n        if (specifiers.length) {\n          this.token(\",\");\n          this.space();\n        }\n      } else {\n        break;\n      }\n    }\n\n    if (specifiers.length || (!specifiers.length && !hasSpecial)) {\n      this.token(\"{\");\n      if (specifiers.length) {\n        this.space();\n        this.printList(specifiers, node);\n        this.space();\n      }\n      this.token(\"}\");\n    }\n\n    if (node.source) {\n      this.space();\n      this.word(\"from\");\n      this.space();\n      if (node.assertions?.length) {\n        this.print(node.source, node, true);\n        this.space();\n        this._printAssertions(node);\n      } else {\n        this.print(node.source, node);\n      }\n    }\n\n    this.semicolon();\n  }\n}\n\nexport function ExportDefaultDeclaration(\n  this: Printer,\n  node: t.ExportDefaultDeclaration,\n) {\n  maybePrintDecoratorsBeforeExport(this, node);\n\n  this.word(\"export\");\n  this.noIndentInnerCommentsHere();\n  this.space();\n  this.word(\"default\");\n  this.space();\n  const declar = node.declaration;\n  this.print(declar, node);\n  if (!isStatement(declar)) this.semicolon();\n}\n\nexport function ImportDeclaration(this: Printer, node: t.ImportDeclaration) {\n  this.word(\"import\");\n  this.space();\n\n  const isTypeKind = node.importKind === \"type\" || node.importKind === \"typeof\";\n  if (isTypeKind) {\n    this.noIndentInnerCommentsHere();\n    this.word(node.importKind);\n    this.space();\n  } else if (node.module) {\n    this.noIndentInnerCommentsHere();\n    this.word(\"module\");\n    this.space();\n  }\n\n  const specifiers = node.specifiers.slice(0);\n  const hasSpecifiers = !!specifiers.length;\n  // print \"special\" specifiers first. The loop condition is constant,\n  // but there is a \"break\" in the body.\n  while (hasSpecifiers) {\n    const first = specifiers[0];\n    if (isImportDefaultSpecifier(first) || isImportNamespaceSpecifier(first)) {\n      this.print(specifiers.shift(), node);\n      if (specifiers.length) {\n        this.token(\",\");\n        this.space();\n      }\n    } else {\n      break;\n    }\n  }\n\n  if (specifiers.length) {\n    this.token(\"{\");\n    this.space();\n    this.printList(specifiers, node);\n    this.space();\n    this.token(\"}\");\n  } else if (isTypeKind && !hasSpecifiers) {\n    this.token(\"{\");\n    this.token(\"}\");\n  }\n\n  if (hasSpecifiers || isTypeKind) {\n    this.space();\n    this.word(\"from\");\n    this.space();\n  }\n\n  if (node.assertions?.length) {\n    this.print(node.source, node, true);\n    this.space();\n    this._printAssertions(node);\n  } else {\n    this.print(node.source, node);\n  }\n  if (!process.env.BABEL_8_BREAKING) {\n    // @ts-ignore(Babel 7 vs Babel 8) Babel 7 supports module attributes\n    if (node.attributes?.length) {\n      this.space();\n      this.word(\"with\");\n      this.space();\n      // @ts-ignore(Babel 7 vs Babel 8) Babel 7 supports module attributes\n      this.printList(node.attributes, node);\n    }\n  }\n\n  this.semicolon();\n}\n\nexport function ImportAttribute(this: Printer, node: t.ImportAttribute) {\n  this.print(node.key);\n  this.token(\":\");\n  this.space();\n  this.print(node.value);\n}\n\nexport function ImportNamespaceSpecifier(\n  this: Printer,\n  node: t.ImportNamespaceSpecifier,\n) {\n  this.token(\"*\");\n  this.space();\n  this.word(\"as\");\n  this.space();\n  this.print(node.local, node);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAOsB;EANpBC,kBAAkB;EAClBC,wBAAwB;EACxBC,0BAA0B;EAC1BC,wBAAwB;EACxBC,0BAA0B;EAC1BC;AAAW,IAAAP,EAAA;AAIN,SAASQ,eAAeA,CAAgBC,IAAuB,EAAE;EACtE,IAAIA,IAAI,CAACC,UAAU,KAAK,MAAM,IAAID,IAAI,CAACC,UAAU,KAAK,QAAQ,EAAE;IAC9D,IAAI,CAACC,IAAI,CAACF,IAAI,CAACC,UAAU,CAAC;IAC1B,IAAI,CAACE,KAAK,EAAE;EACd;EAEA,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACK,QAAQ,EAAEL,IAAI,CAAC;EAE/B,IAAIA,IAAI,CAACM,KAAK,IAAIN,IAAI,CAACM,KAAK,CAACC,IAAI,KAAKP,IAAI,CAACK,QAAQ,CAACE,IAAI,EAAE;IACxD,IAAI,CAACJ,KAAK,EAAE;IACZ,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,CAACC,KAAK,EAAE;IACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACM,KAAK,EAAEN,IAAI,CAAC;EAC9B;AACF;AAEO,SAASQ,sBAAsBA,CAEpCR,IAA8B,EAC9B;EACA,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACM,KAAK,EAAEN,IAAI,CAAC;AAC9B;AAEO,SAASS,sBAAsBA,CAEpCT,IAA8B,EAC9B;EACA,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACU,QAAQ,EAAEV,IAAI,CAAC;AACjC;AAEO,SAASW,eAAeA,CAAgBX,IAAuB,EAAE;EACtE,IAAIA,IAAI,CAACY,UAAU,KAAK,MAAM,EAAE;IAC9B,IAAI,CAACV,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,EAAE;EACd;EAEA,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACM,KAAK,EAAEN,IAAI,CAAC;EAE5B,IAAIA,IAAI,CAACU,QAAQ,IAAIV,IAAI,CAACM,KAAK,CAACC,IAAI,KAAKP,IAAI,CAACU,QAAQ,CAACH,IAAI,EAAE;IAC3D,IAAI,CAACJ,KAAK,EAAE;IACZ,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,CAACC,KAAK,EAAE;IACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACU,QAAQ,EAAEV,IAAI,CAAC;EACjC;AACF;AAEO,SAASa,wBAAwBA,CAEtCb,IAAgC,EAChC;EACA,IAAI,CAACc,SAAK,IAAK;EACf,IAAI,CAACX,KAAK,EAAE;EACZ,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACU,QAAQ,EAAEV,IAAI,CAAC;AACjC;AAEO,SAASe,gBAAgBA,CAE9Bf,IAA2D,EAC3D;EACA,IAAI,CAACE,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACW,SAAK,KAAK;EACf,IAAI,CAACX,KAAK,EAAE;EACZ,IAAI,CAACa,SAAS,CAAChB,IAAI,CAACiB,UAAU,EAAEjB,IAAI,CAAC;EACrC,IAAI,CAACG,KAAK,EAAE;EACZ,IAAI,CAACW,SAAK,KAAK;AACjB;AAEO,SAASI,oBAAoBA,CAElClB,IAA4D,EAC5D;EAAA,IAAAmB,gBAAA;EACA,IAAI,CAACjB,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,EAAE;EACZ,IAAIH,IAAI,CAACY,UAAU,KAAK,MAAM,EAAE;IAC9B,IAAI,CAACV,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,EAAE;EACd;EACA,IAAI,CAACW,SAAK,IAAK;EACf,IAAI,CAACX,KAAK,EAAE;EACZ,IAAI,CAACD,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACC,KAAK,EAAE;EAEZ,KAAAgB,gBAAA,GAAInB,IAAI,CAACiB,UAAU,aAAfE,gBAAA,CAAiBC,MAAM,EAAE;IAC3B,IAAI,CAAChB,KAAK,CAACJ,IAAI,CAACqB,MAAM,EAAErB,IAAI,EAAE,IAAI,CAAC;IACnC,IAAI,CAACG,KAAK,EAAE;IAEZ,IAAI,CAACY,gBAAgB,CAACf,IAAI,CAAC;EAC7B,CAAC,MAAM;IACL,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACqB,MAAM,EAAErB,IAAI,CAAC;EAC/B;EAEA,IAAI,CAACsB,SAAS,EAAE;AAClB;AAEA,SAASC,gCAAgCA,CACvCC,OAAgB,EAChBxB,IAA2D,EAC3D;EACA,IACEP,kBAAkB,CAACO,IAAI,CAACyB,WAAW,CAAC,IACpCD,OAAO,CAACE,kCAAkC,CACxC1B,IAAI,CACL,EACD;IACAwB,OAAO,CAACG,SAAS,CAAC3B,IAAI,CAACyB,WAAW,CAACG,UAAU,EAAE5B,IAAI,CAAC;EACtD;AACF;AAEO,SAAS6B,sBAAsBA,CAEpC7B,IAA8B,EAC9B;EACAuB,gCAAgC,CAAC,IAAI,EAAEvB,IAAI,CAAC;EAE5C,IAAI,CAACE,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,EAAE;EACZ,IAAIH,IAAI,CAACyB,WAAW,EAAE;IACpB,MAAMK,MAAM,GAAG9B,IAAI,CAACyB,WAAW;IAC/B,IAAI,CAACrB,KAAK,CAAC0B,MAAM,EAAE9B,IAAI,CAAC;IACxB,IAAI,CAACF,WAAW,CAACgC,MAAM,CAAC,EAAE,IAAI,CAACR,SAAS,EAAE;EAC5C,CAAC,MAAM;IACL,IAAItB,IAAI,CAACY,UAAU,KAAK,MAAM,EAAE;MAC9B,IAAI,CAACV,IAAI,CAAC,MAAM,CAAC;MACjB,IAAI,CAACC,KAAK,EAAE;IACd;IAEA,MAAM4B,UAAU,GAAG/B,IAAI,CAAC+B,UAAU,CAACC,KAAK,CAAC,CAAC,CAAC;IAG3C,IAAIC,UAAU,GAAG,KAAK;IACtB,SAAS;MACP,MAAMC,KAAK,GAAGH,UAAU,CAAC,CAAC,CAAC;MAC3B,IACErC,wBAAwB,CAACwC,KAAK,CAAC,IAC/BvC,0BAA0B,CAACuC,KAAK,CAAC,EACjC;QACAD,UAAU,GAAG,IAAI;QACjB,IAAI,CAAC7B,KAAK,CAAC2B,UAAU,CAACI,KAAK,EAAE,EAAEnC,IAAI,CAAC;QACpC,IAAI+B,UAAU,CAACX,MAAM,EAAE;UACrB,IAAI,CAACN,SAAK,IAAK;UACf,IAAI,CAACX,KAAK,EAAE;QACd;MACF,CAAC,MAAM;QACL;MACF;IACF;IAEA,IAAI4B,UAAU,CAACX,MAAM,IAAK,CAACW,UAAU,CAACX,MAAM,IAAI,CAACa,UAAW,EAAE;MAC5D,IAAI,CAACnB,SAAK,KAAK;MACf,IAAIiB,UAAU,CAACX,MAAM,EAAE;QACrB,IAAI,CAACjB,KAAK,EAAE;QACZ,IAAI,CAACa,SAAS,CAACe,UAAU,EAAE/B,IAAI,CAAC;QAChC,IAAI,CAACG,KAAK,EAAE;MACd;MACA,IAAI,CAACW,SAAK,KAAK;IACjB;IAEA,IAAId,IAAI,CAACqB,MAAM,EAAE;MAAA,IAAAe,iBAAA;MACf,IAAI,CAACjC,KAAK,EAAE;MACZ,IAAI,CAACD,IAAI,CAAC,MAAM,CAAC;MACjB,IAAI,CAACC,KAAK,EAAE;MACZ,KAAAiC,iBAAA,GAAIpC,IAAI,CAACiB,UAAU,aAAfmB,iBAAA,CAAiBhB,MAAM,EAAE;QAC3B,IAAI,CAAChB,KAAK,CAACJ,IAAI,CAACqB,MAAM,EAAErB,IAAI,EAAE,IAAI,CAAC;QACnC,IAAI,CAACG,KAAK,EAAE;QACZ,IAAI,CAACY,gBAAgB,CAACf,IAAI,CAAC;MAC7B,CAAC,MAAM;QACL,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACqB,MAAM,EAAErB,IAAI,CAAC;MAC/B;IACF;IAEA,IAAI,CAACsB,SAAS,EAAE;EAClB;AACF;AAEO,SAASe,wBAAwBA,CAEtCrC,IAAgC,EAChC;EACAuB,gCAAgC,CAAC,IAAI,EAAEvB,IAAI,CAAC;EAE5C,IAAI,CAACE,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACoC,yBAAyB,EAAE;EAChC,IAAI,CAACnC,KAAK,EAAE;EACZ,IAAI,CAACD,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACC,KAAK,EAAE;EACZ,MAAM2B,MAAM,GAAG9B,IAAI,CAACyB,WAAW;EAC/B,IAAI,CAACrB,KAAK,CAAC0B,MAAM,EAAE9B,IAAI,CAAC;EACxB,IAAI,CAACF,WAAW,CAACgC,MAAM,CAAC,EAAE,IAAI,CAACR,SAAS,EAAE;AAC5C;AAEO,SAASiB,iBAAiBA,CAAgBvC,IAAyB,EAAE;EAAA,IAAAwC,iBAAA;EAC1E,IAAI,CAACtC,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACC,KAAK,EAAE;EAEZ,MAAMsC,UAAU,GAAGzC,IAAI,CAACC,UAAU,KAAK,MAAM,IAAID,IAAI,CAACC,UAAU,KAAK,QAAQ;EAC7E,IAAIwC,UAAU,EAAE;IACd,IAAI,CAACH,yBAAyB,EAAE;IAChC,IAAI,CAACpC,IAAI,CAACF,IAAI,CAACC,UAAU,CAAC;IAC1B,IAAI,CAACE,KAAK,EAAE;EACd,CAAC,MAAM,IAAIH,IAAI,CAAC0C,MAAM,EAAE;IACtB,IAAI,CAACJ,yBAAyB,EAAE;IAChC,IAAI,CAACpC,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACC,KAAK,EAAE;EACd;EAEA,MAAM4B,UAAU,GAAG/B,IAAI,CAAC+B,UAAU,CAACC,KAAK,CAAC,CAAC,CAAC;EAC3C,MAAMW,aAAa,GAAG,CAAC,CAACZ,UAAU,CAACX,MAAM;EAGzC,OAAOuB,aAAa,EAAE;IACpB,MAAMT,KAAK,GAAGH,UAAU,CAAC,CAAC,CAAC;IAC3B,IAAInC,wBAAwB,CAACsC,KAAK,CAAC,IAAIrC,0BAA0B,CAACqC,KAAK,CAAC,EAAE;MACxE,IAAI,CAAC9B,KAAK,CAAC2B,UAAU,CAACI,KAAK,EAAE,EAAEnC,IAAI,CAAC;MACpC,IAAI+B,UAAU,CAACX,MAAM,EAAE;QACrB,IAAI,CAACN,SAAK,IAAK;QACf,IAAI,CAACX,KAAK,EAAE;MACd;IACF,CAAC,MAAM;MACL;IACF;EACF;EAEA,IAAI4B,UAAU,CAACX,MAAM,EAAE;IACrB,IAAI,CAACN,SAAK,KAAK;IACf,IAAI,CAACX,KAAK,EAAE;IACZ,IAAI,CAACa,SAAS,CAACe,UAAU,EAAE/B,IAAI,CAAC;IAChC,IAAI,CAACG,KAAK,EAAE;IACZ,IAAI,CAACW,SAAK,KAAK;EACjB,CAAC,MAAM,IAAI2B,UAAU,IAAI,CAACE,aAAa,EAAE;IACvC,IAAI,CAAC7B,SAAK,KAAK;IACf,IAAI,CAACA,SAAK,KAAK;EACjB;EAEA,IAAI6B,aAAa,IAAIF,UAAU,EAAE;IAC/B,IAAI,CAACtC,KAAK,EAAE;IACZ,IAAI,CAACD,IAAI,CAAC,MAAM,CAAC;IACjB,IAAI,CAACC,KAAK,EAAE;EACd;EAEA,KAAAqC,iBAAA,GAAIxC,IAAI,CAACiB,UAAU,aAAfuB,iBAAA,CAAiBpB,MAAM,EAAE;IAC3B,IAAI,CAAChB,KAAK,CAACJ,IAAI,CAACqB,MAAM,EAAErB,IAAI,EAAE,IAAI,CAAC;IACnC,IAAI,CAACG,KAAK,EAAE;IACZ,IAAI,CAACY,gBAAgB,CAACf,IAAI,CAAC;EAC7B,CAAC,MAAM;IACL,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACqB,MAAM,EAAErB,IAAI,CAAC;EAC/B;EACmC;IAAA,IAAA4C,gBAAA;IAEjC,KAAAA,gBAAA,GAAI5C,IAAI,CAAC6C,UAAU,aAAfD,gBAAA,CAAiBxB,MAAM,EAAE;MAC3B,IAAI,CAACjB,KAAK,EAAE;MACZ,IAAI,CAACD,IAAI,CAAC,MAAM,CAAC;MACjB,IAAI,CAACC,KAAK,EAAE;MAEZ,IAAI,CAACa,SAAS,CAAChB,IAAI,CAAC6C,UAAU,EAAE7C,IAAI,CAAC;IACvC;EACF;EAEA,IAAI,CAACsB,SAAS,EAAE;AAClB;AAEO,SAASwB,eAAeA,CAAgB9C,IAAuB,EAAE;EACtE,IAAI,CAACI,KAAK,CAACJ,IAAI,CAAC+C,GAAG,CAAC;EACpB,IAAI,CAACjC,SAAK,IAAK;EACf,IAAI,CAACX,KAAK,EAAE;EACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACgD,KAAK,CAAC;AACxB;AAEO,SAASC,wBAAwBA,CAEtCjD,IAAgC,EAChC;EACA,IAAI,CAACc,SAAK,IAAK;EACf,IAAI,CAACX,KAAK,EAAE;EACZ,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACM,KAAK,EAAEN,IAAI,CAAC;AAC9B"}