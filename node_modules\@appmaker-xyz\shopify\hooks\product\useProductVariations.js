import { useMemo, useRef, useState } from 'react';
import { currencyHelper } from '../../helper/index';
import { variationConvertor } from '../../helper/productVariaton';

function useProductVariations(props) {
  let product = props?.blockData;
  let shopifyOptions = product?.node?.options;

  const variants = product?.node?.variants?.edges;
  const getVariantOptions = (variation) => {
    let initialSelectedOptions = {};
    variation?.node?.selectedOptions?.map?.((opt) => {
      initialSelectedOptions = {
        ...initialSelectedOptions,
        [opt?.name]: opt?.value,
      };
    });
    return initialSelectedOptions;
  };
  const getFirstAvailableVariant = () =>
    variants.find?.((item) => item?.node?.availableForSale);
  let initialVariation;
  if (product?.node?.selectedVariantId) {
    const initialSelectedVariant = variants.find?.(
      (item) => item?.node?.id === product.node.selectedVariantId,
    );
    initialVariation = initialSelectedVariant?.node?.id
      ? initialSelectedVariant
      : getFirstAvailableVariant();
  } else {
    initialVariation = getFirstAvailableVariant();
  }
  const [selectedVariant, setSelectedVariant] = useState(
    initialVariation || product?.node?.variants?.edges?.[0],
  );
  const [selectedOptions, setSelectedOptions] = useState(
    getVariantOptions(initialVariation) || {},
  );

  const prevId = useRef(product?.node?.id);

  if (prevId.current !== product?.node?.id) {
    setSelectedVariant(initialVariation || product?.node?.variants?.edges?.[0]);
    setSelectedOptions(getVariantOptions(initialVariation) || {});
    prevId.current = product?.node?.id;
  }

  const reformatOptions = () => {
    return shopifyOptions
      ?.map?.((item) => {
        if (item?.name !== 'Title' || item?.values?.[0] !== 'Default Title') {
          return {
            id: item?.name,
            label: item?.name,
            values: item?.values?.map((value) => ({
              id: value,
              label: value,
              isAvailable: true, //isOptionAvailable({ [item.name]: value }),
            })),
          };
        }
      })
      ?.filter?.((i) => i);
  };
  const isOptionAvailable = (option) => {
    let available = false;
    product?.node?.variants?.edges?.map((val) => {
      val?.node?.selectedOptions?.map((opt) => {
        if (option[opt.name] && option[opt.name] === opt.value) {
          // isThere = true;
          available = !available ? val?.node?.availableForSale : available;
          if (available) return;
        }
      });
    });
    return available;
  };

  const updateOptions = (prevSelectedOptions, currentSelectedOptions) => {
    const updatedObject = { ...prevSelectedOptions };
    for (const key in currentSelectedOptions) {
      if (currentSelectedOptions?.hasOwnProperty(key)) {
        updatedObject[key] = currentSelectedOptions[key];
      }
    }
    return updatedObject;
  };

  let options = useMemo(() => reformatOptions(), [product?.node?.id]);
  const setOptions = (currentSelectedOptions) => {
    let newSelectedOptions = updateOptions(
      selectedOptions,
      currentSelectedOptions,
    );
    const variant = getVariationFromOptions(
      newSelectedOptions,
      currentSelectedOptions,
    );
    setSelectedOptions(newSelectedOptions);
    setSelectedVariant(variant);
  };

  const setVariant = ({ variantId }) => {
    if (variantId) {
      const variant = product?.node?.variants?.edges?.find(
        (v) => v?.node?.id === variantId,
      );
      if (variant?.node?.id) {
        let variantSelectedOptions = getVariantOptions(variant);
        let newSelectedOptions = updateOptions(
          selectedOptions,
          variantSelectedOptions,
        );
        setSelectedOptions(newSelectedOptions);
        setSelectedVariant(variant);
      }
    }
  };

  const getVariationFromOptions = (optionsSelected, currentSelectedOptions) => {
    // Finding variant with all the selected options ( including some ui hidden options selected )
    let variant = product?.node?.variants?.edges?.find((variant) => {
      return variant?.node?.selectedOptions?.every((toBeSelectedOPtions) => {
        return (
          optionsSelected[toBeSelectedOPtions?.name] ===
          toBeSelectedOPtions?.value
        );
      });
    });
    if (!variant && currentSelectedOptions) {
      // Finding variant with just the current selected options
      variant = product?.node?.variants?.edges?.find((variant) => {
        return variant?.node?.selectedOptions?.some((toBeSelectedOPtions) => {
          return (
            currentSelectedOptions?.[toBeSelectedOPtions?.name] ===
            toBeSelectedOPtions?.value
          );
        });
      });
    }
    return variant;
  };
  const salePrice = parseFloat(selectedVariant?.node?.price?.amount || 0);
  const regularPrice = parseFloat(
    selectedVariant?.node?.compareAtPrice?.amount || 0,
  );
  const imageUrl = selectedVariant?.node?.image?.url;
  const onSale = regularPrice > salePrice;
  const salePercentage = onSale
    ? `${Math.round((100 * (regularPrice - salePrice)) / regularPrice)} %`
    : null;
  const finalSalePrice = currencyHelper(
    selectedVariant?.node?.price?.amount,
    selectedVariant?.node?.price?.currencyCode,
  );
  const finalRegularPrice = selectedVariant?.node?.compareAtPrice ? currencyHelper(
    selectedVariant?.node?.compareAtPrice?.amount,
    selectedVariant?.node?.compareAtPrice?.currencyCode,
  ) : finalSalePrice;
  const isMultiOptions =
    shopifyOptions?.length > 1 ||
    (shopifyOptions?.length === 1 && shopifyOptions?.[0]?.values?.length > 1);
  const productOptionsV2 = useMemo(
    () => variationConvertor(product),
    [product],
  );
  return {
    isSingleVariation: !isMultiOptions,
    isMultiOptions,
    options,
    selectedOptions,
    selectedVariant,
    setOptions,
    variant: selectedVariant,
    salePrice,
    regularPrice,
    regularPriceWithCurrency:
      finalRegularPrice === finalSalePrice ? '' : finalRegularPrice,
    salePriceWithCurrency: finalSalePrice,
    imageUrl,
    salePercentage,
    onSale,
    isVariantAvailable: selectedVariant?.node?.availableForSale,
    productOptionsV2,
    setVariant,
    isOptionAvailable
  };
}

export { useProductVariations };
