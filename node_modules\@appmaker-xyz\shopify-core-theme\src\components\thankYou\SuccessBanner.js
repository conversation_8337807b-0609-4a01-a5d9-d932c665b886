import React from 'react';

import { StyleSheet } from 'react-native';
import { ThemeText, Layout } from '@appmaker-xyz/ui';
import { useOrderItem } from '@appmaker-xyz/shopify';

import { SuccessCheck } from '../../../assets/svg';

const SuccessBanner = (props) => {
  const { orderId } = useOrderItem(props);
  return (
    <Layout style={styles.container}>
      <SuccessCheck style={styles.imageVector} />
      <Layout style={styles.textContainer}>
        <ThemeText style={styles.orderName}>Order {`#${orderId}`}</ThemeText>
        <ThemeText style={styles.thankYouText} fontFamily="bold">
          Thank You!
        </ThemeText>
        <ThemeText style={styles.confirmText} fontFamily="bold">
          Your order is confirmed
        </ThemeText>
        <ThemeText style={styles.emailText}>
          We will keep you updated on your order status
        </ThemeText>
      </Layout>
    </Layout>
  );
};

export default SuccessBanner;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 96,
    alignItems: 'center',
  },
  imageVector: {
    height: 128,
    width: 128,
  },
  textContainer: {
    marginTop: 10,
    alignItems: 'center',
  },
  orderName: {
    fontSize: 16,
  },
  thankYouText: {
    fontSize: 24,
  },
  confirmText: {
    fontSize: 20,
  },
  emailText: {},
});
