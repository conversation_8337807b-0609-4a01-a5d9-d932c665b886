import React, { useState } from 'react';
import { StyleSheet, Dimensions, ActivityIndicator } from 'react-native';
import { Layout, ThemeText, AppTouchable } from '../atoms';
import { Button } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import BottomSheet from '../coreComponents/BottomSheet';
import { isEmpty } from 'lodash';

const windowWidth = Dimensions.get('window').width;

function AddressCard(props) {
  const {
    addressType,
    name,
    address,
    onRemove,
    onEdit,
    onPress,
    themeColor = '#3280FE',
    id,
    addressSelectLoading,
    defaultAddressID,
    formattedAddress,
    isDefault,
    selectAddress,
  } = props;
  // const [active, setActive] = useState(false);

  const [defaultAddress, setDefaultAddress] = useState(false);
  const active = isDefault;
  return (
    <AppTouchable
      // onPress={() => setActive(!active)}
      style={[
        styles.addressContainer,
        { borderColor: active ? themeColor : 'transparent' },
      ]}>
      <Layout style={styles.addressHeader}>
        <ThemeText color={themeColor}>{addressType}</ThemeText>
        {/* <AppTouchable style={styles.removeAddressButton} onPress={onRemove}>
          <ThemeText color="#BE1706" fontFamily="medium">
            Remove
          </ThemeText>
        </AppTouchable> */}
      </Layout>
      <ThemeText size="lg">{name}</ThemeText>
      <Layout style={styles.addressTextContainer}>
        {/* <ThemeText size="md">{address}</ThemeText> */}
        {formattedAddress?.length > 0
          ? formattedAddress?.map?.((addr) => {
              return <ThemeText size="md">{addr}</ThemeText>;
            })
          : null}
      </Layout>
      <Layout style={styles.cardActionsContainer}>
        <AppTouchable style={styles.editButton} onPress={onEdit}>
          <ThemeText>EDIT</ThemeText>
        </AppTouchable>
        <AppTouchable
          style={styles.deliverButton}
          onPress={() => {
            onPress && onPress({ id: id });
          }}>
          {addressSelectLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <ThemeText color="#fff">DELIVER HERE</ThemeText>
          )}
        </AppTouchable>
      </Layout>
      {/* <AppTouchable
        onPress={() => setDefaultAddress(!defaultAddress)}
        style={styles.radioContainer}>
        <Layout
          style={[
            styles.radioIcon,
            defaultAddress ? styles.radioIconActive : styles.radioIconInactive,
            { borderColor: defaultAddress ? themeColor : '#B9B9B9' },
          ]}
        />
        <ThemeText>Set as default address</ThemeText>
      </AppTouchable> */}
    </AppTouchable>
  );
}

const ChooseAddress = ({
  children,
  currentAddress,
  themeColor = '#EF6C00',
  title,
  isModalVisible,
  setModalVisible,
}) => {
  // const [isModalVisible, setModalVisible] = useState(false);
  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };
  return (
    <Layout>
      <Layout style={styles.addressBarContainer}>
        <Layout
          style={[
            styles.addressBar,
            {
              borderColor: `${themeColor}33`,
              backgroundColor: `${themeColor}1A`,
            },
          ]}>
          <Layout style={styles.addressDisplay}>
            <ThemeText size="md" fontFamily="medium">
              {currentAddress ? `Deliver to ${currentAddress}` : 'Address'}
            </ThemeText>
            {currentAddress ? (
              <ThemeText size="sm" numberOfLines={1}>
                {currentAddress}
              </ThemeText>
            ) : null}
          </Layout>
          <AppTouchable
            style={[styles.addressPickButton, { borderColor: themeColor }]}
            onPress={toggleModal}>
            <ThemeText color={themeColor} fontFamily="medium" size="sm">
              {!isEmpty(currentAddress) ? 'CHANGE' : 'CHOOSE ADDRESS'}
            </ThemeText>
          </AppTouchable>
        </Layout>
      </Layout>
      <BottomSheet
        title={title}
        visible={isModalVisible}
        setVisible={setModalVisible}>
        {/* <Layout style={styles.separator} /> */}
        {children}
      </BottomSheet>
    </Layout>
  );
};
const styles = StyleSheet.create({
  container: {},
  addressBarContainer: {
    backgroundColor: '#fff',
    padding: 12,
  },
  addressBar: {
    padding: 6,
    borderWidth: 1,

    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  addressDisplay: {
    flex: 1,
  },
  addressPickButton: {
    borderWidth: 1,

    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 6,
  },
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 18,
    borderTopRightRadius: 18,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  closeIcon: {
    padding: 6,
  },
  addButton: {
    flexDirection: 'row',
    backgroundColor: '#1E232C',
    marginHorizontal: 12,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 6,
  },
  plusIcon: {
    marginRight: 6,
  },
  separator: {
    height: 1,
    backgroundColor: '#ccc',
    margin: 16,
  },
  scrollViewContentContainer: {
    paddingLeft: 16,
    marginBottom: 16,
  },
  addressContainer: {
    borderWidth: 1,
    borderRadius: 12,
    overflow: 'hidden',
    padding: 12,
    backgroundColor: '#F1F5FA',
    marginRight: 12,
    width: windowWidth - 130,
  },
  addressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  removeAddressButton: {
    paddingVertical: 1,
  },
  addressTextContainer: {
    flexGrow: 1,
    marginTop: 6,
  },
  cardActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 12,
  },
  editButton: {
    borderWidth: 1,
    borderColor: '#000',
    padding: 8,
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
    overflow: 'hidden',
  },
  deliverButton: {
    borderWidth: 1,
    borderColor: '#1E232C',
    backgroundColor: '#1E232C',
    padding: 8,
    flex: 3,
    marginLeft: 6,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
    overflow: 'hidden',
  },
  radioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioIcon: {
    height: 15,
    width: 15,
    borderRadius: 100,
    marginRight: 6,
  },
  radioIconInactive: {
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  radioIconActive: {
    backgroundColor: '#fff',
    borderWidth: 4,
  },
});

export default ChooseAddress;
export { AddressCard };
