import { Fragment } from "./types";

export type MetaObjectsParams = {
    type: string;
    perPage?: number;
    itemQuery?: string;
    fragments?: Fragment[];
}
type MetaObjectItem = {
    itemQuery: string;
    fragments?: Fragment[];
}
type MetaObjectItemResponse = MetaObjectItem | undefined;
type MetaObjectQueryStore = {
    [key: string]: MetaObjectItem
}
const metaObjectQueryStore: MetaObjectQueryStore = {};
export function registerMetaObjectQuery(type: string, itemQuery: string, fragments?: Fragment[]) {
    metaObjectQueryStore[type] = {
        itemQuery,
        fragments,
    }
}
export function getMetaObjectQueryFromStore(type: string): MetaObjectItemResponse {
    return metaObjectQueryStore[type];
}
export function getMetaObjectQuery({ type, itemQuery, fragments }: MetaObjectsParams) {
    const metaObjectItem: MetaObjectItemResponse = getMetaObjectQueryFromStore(type);

    let finalItemQuery = itemQuery || metaObjectItem?.itemQuery;
    if (!finalItemQuery) {
        throw new Error(`MetaObject query not found for type: ${type} or itemQuery not provided`);
    }
    let finalFragments: Fragment[] = [];
    if (fragments) {
        finalFragments = [...fragments];
    }
    if (metaObjectItem?.fragments) {
        finalFragments = [...finalFragments, ...metaObjectItem?.fragments];
    }
    const fragmentsQuery = finalFragments?.map(({ name, fragment }) => fragment).join('\n');
    const RAW_QUERY =
        `#graphql
    ${fragmentsQuery ? fragmentsQuery : ''}
    query MetaObjects($type: String!, $perPage: Int!, $after:String) {
     metaobjects(type: $type, first: $perPage, after: $after) {
        pageInfo{
      endCursor
      hasNextPage
      
    }
    nodes{ 
        ${finalItemQuery}
    }
  }
    }`
    return RAW_QUERY;

}