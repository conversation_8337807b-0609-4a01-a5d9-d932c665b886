import React, { useState, useEffect } from 'react';
import { StyleSheet, ActivityIndicator, Dimensions } from 'react-native';
import { Layout, AppTouchable, Badge, AppImage } from '@appmaker-xyz/uikit';
import { useApThemeState } from '@appmaker-xyz/uikit/src/theme/ThemeContext';
import { BottomRight, ProductData, PriceData, ExtraContents } from './TopLeft';
import { InnerBlocks } from './InnerBlocks';
import { useProductHook } from '../useProductHook';
import { BelowTitle } from './BelowTitle';
import { AboveTitle } from './AboveTitle';

let deviceRatio =
  Dimensions.get('window').height / Dimensions.get('window').width;
const isTab = deviceRatio < 1.6;

const LoadingIndicator = () => <ActivityIndicator size={16} color="#1B1B1B" />;

const ProductGridTwo = ({
  clientId,
  attributes,
  pageDispatch,
  onPress,
  onAction,
  blockData,
  innerBlocks = [],
  data = {},
  BlockItem,
  // blockData
}) => {
  // console.log(blockData,'blockItem');
  // console.log(appmaker.applyFilters);

  const {
    priceSale = '0',
    uri,
    title,
    salePrice,
    regularPrice,
    wishList,
    onSale,
    salePercentage,
    in_stock,
    gridViewListing,
    numColumns,
    // saved = false,
    // onSaved,
    thumbnail_meta = {},
    savedItemIds = {},
    cartCountMaps = {},
    cartQuantity,
    attribute,
    appmakerAction,
    productType,
    id,
    average_rating,
    size = '', // sm for small
    new_product,
    show_last_few_remaining,
    last_few_remaining_text,
    bestSeller,
    brandColor,
    titleNumberOfLines,
    __appmakerCustomStyles = {},
  } = attributes;
  const imageRatio = thumbnail_meta.height / thumbnail_meta.width;
  const customHeight = gridViewListing ? 186 : 180;

  const {
    onAddtoCart,
    onSaved,
    onQuantityChange,
    addingTocartLoading,
    currentQuantity,
    quatity,
    quantityLoading,
    addwish,
    setAddwish,
  } = useProductHook({
    attributes,
    onAction,
    blockData,
    pageDispatch,
  });
  const saved = savedItemIds[id] === true;
  const { color, spacing, font } = useApThemeState();
  const styles = allStyles({ color, spacing, imageRatio, customHeight });
  const containerStyle = [styles.container];
  const imageContainerStyle = [styles.imageContainer];

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  useEffect(() => {
    setAddwish(saved);
  }, [saved]);

  let attributeView = null;
  if (attribute) {
    attributeView = attribute.map((value) => {
      return (
        <Layout style={styles.attributesListing}>
          <Badge
            text={value.label}
            status="demiDark"
            style={styles.attributeBadgeStyle}
          />
        </Layout>
      );
    });
  }
  if (size === 'sm') {
    imageContainerStyle.push({
      width: 100,
      height: imageRatio ? imageRatio * 80 : 100,
    });
  }

  if (gridViewListing === true) {
    containerStyle.push({ marginLeft: 0 });
    imageContainerStyle.push({ width: '100%' });
  }
  if (__appmakerCustomStyles) {
    containerStyle.push(__appmakerCustomStyles?.container);
    imageContainerStyle.push(__appmakerCustomStyles?.image_container);
  }
  const wholeContainerStyle = gridViewListing
    ? {
        width: `${100 / numColumns}%`,
        padding: spacing.nano,
      }
    : {
        width: isTab ? 300 : 196,
        marginLeft: spacing.base,
      };

  return (
    <AppTouchable
      onPress={onPressHandle}
      style={wholeContainerStyle}
      clientId={clientId}>
      <Layout style={[styles.container, __appmakerCustomStyles?.container]}>
        <Layout style={imageContainerStyle}>
          <AppImage
            uri={uri}
            style={[styles.productImage, __appmakerCustomStyles?.product_image]}
          />
          <InnerBlocks
            blockData={blockData}
            BlockItem={BlockItem}
            innerBlocks={innerBlocks}
            styles={styles}
            average_rating={average_rating}
            attributes={attributes}
            bestSeller={bestSeller}
            __appmakerCustomStyles={__appmakerCustomStyles}
          />
          <BottomRight
            wishList={wishList}
            onSaved={onSaved}
            addwish={addwish}
            setAddwish={setAddwish}
            color={color}
            md={font.size.md}
            styles={styles}
            __appmakerCustomStyles={__appmakerCustomStyles}
          />
        </Layout>
        <Layout style={styles.contentContainer}>
          <AboveTitle
            blockData={blockData}
            BlockItem={BlockItem}
            innerBlocks={innerBlocks}
            styles={styles}
            average_rating={average_rating}
            attributes={attributes}
            bestSeller={bestSeller}
            __appmakerCustomStyles={__appmakerCustomStyles}
          />
          <ProductData
            title={title}
            in_stock={in_stock}
            new_product={new_product}
            show_last_few_remaining={show_last_few_remaining}
            last_few_remaining_text={last_few_remaining_text}
            brandColor={brandColor}
            productTitle={styles.productTitle}
            __appmakerCustomStyles={__appmakerCustomStyles}
          />
          <PriceData
            salePrice={salePrice}
            regularPrice={regularPrice}
            onSale={onSale}
            salePercentage={salePercentage}
            brandColor={brandColor}
            styles={styles}
            __appmakerCustomStyles={__appmakerCustomStyles}
          />
          <BelowTitle
            blockData={blockData}
            BlockItem={BlockItem}
            innerBlocks={innerBlocks}
            styles={styles}
            average_rating={average_rating}
            attributes={attributes}
            bestSeller={bestSeller}
            __appmakerCustomStyles={__appmakerCustomStyles}
          />
        </Layout>
      </Layout>
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color, imageRatio }) =>
  StyleSheet.create({
    container: {
      flex: 1,
      marginBottom: spacing.base,
      position: 'relative',
    },
    imageContainer: {
      backgroundColor: color.white,
      borderRadius: spacing.small,
      aspectRatio: imageRatio ? 1 / imageRatio : 1,
      position: 'relative',
    },
    productImage: {
      width: '100%',
      height: '100%',
      overflow: 'hidden',
    },
    overlayContainer: {
      position: 'absolute',
      zIndex: 10,
      flexDirection: 'row',
      bottom: spacing.base,
      right: spacing.base,
    },
    addToCart: {
      backgroundColor: color.white,
      padding: 8,
      borderRadius: spacing.lg,
      marginLeft: 10,
    },
    wishListIcon: {
      backgroundColor: color.white,
      padding: 8,
      borderRadius: spacing.lg,
    },
    shadow: {
      shadowColor: color.demiDark,
      shadowOffset: {
        width: 0,
        height: 8,
      },
      shadowOpacity: 0.3,
      shadowRadius: spacing.small,
      elevation: spacing.small,
    },
    contentContainer: {
      marginTop: spacing.nano,
      flex: 1,
      position: 'relative',
    },
    productTitle: {
      flex: 1,
      marginBottom: spacing.nano,
    },
    priceDataContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-start',
      flexWrap: 'wrap',
    },
    offerPrice: {
      textDecorationLine: 'line-through',
      marginHorizontal: spacing.nano,
    },
    saleBadgeStyle: {
      marginRight: spacing.nano,
    },
    stockOutBadgeStyle: {},
    topLeftBlocks: {
      flexDirection: 'row',
      position: 'absolute',
      flexWrap: 'wrap',
      left: 4,
      top: 4,
    },
    extraContentContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
  });

export default ProductGridTwo;
