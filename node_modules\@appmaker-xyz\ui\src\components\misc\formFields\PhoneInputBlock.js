import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, Platform } from 'react-native';
import { usePageState } from '@appmaker-xyz/core';
import PhoneInput from 'react-native-phone-number-input';
import { Layout, ThemeText } from '../../atoms';

const PhoneInputBlock = ({ attributes, coreDispatch }) => {
  let {
    appmakerAction,
    defaultCountryCode,
    name,
    disabled,
    defaultValue,
    __appmakerCustomStyles = {},
  } = attributes;
  const [phone, setPhone] = useState('');
  const [formatedPhone, setFormatedPhone] = useState('');
  const phoneInput = useRef(null);
  const parentName = attributes.parentName || '_formData';

  const inputValue = usePageState((state) => {
    return state[parentName] && state[parentName][name]
      ? state[parentName][name]
      : '';
  });
  function onChangeInput(_name, valueText) {
    coreDispatch({
      type: 'SET_VALUE',
      name: _name,
      value: valueText,
      parent: parentName,
    });
  }
  useEffect(() => {
    onChangeInput(name, defaultValue);
  }, [defaultValue]);

  useEffect(() => {
    if (formatedPhone || phone) {
      onChangeInput(name, {
        phone,
        formated: formatedPhone,
      });
    }
  }, [formatedPhone, phone]);
  return (
    <Layout style={styles.otpView}>
      {disabled ? (
        <Layout style={styles.disabledText}>
          <ThemeText>{defaultValue?.formated}</ThemeText>
        </Layout>
      ) : (
        <PhoneInput
          ref={phoneInput}
          defaultValue={inputValue}
          defaultCode={'IN'}
          layout="first"
          onChangeText={(text) => {
            setPhone(text);
          }}
          onChangeFormattedText={(text) => {
            setFormatedPhone(text);
          }}
          textContainerStyle={styles.textContainerMain}
          containerStyle={[
            styles.phoneNumberContainer,
            __appmakerCustomStyles?.input?.container,
          ]}
          textInputStyle={styles.textInputStyle}
          codeTextStyle={styles.font}
        />
      )}
    </Layout>
  );
};

const height = Platform.OS === 'ios' ? 54 : 50;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    padding: 12,
    flex: 1,
    alignItems: 'center',
    height: height,
  },
  phoneNumberContainer: {
    height: height,
    marginBottom: 20,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#212121',
    width: '100%',
    overflow: 'hidden',
  },
  textInputStyle: {
    height: height,
  },
  otpView: {
    position: 'relative',
    width: '100%',
  },
  logo: {
    width: '50%',
    height: 80,
    alignSelf: 'center',
    resizeMode: 'contain',
    marginVertical: 16,
  },
  signIn: {
    width: '100%',
    alignItems: 'center',
  },
  signInQue: {
    marginBottom: 6,
  },
  font: {
    lineHeight: Platform.OS === 'ios' ? 20 : 18,
  },
  textContainerMain: {
    height: height,
  },
  disabledText: {
    marginVertical: 12,
  },
});

export default PhoneInputBlock;
