import React from 'react';
import {StyleSheet, ScrollView} from 'react-native';
import {useApThemeState} from '../../../theme/ThemeContext';
import {
  Layout,
  Input,
  Button,
  AppmakerText,
  AppImage,
} from '../../../components';
import {Stepper} from '../../molecules';

const CartItem = () => {
  const {color, spacing} = useApThemeState();
  const styles = allStyles({color, spacing});
  return (
    <Layout style={styles.cartItem}>
      <Layout style={styles.cartItemTitle}>
        <AppmakerText category="bodyParagraph" status="demiDark">
          Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.
        </AppmakerText>
        <AppmakerText category="highlighter2" status="demiDark">
          Lorem ipsum dolor sit amet.
        </AppmakerText>
        <AppmakerText category="bodyParagraphBold">$40.41</AppmakerText>
      </Layout>
      <Stepper count={1} />
    </Layout>
  );
};

const GroceryCart = () => {
  const {color, spacing} = useApThemeState();
  const styles = allStyles({color, spacing});
  return (
    <Layout style={styles.container}>
      <ScrollView style={styles.content}>
        <Layout style={styles.card}>
          <Layout style={styles.cardTitle}>
            <AppmakerText category="highlighter1" status="demiDark">
              CART ITEMS
            </AppmakerText>
            <Button small status="white">
              <AppmakerText category="highlighter1" status="primary">
                +Add More
              </AppmakerText>
            </Button>
          </Layout>
          <CartItem />
          <CartItem />
          <CartItem />
        </Layout>
      </ScrollView>
      <Layout style={styles.bottomBar}>
        <Button status="primary" block>
          Proceed to Checkout
        </Button>
      </Layout>
    </Layout>
  );
};

const allStyles = ({spacing, color}) =>
  StyleSheet.create({
    container: {
      height: '100%',
    },
    content: {
      backgroundColor: color.light,
      flex: 1,
    },
    card: {
      padding: spacing.base,
      backgroundColor: color.white,
    },
    cardTitle: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: spacing.mini,
    },
    cartItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: spacing.small,
      paddingBottom: spacing.small,
      borderBottomWidth: 1,
      borderBottomColor: color.light,
    },
    cartItemTitle: {
      flex: 1,
      marginEnd: spacing.mini,
    },
    bottomBar: {
      padding: spacing.base,
      backgroundColor: color.white,
      borderTopColor: color.light,
      borderTopWidth: 1,
    },
  });

export default GroceryCart;
