import React from 'react';
import {StyleSheet, Platform, KeyboardAvoidingView} from 'react-native';
import {useApThemeState} from '../../../theme/ThemeContext';
import {
  Layout,
  Input,
  Button,
  AppmakerText,
  AppImage,
} from '../../../components';

const SignUp = (props) => {
  const {
    showMobileField = true,
    onChangeUsername,
    onChangePassword,
    onChangeRePassword,
    onChangeEmail,
    onRegister,
    logoUrl,
    registerButtonLeft,
    registerButtonText,
    onLogin,
  } = props;
  const {color, spacing} = useApThemeState();
  const styles = allStyles({color, spacing});
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS == 'ios' ? 'padding' : 'height'}>
      <Layout style={styles.signUpView}>
        {logoUrl && <AppImage uri={logoUrl} style={styles.logo} />}
        <Input
          label="Username*"
          leftIcon="user"
          onChangeText={(v) => onChangeUsername && onChangeUsername(v)}
        />
        <Input
          label="Email*"
          leftIcon="mail"
          onChangeText={(v) => onChangeEmail && onChangeEmail(v)}
        />
        <Input
          label="Password*"
          leftIcon="key"
          type="password"
          onChangeText={(v) => onChangePassword(v)}
        />
        <Input
          label="Repeat Password*"
          leftIcon="key"
          type="password"
          onChangeText={(v) => onChangeRePassword && onChangeRePassword(v)}
        />
        {showMobileField && <Input label="Mobile Number" leftIcon="phone" />}
        <Button
          status="dark"
          accessoryLeft={registerButtonLeft}
          onPress={onRegister}>
          {registerButtonText || 'Register'}
        </Button>
      </Layout>

      <Layout style={styles.signIn}>
        <AppmakerText
          category="bodyParagraph"
          status="demiDark"
          style={styles.signInQue}>
          Already have an account?
        </AppmakerText>
        <Button status="light" small onPress={onLogin}>
          SIGN IN
        </Button>
      </Layout>
    </KeyboardAvoidingView>
  );
};

const allStyles = ({spacing, color}) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      flex: 1,
      alignItems: 'center',
    },
    signUpView: {
      position: 'relative',
      width: '100%',
      flex: 1,
      padding: spacing.base,
    },
    logo: {
      width: '50%',
      height: 80,
      alignSelf: 'center',
      resizeMode: 'contain',
      marginVertical: spacing.md,
    },
    signIn: {
      flexDirection: 'row',
      width: '100%',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: color.light,
      borderRadius: spacing.nano,
    },
    signInQue: {
      marginRight: spacing.mini,
    },
  });

export default SignUp;
