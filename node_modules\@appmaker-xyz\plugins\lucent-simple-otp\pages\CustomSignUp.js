import { appSettings } from '@appmaker-xyz/core';
import { color, spacing } from '@appmaker-xyz/uikit/src/styles/index';

const CustomSignUp = {
  type: 'normal',
  title: 'Register Page',
  attributes: {
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/blocksView',
      attributes: {
        renderType: 'normal',
        rootContainerStyle: {
          flex: 5,
        },
        contentContainerStyle: {
          flex: 1,
          justifyContent: 'center',
          paddingHorizontal: spacing.base,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/remoteImage',
          attributes: {
            name: 'LOGIN_LOGO',
            style: {
              width: 150,
              height: 50,
              alignSelf: 'center',
              resizeMode: 'contain',
              marginBottom: spacing.lg,
            },
          },
        },
        {
          name: 'appmaker/form-input-hidden',
          clientId: 'address-id',
          attributes: {
            value: '{{blockItem.type}}',
            name: 'type',
          },
        },
        {
          name: 'appmaker/form-input-hidden',
          clientId: 'otp_id',
          attributes: {
            value: '{{blockItem.otp_id}}',
            name: 'otp_id',
          },
        },
        {
          name: 'appmaker/floating-label-input',
          attributes: {
            label: 'First name',
            leftIcon: 'user',
            name: 'first_name',
            status: 'demiDark',
            autoCompleteType: 'name',
            defaultValue: '{{ blockItem.first_name }}',
          },
        },
        {
          name: 'appmaker/floating-label-input',
          attributes: {
            label: 'Last name',
            leftIcon: 'user',
            name: 'last_name',
            status: 'demiDark',
            autoCompleteType: 'name',
            defaultValue: '{{ blockItem.last_name }}',
          },
        },
        {
          name: 'appmaker/floating-label-input',
          attributes: {
            label: 'Email',
            leftIcon: 'mail',
            name: 'email',
            status: 'demiDark',
            disabled: '{{ blockItem.email_disabled }}',
            autoCompleteType: 'email',
            __appmakerStylesClassName: 'inputCustom',
            defaultValue: '{{ blockItem.email }}',
          },
        },
        {
          name: 'appmaker/phone-input',
          attributes: {
            label: 'Phone',
            name: 'phone',
            disabled: '{{ blockItem.phone_disabled }}',
            defaultValue: '{{ blockItem.phone }}',
          },
        },
        {
          name: 'appmaker/ActionButton',
          clientId: 'submit-register',
          attributes: {
            appmakerAction: {
              action: 'LUCENT_UPDATE_USER',
            },
            content: 'Create Account',
            baseSize: true,
            wholeContainerStyle: {
              backgroundColor: appSettings.getOption('primary_button_color'),
            },
            fontColor: appSettings.getOption('primary_button_text_color'),
            __appmakerStylesClassName: 'registerButton',
          },
          contextValues: true,
        },
      ],
    },
  ],
};
export default CustomSignUp;
