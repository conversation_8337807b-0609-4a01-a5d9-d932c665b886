{"version": 3, "names": ["_generated", "require", "_default", "createTypeAnnotationBasedOnTypeof", "exports", "default", "type", "stringTypeAnnotation", "numberTypeAnnotation", "voidTypeAnnotation", "booleanTypeAnnotation", "genericTypeAnnotation", "identifier", "anyTypeAnnotation", "Error"], "sources": ["../../../src/builders/flow/createTypeAnnotationBasedOnTypeof.ts"], "sourcesContent": ["import {\n  anyTypeAnnotation,\n  stringTypeAnnotation,\n  numberTypeAnnotation,\n  voidTypeAnnotation,\n  booleanTypeAnnotation,\n  genericTypeAnnotation,\n  identifier,\n} from \"../generated\";\nimport type * as t from \"../..\";\n\nexport default createTypeAnnotationBasedOnTypeof as {\n  (type: \"string\"): t.StringTypeAnnotation;\n  (type: \"number\"): t.NumberTypeAnnotation;\n  (type: \"undefined\"): t.VoidTypeAnnotation;\n  (type: \"boolean\"): t.<PERSON>anTypeAnnotation;\n  (type: \"function\"): t.GenericTypeAnnotation;\n  (type: \"object\"): t.GenericTypeAnnotation;\n  (type: \"symbol\"): t.GenericTypeAnnotation;\n  (type: \"bigint\"): t.AnyTypeAnnotation;\n};\n\n/**\n * Create a type annotation based on typeof expression.\n */\nfunction createTypeAnnotationBasedOnTypeof(type: string): t.FlowType {\n  switch (type) {\n    case \"string\":\n      return stringTypeAnnotation();\n    case \"number\":\n      return numberTypeAnnotation();\n    case \"undefined\":\n      return voidTypeAnnotation();\n    case \"boolean\":\n      return booleanTypeAnnotation();\n    case \"function\":\n      return genericTypeAnnotation(identifier(\"Function\"));\n    case \"object\":\n      return genericTypeAnnotation(identifier(\"Object\"));\n    case \"symbol\":\n      return genericTypeAnnotation(identifier(\"Symbol\"));\n    case \"bigint\":\n      // todo: use BigInt annotation when Flow supports BigInt\n      // https://github.com/facebook/flow/issues/6639\n      return anyTypeAnnotation();\n  }\n  throw new Error(\"Invalid typeof value: \" + type);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAQsB,IAAAC,QAAA,GAGPC,iCAAiC;AAAAC,OAAA,CAAAC,OAAA,GAAAH,QAAA;AAchD,SAASC,iCAAiCA,CAACG,IAAY,EAAc;EACnE,QAAQA,IAAI;IACV,KAAK,QAAQ;MACX,OAAO,IAAAC,+BAAoB,GAAE;IAC/B,KAAK,QAAQ;MACX,OAAO,IAAAC,+BAAoB,GAAE;IAC/B,KAAK,WAAW;MACd,OAAO,IAAAC,6BAAkB,GAAE;IAC7B,KAAK,SAAS;MACZ,OAAO,IAAAC,gCAAqB,GAAE;IAChC,KAAK,UAAU;MACb,OAAO,IAAAC,gCAAqB,EAAC,IAAAC,qBAAU,EAAC,UAAU,CAAC,CAAC;IACtD,KAAK,QAAQ;MACX,OAAO,IAAAD,gCAAqB,EAAC,IAAAC,qBAAU,EAAC,QAAQ,CAAC,CAAC;IACpD,KAAK,QAAQ;MACX,OAAO,IAAAD,gCAAqB,EAAC,IAAAC,qBAAU,EAAC,QAAQ,CAAC,CAAC;IACpD,KAAK,QAAQ;MAGX,OAAO,IAAAC,4BAAiB,GAAE;EAAC;EAE/B,MAAM,IAAIC,KAAK,CAAC,wBAAwB,GAAGR,IAAI,CAAC;AAClD"}