import React from 'react';
import { StyleSheet } from 'react-native';
import {
  Layout,
  ThemeText,
  Button,
  Badge,
  AppTouchable,
} from '@appmaker-xyz/ui';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/Feather';

const ShopifyReview = ({ attributes, onAction }) => {
  const { t } = useTranslation();
  const {
    count,
    average_rating,
    title,
    buttonTitle,
    buttonAction,
    appmakerAction,
  } = attributes;
  const titleText = t(buttonTitle);
  const ratingBadgeColor = () => {
    let rating = average_rating;
    switch (true) {
      case rating >= '3.5':
        return '#2FA478';
      case rating >= '2.5':
        return '#FFC829';
      default:
        return '#1B1B1B';
    }
  };

  return (
    <Layout style={styles.container}>
      <AppTouchable onPress={() => onAction(appmakerAction)}>
        <ThemeText fontFamily="medium" size="md">
          {title}
          <Icon name="chevron-right" />
        </ThemeText>
        {average_rating && (
          <Badge
            text={average_rating}
            color={ratingBadgeColor()}
            iconName="star"
          />
        )}
        {count && (
          <ThemeText fontFamily="medium" size="sm">
            {count} Reviews
          </ThemeText>
        )}
      </AppTouchable>
      {buttonTitle ? (
        <Button
          title={titleText}
          onPress={() => onAction(buttonAction)}
          size="sm"
          isOutline={true}
        />
      ) : null}
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    marginBottom: 4,
  },
});

export default ShopifyReview;
