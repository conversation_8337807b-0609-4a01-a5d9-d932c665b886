declare module '@appmaker-xyz/shopify' {
  type SelectedFilterValue = {
    count: number;
    id: string;
    input: string;
    label: string;
  };

  type PriceRangeFilterValue = {
    type: 'PRICE_RANGE';
    min: number;
    max: number;
  };

  type SelectedFilterGroup = {
    [valueId: string]: SelectedFilterValue | PriceRangeFilterValue;
  };

  type SelectedFilters = {
    [filterKey: string]: SelectedFilterGroup;
  };

  interface FilterOptionsReturn {
    selectFilter: (
      filterKey: string,
      filterOptionKey: string,
      filterOption: FilterValue,
    ) => void;
    removeFilter: (filterKey: string, filterOptionKey: string) => void;
    clearSelectedFilters: () => void;
    selectedFilters: SelectedFilters | null;
    hasAnyFilterSelected: boolean;
  }

  function useFilterOptions(): FilterOptionsReturn;
}
