import { runDataSource, useDataSourceV2, useUser } from "@appmaker-xyz/core";
import { useCallback } from "react";
type Address = any;
type AddressListOutput = {
    addresses: Address[];
    loading: boolean;
    error: string;
}
type AddressActions = {
    openCreateAddressForm: () => void;
    openEditAddressForm: (address: Address) => void;
    deleteAddress: (id: string) => void;
    setDefaultAddress: (id: string) => void;
}
export function useAddressActions() {
    const { user } = useUser();
    const dataSource = {
        attributes: {},
        source: 'shopify',
    };
    const deleteAddress = useCallback(async (id: string) => {
        await runDataSource(
            {
                dataSource,
            },
            {
                methodName: 'customerAddressDelete',
                params: {
                    accessToken: user?.accessToken,
                    id: id,
                },
            },
        );
    }, [
        user,
    ]);
    const setDefaultAddress = useCallback(async (id: string) => {
        const [customerDefaultAddressUpdate] = await runDataSource(
            {
                dataSource,
            },
            {
                methodName: 'customerDefaultAddressUpdate',
                params: {
                    addressId: id,
                    customerAccessToken: user.accessToken,
                },
            },
        );
    }, [
        user,
    ]);


    return {
        deleteAddress,
        setDefaultAddress
    }

}
export function useAddressList() {
    const { user } = useUser();
    const dataSource = {
        source: 'shopify',
        methodName: 'customerAddressList',
        dataExtractor: (data) => data?.data?.data?.customer,
        params: user,
    };
    const [
        queryResult,
        { item }
    ] = useDataSourceV2({ dataSource, enabled: true });
    return {
        addressList: item?.addresses?.edges?.map((edge) => edge.node),
        defaultAddressId: item?.defaultAddress?.id,
        ...queryResult,
    }

}