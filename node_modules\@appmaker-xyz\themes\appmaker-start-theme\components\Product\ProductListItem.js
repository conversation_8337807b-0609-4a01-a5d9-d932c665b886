import { StyleSheet, View, Text, Image, Pressable } from 'react-native';
import React from 'react';
import { useProductListItem } from '@appmaker-xyz/shopify/hooks/useProductListItem';
import useProductWishList from '@appmaker-xyz/shopify/hooks/useProductWishList';
import Icon from 'react-native-vector-icons/AntDesign';
import { AppTouchable, Layout, ThemeText, Badge } from '../../uikit';

function TopLeftContainer({ children }) {
  return <Layout style={styles.topLeftContainer}>{children}</Layout>;
}

function BottomRightContainer({ children }) {
  return <Layout style={styles.bottomRightContainer}>{children}</Layout>;
}

export default function ProductListItem(props) {
  const {
    title,
    imageUrl,
    regularPrice,
    salePrice,
    imageAspectRatio,
    openProduct,
    containerWidth,
    gridViewListing,
    salePercentage,
  } = useProductListItem(props);
  const { toggleWishList, isSaved } = useProductWishList(props);
  return (
    <AppTouchable
      onPress={openProduct}
      style={[
        styles.container,
        { width: containerWidth, marginLeft: gridViewListing ? 0 : 12 },
      ]}>
      <Layout
        style={[styles.imageContainer, { aspectRatio: 1 / imageAspectRatio }]}>
        <Image source={{ uri: imageUrl }} style={styles.image} />
        <BottomRightContainer>
          <Icon
            name={isSaved ? 'heart' : 'hearto'}
            size={16}
            onPress={toggleWishList}
            style={styles.wishListIcon}
          />
        </BottomRightContainer>
        {salePercentage ? (
          <TopLeftContainer>
            <Badge color="#111827">{salePercentage} off</Badge>
          </TopLeftContainer>
        ) : null}
      </Layout>
      <Layout style={styles.textContainer}>
        <ThemeText
          fontFamily={'medium'}
          size={'md'}
          color={'#111827'}
          numberOfLines={1}>
          {title}
        </ThemeText>
        <Layout style={styles.priceContainer}>
          <ThemeText fontFamily={'bold'} color={'#111827'}>
            {salePrice}
          </ThemeText>
          <ThemeText fontFamily={'regular'} style={styles.regularPriceText}>
            {regularPrice}
          </ThemeText>
        </Layout>
      </Layout>
    </AppTouchable>
  );
}

const styles = StyleSheet.create({
  container: {},
  imageContainer: {
    position: 'relative',
  },
  image: {
    height: '100%',
    width: '100%',
  },
  topLeftContainer: {
    position: 'absolute',
    top: 4,
    left: 4,
  },
  bottomRightContainer: {
    position: 'absolute',
    right: 6,
    bottom: 6,
  },
  wishListIcon: {
    padding: 8,
    backgroundColor: 'rgba(255,255,255,0.6)',
    borderRadius: 20,
  },
  textContainer: {
    flex: 1,
    justifyContent: 'space-between',
    paddingTop: 4,
    paddingBottom: 6,
    paddingHorizontal: 2,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginTop: 4,
  },
  regularPriceText: {
    marginLeft: 4,
    color: '#DC2626',
    textDecorationLine: 'line-through',
  },
});
