import React, { useState } from 'react';
import { <PERSON><PERSON>ist, <PERSON>18nManager, StyleSheet } from 'react-native';
import { Layout, ThemeText, AppTouchable, AppImage } from '@appmaker-xyz/ui';
import { Images } from '../../../../assets/images';
import SizeChart from './SizeChart';

function formateColorCode(colorCode) {
  if (colorCode?.includes('#')) {
    return colorCode;
  }
  return colorCode?.toLowerCase();
}
const ProductSwatch = ({ option }) => {
  if (option?.imageUrl) {
    return <AppImage uri={option.imageUrl} style={styles.imageOption} />;
  }
  return (
    <Layout
      style={[
        styles.swatch,
        {
          backgroundColor: formateColorCode(option?.colorCode),
        },
      ]}
    />
  );
};

export default function ProductOptions(props) {
  const {
    name,
    options,
    setOption,
    selectedOption,
    isOptionAvailable,
    product,
    sizeChartUrl,
    sizeChartVariationNames,
    scrollable = true,
  } = props;
  function textColor(option) {
    if (selectedOption === option.value) {
      return '#ffffff';
    }
    if (!isOptionAvailable(option.value)) {
      return '#A9AEB7';
    }
    return '#4F4F4F';
  }
  const [showSizeChart, setShowSizeChart] = useState(false);
  return (
    <Layout testId={props?.testId} style={styles.container}>
      <Layout style={styles.titleContainer}>
        <ThemeText size="lg" fontFamily="medium">
          {name}
        </ThemeText>
        {sizeChartVariationNames?.some?.((i) => i?.name === name) ? (
          <AppTouchable
            onPress={() => {
              setShowSizeChart(!showSizeChart);
            }}>
            <SizeChart
              visible={showSizeChart}
              onClose={() => {
                setShowSizeChart(false);
              }}
              url={sizeChartUrl}
            />
            <ThemeText>Size Chart</ThemeText>
          </AppTouchable>
        ) : null}
      </Layout>
      <FlatList
        horizontal={scrollable ? true : false}
        style={scrollable ? null : styles.stackedContentContainer}
        contentContainerStyle={scrollable ? styles.contentContainer : null}
        data={options}
        renderItem={({ item: option }) => {
          const isSelected = selectedOption === option.value;
          return (
            <AppTouchable
              key={`options-${option.key}`}
              onPress={() => setOption(option)}
              disabled={!isOptionAvailable(option.value)}
              style={[
                styles.itemContainer,
                isSelected
                  ? styles.itemContainerSelected
                  : styles.itemContainerUnselected,
                option?.type === 'color-swatch' ? styles.roundedFull : null,
                scrollable ? null : styles.marginBottom,
              ]}
              testId={`${props?.testId}-${option.key}`}>
              {!isOptionAvailable(option.value) ? (
                <AppImage src={Images.cross_line} style={styles.muteImage} />
              ) : null}
              {option?.type === 'color-swatch' ? (
                <ProductSwatch option={option} />
              ) : (
                <ThemeText color={textColor(option)} style={styles.itemText}>
                  {option.value}
                </ThemeText>
              )}
            </AppTouchable>
          );
        }}
        keyExtractor={(item) => `options-${item.key}`}
      />
    </Layout>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    marginBottom: 4,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingTop: 12,
  },
  contentContainer: {
    paddingHorizontal: 8,
    paddingVertical: 12,
    flexGrow: I18nManager.isRTL ? 1 : undefined,
  },
  stackedContentContainer: {
    paddingHorizontal: 8,
    paddingTop: 12,
    paddingBottom: 4,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  marginBottom: {
    marginBottom: 8,
  },
  itemContainer: {
    marginHorizontal: 4,
    borderRadius: 6,
    borderWidth: 2,
    position: 'relative',
    overflow: 'hidden',
  },
  itemText: { margin: 8 },
  muteImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    zIndex: 10,
    resizeMode: 'stretch',
  },
  itemContainerUnselected: {
    borderColor: '#A9AEB7',
  },
  itemContainerSelected: {
    borderColor: '#1b1b1b',
    backgroundColor: '#1b1b1b',
  },
  imageOption: {
    width: 36,
    height: 36,
    resizeMode: 'cover',
    borderRadius: 20,
    overflow: 'hidden',
  },
  swatch: {
    width: 36,
    height: 36,
    overflow: 'hidden',
    borderRadius: 20,
  },
  roundedFull: {
    borderRadius: 30,
    padding: 1,
  },
});
