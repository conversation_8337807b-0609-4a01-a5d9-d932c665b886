{"version": 3, "names": ["_traverseNode", "require", "_index", "call", "key", "opts", "debug", "node", "_call", "type", "fns", "fn", "ret", "state", "then", "Error", "_traverseFlags", "isDenylisted", "_this$opts$denylist", "denylist", "blacklist", "indexOf", "restoreContext", "path", "context", "visit", "shouldSkip", "currentContext", "shouldStop", "traverseNode", "scope", "<PERSON><PERSON><PERSON><PERSON>", "skip", "<PERSON><PERSON><PERSON>", "stop", "SHOULD_SKIP", "SHOULD_STOP", "setScope", "noScope", "parentPath", "<PERSON><PERSON><PERSON>", "isMethod", "isSwitchStatement", "target", "getScope", "init", "setContext", "resync", "removed", "_resyncParent", "_resyncList", "_resync<PERSON>ey", "parent", "container", "Array", "isArray", "i", "length", "<PERSON><PERSON><PERSON>", "Object", "keys", "inList", "newContainer", "_resyncRemoved", "_mark<PERSON><PERSON>oved", "popContext", "contexts", "pop", "undefined", "pushContext", "push", "setup", "_this$node", "requeue", "pathToQueue", "maybeQueue", "_getQueueContexts"], "sources": ["../../src/path/context.ts"], "sourcesContent": ["// This file contains methods responsible for maintaining a TraversalContext.\n\nimport { traverseNode } from \"../traverse-node\";\nimport { SHOULD_SKIP, SHOULD_STOP } from \"./index\";\nimport type TraversalContext from \"../context\";\nimport type NodePath from \"./index\";\nimport type * as t from \"@babel/types\";\n\nexport function call(this: NodePath, key: string): boolean {\n  const opts = this.opts;\n\n  this.debug(key);\n\n  if (this.node) {\n    if (this._call(opts[key])) return true;\n  }\n\n  if (this.node) {\n    return this._call(opts[this.node.type] && opts[this.node.type][key]);\n  }\n\n  return false;\n}\n\nexport function _call(this: NodePath, fns?: Array<Function>): boolean {\n  if (!fns) return false;\n\n  for (const fn of fns) {\n    if (!fn) continue;\n\n    const node = this.node;\n    if (!node) return true;\n\n    const ret = fn.call(this.state, this, this.state);\n    if (ret && typeof ret === \"object\" && typeof ret.then === \"function\") {\n      throw new Error(\n        `You appear to be using a plugin with an async traversal visitor, ` +\n          `which your current version of Babel does not support. ` +\n          `If you're using a published plugin, you may need to upgrade ` +\n          `your @babel/core version.`,\n      );\n    }\n    if (ret) {\n      throw new Error(`Unexpected return value from visitor method ${fn}`);\n    }\n\n    // node has been replaced, it will have been requeued\n    if (this.node !== node) return true;\n\n    // this.shouldSkip || this.shouldStop || this.removed\n    if (this._traverseFlags > 0) return true;\n  }\n\n  return false;\n}\n\nexport function isDenylisted(this: NodePath): boolean {\n  const denylist = this.opts.denylist ?? this.opts.blacklist;\n  return denylist && denylist.indexOf(this.node.type) > -1;\n}\n\n// TODO: Remove in Babel 8\nexport { isDenylisted as isBlacklisted };\n\nfunction restoreContext(path: NodePath, context: TraversalContext) {\n  if (path.context !== context) {\n    path.context = context;\n    path.state = context.state;\n    path.opts = context.opts;\n  }\n}\n\nexport function visit(this: NodePath): boolean {\n  if (!this.node) {\n    return false;\n  }\n\n  if (this.isDenylisted()) {\n    return false;\n  }\n\n  if (this.opts.shouldSkip && this.opts.shouldSkip(this)) {\n    return false;\n  }\n\n  const currentContext = this.context;\n  // Note: We need to check \"this.shouldSkip\" first because\n  // another visitor can set it to true. Usually .shouldSkip is false\n  // before calling the enter visitor, but it can be true in case of\n  // a requeued node (e.g. by .replaceWith()) that is then marked\n  // with .skip().\n  if (this.shouldSkip || this.call(\"enter\")) {\n    this.debug(\"Skip...\");\n    return this.shouldStop;\n  }\n  restoreContext(this, currentContext);\n\n  this.debug(\"Recursing into...\");\n  this.shouldStop = traverseNode(\n    this.node,\n    this.opts,\n    this.scope,\n    this.state,\n    this,\n    this.skipKeys,\n  );\n\n  restoreContext(this, currentContext);\n\n  this.call(\"exit\");\n\n  return this.shouldStop;\n}\n\nexport function skip(this: NodePath) {\n  this.shouldSkip = true;\n}\n\nexport function skipKey(this: NodePath, key: string) {\n  if (this.skipKeys == null) {\n    this.skipKeys = {};\n  }\n  this.skipKeys[key] = true;\n}\n\nexport function stop(this: NodePath) {\n  // this.shouldSkip = true; this.shouldStop = true;\n  this._traverseFlags |= SHOULD_SKIP | SHOULD_STOP;\n}\n\nexport function setScope(this: NodePath) {\n  if (this.opts && this.opts.noScope) return;\n\n  let path = this.parentPath;\n\n  if (\n    // Skip method scope if is computed method key or decorator expression\n    ((this.key === \"key\" || this.listKey === \"decorators\") &&\n      path.isMethod()) ||\n    // Skip switch scope if for discriminant (`x` in `switch (x) {}`).\n    (this.key === \"discriminant\" && path.isSwitchStatement())\n  ) {\n    path = path.parentPath;\n  }\n\n  let target;\n  while (path && !target) {\n    if (path.opts && path.opts.noScope) return;\n\n    target = path.scope;\n    path = path.parentPath;\n  }\n\n  this.scope = this.getScope(target);\n  if (this.scope) this.scope.init();\n}\n\nexport function setContext<S = unknown>(\n  this: NodePath,\n  context?: TraversalContext<S>,\n) {\n  if (this.skipKeys != null) {\n    this.skipKeys = {};\n  }\n  // this.shouldSkip = false; this.shouldStop = false; this.removed = false;\n  this._traverseFlags = 0;\n\n  if (context) {\n    this.context = context;\n    this.state = context.state;\n    this.opts = context.opts;\n  }\n\n  this.setScope();\n\n  return this;\n}\n\n/**\n * Here we resync the node paths `key` and `container`. If they've changed according\n * to what we have stored internally then we attempt to resync by crawling and looking\n * for the new values.\n */\n\nexport function resync(this: NodePath) {\n  if (this.removed) return;\n\n  this._resyncParent();\n  this._resyncList();\n  this._resyncKey();\n  //this._resyncRemoved();\n}\n\nexport function _resyncParent(this: NodePath) {\n  if (this.parentPath) {\n    this.parent = this.parentPath.node;\n  }\n}\n\nexport function _resyncKey(this: NodePath) {\n  if (!this.container) return;\n\n  if (\n    this.node ===\n    // @ts-expect-error this.key should present in this.container\n    this.container[this.key]\n  ) {\n    return;\n  }\n\n  // grrr, path key is out of sync. this is likely due to a modification to the AST\n  // not done through our path APIs\n\n  if (Array.isArray(this.container)) {\n    for (let i = 0; i < this.container.length; i++) {\n      if (this.container[i] === this.node) {\n        this.setKey(i);\n        return;\n      }\n    }\n  } else {\n    for (const key of Object.keys(this.container)) {\n      // @ts-expect-error this.key should present in this.container\n      if (this.container[key] === this.node) {\n        this.setKey(key);\n        return;\n      }\n    }\n  }\n\n  // ¯\\_(ツ)_/¯ who knows where it's gone lol\n  this.key = null;\n}\n\nexport function _resyncList(this: NodePath) {\n  if (!this.parent || !this.inList) return;\n\n  const newContainer =\n    // @ts-expect-error this.listKey should present in this.parent\n    this.parent[this.listKey];\n  if (this.container === newContainer) return;\n\n  // container is out of sync. this is likely the result of it being reassigned\n  this.container = newContainer || null;\n}\n\nexport function _resyncRemoved(this: NodePath) {\n  if (\n    this.key == null ||\n    !this.container ||\n    // @ts-expect-error this.key should present in this.container\n    this.container[this.key] !== this.node\n  ) {\n    this._markRemoved();\n  }\n}\n\nexport function popContext(this: NodePath) {\n  this.contexts.pop();\n  if (this.contexts.length > 0) {\n    this.setContext(this.contexts[this.contexts.length - 1]);\n  } else {\n    this.setContext(undefined);\n  }\n}\n\nexport function pushContext(this: NodePath, context: TraversalContext) {\n  this.contexts.push(context);\n  this.setContext(context);\n}\n\nexport function setup(\n  this: NodePath,\n  parentPath: NodePath | undefined,\n  container: t.Node,\n  listKey: string,\n  key: string | number,\n) {\n  this.listKey = listKey;\n  this.container = container;\n\n  this.parentPath = parentPath || this.parentPath;\n  this.setKey(key);\n}\n\nexport function setKey(this: NodePath, key: string | number) {\n  this.key = key;\n  this.node =\n    // @ts-expect-error this.key must present in this.container\n    this.container[this.key];\n  this.type = this.node?.type;\n}\n\nexport function requeue(this: NodePath, pathToQueue = this) {\n  if (pathToQueue.removed) return;\n\n  // If a path is skipped, and then replaced with a\n  // new one, the new one shouldn't probably be skipped.\n  if (process.env.BABEL_8_BREAKING) {\n    pathToQueue.shouldSkip = false;\n  }\n\n  // TODO(loganfsmyth): This should be switched back to queue in parent contexts\n  // automatically once #2892 and #4135 have been resolved. See #4140.\n  // let contexts = this._getQueueContexts();\n  const contexts = this.contexts;\n\n  for (const context of contexts) {\n    context.maybeQueue(pathToQueue);\n  }\n}\n\nexport function _getQueueContexts(this: NodePath) {\n  let path = this;\n  let contexts = this.contexts;\n  while (!contexts.length) {\n    path = path.parentPath;\n    if (!path) break;\n    contexts = path.contexts;\n  }\n  return contexts;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAKO,SAASE,IAAIA,CAAiBC,GAAW,EAAW;EACzD,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI;EAEtB,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC;EAEf,IAAI,IAAI,CAACG,IAAI,EAAE;IACb,IAAI,IAAI,CAACC,KAAK,CAACH,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE,OAAO,IAAI;EACxC;EAEA,IAAI,IAAI,CAACG,IAAI,EAAE;IACb,OAAO,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC,IAAI,CAACE,IAAI,CAACE,IAAI,CAAC,IAAIJ,IAAI,CAAC,IAAI,CAACE,IAAI,CAACE,IAAI,CAAC,CAACL,GAAG,CAAC,CAAC;EACtE;EAEA,OAAO,KAAK;AACd;AAEO,SAASI,KAAKA,CAAiBE,GAAqB,EAAW;EACpE,IAAI,CAACA,GAAG,EAAE,OAAO,KAAK;EAEtB,KAAK,MAAMC,EAAE,IAAID,GAAG,EAAE;IACpB,IAAI,CAACC,EAAE,EAAE;IAET,MAAMJ,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtB,MAAMK,GAAG,GAAGD,EAAE,CAACR,IAAI,CAAC,IAAI,CAACU,KAAK,EAAE,IAAI,EAAE,IAAI,CAACA,KAAK,CAAC;IACjD,IAAID,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,CAACE,IAAI,KAAK,UAAU,EAAE;MACpE,MAAM,IAAIC,KAAK,CACZ,mEAAkE,GAChE,wDAAuD,GACvD,8DAA6D,GAC7D,2BAA0B,CAC9B;IACH;IACA,IAAIH,GAAG,EAAE;MACP,MAAM,IAAIG,KAAK,CAAE,+CAA8CJ,EAAG,EAAC,CAAC;IACtE;IAGA,IAAI,IAAI,CAACJ,IAAI,KAAKA,IAAI,EAAE,OAAO,IAAI;IAGnC,IAAI,IAAI,CAACS,cAAc,GAAG,CAAC,EAAE,OAAO,IAAI;EAC1C;EAEA,OAAO,KAAK;AACd;AAEO,SAASC,YAAYA,CAAA,EAA0B;EAAA,IAAAC,mBAAA;EACpD,MAAMC,QAAQ,IAAAD,mBAAA,GAAG,IAAI,CAACb,IAAI,CAACc,QAAQ,YAAAD,mBAAA,GAAI,IAAI,CAACb,IAAI,CAACe,SAAS;EAC1D,OAAOD,QAAQ,IAAIA,QAAQ,CAACE,OAAO,CAAC,IAAI,CAACd,IAAI,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1D;AAKA,SAASa,cAAcA,CAACC,IAAc,EAAEC,OAAyB,EAAE;EACjE,IAAID,IAAI,CAACC,OAAO,KAAKA,OAAO,EAAE;IAC5BD,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtBD,IAAI,CAACV,KAAK,GAAGW,OAAO,CAACX,KAAK;IAC1BU,IAAI,CAAClB,IAAI,GAAGmB,OAAO,CAACnB,IAAI;EAC1B;AACF;AAEO,SAASoB,KAAKA,CAAA,EAA0B;EAC7C,IAAI,CAAC,IAAI,CAAClB,IAAI,EAAE;IACd,OAAO,KAAK;EACd;EAEA,IAAI,IAAI,CAACU,YAAY,EAAE,EAAE;IACvB,OAAO,KAAK;EACd;EAEA,IAAI,IAAI,CAACZ,IAAI,CAACqB,UAAU,IAAI,IAAI,CAACrB,IAAI,CAACqB,UAAU,CAAC,IAAI,CAAC,EAAE;IACtD,OAAO,KAAK;EACd;EAEA,MAAMC,cAAc,GAAG,IAAI,CAACH,OAAO;EAMnC,IAAI,IAAI,CAACE,UAAU,IAAI,IAAI,CAACvB,IAAI,CAAC,OAAO,CAAC,EAAE;IACzC,IAAI,CAACG,KAAK,CAAC,SAAS,CAAC;IACrB,OAAO,IAAI,CAACsB,UAAU;EACxB;EACAN,cAAc,CAAC,IAAI,EAAEK,cAAc,CAAC;EAEpC,IAAI,CAACrB,KAAK,CAAC,mBAAmB,CAAC;EAC/B,IAAI,CAACsB,UAAU,GAAG,IAAAC,0BAAY,EAC5B,IAAI,CAACtB,IAAI,EACT,IAAI,CAACF,IAAI,EACT,IAAI,CAACyB,KAAK,EACV,IAAI,CAACjB,KAAK,EACV,IAAI,EACJ,IAAI,CAACkB,QAAQ,CACd;EAEDT,cAAc,CAAC,IAAI,EAAEK,cAAc,CAAC;EAEpC,IAAI,CAACxB,IAAI,CAAC,MAAM,CAAC;EAEjB,OAAO,IAAI,CAACyB,UAAU;AACxB;AAEO,SAASI,IAAIA,CAAA,EAAiB;EACnC,IAAI,CAACN,UAAU,GAAG,IAAI;AACxB;AAEO,SAASO,OAAOA,CAAiB7B,GAAW,EAAE;EACnD,IAAI,IAAI,CAAC2B,QAAQ,IAAI,IAAI,EAAE;IACzB,IAAI,CAACA,QAAQ,GAAG,CAAC,CAAC;EACpB;EACA,IAAI,CAACA,QAAQ,CAAC3B,GAAG,CAAC,GAAG,IAAI;AAC3B;AAEO,SAAS8B,IAAIA,CAAA,EAAiB;EAEnC,IAAI,CAAClB,cAAc,IAAImB,kBAAW,GAAGC,kBAAW;AAClD;AAEO,SAASC,QAAQA,CAAA,EAAiB;EACvC,IAAI,IAAI,CAAChC,IAAI,IAAI,IAAI,CAACA,IAAI,CAACiC,OAAO,EAAE;EAEpC,IAAIf,IAAI,GAAG,IAAI,CAACgB,UAAU;EAE1B,IAEG,CAAC,IAAI,CAACnC,GAAG,KAAK,KAAK,IAAI,IAAI,CAACoC,OAAO,KAAK,YAAY,KACnDjB,IAAI,CAACkB,QAAQ,EAAE,IAEhB,IAAI,CAACrC,GAAG,KAAK,cAAc,IAAImB,IAAI,CAACmB,iBAAiB,EAAG,EACzD;IACAnB,IAAI,GAAGA,IAAI,CAACgB,UAAU;EACxB;EAEA,IAAII,MAAM;EACV,OAAOpB,IAAI,IAAI,CAACoB,MAAM,EAAE;IACtB,IAAIpB,IAAI,CAAClB,IAAI,IAAIkB,IAAI,CAAClB,IAAI,CAACiC,OAAO,EAAE;IAEpCK,MAAM,GAAGpB,IAAI,CAACO,KAAK;IACnBP,IAAI,GAAGA,IAAI,CAACgB,UAAU;EACxB;EAEA,IAAI,CAACT,KAAK,GAAG,IAAI,CAACc,QAAQ,CAACD,MAAM,CAAC;EAClC,IAAI,IAAI,CAACb,KAAK,EAAE,IAAI,CAACA,KAAK,CAACe,IAAI,EAAE;AACnC;AAEO,SAASC,UAAUA,CAExBtB,OAA6B,EAC7B;EACA,IAAI,IAAI,CAACO,QAAQ,IAAI,IAAI,EAAE;IACzB,IAAI,CAACA,QAAQ,GAAG,CAAC,CAAC;EACpB;EAEA,IAAI,CAACf,cAAc,GAAG,CAAC;EAEvB,IAAIQ,OAAO,EAAE;IACX,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACX,KAAK,GAAGW,OAAO,CAACX,KAAK;IAC1B,IAAI,CAACR,IAAI,GAAGmB,OAAO,CAACnB,IAAI;EAC1B;EAEA,IAAI,CAACgC,QAAQ,EAAE;EAEf,OAAO,IAAI;AACb;AAQO,SAASU,MAAMA,CAAA,EAAiB;EACrC,IAAI,IAAI,CAACC,OAAO,EAAE;EAElB,IAAI,CAACC,aAAa,EAAE;EACpB,IAAI,CAACC,WAAW,EAAE;EAClB,IAAI,CAACC,UAAU,EAAE;AAEnB;AAEO,SAASF,aAAaA,CAAA,EAAiB;EAC5C,IAAI,IAAI,CAACV,UAAU,EAAE;IACnB,IAAI,CAACa,MAAM,GAAG,IAAI,CAACb,UAAU,CAAChC,IAAI;EACpC;AACF;AAEO,SAAS4C,UAAUA,CAAA,EAAiB;EACzC,IAAI,CAAC,IAAI,CAACE,SAAS,EAAE;EAErB,IACE,IAAI,CAAC9C,IAAI,KAET,IAAI,CAAC8C,SAAS,CAAC,IAAI,CAACjD,GAAG,CAAC,EACxB;IACA;EACF;EAKA,IAAIkD,KAAK,CAACC,OAAO,CAAC,IAAI,CAACF,SAAS,CAAC,EAAE;IACjC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,SAAS,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MAC9C,IAAI,IAAI,CAACH,SAAS,CAACG,CAAC,CAAC,KAAK,IAAI,CAACjD,IAAI,EAAE;QACnC,IAAI,CAACmD,MAAM,CAACF,CAAC,CAAC;QACd;MACF;IACF;EACF,CAAC,MAAM;IACL,KAAK,MAAMpD,GAAG,IAAIuD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACP,SAAS,CAAC,EAAE;MAE7C,IAAI,IAAI,CAACA,SAAS,CAACjD,GAAG,CAAC,KAAK,IAAI,CAACG,IAAI,EAAE;QACrC,IAAI,CAACmD,MAAM,CAACtD,GAAG,CAAC;QAChB;MACF;IACF;EACF;EAGA,IAAI,CAACA,GAAG,GAAG,IAAI;AACjB;AAEO,SAAS8C,WAAWA,CAAA,EAAiB;EAC1C,IAAI,CAAC,IAAI,CAACE,MAAM,IAAI,CAAC,IAAI,CAACS,MAAM,EAAE;EAElC,MAAMC,YAAY,GAEhB,IAAI,CAACV,MAAM,CAAC,IAAI,CAACZ,OAAO,CAAC;EAC3B,IAAI,IAAI,CAACa,SAAS,KAAKS,YAAY,EAAE;EAGrC,IAAI,CAACT,SAAS,GAAGS,YAAY,IAAI,IAAI;AACvC;AAEO,SAASC,cAAcA,CAAA,EAAiB;EAC7C,IACE,IAAI,CAAC3D,GAAG,IAAI,IAAI,IAChB,CAAC,IAAI,CAACiD,SAAS,IAEf,IAAI,CAACA,SAAS,CAAC,IAAI,CAACjD,GAAG,CAAC,KAAK,IAAI,CAACG,IAAI,EACtC;IACA,IAAI,CAACyD,YAAY,EAAE;EACrB;AACF;AAEO,SAASC,UAAUA,CAAA,EAAiB;EACzC,IAAI,CAACC,QAAQ,CAACC,GAAG,EAAE;EACnB,IAAI,IAAI,CAACD,QAAQ,CAACT,MAAM,GAAG,CAAC,EAAE;IAC5B,IAAI,CAACX,UAAU,CAAC,IAAI,CAACoB,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACT,MAAM,GAAG,CAAC,CAAC,CAAC;EAC1D,CAAC,MAAM;IACL,IAAI,CAACX,UAAU,CAACsB,SAAS,CAAC;EAC5B;AACF;AAEO,SAASC,WAAWA,CAAiB7C,OAAyB,EAAE;EACrE,IAAI,CAAC0C,QAAQ,CAACI,IAAI,CAAC9C,OAAO,CAAC;EAC3B,IAAI,CAACsB,UAAU,CAACtB,OAAO,CAAC;AAC1B;AAEO,SAAS+C,KAAKA,CAEnBhC,UAAgC,EAChCc,SAAiB,EACjBb,OAAe,EACfpC,GAAoB,EACpB;EACA,IAAI,CAACoC,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACa,SAAS,GAAGA,SAAS;EAE1B,IAAI,CAACd,UAAU,GAAGA,UAAU,IAAI,IAAI,CAACA,UAAU;EAC/C,IAAI,CAACmB,MAAM,CAACtD,GAAG,CAAC;AAClB;AAEO,SAASsD,MAAMA,CAAiBtD,GAAoB,EAAE;EAAA,IAAAoE,UAAA;EAC3D,IAAI,CAACpE,GAAG,GAAGA,GAAG;EACd,IAAI,CAACG,IAAI,GAEP,IAAI,CAAC8C,SAAS,CAAC,IAAI,CAACjD,GAAG,CAAC;EAC1B,IAAI,CAACK,IAAI,IAAA+D,UAAA,GAAG,IAAI,CAACjE,IAAI,qBAATiE,UAAA,CAAW/D,IAAI;AAC7B;AAEO,SAASgE,OAAOA,CAAiBC,WAAW,GAAG,IAAI,EAAE;EAC1D,IAAIA,WAAW,CAAC1B,OAAO,EAAE;EAAO;EAWhC,MAAMkB,QAAQ,GAAG,IAAI,CAACA,QAAQ;EAE9B,KAAK,MAAM1C,OAAO,IAAI0C,QAAQ,EAAE;IAC9B1C,OAAO,CAACmD,UAAU,CAACD,WAAW,CAAC;EACjC;AACF;AAEO,SAASE,iBAAiBA,CAAA,EAAiB;EAChD,IAAIrD,IAAI,GAAG,IAAI;EACf,IAAI2C,QAAQ,GAAG,IAAI,CAACA,QAAQ;EAC5B,OAAO,CAACA,QAAQ,CAACT,MAAM,EAAE;IACvBlC,IAAI,GAAGA,IAAI,CAACgB,UAAU;IACtB,IAAI,CAAChB,IAAI,EAAE;IACX2C,QAAQ,GAAG3C,IAAI,CAAC2C,QAAQ;EAC1B;EACA,OAAOA,QAAQ;AACjB"}