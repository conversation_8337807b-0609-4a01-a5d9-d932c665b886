import { color, spacing } from '@appmaker-xyz/uikit/src/styles/index';
import { appSettings } from '@appmaker-xyz/core';

const OTPLogin = {
  type: 'normal',
  title: 'Register Page',
  attributes: {
    headerShown: true,
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
      paddingHorizontal: spacing.base,
    },
  },
  blocks: [
    {
      name: 'appmaker/remoteImage',
      attributes: {
        name: 'LOGIN_LOGO',
        resizeMode: 'contain',
        style: {
          width: 250,
          height: 100,
          alignSelf: 'center',
          marginTop: spacing.xl,
          marginBottom: spacing.lg,
        },
      },
    },
    {
      name: 'appmaker/layout',
      attributes: {
        style: {
          marginBottom: spacing.lg,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/otp-login-input',
          attributes: {
            enableResendOTP: true,
            otpMaxLength: "{{blockData.otpCount || '6'}}",
            buttonWholeContainerStyle: {
              borderRadius: 40,
              marginTop: spacing.base,
              backgroundColor: '#ee3d63',
              borderWidth: 0,
            },
            onSubmitPhoneNumberAction: {
              action: 'CUSTOM_REQUEST_OTP',
            },
            onVerifyOTPAction: {
              action: 'CUSTOM_VERIFY_OTP',
            },
            __appmakerStylesClassName: 'otpLoginPage',
          },
        },
        // {
        //   name: 'appmaker/text',
        //   attributes: {
        //     content: 'Enter your mobile number to get the OTP.',
        //     category: 'bodySubText',
        //   },
        // },
      ],
    },
    // {
    //   name: 'appmaker/floating-label-input',
    //   attributes: {
    //     label: 'Enter Phone Number',
    //   },
    // },
    // {
    //   name: 'appmaker/button',
    //   attributes: {
    //     content: 'Get OTP',
    //     baseSize: true,
    //     wholeContainerStyle: {
    //       borderRadius: 40,
    //       marginTop: spacing.base,
    //       backgroundColor: '#ee3d63',
    //       borderWidth: 0,
    //     },
    //     appmakerAction: {
    //       action: 'OPEN_INAPP_PAGE',
    //       pageId: 'OtpLoginVerify',
    //     },
    //   },
    // },
  ],
};
export default OTPLogin;
