import VariationSelect from '../VariationSelect';
import * as products from '../product.js';

describe('Variation Select', () => {
  it('should ', async () => {
    // console.log(pro);
    const HXPproduct = products.hxp;
    const variationSelect = new VariationSelect(HXPproduct);
    // variationSelect.setProduct();
    const {attrId, selectedOption} = {
      attrId: 'pa_flavour',
      selectedOption: 'bluberry-muffin',
    };
    var product = variationSelect.getVariation(attrId, selectedOption);
    var imageProduct = variationSelect.getVariationImageId(
      attrId,
      selectedOption,
    );
    console.log(product.id, imageProduct);
  });
});
