import create from 'zustand';
import produce from 'immer';

const useAlertStore = create((set) => ({
  alertShown: false,
  alterDataState: {},
  show: ({ title, message, leftButton, rightButton }) =>
    set(
      produce((state) => {
        state.alertShown = true;
        state.alterDataState = {
          title,
          message,
          leftButton,
          rightButton,
        };
      }),
    ),
  hide: () =>
    set(
      produce((state) => {
        state.alertShown = false;
        state.alterDataState = {};
      }),
    ),
}));
const ModalAlert = {
  show: useAlertStore.getState().show,
  hide: useAlertStore.getState().hide,
};
export { useAlertStore, ModalAlert };
