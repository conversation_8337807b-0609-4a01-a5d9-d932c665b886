{"version": 3, "names": ["_t", "require", "n", "isCallExpression", "isLiteral", "isMemberExpression", "isNewExpression", "UnaryExpression", "node", "operator", "word", "space", "token", "print", "argument", "DoExpression", "async", "body", "ParenthesizedExpression", "expression", "UpdateExpression", "prefix", "printTerminatorless", "ConditionalExpression", "test", "consequent", "alternate", "NewExpression", "parent", "callee", "format", "minified", "arguments", "length", "optional", "typeArguments", "typeParameters", "printList", "SequenceExpression", "expressions", "ThisExpression", "Super", "isDecoratorMemberExpression", "type", "computed", "property", "object", "shouldParenthesizeDecoratorExpression", "_shouldPrintDecoratorsBeforeExport", "decoratorsBeforeExport", "start", "declaration", "Decorator", "newline", "OptionalMemberExpression", "TypeError", "value", "OptionalCallExpression", "CallExpression", "Import", "AwaitExpression", "YieldExpression", "delegate", "EmptyStatement", "semicolon", "ExpressionStatement", "AssignmentPattern", "left", "typeAnnotation", "right", "AssignmentExpression", "parens", "inForStatementInitCounter", "needsParens", "BindExpression", "MemberExpression", "MetaProperty", "meta", "PrivateName", "id", "V8IntrinsicIdentifier", "name", "ModuleExpression", "indent", "directives", "dedent", "sourceWithOffset", "loc", "rightBrace"], "sources": ["../../src/generators/expressions.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport {\n  isCallExpression,\n  isLiteral,\n  isMemberExpression,\n  isNewExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport * as n from \"../node\";\n\nexport function UnaryExpression(this: Printer, node: t.UnaryExpression) {\n  if (\n    node.operator === \"void\" ||\n    node.operator === \"delete\" ||\n    node.operator === \"typeof\" ||\n    // throwExpressions\n    node.operator === \"throw\"\n  ) {\n    this.word(node.operator);\n    this.space();\n  } else {\n    this.token(node.operator);\n  }\n\n  this.print(node.argument, node);\n}\n\nexport function DoExpression(this: Printer, node: t.DoExpression) {\n  if (node.async) {\n    this.word(\"async\", true);\n    this.space();\n  }\n  this.word(\"do\");\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function ParenthesizedExpression(\n  this: Printer,\n  node: t.ParenthesizedExpression,\n) {\n  this.token(\"(\");\n  this.print(node.expression, node);\n  this.token(\")\");\n}\n\nexport function UpdateExpression(this: Printer, node: t.UpdateExpression) {\n  if (node.prefix) {\n    this.token(node.operator);\n    this.print(node.argument, node);\n  } else {\n    this.printTerminatorless(node.argument, node, true);\n    this.token(node.operator);\n  }\n}\n\nexport function ConditionalExpression(\n  this: Printer,\n  node: t.ConditionalExpression,\n) {\n  this.print(node.test, node);\n  this.space();\n  this.token(\"?\");\n  this.space();\n  this.print(node.consequent, node);\n  this.space();\n  this.token(\":\");\n  this.space();\n  this.print(node.alternate, node);\n}\n\nexport function NewExpression(\n  this: Printer,\n  node: t.NewExpression,\n  parent: t.Node,\n) {\n  this.word(\"new\");\n  this.space();\n  this.print(node.callee, node);\n  if (\n    this.format.minified &&\n    node.arguments.length === 0 &&\n    !node.optional &&\n    !isCallExpression(parent, { callee: node }) &&\n    !isMemberExpression(parent) &&\n    !isNewExpression(parent)\n  ) {\n    return;\n  }\n\n  this.print(node.typeArguments, node); // Flow\n  this.print(node.typeParameters, node); // TS\n\n  if (node.optional) {\n    // TODO: This can never happen\n    this.token(\"?.\");\n  }\n  this.token(\"(\");\n  this.printList(node.arguments, node);\n  this.token(\")\");\n}\n\nexport function SequenceExpression(this: Printer, node: t.SequenceExpression) {\n  this.printList(node.expressions, node);\n}\n\nexport function ThisExpression(this: Printer) {\n  this.word(\"this\");\n}\n\nexport function Super(this: Printer) {\n  this.word(\"super\");\n}\n\nfunction isDecoratorMemberExpression(\n  node: t.Expression | t.Super | t.V8IntrinsicIdentifier,\n): boolean {\n  switch (node.type) {\n    case \"Identifier\":\n      return true;\n    case \"MemberExpression\":\n      return (\n        !node.computed &&\n        node.property.type === \"Identifier\" &&\n        isDecoratorMemberExpression(node.object)\n      );\n    default:\n      return false;\n  }\n}\nfunction shouldParenthesizeDecoratorExpression(\n  node: t.Expression | t.Super | t.V8IntrinsicIdentifier,\n) {\n  if (node.type === \"ParenthesizedExpression\") {\n    // We didn't check extra?.parenthesized here because we don't track decorators in needsParen\n    return false;\n  }\n  return !isDecoratorMemberExpression(\n    node.type === \"CallExpression\" ? node.callee : node,\n  );\n}\n\nexport function _shouldPrintDecoratorsBeforeExport(\n  this: Printer,\n  node: t.ExportDeclaration & { declaration: t.ClassDeclaration },\n) {\n  if (typeof this.format.decoratorsBeforeExport === \"boolean\") {\n    return this.format.decoratorsBeforeExport;\n  }\n  return (\n    typeof node.start === \"number\" && node.start === node.declaration.start\n  );\n}\n\nexport function Decorator(this: Printer, node: t.Decorator) {\n  this.token(\"@\");\n  const { expression } = node;\n  if (shouldParenthesizeDecoratorExpression(expression)) {\n    this.token(\"(\");\n    this.print(expression, node);\n    this.token(\")\");\n  } else {\n    this.print(expression, node);\n  }\n  this.newline();\n}\n\nexport function OptionalMemberExpression(\n  this: Printer,\n  node: t.OptionalMemberExpression,\n) {\n  this.print(node.object, node);\n\n  if (!node.computed && isMemberExpression(node.property)) {\n    throw new TypeError(\"Got a MemberExpression for MemberExpression property\");\n  }\n\n  let computed = node.computed;\n  // @ts-expect-error todo(flow->ts) maybe instead of typeof check specific literal types?\n  if (isLiteral(node.property) && typeof node.property.value === \"number\") {\n    computed = true;\n  }\n  if (node.optional) {\n    this.token(\"?.\");\n  }\n\n  if (computed) {\n    this.token(\"[\");\n    this.print(node.property, node);\n    this.token(\"]\");\n  } else {\n    if (!node.optional) {\n      this.token(\".\");\n    }\n    this.print(node.property, node);\n  }\n}\n\nexport function OptionalCallExpression(\n  this: Printer,\n  node: t.OptionalCallExpression,\n) {\n  this.print(node.callee, node);\n\n  this.print(node.typeParameters, node); // TS\n\n  if (node.optional) {\n    this.token(\"?.\");\n  }\n\n  this.print(node.typeArguments, node); // Flow\n\n  this.token(\"(\");\n  this.printList(node.arguments, node);\n  this.token(\")\");\n}\n\nexport function CallExpression(this: Printer, node: t.CallExpression) {\n  this.print(node.callee, node);\n\n  this.print(node.typeArguments, node); // Flow\n  this.print(node.typeParameters, node); // TS\n  this.token(\"(\");\n  this.printList(node.arguments, node);\n  this.token(\")\");\n}\n\nexport function Import(this: Printer) {\n  this.word(\"import\");\n}\n\nexport function AwaitExpression(this: Printer, node: t.AwaitExpression) {\n  this.word(\"await\");\n\n  if (node.argument) {\n    this.space();\n    this.printTerminatorless(node.argument, node, false);\n  }\n}\n\nexport function YieldExpression(this: Printer, node: t.YieldExpression) {\n  this.word(\"yield\", true);\n\n  if (node.delegate) {\n    this.token(\"*\");\n    if (node.argument) {\n      this.space();\n      // line terminators are allowed after yield*\n      this.print(node.argument, node);\n    }\n  } else {\n    if (node.argument) {\n      this.space();\n      this.printTerminatorless(node.argument, node, false);\n    }\n  }\n}\n\nexport function EmptyStatement(this: Printer) {\n  this.semicolon(true /* force */);\n}\n\nexport function ExpressionStatement(\n  this: Printer,\n  node: t.ExpressionStatement,\n) {\n  this.print(node.expression, node);\n  this.semicolon();\n}\n\nexport function AssignmentPattern(this: Printer, node: t.AssignmentPattern) {\n  this.print(node.left, node);\n  // @ts-expect-error todo(flow->ts) property present on some of the types in union but not all\n  if (node.left.optional) this.token(\"?\");\n  // @ts-expect-error todo(flow->ts) property present on some of the types in union but not all\n  this.print(node.left.typeAnnotation, node);\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(node.right, node);\n}\n\nexport function AssignmentExpression(\n  this: Printer,\n  node: t.AssignmentExpression,\n  parent: t.Node,\n) {\n  // Somewhere inside a for statement `init` node but doesn't usually\n  // needs a paren except for `in` expressions: `for (a in b ? a : b;;)`\n  const parens =\n    this.inForStatementInitCounter &&\n    node.operator === \"in\" &&\n    !n.needsParens(node, parent);\n\n  if (parens) {\n    this.token(\"(\");\n  }\n\n  this.print(node.left, node);\n\n  this.space();\n  if (node.operator === \"in\" || node.operator === \"instanceof\") {\n    this.word(node.operator);\n  } else {\n    this.token(node.operator);\n  }\n  this.space();\n\n  this.print(node.right, node);\n\n  if (parens) {\n    this.token(\")\");\n  }\n}\n\nexport function BindExpression(this: Printer, node: t.BindExpression) {\n  this.print(node.object, node);\n  this.token(\"::\");\n  this.print(node.callee, node);\n}\n\nexport {\n  AssignmentExpression as BinaryExpression,\n  AssignmentExpression as LogicalExpression,\n};\n\nexport function MemberExpression(this: Printer, node: t.MemberExpression) {\n  this.print(node.object, node);\n\n  if (!node.computed && isMemberExpression(node.property)) {\n    throw new TypeError(\"Got a MemberExpression for MemberExpression property\");\n  }\n\n  let computed = node.computed;\n  // @ts-expect-error todo(flow->ts) maybe use specific literal types\n  if (isLiteral(node.property) && typeof node.property.value === \"number\") {\n    computed = true;\n  }\n\n  if (computed) {\n    this.token(\"[\");\n    this.print(node.property, node);\n    this.token(\"]\");\n  } else {\n    this.token(\".\");\n    this.print(node.property, node);\n  }\n}\n\nexport function MetaProperty(this: Printer, node: t.MetaProperty) {\n  this.print(node.meta, node);\n  this.token(\".\");\n  this.print(node.property, node);\n}\n\nexport function PrivateName(this: Printer, node: t.PrivateName) {\n  this.token(\"#\");\n  this.print(node.id, node);\n}\n\nexport function V8IntrinsicIdentifier(\n  this: Printer,\n  node: t.V8IntrinsicIdentifier,\n) {\n  this.token(\"%\");\n  this.word(node.name);\n}\n\nexport function ModuleExpression(this: Printer, node: t.ModuleExpression) {\n  this.word(\"module\", true);\n  this.space();\n  this.token(\"{\");\n  this.indent();\n  const { body } = node;\n  if (body.body.length || body.directives.length) {\n    this.newline();\n  }\n  this.print(body, node);\n  this.dedent();\n  this.sourceWithOffset(\"end\", node.loc, 0, -1);\n  this.rightBrace();\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAOA,IAAAC,CAAA,GAAAD,OAAA;AAA6B;EAN3BE,gBAAgB;EAChBC,SAAS;EACTC,kBAAkB;EAClBC;AAAe,IAAAN,EAAA;AAKV,SAASO,eAAeA,CAAgBC,IAAuB,EAAE;EACtE,IACEA,IAAI,CAACC,QAAQ,KAAK,MAAM,IACxBD,IAAI,CAACC,QAAQ,KAAK,QAAQ,IAC1BD,IAAI,CAACC,QAAQ,KAAK,QAAQ,IAE1BD,IAAI,CAACC,QAAQ,KAAK,OAAO,EACzB;IACA,IAAI,CAACC,IAAI,CAACF,IAAI,CAACC,QAAQ,CAAC;IACxB,IAAI,CAACE,KAAK,EAAE;EACd,CAAC,MAAM;IACL,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACC,QAAQ,CAAC;EAC3B;EAEA,IAAI,CAACI,KAAK,CAACL,IAAI,CAACM,QAAQ,EAAEN,IAAI,CAAC;AACjC;AAEO,SAASO,YAAYA,CAAgBP,IAAoB,EAAE;EAChE,IAAIA,IAAI,CAACQ,KAAK,EAAE;IACd,IAAI,CAACN,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;IACxB,IAAI,CAACC,KAAK,EAAE;EACd;EACA,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACS,IAAI,EAAET,IAAI,CAAC;AAC7B;AAEO,SAASU,uBAAuBA,CAErCV,IAA+B,EAC/B;EACA,IAAI,CAACI,SAAK,IAAK;EACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACW,UAAU,EAAEX,IAAI,CAAC;EACjC,IAAI,CAACI,SAAK,IAAK;AACjB;AAEO,SAASQ,gBAAgBA,CAAgBZ,IAAwB,EAAE;EACxE,IAAIA,IAAI,CAACa,MAAM,EAAE;IACf,IAAI,CAACT,KAAK,CAACJ,IAAI,CAACC,QAAQ,CAAC;IACzB,IAAI,CAACI,KAAK,CAACL,IAAI,CAACM,QAAQ,EAAEN,IAAI,CAAC;EACjC,CAAC,MAAM;IACL,IAAI,CAACc,mBAAmB,CAACd,IAAI,CAACM,QAAQ,EAAEN,IAAI,EAAE,IAAI,CAAC;IACnD,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACC,QAAQ,CAAC;EAC3B;AACF;AAEO,SAASc,qBAAqBA,CAEnCf,IAA6B,EAC7B;EACA,IAAI,CAACK,KAAK,CAACL,IAAI,CAACgB,IAAI,EAAEhB,IAAI,CAAC;EAC3B,IAAI,CAACG,KAAK,EAAE;EACZ,IAAI,CAACC,SAAK,IAAK;EACf,IAAI,CAACD,KAAK,EAAE;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACiB,UAAU,EAAEjB,IAAI,CAAC;EACjC,IAAI,CAACG,KAAK,EAAE;EACZ,IAAI,CAACC,SAAK,IAAK;EACf,IAAI,CAACD,KAAK,EAAE;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACkB,SAAS,EAAElB,IAAI,CAAC;AAClC;AAEO,SAASmB,aAAaA,CAE3BnB,IAAqB,EACrBoB,MAAc,EACd;EACA,IAAI,CAAClB,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACqB,MAAM,EAAErB,IAAI,CAAC;EAC7B,IACE,IAAI,CAACsB,MAAM,CAACC,QAAQ,IACpBvB,IAAI,CAACwB,SAAS,CAACC,MAAM,KAAK,CAAC,IAC3B,CAACzB,IAAI,CAAC0B,QAAQ,IACd,CAAC/B,gBAAgB,CAACyB,MAAM,EAAE;IAAEC,MAAM,EAAErB;EAAK,CAAC,CAAC,IAC3C,CAACH,kBAAkB,CAACuB,MAAM,CAAC,IAC3B,CAACtB,eAAe,CAACsB,MAAM,CAAC,EACxB;IACA;EACF;EAEA,IAAI,CAACf,KAAK,CAACL,IAAI,CAAC2B,aAAa,EAAE3B,IAAI,CAAC;EACpC,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC4B,cAAc,EAAE5B,IAAI,CAAC;EAErC,IAAIA,IAAI,CAAC0B,QAAQ,EAAE;IAEjB,IAAI,CAACtB,KAAK,CAAC,IAAI,CAAC;EAClB;EACA,IAAI,CAACA,SAAK,IAAK;EACf,IAAI,CAACyB,SAAS,CAAC7B,IAAI,CAACwB,SAAS,EAAExB,IAAI,CAAC;EACpC,IAAI,CAACI,SAAK,IAAK;AACjB;AAEO,SAAS0B,kBAAkBA,CAAgB9B,IAA0B,EAAE;EAC5E,IAAI,CAAC6B,SAAS,CAAC7B,IAAI,CAAC+B,WAAW,EAAE/B,IAAI,CAAC;AACxC;AAEO,SAASgC,cAAcA,CAAA,EAAgB;EAC5C,IAAI,CAAC9B,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAAS+B,KAAKA,CAAA,EAAgB;EACnC,IAAI,CAAC/B,IAAI,CAAC,OAAO,CAAC;AACpB;AAEA,SAASgC,2BAA2BA,CAClClC,IAAsD,EAC7C;EACT,QAAQA,IAAI,CAACmC,IAAI;IACf,KAAK,YAAY;MACf,OAAO,IAAI;IACb,KAAK,kBAAkB;MACrB,OACE,CAACnC,IAAI,CAACoC,QAAQ,IACdpC,IAAI,CAACqC,QAAQ,CAACF,IAAI,KAAK,YAAY,IACnCD,2BAA2B,CAAClC,IAAI,CAACsC,MAAM,CAAC;IAE5C;MACE,OAAO,KAAK;EAAC;AAEnB;AACA,SAASC,qCAAqCA,CAC5CvC,IAAsD,EACtD;EACA,IAAIA,IAAI,CAACmC,IAAI,KAAK,yBAAyB,EAAE;IAE3C,OAAO,KAAK;EACd;EACA,OAAO,CAACD,2BAA2B,CACjClC,IAAI,CAACmC,IAAI,KAAK,gBAAgB,GAAGnC,IAAI,CAACqB,MAAM,GAAGrB,IAAI,CACpD;AACH;AAEO,SAASwC,kCAAkCA,CAEhDxC,IAA+D,EAC/D;EACA,IAAI,OAAO,IAAI,CAACsB,MAAM,CAACmB,sBAAsB,KAAK,SAAS,EAAE;IAC3D,OAAO,IAAI,CAACnB,MAAM,CAACmB,sBAAsB;EAC3C;EACA,OACE,OAAOzC,IAAI,CAAC0C,KAAK,KAAK,QAAQ,IAAI1C,IAAI,CAAC0C,KAAK,KAAK1C,IAAI,CAAC2C,WAAW,CAACD,KAAK;AAE3E;AAEO,SAASE,SAASA,CAAgB5C,IAAiB,EAAE;EAC1D,IAAI,CAACI,SAAK,IAAK;EACf,MAAM;IAAEO;EAAW,CAAC,GAAGX,IAAI;EAC3B,IAAIuC,qCAAqC,CAAC5B,UAAU,CAAC,EAAE;IACrD,IAAI,CAACP,SAAK,IAAK;IACf,IAAI,CAACC,KAAK,CAACM,UAAU,EAAEX,IAAI,CAAC;IAC5B,IAAI,CAACI,SAAK,IAAK;EACjB,CAAC,MAAM;IACL,IAAI,CAACC,KAAK,CAACM,UAAU,EAAEX,IAAI,CAAC;EAC9B;EACA,IAAI,CAAC6C,OAAO,EAAE;AAChB;AAEO,SAASC,wBAAwBA,CAEtC9C,IAAgC,EAChC;EACA,IAAI,CAACK,KAAK,CAACL,IAAI,CAACsC,MAAM,EAAEtC,IAAI,CAAC;EAE7B,IAAI,CAACA,IAAI,CAACoC,QAAQ,IAAIvC,kBAAkB,CAACG,IAAI,CAACqC,QAAQ,CAAC,EAAE;IACvD,MAAM,IAAIU,SAAS,CAAC,sDAAsD,CAAC;EAC7E;EAEA,IAAIX,QAAQ,GAAGpC,IAAI,CAACoC,QAAQ;EAE5B,IAAIxC,SAAS,CAACI,IAAI,CAACqC,QAAQ,CAAC,IAAI,OAAOrC,IAAI,CAACqC,QAAQ,CAACW,KAAK,KAAK,QAAQ,EAAE;IACvEZ,QAAQ,GAAG,IAAI;EACjB;EACA,IAAIpC,IAAI,CAAC0B,QAAQ,EAAE;IACjB,IAAI,CAACtB,KAAK,CAAC,IAAI,CAAC;EAClB;EAEA,IAAIgC,QAAQ,EAAE;IACZ,IAAI,CAAChC,SAAK,IAAK;IACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACqC,QAAQ,EAAErC,IAAI,CAAC;IAC/B,IAAI,CAACI,SAAK,IAAK;EACjB,CAAC,MAAM;IACL,IAAI,CAACJ,IAAI,CAAC0B,QAAQ,EAAE;MAClB,IAAI,CAACtB,SAAK,IAAK;IACjB;IACA,IAAI,CAACC,KAAK,CAACL,IAAI,CAACqC,QAAQ,EAAErC,IAAI,CAAC;EACjC;AACF;AAEO,SAASiD,sBAAsBA,CAEpCjD,IAA8B,EAC9B;EACA,IAAI,CAACK,KAAK,CAACL,IAAI,CAACqB,MAAM,EAAErB,IAAI,CAAC;EAE7B,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC4B,cAAc,EAAE5B,IAAI,CAAC;EAErC,IAAIA,IAAI,CAAC0B,QAAQ,EAAE;IACjB,IAAI,CAACtB,KAAK,CAAC,IAAI,CAAC;EAClB;EAEA,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC2B,aAAa,EAAE3B,IAAI,CAAC;EAEpC,IAAI,CAACI,SAAK,IAAK;EACf,IAAI,CAACyB,SAAS,CAAC7B,IAAI,CAACwB,SAAS,EAAExB,IAAI,CAAC;EACpC,IAAI,CAACI,SAAK,IAAK;AACjB;AAEO,SAAS8C,cAAcA,CAAgBlD,IAAsB,EAAE;EACpE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACqB,MAAM,EAAErB,IAAI,CAAC;EAE7B,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC2B,aAAa,EAAE3B,IAAI,CAAC;EACpC,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC4B,cAAc,EAAE5B,IAAI,CAAC;EACrC,IAAI,CAACI,SAAK,IAAK;EACf,IAAI,CAACyB,SAAS,CAAC7B,IAAI,CAACwB,SAAS,EAAExB,IAAI,CAAC;EACpC,IAAI,CAACI,SAAK,IAAK;AACjB;AAEO,SAAS+C,MAAMA,CAAA,EAAgB;EACpC,IAAI,CAACjD,IAAI,CAAC,QAAQ,CAAC;AACrB;AAEO,SAASkD,eAAeA,CAAgBpD,IAAuB,EAAE;EACtE,IAAI,CAACE,IAAI,CAAC,OAAO,CAAC;EAElB,IAAIF,IAAI,CAACM,QAAQ,EAAE;IACjB,IAAI,CAACH,KAAK,EAAE;IACZ,IAAI,CAACW,mBAAmB,CAACd,IAAI,CAACM,QAAQ,EAAEN,IAAI,EAAE,KAAK,CAAC;EACtD;AACF;AAEO,SAASqD,eAAeA,CAAgBrD,IAAuB,EAAE;EACtE,IAAI,CAACE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;EAExB,IAAIF,IAAI,CAACsD,QAAQ,EAAE;IACjB,IAAI,CAAClD,SAAK,IAAK;IACf,IAAIJ,IAAI,CAACM,QAAQ,EAAE;MACjB,IAAI,CAACH,KAAK,EAAE;MAEZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAACM,QAAQ,EAAEN,IAAI,CAAC;IACjC;EACF,CAAC,MAAM;IACL,IAAIA,IAAI,CAACM,QAAQ,EAAE;MACjB,IAAI,CAACH,KAAK,EAAE;MACZ,IAAI,CAACW,mBAAmB,CAACd,IAAI,CAACM,QAAQ,EAAEN,IAAI,EAAE,KAAK,CAAC;IACtD;EACF;AACF;AAEO,SAASuD,cAAcA,CAAA,EAAgB;EAC5C,IAAI,CAACC,SAAS,CAAC,IAAI,CAAa;AAClC;AAEO,SAASC,mBAAmBA,CAEjCzD,IAA2B,EAC3B;EACA,IAAI,CAACK,KAAK,CAACL,IAAI,CAACW,UAAU,EAAEX,IAAI,CAAC;EACjC,IAAI,CAACwD,SAAS,EAAE;AAClB;AAEO,SAASE,iBAAiBA,CAAgB1D,IAAyB,EAAE;EAC1E,IAAI,CAACK,KAAK,CAACL,IAAI,CAAC2D,IAAI,EAAE3D,IAAI,CAAC;EAE3B,IAAIA,IAAI,CAAC2D,IAAI,CAACjC,QAAQ,EAAE,IAAI,CAACtB,SAAK,IAAK;EAEvC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC2D,IAAI,CAACC,cAAc,EAAE5D,IAAI,CAAC;EAC1C,IAAI,CAACG,KAAK,EAAE;EACZ,IAAI,CAACC,SAAK,IAAK;EACf,IAAI,CAACD,KAAK,EAAE;EACZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAAC6D,KAAK,EAAE7D,IAAI,CAAC;AAC9B;AAEO,SAAS8D,oBAAoBA,CAElC9D,IAA4B,EAC5BoB,MAAc,EACd;EAGA,MAAM2C,MAAM,GACV,IAAI,CAACC,yBAAyB,IAC9BhE,IAAI,CAACC,QAAQ,KAAK,IAAI,IACtB,CAACP,CAAC,CAACuE,WAAW,CAACjE,IAAI,EAAEoB,MAAM,CAAC;EAE9B,IAAI2C,MAAM,EAAE;IACV,IAAI,CAAC3D,SAAK,IAAK;EACjB;EAEA,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC2D,IAAI,EAAE3D,IAAI,CAAC;EAE3B,IAAI,CAACG,KAAK,EAAE;EACZ,IAAIH,IAAI,CAACC,QAAQ,KAAK,IAAI,IAAID,IAAI,CAACC,QAAQ,KAAK,YAAY,EAAE;IAC5D,IAAI,CAACC,IAAI,CAACF,IAAI,CAACC,QAAQ,CAAC;EAC1B,CAAC,MAAM;IACL,IAAI,CAACG,KAAK,CAACJ,IAAI,CAACC,QAAQ,CAAC;EAC3B;EACA,IAAI,CAACE,KAAK,EAAE;EAEZ,IAAI,CAACE,KAAK,CAACL,IAAI,CAAC6D,KAAK,EAAE7D,IAAI,CAAC;EAE5B,IAAI+D,MAAM,EAAE;IACV,IAAI,CAAC3D,SAAK,IAAK;EACjB;AACF;AAEO,SAAS8D,cAAcA,CAAgBlE,IAAsB,EAAE;EACpE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACsC,MAAM,EAAEtC,IAAI,CAAC;EAC7B,IAAI,CAACI,KAAK,CAAC,IAAI,CAAC;EAChB,IAAI,CAACC,KAAK,CAACL,IAAI,CAACqB,MAAM,EAAErB,IAAI,CAAC;AAC/B;AAOO,SAASmE,gBAAgBA,CAAgBnE,IAAwB,EAAE;EACxE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACsC,MAAM,EAAEtC,IAAI,CAAC;EAE7B,IAAI,CAACA,IAAI,CAACoC,QAAQ,IAAIvC,kBAAkB,CAACG,IAAI,CAACqC,QAAQ,CAAC,EAAE;IACvD,MAAM,IAAIU,SAAS,CAAC,sDAAsD,CAAC;EAC7E;EAEA,IAAIX,QAAQ,GAAGpC,IAAI,CAACoC,QAAQ;EAE5B,IAAIxC,SAAS,CAACI,IAAI,CAACqC,QAAQ,CAAC,IAAI,OAAOrC,IAAI,CAACqC,QAAQ,CAACW,KAAK,KAAK,QAAQ,EAAE;IACvEZ,QAAQ,GAAG,IAAI;EACjB;EAEA,IAAIA,QAAQ,EAAE;IACZ,IAAI,CAAChC,SAAK,IAAK;IACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACqC,QAAQ,EAAErC,IAAI,CAAC;IAC/B,IAAI,CAACI,SAAK,IAAK;EACjB,CAAC,MAAM;IACL,IAAI,CAACA,SAAK,IAAK;IACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACqC,QAAQ,EAAErC,IAAI,CAAC;EACjC;AACF;AAEO,SAASoE,YAAYA,CAAgBpE,IAAoB,EAAE;EAChE,IAAI,CAACK,KAAK,CAACL,IAAI,CAACqE,IAAI,EAAErE,IAAI,CAAC;EAC3B,IAAI,CAACI,SAAK,IAAK;EACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACqC,QAAQ,EAAErC,IAAI,CAAC;AACjC;AAEO,SAASsE,WAAWA,CAAgBtE,IAAmB,EAAE;EAC9D,IAAI,CAACI,SAAK,IAAK;EACf,IAAI,CAACC,KAAK,CAACL,IAAI,CAACuE,EAAE,EAAEvE,IAAI,CAAC;AAC3B;AAEO,SAASwE,qBAAqBA,CAEnCxE,IAA6B,EAC7B;EACA,IAAI,CAACI,SAAK,IAAK;EACf,IAAI,CAACF,IAAI,CAACF,IAAI,CAACyE,IAAI,CAAC;AACtB;AAEO,SAASC,gBAAgBA,CAAgB1E,IAAwB,EAAE;EACxE,IAAI,CAACE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC;EACzB,IAAI,CAACC,KAAK,EAAE;EACZ,IAAI,CAACC,SAAK,KAAK;EACf,IAAI,CAACuE,MAAM,EAAE;EACb,MAAM;IAAElE;EAAK,CAAC,GAAGT,IAAI;EACrB,IAAIS,IAAI,CAACA,IAAI,CAACgB,MAAM,IAAIhB,IAAI,CAACmE,UAAU,CAACnD,MAAM,EAAE;IAC9C,IAAI,CAACoB,OAAO,EAAE;EAChB;EACA,IAAI,CAACxC,KAAK,CAACI,IAAI,EAAET,IAAI,CAAC;EACtB,IAAI,CAAC6E,MAAM,EAAE;EACb,IAAI,CAACC,gBAAgB,CAAC,KAAK,EAAE9E,IAAI,CAAC+E,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC7C,IAAI,CAACC,UAAU,EAAE;AACnB"}