{"name": "@shopify/checkout-sheet-kit", "license": "MIT", "version": "3.2.0", "main": "lib/commonjs/index.js", "types": "src/index.ts", "source": "src/index.ts", "module": "lib/module/index.js", "description": "A React Native library for Shopify's Checkout Kit.", "author": "Shopify", "homepage": "https://github.com/shopify/checkout-sheet-kit-react-native", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "scripts": {"clean": "rm -rf lib", "build": "bob build", "lint:swift": "./../../../sample/ios/Pods/SwiftLint/swiftlint --strict --quiet ios", "lint": "yarn typecheck && eslint src", "typecheck": "tsc --noEmit"}, "files": ["LICENSE", "README.md", "package.json", "src", "ios", "android", "lib", "*.podsp<PERSON>", "!ios/build", "!android/build", "!android/gradle", "!android/gradlew", "!android/gradlew.bat", "!android/local.properties", "!**/*.spec.*", "!**/*.test.*", "!**/.*"], "keywords": ["react-native", "shopify", "checkout"], "peerDependencies": {"react": "*", "react-native": "*"}, "devDependencies": {"react-native-builder-bob": "^0.23.1", "typescript": "^5.4.2"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}}