{"version": 3, "names": ["_definitions", "require", "_generated", "has", "Function", "call", "bind", "Object", "prototype", "hasOwnProperty", "cloneIfNode", "obj", "deep", "withoutLoc", "commentsCache", "type", "cloneNodeInternal", "cloneIfNodeOrArray", "Array", "isArray", "map", "node", "cloneNode", "Map", "newNode", "isIdentifier", "name", "optional", "typeAnnotation", "NODE_FIELDS", "Error", "field", "keys", "isFile", "maybeCloneComments", "comments", "loc", "leadingComments", "innerComments", "trailingComments", "extra", "assign", "comment", "cache", "get", "value", "ret", "set"], "sources": ["../../src/clone/cloneNode.ts"], "sourcesContent": ["import { NODE_FIELDS } from \"../definitions\";\nimport type * as t from \"..\";\nimport { isFile, isIdentifier } from \"../validators/generated\";\n\nconst has = Function.call.bind(Object.prototype.hasOwnProperty);\n\ntype CommentCache = Map<t.Comment, t.Comment>;\n\n// This function will never be called for comments, only for real nodes.\nfunction cloneIfNode(\n  obj: t.Node | undefined | null,\n  deep: boolean,\n  withoutLoc: boolean,\n  commentsCache: CommentCache,\n) {\n  if (obj && typeof obj.type === \"string\") {\n    return cloneNodeInternal(obj, deep, withoutLoc, commentsCache);\n  }\n\n  return obj;\n}\n\nfunction cloneIfNodeOrArray(\n  obj: t.Node | undefined | null | (t.Node | undefined | null)[],\n  deep: boolean,\n  withoutLoc: boolean,\n  commentsCache: CommentCache,\n) {\n  if (Array.isArray(obj)) {\n    return obj.map(node => cloneIfNode(node, deep, withoutLoc, commentsCache));\n  }\n  return cloneIfNode(obj, deep, withoutLoc, commentsCache);\n}\n\n/**\n * Create a clone of a `node` including only properties belonging to the node.\n * If the second parameter is `false`, cloneNode performs a shallow clone.\n * If the third parameter is true, the cloned nodes exclude location properties.\n */\nexport default function cloneNode<T extends t.Node>(\n  node: T,\n  deep: boolean = true,\n  withoutLoc: boolean = false,\n): T {\n  return cloneNodeInternal(node, deep, withoutLoc, new Map());\n}\n\nfunction cloneNodeInternal<T extends t.Node>(\n  node: T,\n  deep: boolean = true,\n  withoutLoc: boolean = false,\n  commentsCache: CommentCache,\n): T {\n  if (!node) return node;\n\n  const { type } = node;\n  const newNode: any = { type: node.type };\n\n  // Special-case identifiers since they are the most cloned nodes.\n  if (isIdentifier(node)) {\n    newNode.name = node.name;\n\n    if (has(node, \"optional\") && typeof node.optional === \"boolean\") {\n      newNode.optional = node.optional;\n    }\n\n    if (has(node, \"typeAnnotation\")) {\n      newNode.typeAnnotation = deep\n        ? cloneIfNodeOrArray(\n            node.typeAnnotation,\n            true,\n            withoutLoc,\n            commentsCache,\n          )\n        : node.typeAnnotation;\n    }\n  } else if (!has(NODE_FIELDS, type)) {\n    throw new Error(`Unknown node type: \"${type}\"`);\n  } else {\n    for (const field of Object.keys(NODE_FIELDS[type])) {\n      if (has(node, field)) {\n        if (deep) {\n          newNode[field] =\n            isFile(node) && field === \"comments\"\n              ? maybeCloneComments(\n                  node.comments,\n                  deep,\n                  withoutLoc,\n                  commentsCache,\n                )\n              : cloneIfNodeOrArray(\n                  // @ts-expect-error node[field] has been guarded by has check\n                  node[field],\n                  true,\n                  withoutLoc,\n                  commentsCache,\n                );\n        } else {\n          newNode[field] =\n            // @ts-expect-error node[field] has been guarded by has check\n            node[field];\n        }\n      }\n    }\n  }\n\n  if (has(node, \"loc\")) {\n    if (withoutLoc) {\n      newNode.loc = null;\n    } else {\n      newNode.loc = node.loc;\n    }\n  }\n  if (has(node, \"leadingComments\")) {\n    newNode.leadingComments = maybeCloneComments(\n      node.leadingComments,\n      deep,\n      withoutLoc,\n      commentsCache,\n    );\n  }\n  if (has(node, \"innerComments\")) {\n    newNode.innerComments = maybeCloneComments(\n      node.innerComments,\n      deep,\n      withoutLoc,\n      commentsCache,\n    );\n  }\n  if (has(node, \"trailingComments\")) {\n    newNode.trailingComments = maybeCloneComments(\n      node.trailingComments,\n      deep,\n      withoutLoc,\n      commentsCache,\n    );\n  }\n  if (has(node, \"extra\")) {\n    newNode.extra = {\n      ...node.extra,\n    };\n  }\n\n  return newNode;\n}\n\nfunction maybeCloneComments<T extends t.Comment>(\n  comments: ReadonlyArray<T> | null,\n  deep: boolean,\n  withoutLoc: boolean,\n  commentsCache: Map<T, T>,\n): ReadonlyArray<T> | null {\n  if (!comments || !deep) {\n    return comments;\n  }\n  return comments.map(comment => {\n    const cache = commentsCache.get(comment);\n    if (cache) return cache;\n\n    const { type, value, loc } = comment;\n\n    const ret = { type, value, loc } as T;\n    if (withoutLoc) {\n      ret.loc = null;\n    }\n\n    commentsCache.set(comment, ret);\n\n    return ret;\n  });\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,UAAA,GAAAD,OAAA;AAEA,MAAME,GAAG,GAAGC,QAAQ,CAACC,IAAI,CAACC,IAAI,CAACC,MAAM,CAACC,SAAS,CAACC,cAAc,CAAC;AAK/D,SAASC,WAAWA,CAClBC,GAA8B,EAC9BC,IAAa,EACbC,UAAmB,EACnBC,aAA2B,EAC3B;EACA,IAAIH,GAAG,IAAI,OAAOA,GAAG,CAACI,IAAI,KAAK,QAAQ,EAAE;IACvC,OAAOC,iBAAiB,CAACL,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,aAAa,CAAC;EAChE;EAEA,OAAOH,GAAG;AACZ;AAEA,SAASM,kBAAkBA,CACzBN,GAA8D,EAC9DC,IAAa,EACbC,UAAmB,EACnBC,aAA2B,EAC3B;EACA,IAAII,KAAK,CAACC,OAAO,CAACR,GAAG,CAAC,EAAE;IACtB,OAAOA,GAAG,CAACS,GAAG,CAACC,IAAI,IAAIX,WAAW,CAACW,IAAI,EAAET,IAAI,EAAEC,UAAU,EAAEC,aAAa,CAAC,CAAC;EAC5E;EACA,OAAOJ,WAAW,CAACC,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,aAAa,CAAC;AAC1D;AAOe,SAASQ,SAASA,CAC/BD,IAAO,EACPT,IAAa,GAAG,IAAI,EACpBC,UAAmB,GAAG,KAAK,EACxB;EACH,OAAOG,iBAAiB,CAACK,IAAI,EAAET,IAAI,EAAEC,UAAU,EAAE,IAAIU,GAAG,CAAC,CAAC,CAAC;AAC7D;AAEA,SAASP,iBAAiBA,CACxBK,IAAO,EACPT,IAAa,GAAG,IAAI,EACpBC,UAAmB,GAAG,KAAK,EAC3BC,aAA2B,EACxB;EACH,IAAI,CAACO,IAAI,EAAE,OAAOA,IAAI;EAEtB,MAAM;IAAEN;EAAK,CAAC,GAAGM,IAAI;EACrB,MAAMG,OAAY,GAAG;IAAET,IAAI,EAAEM,IAAI,CAACN;EAAK,CAAC;EAGxC,IAAI,IAAAU,uBAAY,EAACJ,IAAI,CAAC,EAAE;IACtBG,OAAO,CAACE,IAAI,GAAGL,IAAI,CAACK,IAAI;IAExB,IAAIvB,GAAG,CAACkB,IAAI,EAAE,UAAU,CAAC,IAAI,OAAOA,IAAI,CAACM,QAAQ,KAAK,SAAS,EAAE;MAC/DH,OAAO,CAACG,QAAQ,GAAGN,IAAI,CAACM,QAAQ;IAClC;IAEA,IAAIxB,GAAG,CAACkB,IAAI,EAAE,gBAAgB,CAAC,EAAE;MAC/BG,OAAO,CAACI,cAAc,GAAGhB,IAAI,GACzBK,kBAAkB,CAChBI,IAAI,CAACO,cAAc,EACnB,IAAI,EACJf,UAAU,EACVC,aACF,CAAC,GACDO,IAAI,CAACO,cAAc;IACzB;EACF,CAAC,MAAM,IAAI,CAACzB,GAAG,CAAC0B,wBAAW,EAAEd,IAAI,CAAC,EAAE;IAClC,MAAM,IAAIe,KAAK,CAAE,uBAAsBf,IAAK,GAAE,CAAC;EACjD,CAAC,MAAM;IACL,KAAK,MAAMgB,KAAK,IAAIxB,MAAM,CAACyB,IAAI,CAACH,wBAAW,CAACd,IAAI,CAAC,CAAC,EAAE;MAClD,IAAIZ,GAAG,CAACkB,IAAI,EAAEU,KAAK,CAAC,EAAE;QACpB,IAAInB,IAAI,EAAE;UACRY,OAAO,CAACO,KAAK,CAAC,GACZ,IAAAE,iBAAM,EAACZ,IAAI,CAAC,IAAIU,KAAK,KAAK,UAAU,GAChCG,kBAAkB,CAChBb,IAAI,CAACc,QAAQ,EACbvB,IAAI,EACJC,UAAU,EACVC,aACF,CAAC,GACDG,kBAAkB,CAEhBI,IAAI,CAACU,KAAK,CAAC,EACX,IAAI,EACJlB,UAAU,EACVC,aACF,CAAC;QACT,CAAC,MAAM;UACLU,OAAO,CAACO,KAAK,CAAC,GAEZV,IAAI,CAACU,KAAK,CAAC;QACf;MACF;IACF;EACF;EAEA,IAAI5B,GAAG,CAACkB,IAAI,EAAE,KAAK,CAAC,EAAE;IACpB,IAAIR,UAAU,EAAE;MACdW,OAAO,CAACY,GAAG,GAAG,IAAI;IACpB,CAAC,MAAM;MACLZ,OAAO,CAACY,GAAG,GAAGf,IAAI,CAACe,GAAG;IACxB;EACF;EACA,IAAIjC,GAAG,CAACkB,IAAI,EAAE,iBAAiB,CAAC,EAAE;IAChCG,OAAO,CAACa,eAAe,GAAGH,kBAAkB,CAC1Cb,IAAI,CAACgB,eAAe,EACpBzB,IAAI,EACJC,UAAU,EACVC,aACF,CAAC;EACH;EACA,IAAIX,GAAG,CAACkB,IAAI,EAAE,eAAe,CAAC,EAAE;IAC9BG,OAAO,CAACc,aAAa,GAAGJ,kBAAkB,CACxCb,IAAI,CAACiB,aAAa,EAClB1B,IAAI,EACJC,UAAU,EACVC,aACF,CAAC;EACH;EACA,IAAIX,GAAG,CAACkB,IAAI,EAAE,kBAAkB,CAAC,EAAE;IACjCG,OAAO,CAACe,gBAAgB,GAAGL,kBAAkB,CAC3Cb,IAAI,CAACkB,gBAAgB,EACrB3B,IAAI,EACJC,UAAU,EACVC,aACF,CAAC;EACH;EACA,IAAIX,GAAG,CAACkB,IAAI,EAAE,OAAO,CAAC,EAAE;IACtBG,OAAO,CAACgB,KAAK,GAAAjC,MAAA,CAAAkC,MAAA,KACRpB,IAAI,CAACmB,KAAK,CACd;EACH;EAEA,OAAOhB,OAAO;AAChB;AAEA,SAASU,kBAAkBA,CACzBC,QAAiC,EACjCvB,IAAa,EACbC,UAAmB,EACnBC,aAAwB,EACC;EACzB,IAAI,CAACqB,QAAQ,IAAI,CAACvB,IAAI,EAAE;IACtB,OAAOuB,QAAQ;EACjB;EACA,OAAOA,QAAQ,CAACf,GAAG,CAACsB,OAAO,IAAI;IAC7B,MAAMC,KAAK,GAAG7B,aAAa,CAAC8B,GAAG,CAACF,OAAO,CAAC;IACxC,IAAIC,KAAK,EAAE,OAAOA,KAAK;IAEvB,MAAM;MAAE5B,IAAI;MAAE8B,KAAK;MAAET;IAAI,CAAC,GAAGM,OAAO;IAEpC,MAAMI,GAAG,GAAG;MAAE/B,IAAI;MAAE8B,KAAK;MAAET;IAAI,CAAM;IACrC,IAAIvB,UAAU,EAAE;MACdiC,GAAG,CAACV,GAAG,GAAG,IAAI;IAChB;IAEAtB,aAAa,CAACiC,GAAG,CAACL,OAAO,EAAEI,GAAG,CAAC;IAE/B,OAAOA,GAAG;EACZ,CAAC,CAAC;AACJ"}