import { appmaker } from '@appmaker-xyz/core';
import dayjs from 'dayjs';
import i18n from 'i18next';

function findBlockIndex(blocks, clientId) {
  return blocks.findIndex((block) => block.clientId === clientId);
}

let block = {
  clientId: 'appmaker-ship-within-days',
  name: 'appmaker/actionbar',
  attributes: {
    leftIcon: 'truck',
    title: 'Ship within ',
    subTitle: 'Free shipping on all orders',
  },
};

let deliveryblock = {
  clientId: 'appmaker-delivery-option-block',
  name: 'appmaker/actionbar',
  attributes: {
    leftIcon: 'package',
    title: '100 genuine products',
  },
};

export function addWidgetinProductDetailPage(settings) {
  appmaker.addFilter(
    'product-detail-page-variation-title',
    'product-variationtitle',
    (data) => {
      if (data.split(',').length === 3) {
        let finalTitle = '';
        data.split(',').map((res, key) => {
          if (key == 0) {
            finalTitle = `EU ${res} \n`;
          } else if (key == 1) {
            finalTitle = `${finalTitle}US ${res} \n`;
          } else if (key == 2) {
            finalTitle = `${finalTitle}UK ${res}`;
          }
        });
        return finalTitle;
      }
      return data;
    },
  );

  appmaker.addFilter(
    'inapp-page-data-response',
    'recently-viewed-plugin-sub',
    (data, { pageId }) => {
      if (pageId === 'productDetail') {
        const foundIndexOfDeliveryDate = findBlockIndex(
          data.blocks,
          'appmaker-ship-within-days',
        );
        if (
          settings?.show_delivery_date_in_pdp &&
          foundIndexOfDeliveryDate === -1
        ) {
          let currentDate = dayjs();
          const numToAdd = parseFloat(
            settings?.delivery_within_working_days_count,
          );
          for (let i = 1; i <= numToAdd; i++) {
            currentDate = dayjs(currentDate).add(1, 'day');
            if (dayjs(currentDate).get('day') === 6) {
              currentDate = dayjs(currentDate).add(2, 'day');
            } else if (dayjs(currentDate).get('day') === 0) {
              currentDate = dayjs(currentDate).add(1, 'day');
            }
          }
          const clientId = 'slot-before-description';
          block.attributes.title = `${i18n.t(
            'Estimated Delivery Date',
          )}:${dayjs(currentDate).format('ddd, DD MMM YYYY')}`;
          block.attributes.subTitle = i18n.t(
            'free shipping on all orders over sar 350 00',
          );
          const index = findBlockIndex(data.blocks, clientId);
          data.blocks.splice(index + 1, 0, block);
          data.blocks.splice(index, +1, 0, deliveryblock);
        }
      }
      return data;
    },
  );
}
