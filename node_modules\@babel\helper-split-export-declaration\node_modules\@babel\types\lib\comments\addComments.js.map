{"version": 3, "names": ["addComments", "node", "type", "comments", "key", "concat", "push"], "sources": ["../../src/comments/addComments.ts"], "sourcesContent": ["import type * as t from \"..\";\n\n/**\n * Add comments of certain type to a node.\n */\nexport default function addComments<T extends t.Node>(\n  node: T,\n  type: t.CommentTypeShorthand,\n  comments: Array<t.Comment>,\n): T {\n  if (!comments || !node) return node;\n\n  const key = `${type}Comments` as const;\n\n  if (node[key]) {\n    if (type === \"leading\") {\n      node[key] = comments.concat(node[key]);\n    } else {\n      node[key].push(...comments);\n    }\n  } else {\n    node[key] = comments;\n  }\n\n  return node;\n}\n"], "mappings": ";;;;;;AAKe,SAASA,WAAWA,CACjCC,IAAO,EACPC,IAA4B,EAC5BC,QAA0B,EACvB;EACH,IAAI,CAACA,QAAQ,IAAI,CAACF,IAAI,EAAE,OAAOA,IAAI;EAEnC,MAAMG,GAAG,GAAI,GAAEF,IAAK,UAAkB;EAEtC,IAAID,IAAI,CAACG,GAAG,CAAC,EAAE;IACb,IAAIF,IAAI,KAAK,SAAS,EAAE;MACtBD,IAAI,CAACG,GAAG,CAAC,GAAGD,QAAQ,CAACE,MAAM,CAACJ,IAAI,CAACG,GAAG,CAAC,CAAC;IACxC,CAAC,MAAM;MACLH,IAAI,CAACG,GAAG,CAAC,CAACE,IAAI,CAAC,GAAGH,QAAQ,CAAC;IAC7B;EACF,CAAC,MAAM;IACLF,IAAI,CAACG,GAAG,CAAC,GAAGD,QAAQ;EACtB;EAEA,OAAOF,IAAI;AACb"}