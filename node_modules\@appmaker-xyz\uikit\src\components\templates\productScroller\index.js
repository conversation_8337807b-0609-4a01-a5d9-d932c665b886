import React, { useState } from 'react';
import { View, FlatList, StyleSheet } from 'react-native';
import { spacing, color } from '../../../styles';

import { ProductScrollerItem, Button, AppmakerText } from '../../../components';
import * as config from '@appmaker-xyz/app-config/newConfig/index';
const Item = ({
  item,
  onQuantityChange,
  quatity,
  wishList,
  onPress,
  useAddToCart,
  onSaved,
  staticTexts,
}) => {
  const [state, addtoCart] = useAddToCart();
  const [quantityLoading, setLoading] = useState(false);
  return (
    <ProductScrollerItem
      groceryMode={config.displayOptions.GROCERY_MODE === '1'}
      easyAddCart={false}
      wishList={wishList}
      uri={item.thumbnail}
      saved={item.saved}
      staticTexts={staticTexts}
      onSaved={(status) => {
        onSaved(status, item);
      }}
      onAddtoCart={() => {
        addtoCart(item);
      }}
      title={item.name}
      salePrice={item.on_sale && item.regular_price_display}
      regularPrice={item.price_display}
      onQuantityChange={(qty) => {
        addtoCart(item, qty);
      }}
      quantityLoading={quantityLoading}
      addingTocartLoading={state.isLoading}
      saleBadge={item.sale_percentage || ''}
      onPress={() => {
        let action = {
          type: 'OPEN_PRODUCT',
          params: item,
        };
        // console.log(config.actionHandler.doAction);
        if (item.product_in_webview) {
          action = item.product_in_webview_action;
        }
        onPress(action);
      }}
      outOfStock={item.in_stock ? false : staticTexts.outOfStockText}
    />
  );
};

const ProdcutScroller = (props) => {
  const { useAddToCart, staticTexts, wishList } = props;
  const { data: products, meta, title } = props.data;
  const { onClick, onSaved } = props;

  const titleBar = title ? (
    <View style={styles.title}>
      <AppmakerText category="h1Heading">{title}</AppmakerText>
      {meta.show_view_more_button ? (
        <Button
          onPress={() => onClick(meta.view_more_action)}
          small
          status="white">
          <AppmakerText category="smallButtonText" status="primary">
            {meta.view_more_button_title}
          </AppmakerText>
        </Button>
      ) : null}
    </View>
  ) : null;
  return (
    <View style={styles.container}>
      {titleBar}
      <FlatList
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        data={products}
        renderItem={({ item }) => (
          <Item
            staticTexts={staticTexts}
            item={item}
            wishList={wishList}
            onPress={onClick}
            onSaved={onSaved}
            useAddToCart={useAddToCart}
          />
        )}
        keyExtractor={(item) => item.id.toString()}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: spacing.base,
    backgroundColor: color.white,
    marginBottom: spacing.nano,
  },
  title: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.small,
    paddingHorizontal: spacing.base,
  },
});
export default ProdcutScroller;
