declare module '@appmaker-xyz/shopify' {
  interface ProductReview {
    average_rating: number;
    reviews_count: number;
  }

  interface ProductVariant {
    id: string;
    availableForSale: boolean;
    price: {
      amount: string;
      currencyCode: string;
    };
    compareAtPrice?: {
      amount: string;
      currencyCode: string;
    };
  }

  interface Product {
    node: {
      id: string;
      title: string;
      description: string;
      descriptionHtml: string;
      tags: string[];
      onlineStoreUrl: string;
    };
  }

  interface UseProductDetailProps {
    attributes: {
      product_subtitle?: string;
      average_rating?: number;
      reviews_count?: number;
    };
    onAction: (action: any) => Promise<any>;
    showAddedToCartMessage?: boolean;
  }

  interface UseProductDetailResult {
    shareProduct: () => void;
    descriptionHtml: string;
    description: string;
    savedAmount: string;
    onSale: boolean;
    buyNowLoading: boolean;
    addtoCartLoading: boolean;
    addToCart: (options: { buynow?: boolean; appmakerAction?: any; quantity?: number; customAttributes?: any }) => Promise<any>;
    updateCart: (options: { quantity?: number; appmakerAction?: any }) => Promise<void>;
    count: number;
    adding: boolean;
    setAdding: (adding: boolean) => void;
    setCount: (count: number) => void;
    buyNow: () => Promise<any>;
    title: string;
    tax_included_text: string | null;
    product_subtitle?: string;
    average_rating?: number;
    reviews_count?: number;
    product: Product;
    salePrice: string;
    regularPrice: string;
    currentVariantInStock: boolean;
    regularPriceValue: number;
    tags: string[];
    salePriceValue: number;
    productVariant: ProductVariant;
    variantId: string;
    salePercentage: string | null;
    displayWishlist: boolean;
    displayShareButton: boolean;
    preOrderEnabled: boolean;
  }

  export function useProductDetail(props: UseProductDetailProps): UseProductDetailResult;
}