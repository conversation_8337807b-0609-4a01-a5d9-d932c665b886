import * as React from 'react';
import { Text, View, Image } from 'react-native';

const nativeTypes = {
  View,
  Text,
  Image,
};
export function json2react(schema, customNativeTypes) {
  // console.log(schema);
  if (schema === null) {
    return null;
  }
  if (typeof schema === 'string') {
    return schema;
  }
  const { type, props, children } = schema;
  const Component = nativeTypes[type];
  if (!Component) {
    throw new Error(`${type} is not a valid native type`);
  }
  let childrens;
  if (Array.isArray(children)) {
    childrens = children.map((child) => json2react(child, customNativeTypes));
  } else if (typeof children === 'string') {
    childrens = children;
  } else if (typeof children === 'object') {
    childrens = json2react(children, customNativeTypes);
  }
  return React.createElement(Component, props, childrens);
}
// const create = React.createElement;
// const mapper = {
//   View,
//   Text,
// };
// function json2react(schema) {
//     console.log('------------------');
//     console.log(schema);
//     console.log('------------------1111');
//  // console.log(schema);
//   if (typeof schema === 'undefined') {
//     schema = mapper;
//     mapper = null;
//   }

//   if (schema === null) {
//     return null;
//   }

//   if (typeof schema === 'string') {
//     return schema;
//   }

//   if (!isPlainObject(schema)) {
//     // console.log(schema);
//     console.log(schema, 'schemaschemaschemaschema');
//     throw new Error('schema must be a string or a plain object');
//   }

//   var hasNonEmptySchemaType =
//     schema.type && typeof schema.type === 'string' && schema.type.trim() !== '';

//   if (!hasNonEmptySchemaType) {
//     throw new Error('schema.type must be a non-empty string');
//   }

//   schema.type = schema.type.trim();

//   if (schema.props !== undefined && !isPlainObject(schema.props)) {
//     throw new Error('schema.props must be a plain object');
//   }

//   var type = schema.type;
//   var props = schema.props || null;
//   var children =
//     schema.children &&
//     [].concat(schema.children).map(json2react.bind(null, create, mapper));

//   mapper && (type = mapper(type, props));

//   return create.apply(create, [].concat([type, props]).concat(children));
// }

// function isPlainObject(maybe) {
//   return (
//     maybe !== null &&
//     typeof maybe === 'object' &&
//     Object.prototype.toString.call(maybe) == '[object Object]'
//   );
// }

// // module.exports = json2react;
// export { json2react };
