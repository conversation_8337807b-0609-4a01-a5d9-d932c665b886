import React from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '../../../../theme/ThemeContext';
import { BlockCard, Layout, DropDown } from '../../../../components';
import { PinCheck, LinkCloud, DatePicker } from './blocks/index';

const ProductInfo = ({ clientId, attributes, ...props }) => {
  // console.log(attributes);
  const {
    blockTitle,
    accessButton,
    pinCheck,
    linkCloud,
    datePicker,
    paymentOptions,
  } = attributes;
  // console.log('hello');
  // console.log(JSON.stringify(attributes, null, 2));
  const { spacing } = useApThemeState();
  const styles = allStyles({ spacing });

  return (
    <BlockCard
      clientId={clientId}
      attributes={{ title: blockTitle, accessButton: accessButton }}>
      <Layout>
        {pinCheck && <PinCheck />}
        {linkCloud && <LinkCloud attributes={{ linkCloud: linkCloud }} />}
        {datePicker && <DatePicker />}
        {paymentOptions && (
          <Layout style={styles.subPadding}>
            <DropDown attributes={{ data: paymentOptions }} />
          </Layout>
        )}
      </Layout>
    </BlockCard>
  );
};

const allStyles = ({ spacing }) =>
  StyleSheet.create({
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: spacing.base,
    },
    subPadding: {
      paddingHorizontal: spacing.base,
      paddingBottom: spacing.base,
    },
  });

export default ProductInfo;
