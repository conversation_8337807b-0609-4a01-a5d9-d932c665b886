export function getSavingsValue({
  discountType,
  discountValue,
  subtotal,
  maxDiscountValue,
}) {
  if (discountType === 'percentage') {
    const value = Math.round(
      parseFloat(subtotal) * (parseFloat(discountValue) / 100),
    );
    const maxDiscountValueInt = parseInt(maxDiscountValue, 10);
    if (maxDiscountValueInt && value > maxDiscountValueInt) {
      return maxDiscountValueInt;
    }
    return value;
  }

  if (discountType === 'fixed') {
    return discountValue;
  }
  return 0;
}
export function getMinimumPurchaseRequirementAmount({
  requirementType,
  subtotal,
  cartQuantity,
  requirementValue = 0,
}) {
  if (requirementType === 'cart_value') {
    return parseFloat(requirementValue) - parseFloat(subtotal);
  }
  if (requirementType === 'num_items' || requirementType === 'cart_quantity') {
    return parseFloat(requirementValue) - parseFloat(cartQuantity);
  }
  return 0;
}

export function calculateUpdatedCartSubTotal({ 
  lineItems, 
  coupon, 
  cartSubTotalAmount 
} ){
  if (!coupon?.calculation_based_on_tags) {
    return null;
  }
  let updatedCartSubTotal = 0;
  lineItems?.forEach((lineItem) => {
    const lineItemTags = lineItem.node?.variant?.product?.tags || [];
    const couponTags = coupon.product_tags || [];
    const hasMatchingTags = couponTags?.some((couponTag) =>
      lineItemTags.includes(couponTag.value)
    );
    if (hasMatchingTags) {
      const lineItemPrice = parseFloat(lineItem?.node?.variant?.price?.amount);
      const quantity = lineItem?.node?.quantity;
      updatedCartSubTotal += lineItemPrice * quantity;
    }
  });
  return updatedCartSubTotal;
};