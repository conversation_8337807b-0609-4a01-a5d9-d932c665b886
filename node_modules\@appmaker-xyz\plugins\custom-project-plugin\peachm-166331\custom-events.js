import { appmaker } from '@appmaker-xyz/core';
import produce from 'immer';

export function enableCustomEvents() {
  appmaker.addFilter(
    'firebaseEventData',
    'custom-events',
    ({ eventName, params, eventObject }) => {
      switch (eventName) {
        case 'view_item':
          params.items[0].item_id = eventObject?.product?.product_sku?.value;
          break;
        case 'add_to_cart':
          params.items[0].item_id = eventObject?.product?.product_sku?.value;
          break;
        case 'add_to_wishlist':
          params.items[0].item_id = eventObject?.product?.product_sku?.value;
          break;
        default:
          break;
      }
      return {
        eventName,
        params,
      };
    },
  );
  appmaker.addFilter(
    'cleverTapEventData',
    'custom-events',
    ({ eventName, params, eventObject }) => {
      switch (eventName) {
        case 'Product Viewed':
          // params['Product ID'] = eventObject?.product_sku?.value;
          break;
        case 'Added to Cart':
          // params['Product ID'] = eventObject?.product_sku?.value;
          break;
        case 'Added to Wishlist':
          // params['Product ID'] = eventObject?.product_sku?.value;
          break;
        default:
          break;
      }
      return {
        eventName,
        params,
      };
    },
  );
  appmaker.addFilter('fb_events_params', 'fb_events_params-events', (args) => {
    const { eventName, eventPrice, params, eventObject } = args;
    let modifiedArgs = args;
    switch (eventName) {
      case 'fb_mobile_content_view':
        modifiedArgs = produce(args, (newArgs) => {
          newArgs.params.fb_content_id = eventObject?.product_sku?.value;
        });
        break;
      case 'fb_mobile_add_to_wishlist':
        modifiedArgs = produce(args, (newArgs) => {
          newArgs.params.fb_content_id = eventObject?.product_sku?.value;
        });
        break;
      case 'fb_mobile_add_to_cart':
        modifiedArgs = produce(args, (newArgs) => {
          newArgs.params.fb_content_id = eventObject?.product_sku?.value;
        });
        break;
      case 'fb_mobile_initiated_checkout':
        modifiedArgs = produce(args, (newArgs) => {
          newArgs.params.fb_content_id = JSON.stringify(
            eventObject.lineItems.edges.map(
              ({ node }) => node?.variant?.product.product_sku?.value,
            ),
          );
          newArgs.params.fb_content = JSON.stringify(
            eventObject.lineItems.edges.map(({ node }) => ({
              id: node?.variant?.product.product_sku?.value,
              quantity: node?.quantity,
            })),
          );
        });
        break;
      case 'fb_mobile_purchase':
        modifiedArgs = produce(args, (newArgs) => {
          newArgs.params.fb_content_id = JSON.stringify(
            eventObject.lineItems.edges.map(
              ({ node }) => node?.variant?.product.product_sku?.value,
            ),
          );
          newArgs.params.fb_content = JSON.stringify(
            eventObject.lineItems.edges.map(({ node }) => ({
              id: node?.variant?.product.product_sku?.value,
              quantity: node?.quantity,
            })),
          );
        });
        break;
      default:
        break;
    }
    return modifiedArgs;
  });
}
