import { appmaker } from '@appmaker-xyz/core';
import './old-plugins';
import './shopify-order-customize/index';

import appExclusivePricing from './app-exclusive-pricing/index';

import productBadges from './product-badges/index';

import appCouponsList from './app-coupons-list/index';

import appPermissions from './app-permissions/index';

import customSizeChart from './custom-size-chart/index';

import firebaseCrashlytics from './firebase-crashlytics/index';

import appmakerInterceptAnalytics from './appmaker-intercept-analytics/index';

import appmakerFunctions from './appmaker-functions/index';

import AppmakerWorkflow from './appmaker-workflow/index';

import metaFieldsV2 from './meta-fields-v2/index';

import appmakerInjectBlocks from './appmaker-inject-blocks/index';

import appmakerFreeGift from './appmaker-free-gift/index';

import advancedFilterCustomization from './advanced-filter-customization/index';

import appmakerShopifyCartLimit from './appmaker-shopify-cart-limit/index';

import appmakerBuyXGetY from './appmaker-buy-x-get-y/index';

// import argoid from './argoid/index';

import stampedReviews from './stamped-reviews/index';

import webengage from './webengage/index';

import firebaseRemoteConfig from './firebase-remote-config/index';

import inAppReview from './in-app-review/index';

import lucentSimpleOtp from './lucent-simple-otp/index';

import wigzo from './wigzo/index';

import klaviyo from './klaviyo/index';

import appmakerPlatformDemo from './appmaker-platform-demo/index';

import firebaseOtp from './firebase-otp-login/index';
import appmakerDeveloperMode from './appmaker-developer-mode/index';
import appmakerUpsell from './appmaker-upsell/index';

import appmakerAnalytics from './appmaker-analytics/index';

import shopifyCustomVariations from './shopify-custom-variations/index';
import facebookSdk from './facebook-sdk/index';
import gumstack from './gumstack/index';

// import onboardingScreen from './onboarding-screen/index';
import SesamiBooking from './sesami-booking/index';
import FirebaseAnalytics from './firebase-analytics/index';
import appmakerRecentlyViewed from './appmaker-recently-viewed/index';
import nosto from './nosto/index';
import CustomProjectPlugin from './custom-project-plugin/index';
import AppmakerUiLib from './appmaker-ui-lib';
import CustomOtpLogin from './custom-otp-login/index';
import AppmakerUiKit from './appmaker-ui-lib/index';
import AppleLogin from './apple-login/index';
import GoogleLogin from './google-login/index';
import FacebookLogin from './facebook-login/index';
import AppleTrackingTransparency from './apple-tracking-transparency-v2/index';
import Qwickcilver from './qwickcilver/index';
import AppOnlyCoupons from './app-only-coupons/index';
import appmakerShopifyWishlist from './appmaker-shopify-wishlist';
import searchHistory from './search-history/index';
import orderify from './orderify/index';
import deeplinking from './deeplinking/index';
import ProductExtraFields from './product-extra-fields';
// impoert ends

/**
 * Activating firebase for all apps
 */
appmaker.registerPlugin(FirebaseAnalytics);
AppmakerUiKit.activate({});
// appcenterAnalytics.activate({});

// appmaker.registerPlugin(contlo);
appmaker.registerPlugin(appmakerDeveloperMode);
appmaker.registerPlugin(AppleLogin);
appmaker.registerPlugin(GoogleLogin);
appmaker.registerPlugin(FacebookLogin);
appmaker.registerPlugin(SesamiBooking);
appmaker.registerPlugin(AppmakerWorkflow);
appmaker.registerPlugin(appmakerRecentlyViewed);
appmaker.registerPlugin(nosto);
appmaker.registerPlugin(CustomProjectPlugin);
appmaker.registerPlugin(AppmakerUiLib);
appmaker.registerPlugin(CustomOtpLogin);
// appmaker.registerPlugin(FirebaseAnalytics);
// appmaker.registerPlugin(onboardingScreen);
appmaker.registerPlugin(gumstack);
appmaker.registerPlugin(facebookSdk);
appmaker.registerPlugin(shopifyCustomVariations);
appmaker.registerPlugin(appmakerAnalytics);
appmaker.registerPlugin(firebaseOtp);
appmaker.registerPlugin(appmakerPlatformDemo);

appmaker.registerPlugin(AppleTrackingTransparency);
appmaker.registerPlugin(klaviyo);
appmaker.registerPlugin(appmakerDeveloperMode);

appmaker.registerPlugin(wigzo);

// appmaker.registerPlugin(lucentSimpleOtp);

appmaker.registerPlugin(inAppReview);

appmaker.registerPlugin(firebaseRemoteConfig);

appmaker.registerPlugin(appmakerUpsell);
appmaker.registerPlugin(stampedReviews);

appmaker.registerPlugin(appmakerFreeGift);
appmaker.registerPlugin(appmakerShopifyCartLimit);
appmaker.registerPlugin(appmakerBuyXGetY);

appmaker.registerPlugin(AppOnlyCoupons);
// appmaker.registerPlugin(webengage);
appmaker.registerPlugin(appmakerInjectBlocks);

appmaker.registerPlugin(metaFieldsV2);

appmaker.registerPlugin(Qwickcilver);
appmaker.registerPlugin(appmakerFunctions);

// appmaker.registerPlugin(argoid);
// argoid.activate({});
appmaker.registerPlugin(appmakerInterceptAnalytics);
appmaker.registerPlugin(firebaseCrashlytics);

appmaker.registerPlugin(customSizeChart);

appmaker.registerPlugin(appCouponsList);

appmaker.registerPlugin(appPermissions);
appmaker.registerPlugin(productBadges);
appmaker.registerPlugin(appmakerShopifyWishlist);

appmaker.registerPlugin(appExclusivePricing);
appmaker.registerPlugin(searchHistory);
appmaker.registerPlugin(orderify);
appmaker.registerPlugin(deeplinking);
appmaker.registerPlugin(ProductExtraFields);
