import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Layout, LayoutIcon } from '@appmaker-xyz/ui';
import NewSwiper from './components/NewSwiper';
import Icon from 'react-native-vector-icons/AntDesign';
import {
  useProductWishList,
  ProductBadge,
  useProductDetail,
  useProductImages,
} from '@appmaker-xyz/shopify';
import { testProps, getExtensionAsBoolean } from '@appmaker-xyz/core';

const ProductImage = (props) => {
  const { attributes, blockData } = props;
  const {
    noTopBar,
    centerPagination,
    smallPagination,
    __appmakerCustomStyles = {},
    filterVariantImages,
  } = attributes;
  const isMedia = getExtensionAsBoolean?.(
    'shopify',
    'enable_pdp_video_media',
    false,
  );
  const {
    onAction,
    index,
    imageList,
    customDotColor,
    imageRatio,
    swiperRef,
    openImage,
    media,
  } = useProductImages({
    ...props,
    isMedia,
    filterVariantImages,
  });

  const { isSaved, toggleWishList } = useProductWishList({
    onAction,
    blockData,
  });

  const productImageContainerStyles = [styles.productImageContainer];

  if (imageRatio) {
    productImageContainerStyles.push({
      aspectRatio: 1 / imageRatio,
      height: null,
    });
  }

  const { product, shareProduct, displayWishlist, displayShareButton } =
    useProductDetail(props);
  return (
    <View style={productImageContainerStyles}>
      <NewSwiper
        swiperRef={swiperRef}
        openImage={openImage}
        onAction={onAction}
        attributes={{
          isMedia,
          showLoadingIndicator: true,
          index,
          smallPagination,
          imageList: imageList || [],
          media: media,
          autoplay: false,
          appmakerAction: attributes.appmakerAction,
          customDotColor:
            __appmakerCustomStyles?.product_image?.active_dot_color ||
            customDotColor,
          centerPagination: centerPagination == 1 ? true : false,
        }}
      />
      <TopLeftSlot>
        <ProductBadge product={product} slot="top-left" type="product-detail" />
      </TopLeftSlot>
      <TopRightSlot>
        <ProductBadge
          product={product}
          slot="top-right"
          type="product-detail"
        />
      </TopRightSlot>
      <BottomLeft>
        <ProductBadge
          product={product}
          slot="bottom-left"
          type="product-detail"
        />
      </BottomLeft>
      <BottomRightSlot>
        <ProductBadge
          product={product}
          slot="bottom-right"
          type="product-detail"
        />
      </BottomRightSlot>
      {noTopBar && (
        <Layout style={styles.overlayTop}>
          <LayoutIcon
            onAction={onAction}
            attributes={{
              iconName: 'arrow-left',
              overlay: true,
              appmakerAction: { action: 'GO_BACK' },
            }}
          />
          <LayoutIcon
            onAction={onAction}
            attributes={{
              iconName: 'shopping-cart',
              overlay: true,
              appmakerAction: { action: 'OPEN_CART' },
            }}
          />
        </Layout>
      )}
      {(displayWishlist || displayShareButton) && (
        <Layout
          style={[
            styles.overlayRight,
            __appmakerCustomStyles?.product_image?.overlay_container,
          ]}>
          {displayWishlist && (
            <Icon
              {...testProps('product-image-wishlist-button')}
              name={!isSaved ? 'hearto' : 'heart'}
              color={!isSaved ? '#4F4F4F' : '#DC2626'}
              size={18}
              style={[
                styles.overlayIcon,
                __appmakerCustomStyles?.product_image?.overlay_icon,
              ]}
              onPress={() => {
                toggleWishList && toggleWishList();
              }}
            />
          )}
          {displayShareButton && (
            <Icon
              {...testProps('product-image-share-button')}
              name="sharealt"
              size={18}
              style={[
                styles.overlayIcon,
                __appmakerCustomStyles?.product_image?.overlay_icon,
              ]}
              onPress={shareProduct}
            />
          )}
        </Layout>
      )}
    </View>
  );
};

function TopLeftSlot({ children }) {
  return <Layout style={styles.topLeftContainer}>{children}</Layout>;
}

function TopRightSlot({ children }) {
  return <Layout style={styles.topRightContainer}>{children}</Layout>;
}
function BottomLeft({ children }) {
  return <Layout style={styles.bottomLeftContainer}>{children}</Layout>;
}
function BottomRightSlot({ children }) {
  return <Layout style={styles.bottomRightContainer}>{children}</Layout>;
}

const styles = StyleSheet.create({
  productImageContainer: {
    height: 450,
    width: '100%',
    position: 'relative',
  },
  overlayRight: {
    position: 'absolute',
    bottom: 12,
    right: 12,
  },
  overlayTop: {
    position: 'absolute',
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    padding: 12,
    left: -5,
  },
  overlayIcon: {
    padding: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    overflow: 'hidden',
    marginVertical: 2,
  },
  topLeftContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  topRightContainer: {
    position: 'absolute',
    top: 0,
    right: 0,
  },
  bottomLeftContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
  },
  bottomRightContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
  },
});
export default ProductImage;
