import React, { useState, useRef } from 'react';
import { StyleSheet, Pressable, Animated } from 'react-native';
import { ThemeText, Layout } from '@appmaker-xyz/ui';
import { useOrderItem } from '@appmaker-xyz/shopify';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

const OrderDetails = (props) => {
  // const [isAccordionOpen, setIsAccordionOpen] = useState(true);
  // const animatedHeight = useRef(new Animated.Value(0)).current;
  // const animatedRotate = useRef(new Animated.Value(0)).current;

  const {
    email,
    billingAddress,
    shippingAddress,
    paymentMethod,
    totalShippingPrice,
  } = useOrderItem(props);

  const formatText = (text) => {
    return text.replace(/_/g, ' ').replace(/\w\S*/g, (word) => {
      return word.charAt(0).toUpperCase() + word.substr(1).toLowerCase();
    });
  };

  // const toggleAccordion = () => {
  //   const toValue = isAccordionOpen ? 0 : 1;
  //   Animated.parallel([
  //     Animated.timing(animatedHeight, {
  //       toValue,
  //       duration: 300,
  //       useNativeDriver: false,
  //     }),
  //     Animated.timing(animatedRotate, {
  //       toValue,
  //       duration: 300,
  //       useNativeDriver: true,
  //     }),
  //   ]).start();
  //   setIsAccordionOpen(!isAccordionOpen);
  // };

  // const maxHeight = animatedHeight.interpolate({
  //   inputRange: [0, 1],
  //   outputRange: [0, 300], // Adjust this value based on your content
  // });

  // const rotate = animatedRotate.interpolate({
  //   inputRange: [0, 1],
  //   outputRange: ['0deg', '180deg'],
  // });

  const addressData = [
    {
      title: 'Shipping Address',
      data: shippingAddress,
    },
    {
      title: 'Billing Address',
      data: billingAddress,
    },
  ];

  const additionalData = [
    {
      title: 'Shipping Method',
      data: !!parseFloat(totalShippingPrice?.amount)
        ? 'Flat Rate'
        : 'Free Shipping',
    },
    {
      title: 'Payment Status',
      data: formatText(paymentMethod),
    },
  ];

  return (
    <Layout>
      <Pressable style={styles.titleContainer}>
        <ThemeText style={styles.titleText} fontFamily="bold">
          Order Details
        </ThemeText>
      </Pressable>
      <Layout style={styles.content}>
        <Layout style={styles.contentInner}>
          <Layout>
            <ThemeText style={styles.detailTitle} fontFamily="bold">
              Contact Information
            </ThemeText>
            <ThemeText style={styles.detailContent}>{email}</ThemeText>
          </Layout>
          <Layout style={styles.gridWrapper}>
            {addressData?.map((item, index) => {
              const addressFields = [
                {
                  key: 'name',
                  format: (data) => `${data?.firstName} ${data?.lastName}`,
                },
                'address1',
                'address2',
                'city',
                'country',
                'zip',
                'phone',
              ];
              return (
                <Layout key={index} style={{ width: '50%' }}>
                  <ThemeText style={styles.detailTitle} fontFamily="bold">
                    {item?.title}
                  </ThemeText>
                  {addressFields?.map((field, index) => {
                    const value =
                      typeof field === 'object'
                        ? field?.format(item?.data)
                        : item?.data?.[field];

                    return value ? (
                      <ThemeText key={index} style={styles.detailContent}>
                        {value}
                      </ThemeText>
                    ) : null;
                  })}
                </Layout>
              );
            })}
          </Layout>
          <Layout style={styles.gridWrapper}>
            {additionalData?.map((item, index) => (
              <Layout key={index} style={{ width: '50%' }}>
                <ThemeText style={styles.detailTitle} fontFamily="bold">
                  {item?.title}
                </ThemeText>
                <ThemeText>{item?.data}</ThemeText>
              </Layout>
            ))}
          </Layout>
        </Layout>
      </Layout>
    </Layout>
  );
};

export default OrderDetails;

const styles = StyleSheet.create({
  titleContainer: {
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: 'rgba(0,0,0,0.25)',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  titleText: {
    fontSize: 16,
    color: '#000',
  },
  content: {
    overflow: 'hidden',
  },
  contentInner: {
    padding: 16,
  },
  detailTitle: {},
  detailContent: {},
  gridWrapper: {
    flexDirection: 'row',
    marginTop: 16,
  },
});
