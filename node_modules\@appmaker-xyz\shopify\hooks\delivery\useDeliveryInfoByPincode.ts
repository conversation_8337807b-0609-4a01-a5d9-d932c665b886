import { useState, useEffect, useCallback } from 'react';
import { applyFilters, } from '@appmaker-xyz/core';
import { useAppStorage } from '@appmaker-xyz/core';

interface DeliveryInfo {
  iconName: string;
  message: string;
  zip_code: string;
  message_type?: string;
  tone: string;
  filterType: string;
  rawData?: any;
}

export const useDeliveryInfoByPincode = ({
  pincode: initialPincode = '',
  product,
}: {
  product?: any;
  pincode?: string;
} = {}) => {
  const [deliveryInfos, setDeliveryInfos] = useState<DeliveryInfo[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const defaultPincode = applyFilters(
    'appmaker-delivery-checker-initial-pincode',
    initialPincode,
  ); // Default pincode to use if no pincode is provided or saved and they want

  const { pincode: savedPincode, setState: setAppStorage } = useAppStorage(state => ({
    pincode: state?.delivery_pincode || '',
    setState: state.setState
  }));
  // Determines the effective pincode, prioritizing the provided pincode over the saved one.
  const effectivePincode = initialPincode || savedPincode || defaultPincode;
  const isDefault = (defaultPincode && !initialPincode && !savedPincode);
  const fetchDeliveryInfo = useCallback(async (submittedPincode: string, savePincode: boolean = true) => {
    setIsLoading(true);
    try {
      const deliveryData = false;
      const deliveryInfoChecker = applyFilters('delivery-info-checker-function', null, { deliveryData, product });

      if (typeof deliveryInfoChecker === 'function') {
        let serverResponse = await deliveryInfoChecker(submittedPincode, { deliveryData, product });
        serverResponse = await applyFilters('delivery-info-checker-response', serverResponse, { pincode: submittedPincode, deliveryData, product });    
        setDeliveryInfos(serverResponse);
      } else {
        setDeliveryInfos([]);
      }
      if(savePincode){
        if(savedPincode !== submittedPincode){
          setAppStorage((state: any) => {
            state.delivery_pincode = submittedPincode;
          });
        }
      }
    } catch (error) {
      console.error('Error fetching delivery info:', error);
    } finally {
      setIsLoading(false);
    }
  }, [product?.id]);

  useEffect(() => {
    if (effectivePincode && product?.id) {
      fetchDeliveryInfo(effectivePincode, !isDefault);
    }
  }, [effectivePincode, product?.id, fetchDeliveryInfo]);

  return { deliveryInfos, isLoading, pincode: effectivePincode, fetchDeliveryInfo };
};