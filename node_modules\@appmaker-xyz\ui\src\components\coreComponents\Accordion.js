import React, { useState } from 'react';
import {
  StyleSheet,
  LayoutAnimation,
  Platform,
  UIManager,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { AppTouchable, Layout, ThemeText } from '../atoms';
import { isEmpty } from 'lodash';

const Accordion = ({
  attributes,
  onPress,
  innerBlocks,
  onAction,
  blockData,
  BlocksView,
}) => {
  const {
    data,
    title,
    loading,
    type,
    filter,
    icons = ['plus', 'minus'],
    active,
    initiallyExpanded = false,
    containerStyle,
    titleContainerStyle,
    titleTextStyle,
    iconStyle,
    dataContainerStyle,
    dataTextStyle,
    showOnlyIf,
    obeyShowOnlyIf = false,
  } = attributes;

  const [expand, setExpand] = useState(initiallyExpanded);
  if (Platform.OS === 'android') {
    if (UIManager.setLayoutAnimationEnabledExperimental) {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }
  }
  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpand(!expand);
  };

  const containerStyles = [styles.container];
  const titleTextStyles = [styles.title];

  if (containerStyle) {
    containerStyles.push(containerStyle);
  }

  if (titleTextStyle) {
    titleTextStyles.push(titleTextStyle);
  }

  if (filter) {
    titleTextStyles.push(styles.filterAccordionTitle);
  }

  const activeState = onPress && active ? active : expand;
  const iconName = activeState ? icons[1] : icons[0];
  const hasInnerBlocks = innerBlocks && innerBlocks.length > 0;

  if (obeyShowOnlyIf && isEmpty(showOnlyIf)) {
    return null;
  }

  return data || hasInnerBlocks ? (
    <Layout style={containerStyles}>
      <AppTouchable
        style={[styles.row, titleContainerStyle]}
        onPress={onPress ? onPress : toggleExpand}>
        <ThemeText size="lg" fontFamily="medium" style={titleTextStyles}>
          {title}
        </ThemeText>
        {loading ? (
          <ActivityIndicator color="#1B1B1B" />
        ) : (
          <Icon name={iconName} size={18} color="#1B1B1B" style={iconStyle} />
        )}
      </AppTouchable>
      {activeState ? (
        hasInnerBlocks ? (
          <Layout style={dataContainerStyle}>
            <BlocksView
              onAction={onAction}
              customBlockData={blockData}
              inAppPage={{
                blocks: innerBlocks,
                attributes: { renderType: 'normal', padding: 0 },
              }}
            />
          </Layout>
        ) : (
          <Layout style={[styles.child, dataContainerStyle]}>
            {typeof data === 'string' ? (
              <ThemeText
                html={type && type === 'html' ? true : false}
                style={dataTextStyle}>
                {data}
              </ThemeText>
            ) : (
              <Layout>{data}</Layout>
            )}
          </Layout>
        )
      ) : null}
    </Layout>
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 4,
    borderBottomColor: '#E9EDF1',
    borderBottomWidth: 1,
    backgroundColor: '#FFFFFF',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingTop: 8,
    paddingBottom: 12,
  },
  filterAccordionTitle: { fontSize: 17 },
  title: {
    flexGrow: 1,
    flexShrink: 1,
  },
  child: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
});

export default Accordion;
