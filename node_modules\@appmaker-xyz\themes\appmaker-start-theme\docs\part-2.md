## Create Molecules

All Molecules are components that are composed of Atoms. They are used to create more complex components.

### `Badge`

Path:

`appmaker-start-theme/uikit/components/molecules/Badge.js`

sample code

```js
import React from 'react';
import { View } from 'react-native';
import { ThemeText } from '@appmaker-start-theme/uikit';

const Badge = ({
  color = '#000',
  textColor = '#fff',
  children,
  containerStyle,
  textStyle,
}) => {
  const finalContainerStyles = {
    backgroundColor: color,
    paddingHorizontal: 4,
    flexDirection: 'row',
    alignItems: 'center',
    textAlign: 'center',
    alignSelf: 'flex-start',
    ...containerStyle,
  };
  const finalTextStyles = {
    ...textStyle,
  };
  return (
    <View style={finalContainerStyles}>
      <ThemeText color={textColor} size="sm" style={finalTextStyles}>
        {children}
      </ThemeText>
    </View>
  );
};

export default Badge;
```
