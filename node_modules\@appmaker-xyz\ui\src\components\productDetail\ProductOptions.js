import { ScrollView, StyleSheet } from 'react-native';
import React from 'react';
import { Layout, ThemeText, AppTouchable, AppImage } from '../atoms';
import CrossLine from '../../assets/images/cross-line.png';

export default function ProductOptions({
  name,
  options,
  setOption,
  selectedOption,
  isOptionAvailable,
}) {
  function textColor(option) {
    if (selectedOption === option.value) {
      return '#FFFFFF';
    }
    if (!isOptionAvailable(option.value)) {
      return '#A9AEB7';
    }
    return '#4F4F4F';
  }
  return (
    <Layout style={styles.container}>
      <Layout style={styles.titleContainer}>
        <ThemeText size="lg" fontFamily="medium">
          {name}
        </ThemeText>
        {/* <AppTouchable>
          <ThemeText>Size Chart</ThemeText>
        </AppTouchable> */}
      </Layout>
      <ScrollView horizontal contentContainerStyle={styles.contentContainer}>
        {options.map((option) => {
          const isSelected = selectedOption === option.value;
          return (
            <AppTouchable
              key={`options-${option.key}`}
              onPress={() => setOption(option)}
              disabled={!isOptionAvailable(option.value)}
              style={[
                styles.itemContainer,
                isSelected
                  ? styles.itemContainerSelected
                  : styles.itemContainerUnselected,
              ]}>
              {!isOptionAvailable(option.value) ? (
                <AppImage src={CrossLine} style={styles.muteImage} />
              ) : null}
              <ThemeText color={textColor(option)} style={styles.itemText}>
                {option.value}
              </ThemeText>
            </AppTouchable>
          );
        })}
      </ScrollView>
    </Layout>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    marginBottom: 4,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingTop: 12,
  },
  contentContainer: {
    paddingHorizontal: 8,
    paddingVertical: 12,
  },
  itemContainer: {
    marginHorizontal: 4,
    borderRadius: 6,
    borderWidth: 2,
    position: 'relative',
    overflow: 'hidden',
  },
  itemText: { margin: 8 },
  muteImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    zIndex: 10,
    resizeMode: 'stretch',
  },
  itemContainerUnselected: {
    borderColor: '#A9AEB7',
  },
  itemContainerSelected: {
    borderColor: '#1b1b1b',
    backgroundColor: '#1b1b1b',
  },
});
