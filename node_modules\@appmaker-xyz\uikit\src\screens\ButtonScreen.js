import React from 'react';
import { View, StyleSheet, ScrollView, ActivityIndicator } from 'react-native';
import { spacing } from '../styles';
import {
  AppmakerText,
  Button,
  Stepper,
  Radio,
  Layout,
  Badge,
} from '../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { useApThemeDispatch } from '../theme/ThemeContext';
const ButtonIcon = (props) => <Icon name="aperture" color="#fff" {...props} />;
const FacebookButtonIcon = (props) => (
  <Icon name="facebook" color="#fff" {...props} />
);
const LoadingIndicator = () => <ActivityIndicator size="small" color="#fff" />;

function getRandomColor() {
  var letters = '0123456789ABCDEF';
  var color = '#';
  for (var i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
}

const ButtonScreen = () => {
  const dispatch = useApThemeDispatch();
  return (
    <ScrollView>
      <View style={styles.container}>
        <View style={{ marginVertical: spacing.small }}>
          <AppmakerText category="h1Heading">Block Buttons</AppmakerText>
          <Button accessoryLeft={FacebookButtonIcon}>Left Icons</Button>
          <Button
            status="dark"
            onPress={() => {
              const newColors = {
                red: getRandomColor(),
                yellow: getRandomColor(),
                green: getRandomColor(),
                blue: getRandomColor(),
                dark: getRandomColor(),
                demiDark: getRandomColor(),
                grey: getRandomColor(),
                light: getRandomColor(),
                white: getRandomColor(),
              };
              dispatch({
                type: 'set_config',
                name: 'color',
                value: newColors,
              });
            }}>
            Change theme
          </Button>
          <Button accessoryRight={ButtonIcon}>Right Icon</Button>
          <Button accessoryLeft={LoadingIndicator}>Loading</Button>
          <Button accessoryLeft={LoadingIndicator} />
          <Button>Primary Button</Button>
          <Button>
            <Layout style={{ backgroundColor: 'pink' }}>
              <AppmakerText category="h1Heading">
                Custom styled View Inside Button
              </AppmakerText>
            </Layout>
          </Button>
          <Button outline status="primary">
            Primary Outline Button
          </Button>
          <Button status="success">Status Green</Button>
          <Button status="warning">Status Yellow</Button>
          <Button status="dark">Status Dark</Button>
          <Button status="danger">Status Red</Button>
          <Button status="light">Status Light</Button>
          <Button status="white">Status White</Button>
        </View>
        <View style={{ marginVertical: spacing.small }}>
          <AppmakerText category="h1Heading">Block Buttons -Small</AppmakerText>
          <Button small>Primary Button</Button>
          <Button accessoryLeft={LoadingIndicator} small>
            Primary Button
          </Button>
          <Button
            accessoryLeft={LoadingIndicator}
            small
            outline
            status="primary">
            Loading
          </Button>
        </View>
        <View style={{ marginVertical: spacing.small }}>
          <AppmakerText category="h1Heading">Buttons</AppmakerText>
          <View style={styles.splitContainer}>
            <Button accessoryLeft={LoadingIndicator}>Primary Button</Button>
            <Button>Primary Button</Button>
            <Button outline status="primary">
              Primary Button
            </Button>
          </View>
        </View>
        <View style={{ marginVertical: spacing.small }}>
          <AppmakerText category="h1Heading">Buttons -Small</AppmakerText>
          <View style={styles.splitContainer}>
            <Button small accessoryLeft={LoadingIndicator}>
              Primary Button
            </Button>
            <Button small>Primary Button</Button>
            <Button small outline status="primary">
              Primary Button
            </Button>
          </View>
        </View>
        <View style={{ marginVertical: spacing.small }}>
          <AppmakerText category="h1Heading">Stepper button</AppmakerText>
          <View style={styles.splitContainer}>
            <Stepper />
            <Stepper customizable />
            <Stepper adding={true} customizable />
          </View>
        </View>
        <View style={{ marginVertical: spacing.small }}>
          <AppmakerText category="h1Heading">Radio</AppmakerText>
          <View style={styles.splitContainer}>
            <Radio
              radio_items={[
                { label: 'Checked', value: 0 },
                { label: 'Not checked', value: 1 },
              ]}
            />
          </View>
        </View>
        <View style={{ marginVertical: spacing.small }}>
          <AppmakerText category="h1Heading">Badge</AppmakerText>
          <View style={styles.splitContainer}>
            <Badge text="Badge" />
            <Badge status="success" text="status success" />
            <Badge status="danger" text="status danger" />
            <Badge status="warning" text="status warning" />
            <Badge status="dark" text="status dark" />
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: spacing.base,
  },
  splitContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  horzontalContainer: {
    width: '100%',
  },
});

export default ButtonScreen;
