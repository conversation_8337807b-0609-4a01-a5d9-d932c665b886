{"version": 3, "names": ["NodeDescriptions", "ArrayPattern", "AssignmentExpression", "AssignmentPattern", "ArrowFunctionExpression", "ConditionalExpression", "CatchClause", "ForOfStatement", "ForInStatement", "ForStatement", "FormalParameters", "Identifier", "ImportSpecifier", "ImportDefaultSpecifier", "ImportNamespaceSpecifier", "ObjectPattern", "ParenthesizedExpression", "RestElement", "UpdateExpression", "true", "false", "VariableDeclarator", "YieldExpression", "toNodeDescription", "type", "prefix", "String", "_default", "exports", "default"], "sources": ["../../src/parse-error/to-node-description.ts"], "sourcesContent": ["const NodeDescriptions = {\n  ArrayPattern: \"array destructuring pattern\",\n  AssignmentExpression: \"assignment expression\",\n  AssignmentPattern: \"assignment expression\",\n  ArrowFunctionExpression: \"arrow function expression\",\n  ConditionalExpression: \"conditional expression\",\n  CatchClause: \"catch clause\",\n  ForOfStatement: \"for-of statement\",\n  ForInStatement: \"for-in statement\",\n  ForStatement: \"for-loop\",\n  FormalParameters: \"function parameter list\",\n  Identifier: \"identifier\",\n  ImportSpecifier: \"import specifier\",\n  ImportDefaultSpecifier: \"import default specifier\",\n  ImportNamespaceSpecifier: \"import namespace specifier\",\n  ObjectPattern: \"object destructuring pattern\",\n  ParenthesizedExpression: \"parenthesized expression\",\n  RestElement: \"rest element\",\n  UpdateExpression: {\n    true: \"prefix operation\",\n    false: \"postfix operation\",\n  },\n  VariableDeclarator: \"variable declaration\",\n  YieldExpression: \"yield expression\",\n};\n\ntype NodeTypesWithDescriptions = keyof Omit<\n  typeof NodeDescriptions,\n  \"UpdateExpression\"\n>;\n\ntype NodeWithDescription =\n  | {\n      type: \"UpdateExpression\";\n      prefix: boolean;\n    }\n  | {\n      type: NodeTypesWithDescriptions;\n    };\n\n// @ts-expect-error prefix is specified only when type is UpdateExpression\n// eslint-disable-next-line no-confusing-arrow\nconst toNodeDescription = ({ type, prefix }: NodeWithDescription) =>\n  type === \"UpdateExpression\"\n    ? NodeDescriptions.UpdateExpression[String(prefix) as \"true\" | \"false\"]\n    : NodeDescriptions[type];\n\nexport default toNodeDescription;\n"], "mappings": ";;;;;;AAAA,MAAMA,gBAAgB,GAAG;EACvBC,YAAY,EAAE,6BAA6B;EAC3CC,oBAAoB,EAAE,uBAAuB;EAC7CC,iBAAiB,EAAE,uBAAuB;EAC1CC,uBAAuB,EAAE,2BAA2B;EACpDC,qBAAqB,EAAE,wBAAwB;EAC/CC,WAAW,EAAE,cAAc;EAC3BC,cAAc,EAAE,kBAAkB;EAClCC,cAAc,EAAE,kBAAkB;EAClCC,YAAY,EAAE,UAAU;EACxBC,gBAAgB,EAAE,yBAAyB;EAC3CC,UAAU,EAAE,YAAY;EACxBC,eAAe,EAAE,kBAAkB;EACnCC,sBAAsB,EAAE,0BAA0B;EAClDC,wBAAwB,EAAE,4BAA4B;EACtDC,aAAa,EAAE,8BAA8B;EAC7CC,uBAAuB,EAAE,0BAA0B;EACnDC,WAAW,EAAE,cAAc;EAC3BC,gBAAgB,EAAE;IAChBC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDC,kBAAkB,EAAE,sBAAsB;EAC1CC,eAAe,EAAE;AACnB,CAAC;AAkBD,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAA4B,CAAC,KAC9DD,IAAI,KAAK,kBAAkB,GACvBxB,gBAAgB,CAACkB,gBAAgB,CAACQ,MAAM,CAACD,MAAM,CAAC,CAAqB,GACrEzB,gBAAgB,CAACwB,IAAI,CAAC;AAAC,IAAAG,QAAA,GAEdJ,iBAAiB;AAAAK,OAAA,CAAAC,OAAA,GAAAF,QAAA"}