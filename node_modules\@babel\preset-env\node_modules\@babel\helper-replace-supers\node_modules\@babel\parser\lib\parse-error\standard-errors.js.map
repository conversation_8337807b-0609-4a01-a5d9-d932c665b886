{"version": 3, "names": ["_toNodeDescription", "require", "_default", "AccessorIsGenerator", "kind", "ArgumentsInClass", "AsyncFunctionInSingleStatementContext", "AwaitBindingIdentifier", "AwaitBindingIdentifierInStaticBlock", "AwaitExpressionFormalParameter", "AwaitUsingNotInAsyncContext", "AwaitNotInAsyncContext", "AwaitNotInAsyncFunction", "BadGetterArity", "BadSetterArity", "BadSetterRestParameter", "ConstructorClassField", "ConstructorClassPrivateField", "ConstructorIsAccessor", "ConstructorIsAsync", "ConstructorIsGenerator", "DeclarationMissingInitializer", "DecoratorArgumentsOutsideParentheses", "DecoratorBeforeExport", "DecoratorsBeforeAfterExport", "DecoratorConstructor", "DecoratorExportClass", "DecoratorSemicolon", "DecoratorStaticBlock", "DeletePrivateField", "DestructureNamedImport", "DuplicateConstructor", "DuplicateDefaultExport", "DuplicateExport", "exportName", "DuplicateProto", "DuplicateRegExpFlags", "ElementAfterRest", "EscapedCharNotAnIdentifier", "ExportBindingIsString", "localName", "ExportDefaultFromAsIdentifier", "ForInOfLoopInitializer", "type", "ForInUsing", "ForOfAsync", "ForOfLet", "GeneratorInSingleStatementContext", "IllegalBreakContinue", "IllegalLanguageModeDirective", "IllegalReturn", "ImportAttributesUseAssert", "ImportBindingIsString", "importName", "ImportCallArgumentTrailingComma", "ImportCallArity", "maxArgumentCount", "ImportCallNotNewExpression", "ImportCallSpreadArgument", "ImportJSONBindingNotDefault", "ImportReflectionHasAssertion", "ImportReflectionNotBinding", "IncompatibleRegExpUVFlags", "InvalidBigIntLiteral", "InvalidCodePoint", "InvalidCoverInitializedName", "InvalidDecimal", "InvalidDigit", "radix", "InvalidEscapeSequence", "InvalidEscapeSequenceTemplate", "InvalidEscapedReservedWord", "reservedWord", "InvalidIdentifier", "identifierName", "InvalidLhs", "ancestor", "toNodeDescription", "InvalidLhsBinding", "InvalidNumber", "InvalidOrMissingExponent", "InvalidOrUnexpectedToken", "unexpected", "InvalidParenthesizedAssignment", "InvalidPrivateFieldResolution", "InvalidPropertyBindingPattern", "InvalidRecordProperty", "InvalidRestAssignmentPattern", "LabelRedeclaration", "labelName", "LetInLexicalBinding", "LineTerminatorBeforeArrow", "MalformedRegExpFlags", "MissingClassName", "MissingEqInAssignment", "MissingSemicolon", "MissingPlugin", "missingPlugin", "map", "name", "JSON", "stringify", "join", "MissingOneOfPlugins", "MissingUnicodeEscape", "MixingCoalesceWithLogical", "ModuleAttributeDifferentFromType", "ModuleAttributeInvalidValue", "ModuleAttributesWithDuplicateKeys", "key", "ModuleExportNameHasLoneSurrogate", "surrogateCharCode", "toString", "ModuleExportUndefined", "MultipleDefaultsInSwitch", "NewlineAfterThrow", "NoCatchOrFinally", "NumberIdentifier", "NumericSeparatorInEscapeSequence", "ObsoleteAwaitStar", "OptionalChainingNoNew", "OptionalChainingNoTemplate", "OverrideOnConstructor", "ParamDupe", "PatternHasAccessor", "PatternHasMethod", "PrivateInExpectedIn", "PrivateNameRedeclaration", "RecordExpressionBarIncorrectEndSyntaxType", "RecordExpressionBarIncorrectStartSyntaxType", "RecordExpressionHashIncorrectStartSyntaxType", "RecordNoProto", "RestTrailingComma", "SloppyFunction", "SloppyFunctionAnnexB", "StaticPrototype", "SuperNotAllowed", "SuperPrivateField", "TrailingDecorator", "TupleExpressionBarIncorrectEndSyntaxType", "TupleExpressionBarIncorrectStartSyntaxType", "TupleExpressionHashIncorrectStartSyntaxType", "UnexpectedArgumentPlaceholder", "UnexpectedAwaitAfterPipelineBody", "UnexpectedDigitAfterHash", "UnexpectedImportExport", "UnexpectedKeyword", "keyword", "UnexpectedLeadingDecorator", "UnexpectedLexicalDeclaration", "UnexpectedNewTarget", "UnexpectedNumericSeparator", "UnexpectedPrivateField", "UnexpectedReservedWord", "UnexpectedSuper", "UnexpectedToken", "expected", "UnexpectedTokenUnaryExponentiation", "UnexpectedUsingDeclaration", "UnsupportedBind", "UnsupportedDecoratorExport", "UnsupportedDefaultExport", "UnsupportedImport", "UnsupportedMetaProperty", "target", "onlyValidPropertyName", "UnsupportedParameterDecorator", "UnsupportedPropertyDecorator", "UnsupportedSuper", "UnterminatedComment", "UnterminatedRegExp", "UnterminatedString", "UnterminatedTemplate", "UsingDeclarationHasBindingPattern", "VarRedeclaration", "YieldBindingIdentifier", "YieldInParameter", "ZeroDigitNumericSeparator", "exports", "default"], "sources": ["../../src/parse-error/standard-errors.ts"], "sourcesContent": ["import toNodeDescription from \"./to-node-description\";\n\nexport type LValAncestor =\n  | { type: \"UpdateExpression\"; prefix: boolean }\n  | {\n      type:\n        | \"ArrayPattern\"\n        | \"AssignmentExpression\"\n        | \"CatchClause\"\n        | \"ForOfStatement\"\n        | \"FormalParameters\"\n        | \"ForInStatement\"\n        | \"ForStatement\"\n        | \"ImportSpecifier\"\n        | \"ImportNamespaceSpecifier\"\n        | \"ImportDefaultSpecifier\"\n        | \"ParenthesizedExpression\"\n        | \"ObjectPattern\"\n        | \"RestElement\"\n        | \"VariableDeclarator\";\n    };\n\nexport default {\n  AccessorIsGenerator: ({ kind }: { kind: \"get\" | \"set\" }) =>\n    `A ${kind}ter cannot be a generator.`,\n  ArgumentsInClass:\n    \"'arguments' is only allowed in functions and class methods.\",\n  AsyncFunctionInSingleStatementContext:\n    \"Async functions can only be declared at the top level or inside a block.\",\n  AwaitBindingIdentifier:\n    \"Can not use 'await' as identifier inside an async function.\",\n  AwaitBindingIdentifierInStaticBlock:\n    \"Can not use 'await' as identifier inside a static block.\",\n  AwaitExpressionFormalParameter:\n    \"'await' is not allowed in async function parameters.\",\n  AwaitUsingNotInAsyncContext:\n    \"'await using' is only allowed within async functions and at the top levels of modules.\",\n  AwaitNotInAsyncContext:\n    \"'await' is only allowed within async functions and at the top levels of modules.\",\n  AwaitNotInAsyncFunction: \"'await' is only allowed within async functions.\",\n  BadGetterArity: \"A 'get' accessor must not have any formal parameters.\",\n  BadSetterArity: \"A 'set' accessor must have exactly one formal parameter.\",\n  BadSetterRestParameter:\n    \"A 'set' accessor function argument must not be a rest parameter.\",\n  ConstructorClassField: \"Classes may not have a field named 'constructor'.\",\n  ConstructorClassPrivateField:\n    \"Classes may not have a private field named '#constructor'.\",\n  ConstructorIsAccessor: \"Class constructor may not be an accessor.\",\n  ConstructorIsAsync: \"Constructor can't be an async function.\",\n  ConstructorIsGenerator: \"Constructor can't be a generator.\",\n  DeclarationMissingInitializer: ({\n    kind,\n  }: {\n    kind: \"const\" | \"destructuring\";\n  }) => `Missing initializer in ${kind} declaration.`,\n  DecoratorArgumentsOutsideParentheses:\n    \"Decorator arguments must be moved inside parentheses: use '@(decorator(args))' instead of '@(decorator)(args)'.\",\n  DecoratorBeforeExport:\n    \"Decorators must be placed *before* the 'export' keyword. Remove the 'decoratorsBeforeExport: true' option to use the 'export @decorator class {}' syntax.\",\n  DecoratorsBeforeAfterExport:\n    \"Decorators can be placed *either* before or after the 'export' keyword, but not in both locations at the same time.\",\n  DecoratorConstructor:\n    \"Decorators can't be used with a constructor. Did you mean '@dec class { ... }'?\",\n  DecoratorExportClass:\n    \"Decorators must be placed *after* the 'export' keyword. Remove the 'decoratorsBeforeExport: false' option to use the '@decorator export class {}' syntax.\",\n  DecoratorSemicolon: \"Decorators must not be followed by a semicolon.\",\n  DecoratorStaticBlock: \"Decorators can't be used with a static block.\",\n  DeletePrivateField: \"Deleting a private field is not allowed.\",\n  DestructureNamedImport:\n    \"ES2015 named imports do not destructure. Use another statement for destructuring after the import.\",\n  DuplicateConstructor: \"Duplicate constructor in the same class.\",\n  DuplicateDefaultExport: \"Only one default export allowed per module.\",\n  DuplicateExport: ({ exportName }: { exportName: string }) =>\n    `\\`${exportName}\\` has already been exported. Exported identifiers must be unique.`,\n  DuplicateProto: \"Redefinition of __proto__ property.\",\n  DuplicateRegExpFlags: \"Duplicate regular expression flag.\",\n  ElementAfterRest: \"Rest element must be last element.\",\n  EscapedCharNotAnIdentifier: \"Invalid Unicode escape.\",\n  ExportBindingIsString: ({\n    localName,\n    exportName,\n  }: {\n    localName: string;\n    exportName: string;\n  }) =>\n    `A string literal cannot be used as an exported binding without \\`from\\`.\\n- Did you mean \\`export { '${localName}' as '${exportName}' } from 'some-module'\\`?`,\n  ExportDefaultFromAsIdentifier:\n    \"'from' is not allowed as an identifier after 'export default'.\",\n\n  ForInOfLoopInitializer: ({\n    type,\n  }: {\n    type: \"ForInStatement\" | \"ForOfStatement\";\n  }) =>\n    `'${\n      type === \"ForInStatement\" ? \"for-in\" : \"for-of\"\n    }' loop variable declaration may not have an initializer.`,\n  ForInUsing: \"For-in loop may not start with 'using' declaration.\",\n\n  ForOfAsync: \"The left-hand side of a for-of loop may not be 'async'.\",\n  ForOfLet: \"The left-hand side of a for-of loop may not start with 'let'.\",\n  GeneratorInSingleStatementContext:\n    \"Generators can only be declared at the top level or inside a block.\",\n\n  IllegalBreakContinue: ({\n    type,\n  }: {\n    type: \"BreakStatement\" | \"ContinueStatement\";\n  }) => `Unsyntactic ${type === \"BreakStatement\" ? \"break\" : \"continue\"}.`,\n\n  IllegalLanguageModeDirective:\n    \"Illegal 'use strict' directive in function with non-simple parameter list.\",\n  IllegalReturn: \"'return' outside of function.\",\n  ImportAttributesUseAssert:\n    \"The `assert` keyword in import attributes is deprecated and it has been replaced by the `with` keyword. You can enable the `deprecatedAssertSyntax: true` option in the import attributes plugin to suppress this error.\",\n  ImportBindingIsString: ({ importName }: { importName: string }) =>\n    `A string literal cannot be used as an imported binding.\\n- Did you mean \\`import { \"${importName}\" as foo }\\`?`,\n  ImportCallArgumentTrailingComma:\n    \"Trailing comma is disallowed inside import(...) arguments.\",\n  ImportCallArity: ({ maxArgumentCount }: { maxArgumentCount: 1 | 2 }) =>\n    `\\`import()\\` requires exactly ${\n      maxArgumentCount === 1 ? \"one argument\" : \"one or two arguments\"\n    }.`,\n  ImportCallNotNewExpression: \"Cannot use new with import(...).\",\n  ImportCallSpreadArgument: \"`...` is not allowed in `import()`.\",\n  ImportJSONBindingNotDefault:\n    \"A JSON module can only be imported with `default`.\",\n  ImportReflectionHasAssertion: \"`import module x` cannot have assertions.\",\n  ImportReflectionNotBinding:\n    'Only `import module x from \"./module\"` is valid.',\n  IncompatibleRegExpUVFlags:\n    \"The 'u' and 'v' regular expression flags cannot be enabled at the same time.\",\n  InvalidBigIntLiteral: \"Invalid BigIntLiteral.\",\n  InvalidCodePoint: \"Code point out of bounds.\",\n  InvalidCoverInitializedName: \"Invalid shorthand property initializer.\",\n  InvalidDecimal: \"Invalid decimal.\",\n  InvalidDigit: ({ radix }: { radix: number }) =>\n    `Expected number in radix ${radix}.`,\n  InvalidEscapeSequence: \"Bad character escape sequence.\",\n  InvalidEscapeSequenceTemplate: \"Invalid escape sequence in template.\",\n  InvalidEscapedReservedWord: ({ reservedWord }: { reservedWord: string }) =>\n    `Escape sequence in keyword ${reservedWord}.`,\n  InvalidIdentifier: ({ identifierName }: { identifierName: string }) =>\n    `Invalid identifier ${identifierName}.`,\n  InvalidLhs: ({ ancestor }: { ancestor: LValAncestor }) =>\n    `Invalid left-hand side in ${toNodeDescription(ancestor)}.`,\n  InvalidLhsBinding: ({ ancestor }: { ancestor: LValAncestor }) =>\n    `Binding invalid left-hand side in ${toNodeDescription(ancestor)}.`,\n  InvalidNumber: \"Invalid number.\",\n  InvalidOrMissingExponent:\n    \"Floating-point numbers require a valid exponent after the 'e'.\",\n  InvalidOrUnexpectedToken: ({ unexpected }: { unexpected: string }) =>\n    `Unexpected character '${unexpected}'.`,\n  InvalidParenthesizedAssignment: \"Invalid parenthesized assignment pattern.\",\n  InvalidPrivateFieldResolution: ({\n    identifierName,\n  }: {\n    identifierName: string;\n  }) => `Private name #${identifierName} is not defined.`,\n  InvalidPropertyBindingPattern: \"Binding member expression.\",\n  InvalidRecordProperty:\n    \"Only properties and spread elements are allowed in record definitions.\",\n  InvalidRestAssignmentPattern: \"Invalid rest operator's argument.\",\n  LabelRedeclaration: ({ labelName }: { labelName: string }) =>\n    `Label '${labelName}' is already declared.`,\n  LetInLexicalBinding:\n    \"'let' is not allowed to be used as a name in 'let' or 'const' declarations.\",\n  LineTerminatorBeforeArrow: \"No line break is allowed before '=>'.\",\n  MalformedRegExpFlags: \"Invalid regular expression flag.\",\n  MissingClassName: \"A class name is required.\",\n  MissingEqInAssignment:\n    \"Only '=' operator can be used for specifying default value.\",\n  MissingSemicolon: \"Missing semicolon.\",\n  MissingPlugin: ({ missingPlugin }: { missingPlugin: [string] }) =>\n    `This experimental syntax requires enabling the parser plugin: ${missingPlugin\n      .map(name => JSON.stringify(name))\n      .join(\", \")}.`,\n  // FIXME: Would be nice to make this \"missingPlugins\" instead.\n  // Also, seems like we can drop the \"(s)\" from the message and just make it \"s\".\n  MissingOneOfPlugins: ({ missingPlugin }: { missingPlugin: string[] }) =>\n    `This experimental syntax requires enabling one of the following parser plugin(s): ${missingPlugin\n      .map(name => JSON.stringify(name))\n      .join(\", \")}.`,\n  MissingUnicodeEscape: \"Expecting Unicode escape sequence \\\\uXXXX.\",\n  MixingCoalesceWithLogical:\n    \"Nullish coalescing operator(??) requires parens when mixing with logical operators.\",\n  ModuleAttributeDifferentFromType:\n    \"The only accepted module attribute is `type`.\",\n  ModuleAttributeInvalidValue:\n    \"Only string literals are allowed as module attribute values.\",\n  ModuleAttributesWithDuplicateKeys: ({ key }: { key: string }) =>\n    `Duplicate key \"${key}\" is not allowed in module attributes.`,\n  ModuleExportNameHasLoneSurrogate: ({\n    surrogateCharCode,\n  }: {\n    surrogateCharCode: number;\n  }) =>\n    `An export name cannot include a lone surrogate, found '\\\\u${surrogateCharCode.toString(\n      16,\n    )}'.`,\n  ModuleExportUndefined: ({ localName }: { localName: string }) =>\n    `Export '${localName}' is not defined.`,\n  MultipleDefaultsInSwitch: \"Multiple default clauses.\",\n  NewlineAfterThrow: \"Illegal newline after throw.\",\n  NoCatchOrFinally: \"Missing catch or finally clause.\",\n  NumberIdentifier: \"Identifier directly after number.\",\n  NumericSeparatorInEscapeSequence:\n    \"Numeric separators are not allowed inside unicode escape sequences or hex escape sequences.\",\n  ObsoleteAwaitStar:\n    \"'await*' has been removed from the async functions proposal. Use Promise.all() instead.\",\n  OptionalChainingNoNew:\n    \"Constructors in/after an Optional Chain are not allowed.\",\n  OptionalChainingNoTemplate:\n    \"Tagged Template Literals are not allowed in optionalChain.\",\n  OverrideOnConstructor:\n    \"'override' modifier cannot appear on a constructor declaration.\",\n  ParamDupe: \"Argument name clash.\",\n  PatternHasAccessor: \"Object pattern can't contain getter or setter.\",\n  PatternHasMethod: \"Object pattern can't contain methods.\",\n  PrivateInExpectedIn: ({ identifierName }: { identifierName: string }) =>\n    `Private names are only allowed in property accesses (\\`obj.#${identifierName}\\`) or in \\`in\\` expressions (\\`#${identifierName} in obj\\`).`,\n  PrivateNameRedeclaration: ({ identifierName }: { identifierName: string }) =>\n    `Duplicate private name #${identifierName}.`,\n  RecordExpressionBarIncorrectEndSyntaxType:\n    \"Record expressions ending with '|}' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.\",\n  RecordExpressionBarIncorrectStartSyntaxType:\n    \"Record expressions starting with '{|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.\",\n  RecordExpressionHashIncorrectStartSyntaxType:\n    \"Record expressions starting with '#{' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.\",\n  RecordNoProto: \"'__proto__' is not allowed in Record expressions.\",\n  RestTrailingComma: \"Unexpected trailing comma after rest element.\",\n  SloppyFunction:\n    \"In non-strict mode code, functions can only be declared at top level or inside a block.\",\n  SloppyFunctionAnnexB:\n    \"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement.\",\n  StaticPrototype: \"Classes may not have static property named prototype.\",\n  SuperNotAllowed:\n    \"`super()` is only valid inside a class constructor of a subclass. Maybe a typo in the method name ('constructor') or not extending another class?\",\n  SuperPrivateField: \"Private fields can't be accessed on super.\",\n  TrailingDecorator: \"Decorators must be attached to a class element.\",\n  TupleExpressionBarIncorrectEndSyntaxType:\n    \"Tuple expressions ending with '|]' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.\",\n  TupleExpressionBarIncorrectStartSyntaxType:\n    \"Tuple expressions starting with '[|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.\",\n  TupleExpressionHashIncorrectStartSyntaxType:\n    \"Tuple expressions starting with '#[' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.\",\n  UnexpectedArgumentPlaceholder: \"Unexpected argument placeholder.\",\n  UnexpectedAwaitAfterPipelineBody:\n    'Unexpected \"await\" after pipeline body; await must have parentheses in minimal proposal.',\n  UnexpectedDigitAfterHash: \"Unexpected digit after hash token.\",\n  UnexpectedImportExport:\n    \"'import' and 'export' may only appear at the top level.\",\n  UnexpectedKeyword: ({ keyword }: { keyword: string }) =>\n    `Unexpected keyword '${keyword}'.`,\n  UnexpectedLeadingDecorator:\n    \"Leading decorators must be attached to a class declaration.\",\n  UnexpectedLexicalDeclaration:\n    \"Lexical declaration cannot appear in a single-statement context.\",\n  UnexpectedNewTarget:\n    \"`new.target` can only be used in functions or class properties.\",\n  UnexpectedNumericSeparator:\n    \"A numeric separator is only allowed between two digits.\",\n  UnexpectedPrivateField: \"Unexpected private name.\",\n  UnexpectedReservedWord: ({ reservedWord }: { reservedWord: string }) =>\n    `Unexpected reserved word '${reservedWord}'.`,\n  UnexpectedSuper: \"'super' is only allowed in object methods and classes.\",\n  UnexpectedToken: ({\n    expected,\n    unexpected,\n  }: {\n    expected?: string | null;\n    unexpected?: string | null;\n  }) =>\n    `Unexpected token${unexpected ? ` '${unexpected}'.` : \"\"}${\n      expected ? `, expected \"${expected}\"` : \"\"\n    }`,\n  UnexpectedTokenUnaryExponentiation:\n    \"Illegal expression. Wrap left hand side or entire exponentiation in parentheses.\",\n  UnexpectedUsingDeclaration:\n    \"Using declaration cannot appear in the top level when source type is `script`.\",\n  UnsupportedBind: \"Binding should be performed on object property.\",\n  UnsupportedDecoratorExport:\n    \"A decorated export must export a class declaration.\",\n  UnsupportedDefaultExport:\n    \"Only expressions, functions or classes are allowed as the `default` export.\",\n  UnsupportedImport:\n    \"`import` can only be used in `import()` or `import.meta`.\",\n  UnsupportedMetaProperty: ({\n    target,\n    onlyValidPropertyName,\n  }: {\n    target: string;\n    onlyValidPropertyName: string;\n  }) =>\n    `The only valid meta property for ${target} is ${target}.${onlyValidPropertyName}.`,\n  UnsupportedParameterDecorator:\n    \"Decorators cannot be used to decorate parameters.\",\n  UnsupportedPropertyDecorator:\n    \"Decorators cannot be used to decorate object literal properties.\",\n  UnsupportedSuper:\n    \"'super' can only be used with function calls (i.e. super()) or in property accesses (i.e. super.prop or super[prop]).\",\n  UnterminatedComment: \"Unterminated comment.\",\n  UnterminatedRegExp: \"Unterminated regular expression.\",\n  UnterminatedString: \"Unterminated string constant.\",\n  UnterminatedTemplate: \"Unterminated template.\",\n  UsingDeclarationHasBindingPattern:\n    \"Using declaration cannot have destructuring patterns.\",\n  VarRedeclaration: ({ identifierName }: { identifierName: string }) =>\n    `Identifier '${identifierName}' has already been declared.`,\n  YieldBindingIdentifier:\n    \"Can not use 'yield' as identifier inside a generator.\",\n  YieldInParameter: \"Yield expression is not allowed in formal parameters.\",\n  ZeroDigitNumericSeparator:\n    \"Numeric separator can not be used after leading 0.\",\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAAsD,IAAAC,QAAA,GAsBvC;EACbC,mBAAmB,EAAEA,CAAC;IAAEC;EAA8B,CAAC,KACpD,KAAIA,IAAK,4BAA2B;EACvCC,gBAAgB,EACd,6DAA6D;EAC/DC,qCAAqC,EACnC,0EAA0E;EAC5EC,sBAAsB,EACpB,6DAA6D;EAC/DC,mCAAmC,EACjC,0DAA0D;EAC5DC,8BAA8B,EAC5B,sDAAsD;EACxDC,2BAA2B,EACzB,wFAAwF;EAC1FC,sBAAsB,EACpB,kFAAkF;EACpFC,uBAAuB,EAAE,iDAAiD;EAC1EC,cAAc,EAAE,uDAAuD;EACvEC,cAAc,EAAE,0DAA0D;EAC1EC,sBAAsB,EACpB,kEAAkE;EACpEC,qBAAqB,EAAE,mDAAmD;EAC1EC,4BAA4B,EAC1B,4DAA4D;EAC9DC,qBAAqB,EAAE,2CAA2C;EAClEC,kBAAkB,EAAE,yCAAyC;EAC7DC,sBAAsB,EAAE,mCAAmC;EAC3DC,6BAA6B,EAAEA,CAAC;IAC9BjB;EAGF,CAAC,KAAM,0BAAyBA,IAAK,eAAc;EACnDkB,oCAAoC,EAClC,iHAAiH;EACnHC,qBAAqB,EACnB,2JAA2J;EAC7JC,2BAA2B,EACzB,qHAAqH;EACvHC,oBAAoB,EAClB,iFAAiF;EACnFC,oBAAoB,EAClB,2JAA2J;EAC7JC,kBAAkB,EAAE,iDAAiD;EACrEC,oBAAoB,EAAE,+CAA+C;EACrEC,kBAAkB,EAAE,0CAA0C;EAC9DC,sBAAsB,EACpB,oGAAoG;EACtGC,oBAAoB,EAAE,0CAA0C;EAChEC,sBAAsB,EAAE,6CAA6C;EACrEC,eAAe,EAAEA,CAAC;IAAEC;EAAmC,CAAC,KACrD,KAAIA,UAAW,oEAAmE;EACrFC,cAAc,EAAE,qCAAqC;EACrDC,oBAAoB,EAAE,oCAAoC;EAC1DC,gBAAgB,EAAE,oCAAoC;EACtDC,0BAA0B,EAAE,yBAAyB;EACrDC,qBAAqB,EAAEA,CAAC;IACtBC,SAAS;IACTN;EAIF,CAAC,KACE,wGAAuGM,SAAU,SAAQN,UAAW,2BAA0B;EACjKO,6BAA6B,EAC3B,gEAAgE;EAElEC,sBAAsB,EAAEA,CAAC;IACvBC;EAGF,CAAC,KACE,IACCA,IAAI,KAAK,gBAAgB,GAAG,QAAQ,GAAG,QACxC,0DAAyD;EAC5DC,UAAU,EAAE,qDAAqD;EAEjEC,UAAU,EAAE,yDAAyD;EACrEC,QAAQ,EAAE,+DAA+D;EACzEC,iCAAiC,EAC/B,qEAAqE;EAEvEC,oBAAoB,EAAEA,CAAC;IACrBL;EAGF,CAAC,KAAM,eAAcA,IAAI,KAAK,gBAAgB,GAAG,OAAO,GAAG,UAAW,GAAE;EAExEM,4BAA4B,EAC1B,4EAA4E;EAC9EC,aAAa,EAAE,+BAA+B;EAC9CC,yBAAyB,EACvB,0NAA0N;EAC5NC,qBAAqB,EAAEA,CAAC;IAAEC;EAAmC,CAAC,KAC3D,uFAAsFA,UAAW,eAAc;EAClHC,+BAA+B,EAC7B,4DAA4D;EAC9DC,eAAe,EAAEA,CAAC;IAAEC;EAA8C,CAAC,KAChE,iCACCA,gBAAgB,KAAK,CAAC,GAAG,cAAc,GAAG,sBAC3C,GAAE;EACLC,0BAA0B,EAAE,kCAAkC;EAC9DC,wBAAwB,EAAE,qCAAqC;EAC/DC,2BAA2B,EACzB,oDAAoD;EACtDC,4BAA4B,EAAE,2CAA2C;EACzEC,0BAA0B,EACxB,kDAAkD;EACpDC,yBAAyB,EACvB,8EAA8E;EAChFC,oBAAoB,EAAE,wBAAwB;EAC9CC,gBAAgB,EAAE,2BAA2B;EAC7CC,2BAA2B,EAAE,yCAAyC;EACtEC,cAAc,EAAE,kBAAkB;EAClCC,YAAY,EAAEA,CAAC;IAAEC;EAAyB,CAAC,KACxC,4BAA2BA,KAAM,GAAE;EACtCC,qBAAqB,EAAE,gCAAgC;EACvDC,6BAA6B,EAAE,sCAAsC;EACrEC,0BAA0B,EAAEA,CAAC;IAAEC;EAAuC,CAAC,KACpE,8BAA6BA,YAAa,GAAE;EAC/CC,iBAAiB,EAAEA,CAAC;IAAEC;EAA2C,CAAC,KAC/D,sBAAqBA,cAAe,GAAE;EACzCC,UAAU,EAAEA,CAAC;IAAEC;EAAqC,CAAC,KAClD,6BAA4B,IAAAC,0BAAiB,EAACD,QAAQ,CAAE,GAAE;EAC7DE,iBAAiB,EAAEA,CAAC;IAAEF;EAAqC,CAAC,KACzD,qCAAoC,IAAAC,0BAAiB,EAACD,QAAQ,CAAE,GAAE;EACrEG,aAAa,EAAE,iBAAiB;EAChCC,wBAAwB,EACtB,gEAAgE;EAClEC,wBAAwB,EAAEA,CAAC;IAAEC;EAAmC,CAAC,KAC9D,yBAAwBA,UAAW,IAAG;EACzCC,8BAA8B,EAAE,2CAA2C;EAC3EC,6BAA6B,EAAEA,CAAC;IAC9BV;EAGF,CAAC,KAAM,iBAAgBA,cAAe,kBAAiB;EACvDW,6BAA6B,EAAE,4BAA4B;EAC3DC,qBAAqB,EACnB,wEAAwE;EAC1EC,4BAA4B,EAAE,mCAAmC;EACjEC,kBAAkB,EAAEA,CAAC;IAAEC;EAAiC,CAAC,KACtD,UAASA,SAAU,wBAAuB;EAC7CC,mBAAmB,EACjB,6EAA6E;EAC/EC,yBAAyB,EAAE,uCAAuC;EAClEC,oBAAoB,EAAE,kCAAkC;EACxDC,gBAAgB,EAAE,2BAA2B;EAC7CC,qBAAqB,EACnB,6DAA6D;EAC/DC,gBAAgB,EAAE,oBAAoB;EACtCC,aAAa,EAAEA,CAAC;IAAEC;EAA2C,CAAC,KAC3D,iEAAgEA,aAAa,CAC3EC,GAAG,CAACC,IAAI,IAAIC,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,CAAC,CACjCG,IAAI,CAAC,IAAI,CAAE,GAAE;EAGlBC,mBAAmB,EAAEA,CAAC;IAAEN;EAA2C,CAAC,KACjE,qFAAoFA,aAAa,CAC/FC,GAAG,CAACC,IAAI,IAAIC,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,CAAC,CACjCG,IAAI,CAAC,IAAI,CAAE,GAAE;EAClBE,oBAAoB,EAAE,4CAA4C;EAClEC,yBAAyB,EACvB,qFAAqF;EACvFC,gCAAgC,EAC9B,+CAA+C;EACjDC,2BAA2B,EACzB,8DAA8D;EAChEC,iCAAiC,EAAEA,CAAC;IAAEC;EAAqB,CAAC,KACzD,kBAAiBA,GAAI,wCAAuC;EAC/DC,gCAAgC,EAAEA,CAAC;IACjCC;EAGF,CAAC,KACE,6DAA4DA,iBAAiB,CAACC,QAAQ,CACrF,EACF,CAAE,IAAG;EACPC,qBAAqB,EAAEA,CAAC;IAAEzE;EAAiC,CAAC,KACzD,WAAUA,SAAU,mBAAkB;EACzC0E,wBAAwB,EAAE,2BAA2B;EACrDC,iBAAiB,EAAE,8BAA8B;EACjDC,gBAAgB,EAAE,kCAAkC;EACpDC,gBAAgB,EAAE,mCAAmC;EACrDC,gCAAgC,EAC9B,6FAA6F;EAC/FC,iBAAiB,EACf,yFAAyF;EAC3FC,qBAAqB,EACnB,0DAA0D;EAC5DC,0BAA0B,EACxB,4DAA4D;EAC9DC,qBAAqB,EACnB,iEAAiE;EACnEC,SAAS,EAAE,sBAAsB;EACjCC,kBAAkB,EAAE,gDAAgD;EACpEC,gBAAgB,EAAE,uCAAuC;EACzDC,mBAAmB,EAAEA,CAAC;IAAEpD;EAA2C,CAAC,KACjE,+DAA8DA,cAAe,oCAAmCA,cAAe,aAAY;EAC9IqD,wBAAwB,EAAEA,CAAC;IAAErD;EAA2C,CAAC,KACtE,2BAA0BA,cAAe,GAAE;EAC9CsD,yCAAyC,EACvC,mIAAmI;EACrIC,2CAA2C,EACzC,qIAAqI;EACvIC,4CAA4C,EAC1C,sIAAsI;EACxIC,aAAa,EAAE,mDAAmD;EAClEC,iBAAiB,EAAE,+CAA+C;EAClEC,cAAc,EACZ,yFAAyF;EAC3FC,oBAAoB,EAClB,0HAA0H;EAC5HC,eAAe,EAAE,uDAAuD;EACxEC,eAAe,EACb,mJAAmJ;EACrJC,iBAAiB,EAAE,4CAA4C;EAC/DC,iBAAiB,EAAE,iDAAiD;EACpEC,wCAAwC,EACtC,kIAAkI;EACpIC,0CAA0C,EACxC,oIAAoI;EACtIC,2CAA2C,EACzC,qIAAqI;EACvIC,6BAA6B,EAAE,kCAAkC;EACjEC,gCAAgC,EAC9B,0FAA0F;EAC5FC,wBAAwB,EAAE,oCAAoC;EAC9DC,sBAAsB,EACpB,yDAAyD;EAC3DC,iBAAiB,EAAEA,CAAC;IAAEC;EAA6B,CAAC,KACjD,uBAAsBA,OAAQ,IAAG;EACpCC,0BAA0B,EACxB,6DAA6D;EAC/DC,4BAA4B,EAC1B,kEAAkE;EACpEC,mBAAmB,EACjB,iEAAiE;EACnEC,0BAA0B,EACxB,yDAAyD;EAC3DC,sBAAsB,EAAE,0BAA0B;EAClDC,sBAAsB,EAAEA,CAAC;IAAEjF;EAAuC,CAAC,KAChE,6BAA4BA,YAAa,IAAG;EAC/CkF,eAAe,EAAE,wDAAwD;EACzEC,eAAe,EAAEA,CAAC;IAChBC,QAAQ;IACR1E;EAIF,CAAC,KACE,mBAAkBA,UAAU,GAAI,KAAIA,UAAW,IAAG,GAAG,EAAG,GACvD0E,QAAQ,GAAI,eAAcA,QAAS,GAAE,GAAG,EACzC,EAAC;EACJC,kCAAkC,EAChC,kFAAkF;EACpFC,0BAA0B,EACxB,gFAAgF;EAClFC,eAAe,EAAE,iDAAiD;EAClEC,0BAA0B,EACxB,qDAAqD;EACvDC,wBAAwB,EACtB,6EAA6E;EAC/EC,iBAAiB,EACf,2DAA2D;EAC7DC,uBAAuB,EAAEA,CAAC;IACxBC,MAAM;IACNC;EAIF,CAAC,KACE,oCAAmCD,MAAO,OAAMA,MAAO,IAAGC,qBAAsB,GAAE;EACrFC,6BAA6B,EAC3B,mDAAmD;EACrDC,4BAA4B,EAC1B,kEAAkE;EACpEC,gBAAgB,EACd,uHAAuH;EACzHC,mBAAmB,EAAE,uBAAuB;EAC5CC,kBAAkB,EAAE,kCAAkC;EACtDC,kBAAkB,EAAE,+BAA+B;EACnDC,oBAAoB,EAAE,wBAAwB;EAC9CC,iCAAiC,EAC/B,uDAAuD;EACzDC,gBAAgB,EAAEA,CAAC;IAAEpG;EAA2C,CAAC,KAC9D,eAAcA,cAAe,8BAA6B;EAC7DqG,sBAAsB,EACpB,uDAAuD;EACzDC,gBAAgB,EAAE,uDAAuD;EACzEC,yBAAyB,EACvB;AACJ,CAAC;AAAAC,OAAA,CAAAC,OAAA,GAAAjL,QAAA"}