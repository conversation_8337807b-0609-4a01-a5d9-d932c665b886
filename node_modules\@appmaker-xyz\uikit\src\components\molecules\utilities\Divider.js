import React from 'react';
import { Layout } from '@appmaker-xyz/uikit';

const Divider = ({ attributes, clientId }) => {
  const { borderWidth = 1, borderColor = '#E9EDF1', gap = 0 } = attributes;

  return (
    <Layout style={{ paddingVertical: parseInt(gap) }}>
      <Layout
        style={{ height: parseInt(borderWidth), backgroundColor: borderColor }}
      />
    </Layout>
  );
};

export default Divider;
