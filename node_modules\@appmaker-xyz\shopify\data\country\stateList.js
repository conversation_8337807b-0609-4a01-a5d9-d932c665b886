const countryState = {
  AF: {
    provinces: [],
  },
  AX: {
    provinces: [],
  },
  AL: {
    provinces: [],
  },
  DZ: {
    provinces: [],
  },
  AD: {
    provinces: [],
  },
  AO: {
    provinces: [],
  },
  AI: {
    provinces: [],
  },
  AG: {
    provinces: [],
  },
  AR: {
    provinces: [
      {
        label: 'Ciudad Autónoma de Buenos Aires',
        value: 'C',
      },
      {
        label: 'Buenos Aires',
        value: 'B',
      },
      {
        label: 'Catamarca',
        value: 'K',
      },
      {
        label: 'Chaco',
        value: 'H',
      },
      {
        label: 'Chubut',
        value: 'U',
      },
      {
        label: 'Córdoba',
        value: 'X',
      },
      {
        label: 'Corrientes',
        value: 'W',
      },
      {
        label: 'Entre Ríos',
        value: 'E',
      },
      {
        label: 'Formosa',
        value: 'P',
      },
      {
        label: 'Jujuy',
        value: 'Y',
      },
      {
        label: 'La Pampa',
        value: 'L',
      },
      {
        label: 'La Rioja',
        value: 'F',
      },
      {
        label: 'Mendoza',
        value: 'M',
      },
      {
        label: 'Misiones',
        value: 'N',
      },
      {
        label: 'Neuquén',
        value: 'Q',
      },
      {
        label: 'Río Negro',
        value: 'R',
      },
      {
        label: 'Salta',
        value: 'A',
      },
      {
        label: 'San Juan',
        value: 'J',
      },
      {
        label: 'San Luis',
        value: 'D',
      },
      {
        label: 'Santa Cruz',
        value: 'Z',
      },
      {
        label: 'Santa Fe',
        value: 'S',
      },
      {
        label: 'Santiago Del Estero',
        value: 'G',
      },
      {
        label: 'Tierra Del Fuego',
        value: 'V',
      },
      {
        label: 'Tucumán',
        value: 'T',
      },
    ],
  },
  AM: {
    provinces: [],
  },
  AW: {
    provinces: [],
  },
  AC: {
    provinces: [],
  },
  AU: {
    provinces: [
      {
        label: 'Australian Capital Territory',
        value: 'ACT',
      },
      {
        label: 'New South Wales',
        value: 'NSW',
      },
      {
        label: 'Northern Territory',
        value: 'NT',
      },
      {
        label: 'Queensland',
        value: 'QLD',
      },
      {
        label: 'South Australia',
        value: 'SA',
      },
      {
        label: 'Tasmania',
        value: 'TAS',
      },
      {
        label: 'Victoria',
        value: 'VIC',
      },
      {
        label: 'Western Australia',
        value: 'WA',
      },
    ],
  },
  AT: {
    provinces: [],
  },
  AZ: {
    provinces: [],
  },
  BS: {
    provinces: [],
  },
  BH: {
    provinces: [],
  },
  BD: {
    provinces: [],
  },
  BB: {
    provinces: [],
  },
  BY: {
    provinces: [],
  },
  BE: {
    provinces: [],
  },
  BZ: {
    provinces: [],
  },
  BJ: {
    provinces: [],
  },
  BM: {
    provinces: [],
  },
  BT: {
    provinces: [],
  },
  BO: {
    provinces: [],
  },
  BA: {
    provinces: [],
  },
  BW: {
    provinces: [],
  },
  BV: {
    provinces: [],
  },
  BR: {
    provinces: [
      {
        label: 'Acre',
        value: 'AC',
      },
      {
        label: 'Alagoas',
        value: 'AL',
      },
      {
        label: 'Amapá',
        value: 'AP',
      },
      {
        label: 'Amazonas',
        value: 'AM',
      },
      {
        label: 'Bahia',
        value: 'BA',
      },
      {
        label: 'Ceará',
        value: 'CE',
      },
      {
        label: 'Espírito Santo',
        value: 'ES',
      },
      {
        label: 'Distrito Federal',
        value: 'DF',
      },
      {
        label: 'Goiás',
        value: 'GO',
      },
      {
        label: 'Maranhão',
        value: 'MA',
      },
      {
        label: 'Mato Grosso',
        value: 'MT',
      },
      {
        label: 'Mato Grosso do Sul',
        value: 'MS',
      },
      {
        label: 'Minas Gerais',
        value: 'MG',
      },
      {
        label: 'Pará',
        value: 'PA',
      },
      {
        label: 'Paraíba',
        value: 'PB',
      },
      {
        label: 'Paraná',
        value: 'PR',
      },
      {
        label: 'Pernambuco',
        value: 'PE',
      },
      {
        label: 'Piauí',
        value: 'PI',
      },
      {
        label: 'Rio Grande do Norte',
        value: 'RN',
      },
      {
        label: 'Rio Grande do Sul',
        value: 'RS',
      },
      {
        label: 'Rio de Janeiro',
        value: 'RJ',
      },
      {
        label: 'Rondônia',
        value: 'RO',
      },
      {
        label: 'Roraima',
        value: 'RR',
      },
      {
        label: 'Santa Catarina',
        value: 'SC',
      },
      {
        label: 'São Paulo',
        value: 'SP',
      },
      {
        label: 'Sergipe',
        value: 'SE',
      },
      {
        label: 'Tocantins',
        value: 'TO',
      },
    ],
  },
  IO: {
    provinces: [],
  },
  BN: {
    provinces: [],
  },
  BG: {
    provinces: [],
  },
  BF: {
    provinces: [],
  },
  BI: {
    provinces: [],
  },
  KH: {
    provinces: [],
  },
  CA: {
    provinces: [
      {
        label: 'Alberta',
        value: 'AB',
      },
      {
        label: 'British Columbia',
        value: 'BC',
      },
      {
        label: 'Manitoba',
        value: 'MB',
      },
      {
        label: 'New Brunswick',
        value: 'NB',
      },
      {
        label: 'Newfoundland and Labrador',
        value: 'NL',
      },
      {
        label: 'Northwest Territories',
        value: 'NT',
      },
      {
        label: 'Nova Scotia',
        value: 'NS',
      },
      {
        label: 'Nunavut',
        value: 'NU',
      },
      {
        label: 'Ontario',
        value: 'ON',
      },
      {
        label: 'Prince Edward Island',
        value: 'PE',
      },
      {
        label: 'Quebec',
        value: 'QC',
      },
      {
        label: 'Saskatchewan',
        value: 'SK',
      },
      {
        label: 'Yukon',
        value: 'YT',
      },
    ],
  },
  CV: {
    provinces: [],
  },
  BQ: {
    provinces: [],
  },
  KY: {
    provinces: [],
  },
  CF: {
    provinces: [],
  },
  TD: {
    provinces: [],
  },
  CL: {
    provinces: [
      {
        label: 'Arica and Parinacota',
        value: 'AP',
      },
      {
        label: 'Tarapacá',
        value: 'TA',
      },
      {
        label: 'Antofagasta',
        value: 'AN',
      },
      {
        label: 'Atacama',
        value: 'AT',
      },
      {
        label: 'Coquimbo',
        value: 'CO',
      },
      {
        label: 'Valparaíso',
        value: 'VS',
      },
      {
        label: 'Santiago',
        value: 'RM',
      },
      {
        label: "O'Higgins",
        value: 'LI',
      },
      {
        label: 'Maule',
        value: 'ML',
      },
      {
        label: 'Ñuble',
        value: 'NB',
      },
      {
        label: 'Biobío',
        value: 'BI',
      },
      {
        label: 'Araucanía',
        value: 'AR',
      },
      {
        label: 'Los Ríos',
        value: 'LR',
      },
      {
        label: 'Los Lagos',
        value: 'LL',
      },
      {
        label: 'Aysén',
        value: 'AI',
      },
      {
        label: 'Magallanes',
        value: 'MA',
      },
    ],
  },
  CN: {
    provinces: [
      {
        label: 'Anhui',
        value: 'AH',
      },
      {
        label: 'Beijing',
        value: 'BJ',
      },
      {
        label: 'Chongqing',
        value: 'CQ',
      },
      {
        label: 'Fujian',
        value: 'FJ',
      },
      {
        label: 'Gansu',
        value: 'GS',
      },
      {
        label: 'Guangdong',
        value: 'GD',
      },
      {
        label: 'Guangxi',
        value: 'GX',
      },
      {
        label: 'Guizhou',
        value: 'GZ',
      },
      {
        label: 'Hainan',
        value: 'HI',
      },
      {
        label: 'Hebei',
        value: 'HE',
      },
      {
        label: 'Heilongjiang',
        value: 'HL',
      },
      {
        label: 'Henan',
        value: 'HA',
      },
      {
        label: 'Hubei',
        value: 'HB',
      },
      {
        label: 'Hunan',
        value: 'HN',
      },
      {
        label: 'Inner Mongolia',
        value: 'NM',
      },
      {
        label: 'Jiangsu',
        value: 'JS',
      },
      {
        label: 'Jiangxi',
        value: 'JX',
      },
      {
        label: 'Jilin',
        value: 'JL',
      },
      {
        label: 'Liaoning',
        value: 'LN',
      },
      {
        label: 'Ningxia',
        value: 'NX',
      },
      {
        label: 'Qinghai',
        value: 'QH',
      },
      {
        label: 'Shaanxi',
        value: 'SN',
      },
      {
        label: 'Shandong',
        value: 'SD',
      },
      {
        label: 'Shanghai',
        value: 'SH',
      },
      {
        label: 'Shanxi',
        value: 'SX',
      },
      {
        label: 'Sichuan',
        value: 'SC',
      },
      {
        label: 'Tianjin',
        value: 'TJ',
      },
      {
        label: 'Xizang',
        value: 'YZ',
      },
      {
        label: 'Xinjiang',
        value: 'XJ',
      },
      {
        label: 'Yunnan',
        value: 'YN',
      },
      {
        label: 'Zhejiang',
        value: 'ZJ',
      },
    ],
  },
  CX: {
    provinces: [],
  },
  CC: {
    provinces: [],
  },
  CO: {
    provinces: [
      {
        label: 'Bogotá, D.C.',
        value: 'DC',
      },
      {
        label: 'Amazonas',
        value: 'AMA',
      },
      {
        label: 'Antioquia',
        value: 'ANT',
      },
      {
        label: 'Arauca',
        value: 'ARA',
      },
      {
        label: 'Atlántico',
        value: 'ATL',
      },
      {
        label: 'Bolívar',
        value: 'BOL',
      },
      {
        label: 'Boyacá',
        value: 'BOY',
      },
      {
        label: 'Caldas',
        value: 'CAL',
      },
      {
        label: 'Caquetá',
        value: 'CAQ',
      },
      {
        label: 'Casanare',
        value: 'CAS',
      },
      {
        label: 'Cauca',
        value: 'CAU',
      },
      {
        label: 'Cesar',
        value: 'CES',
      },
      {
        label: 'Chocó',
        value: 'CHO',
      },
      {
        label: 'Córdoba',
        value: 'COR',
      },
      {
        label: 'Cundinamarca',
        value: 'CUN',
      },
      {
        label: 'Guainía',
        value: 'GUA',
      },
      {
        label: 'Guaviare',
        value: 'GUV',
      },
      {
        label: 'Huila',
        value: 'HUI',
      },
      {
        label: 'La Guajira',
        value: 'LAG',
      },
      {
        label: 'Magdalena',
        value: 'MAG',
      },
      {
        label: 'Meta',
        value: 'MET',
      },
      {
        label: 'Nariño',
        value: 'NAR',
      },
      {
        label: 'Norte de Santander',
        value: 'NSA',
      },
      {
        label: 'Putumayo',
        value: 'PUT',
      },
      {
        label: 'Quindío',
        value: 'QUI',
      },
      {
        label: 'Risaralda',
        value: 'RIS',
      },
      {
        label: 'San Andrés, Providencia y Santa Catalina',
        value: 'SAP',
      },
      {
        label: 'Santander',
        value: 'SAN',
      },
      {
        label: 'Sucre',
        value: 'SUC',
      },
      {
        label: 'Tolima',
        value: 'TOL',
      },
      {
        label: 'Valle del Cauca',
        value: 'VAC',
      },
      {
        label: 'Vaupés',
        value: 'VAU',
      },
      {
        label: 'Vichada',
        value: 'VID',
      },
    ],
  },
  KM: {
    provinces: [],
  },
  CG: {
    provinces: [],
  },
  CD: {
    provinces: [],
  },
  CK: {
    provinces: [],
  },
  CR: {
    provinces: [],
  },
  HR: {
    provinces: [],
  },
  CU: {
    provinces: [],
  },
  CW: {
    provinces: [],
  },
  CY: {
    provinces: [],
  },
  CZ: {
    provinces: [],
  },
  CI: {
    provinces: [],
  },
  DK: {
    provinces: [],
  },
  DJ: {
    provinces: [],
  },
  DM: {
    provinces: [],
  },
  DO: {
    provinces: [],
  },
  EC: {
    provinces: [],
  },
  EG: {
    provinces: [
      {
        label: '6th of October',
        value: 'SU',
      },
      {
        label: 'Al Sharqia',
        value: 'SHR',
      },
      {
        label: 'Alexandria',
        value: 'ALX',
      },
      {
        label: 'Aswan',
        value: 'ASN',
      },
      {
        label: 'Asyut',
        value: 'AST',
      },
      {
        label: 'Beheira',
        value: 'BH',
      },
      {
        label: 'Beni Suef',
        value: 'BNS',
      },
      {
        label: 'Cairo',
        value: 'C',
      },
      {
        label: 'Dakahlia',
        value: 'DK',
      },
      {
        label: 'Damietta',
        value: 'DT',
      },
      {
        label: 'Faiyum',
        value: 'FYM',
      },
      {
        label: 'Gharbia',
        value: 'GH',
      },
      {
        label: 'Giza',
        value: 'GZ',
      },
      {
        label: 'Helwan',
        value: 'HU',
      },
      {
        label: 'Ismailia',
        value: 'IS',
      },
      {
        label: 'Kafr el-Sheikh',
        value: 'KFS',
      },
      {
        label: 'Luxor',
        value: 'LX',
      },
      {
        label: 'Matrouh',
        value: 'MT',
      },
      {
        label: 'Minya',
        value: 'MN',
      },
      {
        label: 'Monufia',
        value: 'MNF',
      },
      {
        label: 'New Valley',
        value: 'WAD',
      },
      {
        label: 'North Sinai',
        value: 'SIN',
      },
      {
        label: 'Port Said',
        value: 'PTS',
      },
      {
        label: 'Qalyubia',
        value: 'KB',
      },
      {
        label: 'Qena',
        value: 'KN',
      },
      {
        label: 'Red Sea',
        value: 'BA',
      },
      {
        label: 'Sohag',
        value: 'SHG',
      },
      {
        label: 'South Sinai',
        value: 'JS',
      },
      {
        label: 'Suez',
        value: 'SUZ',
      },
    ],
  },
  SV: {
    provinces: [],
  },
  GQ: {
    provinces: [],
  },
  ER: {
    provinces: [],
  },
  EE: {
    provinces: [],
  },
  SZ: {
    provinces: [],
  },
  ET: {
    provinces: [],
  },
  FK: {
    provinces: [],
  },
  FO: {
    provinces: [],
  },
  FJ: {
    provinces: [],
  },
  FI: {
    provinces: [],
  },
  FR: {
    provinces: [],
  },
  GF: {
    provinces: [],
  },
  PF: {
    provinces: [],
  },
  TF: {
    provinces: [],
  },
  GA: {
    provinces: [],
  },
  GM: {
    provinces: [],
  },
  GE: {
    provinces: [],
  },
  DE: {
    provinces: [],
  },
  GH: {
    provinces: [],
  },
  GI: {
    provinces: [],
  },
  GR: {
    provinces: [],
  },
  GL: {
    provinces: [],
  },
  GD: {
    provinces: [],
  },
  GP: {
    provinces: [],
  },
  GT: {
    provinces: [
      {
        label: 'Alta Verapaz',
        value: 'AVE',
      },
      {
        label: 'Baja Verapaz',
        value: 'BVE',
      },
      {
        label: 'Chimaltenango',
        value: 'CMT',
      },
      {
        label: 'Chiquimula',
        value: 'CQM',
      },
      {
        label: 'El Progreso',
        value: 'EPR',
      },
      {
        label: 'Escuintla',
        value: 'ESC',
      },
      {
        label: 'Guatemala',
        value: 'GUA',
      },
      {
        label: 'Huehuetenango',
        value: 'HUE',
      },
      {
        label: 'Izabal',
        value: 'IZA',
      },
      {
        label: 'Jalapa',
        value: 'JAL',
      },
      {
        label: 'Jutiapa',
        value: 'JUT',
      },
      {
        label: 'Petén',
        value: 'PET',
      },
      {
        label: 'Quetzaltenango',
        value: 'QUE',
      },
      {
        label: 'Quiché',
        value: 'QUI',
      },
      {
        label: 'Retalhuleu',
        value: 'RET',
      },
      {
        label: 'Sacatepéquez',
        value: 'SAC',
      },
      {
        label: 'San Marcos',
        value: 'SMA',
      },
      {
        label: 'Santa Rosa',
        value: 'SRO',
      },
      {
        label: 'Sololá',
        value: 'SOL',
      },
      {
        label: 'Suchitepéquez',
        value: 'SUC',
      },
      {
        label: 'Totonicapán',
        value: 'TOT',
      },
      {
        label: 'Zacapa',
        value: 'ZAC',
      },
    ],
  },
  GG: {
    provinces: [],
  },
  GN: {
    provinces: [],
  },
  GW: {
    provinces: [],
  },
  GY: {
    provinces: [],
  },
  HT: {
    provinces: [],
  },
  HM: {
    provinces: [],
  },
  VA: {
    provinces: [],
  },
  HN: {
    provinces: [],
  },
  HK: {
    provinces: [
      {
        label: 'Hong Kong Island',
        value: 'HK',
      },
      {
        label: 'Kowloon',
        value: 'KL',
      },
      {
        label: 'New Territories',
        value: 'NT',
      },
    ],
  },
  HU: {
    provinces: [],
  },
  IS: {
    provinces: [],
  },
  IN: {
    provinces: [
      {
        label: 'Andaman and Nicobar Islands',
        value: 'AN',
      },
      {
        label: 'Andhra Pradesh',
        value: 'AP',
      },
      {
        label: 'Arunachal Pradesh',
        value: 'AR',
      },
      {
        label: 'Assam',
        value: 'AS',
      },
      {
        label: 'Bihar',
        value: 'BR',
      },
      {
        label: 'Chandigarh',
        value: 'CH',
      },
      {
        label: 'Chhattisgarh',
        value: 'CG',
      },
      {
        label: 'Dadra and Nagar Haveli',
        value: 'DN',
      },
      {
        label: 'Daman and Diu',
        value: 'DD',
      },
      {
        label: 'Delhi',
        value: 'DL',
      },
      {
        label: 'Goa',
        value: 'GA',
      },
      {
        label: 'Gujarat',
        value: 'GJ',
      },
      {
        label: 'Haryana',
        value: 'HR',
      },
      {
        label: 'Himachal Pradesh',
        value: 'HP',
      },
      {
        label: 'Jammu and Kashmir',
        value: 'JK',
      },
      {
        label: 'Jharkhand',
        value: 'JH',
      },
      {
        label: 'Karnataka',
        value: 'KA',
      },
      {
        label: 'Kerala',
        value: 'KL',
      },
      {
        label: 'Ladakh',
        value: 'LA',
      },
      {
        label: 'Lakshadweep',
        value: 'LD',
      },
      {
        label: 'Madhya Pradesh',
        value: 'MP',
      },
      {
        label: 'Maharashtra',
        value: 'MH',
      },
      {
        label: 'Manipur',
        value: 'MN',
      },
      {
        label: 'Meghalaya',
        value: 'ML',
      },
      {
        label: 'Mizoram',
        value: 'MZ',
      },
      {
        label: 'Nagaland',
        value: 'NL',
      },
      {
        label: 'Odisha',
        value: 'OR',
      },
      {
        label: 'Puducherry',
        value: 'PY',
      },
      {
        label: 'Punjab',
        value: 'PB',
      },
      {
        label: 'Rajasthan',
        value: 'RJ',
      },
      {
        label: 'Sikkim',
        value: 'SK',
      },
      {
        label: 'Tamil Nadu',
        value: 'TN',
      },
      {
        label: 'Telangana',
        value: 'TS',
      },
      {
        label: 'Tripura',
        value: 'TR',
      },
      {
        label: 'Uttar Pradesh',
        value: 'UP',
      },
      {
        label: 'Uttarakhand',
        value: 'UK',
      },
      {
        label: 'West Bengal',
        value: 'WB',
      },
    ],
  },
  ID: {
    provinces: [
      {
        label: 'Aceh',
        value: 'AC',
      },
      {
        label: 'Bali',
        value: 'BA',
      },
      {
        label: 'Bangka Belitung',
        value: 'BB',
      },
      {
        label: 'Banten',
        value: 'BT',
      },
      {
        label: 'Bengkulu',
        value: 'BE',
      },
      {
        label: 'Jawa Tengah',
        value: 'JT',
      },
      {
        label: 'Kalimantan Tengah',
        value: 'KT',
      },
      {
        label: 'Sulawesi Tengah',
        value: 'ST',
      },
      {
        label: 'Jawa Timur',
        value: 'JI',
      },
      {
        label: 'Kalimantan Timur',
        value: 'KI',
      },
      {
        label: 'Nusa Tenggara Timur',
        value: 'NT',
      },
      {
        label: 'Gorontalo',
        value: 'GO',
      },
      {
        label: 'Jakarta',
        value: 'JK',
      },
      {
        label: 'Jambi',
        value: 'JA',
      },
      {
        label: 'Lampung',
        value: 'LA',
      },
      {
        label: 'Maluku',
        value: 'MA',
      },
      {
        label: 'Kalimantan Utara',
        value: 'KU',
      },
      {
        label: 'Maluku Utara',
        value: 'MU',
      },
      {
        label: 'Sulawesi Utara',
        value: 'SA',
      },
      {
        label: 'North Sumatra',
        value: 'SU',
      },
      {
        label: 'Papua',
        value: 'PA',
      },
      {
        label: 'Riau',
        value: 'RI',
      },
      {
        label: 'Kepulauan Riau',
        value: 'KR',
      },
      {
        label: 'Kalimantan Selatan',
        value: 'KS',
      },
      {
        label: 'Sulawesi Selatan',
        value: 'SN',
      },
      {
        label: 'South Sumatra',
        value: 'SS',
      },
      {
        label: 'Sulawesi Tenggara',
        value: 'SG',
      },
      {
        label: 'Jawa Barat',
        value: 'JB',
      },
      {
        label: 'Kalimantan Barat',
        value: 'KB',
      },
      {
        label: 'Nusa Tenggara Barat',
        value: 'NB',
      },
      {
        label: 'Papua Barat',
        value: 'PB',
      },
      {
        label: 'Sulawesi Barat',
        value: 'SR',
      },
      {
        label: 'West Sumatra',
        value: 'SB',
      },
      {
        label: 'Yogyakarta',
        value: 'YO',
      },
    ],
  },
  IR: {
    provinces: [],
  },
  IQ: {
    provinces: [],
  },
  IE: {
    provinces: [
      {
        label: 'Carlow',
        value: 'CW',
      },
      {
        label: 'Cavan',
        value: 'CN',
      },
      {
        label: 'Clare',
        value: 'CE',
      },
      {
        label: 'Cork',
        value: 'CO',
      },
      {
        label: 'Donegal',
        value: 'DL',
      },
      {
        label: 'Dublin',
        value: 'D',
      },
      {
        label: 'Galway',
        value: 'G',
      },
      {
        label: 'Kerry',
        value: 'KY',
      },
      {
        label: 'Kildare',
        value: 'KE',
      },
      {
        label: 'Kilkenny',
        value: 'KK',
      },
      {
        label: 'Laois',
        value: 'LS',
      },
      {
        label: 'Leitrim',
        value: 'LM',
      },
      {
        label: 'Limerick',
        value: 'LK',
      },
      {
        label: 'Longford',
        value: 'LD',
      },
      {
        label: 'Louth',
        value: 'LH',
      },
      {
        label: 'Mayo',
        value: 'MO',
      },
      {
        label: 'Meath',
        value: 'MH',
      },
      {
        label: 'Monaghan',
        value: 'MN',
      },
      {
        label: 'Offaly',
        value: 'OY',
      },
      {
        label: 'Roscommon',
        value: 'RN',
      },
      {
        label: 'Sligo',
        value: 'SO',
      },
      {
        label: 'Tipperary',
        value: 'TA',
      },
      {
        label: 'Waterford',
        value: 'WD',
      },
      {
        label: 'Westmeath',
        value: 'WH',
      },
      {
        label: 'Wexford',
        value: 'WX',
      },
      {
        label: 'Wicklow',
        value: 'WW',
      },
    ],
  },
  IM: {
    provinces: [],
  },
  IL: {
    provinces: [],
  },
  IT: {
    provinces: [
      {
        label: 'Agrigento',
        value: 'AG',
      },
      {
        label: 'Alessandria',
        value: 'AL',
      },
      {
        label: 'Ancona',
        value: 'AN',
      },
      {
        label: 'Aosta',
        value: 'AO',
      },
      {
        label: 'Arezzo',
        value: 'AR',
      },
      {
        label: 'Ascoli Piceno',
        value: 'AP',
      },
      {
        label: 'Asti',
        value: 'AT',
      },
      {
        label: 'Avellino',
        value: 'AV',
      },
      {
        label: 'Bari',
        value: 'BA',
      },
      {
        label: 'Barletta-Andria-Trani',
        value: 'BT',
      },
      {
        label: 'Belluno',
        value: 'BL',
      },
      {
        label: 'Benevento',
        value: 'BN',
      },
      {
        label: 'Bergamo',
        value: 'BG',
      },
      {
        label: 'Biella',
        value: 'BI',
      },
      {
        label: 'Bologna',
        value: 'BO',
      },
      {
        label: 'Brescia',
        value: 'BS',
      },
      {
        label: 'Brindisi',
        value: 'BR',
      },
      {
        label: 'Cagliari',
        value: 'CA',
      },
      {
        label: 'Caltanissetta',
        value: 'CL',
      },
      {
        label: 'Campobasso',
        value: 'CB',
      },
      {
        label: 'Carbonia-Iglesias',
        value: 'CI',
      },
      {
        label: 'Caserta',
        value: 'CE',
      },
      {
        label: 'Catania',
        value: 'CT',
      },
      {
        label: 'Catanzaro',
        value: 'CZ',
      },
      {
        label: 'Chieti',
        value: 'CH',
      },
      {
        label: 'Como',
        value: 'CO',
      },
      {
        label: 'Cosenza',
        value: 'CS',
      },
      {
        label: 'Cremona',
        value: 'CR',
      },
      {
        label: 'Crotone',
        value: 'KR',
      },
      {
        label: 'Cuneo',
        value: 'CN',
      },
      {
        label: 'Enna',
        value: 'EN',
      },
      {
        label: 'Fermo',
        value: 'FM',
      },
      {
        label: 'Ferrara',
        value: 'FE',
      },
      {
        label: 'Firenze',
        value: 'FI',
      },
      {
        label: 'Foggia',
        value: 'FG',
      },
      {
        label: 'Forlì-Cesena',
        value: 'FC',
      },
      {
        label: 'Frosinone',
        value: 'FR',
      },
      {
        label: 'Genova',
        value: 'GE',
      },
      {
        label: 'Gorizia',
        value: 'GO',
      },
      {
        label: 'Grosseto',
        value: 'GR',
      },
      {
        label: 'Imperia',
        value: 'IM',
      },
      {
        label: 'Isernia',
        value: 'IS',
      },
      {
        label: 'La Spezia',
        value: 'SP',
      },
      {
        label: 'Latina',
        value: 'LT',
      },
      {
        label: 'Lecce',
        value: 'LE',
      },
      {
        label: 'Lecco',
        value: 'LC',
      },
      {
        label: 'Livorno',
        value: 'LI',
      },
      {
        label: 'Lodi',
        value: 'LO',
      },
      {
        label: 'Lucca',
        value: 'LU',
      },
      {
        label: "L'Aquila",
        value: 'AQ',
      },
      {
        label: 'Macerata',
        value: 'MC',
      },
      {
        label: 'Mantova',
        value: 'MN',
      },
      {
        label: 'Massa-Carrara',
        value: 'MS',
      },
      {
        label: 'Matera',
        value: 'MT',
      },
      {
        label: 'Medio Campidano',
        value: 'VS',
      },
      {
        label: 'Messina',
        value: 'ME',
      },
      {
        label: 'Milano',
        value: 'MI',
      },
      {
        label: 'Modena',
        value: 'MO',
      },
      {
        label: 'Monza e Brianza',
        value: 'MB',
      },
      {
        label: 'Napoli',
        value: 'NA',
      },
      {
        label: 'Novara',
        value: 'NO',
      },
      {
        label: 'Nuoro',
        value: 'NU',
      },
      {
        label: 'Ogliastra',
        value: 'OG',
      },
      {
        label: 'Olbia-Tempio',
        value: 'OT',
      },
      {
        label: 'Oristano',
        value: 'OR',
      },
      {
        label: 'Padova',
        value: 'PD',
      },
      {
        label: 'Palermo',
        value: 'PA',
      },
      {
        label: 'Parma',
        value: 'PR',
      },
      {
        label: 'Pavia',
        value: 'PV',
      },
      {
        label: 'Perugia',
        value: 'PG',
      },
      {
        label: 'Pesaro e Urbino',
        value: 'PU',
      },
      {
        label: 'Pescara',
        value: 'PE',
      },
      {
        label: 'Piacenza',
        value: 'PC',
      },
      {
        label: 'Pisa',
        value: 'PI',
      },
      {
        label: 'Pistoia',
        value: 'PT',
      },
      {
        label: 'Pordenone',
        value: 'PN',
      },
      {
        label: 'Potenza',
        value: 'PZ',
      },
      {
        label: 'Prato',
        value: 'PO',
      },
      {
        label: 'Ragusa',
        value: 'RG',
      },
      {
        label: 'Ravenna',
        value: 'RA',
      },
      {
        label: 'Reggio Calabria',
        value: 'RC',
      },
      {
        label: 'Reggio Emilia',
        value: 'RE',
      },
      {
        label: 'Rieti',
        value: 'RI',
      },
      {
        label: 'Rimini',
        value: 'RN',
      },
      {
        label: 'Roma',
        value: 'RM',
      },
      {
        label: 'Rovigo',
        value: 'RO',
      },
      {
        label: 'Salerno',
        value: 'SA',
      },
      {
        label: 'Sassari',
        value: 'SS',
      },
      {
        label: 'Savona',
        value: 'SV',
      },
      {
        label: 'Siena',
        value: 'SI',
      },
      {
        label: 'Sondrio',
        value: 'SO',
      },
      {
        label: 'Bolzano',
        value: 'BZ',
      },
      {
        label: 'Siracusa',
        value: 'SR',
      },
      {
        label: 'Taranto',
        value: 'TA',
      },
      {
        label: 'Teramo',
        value: 'TE',
      },
      {
        label: 'Terni',
        value: 'TR',
      },
      {
        label: 'Trapani',
        value: 'TP',
      },
      {
        label: 'Trento',
        value: 'TN',
      },
      {
        label: 'Treviso',
        value: 'TV',
      },
      {
        label: 'Trieste',
        value: 'TS',
      },
      {
        label: 'Torino',
        value: 'TO',
      },
      {
        label: 'Udine',
        value: 'UD',
      },
      {
        label: 'Varese',
        value: 'VA',
      },
      {
        label: 'Venezia',
        value: 'VE',
      },
      {
        label: 'Verbano-Cusio-Ossola',
        value: 'VB',
      },
      {
        label: 'Vercelli',
        value: 'VC',
      },
      {
        label: 'Verona',
        value: 'VR',
      },
      {
        label: 'Vibo Valentia',
        value: 'VV',
      },
      {
        label: 'Vicenza',
        value: 'VI',
      },
      {
        label: 'Viterbo',
        value: 'VT',
      },
    ],
  },
  JM: {
    provinces: [],
  },
  JP: {
    provinces: [
      {
        label: 'Hokkaidō',
        value: 'JP-01',
      },
      {
        label: 'Aomori',
        value: 'JP-02',
      },
      {
        label: 'Iwate',
        value: 'JP-03',
      },
      {
        label: 'Miyagi',
        value: 'JP-04',
      },
      {
        label: 'Akita',
        value: 'JP-05',
      },
      {
        label: 'Yamagata',
        value: 'JP-06',
      },
      {
        label: 'Fukushima',
        value: 'JP-07',
      },
      {
        label: 'Ibaraki',
        value: 'JP-08',
      },
      {
        label: 'Tochigi',
        value: 'JP-09',
      },
      {
        label: 'Gunma',
        value: 'JP-10',
      },
      {
        label: 'Saitama',
        value: 'JP-11',
      },
      {
        label: 'Chiba',
        value: 'JP-12',
      },
      {
        label: 'Tōkyō',
        value: 'JP-13',
      },
      {
        label: 'Kanagawa',
        value: 'JP-14',
      },
      {
        label: 'Niigata',
        value: 'JP-15',
      },
      {
        label: 'Toyama',
        value: 'JP-16',
      },
      {
        label: 'Ishikawa',
        value: 'JP-17',
      },
      {
        label: 'Fukui',
        value: 'JP-18',
      },
      {
        label: 'Yamanashi',
        value: 'JP-19',
      },
      {
        label: 'Nagano',
        value: 'JP-20',
      },
      {
        label: 'Gifu',
        value: 'JP-21',
      },
      {
        label: 'Shizuoka',
        value: 'JP-22',
      },
      {
        label: 'Aichi',
        value: 'JP-23',
      },
      {
        label: 'Mie',
        value: 'JP-24',
      },
      {
        label: 'Shiga',
        value: 'JP-25',
      },
      {
        label: 'Kyōto',
        value: 'JP-26',
      },
      {
        label: 'Ōsaka',
        value: 'JP-27',
      },
      {
        label: 'Hyōgo',
        value: 'JP-28',
      },
      {
        label: 'Nara',
        value: 'JP-29',
      },
      {
        label: 'Wakayama',
        value: 'JP-30',
      },
      {
        label: 'Tottori',
        value: 'JP-31',
      },
      {
        label: 'Shimane',
        value: 'JP-32',
      },
      {
        label: 'Okayama',
        value: 'JP-33',
      },
      {
        label: 'Hiroshima',
        value: 'JP-34',
      },
      {
        label: 'Yamaguchi',
        value: 'JP-35',
      },
      {
        label: 'Tokushima',
        value: 'JP-36',
      },
      {
        label: 'Kagawa',
        value: 'JP-37',
      },
      {
        label: 'Ehime',
        value: 'JP-38',
      },
      {
        label: 'Kōchi',
        value: 'JP-39',
      },
      {
        label: 'Fukuoka',
        value: 'JP-40',
      },
      {
        label: 'Saga',
        value: 'JP-41',
      },
      {
        label: 'Nagasaki',
        value: 'JP-42',
      },
      {
        label: 'Kumamoto',
        value: 'JP-43',
      },
      {
        label: 'Ōita',
        value: 'JP-44',
      },
      {
        label: 'Miyazaki',
        value: 'JP-45',
      },
      {
        label: 'Kagoshima',
        value: 'JP-46',
      },
      {
        label: 'Okinawa',
        value: 'JP-47',
      },
    ],
  },
  JE: {
    provinces: [],
  },
  JO: {
    provinces: [],
  },
  KZ: {
    provinces: [],
  },
  KE: {
    provinces: [],
  },
  KI: {
    provinces: [],
  },
  KP: {
    provinces: [],
  },
  XK: {
    provinces: [],
  },
  KW: {
    provinces: [],
  },
  KG: {
    provinces: [],
  },
  LA: {
    provinces: [],
  },
  LV: {
    provinces: [],
  },
  LB: {
    provinces: [],
  },
  LS: {
    provinces: [],
  },
  LR: {
    provinces: [],
  },
  LY: {
    provinces: [],
  },
  LI: {
    provinces: [],
  },
  LT: {
    provinces: [],
  },
  LU: {
    provinces: [],
  },
  MO: {
    provinces: [],
  },
  MG: {
    provinces: [],
  },
  MW: {
    provinces: [],
  },
  MY: {
    provinces: [
      {
        label: 'Johor',
        value: 'JHR',
      },
      {
        label: 'Kedah',
        value: 'KDH',
      },
      {
        label: 'Kelantan',
        value: 'KTN',
      },
      {
        label: 'Kuala Lumpur',
        value: 'KUL',
      },
      {
        label: 'Labuan',
        value: 'LBN',
      },
      {
        label: 'Melaka',
        value: 'MLK',
      },
      {
        label: 'Negeri Sembilan',
        value: 'NSN',
      },
      {
        label: 'Pahang',
        value: 'PHG',
      },
      {
        label: 'Penang',
        value: 'PNG',
      },
      {
        label: 'Perak',
        value: 'PRK',
      },
      {
        label: 'Perlis',
        value: 'PLS',
      },
      {
        label: 'Putrajaya',
        value: 'PJY',
      },
      {
        label: 'Sabah',
        value: 'SBH',
      },
      {
        label: 'Sarawak',
        value: 'SWK',
      },
      {
        label: 'Selangor',
        value: 'SGR',
      },
      {
        label: 'Terengganu',
        value: 'TRG',
      },
    ],
  },
  MV: {
    provinces: [],
  },
  ML: {
    provinces: [],
  },
  MT: {
    provinces: [],
  },
  MQ: {
    provinces: [],
  },
  MR: {
    provinces: [],
  },
  MU: {
    provinces: [],
  },
  YT: {
    provinces: [],
  },
  MX: {
    provinces: [
      {
        label: 'Aguascalientes',
        value: 'AGS',
      },
      {
        label: 'Baja California',
        value: 'BC',
      },
      {
        label: 'Baja California Sur',
        value: 'BCS',
      },
      {
        label: 'Campeche',
        value: 'CAMP',
      },
      {
        label: 'Chiapas',
        value: 'CHIS',
      },
      {
        label: 'Chihuahua',
        value: 'CHIH',
      },
      {
        label: 'Ciudad de México',
        value: 'DF',
      },
      {
        label: 'Coahuila',
        value: 'COAH',
      },
      {
        label: 'Colima',
        value: 'COL',
      },
      {
        label: 'Durango',
        value: 'DGO',
      },
      {
        label: 'Guanajuato',
        value: 'GTO',
      },
      {
        label: 'Guerrero',
        value: 'GRO',
      },
      {
        label: 'Hidalgo',
        value: 'HGO',
      },
      {
        label: 'Jalisco',
        value: 'JAL',
      },
      {
        label: 'México',
        value: 'MEX',
      },
      {
        label: 'Michoacán',
        value: 'MICH',
      },
      {
        label: 'Morelos',
        value: 'MOR',
      },
      {
        label: 'Nayarit',
        value: 'NAY',
      },
      {
        label: 'Nuevo León',
        value: 'NL',
      },
      {
        label: 'Oaxaca',
        value: 'OAX',
      },
      {
        label: 'Puebla',
        value: 'PUE',
      },
      {
        label: 'Querétaro',
        value: 'QRO',
      },
      {
        label: 'Quintana Roo',
        value: 'Q ROO',
      },
      {
        label: 'San Luis Potosí',
        value: 'SLP',
      },
      {
        label: 'Sinaloa',
        value: 'SIN',
      },
      {
        label: 'Sonora',
        value: 'SON',
      },
      {
        label: 'Tabasco',
        value: 'TAB',
      },
      {
        label: 'Tamaulipas',
        value: 'TAMPS',
      },
      {
        label: 'Tlaxcala',
        value: 'TLAX',
      },
      {
        label: 'Veracruz',
        value: 'VER',
      },
      {
        label: 'Yucatán',
        value: 'YUC',
      },
      {
        label: 'Zacatecas',
        value: 'ZAC',
      },
    ],
  },
  MD: {
    provinces: [],
  },
  MC: {
    provinces: [],
  },
  MN: {
    provinces: [],
  },
  ME: {
    provinces: [],
  },
  MS: {
    provinces: [],
  },
  MA: {
    provinces: [],
  },
  MZ: {
    provinces: [],
  },
  MM: {
    provinces: [],
  },
  NA: {
    provinces: [],
  },
  NR: {
    provinces: [],
  },
  NP: {
    provinces: [],
  },
  NL: {
    provinces: [],
  },
  AN: {
    provinces: [],
  },
  NC: {
    provinces: [],
  },
  NZ: {
    provinces: [
      {
        label: 'Auckland',
        value: 'AUK',
      },
      {
        label: 'Bay of Plenty',
        value: 'BOP',
      },
      {
        label: 'Canterbury',
        value: 'CAN',
      },
      {
        label: 'Chatham Islands',
        value: 'CIT',
      },
      {
        label: 'Gisborne',
        value: 'GIS',
      },
      {
        label: "Hawke's Bay",
        value: 'HKB',
      },
      {
        label: 'Manawatu-Wanganui',
        value: 'MWT',
      },
      {
        label: 'Marlborough',
        value: 'MBH',
      },
      {
        label: 'Nelson',
        value: 'NSN',
      },
      {
        label: 'Northland',
        value: 'NTL',
      },
      {
        label: 'Otago',
        value: 'OTA',
      },
      {
        label: 'Southland',
        value: 'STL',
      },
      {
        label: 'Taranaki',
        value: 'TKI',
      },
      {
        label: 'Tasman',
        value: 'TAS',
      },
      {
        label: 'Waikato',
        value: 'WKO',
      },
      {
        label: 'Wellington',
        value: 'WGN',
      },
      {
        label: 'West Coast',
        value: 'WTC',
      },
    ],
  },
  NI: {
    provinces: [],
  },
  NE: {
    provinces: [],
  },
  NG: {
    provinces: [
      {
        label: 'Abia',
        value: 'AB',
      },
      {
        label: 'Adamawa',
        value: 'AD',
      },
      {
        label: 'Akwa Ibom',
        value: 'AK',
      },
      {
        label: 'Anambra',
        value: 'AN',
      },
      {
        label: 'Bauchi',
        value: 'BA',
      },
      {
        label: 'Bayelsa',
        value: 'BY',
      },
      {
        label: 'Benue',
        value: 'BE',
      },
      {
        label: 'Borno',
        value: 'BO',
      },
      {
        label: 'Cross River',
        value: 'CR',
      },
      {
        label: 'Delta',
        value: 'DE',
      },
      {
        label: 'Ebonyi',
        value: 'EB',
      },
      {
        label: 'Edo',
        value: 'ED',
      },
      {
        label: 'Ekiti',
        value: 'EK',
      },
      {
        label: 'Enugu',
        value: 'EN',
      },
      {
        label: 'Abuja Federal Capital Territory',
        value: 'FC',
      },
      {
        label: 'Gombe',
        value: 'GO',
      },
      {
        label: 'Imo',
        value: 'IM',
      },
      {
        label: 'Jigawa',
        value: 'JI',
      },
      {
        label: 'Kaduna',
        value: 'KD',
      },
      {
        label: 'Kano',
        value: 'KN',
      },
      {
        label: 'Katsina',
        value: 'KT',
      },
      {
        label: 'Kebbi',
        value: 'KE',
      },
      {
        label: 'Kogi',
        value: 'KO',
      },
      {
        label: 'Kwara',
        value: 'KW',
      },
      {
        label: 'Lagos',
        value: 'LA',
      },
      {
        label: 'Nasarawa',
        value: 'NA',
      },
      {
        label: 'Niger',
        value: 'NI',
      },
      {
        label: 'Ogun',
        value: 'OG',
      },
      {
        label: 'Ondo',
        value: 'ON',
      },
      {
        label: 'Osun',
        value: 'OS',
      },
      {
        label: 'Oyo',
        value: 'OY',
      },
      {
        label: 'Plateau',
        value: 'PL',
      },
      {
        label: 'Rivers',
        value: 'RI',
      },
      {
        label: 'Sokoto',
        value: 'SO',
      },
      {
        label: 'Taraba',
        value: 'TA',
      },
      {
        label: 'Yobe',
        value: 'YO',
      },
      {
        label: 'Zamfara',
        value: 'ZA',
      },
    ],
  },
  NU: {
    provinces: [],
  },
  NF: {
    provinces: [],
  },
  MK: {
    provinces: [],
  },
  NO: {
    provinces: [],
  },
  OM: {
    provinces: [],
  },
  PK: {
    provinces: [],
  },
  PS: {
    provinces: [],
  },
  PA: {
    provinces: [
      {
        label: 'Bocas del Toro',
        value: 'PA-1',
      },
      {
        label: 'Chiriquí',
        value: 'PA-4',
      },
      {
        label: 'Coclé',
        value: 'PA-2',
      },
      {
        label: 'Colón',
        value: 'PA-3',
      },
      {
        label: 'Darién',
        value: 'PA-5',
      },
      {
        label: 'Emberá',
        value: 'PA-EM',
      },
      {
        label: 'Kuna Yala',
        value: 'PA-KY',
      },
      {
        label: 'Herrera',
        value: 'PA-6',
      },
      {
        label: 'Los Santos',
        value: 'PA-7',
      },
      {
        label: 'Ngöbe-Buglé',
        value: 'PA-NB',
      },
      {
        label: 'Panamá',
        value: 'PA-8',
      },
      {
        label: 'Veraguas',
        value: 'PA-9',
      },
      {
        label: 'Panamá Oeste',
        value: 'PA-10',
      },
    ],
  },
  PG: {
    provinces: [],
  },
  PY: {
    provinces: [],
  },
  PE: {
    provinces: [
      {
        label: 'Amazonas',
        value: 'PE-AMA',
      },
      {
        label: 'Áncash',
        value: 'PE-ANC',
      },
      {
        label: 'Apurímac',
        value: 'PE-APU',
      },
      {
        label: 'Arequipa',
        value: 'PE-ARE',
      },
      {
        label: 'Ayacucho',
        value: 'PE-AYA',
      },
      {
        label: 'Cajamarca',
        value: 'PE-CAJ',
      },
      {
        label: 'Cuzco',
        value: 'PE-CUS',
      },
      {
        label: 'Callao',
        value: 'PE-CAL',
      },
      {
        label: 'Huancavelica',
        value: 'PE-HUV',
      },
      {
        label: 'Huánuco',
        value: 'PE-HUC',
      },
      {
        label: 'Ica',
        value: 'PE-ICA',
      },
      {
        label: 'Junín',
        value: 'PE-JUN',
      },
      {
        label: 'La Libertad',
        value: 'PE-LAL',
      },
      {
        label: 'Lambayeque',
        value: 'PE-LAM',
      },
      {
        label: 'Lima (departamento)',
        value: 'PE-LIM',
      },
      {
        label: 'Lima (provincia)',
        value: 'PE-LMA',
      },
      {
        label: 'Loreto',
        value: 'PE-LOR',
      },
      {
        label: 'Madre de Dios',
        value: 'PE-MDD',
      },
      {
        label: 'Moquegua',
        value: 'PE-MOQ',
      },
      {
        label: 'Pasco',
        value: 'PE-PAS',
      },
      {
        label: 'Piura',
        value: 'PE-PIU',
      },
      {
        label: 'Puno',
        value: 'PE-PUN',
      },
      {
        label: 'San Martín',
        value: 'PE-SAM',
      },
      {
        label: 'Tacna',
        value: 'PE-TAC',
      },
      {
        label: 'Tumbes',
        value: 'PE-TUM',
      },
      {
        label: 'Ucayali',
        value: 'PE-UCA',
      },
    ],
  },
  PH: {
    provinces: [
      {
        label: 'Abra',
        value: 'PH-ABR',
      },
      {
        label: 'Agusan del Norte',
        value: 'PH-AGN',
      },
      {
        label: 'Agusan del Sur',
        value: 'PH-AGS',
      },
      {
        label: 'Aklan',
        value: 'PH-AKL',
      },
      {
        label: 'Albay',
        value: 'PH-ALB',
      },
      {
        label: 'Antique',
        value: 'PH-ANT',
      },
      {
        label: 'Apayao',
        value: 'PH-APA',
      },
      {
        label: 'Aurora',
        value: 'PH-AUR',
      },
      {
        label: 'Basilan',
        value: 'PH-BAS',
      },
      {
        label: 'Bataan',
        value: 'PH-BAN',
      },
      {
        label: 'Batanes',
        value: 'PH-BTN',
      },
      {
        label: 'Batangas',
        value: 'PH-BTG',
      },
      {
        label: 'Benguet',
        value: 'PH-BEN',
      },
      {
        label: 'Biliran',
        value: 'PH-BIL',
      },
      {
        label: 'Bohol',
        value: 'PH-BOH',
      },
      {
        label: 'Bukidnon',
        value: 'PH-BUK',
      },
      {
        label: 'Bulacan',
        value: 'PH-BUL',
      },
      {
        label: 'Cagayan',
        value: 'PH-CAG',
      },
      {
        label: 'Camarines Norte',
        value: 'PH-CAN',
      },
      {
        label: 'Camarines Sur',
        value: 'PH-CAS',
      },
      {
        label: 'Camiguin',
        value: 'PH-CAM',
      },
      {
        label: 'Capiz',
        value: 'PH-CAP',
      },
      {
        label: 'Catanduanes',
        value: 'PH-CAT',
      },
      {
        label: 'Cavite',
        value: 'PH-CAV',
      },
      {
        label: 'Cebu',
        value: 'PH-CEB',
      },
      {
        label: 'Davao de Oro',
        value: 'PH-COM',
      },
      {
        label: 'Cotabato',
        value: 'PH-NCO',
      },
      {
        label: 'Davao Occidental',
        value: 'PH-DVO',
      },
      {
        label: 'Davao Oriental',
        value: 'PH-DAO',
      },
      {
        label: 'Davao del Norte',
        value: 'PH-DAV',
      },
      {
        label: 'Davao del Sur',
        value: 'PH-DAS',
      },
      {
        label: 'Dinagat Islands',
        value: 'PH-DIN',
      },
      {
        label: 'Eastern Samar',
        value: 'PH-EAS',
      },
      {
        label: 'Guimaras',
        value: 'PH-GUI',
      },
      {
        label: 'Ifugao',
        value: 'PH-IFU',
      },
      {
        label: 'Ilocos Norte',
        value: 'PH-ILN',
      },
      {
        label: 'Ilocos Sur',
        value: 'PH-ILS',
      },
      {
        label: 'Iloilo',
        value: 'PH-ILI',
      },
      {
        label: 'Isabela',
        value: 'PH-ISA',
      },
      {
        label: 'Kalinga',
        value: 'PH-KAL',
      },
      {
        label: 'La Union',
        value: 'PH-LUN',
      },
      {
        label: 'Laguna',
        value: 'PH-LAG',
      },
      {
        label: 'Lanao del Norte',
        value: 'PH-LAN',
      },
      {
        label: 'Lanao del Sur',
        value: 'PH-LAS',
      },
      {
        label: 'Leyte',
        value: 'PH-LEY',
      },
      {
        label: 'Maguindanao',
        value: 'PH-MAG',
      },
      {
        label: 'Marinduque',
        value: 'PH-MAD',
      },
      {
        label: 'Masbate',
        value: 'PH-MAS',
      },
      {
        label: 'Metro Manila',
        value: 'PH-00',
      },
      {
        label: 'Misamis Occidental',
        value: 'PH-MSC',
      },
      {
        label: 'Misamis Oriental',
        value: 'PH-MSR',
      },
      {
        label: 'Mountain Province',
        value: 'PH-MOU',
      },
      {
        label: 'Negros Occidental',
        value: 'PH-NEC',
      },
      {
        label: 'Negros Oriental',
        value: 'PH-NER',
      },
      {
        label: 'Northern Samar',
        value: 'PH-NSA',
      },
      {
        label: 'Nueva Ecija',
        value: 'PH-NUE',
      },
      {
        label: 'Nueva Vizcaya',
        value: 'PH-NUV',
      },
      {
        label: 'Occidental Mindoro',
        value: 'PH-MDC',
      },
      {
        label: 'Oriental Mindoro',
        value: 'PH-MDR',
      },
      {
        label: 'Palawan',
        value: 'PH-PLW',
      },
      {
        label: 'Pampanga',
        value: 'PH-PAM',
      },
      {
        label: 'Pangasinan',
        value: 'PH-PAN',
      },
      {
        label: 'Quezon',
        value: 'PH-QUE',
      },
      {
        label: 'Quirino',
        value: 'PH-QUI',
      },
      {
        label: 'Rizal',
        value: 'PH-RIZ',
      },
      {
        label: 'Romblon',
        value: 'PH-ROM',
      },
      {
        label: 'Samar',
        value: 'PH-WSA',
      },
      {
        label: 'Sarangani',
        value: 'PH-SAR',
      },
      {
        label: 'Siquijor',
        value: 'PH-SIG',
      },
      {
        label: 'Sorsogon',
        value: 'PH-SOR',
      },
      {
        label: 'South Cotabato',
        value: 'PH-SCO',
      },
      {
        label: 'Southern Leyte',
        value: 'PH-SLE',
      },
      {
        label: 'Sultan Kudarat',
        value: 'PH-SUK',
      },
      {
        label: 'Sulu',
        value: 'PH-SLU',
      },
      {
        label: 'Surigao del Norte',
        value: 'PH-SUN',
      },
      {
        label: 'Surigao del Sur',
        value: 'PH-SUR',
      },
      {
        label: 'Tarlac',
        value: 'PH-TAR',
      },
      {
        label: 'Tawi-Tawi',
        value: 'PH-TAW',
      },
      {
        label: 'Zambales',
        value: 'PH-ZMB',
      },
      {
        label: 'Zamboanga Sibugay',
        value: 'PH-ZSI',
      },
      {
        label: 'Zamboanga del Norte',
        value: 'PH-ZAN',
      },
      {
        label: 'Zamboanga del Sur',
        value: 'PH-ZAS',
      },
    ],
  },
  PN: {
    provinces: [],
  },
  PL: {
    provinces: [],
  },
  PT: {
    provinces: [
      {
        label: 'Aveiro',
        value: 'PT-01',
      },
      {
        label: 'Açores',
        value: 'PT-20',
      },
      {
        label: 'Beja',
        value: 'PT-02',
      },
      {
        label: 'Braga',
        value: 'PT-03',
      },
      {
        label: 'Bragança',
        value: 'PT-04',
      },
      {
        label: 'Castelo Branco',
        value: 'PT-05',
      },
      {
        label: 'Coimbra',
        value: 'PT-06',
      },
      {
        label: 'Évora',
        value: 'PT-07',
      },
      {
        label: 'Faro',
        value: 'PT-08',
      },
      {
        label: 'Guarda',
        value: 'PT-09',
      },
      {
        label: 'Leiria',
        value: 'PT-10',
      },
      {
        label: 'Lisboa',
        value: 'PT-11',
      },
      {
        label: 'Madeira',
        value: 'PT-30',
      },
      {
        label: 'Portalegre',
        value: 'PT-12',
      },
      {
        label: 'Porto',
        value: 'PT-13',
      },
      {
        label: 'Santarém',
        value: 'PT-14',
      },
      {
        label: 'Setúbal',
        value: 'PT-15',
      },
      {
        label: 'Viana do Castelo',
        value: 'PT-16',
      },
      {
        label: 'Vila Real',
        value: 'PT-17',
      },
      {
        label: 'Viseu',
        value: 'PT-18',
      },
    ],
  },
  QA: {
    provinces: [],
  },
  CM: {
    provinces: [],
  },
  RE: {
    provinces: [],
  },
  RO: {
    provinces: [
      {
        label: 'Alba',
        value: 'AB',
      },
      {
        label: 'Arad',
        value: 'AR',
      },
      {
        label: 'Argeș',
        value: 'AG',
      },
      {
        label: 'Bacău',
        value: 'BC',
      },
      {
        label: 'Bihor',
        value: 'BH',
      },
      {
        label: 'Bistrița-Năsăud',
        value: 'BN',
      },
      {
        label: 'Botoșani',
        value: 'BT',
      },
      {
        label: 'Brăila',
        value: 'BR',
      },
      {
        label: 'Brașov',
        value: 'BV',
      },
      {
        label: 'București',
        value: 'B',
      },
      {
        label: 'Buzău',
        value: 'BZ',
      },
      {
        label: 'Caraș-Severin',
        value: 'CS',
      },
      {
        label: 'Cluj',
        value: 'CJ',
      },
      {
        label: 'Constanța',
        value: 'CT',
      },
      {
        label: 'Covasna',
        value: 'CV',
      },
      {
        label: 'Călărași',
        value: 'CL',
      },
      {
        label: 'Dolj',
        value: 'DJ',
      },
      {
        label: 'Dâmbovița',
        value: 'DB',
      },
      {
        label: 'Galați',
        value: 'GL',
      },
      {
        label: 'Giurgiu',
        value: 'GR',
      },
      {
        label: 'Gorj',
        value: 'GJ',
      },
      {
        label: 'Harghita',
        value: 'HR',
      },
      {
        label: 'Hunedoara',
        value: 'HD',
      },
      {
        label: 'Ialomița',
        value: 'IL',
      },
      {
        label: 'Iași',
        value: 'IS',
      },
      {
        label: 'Ilfov',
        value: 'IF',
      },
      {
        label: 'Maramureș',
        value: 'MM',
      },
      {
        label: 'Mehedinți',
        value: 'MH',
      },
      {
        label: 'Mureș',
        value: 'MS',
      },
      {
        label: 'Neamț',
        value: 'NT',
      },
      {
        label: 'Olt',
        value: 'OT',
      },
      {
        label: 'Prahova',
        value: 'PH',
      },
      {
        label: 'Sălaj',
        value: 'SJ',
      },
      {
        label: 'Satu Mare',
        value: 'SM',
      },
      {
        label: 'Sibiu',
        value: 'SB',
      },
      {
        label: 'Suceava',
        value: 'SV',
      },
      {
        label: 'Teleorman',
        value: 'TR',
      },
      {
        label: 'Timiș',
        value: 'TM',
      },
      {
        label: 'Tulcea',
        value: 'TL',
      },
      {
        label: 'Vâlcea',
        value: 'VL',
      },
      {
        label: 'Vaslui',
        value: 'VS',
      },
      {
        label: 'Vrancea',
        value: 'VN',
      },
    ],
  },
  RU: {
    provinces: [
      {
        label: 'Republic of Adygeya',
        value: 'AD',
      },
      {
        label: 'Altai Republic',
        value: 'AL',
      },
      {
        label: 'Altai Krai',
        value: 'ALT',
      },
      {
        label: 'Amur Oblast',
        value: 'AMU',
      },
      {
        label: 'Arkhangelsk Oblast',
        value: 'ARK',
      },
      {
        label: 'Astrakhan Oblast',
        value: 'AST',
      },
      {
        label: 'Republic of Bashkortostan',
        value: 'BA',
      },
      {
        label: 'Belgorod Oblast',
        value: 'BEL',
      },
      {
        label: 'Bryansk Oblast',
        value: 'BRY',
      },
      {
        label: 'Republic of Buryatia',
        value: 'BU',
      },
      {
        label: 'Chechen Republic',
        value: 'CE',
      },
      {
        label: 'Chelyabinsk Oblast',
        value: 'CHE',
      },
      {
        label: 'Chukotka Autonomous Okrug',
        value: 'CHU',
      },
      {
        label: 'Chuvash Republic',
        value: 'CU',
      },
      {
        label: 'Republic of Dagestan',
        value: 'DA',
      },
      {
        label: 'Republic of Ingushetia',
        value: 'IN',
      },
      {
        label: 'Irkutsk Oblast',
        value: 'IRK',
      },
      {
        label: 'Ivanovo Oblast',
        value: 'IVA',
      },
      {
        label: 'Jewish Autonomous Oblast',
        value: 'YEV',
      },
      {
        label: 'Kabardino-Balkarian Republic',
        value: 'KB',
      },
      {
        label: 'Kaliningrad Oblast',
        value: 'KGD',
      },
      {
        label: 'Republic of Kalmykia',
        value: 'KL',
      },
      {
        label: 'Kaluga Oblast',
        value: 'KLU',
      },
      {
        label: 'Kamchatka Krai',
        value: 'KAM',
      },
      {
        label: 'Karachay–Cherkess Republic',
        value: 'KC',
      },
      {
        label: 'Republic of Karelia',
        value: 'KR',
      },
      {
        label: 'Kemerovo Oblast',
        value: 'KEM',
      },
      {
        label: 'Khabarovsk Krai',
        value: 'KHA',
      },
      {
        label: 'Republic of Khakassia',
        value: 'KK',
      },
      {
        label: 'Khanty-Mansi Autonomous Okrug',
        value: 'KHM',
      },
      {
        label: 'Kirov Oblast',
        value: 'KIR',
      },
      {
        label: 'Komi Republic',
        value: 'KO',
      },
      {
        label: 'Kostroma Oblast',
        value: 'KOS',
      },
      {
        label: 'Krasnodar Krai',
        value: 'KDA',
      },
      {
        label: 'Krasnoyarsk Krai',
        value: 'KYA',
      },
      {
        label: 'Kurgan Oblast',
        value: 'KGN',
      },
      {
        label: 'Kursk Oblast',
        value: 'KRS',
      },
      {
        label: 'Leningrad Oblast',
        value: 'LEN',
      },
      {
        label: 'Lipetsk Oblast',
        value: 'LIP',
      },
      {
        label: 'Magadan Oblast',
        value: 'MAG',
      },
      {
        label: 'Mari El Republic',
        value: 'ME',
      },
      {
        label: 'Republic of Mordovia',
        value: 'MO',
      },
      {
        label: 'Moscow',
        value: 'MOW',
      },
      {
        label: 'Moscow Oblast',
        value: 'MOS',
      },
      {
        label: 'Murmansk Oblast',
        value: 'MUR',
      },
      {
        label: 'Nizhny Novgorod Oblast',
        value: 'NIZ',
      },
      {
        label: 'Republic of North Ossetia–Alania',
        value: 'SE',
      },
      {
        label: 'Novgorod Oblast',
        value: 'NGR',
      },
      {
        label: 'Novosibirsk Oblast',
        value: 'NVS',
      },
      {
        label: 'Omsk Oblast',
        value: 'OMS',
      },
      {
        label: 'Orenburg Oblast',
        value: 'ORE',
      },
      {
        label: 'Oryol Oblast',
        value: 'ORL',
      },
      {
        label: 'Penza Oblast',
        value: 'PNZ',
      },
      {
        label: 'Perm Krai',
        value: 'PER',
      },
      {
        label: 'Primorsky Krai',
        value: 'PRI',
      },
      {
        label: 'Pskov Oblast',
        value: 'PSK',
      },
      {
        label: 'Rostov Oblast',
        value: 'ROS',
      },
      {
        label: 'Ryazan Oblast',
        value: 'RYA',
      },
      {
        label: 'Saint Petersburg',
        value: 'SPE',
      },
      {
        label: 'Sakha Republic (Yakutia)',
        value: 'SA',
      },
      {
        label: 'Sakhalin Oblast',
        value: 'SAK',
      },
      {
        label: 'Samara Oblast',
        value: 'SAM',
      },
      {
        label: 'Saratov Oblast',
        value: 'SAR',
      },
      {
        label: 'Smolensk Oblast',
        value: 'SMO',
      },
      {
        label: 'Stavropol Krai',
        value: 'STA',
      },
      {
        label: 'Sverdlovsk Oblast',
        value: 'SVE',
      },
      {
        label: 'Tambov Oblast',
        value: 'TAM',
      },
      {
        label: 'Republic of Tatarstan',
        value: 'TA',
      },
      {
        label: 'Tomsk Oblast',
        value: 'TOM',
      },
      {
        label: 'Tula Oblast',
        value: 'TUL',
      },
      {
        label: 'Tyva Republic',
        value: 'TY',
      },
      {
        label: 'Tver Oblast',
        value: 'TVE',
      },
      {
        label: 'Tyumen Oblast',
        value: 'TYU',
      },
      {
        label: 'Udmurtia',
        value: 'UD',
      },
      {
        label: 'Ulyanovsk Oblast',
        value: 'ULY',
      },
      {
        label: 'Vladimir Oblast',
        value: 'VLA',
      },
      {
        label: 'Volgograd Oblast',
        value: 'VGG',
      },
      {
        label: 'Vologda Oblast',
        value: 'VLG',
      },
      {
        label: 'Voronezh Oblast',
        value: 'VOR',
      },
      {
        label: 'Yamalo-Nenets Autonomous Okrug',
        value: 'YAN',
      },
      {
        label: 'Yaroslavl Oblast',
        value: 'YAR',
      },
      {
        label: 'Zabaykalsky Krai',
        value: 'ZAB',
      },
    ],
  },
  RW: {
    provinces: [],
  },
  BL: {
    provinces: [],
  },
  SH: {
    provinces: [],
  },
  KN: {
    provinces: [],
  },
  LC: {
    provinces: [],
  },
  MF: {
    provinces: [],
  },
  PM: {
    provinces: [],
  },
  WS: {
    provinces: [],
  },
  SM: {
    provinces: [],
  },
  ST: {
    provinces: [],
  },
  SA: {
    provinces: [],
  },
  SN: {
    provinces: [],
  },
  RS: {
    provinces: [],
  },
  SC: {
    provinces: [],
  },
  SL: {
    provinces: [],
  },
  SG: {
    provinces: [],
  },
  SX: {
    provinces: [],
  },
  SK: {
    provinces: [],
  },
  SI: {
    provinces: [],
  },
  SB: {
    provinces: [],
  },
  SO: {
    provinces: [],
  },
  ZA: {
    provinces: [
      {
        label: 'Eastern Cape',
        value: 'EC',
      },
      {
        label: 'Free State',
        value: 'FS',
      },
      {
        label: 'Gauteng',
        value: 'GP',
      },
      {
        label: 'KwaZulu-Natal',
        value: 'NL',
      },
      {
        label: 'Limpopo',
        value: 'LP',
      },
      {
        label: 'Mpumalanga',
        value: 'MP',
      },
      {
        label: 'North West',
        value: 'NW',
      },
      {
        label: 'Northern Cape',
        value: 'NC',
      },
      {
        label: 'Western Cape',
        value: 'WC',
      },
    ],
  },
  GS: {
    provinces: [],
  },
  KR: {
    provinces: [
      {
        label: 'Busan',
        value: 'KR-26',
      },
      {
        label: 'Daegu',
        value: 'KR-27',
      },
      {
        label: 'Daejeon',
        value: 'KR-30',
      },
      {
        label: 'Gangwon',
        value: 'KR-42',
      },
      {
        label: 'Gwangju',
        value: 'KR-29',
      },
      {
        label: 'Gyeonggi',
        value: 'KR-41',
      },
      {
        label: 'Incheon',
        value: 'KR-28',
      },
      {
        label: 'Jeju',
        value: 'KR-49',
      },
      {
        label: 'Chungbuk',
        value: 'KR-43',
      },
      {
        label: 'Gyeongbuk',
        value: 'KR-47',
      },
      {
        label: 'Jeonbuk',
        value: 'KR-45',
      },
      {
        label: 'Sejong',
        value: 'KR-50',
      },
      {
        label: 'Seoul',
        value: 'KR-11',
      },
      {
        label: 'Chungnam',
        value: 'KR-44',
      },
      {
        label: 'Gyeongnam',
        value: 'KR-48',
      },
      {
        label: 'Jeonnam',
        value: 'KR-46',
      },
      {
        label: 'Ulsan',
        value: 'KR-31',
      },
    ],
  },
  SS: {
    provinces: [],
  },
  ES: {
    provinces: [
      {
        label: 'A Coruña',
        value: 'C',
      },
      {
        label: 'Álava',
        value: 'VI',
      },
      {
        label: 'Albacete',
        value: 'AB',
      },
      {
        label: 'Alicante',
        value: 'A',
      },
      {
        label: 'Almería',
        value: 'AL',
      },
      {
        label: 'Asturias',
        value: 'O',
      },
      {
        label: 'Ávila',
        value: 'AV',
      },
      {
        label: 'Badajoz',
        value: 'BA',
      },
      {
        label: 'Balears',
        value: 'PM',
      },
      {
        label: 'Barcelona',
        value: 'B',
      },
      {
        label: 'Vizcaya',
        value: 'BI',
      },
      {
        label: 'Burgos',
        value: 'BU',
      },
      {
        label: 'Cáceres',
        value: 'CC',
      },
      {
        label: 'Cádiz',
        value: 'CA',
      },
      {
        label: 'Cantabria',
        value: 'S',
      },
      {
        label: 'Castellón',
        value: 'CS',
      },
      {
        label: 'Ceuta',
        value: 'CE',
      },
      {
        label: 'Ciudad Real',
        value: 'CR',
      },
      {
        label: 'Córdoba',
        value: 'CO',
      },
      {
        label: 'Cuenca',
        value: 'CU',
      },
      {
        label: 'Guipúzcoa',
        value: 'SS',
      },
      {
        label: 'Girona',
        value: 'GI',
      },
      {
        label: 'Granada',
        value: 'GR',
      },
      {
        label: 'Guadalajara',
        value: 'GU',
      },
      {
        label: 'Huelva',
        value: 'H',
      },
      {
        label: 'Huesca',
        value: 'HU',
      },
      {
        label: 'Jaén',
        value: 'J',
      },
      {
        label: 'La Rioja',
        value: 'LO',
      },
      {
        label: 'Las Palmas',
        value: 'GC',
      },
      {
        label: 'León',
        value: 'LE',
      },
      {
        label: 'Lleida',
        value: 'L',
      },
      {
        label: 'Lugo',
        value: 'LU',
      },
      {
        label: 'Madrid',
        value: 'M',
      },
      {
        label: 'Málaga',
        value: 'MA',
      },
      {
        label: 'Melilla',
        value: 'ML',
      },
      {
        label: 'Murcia',
        value: 'MU',
      },
      {
        label: 'Navarra',
        value: 'NA',
      },
      {
        label: 'Ourense',
        value: 'OR',
      },
      {
        label: 'Palencia',
        value: 'P',
      },
      {
        label: 'Pontevedra',
        value: 'PO',
      },
      {
        label: 'Salamanca',
        value: 'SA',
      },
      {
        label: 'Santa Cruz de Tenerife',
        value: 'TF',
      },
      {
        label: 'Segovia',
        value: 'SG',
      },
      {
        label: 'Sevilla',
        value: 'SE',
      },
      {
        label: 'Soria',
        value: 'SO',
      },
      {
        label: 'Tarragona',
        value: 'T',
      },
      {
        label: 'Teruel',
        value: 'TE',
      },
      {
        label: 'Toledo',
        value: 'TO',
      },
      {
        label: 'Valencia',
        value: 'V',
      },
      {
        label: 'Valladolid',
        value: 'VA',
      },
      {
        label: 'Zamora',
        value: 'ZA',
      },
      {
        label: 'Zaragoza',
        value: 'Z',
      },
    ],
  },
  LK: {
    provinces: [],
  },
  VC: {
    provinces: [],
  },
  SD: {
    provinces: [],
  },
  SR: {
    provinces: [],
  },
  SJ: {
    provinces: [],
  },
  SE: {
    provinces: [],
  },
  CH: {
    provinces: [],
  },
  SY: {
    provinces: [],
  },
  TW: {
    provinces: [],
  },
  TJ: {
    provinces: [],
  },
  TZ: {
    provinces: [],
  },
  TH: {
    provinces: [
      {
        label: 'Amnat Charoen',
        value: 'TH-37',
      },
      {
        label: 'Ang Thong',
        value: 'TH-15',
      },
      {
        label: 'Bangkok',
        value: 'TH-10',
      },
      {
        label: 'Bueng Kan',
        value: 'TH-38',
      },
      {
        label: 'Buriram',
        value: 'TH-31',
      },
      {
        label: 'Chachoengsao',
        value: 'TH-24',
      },
      {
        label: 'Chai Nat',
        value: 'TH-18',
      },
      {
        label: 'Chaiyaphum',
        value: 'TH-36',
      },
      {
        label: 'Chanthaburi',
        value: 'TH-22',
      },
      {
        label: 'Chiang Mai',
        value: 'TH-50',
      },
      {
        label: 'Chiang Rai',
        value: 'TH-57',
      },
      {
        label: 'Chon Buri',
        value: 'TH-20',
      },
      {
        label: 'Chumphon',
        value: 'TH-86',
      },
      {
        label: 'Kalasin',
        value: 'TH-46',
      },
      {
        label: 'Kamphaeng Phet',
        value: 'TH-62',
      },
      {
        label: 'Kanchanaburi',
        value: 'TH-71',
      },
      {
        label: 'Khon Kaen',
        value: 'TH-40',
      },
      {
        label: 'Krabi',
        value: 'TH-81',
      },
      {
        label: 'Lampang',
        value: 'TH-52',
      },
      {
        label: 'Lamphun',
        value: 'TH-51',
      },
      {
        label: 'Loei',
        value: 'TH-42',
      },
      {
        label: 'Lopburi',
        value: 'TH-16',
      },
      {
        label: 'Mae Hong Son',
        value: 'TH-58',
      },
      {
        label: 'Maha Sarakham',
        value: 'TH-44',
      },
      {
        label: 'Mukdahan',
        value: 'TH-49',
      },
      {
        label: 'Nakhon Nayok',
        value: 'TH-26',
      },
      {
        label: 'Nakhon Pathom',
        value: 'TH-73',
      },
      {
        label: 'Nakhon Phanom',
        value: 'TH-48',
      },
      {
        label: 'Nakhon Ratchasima',
        value: 'TH-30',
      },
      {
        label: 'Nakhon Sawan',
        value: 'TH-60',
      },
      {
        label: 'Nakhon Si Thammarat',
        value: 'TH-80',
      },
      {
        label: 'Nan',
        value: 'TH-55',
      },
      {
        label: 'Narathiwat',
        value: 'TH-96',
      },
      {
        label: 'Nong Bua Lam Phu',
        value: 'TH-39',
      },
      {
        label: 'Nong Khai',
        value: 'TH-43',
      },
      {
        label: 'Nonthaburi',
        value: 'TH-12',
      },
      {
        label: 'Pathum Thani',
        value: 'TH-13',
      },
      {
        label: 'Pattani',
        value: 'TH-94',
      },
      {
        label: 'Pattaya',
        value: 'TH-S',
      },
      {
        label: 'Phangnga',
        value: 'TH-82',
      },
      {
        label: 'Phatthalung',
        value: 'TH-93',
      },
      {
        label: 'Phayao',
        value: 'TH-56',
      },
      {
        label: 'Phetchabun',
        value: 'TH-67',
      },
      {
        label: 'Phetchaburi',
        value: 'TH-76',
      },
      {
        label: 'Phichit',
        value: 'TH-66',
      },
      {
        label: 'Phitsanulok',
        value: 'TH-65',
      },
      {
        label: 'Phra Nakhon Si Ayutthaya',
        value: 'TH-14',
      },
      {
        label: 'Phrae',
        value: 'TH-54',
      },
      {
        label: 'Phuket',
        value: 'TH-83',
      },
      {
        label: 'Prachin Buri',
        value: 'TH-25',
      },
      {
        label: 'Prachuap Khiri Khan',
        value: 'TH-77',
      },
      {
        label: 'Ranong',
        value: 'TH-85',
      },
      {
        label: 'Ratchaburi',
        value: 'TH-70',
      },
      {
        label: 'Rayong',
        value: 'TH-21',
      },
      {
        label: 'Roi Et',
        value: 'TH-45',
      },
      {
        label: 'Sa Kaeo',
        value: 'TH-27',
      },
      {
        label: 'Sakon Nakhon',
        value: 'TH-47',
      },
      {
        label: 'Samut Prakan',
        value: 'TH-11',
      },
      {
        label: 'Samut Sakhon',
        value: 'TH-74',
      },
      {
        label: 'Samut Songkhram',
        value: 'TH-75',
      },
      {
        label: 'Saraburi',
        value: 'TH-19',
      },
      {
        label: 'Satun',
        value: 'TH-91',
      },
      {
        label: 'Sisaket',
        value: 'TH-33',
      },
      {
        label: 'Sing Buri',
        value: 'TH-17',
      },
      {
        label: 'Songkhla',
        value: 'TH-90',
      },
      {
        label: 'Sukhothai',
        value: 'TH-64',
      },
      {
        label: 'Suphan Buri',
        value: 'TH-72',
      },
      {
        label: 'Surat Thani',
        value: 'TH-84',
      },
      {
        label: 'Surin',
        value: 'TH-32',
      },
      {
        label: 'Tak',
        value: 'TH-63',
      },
      {
        label: 'Trang',
        value: 'TH-92',
      },
      {
        label: 'Trat',
        value: 'TH-23',
      },
      {
        label: 'Ubon Ratchathani',
        value: 'TH-34',
      },
      {
        label: 'Udon Thani',
        value: 'TH-41',
      },
      {
        label: 'Uthai Thani',
        value: 'TH-61',
      },
      {
        label: 'Uttaradit',
        value: 'TH-53',
      },
      {
        label: 'Yala',
        value: 'TH-95',
      },
      {
        label: 'Yasothon',
        value: 'TH-35',
      },
    ],
  },
  TL: {
    provinces: [],
  },
  TG: {
    provinces: [],
  },
  TK: {
    provinces: [],
  },
  TO: {
    provinces: [],
  },
  TT: {
    provinces: [],
  },
  TA: {
    provinces: [],
  },
  TN: {
    provinces: [],
  },
  TR: {
    provinces: [],
  },
  TM: {
    provinces: [],
  },
  TC: {
    provinces: [],
  },
  TV: {
    provinces: [],
  },
  UG: {
    provinces: [],
  },
  UA: {
    provinces: [],
  },
  AE: {
    provinces: [
      {
        label: 'Abu Dhabi',
        value: 'AZ',
      },
      {
        label: 'Ajman',
        value: 'AJ',
      },
      {
        label: 'Dubai',
        value: 'DU',
      },
      {
        label: 'Fujairah',
        value: 'FU',
      },
      {
        label: 'Ras al-Khaimah',
        value: 'RK',
      },
      {
        label: 'Sharjah',
        value: 'SH',
      },
      {
        label: 'Umm al-Quwain',
        value: 'UQ',
      },
    ],
  },
  GB: {
    provinces: [
      {
        label: 'British Forces',
        value: 'BFP',
      },
      {
        label: 'England',
        value: 'ENG',
      },
      {
        label: 'Northern Ireland',
        value: 'NIR',
      },
      {
        label: 'Scotland',
        value: 'SCT',
      },
      {
        label: 'Wales',
        value: 'WLS',
      },
    ],
  },
  US: {
    provinces: [
      {
        label: 'Alabama',
        value: 'AL',
      },
      {
        label: 'Alaska',
        value: 'AK',
      },
      {
        label: 'American Samoa',
        value: 'AS',
      },
      {
        label: 'Arizona',
        value: 'AZ',
      },
      {
        label: 'Arkansas',
        value: 'AR',
      },
      {
        label: 'California',
        value: 'CA',
      },
      {
        label: 'Colorado',
        value: 'CO',
      },
      {
        label: 'Connecticut',
        value: 'CT',
      },
      {
        label: 'Delaware',
        value: 'DE',
      },
      {
        label: 'Florida',
        value: 'FL',
      },
      {
        label: 'Georgia',
        value: 'GA',
      },
      {
        label: 'Guam',
        value: 'GU',
      },
      {
        label: 'Hawaii',
        value: 'HI',
      },
      {
        label: 'Idaho',
        value: 'ID',
      },
      {
        label: 'Illinois',
        value: 'IL',
      },
      {
        label: 'Indiana',
        value: 'IN',
      },
      {
        label: 'Iowa',
        value: 'IA',
      },
      {
        label: 'Kansas',
        value: 'KS',
      },
      {
        label: 'Kentucky',
        value: 'KY',
      },
      {
        label: 'Louisiana',
        value: 'LA',
      },
      {
        label: 'Maine',
        value: 'ME',
      },
      {
        label: 'Marshall Islands',
        value: 'MH',
      },
      {
        label: 'Maryland',
        value: 'MD',
      },
      {
        label: 'Massachusetts',
        value: 'MA',
      },
      {
        label: 'Michigan',
        value: 'MI',
      },
      {
        label: 'Federated States of Micronesia',
        value: 'FM',
      },
      {
        label: 'Minnesota',
        value: 'MN',
      },
      {
        label: 'Mississippi',
        value: 'MS',
      },
      {
        label: 'Missouri',
        value: 'MO',
      },
      {
        label: 'Montana',
        value: 'MT',
      },
      {
        label: 'Nebraska',
        value: 'NE',
      },
      {
        label: 'Nevada',
        value: 'NV',
      },
      {
        label: 'New Hampshire',
        value: 'NH',
      },
      {
        label: 'New Jersey',
        value: 'NJ',
      },
      {
        label: 'New Mexico',
        value: 'NM',
      },
      {
        label: 'New York',
        value: 'NY',
      },
      {
        label: 'North Carolina',
        value: 'NC',
      },
      {
        label: 'North Dakota',
        value: 'ND',
      },
      {
        label: 'Northern Mariana Islands',
        value: 'MP',
      },
      {
        label: 'Ohio',
        value: 'OH',
      },
      {
        label: 'Oklahoma',
        value: 'OK',
      },
      {
        label: 'Oregon',
        value: 'OR',
      },
      {
        label: 'Palau',
        value: 'PW',
      },
      {
        label: 'Pennsylvania',
        value: 'PA',
      },
      {
        label: 'Puerto Rico',
        value: 'PR',
      },
      {
        label: 'Rhode Island',
        value: 'RI',
      },
      {
        label: 'South Carolina',
        value: 'SC',
      },
      {
        label: 'South Dakota',
        value: 'SD',
      },
      {
        label: 'Tennessee',
        value: 'TN',
      },
      {
        label: 'Texas',
        value: 'TX',
      },
      {
        label: 'Virgin Islands',
        value: 'VI',
      },
      {
        label: 'Utah',
        value: 'UT',
      },
      {
        label: 'Vermont',
        value: 'VT',
      },
      {
        label: 'Virginia',
        value: 'VA',
      },
      {
        label: 'Washington',
        value: 'WA',
      },
      {
        label: 'District of Columbia',
        value: 'DC',
      },
      {
        label: 'West Virginia',
        value: 'WV',
      },
      {
        label: 'Wisconsin',
        value: 'WI',
      },
      {
        label: 'Wyoming',
        value: 'WY',
      },
      {
        label: 'Armed Forces Americas',
        value: 'AA',
      },
      {
        label: 'Armed Forces Europe',
        value: 'AE',
      },
      {
        label: 'Armed Forces Pacific',
        value: 'AP',
      },
    ],
  },
  UM: {
    provinces: [],
  },
  UY: {
    provinces: [],
  },
  UZ: {
    provinces: [],
  },
  VU: {
    provinces: [],
  },
  VE: {
    provinces: [],
  },
  VN: {
    provinces: [],
  },
  VG: {
    provinces: [],
  },
  WF: {
    provinces: [],
  },
  EH: {
    provinces: [],
  },
  YE: {
    provinces: [],
  },
  ZM: {
    provinces: [],
  },
  ZW: {
    provinces: [],
  },
};

export { countryState };
