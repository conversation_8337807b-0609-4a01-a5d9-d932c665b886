{"version": 3, "names": ["visitors", "require", "exports", "_t", "cache", "_traverseNode", "_index", "_index2", "_hub", "VISITOR_KEYS", "removeProperties", "traverseFast", "traverse", "parent", "opts", "scope", "state", "parentPath", "visitSelf", "noScope", "type", "Error", "explode", "traverseNode", "_default", "default", "verify", "cheap", "node", "enter", "path", "<PERSON><PERSON><PERSON><PERSON>", "clearNode", "tree", "hasDenylistedType", "has", "stop", "hasType", "denylistTypes", "includes", "denylist", "process", "version"], "sources": ["../src/index.ts"], "sourcesContent": ["import * as visitors from \"./visitors.ts\";\nimport {\n  VISITOR_KEYS,\n  removeProperties,\n  type RemovePropertiesOptions,\n  traverseFast,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport * as cache from \"./cache.ts\";\nimport type NodePath from \"./path/index.ts\";\nimport type { default as Scope, Binding } from \"./scope/index.ts\";\nimport type { ExplodedVisitor, Visitor } from \"./types.ts\";\nimport { traverseNode } from \"./traverse-node.ts\";\n\nexport type { ExplodedVisitor, Visitor, Binding };\nexport { default as NodePath } from \"./path/index.ts\";\nexport { default as Scope } from \"./scope/index.ts\";\nexport { default as Hub } from \"./hub.ts\";\nexport type { HubInterface } from \"./hub.ts\";\n\nexport { visitors };\n\nexport type TraverseOptions<S = t.Node> = {\n  scope?: Scope;\n  noScope?: boolean;\n  denylist?: string[];\n  shouldSkip?: (node: NodePath) => boolean;\n} & Visitor<S>;\n\nexport type ExplodedTraverseOptions<S = t.Node> = TraverseOptions<S> &\n  ExplodedVisitor<S>;\n\nfunction traverse<S>(\n  parent: t.Node,\n  opts: TraverseOptions<S>,\n  scope: Scope | undefined,\n  state: S,\n  parentPath?: NodePath,\n  visitSelf?: boolean,\n): void;\n\nfunction traverse(\n  parent: t.Node,\n  opts: TraverseOptions,\n  scope?: Scope,\n  state?: any,\n  parentPath?: NodePath,\n  visitSelf?: boolean,\n): void;\n\nfunction traverse<Options extends TraverseOptions>(\n  parent: t.Node,\n  // @ts-expect-error provide {} as default value for Options\n  opts: Options = {},\n  scope?: Scope,\n  state?: any,\n  parentPath?: NodePath,\n  visitSelf?: boolean,\n) {\n  if (!parent) return;\n\n  if (!opts.noScope && !scope) {\n    if (parent.type !== \"Program\" && parent.type !== \"File\") {\n      throw new Error(\n        \"You must pass a scope and parentPath unless traversing a Program/File. \" +\n          `Instead of that you tried to traverse a ${parent.type} node without ` +\n          \"passing scope and parentPath.\",\n      );\n    }\n  }\n\n  if (!parentPath && visitSelf) {\n    throw new Error(\"visitSelf can only be used when providing a NodePath.\");\n  }\n\n  if (!VISITOR_KEYS[parent.type]) {\n    return;\n  }\n\n  visitors.explode(opts as Visitor);\n\n  traverseNode(\n    parent,\n    opts as ExplodedVisitor,\n    scope,\n    state,\n    parentPath,\n    /* skipKeys */ null,\n    visitSelf,\n  );\n}\n\nexport default traverse;\n\ntraverse.visitors = visitors;\ntraverse.verify = visitors.verify;\ntraverse.explode = visitors.explode;\n\ntraverse.cheap = function (node: t.Node, enter: (node: t.Node) => void) {\n  traverseFast(node, enter);\n  return;\n};\n\ntraverse.node = function (\n  node: t.Node,\n  opts: ExplodedTraverseOptions,\n  scope?: Scope,\n  state?: any,\n  path?: NodePath,\n  skipKeys?: Record<string, boolean>,\n) {\n  traverseNode(node, opts, scope, state, path, skipKeys);\n  // traverse.node always returns undefined\n};\n\ntraverse.clearNode = function (node: t.Node, opts?: RemovePropertiesOptions) {\n  removeProperties(node, opts);\n};\n\ntraverse.removeProperties = function (\n  tree: t.Node,\n  opts?: RemovePropertiesOptions,\n) {\n  traverseFast(tree, traverse.clearNode, opts);\n  return tree;\n};\n\ntype HasDenylistedTypeState = {\n  has: boolean;\n  type: t.Node[\"type\"];\n};\nfunction hasDenylistedType(path: NodePath, state: HasDenylistedTypeState) {\n  if (path.node.type === state.type) {\n    state.has = true;\n    path.stop();\n  }\n}\n\ntraverse.hasType = function (\n  tree: t.Node,\n  type: t.Node[\"type\"],\n  denylistTypes?: Array<string>,\n): boolean {\n  // the node we're searching in is denylisted\n  if (denylistTypes?.includes(tree.type)) return false;\n\n  // the type we're looking for is the same as the passed node\n  if (tree.type === type) return true;\n\n  const state: HasDenylistedTypeState = {\n    has: false,\n    type: type,\n  };\n\n  traverse(\n    tree,\n    {\n      noScope: true,\n      denylist: denylistTypes,\n      enter: hasDenylistedType,\n    },\n    null,\n    state,\n  );\n\n  return state.has;\n};\n\ntraverse.cache = cache;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAA0CC,OAAA,CAAAF,QAAA,GAAAA,QAAA;AAC1C,IAAAG,EAAA,GAAAF,OAAA;AAOA,IAAAG,KAAA,GAAAH,OAAA;AAIA,IAAAI,aAAA,GAAAJ,OAAA;AAGA,IAAAK,MAAA,GAAAL,OAAA;AACA,IAAAM,OAAA,GAAAN,OAAA;AACA,IAAAO,IAAA,GAAAP,OAAA;AAA0C;EAfxCQ,YAAY;EACZC,gBAAgB;EAEhBC;AAAY,IAAAR,EAAA;AA6Cd,SAASS,QAAQA,CACfC,MAAc,EAEdC,IAAa,GAAG,CAAC,CAAC,EAClBC,KAAa,EACbC,KAAW,EACXC,UAAqB,EACrBC,SAAmB,EACnB;EACA,IAAI,CAACL,MAAM,EAAE;EAEb,IAAI,CAACC,IAAI,CAACK,OAAO,IAAI,CAACJ,KAAK,EAAE;IAC3B,IAAIF,MAAM,CAACO,IAAI,KAAK,SAAS,IAAIP,MAAM,CAACO,IAAI,KAAK,MAAM,EAAE;MACvD,MAAM,IAAIC,KAAK,CACb,yEAAyE,GACtE,2CAA0CR,MAAM,CAACO,IAAK,gBAAe,GACtE,+BACJ,CAAC;IACH;EACF;EAEA,IAAI,CAACH,UAAU,IAAIC,SAAS,EAAE;IAC5B,MAAM,IAAIG,KAAK,CAAC,uDAAuD,CAAC;EAC1E;EAEA,IAAI,CAACZ,YAAY,CAACI,MAAM,CAACO,IAAI,CAAC,EAAE;IAC9B;EACF;EAEApB,QAAQ,CAACsB,OAAO,CAACR,IAAe,CAAC;EAEjC,IAAAS,0BAAY,EACVV,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,UAAU,EACK,IAAI,EACnBC,SACF,CAAC;AACH;AAAC,IAAAM,QAAA,GAEcZ,QAAQ;AAAAV,OAAA,CAAAuB,OAAA,GAAAD,QAAA;AAEvBZ,QAAQ,CAACZ,QAAQ,GAAGA,QAAQ;AAC5BY,QAAQ,CAACc,MAAM,GAAG1B,QAAQ,CAAC0B,MAAM;AACjCd,QAAQ,CAACU,OAAO,GAAGtB,QAAQ,CAACsB,OAAO;AAEnCV,QAAQ,CAACe,KAAK,GAAG,UAAUC,IAAY,EAAEC,KAA6B,EAAE;EACtElB,YAAY,CAACiB,IAAI,EAAEC,KAAK,CAAC;EACzB;AACF,CAAC;AAEDjB,QAAQ,CAACgB,IAAI,GAAG,UACdA,IAAY,EACZd,IAA6B,EAC7BC,KAAa,EACbC,KAAW,EACXc,IAAe,EACfC,QAAkC,EAClC;EACA,IAAAR,0BAAY,EAACK,IAAI,EAAEd,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEc,IAAI,EAAEC,QAAQ,CAAC;AAExD,CAAC;AAEDnB,QAAQ,CAACoB,SAAS,GAAG,UAAUJ,IAAY,EAAEd,IAA8B,EAAE;EAC3EJ,gBAAgB,CAACkB,IAAI,EAAEd,IAAI,CAAC;AAC9B,CAAC;AAEDF,QAAQ,CAACF,gBAAgB,GAAG,UAC1BuB,IAAY,EACZnB,IAA8B,EAC9B;EACAH,YAAY,CAACsB,IAAI,EAAErB,QAAQ,CAACoB,SAAS,EAAElB,IAAI,CAAC;EAC5C,OAAOmB,IAAI;AACb,CAAC;AAMD,SAASC,iBAAiBA,CAACJ,IAAc,EAAEd,KAA6B,EAAE;EACxE,IAAIc,IAAI,CAACF,IAAI,CAACR,IAAI,KAAKJ,KAAK,CAACI,IAAI,EAAE;IACjCJ,KAAK,CAACmB,GAAG,GAAG,IAAI;IAChBL,IAAI,CAACM,IAAI,CAAC,CAAC;EACb;AACF;AAEAxB,QAAQ,CAACyB,OAAO,GAAG,UACjBJ,IAAY,EACZb,IAAoB,EACpBkB,aAA6B,EACpB;EAET,IAAIA,aAAa,YAAbA,aAAa,CAAEC,QAAQ,CAACN,IAAI,CAACb,IAAI,CAAC,EAAE,OAAO,KAAK;EAGpD,IAAIa,IAAI,CAACb,IAAI,KAAKA,IAAI,EAAE,OAAO,IAAI;EAEnC,MAAMJ,KAA6B,GAAG;IACpCmB,GAAG,EAAE,KAAK;IACVf,IAAI,EAAEA;EACR,CAAC;EAEDR,QAAQ,CACNqB,IAAI,EACJ;IACEd,OAAO,EAAE,IAAI;IACbqB,QAAQ,EAAEF,aAAa;IACvBT,KAAK,EAAEK;EACT,CAAC,EACD,IAAI,EACJlB,KACF,CAAC;EAED,OAAOA,KAAK,CAACmB,GAAG;AAClB,CAAC;AAEDvB,QAAQ,CAACR,KAAK,GAAGA,KAAK;AAAC,WAAAqC,OAAA,iBAAAA,OAAA,CAAAC,OAAA,uBAAAxC,OAAA"}