import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout } from '../atoms';
import { LayoutIcon } from '../coreComponents';

const SocialLinkBar = ({ attributes, onPress, onAction }) => {
  const { appmakerAction } = attributes;
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  return (
    <Layout style={styles.container}>
      <LayoutIcon attributes={{ iconName: 'instagram' }} />
      <LayoutIcon attributes={{ iconName: 'facebook' }} />
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    backgroundColor: '#ffffff',
    padding: 6,
  },
});

export default SocialLinkBar;
