import React from 'react';
import { View, Text } from 'react-native';
import { useDataSourceV2, usePageState } from '@appmaker-xyz/core';
import { ShopifyDataSourceConfig } from '@appmaker-xyz/shopify';

const ShopifyCollectionFilterSort = ({
  BlockItemRender,
  onAction,
}: {
  BlockItemRender: any;
  onAction: any;
}) => {
  const currentAction = usePageState(
    (pageState: any) => pageState.currentAction,
  );
  const searchQuery = usePageState((pageState: any) => pageState?.searchQuery);
  const params = {
    collectionId: currentAction?.params?.collectionId,
    collectionHandle: currentAction?.params?.collectionHandle,
    searchQuery: searchQuery,
  };
  const dataSource = ShopifyDataSourceConfig.ProductFilters(params);
  const [data, { item }] = useDataSourceV2({
    dataSource,
    enabled: true,
  });
  return (
    <BlockItemRender
      blockData={item}
      onAction={onAction}
      block={{
        name: 'shopify/collection-filter',
        attributes: {
          show_filter: true,
          show_sort: true,
        },
      }}
    />
  );
};

export default ShopifyCollectionFilterSort;
