import React from 'react';
import { appStorageApi, appPluginStoreApi } from '@appmaker-xyz/core';
import { Layout, AppmakerText } from '@appmaker-xyz/uikit';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '@appmaker-xyz/uikit/src/theme/ThemeContext';
import { useCartProduct, currencyHelper } from '@appmaker-xyz/shopify';

function amountFinal(a, b) {
  return Math.round(
    parseFloat(a) -
      (parseFloat(a) - parseFloat(a) * (parseFloat(b) / 100)).toFixed(
        parseFloat(2),
      ),
  );
}

const SpecialText = (props) => {
  const { customAttributes, variant, quantity, isLineItemFree, isFreeGift } =
    useCartProduct(props);
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  const pluginSettings = appPluginStoreApi().getState().plugins?.xeno?.settings;
  /*
  if(blockItem.node.variant.product.productType != "membership"){

    echo(`Insider member discount is ${currencyHelper(amountFinal(parseFloat(blockItem.node.quantity)*parseFloat(blockItem.node.variant.price.amount),parseFloat(plugins.xeno.settings.discountPercentageOnMembership)),blockItem.node.variant.price.amount)}`) }
  */
  const { user, checkout } = appStorageApi().getState();
  let isMemberProductInCart = false;

  //check if an array of object contains a key productType with a value membership
  const startsWith = (arr, str) => {
    return arr.some((item) => item.startsWith(str));
  };
  const isCurrentUserChumbakInsider =
    user && user?.tags?.length > 0
      ? startsWith(
          user.tags,
          pluginSettings?.userTagsForXenoStartsWith || 'Xeno Tier:Member',
        )
      : false;

  const isCurrentProductIsChumbakInsider = customAttributes.find(
    (item) => item.key === '_membership' && item.value === '_membership',
  );
  const dontShowSpecialText =
    isCurrentProductIsChumbakInsider || isFreeGift || isLineItemFree;

  if (checkout?.lineItems?.edges?.length > 0 && !dontShowSpecialText) {
    let lineItems = checkout?.lineItems?.edges;
    isMemberProductInCart = lineItems?.some?.((lineItem) => {
      return lineItem?.node?.customAttributes?.some?.(
        (item) => item.key === '_membership' && item.value === '_membership',
      );
    });
  }
  if (
    (!dontShowSpecialText && isCurrentUserChumbakInsider) ||
    isMemberProductInCart
  ) {
    return (
      <Layout style={styles.insiderBadge}>
        <Icon name="tag" size={12} color="#3b3b3b" style={styles.tagicon} />
        <AppmakerText fontColor="#3b3b3b" category="smallButtonText">
          Insider member discount is{' '}
          {currencyHelper(
            amountFinal(
              parseFloat(quantity) * parseFloat(variant.price.amount),
              parseFloat(pluginSettings.discountPercentageOnMembership),
            ),
            variant.price.currencyCode,
          )}
        </AppmakerText>
      </Layout>
    );
  } else {
    return null;
  }
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    insiderBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 6,
      paddingVertical: 2,
      backgroundColor: '#E7E7E7',
      marginBottom: 6,
    },
    tagicon: {
      marginRight: 4,
    },
  });

export default SpecialText;
