import { appmaker } from '@appmaker-xyz/core';
import { spacing } from '@appmaker-xyz/uikit/src/styles/index';
import { helpers } from '@appmaker-xyz/core';
const { findBlockIndex } = helpers;

const phoneBlock = {
  clientId: 'custom-otp-login',
  name: 'appmaker/actionbar',
  attributes: {
    featureImg: 'https://app-static-pages.appmaker.xyz/images/otp.png',
    imageResize: 'contain',
    title: 'Continue with Phone Number',
    loading: false,
    fontColor: '#ffffff',
    containerStyle: {
      backgroundColor: '#4F4F4F',
      marginHorizontal: spacing.base,
      marginBottom: spacing.mini,
      borderRadius: 0,
    },
    appmakerAction: {
      action: 'OPEN_INAPP_PAGE',
      pageId: 'LucentCustomOTPLogin',
      params: {
        pageData: {
          otpCount:
            "{{plugins['lucent-simple-otp'].settings.total_otp_digit_count}}",
        },
      },
    },
  },
};
const emailBlock = {
  clientId: 'custom-email-login',
  name: 'appmaker/actionbar',
  attributes: {
    featureImg: 'https://app-static-pages.appmaker.xyz/images/mail.png',
    imageResize: 'contain',
    title: 'Continue with Email',
    loading: false,
    fontColor: '#ffffff',
    containerStyle: {
      backgroundColor: '#A9AEB7',
      marginHorizontal: spacing.base,
      marginBottom: spacing.mini,
      borderRadius: 0,
    },
    appmakerAction: {
      action: 'OPEN_INAPP_PAGE',
      pageId: 'LucentEmailOtpRequest',
    },
  },
};
export function addWidgetinLoginPage() {
  // Adding extra blocksLogin
  appmaker.addFilter(
    'inapp-page-data-response',
    'recently-viewed-plugin-sub',
    (data, { pageId }) => {
      if (pageId === 'LoginOptions') {
        const clientId = 'login-layout';
        const { blocks } = data;
        const index = findBlockIndex(blocks, clientId);
        // const
        const otpIndex = findBlockIndex(
          blocks[index].innerBlocks,
          phoneBlock.clientId,
        );
        if (otpIndex === -1) {
          blocks[index].innerBlocks = [phoneBlock, emailBlock]
          //     blocks.splice(index + 1, 0, block);
        }
      }
      return data;
    },
  );
}
