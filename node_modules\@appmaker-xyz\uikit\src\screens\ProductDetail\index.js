import React, { useEffect, useState } from 'react';
import { StyleSheet, ScrollView, SafeAreaView, View, Text } from 'react-native';
import * as productData from './product';
import ServiceAPI from '../../../../../src/config/rest';
import Config from 'Config';
import VariationSelect from './VariationSelect';
import { appSettings } from '@appmaker-xyz/core';
import {
  ActionButtons,
  ProductData,
  ProductImage,
  CounterBlock,
  ProductInfo,
  ProductVariationBlock,
  ProductReview,
  ExpandableTextBlock,
} from '../../components/pages/productDetail/components';
import {
  Layout,
  ActionBar,
  DrawerMenu,
  ListItem,
  TableCell,
  GridItem,
  ProductGridBlock,
  ProductListBlock,
  CheckoutButton,
} from '@appmaker-xyz/uikit';
import { getDetailBlocks } from './data';
import { BlocksView } from '@appmaker-xyz/react-native';
import { addFilter } from '@appmaker-xyz/core';
import MessageView from 'appmakerLibComponents/MessageView';
import { testProps } from '@appmaker-xyz/core';
import { color, spacing } from '../../styles';
function handleAction(params) {
  console.log(
    `action not implemeted talk to Saleeh  ${JSON.stringify(params, null, 2)}`,
  );
}
ServiceAPI.init(Config.URL, Config.TOKEN);
global.ServiceAPI = ServiceAPI;
{
  /* <ActionBar
attributes={{title: 'Go Somewhere', leftIcon: 'settings'}}
onPress={() => console.log('Go somewhre')}
/> */
}
const SaleehBlock = ({ attributes }) => <Text>{attributes.name}:====</Text>;
// addFilter('appmaker-blocks', 'product-image', (blocks) => {
//   // blocks['appmaker/woocommerce-product-detail-page'] = {
//   //   View: ({ attributes, pageDispatch, pageState, data }) => {
//   //     // console.log(pageState?.state);
//   //     console.log(data, 'detail-page');
//   //     // if(pageState?)
//   //     return null
//   //   }
//   // }
//   // blocks['appmaker/saleeh'] = {
//   //   View: SaleehBlock,
//   // };
//   // blocks['appmaker/product-extra-blocks'] = {
//   //   View: ({
//   //     attributes,
//   //     pageDispatch,
//   //     BlocksView,
//   //     pageState,
//   //     data,
//   //     onAction,
//   //   }) => {
//   //     return (
//   //       <BlocksView template={attributes.extra_blocks} onAction={onAction} />
//   //     );
//   //   },
//   // };
//   // blocks['appmaker/product-variations-blocks'] = {
//   //   View: ({
//   //     attributes,
//   //     pageDispatch,
//   //     BlocksView,
//   //     pageState,
//   //     data,
//   //     onAction,
//   //   }) => {
//   //     return (
//   //       <BlocksView
//   //         template={attributes.variations_blocks}
//   //         onAction={onAction}
//   //       />
//   //     );
//   //   },
//   // };
//   // blocks['appmaker/custom-app-list-listener'] = {
//   //   View: ({ attributes, pageDispatch, pageState, data }) => {
//   //     addFilter('appmaker-actions', 'add-SET_FILTER', (actions) => {
//   //       actions.SET_FILTER = (action) => {
//   //         pageDispatch({
//   //           type: 'set_values',
//   //           values: { answer: action.dataSource.attributes },
//   //         });
//   //       };
//   //       return actions;
//   //     });
//   //     // console.log(pageState?.state);
//   //     // console.log(data);
//   //     // if(pageState?)
//   //     return <Text>Hello : {JSON.stringify(pageState, null, 2)}</Text>;
//   //   },
//   // };
//   // blocks['appmaker/woocommerce-product-list-listener'] = {
//   //   View: ({ attributes, pageDispatch, pageState, data }) => {
//   //     // console.log(pageState?.state);
//   //     // console.log(data);
//   //     // if(pageState?)
//   //     return null;
//   //   },
//   // };
//   // blocks['appmaker/checkout-button'] = {
//   //   View: ({ onAction, attributes, pageState }) => {
//   //     const { appmakerAction } = attributes;
//   //     // console.log(pageState?.state?.cartResponse?.count);
//   //     return (
//   //       <CheckoutButton
//   //         onPress={() => {
//   //           onAction(appmakerAction);
//   //         }}
//   //         {...attributes}
//   //       />
//   //     );
//   //   },
//   // };
//   // blocks['appmaker/product-grid-item'] = {
//   //   View: ProductGridBlock,
//   // };
//   // blocks['appmaker/product-list-item'] = {
//   //   View: ProductListBlock,
//   // };
//   // blocks['appmaker/product-list-item-old'] = {
//   //   View: ({ attributes, clientId, data, onAction, ...props }) => {
//   //     // return ListItem;
//   //     const item = data;
//   //     const staticTexts = {
//   //       outOfStockText: 'Out of stock',
//   //     };
//   //     let defaultItemProps = {
//   //       clientId,
//   //       uri: item.thumbnail,
//   //       title: item.name,
//   //       salePrice: item.on_sale && item.regular_price_display,
//   //       regularPrice: item.price_display,
//   //       groceryMode: appSettings.getOptionAsBoolean('grocery_mode'),
//   //       saved: item.saved,
//   //       staticTexts: staticTexts,
//   //       onSaved: (status) => {
//   //         // status ? saveBookMark(toProduct(item)) : deleteBookMark(toProduct(item));
//   //       },
//   //       quatity: 1,
//   //       outOfStock: item.in_stock ? false : staticTexts.outOfStockText,
//   //       attribute: item.labels,
//   //       quantityLoading: false,
//   //       saleBadge: item.sale_percentage || '',
//   //       onPress: () => {
//   //         onAction(attributes.appmakerAction);
//   //       },
//   //       onQuantityChange: async (quantity) => {},
//   //     };
//   //     return <ProductListBlock {...defaultItemProps} />;
//   //     return (
//   //       <View>
//   //         <Text>{JSON.stringify(attributes, null, 2)}</Text>
//   //       </View>
//   //     );
//   //   },
//   // };
//   // blocks['appmaker/product-info'] = {
//   //   View: ProductInfo,
//   // };
//   // blocks['appmaker/expandable-text-block'] = {
//   //   View: ExpandableTextBlock,
//   // };
//   // blocks['appmaker/product-review'] = {
//   //   View: ProductReview,
//   // };
//   // blocks['appmaker/product-counter'] = {
//   //   View: CounterBlock,
//   // };
//   // blocks['appmaker/product-variation'] = {
//   //   View: ProductVariationBlock,
//   // };
//   // blocks['appmaker/product-image'] = {
//   //   View: ProductImage,
//   // };
//   // blocks['appmaker/grid-item'] = {
//   //   View: GridItem,
//   // };
//   // blocks['appmaker/table-cell'] = {
//   //   View: TableCell,
//   // };
//   // blocks['appmaker/actionbar'] = {
//   //   View: ActionBar,
//   // };
//   // blocks['appmaker/drawer-menu'] = {
//   //   View: DrawerMenu,
//   // };
//   // blocks['appmaker/product-data'] = {
//   //   View: ProductData,
//   // };

//   // blocks['appmaker/buttons'] = {
//   //   View: ActionButtons,
//   // };

//   // blocks['appmaker/woocommerce-prodict-detail-listener'] = {
//   //   View: ({ attributes, pageDispatch, pageState, data, ...props }) => {
//   //     // console.log(data);
//   //     // if(pageState?)
//   //     const variationSelect = new VariationSelect(data);

//   //     useEffect(() => {
//   //       if (pageState?.state?.filters) {
//   //         let product, imageIndex;

//   //         Object.keys(pageState?.state?.filters).map((attribute) => {
//   //           const attribute_value = pageState?.state?.filters[attribute];
//   //           imageIndex =
//   //             variationSelect.getVariationImageId(attribute, attribute_value) +
//   //             1;
//   //           product = variationSelect.getVariation(attribute, attribute_value);
//   //         });

//   //         pageDispatch({
//   //           type: 'set_values',
//   //           values: { product, imageIndex },
//   //         });
//   //       }
//   //     }, [pageState?.state?.filters]);
//   //     const productId = pageState?.state?.product?.id;
//   //     // console.log(productId);
//   //     return null;
//   //     return (
//   //       <View {...testProps(`product-${productId}`)}>
//   //         {/* <Text>{productId}</Text> */}
//   //       </View>
//   //     );
//   //     // return (
//   //     //   <Text>{JSON.stringify(pageState?.state?.product?.id, null, 2)}</Text>
//   //     // );
//   //   },
//   // };
//   return blocks;
// });

// addFilter('appmaker-actions', 'custom-actions', (actions) => {
//   actions.ADD_TO_CART = async (action) =>
//     ServiceAPI.api.addToCart(action.params);

//   // actions.ADD_TO_CART = async (action) => {
//   //   console.log('adding');
//   //   // console.log(action.params);
//   //   try {
//   //     const resp = await ServiceAPI.api.addToCart(action.params);
//   //     console.log(resp);
//   //     return resp;
//   //   } catch (error) {
//   //     console.log(err);
//   //     console.log(error);
//   //   }
//   // };
//   actions.SHOW_MESSAGE = (action) => {
//     const { title, message } = action.params;
//     MessageView.success({ title, message });
//   };
//   return actions;
// });

const BlocksListScreen = (props) => {
  // console.log(getCurrent);
  // const dataSource = {attributes: productData.hxp};
  // const templateLoading = false;
  const [productSingeData, setProductSingeData] = useState(productData.hxp);
  const [pageTemplate, setPageTemplate] = useState(
    getDetailBlocks(productSingeData),
  );

  const [templateLoading, setTemplateLoading] = useState(false);
  const [refreshData, setrefresh] = useState(0);
  // useEffect(() => {
  //   const loadData = async () => {
  //     const products = await ServiceAPI.api.getProductList();
  //     const rand = Math.round(Math.random(0, 10) * 10);
  //     // // console.log();
  //     const resp = await ServiceAPI.api.getProduct(products[rand].id);
  //     // const resp = productData.hxp;
  //     const pageTemplate = getDetailBlocks(resp);
  //     setProductSingeData(resp);
  //     setPageTemplate(pageTemplate);
  //     setTemplateLoading(false);
  //   };
  //   loadData();
  // }, [refreshData]);

  const refetch = () => {
    setrefresh(Math.random(0, 100));
  };
  return (
    <Layout
      loading={templateLoading}
      style={{ flex: 1, backgroundColor: color.light }}>
      {/* {console.log('sss')} */}
      {!templateLoading && (
        <BlocksView
          template={pageTemplate?.blocks}
          fixedFooterBlock={pageTemplate?.fixedFooter}
          blockData={productSingeData}
          isRefreshing={templateLoading}
          onRefresh={() => {
            refetch();
          }}
          onAction={(appmakerAction) => {
            // console.log(JSON.stringify(appmakerAction, null, 2));
            handleAction(appmakerAction);
          }}
        />
      )}
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.light,
    },
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: spacing.base,
    },
  });

// export default ProductDetail;
export default BlocksListScreen;
