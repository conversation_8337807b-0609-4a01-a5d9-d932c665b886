import { View, ScrollView, StyleSheet } from 'react-native';
import React from 'react';
import { Layout, Button, AppmakerText, Accordion } from '@appmaker-xyz/uikit';
import { spacing, color } from '../../../../styles';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import MultiSliders from './filterBlocks/MultiSliders';
import CheckList from './filterBlocks/CheckList';
import { useFilterStore } from '../store';

const CloseIcon = (props) => <Icon name="x" color={color.dark} {...props} />;
const Check = (props) => <Icon name="check" color={color.white} {...props} />;
const filterComponents = {
  PRICE_RANGE: MultiSliders,
  LIST: CheckList,
};
function RenderFilterItem({ filterConfig }) {
  const Item = filterComponents[filterConfig.type];
  return Item ? <Item attributes={filterConfig} /> : null;
}
function FilterBlock({ avilableFilters }) {
  return (
    <>
      {avilableFilters.map((item) => (
        <Accordion
          attributes={{
            title: item.label,
            data: <RenderFilterItem filterConfig={item} />,
            filter: true,
          }}
        />
      ))}
    </>
  );
}
function DebugView() {
  const state = useFilterStore((state) => state.appliedFilters);
  console.log(JSON.stringify(state, null, 2));
  return <AppmakerText>Debug:-{JSON.stringify(state, null, 2)}</AppmakerText>;
}
export default function FilterModalContent({
  attributes,
  onClear,
  onApplyFilter,
  onClose,
}) {
  const { avilableFilters, appliedFilters } = attributes;
  return (
    <Layout style={styles.modalBody}>
      {/* <DebugView /> */}
      <Layout style={styles.modalHeader}>
        <Layout style={styles.headerItems}>
          <Button
            onPress={onClose}
            status="light"
            accessoryLeft={CloseIcon}
            small
          />
          <Layout>
            <AppmakerText category="bodyParagraphBold" status="demiDark">
              Filters
            </AppmakerText>
            {/* <AppmakerText category="highlighter2" status="success">
            2 Filters Selected
          </AppmakerText> */}
          </Layout>
        </Layout>
        <Layout style={styles.headerItems}>
          <Button onPress={onClear} status="light" small>
            <AppmakerText category="smallButtonText" status="danger">
              Clear All
            </AppmakerText>
          </Button>
          <Button
            onPress={() => {
              onApplyFilter(appliedFilters);
            }}
            status="dark"
            small
            accessoryLeft={Check}>
            Apply Filters
          </Button>
        </Layout>
      </Layout>
      <ScrollView style={styles.modalContent}>
        <Layout
          blank={avilableFilters?.length === 0 ? true : false}
          message="No filters available"
          iconName="filter">
          {/* {filterItems} */}
          <FilterBlock avilableFilters={avilableFilters} />
        </Layout>
        <Layout style={{ height: 24 }} />
      </ScrollView>
    </Layout>
  );
}

const styles = StyleSheet.create({
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    borderBottomColor: color.light,
    borderBottomWidth: 1,
  },
  innerContainer: {
    backgroundColor: color.white,
    flex: 1,
    borderRightColor: color.light,
    borderRightWidth: 1,
  },
  view: {
    justifyContent: 'flex-end',
    marginVertical: 0,
    marginHorizontal: 1,
  },
  modalBody: {
    backgroundColor: color.white,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
    flexDirection: 'column',
    height: '85%',
  },
  modalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: color.light,
    padding: spacing.base,
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
  },
  modalContent: {
    padding: spacing.md,
    flexDirection: 'column',
    width: '100%',
  },
  modalFooter: {
    width: '100%',
    backgroundColor: color.white,
    padding: spacing.base,
  },
  headerItems: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  centerView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  sortModalBody: {
    width: '80%',
    backgroundColor: color.white,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: spacing.small,
    flexDirection: 'column',
  },
  sortModalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: color.light,
    padding: spacing.base,
    borderTopStartRadius: spacing.md,
    borderTopEndRadius: spacing.md,
  },
  sortModalContent: {
    width: '100%',
    padding: spacing.base,
  },
  sortItem: {
    marginVertical: spacing.nano,
  },
  sliderContainer: {
    alignItems: 'center',
    paddingVertical: spacing.base,
  },
  sliderLabels: {
    flexDirection: 'row',
    width: 310,
    justifyContent: 'space-between',
  },
  trackStyle: {
    borderRadius: spacing.small,
    height: spacing.nano / 2,
  },
  slideSelectedStyle: {
    backgroundColor: color.primary,
  },
  markerStyle: {
    height: 30,
    width: 30,
    borderRadius: spacing.md,
    backgroundColor: color.light,
    borderWidth: 0.5,
    borderColor: color.demiDark,
  },
  markerPressed: {
    backgroundColor: color.grey,
  },
});
