import { StyleSheet, Dimensions } from 'react-native';

const { width } = Dimensions.get('window');

// spacing b/w image and text container
const marginLeftTextContainer = 10;
// Image height and width
const imageWidth = 100;
const imageHeight = 80;
// Right and left margin for container. so total margin will be 20
const horizontalMargin = 10;

const styles = StyleSheet.create({
  rootContainer: {
    flex: 1,
    alignContent: 'center',
    justifyContent: 'center',
  },
  container: {
    backgroundColor: '#fff',
    alignContent: 'center',
    alignItems: 'center',
    marginHorizontal: 0,
    marginVertical: 0,
    flexDirection: 'row',
    paddingHorizontal: 16,
    height: 107,
  },
  image: {
    height: imageHeight,
    width: imageWidth,
    borderRadius: 0,
    marginBottom: 0,
    marginRight: 0,
  },
  title: {
    color: '#000',
    fontFamily: 'Lato-Bold',
    fontSize: 16,
    marginRight: 7,
    marginBottom: 17,
  },
  // description: {
  //   color: '#99999c',
  //   fontSize: 13,
  //   marginTop: 5,
  // },
  author: {
    fontStyle: 'italic',
    color: '#000000',
    marginTop: 5,
    fontSize: 15,
    alignItems: 'flex-end',
  },
  textContainer: {
    marginLeft: 0,
    // width of the whole text container will be Screen width minus image width and margin
    width:
      width - (imageWidth + marginLeftTextContainer + horizontalMargin * 2),
    flex: 1,
    flexDirection: 'column',
  },
  descriptionNoImage: {
    color: '#99999c',
    fontSize: 15,
    fontFamily: 'Lato-Regular',
    marginTop: 10,
  },
  category: {
    marginBottom: 6,
    fontSize: 9,
    color: '#1A1A1A',
    fontFamily: 'NotoSans',
  },
  authorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  userIcon: {
    marginRight: 6,
  },
  authorText: {
    marginBottom: 0,
    fontSize: 9,
    color: '#1A1A1A',
    fontFamily: 'NotoSans',
  },
});

export default styles;
