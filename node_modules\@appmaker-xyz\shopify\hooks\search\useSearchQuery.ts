import { usePageState } from '@appmaker-xyz/core';

const useSearchResult = () => {
  const predictiveSearchResult = usePageState(
    (state) => state.predictiveResults,
  );
  return {
    predictiveSearchResult,
  };
};

const useSearchQuery = () => {
  const setPageState = usePageState((state) => state?.setPageState);
  const searchQuery = usePageState((state) => state?.searchInputValue);
  const setSearchQuery = (value: string) => {
    setPageState({ searchInputValue: value });
  };
  return {
    searchQuery: searchQuery || '',
    setSearchQuery,
  };
};

export { useSearchResult, useSearchQuery };
