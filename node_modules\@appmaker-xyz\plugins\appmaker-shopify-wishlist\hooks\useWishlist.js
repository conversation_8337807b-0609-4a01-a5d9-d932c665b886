import { useState, useEffect } from 'react';
import { runDataSource } from '@appmaker-xyz/core';

export function useWishlist(props) {
  const key = props?.key || 'default';
  const [wishlist, setWishlist] = useState([]);
  const [loading, setLoading] = useState(true);
  const wishlistDatasource = {
    source: 'appmaker-shopify-wishlist',
    attributes: {},
  };
  async function fetchWishlist() {
    setLoading(true);
    try {
      const [response] = await runDataSource(
        {
          dataSource: wishlistDatasource,
        },
        {
          methodName: 'getAllProducts',
          params: {
            key,
          },
        },
      );
      setLoading(false);
      response?.nodes ? setWishlist(response.nodes) : setWishlist([]);
    } catch (err) {
      setLoading(false);
      console.error(err);
    }
  }
  useEffect(() => {
    fetchWishlist();
  }, []);

  const removeProduct = async ({ id, key = 'default' }) => {
    const [response] = await runDataSource(
      {
        dataSource: {
          source: 'appmaker-shopify-wishlist',
          attributes: {},
        },
      },
      {
        methodName: 'removeItem',
        params: {
          id,
          key,
        },
      },
    );
    await fetchWishlist();
  };
  return { wishlist, removeProduct, isLoading: loading };
}

// export default useWishlist;
