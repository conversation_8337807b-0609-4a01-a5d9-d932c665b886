import React from 'react';
import { CheckoutButton } from '@appmaker-xyz/ui';
import { useCart } from '../../hooks/cart/useCart';

export default function CheckoutButtonContainer({ onAction, attributes }) {
  const { totalQuantity, openCheckout, cartTotalPayable } = useCart({
    onAction,
  });
  return (
    <CheckoutButton
      {...attributes}
      __appmakerCustomStyles={attributes?.__appmakerCustomStyles}
      viewCartText="Checkout"
      onPress={openCheckout}
      itemCount={`${totalQuantity}`}
      totalPrice={cartTotalPayable}
    />
  );
}
