{"version": 3, "names": ["isCallExpression", "isExpressionStatement", "isFunction", "isIdentifier", "isJSXIdentifier", "isNewExpression", "isPlaceholder", "isStatement", "isStringLiteral", "removePropertiesDeep", "traverse", "PATTERN", "parseAndBuildMetadata", "formatter", "code", "opts", "placeholder<PERSON><PERSON><PERSON><PERSON>", "placeholder<PERSON><PERSON><PERSON>", "preserveComments", "syntacticPlaceholders", "ast", "parseWithCodeFrame", "parser", "validate", "syntactic", "placeholders", "placeholder<PERSON><PERSON><PERSON>", "Set", "legacy", "isLegacyRef", "value", "undefined", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "ancestors", "state", "name", "Error", "test", "has", "slice", "parent", "key", "length", "type", "expectedNode", "push", "resolve", "resolveAncestors", "isDuplicate", "add", "i", "index", "parserOpts", "plugins", "allowReturnOutsideFunction", "allowSuperOutsideMethod", "sourceType", "parse", "err", "loc", "message", "codeFrameColumns", "start"], "sources": ["../src/parse.ts"], "sourcesContent": ["import {\n  isCallExpression,\n  isExpressionStatement,\n  isFunction,\n  isIdentifier,\n  isJSXIdentifier,\n  isNewExpression,\n  isPlaceholder,\n  isStatement,\n  isStringLiteral,\n  removePropertiesDeep,\n  traverse,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type { TraversalAncestors, TraversalHandler } from \"@babel/types\";\nimport { parse } from \"@babel/parser\";\nimport { codeFrameColumns } from \"@babel/code-frame\";\nimport type { TemplateOpts, ParserOpts } from \"./options\";\nimport type { Formatter } from \"./formatters\";\n\nexport type Metadata = {\n  ast: t.File;\n  placeholders: Array<Placeholder>;\n  placeholderNames: Set<string>;\n};\n\ntype PlaceholderType = \"string\" | \"param\" | \"statement\" | \"other\";\nexport type Placeholder = {\n  name: string;\n  resolve: (a: t.File) => { parent: t.Node; key: string; index?: number };\n  type: PlaceholderType;\n  isDuplicate: boolean;\n};\n\nconst PATTERN = /^[_$A-Z0-9]+$/;\n\nexport default function parseAndBuildMetadata<T>(\n  formatter: Formatter<T>,\n  code: string,\n  opts: TemplateOpts,\n): Metadata {\n  const {\n    placeholderWhitelist,\n    placeholderPattern,\n    preserveComments,\n    syntacticPlaceholders,\n  } = opts;\n\n  const ast = parseWithCodeFrame(code, opts.parser, syntacticPlaceholders);\n\n  removePropertiesDeep(ast, {\n    preserveComments,\n  });\n\n  formatter.validate(ast);\n\n  const syntactic: MetadataState[\"syntactic\"] = {\n    placeholders: [],\n    placeholderNames: new Set(),\n  };\n  const legacy: MetadataState[\"legacy\"] = {\n    placeholders: [],\n    placeholderNames: new Set(),\n  };\n  const isLegacyRef: MetadataState[\"isLegacyRef\"] = { value: undefined };\n\n  traverse(ast, placeholderVisitorHandler as TraversalHandler<any>, {\n    syntactic,\n    legacy,\n    isLegacyRef,\n    placeholderWhitelist,\n    placeholderPattern,\n    syntacticPlaceholders,\n  });\n\n  return {\n    ast,\n    ...(isLegacyRef.value ? legacy : syntactic),\n  };\n}\n\nfunction placeholderVisitorHandler(\n  node: t.Node,\n  ancestors: TraversalAncestors,\n  state: MetadataState,\n) {\n  let name: string;\n\n  if (isPlaceholder(node)) {\n    if (state.syntacticPlaceholders === false) {\n      throw new Error(\n        \"%%foo%%-style placeholders can't be used when \" +\n          \"'.syntacticPlaceholders' is false.\",\n      );\n    } else {\n      name = node.name.name;\n      state.isLegacyRef.value = false;\n    }\n  } else if (state.isLegacyRef.value === false || state.syntacticPlaceholders) {\n    return;\n  } else if (isIdentifier(node) || isJSXIdentifier(node)) {\n    name = node.name;\n    state.isLegacyRef.value = true;\n  } else if (isStringLiteral(node)) {\n    name = node.value;\n    state.isLegacyRef.value = true;\n  } else {\n    return;\n  }\n\n  if (\n    !state.isLegacyRef.value &&\n    (state.placeholderPattern != null || state.placeholderWhitelist != null)\n  ) {\n    // This check is also in options.js. We need it there to handle the default\n    // .syntacticPlaceholders behavior.\n    throw new Error(\n      \"'.placeholderWhitelist' and '.placeholderPattern' aren't compatible\" +\n        \" with '.syntacticPlaceholders: true'\",\n    );\n  }\n\n  if (\n    state.isLegacyRef.value &&\n    (state.placeholderPattern === false ||\n      !(state.placeholderPattern || PATTERN).test(name)) &&\n    !state.placeholderWhitelist?.has(name)\n  ) {\n    return;\n  }\n\n  // Keep our own copy of the ancestors so we can use it in .resolve().\n  ancestors = ancestors.slice();\n\n  const { node: parent, key } = ancestors[ancestors.length - 1];\n\n  let type: PlaceholderType;\n  if (\n    isStringLiteral(node) ||\n    isPlaceholder(node, { expectedNode: \"StringLiteral\" })\n  ) {\n    type = \"string\";\n  } else if (\n    (isNewExpression(parent) && key === \"arguments\") ||\n    (isCallExpression(parent) && key === \"arguments\") ||\n    (isFunction(parent) && key === \"params\")\n  ) {\n    type = \"param\";\n  } else if (isExpressionStatement(parent) && !isPlaceholder(node)) {\n    type = \"statement\";\n    ancestors = ancestors.slice(0, -1);\n  } else if (isStatement(node) && isPlaceholder(node)) {\n    type = \"statement\";\n  } else {\n    type = \"other\";\n  }\n\n  const { placeholders, placeholderNames } = state.isLegacyRef.value\n    ? state.legacy\n    : state.syntactic;\n\n  placeholders.push({\n    name,\n    type,\n    resolve: ast => resolveAncestors(ast, ancestors),\n    isDuplicate: placeholderNames.has(name),\n  });\n  placeholderNames.add(name);\n}\n\nfunction resolveAncestors(ast: t.File, ancestors: TraversalAncestors) {\n  let parent: t.Node = ast;\n  for (let i = 0; i < ancestors.length - 1; i++) {\n    const { key, index } = ancestors[i];\n\n    if (index === undefined) {\n      parent = (parent as any)[key];\n    } else {\n      parent = (parent as any)[key][index];\n    }\n  }\n\n  const { key, index } = ancestors[ancestors.length - 1];\n\n  return { parent, key, index };\n}\n\ntype MetadataState = {\n  syntactic: {\n    placeholders: Array<Placeholder>;\n    placeholderNames: Set<string>;\n  };\n  legacy: {\n    placeholders: Array<Placeholder>;\n    placeholderNames: Set<string>;\n  };\n  isLegacyRef: {\n    value?: boolean;\n  };\n  placeholderWhitelist?: Set<string>;\n  placeholderPattern?: RegExp | false;\n  syntacticPlaceholders?: boolean;\n};\n\nfunction parseWithCodeFrame(\n  code: string,\n  parserOpts: ParserOpts,\n  syntacticPlaceholders?: boolean,\n): t.File {\n  const plugins = (parserOpts.plugins || []).slice();\n  if (syntacticPlaceholders !== false) {\n    plugins.push(\"placeholders\");\n  }\n\n  parserOpts = {\n    allowReturnOutsideFunction: true,\n    allowSuperOutsideMethod: true,\n    sourceType: \"module\",\n    ...parserOpts,\n    plugins,\n  };\n\n  try {\n    // @ts-expect-error todo: use babel-types ast typings in Babel parser\n    return parse(code, parserOpts);\n  } catch (err) {\n    const loc = err.loc;\n    if (loc) {\n      err.message += \"\\n\" + codeFrameColumns(code, { start: loc });\n      err.code = \"BABEL_TEMPLATE_PARSE_ERROR\";\n    }\n    throw err;\n  }\n}\n"], "mappings": ";;;;;;AAAA;AAeA;AACA;AAAqD;EAfnDA,gBAAgB;EAChBC,qBAAqB;EACrBC,UAAU;EACVC,YAAY;EACZC,eAAe;EACfC,eAAe;EACfC,aAAa;EACbC,WAAW;EACXC,eAAe;EACfC,oBAAoB;EACpBC;AAAQ;AAuBV,MAAMC,OAAO,GAAG,eAAe;AAEhB,SAASC,qBAAqB,CAC3CC,SAAuB,EACvBC,IAAY,EACZC,IAAkB,EACR;EACV,MAAM;IACJC,oBAAoB;IACpBC,kBAAkB;IAClBC,gBAAgB;IAChBC;EACF,CAAC,GAAGJ,IAAI;EAER,MAAMK,GAAG,GAAGC,kBAAkB,CAACP,IAAI,EAAEC,IAAI,CAACO,MAAM,EAAEH,qBAAqB,CAAC;EAExEV,oBAAoB,CAACW,GAAG,EAAE;IACxBF;EACF,CAAC,CAAC;EAEFL,SAAS,CAACU,QAAQ,CAACH,GAAG,CAAC;EAEvB,MAAMI,SAAqC,GAAG;IAC5CC,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE,IAAIC,GAAG;EAC3B,CAAC;EACD,MAAMC,MAA+B,GAAG;IACtCH,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE,IAAIC,GAAG;EAC3B,CAAC;EACD,MAAME,WAAyC,GAAG;IAAEC,KAAK,EAAEC;EAAU,CAAC;EAEtErB,QAAQ,CAACU,GAAG,EAAEY,yBAAyB,EAA2B;IAChER,SAAS;IACTI,MAAM;IACNC,WAAW;IACXb,oBAAoB;IACpBC,kBAAkB;IAClBE;EACF,CAAC,CAAC;EAEF;IACEC;EAAG,GACCS,WAAW,CAACC,KAAK,GAAGF,MAAM,GAAGJ,SAAS;AAE9C;AAEA,SAASQ,yBAAyB,CAChCC,IAAY,EACZC,SAA6B,EAC7BC,KAAoB,EACpB;EAAA;EACA,IAAIC,IAAY;EAEhB,IAAI9B,aAAa,CAAC2B,IAAI,CAAC,EAAE;IACvB,IAAIE,KAAK,CAAChB,qBAAqB,KAAK,KAAK,EAAE;MACzC,MAAM,IAAIkB,KAAK,CACb,gDAAgD,GAC9C,oCAAoC,CACvC;IACH,CAAC,MAAM;MACLD,IAAI,GAAGH,IAAI,CAACG,IAAI,CAACA,IAAI;MACrBD,KAAK,CAACN,WAAW,CAACC,KAAK,GAAG,KAAK;IACjC;EACF,CAAC,MAAM,IAAIK,KAAK,CAACN,WAAW,CAACC,KAAK,KAAK,KAAK,IAAIK,KAAK,CAAChB,qBAAqB,EAAE;IAC3E;EACF,CAAC,MAAM,IAAIhB,YAAY,CAAC8B,IAAI,CAAC,IAAI7B,eAAe,CAAC6B,IAAI,CAAC,EAAE;IACtDG,IAAI,GAAGH,IAAI,CAACG,IAAI;IAChBD,KAAK,CAACN,WAAW,CAACC,KAAK,GAAG,IAAI;EAChC,CAAC,MAAM,IAAItB,eAAe,CAACyB,IAAI,CAAC,EAAE;IAChCG,IAAI,GAAGH,IAAI,CAACH,KAAK;IACjBK,KAAK,CAACN,WAAW,CAACC,KAAK,GAAG,IAAI;EAChC,CAAC,MAAM;IACL;EACF;EAEA,IACE,CAACK,KAAK,CAACN,WAAW,CAACC,KAAK,KACvBK,KAAK,CAAClB,kBAAkB,IAAI,IAAI,IAAIkB,KAAK,CAACnB,oBAAoB,IAAI,IAAI,CAAC,EACxE;IAGA,MAAM,IAAIqB,KAAK,CACb,qEAAqE,GACnE,sCAAsC,CACzC;EACH;EAEA,IACEF,KAAK,CAACN,WAAW,CAACC,KAAK,KACtBK,KAAK,CAAClB,kBAAkB,KAAK,KAAK,IACjC,CAAC,CAACkB,KAAK,CAAClB,kBAAkB,IAAIN,OAAO,EAAE2B,IAAI,CAACF,IAAI,CAAC,CAAC,IACpD,2BAACD,KAAK,CAACnB,oBAAoB,aAA1B,sBAA4BuB,GAAG,CAACH,IAAI,CAAC,GACtC;IACA;EACF;;EAGAF,SAAS,GAAGA,SAAS,CAACM,KAAK,EAAE;EAE7B,MAAM;IAAEP,IAAI,EAAEQ,MAAM;IAAEC;EAAI,CAAC,GAAGR,SAAS,CAACA,SAAS,CAACS,MAAM,GAAG,CAAC,CAAC;EAE7D,IAAIC,IAAqB;EACzB,IACEpC,eAAe,CAACyB,IAAI,CAAC,IACrB3B,aAAa,CAAC2B,IAAI,EAAE;IAAEY,YAAY,EAAE;EAAgB,CAAC,CAAC,EACtD;IACAD,IAAI,GAAG,QAAQ;EACjB,CAAC,MAAM,IACJvC,eAAe,CAACoC,MAAM,CAAC,IAAIC,GAAG,KAAK,WAAW,IAC9C1C,gBAAgB,CAACyC,MAAM,CAAC,IAAIC,GAAG,KAAK,WAAY,IAChDxC,UAAU,CAACuC,MAAM,CAAC,IAAIC,GAAG,KAAK,QAAS,EACxC;IACAE,IAAI,GAAG,OAAO;EAChB,CAAC,MAAM,IAAI3C,qBAAqB,CAACwC,MAAM,CAAC,IAAI,CAACnC,aAAa,CAAC2B,IAAI,CAAC,EAAE;IAChEW,IAAI,GAAG,WAAW;IAClBV,SAAS,GAAGA,SAAS,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpC,CAAC,MAAM,IAAIjC,WAAW,CAAC0B,IAAI,CAAC,IAAI3B,aAAa,CAAC2B,IAAI,CAAC,EAAE;IACnDW,IAAI,GAAG,WAAW;EACpB,CAAC,MAAM;IACLA,IAAI,GAAG,OAAO;EAChB;EAEA,MAAM;IAAEnB,YAAY;IAAEC;EAAiB,CAAC,GAAGS,KAAK,CAACN,WAAW,CAACC,KAAK,GAC9DK,KAAK,CAACP,MAAM,GACZO,KAAK,CAACX,SAAS;EAEnBC,YAAY,CAACqB,IAAI,CAAC;IAChBV,IAAI;IACJQ,IAAI;IACJG,OAAO,EAAE3B,GAAG,IAAI4B,gBAAgB,CAAC5B,GAAG,EAAEc,SAAS,CAAC;IAChDe,WAAW,EAAEvB,gBAAgB,CAACa,GAAG,CAACH,IAAI;EACxC,CAAC,CAAC;EACFV,gBAAgB,CAACwB,GAAG,CAACd,IAAI,CAAC;AAC5B;AAEA,SAASY,gBAAgB,CAAC5B,GAAW,EAAEc,SAA6B,EAAE;EACpE,IAAIO,MAAc,GAAGrB,GAAG;EACxB,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,SAAS,CAACS,MAAM,GAAG,CAAC,EAAEQ,CAAC,EAAE,EAAE;IAC7C,MAAM;MAAET,GAAG;MAAEU;IAAM,CAAC,GAAGlB,SAAS,CAACiB,CAAC,CAAC;IAEnC,IAAIC,KAAK,KAAKrB,SAAS,EAAE;MACvBU,MAAM,GAAIA,MAAM,CAASC,GAAG,CAAC;IAC/B,CAAC,MAAM;MACLD,MAAM,GAAIA,MAAM,CAASC,GAAG,CAAC,CAACU,KAAK,CAAC;IACtC;EACF;EAEA,MAAM;IAAEV,GAAG;IAAEU;EAAM,CAAC,GAAGlB,SAAS,CAACA,SAAS,CAACS,MAAM,GAAG,CAAC,CAAC;EAEtD,OAAO;IAAEF,MAAM;IAAEC,GAAG;IAAEU;EAAM,CAAC;AAC/B;AAmBA,SAAS/B,kBAAkB,CACzBP,IAAY,EACZuC,UAAsB,EACtBlC,qBAA+B,EACvB;EACR,MAAMmC,OAAO,GAAG,CAACD,UAAU,CAACC,OAAO,IAAI,EAAE,EAAEd,KAAK,EAAE;EAClD,IAAIrB,qBAAqB,KAAK,KAAK,EAAE;IACnCmC,OAAO,CAACR,IAAI,CAAC,cAAc,CAAC;EAC9B;EAEAO,UAAU;IACRE,0BAA0B,EAAE,IAAI;IAChCC,uBAAuB,EAAE,IAAI;IAC7BC,UAAU,EAAE;EAAQ,GACjBJ,UAAU;IACbC;EAAO,EACR;EAED,IAAI;IAEF,OAAO,IAAAI,aAAK,EAAC5C,IAAI,EAAEuC,UAAU,CAAC;EAChC,CAAC,CAAC,OAAOM,GAAG,EAAE;IACZ,MAAMC,GAAG,GAAGD,GAAG,CAACC,GAAG;IACnB,IAAIA,GAAG,EAAE;MACPD,GAAG,CAACE,OAAO,IAAI,IAAI,GAAG,IAAAC,2BAAgB,EAAChD,IAAI,EAAE;QAAEiD,KAAK,EAAEH;MAAI,CAAC,CAAC;MAC5DD,GAAG,CAAC7C,IAAI,GAAG,4BAA4B;IACzC;IACA,MAAM6C,GAAG;EACX;AACF"}