import React from 'react';
import {ScrollView, StyleSheet} from 'react-native';
import {color} from '../styles';
import {NotificationWidget, HistoryHeader, Layout} from '../components';

const NotificationsHistory = () => {
  return (
    <Layout blank={false} iconName="bell" message="No new notifications">
      <ScrollView style={styles.container}>
        <HistoryHeader
          count="2"
          onPress={() => console.log('clear-notifications')}
        />
        <NotificationWidget
          onPress={() => {
            console.log('Notification action');
          }}
          attributes={{
            title: 'Awsome Deal is here',
            message:
              'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut',
            imgUri: 'https://loremflickr.com/320/240/banner',
            timeStamp: 'Mar 31 06:44 pm',
          }}
        />
        <NotificationWidget
          onPress={() => {
            console.log('Notification action');
          }}
          attributes={{
            title: 'FLAT 50% OFF',
            message:
              'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut',
            timeStamp: 'Mar 31 06:44 pm',
          }}
        />
        <NotificationWidget
          onPress={() => {
            console.log('Notification action');
          }}
          attributes={{
            title: 'Awsome Deal is here',
            message:
              'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut',
            imgUri: 'https://loremflickr.com/320/240/banner',
            timeStamp: 'Mar 31 06:44 pm',
          }}
        />
        <NotificationWidget
          onPress={() => {
            console.log('Notification action');
          }}
          attributes={{
            title: 'FLAT 50% OFF',
            message:
              'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut',
            timeStamp: 'Mar 31 06:44 pm',
          }}
        />
        <NotificationWidget
          onPress={() => {
            console.log('Notification action');
          }}
          attributes={{
            title: 'Awsome Deal is here',
            message:
              'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut',
            imgUri: 'https://loremflickr.com/320/240/banner',
            timeStamp: 'Mar 31 06:44 pm',
          }}
        />
      </ScrollView>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: color.light,
  },
});

export default NotificationsHistory;
