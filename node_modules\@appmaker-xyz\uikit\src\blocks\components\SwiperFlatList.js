import React from 'react';
import { useRef } from 'react';
import { useEffect } from 'react';
import { useState } from 'react';
import {
  Text,
  Dimensions,
  StyleSheet,
  View,
  Image,
  Button,
} from 'react-native';
import { SwiperFlatList } from '@appmaker-xyz/react-native';

const colors = ['tomato', 'thistle', 'skyblue', 'teal'];
const images = [
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/22.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/22_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/1.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/1_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/23.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/23_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/24.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/24_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/25.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/25_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0098.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0098_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0099.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0099_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_039e9e10-bf6f-4661-a787-ee0c5ac5a49b.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_039e9e10-bf6f-4661-a787-ee0c5ac5a49b_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0100.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0100_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0101.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0101_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/26.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/26_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/8.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/8_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_2bac4236-9283-4a95-84b8-4b675b26e53d.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_2bac4236-9283-4a95-84b8-4b675b26e53d_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/27.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/27_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/28.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/28_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/29.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/29_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/30.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/30_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0172.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0172_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_1ce700c4-3bf2-4823-a2eb-0d0cca1524d2.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_1ce700c4-3bf2-4823-a2eb-0d0cca1524d2_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0174.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0174_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0175.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0175_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0176.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0176_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0177.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0177_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/14.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/14_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_ae95dc53-5ca6-44ab-b2a9-791ed45de09e.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_ae95dc53-5ca6-44ab-b2a9-791ed45de09e_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/15.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/15_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/17.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/17_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/35.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/35_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_53a6ed8e-d22d-4e92-9da2-935253ccb961.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_53a6ed8e-d22d-4e92-9da2-935253ccb961_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/36.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/36_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/37.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/37_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/38.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/38_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/01.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/01_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_d62c9dc1-a459-4fc9-8ac6-c1a79f388fb8.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_d62c9dc1-a459-4fc9-8ac6-c1a79f388fb8_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/02.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/02_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/03.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/03_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/31.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/31_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/14_193f7207-ce33-4c42-9013-22361dd58c02.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/14_193f7207-ce33-4c42-9013-22361dd58c02_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_f55622e7-c0c4-46f0-818d-9f4cf906cd01.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/2021_09_30_TBOF_Amazon_Infographics_CulturedGhee_f55622e7-c0c4-46f0-818d-9f4cf906cd01_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/32.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/32_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/33.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/33_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/34.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/34_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0179.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0179_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0180.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0180_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0182.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0182_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0183.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/TBOF-Ecommerce0183_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/Untitleddesign_13.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/Untitleddesign_13_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/Untitleddesign_14.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/Untitleddesign_14_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/Untitleddesign_15.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/Untitleddesign_15_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/Ghee5000ml_RetouchedImages2_1.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/Ghee5000ml_RetouchedImages2_1_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/Ghee5000ml_RetouchedImages5.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/Ghee5000ml_RetouchedImages5_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/Ghee5000ml_RetouchedImages1.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/Ghee5000ml_RetouchedImages1_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/Ghee5000ml_RetouchedImages4.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/Ghee5000ml_RetouchedImages4_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/Ghee_5000ml_Retouched_Images3.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/Ghee_5000ml_Retouched_Images3_x300.jpg?v=1638804326',
    },
  },
  {
    node: {
      src: 'https://cdn.shopify.com/s/files/1/2598/1404/products/Ghee_5000ml_Retouched_Images6.jpg?v=1638804326',
      transformedSrc:
        'https://cdn.shopify.com/s/files/1/2598/1404/products/Ghee_5000ml_Retouched_Images6_x300.jpg?v=1638804326',
    },
  },
];
const finalImages = images;
// .slice(1, 10);
const getItemLayout = (data, index) => ({
  length: Dimensions.get('window').width,
  offset: Dimensions.get('window').width * index,
  index,
});

const App = () => {
  const [random, setRandom] = useState(0);
  const swipeRef = useRef(null);
  useEffect(() => {
    swipeRef.current.scrollToIndex({ index: random, animated: true });
  }, [random]);
  return (
    <View style={styles.container}>
      <Button
        onPress={() => {
          setRandom(Math.floor(Math.random() * finalImages.length));
        }}
        title="Press me"
      />
      <Text style={styles.text}>
        random:{random} / Total:{finalImages.length}
      </Text>
      {/* <Image
      source={{ uri: images[0].node.transformedSrc }}
      style={styles.image}
    /> */}
      <SwiperFlatList
        ref={swipeRef}
        autoplay={false}
        // autoplayDelay={2}
        // autoplayLoop
        index={2}
        showPagination={false}
        data={finalImages}
        getItemLayout={getItemLayout}
        renderItem={({ item, index }) => (
          <View style={[styles.child]}>
            <Text style={styles.text}>{index}</Text>
            <Image source={{ uri: item.node.url }} style={styles.image} />
          </View>
        )}
      />
    </View>
  );
};

const { width } = Dimensions.get('window');
const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: 'white' },
  child: { width, justifyContent: 'center' },
  text: { fontSize: width * 0.1, textAlign: 'center' },
  image: {
    flex: 1,
    width: 400,
    height: 400,
  },
});

export default App;
