import React from 'react';
import { Text } from 'react-native';
import { styles } from '../../../styles';

export default function ThemeText({
  children,
  fontFamily = 'regular',
  size = 'base',
  style,
  color,
  ...props
}) {
  const newStyles = {
    fontFamily: styles.fontFamily[fontFamily],
    fontSize: styles.size[size],
    color: color,
    ...style,
  };
  return (
    <Text style={newStyles} {...props}>
      {children}
    </Text>
  );
}
