import { requestOtp, updateCustomer, verifyOtp } from './api/index';
import { appmaker } from '@appmaker-xyz/core/';

export async function LUCENT_REQUEST_MOBILE_OTP(
  { params, fromResend = false },
  deps,
) {
  const { coreDispatch, handleAction } = deps;

  let { formatedPhone, username } = params;
  let { _formData, blockData } = deps?.pageState;
  const formated = _formData?.phone?.formated;
  const { phone } = blockData;
  if ((!formated || formated === '') && !phone) {
    handleAction({
      action: 'SHOW_MESSAGE',
      params: {
        title: 'Please enter your phone number to continue',
      },
    });
    return;
  }
  const data = await requestOtp({
    username: formatedPhone || phone || formated,
  });
  if (data?.error === 'No customer found.') {
    handleAction({
      action: 'OPEN_REGISTER',
    });
    throw new Error(data?.error);
  }
  if (data.status === 200) {
    if (fromResend) {
      handleAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'OTP Sent Successfully',
        },
      });
    }
    coreDispatch({
      type: 'SET_OBJECT',
      name: 'otpId',
      value: data.data.otpId,
    });
    // addFilter('lucent-phone-otp-verify', 'custom-otp', () => 'EnterPhoneOtp');
    !fromResend &&
      (await deps.handleAction(
        {
          action: 'OPEN_INAPP_PAGE',
          pageId: appmaker.applyFilters(
            'lucent-phone-otp-verify',
            'LucentEmailOtpVerify',
          ),
          params: {
            pageData: {
              otpId: data.data.otpId,
              phone: phone || formated,

              // email: email,
            },
          },
        },
        deps,
      ));
  } else {
    handleAction({
      action: 'SHOW_MESSAGE',
      params: {
        title: 'Something went wrong, Please try again',
      },
    });
  }
}

export async function LUCENT_VERIFY_MOBILE_OTP({ params }, deps, ...props) {
  const { pageState, handleAction } = deps;
  let { formatedPhone } = params;
  const { blockData, _formData } = pageState;
  const { phoneotp } = _formData;
  const { otpId, phone } = blockData;
  const type = 'mobile';
  if (phoneotp === '' || !phoneotp) {
    handleAction({
      action: 'SHOW_MESSAGE',
      params: {
        title: 'Please enter OTP',
      },
    });
    return;
  }
  // const otpId = pageState.otpId;
  const responseData = await verifyOtp({
    username: formatedPhone || phone,
    otp: phoneotp,
    otp_id: otpId,
    type,
  });
  if (responseData.status === 200) {
    await processLogin({
      responseData,
      deps,
      type,
    });
  } else {
    // throw new Error(responseData?.message);
    handleAction({
      action: 'SHOW_MESSAGE',
      params: {
        title: 'Incorrect OTP please try again',
      },
    });
  }
}

export async function LUCENT_REQUEST_EMAIL_OTP(
  { params, fromResend = false },
  deps,
) {
  const { pageState, handleAction, coreDispatch } = deps;
  const { _formData, blockData } = pageState;
  const { email } = _formData;
  const { email: resendEmail } = blockData;
  if ((email === '' || !email) && !resendEmail) {
    handleAction({
      action: 'SHOW_MESSAGE',
      params: {
        title: 'Please enter your email to continue',
      },
    });
    return;
  }
  const emailVerify = verifyEmail(email || resendEmail);
  // return;
  if (!emailVerify) {
    handleAction({
      action: 'SHOW_MESSAGE',
      params: {
        title: 'Please enter a valid email',
      },
    });
    return;
  }
  // }
  const data = await requestOtp({
    username: email || resendEmail,
    action: email ? 'sendOTP' : 'resendOTP',
    type: 'email',
  });
  if (data.status === 200) {
    if (fromResend) {
      handleAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'OTP Sent Successfully',
        },
      });
    }
    coreDispatch({
      type: 'SET_OBJECT',
      name: 'otpId',
      value: data.data.otpId,
    });
    // const { handleAction } = deps;

    // if (data?.error === 'No customer found.') {
    email &&
      (await deps.handleAction(
        {
          action: 'OPEN_INAPP_PAGE',
          pageId: appmaker.applyFilters(
            'lucent-email-otp-verify',
            'LucentEmailOtpVerify',
          ),
          params: {
            pageData: {
              otpId: data.data.otpId,
              email: email,
            },
          },
        },
        deps,
      ));
  } else {
    handleAction({
      action: 'SHOW_MESSAGE',
      params: {
        title: 'Something went wrong, Please try again',
      },
    });
  }
  // throw new Error(data?.error);
  // }
}

export async function LUCENT_VERIFY_EMAIL_OTP({ params }, deps) {
  const { pageState, handleAction, coreDispatch } = deps;
  const { _formData, blockData, otpRetry = 0, otpId: otpIdResend } = pageState;
  const { otpId, email: username } = blockData;
  const { otp } = _formData;
  const type = 'email';
  // const otpId = pageState.blockData.otpId;
  if (otp === '' || !otp) {
    handleAction({
      action: 'SHOW_MESSAGE',
      params: {
        title: 'Please enter OTP',
      },
    });
    return;
  }
  const responseData = await verifyOtp({
    username,
    otp: otp,
    type,
    otp_id: otpIdResend ? otpIdResend : otpId,
  });
  if (responseData.status === 200) {
    await processLogin({
      responseData,
      deps,
      type,
    });
    coreDispatch({
      type: 'SET_VALUE',
      name: 'loadingButton',
      value: false,
    });
  } else {
    coreDispatch({
      type: 'SET_OBJECT',
      name: 'otpRetry',
      value: otpRetry < 2 ? otpRetry + 1 : 0,
    });
    if (otpRetry === 2) {
      await deps.handleAction({
        action: 'GO_BACK',
        // pageId: 'LucentEmailOtpRequest',
      });
      handleAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'Incorrect OTP please try again',
        },
      });
    }
    coreDispatch({
      type: 'SET_VALUE',
      name: 'loadingButton',
      value: false,
    });
    coreDispatch({
      type: 'SET_VALUE',
      name: 'otp',
      value: '',
      parent: '_formData',
    });
    // throw new Error();
    handleAction({
      action: 'SHOW_MESSAGE',
      params: {
        // title: responseData?.message,
        title: 'Incorrect OTP please try again',
      },
    });
  }
  // const multipassToken = data.token;
  // await handleAction(
  //   {
  //     action: 'LOGIN_USER_MULTIPASS',
  //     params: {
  //       multipassToken,
  //     },
  //   },
  //   deps,
  // );
}
function verifyEmail(email) {
  const regexp =
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return regexp.test(email);
}
function getStatus({ responseData, type }) {
  const { data } = responseData;
  if (data.multipass_url) {
    return 'login-via-multipass';
  } else {
    return 'create-account-with-default-email';
  }
  // if (type === 'email') {
  // } else {
  // }
}
async function processLogin({ responseData, deps, type }) {
  const { first_name, last_name, phone, email } = responseData.data;
  const multipass_url = responseData.data.multipass_url;
  let multipassToken;
  if (multipass_url) {
    multipassToken = multipass_url.match(/multipass\/(.*)/)[1];
  }
  const { handleAction, pageState } = deps;
  const status = getStatus({ responseData, type });
  switch (status) {
    case 'create-account-with-default-email':
      return openCreateAccountPage(
        {
          pageData: {
            otpId: pageState?.blockData?.otpId,
            email,
            email_disabled: type === 'email',
            phone_disabled: type === 'mobile',
            phone,
            first_name,
            last_name,
            type: 'create-account-with-default-email',
          },
        },
        deps,
      );
    case 'login-via-multipass':
      return await handleAction(
        {
          action: 'LOGIN_USER_MULTIPASS',
          params: {
            multipassToken: multipassToken,
          },
        },
        deps,
      );
    case 'phone-exists-in-addresses-email-in-profile':
      return await handleAction(
        {
          action: 'LOGIN_USER_MULTIPASS',
          params: {
            multipassToken: multipassToken,
          },
        },
        deps,
      );
    case 'phone-exists-in-multiple-addresses-not-in-profile':
      console.log('phone-exists-in-multiple-addresses-not-in-profile');
      break;
    // return await openChooseEmailPage({ response, phone }, deps);
    case 'phone-exist-in-profile-email-not-exist':
      return openCreateAccountPage(
        {
          pageData: {
            phone: {
              phone,
              formated: phone,
            },
            first_name,
            last_name,
            phone_disabled: true,
            type: 'link-account',
          },
        },
        deps,
      );

    default:
  }
}

async function openCreateAccountPage(params, deps) {
  const { pageData } = params;
  await deps.handleAction(
    {
      action: 'OPEN_INAPP_PAGE',
      pageId: 'AccountDetails',
      params: {
        pageData,
      },
    },
    deps,
  );
}
export async function LUCENT_UPDATE_USER({ params }, deps) {
  const { pageState, handleAction } = deps;
  const { _formData, blockData } = pageState;
  const { formData } = params;
  const type = 'email';
  const {
    first_name,
    last_name,
    phone,
    email,
    otp_id,
    market_email,
    market_phone,
    market_whatsapp,
  } = _formData || formData;
  const { otpId } = blockData;
  if (first_name && last_name && phone && email) {
    const responseData = await updateCustomer({
      first_name,
      last_name,
      phone_no: phone?.formated || phone,
      email,
      otp_id: otpId || otp_id,
      accept_email_marketing: market_email || !!market_email,
      accept_sms_marketing: market_phone || !!market_phone,
      accept_whatsapp_marketing: market_whatsapp || !!market_whatsapp,
    });
    if (responseData.status === 200) {
      await processLogin({
        responseData,
        deps,
        type,
      });
    } else {
      handleAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: responseData?.message,
        },
      });
    }
  } else {
    handleAction({
      action: 'SHOW_MESSAGE',
      params: {
        title: 'All fields are mandatory',
      },
    });
  }
}
export async function LUCENT_DEBUG_SIGNUP({ params }, deps) {
  await deps.handleAction(
    {
      action: 'OPEN_INAPP_PAGE',
      pageId: 'LucentCustomSignUp',
      params: {
        pageData: {
          email: '<EMAIL>',
          email_disabled: true,
          first_name: 'Saleeh',
          last_name: 'K',
          otp_id: '11f0dbc5-f95f-407b-a0d5-8518bc4ad4d8',
          type: 'create-account-with-default-email',
        },
      },
    },
    deps,
  );
}
