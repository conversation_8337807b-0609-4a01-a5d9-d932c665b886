import axios from 'axios';
let _baseurl = '';
export function initApp({ BASE_URL }) {
  _baseurl = BASE_URL;
}

function sendEvent({ url, data }) {
  return axios.post(url, data);
}

export function track(event, params, context, rest) {
  sendEvent({
    url: `${_baseurl}/track/${event}`,
    data: {
      event,
      params,
      context,
      rest,
    },
  });
}

export function page(pageId, name, context, rest) {
  sendEvent({
    url: `${_baseurl}/page/${pageId}`,
    data: {
      name,
      context,
      rest,
    },
  });
}

export function interceptEvent(platform, params) {
  const [event] = params;
  sendEvent({
    url: `${_baseurl}/track/${platform}/${event}`,
    data: {
      params,
    },
  });
}
