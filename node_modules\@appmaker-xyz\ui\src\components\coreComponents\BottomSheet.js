import React from 'react';
import { StyleSheet, Dimensions } from 'react-native';
import { Layout, ThemeText } from '../atoms';
import Icon from 'react-native-vector-icons/Feather';
import AppModal from './AppModal';

const windowWidth = Dimensions.get('window').width;

const BottomSheet = ({ title, children, visible, setVisible }) => {
  const toggleModal = () => {
    setVisible(!visible);
  };
  return (
    <AppModal
      isVisible={visible}
      onBackdropPress={() => setVisible(false)}
      propagateSwipe={true}
      style={styles.modal}>
      <Layout style={styles.modalContainer}>
        <Layout style={styles.modalHeader}>
          <ThemeText size="xl" fontFamily="medium">
            {title}
          </ThemeText>
          <Icon
            name="x"
            size={28}
            color="#1E232C"
            style={styles.closeIcon}
            onPress={toggleModal}
          />
        </Layout>
        {children}
      </Layout>
    </AppModal>
  );
};
const styles = StyleSheet.create({
  container: {},
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 18,
    borderTopRightRadius: 18,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  closeIcon: {
    padding: 6,
  },
  addButton: {
    flexDirection: 'row',
    backgroundColor: '#1E232C',
    marginHorizontal: 12,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 6,
  },
  plusIcon: {
    marginRight: 6,
  },
  separator: {
    height: 1,
    backgroundColor: '#ccc',
    margin: 16,
  },
  scrollViewContentContainer: {
    paddingLeft: 16,
    marginBottom: 16,
  },
  addressContainer: {
    borderWidth: 1,
    borderRadius: 12,
    overflow: 'hidden',
    padding: 12,
    backgroundColor: '#F1F5FA',
    marginRight: 12,
    width: windowWidth - 130,
  },
  addressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  removeAddressButton: {
    paddingVertical: 1,
  },
  addressTextContainer: {
    flexGrow: 1,
    marginTop: 6,
  },
  cardActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 12,
  },
  editButton: {
    borderWidth: 1,
    borderColor: '#000',
    padding: 8,
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
    overflow: 'hidden',
  },
  deliverButton: {
    borderWidth: 1,
    borderColor: '#1E232C',
    backgroundColor: '#1E232C',
    padding: 8,
    flex: 3,
    marginLeft: 6,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
    overflow: 'hidden',
  },
  radioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioIcon: {
    height: 15,
    width: 15,
    borderRadius: 100,
    marginRight: 6,
  },
  radioIconInactive: {
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  radioIconActive: {
    backgroundColor: '#fff',
    borderWidth: 4,
  },
});

export default BottomSheet;
