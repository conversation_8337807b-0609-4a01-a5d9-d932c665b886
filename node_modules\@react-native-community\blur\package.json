{"name": "@react-native-community/blur", "version": "4.4.1", "description": "React Native Blur component", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/index.d.ts", "react-native": "src/index", "source": "src/index", "files": ["src", "lib", "android", "ios", "cpp", "react-native-blur.podspec", "!lib/typescript/example", "!android/build", "!ios/build", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__"], "scripts": {"test": "jest", "typescript": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\"", "prepare": "bob build", "release": "release-it", "example": "yarn --cwd example", "bootstrap": "yarn example && yarn && yarn example pods"}, "keywords": ["react-native", "ios", "android"], "repository": "https://github.com/react-native-community/react-native-blur", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/Kureev)", "license": "MIT", "bugs": {"url": "https://github.com/react-native-community/react-native-blur/issues"}, "homepage": "https://github.com/react-native-community/react-native-blur", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@arkweid/lefthook": "^0.7.7", "@babel/eslint-parser": "^7.18.2", "@commitlint/config-conventional": "^17.0.2", "@react-native-community/eslint-config": "^3.0.2", "@release-it/conventional-changelog": "^5.0.0", "@types/jest": "^29.5.1", "@types/react": "~18.0.20", "@types/react-native": "^0.70.1", "commitlint": "^17.0.2", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.5.0", "pod-install": "^0.1.0", "prettier": "^2.0.5", "react": "18.1.0", "react-native": "0.70.1", "react-native-builder-bob": "^0.18.3", "react-native-test-app": "^1.6.16", "release-it": "^15.0.0", "typescript": "^4.5.2"}, "peerDependencies": {"react": "*", "react-native": "*"}, "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/example/node_modules", "<rootDir>/lib/"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular"}}}, "eslintConfig": {"root": true, "parser": "@babel/eslint-parser", "extends": ["@react-native-community", "prettier"], "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "eslintIgnore": ["node_modules/", "lib/"], "prettier": {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}, "codegenConfig": {"name": "rnblurview", "type": "components", "jsSrcsDir": "./src/fabric", "android": {"javaPackageName": "com.reactnativecommunityblur"}}}