import { createStyleSheet } from '@appmaker-xyz/react-native';

export const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.background,
    flexDirection: 'row',
    borderBottomColor: theme.colors.separator,
    borderBottomWidth: 1,
  },
  imageContainer: {
    flexDirection: 'row',
    borderRadius: theme.borderRadii.lg,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 6,
  },
  rowBetweenContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginLeft: 6,
  },
  deleteIcon: {
    backgroundColor: theme.colors.lightBackground,
    borderRadius: 16,
    height: 28,
    width: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textShrink: {
    flexShrink: 1,
    color: theme.colors.text,
  },
  variantText: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    backgroundColor: theme.colors.lightBackground,
    borderRadius: 4,
    flexShrink: 1,
    marginRight: 4,
  },
  colorChip: {
    width: 12,
    height: 12,
    marginRight: 4,
    borderRadius: 40,
    overflow: 'hidden',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  regularPrice: {
    marginRight: 4,
    textDecorationLine: 'line-through',
  },
  stepperContainer: {
    borderRadius: 2,
    borderColor: theme.colors.separator,
  },
  bundleProduct: {
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 1,
  },
  bundleItemImage: {
    width: 25,
    height: 25,
    marginRight: 6,
  },
  bundleContent: {
    marginRight: 40,
  },
  productListContainer: {
    flexShrink: 1,
    marginBottom: 8,
  },
  showButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 6,
  },
  middleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginLeft: 6,
  },
  middleContainerStart: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginLeft: 6,
  },
}));
