import { constants, integrations } from '@appmaker-xyz/app-config/newConfig';
import base64 from 'base-64';
import { logUserLogin } from '../../analytics';
import { appSettings } from '@appmaker-xyz/core';

const encodedLoginPath = base64.encode('login');
const onUrlChange = (url, onAction) => {
  const regex = /order-received\/([0-9]*)/;
  const orderRegex = /order-received\/([0-9]*)/;
  const thankyouPage = /(thank-you|(Thank[\+]you)|page_id=11168)/;
  const orderIdRegex = /order_id\=([0-9]*)/;
  const myAccountRegex = /my-account/;
  const payForOrder = /(pay_for_order=true|order-pay)/;
  const orderInWebview = /[?&]app_order_in_webview=/.test(url);
  const responseRegex = /response\=(.*)&/;

  if (responseRegex.test(url)) {
    const responseBase64 = url.match(responseRegex)[1];
    const response = base64.decode(responseBase64);
    try {
      const jsonResponse = JSON.parse(response);
      onAction({
        action: 'REDUX_DISPATCH',
        params: { type: 'LOGGED_IN', data: { user: jsonResponse } },
      });
      onAction({
        action: 'SUBSCRIBE_TO_PUSH_TOPIC',
        params: { topic: `user-${jsonResponse.user.id}` },
      });
      onAction({
        action: 'SYNC_CART',
      });
      logUserLogin(jsonResponse);
      if (appSettings.getOptionAsBoolean('force_login')) {
        // to go to home when login
        onAction({
          action: 'GO_BACK',
        }); // back button was showing else
        onAction({
          action: 'OPEN_HOME',
          params: { replacePage: true },
        });
      } else {
        onAction({
          action: 'GO_BACK',
        });
        onAction({
          action: 'OPEN_HOME',
          params: { replacePage: true },
        });
      }
      // this.authService.setToken(
      //   `${jsonResponse.user_id}:${jsonResponse.access_token}`,
      // );
      // this.props.dispatch(login(jsonResponse));
      // this.props.navigation.dispatch(resetAction);
    } catch {}
  }
  if (regex.test(url)) {
    const id = url.match(regex)[1];
    return onAction({
      action: 'OPEN_THANKYOU_PAGE',
      params: { replacePage: true },
      inlineData: {
        source: 'inline',
        attributes: { id },
      },
    });
  }
};

// const getURL()

const page = {
  id: 'login',
  status: 'active',
  title: 'My Account',
  attributes: {
    renderType: 'normal',
    contentContainerStyle: { flex: 1 },
    rootContainerStyle: { flex: 1 },
  },
  blocks: [
    {
      name: 'appmaker/webview',
      isValid: true,
      clientId: 'f496b61a-56c9-4862-b4a5-d5438bb530aa',
      attributes: {
        // navigationFilter: checkOrder,
        urlListener: onUrlChange,
        url: '',
        source: {
          uri: `${appSettings.getOption('register_in_webview_url')}`,
        },
      },
    },
  ],
};
export default page;
