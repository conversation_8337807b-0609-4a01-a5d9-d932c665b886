import { appmaker } from '@appmaker-xyz/core';

export function useAccountDetailUpdate({ onAction, blockData, ...props } = {}) {
  let email = blockData?.email || '';
  let firstName = blockData?.first_name || '';
  const lastName = blockData?.last_name || '';
  const phone = blockData?.phone || '';
  const isPhoneDisabled = blockData?.phone_disabled || false;
  const isEmailDisabled = blockData?.email_disabled || false;
  const redirectAction = blockData?.redirectAction;
  const updateAccountDetails = async (data) => {
    const response = await appmaker.applyFilters(
      'appmaker-submit-account-details',
      {
        onAction,
        redirectAction,
        ...data,
        extraData: {
          ...blockData,
        },
      },
    );
    if (response?.error) {
    } else {
    }
  };
  return {
    updateAccountDetails,
    email,
    firstName,
    lastName,
    phone,
    isEmailDisabled,
    isPhoneDisabled,
  };
}
