{"version": 3, "names": ["_async", "require", "once", "fn", "result", "resultP", "isAsync", "waitFor", "resolve", "reject", "Promise", "res", "rej", "error"], "sources": ["../../src/gensync-utils/functional.ts"], "sourcesContent": ["import type { <PERSON><PERSON> } from \"gensync\";\n\nimport { isAsync, waitFor } from \"./async\";\n\nexport function once<R>(fn: () => Handler<R>): () => Handler<R> {\n  let result: R;\n  let resultP: Promise<R>;\n  return function* () {\n    if (result) return result;\n    if (!(yield* isAsync())) return (result = yield* fn());\n    if (resultP) return yield* waitFor(resultP);\n\n    let resolve: (result: R) => void, reject: (error: unknown) => void;\n    resultP = new Promise((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n\n    try {\n      result = yield* fn();\n      // Avoid keeping the promise around\n      // now that we have the result.\n      resultP = null;\n      resolve(result);\n      return result;\n    } catch (error) {\n      reject(error);\n      throw error;\n    }\n  };\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,MAAA,GAAAC,OAAA;AAEO,SAASC,IAAIA,CAAIC,EAAoB,EAAoB;EAC9D,IAAIC,MAAS;EACb,IAAIC,OAAmB;EACvB,OAAO,aAAa;IAClB,IAAID,MAAM,EAAE,OAAOA,MAAM;IACzB,IAAI,EAAE,OAAO,IAAAE,cAAO,GAAE,CAAC,EAAE,OAAQF,MAAM,GAAG,OAAOD,EAAE,EAAE;IACrD,IAAIE,OAAO,EAAE,OAAO,OAAO,IAAAE,cAAO,EAACF,OAAO,CAAC;IAE3C,IAAIG,OAA4B,EAAEC,MAAgC;IAClEJ,OAAO,GAAG,IAAIK,OAAO,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MAClCJ,OAAO,GAAGG,GAAG;MACbF,MAAM,GAAGG,GAAG;IACd,CAAC,CAAC;IAEF,IAAI;MACFR,MAAM,GAAG,OAAOD,EAAE,EAAE;MAGpBE,OAAO,GAAG,IAAI;MACdG,OAAO,CAACJ,MAAM,CAAC;MACf,OAAOA,MAAM;IACf,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdJ,MAAM,CAACI,KAAK,CAAC;MACb,MAAMA,KAAK;IACb;EACF,CAAC;AACH;AAAC"}