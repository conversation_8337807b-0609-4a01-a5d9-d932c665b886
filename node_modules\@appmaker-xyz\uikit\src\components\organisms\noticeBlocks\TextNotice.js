import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout, AppmakerText, Button } from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';

const TextNotice = ({ attributes, onPress, onAction }) => {
  const { message, status = 'dark', appmakerAction } = attributes;
  const { color, spacing, font, dimensions } = useApThemeState();
  let wordCount = message?.length || 0;
  const styles = allStyles({ color, spacing, dimensions, status, wordCount });
  function textColor() {
    switch (status) {
      case 'success':
        return 'white';
      case 'grey':
        return 'dark';
      case 'warning':
        return 'dark';
      default:
        return 'white';
    }
  }
  if (!message) {
    return null;
  }
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  return (
    <Layout style={styles.container}>
      <AppmakerText status={textColor()} style={styles.text} numberOfLines={2}>
        {message}
      </AppmakerText>
    </Layout>
  );
};

const allStyles = ({ color, spacing, dimensions, status, wordCount }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color[status],
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      width: dimensions.fullWidth,
      paddingVertical: spacing.nano,
      paddingHorizontal: spacing.base,
    },
    text: {
      textAlign: 'center',
      fontSize: wordCount > 30 ? 14 : 16,
    },
  });

export default TextNotice;
