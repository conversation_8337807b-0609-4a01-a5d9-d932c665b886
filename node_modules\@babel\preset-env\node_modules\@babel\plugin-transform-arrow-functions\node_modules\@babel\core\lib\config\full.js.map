{"version": 3, "names": ["_gensync", "data", "require", "_async", "_util", "context", "_plugin", "_item", "_config<PERSON><PERSON>n", "_deepArray", "_traverse", "_caching", "_options", "_plugins", "_configApi", "_partial", "_configError", "_default", "gens<PERSON>", "loadFullConfig", "inputOpts", "_opts$assumptions", "result", "loadPrivatePartialConfig", "options", "fileHandling", "optionDefaults", "plugins", "presets", "Error", "presetContext", "Object", "assign", "targets", "toDescriptor", "item", "desc", "getItemDescriptor", "presetsDescriptors", "map", "initialPluginsDescriptors", "pluginDescriptorsByPass", "passes", "externalDependencies", "ignored", "enhanceError", "recursePresetDescriptors", "rawPresets", "pluginDescriptorsPass", "i", "length", "descriptor", "preset", "loadPresetDescriptor", "e", "code", "checkNoUnwrappedItemOptionPairs", "push", "ownPass", "chain", "pass", "unshift", "splice", "o", "filter", "p", "for<PERSON>ach", "opts", "mergeOptions", "pluginContext", "assumptions", "loadPluginDescriptors", "descs", "plugin", "loadPluginDescriptor", "slice", "passPerPreset", "freezeDeepArray", "exports", "default", "fn", "arg1", "arg2", "test", "message", "_context$filename", "filename", "makeDescriptorLoader", "apiFactory", "makeWeakCache", "value", "dirname", "alias", "cache", "factory", "maybe<PERSON><PERSON>", "api", "JSON", "stringify", "isThenable", "configured", "mode", "error", "pluginDescriptorLoader", "makePluginAPI", "presetDes<PERSON>or<PERSON><PERSON><PERSON>", "makePresetAPI", "instantiatePlugin", "pluginObj", "validatePluginObject", "visitor", "traverse", "explode", "inherits", "inheritsDescriptor", "name", "undefined", "forwardAsync", "run", "invalidate", "pre", "post", "manipulateOptions", "visitors", "merge", "Plugin", "needsFilename", "val", "validateIfOptionNeedsFilename", "include", "exclude", "formattedPresetName", "ConfigError", "join", "validatePreset", "overrides", "overrideOptions", "instantiatePreset", "makeWeakCacheSync", "validate", "buildPresetChain", "a", "b", "fns", "Boolean", "args", "apply"], "sources": ["../../src/config/full.ts"], "sourcesContent": ["import gensync, { type Handler } from \"gensync\";\nimport { forwardAsync, maybeAsync, isThenable } from \"../gensync-utils/async\";\n\nimport { mergeOptions } from \"./util\";\nimport * as context from \"../index\";\nimport Plugin from \"./plugin\";\nimport { getItemDescriptor } from \"./item\";\nimport { buildPresetChain } from \"./config-chain\";\nimport { finalize as freezeDeepArray } from \"./helpers/deep-array\";\nimport type { DeepArray, ReadonlyDeepArray } from \"./helpers/deep-array\";\nimport type {\n  ConfigContext,\n  ConfigChain,\n  PresetInstance,\n} from \"./config-chain\";\nimport type { UnloadedDescriptor } from \"./config-descriptors\";\nimport traverse from \"@babel/traverse\";\nimport { makeWeakCache, makeWeakCacheSync } from \"./caching\";\nimport type { CacheConfigurator } from \"./caching\";\nimport {\n  validate,\n  checkNoUnwrappedItemOptionPairs,\n} from \"./validation/options\";\nimport type { PluginItem } from \"./validation/options\";\nimport { validatePluginObject } from \"./validation/plugins\";\nimport { makePluginAPI, makePresetAPI } from \"./helpers/config-api\";\nimport type { PluginAPI, PresetAPI } from \"./helpers/config-api\";\n\nimport loadPrivatePartialConfig from \"./partial\";\nimport type { ValidatedOptions } from \"./validation/options\";\n\nimport type * as Context from \"./cache-contexts\";\nimport ConfigError from \"../errors/config-error\";\n\ntype LoadedDescriptor = {\n  value: {};\n  options: {};\n  dirname: string;\n  alias: string;\n  externalDependencies: ReadonlyDeepArray<string>;\n};\n\nexport type { InputOptions } from \"./validation/options\";\n\nexport type ResolvedConfig = {\n  options: any;\n  passes: PluginPasses;\n  externalDependencies: ReadonlyDeepArray<string>;\n};\n\nexport type { Plugin };\nexport type PluginPassList = Array<Plugin>;\nexport type PluginPasses = Array<PluginPassList>;\n\nexport default gensync(function* loadFullConfig(\n  inputOpts: unknown,\n): Handler<ResolvedConfig | null> {\n  const result = yield* loadPrivatePartialConfig(inputOpts);\n  if (!result) {\n    return null;\n  }\n  const { options, context, fileHandling } = result;\n\n  if (fileHandling === \"ignored\") {\n    return null;\n  }\n\n  const optionDefaults = {};\n\n  const { plugins, presets } = options;\n\n  if (!plugins || !presets) {\n    throw new Error(\"Assertion failure - plugins and presets exist\");\n  }\n\n  const presetContext: Context.FullPreset = {\n    ...context,\n    targets: options.targets,\n  };\n\n  const toDescriptor = (item: PluginItem) => {\n    const desc = getItemDescriptor(item);\n    if (!desc) {\n      throw new Error(\"Assertion failure - must be config item\");\n    }\n\n    return desc;\n  };\n\n  const presetsDescriptors = presets.map(toDescriptor);\n  const initialPluginsDescriptors = plugins.map(toDescriptor);\n  const pluginDescriptorsByPass: Array<Array<UnloadedDescriptor>> = [[]];\n  const passes: Array<Array<Plugin>> = [];\n\n  const externalDependencies: DeepArray<string> = [];\n\n  const ignored = yield* enhanceError(\n    context,\n    function* recursePresetDescriptors(\n      rawPresets: Array<UnloadedDescriptor>,\n      pluginDescriptorsPass: Array<UnloadedDescriptor>,\n    ): Handler<true | void> {\n      const presets: Array<{\n        preset: ConfigChain | null;\n        pass: Array<UnloadedDescriptor>;\n      }> = [];\n\n      for (let i = 0; i < rawPresets.length; i++) {\n        const descriptor = rawPresets[i];\n        if (descriptor.options !== false) {\n          try {\n            // eslint-disable-next-line no-var\n            var preset = yield* loadPresetDescriptor(descriptor, presetContext);\n          } catch (e) {\n            if (e.code === \"BABEL_UNKNOWN_OPTION\") {\n              checkNoUnwrappedItemOptionPairs(rawPresets, i, \"preset\", e);\n            }\n            throw e;\n          }\n\n          externalDependencies.push(preset.externalDependencies);\n\n          // Presets normally run in reverse order, but if they\n          // have their own pass they run after the presets\n          // in the previous pass.\n          if (descriptor.ownPass) {\n            presets.push({ preset: preset.chain, pass: [] });\n          } else {\n            presets.unshift({\n              preset: preset.chain,\n              pass: pluginDescriptorsPass,\n            });\n          }\n        }\n      }\n\n      // resolve presets\n      if (presets.length > 0) {\n        // The passes are created in the same order as the preset list, but are inserted before any\n        // existing additional passes.\n        pluginDescriptorsByPass.splice(\n          1,\n          0,\n          ...presets.map(o => o.pass).filter(p => p !== pluginDescriptorsPass),\n        );\n\n        for (const { preset, pass } of presets) {\n          if (!preset) return true;\n\n          pass.push(...preset.plugins);\n\n          const ignored = yield* recursePresetDescriptors(preset.presets, pass);\n          if (ignored) return true;\n\n          preset.options.forEach(opts => {\n            mergeOptions(optionDefaults, opts);\n          });\n        }\n      }\n    },\n  )(presetsDescriptors, pluginDescriptorsByPass[0]);\n\n  if (ignored) return null;\n\n  const opts: any = optionDefaults;\n  mergeOptions(opts, options);\n\n  const pluginContext: Context.FullPlugin = {\n    ...presetContext,\n    assumptions: opts.assumptions ?? {},\n  };\n\n  yield* enhanceError(context, function* loadPluginDescriptors() {\n    pluginDescriptorsByPass[0].unshift(...initialPluginsDescriptors);\n\n    for (const descs of pluginDescriptorsByPass) {\n      const pass: Plugin[] = [];\n      passes.push(pass);\n\n      for (let i = 0; i < descs.length; i++) {\n        const descriptor: UnloadedDescriptor = descs[i];\n        if (descriptor.options !== false) {\n          try {\n            // eslint-disable-next-line no-var\n            var plugin = yield* loadPluginDescriptor(descriptor, pluginContext);\n          } catch (e) {\n            if (e.code === \"BABEL_UNKNOWN_PLUGIN_PROPERTY\") {\n              // print special message for `plugins: [\"@babel/foo\", { foo: \"option\" }]`\n              checkNoUnwrappedItemOptionPairs(descs, i, \"plugin\", e);\n            }\n            throw e;\n          }\n          pass.push(plugin);\n\n          externalDependencies.push(plugin.externalDependencies);\n        }\n      }\n    }\n  })();\n\n  opts.plugins = passes[0];\n  opts.presets = passes\n    .slice(1)\n    .filter(plugins => plugins.length > 0)\n    .map(plugins => ({ plugins }));\n  opts.passPerPreset = opts.presets.length > 0;\n\n  return {\n    options: opts,\n    passes: passes,\n    externalDependencies: freezeDeepArray(externalDependencies),\n  };\n});\n\nfunction enhanceError<T extends Function>(context: ConfigContext, fn: T): T {\n  return function* (arg1: unknown, arg2: unknown) {\n    try {\n      return yield* fn(arg1, arg2);\n    } catch (e) {\n      // There are a few case where thrown errors will try to annotate themselves multiple times, so\n      // to keep things simple we just bail out if re-wrapping the message.\n      if (!/^\\[BABEL\\]/.test(e.message)) {\n        e.message = `[BABEL] ${context.filename ?? \"unknown file\"}: ${\n          e.message\n        }`;\n      }\n\n      throw e;\n    }\n  } as any;\n}\n\n/**\n * Load a generic plugin/preset from the given descriptor loaded from the config object.\n */\nconst makeDescriptorLoader = <Context, API>(\n  apiFactory: (\n    cache: CacheConfigurator<Context>,\n    externalDependencies: Array<string>,\n  ) => API,\n) =>\n  makeWeakCache(function* (\n    { value, options, dirname, alias }: UnloadedDescriptor,\n    cache: CacheConfigurator<Context>,\n  ): Handler<LoadedDescriptor> {\n    // Disabled presets should already have been filtered out\n    if (options === false) throw new Error(\"Assertion failure\");\n\n    options = options || {};\n\n    const externalDependencies: Array<string> = [];\n\n    let item = value;\n    if (typeof value === \"function\") {\n      const factory = maybeAsync(\n        value,\n        `You appear to be using an async plugin/preset, but Babel has been called synchronously`,\n      );\n\n      const api = {\n        ...context,\n        ...apiFactory(cache, externalDependencies),\n      };\n      try {\n        item = yield* factory(api, options, dirname);\n      } catch (e) {\n        if (alias) {\n          e.message += ` (While processing: ${JSON.stringify(alias)})`;\n        }\n        throw e;\n      }\n    }\n\n    if (!item || typeof item !== \"object\") {\n      throw new Error(\"Plugin/Preset did not return an object.\");\n    }\n\n    if (isThenable(item)) {\n      // @ts-expect-error - if we want to support async plugins\n      yield* [];\n\n      throw new Error(\n        `You appear to be using a promise as a plugin, ` +\n          `which your current version of Babel does not support. ` +\n          `If you're using a published plugin, ` +\n          `you may need to upgrade your @babel/core version. ` +\n          `As an alternative, you can prefix the promise with \"await\". ` +\n          `(While processing: ${JSON.stringify(alias)})`,\n      );\n    }\n\n    if (\n      externalDependencies.length > 0 &&\n      (!cache.configured() || cache.mode() === \"forever\")\n    ) {\n      let error =\n        `A plugin/preset has external untracked dependencies ` +\n        `(${externalDependencies[0]}), but the cache `;\n      if (!cache.configured()) {\n        error += `has not been configured to be invalidated when the external dependencies change. `;\n      } else {\n        error += ` has been configured to never be invalidated. `;\n      }\n      error +=\n        `Plugins/presets should configure their cache to be invalidated when the external ` +\n        `dependencies change, for example using \\`api.cache.invalidate(() => ` +\n        `statSync(filepath).mtimeMs)\\` or \\`api.cache.never()\\`\\n` +\n        `(While processing: ${JSON.stringify(alias)})`;\n\n      throw new Error(error);\n    }\n\n    return {\n      value: item,\n      options,\n      dirname,\n      alias,\n      externalDependencies: freezeDeepArray(externalDependencies),\n    };\n  });\n\nconst pluginDescriptorLoader = makeDescriptorLoader<\n  Context.SimplePlugin,\n  PluginAPI\n>(makePluginAPI);\nconst presetDescriptorLoader = makeDescriptorLoader<\n  Context.SimplePreset,\n  PresetAPI\n>(makePresetAPI);\n\nconst instantiatePlugin = makeWeakCache(function* (\n  { value, options, dirname, alias, externalDependencies }: LoadedDescriptor,\n  cache: CacheConfigurator<Context.SimplePlugin>,\n): Handler<Plugin> {\n  const pluginObj = validatePluginObject(value);\n\n  const plugin = {\n    ...pluginObj,\n  };\n  if (plugin.visitor) {\n    plugin.visitor = traverse.explode({\n      ...plugin.visitor,\n    });\n  }\n\n  if (plugin.inherits) {\n    const inheritsDescriptor: UnloadedDescriptor = {\n      name: undefined,\n      alias: `${alias}$inherits`,\n      value: plugin.inherits,\n      options,\n      dirname,\n    };\n\n    const inherits = yield* forwardAsync(loadPluginDescriptor, run => {\n      // If the inherited plugin changes, reinstantiate this plugin.\n      return cache.invalidate(data => run(inheritsDescriptor, data));\n    });\n\n    plugin.pre = chain(inherits.pre, plugin.pre);\n    plugin.post = chain(inherits.post, plugin.post);\n    plugin.manipulateOptions = chain(\n      inherits.manipulateOptions,\n      plugin.manipulateOptions,\n    );\n    plugin.visitor = traverse.visitors.merge([\n      inherits.visitor || {},\n      plugin.visitor || {},\n    ]);\n\n    if (inherits.externalDependencies.length > 0) {\n      if (externalDependencies.length === 0) {\n        externalDependencies = inherits.externalDependencies;\n      } else {\n        externalDependencies = freezeDeepArray([\n          externalDependencies,\n          inherits.externalDependencies,\n        ]);\n      }\n    }\n  }\n\n  return new Plugin(plugin, options, alias, externalDependencies);\n});\n\n/**\n * Instantiate a plugin for the given descriptor, returning the plugin/options pair.\n */\nfunction* loadPluginDescriptor(\n  descriptor: UnloadedDescriptor,\n  context: Context.SimplePlugin,\n): Handler<Plugin> {\n  if (descriptor.value instanceof Plugin) {\n    if (descriptor.options) {\n      throw new Error(\n        \"Passed options to an existing Plugin instance will not work.\",\n      );\n    }\n\n    return descriptor.value;\n  }\n\n  return yield* instantiatePlugin(\n    yield* pluginDescriptorLoader(descriptor, context),\n    context,\n  );\n}\n\nconst needsFilename = (val: unknown) => val && typeof val !== \"function\";\n\nconst validateIfOptionNeedsFilename = (\n  options: ValidatedOptions,\n  descriptor: UnloadedDescriptor,\n): void => {\n  if (\n    needsFilename(options.test) ||\n    needsFilename(options.include) ||\n    needsFilename(options.exclude)\n  ) {\n    const formattedPresetName = descriptor.name\n      ? `\"${descriptor.name}\"`\n      : \"/* your preset */\";\n    throw new ConfigError(\n      [\n        `Preset ${formattedPresetName} requires a filename to be set when babel is called directly,`,\n        `\\`\\`\\``,\n        `babel.transformSync(code, { filename: 'file.ts', presets: [${formattedPresetName}] });`,\n        `\\`\\`\\``,\n        `See https://babeljs.io/docs/en/options#filename for more information.`,\n      ].join(\"\\n\"),\n    );\n  }\n};\n\nconst validatePreset = (\n  preset: PresetInstance,\n  context: ConfigContext,\n  descriptor: UnloadedDescriptor,\n): void => {\n  if (!context.filename) {\n    const { options } = preset;\n    validateIfOptionNeedsFilename(options, descriptor);\n    if (options.overrides) {\n      options.overrides.forEach(overrideOptions =>\n        validateIfOptionNeedsFilename(overrideOptions, descriptor),\n      );\n    }\n  }\n};\n\nconst instantiatePreset = makeWeakCacheSync(\n  ({\n    value,\n    dirname,\n    alias,\n    externalDependencies,\n  }: LoadedDescriptor): PresetInstance => {\n    return {\n      options: validate(\"preset\", value),\n      alias,\n      dirname,\n      externalDependencies,\n    };\n  },\n);\n\n/**\n * Generate a config object that will act as the root of a new nested config.\n */\nfunction* loadPresetDescriptor(\n  descriptor: UnloadedDescriptor,\n  context: Context.FullPreset,\n): Handler<{\n  chain: ConfigChain | null;\n  externalDependencies: ReadonlyDeepArray<string>;\n}> {\n  const preset = instantiatePreset(\n    yield* presetDescriptorLoader(descriptor, context),\n  );\n  validatePreset(preset, context, descriptor);\n  return {\n    chain: yield* buildPresetChain(preset, context),\n    externalDependencies: preset.externalDependencies,\n  };\n}\n\nfunction chain<Args extends any[]>(\n  a: undefined | ((...args: Args) => void),\n  b: undefined | ((...args: Args) => void),\n) {\n  const fns = [a, b].filter(Boolean);\n  if (fns.length <= 1) return fns[0];\n\n  return function (this: unknown, ...args: unknown[]) {\n    for (const fn of fns) {\n      fn.apply(this, args);\n    }\n  };\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,IAAAE,MAAA,GAAAD,OAAA;AAEA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,YAAA,GAAAN,OAAA;AACA,IAAAO,UAAA,GAAAP,OAAA;AAQA,SAAAQ,UAAA;EAAA,MAAAT,IAAA,GAAAC,OAAA;EAAAQ,SAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,IAAAU,QAAA,GAAAT,OAAA;AAEA,IAAAU,QAAA,GAAAV,OAAA;AAKA,IAAAW,QAAA,GAAAX,OAAA;AACA,IAAAY,UAAA,GAAAZ,OAAA;AAGA,IAAAa,QAAA,GAAAb,OAAA;AAIA,IAAAc,YAAA,GAAAd,OAAA;AAAiD,IAAAe,QAAA,GAsBlCC,UAAO,CAAC,UAAUC,cAAcA,CAC7CC,SAAkB,EACc;EAAA,IAAAC,iBAAA;EAChC,MAAMC,MAAM,GAAG,OAAO,IAAAC,gBAAwB,EAACH,SAAS,CAAC;EACzD,IAAI,CAACE,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EACA,MAAM;IAAEE,OAAO;IAAEnB,OAAO;IAAEoB;EAAa,CAAC,GAAGH,MAAM;EAEjD,IAAIG,YAAY,KAAK,SAAS,EAAE;IAC9B,OAAO,IAAI;EACb;EAEA,MAAMC,cAAc,GAAG,CAAC,CAAC;EAEzB,MAAM;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGJ,OAAO;EAEpC,IAAI,CAACG,OAAO,IAAI,CAACC,OAAO,EAAE;IACxB,MAAM,IAAIC,KAAK,CAAC,+CAA+C,CAAC;EAClE;EAEA,MAAMC,aAAiC,GAAAC,MAAA,CAAAC,MAAA,KAClC3B,OAAO;IACV4B,OAAO,EAAET,OAAO,CAACS;EAAO,EACzB;EAED,MAAMC,YAAY,GAAIC,IAAgB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAAC,uBAAiB,EAACF,IAAI,CAAC;IACpC,IAAI,CAACC,IAAI,EAAE;MACT,MAAM,IAAIP,KAAK,CAAC,yCAAyC,CAAC;IAC5D;IAEA,OAAOO,IAAI;EACb,CAAC;EAED,MAAME,kBAAkB,GAAGV,OAAO,CAACW,GAAG,CAACL,YAAY,CAAC;EACpD,MAAMM,yBAAyB,GAAGb,OAAO,CAACY,GAAG,CAACL,YAAY,CAAC;EAC3D,MAAMO,uBAAyD,GAAG,CAAC,EAAE,CAAC;EACtE,MAAMC,MAA4B,GAAG,EAAE;EAEvC,MAAMC,oBAAuC,GAAG,EAAE;EAElD,MAAMC,OAAO,GAAG,OAAOC,YAAY,CACjCxC,OAAO,EACP,UAAUyC,wBAAwBA,CAChCC,UAAqC,EACrCC,qBAAgD,EAC1B;IACtB,MAAMpB,OAGJ,GAAG,EAAE;IAEP,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1C,MAAME,UAAU,GAAGJ,UAAU,CAACE,CAAC,CAAC;MAChC,IAAIE,UAAU,CAAC3B,OAAO,KAAK,KAAK,EAAE;QAChC,IAAI;UAEF,IAAI4B,MAAM,GAAG,OAAOC,oBAAoB,CAACF,UAAU,EAAErB,aAAa,CAAC;QACrE,CAAC,CAAC,OAAOwB,CAAC,EAAE;UACV,IAAIA,CAAC,CAACC,IAAI,KAAK,sBAAsB,EAAE;YACrC,IAAAC,wCAA+B,EAACT,UAAU,EAAEE,CAAC,EAAE,QAAQ,EAAEK,CAAC,CAAC;UAC7D;UACA,MAAMA,CAAC;QACT;QAEAX,oBAAoB,CAACc,IAAI,CAACL,MAAM,CAACT,oBAAoB,CAAC;QAKtD,IAAIQ,UAAU,CAACO,OAAO,EAAE;UACtB9B,OAAO,CAAC6B,IAAI,CAAC;YAAEL,MAAM,EAAEA,MAAM,CAACO,KAAK;YAAEC,IAAI,EAAE;UAAG,CAAC,CAAC;QAClD,CAAC,MAAM;UACLhC,OAAO,CAACiC,OAAO,CAAC;YACdT,MAAM,EAAEA,MAAM,CAACO,KAAK;YACpBC,IAAI,EAAEZ;UACR,CAAC,CAAC;QACJ;MACF;IACF;IAGA,IAAIpB,OAAO,CAACsB,MAAM,GAAG,CAAC,EAAE;MAGtBT,uBAAuB,CAACqB,MAAM,CAC5B,CAAC,EACD,CAAC,EACD,GAAGlC,OAAO,CAACW,GAAG,CAACwB,CAAC,IAAIA,CAAC,CAACH,IAAI,CAAC,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKjB,qBAAqB,CAAC,CACrE;MAED,KAAK,MAAM;QAAEI,MAAM;QAAEQ;MAAK,CAAC,IAAIhC,OAAO,EAAE;QACtC,IAAI,CAACwB,MAAM,EAAE,OAAO,IAAI;QAExBQ,IAAI,CAACH,IAAI,CAAC,GAAGL,MAAM,CAACzB,OAAO,CAAC;QAE5B,MAAMiB,OAAO,GAAG,OAAOE,wBAAwB,CAACM,MAAM,CAACxB,OAAO,EAAEgC,IAAI,CAAC;QACrE,IAAIhB,OAAO,EAAE,OAAO,IAAI;QAExBQ,MAAM,CAAC5B,OAAO,CAAC0C,OAAO,CAACC,IAAI,IAAI;UAC7B,IAAAC,kBAAY,EAAC1C,cAAc,EAAEyC,IAAI,CAAC;QACpC,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CACF,CAAC7B,kBAAkB,EAAEG,uBAAuB,CAAC,CAAC,CAAC,CAAC;EAEjD,IAAIG,OAAO,EAAE,OAAO,IAAI;EAExB,MAAMuB,IAAS,GAAGzC,cAAc;EAChC,IAAA0C,kBAAY,EAACD,IAAI,EAAE3C,OAAO,CAAC;EAE3B,MAAM6C,aAAiC,GAAAtC,MAAA,CAAAC,MAAA,KAClCF,aAAa;IAChBwC,WAAW,GAAAjD,iBAAA,GAAE8C,IAAI,CAACG,WAAW,YAAAjD,iBAAA,GAAI,CAAC;EAAC,EACpC;EAED,OAAOwB,YAAY,CAACxC,OAAO,EAAE,UAAUkE,qBAAqBA,CAAA,EAAG;IAC7D9B,uBAAuB,CAAC,CAAC,CAAC,CAACoB,OAAO,CAAC,GAAGrB,yBAAyB,CAAC;IAEhE,KAAK,MAAMgC,KAAK,IAAI/B,uBAAuB,EAAE;MAC3C,MAAMmB,IAAc,GAAG,EAAE;MACzBlB,MAAM,CAACe,IAAI,CAACG,IAAI,CAAC;MAEjB,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,KAAK,CAACtB,MAAM,EAAED,CAAC,EAAE,EAAE;QACrC,MAAME,UAA8B,GAAGqB,KAAK,CAACvB,CAAC,CAAC;QAC/C,IAAIE,UAAU,CAAC3B,OAAO,KAAK,KAAK,EAAE;UAChC,IAAI;YAEF,IAAIiD,MAAM,GAAG,OAAOC,oBAAoB,CAACvB,UAAU,EAAEkB,aAAa,CAAC;UACrE,CAAC,CAAC,OAAOf,CAAC,EAAE;YACV,IAAIA,CAAC,CAACC,IAAI,KAAK,+BAA+B,EAAE;cAE9C,IAAAC,wCAA+B,EAACgB,KAAK,EAAEvB,CAAC,EAAE,QAAQ,EAAEK,CAAC,CAAC;YACxD;YACA,MAAMA,CAAC;UACT;UACAM,IAAI,CAACH,IAAI,CAACgB,MAAM,CAAC;UAEjB9B,oBAAoB,CAACc,IAAI,CAACgB,MAAM,CAAC9B,oBAAoB,CAAC;QACxD;MACF;IACF;EACF,CAAC,CAAC,EAAE;EAEJwB,IAAI,CAACxC,OAAO,GAAGe,MAAM,CAAC,CAAC,CAAC;EACxByB,IAAI,CAACvC,OAAO,GAAGc,MAAM,CAClBiC,KAAK,CAAC,CAAC,CAAC,CACRX,MAAM,CAACrC,OAAO,IAAIA,OAAO,CAACuB,MAAM,GAAG,CAAC,CAAC,CACrCX,GAAG,CAACZ,OAAO,KAAK;IAAEA;EAAQ,CAAC,CAAC,CAAC;EAChCwC,IAAI,CAACS,aAAa,GAAGT,IAAI,CAACvC,OAAO,CAACsB,MAAM,GAAG,CAAC;EAE5C,OAAO;IACL1B,OAAO,EAAE2C,IAAI;IACbzB,MAAM,EAAEA,MAAM;IACdC,oBAAoB,EAAE,IAAAkC,mBAAe,EAAClC,oBAAoB;EAC5D,CAAC;AACH,CAAC,CAAC;AAAAmC,OAAA,CAAAC,OAAA,GAAA9D,QAAA;AAEF,SAAS4B,YAAYA,CAAqBxC,OAAsB,EAAE2E,EAAK,EAAK;EAC1E,OAAO,WAAWC,IAAa,EAAEC,IAAa,EAAE;IAC9C,IAAI;MACF,OAAO,OAAOF,EAAE,CAACC,IAAI,EAAEC,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAO5B,CAAC,EAAE;MAGV,IAAI,CAAC,YAAY,CAAC6B,IAAI,CAAC7B,CAAC,CAAC8B,OAAO,CAAC,EAAE;QAAA,IAAAC,iBAAA;QACjC/B,CAAC,CAAC8B,OAAO,GAAI,WAAQ,CAAAC,iBAAA,GAAEhF,OAAO,CAACiF,QAAQ,YAAAD,iBAAA,GAAI,cAAe,KACxD/B,CAAC,CAAC8B,OACH,EAAC;MACJ;MAEA,MAAM9B,CAAC;IACT;EACF,CAAC;AACH;AAKA,MAAMiC,oBAAoB,GACxBC,UAGQ,IAER,IAAAC,sBAAa,EAAC,WACZ;EAAEC,KAAK;EAAElE,OAAO;EAAEmE,OAAO;EAAEC;AAA0B,CAAC,EACtDC,KAAiC,EACN;EAE3B,IAAIrE,OAAO,KAAK,KAAK,EAAE,MAAM,IAAIK,KAAK,CAAC,mBAAmB,CAAC;EAE3DL,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,MAAMmB,oBAAmC,GAAG,EAAE;EAE9C,IAAIR,IAAI,GAAGuD,KAAK;EAChB,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;IAC/B,MAAMI,OAAO,GAAG,IAAAC,iBAAU,EACxBL,KAAK,EACJ,wFAAuF,CACzF;IAED,MAAMM,GAAG,GAAAjE,MAAA,CAAAC,MAAA,KACJ3B,OAAO,EACPmF,UAAU,CAACK,KAAK,EAAElD,oBAAoB,CAAC,CAC3C;IACD,IAAI;MACFR,IAAI,GAAG,OAAO2D,OAAO,CAACE,GAAG,EAAExE,OAAO,EAAEmE,OAAO,CAAC;IAC9C,CAAC,CAAC,OAAOrC,CAAC,EAAE;MACV,IAAIsC,KAAK,EAAE;QACTtC,CAAC,CAAC8B,OAAO,IAAK,uBAAsBa,IAAI,CAACC,SAAS,CAACN,KAAK,CAAE,GAAE;MAC9D;MACA,MAAMtC,CAAC;IACT;EACF;EAEA,IAAI,CAACnB,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACrC,MAAM,IAAIN,KAAK,CAAC,yCAAyC,CAAC;EAC5D;EAEA,IAAI,IAAAsE,iBAAU,EAAChE,IAAI,CAAC,EAAE;IAEpB,OAAO,EAAE;IAET,MAAM,IAAIN,KAAK,CACZ,gDAA+C,GAC7C,wDAAuD,GACvD,sCAAqC,GACrC,oDAAmD,GACnD,8DAA6D,GAC7D,sBAAqBoE,IAAI,CAACC,SAAS,CAACN,KAAK,CAAE,GAAE,CACjD;EACH;EAEA,IACEjD,oBAAoB,CAACO,MAAM,GAAG,CAAC,KAC9B,CAAC2C,KAAK,CAACO,UAAU,EAAE,IAAIP,KAAK,CAACQ,IAAI,EAAE,KAAK,SAAS,CAAC,EACnD;IACA,IAAIC,KAAK,GACN,sDAAqD,GACrD,IAAG3D,oBAAoB,CAAC,CAAC,CAAE,mBAAkB;IAChD,IAAI,CAACkD,KAAK,CAACO,UAAU,EAAE,EAAE;MACvBE,KAAK,IAAK,mFAAkF;IAC9F,CAAC,MAAM;MACLA,KAAK,IAAK,gDAA+C;IAC3D;IACAA,KAAK,IACF,mFAAkF,GAClF,sEAAqE,GACrE,0DAAyD,GACzD,sBAAqBL,IAAI,CAACC,SAAS,CAACN,KAAK,CAAE,GAAE;IAEhD,MAAM,IAAI/D,KAAK,CAACyE,KAAK,CAAC;EACxB;EAEA,OAAO;IACLZ,KAAK,EAAEvD,IAAI;IACXX,OAAO;IACPmE,OAAO;IACPC,KAAK;IACLjD,oBAAoB,EAAE,IAAAkC,mBAAe,EAAClC,oBAAoB;EAC5D,CAAC;AACH,CAAC,CAAC;AAEJ,MAAM4D,sBAAsB,GAAGhB,oBAAoB,CAGjDiB,wBAAa,CAAC;AAChB,MAAMC,sBAAsB,GAAGlB,oBAAoB,CAGjDmB,wBAAa,CAAC;AAEhB,MAAMC,iBAAiB,GAAG,IAAAlB,sBAAa,EAAC,WACtC;EAAEC,KAAK;EAAElE,OAAO;EAAEmE,OAAO;EAAEC,KAAK;EAAEjD;AAAuC,CAAC,EAC1EkD,KAA8C,EAC7B;EACjB,MAAMe,SAAS,GAAG,IAAAC,6BAAoB,EAACnB,KAAK,CAAC;EAE7C,MAAMjB,MAAM,GAAA1C,MAAA,CAAAC,MAAA,KACP4E,SAAS,CACb;EACD,IAAInC,MAAM,CAACqC,OAAO,EAAE;IAClBrC,MAAM,CAACqC,OAAO,GAAGC,mBAAQ,CAACC,OAAO,CAAAjF,MAAA,CAAAC,MAAA,KAC5ByC,MAAM,CAACqC,OAAO,EACjB;EACJ;EAEA,IAAIrC,MAAM,CAACwC,QAAQ,EAAE;IACnB,MAAMC,kBAAsC,GAAG;MAC7CC,IAAI,EAAEC,SAAS;MACfxB,KAAK,EAAG,GAAEA,KAAM,WAAU;MAC1BF,KAAK,EAAEjB,MAAM,CAACwC,QAAQ;MACtBzF,OAAO;MACPmE;IACF,CAAC;IAED,MAAMsB,QAAQ,GAAG,OAAO,IAAAI,mBAAY,EAAC3C,oBAAoB,EAAE4C,GAAG,IAAI;MAEhE,OAAOzB,KAAK,CAAC0B,UAAU,CAACtH,IAAI,IAAIqH,GAAG,CAACJ,kBAAkB,EAAEjH,IAAI,CAAC,CAAC;IAChE,CAAC,CAAC;IAEFwE,MAAM,CAAC+C,GAAG,GAAG7D,KAAK,CAACsD,QAAQ,CAACO,GAAG,EAAE/C,MAAM,CAAC+C,GAAG,CAAC;IAC5C/C,MAAM,CAACgD,IAAI,GAAG9D,KAAK,CAACsD,QAAQ,CAACQ,IAAI,EAAEhD,MAAM,CAACgD,IAAI,CAAC;IAC/ChD,MAAM,CAACiD,iBAAiB,GAAG/D,KAAK,CAC9BsD,QAAQ,CAACS,iBAAiB,EAC1BjD,MAAM,CAACiD,iBAAiB,CACzB;IACDjD,MAAM,CAACqC,OAAO,GAAGC,mBAAQ,CAACY,QAAQ,CAACC,KAAK,CAAC,CACvCX,QAAQ,CAACH,OAAO,IAAI,CAAC,CAAC,EACtBrC,MAAM,CAACqC,OAAO,IAAI,CAAC,CAAC,CACrB,CAAC;IAEF,IAAIG,QAAQ,CAACtE,oBAAoB,CAACO,MAAM,GAAG,CAAC,EAAE;MAC5C,IAAIP,oBAAoB,CAACO,MAAM,KAAK,CAAC,EAAE;QACrCP,oBAAoB,GAAGsE,QAAQ,CAACtE,oBAAoB;MACtD,CAAC,MAAM;QACLA,oBAAoB,GAAG,IAAAkC,mBAAe,EAAC,CACrClC,oBAAoB,EACpBsE,QAAQ,CAACtE,oBAAoB,CAC9B,CAAC;MACJ;IACF;EACF;EAEA,OAAO,IAAIkF,eAAM,CAACpD,MAAM,EAAEjD,OAAO,EAAEoE,KAAK,EAAEjD,oBAAoB,CAAC;AACjE,CAAC,CAAC;AAKF,UAAU+B,oBAAoBA,CAC5BvB,UAA8B,EAC9B9C,OAA6B,EACZ;EACjB,IAAI8C,UAAU,CAACuC,KAAK,YAAYmC,eAAM,EAAE;IACtC,IAAI1E,UAAU,CAAC3B,OAAO,EAAE;MACtB,MAAM,IAAIK,KAAK,CACb,8DAA8D,CAC/D;IACH;IAEA,OAAOsB,UAAU,CAACuC,KAAK;EACzB;EAEA,OAAO,OAAOiB,iBAAiB,CAC7B,OAAOJ,sBAAsB,CAACpD,UAAU,EAAE9C,OAAO,CAAC,EAClDA,OAAO,CACR;AACH;AAEA,MAAMyH,aAAa,GAAIC,GAAY,IAAKA,GAAG,IAAI,OAAOA,GAAG,KAAK,UAAU;AAExE,MAAMC,6BAA6B,GAAGA,CACpCxG,OAAyB,EACzB2B,UAA8B,KACrB;EACT,IACE2E,aAAa,CAACtG,OAAO,CAAC2D,IAAI,CAAC,IAC3B2C,aAAa,CAACtG,OAAO,CAACyG,OAAO,CAAC,IAC9BH,aAAa,CAACtG,OAAO,CAAC0G,OAAO,CAAC,EAC9B;IACA,MAAMC,mBAAmB,GAAGhF,UAAU,CAACgE,IAAI,GACtC,IAAGhE,UAAU,CAACgE,IAAK,GAAE,GACtB,mBAAmB;IACvB,MAAM,IAAIiB,oBAAW,CACnB,CACG,UAASD,mBAAoB,+DAA8D,EAC3F,QAAO,EACP,8DAA6DA,mBAAoB,OAAM,EACvF,QAAO,EACP,uEAAsE,CACxE,CAACE,IAAI,CAAC,IAAI,CAAC,CACb;EACH;AACF,CAAC;AAED,MAAMC,cAAc,GAAGA,CACrBlF,MAAsB,EACtB/C,OAAsB,EACtB8C,UAA8B,KACrB;EACT,IAAI,CAAC9C,OAAO,CAACiF,QAAQ,EAAE;IACrB,MAAM;MAAE9D;IAAQ,CAAC,GAAG4B,MAAM;IAC1B4E,6BAA6B,CAACxG,OAAO,EAAE2B,UAAU,CAAC;IAClD,IAAI3B,OAAO,CAAC+G,SAAS,EAAE;MACrB/G,OAAO,CAAC+G,SAAS,CAACrE,OAAO,CAACsE,eAAe,IACvCR,6BAA6B,CAACQ,eAAe,EAAErF,UAAU,CAAC,CAC3D;IACH;EACF;AACF,CAAC;AAED,MAAMsF,iBAAiB,GAAG,IAAAC,0BAAiB,EACzC,CAAC;EACChD,KAAK;EACLC,OAAO;EACPC,KAAK;EACLjD;AACgB,CAAC,KAAqB;EACtC,OAAO;IACLnB,OAAO,EAAE,IAAAmH,iBAAQ,EAAC,QAAQ,EAAEjD,KAAK,CAAC;IAClCE,KAAK;IACLD,OAAO;IACPhD;EACF,CAAC;AACH,CAAC,CACF;AAKD,UAAUU,oBAAoBA,CAC5BF,UAA8B,EAC9B9C,OAA2B,EAI1B;EACD,MAAM+C,MAAM,GAAGqF,iBAAiB,CAC9B,OAAOhC,sBAAsB,CAACtD,UAAU,EAAE9C,OAAO,CAAC,CACnD;EACDiI,cAAc,CAAClF,MAAM,EAAE/C,OAAO,EAAE8C,UAAU,CAAC;EAC3C,OAAO;IACLQ,KAAK,EAAE,OAAO,IAAAiF,6BAAgB,EAACxF,MAAM,EAAE/C,OAAO,CAAC;IAC/CsC,oBAAoB,EAAES,MAAM,CAACT;EAC/B,CAAC;AACH;AAEA,SAASgB,KAAKA,CACZkF,CAAwC,EACxCC,CAAwC,EACxC;EACA,MAAMC,GAAG,GAAG,CAACF,CAAC,EAAEC,CAAC,CAAC,CAAC9E,MAAM,CAACgF,OAAO,CAAC;EAClC,IAAID,GAAG,CAAC7F,MAAM,IAAI,CAAC,EAAE,OAAO6F,GAAG,CAAC,CAAC,CAAC;EAElC,OAAO,UAAyB,GAAGE,IAAe,EAAE;IAClD,KAAK,MAAMjE,EAAE,IAAI+D,GAAG,EAAE;MACpB/D,EAAE,CAACkE,KAAK,CAAC,IAAI,EAAED,IAAI,CAAC;IACtB;EACF,CAAC;AACH;AAAC"}