import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '../../../../theme/ThemeContext';
import { useTranslation } from 'react-i18next';
import {
  Layout,
  Button,
  AppmakerText,
  Input,
  Modal,
} from '@appmaker-xyz/uikit/src/index';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';

const CustomizeButtons = ({
  attributes,
  pageState,
  onAction,
  blockData,
  clientId,
}) => {
  const [addtoCartLoading, setAddtoCartLoading] = useState(false);
  const [buyNowLoading, setBuyNowLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [notifyEmail, setNotifyEmail] = useState('');
  const [visible, setVisible] = useState(false);
  const [notifySuccess, setNotifySuccess] = useState(false);
  let toggle = () => setVisible(!visible);
  const {
    buyNowText,
    addCartText,
    buyNowAction,
    in_stock,
    addCartButtonStyle,
    buyNowButtonStyle,
    addCartFontColor,
    buyNowFontColor,
    showInReverseOrder,
    enableNotifyMe,
    __appmakerCustomStyles = {},
  } = attributes;
  const { t } = useTranslation();
  const { color, spacing, font } = useApThemeState();
  const styles = allStyles({ color, spacing, font });
  const FillButton = ({
    btnText,
    status,
    onPress,
    loading,
    disabled,
    clientId: _clientId,
    wholeContainerStyle,
    customStyles,
    fontColor,
    ...props
  }) => {
    const filledButtonStyle = [styles.filledButton];
    if (status) {
      filledButtonStyle.push({ backgroundColor: color[status] });
    }
    if (wholeContainerStyle?.backgroundColor) {
      filledButtonStyle.push({
        backgroundColor: wholeContainerStyle?.backgroundColor,
      });
    }
    return (
      <Layout style={filledButtonStyle}>
        <Button
          clientId={_clientId}
          status={status}
          onPress={onPress}
          loading={loading}
          disabled={disabled}
          wholeContainerStyle={wholeContainerStyle}
          fontColor={fontColor}
          __appmakerCustomStyles={customStyles}
          {...props}>
          {btnText}
        </Button>
      </Layout>
    );
  };
  const addToCart = async ({ buynow = false }) => {
    const setCartLoadingIndicator = buynow
      ? setBuyNowLoading
      : setAddtoCartLoading;
    // const quantity = pageState.state.quantity;

    setCartLoadingIndicator(true);
    try {
      const cartResp = await onAction({
        action: 'ADD_TO_CART',
        params: { pageState, product: blockData },
      });
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: `${t(cartResp?.message)}`,
          buttonTitle: buynow ? '' : t('View Cart'),
          buttonAction: buynow
            ? ''
            : {
                action: 'OPEN_CART',
              },
        },
        t,
      });
      if (buynow && cartResp?.status === 'success') {
        onAction({ action: 'OPEN_CART' });
      }
    } catch (error) {
      console.log(error);
      // console.log();
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: error.message || error.data.message,
        },
        t,
      });
    } finally {
      setCartLoadingIndicator(false);
    }
  };
  const MainButtons = () => (
    <>
      {!!buyNowText && (
        <FillButton
          clientId={`${clientId}-customize`}
          onPress={() => {
            buyNowAction
              ? buyNowAction.type === 'normal'
                ? addToCart({ buynow: true })
                : onAction(buyNowAction)
              : addToCart({ buynow: true });
          }}
          btnText={buyNowText}
          status="primary"
          loading={buyNowLoading}
          wholeContainerStyle={buyNowButtonStyle}
          fontColor={buyNowFontColor}
          disabled={buyNowLoading || addtoCartLoading}
          customStyles={__appmakerCustomStyles?.action_buttons?.buy_now_button}
        />
      )}
      {!!addCartText && (
        <FillButton
          clientId={`${clientId}-add-to-cart`}
          onPress={() => addToCart({ buynow: false })}
          btnText={addCartText}
          status="dark"
          loading={addtoCartLoading}
          wholeContainerStyle={addCartButtonStyle}
          fontColor={addCartFontColor}
          disabled={addtoCartLoading || buyNowLoading}
          customStyles={
            __appmakerCustomStyles?.action_buttons?.add_to_cart_button
          }
        />
      )}
    </>
  );
  return (
    <Layout
      style={{ flexDirection: showInReverseOrder ? 'row-reverse' : 'row' }}>
      {in_stock === 'false' ? (
        <>
          <FillButton
            onPress={
              () =>
                onAction({
                  action: 'SHOW_MESSAGE',
                  params: {
                    title:
                      "We'll notify you when this product is back in stock",
                  },
                  t,
                })
              // toggle()
            }
            btnText={
              enableNotifyMe == 1 ? 'Notify when available' : 'Out of Stock'
            }
            status={enableNotifyMe == 1 ? 'demiDark' : 'grey'}
          />
          {enableNotifyMe == 1 ? (
            <Modal
              isVisible={visible}
              onSwipeComplete={toggle}
              onBackButtonPress={toggle}
              backdropTransitionOutTiming={0}
              onBackdropPress={toggle}
              style={styles.modal}>
              <Layout style={styles.modalBody}>
                <Layout style={styles.modalHeader}>
                  <AppmakerText category="actionTitle" status="dark">
                    Notify through email
                  </AppmakerText>
                  <Button
                    onPress={toggle}
                    status="white"
                    accessoryLeft={() => <Icon name="x" size={font.size.lg} />}
                    small
                  />
                </Layout>
                <Layout style={styles.modalContent}>
                  {notifySuccess ? (
                    <>
                      <AppmakerText category="bodyParagraphBold">
                        We'll notify you when this product is back in stock to{' '}
                        {notifyEmail}
                      </AppmakerText>
                    </>
                  ) : (
                    <>
                      <Input
                        value={notifyEmail}
                        onChangeText={setNotifyEmail}
                        label="Enter Email"
                        leftIcon="mail"
                        name="email"
                        status="demiDark"
                        autoCompleteType="email"
                      />
                      <Button
                        baseSize
                        onPress={() => setNotifySuccess(true)}
                        status="dark"
                        loading={loading}>
                        Notify Me
                      </Button>
                    </>
                  )}
                </Layout>
              </Layout>
            </Modal>
          ) : null}
        </>
      ) : (
        <MainButtons />
      )}
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    bottomButtons: {
      flexDirection: 'row',
    },
    filledButton: {
      flex: 1,
      backgroundColor: color.primary,
      height: 56,
    },
    modal: {
      justifyContent: 'flex-end',
      marginVertical: 0,
      marginHorizontal: 1,
    },
    modalBody: {
      backgroundColor: color.white,
      padding: spacing.md,
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      borderTopStartRadius: spacing.md,
      borderTopEndRadius: spacing.md,
      flexDirection: 'column',
      maxHeight: '80%',
    },
    modalHeader: {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    modalContent: {
      paddingVertical: spacing.base,
      flexDirection: 'column',
      width: '100%',
    },
  });

export default CustomizeButtons;
