import {
  ShopifyCheckoutSheet,
  CheckoutEvent,
  CheckoutEventCallback,
} from '@shopify/checkout-sheet-kit';
import { getCheckoutUrl } from './helper';

class ShopifyCheckout {
  private shopifyCheckout: ShopifyCheckoutSheet;

  constructor() {
    this.shopifyCheckout = new ShopifyCheckoutSheet({});
  }

  public presentCheckout(checkoutUrl?: string) {
    const currentCartCheckoutUrl = checkoutUrl || getCheckoutUrl();
    return currentCartCheckoutUrl && this.shopifyCheckout.present(checkoutUrl);
  }

  public preload(checkoutUrl?: string) {
    const currentCartCheckoutUrl = checkoutUrl || getCheckoutUrl();
    if (currentCartCheckoutUrl) {
      return this.shopifyCheckout.preload(currentCartCheckoutUrl)
    }
    return false;
  }

  public invalidate() {
    return this.shopifyCheckout.invalidate();
  }

  public addEventListener(
    event: CheckoutEvent,
    listener: CheckoutEventCallback,
  ) {
    this.shopifyCheckout.addEventListener(event, listener);
  }

  public removeEventListener(event: CheckoutEvent) {
    this.shopifyCheckout.removeEventListeners(event);
  }
}

export default ShopifyCheckout;
