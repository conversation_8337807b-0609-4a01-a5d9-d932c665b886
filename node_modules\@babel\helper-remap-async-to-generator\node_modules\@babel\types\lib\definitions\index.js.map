{"version": 3, "names": ["_toFastProperties", "require", "_utils", "_placeholders", "_deprecatedAliases", "Object", "keys", "DEPRECATED_ALIASES", "for<PERSON>ach", "depre<PERSON><PERSON><PERSON><PERSON>", "FLIPPED_ALIAS_KEYS", "toFastProperties", "VISITOR_KEYS", "ALIAS_KEYS", "NODE_FIELDS", "BUILDER_KEYS", "DEPRECATED_KEYS", "PLACEHOLDERS_ALIAS", "PLACEHOLDERS_FLIPPED_ALIAS", "TYPES", "concat", "exports"], "sources": ["../../src/definitions/index.ts"], "sourcesContent": ["import toFastProperties from \"to-fast-properties\";\nimport \"./core\";\nimport \"./flow\";\nimport \"./jsx\";\nimport \"./misc\";\nimport \"./experimental\";\nimport \"./typescript\";\nimport {\n  VISITOR_KEYS,\n  ALIAS_KEYS,\n  FLIPPED_ALIAS_KEYS,\n  NODE_FIELDS,\n  BUILDER_KEYS,\n  DEPRECATED_KEYS,\n  NODE_PARENT_VALIDATIONS,\n} from \"./utils\";\nimport {\n  PLACEHOLDERS,\n  PLACEHOLDERS_ALIAS,\n  PLACEHOLDERS_FLIPPED_ALIAS,\n} from \"./placeholders\";\nimport { DEPRECATED_ALIASES } from \"./deprecated-aliases\";\n\n(\n  Object.keys(DEPRECATED_ALIASES) as (keyof typeof DEPRECATED_ALIASES)[]\n).forEach(deprecatedAlias => {\n  FLIPPED_ALIAS_KEYS[deprecatedAlias] =\n    FLIPPED_ALIAS_KEYS[DEPRECATED_ALIASES[deprecatedAlias]];\n});\n\n// We do this here, because at this point the visitor keys should be ready and setup\ntoFastProperties(VISITOR_KEYS);\ntoFastProperties(ALIAS_KEYS);\ntoFastProperties(FLIPPED_ALIAS_KEYS);\ntoFastProperties(NODE_FIELDS);\ntoFastProperties(BUILDER_KEYS);\ntoFastProperties(DEPRECATED_KEYS);\n\ntoFastProperties(PLACEHOLDERS_ALIAS);\ntoFastProperties(PLACEHOLDERS_FLIPPED_ALIAS);\n\nconst TYPES: Array<string> = [].concat(\n  Object.keys(VISITOR_KEYS),\n  Object.keys(FLIPPED_ALIAS_KEYS),\n  Object.keys(DEPRECATED_KEYS),\n);\n\nexport {\n  VISITOR_KEYS,\n  ALIAS_KEYS,\n  FLIPPED_ALIAS_KEYS,\n  NODE_FIELDS,\n  BUILDER_KEYS,\n  DEPRECATED_ALIASES,\n  DEPRECATED_KEYS,\n  NODE_PARENT_VALIDATIONS,\n  PLACEHOLDERS,\n  PLACEHOLDERS_ALIAS,\n  PLACEHOLDERS_FLIPPED_ALIAS,\n  TYPES,\n};\n\nexport type { FieldOptions } from \"./utils\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,iBAAA,GAAAC,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AASA,IAAAE,aAAA,GAAAF,OAAA;AAKA,IAAAG,kBAAA,GAAAH,OAAA;AAGEI,MAAM,CAACC,IAAI,CAACC,qCAAkB,CAAC,CAC/BC,OAAO,CAACC,eAAe,IAAI;EAC3BC,yBAAkB,CAACD,eAAe,CAAC,GACjCC,yBAAkB,CAACH,qCAAkB,CAACE,eAAe,CAAC,CAAC;AAC3D,CAAC,CAAC;AAGFE,iBAAgB,CAACC,mBAAY,CAAC;AAC9BD,iBAAgB,CAACE,iBAAU,CAAC;AAC5BF,iBAAgB,CAACD,yBAAkB,CAAC;AACpCC,iBAAgB,CAACG,kBAAW,CAAC;AAC7BH,iBAAgB,CAACI,mBAAY,CAAC;AAC9BJ,iBAAgB,CAACK,sBAAe,CAAC;AAEjCL,iBAAgB,CAACM,gCAAkB,CAAC;AACpCN,iBAAgB,CAACO,wCAA0B,CAAC;AAE5C,MAAMC,KAAoB,GAAG,EAAE,CAACC,MAAM,CACpCf,MAAM,CAACC,IAAI,CAACM,mBAAY,CAAC,EACzBP,MAAM,CAACC,IAAI,CAACI,yBAAkB,CAAC,EAC/BL,MAAM,CAACC,IAAI,CAACU,sBAAe,CAC7B,CAAC;AAACK,OAAA,CAAAF,KAAA,GAAAA,KAAA"}