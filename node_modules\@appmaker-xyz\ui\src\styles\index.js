import { appSettings } from '@appmaker-xyz/core';

const fontFamilyConfig = {
  regular: appSettings.getExtensionOption(
    'custom-font',
    'ui_kit_custom_fonts_regular_name',
    'SFProDisplay-Regular',
  ),
  medium: appSettings.getExtensionOption(
    'custom-font',
    'ui_kit_custom_fonts_medium_name',
    'SFProDisplay-Medium',
  ),
  bold: appSettings.getExtensionOption(
    'custom-font',
    'ui_kit_custom_fonts_bold_name',
    'SFProDisplay-Bold',
  ),
};

const fontFamilySettings = appSettings.getExtensionOption(
  'app-branding',
  'font_family_group',
  {},
);

const lineHeightRatio = fontFamilySettings?.custom_line_height_ratio
  ? parseFloat(fontFamilySettings?.custom_line_height_ratio)
  : null;

const sizeConfig = {
  xs: 8,
  10: 10,
  sm: 12,
  base: 14,
  md: 16,
  lg: 18,
  xl: 24,
  '2xl': 28,
  '3xl': 32,
  '4xl': 36,
};

export const styles = {
  fontFamily: fontFamilyConfig,
  size: sizeConfig,
  lineHeightRatio,
};
