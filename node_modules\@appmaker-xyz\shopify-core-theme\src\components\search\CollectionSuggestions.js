import React from 'react';
import { FlatList, StyleSheet } from 'react-native';
import { Layout, ThemeText, AppImage, AppTouchable } from '@appmaker-xyz/ui';
import { Images } from '../../../assets/images';
import { ShopifyNavigation } from '@appmaker-xyz/shopify';

const Item = ({ item }) => {
  return (
    <AppTouchable
      style={styles.item}
      onPress={() =>
        ShopifyNavigation.openCollection({
          handle: item.handle,
          pageTitle: item.title,
        })
      }>
      <AppImage
        uri={item?.image?.url}
        src={Images.box}
        style={styles.image}
        resizeMode="cover"
      />
      <ThemeText color="#898989" size="sm" numberOfLines={1}>
        {item.title}
      </ThemeText>
    </AppTouchable>
  );
};

const CollectionSuggestions = ({ attributes }) => {
  const { title, collections = [] } = attributes;
  if (!collections?.length) {
    return null;
  }
  return (
    <Layout style={styles.container}>
      <ThemeText size="md" style={styles.title}>
        {title}
      </ThemeText>
      <FlatList
        data={collections}
        renderItem={({ item }) => <Item item={item} />}
        keyExtractor={(item) => item.title}
        showsHorizontalScrollIndicator={false}
        horizontal={true}
        contentContainerStyle={styles.contentContainerStyle}
      />
    </Layout>
  );
};

export default CollectionSuggestions;

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  title: {
    marginBottom: 8,
    marginLeft: 12,
  },
  contentContainerStyle: {
    paddingHorizontal: 8,
    flexGrow: 1,
  },
  item: {
    alignItems: 'center',
    paddingHorizontal: 4,
    maxWidth: 80,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: 4,
    overflow: 'hidden',
    borderWidth: 0.5,
    borderColor: '#EFEFEF',
  },
});
