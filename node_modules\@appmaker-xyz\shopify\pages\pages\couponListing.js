import { color, spacing } from '../styles';
const page = {
  title: 'Apply Coupon',
  attributes: {
    // headerShown: false,
    rootContainerStyle: {
      backgroundColor: color.white,
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      paddingHorizontal: spacing.base,
      paddingTop: spacing.base,
    },
  },
  blocks: [
    {
      name: 'appmaker/input',
      attributes: {
        label: 'Enter Coupon Code',
        leftIcon: 'gift',
        status: 'grey',
      },
    },
    {
      name: 'appmaker/button',
      attributes: {
        content: 'Apply',
        small: true,
        status: 'demiDark',
      },
    },
    {
      name: 'appmaker/blocksView',
      attributes: {
        headerShown: false,
        rootContainerStyle: {
          marginTop: spacing.base,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/text',
          attributes: {
            content: 'Available Coupons',
            category: 'bodyParagraph',
            status: 'demiDark',
          },
        },
        {
          name: 'appmaker/coupon-block',
          attributes: {},
        },
      ],
    },
  ],
};
export default page;
