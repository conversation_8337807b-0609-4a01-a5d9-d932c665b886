import { shouldForceUpdate } from '../lib/updateCheck';
describe('Android', () => {
  const platform = 'android';
  test('must force Update foceUpdate=>true', () => {
    const serverConfig = {
      force_update_android: true,
      android_minimum_app_version_required: '69',
    };
    const current_version_code = 68;
    const status = shouldForceUpdate({
      serverConfig,
      platform,
      current_version_code,
    });
    expect(status).toBe(true);
  });

  test('must force Update foceUpdate=>false', () => {
    const serverConfig = {
      force_update_android: false,
      android_minimum_app_version_required: '69',
    };
    const current_version_code = 68;
    const status = shouldForceUpdate({
      serverConfig,
      platform,
      current_version_code,
    });
    expect(status).toBe(false);
  });

  test('must not force Update', () => {
    const serverConfig = {
      force_update_android: true,
      android_minimum_app_version_required: '69',
    };
    const current_version_code = '69';
    const status = shouldForceUpdate({
      serverConfig,
      platform,
      current_version_code,
    });
    expect(status).toBe(false);
  });
});
describe('iOS', () => {
  const platform = 'ios';
  test('must force Update foceUpdate=>true', () => {
    const serverConfig = {
      force_update_ios: true,
      ios_minimum_app_version_required: '69',
    };
    const current_version_code = 68;
    const status = shouldForceUpdate({
      serverConfig,
      platform,
      current_version_code,
    });
    expect(status).toBe(true);
  });

  test('must force Update foceUpdate=>false', () => {
    const serverConfig = {
      force_update_ios: false,
      ios_minimum_app_version_required: '69',
    };
    const current_version_code = 68;
    const status = shouldForceUpdate({
      serverConfig,
      platform,
      current_version_code,
    });
    expect(status).toBe(false);
  });

  test('must not force Update', () => {
    const serverConfig = {
      force_update_ios: true,
      ios_minimum_app_version_required: '69',
    };
    const current_version_code = '69';
    const status = shouldForceUpdate({
      serverConfig,
      platform,
      current_version_code,
    });
    expect(status).toBe(false);
  });
});
