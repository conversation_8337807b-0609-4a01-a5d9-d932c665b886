import React from 'react';
import { View } from 'react-native';
import { useAppStorage } from '@appmaker-xyz/core';
import { ActionBar } from '@appmaker-xyz/uikit/src/components/molecules/index';
import { redirectLogin } from '../datasource/orderswebview';

function OrdersInWebview({ attributes, onAction }) {
  const [loading, setLoading] = React.useState(false);
  // const {orderId}
  const user = useAppStorage((state) => state.user);
  return (
    <View>
      <ActionBar
        attributes={{
          title: 'Orders',
          loading,
        }}
        onPress={async () => {
          setLoading(true);
          const response = await redirectLogin(user?.accessToken);
          response?.url &&
            onAction({
              action: 'OPEN_IN_WEB_VIEW',
              params: {
                title: 'Orders',
                url: response.url,
              },
            });
          setLoading(false);
        }}
      />
    </View>
  );
  // return <Text>Hello world = {JSON.stringify(attributes, null, 2)}</Text>;
}
export { OrdersInWebview };
