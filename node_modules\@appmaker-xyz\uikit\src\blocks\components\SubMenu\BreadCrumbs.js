import React from 'react';
import { StyleSheet } from 'react-native';
import { AppmakerText, Layout, AppTouchable } from '@appmaker-xyz/uikit';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';

// const items = [
//   {
//     title: 'One',
//     action: {},
//   },
//   {
//     title: 'Two',
//     action: {},
//   },
//   {
//     title: 'Three',
//     action: {},
//   },
// ];

export default function BreadCrumbs({ attributes, onPress, onAction }) {
  let onPressHandle = onPress;
  if (attributes.appmakerAction && onAction) {
    onPressHandle = () => onAction(attributes.appmakerAction);
  }
  const color = '#707070';
  const { items } = attributes;

  return items && items.length > 1 ? (
    <Layout style={styles.container}>
      {items?.map((item, index) => (
        <AppTouchable
          style={styles.container}
          key={index}
          onPress={() => {
            onPressHandle(item, index);
          }}>
          <Icon
            name="chevron-left"
            size={18}
            color={color}
            style={styles.icon}
          />
          <AppmakerText fontColor={color}>
            {item.title || item?.attributes?.text}
          </AppmakerText>
        </AppTouchable>
      ))}
    </Layout>
  ) : null;
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  icon: {
    paddingHorizontal: 4,
  },
});
