import React from 'react';
import {
  AppTouchable,
  AppmakerText,
  Layout,
  AppImage,
} from '@appmaker-xyz/uikit/src/components/index';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '../../../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';

const ExtraPaymentInfo = ({ attributes, onPress, onAction, clientId }) => {
  const { text, imageSrc, extraText, appmakerAction } = attributes;
  const { color, spacing, fonts, font } = useApThemeState();
  const styles = allStyles({
    color,
    spacing,
    fonts,
    font,
  });
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      onAction(appmakerAction);
    };
  }
  return (
    <AppTouchable onPress={onPressHandle} clientId={clientId}>
      <Layout style={styles.container}>
        <Layout style={styles.mainText}>
          <AppmakerText category="bodySubText">{text}</AppmakerText>
          {imageSrc != '' && (
            <AppImage
              uri={imageSrc}
              style={styles.image}
              resizeMode="contain"
            />
          )}
          <Icon name="info" size={spacing.base} color={color.demiDark} />
        </Layout>
        {extraText && (
          <Layout>
            <AppmakerText category="h1SubHeading" status="grey">
              {extraText}
            </AppmakerText>
          </Layout>
        )}
      </Layout>
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      paddingHorizontal: spacing.base,
      paddingVertical: spacing.nano,
      marginTop: -spacing.base,
    },
    mainText: {
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
      flex: 1,
    },
    image: {
      width: 60,
      height: 18,
      marginHorizontal: spacing.mini,
    },
  });

export default ExtraPaymentInfo;
