import React from 'react';
import {StyleSheet} from 'react-native';
import {useApThemeState} from '../../../../../theme/ThemeContext';
import {Layout, AppmakerText, Button} from '../../../../../components';

const LinkCloud = ({attributes}) => {
  const {linkCloud} = attributes;
  const {spacing} = useApThemeState();
  const styles = allStyles({spacing});
  return (
    <Layout style={styles.linkCloudContainer}>
      {linkCloud.map((item, key) => {
        return (
          <>
            <Button
              link
              status="primary"
              small
              onPress={() => console.log('Goto')}>
              {item.category}
            </Button>
            <AppmakerText category="smallButtonText" status="primary">
              {', '}
            </AppmakerText>
          </>
        );
      })}
    </Layout>
  );
};

const allStyles = ({spacing}) =>
  StyleSheet.create({
    linkCloudContainer: {
      paddingHorizontal: spacing.base,
      paddingBottom: spacing.base,
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
  });

export default LinkCloud;
