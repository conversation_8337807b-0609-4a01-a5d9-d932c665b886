{"version": 3, "names": ["_definitions", "require", "isNode", "node", "VISITOR_KEYS", "type"], "sources": ["../../src/validators/isNode.ts"], "sourcesContent": ["import { VISITOR_KEYS } from \"../definitions\";\nimport type * as t from \"..\";\n\nexport default function isNode(node: any): node is t.Node {\n  return !!(node && VISITOR_KEYS[node.type]);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAGe,SAASC,MAAMA,CAACC,IAAS,EAAkB;EACxD,OAAO,CAAC,EAAEA,IAAI,IAAIC,yBAAY,CAACD,IAAI,CAACE,IAAI,CAAC,CAAC;AAC5C"}