import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '../../../../theme/ThemeContext';
import {
  Layout,
  AppmakerText,
  AppTouchable,
  Badge,
  Button,
  TableCell,
} from '../../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import Modal from 'react-native-modal';
import { appmaker } from '@appmaker-xyz/core';

const ProductData = ({ attributes, clientId, ...props }) => {
  // console.log(attributes);
  const {
    priceSale = '0',
    in_stock,
    title,
    regularPrice,
    salePrice,
    onSale,
    salePercentage,
    currency_symbol,
    regular_price_value,
    price_value,
    vendorName,
    stockPhrase,
    average_rating,
    saved_amount,
    priceDetailModal = true,
    customStyles,
    tax_included_text = false,
    brandName,
    __appmakerCustomStyles = {},
  } = attributes;
  const { color, spacing, font } = useApThemeState();
  const styles = allStyles({ color, spacing });
  const [visible, setVisible] = useState(false);

  let toggle = () => setVisible(!visible);
  const ratingBadgeColor = () => {
    let rating = average_rating;
    switch (true) {
      case rating >= '3.5':
        return 'success';
      case rating >= '2.5':
        return 'warining';
      default:
        return 'dark';
    }
  };
  // var saved_amount = Math.abs((regular_price_value - price_value).toFixed(2));
  const canShowSavedText =
    regular_price_value &&
    price_value &&
    saved_amount != 0 &&
    saved_amount != '';

  const savedInitialText = 'Total savings for this product is';
  const displayPriceDetailModal =
    priceDetailModal && priceDetailModal != 'false';
  const savedAmountText = ` ${saved_amount} (${salePercentage}) `;
  const ReviewsComp = appmaker.applyFilters(
    'appmaker-product-data-reviews',
    () => null,
  );
  return (
    <Layout style={styles.productDetails}>
      <Layout style={styles.metaData}>
        <Layout flexDirection="row" alignItems="center">
          {in_stock != 'hide' ? (
            <>
              {brandName ? (
                <AppmakerText
                  category="smallButtonText"
                  status="demiDark"
                  style={styles.brandName}>
                  {brandName}
                </AppmakerText>
              ) : null}
              <AppmakerText
                clientId={`${clientId}-stock-text`}
                category="bodyParagraph"
                fontColor={
                  customStyles?.brandColor?.primary ||
                  __appmakerCustomStyles?.product_data?.out_of_stock?.color
                }
                status={in_stock === 'false' ? 'danger' : 'success'}>
                {in_stock === 'false' ? 'Out of Stock' : 'In-Stock'}
              </AppmakerText>
            </>
          ) : null}
          {stockPhrase ? (
            <AppmakerText status="danger" category="highlighter1">
              ({stockPhrase})
            </AppmakerText>
          ) : null}
        </Layout>
        {ReviewsComp ? (
          <ReviewsComp
            from="product-data"
            pageDispatch={props?.pageDispatch}
            attributes={attributes}
          />
        ) : null}

        {average_rating && average_rating > 0 ? (
          <Badge
            text={average_rating}
            status={ratingBadgeColor()}
            customStyles={
              customStyles?.reviewBadgeStyle ||
              __appmakerCustomStyles?.product_data?.rating_badge
            }
            iconName="star"
          />
        ) : null}
      </Layout>
      <AppmakerText
        clientId={`${clientId}-product-name`}
        category="pageSubHeading"
        style={styles.title}>
        {title}
      </AppmakerText>
      <AppTouchable
        style={styles.priceData}
        onPress={() => onSale && displayPriceDetailModal && toggle()}>
        <AppmakerText clientId={`${clientId}-sale-price`} category="h1Heading">
          {salePrice}
        </AppmakerText>
        {onSale && !!priceSale ? (
          <AppmakerText
            clientId={`${clientId}-regular-price`}
            category="bodySubText"
            style={styles.strikePrice}
            fontColor={
              __appmakerCustomStyles?.product_data?.strike_price?.color
            }
            status="grey">
            {regularPrice}
          </AppmakerText>
        ) : null}
        {onSale ? (
          <AppmakerText
            clientId={`${clientId}-sale-percentage`}
            category="bodySubText"
            fontColor={
              (customStyles ? customStyles?.brandColor?.secondary : null) ||
              __appmakerCustomStyles?.product_data?.offer_percent?.color
            }
            status="success"
            style={styles.offer}>
            {salePercentage}{' '}
            {displayPriceDetailModal ? (
              <Icon
                name="info"
                style={__appmakerCustomStyles?.product_data?.more_info_icon}
              />
            ) : null}
          </AppmakerText>
        ) : null}
      </AppTouchable>
      {tax_included_text && tax_included_text !== '' ? (
        <AppmakerText category="highlighter2" status="grey">
          {tax_included_text}
        </AppmakerText>
      ) : null}
      <Modal
        isVisible={visible}
        onSwipeComplete={toggle}
        onBackButtonPress={toggle}
        backdropTransitionOutTiming={0}
        onBackdropPress={toggle}
        style={styles.modal}>
        <Layout style={styles.modalBody}>
          <Layout style={styles.modalHeader}>
            <Layout>
              <AppmakerText category="actionTitle" status="dark">
                Price details
              </AppmakerText>
            </Layout>
            <Button
              onPress={toggle}
              status="white"
              accessoryLeft={() => <Icon name="x" size={font.size.lg} />}
              small
            />
          </Layout>
          <Layout style={styles.modalContent}>
            {regularPrice ? (
              <TableCell
                attributes={{
                  title: 'Maximum Retail Price',
                  subTitle: 'Including all Taxes',
                  value: regularPrice,
                }}
              />
            ) : null}
            {onSale ? (
              <TableCell
                attributes={{ title: 'Selling Price', value: salePrice }}
              />
            ) : null}
            {regularPrice ? (
              <AppmakerText
                status="success"
                category="h1SubHeading"
                style={styles.savedPriceData}
                fontColor={
                  __appmakerCustomStyles?.product_data?.offer_percent?.color
                }>
                {savedInitialText}
                <AppmakerText
                  status="success"
                  category="h1SubHeading"
                  style={styles.savedPriceData}
                  fontColor={
                    __appmakerCustomStyles?.product_data?.offer_percent?.color
                  }>
                  {savedAmountText}
                </AppmakerText>
              </AppmakerText>
            ) : null}
          </Layout>
        </Layout>
      </Modal>
      {vendorName && vendorName !== '' ? (
        <AppmakerText status="grey" category="bodySubText">
          Sold By:{' '}
          <AppmakerText status="primary" category="smallButtonText">
            {vendorName}
          </AppmakerText>
        </AppmakerText>
      ) : null}
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    productDetails: {
      padding: spacing.base,
      backgroundColor: color.white,
    },
    metaData: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    title: {
      marginTop: spacing.mini,
      marginBottom: spacing.small,
    },
    priceData: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.small,
    },
    strikePrice: {
      marginHorizontal: spacing.nano,
      textDecorationLine: 'line-through',
    },
    offer: {
      marginHorizontal: spacing.nano,
    },
    modal: {
      justifyContent: 'flex-end',
      marginVertical: 0,
      marginHorizontal: 1,
    },
    modalBody: {
      backgroundColor: color.white,
      padding: spacing.md,
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      borderTopStartRadius: spacing.md,
      borderTopEndRadius: spacing.md,
      flexDirection: 'column',
      maxHeight: '80%',
    },
    modalHeader: {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderBottomWidth: 1,
      borderBottomColor: color.light,
      paddingBottom: spacing.base,
    },
    modalContent: {
      paddingVertical: spacing.base,
      flexDirection: 'column',
      width: '100%',
    },
    savedPriceData: {
      textAlign: 'center',
      marginTop: spacing.base,
    },
    brandName: {
      marginRight: spacing.mini,
      paddingRight: spacing.mini,
      borderRightWidth: 1,
      borderRightColor: color.light,
    },
  });

export default ProductData;
