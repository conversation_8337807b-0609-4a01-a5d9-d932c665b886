import React from 'react';
import { SocialLogin } from '@appmaker-xyz/uikit/src/components/pages/userReg/components/index';

const SocialLoginHelper = ({ onAction, attributes }) => {
  const { apple, google, facebook, __appmakerCustomStyles = {} } = attributes;
  const [loading, setLoading] = React.useState(false);
  const onPress = async (platform) => {
    setLoading(true);
    let action = '';
    if (platform == 'google') {
      action = 'LOGIN_USER_VIA_GOOGLE';
    } else if (platform == 'apple') {
      action = 'LOGIN_USER_VIA_APPLE';
    } else if (platform == 'facebook') {
      action = 'LOGIN_USER_VIA_FACEBOOK';
    }
    try {
      await onAction({
        action,
      });
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  return (
    <SocialLogin
      loading={loading}
      google={google}
      apple={apple}
      facebook={facebook}
      appleOnPress={() => {
        onPress('apple');
      }}
      googleOnPress={() => {
        onPress('google');
      }}
      facebookOnPress={() => {
        onPress('facebook');
      }}
      __appmakerCustomStyles={__appmakerCustomStyles}
    />
  );
};

export default SocialLoginHelper;
