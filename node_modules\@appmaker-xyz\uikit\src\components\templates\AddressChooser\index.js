import React, { useState, useEffect } from 'react';
import { StyleSheet } from 'react-native';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import {
  Layout,
  AppmakerText,
  BlockCard,
  AppTouchable,
  DropDown,
} from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import { Button } from '../../molecules/index';
import { usePageState } from '@appmaker-xyz/core';

const AddressChooser = ({
  attributes = {},
  onPress,
  onEditPress,
  onAction,
  coreDispatch,
}) => {
  const {
    defaultAddressId,
    id,
    firstName,
    lastName,
    address1,
    address2,
    city,
    province,
    company,
    country,
    phone,
    zip,
    formatted,
    hideDeliverToAddressButton,
    appmakerAction,
    appmakerEditAction,
  } = attributes;
  // const [active, setActive] = useState(false);
  const activeItem =
    usePageState((state) => state.__currentAddress) || defaultAddressId;

  const { color, spacing } = useApThemeState();
  const active = activeItem === id;
  const styles = allStyles({ color, spacing, active });
  const [deliverAddressloading, setDeliverAddressLoading] = useState(false);
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = async (setDefault) => {
      let finalAppmakerAction = appmakerAction;
      if (setDefault) {
        finalAppmakerAction.params.setDefault = setDefault;
      }
      setDeliverAddressLoading(true);
      await onAction(finalAppmakerAction);
      setDeliverAddressLoading(false);
    };
  }
  let onPressEditHandle = onEditPress;
  if (appmakerAction && onAction) {
    onPressEditHandle = () => onAction(appmakerEditAction);
  }
  // console.log();
  return (
    <AppTouchable
      style={styles.container}
      onPress={() => {
        coreDispatch({
          type: 'SET_VALUE',
          name: '__currentAddress',
          value: id,
        });
      }}>
      {firstName || lastName ? (
        <AppmakerText category="h1Heading">
          {firstName} {lastName}
        </AppmakerText>
      ) : null}
      {address1 ? <AppmakerText>{address1}</AppmakerText> : null}
      {address2 ? <AppmakerText>{address2}</AppmakerText> : null}
      <AppmakerText>
        {city},{province},{country}-{zip}
      </AppmakerText>
      <Layout style={styles.radioIcon}>
        <Layout style={styles.icon} />
      </Layout>
      {active ? (
        <Layout style={styles.deliverButton}>
          <Layout style={styles.editAddress}>
            <Button link small status="primary" onPress={onPressEditHandle}>
              Edit
            </Button>
          </Layout>
          {hideDeliverToAddressButton == true ? (
            <Button
              small
              onPress={() => onPressHandle(false)}
              loading={deliverAddressloading}>
              Deliver to this address
            </Button>
          ) : null}
          <Button
            small
            onPress={() => onPressHandle(true)}
            loading={deliverAddressloading}>
            Set as default
          </Button>
        </Layout>
      ) : null}
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color, active }) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: spacing.base,
      paddingVertical: spacing.md,
      backgroundColor: active ? `${color.primary}1A` : `${color.light}33`,
      borderBottomColor: color.light,
      borderBottomWidth: 1,
      position: 'relative',
    },
    radioIcon: {
      borderRadius: spacing.base,
      borderWidth: 2,
      borderColor: active ? color.primary : color.grey,
      position: 'absolute',
      right: 18,
      top: 18,
    },
    icon: {
      height: spacing.base,
      width: spacing.base,
      backgroundColor: active ? color.primary : color.grey,
      borderRadius: spacing.mini,
      margin: 2,
    },
    editAddress: {
      paddingVertical: spacing.base,
      flexDirection: 'row',
    },
  });

export default AddressChooser;
