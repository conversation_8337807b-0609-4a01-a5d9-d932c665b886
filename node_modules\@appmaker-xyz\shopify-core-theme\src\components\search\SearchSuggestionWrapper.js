import React from 'react';
import { View, Text } from 'react-native';
import SearchSuggestions from './SearchSuggestions';
import { usePageState } from '@appmaker-xyz/core';
import { useSearchQuery, useSearchResult } from '@appmaker-xyz/shopify';

const SearchSuggestionWrapper = (props) => {
  const { predictiveSearchResult: predictiveResults } = useSearchResult();
  const { setSearchQuery } = useSearchQuery();
  return (
    <View>
      <SearchSuggestions
        setValue={setSearchQuery}
        queries={predictiveResults?.queries}
        products={predictiveResults?.products}
        {...props}
      />
    </View>
  );
};

export default SearchSuggestionWrapper;
