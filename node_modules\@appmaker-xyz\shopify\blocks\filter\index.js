import React from 'react';
import FilterModal from './FilterModal';
import FilterButton from './components/FilterButton';
import SortButton from '../sort/components/SortButton';
import SortModal from '../sort/SortModal';
import { View, StyleSheet } from 'react-native';
import ShopifyFilterProviderWrapper from './FilterProviderWrapper';
import { getExtensionAsBoolean, getExtensionConfig } from '@appmaker-xyz/core';

export function Filter(props) {
  const { attributes } = props;
  const {
    filterButtonStyle,
    sortButtonStyle,
    hideButtons = false,
    disableFilterAndSort = false,
  } = attributes;
  const extensionStyles = getExtensionConfig?.(
    'shopify',
    'sortFilterButtonGroup',
    {},
  );
  if (disableFilterAndSort === true) {
    return null;
  }
  return (
    <ShopifyFilterProviderWrapper {...props}>
      {hideButtons ? null : (
        <View
          style={[
            styles.container,
            extensionStyles?.sortFilterButtonsContainer,
          ]}>
          <FilterButton
            attributes={{
              customStyles: filterButtonStyle,
            }}
          />
          <View style={[styles.separator, extensionStyles?.separatorStyle]} />
          <SortButton
            attributes={{
              customStyles: sortButtonStyle,
            }}
          />
        </View>
      )}
      <SortModal />
      <FilterModal />
    </ShopifyFilterProviderWrapper>
  );
}
export default function ShopifySortFilter({
  attributes,
  BlockItem,
  onAction,
  currentAction,
  BlocksView,
  innerBlocks,
  blockData,
}) {
  const {
    shouldRunDataSourceByDefault = false,
    collectionHandle,
    collectionId,
  } = attributes;
  const hideSortFilter = getExtensionAsBoolean?.(
    'shopify',
    'hide_sort_filter',
    false,
  );
  const dataSourceParams =
    collectionHandle || collectionId
      ? {
          ...(collectionHandle && { collectionHandle }),
          ...(collectionId && { collectionId }),
        }
      : '{{blockData.sub_collection || currentAction.params}}';
  return hideSortFilter ? null : (
    <BlockItem
      BlockItem={BlockItem}
      BlocksView={BlocksView}
      currentAction={currentAction}
      onAction={onAction}
      block={{
        name: 'shopify/collection-filter',
        innerBlocks,
        clientId: 'product-list',
        isValid: true,
        attributes: {
          viewSingle: true,
          show_filter: true, // themeHelper
          show_sort: true, // themeHelper
          filter_data: '{{blockItem}}',
          sort_default: '{{pageState.sort}}',
          filter_default: '{{pageState.filter}}',
          defaultFilterItem: { app_filter: {} },
          dataSource: {
            responseType: 'replace',
            source: 'shopify',
            attributes: {
              runDefault: shouldRunDataSourceByDefault,
              runWhen: ['pageState', 'dataSourceResponse', 'productList'],
              mapping: {
                items: 'data.data.collection.products.filters',
              },
              methodName: 'productFilters',
              params: dataSourceParams,
            },
            repeatable: 'Yes',
            repeatItem: 'DataSource',
            dependencies: {
              pageState: ['searchQuery', 'dataSourceResponse', 'blockData'],
            },
          },
          ...attributes,
        },
      }}
    />
  );
}
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  separator: {
    width: 1,
    backgroundColor: '#fff',
  },
});
