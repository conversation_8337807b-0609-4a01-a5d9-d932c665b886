import AutoHeightWebView from 'react-native-autoheight-webview';

import { Dimensions } from 'react-native';

import React from 'react';

export default function AutoHeightWebViewBlock({ attributes = {} }) {
  const { source } = attributes;
  return source ? (
    <AutoHeightWebView
      style={{ width: Dimensions.get('window').width - 15, marginTop: 35 }}
      onSizeUpdated={(size) => console.log(size.height)}
      source={source}
      scalesPageToFit={true}
      viewportContent={'width=device-width, user-scalable=no'}
    />
  ) : null;
}
