fragment cartResponse on Cart {
  id
  checkoutUrl
  totalQuantity
  estimatedCost {
    checkoutChargeAmount {
      amount
      currencyCode
    }
    subtotalAmount {
      amount
      currencyCode
    }
    totalAmount {
      amount
      currencyCode
    }
    totalDutyAmount {
      amount
      currencyCode
    }
    totalTaxAmount {
      amount
      currencyCode
    }
  }
  attributes {
    value
    key
  }
  lines(first: 250) {
    nodes {
      id
      attributes {
        key
        value
      }
      cost {
        amountPerQuantity {
          amount
          currencyCode
        }
        compareAtAmountPerQuantity {
          amount
          currencyCode
        }
        subtotalAmount {
          amount
          currencyCode
        }
        totalAmount {
          amount
          currencyCode
        }
      }
      discountAllocations {
        discountedAmount {
          amount
          currencyCode
        }
        ... on CartAutomaticDiscountAllocation {
          __typename
          discountedAmount {
            amount
            currencyCode
          }
          title
        }
        ... on CartCodeDiscountAllocation {
          __typename
          code
          discountedAmount {
            amount
            currencyCode
          }
        }
        ... on CartCustomDiscountAllocation {
          __typename
          discountedAmount {
            amount
            currencyCode
          }
          title
        }
      }
      estimatedCost {
        amount {
          amount
          currencyCode
        }
        compareAtAmount {
          amount
          currencyCode
        }
        subtotalAmount {
          amount
          currencyCode
        }
        totalAmount {
          amount
          currencyCode
        }
      }
      id
      quantity
      merchandise {
        ... on ProductVariant {
          ...CartProductVariantFragment
          product {
            ...SimpleProductFragment
          }
        }
      }
    }
  }
  discountAllocations {
    discountedAmount {
      amount
      currencyCode
    }
    ... on CartAutomaticDiscountAllocation {
      __typename
      discountedAmount {
        currencyCode
        amount
      }
      title
    }
    ... on CartCodeDiscountAllocation {
      __typename
      code
      discountedAmount {
        amount
        currencyCode
      }
    }
    ... on CartCustomDiscountAllocation {
      __typename
      discountedAmount {
        amount
        currencyCode
      }
      title
    }
  }
  buyerIdentity {
    countryCode
    customer {
      email
      firstName
      id
    }
    # deliveryAddressPreferences {
    #   # id
    #   ... on MailingAddress {
    #     id
    #     name
    #     lastName
    #     latitude
    #     longitude
    #     phone
    #     province
    #   }
    # }
    email
    phone
    # walletPreferences
  }
}

fragment CartProductVariantFragment on ProductVariant {
  availableForSale
  quantityAvailable
  sku
  id
  title
  selectedOptions {
    name
    value
  }
  image {
    thumbnail: url(transform: { maxWidth: 200 })
  }
  price {
    amount
    currencyCode
  }
  compareAtPrice {
    currencyCode
    amount
  }
}
