let pageDataList = {
  name: 'appmaker/upsell-product-container',
  clientId: 'upsell-cart',
  attributes: {
    limit: '{{plugins[`appmaker-upsell`].settings.num_of_product_cart}}',
    __display: '{{blockData.lineItems.edges.length > 0 ? true : false}}',
  },
};

let pageDataScroll = {
  name: 'appmaker/upsell-product-container-general-ui',
  clientId: 'upsell-cart',
  attributes: {
    limit: '{{plugins[`appmaker-upsell`].settings.num_of_product_cart}}',
    title: 'Frequently bought together',
    __display: '{{blockData.lineItems.edges.length > 0 ? true : false}}',
    //__display: '{{blockData.lineItems.edges.length > 0 ? true : false}}',
  },
};
export { pageDataScroll, pageDataList };
