/**
 * Post list Template View for inAppView
 */
import PropTypes from 'prop-types';
import React from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  ImageBackground,
  Text,
  TouchableOpacity,
} from 'react-native';
// import AppTouchable from '../../../common/AppTouchable';
// import { shadeColor } from '../../../common/Utils';

const { width } = Dimensions.get('window');

// Image height and width
// const imageWidth = width - 20;
const imageHeight = 250;

// Right and left margin for container. so total margin will be 20
const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    flex: 1,
    justifyContent: 'center',
    flexDirection: 'row',
    borderRadius: 5,
  },
  image: {
    marginBottom: 0,
    borderRadius: 5,
    height: imageHeight,
    width,
    justifyContent: 'flex-end',
  },
  title: {
    color: '#ffffff',
    elevation: 2,
    fontFamily: 'Lato-Bold',
    fontSize: 24,
    marginHorizontal: 5,
    marginVertical: 8,
  },
  description: {
    color: '#99999c',
    fontSize: 13,
    fontFamily: 'Lato-Regular',
    marginTop: 5,
  },
  author: {
    color: '#000000',
    fontFamily: 'Lato-Light',
    marginTop: 5,
    fontSize: 10,
    alignItems: 'flex-end',
  },
  textContainer: {
    // width of the whole text container will be Screen width minus image width and margin
    // width: width - (imageWidth + marginLeftTextContainer + (horizontalMargin * 2)),
    flexDirection: 'column',
    justifyContent: 'center',
  },
  descriptionNoImage: {
    color: '#99999c',
    fontSize: 15,
    fontFamily: 'Lato-Regular',
    marginTop: 10,
  },
  titleContainer: {
    width,
    height: imageHeight,
    backgroundColor: 'rgba(52, 52, 52, 0.3)',
    borderRadius: 6,
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 10,
    paddingBottom: 30,
  },
});

/**
 * Template Two for Post list item with Banner type image
 * @param { title, data, onClick } props.
 */

const ListTemplateTwo = (props) => {
  const item = props.data;
  const titleStyle = props.data.styles ? props.data.styles.titleStyle : {};
  return (
    <TouchableOpacity
      //testID={GLOBAL.TEST_ID.POST_LIST_TEMPLATE_TWO}
      onPress={() => props.onClick(props.data.action)}>
      <View style={styles.container}>
        {item.image && (
          <ImageBackground style={styles.image} source={{ uri: item.image }}>
            <View style={styles.titleContainer}>
              <Text
                allowFontScaling={false}
                style={[styles.title, titleStyle]}
                numberOfLines={3}>
                {props.title}
              </Text>
            </View>
          </ImageBackground>
        )}
      </View>
    </TouchableOpacity>
  );
};
ListTemplateTwo.propTypes = {
  data: PropTypes.any.isRequired,
  title: PropTypes.string,
  onClick: PropTypes.func,
  parentWidth: PropTypes.number,
};

ListTemplateTwo.defaultProps = {};

export default ListTemplateTwo;
