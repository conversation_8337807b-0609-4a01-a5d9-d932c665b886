import { SearchBar } from '@appmaker-xyz/uikit';
import React from 'react';
import { emitEvent, analytics } from '@appmaker-xyz/core';
import { useSearch } from '../../hooks/search/index';

export default function SearchBarBlock({ attributes, onAction, coreDispatch }) {
  const { setQuery, suggestLoading } = useSearch();
  return (
    <SearchBar
      searchLoading={suggestLoading}
      label={attributes.label}
      topBarView={attributes.topBarView}
      onBack={() => onAction({ action: 'GO_BACK' })}
      debounceInterval={attributes.debounceInterval}
      debounceOnChange={setQuery}
      onPress={async (searchQuery) => {
        analytics.track('product_search', { query: searchQuery });
        emitEvent('product.search', searchQuery);
        onAction({
          ...attributes.appmakerAction,
          params: {
            ...attributes.appmakerAction?.params,
            title: searchQuery || '',
            searchQuery,
          },
        });
      }}
    />
  );
}
