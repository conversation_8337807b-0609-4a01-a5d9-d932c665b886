{"version": 3, "names": ["SCOPE_OTHER", "SCOPE_PROGRAM", "SCOPE_FUNCTION", "SCOPE_ARROW", "SCOPE_SIMPLE_CATCH", "SCOPE_SUPER", "SCOPE_DIRECT_SUPER", "SCOPE_CLASS", "SCOPE_STATIC_BLOCK", "SCOPE_TS_MODULE", "SCOPE_VAR", "exports", "BIND_KIND_VALUE", "BIND_KIND_TYPE", "BIND_SCOPE_VAR", "BIND_SCOPE_LEXICAL", "BIND_SCOPE_FUNCTION", "BIND_SCOPE_OUTSIDE", "BIND_FLAGS_NONE", "BIND_FLAGS_CLASS", "BIND_FLAGS_TS_ENUM", "BIND_FLAGS_TS_CONST_ENUM", "BIND_FLAGS_TS_EXPORT_ONLY", "BIND_FLAGS_FLOW_DECLARE_FN", "BIND_FLAGS_TS_IMPORT", "BIND_FLAGS_NO_LET_IN_LEXICAL", "BIND_CLASS", "BIND_LEXICAL", "BIND_CATCH_PARAM", "BIND_VAR", "BIND_FUNCTION", "BIND_TS_INTERFACE", "BIND_TS_TYPE", "BIND_TS_ENUM", "BIND_TS_AMBIENT", "BIND_NONE", "BIND_OUTSIDE", "BIND_TS_CONST_ENUM", "BIND_TS_NAMESPACE", "BIND_TS_TYPE_IMPORT", "BIND_FLOW_DECLARE_FN", "CLASS_ELEMENT_FLAG_STATIC", "CLASS_ELEMENT_KIND_GETTER", "CLASS_ELEMENT_KIND_SETTER", "CLASS_ELEMENT_KIND_ACCESSOR", "CLASS_ELEMENT_STATIC_GETTER", "CLASS_ELEMENT_STATIC_SETTER", "CLASS_ELEMENT_INSTANCE_GETTER", "CLASS_ELEMENT_INSTANCE_SETTER", "CLASS_ELEMENT_OTHER"], "sources": ["../../src/util/scopeflags.ts"], "sourcesContent": ["// Each scope gets a bitset that may contain these flags\n// prettier-ignore\nexport const SCOPE_OTHER        = 0b000000000,\n             SCOPE_PROGRAM      = 0b000000001,\n             SCOPE_FUNCTION     = 0b000000010,\n             SCOPE_ARROW        = 0b000000100,\n             SCOPE_SIMPLE_CATCH = 0b000001000,\n             SCOPE_SUPER        = 0b000010000,\n             SCOPE_DIRECT_SUPER = 0b000100000,\n             SCOPE_CLASS        = 0b001000000,\n             SCOPE_STATIC_BLOCK = 0b010000000,\n             SCOPE_TS_MODULE    = 0b100000000,\n             SCOPE_VAR = SCOPE_PROGRAM | SCOPE_FUNCTION | SCOPE_STATIC_BLOCK | SCOPE_TS_MODULE;\n\nexport type ScopeFlags =\n  | typeof SCOPE_OTHER\n  | typeof SCOPE_PROGRAM\n  | typeof SCOPE_FUNCTION\n  | typeof SCOPE_VAR\n  | typeof SCOPE_ARROW\n  | typeof SCOPE_SIMPLE_CATCH\n  | typeof SCOPE_SUPER\n  | typeof SCOPE_DIRECT_SUPER\n  | typeof SCOPE_CLASS\n  | typeof SCOPE_STATIC_BLOCK;\n\n// These flags are meant to be _only_ used inside the Scope class (or subclasses).\n// prettier-ignore\nexport const BIND_KIND_VALUE            = 0b0000000_0000_01,\n             BIND_KIND_TYPE             = 0b0000000_0000_10,\n             // Used in checkLVal and declareName to determine the type of a binding\n             BIND_SCOPE_VAR             = 0b0000000_0001_00, // Var-style binding\n             BIND_SCOPE_LEXICAL         = 0b0000000_0010_00, // Let- or const-style binding\n             BIND_SCOPE_FUNCTION        = 0b0000000_0100_00, // Function declaration\n             BIND_SCOPE_OUTSIDE         = 0b0000000_1000_00, // Special case for function names as\n                                                   // bound inside the function\n             // Misc flags\n             BIND_FLAGS_NONE            = 0b00000001_0000_00,\n             BIND_FLAGS_CLASS           = 0b00000010_0000_00,\n             BIND_FLAGS_TS_ENUM         = 0b00000100_0000_00,\n             BIND_FLAGS_TS_CONST_ENUM   = 0b00001000_0000_00,\n             BIND_FLAGS_TS_EXPORT_ONLY  = 0b00010000_0000_00,\n             BIND_FLAGS_FLOW_DECLARE_FN = 0b00100000_0000_00,\n             BIND_FLAGS_TS_IMPORT       = 0b01000000_0000_00,\n             // Whether \"let\" should be allowed in bound names in sloppy mode\n             BIND_FLAGS_NO_LET_IN_LEXICAL = 0b10000000_0000_00;\n\n// These flags are meant to be _only_ used by Scope consumers\n// prettier-ignore\n/*                              =    is value?    |    is type?    |      scope          |    misc flags    */\nexport const BIND_CLASS         = BIND_KIND_VALUE | BIND_KIND_TYPE | BIND_SCOPE_LEXICAL  | BIND_FLAGS_CLASS|BIND_FLAGS_NO_LET_IN_LEXICAL,\n             BIND_LEXICAL       = BIND_KIND_VALUE | 0              | BIND_SCOPE_LEXICAL  | BIND_FLAGS_NO_LET_IN_LEXICAL,\n             BIND_CATCH_PARAM   = BIND_KIND_VALUE | 0              | BIND_SCOPE_LEXICAL  | 0                 ,\n             BIND_VAR           = BIND_KIND_VALUE | 0              | BIND_SCOPE_VAR      | 0                 ,\n             BIND_FUNCTION      = BIND_KIND_VALUE | 0              | BIND_SCOPE_FUNCTION | 0                 ,\n             BIND_TS_INTERFACE  = 0               | BIND_KIND_TYPE | 0                   | BIND_FLAGS_CLASS  ,\n             BIND_TS_TYPE       = 0               | BIND_KIND_TYPE | 0                   | 0                 ,\n             BIND_TS_ENUM       = BIND_KIND_VALUE | BIND_KIND_TYPE | BIND_SCOPE_LEXICAL  | BIND_FLAGS_TS_ENUM|BIND_FLAGS_NO_LET_IN_LEXICAL,\n             BIND_TS_AMBIENT    = 0               | 0              | 0            | BIND_FLAGS_TS_EXPORT_ONLY,\n             // These bindings don't introduce anything in the scope. They are used for assignments and\n             // function expressions IDs.\n             BIND_NONE          = 0               | 0              | 0                   | BIND_FLAGS_NONE          ,\n             BIND_OUTSIDE       = BIND_KIND_VALUE | 0              | 0                   | BIND_FLAGS_NONE          ,\n\n             BIND_TS_CONST_ENUM = BIND_TS_ENUM    | BIND_FLAGS_TS_CONST_ENUM                                        ,\n             BIND_TS_NAMESPACE  = 0               | 0              | 0                   | BIND_FLAGS_TS_EXPORT_ONLY,\n             BIND_TS_TYPE_IMPORT= 0               | BIND_KIND_TYPE | 0                   | BIND_FLAGS_TS_IMPORT     ,\n\n             BIND_FLOW_DECLARE_FN = BIND_FLAGS_FLOW_DECLARE_FN;\n\nexport type BindingTypes =\n  | typeof BIND_NONE\n  | typeof BIND_OUTSIDE\n  | typeof BIND_VAR\n  | typeof BIND_LEXICAL\n  | typeof BIND_CLASS\n  | typeof BIND_FUNCTION\n  | typeof BIND_TS_INTERFACE\n  | typeof BIND_TS_TYPE\n  | typeof BIND_TS_ENUM\n  | typeof BIND_TS_AMBIENT\n  | typeof BIND_TS_NAMESPACE;\n\n// prettier-ignore\nexport const CLASS_ELEMENT_FLAG_STATIC = 0b1_00,\n             CLASS_ELEMENT_KIND_GETTER = 0b0_10,\n             CLASS_ELEMENT_KIND_SETTER = 0b0_01,\n             CLASS_ELEMENT_KIND_ACCESSOR = CLASS_ELEMENT_KIND_GETTER | CLASS_ELEMENT_KIND_SETTER;\n\n// prettier-ignore\nexport const CLASS_ELEMENT_STATIC_GETTER   = CLASS_ELEMENT_KIND_GETTER | CLASS_ELEMENT_FLAG_STATIC,\n             CLASS_ELEMENT_STATIC_SETTER   = CLASS_ELEMENT_KIND_SETTER | CLASS_ELEMENT_FLAG_STATIC,\n             CLASS_ELEMENT_INSTANCE_GETTER = CLASS_ELEMENT_KIND_GETTER,\n             CLASS_ELEMENT_INSTANCE_SETTER = CLASS_ELEMENT_KIND_SETTER,\n             CLASS_ELEMENT_OTHER           = 0;\n\nexport type ClassElementTypes =\n  | typeof CLASS_ELEMENT_STATIC_GETTER\n  | typeof CLASS_ELEMENT_STATIC_SETTER\n  | typeof CLASS_ELEMENT_INSTANCE_GETTER\n  | typeof CLASS_ELEMENT_INSTANCE_SETTER\n  | typeof CLASS_ELEMENT_OTHER;\n"], "mappings": ";;;;;;AAEO,MAAMA,WAAW,GAAU,WAAW;EAChCC,aAAa,GAAQ,WAAW;EAChCC,cAAc,GAAO,WAAW;EAChCC,WAAW,GAAU,WAAW;EAChCC,kBAAkB,GAAG,WAAW;EAChCC,WAAW,GAAU,WAAW;EAChCC,kBAAkB,GAAG,WAAW;EAChCC,WAAW,GAAU,WAAW;EAChCC,kBAAkB,GAAG,WAAW;EAChCC,eAAe,GAAM,WAAW;EAChCC,SAAS,GAAGT,aAAa,GAAGC,cAAc,GAAGM,kBAAkB,GAAGC,eAAe;AAACE,OAAA,CAAAD,SAAA,GAAAA,SAAA;AAAAC,OAAA,CAAAF,eAAA,GAAAA,eAAA;AAAAE,OAAA,CAAAH,kBAAA,GAAAA,kBAAA;AAAAG,OAAA,CAAAJ,WAAA,GAAAA,WAAA;AAAAI,OAAA,CAAAL,kBAAA,GAAAA,kBAAA;AAAAK,OAAA,CAAAN,WAAA,GAAAA,WAAA;AAAAM,OAAA,CAAAP,kBAAA,GAAAA,kBAAA;AAAAO,OAAA,CAAAR,WAAA,GAAAA,WAAA;AAAAQ,OAAA,CAAAT,cAAA,GAAAA,cAAA;AAAAS,OAAA,CAAAV,aAAA,GAAAA,aAAA;AAAAU,OAAA,CAAAX,WAAA,GAAAA,WAAA;AAgBxF,MAAMY,eAAe,GAAc,eAAiB;EAC9CC,cAAc,GAAe,eAAiB;EAE9CC,cAAc,GAAe,eAAiB;EAC9CC,kBAAkB,GAAW,eAAiB;EAC9CC,mBAAmB,GAAU,eAAiB;EAC9CC,kBAAkB,GAAW,eAAiB;EAG9CC,eAAe,GAAc,gBAAkB;EAC/CC,gBAAgB,GAAa,gBAAkB;EAC/CC,kBAAkB,GAAW,gBAAkB;EAC/CC,wBAAwB,GAAK,gBAAkB;EAC/CC,yBAAyB,GAAI,gBAAkB;EAC/CC,0BAA0B,GAAG,gBAAkB;EAC/CC,oBAAoB,GAAS,gBAAkB;EAE/CC,4BAA4B,GAAG,gBAAkB;AAACd,OAAA,CAAAc,4BAAA,GAAAA,4BAAA;AAAAd,OAAA,CAAAa,oBAAA,GAAAA,oBAAA;AAAAb,OAAA,CAAAY,0BAAA,GAAAA,0BAAA;AAAAZ,OAAA,CAAAW,yBAAA,GAAAA,yBAAA;AAAAX,OAAA,CAAAU,wBAAA,GAAAA,wBAAA;AAAAV,OAAA,CAAAS,kBAAA,GAAAA,kBAAA;AAAAT,OAAA,CAAAQ,gBAAA,GAAAA,gBAAA;AAAAR,OAAA,CAAAO,eAAA,GAAAA,eAAA;AAAAP,OAAA,CAAAM,kBAAA,GAAAA,kBAAA;AAAAN,OAAA,CAAAK,mBAAA,GAAAA,mBAAA;AAAAL,OAAA,CAAAI,kBAAA,GAAAA,kBAAA;AAAAJ,OAAA,CAAAG,cAAA,GAAAA,cAAA;AAAAH,OAAA,CAAAE,cAAA,GAAAA,cAAA;AAAAF,OAAA,CAAAC,eAAA,GAAAA,eAAA;AAKxD,MAAMc,UAAU,GAAWd,eAAe,GAAGC,cAAc,GAAGE,kBAAkB,GAAII,gBAAgB,GAACM,4BAA4B;EAC3HE,YAAY,GAASf,eAAe,GAAG,CAAC,GAAgBG,kBAAkB,GAAIU,4BAA4B;EAC1GG,gBAAgB,GAAKhB,eAAe,GAAG,CAAC,GAAgBG,kBAAkB,GAAI,CAAC;EAC/Ec,QAAQ,GAAajB,eAAe,GAAG,CAAC,GAAgBE,cAAc,GAAQ,CAAC;EAC/EgB,aAAa,GAAQlB,eAAe,GAAG,CAAC,GAAgBI,mBAAmB,GAAG,CAAC;EAC/Ee,iBAAiB,GAAI,CAAC,GAAiBlB,cAAc,GAAG,CAAC,GAAqBM,gBAAgB;EAC9Fa,YAAY,GAAS,CAAC,GAAiBnB,cAAc,GAAG,CAAC,GAAqB,CAAC;EAC/EoB,YAAY,GAASrB,eAAe,GAAGC,cAAc,GAAGE,kBAAkB,GAAIK,kBAAkB,GAACK,4BAA4B;EAC7HS,eAAe,GAAM,CAAC,GAAiB,CAAC,GAAgB,CAAC,GAAcZ,yBAAyB;EAGhGa,SAAS,GAAY,CAAC,GAAiB,CAAC,GAAgB,CAAC,GAAqBjB,eAAe;EAC7FkB,YAAY,GAASxB,eAAe,GAAG,CAAC,GAAgB,CAAC,GAAqBM,eAAe;EAE7FmB,kBAAkB,GAAGJ,YAAY,GAAMZ,wBAAwB;EAC/DiB,iBAAiB,GAAI,CAAC,GAAiB,CAAC,GAAgB,CAAC,GAAqBhB,yBAAyB;EACvGiB,mBAAmB,GAAE,CAAC,GAAiB1B,cAAc,GAAG,CAAC,GAAqBW,oBAAoB;EAElGgB,oBAAoB,GAAGjB,0BAA0B;AAACZ,OAAA,CAAA6B,oBAAA,GAAAA,oBAAA;AAAA7B,OAAA,CAAA4B,mBAAA,GAAAA,mBAAA;AAAA5B,OAAA,CAAA2B,iBAAA,GAAAA,iBAAA;AAAA3B,OAAA,CAAA0B,kBAAA,GAAAA,kBAAA;AAAA1B,OAAA,CAAAyB,YAAA,GAAAA,YAAA;AAAAzB,OAAA,CAAAwB,SAAA,GAAAA,SAAA;AAAAxB,OAAA,CAAAuB,eAAA,GAAAA,eAAA;AAAAvB,OAAA,CAAAsB,YAAA,GAAAA,YAAA;AAAAtB,OAAA,CAAAqB,YAAA,GAAAA,YAAA;AAAArB,OAAA,CAAAoB,iBAAA,GAAAA,iBAAA;AAAApB,OAAA,CAAAmB,aAAA,GAAAA,aAAA;AAAAnB,OAAA,CAAAkB,QAAA,GAAAA,QAAA;AAAAlB,OAAA,CAAAiB,gBAAA,GAAAA,gBAAA;AAAAjB,OAAA,CAAAgB,YAAA,GAAAA,YAAA;AAAAhB,OAAA,CAAAe,UAAA,GAAAA,UAAA;AAgBxD,MAAMe,yBAAyB,GAAG,KAAM;EAClCC,yBAAyB,GAAG,KAAM;EAClCC,yBAAyB,GAAG,KAAM;EAClCC,2BAA2B,GAAGF,yBAAyB,GAAGC,yBAAyB;AAAChC,OAAA,CAAAiC,2BAAA,GAAAA,2BAAA;AAAAjC,OAAA,CAAAgC,yBAAA,GAAAA,yBAAA;AAAAhC,OAAA,CAAA+B,yBAAA,GAAAA,yBAAA;AAAA/B,OAAA,CAAA8B,yBAAA,GAAAA,yBAAA;AAG1F,MAAMI,2BAA2B,GAAKH,yBAAyB,GAAGD,yBAAyB;EACrFK,2BAA2B,GAAKH,yBAAyB,GAAGF,yBAAyB;EACrFM,6BAA6B,GAAGL,yBAAyB;EACzDM,6BAA6B,GAAGL,yBAAyB;EACzDM,mBAAmB,GAAa,CAAC;AAACtC,OAAA,CAAAsC,mBAAA,GAAAA,mBAAA;AAAAtC,OAAA,CAAAqC,6BAAA,GAAAA,6BAAA;AAAArC,OAAA,CAAAoC,6BAAA,GAAAA,6BAAA;AAAApC,OAAA,CAAAmC,2BAAA,GAAAA,2BAAA;AAAAnC,OAAA,CAAAkC,2BAAA,GAAAA,2BAAA"}