{"name": "@appmaker-xyz/ui", "version": "0.2.33-expo-v2-build-test-25-04-e5e5c0b.0", "description": "", "main": "src/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rollup --config rollup.config.js", "watch": "rollup --config rollup.config.js --watch"}, "keywords": [], "author": "", "license": "UNLICENSED", "dependencies": {"html-entities": "^1.3.1"}, "devDependencies": {"@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-image": "^3.0.2", "@rollup/plugin-typescript": "^11.1.2", "rollup": "^2.79.0", "rollup-plugin-local-resolve": "^1.0.7", "rollup-plugin-svg": "^2.0.0", "rollup-plugin-uglify": "^6.0.4"}, "peerDependencies": {"react": "16.13.1", "react-dom": "16.13.1", "react-native": "0.63.5"}, "repository": "https://github.com/Appmaker-xyz/starter-app", "publishConfig": {"registry": "https://flash.appmaker.xyz", "directory": "build-output"}}