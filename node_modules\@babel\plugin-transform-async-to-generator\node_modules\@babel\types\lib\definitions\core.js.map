{"version": 3, "names": ["_is", "require", "_isValidIdentifier", "_helperValidatorIdentifier", "_helper<PERSON><PERSON><PERSON><PERSON>er", "_constants", "_utils", "defineType", "defineAliasedType", "fields", "elements", "validate", "chain", "assertValueType", "assertEach", "assertNodeOrValueType", "default", "process", "env", "BABEL_TYPES_8_BREAKING", "undefined", "visitor", "aliases", "operator", "identifier", "assertOneOf", "ASSIGNMENT_OPERATORS", "pattern", "node", "key", "val", "validator", "is", "left", "assertNodeType", "right", "builder", "BINARY_OPERATORS", "expression", "inOp", "Object", "assign", "oneOfNodeTypes", "value", "directives", "body", "label", "optional", "callee", "arguments", "typeArguments", "typeParameters", "param", "test", "consequent", "alternate", "program", "comments", "each", "tokens", "type", "init", "update", "functionCommon", "params", "generator", "async", "exports", "functionTypeAnnotationCommon", "returnType", "functionDeclaration<PERSON>ommon", "declare", "id", "predicate", "parent", "inherits", "patternLikeCommon", "typeAnnotation", "decorators", "name", "isValidIdentifier", "TypeError", "match", "exec", "parent<PERSON><PERSON>", "nonComp", "computed", "imported", "meta", "isKeyword", "isReservedWord", "depre<PERSON><PERSON><PERSON><PERSON>", "flags", "invalid", "LOGICAL_OPERATORS", "object", "property", "normal", "sourceFile", "sourceType", "interpreter", "properties", "kind", "shorthand", "argument", "Error", "<PERSON><PERSON><PERSON>", "index", "length", "expressions", "discriminant", "cases", "block", "handler", "finalizer", "prefix", "UNARY_OPERATORS", "UPDATE_OPERATORS", "declarations", "without", "definite", "superClass", "superTypeParameters", "implements", "mixins", "abstract", "source", "exportKind", "validateOptional", "attributes", "assertions", "declaration", "specifiers", "sourced", "sourceless", "local", "exported", "lval", "await", "module", "importKind", "classMethodOrPropertyCommon", "accessibility", "static", "override", "classMethodOrDeclareMethodCommon", "access", "tag", "quasi", "assertShape", "raw", "cooked", "templateElementCookedValidator", "unterminatedCalled", "error", "str", "firstInvalidLoc", "readStringContents", "unterminated", "strictNumericEscape", "invalidEscapeSequence", "numericSeparatorInEscapeSequence", "unexpectedNumericSeparator", "invalidDigit", "invalidCodePoint", "tail", "quasis", "delegate", "assertOptionalChainStart", "readonly", "variance"], "sources": ["../../src/definitions/core.ts"], "sourcesContent": ["import is from \"../validators/is\";\nimport isValidIdentifier from \"../validators/isValidIdentifier\";\nimport { isKeyword, isReservedWord } from \"@babel/helper-validator-identifier\";\nimport type * as t from \"..\";\nimport { readStringContents } from \"@babel/helper-string-parser\";\n\nimport {\n  BINARY_OPERATORS,\n  LOGICAL_OPERATORS,\n  ASSIGNMENT_OPERATORS,\n  UNARY_OPERATORS,\n  UPDATE_OPERATORS,\n} from \"../constants\";\n\nimport {\n  defineAliasedType,\n  assertShape,\n  assertOptionalChainStart,\n  assertValueType,\n  assertNodeType,\n  assertNodeOrValueType,\n  assertEach,\n  chain,\n  assertOneOf,\n  validateOptional,\n  type Validator,\n} from \"./utils\";\n\nconst defineType = defineAliasedType(\"Standardized\");\n\ndefineType(\"ArrayExpression\", {\n  fields: {\n    elements: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeOrValueType(\"null\", \"Expression\", \"SpreadElement\"),\n        ),\n      ),\n      default: !process.env.BABEL_TYPES_8_BREAKING ? [] : undefined,\n    },\n  },\n  visitor: [\"elements\"],\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"AssignmentExpression\", {\n  fields: {\n    operator: {\n      validate: (function () {\n        if (!process.env.BABEL_TYPES_8_BREAKING) {\n          return assertValueType(\"string\");\n        }\n\n        const identifier = assertOneOf(...ASSIGNMENT_OPERATORS);\n        const pattern = assertOneOf(\"=\");\n\n        return function (node: t.AssignmentExpression, key, val) {\n          const validator = is(\"Pattern\", node.left) ? pattern : identifier;\n          validator(node, key, val);\n        };\n      })(),\n    },\n    left: {\n      validate: !process.env.BABEL_TYPES_8_BREAKING\n        ? assertNodeType(\"LVal\")\n        : assertNodeType(\n            \"Identifier\",\n            \"MemberExpression\",\n            \"ArrayPattern\",\n            \"ObjectPattern\",\n            \"TSAsExpression\",\n            \"TSSatisfiesExpression\",\n            \"TSTypeAssertion\",\n            \"TSNonNullExpression\",\n          ),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  builder: [\"operator\", \"left\", \"right\"],\n  visitor: [\"left\", \"right\"],\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"BinaryExpression\", {\n  builder: [\"operator\", \"left\", \"right\"],\n  fields: {\n    operator: {\n      validate: assertOneOf(...BINARY_OPERATORS),\n    },\n    left: {\n      validate: (function () {\n        const expression = assertNodeType(\"Expression\");\n        const inOp = assertNodeType(\"Expression\", \"PrivateName\");\n\n        const validator: Validator = Object.assign(\n          function (node: t.BinaryExpression, key, val) {\n            const validator = node.operator === \"in\" ? inOp : expression;\n            validator(node, key, val);\n          } as Validator,\n          // todo(ts): can be discriminated union by `operator` property\n          { oneOfNodeTypes: [\"Expression\", \"PrivateName\"] },\n        );\n        return validator;\n      })(),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  visitor: [\"left\", \"right\"],\n  aliases: [\"Binary\", \"Expression\"],\n});\n\ndefineType(\"InterpreterDirective\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n});\n\ndefineType(\"Directive\", {\n  visitor: [\"value\"],\n  fields: {\n    value: {\n      validate: assertNodeType(\"DirectiveLiteral\"),\n    },\n  },\n});\n\ndefineType(\"DirectiveLiteral\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n});\n\ndefineType(\"BlockStatement\", {\n  builder: [\"body\", \"directives\"],\n  visitor: [\"directives\", \"body\"],\n  fields: {\n    directives: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Directive\")),\n      ),\n      default: [],\n    },\n    body: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Statement\")),\n      ),\n    },\n  },\n  aliases: [\"Scopable\", \"BlockParent\", \"Block\", \"Statement\"],\n});\n\ndefineType(\"BreakStatement\", {\n  visitor: [\"label\"],\n  fields: {\n    label: {\n      validate: assertNodeType(\"Identifier\"),\n      optional: true,\n    },\n  },\n  aliases: [\"Statement\", \"Terminatorless\", \"CompletionStatement\"],\n});\n\ndefineType(\"CallExpression\", {\n  visitor: [\"callee\", \"arguments\", \"typeParameters\", \"typeArguments\"],\n  builder: [\"callee\", \"arguments\"],\n  aliases: [\"Expression\"],\n  fields: {\n    callee: {\n      validate: assertNodeType(\"Expression\", \"Super\", \"V8IntrinsicIdentifier\"),\n    },\n    arguments: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"Expression\",\n            \"SpreadElement\",\n            \"JSXNamespacedName\",\n            \"ArgumentPlaceholder\",\n          ),\n        ),\n      ),\n    },\n    ...(!process.env.BABEL_TYPES_8_BREAKING\n      ? {\n          optional: {\n            validate: assertOneOf(true, false),\n            optional: true,\n          },\n        }\n      : {}),\n    typeArguments: {\n      validate: assertNodeType(\"TypeParameterInstantiation\"),\n      optional: true,\n    },\n    typeParameters: {\n      validate: assertNodeType(\"TSTypeParameterInstantiation\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"CatchClause\", {\n  visitor: [\"param\", \"body\"],\n  fields: {\n    param: {\n      validate: assertNodeType(\"Identifier\", \"ArrayPattern\", \"ObjectPattern\"),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n  aliases: [\"Scopable\", \"BlockParent\"],\n});\n\ndefineType(\"ConditionalExpression\", {\n  visitor: [\"test\", \"consequent\", \"alternate\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    consequent: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    alternate: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  aliases: [\"Expression\", \"Conditional\"],\n});\n\ndefineType(\"ContinueStatement\", {\n  visitor: [\"label\"],\n  fields: {\n    label: {\n      validate: assertNodeType(\"Identifier\"),\n      optional: true,\n    },\n  },\n  aliases: [\"Statement\", \"Terminatorless\", \"CompletionStatement\"],\n});\n\ndefineType(\"DebuggerStatement\", {\n  aliases: [\"Statement\"],\n});\n\ndefineType(\"DoWhileStatement\", {\n  visitor: [\"test\", \"body\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n  aliases: [\"Statement\", \"BlockParent\", \"Loop\", \"While\", \"Scopable\"],\n});\n\ndefineType(\"EmptyStatement\", {\n  aliases: [\"Statement\"],\n});\n\ndefineType(\"ExpressionStatement\", {\n  visitor: [\"expression\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n  aliases: [\"Statement\", \"ExpressionWrapper\"],\n});\n\ndefineType(\"File\", {\n  builder: [\"program\", \"comments\", \"tokens\"],\n  visitor: [\"program\"],\n  fields: {\n    program: {\n      validate: assertNodeType(\"Program\"),\n    },\n    comments: {\n      validate: !process.env.BABEL_TYPES_8_BREAKING\n        ? Object.assign(() => {}, {\n            each: { oneOfNodeTypes: [\"CommentBlock\", \"CommentLine\"] },\n          })\n        : assertEach(assertNodeType(\"CommentBlock\", \"CommentLine\")),\n      optional: true,\n    },\n    tokens: {\n      // todo(ts): add Token type\n      validate: assertEach(Object.assign(() => {}, { type: \"any\" })),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ForInStatement\", {\n  visitor: [\"left\", \"right\", \"body\"],\n  aliases: [\n    \"Scopable\",\n    \"Statement\",\n    \"For\",\n    \"BlockParent\",\n    \"Loop\",\n    \"ForXStatement\",\n  ],\n  fields: {\n    left: {\n      validate: !process.env.BABEL_TYPES_8_BREAKING\n        ? assertNodeType(\"VariableDeclaration\", \"LVal\")\n        : assertNodeType(\n            \"VariableDeclaration\",\n            \"Identifier\",\n            \"MemberExpression\",\n            \"ArrayPattern\",\n            \"ObjectPattern\",\n            \"TSAsExpression\",\n            \"TSSatisfiesExpression\",\n            \"TSTypeAssertion\",\n            \"TSNonNullExpression\",\n          ),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\ndefineType(\"ForStatement\", {\n  visitor: [\"init\", \"test\", \"update\", \"body\"],\n  aliases: [\"Scopable\", \"Statement\", \"For\", \"BlockParent\", \"Loop\"],\n  fields: {\n    init: {\n      validate: assertNodeType(\"VariableDeclaration\", \"Expression\"),\n      optional: true,\n    },\n    test: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    update: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\nexport const functionCommon = () => ({\n  params: {\n    validate: chain(\n      assertValueType(\"array\"),\n      assertEach(assertNodeType(\"Identifier\", \"Pattern\", \"RestElement\")),\n    ),\n  },\n  generator: {\n    default: false,\n  },\n  async: {\n    default: false,\n  },\n});\n\nexport const functionTypeAnnotationCommon = () => ({\n  returnType: {\n    validate: process.env.BABEL_8_BREAKING\n      ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n      : assertNodeType(\n          \"TypeAnnotation\",\n          \"TSTypeAnnotation\",\n          // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n          \"Noop\",\n        ),\n    optional: true,\n  },\n  typeParameters: {\n    validate: process.env.BABEL_8_BREAKING\n      ? assertNodeType(\"TypeParameterDeclaration\", \"TSTypeParameterDeclaration\")\n      : assertNodeType(\n          \"TypeParameterDeclaration\",\n          \"TSTypeParameterDeclaration\",\n          // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n          \"Noop\",\n        ),\n    optional: true,\n  },\n});\n\nexport const functionDeclarationCommon = () => ({\n  ...functionCommon(),\n  declare: {\n    validate: assertValueType(\"boolean\"),\n    optional: true,\n  },\n  id: {\n    validate: assertNodeType(\"Identifier\"),\n    optional: true, // May be null for `export default function`\n  },\n});\n\ndefineType(\"FunctionDeclaration\", {\n  builder: [\"id\", \"params\", \"body\", \"generator\", \"async\"],\n  visitor: [\"id\", \"params\", \"body\", \"returnType\", \"typeParameters\"],\n  fields: {\n    ...functionDeclarationCommon(),\n    ...functionTypeAnnotationCommon(),\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n    predicate: {\n      validate: assertNodeType(\"DeclaredPredicate\", \"InferredPredicate\"),\n      optional: true,\n    },\n  },\n  aliases: [\n    \"Scopable\",\n    \"Function\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Statement\",\n    \"Pureish\",\n    \"Declaration\",\n  ],\n  validate: (function () {\n    if (!process.env.BABEL_TYPES_8_BREAKING) return () => {};\n\n    const identifier = assertNodeType(\"Identifier\");\n\n    return function (parent, key, node) {\n      if (!is(\"ExportDefaultDeclaration\", parent)) {\n        identifier(node, \"id\", node.id);\n      }\n    };\n  })(),\n});\n\ndefineType(\"FunctionExpression\", {\n  inherits: \"FunctionDeclaration\",\n  aliases: [\n    \"Scopable\",\n    \"Function\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Expression\",\n    \"Pureish\",\n  ],\n  fields: {\n    ...functionCommon(),\n    ...functionTypeAnnotationCommon(),\n    id: {\n      validate: assertNodeType(\"Identifier\"),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n    predicate: {\n      validate: assertNodeType(\"DeclaredPredicate\", \"InferredPredicate\"),\n      optional: true,\n    },\n  },\n});\n\nexport const patternLikeCommon = () => ({\n  typeAnnotation: {\n    validate: process.env.BABEL_8_BREAKING\n      ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n      : assertNodeType(\n          \"TypeAnnotation\",\n          \"TSTypeAnnotation\",\n          // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n          \"Noop\",\n        ),\n    optional: true,\n  },\n  optional: {\n    validate: assertValueType(\"boolean\"),\n    optional: true,\n  },\n  decorators: {\n    validate: chain(\n      assertValueType(\"array\"),\n      assertEach(assertNodeType(\"Decorator\")),\n    ),\n    optional: true,\n  },\n});\n\ndefineType(\"Identifier\", {\n  builder: [\"name\"],\n  visitor: [\"typeAnnotation\", \"decorators\" /* for legacy param decorators */],\n  aliases: [\"Expression\", \"PatternLike\", \"LVal\", \"TSEntityName\"],\n  fields: {\n    ...patternLikeCommon(),\n    name: {\n      validate: chain(\n        assertValueType(\"string\"),\n        Object.assign(\n          function (node, key, val) {\n            if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n            if (!isValidIdentifier(val, false)) {\n              throw new TypeError(`\"${val}\" is not a valid identifier name`);\n            }\n          } as Validator,\n          { type: \"string\" },\n        ),\n      ),\n    },\n  },\n  validate(parent, key, node) {\n    if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n    const match = /\\.(\\w+)$/.exec(key);\n    if (!match) return;\n\n    const [, parentKey] = match;\n    const nonComp = { computed: false };\n\n    // We can't check if `parent.property === node`, because nodes are validated\n    // before replacing them in the AST.\n    if (parentKey === \"property\") {\n      if (is(\"MemberExpression\", parent, nonComp)) return;\n      if (is(\"OptionalMemberExpression\", parent, nonComp)) return;\n    } else if (parentKey === \"key\") {\n      if (is(\"Property\", parent, nonComp)) return;\n      if (is(\"Method\", parent, nonComp)) return;\n    } else if (parentKey === \"exported\") {\n      if (is(\"ExportSpecifier\", parent)) return;\n    } else if (parentKey === \"imported\") {\n      if (is(\"ImportSpecifier\", parent, { imported: node })) return;\n    } else if (parentKey === \"meta\") {\n      if (is(\"MetaProperty\", parent, { meta: node })) return;\n    }\n\n    if (\n      // Ideally we should call isStrictReservedWord if this node is a descendant\n      // of a block in strict mode. Also, we should pass the inModule option so\n      // we can disable \"await\" in module.\n      (isKeyword(node.name) || isReservedWord(node.name, false)) &&\n      // Even if \"this\" is a keyword, we are using the Identifier\n      // node to represent it.\n      node.name !== \"this\"\n    ) {\n      throw new TypeError(`\"${node.name}\" is not a valid identifier`);\n    }\n  },\n});\n\ndefineType(\"IfStatement\", {\n  visitor: [\"test\", \"consequent\", \"alternate\"],\n  aliases: [\"Statement\", \"Conditional\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    consequent: {\n      validate: assertNodeType(\"Statement\"),\n    },\n    alternate: {\n      optional: true,\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\ndefineType(\"LabeledStatement\", {\n  visitor: [\"label\", \"body\"],\n  aliases: [\"Statement\"],\n  fields: {\n    label: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\ndefineType(\"StringLiteral\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"NumericLiteral\", {\n  builder: [\"value\"],\n  deprecatedAlias: \"NumberLiteral\",\n  fields: {\n    value: {\n      validate: assertValueType(\"number\"),\n    },\n  },\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"NullLiteral\", {\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"BooleanLiteral\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"boolean\"),\n    },\n  },\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"RegExpLiteral\", {\n  builder: [\"pattern\", \"flags\"],\n  deprecatedAlias: \"RegexLiteral\",\n  aliases: [\"Expression\", \"Pureish\", \"Literal\"],\n  fields: {\n    pattern: {\n      validate: assertValueType(\"string\"),\n    },\n    flags: {\n      validate: chain(\n        assertValueType(\"string\"),\n        Object.assign(\n          function (node, key, val) {\n            if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n            const invalid = /[^gimsuy]/.exec(val);\n            if (invalid) {\n              throw new TypeError(`\"${invalid[0]}\" is not a valid RegExp flag`);\n            }\n          } as Validator,\n          { type: \"string\" },\n        ),\n      ),\n      default: \"\",\n    },\n  },\n});\n\ndefineType(\"LogicalExpression\", {\n  builder: [\"operator\", \"left\", \"right\"],\n  visitor: [\"left\", \"right\"],\n  aliases: [\"Binary\", \"Expression\"],\n  fields: {\n    operator: {\n      validate: assertOneOf(...LOGICAL_OPERATORS),\n    },\n    left: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"MemberExpression\", {\n  builder: [\n    \"object\",\n    \"property\",\n    \"computed\",\n    ...(!process.env.BABEL_TYPES_8_BREAKING ? [\"optional\"] : []),\n  ],\n  visitor: [\"object\", \"property\"],\n  aliases: [\"Expression\", \"LVal\"],\n  fields: {\n    object: {\n      validate: assertNodeType(\"Expression\", \"Super\"),\n    },\n    property: {\n      validate: (function () {\n        const normal = assertNodeType(\"Identifier\", \"PrivateName\");\n        const computed = assertNodeType(\"Expression\");\n\n        const validator: Validator = function (\n          node: t.MemberExpression,\n          key,\n          val,\n        ) {\n          const validator: Validator = node.computed ? computed : normal;\n          validator(node, key, val);\n        };\n        // @ts-expect-error todo(ts): can be discriminated union by `computed` property\n        validator.oneOfNodeTypes = [\"Expression\", \"Identifier\", \"PrivateName\"];\n        return validator;\n      })(),\n    },\n    computed: {\n      default: false,\n    },\n    ...(!process.env.BABEL_TYPES_8_BREAKING\n      ? {\n          optional: {\n            validate: assertOneOf(true, false),\n            optional: true,\n          },\n        }\n      : {}),\n  },\n});\n\ndefineType(\"NewExpression\", { inherits: \"CallExpression\" });\n\ndefineType(\"Program\", {\n  // Note: We explicitly leave 'interpreter' out here because it is\n  // conceptually comment-like, and Babel does not traverse comments either.\n  visitor: [\"directives\", \"body\"],\n  builder: [\"body\", \"directives\", \"sourceType\", \"interpreter\"],\n  fields: {\n    sourceFile: {\n      validate: assertValueType(\"string\"),\n    },\n    sourceType: {\n      validate: assertOneOf(\"script\", \"module\"),\n      default: \"script\",\n    },\n    interpreter: {\n      validate: assertNodeType(\"InterpreterDirective\"),\n      default: null,\n      optional: true,\n    },\n    directives: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Directive\")),\n      ),\n      default: [],\n    },\n    body: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Statement\")),\n      ),\n    },\n  },\n  aliases: [\"Scopable\", \"BlockParent\", \"Block\"],\n});\n\ndefineType(\"ObjectExpression\", {\n  visitor: [\"properties\"],\n  aliases: [\"Expression\"],\n  fields: {\n    properties: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\"ObjectMethod\", \"ObjectProperty\", \"SpreadElement\"),\n        ),\n      ),\n    },\n  },\n});\n\ndefineType(\"ObjectMethod\", {\n  builder: [\"kind\", \"key\", \"params\", \"body\", \"computed\", \"generator\", \"async\"],\n  fields: {\n    ...functionCommon(),\n    ...functionTypeAnnotationCommon(),\n    kind: {\n      validate: assertOneOf(\"method\", \"get\", \"set\"),\n      ...(!process.env.BABEL_TYPES_8_BREAKING ? { default: \"method\" } : {}),\n    },\n    computed: {\n      default: false,\n    },\n    key: {\n      validate: (function () {\n        const normal = assertNodeType(\n          \"Identifier\",\n          \"StringLiteral\",\n          \"NumericLiteral\",\n          \"BigIntLiteral\",\n        );\n        const computed = assertNodeType(\"Expression\");\n\n        const validator: Validator = function (node: t.ObjectMethod, key, val) {\n          const validator = node.computed ? computed : normal;\n          validator(node, key, val);\n        };\n        // @ts-expect-error todo(ts): can be discriminated union by `computed` property\n        validator.oneOfNodeTypes = [\n          \"Expression\",\n          \"Identifier\",\n          \"StringLiteral\",\n          \"NumericLiteral\",\n          \"BigIntLiteral\",\n        ];\n        return validator;\n      })(),\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n  visitor: [\n    \"key\",\n    \"params\",\n    \"body\",\n    \"decorators\",\n    \"returnType\",\n    \"typeParameters\",\n  ],\n  aliases: [\n    \"UserWhitespacable\",\n    \"Function\",\n    \"Scopable\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Method\",\n    \"ObjectMember\",\n  ],\n});\n\ndefineType(\"ObjectProperty\", {\n  builder: [\n    \"key\",\n    \"value\",\n    \"computed\",\n    \"shorthand\",\n    ...(!process.env.BABEL_TYPES_8_BREAKING ? [\"decorators\"] : []),\n  ],\n  fields: {\n    computed: {\n      default: false,\n    },\n    key: {\n      validate: (function () {\n        const normal = assertNodeType(\n          \"Identifier\",\n          \"StringLiteral\",\n          \"NumericLiteral\",\n          \"BigIntLiteral\",\n          \"DecimalLiteral\",\n          \"PrivateName\",\n        );\n        const computed = assertNodeType(\"Expression\");\n\n        const validator: Validator = Object.assign(\n          function (node: t.ObjectProperty, key, val) {\n            const validator = node.computed ? computed : normal;\n            validator(node, key, val);\n          } as Validator,\n          {\n            // todo(ts): can be discriminated union by `computed` property\n            oneOfNodeTypes: [\n              \"Expression\",\n              \"Identifier\",\n              \"StringLiteral\",\n              \"NumericLiteral\",\n              \"BigIntLiteral\",\n              \"DecimalLiteral\",\n              \"PrivateName\",\n            ],\n          },\n        );\n        return validator;\n      })(),\n    },\n    value: {\n      // Value may be PatternLike if this is an AssignmentProperty\n      // https://github.com/babel/babylon/issues/434\n      validate: assertNodeType(\"Expression\", \"PatternLike\"),\n    },\n    shorthand: {\n      validate: chain(\n        assertValueType(\"boolean\"),\n        Object.assign(\n          function (node: t.ObjectProperty, key, val) {\n            if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n            if (val && node.computed) {\n              throw new TypeError(\n                \"Property shorthand of ObjectProperty cannot be true if computed is true\",\n              );\n            }\n          } as Validator,\n          { type: \"boolean\" },\n        ),\n        function (node: t.ObjectProperty, key, val) {\n          if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n          if (val && !is(\"Identifier\", node.key)) {\n            throw new TypeError(\n              \"Property shorthand of ObjectProperty cannot be true if key is not an Identifier\",\n            );\n          }\n        } as Validator,\n      ),\n      default: false,\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n  },\n  visitor: [\"key\", \"value\", \"decorators\"],\n  aliases: [\"UserWhitespacable\", \"Property\", \"ObjectMember\"],\n  validate: (function () {\n    const pattern = assertNodeType(\n      \"Identifier\",\n      \"Pattern\",\n      \"TSAsExpression\",\n      \"TSSatisfiesExpression\",\n      \"TSNonNullExpression\",\n      \"TSTypeAssertion\",\n    );\n    const expression = assertNodeType(\"Expression\");\n\n    return function (parent, key, node) {\n      if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n      const validator = is(\"ObjectPattern\", parent) ? pattern : expression;\n      validator(node, \"value\", node.value);\n    };\n  })(),\n});\n\ndefineType(\"RestElement\", {\n  visitor: [\"argument\", \"typeAnnotation\"],\n  builder: [\"argument\"],\n  aliases: [\"LVal\", \"PatternLike\"],\n  deprecatedAlias: \"RestProperty\",\n  fields: {\n    ...patternLikeCommon(),\n    argument: {\n      validate: !process.env.BABEL_TYPES_8_BREAKING\n        ? assertNodeType(\"LVal\")\n        : assertNodeType(\n            \"Identifier\",\n            \"ArrayPattern\",\n            \"ObjectPattern\",\n            \"MemberExpression\",\n            \"TSAsExpression\",\n            \"TSSatisfiesExpression\",\n            \"TSTypeAssertion\",\n            \"TSNonNullExpression\",\n          ),\n    },\n  },\n  validate(parent: t.ArrayPattern | t.ObjectPattern, key) {\n    if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n    const match = /(\\w+)\\[(\\d+)\\]/.exec(key);\n    if (!match) throw new Error(\"Internal Babel error: malformed key.\");\n\n    const [, listKey, index] = match as unknown as [\n      string,\n      keyof typeof parent,\n      string,\n    ];\n    if ((parent[listKey] as t.Node[]).length > +index + 1) {\n      throw new TypeError(`RestElement must be last element of ${listKey}`);\n    }\n  },\n});\n\ndefineType(\"ReturnStatement\", {\n  visitor: [\"argument\"],\n  aliases: [\"Statement\", \"Terminatorless\", \"CompletionStatement\"],\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"SequenceExpression\", {\n  visitor: [\"expressions\"],\n  fields: {\n    expressions: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Expression\")),\n      ),\n    },\n  },\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"ParenthesizedExpression\", {\n  visitor: [\"expression\"],\n  aliases: [\"Expression\", \"ExpressionWrapper\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"SwitchCase\", {\n  visitor: [\"test\", \"consequent\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    consequent: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Statement\")),\n      ),\n    },\n  },\n});\n\ndefineType(\"SwitchStatement\", {\n  visitor: [\"discriminant\", \"cases\"],\n  aliases: [\"Statement\", \"BlockParent\", \"Scopable\"],\n  fields: {\n    discriminant: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    cases: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"SwitchCase\")),\n      ),\n    },\n  },\n});\n\ndefineType(\"ThisExpression\", {\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"ThrowStatement\", {\n  visitor: [\"argument\"],\n  aliases: [\"Statement\", \"Terminatorless\", \"CompletionStatement\"],\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"TryStatement\", {\n  visitor: [\"block\", \"handler\", \"finalizer\"],\n  aliases: [\"Statement\"],\n  fields: {\n    block: {\n      validate: chain(\n        assertNodeType(\"BlockStatement\"),\n        Object.assign(\n          function (node: t.TryStatement) {\n            if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n            // This validator isn't put at the top level because we can run it\n            // even if this node doesn't have a parent.\n\n            if (!node.handler && !node.finalizer) {\n              throw new TypeError(\n                \"TryStatement expects either a handler or finalizer, or both\",\n              );\n            }\n          } as Validator,\n          {\n            oneOfNodeTypes: [\"BlockStatement\"],\n          },\n        ),\n      ),\n    },\n    handler: {\n      optional: true,\n      validate: assertNodeType(\"CatchClause\"),\n    },\n    finalizer: {\n      optional: true,\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n});\n\ndefineType(\"UnaryExpression\", {\n  builder: [\"operator\", \"argument\", \"prefix\"],\n  fields: {\n    prefix: {\n      default: true,\n    },\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    operator: {\n      validate: assertOneOf(...UNARY_OPERATORS),\n    },\n  },\n  visitor: [\"argument\"],\n  aliases: [\"UnaryLike\", \"Expression\"],\n});\n\ndefineType(\"UpdateExpression\", {\n  builder: [\"operator\", \"argument\", \"prefix\"],\n  fields: {\n    prefix: {\n      default: false,\n    },\n    argument: {\n      validate: !process.env.BABEL_TYPES_8_BREAKING\n        ? assertNodeType(\"Expression\")\n        : assertNodeType(\"Identifier\", \"MemberExpression\"),\n    },\n    operator: {\n      validate: assertOneOf(...UPDATE_OPERATORS),\n    },\n  },\n  visitor: [\"argument\"],\n  aliases: [\"Expression\"],\n});\n\ndefineType(\"VariableDeclaration\", {\n  builder: [\"kind\", \"declarations\"],\n  visitor: [\"declarations\"],\n  aliases: [\"Statement\", \"Declaration\"],\n  fields: {\n    declare: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    kind: {\n      validate: assertOneOf(\n        \"var\",\n        \"let\",\n        \"const\",\n        // https://github.com/tc39/proposal-explicit-resource-management\n        \"using\",\n        // https://github.com/tc39/proposal-async-explicit-resource-management\n        \"await using\",\n      ),\n    },\n    declarations: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"VariableDeclarator\")),\n      ),\n    },\n  },\n  validate(parent, key, node) {\n    if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n    if (!is(\"ForXStatement\", parent, { left: node })) return;\n    if (node.declarations.length !== 1) {\n      throw new TypeError(\n        `Exactly one VariableDeclarator is required in the VariableDeclaration of a ${parent.type}`,\n      );\n    }\n  },\n});\n\ndefineType(\"VariableDeclarator\", {\n  visitor: [\"id\", \"init\"],\n  fields: {\n    id: {\n      validate: (function () {\n        if (!process.env.BABEL_TYPES_8_BREAKING) {\n          return assertNodeType(\"LVal\");\n        }\n\n        const normal = assertNodeType(\n          \"Identifier\",\n          \"ArrayPattern\",\n          \"ObjectPattern\",\n        );\n        const without = assertNodeType(\"Identifier\");\n\n        return function (node: t.VariableDeclarator, key, val) {\n          const validator = node.init ? normal : without;\n          validator(node, key, val);\n        };\n      })(),\n    },\n    definite: {\n      optional: true,\n      validate: assertValueType(\"boolean\"),\n    },\n    init: {\n      optional: true,\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"WhileStatement\", {\n  visitor: [\"test\", \"body\"],\n  aliases: [\"Statement\", \"BlockParent\", \"Loop\", \"While\", \"Scopable\"],\n  fields: {\n    test: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\ndefineType(\"WithStatement\", {\n  visitor: [\"object\", \"body\"],\n  aliases: [\"Statement\"],\n  fields: {\n    object: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n  },\n});\n\n// --- ES2015 ---\ndefineType(\"AssignmentPattern\", {\n  visitor: [\"left\", \"right\", \"decorators\" /* for legacy param decorators */],\n  builder: [\"left\", \"right\"],\n  aliases: [\"Pattern\", \"PatternLike\", \"LVal\"],\n  fields: {\n    ...patternLikeCommon(),\n    left: {\n      validate: assertNodeType(\n        \"Identifier\",\n        \"ObjectPattern\",\n        \"ArrayPattern\",\n        \"MemberExpression\",\n        \"TSAsExpression\",\n        \"TSSatisfiesExpression\",\n        \"TSTypeAssertion\",\n        \"TSNonNullExpression\",\n      ),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    // For TypeScript\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ArrayPattern\", {\n  visitor: [\"elements\", \"typeAnnotation\"],\n  builder: [\"elements\"],\n  aliases: [\"Pattern\", \"PatternLike\", \"LVal\"],\n  fields: {\n    ...patternLikeCommon(),\n    elements: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeOrValueType(\"null\", \"PatternLike\", \"LVal\")),\n      ),\n    },\n  },\n});\n\ndefineType(\"ArrowFunctionExpression\", {\n  builder: [\"params\", \"body\", \"async\"],\n  visitor: [\"params\", \"body\", \"returnType\", \"typeParameters\"],\n  aliases: [\n    \"Scopable\",\n    \"Function\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Expression\",\n    \"Pureish\",\n  ],\n  fields: {\n    ...functionCommon(),\n    ...functionTypeAnnotationCommon(),\n    expression: {\n      // https://github.com/babel/babylon/issues/505\n      validate: assertValueType(\"boolean\"),\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\", \"Expression\"),\n    },\n    predicate: {\n      validate: assertNodeType(\"DeclaredPredicate\", \"InferredPredicate\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassBody\", {\n  visitor: [\"body\"],\n  fields: {\n    body: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"ClassMethod\",\n            \"ClassPrivateMethod\",\n            \"ClassProperty\",\n            \"ClassPrivateProperty\",\n            \"ClassAccessorProperty\",\n            \"TSDeclareMethod\",\n            \"TSIndexSignature\",\n            \"StaticBlock\",\n          ),\n        ),\n      ),\n    },\n  },\n});\n\ndefineType(\"ClassExpression\", {\n  builder: [\"id\", \"superClass\", \"body\", \"decorators\"],\n  visitor: [\n    \"id\",\n    \"body\",\n    \"superClass\",\n    \"mixins\",\n    \"typeParameters\",\n    \"superTypeParameters\",\n    \"implements\",\n    \"decorators\",\n  ],\n  aliases: [\"Scopable\", \"Class\", \"Expression\"],\n  fields: {\n    id: {\n      validate: assertNodeType(\"Identifier\"),\n      // In declarations, this is missing if this is the\n      // child of an ExportDefaultDeclaration.\n      optional: true,\n    },\n    typeParameters: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\n            \"TypeParameterDeclaration\",\n            \"TSTypeParameterDeclaration\",\n          )\n        : assertNodeType(\n            \"TypeParameterDeclaration\",\n            \"TSTypeParameterDeclaration\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"ClassBody\"),\n    },\n    superClass: {\n      optional: true,\n      validate: assertNodeType(\"Expression\"),\n    },\n    superTypeParameters: {\n      validate: assertNodeType(\n        \"TypeParameterInstantiation\",\n        \"TSTypeParameterInstantiation\",\n      ),\n      optional: true,\n    },\n    implements: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\"TSExpressionWithTypeArguments\", \"ClassImplements\"),\n        ),\n      ),\n      optional: true,\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n    mixins: {\n      validate: assertNodeType(\"InterfaceExtends\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassDeclaration\", {\n  inherits: \"ClassExpression\",\n  aliases: [\"Scopable\", \"Class\", \"Statement\", \"Declaration\"],\n  fields: {\n    id: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n    typeParameters: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\n            \"TypeParameterDeclaration\",\n            \"TSTypeParameterDeclaration\",\n          )\n        : assertNodeType(\n            \"TypeParameterDeclaration\",\n            \"TSTypeParameterDeclaration\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    body: {\n      validate: assertNodeType(\"ClassBody\"),\n    },\n    superClass: {\n      optional: true,\n      validate: assertNodeType(\"Expression\"),\n    },\n    superTypeParameters: {\n      validate: assertNodeType(\n        \"TypeParameterInstantiation\",\n        \"TSTypeParameterInstantiation\",\n      ),\n      optional: true,\n    },\n    implements: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\"TSExpressionWithTypeArguments\", \"ClassImplements\"),\n        ),\n      ),\n      optional: true,\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n    mixins: {\n      validate: assertNodeType(\"InterfaceExtends\"),\n      optional: true,\n    },\n    declare: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    abstract: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n  },\n  validate: (function () {\n    const identifier = assertNodeType(\"Identifier\");\n\n    return function (parent, key, node) {\n      if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n      if (!is(\"ExportDefaultDeclaration\", parent)) {\n        identifier(node, \"id\", node.id);\n      }\n    };\n  })(),\n});\n\ndefineType(\"ExportAllDeclaration\", {\n  builder: [\"source\"],\n  visitor: [\"source\", \"attributes\", \"assertions\"],\n  aliases: [\n    \"Statement\",\n    \"Declaration\",\n    \"ImportOrExportDeclaration\",\n    \"ExportDeclaration\",\n  ],\n  fields: {\n    source: {\n      validate: assertNodeType(\"StringLiteral\"),\n    },\n    exportKind: validateOptional(assertOneOf(\"type\", \"value\")),\n    attributes: {\n      optional: true,\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"ImportAttribute\")),\n      ),\n    },\n    // TODO(Babel 8): Deprecated\n    assertions: {\n      optional: true,\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"ImportAttribute\")),\n      ),\n    },\n  },\n});\n\ndefineType(\"ExportDefaultDeclaration\", {\n  visitor: [\"declaration\"],\n  aliases: [\n    \"Statement\",\n    \"Declaration\",\n    \"ImportOrExportDeclaration\",\n    \"ExportDeclaration\",\n  ],\n  fields: {\n    declaration: {\n      validate: assertNodeType(\n        \"TSDeclareFunction\",\n        \"FunctionDeclaration\",\n        \"ClassDeclaration\",\n        \"Expression\",\n      ),\n    },\n    exportKind: validateOptional(assertOneOf(\"value\")),\n  },\n});\n\ndefineType(\"ExportNamedDeclaration\", {\n  builder: [\"declaration\", \"specifiers\", \"source\"],\n  visitor: [\"declaration\", \"specifiers\", \"source\", \"attributes\", \"assertions\"],\n  aliases: [\n    \"Statement\",\n    \"Declaration\",\n    \"ImportOrExportDeclaration\",\n    \"ExportDeclaration\",\n  ],\n  fields: {\n    declaration: {\n      optional: true,\n      validate: chain(\n        assertNodeType(\"Declaration\"),\n        Object.assign(\n          function (node: t.ExportNamedDeclaration, key, val) {\n            if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n            // This validator isn't put at the top level because we can run it\n            // even if this node doesn't have a parent.\n\n            if (val && node.specifiers.length) {\n              throw new TypeError(\n                \"Only declaration or specifiers is allowed on ExportNamedDeclaration\",\n              );\n            }\n          } as Validator,\n          { oneOfNodeTypes: [\"Declaration\"] },\n        ),\n        function (node: t.ExportNamedDeclaration, key, val) {\n          if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n          // This validator isn't put at the top level because we can run it\n          // even if this node doesn't have a parent.\n\n          if (val && node.source) {\n            throw new TypeError(\"Cannot export a declaration from a source\");\n          }\n        },\n      ),\n    },\n    attributes: {\n      optional: true,\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"ImportAttribute\")),\n      ),\n    },\n    // TODO(Babel 8): Deprecated\n    assertions: {\n      optional: true,\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"ImportAttribute\")),\n      ),\n    },\n    specifiers: {\n      default: [],\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          (function () {\n            const sourced = assertNodeType(\n              \"ExportSpecifier\",\n              \"ExportDefaultSpecifier\",\n              \"ExportNamespaceSpecifier\",\n            );\n            const sourceless = assertNodeType(\"ExportSpecifier\");\n\n            if (!process.env.BABEL_TYPES_8_BREAKING) return sourced;\n\n            return function (node: t.ExportNamedDeclaration, key, val) {\n              const validator = node.source ? sourced : sourceless;\n              validator(node, key, val);\n            } as Validator;\n          })(),\n        ),\n      ),\n    },\n    source: {\n      validate: assertNodeType(\"StringLiteral\"),\n      optional: true,\n    },\n    exportKind: validateOptional(assertOneOf(\"type\", \"value\")),\n  },\n});\n\ndefineType(\"ExportSpecifier\", {\n  visitor: [\"local\", \"exported\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    local: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n    exported: {\n      validate: assertNodeType(\"Identifier\", \"StringLiteral\"),\n    },\n    exportKind: {\n      // And TypeScript's \"export { type foo } from\"\n      validate: assertOneOf(\"type\", \"value\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ForOfStatement\", {\n  visitor: [\"left\", \"right\", \"body\"],\n  builder: [\"left\", \"right\", \"body\", \"await\"],\n  aliases: [\n    \"Scopable\",\n    \"Statement\",\n    \"For\",\n    \"BlockParent\",\n    \"Loop\",\n    \"ForXStatement\",\n  ],\n  fields: {\n    left: {\n      validate: (function () {\n        if (!process.env.BABEL_TYPES_8_BREAKING) {\n          return assertNodeType(\"VariableDeclaration\", \"LVal\");\n        }\n\n        const declaration = assertNodeType(\"VariableDeclaration\");\n        const lval = assertNodeType(\n          \"Identifier\",\n          \"MemberExpression\",\n          \"ArrayPattern\",\n          \"ObjectPattern\",\n          \"TSAsExpression\",\n          \"TSSatisfiesExpression\",\n          \"TSTypeAssertion\",\n          \"TSNonNullExpression\",\n        );\n\n        return function (node, key, val) {\n          if (is(\"VariableDeclaration\", val)) {\n            declaration(node, key, val);\n          } else {\n            lval(node, key, val);\n          }\n        };\n      })(),\n    },\n    right: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    body: {\n      validate: assertNodeType(\"Statement\"),\n    },\n    await: {\n      default: false,\n    },\n  },\n});\n\ndefineType(\"ImportDeclaration\", {\n  builder: [\"specifiers\", \"source\"],\n  visitor: [\"specifiers\", \"source\", \"attributes\", \"assertions\"],\n  aliases: [\"Statement\", \"Declaration\", \"ImportOrExportDeclaration\"],\n  fields: {\n    attributes: {\n      optional: true,\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"ImportAttribute\")),\n      ),\n    },\n    // TODO(Babel 8): Deprecated\n    assertions: {\n      optional: true,\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"ImportAttribute\")),\n      ),\n    },\n    module: {\n      optional: true,\n      validate: assertValueType(\"boolean\"),\n    },\n    specifiers: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"ImportSpecifier\",\n            \"ImportDefaultSpecifier\",\n            \"ImportNamespaceSpecifier\",\n          ),\n        ),\n      ),\n    },\n    source: {\n      validate: assertNodeType(\"StringLiteral\"),\n    },\n    importKind: {\n      // Handle TypeScript/Flowtype's extension \"import type foo from\"\n      // TypeScript doesn't support typeof\n      validate: assertOneOf(\"type\", \"typeof\", \"value\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ImportDefaultSpecifier\", {\n  visitor: [\"local\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    local: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"ImportNamespaceSpecifier\", {\n  visitor: [\"local\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    local: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"ImportSpecifier\", {\n  visitor: [\"local\", \"imported\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    local: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n    imported: {\n      validate: assertNodeType(\"Identifier\", \"StringLiteral\"),\n    },\n    importKind: {\n      // Handle Flowtype's extension \"import {typeof foo} from\"\n      // And TypeScript's \"import { type foo } from\"\n      validate: assertOneOf(\"type\", \"typeof\", \"value\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"MetaProperty\", {\n  visitor: [\"meta\", \"property\"],\n  aliases: [\"Expression\"],\n  fields: {\n    meta: {\n      validate: chain(\n        assertNodeType(\"Identifier\"),\n        Object.assign(\n          function (node: t.MetaProperty, key, val) {\n            if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n            let property;\n            switch (val.name) {\n              case \"function\":\n                property = \"sent\";\n                break;\n              case \"new\":\n                property = \"target\";\n                break;\n              case \"import\":\n                property = \"meta\";\n                break;\n            }\n            if (!is(\"Identifier\", node.property, { name: property })) {\n              throw new TypeError(\"Unrecognised MetaProperty\");\n            }\n          } as Validator,\n          { oneOfNodeTypes: [\"Identifier\"] },\n        ),\n      ),\n    },\n    property: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\nexport const classMethodOrPropertyCommon = () => ({\n  abstract: {\n    validate: assertValueType(\"boolean\"),\n    optional: true,\n  },\n  accessibility: {\n    validate: assertOneOf(\"public\", \"private\", \"protected\"),\n    optional: true,\n  },\n  static: {\n    default: false,\n  },\n  override: {\n    default: false,\n  },\n  computed: {\n    default: false,\n  },\n  optional: {\n    validate: assertValueType(\"boolean\"),\n    optional: true,\n  },\n  key: {\n    validate: chain(\n      (function () {\n        const normal = assertNodeType(\n          \"Identifier\",\n          \"StringLiteral\",\n          \"NumericLiteral\",\n        );\n        const computed = assertNodeType(\"Expression\");\n\n        return function (node: any, key: string, val: any) {\n          const validator = node.computed ? computed : normal;\n          validator(node, key, val);\n        };\n      })(),\n      assertNodeType(\n        \"Identifier\",\n        \"StringLiteral\",\n        \"NumericLiteral\",\n        \"BigIntLiteral\",\n        \"Expression\",\n      ),\n    ),\n  },\n});\n\nexport const classMethodOrDeclareMethodCommon = () => ({\n  ...functionCommon(),\n  ...classMethodOrPropertyCommon(),\n  params: {\n    validate: chain(\n      assertValueType(\"array\"),\n      assertEach(\n        assertNodeType(\n          \"Identifier\",\n          \"Pattern\",\n          \"RestElement\",\n          \"TSParameterProperty\",\n        ),\n      ),\n    ),\n  },\n  kind: {\n    validate: assertOneOf(\"get\", \"set\", \"method\", \"constructor\"),\n    default: \"method\",\n  },\n  access: {\n    validate: chain(\n      assertValueType(\"string\"),\n      assertOneOf(\"public\", \"private\", \"protected\"),\n    ),\n    optional: true,\n  },\n  decorators: {\n    validate: chain(\n      assertValueType(\"array\"),\n      assertEach(assertNodeType(\"Decorator\")),\n    ),\n    optional: true,\n  },\n});\n\ndefineType(\"ClassMethod\", {\n  aliases: [\"Function\", \"Scopable\", \"BlockParent\", \"FunctionParent\", \"Method\"],\n  builder: [\n    \"kind\",\n    \"key\",\n    \"params\",\n    \"body\",\n    \"computed\",\n    \"static\",\n    \"generator\",\n    \"async\",\n  ],\n  visitor: [\n    \"key\",\n    \"params\",\n    \"body\",\n    \"decorators\",\n    \"returnType\",\n    \"typeParameters\",\n  ],\n  fields: {\n    ...classMethodOrDeclareMethodCommon(),\n    ...functionTypeAnnotationCommon(),\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n});\n\ndefineType(\"ObjectPattern\", {\n  visitor: [\n    \"properties\",\n    \"typeAnnotation\",\n    \"decorators\" /* for legacy param decorators */,\n  ],\n  builder: [\"properties\"],\n  aliases: [\"Pattern\", \"PatternLike\", \"LVal\"],\n  fields: {\n    ...patternLikeCommon(),\n    properties: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"RestElement\", \"ObjectProperty\")),\n      ),\n    },\n  },\n});\n\ndefineType(\"SpreadElement\", {\n  visitor: [\"argument\"],\n  aliases: [\"UnaryLike\"],\n  deprecatedAlias: \"SpreadProperty\",\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\n  \"Super\",\n  process.env.BABEL_8_BREAKING\n    ? undefined\n    : {\n        aliases: [\"Expression\"],\n      },\n);\n\ndefineType(\"TaggedTemplateExpression\", {\n  visitor: [\"tag\", \"quasi\", \"typeParameters\"],\n  builder: [\"tag\", \"quasi\"],\n  aliases: [\"Expression\"],\n  fields: {\n    tag: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    quasi: {\n      validate: assertNodeType(\"TemplateLiteral\"),\n    },\n    typeParameters: {\n      validate: assertNodeType(\n        \"TypeParameterInstantiation\",\n        \"TSTypeParameterInstantiation\",\n      ),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"TemplateElement\", {\n  builder: [\"value\", \"tail\"],\n  fields: {\n    value: {\n      validate: chain(\n        assertShape({\n          raw: {\n            validate: assertValueType(\"string\"),\n          },\n          cooked: {\n            validate: assertValueType(\"string\"),\n            optional: true,\n          },\n        }),\n        function templateElementCookedValidator(node: t.TemplateElement) {\n          const raw = node.value.raw;\n\n          let unterminatedCalled = false;\n\n          const error = () => {\n            // unreachable\n            throw new Error(\"Internal @babel/types error.\");\n          };\n          const { str, firstInvalidLoc } = readStringContents(\n            \"template\",\n            raw,\n            0,\n            0,\n            0,\n            {\n              unterminated() {\n                unterminatedCalled = true;\n              },\n              strictNumericEscape: error,\n              invalidEscapeSequence: error,\n              numericSeparatorInEscapeSequence: error,\n              unexpectedNumericSeparator: error,\n              invalidDigit: error,\n              invalidCodePoint: error,\n            },\n          );\n          if (!unterminatedCalled) throw new Error(\"Invalid raw\");\n\n          node.value.cooked = firstInvalidLoc ? null : str;\n        },\n      ),\n    },\n    tail: {\n      default: false,\n    },\n  },\n});\n\ndefineType(\"TemplateLiteral\", {\n  visitor: [\"quasis\", \"expressions\"],\n  aliases: [\"Expression\", \"Literal\"],\n  fields: {\n    quasis: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"TemplateElement\")),\n      ),\n    },\n    expressions: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"Expression\",\n            // For TypeScript template literal types\n            \"TSType\",\n          ),\n        ),\n        function (node: t.TemplateLiteral, key, val) {\n          if (node.quasis.length !== val.length + 1) {\n            throw new TypeError(\n              `Number of ${\n                node.type\n              } quasis should be exactly one more than the number of expressions.\\nExpected ${\n                val.length + 1\n              } quasis but got ${node.quasis.length}`,\n            );\n          }\n        } as Validator,\n      ),\n    },\n  },\n});\n\ndefineType(\"YieldExpression\", {\n  builder: [\"argument\", \"delegate\"],\n  visitor: [\"argument\"],\n  aliases: [\"Expression\", \"Terminatorless\"],\n  fields: {\n    delegate: {\n      validate: chain(\n        assertValueType(\"boolean\"),\n        Object.assign(\n          function (node: t.YieldExpression, key, val) {\n            if (!process.env.BABEL_TYPES_8_BREAKING) return;\n\n            if (val && !node.argument) {\n              throw new TypeError(\n                \"Property delegate of YieldExpression cannot be true if there is no argument\",\n              );\n            }\n          } as Validator,\n          { type: \"boolean\" },\n        ),\n      ),\n      default: false,\n    },\n    argument: {\n      optional: true,\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\n// --- ES2017 ---\ndefineType(\"AwaitExpression\", {\n  builder: [\"argument\"],\n  visitor: [\"argument\"],\n  aliases: [\"Expression\", \"Terminatorless\"],\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\n// --- ES2019 ---\ndefineType(\"Import\", {\n  aliases: [\"Expression\"],\n});\n\n// --- ES2020 ---\ndefineType(\"BigIntLiteral\", {\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n  aliases: [\"Expression\", \"Pureish\", \"Literal\", \"Immutable\"],\n});\n\ndefineType(\"ExportNamespaceSpecifier\", {\n  visitor: [\"exported\"],\n  aliases: [\"ModuleSpecifier\"],\n  fields: {\n    exported: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"OptionalMemberExpression\", {\n  builder: [\"object\", \"property\", \"computed\", \"optional\"],\n  visitor: [\"object\", \"property\"],\n  aliases: [\"Expression\"],\n  fields: {\n    object: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    property: {\n      validate: (function () {\n        const normal = assertNodeType(\"Identifier\");\n        const computed = assertNodeType(\"Expression\");\n\n        const validator: Validator = Object.assign(\n          function (node: t.OptionalMemberExpression, key, val) {\n            const validator = node.computed ? computed : normal;\n            validator(node, key, val);\n          } as Validator,\n          // todo(ts): can be discriminated union by `computed` property\n          { oneOfNodeTypes: [\"Expression\", \"Identifier\"] },\n        );\n        return validator;\n      })(),\n    },\n    computed: {\n      default: false,\n    },\n    optional: {\n      validate: !process.env.BABEL_TYPES_8_BREAKING\n        ? assertValueType(\"boolean\")\n        : chain(assertValueType(\"boolean\"), assertOptionalChainStart()),\n    },\n  },\n});\n\ndefineType(\"OptionalCallExpression\", {\n  visitor: [\"callee\", \"arguments\", \"typeParameters\", \"typeArguments\"],\n  builder: [\"callee\", \"arguments\", \"optional\"],\n  aliases: [\"Expression\"],\n  fields: {\n    callee: {\n      validate: assertNodeType(\"Expression\"),\n    },\n    arguments: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"Expression\",\n            \"SpreadElement\",\n            \"JSXNamespacedName\",\n            \"ArgumentPlaceholder\",\n          ),\n        ),\n      ),\n    },\n    optional: {\n      validate: !process.env.BABEL_TYPES_8_BREAKING\n        ? assertValueType(\"boolean\")\n        : chain(assertValueType(\"boolean\"), assertOptionalChainStart()),\n    },\n    typeArguments: {\n      validate: assertNodeType(\"TypeParameterInstantiation\"),\n      optional: true,\n    },\n    typeParameters: {\n      validate: assertNodeType(\"TSTypeParameterInstantiation\"),\n      optional: true,\n    },\n  },\n});\n\n// --- ES2022 ---\ndefineType(\"ClassProperty\", {\n  visitor: [\"key\", \"value\", \"typeAnnotation\", \"decorators\"],\n  builder: [\n    \"key\",\n    \"value\",\n    \"typeAnnotation\",\n    \"decorators\",\n    \"computed\",\n    \"static\",\n  ],\n  aliases: [\"Property\"],\n  fields: {\n    ...classMethodOrPropertyCommon(),\n    value: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    definite: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    typeAnnotation: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n        : assertNodeType(\n            \"TypeAnnotation\",\n            \"TSTypeAnnotation\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n    readonly: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    declare: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    variance: {\n      validate: assertNodeType(\"Variance\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassAccessorProperty\", {\n  visitor: [\"key\", \"value\", \"typeAnnotation\", \"decorators\"],\n  builder: [\n    \"key\",\n    \"value\",\n    \"typeAnnotation\",\n    \"decorators\",\n    \"computed\",\n    \"static\",\n  ],\n  aliases: [\"Property\", \"Accessor\"],\n  fields: {\n    ...classMethodOrPropertyCommon(),\n    key: {\n      validate: chain(\n        (function () {\n          const normal = assertNodeType(\n            \"Identifier\",\n            \"StringLiteral\",\n            \"NumericLiteral\",\n            \"BigIntLiteral\",\n            \"PrivateName\",\n          );\n          const computed = assertNodeType(\"Expression\");\n\n          return function (node: any, key: string, val: any) {\n            const validator = node.computed ? computed : normal;\n            validator(node, key, val);\n          };\n        })(),\n        assertNodeType(\n          \"Identifier\",\n          \"StringLiteral\",\n          \"NumericLiteral\",\n          \"BigIntLiteral\",\n          \"Expression\",\n          \"PrivateName\",\n        ),\n      ),\n    },\n    value: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    definite: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    typeAnnotation: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n        : assertNodeType(\n            \"TypeAnnotation\",\n            \"TSTypeAnnotation\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n    readonly: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    declare: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    variance: {\n      validate: assertNodeType(\"Variance\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassPrivateProperty\", {\n  visitor: [\"key\", \"value\", \"decorators\", \"typeAnnotation\"],\n  builder: [\"key\", \"value\", \"decorators\", \"static\"],\n  aliases: [\"Property\", \"Private\"],\n  fields: {\n    key: {\n      validate: assertNodeType(\"PrivateName\"),\n    },\n    value: {\n      validate: assertNodeType(\"Expression\"),\n      optional: true,\n    },\n    typeAnnotation: {\n      validate: process.env.BABEL_8_BREAKING\n        ? assertNodeType(\"TypeAnnotation\", \"TSTypeAnnotation\")\n        : assertNodeType(\n            \"TypeAnnotation\",\n            \"TSTypeAnnotation\",\n            // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n            \"Noop\",\n          ),\n      optional: true,\n    },\n    decorators: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Decorator\")),\n      ),\n      optional: true,\n    },\n    static: {\n      validate: assertValueType(\"boolean\"),\n      default: false,\n    },\n    readonly: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    definite: {\n      validate: assertValueType(\"boolean\"),\n      optional: true,\n    },\n    variance: {\n      validate: assertNodeType(\"Variance\"),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"ClassPrivateMethod\", {\n  builder: [\"kind\", \"key\", \"params\", \"body\", \"static\"],\n  visitor: [\n    \"key\",\n    \"params\",\n    \"body\",\n    \"decorators\",\n    \"returnType\",\n    \"typeParameters\",\n  ],\n  aliases: [\n    \"Function\",\n    \"Scopable\",\n    \"BlockParent\",\n    \"FunctionParent\",\n    \"Method\",\n    \"Private\",\n  ],\n  fields: {\n    ...classMethodOrDeclareMethodCommon(),\n    ...functionTypeAnnotationCommon(),\n    kind: {\n      validate: assertOneOf(\"get\", \"set\", \"method\"),\n      default: \"method\",\n    },\n    key: {\n      validate: assertNodeType(\"PrivateName\"),\n    },\n    body: {\n      validate: assertNodeType(\"BlockStatement\"),\n    },\n  },\n});\n\ndefineType(\"PrivateName\", {\n  visitor: [\"id\"],\n  aliases: [\"Private\"],\n  fields: {\n    id: {\n      validate: assertNodeType(\"Identifier\"),\n    },\n  },\n});\n\ndefineType(\"StaticBlock\", {\n  visitor: [\"body\"],\n  fields: {\n    body: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"Statement\")),\n      ),\n    },\n  },\n  aliases: [\"Scopable\", \"BlockParent\", \"FunctionParent\"],\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,GAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AACA,IAAAE,0BAAA,GAAAF,OAAA;AAEA,IAAAG,mBAAA,GAAAH,OAAA;AAEA,IAAAI,UAAA,GAAAJ,OAAA;AAQA,IAAAK,MAAA,GAAAL,OAAA;AAcA,MAAMM,UAAU,GAAG,IAAAC,wBAAiB,EAAC,cAAc,CAAC;AAEpDD,UAAU,CAAC,iBAAiB,EAAE;EAC5BE,MAAM,EAAE;IACNC,QAAQ,EAAE;MACRC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EACR,IAAAC,4BAAqB,EAAC,MAAM,EAAE,YAAY,EAAE,eAAe,CAC7D,CACF,CAAC;MACDC,OAAO,EAAE,CAACC,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAAG,EAAE,GAAGC;IACtD;EACF,CAAC;EACDC,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEFf,UAAU,CAAC,sBAAsB,EAAE;EACjCE,MAAM,EAAE;IACNc,QAAQ,EAAE;MACRZ,QAAQ,EAAG,YAAY;QACrB,IAAI,CAACM,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;UACvC,OAAO,IAAAN,sBAAe,EAAC,QAAQ,CAAC;QAClC;QAEA,MAAMW,UAAU,GAAG,IAAAC,kBAAW,EAAC,GAAGC,+BAAoB,CAAC;QACvD,MAAMC,OAAO,GAAG,IAAAF,kBAAW,EAAC,GAAG,CAAC;QAEhC,OAAO,UAAUG,IAA4B,EAAEC,GAAG,EAAEC,GAAG,EAAE;UACvD,MAAMC,SAAS,GAAG,IAAAC,WAAE,EAAC,SAAS,EAAEJ,IAAI,CAACK,IAAI,CAAC,GAAGN,OAAO,GAAGH,UAAU;UACjEO,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC;MACH,CAAC,CAAE;IACL,CAAC;IACDG,IAAI,EAAE;MACJtB,QAAQ,EAAE,CAACM,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACzC,IAAAe,qBAAc,EAAC,MAAM,CAAC,GACtB,IAAAA,qBAAc,EACZ,YAAY,EACZ,kBAAkB,EAClB,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,uBAAuB,EACvB,iBAAiB,EACjB,qBACF;IACN,CAAC;IACDC,KAAK,EAAE;MACLxB,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF,CAAC;EACDE,OAAO,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC;EACtCf,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC1BC,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEFf,UAAU,CAAC,kBAAkB,EAAE;EAC7B6B,OAAO,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC;EACtC3B,MAAM,EAAE;IACNc,QAAQ,EAAE;MACRZ,QAAQ,EAAE,IAAAc,kBAAW,EAAC,GAAGY,2BAAgB;IAC3C,CAAC;IACDJ,IAAI,EAAE;MACJtB,QAAQ,EAAG,YAAY;QACrB,MAAM2B,UAAU,GAAG,IAAAJ,qBAAc,EAAC,YAAY,CAAC;QAC/C,MAAMK,IAAI,GAAG,IAAAL,qBAAc,EAAC,YAAY,EAAE,aAAa,CAAC;QAExD,MAAMH,SAAoB,GAAGS,MAAM,CAACC,MAAM,CACxC,UAAUb,IAAwB,EAAEC,GAAG,EAAEC,GAAG,EAAE;UAC5C,MAAMC,SAAS,GAAGH,IAAI,CAACL,QAAQ,KAAK,IAAI,GAAGgB,IAAI,GAAGD,UAAU;UAC5DP,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC,EAED;UAAEY,cAAc,EAAE,CAAC,YAAY,EAAE,aAAa;QAAE,CAClD,CAAC;QACD,OAAOX,SAAS;MAClB,CAAC,CAAE;IACL,CAAC;IACDI,KAAK,EAAE;MACLxB,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF,CAAC;EACDb,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC1BC,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY;AAClC,CAAC,CAAC;AAEFf,UAAU,CAAC,sBAAsB,EAAE;EACjC6B,OAAO,EAAE,CAAC,OAAO,CAAC;EAClB3B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAE,sBAAe,EAAC,QAAQ;IACpC;EACF;AACF,CAAC,CAAC;AAEFN,UAAU,CAAC,WAAW,EAAE;EACtBc,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBZ,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,kBAAkB;IAC7C;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,kBAAkB,EAAE;EAC7B6B,OAAO,EAAE,CAAC,OAAO,CAAC;EAClB3B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAE,sBAAe,EAAC,QAAQ;IACpC;EACF;AACF,CAAC,CAAC;AAEFN,UAAU,CAAC,gBAAgB,EAAE;EAC3B6B,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;EAC/Bf,OAAO,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC;EAC/BZ,MAAM,EAAE;IACNmC,UAAU,EAAE;MACVjC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC,CAAC;MACDlB,OAAO,EAAE;IACX,CAAC;IACD6B,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC;IACF;EACF,CAAC;EACDZ,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW;AAC3D,CAAC,CAAC;AAEFf,UAAU,CAAC,gBAAgB,EAAE;EAC3Bc,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBZ,MAAM,EAAE;IACNqC,KAAK,EAAE;MACLnC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ;EACF,CAAC;EACDzB,OAAO,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,qBAAqB;AAChE,CAAC,CAAC;AAEFf,UAAU,CAAC,gBAAgB,EAAE;EAC3Bc,OAAO,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,CAAC;EACnEe,OAAO,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC;EAChCd,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBb,MAAM,EAAA+B,MAAA,CAAAC,MAAA;IACJO,MAAM,EAAE;MACNrC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,EAAE,OAAO,EAAE,uBAAuB;IACzE,CAAC;IACDe,SAAS,EAAE;MACTtC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EACR,IAAAoB,qBAAc,EACZ,YAAY,EACZ,eAAe,EACf,mBAAmB,EACnB,qBACF,CACF,CACF;IACF;EAAC,GACG,CAACjB,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACnC;IACE4B,QAAQ,EAAE;MACRpC,QAAQ,EAAE,IAAAc,kBAAW,EAAC,IAAI,EAAE,KAAK,CAAC;MAClCsB,QAAQ,EAAE;IACZ;EACF,CAAC,GACD,CAAC,CAAC;IACNG,aAAa,EAAE;MACbvC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,4BAA4B,CAAC;MACtDa,QAAQ,EAAE;IACZ,CAAC;IACDI,cAAc,EAAE;MACdxC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,8BAA8B,CAAC;MACxDa,QAAQ,EAAE;IACZ;EAAC;AAEL,CAAC,CAAC;AAEFxC,UAAU,CAAC,aAAa,EAAE;EACxBc,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;EAC1BZ,MAAM,EAAE;IACN2C,KAAK,EAAE;MACLzC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,EAAE,cAAc,EAAE,eAAe,CAAC;MACvEa,QAAQ,EAAE;IACZ,CAAC;IACDF,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,gBAAgB;IAC3C;EACF,CAAC;EACDZ,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa;AACrC,CAAC,CAAC;AAEFf,UAAU,CAAC,uBAAuB,EAAE;EAClCc,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,CAAC;EAC5CZ,MAAM,EAAE;IACN4C,IAAI,EAAE;MACJ1C,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDoB,UAAU,EAAE;MACV3C,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDqB,SAAS,EAAE;MACT5C,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF,CAAC;EACDZ,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa;AACvC,CAAC,CAAC;AAEFf,UAAU,CAAC,mBAAmB,EAAE;EAC9Bc,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBZ,MAAM,EAAE;IACNqC,KAAK,EAAE;MACLnC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ;EACF,CAAC;EACDzB,OAAO,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,qBAAqB;AAChE,CAAC,CAAC;AAEFf,UAAU,CAAC,mBAAmB,EAAE;EAC9Be,OAAO,EAAE,CAAC,WAAW;AACvB,CAAC,CAAC;AAEFf,UAAU,CAAC,kBAAkB,EAAE;EAC7Bc,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACzBZ,MAAM,EAAE;IACN4C,IAAI,EAAE;MACJ1C,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDW,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,WAAW;IACtC;EACF,CAAC;EACDZ,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU;AACnE,CAAC,CAAC;AAEFf,UAAU,CAAC,gBAAgB,EAAE;EAC3Be,OAAO,EAAE,CAAC,WAAW;AACvB,CAAC,CAAC;AAEFf,UAAU,CAAC,qBAAqB,EAAE;EAChCc,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBZ,MAAM,EAAE;IACN6B,UAAU,EAAE;MACV3B,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF,CAAC;EACDZ,OAAO,EAAE,CAAC,WAAW,EAAE,mBAAmB;AAC5C,CAAC,CAAC;AAEFf,UAAU,CAAC,MAAM,EAAE;EACjB6B,OAAO,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;EAC1Cf,OAAO,EAAE,CAAC,SAAS,CAAC;EACpBZ,MAAM,EAAE;IACN+C,OAAO,EAAE;MACP7C,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,SAAS;IACpC,CAAC;IACDuB,QAAQ,EAAE;MACR9C,QAAQ,EAAE,CAACM,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACzCqB,MAAM,CAACC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;QACtBiB,IAAI,EAAE;UAAEhB,cAAc,EAAE,CAAC,cAAc,EAAE,aAAa;QAAE;MAC1D,CAAC,CAAC,GACF,IAAA5B,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,cAAc,EAAE,aAAa,CAAC,CAAC;MAC7Da,QAAQ,EAAE;IACZ,CAAC;IACDY,MAAM,EAAE;MAENhD,QAAQ,EAAE,IAAAG,iBAAU,EAAC0B,MAAM,CAACC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;QAAEmB,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;MAC9Db,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEFxC,UAAU,CAAC,gBAAgB,EAAE;EAC3Bc,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;EAClCC,OAAO,EAAE,CACP,UAAU,EACV,WAAW,EACX,KAAK,EACL,aAAa,EACb,MAAM,EACN,eAAe,CAChB;EACDb,MAAM,EAAE;IACNwB,IAAI,EAAE;MACJtB,QAAQ,EAAE,CAACM,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACzC,IAAAe,qBAAc,EAAC,qBAAqB,EAAE,MAAM,CAAC,GAC7C,IAAAA,qBAAc,EACZ,qBAAqB,EACrB,YAAY,EACZ,kBAAkB,EAClB,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,uBAAuB,EACvB,iBAAiB,EACjB,qBACF;IACN,CAAC;IACDC,KAAK,EAAE;MACLxB,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDW,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,WAAW;IACtC;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,cAAc,EAAE;EACzBc,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC;EAC3CC,OAAO,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,aAAa,EAAE,MAAM,CAAC;EAChEb,MAAM,EAAE;IACNoD,IAAI,EAAE;MACJlD,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,qBAAqB,EAAE,YAAY,CAAC;MAC7Da,QAAQ,EAAE;IACZ,CAAC;IACDM,IAAI,EAAE;MACJ1C,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACDe,MAAM,EAAE;MACNnD,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACDF,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,WAAW;IACtC;EACF;AACF,CAAC,CAAC;AAEK,MAAM6B,cAAc,GAAGA,CAAA,MAAO;EACnCC,MAAM,EAAE;IACNrD,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,YAAY,EAAE,SAAS,EAAE,aAAa,CAAC,CACnE;EACF,CAAC;EACD+B,SAAS,EAAE;IACTjD,OAAO,EAAE;EACX,CAAC;EACDkD,KAAK,EAAE;IACLlD,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AAACmD,OAAA,CAAAJ,cAAA,GAAAA,cAAA;AAEI,MAAMK,4BAA4B,GAAGA,CAAA,MAAO;EACjDC,UAAU,EAAE;IACV1D,QAAQ,EAEJ,IAAAuB,qBAAc,EACZ,gBAAgB,EAChB,kBAAkB,EAElB,MACF,CAAC;IACLa,QAAQ,EAAE;EACZ,CAAC;EACDI,cAAc,EAAE;IACdxC,QAAQ,EAEJ,IAAAuB,qBAAc,EACZ,0BAA0B,EAC1B,4BAA4B,EAE5B,MACF,CAAC;IACLa,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAACoB,OAAA,CAAAC,4BAAA,GAAAA,4BAAA;AAEI,MAAME,yBAAyB,GAAGA,CAAA,KAAA9B,MAAA,CAAAC,MAAA,KACpCsB,cAAc,CAAC,CAAC;EACnBQ,OAAO,EAAE;IACP5D,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;IACpCkC,QAAQ,EAAE;EACZ,CAAC;EACDyB,EAAE,EAAE;IACF7D,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,CAAC;IACtCa,QAAQ,EAAE;EACZ;AAAC,EACD;AAACoB,OAAA,CAAAG,yBAAA,GAAAA,yBAAA;AAEH/D,UAAU,CAAC,qBAAqB,EAAE;EAChC6B,OAAO,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC;EACvDf,OAAO,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,CAAC;EACjEZ,MAAM,EAAA+B,MAAA,CAAAC,MAAA,KACD6B,yBAAyB,CAAC,CAAC,EAC3BF,4BAA4B,CAAC,CAAC;IACjCvB,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,gBAAgB;IAC3C,CAAC;IACDuC,SAAS,EAAE;MACT9D,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,mBAAmB,EAAE,mBAAmB,CAAC;MAClEa,QAAQ,EAAE;IACZ;EAAC,EACF;EACDzB,OAAO,EAAE,CACP,UAAU,EACV,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,SAAS,EACT,aAAa,CACd;EACDX,QAAQ,EAAG,YAAY;IACrB,IAAI,CAACM,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE,OAAO,MAAM,CAAC,CAAC;IAExD,MAAMK,UAAU,GAAG,IAAAU,qBAAc,EAAC,YAAY,CAAC;IAE/C,OAAO,UAAUwC,MAAM,EAAE7C,GAAG,EAAED,IAAI,EAAE;MAClC,IAAI,CAAC,IAAAI,WAAE,EAAC,0BAA0B,EAAE0C,MAAM,CAAC,EAAE;QAC3ClD,UAAU,CAACI,IAAI,EAAE,IAAI,EAAEA,IAAI,CAAC4C,EAAE,CAAC;MACjC;IACF,CAAC;EACH,CAAC,CAAE;AACL,CAAC,CAAC;AAEFjE,UAAU,CAAC,oBAAoB,EAAE;EAC/BoE,QAAQ,EAAE,qBAAqB;EAC/BrD,OAAO,EAAE,CACP,UAAU,EACV,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,SAAS,CACV;EACDb,MAAM,EAAA+B,MAAA,CAAAC,MAAA,KACDsB,cAAc,CAAC,CAAC,EAChBK,4BAA4B,CAAC,CAAC;IACjCI,EAAE,EAAE;MACF7D,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACDF,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,gBAAgB;IAC3C,CAAC;IACDuC,SAAS,EAAE;MACT9D,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,mBAAmB,EAAE,mBAAmB,CAAC;MAClEa,QAAQ,EAAE;IACZ;EAAC;AAEL,CAAC,CAAC;AAEK,MAAM6B,iBAAiB,GAAGA,CAAA,MAAO;EACtCC,cAAc,EAAE;IACdlE,QAAQ,EAEJ,IAAAuB,qBAAc,EACZ,gBAAgB,EAChB,kBAAkB,EAElB,MACF,CAAC;IACLa,QAAQ,EAAE;EACZ,CAAC;EACDA,QAAQ,EAAE;IACRpC,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;IACpCkC,QAAQ,EAAE;EACZ,CAAC;EACD+B,UAAU,EAAE;IACVnE,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC,CAAC;IACDa,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAACoB,OAAA,CAAAS,iBAAA,GAAAA,iBAAA;AAEHrE,UAAU,CAAC,YAAY,EAAE;EACvB6B,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBf,OAAO,EAAE,CAAC,gBAAgB,EAAE,YAAY,CAAmC;EAC3EC,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,MAAM,EAAE,cAAc,CAAC;EAC9Db,MAAM,EAAA+B,MAAA,CAAAC,MAAA,KACDmC,iBAAiB,CAAC,CAAC;IACtBG,IAAI,EAAE;MACJpE,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,QAAQ,CAAC,EACzB2B,MAAM,CAACC,MAAM,CACX,UAAUb,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;QACxB,IAAI,CAACb,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;QAEzC,IAAI,CAAC,IAAA6D,0BAAiB,EAAClD,GAAG,EAAE,KAAK,CAAC,EAAE;UAClC,MAAM,IAAImD,SAAS,CAAE,IAAGnD,GAAI,kCAAiC,CAAC;QAChE;MACF,CAAC,EACD;QAAE8B,IAAI,EAAE;MAAS,CACnB,CACF;IACF;EAAC,EACF;EACDjD,QAAQA,CAAC+D,MAAM,EAAE7C,GAAG,EAAED,IAAI,EAAE;IAC1B,IAAI,CAACX,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;IAEzC,MAAM+D,KAAK,GAAG,UAAU,CAACC,IAAI,CAACtD,GAAG,CAAC;IAClC,IAAI,CAACqD,KAAK,EAAE;IAEZ,MAAM,GAAGE,SAAS,CAAC,GAAGF,KAAK;IAC3B,MAAMG,OAAO,GAAG;MAAEC,QAAQ,EAAE;IAAM,CAAC;IAInC,IAAIF,SAAS,KAAK,UAAU,EAAE;MAC5B,IAAI,IAAApD,WAAE,EAAC,kBAAkB,EAAE0C,MAAM,EAAEW,OAAO,CAAC,EAAE;MAC7C,IAAI,IAAArD,WAAE,EAAC,0BAA0B,EAAE0C,MAAM,EAAEW,OAAO,CAAC,EAAE;IACvD,CAAC,MAAM,IAAID,SAAS,KAAK,KAAK,EAAE;MAC9B,IAAI,IAAApD,WAAE,EAAC,UAAU,EAAE0C,MAAM,EAAEW,OAAO,CAAC,EAAE;MACrC,IAAI,IAAArD,WAAE,EAAC,QAAQ,EAAE0C,MAAM,EAAEW,OAAO,CAAC,EAAE;IACrC,CAAC,MAAM,IAAID,SAAS,KAAK,UAAU,EAAE;MACnC,IAAI,IAAApD,WAAE,EAAC,iBAAiB,EAAE0C,MAAM,CAAC,EAAE;IACrC,CAAC,MAAM,IAAIU,SAAS,KAAK,UAAU,EAAE;MACnC,IAAI,IAAApD,WAAE,EAAC,iBAAiB,EAAE0C,MAAM,EAAE;QAAEa,QAAQ,EAAE3D;MAAK,CAAC,CAAC,EAAE;IACzD,CAAC,MAAM,IAAIwD,SAAS,KAAK,MAAM,EAAE;MAC/B,IAAI,IAAApD,WAAE,EAAC,cAAc,EAAE0C,MAAM,EAAE;QAAEc,IAAI,EAAE5D;MAAK,CAAC,CAAC,EAAE;IAClD;IAEA,IAIE,CAAC,IAAA6D,oCAAS,EAAC7D,IAAI,CAACmD,IAAI,CAAC,IAAI,IAAAW,yCAAc,EAAC9D,IAAI,CAACmD,IAAI,EAAE,KAAK,CAAC,KAGzDnD,IAAI,CAACmD,IAAI,KAAK,MAAM,EACpB;MACA,MAAM,IAAIE,SAAS,CAAE,IAAGrD,IAAI,CAACmD,IAAK,6BAA4B,CAAC;IACjE;EACF;AACF,CAAC,CAAC;AAEFxE,UAAU,CAAC,aAAa,EAAE;EACxBc,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,CAAC;EAC5CC,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;EACrCb,MAAM,EAAE;IACN4C,IAAI,EAAE;MACJ1C,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDoB,UAAU,EAAE;MACV3C,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,WAAW;IACtC,CAAC;IACDqB,SAAS,EAAE;MACTR,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,WAAW;IACtC;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,kBAAkB,EAAE;EAC7Bc,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;EAC1BC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBb,MAAM,EAAE;IACNqC,KAAK,EAAE;MACLnC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDW,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,WAAW;IACtC;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,eAAe,EAAE;EAC1B6B,OAAO,EAAE,CAAC,OAAO,CAAC;EAClB3B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAE,sBAAe,EAAC,QAAQ;IACpC;EACF,CAAC;EACDS,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW;AAC3D,CAAC,CAAC;AAEFf,UAAU,CAAC,gBAAgB,EAAE;EAC3B6B,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBuD,eAAe,EAAE,eAAe;EAChClF,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAE,sBAAe,EAAC,QAAQ;IACpC;EACF,CAAC;EACDS,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW;AAC3D,CAAC,CAAC;AAEFf,UAAU,CAAC,aAAa,EAAE;EACxBe,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW;AAC3D,CAAC,CAAC;AAEFf,UAAU,CAAC,gBAAgB,EAAE;EAC3B6B,OAAO,EAAE,CAAC,OAAO,CAAC;EAClB3B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS;IACrC;EACF,CAAC;EACDS,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW;AAC3D,CAAC,CAAC;AAEFf,UAAU,CAAC,eAAe,EAAE;EAC1B6B,OAAO,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC;EAC7BuD,eAAe,EAAE,cAAc;EAC/BrE,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,CAAC;EAC7Cb,MAAM,EAAE;IACNkB,OAAO,EAAE;MACPhB,QAAQ,EAAE,IAAAE,sBAAe,EAAC,QAAQ;IACpC,CAAC;IACD+E,KAAK,EAAE;MACLjF,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,QAAQ,CAAC,EACzB2B,MAAM,CAACC,MAAM,CACX,UAAUb,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;QACxB,IAAI,CAACb,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;QAEzC,MAAM0E,OAAO,GAAG,WAAW,CAACV,IAAI,CAACrD,GAAG,CAAC;QACrC,IAAI+D,OAAO,EAAE;UACX,MAAM,IAAIZ,SAAS,CAAE,IAAGY,OAAO,CAAC,CAAC,CAAE,8BAA6B,CAAC;QACnE;MACF,CAAC,EACD;QAAEjC,IAAI,EAAE;MAAS,CACnB,CACF,CAAC;MACD5C,OAAO,EAAE;IACX;EACF;AACF,CAAC,CAAC;AAEFT,UAAU,CAAC,mBAAmB,EAAE;EAC9B6B,OAAO,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC;EACtCf,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC1BC,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;EACjCb,MAAM,EAAE;IACNc,QAAQ,EAAE;MACRZ,QAAQ,EAAE,IAAAc,kBAAW,EAAC,GAAGqE,4BAAiB;IAC5C,CAAC;IACD7D,IAAI,EAAE;MACJtB,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDC,KAAK,EAAE;MACLxB,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,kBAAkB,EAAE;EAC7B6B,OAAO,EAAE,CACP,QAAQ,EACR,UAAU,EACV,UAAU,EACV,IAAI,CAACnB,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAC7D;EACDE,OAAO,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;EAC/BC,OAAO,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC;EAC/Bb,MAAM,EAAA+B,MAAA,CAAAC,MAAA;IACJsD,MAAM,EAAE;MACNpF,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,EAAE,OAAO;IAChD,CAAC;IACD8D,QAAQ,EAAE;MACRrF,QAAQ,EAAG,YAAY;QACrB,MAAMsF,MAAM,GAAG,IAAA/D,qBAAc,EAAC,YAAY,EAAE,aAAa,CAAC;QAC1D,MAAMoD,QAAQ,GAAG,IAAApD,qBAAc,EAAC,YAAY,CAAC;QAE7C,MAAMH,SAAoB,GAAG,SAAAA,CAC3BH,IAAwB,EACxBC,GAAG,EACHC,GAAG,EACH;UACA,MAAMC,SAAoB,GAAGH,IAAI,CAAC0D,QAAQ,GAAGA,QAAQ,GAAGW,MAAM;UAC9DlE,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC;QAEDC,SAAS,CAACW,cAAc,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC;QACtE,OAAOX,SAAS;MAClB,CAAC,CAAE;IACL,CAAC;IACDuD,QAAQ,EAAE;MACRtE,OAAO,EAAE;IACX;EAAC,GACG,CAACC,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACnC;IACE4B,QAAQ,EAAE;MACRpC,QAAQ,EAAE,IAAAc,kBAAW,EAAC,IAAI,EAAE,KAAK,CAAC;MAClCsB,QAAQ,EAAE;IACZ;EACF,CAAC,GACD,CAAC,CAAC;AAEV,CAAC,CAAC;AAEFxC,UAAU,CAAC,eAAe,EAAE;EAAEoE,QAAQ,EAAE;AAAiB,CAAC,CAAC;AAE3DpE,UAAU,CAAC,SAAS,EAAE;EAGpBc,OAAO,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC;EAC/Be,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC;EAC5D3B,MAAM,EAAE;IACNyF,UAAU,EAAE;MACVvF,QAAQ,EAAE,IAAAE,sBAAe,EAAC,QAAQ;IACpC,CAAC;IACDsF,UAAU,EAAE;MACVxF,QAAQ,EAAE,IAAAc,kBAAW,EAAC,QAAQ,EAAE,QAAQ,CAAC;MACzCT,OAAO,EAAE;IACX,CAAC;IACDoF,WAAW,EAAE;MACXzF,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,sBAAsB,CAAC;MAChDlB,OAAO,EAAE,IAAI;MACb+B,QAAQ,EAAE;IACZ,CAAC;IACDH,UAAU,EAAE;MACVjC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC,CAAC;MACDlB,OAAO,EAAE;IACX,CAAC;IACD6B,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC;IACF;EACF,CAAC;EACDZ,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,OAAO;AAC9C,CAAC,CAAC;AAEFf,UAAU,CAAC,kBAAkB,EAAE;EAC7Bc,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBb,MAAM,EAAE;IACN4F,UAAU,EAAE;MACV1F,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EACR,IAAAoB,qBAAc,EAAC,cAAc,EAAE,gBAAgB,EAAE,eAAe,CAClE,CACF;IACF;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,cAAc,EAAE;EACzB6B,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;EAC5E3B,MAAM,EAAA+B,MAAA,CAAAC,MAAA,KACDsB,cAAc,CAAC,CAAC,EAChBK,4BAA4B,CAAC,CAAC;IACjCkC,IAAI,EAAA9D,MAAA,CAAAC,MAAA;MACF9B,QAAQ,EAAE,IAAAc,kBAAW,EAAC,QAAQ,EAAE,KAAK,EAAE,KAAK;IAAC,GACzC,CAACR,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAAG;MAAEH,OAAO,EAAE;IAAS,CAAC,GAAG,CAAC,CAAC,CACrE;IACDsE,QAAQ,EAAE;MACRtE,OAAO,EAAE;IACX,CAAC;IACDa,GAAG,EAAE;MACHlB,QAAQ,EAAG,YAAY;QACrB,MAAMsF,MAAM,GAAG,IAAA/D,qBAAc,EAC3B,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eACF,CAAC;QACD,MAAMoD,QAAQ,GAAG,IAAApD,qBAAc,EAAC,YAAY,CAAC;QAE7C,MAAMH,SAAoB,GAAG,SAAAA,CAAUH,IAAoB,EAAEC,GAAG,EAAEC,GAAG,EAAE;UACrE,MAAMC,SAAS,GAAGH,IAAI,CAAC0D,QAAQ,GAAGA,QAAQ,GAAGW,MAAM;UACnDlE,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC;QAEDC,SAAS,CAACW,cAAc,GAAG,CACzB,YAAY,EACZ,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eAAe,CAChB;QACD,OAAOX,SAAS;MAClB,CAAC,CAAE;IACL,CAAC;IACD+C,UAAU,EAAE;MACVnE,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC,CAAC;MACDa,QAAQ,EAAE;IACZ,CAAC;IACDF,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,gBAAgB;IAC3C;EAAC,EACF;EACDb,OAAO,EAAE,CACP,KAAK,EACL,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,gBAAgB,CACjB;EACDC,OAAO,EAAE,CACP,mBAAmB,EACnB,UAAU,EACV,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,QAAQ,EACR,cAAc;AAElB,CAAC,CAAC;AAEFf,UAAU,CAAC,gBAAgB,EAAE;EAC3B6B,OAAO,EAAE,CACP,KAAK,EACL,OAAO,EACP,UAAU,EACV,WAAW,EACX,IAAI,CAACnB,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAAG,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,CAC/D;EACDV,MAAM,EAAE;IACN6E,QAAQ,EAAE;MACRtE,OAAO,EAAE;IACX,CAAC;IACDa,GAAG,EAAE;MACHlB,QAAQ,EAAG,YAAY;QACrB,MAAMsF,MAAM,GAAG,IAAA/D,qBAAc,EAC3B,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,gBAAgB,EAChB,aACF,CAAC;QACD,MAAMoD,QAAQ,GAAG,IAAApD,qBAAc,EAAC,YAAY,CAAC;QAE7C,MAAMH,SAAoB,GAAGS,MAAM,CAACC,MAAM,CACxC,UAAUb,IAAsB,EAAEC,GAAG,EAAEC,GAAG,EAAE;UAC1C,MAAMC,SAAS,GAAGH,IAAI,CAAC0D,QAAQ,GAAGA,QAAQ,GAAGW,MAAM;UACnDlE,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC,EACD;UAEEY,cAAc,EAAE,CACd,YAAY,EACZ,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,gBAAgB,EAChB,aAAa;QAEjB,CACF,CAAC;QACD,OAAOX,SAAS;MAClB,CAAC,CAAE;IACL,CAAC;IACDY,KAAK,EAAE;MAGLhC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,EAAE,aAAa;IACtD,CAAC;IACDqE,SAAS,EAAE;MACT5F,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,SAAS,CAAC,EAC1B2B,MAAM,CAACC,MAAM,CACX,UAAUb,IAAsB,EAAEC,GAAG,EAAEC,GAAG,EAAE;QAC1C,IAAI,CAACb,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;QAEzC,IAAIW,GAAG,IAAIF,IAAI,CAAC0D,QAAQ,EAAE;UACxB,MAAM,IAAIL,SAAS,CACjB,yEACF,CAAC;QACH;MACF,CAAC,EACD;QAAErB,IAAI,EAAE;MAAU,CACpB,CAAC,EACD,UAAUhC,IAAsB,EAAEC,GAAG,EAAEC,GAAG,EAAE;QAC1C,IAAI,CAACb,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;QAEzC,IAAIW,GAAG,IAAI,CAAC,IAAAE,WAAE,EAAC,YAAY,EAAEJ,IAAI,CAACC,GAAG,CAAC,EAAE;UACtC,MAAM,IAAIoD,SAAS,CACjB,iFACF,CAAC;QACH;MACF,CACF,CAAC;MACDjE,OAAO,EAAE;IACX,CAAC;IACD8D,UAAU,EAAE;MACVnE,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC,CAAC;MACDa,QAAQ,EAAE;IACZ;EACF,CAAC;EACD1B,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,YAAY,CAAC;EACvCC,OAAO,EAAE,CAAC,mBAAmB,EAAE,UAAU,EAAE,cAAc,CAAC;EAC1DX,QAAQ,EAAG,YAAY;IACrB,MAAMgB,OAAO,GAAG,IAAAO,qBAAc,EAC5B,YAAY,EACZ,SAAS,EACT,gBAAgB,EAChB,uBAAuB,EACvB,qBAAqB,EACrB,iBACF,CAAC;IACD,MAAMI,UAAU,GAAG,IAAAJ,qBAAc,EAAC,YAAY,CAAC;IAE/C,OAAO,UAAUwC,MAAM,EAAE7C,GAAG,EAAED,IAAI,EAAE;MAClC,IAAI,CAACX,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;MAEzC,MAAMY,SAAS,GAAG,IAAAC,WAAE,EAAC,eAAe,EAAE0C,MAAM,CAAC,GAAG/C,OAAO,GAAGW,UAAU;MACpEP,SAAS,CAACH,IAAI,EAAE,OAAO,EAAEA,IAAI,CAACe,KAAK,CAAC;IACtC,CAAC;EACH,CAAC,CAAE;AACL,CAAC,CAAC;AAEFpC,UAAU,CAAC,aAAa,EAAE;EACxBc,OAAO,EAAE,CAAC,UAAU,EAAE,gBAAgB,CAAC;EACvCe,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBd,OAAO,EAAE,CAAC,MAAM,EAAE,aAAa,CAAC;EAChCqE,eAAe,EAAE,cAAc;EAC/BlF,MAAM,EAAA+B,MAAA,CAAAC,MAAA,KACDmC,iBAAiB,CAAC,CAAC;IACtB4B,QAAQ,EAAE;MACR7F,QAAQ,EAAE,CAACM,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACzC,IAAAe,qBAAc,EAAC,MAAM,CAAC,GACtB,IAAAA,qBAAc,EACZ,YAAY,EACZ,cAAc,EACd,eAAe,EACf,kBAAkB,EAClB,gBAAgB,EAChB,uBAAuB,EACvB,iBAAiB,EACjB,qBACF;IACN;EAAC,EACF;EACDvB,QAAQA,CAAC+D,MAAwC,EAAE7C,GAAG,EAAE;IACtD,IAAI,CAACZ,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;IAEzC,MAAM+D,KAAK,GAAG,gBAAgB,CAACC,IAAI,CAACtD,GAAG,CAAC;IACxC,IAAI,CAACqD,KAAK,EAAE,MAAM,IAAIuB,KAAK,CAAC,sCAAsC,CAAC;IAEnE,MAAM,GAAGC,OAAO,EAAEC,KAAK,CAAC,GAAGzB,KAI1B;IACD,IAAKR,MAAM,CAACgC,OAAO,CAAC,CAAcE,MAAM,GAAG,CAACD,KAAK,GAAG,CAAC,EAAE;MACrD,MAAM,IAAI1B,SAAS,CAAE,uCAAsCyB,OAAQ,EAAC,CAAC;IACvE;EACF;AACF,CAAC,CAAC;AAEFnG,UAAU,CAAC,iBAAiB,EAAE;EAC5Bc,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,qBAAqB,CAAC;EAC/Db,MAAM,EAAE;IACN+F,QAAQ,EAAE;MACR7F,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEFxC,UAAU,CAAC,oBAAoB,EAAE;EAC/Bc,OAAO,EAAE,CAAC,aAAa,CAAC;EACxBZ,MAAM,EAAE;IACNoG,WAAW,EAAE;MACXlG,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,YAAY,CAAC,CACzC;IACF;EACF,CAAC;EACDZ,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEFf,UAAU,CAAC,yBAAyB,EAAE;EACpCc,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBC,OAAO,EAAE,CAAC,YAAY,EAAE,mBAAmB,CAAC;EAC5Cb,MAAM,EAAE;IACN6B,UAAU,EAAE;MACV3B,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,YAAY,EAAE;EACvBc,OAAO,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;EAC/BZ,MAAM,EAAE;IACN4C,IAAI,EAAE;MACJ1C,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACDO,UAAU,EAAE;MACV3C,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC;IACF;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,iBAAiB,EAAE;EAC5Bc,OAAO,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC;EAClCC,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,UAAU,CAAC;EACjDb,MAAM,EAAE;IACNqG,YAAY,EAAE;MACZnG,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACD6E,KAAK,EAAE;MACLpG,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,YAAY,CAAC,CACzC;IACF;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,gBAAgB,EAAE;EAC3Be,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEFf,UAAU,CAAC,gBAAgB,EAAE;EAC3Bc,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,qBAAqB,CAAC;EAC/Db,MAAM,EAAE;IACN+F,QAAQ,EAAE;MACR7F,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,cAAc,EAAE;EACzBc,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC;EAC1CC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBb,MAAM,EAAE;IACNuG,KAAK,EAAE;MACLrG,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAsB,qBAAc,EAAC,gBAAgB,CAAC,EAChCM,MAAM,CAACC,MAAM,CACX,UAAUb,IAAoB,EAAE;QAC9B,IAAI,CAACX,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;QAKzC,IAAI,CAACS,IAAI,CAACqF,OAAO,IAAI,CAACrF,IAAI,CAACsF,SAAS,EAAE;UACpC,MAAM,IAAIjC,SAAS,CACjB,6DACF,CAAC;QACH;MACF,CAAC,EACD;QACEvC,cAAc,EAAE,CAAC,gBAAgB;MACnC,CACF,CACF;IACF,CAAC;IACDuE,OAAO,EAAE;MACPlE,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,aAAa;IACxC,CAAC;IACDgF,SAAS,EAAE;MACTnE,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,gBAAgB;IAC3C;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,iBAAiB,EAAE;EAC5B6B,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC;EAC3C3B,MAAM,EAAE;IACN0G,MAAM,EAAE;MACNnG,OAAO,EAAE;IACX,CAAC;IACDwF,QAAQ,EAAE;MACR7F,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDX,QAAQ,EAAE;MACRZ,QAAQ,EAAE,IAAAc,kBAAW,EAAC,GAAG2F,0BAAe;IAC1C;EACF,CAAC;EACD/F,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,WAAW,EAAE,YAAY;AACrC,CAAC,CAAC;AAEFf,UAAU,CAAC,kBAAkB,EAAE;EAC7B6B,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC;EAC3C3B,MAAM,EAAE;IACN0G,MAAM,EAAE;MACNnG,OAAO,EAAE;IACX,CAAC;IACDwF,QAAQ,EAAE;MACR7F,QAAQ,EAAE,CAACM,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACzC,IAAAe,qBAAc,EAAC,YAAY,CAAC,GAC5B,IAAAA,qBAAc,EAAC,YAAY,EAAE,kBAAkB;IACrD,CAAC;IACDX,QAAQ,EAAE;MACRZ,QAAQ,EAAE,IAAAc,kBAAW,EAAC,GAAG4F,2BAAgB;IAC3C;EACF,CAAC;EACDhG,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAEFf,UAAU,CAAC,qBAAqB,EAAE;EAChC6B,OAAO,EAAE,CAAC,MAAM,EAAE,cAAc,CAAC;EACjCf,OAAO,EAAE,CAAC,cAAc,CAAC;EACzBC,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;EACrCb,MAAM,EAAE;IACN8D,OAAO,EAAE;MACP5D,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;MACpCkC,QAAQ,EAAE;IACZ,CAAC;IACDuD,IAAI,EAAE;MACJ3F,QAAQ,EAAE,IAAAc,kBAAW,EACnB,KAAK,EACL,KAAK,EACL,OAAO,EAEP,OAAO,EAEP,aACF;IACF,CAAC;IACD6F,YAAY,EAAE;MACZ3G,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,oBAAoB,CAAC,CACjD;IACF;EACF,CAAC;EACDvB,QAAQA,CAAC+D,MAAM,EAAE7C,GAAG,EAAED,IAAI,EAAE;IAC1B,IAAI,CAACX,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;IAEzC,IAAI,CAAC,IAAAa,WAAE,EAAC,eAAe,EAAE0C,MAAM,EAAE;MAAEzC,IAAI,EAAEL;IAAK,CAAC,CAAC,EAAE;IAClD,IAAIA,IAAI,CAAC0F,YAAY,CAACV,MAAM,KAAK,CAAC,EAAE;MAClC,MAAM,IAAI3B,SAAS,CAChB,8EAA6EP,MAAM,CAACd,IAAK,EAC5F,CAAC;IACH;EACF;AACF,CAAC,CAAC;AAEFrD,UAAU,CAAC,oBAAoB,EAAE;EAC/Bc,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;EACvBZ,MAAM,EAAE;IACN+D,EAAE,EAAE;MACF7D,QAAQ,EAAG,YAAY;QACrB,IAAI,CAACM,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;UACvC,OAAO,IAAAe,qBAAc,EAAC,MAAM,CAAC;QAC/B;QAEA,MAAM+D,MAAM,GAAG,IAAA/D,qBAAc,EAC3B,YAAY,EACZ,cAAc,EACd,eACF,CAAC;QACD,MAAMqF,OAAO,GAAG,IAAArF,qBAAc,EAAC,YAAY,CAAC;QAE5C,OAAO,UAAUN,IAA0B,EAAEC,GAAG,EAAEC,GAAG,EAAE;UACrD,MAAMC,SAAS,GAAGH,IAAI,CAACiC,IAAI,GAAGoC,MAAM,GAAGsB,OAAO;UAC9CxF,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC;MACH,CAAC,CAAE;IACL,CAAC;IACD0F,QAAQ,EAAE;MACRzE,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS;IACrC,CAAC;IACDgD,IAAI,EAAE;MACJd,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,gBAAgB,EAAE;EAC3Bc,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACzBC,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC;EAClEb,MAAM,EAAE;IACN4C,IAAI,EAAE;MACJ1C,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDW,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,WAAW;IACtC;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,eAAe,EAAE;EAC1Bc,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;EAC3BC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBb,MAAM,EAAE;IACNsF,MAAM,EAAE;MACNpF,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDW,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,WAAW;IACtC;EACF;AACF,CAAC,CAAC;AAGF3B,UAAU,CAAC,mBAAmB,EAAE;EAC9Bc,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,CAAmC;EAC1Ee,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;EAC1Bd,OAAO,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,MAAM,CAAC;EAC3Cb,MAAM,EAAA+B,MAAA,CAAAC,MAAA,KACDmC,iBAAiB,CAAC,CAAC;IACtB3C,IAAI,EAAE;MACJtB,QAAQ,EAAE,IAAAuB,qBAAc,EACtB,YAAY,EACZ,eAAe,EACf,cAAc,EACd,kBAAkB,EAClB,gBAAgB,EAChB,uBAAuB,EACvB,iBAAiB,EACjB,qBACF;IACF,CAAC;IACDC,KAAK,EAAE;MACLxB,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IAED4C,UAAU,EAAE;MACVnE,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC,CAAC;MACDa,QAAQ,EAAE;IACZ;EAAC;AAEL,CAAC,CAAC;AAEFxC,UAAU,CAAC,cAAc,EAAE;EACzBc,OAAO,EAAE,CAAC,UAAU,EAAE,gBAAgB,CAAC;EACvCe,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBd,OAAO,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,MAAM,CAAC;EAC3Cb,MAAM,EAAA+B,MAAA,CAAAC,MAAA,KACDmC,iBAAiB,CAAC,CAAC;IACtBlE,QAAQ,EAAE;MACRC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAC,4BAAqB,EAAC,MAAM,EAAE,aAAa,EAAE,MAAM,CAAC,CACjE;IACF;EAAC;AAEL,CAAC,CAAC;AAEFR,UAAU,CAAC,yBAAyB,EAAE;EACpC6B,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;EACpCf,OAAO,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB,CAAC;EAC3DC,OAAO,EAAE,CACP,UAAU,EACV,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,SAAS,CACV;EACDb,MAAM,EAAA+B,MAAA,CAAAC,MAAA,KACDsB,cAAc,CAAC,CAAC,EAChBK,4BAA4B,CAAC,CAAC;IACjC9B,UAAU,EAAE;MAEV3B,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS;IACrC,CAAC;IACDgC,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,gBAAgB,EAAE,YAAY;IACzD,CAAC;IACDuC,SAAS,EAAE;MACT9D,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,mBAAmB,EAAE,mBAAmB,CAAC;MAClEa,QAAQ,EAAE;IACZ;EAAC;AAEL,CAAC,CAAC;AAEFxC,UAAU,CAAC,WAAW,EAAE;EACtBc,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBZ,MAAM,EAAE;IACNoC,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EACR,IAAAoB,qBAAc,EACZ,aAAa,EACb,oBAAoB,EACpB,eAAe,EACf,sBAAsB,EACtB,uBAAuB,EACvB,iBAAiB,EACjB,kBAAkB,EAClB,aACF,CACF,CACF;IACF;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,iBAAiB,EAAE;EAC5B6B,OAAO,EAAE,CAAC,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,CAAC;EACnDf,OAAO,EAAE,CACP,IAAI,EACJ,MAAM,EACN,YAAY,EACZ,QAAQ,EACR,gBAAgB,EAChB,qBAAqB,EACrB,YAAY,EACZ,YAAY,CACb;EACDC,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC;EAC5Cb,MAAM,EAAE;IACN+D,EAAE,EAAE;MACF7D,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,CAAC;MAGtCa,QAAQ,EAAE;IACZ,CAAC;IACDI,cAAc,EAAE;MACdxC,QAAQ,EAKJ,IAAAuB,qBAAc,EACZ,0BAA0B,EAC1B,4BAA4B,EAE5B,MACF,CAAC;MACLa,QAAQ,EAAE;IACZ,CAAC;IACDF,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,WAAW;IACtC,CAAC;IACDuF,UAAU,EAAE;MACV1E,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDwF,mBAAmB,EAAE;MACnB/G,QAAQ,EAAE,IAAAuB,qBAAc,EACtB,4BAA4B,EAC5B,8BACF,CAAC;MACDa,QAAQ,EAAE;IACZ,CAAC;IACD4E,UAAU,EAAE;MACVhH,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EACR,IAAAoB,qBAAc,EAAC,+BAA+B,EAAE,iBAAiB,CACnE,CACF,CAAC;MACDa,QAAQ,EAAE;IACZ,CAAC;IACD+B,UAAU,EAAE;MACVnE,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC,CAAC;MACDa,QAAQ,EAAE;IACZ,CAAC;IACD6E,MAAM,EAAE;MACNjH,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,kBAAkB,CAAC;MAC5Ca,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEFxC,UAAU,CAAC,kBAAkB,EAAE;EAC7BoE,QAAQ,EAAE,iBAAiB;EAC3BrD,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,CAAC;EAC1Db,MAAM,EAAE;IACN+D,EAAE,EAAE;MACF7D,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDiB,cAAc,EAAE;MACdxC,QAAQ,EAKJ,IAAAuB,qBAAc,EACZ,0BAA0B,EAC1B,4BAA4B,EAE5B,MACF,CAAC;MACLa,QAAQ,EAAE;IACZ,CAAC;IACDF,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,WAAW;IACtC,CAAC;IACDuF,UAAU,EAAE;MACV1E,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDwF,mBAAmB,EAAE;MACnB/G,QAAQ,EAAE,IAAAuB,qBAAc,EACtB,4BAA4B,EAC5B,8BACF,CAAC;MACDa,QAAQ,EAAE;IACZ,CAAC;IACD4E,UAAU,EAAE;MACVhH,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EACR,IAAAoB,qBAAc,EAAC,+BAA+B,EAAE,iBAAiB,CACnE,CACF,CAAC;MACDa,QAAQ,EAAE;IACZ,CAAC;IACD+B,UAAU,EAAE;MACVnE,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC,CAAC;MACDa,QAAQ,EAAE;IACZ,CAAC;IACD6E,MAAM,EAAE;MACNjH,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,kBAAkB,CAAC;MAC5Ca,QAAQ,EAAE;IACZ,CAAC;IACDwB,OAAO,EAAE;MACP5D,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;MACpCkC,QAAQ,EAAE;IACZ,CAAC;IACD8E,QAAQ,EAAE;MACRlH,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;MACpCkC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDpC,QAAQ,EAAG,YAAY;IACrB,MAAMa,UAAU,GAAG,IAAAU,qBAAc,EAAC,YAAY,CAAC;IAE/C,OAAO,UAAUwC,MAAM,EAAE7C,GAAG,EAAED,IAAI,EAAE;MAClC,IAAI,CAACX,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;MAEzC,IAAI,CAAC,IAAAa,WAAE,EAAC,0BAA0B,EAAE0C,MAAM,CAAC,EAAE;QAC3ClD,UAAU,CAACI,IAAI,EAAE,IAAI,EAAEA,IAAI,CAAC4C,EAAE,CAAC;MACjC;IACF,CAAC;EACH,CAAC,CAAE;AACL,CAAC,CAAC;AAEFjE,UAAU,CAAC,sBAAsB,EAAE;EACjC6B,OAAO,EAAE,CAAC,QAAQ,CAAC;EACnBf,OAAO,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC;EAC/CC,OAAO,EAAE,CACP,WAAW,EACX,aAAa,EACb,2BAA2B,EAC3B,mBAAmB,CACpB;EACDb,MAAM,EAAE;IACNqH,MAAM,EAAE;MACNnH,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,eAAe;IAC1C,CAAC;IACD6F,UAAU,EAAE,IAAAC,uBAAgB,EAAC,IAAAvG,kBAAW,EAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC1DwG,UAAU,EAAE;MACVlF,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,iBAAiB,CAAC,CAC9C;IACF,CAAC;IAEDgG,UAAU,EAAE;MACVnF,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,iBAAiB,CAAC,CAC9C;IACF;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,0BAA0B,EAAE;EACrCc,OAAO,EAAE,CAAC,aAAa,CAAC;EACxBC,OAAO,EAAE,CACP,WAAW,EACX,aAAa,EACb,2BAA2B,EAC3B,mBAAmB,CACpB;EACDb,MAAM,EAAE;IACN0H,WAAW,EAAE;MACXxH,QAAQ,EAAE,IAAAuB,qBAAc,EACtB,mBAAmB,EACnB,qBAAqB,EACrB,kBAAkB,EAClB,YACF;IACF,CAAC;IACD6F,UAAU,EAAE,IAAAC,uBAAgB,EAAC,IAAAvG,kBAAW,EAAC,OAAO,CAAC;EACnD;AACF,CAAC,CAAC;AAEFlB,UAAU,CAAC,wBAAwB,EAAE;EACnC6B,OAAO,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,QAAQ,CAAC;EAChDf,OAAO,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC;EAC5EC,OAAO,EAAE,CACP,WAAW,EACX,aAAa,EACb,2BAA2B,EAC3B,mBAAmB,CACpB;EACDb,MAAM,EAAE;IACN0H,WAAW,EAAE;MACXpF,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAsB,qBAAc,EAAC,aAAa,CAAC,EAC7BM,MAAM,CAACC,MAAM,CACX,UAAUb,IAA8B,EAAEC,GAAG,EAAEC,GAAG,EAAE;QAClD,IAAI,CAACb,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;QAKzC,IAAIW,GAAG,IAAIF,IAAI,CAACwG,UAAU,CAACxB,MAAM,EAAE;UACjC,MAAM,IAAI3B,SAAS,CACjB,qEACF,CAAC;QACH;MACF,CAAC,EACD;QAAEvC,cAAc,EAAE,CAAC,aAAa;MAAE,CACpC,CAAC,EACD,UAAUd,IAA8B,EAAEC,GAAG,EAAEC,GAAG,EAAE;QAClD,IAAI,CAACb,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;QAKzC,IAAIW,GAAG,IAAIF,IAAI,CAACkG,MAAM,EAAE;UACtB,MAAM,IAAI7C,SAAS,CAAC,2CAA2C,CAAC;QAClE;MACF,CACF;IACF,CAAC;IACDgD,UAAU,EAAE;MACVlF,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,iBAAiB,CAAC,CAC9C;IACF,CAAC;IAEDgG,UAAU,EAAE;MACVnF,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,iBAAiB,CAAC,CAC9C;IACF,CAAC;IACDkG,UAAU,EAAE;MACVpH,OAAO,EAAE,EAAE;MACXL,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EACP,YAAY;QACX,MAAMuH,OAAO,GAAG,IAAAnG,qBAAc,EAC5B,iBAAiB,EACjB,wBAAwB,EACxB,0BACF,CAAC;QACD,MAAMoG,UAAU,GAAG,IAAApG,qBAAc,EAAC,iBAAiB,CAAC;QAEpD,IAAI,CAACjB,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE,OAAOkH,OAAO;QAEvD,OAAO,UAAUzG,IAA8B,EAAEC,GAAG,EAAEC,GAAG,EAAE;UACzD,MAAMC,SAAS,GAAGH,IAAI,CAACkG,MAAM,GAAGO,OAAO,GAAGC,UAAU;UACpDvG,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC;MACH,CAAC,CAAE,CACL,CACF;IACF,CAAC;IACDgG,MAAM,EAAE;MACNnH,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,eAAe,CAAC;MACzCa,QAAQ,EAAE;IACZ,CAAC;IACDgF,UAAU,EAAE,IAAAC,uBAAgB,EAAC,IAAAvG,kBAAW,EAAC,MAAM,EAAE,OAAO,CAAC;EAC3D;AACF,CAAC,CAAC;AAEFlB,UAAU,CAAC,iBAAiB,EAAE;EAC5Bc,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;EAC9BC,OAAO,EAAE,CAAC,iBAAiB,CAAC;EAC5Bb,MAAM,EAAE;IACN8H,KAAK,EAAE;MACL5H,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDsG,QAAQ,EAAE;MACR7H,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,EAAE,eAAe;IACxD,CAAC;IACD6F,UAAU,EAAE;MAEVpH,QAAQ,EAAE,IAAAc,kBAAW,EAAC,MAAM,EAAE,OAAO,CAAC;MACtCsB,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEFxC,UAAU,CAAC,gBAAgB,EAAE;EAC3Bc,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;EAClCe,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;EAC3Cd,OAAO,EAAE,CACP,UAAU,EACV,WAAW,EACX,KAAK,EACL,aAAa,EACb,MAAM,EACN,eAAe,CAChB;EACDb,MAAM,EAAE;IACNwB,IAAI,EAAE;MACJtB,QAAQ,EAAG,YAAY;QACrB,IAAI,CAACM,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;UACvC,OAAO,IAAAe,qBAAc,EAAC,qBAAqB,EAAE,MAAM,CAAC;QACtD;QAEA,MAAMiG,WAAW,GAAG,IAAAjG,qBAAc,EAAC,qBAAqB,CAAC;QACzD,MAAMuG,IAAI,GAAG,IAAAvG,qBAAc,EACzB,YAAY,EACZ,kBAAkB,EAClB,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,uBAAuB,EACvB,iBAAiB,EACjB,qBACF,CAAC;QAED,OAAO,UAAUN,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;UAC/B,IAAI,IAAAE,WAAE,EAAC,qBAAqB,EAAEF,GAAG,CAAC,EAAE;YAClCqG,WAAW,CAACvG,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;UAC7B,CAAC,MAAM;YACL2G,IAAI,CAAC7G,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;UACtB;QACF,CAAC;MACH,CAAC,CAAE;IACL,CAAC;IACDK,KAAK,EAAE;MACLxB,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDW,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,WAAW;IACtC,CAAC;IACDwG,KAAK,EAAE;MACL1H,OAAO,EAAE;IACX;EACF;AACF,CAAC,CAAC;AAEFT,UAAU,CAAC,mBAAmB,EAAE;EAC9B6B,OAAO,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC;EACjCf,OAAO,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC;EAC7DC,OAAO,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,2BAA2B,CAAC;EAClEb,MAAM,EAAE;IACNwH,UAAU,EAAE;MACVlF,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,iBAAiB,CAAC,CAC9C;IACF,CAAC;IAEDgG,UAAU,EAAE;MACVnF,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,iBAAiB,CAAC,CAC9C;IACF,CAAC;IACDyG,MAAM,EAAE;MACN5F,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS;IACrC,CAAC;IACDuH,UAAU,EAAE;MACVzH,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EACR,IAAAoB,qBAAc,EACZ,iBAAiB,EACjB,wBAAwB,EACxB,0BACF,CACF,CACF;IACF,CAAC;IACD4F,MAAM,EAAE;MACNnH,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,eAAe;IAC1C,CAAC;IACD0G,UAAU,EAAE;MAGVjI,QAAQ,EAAE,IAAAc,kBAAW,EAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;MAChDsB,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEFxC,UAAU,CAAC,wBAAwB,EAAE;EACnCc,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBC,OAAO,EAAE,CAAC,iBAAiB,CAAC;EAC5Bb,MAAM,EAAE;IACN8H,KAAK,EAAE;MACL5H,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,0BAA0B,EAAE;EACrCc,OAAO,EAAE,CAAC,OAAO,CAAC;EAClBC,OAAO,EAAE,CAAC,iBAAiB,CAAC;EAC5Bb,MAAM,EAAE;IACN8H,KAAK,EAAE;MACL5H,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,iBAAiB,EAAE;EAC5Bc,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;EAC9BC,OAAO,EAAE,CAAC,iBAAiB,CAAC;EAC5Bb,MAAM,EAAE;IACN8H,KAAK,EAAE;MACL5H,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDqD,QAAQ,EAAE;MACR5E,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,EAAE,eAAe;IACxD,CAAC;IACD0G,UAAU,EAAE;MAGVjI,QAAQ,EAAE,IAAAc,kBAAW,EAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;MAChDsB,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEFxC,UAAU,CAAC,cAAc,EAAE;EACzBc,OAAO,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;EAC7BC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBb,MAAM,EAAE;IACN+E,IAAI,EAAE;MACJ7E,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAsB,qBAAc,EAAC,YAAY,CAAC,EAC5BM,MAAM,CAACC,MAAM,CACX,UAAUb,IAAoB,EAAEC,GAAG,EAAEC,GAAG,EAAE;QACxC,IAAI,CAACb,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;QAEzC,IAAI6E,QAAQ;QACZ,QAAQlE,GAAG,CAACiD,IAAI;UACd,KAAK,UAAU;YACbiB,QAAQ,GAAG,MAAM;YACjB;UACF,KAAK,KAAK;YACRA,QAAQ,GAAG,QAAQ;YACnB;UACF,KAAK,QAAQ;YACXA,QAAQ,GAAG,MAAM;YACjB;QACJ;QACA,IAAI,CAAC,IAAAhE,WAAE,EAAC,YAAY,EAAEJ,IAAI,CAACoE,QAAQ,EAAE;UAAEjB,IAAI,EAAEiB;QAAS,CAAC,CAAC,EAAE;UACxD,MAAM,IAAIf,SAAS,CAAC,2BAA2B,CAAC;QAClD;MACF,CAAC,EACD;QAAEvC,cAAc,EAAE,CAAC,YAAY;MAAE,CACnC,CACF;IACF,CAAC;IACDsD,QAAQ,EAAE;MACRrF,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEK,MAAM2G,2BAA2B,GAAGA,CAAA,MAAO;EAChDhB,QAAQ,EAAE;IACRlH,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;IACpCkC,QAAQ,EAAE;EACZ,CAAC;EACD+F,aAAa,EAAE;IACbnI,QAAQ,EAAE,IAAAc,kBAAW,EAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC;IACvDsB,QAAQ,EAAE;EACZ,CAAC;EACDgG,MAAM,EAAE;IACN/H,OAAO,EAAE;EACX,CAAC;EACDgI,QAAQ,EAAE;IACRhI,OAAO,EAAE;EACX,CAAC;EACDsE,QAAQ,EAAE;IACRtE,OAAO,EAAE;EACX,CAAC;EACD+B,QAAQ,EAAE;IACRpC,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;IACpCkC,QAAQ,EAAE;EACZ,CAAC;EACDlB,GAAG,EAAE;IACHlB,QAAQ,EAAE,IAAAC,YAAK,EACZ,YAAY;MACX,MAAMqF,MAAM,GAAG,IAAA/D,qBAAc,EAC3B,YAAY,EACZ,eAAe,EACf,gBACF,CAAC;MACD,MAAMoD,QAAQ,GAAG,IAAApD,qBAAc,EAAC,YAAY,CAAC;MAE7C,OAAO,UAAUN,IAAS,EAAEC,GAAW,EAAEC,GAAQ,EAAE;QACjD,MAAMC,SAAS,GAAGH,IAAI,CAAC0D,QAAQ,GAAGA,QAAQ,GAAGW,MAAM;QACnDlE,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;MAC3B,CAAC;IACH,CAAC,CAAE,CAAC,EACJ,IAAAI,qBAAc,EACZ,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,YACF,CACF;EACF;AACF,CAAC,CAAC;AAACiC,OAAA,CAAA0E,2BAAA,GAAAA,2BAAA;AAEI,MAAMI,gCAAgC,GAAGA,CAAA,KAAAzG,MAAA,CAAAC,MAAA,KAC3CsB,cAAc,CAAC,CAAC,EAChB8E,2BAA2B,CAAC,CAAC;EAChC7E,MAAM,EAAE;IACNrD,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EACR,IAAAoB,qBAAc,EACZ,YAAY,EACZ,SAAS,EACT,aAAa,EACb,qBACF,CACF,CACF;EACF,CAAC;EACDoE,IAAI,EAAE;IACJ3F,QAAQ,EAAE,IAAAc,kBAAW,EAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,CAAC;IAC5DT,OAAO,EAAE;EACX,CAAC;EACDkI,MAAM,EAAE;IACNvI,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,QAAQ,CAAC,EACzB,IAAAY,kBAAW,EAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAC9C,CAAC;IACDsB,QAAQ,EAAE;EACZ,CAAC;EACD+B,UAAU,EAAE;IACVnE,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC,CAAC;IACDa,QAAQ,EAAE;EACZ;AAAC,EACD;AAACoB,OAAA,CAAA8E,gCAAA,GAAAA,gCAAA;AAEH1I,UAAU,CAAC,aAAa,EAAE;EACxBe,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,gBAAgB,EAAE,QAAQ,CAAC;EAC5Ec,OAAO,EAAE,CACP,MAAM,EACN,KAAK,EACL,QAAQ,EACR,MAAM,EACN,UAAU,EACV,QAAQ,EACR,WAAW,EACX,OAAO,CACR;EACDf,OAAO,EAAE,CACP,KAAK,EACL,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,gBAAgB,CACjB;EACDZ,MAAM,EAAA+B,MAAA,CAAAC,MAAA,KACDwG,gCAAgC,CAAC,CAAC,EAClC7E,4BAA4B,CAAC,CAAC;IACjCvB,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,gBAAgB;IAC3C;EAAC;AAEL,CAAC,CAAC;AAEF3B,UAAU,CAAC,eAAe,EAAE;EAC1Bc,OAAO,EAAE,CACP,YAAY,EACZ,gBAAgB,EAChB,YAAY,CACb;EACDe,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBd,OAAO,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,MAAM,CAAC;EAC3Cb,MAAM,EAAA+B,MAAA,CAAAC,MAAA,KACDmC,iBAAiB,CAAC,CAAC;IACtByB,UAAU,EAAE;MACV1F,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,aAAa,EAAE,gBAAgB,CAAC,CAC5D;IACF;EAAC;AAEL,CAAC,CAAC;AAEF3B,UAAU,CAAC,eAAe,EAAE;EAC1Bc,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,WAAW,CAAC;EACtBqE,eAAe,EAAE,gBAAgB;EACjClF,MAAM,EAAE;IACN+F,QAAQ,EAAE;MACR7F,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CACR,OAAO,EAGH;EACEe,OAAO,EAAE,CAAC,YAAY;AACxB,CACN,CAAC;AAEDf,UAAU,CAAC,0BAA0B,EAAE;EACrCc,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,gBAAgB,CAAC;EAC3Ce,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC;EACzBd,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBb,MAAM,EAAE;IACN0I,GAAG,EAAE;MACHxI,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDkH,KAAK,EAAE;MACLzI,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,iBAAiB;IAC5C,CAAC;IACDiB,cAAc,EAAE;MACdxC,QAAQ,EAAE,IAAAuB,qBAAc,EACtB,4BAA4B,EAC5B,8BACF,CAAC;MACDa,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEFxC,UAAU,CAAC,iBAAiB,EAAE;EAC5B6B,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;EAC1B3B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAyI,kBAAW,EAAC;QACVC,GAAG,EAAE;UACH3I,QAAQ,EAAE,IAAAE,sBAAe,EAAC,QAAQ;QACpC,CAAC;QACD0I,MAAM,EAAE;UACN5I,QAAQ,EAAE,IAAAE,sBAAe,EAAC,QAAQ,CAAC;UACnCkC,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC,EACF,SAASyG,8BAA8BA,CAAC5H,IAAuB,EAAE;QAC/D,MAAM0H,GAAG,GAAG1H,IAAI,CAACe,KAAK,CAAC2G,GAAG;QAE1B,IAAIG,kBAAkB,GAAG,KAAK;QAE9B,MAAMC,KAAK,GAAGA,CAAA,KAAM;UAElB,MAAM,IAAIjD,KAAK,CAAC,8BAA8B,CAAC;QACjD,CAAC;QACD,MAAM;UAAEkD,GAAG;UAAEC;QAAgB,CAAC,GAAG,IAAAC,sCAAkB,EACjD,UAAU,EACVP,GAAG,EACH,CAAC,EACD,CAAC,EACD,CAAC,EACD;UACEQ,YAAYA,CAAA,EAAG;YACbL,kBAAkB,GAAG,IAAI;UAC3B,CAAC;UACDM,mBAAmB,EAAEL,KAAK;UAC1BM,qBAAqB,EAAEN,KAAK;UAC5BO,gCAAgC,EAAEP,KAAK;UACvCQ,0BAA0B,EAAER,KAAK;UACjCS,YAAY,EAAET,KAAK;UACnBU,gBAAgB,EAAEV;QACpB,CACF,CAAC;QACD,IAAI,CAACD,kBAAkB,EAAE,MAAM,IAAIhD,KAAK,CAAC,aAAa,CAAC;QAEvD7E,IAAI,CAACe,KAAK,CAAC4G,MAAM,GAAGK,eAAe,GAAG,IAAI,GAAGD,GAAG;MAClD,CACF;IACF,CAAC;IACDU,IAAI,EAAE;MACJrJ,OAAO,EAAE;IACX;EACF;AACF,CAAC,CAAC;AAEFT,UAAU,CAAC,iBAAiB,EAAE;EAC5Bc,OAAO,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC;EAClCC,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC;EAClCb,MAAM,EAAE;IACN6J,MAAM,EAAE;MACN3J,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,iBAAiB,CAAC,CAC9C;IACF,CAAC;IACD2E,WAAW,EAAE;MACXlG,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EACR,IAAAoB,qBAAc,EACZ,YAAY,EAEZ,QACF,CACF,CAAC,EACD,UAAUN,IAAuB,EAAEC,GAAG,EAAEC,GAAG,EAAE;QAC3C,IAAIF,IAAI,CAAC0I,MAAM,CAAC1D,MAAM,KAAK9E,GAAG,CAAC8E,MAAM,GAAG,CAAC,EAAE;UACzC,MAAM,IAAI3B,SAAS,CAChB,aACCrD,IAAI,CAACgC,IACN,gFACC9B,GAAG,CAAC8E,MAAM,GAAG,CACd,mBAAkBhF,IAAI,CAAC0I,MAAM,CAAC1D,MAAO,EACxC,CAAC;QACH;MACF,CACF;IACF;EACF;AACF,CAAC,CAAC;AAEFrG,UAAU,CAAC,iBAAiB,EAAE;EAC5B6B,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;EACjCf,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;EACzCb,MAAM,EAAE;IACN8J,QAAQ,EAAE;MACR5J,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,SAAS,CAAC,EAC1B2B,MAAM,CAACC,MAAM,CACX,UAAUb,IAAuB,EAAEC,GAAG,EAAEC,GAAG,EAAE;QAC3C,IAAI,CAACb,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;QAEzC,IAAIW,GAAG,IAAI,CAACF,IAAI,CAAC4E,QAAQ,EAAE;UACzB,MAAM,IAAIvB,SAAS,CACjB,6EACF,CAAC;QACH;MACF,CAAC,EACD;QAAErB,IAAI,EAAE;MAAU,CACpB,CACF,CAAC;MACD5C,OAAO,EAAE;IACX,CAAC;IACDwF,QAAQ,EAAE;MACRzD,QAAQ,EAAE,IAAI;MACdpC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAGF3B,UAAU,CAAC,iBAAiB,EAAE;EAC5B6B,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBf,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;EACzCb,MAAM,EAAE;IACN+F,QAAQ,EAAE;MACR7F,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAGF3B,UAAU,CAAC,QAAQ,EAAE;EACnBe,OAAO,EAAE,CAAC,YAAY;AACxB,CAAC,CAAC;AAGFf,UAAU,CAAC,eAAe,EAAE;EAC1B6B,OAAO,EAAE,CAAC,OAAO,CAAC;EAClB3B,MAAM,EAAE;IACNkC,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAE,sBAAe,EAAC,QAAQ;IACpC;EACF,CAAC;EACDS,OAAO,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW;AAC3D,CAAC,CAAC;AAEFf,UAAU,CAAC,0BAA0B,EAAE;EACrCc,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBC,OAAO,EAAE,CAAC,iBAAiB,CAAC;EAC5Bb,MAAM,EAAE;IACN+H,QAAQ,EAAE;MACR7H,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,0BAA0B,EAAE;EACrC6B,OAAO,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EACvDf,OAAO,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;EAC/BC,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBb,MAAM,EAAE;IACNsF,MAAM,EAAE;MACNpF,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACD8D,QAAQ,EAAE;MACRrF,QAAQ,EAAG,YAAY;QACrB,MAAMsF,MAAM,GAAG,IAAA/D,qBAAc,EAAC,YAAY,CAAC;QAC3C,MAAMoD,QAAQ,GAAG,IAAApD,qBAAc,EAAC,YAAY,CAAC;QAE7C,MAAMH,SAAoB,GAAGS,MAAM,CAACC,MAAM,CACxC,UAAUb,IAAgC,EAAEC,GAAG,EAAEC,GAAG,EAAE;UACpD,MAAMC,SAAS,GAAGH,IAAI,CAAC0D,QAAQ,GAAGA,QAAQ,GAAGW,MAAM;UACnDlE,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC,EAED;UAAEY,cAAc,EAAE,CAAC,YAAY,EAAE,YAAY;QAAE,CACjD,CAAC;QACD,OAAOX,SAAS;MAClB,CAAC,CAAE;IACL,CAAC;IACDuD,QAAQ,EAAE;MACRtE,OAAO,EAAE;IACX,CAAC;IACD+B,QAAQ,EAAE;MACRpC,QAAQ,EAAE,CAACM,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACzC,IAAAN,sBAAe,EAAC,SAAS,CAAC,GAC1B,IAAAD,YAAK,EAAC,IAAAC,sBAAe,EAAC,SAAS,CAAC,EAAE,IAAA2J,+BAAwB,EAAC,CAAC;IAClE;EACF;AACF,CAAC,CAAC;AAEFjK,UAAU,CAAC,wBAAwB,EAAE;EACnCc,OAAO,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,CAAC;EACnEe,OAAO,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC;EAC5Cd,OAAO,EAAE,CAAC,YAAY,CAAC;EACvBb,MAAM,EAAE;IACNuC,MAAM,EAAE;MACNrC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC,CAAC;IACDe,SAAS,EAAE;MACTtC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EACR,IAAAoB,qBAAc,EACZ,YAAY,EACZ,eAAe,EACf,mBAAmB,EACnB,qBACF,CACF,CACF;IACF,CAAC;IACDa,QAAQ,EAAE;MACRpC,QAAQ,EAAE,CAACM,OAAO,CAACC,GAAG,CAACC,sBAAsB,GACzC,IAAAN,sBAAe,EAAC,SAAS,CAAC,GAC1B,IAAAD,YAAK,EAAC,IAAAC,sBAAe,EAAC,SAAS,CAAC,EAAE,IAAA2J,+BAAwB,EAAC,CAAC;IAClE,CAAC;IACDtH,aAAa,EAAE;MACbvC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,4BAA4B,CAAC;MACtDa,QAAQ,EAAE;IACZ,CAAC;IACDI,cAAc,EAAE;MACdxC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,8BAA8B,CAAC;MACxDa,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAGFxC,UAAU,CAAC,eAAe,EAAE;EAC1Bc,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,gBAAgB,EAAE,YAAY,CAAC;EACzDe,OAAO,EAAE,CACP,KAAK,EACL,OAAO,EACP,gBAAgB,EAChB,YAAY,EACZ,UAAU,EACV,QAAQ,CACT;EACDd,OAAO,EAAE,CAAC,UAAU,CAAC;EACrBb,MAAM,EAAA+B,MAAA,CAAAC,MAAA,KACDoG,2BAA2B,CAAC,CAAC;IAChClG,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACDyE,QAAQ,EAAE;MACR7G,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;MACpCkC,QAAQ,EAAE;IACZ,CAAC;IACD8B,cAAc,EAAE;MACdlE,QAAQ,EAEJ,IAAAuB,qBAAc,EACZ,gBAAgB,EAChB,kBAAkB,EAElB,MACF,CAAC;MACLa,QAAQ,EAAE;IACZ,CAAC;IACD+B,UAAU,EAAE;MACVnE,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC,CAAC;MACDa,QAAQ,EAAE;IACZ,CAAC;IACD0H,QAAQ,EAAE;MACR9J,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;MACpCkC,QAAQ,EAAE;IACZ,CAAC;IACDwB,OAAO,EAAE;MACP5D,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;MACpCkC,QAAQ,EAAE;IACZ,CAAC;IACD2H,QAAQ,EAAE;MACR/J,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,UAAU,CAAC;MACpCa,QAAQ,EAAE;IACZ;EAAC;AAEL,CAAC,CAAC;AAEFxC,UAAU,CAAC,uBAAuB,EAAE;EAClCc,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,gBAAgB,EAAE,YAAY,CAAC;EACzDe,OAAO,EAAE,CACP,KAAK,EACL,OAAO,EACP,gBAAgB,EAChB,YAAY,EACZ,UAAU,EACV,QAAQ,CACT;EACDd,OAAO,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;EACjCb,MAAM,EAAA+B,MAAA,CAAAC,MAAA,KACDoG,2BAA2B,CAAC,CAAC;IAChChH,GAAG,EAAE;MACHlB,QAAQ,EAAE,IAAAC,YAAK,EACZ,YAAY;QACX,MAAMqF,MAAM,GAAG,IAAA/D,qBAAc,EAC3B,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,aACF,CAAC;QACD,MAAMoD,QAAQ,GAAG,IAAApD,qBAAc,EAAC,YAAY,CAAC;QAE7C,OAAO,UAAUN,IAAS,EAAEC,GAAW,EAAEC,GAAQ,EAAE;UACjD,MAAMC,SAAS,GAAGH,IAAI,CAAC0D,QAAQ,GAAGA,QAAQ,GAAGW,MAAM;UACnDlE,SAAS,CAACH,IAAI,EAAEC,GAAG,EAAEC,GAAG,CAAC;QAC3B,CAAC;MACH,CAAC,CAAE,CAAC,EACJ,IAAAI,qBAAc,EACZ,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,YAAY,EACZ,aACF,CACF;IACF,CAAC;IACDS,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACDyE,QAAQ,EAAE;MACR7G,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;MACpCkC,QAAQ,EAAE;IACZ,CAAC;IACD8B,cAAc,EAAE;MACdlE,QAAQ,EAEJ,IAAAuB,qBAAc,EACZ,gBAAgB,EAChB,kBAAkB,EAElB,MACF,CAAC;MACLa,QAAQ,EAAE;IACZ,CAAC;IACD+B,UAAU,EAAE;MACVnE,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC,CAAC;MACDa,QAAQ,EAAE;IACZ,CAAC;IACD0H,QAAQ,EAAE;MACR9J,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;MACpCkC,QAAQ,EAAE;IACZ,CAAC;IACDwB,OAAO,EAAE;MACP5D,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;MACpCkC,QAAQ,EAAE;IACZ,CAAC;IACD2H,QAAQ,EAAE;MACR/J,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,UAAU,CAAC;MACpCa,QAAQ,EAAE;IACZ;EAAC;AAEL,CAAC,CAAC;AAEFxC,UAAU,CAAC,sBAAsB,EAAE;EACjCc,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,gBAAgB,CAAC;EACzDe,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,CAAC;EACjDd,OAAO,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC;EAChCb,MAAM,EAAE;IACNoB,GAAG,EAAE;MACHlB,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,aAAa;IACxC,CAAC;IACDS,KAAK,EAAE;MACLhC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY,CAAC;MACtCa,QAAQ,EAAE;IACZ,CAAC;IACD8B,cAAc,EAAE;MACdlE,QAAQ,EAEJ,IAAAuB,qBAAc,EACZ,gBAAgB,EAChB,kBAAkB,EAElB,MACF,CAAC;MACLa,QAAQ,EAAE;IACZ,CAAC;IACD+B,UAAU,EAAE;MACVnE,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC,CAAC;MACDa,QAAQ,EAAE;IACZ,CAAC;IACDgG,MAAM,EAAE;MACNpI,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;MACpCG,OAAO,EAAE;IACX,CAAC;IACDyJ,QAAQ,EAAE;MACR9J,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;MACpCkC,QAAQ,EAAE;IACZ,CAAC;IACDyE,QAAQ,EAAE;MACR7G,QAAQ,EAAE,IAAAE,sBAAe,EAAC,SAAS,CAAC;MACpCkC,QAAQ,EAAE;IACZ,CAAC;IACD2H,QAAQ,EAAE;MACR/J,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,UAAU,CAAC;MACpCa,QAAQ,EAAE;IACZ;EACF;AACF,CAAC,CAAC;AAEFxC,UAAU,CAAC,oBAAoB,EAAE;EAC/B6B,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;EACpDf,OAAO,EAAE,CACP,KAAK,EACL,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,gBAAgB,CACjB;EACDC,OAAO,EAAE,CACP,UAAU,EACV,UAAU,EACV,aAAa,EACb,gBAAgB,EAChB,QAAQ,EACR,SAAS,CACV;EACDb,MAAM,EAAA+B,MAAA,CAAAC,MAAA,KACDwG,gCAAgC,CAAC,CAAC,EAClC7E,4BAA4B,CAAC,CAAC;IACjCkC,IAAI,EAAE;MACJ3F,QAAQ,EAAE,IAAAc,kBAAW,EAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC;MAC7CT,OAAO,EAAE;IACX,CAAC;IACDa,GAAG,EAAE;MACHlB,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,aAAa;IACxC,CAAC;IACDW,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,gBAAgB;IAC3C;EAAC;AAEL,CAAC,CAAC;AAEF3B,UAAU,CAAC,aAAa,EAAE;EACxBc,OAAO,EAAE,CAAC,IAAI,CAAC;EACfC,OAAO,EAAE,CAAC,SAAS,CAAC;EACpBb,MAAM,EAAE;IACN+D,EAAE,EAAE;MACF7D,QAAQ,EAAE,IAAAuB,qBAAc,EAAC,YAAY;IACvC;EACF;AACF,CAAC,CAAC;AAEF3B,UAAU,CAAC,aAAa,EAAE;EACxBc,OAAO,EAAE,CAAC,MAAM,CAAC;EACjBZ,MAAM,EAAE;IACNoC,IAAI,EAAE;MACJlC,QAAQ,EAAE,IAAAC,YAAK,EACb,IAAAC,sBAAe,EAAC,OAAO,CAAC,EACxB,IAAAC,iBAAU,EAAC,IAAAoB,qBAAc,EAAC,WAAW,CAAC,CACxC;IACF;EACF,CAAC;EACDZ,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,gBAAgB;AACvD,CAAC,CAAC"}