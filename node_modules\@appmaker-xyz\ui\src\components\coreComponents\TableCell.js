import React from 'react';
import { StyleSheet } from 'react-native';
import { ThemeText, AppTouchable, Layout } from '../atoms';
import Icon from 'react-native-vector-icons/Feather';

const TableCell = ({ attributes, onPress, onAction }) => {
  const {
    title,
    value,
    iconName,
    type,
    appmakerAction,
    subTitle,
    noMargin,
    style,
  } = attributes;
  const containerStyles = [styles.container];
  const tableContainerStyles = [styles.tableContainer];

  if (style) {
    tableContainerStyles.push(style);
  }
  if (type === 'stacked') {
    containerStyles.push({ flexDirection: 'column' });
  }
  if (type === 'total') {
    tableContainerStyles.push({ backgroundColor: '#E9EDF1', marginTop: -5 });
  }
  if (noMargin) {
    tableContainerStyles.push({ marginBottom: 0 });
  }
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  return (
    <Layout style={tableContainerStyles}>
      <AppTouchable onPress={onPressHandle} style={styles.wholeContainer}>
        <Layout style={containerStyles}>
          <Layout style={styles.column}>
            <ThemeText size={type === 'total' ? 'lg' : 'base'} color="#4F4F4F">
              {title}
            </ThemeText>
            {subTitle ? (
              <ThemeText size="sm" status="#4F4F4F">
                {subTitle}
              </ThemeText>
            ) : null}
          </Layout>
          <Layout style={styles.column}>
            <ThemeText
              size={type === 'total' ? 'lg' : 'base'}
              fontFamily={type === 'total' && 'bold'}
              style={styles.textRight}>
              {value}
            </ThemeText>
          </Layout>
        </Layout>
        {type === 'stacked' && iconName && (
          <Layout style={styles.iconContainer}>
            <Icon name={iconName} size={16} color="#A9AEB7" />
          </Layout>
        )}
      </AppTouchable>
    </Layout>
  );
};

const styles = StyleSheet.create({
  tableContainer: {
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E9EDF1',
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    marginBottom: 4,
  },
  wholeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  container: {
    flexDirection: 'row',
    paddingVertical: 4,
    flex: 1,
  },
  column: {
    width: '50%',
  },
  textRight: {
    textAlign: 'right',
  },
  iconContainer: {
    padding: 8,
  },
});

export default TableCell;
