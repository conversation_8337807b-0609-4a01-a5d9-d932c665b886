{"version": 3, "names": ["_renamer", "require", "_index", "_binding", "_globals", "_t", "t", "_cache", "_visitors", "NOT_LOCAL_BINDING", "callExpression", "cloneNode", "getBindingIdentifiers", "identifier", "isArrayExpression", "isBinary", "isClass", "isClassBody", "isClassDeclaration", "isExportAllDeclaration", "isExportDefaultDeclaration", "isExportNamedDeclaration", "isFunctionDeclaration", "isIdentifier", "isImportDeclaration", "isLiteral", "isMethod", "isModuleSpecifier", "is<PERSON>ull<PERSON>iteral", "isObjectExpression", "isProperty", "isPureish", "isRegExpLiteral", "is<PERSON><PERSON><PERSON>", "isTaggedTemplateExpression", "isTemplateLiteral", "isThisExpression", "isUnaryExpression", "isVariableDeclaration", "matchesPattern", "memberExpression", "numericLiteral", "toIdentifier", "unaryExpression", "variableDeclaration", "variableDeclarator", "isRecordExpression", "isTupleExpression", "isObjectProperty", "isTopicReference", "isMetaProperty", "isPrivateName", "isExportDeclaration", "gatherNodeParts", "node", "parts", "type", "_node$specifiers", "source", "specifiers", "length", "e", "declaration", "local", "push", "value", "object", "property", "name", "callee", "properties", "argument", "key", "left", "id", "expression", "meta", "openingElement", "openingFragment", "namespace", "collectorVisitor", "ForStatement", "path", "declar", "get", "isVar", "scope", "parentScope", "getFunctionParent", "getProgramParent", "registerBinding", "Declaration", "isBlockScoped", "parent", "registerDeclaration", "ImportDeclaration", "getBlockParent", "ReferencedIdentifier", "state", "references", "ForXStatement", "isPattern", "constantViolations", "ExportDeclaration", "exit", "binding", "getBinding", "reference", "decl", "declarations", "Object", "keys", "LabeledStatement", "AssignmentExpression", "assignments", "UpdateExpression", "UnaryExpression", "operator", "BlockScoped", "bindings", "CatchClause", "Function", "params", "param", "isFunctionExpression", "has", "ClassExpression", "uid", "<PERSON><PERSON>", "constructor", "block", "labels", "inited", "globals", "uids", "data", "crawling", "cached", "scopeCache", "set", "Map", "_parent", "shouldSkip", "<PERSON><PERSON><PERSON>", "parentPath", "isScope", "parentBlock", "hub", "traverse", "opts", "generateDeclaredUidIdentifier", "generateUidIdentifier", "generateUid", "replace", "i", "_generateUid", "<PERSON><PERSON><PERSON><PERSON>", "hasBinding", "hasGlobal", "hasReference", "program", "generateUidBasedOnNode", "defaultName", "join", "slice", "generateUidIdentifierBasedOnNode", "isStatic", "constant", "maybeGenerateMemoised", "dont<PERSON><PERSON>", "checkBlockScopedCollisions", "kind", "duplicate", "buildError", "TypeError", "rename", "old<PERSON>ame", "newName", "renamer", "Renamer", "arguments", "_renameFromMap", "map", "dump", "sep", "repeat", "console", "log", "violations", "toArray", "arrayLikeIsIterable", "isGenericType", "helper<PERSON><PERSON>", "args", "unshift", "addHelper", "get<PERSON><PERSON><PERSON>", "registerLabel", "label", "isLabeledStatement", "declare", "isTypeDeclaration", "importKind", "specifier", "isTypeSpecifier", "isImportSpecifier", "buildUndefinedNode", "registerConstantViolation", "ids", "_this$getBinding", "reassign", "bindingPath", "ReferenceError", "declarators", "getOuterBindingIdentifiers", "getOwnBinding", "Binding", "addGlobal", "hasUid", "isPure", "constantsOnly", "_node$decorators", "superClass", "decorators", "body", "method", "right", "elem", "elements", "prop", "_node$decorators2", "computed", "_node$decorators3", "static", "tag", "quasi", "expressions", "setData", "val", "getData", "removeData", "init", "crawl", "create", "programParent", "isExplodedVisitor", "visit", "enter", "call", "typeVisitors", "ref", "getPatternParent", "isBlockStatement", "isProgram", "isSwitchStatement", "unique", "isFunction", "isCallExpression", "pushContainer", "isLoop", "isCatchClause", "ensureBlock", "blockHoist", "_blockHoist", "dataKey", "declar<PERSON><PERSON>", "unshiftContainer", "declarator", "len", "Error", "isFunctionParent", "isBlockParent", "getAllBindings", "getAllBindingsOfKind", "kinds", "bindingIdentifierEquals", "getBindingIdentifier", "previousPath", "_previousPath", "isArrowFunctionExpression", "_this$getBinding2", "getOwnBindingIdentifier", "hasOwnBinding", "_opts", "_opts2", "_opts3", "noGlobals", "parentHasBinding", "noUids", "includes", "contextVariables", "_this$parent", "moveBindingTo", "info", "removeOwnBinding", "removeBinding", "_this$getBinding3", "exports", "default", "builtin"], "sources": ["../../src/scope/index.ts"], "sourcesContent": ["import Renamer from \"./lib/renamer.ts\";\nimport type NodePath from \"../path/index.ts\";\nimport traverse from \"../index.ts\";\nimport type { TraverseOptions } from \"../index.ts\";\nimport Binding from \"./binding.ts\";\nimport type { BindingKind } from \"./binding.ts\";\nimport globals from \"globals\";\nimport {\n  NOT_LOCAL_BINDING,\n  callExpression,\n  cloneNode,\n  getBindingIdentifiers,\n  identifier,\n  isArrayExpression,\n  isBinary,\n  isClass,\n  isClassBody,\n  isClassDeclaration,\n  isExportAllDeclaration,\n  isExportDefaultDeclaration,\n  isExportNamedDeclaration,\n  isFunctionDeclaration,\n  isIdentifier,\n  isImportDeclaration,\n  isLiteral,\n  isMethod,\n  isModuleSpecifier,\n  isNullLiteral,\n  isObjectExpression,\n  isProperty,\n  isPureish,\n  isRegExpLiteral,\n  isSuper,\n  isTaggedTemplateExpression,\n  isTemplateLiteral,\n  isThisExpression,\n  isUnaryExpression,\n  isVariableDeclaration,\n  matchesPattern,\n  memberExpression,\n  numericLiteral,\n  toIdentifier,\n  unaryExpression,\n  variableDeclaration,\n  variableDeclarator,\n  isRecordExpression,\n  isTupleExpression,\n  isObjectProperty,\n  isTopicReference,\n  isMetaProperty,\n  isPrivateName,\n  isExportDeclaration,\n} from \"@babel/types\";\nimport * as t from \"@babel/types\";\nimport { scope as scopeCache } from \"../cache.ts\";\nimport type { Visitor } from \"../types.ts\";\nimport { isExplodedVisitor } from \"../visitors.ts\";\n\ntype NodePart = string | number | boolean;\n// Recursively gathers the identifying names of a node.\nfunction gatherNodeParts(node: t.Node, parts: NodePart[]) {\n  switch (node?.type) {\n    default:\n      if (isImportDeclaration(node) || isExportDeclaration(node)) {\n        if (\n          (isExportAllDeclaration(node) ||\n            isExportNamedDeclaration(node) ||\n            isImportDeclaration(node)) &&\n          node.source\n        ) {\n          gatherNodeParts(node.source, parts);\n        } else if (\n          (isExportNamedDeclaration(node) || isImportDeclaration(node)) &&\n          node.specifiers?.length\n        ) {\n          for (const e of node.specifiers) gatherNodeParts(e, parts);\n        } else if (\n          (isExportDefaultDeclaration(node) ||\n            isExportNamedDeclaration(node)) &&\n          node.declaration\n        ) {\n          gatherNodeParts(node.declaration, parts);\n        }\n      } else if (isModuleSpecifier(node)) {\n        // todo(flow->ts): should condition instead be:\n        //    ```\n        //    t.isExportSpecifier(node) ||\n        //    t.isImportDefaultSpecifier(node) ||\n        //    t.isImportNamespaceSpecifier(node) ||\n        //    t.isImportSpecifier(node)\n        //    ```\n        //    allowing only nodes with `.local`?\n        // @ts-expect-error todo(flow->ts)\n        gatherNodeParts(node.local, parts);\n      } else if (\n        isLiteral(node) &&\n        !isNullLiteral(node) &&\n        !isRegExpLiteral(node) &&\n        !isTemplateLiteral(node)\n      ) {\n        parts.push(node.value);\n      }\n      break;\n\n    case \"MemberExpression\":\n    case \"OptionalMemberExpression\":\n    case \"JSXMemberExpression\":\n      gatherNodeParts(node.object, parts);\n      gatherNodeParts(node.property, parts);\n      break;\n\n    case \"Identifier\":\n    case \"JSXIdentifier\":\n      parts.push(node.name);\n      break;\n\n    case \"CallExpression\":\n    case \"OptionalCallExpression\":\n    case \"NewExpression\":\n      gatherNodeParts(node.callee, parts);\n      break;\n\n    case \"ObjectExpression\":\n    case \"ObjectPattern\":\n      for (const e of node.properties) {\n        gatherNodeParts(e, parts);\n      }\n      break;\n\n    case \"SpreadElement\":\n    case \"RestElement\":\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"ObjectProperty\":\n    case \"ObjectMethod\":\n    case \"ClassProperty\":\n    case \"ClassMethod\":\n    case \"ClassPrivateProperty\":\n    case \"ClassPrivateMethod\":\n      gatherNodeParts(node.key, parts);\n      break;\n\n    case \"ThisExpression\":\n      parts.push(\"this\");\n      break;\n\n    case \"Super\":\n      parts.push(\"super\");\n      break;\n\n    case \"Import\":\n      parts.push(\"import\");\n      break;\n\n    case \"DoExpression\":\n      parts.push(\"do\");\n      break;\n\n    case \"YieldExpression\":\n      parts.push(\"yield\");\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"AwaitExpression\":\n      parts.push(\"await\");\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"AssignmentExpression\":\n      gatherNodeParts(node.left, parts);\n      break;\n\n    case \"VariableDeclarator\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"FunctionExpression\":\n    case \"FunctionDeclaration\":\n    case \"ClassExpression\":\n    case \"ClassDeclaration\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"PrivateName\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"ParenthesizedExpression\":\n      gatherNodeParts(node.expression, parts);\n      break;\n\n    case \"UnaryExpression\":\n    case \"UpdateExpression\":\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"MetaProperty\":\n      gatherNodeParts(node.meta, parts);\n      gatherNodeParts(node.property, parts);\n      break;\n\n    case \"JSXElement\":\n      gatherNodeParts(node.openingElement, parts);\n      break;\n\n    case \"JSXOpeningElement\":\n      gatherNodeParts(node.name, parts);\n      break;\n\n    case \"JSXFragment\":\n      gatherNodeParts(node.openingFragment, parts);\n      break;\n\n    case \"JSXOpeningFragment\":\n      parts.push(\"Fragment\");\n      break;\n\n    case \"JSXNamespacedName\":\n      gatherNodeParts(node.namespace, parts);\n      gatherNodeParts(node.name, parts);\n      break;\n  }\n}\n\n//\ninterface CollectVisitorState {\n  assignments: NodePath<t.AssignmentExpression>[];\n  references: NodePath<t.Identifier | t.JSXIdentifier>[];\n  constantViolations: NodePath[];\n}\n\nconst collectorVisitor: Visitor<CollectVisitorState> = {\n  ForStatement(path) {\n    const declar = path.get(\"init\");\n    // delegate block scope handling to the `BlockScoped` method\n    if (declar.isVar()) {\n      const { scope } = path;\n      const parentScope = scope.getFunctionParent() || scope.getProgramParent();\n      parentScope.registerBinding(\"var\", declar);\n    }\n  },\n\n  Declaration(path) {\n    // delegate block scope handling to the `BlockScoped` method\n    if (path.isBlockScoped()) return;\n\n    // delegate import handing to the `ImportDeclaration` method\n    if (path.isImportDeclaration()) return;\n\n    // this will be hit again once we traverse into it after this iteration\n    if (path.isExportDeclaration()) return;\n\n    // we've ran into a declaration!\n    const parent =\n      path.scope.getFunctionParent() || path.scope.getProgramParent();\n    parent.registerDeclaration(path);\n  },\n\n  ImportDeclaration(path) {\n    // import may only appear in the top level or inside a module/namespace (for TS/flow)\n    const parent = path.scope.getBlockParent();\n\n    parent.registerDeclaration(path);\n  },\n\n  ReferencedIdentifier(path, state) {\n    state.references.push(path);\n  },\n\n  ForXStatement(path, state) {\n    const left = path.get(\"left\");\n    if (left.isPattern() || left.isIdentifier()) {\n      state.constantViolations.push(path);\n    }\n    // delegate block scope handling to the `BlockScoped` method\n    else if (left.isVar()) {\n      const { scope } = path;\n      const parentScope = scope.getFunctionParent() || scope.getProgramParent();\n      parentScope.registerBinding(\"var\", left);\n    }\n  },\n\n  ExportDeclaration: {\n    exit(path) {\n      const { node, scope } = path;\n      // ExportAllDeclaration does not have `declaration`\n      if (isExportAllDeclaration(node)) return;\n      const declar = node.declaration;\n      if (isClassDeclaration(declar) || isFunctionDeclaration(declar)) {\n        const id = declar.id;\n        if (!id) return;\n\n        const binding = scope.getBinding(id.name);\n        binding?.reference(path);\n      } else if (isVariableDeclaration(declar)) {\n        for (const decl of declar.declarations) {\n          for (const name of Object.keys(getBindingIdentifiers(decl))) {\n            const binding = scope.getBinding(name);\n            binding?.reference(path);\n          }\n        }\n      }\n    },\n  },\n\n  LabeledStatement(path) {\n    path.scope.getBlockParent().registerDeclaration(path);\n  },\n\n  AssignmentExpression(path, state) {\n    state.assignments.push(path);\n  },\n\n  UpdateExpression(path, state) {\n    state.constantViolations.push(path);\n  },\n\n  UnaryExpression(path, state) {\n    if (path.node.operator === \"delete\") {\n      state.constantViolations.push(path);\n    }\n  },\n\n  BlockScoped(path) {\n    let scope = path.scope;\n    if (scope.path === path) scope = scope.parent;\n\n    const parent = scope.getBlockParent();\n    parent.registerDeclaration(path);\n\n    // Register class identifier in class' scope if this is a class declaration.\n    if (path.isClassDeclaration() && path.node.id) {\n      const id = path.node.id;\n      const name = id.name;\n\n      path.scope.bindings[name] = path.scope.parent.getBinding(name);\n    }\n  },\n\n  CatchClause(path) {\n    path.scope.registerBinding(\"let\", path);\n  },\n\n  Function(path) {\n    const params: Array<NodePath> = path.get(\"params\");\n    for (const param of params) {\n      path.scope.registerBinding(\"param\", param);\n    }\n\n    // Register function expression id after params. When the id\n    // collides with a function param, the id effectively can't be\n    // referenced: here we registered it as a constantViolation\n    if (\n      path.isFunctionExpression() &&\n      path.has(\"id\") &&\n      // @ts-expect-error Fixme: document symbol ast properties\n      !path.get(\"id\").node[NOT_LOCAL_BINDING]\n    ) {\n      path.scope.registerBinding(\"local\", path.get(\"id\"), path);\n    }\n  },\n\n  ClassExpression(path) {\n    if (\n      path.has(\"id\") &&\n      // @ts-expect-error Fixme: document symbol ast properties\n      !path.get(\"id\").node[NOT_LOCAL_BINDING]\n    ) {\n      path.scope.registerBinding(\"local\", path);\n    }\n  },\n};\n\nlet uid = 0;\n\nexport type { Binding };\n\nexport default class Scope {\n  uid;\n\n  path: NodePath;\n  block: t.Pattern | t.Scopable;\n\n  labels;\n  inited;\n\n  bindings: { [name: string]: Binding };\n  references: { [name: string]: true };\n  globals: { [name: string]: t.Identifier | t.JSXIdentifier };\n  uids: { [name: string]: boolean };\n  data: { [key: string | symbol]: unknown };\n  crawling: boolean;\n\n  /**\n   * This searches the current \"scope\" and collects all references/bindings\n   * within.\n   */\n  constructor(path: NodePath<t.Pattern | t.Scopable>) {\n    const { node } = path;\n    const cached = scopeCache.get(node);\n    // Sometimes, a scopable path is placed higher in the AST tree.\n    // In these cases, have to create a new Scope.\n    if (cached?.path === path) {\n      return cached;\n    }\n    scopeCache.set(node, this);\n\n    this.uid = uid++;\n\n    this.block = node;\n    this.path = path;\n\n    this.labels = new Map();\n    this.inited = false;\n  }\n\n  /**\n   * Globals.\n   */\n\n  static globals = Object.keys(globals.builtin);\n\n  /**\n   * Variables available in current context.\n   */\n\n  static contextVariables = [\"arguments\", \"undefined\", \"Infinity\", \"NaN\"];\n\n  get parent() {\n    let parent,\n      path = this.path;\n    do {\n      // Skip method scope if coming from inside computed key or decorator expression\n      const shouldSkip = path.key === \"key\" || path.listKey === \"decorators\";\n      path = path.parentPath;\n      if (shouldSkip && path.isMethod()) path = path.parentPath;\n      if (path && path.isScope()) parent = path;\n    } while (path && !parent);\n\n    return parent?.scope;\n  }\n\n  get parentBlock() {\n    return this.path.parent;\n  }\n\n  get hub() {\n    return this.path.hub;\n  }\n\n  traverse<S>(\n    node: t.Node | t.Node[],\n    opts: TraverseOptions<S>,\n    state: S,\n  ): void;\n  traverse(node: t.Node | t.Node[], opts?: TraverseOptions, state?: any): void;\n  /**\n   * Traverse node with current scope and path.\n   *\n   * !!! WARNING !!!\n   * This method assumes that `this.path` is the NodePath representing `node`.\n   * After running the traversal, the `.parentPath` of the NodePaths\n   * corresponding to `node`'s children will be set to `this.path`.\n   *\n   * There is no good reason to use this method, since the only safe way to use\n   * it is equivalent to `scope.path.traverse(opts, state)`.\n   */\n  traverse<S>(node: any, opts: any, state?: S) {\n    traverse(node, opts, this, state, this.path);\n  }\n\n  /**\n   * Generate a unique identifier and add it to the current scope.\n   */\n\n  generateDeclaredUidIdentifier(name?: string) {\n    const id = this.generateUidIdentifier(name);\n    this.push({ id });\n    return cloneNode(id);\n  }\n\n  /**\n   * Generate a unique identifier.\n   */\n\n  generateUidIdentifier(name?: string) {\n    return identifier(this.generateUid(name));\n  }\n\n  /**\n   * Generate a unique `_id1` binding.\n   */\n\n  generateUid(name: string = \"temp\"): string {\n    name = toIdentifier(name)\n      .replace(/^_+/, \"\")\n      .replace(/[0-9]+$/g, \"\");\n\n    let uid;\n    let i = 1;\n    do {\n      uid = this._generateUid(name, i);\n      i++;\n    } while (\n      this.hasLabel(uid) ||\n      this.hasBinding(uid) ||\n      this.hasGlobal(uid) ||\n      this.hasReference(uid)\n    );\n\n    const program = this.getProgramParent();\n    program.references[uid] = true;\n    program.uids[uid] = true;\n\n    return uid;\n  }\n\n  /**\n   * Generate an `_id1`.\n   */\n\n  _generateUid(name: string, i: number) {\n    let id = name;\n    if (i > 1) id += i;\n    return `_${id}`;\n  }\n\n  generateUidBasedOnNode(node: t.Node, defaultName?: string) {\n    const parts: NodePart[] = [];\n    gatherNodeParts(node, parts);\n\n    let id = parts.join(\"$\");\n    id = id.replace(/^_/, \"\") || defaultName || \"ref\";\n\n    return this.generateUid(id.slice(0, 20));\n  }\n\n  /**\n   * Generate a unique identifier based on a node.\n   */\n\n  generateUidIdentifierBasedOnNode(node: t.Node, defaultName?: string) {\n    return identifier(this.generateUidBasedOnNode(node, defaultName));\n  }\n\n  /**\n   * Determine whether evaluating the specific input `node` is a consequenceless reference. ie.\n   * evaluating it won't result in potentially arbitrary code from being ran. The following are\n   * allowed and determined not to cause side effects:\n   *\n   *  - `this` expressions\n   *  - `super` expressions\n   *  - Bound identifiers\n   */\n\n  isStatic(node: t.Node): boolean {\n    if (isThisExpression(node) || isSuper(node) || isTopicReference(node)) {\n      return true;\n    }\n\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (binding) {\n        return binding.constant;\n      } else {\n        return this.hasBinding(node.name);\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * Possibly generate a memoised identifier if it is not static and has consequences.\n   */\n\n  maybeGenerateMemoised(node: t.Node, dontPush?: boolean) {\n    if (this.isStatic(node)) {\n      return null;\n    } else {\n      const id = this.generateUidIdentifierBasedOnNode(node);\n      if (!dontPush) {\n        this.push({ id });\n        return cloneNode(id);\n      }\n      return id;\n    }\n  }\n\n  checkBlockScopedCollisions(\n    local: Binding,\n    kind: BindingKind,\n    name: string,\n    id: any,\n  ) {\n    // ignore parameters\n    if (kind === \"param\") return;\n\n    // Ignore existing binding if it's the name of the current function or\n    // class expression\n    if (local.kind === \"local\") return;\n\n    const duplicate =\n      // don't allow duplicate bindings to exist alongside\n      kind === \"let\" ||\n      local.kind === \"let\" ||\n      local.kind === \"const\" ||\n      local.kind === \"module\" ||\n      // don't allow a local of param with a kind of let\n      (local.kind === \"param\" && kind === \"const\");\n\n    if (duplicate) {\n      throw this.hub.buildError(\n        id,\n        `Duplicate declaration \"${name}\"`,\n        TypeError,\n      );\n    }\n  }\n\n  rename(\n    oldName: string,\n    newName?: string,\n    // prettier-ignore\n    /* Babel 7 - block?: t.Pattern | t.Scopable */\n  ) {\n    const binding = this.getBinding(oldName);\n    if (binding) {\n      newName ||= this.generateUidIdentifier(oldName).name;\n      const renamer = new Renamer(binding, oldName, newName);\n      if (process.env.BABEL_8_BREAKING) {\n        renamer.rename();\n      } else {\n        // @ts-ignore(Babel 7 vs Babel 8) TODO: Delete this\n        renamer.rename(arguments[2]);\n      }\n    }\n  }\n\n  /** @deprecated Not used in our codebase */\n  _renameFromMap(\n    map: Record<string | symbol, unknown>,\n    oldName: string | symbol,\n    newName: string | symbol,\n    value: unknown,\n  ) {\n    if (map[oldName]) {\n      map[newName] = value;\n      map[oldName] = null;\n    }\n  }\n\n  dump() {\n    const sep = \"-\".repeat(60);\n    console.log(sep);\n    let scope: Scope = this;\n    do {\n      console.log(\"#\", scope.block.type);\n      for (const name of Object.keys(scope.bindings)) {\n        const binding = scope.bindings[name];\n        console.log(\" -\", name, {\n          constant: binding.constant,\n          references: binding.references,\n          violations: binding.constantViolations.length,\n          kind: binding.kind,\n        });\n      }\n    } while ((scope = scope.parent));\n    console.log(sep);\n  }\n\n  // TODO: (Babel 8) Split i in two parameters, and use an object of flags\n  toArray(\n    node: t.Node,\n    i?: number | boolean,\n    arrayLikeIsIterable?: boolean | void,\n  ) {\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (binding?.constant && binding.path.isGenericType(\"Array\")) {\n        return node;\n      }\n    }\n\n    if (isArrayExpression(node)) {\n      return node;\n    }\n\n    if (isIdentifier(node, { name: \"arguments\" })) {\n      return callExpression(\n        memberExpression(\n          memberExpression(\n            memberExpression(identifier(\"Array\"), identifier(\"prototype\")),\n            identifier(\"slice\"),\n          ),\n          identifier(\"call\"),\n        ),\n        [node],\n      );\n    }\n\n    let helperName;\n    const args = [node];\n    if (i === true) {\n      // Used in array-spread to create an array.\n      helperName = \"toConsumableArray\";\n    } else if (typeof i === \"number\") {\n      args.push(numericLiteral(i));\n\n      // Used in array-rest to create an array from a subset of an iterable.\n      helperName = \"slicedToArray\";\n      // TODO if (this.hub.isLoose(\"es6.forOf\")) helperName += \"-loose\";\n    } else {\n      // Used in array-rest to create an array\n      helperName = \"toArray\";\n    }\n\n    if (arrayLikeIsIterable) {\n      args.unshift(this.hub.addHelper(helperName));\n      helperName = \"maybeArrayLike\";\n    }\n\n    // @ts-expect-error todo(flow->ts): t.Node is not valid to use in args, function argument typeneeds to be clarified\n    return callExpression(this.hub.addHelper(helperName), args);\n  }\n\n  hasLabel(name: string) {\n    return !!this.getLabel(name);\n  }\n\n  getLabel(name: string) {\n    return this.labels.get(name);\n  }\n\n  registerLabel(path: NodePath<t.LabeledStatement>) {\n    this.labels.set(path.node.label.name, path);\n  }\n\n  registerDeclaration(path: NodePath) {\n    if (path.isLabeledStatement()) {\n      this.registerLabel(path);\n    } else if (path.isFunctionDeclaration()) {\n      this.registerBinding(\"hoisted\", path.get(\"id\"), path);\n    } else if (path.isVariableDeclaration()) {\n      const declarations = path.get(\"declarations\");\n      const { kind } = path.node;\n      for (const declar of declarations) {\n        this.registerBinding(\n          kind === \"using\" || kind === \"await using\" ? \"const\" : kind,\n          declar,\n        );\n      }\n    } else if (path.isClassDeclaration()) {\n      if (path.node.declare) return;\n      this.registerBinding(\"let\", path);\n    } else if (path.isImportDeclaration()) {\n      const isTypeDeclaration =\n        path.node.importKind === \"type\" || path.node.importKind === \"typeof\";\n      const specifiers = path.get(\"specifiers\");\n      for (const specifier of specifiers) {\n        const isTypeSpecifier =\n          isTypeDeclaration ||\n          (specifier.isImportSpecifier() &&\n            (specifier.node.importKind === \"type\" ||\n              specifier.node.importKind === \"typeof\"));\n\n        this.registerBinding(isTypeSpecifier ? \"unknown\" : \"module\", specifier);\n      }\n    } else if (path.isExportDeclaration()) {\n      // todo: improve babel-types\n      const declar = path.get(\"declaration\") as NodePath;\n      if (\n        declar.isClassDeclaration() ||\n        declar.isFunctionDeclaration() ||\n        declar.isVariableDeclaration()\n      ) {\n        this.registerDeclaration(declar);\n      }\n    } else {\n      this.registerBinding(\"unknown\", path);\n    }\n  }\n\n  buildUndefinedNode() {\n    return unaryExpression(\"void\", numericLiteral(0), true);\n  }\n\n  registerConstantViolation(path: NodePath) {\n    const ids = path.getBindingIdentifiers();\n    for (const name of Object.keys(ids)) {\n      this.getBinding(name)?.reassign(path);\n    }\n  }\n\n  registerBinding(\n    kind: Binding[\"kind\"],\n    path: NodePath,\n    bindingPath: NodePath = path,\n  ) {\n    if (!kind) throw new ReferenceError(\"no `kind`\");\n\n    if (path.isVariableDeclaration()) {\n      const declarators: Array<NodePath> = path.get(\"declarations\");\n      for (const declar of declarators) {\n        this.registerBinding(kind, declar);\n      }\n      return;\n    }\n\n    const parent = this.getProgramParent();\n    const ids = path.getOuterBindingIdentifiers(true);\n\n    for (const name of Object.keys(ids)) {\n      parent.references[name] = true;\n\n      for (const id of ids[name]) {\n        const local = this.getOwnBinding(name);\n\n        if (local) {\n          // same identifier so continue safely as we're likely trying to register it\n          // multiple times\n          if (local.identifier === id) continue;\n\n          this.checkBlockScopedCollisions(local, kind, name, id);\n        }\n\n        // A redeclaration of an existing variable is a modification\n        if (local) {\n          this.registerConstantViolation(bindingPath);\n        } else {\n          this.bindings[name] = new Binding({\n            identifier: id,\n            scope: this,\n            path: bindingPath,\n            kind: kind,\n          });\n        }\n      }\n    }\n  }\n\n  addGlobal(node: t.Identifier | t.JSXIdentifier) {\n    this.globals[node.name] = node;\n  }\n\n  hasUid(name: string): boolean {\n    let scope: Scope = this;\n\n    do {\n      if (scope.uids[name]) return true;\n    } while ((scope = scope.parent));\n\n    return false;\n  }\n\n  hasGlobal(name: string): boolean {\n    let scope: Scope = this;\n\n    do {\n      if (scope.globals[name]) return true;\n    } while ((scope = scope.parent));\n\n    return false;\n  }\n\n  hasReference(name: string): boolean {\n    return !!this.getProgramParent().references[name];\n  }\n\n  isPure(node: t.Node, constantsOnly?: boolean): boolean {\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (!binding) return false;\n      if (constantsOnly) return binding.constant;\n      return true;\n    } else if (\n      isThisExpression(node) ||\n      isMetaProperty(node) ||\n      isTopicReference(node) ||\n      isPrivateName(node)\n    ) {\n      return true;\n    } else if (isClass(node)) {\n      if (node.superClass && !this.isPure(node.superClass, constantsOnly)) {\n        return false;\n      }\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      return this.isPure(node.body, constantsOnly);\n    } else if (isClassBody(node)) {\n      for (const method of node.body) {\n        if (!this.isPure(method, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isBinary(node)) {\n      return (\n        this.isPure(node.left, constantsOnly) &&\n        this.isPure(node.right, constantsOnly)\n      );\n    } else if (isArrayExpression(node) || isTupleExpression(node)) {\n      for (const elem of node.elements) {\n        if (elem !== null && !this.isPure(elem, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isObjectExpression(node) || isRecordExpression(node)) {\n      for (const prop of node.properties) {\n        if (!this.isPure(prop, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isMethod(node)) {\n      if (node.computed && !this.isPure(node.key, constantsOnly)) return false;\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      return true;\n    } else if (isProperty(node)) {\n      // @ts-expect-error todo(flow->ts): computed in not present on private properties\n      if (node.computed && !this.isPure(node.key, constantsOnly)) return false;\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      if (isObjectProperty(node) || node.static) {\n        if (node.value !== null && !this.isPure(node.value, constantsOnly)) {\n          return false;\n        }\n      }\n      return true;\n    } else if (isUnaryExpression(node)) {\n      return this.isPure(node.argument, constantsOnly);\n    } else if (isTaggedTemplateExpression(node)) {\n      return (\n        matchesPattern(node.tag, \"String.raw\") &&\n        !this.hasBinding(\"String\", true) &&\n        this.isPure(node.quasi, constantsOnly)\n      );\n    } else if (isTemplateLiteral(node)) {\n      for (const expression of node.expressions) {\n        if (!this.isPure(expression, constantsOnly)) return false;\n      }\n      return true;\n    } else {\n      return isPureish(node);\n    }\n  }\n\n  /**\n   * Set some arbitrary data on the current scope.\n   */\n\n  setData(key: string | symbol, val: any) {\n    return (this.data[key] = val);\n  }\n\n  /**\n   * Recursively walk up scope tree looking for the data `key`.\n   */\n\n  getData(key: string | symbol): any {\n    let scope: Scope = this;\n    do {\n      const data = scope.data[key];\n      if (data != null) return data;\n    } while ((scope = scope.parent));\n  }\n\n  /**\n   * Recursively walk up scope tree looking for the data `key` and if it exists,\n   * remove it.\n   */\n\n  removeData(key: string) {\n    let scope: Scope = this;\n    do {\n      const data = scope.data[key];\n      if (data != null) scope.data[key] = null;\n    } while ((scope = scope.parent));\n  }\n\n  init() {\n    if (!this.inited) {\n      this.inited = true;\n      this.crawl();\n    }\n  }\n\n  crawl() {\n    const path = this.path;\n\n    this.references = Object.create(null);\n    this.bindings = Object.create(null);\n    this.globals = Object.create(null);\n    this.uids = Object.create(null);\n    this.data = Object.create(null);\n\n    const programParent = this.getProgramParent();\n    if (programParent.crawling) return;\n\n    const state: CollectVisitorState = {\n      references: [],\n      constantViolations: [],\n      assignments: [],\n    };\n\n    this.crawling = true;\n    // traverse does not visit the root node, here we explicitly collect\n    // root node binding info when the root is not a Program.\n    if (path.type !== \"Program\" && isExplodedVisitor(collectorVisitor)) {\n      for (const visit of collectorVisitor.enter) {\n        visit.call(state, path, state);\n      }\n      const typeVisitors = collectorVisitor[path.type];\n      if (typeVisitors) {\n        for (const visit of typeVisitors.enter) {\n          visit.call(state, path, state);\n        }\n      }\n    }\n    path.traverse(collectorVisitor, state);\n    this.crawling = false;\n\n    // register assignments\n    for (const path of state.assignments) {\n      // register undeclared bindings as globals\n      const ids = path.getBindingIdentifiers();\n      for (const name of Object.keys(ids)) {\n        if (path.scope.getBinding(name)) continue;\n        programParent.addGlobal(ids[name]);\n      }\n\n      // register as constant violation\n      path.scope.registerConstantViolation(path);\n    }\n\n    // register references\n    for (const ref of state.references) {\n      const binding = ref.scope.getBinding(ref.node.name);\n      if (binding) {\n        binding.reference(ref);\n      } else {\n        programParent.addGlobal(ref.node);\n      }\n    }\n\n    // register constant violations\n    for (const path of state.constantViolations) {\n      path.scope.registerConstantViolation(path);\n    }\n  }\n\n  push(opts: {\n    id: t.LVal;\n    init?: t.Expression;\n    unique?: boolean;\n    _blockHoist?: number | undefined;\n    kind?: \"var\" | \"let\" | \"const\";\n  }) {\n    let path = this.path;\n\n    if (path.isPattern()) {\n      path = this.getPatternParent().path;\n    } else if (!path.isBlockStatement() && !path.isProgram()) {\n      path = this.getBlockParent().path;\n    }\n\n    if (path.isSwitchStatement()) {\n      path = (this.getFunctionParent() || this.getProgramParent()).path;\n    }\n\n    const { init, unique, kind = \"var\", id } = opts;\n\n    // When injecting a non-const non-initialized binding inside\n    // an IIFE, if the number of call arguments is less than or\n    // equal to the number of function parameters, we can safely\n    // inject the binding into the parameter list.\n    if (\n      !init &&\n      !unique &&\n      (kind === \"var\" || kind === \"let\") &&\n      path.isFunction() &&\n      // @ts-expect-error ArrowFunctionExpression never has a name\n      !path.node.name &&\n      t.isCallExpression(path.parent, { callee: path.node }) &&\n      path.parent.arguments.length <= path.node.params.length &&\n      t.isIdentifier(id)\n    ) {\n      path.pushContainer(\"params\", id);\n      path.scope.registerBinding(\n        \"param\",\n        path.get(\"params\")[path.node.params.length - 1],\n      );\n      return;\n    }\n\n    if (path.isLoop() || path.isCatchClause() || path.isFunction()) {\n      // @ts-expect-error TS can not infer NodePath<Loop> | NodePath<CatchClause> as NodePath<Loop | CatchClause>\n      path.ensureBlock();\n      // @ts-expect-error todo(flow->ts): improve types\n      path = path.get(\"body\");\n    }\n\n    const blockHoist = opts._blockHoist == null ? 2 : opts._blockHoist;\n\n    const dataKey = `declaration:${kind}:${blockHoist}`;\n    let declarPath = !unique && path.getData(dataKey);\n\n    if (!declarPath) {\n      const declar = variableDeclaration(kind, []);\n      // @ts-expect-error todo(flow->ts): avoid modifying nodes\n      declar._blockHoist = blockHoist;\n\n      [declarPath] = (path as NodePath<t.BlockStatement>).unshiftContainer(\n        \"body\",\n        [declar],\n      );\n      if (!unique) path.setData(dataKey, declarPath);\n    }\n\n    const declarator = variableDeclarator(id, init);\n    const len = declarPath.node.declarations.push(declarator);\n    path.scope.registerBinding(kind, declarPath.get(\"declarations\")[len - 1]);\n  }\n\n  /**\n   * Walk up to the top of the scope tree and get the `Program`.\n   */\n\n  getProgramParent() {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isProgram()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    throw new Error(\"Couldn't find a Program\");\n  }\n\n  /**\n   * Walk up the scope tree until we hit either a Function or return null.\n   */\n\n  getFunctionParent(): Scope | null {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isFunctionParent()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    return null;\n  }\n\n  /**\n   * Walk up the scope tree until we hit either a BlockStatement/Loop/Program/Function/Switch or reach the\n   * very top and hit Program.\n   */\n\n  getBlockParent() {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isBlockParent()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    throw new Error(\n      \"We couldn't find a BlockStatement, For, Switch, Function, Loop or Program...\",\n    );\n  }\n\n  /**\n   * Walk up from a pattern scope (function param initializer) until we hit a non-pattern scope,\n   * then returns its block parent\n   * @returns An ancestry scope whose path is a block parent\n   */\n  getPatternParent() {\n    let scope: Scope = this;\n    do {\n      if (!scope.path.isPattern()) {\n        return scope.getBlockParent();\n      }\n    } while ((scope = scope.parent.parent));\n    throw new Error(\n      \"We couldn't find a BlockStatement, For, Switch, Function, Loop or Program...\",\n    );\n  }\n\n  /**\n   * Walks the scope tree and gathers **all** bindings.\n   */\n\n  getAllBindings(): Record<string, Binding> {\n    const ids = Object.create(null);\n\n    let scope: Scope = this;\n    do {\n      for (const key of Object.keys(scope.bindings)) {\n        if (key in ids === false) {\n          ids[key] = scope.bindings[key];\n        }\n      }\n      scope = scope.parent;\n    } while (scope);\n\n    return ids;\n  }\n\n  /**\n   * Walks the scope tree and gathers all declarations of `kind`.\n   */\n\n  getAllBindingsOfKind(...kinds: string[]): Record<string, Binding> {\n    const ids = Object.create(null);\n\n    for (const kind of kinds) {\n      let scope: Scope = this;\n      do {\n        for (const name of Object.keys(scope.bindings)) {\n          const binding = scope.bindings[name];\n          if (binding.kind === kind) ids[name] = binding;\n        }\n        scope = scope.parent;\n      } while (scope);\n    }\n\n    return ids;\n  }\n\n  bindingIdentifierEquals(name: string, node: t.Node): boolean {\n    return this.getBindingIdentifier(name) === node;\n  }\n\n  getBinding(name: string): Binding | undefined {\n    let scope: Scope = this;\n    let previousPath;\n\n    do {\n      const binding = scope.getOwnBinding(name);\n      if (binding) {\n        // Check if a pattern is a part of parameter expressions.\n        // Note: for performance reason we skip checking previousPath.parentPath.isFunction()\n        // because `scope.path` is validated as scope in packages/babel-types/src/validators/isScope.js\n        // That is, if a scope path is pattern, its parent must be Function/CatchClause\n\n        // Spec *********: The closure created by this expression should not have visibility of\n        // declarations in the function body. If the binding is not a `param`-kind (as function parameters)\n        // or `local`-kind (as id in function expression),\n        // then it must be defined inside the function body, thus it should be skipped\n        if (\n          previousPath?.isPattern() &&\n          binding.kind !== \"param\" &&\n          binding.kind !== \"local\"\n        ) {\n          // do nothing\n        } else {\n          return binding;\n        }\n      } else if (\n        !binding &&\n        name === \"arguments\" &&\n        scope.path.isFunction() &&\n        !scope.path.isArrowFunctionExpression()\n      ) {\n        break;\n      }\n      previousPath = scope.path;\n    } while ((scope = scope.parent));\n  }\n\n  getOwnBinding(name: string): Binding | undefined {\n    return this.bindings[name];\n  }\n\n  // todo: return probably can be undefined…\n  getBindingIdentifier(name: string): t.Identifier {\n    return this.getBinding(name)?.identifier;\n  }\n\n  // todo: flow->ts return probably can be undefined\n  getOwnBindingIdentifier(name: string): t.Identifier {\n    const binding = this.bindings[name];\n    return binding?.identifier;\n  }\n\n  hasOwnBinding(name: string) {\n    return !!this.getOwnBinding(name);\n  }\n\n  // By default, we consider generated UIDs as bindings.\n  // This is because they are almost always used to declare variables,\n  // and since the scope isn't always up-to-date it's better to assume that\n  // there is a variable with that name. The `noUids` option can be used to\n  // turn off this behavior, for example if you know that the generate UID\n  // was used to declare a variable in a different scope.\n  hasBinding(\n    name: string,\n    opts?: boolean | { noGlobals?: boolean; noUids?: boolean },\n  ) {\n    if (!name) return false;\n    if (this.hasOwnBinding(name)) return true;\n    {\n      // TODO: Only accept the object form.\n      if (typeof opts === \"boolean\") opts = { noGlobals: opts };\n    }\n    if (this.parentHasBinding(name, opts)) return true;\n    if (!opts?.noUids && this.hasUid(name)) return true;\n    if (!opts?.noGlobals && Scope.globals.includes(name)) return true;\n    if (!opts?.noGlobals && Scope.contextVariables.includes(name)) return true;\n    return false;\n  }\n\n  parentHasBinding(\n    name: string,\n    opts?: { noGlobals?: boolean; noUids?: boolean },\n  ) {\n    return this.parent?.hasBinding(name, opts);\n  }\n\n  /**\n   * Move a binding of `name` to another `scope`.\n   */\n\n  moveBindingTo(name: string, scope: Scope) {\n    const info = this.getBinding(name);\n    if (info) {\n      info.scope.removeOwnBinding(name);\n      info.scope = scope;\n      scope.bindings[name] = info;\n    }\n  }\n\n  removeOwnBinding(name: string) {\n    delete this.bindings[name];\n  }\n\n  removeBinding(name: string) {\n    // clear literal binding\n    this.getBinding(name)?.scope.removeOwnBinding(name);\n\n    // clear uids with this name - https://github.com/babel/babel/issues/2101\n    let scope: Scope = this;\n    do {\n      if (scope.uids[name]) {\n        scope.uids[name] = false;\n      }\n    } while ((scope = scope.parent));\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAEA,IAAAE,QAAA,GAAAF,OAAA;AAEA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,EAAA,GAAAJ,OAAA;AA6CsB,IAAAK,CAAA,GAAAD,EAAA;AAEtB,IAAAE,MAAA,GAAAN,OAAA;AAEA,IAAAO,SAAA,GAAAP,OAAA;AAAmD;EAhDjDQ,iBAAiB;EACjBC,cAAc;EACdC,SAAS;EACTC,qBAAqB;EACrBC,UAAU;EACVC,iBAAiB;EACjBC,QAAQ;EACRC,OAAO;EACPC,WAAW;EACXC,kBAAkB;EAClBC,sBAAsB;EACtBC,0BAA0B;EAC1BC,wBAAwB;EACxBC,qBAAqB;EACrBC,YAAY;EACZC,mBAAmB;EACnBC,SAAS;EACTC,QAAQ;EACRC,iBAAiB;EACjBC,aAAa;EACbC,kBAAkB;EAClBC,UAAU;EACVC,SAAS;EACTC,eAAe;EACfC,OAAO;EACPC,0BAA0B;EAC1BC,iBAAiB;EACjBC,gBAAgB;EAChBC,iBAAiB;EACjBC,qBAAqB;EACrBC,cAAc;EACdC,gBAAgB;EAChBC,cAAc;EACdC,YAAY;EACZC,eAAe;EACfC,mBAAmB;EACnBC,kBAAkB;EAClBC,kBAAkB;EAClBC,iBAAiB;EACjBC,gBAAgB;EAChBC,gBAAgB;EAChBC,cAAc;EACdC,aAAa;EACbC;AAAmB,IAAA/C,EAAA;AASrB,SAASgD,eAAeA,CAACC,IAAY,EAAEC,KAAiB,EAAE;EACxD,QAAQD,IAAI,oBAAJA,IAAI,CAAEE,IAAI;IAChB;MACE,IAAIhC,mBAAmB,CAAC8B,IAAI,CAAC,IAAIF,mBAAmB,CAACE,IAAI,CAAC,EAAE;QAAA,IAAAG,gBAAA;QAC1D,IACE,CAACtC,sBAAsB,CAACmC,IAAI,CAAC,IAC3BjC,wBAAwB,CAACiC,IAAI,CAAC,IAC9B9B,mBAAmB,CAAC8B,IAAI,CAAC,KAC3BA,IAAI,CAACI,MAAM,EACX;UACAL,eAAe,CAACC,IAAI,CAACI,MAAM,EAAEH,KAAK,CAAC;QACrC,CAAC,MAAM,IACL,CAAClC,wBAAwB,CAACiC,IAAI,CAAC,IAAI9B,mBAAmB,CAAC8B,IAAI,CAAC,MAAAG,gBAAA,GAC5DH,IAAI,CAACK,UAAU,aAAfF,gBAAA,CAAiBG,MAAM,EACvB;UACA,KAAK,MAAMC,CAAC,IAAIP,IAAI,CAACK,UAAU,EAAEN,eAAe,CAACQ,CAAC,EAAEN,KAAK,CAAC;QAC5D,CAAC,MAAM,IACL,CAACnC,0BAA0B,CAACkC,IAAI,CAAC,IAC/BjC,wBAAwB,CAACiC,IAAI,CAAC,KAChCA,IAAI,CAACQ,WAAW,EAChB;UACAT,eAAe,CAACC,IAAI,CAACQ,WAAW,EAAEP,KAAK,CAAC;QAC1C;MACF,CAAC,MAAM,IAAI5B,iBAAiB,CAAC2B,IAAI,CAAC,EAAE;QAUlCD,eAAe,CAACC,IAAI,CAACS,KAAK,EAAER,KAAK,CAAC;MACpC,CAAC,MAAM,IACL9B,SAAS,CAAC6B,IAAI,CAAC,IACf,CAAC1B,aAAa,CAAC0B,IAAI,CAAC,IACpB,CAACtB,eAAe,CAACsB,IAAI,CAAC,IACtB,CAACnB,iBAAiB,CAACmB,IAAI,CAAC,EACxB;QACAC,KAAK,CAACS,IAAI,CAACV,IAAI,CAACW,KAAK,CAAC;MACxB;MACA;IAEF,KAAK,kBAAkB;IACvB,KAAK,0BAA0B;IAC/B,KAAK,qBAAqB;MACxBZ,eAAe,CAACC,IAAI,CAACY,MAAM,EAAEX,KAAK,CAAC;MACnCF,eAAe,CAACC,IAAI,CAACa,QAAQ,EAAEZ,KAAK,CAAC;MACrC;IAEF,KAAK,YAAY;IACjB,KAAK,eAAe;MAClBA,KAAK,CAACS,IAAI,CAACV,IAAI,CAACc,IAAI,CAAC;MACrB;IAEF,KAAK,gBAAgB;IACrB,KAAK,wBAAwB;IAC7B,KAAK,eAAe;MAClBf,eAAe,CAACC,IAAI,CAACe,MAAM,EAAEd,KAAK,CAAC;MACnC;IAEF,KAAK,kBAAkB;IACvB,KAAK,eAAe;MAClB,KAAK,MAAMM,CAAC,IAAIP,IAAI,CAACgB,UAAU,EAAE;QAC/BjB,eAAe,CAACQ,CAAC,EAAEN,KAAK,CAAC;MAC3B;MACA;IAEF,KAAK,eAAe;IACpB,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,gBAAgB;IACrB,KAAK,cAAc;IACnB,KAAK,eAAe;IACpB,KAAK,aAAa;IAClB,KAAK,sBAAsB;IAC3B,KAAK,oBAAoB;MACvBF,eAAe,CAACC,IAAI,CAACkB,GAAG,EAAEjB,KAAK,CAAC;MAChC;IAEF,KAAK,gBAAgB;MACnBA,KAAK,CAACS,IAAI,CAAC,MAAM,CAAC;MAClB;IAEF,KAAK,OAAO;MACVT,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnB;IAEF,KAAK,QAAQ;MACXT,KAAK,CAACS,IAAI,CAAC,QAAQ,CAAC;MACpB;IAEF,KAAK,cAAc;MACjBT,KAAK,CAACS,IAAI,CAAC,IAAI,CAAC;MAChB;IAEF,KAAK,iBAAiB;MACpBT,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnBX,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,iBAAiB;MACpBA,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnBX,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,sBAAsB;MACzBF,eAAe,CAACC,IAAI,CAACmB,IAAI,EAAElB,KAAK,CAAC;MACjC;IAEF,KAAK,oBAAoB;MACvBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,oBAAoB;IACzB,KAAK,qBAAqB;IAC1B,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,yBAAyB;MAC5BF,eAAe,CAACC,IAAI,CAACqB,UAAU,EAAEpB,KAAK,CAAC;MACvC;IAEF,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrBF,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,cAAc;MACjBF,eAAe,CAACC,IAAI,CAACsB,IAAI,EAAErB,KAAK,CAAC;MACjCF,eAAe,CAACC,IAAI,CAACa,QAAQ,EAAEZ,KAAK,CAAC;MACrC;IAEF,KAAK,YAAY;MACfF,eAAe,CAACC,IAAI,CAACuB,cAAc,EAAEtB,KAAK,CAAC;MAC3C;IAEF,KAAK,mBAAmB;MACtBF,eAAe,CAACC,IAAI,CAACc,IAAI,EAAEb,KAAK,CAAC;MACjC;IAEF,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACwB,eAAe,EAAEvB,KAAK,CAAC;MAC5C;IAEF,KAAK,oBAAoB;MACvBA,KAAK,CAACS,IAAI,CAAC,UAAU,CAAC;MACtB;IAEF,KAAK,mBAAmB;MACtBX,eAAe,CAACC,IAAI,CAACyB,SAAS,EAAExB,KAAK,CAAC;MACtCF,eAAe,CAACC,IAAI,CAACc,IAAI,EAAEb,KAAK,CAAC;MACjC;EACJ;AACF;AASA,MAAMyB,gBAA8C,GAAG;EACrDC,YAAYA,CAACC,IAAI,EAAE;IACjB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IAE/B,IAAID,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE;MAClB,MAAM;QAAEC;MAAM,CAAC,GAAGJ,IAAI;MACtB,MAAMK,WAAW,GAAGD,KAAK,CAACE,iBAAiB,CAAC,CAAC,IAAIF,KAAK,CAACG,gBAAgB,CAAC,CAAC;MACzEF,WAAW,CAACG,eAAe,CAAC,KAAK,EAAEP,MAAM,CAAC;IAC5C;EACF,CAAC;EAEDQ,WAAWA,CAACT,IAAI,EAAE;IAEhB,IAAIA,IAAI,CAACU,aAAa,CAAC,CAAC,EAAE;IAG1B,IAAIV,IAAI,CAAC1D,mBAAmB,CAAC,CAAC,EAAE;IAGhC,IAAI0D,IAAI,CAAC9B,mBAAmB,CAAC,CAAC,EAAE;IAGhC,MAAMyC,MAAM,GACVX,IAAI,CAACI,KAAK,CAACE,iBAAiB,CAAC,CAAC,IAAIN,IAAI,CAACI,KAAK,CAACG,gBAAgB,CAAC,CAAC;IACjEI,MAAM,CAACC,mBAAmB,CAACZ,IAAI,CAAC;EAClC,CAAC;EAEDa,iBAAiBA,CAACb,IAAI,EAAE;IAEtB,MAAMW,MAAM,GAAGX,IAAI,CAACI,KAAK,CAACU,cAAc,CAAC,CAAC;IAE1CH,MAAM,CAACC,mBAAmB,CAACZ,IAAI,CAAC;EAClC,CAAC;EAEDe,oBAAoBA,CAACf,IAAI,EAAEgB,KAAK,EAAE;IAChCA,KAAK,CAACC,UAAU,CAACnC,IAAI,CAACkB,IAAI,CAAC;EAC7B,CAAC;EAEDkB,aAAaA,CAAClB,IAAI,EAAEgB,KAAK,EAAE;IACzB,MAAMzB,IAAI,GAAGS,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IAC7B,IAAIX,IAAI,CAAC4B,SAAS,CAAC,CAAC,IAAI5B,IAAI,CAAClD,YAAY,CAAC,CAAC,EAAE;MAC3C2E,KAAK,CAACI,kBAAkB,CAACtC,IAAI,CAACkB,IAAI,CAAC;IACrC,CAAC,MAEI,IAAIT,IAAI,CAACY,KAAK,CAAC,CAAC,EAAE;MACrB,MAAM;QAAEC;MAAM,CAAC,GAAGJ,IAAI;MACtB,MAAMK,WAAW,GAAGD,KAAK,CAACE,iBAAiB,CAAC,CAAC,IAAIF,KAAK,CAACG,gBAAgB,CAAC,CAAC;MACzEF,WAAW,CAACG,eAAe,CAAC,KAAK,EAAEjB,IAAI,CAAC;IAC1C;EACF,CAAC;EAED8B,iBAAiB,EAAE;IACjBC,IAAIA,CAACtB,IAAI,EAAE;MACT,MAAM;QAAE5B,IAAI;QAAEgC;MAAM,CAAC,GAAGJ,IAAI;MAE5B,IAAI/D,sBAAsB,CAACmC,IAAI,CAAC,EAAE;MAClC,MAAM6B,MAAM,GAAG7B,IAAI,CAACQ,WAAW;MAC/B,IAAI5C,kBAAkB,CAACiE,MAAM,CAAC,IAAI7D,qBAAqB,CAAC6D,MAAM,CAAC,EAAE;QAC/D,MAAMT,EAAE,GAAGS,MAAM,CAACT,EAAE;QACpB,IAAI,CAACA,EAAE,EAAE;QAET,MAAM+B,OAAO,GAAGnB,KAAK,CAACoB,UAAU,CAAChC,EAAE,CAACN,IAAI,CAAC;QACzCqC,OAAO,oBAAPA,OAAO,CAAEE,SAAS,CAACzB,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAI5C,qBAAqB,CAAC6C,MAAM,CAAC,EAAE;QACxC,KAAK,MAAMyB,IAAI,IAAIzB,MAAM,CAAC0B,YAAY,EAAE;UACtC,KAAK,MAAMzC,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACnG,qBAAqB,CAACgG,IAAI,CAAC,CAAC,EAAE;YAC3D,MAAMH,OAAO,GAAGnB,KAAK,CAACoB,UAAU,CAACtC,IAAI,CAAC;YACtCqC,OAAO,oBAAPA,OAAO,CAAEE,SAAS,CAACzB,IAAI,CAAC;UAC1B;QACF;MACF;IACF;EACF,CAAC;EAED8B,gBAAgBA,CAAC9B,IAAI,EAAE;IACrBA,IAAI,CAACI,KAAK,CAACU,cAAc,CAAC,CAAC,CAACF,mBAAmB,CAACZ,IAAI,CAAC;EACvD,CAAC;EAED+B,oBAAoBA,CAAC/B,IAAI,EAAEgB,KAAK,EAAE;IAChCA,KAAK,CAACgB,WAAW,CAAClD,IAAI,CAACkB,IAAI,CAAC;EAC9B,CAAC;EAEDiC,gBAAgBA,CAACjC,IAAI,EAAEgB,KAAK,EAAE;IAC5BA,KAAK,CAACI,kBAAkB,CAACtC,IAAI,CAACkB,IAAI,CAAC;EACrC,CAAC;EAEDkC,eAAeA,CAAClC,IAAI,EAAEgB,KAAK,EAAE;IAC3B,IAAIhB,IAAI,CAAC5B,IAAI,CAAC+D,QAAQ,KAAK,QAAQ,EAAE;MACnCnB,KAAK,CAACI,kBAAkB,CAACtC,IAAI,CAACkB,IAAI,CAAC;IACrC;EACF,CAAC;EAEDoC,WAAWA,CAACpC,IAAI,EAAE;IAChB,IAAII,KAAK,GAAGJ,IAAI,CAACI,KAAK;IACtB,IAAIA,KAAK,CAACJ,IAAI,KAAKA,IAAI,EAAEI,KAAK,GAAGA,KAAK,CAACO,MAAM;IAE7C,MAAMA,MAAM,GAAGP,KAAK,CAACU,cAAc,CAAC,CAAC;IACrCH,MAAM,CAACC,mBAAmB,CAACZ,IAAI,CAAC;IAGhC,IAAIA,IAAI,CAAChE,kBAAkB,CAAC,CAAC,IAAIgE,IAAI,CAAC5B,IAAI,CAACoB,EAAE,EAAE;MAC7C,MAAMA,EAAE,GAAGQ,IAAI,CAAC5B,IAAI,CAACoB,EAAE;MACvB,MAAMN,IAAI,GAAGM,EAAE,CAACN,IAAI;MAEpBc,IAAI,CAACI,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC,GAAGc,IAAI,CAACI,KAAK,CAACO,MAAM,CAACa,UAAU,CAACtC,IAAI,CAAC;IAChE;EACF,CAAC;EAEDoD,WAAWA,CAACtC,IAAI,EAAE;IAChBA,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,KAAK,EAAER,IAAI,CAAC;EACzC,CAAC;EAEDuC,QAAQA,CAACvC,IAAI,EAAE;IACb,MAAMwC,MAAuB,GAAGxC,IAAI,CAACE,GAAG,CAAC,QAAQ,CAAC;IAClD,KAAK,MAAMuC,KAAK,IAAID,MAAM,EAAE;MAC1BxC,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,OAAO,EAAEiC,KAAK,CAAC;IAC5C;IAKA,IACEzC,IAAI,CAAC0C,oBAAoB,CAAC,CAAC,IAC3B1C,IAAI,CAAC2C,GAAG,CAAC,IAAI,CAAC,IAEd,CAAC3C,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,CAAC9B,IAAI,CAAC7C,iBAAiB,CAAC,EACvC;MACAyE,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,OAAO,EAAER,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IAC3D;EACF,CAAC;EAED4C,eAAeA,CAAC5C,IAAI,EAAE;IACpB,IACEA,IAAI,CAAC2C,GAAG,CAAC,IAAI,CAAC,IAEd,CAAC3C,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,CAAC9B,IAAI,CAAC7C,iBAAiB,CAAC,EACvC;MACAyE,IAAI,CAACI,KAAK,CAACI,eAAe,CAAC,OAAO,EAAER,IAAI,CAAC;IAC3C;EACF;AACF,CAAC;AAED,IAAI6C,GAAG,GAAG,CAAC;AAII,MAAMC,KAAK,CAAC;EAoBzBC,WAAWA,CAAC/C,IAAsC,EAAE;IAAA,KAnBpD6C,GAAG;IAAA,KAEH7C,IAAI;IAAA,KACJgD,KAAK;IAAA,KAELC,MAAM;IAAA,KACNC,MAAM;IAAA,KAENb,QAAQ;IAAA,KACRpB,UAAU;IAAA,KACVkC,OAAO;IAAA,KACPC,IAAI;IAAA,KACJC,IAAI;IAAA,KACJC,QAAQ;IAON,MAAM;MAAElF;IAAK,CAAC,GAAG4B,IAAI;IACrB,MAAMuD,MAAM,GAAGC,YAAU,CAACtD,GAAG,CAAC9B,IAAI,CAAC;IAGnC,IAAI,CAAAmF,MAAM,oBAANA,MAAM,CAAEvD,IAAI,MAAKA,IAAI,EAAE;MACzB,OAAOuD,MAAM;IACf;IACAC,YAAU,CAACC,GAAG,CAACrF,IAAI,EAAE,IAAI,CAAC;IAE1B,IAAI,CAACyE,GAAG,GAAGA,GAAG,EAAE;IAEhB,IAAI,CAACG,KAAK,GAAG5E,IAAI;IACjB,IAAI,CAAC4B,IAAI,GAAGA,IAAI;IAEhB,IAAI,CAACiD,MAAM,GAAG,IAAIS,GAAG,CAAC,CAAC;IACvB,IAAI,CAACR,MAAM,GAAG,KAAK;EACrB;EAcA,IAAIvC,MAAMA,CAAA,EAAG;IAAA,IAAAgD,OAAA;IACX,IAAIhD,MAAM;MACRX,IAAI,GAAG,IAAI,CAACA,IAAI;IAClB,GAAG;MAED,MAAM4D,UAAU,GAAG5D,IAAI,CAACV,GAAG,KAAK,KAAK,IAAIU,IAAI,CAAC6D,OAAO,KAAK,YAAY;MACtE7D,IAAI,GAAGA,IAAI,CAAC8D,UAAU;MACtB,IAAIF,UAAU,IAAI5D,IAAI,CAACxD,QAAQ,CAAC,CAAC,EAAEwD,IAAI,GAAGA,IAAI,CAAC8D,UAAU;MACzD,IAAI9D,IAAI,IAAIA,IAAI,CAAC+D,OAAO,CAAC,CAAC,EAAEpD,MAAM,GAAGX,IAAI;IAC3C,CAAC,QAAQA,IAAI,IAAI,CAACW,MAAM;IAExB,QAAAgD,OAAA,GAAOhD,MAAM,qBAANgD,OAAA,CAAQvD,KAAK;EACtB;EAEA,IAAI4D,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAChE,IAAI,CAACW,MAAM;EACzB;EAEA,IAAIsD,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACjE,IAAI,CAACiE,GAAG;EACtB;EAmBAC,QAAQA,CAAI9F,IAAS,EAAE+F,IAAS,EAAEnD,KAAS,EAAE;IAC3C,IAAAkD,cAAQ,EAAC9F,IAAI,EAAE+F,IAAI,EAAE,IAAI,EAAEnD,KAAK,EAAE,IAAI,CAAChB,IAAI,CAAC;EAC9C;EAMAoE,6BAA6BA,CAAClF,IAAa,EAAE;IAC3C,MAAMM,EAAE,GAAG,IAAI,CAAC6E,qBAAqB,CAACnF,IAAI,CAAC;IAC3C,IAAI,CAACJ,IAAI,CAAC;MAAEU;IAAG,CAAC,CAAC;IACjB,OAAO/D,SAAS,CAAC+D,EAAE,CAAC;EACtB;EAMA6E,qBAAqBA,CAACnF,IAAa,EAAE;IACnC,OAAOvD,UAAU,CAAC,IAAI,CAAC2I,WAAW,CAACpF,IAAI,CAAC,CAAC;EAC3C;EAMAoF,WAAWA,CAACpF,IAAY,GAAG,MAAM,EAAU;IACzCA,IAAI,GAAG1B,YAAY,CAAC0B,IAAI,CAAC,CACtBqF,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IAE1B,IAAI1B,GAAG;IACP,IAAI2B,CAAC,GAAG,CAAC;IACT,GAAG;MACD3B,GAAG,GAAG,IAAI,CAAC4B,YAAY,CAACvF,IAAI,EAAEsF,CAAC,CAAC;MAChCA,CAAC,EAAE;IACL,CAAC,QACC,IAAI,CAACE,QAAQ,CAAC7B,GAAG,CAAC,IAClB,IAAI,CAAC8B,UAAU,CAAC9B,GAAG,CAAC,IACpB,IAAI,CAAC+B,SAAS,CAAC/B,GAAG,CAAC,IACnB,IAAI,CAACgC,YAAY,CAAChC,GAAG,CAAC;IAGxB,MAAMiC,OAAO,GAAG,IAAI,CAACvE,gBAAgB,CAAC,CAAC;IACvCuE,OAAO,CAAC7D,UAAU,CAAC4B,GAAG,CAAC,GAAG,IAAI;IAC9BiC,OAAO,CAAC1B,IAAI,CAACP,GAAG,CAAC,GAAG,IAAI;IAExB,OAAOA,GAAG;EACZ;EAMA4B,YAAYA,CAACvF,IAAY,EAAEsF,CAAS,EAAE;IACpC,IAAIhF,EAAE,GAAGN,IAAI;IACb,IAAIsF,CAAC,GAAG,CAAC,EAAEhF,EAAE,IAAIgF,CAAC;IAClB,OAAQ,IAAGhF,EAAG,EAAC;EACjB;EAEAuF,sBAAsBA,CAAC3G,IAAY,EAAE4G,WAAoB,EAAE;IACzD,MAAM3G,KAAiB,GAAG,EAAE;IAC5BF,eAAe,CAACC,IAAI,EAAEC,KAAK,CAAC;IAE5B,IAAImB,EAAE,GAAGnB,KAAK,CAAC4G,IAAI,CAAC,GAAG,CAAC;IACxBzF,EAAE,GAAGA,EAAE,CAAC+E,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,IAAIS,WAAW,IAAI,KAAK;IAEjD,OAAO,IAAI,CAACV,WAAW,CAAC9E,EAAE,CAAC0F,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1C;EAMAC,gCAAgCA,CAAC/G,IAAY,EAAE4G,WAAoB,EAAE;IACnE,OAAOrJ,UAAU,CAAC,IAAI,CAACoJ,sBAAsB,CAAC3G,IAAI,EAAE4G,WAAW,CAAC,CAAC;EACnE;EAYAI,QAAQA,CAAChH,IAAY,EAAW;IAC9B,IAAIlB,gBAAgB,CAACkB,IAAI,CAAC,IAAIrB,OAAO,CAACqB,IAAI,CAAC,IAAIL,gBAAgB,CAACK,IAAI,CAAC,EAAE;MACrE,OAAO,IAAI;IACb;IAEA,IAAI/B,YAAY,CAAC+B,IAAI,CAAC,EAAE;MACtB,MAAMmD,OAAO,GAAG,IAAI,CAACC,UAAU,CAACpD,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAIqC,OAAO,EAAE;QACX,OAAOA,OAAO,CAAC8D,QAAQ;MACzB,CAAC,MAAM;QACL,OAAO,IAAI,CAACV,UAAU,CAACvG,IAAI,CAACc,IAAI,CAAC;MACnC;IACF;IAEA,OAAO,KAAK;EACd;EAMAoG,qBAAqBA,CAAClH,IAAY,EAAEmH,QAAkB,EAAE;IACtD,IAAI,IAAI,CAACH,QAAQ,CAAChH,IAAI,CAAC,EAAE;MACvB,OAAO,IAAI;IACb,CAAC,MAAM;MACL,MAAMoB,EAAE,GAAG,IAAI,CAAC2F,gCAAgC,CAAC/G,IAAI,CAAC;MACtD,IAAI,CAACmH,QAAQ,EAAE;QACb,IAAI,CAACzG,IAAI,CAAC;UAAEU;QAAG,CAAC,CAAC;QACjB,OAAO/D,SAAS,CAAC+D,EAAE,CAAC;MACtB;MACA,OAAOA,EAAE;IACX;EACF;EAEAgG,0BAA0BA,CACxB3G,KAAc,EACd4G,IAAiB,EACjBvG,IAAY,EACZM,EAAO,EACP;IAEA,IAAIiG,IAAI,KAAK,OAAO,EAAE;IAItB,IAAI5G,KAAK,CAAC4G,IAAI,KAAK,OAAO,EAAE;IAE5B,MAAMC,SAAS,GAEbD,IAAI,KAAK,KAAK,IACd5G,KAAK,CAAC4G,IAAI,KAAK,KAAK,IACpB5G,KAAK,CAAC4G,IAAI,KAAK,OAAO,IACtB5G,KAAK,CAAC4G,IAAI,KAAK,QAAQ,IAEtB5G,KAAK,CAAC4G,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAQ;IAE9C,IAAIC,SAAS,EAAE;MACb,MAAM,IAAI,CAACzB,GAAG,CAAC0B,UAAU,CACvBnG,EAAE,EACD,0BAAyBN,IAAK,GAAE,EACjC0G,SACF,CAAC;IACH;EACF;EAEAC,MAAMA,CACJC,OAAe,EACfC,OAAgB,EAGhB;IACA,MAAMxE,OAAO,GAAG,IAAI,CAACC,UAAU,CAACsE,OAAO,CAAC;IACxC,IAAIvE,OAAO,EAAE;MACXwE,OAAO,KAAPA,OAAO,GAAK,IAAI,CAAC1B,qBAAqB,CAACyB,OAAO,CAAC,CAAC5G,IAAI;MACpD,MAAM8G,OAAO,GAAG,IAAIC,gBAAO,CAAC1E,OAAO,EAAEuE,OAAO,EAAEC,OAAO,CAAC;MAG/C;QAELC,OAAO,CAACH,MAAM,CAACK,SAAS,CAAC,CAAC,CAAC,CAAC;MAC9B;IACF;EACF;EAGAC,cAAcA,CACZC,GAAqC,EACrCN,OAAwB,EACxBC,OAAwB,EACxBhH,KAAc,EACd;IACA,IAAIqH,GAAG,CAACN,OAAO,CAAC,EAAE;MAChBM,GAAG,CAACL,OAAO,CAAC,GAAGhH,KAAK;MACpBqH,GAAG,CAACN,OAAO,CAAC,GAAG,IAAI;IACrB;EACF;EAEAO,IAAIA,CAAA,EAAG;IACL,MAAMC,GAAG,GAAG,GAAG,CAACC,MAAM,CAAC,EAAE,CAAC;IAC1BC,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;IAChB,IAAIlG,KAAY,GAAG,IAAI;IACvB,GAAG;MACDoG,OAAO,CAACC,GAAG,CAAC,GAAG,EAAErG,KAAK,CAAC4C,KAAK,CAAC1E,IAAI,CAAC;MAClC,KAAK,MAAMY,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACzB,KAAK,CAACiC,QAAQ,CAAC,EAAE;QAC9C,MAAMd,OAAO,GAAGnB,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC;QACpCsH,OAAO,CAACC,GAAG,CAAC,IAAI,EAAEvH,IAAI,EAAE;UACtBmG,QAAQ,EAAE9D,OAAO,CAAC8D,QAAQ;UAC1BpE,UAAU,EAAEM,OAAO,CAACN,UAAU;UAC9ByF,UAAU,EAAEnF,OAAO,CAACH,kBAAkB,CAAC1C,MAAM;UAC7C+G,IAAI,EAAElE,OAAO,CAACkE;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,QAASrF,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9B6F,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;EAClB;EAGAK,OAAOA,CACLvI,IAAY,EACZoG,CAAoB,EACpBoC,mBAAoC,EACpC;IACA,IAAIvK,YAAY,CAAC+B,IAAI,CAAC,EAAE;MACtB,MAAMmD,OAAO,GAAG,IAAI,CAACC,UAAU,CAACpD,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAIqC,OAAO,YAAPA,OAAO,CAAE8D,QAAQ,IAAI9D,OAAO,CAACvB,IAAI,CAAC6G,aAAa,CAAC,OAAO,CAAC,EAAE;QAC5D,OAAOzI,IAAI;MACb;IACF;IAEA,IAAIxC,iBAAiB,CAACwC,IAAI,CAAC,EAAE;MAC3B,OAAOA,IAAI;IACb;IAEA,IAAI/B,YAAY,CAAC+B,IAAI,EAAE;MAAEc,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE;MAC7C,OAAO1D,cAAc,CACnB8B,gBAAgB,CACdA,gBAAgB,CACdA,gBAAgB,CAAC3B,UAAU,CAAC,OAAO,CAAC,EAAEA,UAAU,CAAC,WAAW,CAAC,CAAC,EAC9DA,UAAU,CAAC,OAAO,CACpB,CAAC,EACDA,UAAU,CAAC,MAAM,CACnB,CAAC,EACD,CAACyC,IAAI,CACP,CAAC;IACH;IAEA,IAAI0I,UAAU;IACd,MAAMC,IAAI,GAAG,CAAC3I,IAAI,CAAC;IACnB,IAAIoG,CAAC,KAAK,IAAI,EAAE;MAEdsC,UAAU,GAAG,mBAAmB;IAClC,CAAC,MAAM,IAAI,OAAOtC,CAAC,KAAK,QAAQ,EAAE;MAChCuC,IAAI,CAACjI,IAAI,CAACvB,cAAc,CAACiH,CAAC,CAAC,CAAC;MAG5BsC,UAAU,GAAG,eAAe;IAE9B,CAAC,MAAM;MAELA,UAAU,GAAG,SAAS;IACxB;IAEA,IAAIF,mBAAmB,EAAE;MACvBG,IAAI,CAACC,OAAO,CAAC,IAAI,CAAC/C,GAAG,CAACgD,SAAS,CAACH,UAAU,CAAC,CAAC;MAC5CA,UAAU,GAAG,gBAAgB;IAC/B;IAGA,OAAOtL,cAAc,CAAC,IAAI,CAACyI,GAAG,CAACgD,SAAS,CAACH,UAAU,CAAC,EAAEC,IAAI,CAAC;EAC7D;EAEArC,QAAQA,CAACxF,IAAY,EAAE;IACrB,OAAO,CAAC,CAAC,IAAI,CAACgI,QAAQ,CAAChI,IAAI,CAAC;EAC9B;EAEAgI,QAAQA,CAAChI,IAAY,EAAE;IACrB,OAAO,IAAI,CAAC+D,MAAM,CAAC/C,GAAG,CAAChB,IAAI,CAAC;EAC9B;EAEAiI,aAAaA,CAACnH,IAAkC,EAAE;IAChD,IAAI,CAACiD,MAAM,CAACQ,GAAG,CAACzD,IAAI,CAAC5B,IAAI,CAACgJ,KAAK,CAAClI,IAAI,EAAEc,IAAI,CAAC;EAC7C;EAEAY,mBAAmBA,CAACZ,IAAc,EAAE;IAClC,IAAIA,IAAI,CAACqH,kBAAkB,CAAC,CAAC,EAAE;MAC7B,IAAI,CAACF,aAAa,CAACnH,IAAI,CAAC;IAC1B,CAAC,MAAM,IAAIA,IAAI,CAAC5D,qBAAqB,CAAC,CAAC,EAAE;MACvC,IAAI,CAACoE,eAAe,CAAC,SAAS,EAAER,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IACvD,CAAC,MAAM,IAAIA,IAAI,CAAC5C,qBAAqB,CAAC,CAAC,EAAE;MACvC,MAAMuE,YAAY,GAAG3B,IAAI,CAACE,GAAG,CAAC,cAAc,CAAC;MAC7C,MAAM;QAAEuF;MAAK,CAAC,GAAGzF,IAAI,CAAC5B,IAAI;MAC1B,KAAK,MAAM6B,MAAM,IAAI0B,YAAY,EAAE;QACjC,IAAI,CAACnB,eAAe,CAClBiF,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,aAAa,GAAG,OAAO,GAAGA,IAAI,EAC3DxF,MACF,CAAC;MACH;IACF,CAAC,MAAM,IAAID,IAAI,CAAChE,kBAAkB,CAAC,CAAC,EAAE;MACpC,IAAIgE,IAAI,CAAC5B,IAAI,CAACkJ,OAAO,EAAE;MACvB,IAAI,CAAC9G,eAAe,CAAC,KAAK,EAAER,IAAI,CAAC;IACnC,CAAC,MAAM,IAAIA,IAAI,CAAC1D,mBAAmB,CAAC,CAAC,EAAE;MACrC,MAAMiL,iBAAiB,GACrBvH,IAAI,CAAC5B,IAAI,CAACoJ,UAAU,KAAK,MAAM,IAAIxH,IAAI,CAAC5B,IAAI,CAACoJ,UAAU,KAAK,QAAQ;MACtE,MAAM/I,UAAU,GAAGuB,IAAI,CAACE,GAAG,CAAC,YAAY,CAAC;MACzC,KAAK,MAAMuH,SAAS,IAAIhJ,UAAU,EAAE;QAClC,MAAMiJ,eAAe,GACnBH,iBAAiB,IAChBE,SAAS,CAACE,iBAAiB,CAAC,CAAC,KAC3BF,SAAS,CAACrJ,IAAI,CAACoJ,UAAU,KAAK,MAAM,IACnCC,SAAS,CAACrJ,IAAI,CAACoJ,UAAU,KAAK,QAAQ,CAAE;QAE9C,IAAI,CAAChH,eAAe,CAACkH,eAAe,GAAG,SAAS,GAAG,QAAQ,EAAED,SAAS,CAAC;MACzE;IACF,CAAC,MAAM,IAAIzH,IAAI,CAAC9B,mBAAmB,CAAC,CAAC,EAAE;MAErC,MAAM+B,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAC,aAAa,CAAa;MAClD,IACED,MAAM,CAACjE,kBAAkB,CAAC,CAAC,IAC3BiE,MAAM,CAAC7D,qBAAqB,CAAC,CAAC,IAC9B6D,MAAM,CAAC7C,qBAAqB,CAAC,CAAC,EAC9B;QACA,IAAI,CAACwD,mBAAmB,CAACX,MAAM,CAAC;MAClC;IACF,CAAC,MAAM;MACL,IAAI,CAACO,eAAe,CAAC,SAAS,EAAER,IAAI,CAAC;IACvC;EACF;EAEA4H,kBAAkBA,CAAA,EAAG;IACnB,OAAOnK,eAAe,CAAC,MAAM,EAAEF,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;EACzD;EAEAsK,yBAAyBA,CAAC7H,IAAc,EAAE;IACxC,MAAM8H,GAAG,GAAG9H,IAAI,CAACtE,qBAAqB,CAAC,CAAC;IACxC,KAAK,MAAMwD,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACiG,GAAG,CAAC,EAAE;MAAA,IAAAC,gBAAA;MACnC,CAAAA,gBAAA,OAAI,CAACvG,UAAU,CAACtC,IAAI,CAAC,qBAArB6I,gBAAA,CAAuBC,QAAQ,CAAChI,IAAI,CAAC;IACvC;EACF;EAEAQ,eAAeA,CACbiF,IAAqB,EACrBzF,IAAc,EACdiI,WAAqB,GAAGjI,IAAI,EAC5B;IACA,IAAI,CAACyF,IAAI,EAAE,MAAM,IAAIyC,cAAc,CAAC,WAAW,CAAC;IAEhD,IAAIlI,IAAI,CAAC5C,qBAAqB,CAAC,CAAC,EAAE;MAChC,MAAM+K,WAA4B,GAAGnI,IAAI,CAACE,GAAG,CAAC,cAAc,CAAC;MAC7D,KAAK,MAAMD,MAAM,IAAIkI,WAAW,EAAE;QAChC,IAAI,CAAC3H,eAAe,CAACiF,IAAI,EAAExF,MAAM,CAAC;MACpC;MACA;IACF;IAEA,MAAMU,MAAM,GAAG,IAAI,CAACJ,gBAAgB,CAAC,CAAC;IACtC,MAAMuH,GAAG,GAAG9H,IAAI,CAACoI,0BAA0B,CAAC,IAAI,CAAC;IAEjD,KAAK,MAAMlJ,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACiG,GAAG,CAAC,EAAE;MACnCnH,MAAM,CAACM,UAAU,CAAC/B,IAAI,CAAC,GAAG,IAAI;MAE9B,KAAK,MAAMM,EAAE,IAAIsI,GAAG,CAAC5I,IAAI,CAAC,EAAE;QAC1B,MAAML,KAAK,GAAG,IAAI,CAACwJ,aAAa,CAACnJ,IAAI,CAAC;QAEtC,IAAIL,KAAK,EAAE;UAGT,IAAIA,KAAK,CAAClD,UAAU,KAAK6D,EAAE,EAAE;UAE7B,IAAI,CAACgG,0BAA0B,CAAC3G,KAAK,EAAE4G,IAAI,EAAEvG,IAAI,EAAEM,EAAE,CAAC;QACxD;QAGA,IAAIX,KAAK,EAAE;UACT,IAAI,CAACgJ,yBAAyB,CAACI,WAAW,CAAC;QAC7C,CAAC,MAAM;UACL,IAAI,CAAC5F,QAAQ,CAACnD,IAAI,CAAC,GAAG,IAAIoJ,gBAAO,CAAC;YAChC3M,UAAU,EAAE6D,EAAE;YACdY,KAAK,EAAE,IAAI;YACXJ,IAAI,EAAEiI,WAAW;YACjBxC,IAAI,EAAEA;UACR,CAAC,CAAC;QACJ;MACF;IACF;EACF;EAEA8C,SAASA,CAACnK,IAAoC,EAAE;IAC9C,IAAI,CAAC+E,OAAO,CAAC/E,IAAI,CAACc,IAAI,CAAC,GAAGd,IAAI;EAChC;EAEAoK,MAAMA,CAACtJ,IAAY,EAAW;IAC5B,IAAIkB,KAAY,GAAG,IAAI;IAEvB,GAAG;MACD,IAAIA,KAAK,CAACgD,IAAI,CAAClE,IAAI,CAAC,EAAE,OAAO,IAAI;IACnC,CAAC,QAASkB,KAAK,GAAGA,KAAK,CAACO,MAAM;IAE9B,OAAO,KAAK;EACd;EAEAiE,SAASA,CAAC1F,IAAY,EAAW;IAC/B,IAAIkB,KAAY,GAAG,IAAI;IAEvB,GAAG;MACD,IAAIA,KAAK,CAAC+C,OAAO,CAACjE,IAAI,CAAC,EAAE,OAAO,IAAI;IACtC,CAAC,QAASkB,KAAK,GAAGA,KAAK,CAACO,MAAM;IAE9B,OAAO,KAAK;EACd;EAEAkE,YAAYA,CAAC3F,IAAY,EAAW;IAClC,OAAO,CAAC,CAAC,IAAI,CAACqB,gBAAgB,CAAC,CAAC,CAACU,UAAU,CAAC/B,IAAI,CAAC;EACnD;EAEAuJ,MAAMA,CAACrK,IAAY,EAAEsK,aAAuB,EAAW;IACrD,IAAIrM,YAAY,CAAC+B,IAAI,CAAC,EAAE;MACtB,MAAMmD,OAAO,GAAG,IAAI,CAACC,UAAU,CAACpD,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAI,CAACqC,OAAO,EAAE,OAAO,KAAK;MAC1B,IAAImH,aAAa,EAAE,OAAOnH,OAAO,CAAC8D,QAAQ;MAC1C,OAAO,IAAI;IACb,CAAC,MAAM,IACLnI,gBAAgB,CAACkB,IAAI,CAAC,IACtBJ,cAAc,CAACI,IAAI,CAAC,IACpBL,gBAAgB,CAACK,IAAI,CAAC,IACtBH,aAAa,CAACG,IAAI,CAAC,EACnB;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAItC,OAAO,CAACsC,IAAI,CAAC,EAAE;MAAA,IAAAuK,gBAAA;MACxB,IAAIvK,IAAI,CAACwK,UAAU,IAAI,CAAC,IAAI,CAACH,MAAM,CAACrK,IAAI,CAACwK,UAAU,EAAEF,aAAa,CAAC,EAAE;QACnE,OAAO,KAAK;MACd;MACA,IAAI,EAAAC,gBAAA,GAAAvK,IAAI,CAACyK,UAAU,qBAAfF,gBAAA,CAAiBjK,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI,CAAC+J,MAAM,CAACrK,IAAI,CAAC0K,IAAI,EAAEJ,aAAa,CAAC;IAC9C,CAAC,MAAM,IAAI3M,WAAW,CAACqC,IAAI,CAAC,EAAE;MAC5B,KAAK,MAAM2K,MAAM,IAAI3K,IAAI,CAAC0K,IAAI,EAAE;QAC9B,IAAI,CAAC,IAAI,CAACL,MAAM,CAACM,MAAM,EAAEL,aAAa,CAAC,EAAE,OAAO,KAAK;MACvD;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAI7M,QAAQ,CAACuC,IAAI,CAAC,EAAE;MACzB,OACE,IAAI,CAACqK,MAAM,CAACrK,IAAI,CAACmB,IAAI,EAAEmJ,aAAa,CAAC,IACrC,IAAI,CAACD,MAAM,CAACrK,IAAI,CAAC4K,KAAK,EAAEN,aAAa,CAAC;IAE1C,CAAC,MAAM,IAAI9M,iBAAiB,CAACwC,IAAI,CAAC,IAAIP,iBAAiB,CAACO,IAAI,CAAC,EAAE;MAC7D,KAAK,MAAM6K,IAAI,IAAI7K,IAAI,CAAC8K,QAAQ,EAAE;QAChC,IAAID,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,CAACR,MAAM,CAACQ,IAAI,EAAEP,aAAa,CAAC,EAAE,OAAO,KAAK;MACtE;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAI/L,kBAAkB,CAACyB,IAAI,CAAC,IAAIR,kBAAkB,CAACQ,IAAI,CAAC,EAAE;MAC/D,KAAK,MAAM+K,IAAI,IAAI/K,IAAI,CAACgB,UAAU,EAAE;QAClC,IAAI,CAAC,IAAI,CAACqJ,MAAM,CAACU,IAAI,EAAET,aAAa,CAAC,EAAE,OAAO,KAAK;MACrD;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIlM,QAAQ,CAAC4B,IAAI,CAAC,EAAE;MAAA,IAAAgL,iBAAA;MACzB,IAAIhL,IAAI,CAACiL,QAAQ,IAAI,CAAC,IAAI,CAACZ,MAAM,CAACrK,IAAI,CAACkB,GAAG,EAAEoJ,aAAa,CAAC,EAAE,OAAO,KAAK;MACxE,IAAI,EAAAU,iBAAA,GAAAhL,IAAI,CAACyK,UAAU,qBAAfO,iBAAA,CAAiB1K,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAI9B,UAAU,CAACwB,IAAI,CAAC,EAAE;MAAA,IAAAkL,iBAAA;MAE3B,IAAIlL,IAAI,CAACiL,QAAQ,IAAI,CAAC,IAAI,CAACZ,MAAM,CAACrK,IAAI,CAACkB,GAAG,EAAEoJ,aAAa,CAAC,EAAE,OAAO,KAAK;MACxE,IAAI,EAAAY,iBAAA,GAAAlL,IAAI,CAACyK,UAAU,qBAAfS,iBAAA,CAAiB5K,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,IAAIZ,gBAAgB,CAACM,IAAI,CAAC,IAAIA,IAAI,CAACmL,MAAM,EAAE;QACzC,IAAInL,IAAI,CAACW,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC0J,MAAM,CAACrK,IAAI,CAACW,KAAK,EAAE2J,aAAa,CAAC,EAAE;UAClE,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIvL,iBAAiB,CAACiB,IAAI,CAAC,EAAE;MAClC,OAAO,IAAI,CAACqK,MAAM,CAACrK,IAAI,CAACiB,QAAQ,EAAEqJ,aAAa,CAAC;IAClD,CAAC,MAAM,IAAI1L,0BAA0B,CAACoB,IAAI,CAAC,EAAE;MAC3C,OACEf,cAAc,CAACe,IAAI,CAACoL,GAAG,EAAE,YAAY,CAAC,IACtC,CAAC,IAAI,CAAC7E,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,IAChC,IAAI,CAAC8D,MAAM,CAACrK,IAAI,CAACqL,KAAK,EAAEf,aAAa,CAAC;IAE1C,CAAC,MAAM,IAAIzL,iBAAiB,CAACmB,IAAI,CAAC,EAAE;MAClC,KAAK,MAAMqB,UAAU,IAAIrB,IAAI,CAACsL,WAAW,EAAE;QACzC,IAAI,CAAC,IAAI,CAACjB,MAAM,CAAChJ,UAAU,EAAEiJ,aAAa,CAAC,EAAE,OAAO,KAAK;MAC3D;MACA,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO7L,SAAS,CAACuB,IAAI,CAAC;IACxB;EACF;EAMAuL,OAAOA,CAACrK,GAAoB,EAAEsK,GAAQ,EAAE;IACtC,OAAQ,IAAI,CAACvG,IAAI,CAAC/D,GAAG,CAAC,GAAGsK,GAAG;EAC9B;EAMAC,OAAOA,CAACvK,GAAoB,EAAO;IACjC,IAAIc,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,MAAMiD,IAAI,GAAGjD,KAAK,CAACiD,IAAI,CAAC/D,GAAG,CAAC;MAC5B,IAAI+D,IAAI,IAAI,IAAI,EAAE,OAAOA,IAAI;IAC/B,CAAC,QAASjD,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;EAOAmJ,UAAUA,CAACxK,GAAW,EAAE;IACtB,IAAIc,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,MAAMiD,IAAI,GAAGjD,KAAK,CAACiD,IAAI,CAAC/D,GAAG,CAAC;MAC5B,IAAI+D,IAAI,IAAI,IAAI,EAAEjD,KAAK,CAACiD,IAAI,CAAC/D,GAAG,CAAC,GAAG,IAAI;IAC1C,CAAC,QAASc,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;EAEAoJ,IAAIA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAAC7G,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG,IAAI;MAClB,IAAI,CAAC8G,KAAK,CAAC,CAAC;IACd;EACF;EAEAA,KAAKA,CAAA,EAAG;IACN,MAAMhK,IAAI,GAAG,IAAI,CAACA,IAAI;IAEtB,IAAI,CAACiB,UAAU,GAAGW,MAAM,CAACqI,MAAM,CAAC,IAAI,CAAC;IACrC,IAAI,CAAC5H,QAAQ,GAAGT,MAAM,CAACqI,MAAM,CAAC,IAAI,CAAC;IACnC,IAAI,CAAC9G,OAAO,GAAGvB,MAAM,CAACqI,MAAM,CAAC,IAAI,CAAC;IAClC,IAAI,CAAC7G,IAAI,GAAGxB,MAAM,CAACqI,MAAM,CAAC,IAAI,CAAC;IAC/B,IAAI,CAAC5G,IAAI,GAAGzB,MAAM,CAACqI,MAAM,CAAC,IAAI,CAAC;IAE/B,MAAMC,aAAa,GAAG,IAAI,CAAC3J,gBAAgB,CAAC,CAAC;IAC7C,IAAI2J,aAAa,CAAC5G,QAAQ,EAAE;IAE5B,MAAMtC,KAA0B,GAAG;MACjCC,UAAU,EAAE,EAAE;MACdG,kBAAkB,EAAE,EAAE;MACtBY,WAAW,EAAE;IACf,CAAC;IAED,IAAI,CAACsB,QAAQ,GAAG,IAAI;IAGpB,IAAItD,IAAI,CAAC1B,IAAI,KAAK,SAAS,IAAI,IAAA6L,2BAAiB,EAACrK,gBAAgB,CAAC,EAAE;MAClE,KAAK,MAAMsK,KAAK,IAAItK,gBAAgB,CAACuK,KAAK,EAAE;QAC1CD,KAAK,CAACE,IAAI,CAACtJ,KAAK,EAAEhB,IAAI,EAAEgB,KAAK,CAAC;MAChC;MACA,MAAMuJ,YAAY,GAAGzK,gBAAgB,CAACE,IAAI,CAAC1B,IAAI,CAAC;MAChD,IAAIiM,YAAY,EAAE;QAChB,KAAK,MAAMH,KAAK,IAAIG,YAAY,CAACF,KAAK,EAAE;UACtCD,KAAK,CAACE,IAAI,CAACtJ,KAAK,EAAEhB,IAAI,EAAEgB,KAAK,CAAC;QAChC;MACF;IACF;IACAhB,IAAI,CAACkE,QAAQ,CAACpE,gBAAgB,EAAEkB,KAAK,CAAC;IACtC,IAAI,CAACsC,QAAQ,GAAG,KAAK;IAGrB,KAAK,MAAMtD,IAAI,IAAIgB,KAAK,CAACgB,WAAW,EAAE;MAEpC,MAAM8F,GAAG,GAAG9H,IAAI,CAACtE,qBAAqB,CAAC,CAAC;MACxC,KAAK,MAAMwD,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACiG,GAAG,CAAC,EAAE;QACnC,IAAI9H,IAAI,CAACI,KAAK,CAACoB,UAAU,CAACtC,IAAI,CAAC,EAAE;QACjCgL,aAAa,CAAC3B,SAAS,CAACT,GAAG,CAAC5I,IAAI,CAAC,CAAC;MACpC;MAGAc,IAAI,CAACI,KAAK,CAACyH,yBAAyB,CAAC7H,IAAI,CAAC;IAC5C;IAGA,KAAK,MAAMwK,GAAG,IAAIxJ,KAAK,CAACC,UAAU,EAAE;MAClC,MAAMM,OAAO,GAAGiJ,GAAG,CAACpK,KAAK,CAACoB,UAAU,CAACgJ,GAAG,CAACpM,IAAI,CAACc,IAAI,CAAC;MACnD,IAAIqC,OAAO,EAAE;QACXA,OAAO,CAACE,SAAS,CAAC+I,GAAG,CAAC;MACxB,CAAC,MAAM;QACLN,aAAa,CAAC3B,SAAS,CAACiC,GAAG,CAACpM,IAAI,CAAC;MACnC;IACF;IAGA,KAAK,MAAM4B,IAAI,IAAIgB,KAAK,CAACI,kBAAkB,EAAE;MAC3CpB,IAAI,CAACI,KAAK,CAACyH,yBAAyB,CAAC7H,IAAI,CAAC;IAC5C;EACF;EAEAlB,IAAIA,CAACqF,IAMJ,EAAE;IACD,IAAInE,IAAI,GAAG,IAAI,CAACA,IAAI;IAEpB,IAAIA,IAAI,CAACmB,SAAS,CAAC,CAAC,EAAE;MACpBnB,IAAI,GAAG,IAAI,CAACyK,gBAAgB,CAAC,CAAC,CAACzK,IAAI;IACrC,CAAC,MAAM,IAAI,CAACA,IAAI,CAAC0K,gBAAgB,CAAC,CAAC,IAAI,CAAC1K,IAAI,CAAC2K,SAAS,CAAC,CAAC,EAAE;MACxD3K,IAAI,GAAG,IAAI,CAACc,cAAc,CAAC,CAAC,CAACd,IAAI;IACnC;IAEA,IAAIA,IAAI,CAAC4K,iBAAiB,CAAC,CAAC,EAAE;MAC5B5K,IAAI,GAAG,CAAC,IAAI,CAACM,iBAAiB,CAAC,CAAC,IAAI,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAAEP,IAAI;IACnE;IAEA,MAAM;MAAE+J,IAAI;MAAEc,MAAM;MAAEpF,IAAI,GAAG,KAAK;MAAEjG;IAAG,CAAC,GAAG2E,IAAI;IAM/C,IACE,CAAC4F,IAAI,IACL,CAACc,MAAM,KACNpF,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,CAAC,IAClCzF,IAAI,CAAC8K,UAAU,CAAC,CAAC,IAEjB,CAAC9K,IAAI,CAAC5B,IAAI,CAACc,IAAI,IACf9D,CAAC,CAAC2P,gBAAgB,CAAC/K,IAAI,CAACW,MAAM,EAAE;MAAExB,MAAM,EAAEa,IAAI,CAAC5B;IAAK,CAAC,CAAC,IACtD4B,IAAI,CAACW,MAAM,CAACuF,SAAS,CAACxH,MAAM,IAAIsB,IAAI,CAAC5B,IAAI,CAACoE,MAAM,CAAC9D,MAAM,IACvDtD,CAAC,CAACiB,YAAY,CAACmD,EAAE,CAAC,EAClB;MACAQ,IAAI,CAACgL,aAAa,CAAC,QAAQ,EAAExL,EAAE,CAAC;MAChCQ,IAAI,CAACI,KAAK,CAACI,eAAe,CACxB,OAAO,EACPR,IAAI,CAACE,GAAG,CAAC,QAAQ,CAAC,CAACF,IAAI,CAAC5B,IAAI,CAACoE,MAAM,CAAC9D,MAAM,GAAG,CAAC,CAChD,CAAC;MACD;IACF;IAEA,IAAIsB,IAAI,CAACiL,MAAM,CAAC,CAAC,IAAIjL,IAAI,CAACkL,aAAa,CAAC,CAAC,IAAIlL,IAAI,CAAC8K,UAAU,CAAC,CAAC,EAAE;MAE9D9K,IAAI,CAACmL,WAAW,CAAC,CAAC;MAElBnL,IAAI,GAAGA,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IACzB;IAEA,MAAMkL,UAAU,GAAGjH,IAAI,CAACkH,WAAW,IAAI,IAAI,GAAG,CAAC,GAAGlH,IAAI,CAACkH,WAAW;IAElE,MAAMC,OAAO,GAAI,eAAc7F,IAAK,IAAG2F,UAAW,EAAC;IACnD,IAAIG,UAAU,GAAG,CAACV,MAAM,IAAI7K,IAAI,CAAC6J,OAAO,CAACyB,OAAO,CAAC;IAEjD,IAAI,CAACC,UAAU,EAAE;MACf,MAAMtL,MAAM,GAAGvC,mBAAmB,CAAC+H,IAAI,EAAE,EAAE,CAAC;MAE5CxF,MAAM,CAACoL,WAAW,GAAGD,UAAU;MAE/B,CAACG,UAAU,CAAC,GAAIvL,IAAI,CAAgCwL,gBAAgB,CAClE,MAAM,EACN,CAACvL,MAAM,CACT,CAAC;MACD,IAAI,CAAC4K,MAAM,EAAE7K,IAAI,CAAC2J,OAAO,CAAC2B,OAAO,EAAEC,UAAU,CAAC;IAChD;IAEA,MAAME,UAAU,GAAG9N,kBAAkB,CAAC6B,EAAE,EAAEuK,IAAI,CAAC;IAC/C,MAAM2B,GAAG,GAAGH,UAAU,CAACnN,IAAI,CAACuD,YAAY,CAAC7C,IAAI,CAAC2M,UAAU,CAAC;IACzDzL,IAAI,CAACI,KAAK,CAACI,eAAe,CAACiF,IAAI,EAAE8F,UAAU,CAACrL,GAAG,CAAC,cAAc,CAAC,CAACwL,GAAG,GAAG,CAAC,CAAC,CAAC;EAC3E;EAMAnL,gBAAgBA,CAAA,EAAG;IACjB,IAAIH,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACJ,IAAI,CAAC2K,SAAS,CAAC,CAAC,EAAE;QAC1B,OAAOvK,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9B,MAAM,IAAIgL,KAAK,CAAC,yBAAyB,CAAC;EAC5C;EAMArL,iBAAiBA,CAAA,EAAiB;IAChC,IAAIF,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACJ,IAAI,CAAC4L,gBAAgB,CAAC,CAAC,EAAE;QACjC,OAAOxL,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9B,OAAO,IAAI;EACb;EAOAG,cAAcA,CAAA,EAAG;IACf,IAAIV,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACJ,IAAI,CAAC6L,aAAa,CAAC,CAAC,EAAE;QAC9B,OAAOzL,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACO,MAAM;IAC9B,MAAM,IAAIgL,KAAK,CACb,8EACF,CAAC;EACH;EAOAlB,gBAAgBA,CAAA,EAAG;IACjB,IAAIrK,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAI,CAACA,KAAK,CAACJ,IAAI,CAACmB,SAAS,CAAC,CAAC,EAAE;QAC3B,OAAOf,KAAK,CAACU,cAAc,CAAC,CAAC;MAC/B;IACF,CAAC,QAASV,KAAK,GAAGA,KAAK,CAACO,MAAM,CAACA,MAAM;IACrC,MAAM,IAAIgL,KAAK,CACb,8EACF,CAAC;EACH;EAMAG,cAAcA,CAAA,EAA4B;IACxC,MAAMhE,GAAG,GAAGlG,MAAM,CAACqI,MAAM,CAAC,IAAI,CAAC;IAE/B,IAAI7J,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,KAAK,MAAMd,GAAG,IAAIsC,MAAM,CAACC,IAAI,CAACzB,KAAK,CAACiC,QAAQ,CAAC,EAAE;QAC7C,IAAI/C,GAAG,IAAIwI,GAAG,KAAK,KAAK,EAAE;UACxBA,GAAG,CAACxI,GAAG,CAAC,GAAGc,KAAK,CAACiC,QAAQ,CAAC/C,GAAG,CAAC;QAChC;MACF;MACAc,KAAK,GAAGA,KAAK,CAACO,MAAM;IACtB,CAAC,QAAQP,KAAK;IAEd,OAAO0H,GAAG;EACZ;EAMAiE,oBAAoBA,CAAC,GAAGC,KAAe,EAA2B;IAChE,MAAMlE,GAAG,GAAGlG,MAAM,CAACqI,MAAM,CAAC,IAAI,CAAC;IAE/B,KAAK,MAAMxE,IAAI,IAAIuG,KAAK,EAAE;MACxB,IAAI5L,KAAY,GAAG,IAAI;MACvB,GAAG;QACD,KAAK,MAAMlB,IAAI,IAAI0C,MAAM,CAACC,IAAI,CAACzB,KAAK,CAACiC,QAAQ,CAAC,EAAE;UAC9C,MAAMd,OAAO,GAAGnB,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC;UACpC,IAAIqC,OAAO,CAACkE,IAAI,KAAKA,IAAI,EAAEqC,GAAG,CAAC5I,IAAI,CAAC,GAAGqC,OAAO;QAChD;QACAnB,KAAK,GAAGA,KAAK,CAACO,MAAM;MACtB,CAAC,QAAQP,KAAK;IAChB;IAEA,OAAO0H,GAAG;EACZ;EAEAmE,uBAAuBA,CAAC/M,IAAY,EAAEd,IAAY,EAAW;IAC3D,OAAO,IAAI,CAAC8N,oBAAoB,CAAChN,IAAI,CAAC,KAAKd,IAAI;EACjD;EAEAoD,UAAUA,CAACtC,IAAY,EAAuB;IAC5C,IAAIkB,KAAY,GAAG,IAAI;IACvB,IAAI+L,YAAY;IAEhB,GAAG;MACD,MAAM5K,OAAO,GAAGnB,KAAK,CAACiI,aAAa,CAACnJ,IAAI,CAAC;MACzC,IAAIqC,OAAO,EAAE;QAAA,IAAA6K,aAAA;QAUX,IACE,CAAAA,aAAA,GAAAD,YAAY,aAAZC,aAAA,CAAcjL,SAAS,CAAC,CAAC,IACzBI,OAAO,CAACkE,IAAI,KAAK,OAAO,IACxBlE,OAAO,CAACkE,IAAI,KAAK,OAAO,EACxB,CAEF,CAAC,MAAM;UACL,OAAOlE,OAAO;QAChB;MACF,CAAC,MAAM,IACL,CAACA,OAAO,IACRrC,IAAI,KAAK,WAAW,IACpBkB,KAAK,CAACJ,IAAI,CAAC8K,UAAU,CAAC,CAAC,IACvB,CAAC1K,KAAK,CAACJ,IAAI,CAACqM,yBAAyB,CAAC,CAAC,EACvC;QACA;MACF;MACAF,YAAY,GAAG/L,KAAK,CAACJ,IAAI;IAC3B,CAAC,QAASI,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;EAEA0H,aAAaA,CAACnJ,IAAY,EAAuB;IAC/C,OAAO,IAAI,CAACmD,QAAQ,CAACnD,IAAI,CAAC;EAC5B;EAGAgN,oBAAoBA,CAAChN,IAAY,EAAgB;IAAA,IAAAoN,iBAAA;IAC/C,QAAAA,iBAAA,GAAO,IAAI,CAAC9K,UAAU,CAACtC,IAAI,CAAC,qBAArBoN,iBAAA,CAAuB3Q,UAAU;EAC1C;EAGA4Q,uBAAuBA,CAACrN,IAAY,EAAgB;IAClD,MAAMqC,OAAO,GAAG,IAAI,CAACc,QAAQ,CAACnD,IAAI,CAAC;IACnC,OAAOqC,OAAO,oBAAPA,OAAO,CAAE5F,UAAU;EAC5B;EAEA6Q,aAAaA,CAACtN,IAAY,EAAE;IAC1B,OAAO,CAAC,CAAC,IAAI,CAACmJ,aAAa,CAACnJ,IAAI,CAAC;EACnC;EAQAyF,UAAUA,CACRzF,IAAY,EACZiF,IAA0D,EAC1D;IAAA,IAAAsI,KAAA,EAAAC,MAAA,EAAAC,MAAA;IACA,IAAI,CAACzN,IAAI,EAAE,OAAO,KAAK;IACvB,IAAI,IAAI,CAACsN,aAAa,CAACtN,IAAI,CAAC,EAAE,OAAO,IAAI;IACzC;MAEE,IAAI,OAAOiF,IAAI,KAAK,SAAS,EAAEA,IAAI,GAAG;QAAEyI,SAAS,EAAEzI;MAAK,CAAC;IAC3D;IACA,IAAI,IAAI,CAAC0I,gBAAgB,CAAC3N,IAAI,EAAEiF,IAAI,CAAC,EAAE,OAAO,IAAI;IAClD,IAAI,GAAAsI,KAAA,GAACtI,IAAI,aAAJsI,KAAA,CAAMK,MAAM,KAAI,IAAI,CAACtE,MAAM,CAACtJ,IAAI,CAAC,EAAE,OAAO,IAAI;IACnD,IAAI,GAAAwN,MAAA,GAACvI,IAAI,aAAJuI,MAAA,CAAME,SAAS,KAAI9J,KAAK,CAACK,OAAO,CAAC4J,QAAQ,CAAC7N,IAAI,CAAC,EAAE,OAAO,IAAI;IACjE,IAAI,GAAAyN,MAAA,GAACxI,IAAI,aAAJwI,MAAA,CAAMC,SAAS,KAAI9J,KAAK,CAACkK,gBAAgB,CAACD,QAAQ,CAAC7N,IAAI,CAAC,EAAE,OAAO,IAAI;IAC1E,OAAO,KAAK;EACd;EAEA2N,gBAAgBA,CACd3N,IAAY,EACZiF,IAAgD,EAChD;IAAA,IAAA8I,YAAA;IACA,QAAAA,YAAA,GAAO,IAAI,CAACtM,MAAM,qBAAXsM,YAAA,CAAatI,UAAU,CAACzF,IAAI,EAAEiF,IAAI,CAAC;EAC5C;EAMA+I,aAAaA,CAAChO,IAAY,EAAEkB,KAAY,EAAE;IACxC,MAAM+M,IAAI,GAAG,IAAI,CAAC3L,UAAU,CAACtC,IAAI,CAAC;IAClC,IAAIiO,IAAI,EAAE;MACRA,IAAI,CAAC/M,KAAK,CAACgN,gBAAgB,CAAClO,IAAI,CAAC;MACjCiO,IAAI,CAAC/M,KAAK,GAAGA,KAAK;MAClBA,KAAK,CAACiC,QAAQ,CAACnD,IAAI,CAAC,GAAGiO,IAAI;IAC7B;EACF;EAEAC,gBAAgBA,CAAClO,IAAY,EAAE;IAC7B,OAAO,IAAI,CAACmD,QAAQ,CAACnD,IAAI,CAAC;EAC5B;EAEAmO,aAAaA,CAACnO,IAAY,EAAE;IAAA,IAAAoO,iBAAA;IAE1B,CAAAA,iBAAA,OAAI,CAAC9L,UAAU,CAACtC,IAAI,CAAC,qBAArBoO,iBAAA,CAAuBlN,KAAK,CAACgN,gBAAgB,CAAClO,IAAI,CAAC;IAGnD,IAAIkB,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACgD,IAAI,CAAClE,IAAI,CAAC,EAAE;QACpBkB,KAAK,CAACgD,IAAI,CAAClE,IAAI,CAAC,GAAG,KAAK;MAC1B;IACF,CAAC,QAASkB,KAAK,GAAGA,KAAK,CAACO,MAAM;EAChC;AACF;AAAC4M,OAAA,CAAAC,OAAA,GAAA1K,KAAA;AAx8BoBA,KAAK,CA2CjBK,OAAO,GAAGvB,MAAM,CAACC,IAAI,CAACsB,QAAO,CAACsK,OAAO,CAAC;AA3C1B3K,KAAK,CAiDjBkK,gBAAgB,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,CAAC"}