import React from 'react';
import { Dimensions, View } from 'react-native';
import { appSettings } from '@appmaker-xyz/core';
import { AppmakerRemoteImage } from '@appmaker-xyz/react-native';

const SplashScreen = () => {
  const { height, width } = Dimensions.get('window');

  return (
    <View style={{ backgroundColor: appSettings.getOption('splash_color') }}>
      <AppmakerRemoteImage
        name="SPLASH_IMAGE"
        resizeMode="contain"
        style={{ height, width }}
      />
    </View>
  );
};

export default SplashScreen;
