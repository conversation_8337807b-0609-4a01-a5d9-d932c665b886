import React from 'react';
import { StyleSheet, FlatList } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { AppImage, AppTouchable, Layout, ThemeText } from '../atoms';
import { Badge } from '../coreComponents';

const Card = ({ attributes, children, onPress, onAction }) => {
  const {
    imgSrc,
    featureImg,
    titleMeta,
    title,
    excerpt,
    desc,
    meta,
    type,
    reverse,
    appmakerAction,
    htmlExcerpt,
    stripTagsExcerpt,
    unescape = true,
    postBlock,
    restrictLines = true,
    extraText,
    strikeText,
    message,
    extraTextTwo,
  } = attributes;
  const mainContainer = [styles.container];
  const cardContainerStyle = [styles.cardContainer];
  const imageContainerStyle = [styles.imgContainer];
  const contentContainerStyle = [styles.contentContainer];
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  if (type === 'type-1') {
    cardContainerStyle.push({ flexDirection: 'row', padding: 6 });
    imageContainerStyle.push({ width: 150, height: 150 });
    contentContainerStyle.push({ paddingVertical: 0 });
  }
  if (type === 'type-2') {
    cardContainerStyle.push({ flexDirection: 'row', padding: 6 });
    imageContainerStyle.push({ width: 105, height: 105 });
    contentContainerStyle.push({ paddingVertical: 0 });
  }
  if (type === 'review') {
    cardContainerStyle.push({ flexDirection: 'row', padding: 6 });
    imageContainerStyle.push({ width: 48, height: 48 });
    contentContainerStyle.push({ paddingVertical: 0 });
    mainContainer.push({ borderWidth: 0, borderBottomWidth: 1 });
  }
  if (postBlock && reverse) {
    cardContainerStyle.push({ flexDirection: 'row-reverse' });
    contentContainerStyle.push({
      paddingHorizontal: 4,
      marginRight: 4,
    });
  }

  const numLines = restrictLines ? 1 : null;

  const CardExcerpt = () => {
    if (type === 'review') {
      const ratingBadgeColor = () => {
        let rating = excerpt;
        switch (true) {
          case rating >= '3.5':
            return '#2FA478';
          case rating >= '2.5':
            return '#FFC829';
          default:
            return '#1B1B1B';
        }
      };
      return (
        <Layout flexDirection="row">
          <Badge text={excerpt} iconName="star" color={ratingBadgeColor()} />
        </Layout>
      );
    } else {
      return (
        <Layout style={styles.descriptionContainer}>
          <ThemeText
            unescape={unescape}
            html={htmlExcerpt}
            striptags={stripTagsExcerpt}
            size="sm"
            fontFamily="medium"
            numberOfLines={type === 'type-1' ? 3 : 2}>
            {`${excerpt}`}
          </ThemeText>
          {strikeText ? (
            <ThemeText
              unescape={unescape}
              html={htmlExcerpt}
              striptags={stripTagsExcerpt}
              style={styles.strike}
              size="sm"
              numberOfLines={type === 'type-1' ? 3 : 2}>
              {`${strikeText}`}
            </ThemeText>
          ) : null}
          {desc ? (
            <ThemeText
              unescape={unescape}
              html={htmlExcerpt}
              striptags={stripTagsExcerpt}
              size="sm"
              color="#4F4F4F"
              numberOfLines={type === 'type-1' ? 3 : 2}>
              {`${desc}`}
            </ThemeText>
          ) : null}
        </Layout>
      );
    }
  };

  const CardImage = () => {
    if (featureImg) {
      return (
        <Layout style={imageContainerStyle}>
          <AppImage uri={featureImg} style={styles.imgStyle} />
        </Layout>
      );
    }
    if (imgSrc) {
      return (
        <Layout style={imageContainerStyle}>
          <AppImage src={imgSrc} style={styles.imgStyle} />
        </Layout>
      );
    }
    if (postBlock && !featureImg) {
      return (
        <Layout style={imageContainerStyle}>
          <Layout style={[styles.imgStyle, styles.letterCover]}>
            <ThemeText color="#A9AEB7" fontFamily="bold" size="2xl">
              {title.charAt(0).toUpperCase()}
            </ThemeText>
          </Layout>
        </Layout>
      );
    }
    return null;
  };

  return (
    <AppTouchable onPress={onPressHandle}>
      <Layout style={mainContainer}>
        <Layout style={cardContainerStyle}>
          <CardImage />
          <Layout style={contentContainerStyle}>
            {titleMeta ? (
              <ThemeText fontFamily="medium" size="sm">
                {(titleMeta.toUpperCase && titleMeta.toUpperCase()) ||
                  titleMeta}
              </ThemeText>
            ) : null}
            {title ? (
              <ThemeText
                size="lg"
                fontFamily="bold"
                color={type === 'review' ? '#A9AEB7' : '#1B1B1B'}
                numberOfLines={type === 'type-2' ? numLines : 2}>
                {title}
              </ThemeText>
            ) : null}
            {excerpt ? <CardExcerpt /> : null}
            {meta && (
              <Layout>
                {/* map metaItem with */}
                <FlatList
                  columnWrapperStyle={styles.metaContainer}
                  numColumns={type === 'type-1' ? 2 : 3}
                  data={meta}
                  keyExtractor={(item) => item.id}
                  renderItem={({ item }) => (
                    <Layout style={styles.metaItem}>
                      <ThemeText size="sm" color="#A9AEB7">
                        <Icon name={item.iconName} /> {item.title}
                      </ThemeText>
                    </Layout>
                  )}
                />
              </Layout>
            )}
            {extraText ? (
              <ThemeText color="#4F4F4F" size="sm" fontFamily="medium">
                {extraText}
              </ThemeText>
            ) : null}
            {extraTextTwo ? (
              <ThemeText color="#4F4F4F" size="sm" fontFamily="medium">
                {extraTextTwo}
              </ThemeText>
            ) : null}
          </Layout>
        </Layout>
        {message?.title ? (
          <Layout style={styles.messageContainerStyle}>
            <Icon
              name="info"
              size={18}
              color="#1B1B1B"
              style={styles.infoIcon}
            />
            <ThemeText>{attributes.message.title}</ThemeText>
          </Layout>
        ) : null}
        {children && (
          <Layout style={styles.childrenContainer}>{children}</Layout>
        )}
      </Layout>
    </AppTouchable>
  );
};

const styles = StyleSheet.create({
  messageContainerStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#f5f5f5',
    marginHorizontal: 6,
    marginBottom: 6,
  },
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#E9EDF1',
    marginBottom: 4,
  },
  strike: {
    textDecorationLine: 'line-through',
  },
  cardContainer: {},
  imgContainer: {
    width: '100%',
    height: 185,
  },
  imgStyle: {
    width: '100%',
    height: '100%',
    borderRadius: 4,
    resizeMode: 'cover',
  },
  letterCover: {
    backgroundColor: '#1B1B1B',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    padding: 12,
    flex: 1,
  },
  metaContainer: {
    flexWrap: 'wrap',
  },
  metaItem: {
    marginRight: 24,
    marginTop: 8,
  },
  childrenContainer: {
    paddingHorizontal: 6,
    paddingBottom: 6,
  },
  descriptionContainer: {
    flex: 1,
  },
  infoIcon: {
    backgroundColor: '#1B1B1B40',
    borderRadius: 20,
    padding: 6,
    marginRight: 6,
  },
});

export default Card;
