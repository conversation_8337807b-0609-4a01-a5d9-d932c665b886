{"version": 3, "names": ["_deepArray", "require", "Plugin", "constructor", "plugin", "options", "key", "externalDependencies", "finalize", "manipulateOptions", "post", "pre", "visitor", "parserOverride", "generatorOverride", "name", "exports", "default"], "sources": ["../../src/config/plugin.ts"], "sourcesContent": ["import { finalize } from \"./helpers/deep-array\";\nimport type { ReadonlyDeepArray } from \"./helpers/deep-array\";\nimport type { PluginObject } from \"./validation/plugins\";\n\nexport default class Plugin {\n  key: string | undefined | null;\n  manipulateOptions?: (options: unknown, parserOpts: unknown) => void;\n  post?: PluginObject[\"post\"];\n  pre?: PluginObject[\"pre\"];\n  visitor: PluginObject[\"visitor\"];\n\n  parserOverride?: Function;\n  generatorOverride?: Function;\n\n  options: {};\n\n  externalDependencies: ReadonlyDeepArray<string>;\n\n  constructor(\n    plugin: PluginObject,\n    options: {},\n    key?: string,\n    externalDependencies: ReadonlyDeepArray<string> = finalize([]),\n  ) {\n    this.key = plugin.name || key;\n\n    this.manipulateOptions = plugin.manipulateOptions;\n    this.post = plugin.post;\n    this.pre = plugin.pre;\n    this.visitor = plugin.visitor || {};\n    this.parserOverride = plugin.parserOverride;\n    this.generatorOverride = plugin.generatorOverride;\n\n    this.options = options;\n    this.externalDependencies = externalDependencies;\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAIe,MAAMC,MAAM,CAAC;EAc1BC,WAAWA,CACTC,MAAoB,EACpBC,OAAW,EACXC,GAAY,EACZC,oBAA+C,GAAG,IAAAC,mBAAQ,EAAC,EAAE,CAAC,EAC9D;IAAA,KAlBFF,GAAG;IAAA,KACHG,iBAAiB;IAAA,KACjBC,IAAI;IAAA,KACJC,GAAG;IAAA,KACHC,OAAO;IAAA,KAEPC,cAAc;IAAA,KACdC,iBAAiB;IAAA,KAEjBT,OAAO;IAAA,KAEPE,oBAAoB;IAQlB,IAAI,CAACD,GAAG,GAAGF,MAAM,CAACW,IAAI,IAAIT,GAAG;IAE7B,IAAI,CAACG,iBAAiB,GAAGL,MAAM,CAACK,iBAAiB;IACjD,IAAI,CAACC,IAAI,GAAGN,MAAM,CAACM,IAAI;IACvB,IAAI,CAACC,GAAG,GAAGP,MAAM,CAACO,GAAG;IACrB,IAAI,CAACC,OAAO,GAAGR,MAAM,CAACQ,OAAO,IAAI,CAAC,CAAC;IACnC,IAAI,CAACC,cAAc,GAAGT,MAAM,CAACS,cAAc;IAC3C,IAAI,CAACC,iBAAiB,GAAGV,MAAM,CAACU,iBAAiB;IAEjD,IAAI,CAACT,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,oBAAoB,GAAGA,oBAAoB;EAClD;AACF;AAACS,OAAA,CAAAC,OAAA,GAAAf,MAAA;AAAA"}