import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout, AppTouchable, ThemeText } from '../atoms';
import Icon from 'react-native-vector-icons/Feather';
import { testProps } from '@appmaker-xyz/core';

const ControlledCheckBox = (props) => {
  const {
    noMargin,
    checkedStatus,
    value,
    containerStyle,
    onValueChange,
    label,
    small = false,
    activeColor: activeColorProp,
    labelStyles,
    disable = false,
    testId,
    error,
  } = props;
  const checked = value;
  const activeColor = activeColorProp || '#212121';
  const checkBoxContainerStyle = [styles.checkBoxContainer];
  const checkBoxStyle = [styles.checkBox];

  if (noMargin) {
    checkBoxContainerStyle.push({ marginBottom: 0 });
  }
  if (checked) {
    checkBoxStyle.push({
      backgroundColor: activeColor ? activeColor : '#FFFFFF',
      borderColor: activeColor ? activeColor : '#4F4F4F',
    });
  }
  if (small) {
    checkBoxStyle.push({
      height: 18,
      width: 18,
    });
  }
  if (disable) {
    checkBoxStyle.push({
      borderColor: '#A1A1AA',
    });
  }
  return (
    <AppTouchable
      {...testProps(testId)}
      style={containerStyle}
      disabled={disable}
      onPress={() => {
        // setChecked(!checked);
        onValueChange && onValueChange(!value);
      }}>
      <Layout style={checkBoxContainerStyle}>
        <Layout style={checkBoxStyle}>
          <Icon name="check" size={small ? 14 : 16} color="#ffffff" />
        </Layout>
        <Layout>
          <ThemeText
            fontFamily="medium"
            style={{ ...styles.labelTextStyles, ...labelStyles }}>
            {label}
          </ThemeText>
          {error && (
            <ThemeText size="sm" color="#E03131">
              {error}
            </ThemeText>
          )}
        </Layout>
      </Layout>
    </AppTouchable>
  );
};

const styles = StyleSheet.create({
  checkBoxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  checkBox: {
    marginRight: 10,
    borderWidth: 1,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 1,
    height: 20,
    width: 20,
  },
  labelTextStyles: {
    flexWrap: 'wrap',
    flexShrink: 1,
  },
});

export default ControlledCheckBox;
