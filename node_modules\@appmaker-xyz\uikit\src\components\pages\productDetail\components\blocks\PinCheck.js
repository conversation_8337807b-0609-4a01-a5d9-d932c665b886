import React, { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '../../../../../theme/ThemeContext';
import {
  Layout,
  Button,
  Input,
  BlockCard,
  AppmakerText,
} from '../../../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import axios from 'axios';
import { shopifyIdHelper } from '@appmaker-xyz/shopify';
import { useAsyncStorage } from '@appmaker-xyz/react-native';
import { emitEvent } from '@appmaker-xyz/core';

async function checkData({ pincode, product_id }) {
  const resp = await axios.get(
    'https://mby6me5g2i.execute-api.ap-south-1.amazonaws.com/production/pincode-checker',
    {
      params: {
        pincode,
        product_id,
        // product_type: 'nonfurniture',
        add_buffer: false,
      },
    },
  );
  const { data } = resp;
  return {
    loaded: true,
    deliveryType: data.delivery_available
      ? 'Standard Delivery'
      : 'Undeliverable',
    deliverybyDate: data.estimated_delivery,
    cod_available: data.cod_available,
    locationName: data.city,
    // deliveryFee: 'Rs. 100',
  };
}

const PinCheck = ({ attributes = {}, onAction }) => {
  // const { result = false } = attributes;
  // base64
  // console.log(attributes.blockItem.node.id);
  const { spacing, color } = useApThemeState();
  const [loading, setLoading] = React.useState(false);
  const [pincodeStorage, setPinCodeStorage] = useAsyncStorage('pincode', '');
  const [pincode, setPinCode] = useState('');
  const [result, setResult] = React.useState({});
  const styles = allStyles({ spacing, color });
  const { __appmakerCustomStyles = {} } = attributes;

  useEffect(() => {
    if (pincodeStorage) {
      async function checkPin() {
        setPinCode(pincodeStorage);
        setLoading(true);
        // const product_id = base64
        const product_id = shopifyIdHelper(
          attributes?.blockItem?.node?.id,
          true,
        );
        try {
          const resp = await checkData({ pincode: pincodeStorage, product_id });
          setResult(resp);
          setLoading(false);
        } catch (error) {
          console.log(error);
          setLoading(false);
          onAction({
            action: 'SHOW_MESSAGE',
            params: { title: 'Error checking pincode' },
          });
        }
      }
      checkPin();
    }
  }, [pincodeStorage]);
  // console.log(result);
  return (
    <BlockCard
      attributes={{
        title: result.loaded
          ? `Delivery to ${result.locationName ? result.locationName : pincode}`
          : 'Check Delivery',
        accessButton: result ? 'Change Pin' : null,
        accessButtonColor: attributes.accessButtonColor,
      }}
      onPress={() => setResult({})}>
      {!result.loaded ? (
        <Layout
          style={[
            styles.pinCheck,
            styles.subPadding,
            __appmakerCustomStyles?.container,
          ]}>
          <Layout style={styles.column1}>
            <Input
              label="PIN Code"
              maxLength={6}
              styleType="noContainer"
              value={pincode}
              onChangeText={setPinCode}
              type="number"
              __appmakerCustomStyles={__appmakerCustomStyles?.input}
            />
          </Layout>
          <Layout style={styles.column2}>
            <Button
              baseSize={true}
              status="dark"
              loading={loading}
              onPress={async () => {
                if (pincode.length === 6) {
                  emitEvent('pincodeCheck', {
                    pincode,
                    product_name: attributes?.blockItem?.node?.title,
                  });
                  setPinCodeStorage(pincode);
                } else {
                  onAction({
                    action: 'SHOW_MESSAGE',
                    params: { title: 'Please enter a valid pincode' },
                  });
                }
              }}
              wholeContainerStyle={__appmakerCustomStyles?.button}>
              Check
            </Button>
          </Layout>
        </Layout>
      ) : (
        <Layout style={styles.subPadding}>
          <Layout
            style={[
              styles.resultContainer,
              __appmakerCustomStyles?.success_container,
            ]}>
            <Layout flexDirection="row">
              <Icon
                name={result?.deliverIcon || 'truck'}
                size={spacing.lg}
                style={[styles.icon, __appmakerCustomStyles?.success_icon]}
                color={
                  result.deliveryType === 'Undeliverable'
                    ? color.danger
                    : color.dark
                }
              />
              <Layout>
                {result?.deliveryType ? (
                  <AppmakerText
                    category="h1Heading"
                    status={
                      result.deliveryType === 'Undeliverable'
                        ? 'danger'
                        : 'dark'
                    }
                    style={__appmakerCustomStyles?.success_text}>
                    {result.deliveryType}
                  </AppmakerText>
                ) : null}
                {result?.deliverybyDate ? (
                  <AppmakerText style={__appmakerCustomStyles?.success_text}>
                    Delivery by
                    <AppmakerText>{result.deliverybyDate}</AppmakerText>
                  </AppmakerText>
                ) : null}
              </Layout>
            </Layout>
            {result.deliveryType !== 'Undeliverable' ? (
              <AppmakerText
                category="bodyParagraphBold"
                status={result.cod_available ? 'success' : 'dark'}
                style={__appmakerCustomStyles?.success_cod_text}>
                {result.cod_available ? 'COD Available' : 'COD Not Available'}
              </AppmakerText>
            ) : null}
          </Layout>
        </Layout>
      )}
    </BlockCard>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: spacing.base,
    },
    pinCheck: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      // paddingTop: spacing.mini,
    },
    column1: {
      width: '70%',
    },
    column2: {
      width: '28%',
    },
    subPadding: {
      paddingHorizontal: spacing.base,
      paddingBottom: spacing.base,
    },
    resultContainer: {
      backgroundColor: color.light,
      paddingVertical: spacing.mini,
      paddingHorizontal: spacing.small,
      borderRadius: spacing.mini,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    icon: {
      marginRight: spacing.mini,
    },
  });

export default PinCheck;
