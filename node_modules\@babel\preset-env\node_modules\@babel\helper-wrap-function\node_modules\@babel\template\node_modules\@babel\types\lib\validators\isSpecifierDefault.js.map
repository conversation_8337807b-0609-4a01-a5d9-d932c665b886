{"version": 3, "names": ["_generated", "require", "isSpecifierDefault", "specifier", "isImportDefaultSpecifier", "isIdentifier", "imported", "exported", "name"], "sources": ["../../src/validators/isSpecifierDefault.ts"], "sourcesContent": ["import { isIdentifier, isImportDefaultSpecifier } from \"./generated\";\nimport type * as t from \"..\";\n\n/**\n * Check if the input `specifier` is a `default` import or export.\n */\nexport default function isSpecifierDefault(\n  specifier: t.ModuleSpecifier,\n): boolean {\n  return (\n    isImportDefaultSpecifier(specifier) ||\n    // @ts-expect-error todo(flow->ts): stricter type for specifier\n    isIdentifier(specifier.imported || specifier.exported, {\n      name: \"default\",\n    })\n  );\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAMe,SAASC,kBAAkBA,CACxCC,SAA4B,EACnB;EACT,OACE,IAAAC,mCAAwB,EAACD,SAAS,CAAC,IAEnC,IAAAE,uBAAY,EAACF,SAAS,CAACG,QAAQ,IAAIH,SAAS,CAACI,QAAQ,EAAE;IACrDC,IAAI,EAAE;EACR,CAAC,CAAC;AAEN"}