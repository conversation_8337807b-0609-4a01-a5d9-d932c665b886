import React from 'react';
import { StyleSheet } from 'react-native';
import { ThemeText, Layout } from '@appmaker-xyz/ui';
import { useOrderItem } from '@appmaker-xyz/shopify';

const ShippingAddress = (props) => {
  const { addressName, address1, address2, city, country, zip } =
    useOrderItem(props);

  return (
    <Layout style={styles.container}>
      <ThemeText size="md" fontFamily="medium" style={styles.title}>
        Delivery Address
      </ThemeText>
      <ThemeText fontFamily="medium">{addressName}</ThemeText>
      {address1 ? <ThemeText>{address1}</ThemeText> : null}
      {address2 ? <ThemeText>{address2}</ThemeText> : null}
      {city ? <ThemeText>{city}</ThemeText> : null}
      <ThemeText>
        {country} - {zip}
      </ThemeText>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 12,
    marginTop: 8,
    marginBottom: 2,
    padding: 8,
    borderRadius: 4,
  },
  title: {
    marginBottom: 4,
  },
});

export default ShippingAddress;
