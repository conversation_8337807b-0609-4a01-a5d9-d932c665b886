/**
 * Created by y on 3/7/2017.
 */
import React, { useState, useEffect, useRef } from 'react';
import { View, Dimensions } from 'react-native';
import t from '@appmaker/tcomb-form-native';
import { toForm } from './form';
const Form = t.form.Form;
const DynamicForm = ({ attributes, pageDispatch }) => {
  if (attributes?.fields?.items == undefined) {
    return null;
  }
  const [formValues, setFormValues] = useState(() => {
    if (attributes?.fields?.items) {
      const { items } = attributes.fields;
      const defaultState = {};
      Object.keys(items).map((item) => {
        defaultState[item] = items[item].default_value;
      });
      return defaultState;
    }
    return {};
  });
  useEffect(() => {
    pageDispatch({
      type: 'set_values',
      values: { customValues: formValues.form },
    });
  }, [formValues, pageDispatch]);
  const forms = attributes.fields || { items: [] };
  const formStruture = toForm(forms.items, forms.dependencies, forms.orders);
  const form = {
    type: t.struct({ form: formStruture.types }),
    options: {
      fields: { form: { ...formStruture.options, label: ' ' } },
    },
    values: { form: formStruture.values },
  };

  const { tcombType } = attributes;

  return attributes.fields ? (
    <View style={attributes.containerStyle}>
      <Form
        type={tcombType || form.type}
        options={form.options}
        value={form.values}
        onChange={(data) => {
          // setFormValues({ ...data });
          pageDispatch({
            type: 'set_values',
            values: { customValues: data.form },
          });
        }}
      />
    </View>
  ) : null;
};
export default DynamicForm;
