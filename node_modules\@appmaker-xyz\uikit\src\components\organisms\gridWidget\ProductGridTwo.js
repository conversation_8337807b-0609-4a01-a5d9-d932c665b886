import React, { useState, useEffect } from 'react';
import { StyleSheet, ActivityIndicator, Dimensions } from 'react-native';
import {
  Layout,
  AppmakerText,
  AppTouchable,
  Badge,
  AppImage,
} from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/AntDesign';
import { useTranslation } from 'react-i18next';
import { appmaker } from '@appmaker-xyz/core';
import { appSettings } from '@appmaker-xyz/core';
import { useProductListItem, useProductWishList } from '@appmaker-xyz/shopify';
import { useProductHook } from './useProductHook';

let deviceRatio =
  Dimensions.get('window').height / Dimensions.get('window').width;
const isTab = deviceRatio < 1.6;

const LoadingIndicator = () => <ActivityIndicator size={16} color="#1B1B1B" />;

function SpecialBadge(props) {
  return (
    <Layout style={props.styles.chumbakInsider}>
      <AppmakerText category="smallButtonText">
        {props.specialPrice}
      </AppmakerText>
      <AppImage
        uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
        style={props.styles.insiderImage}
      />
      <AppmakerText category="smallButtonText">members</AppmakerText>
    </Layout>
  );
}

const ProductGridTwo = ({
  clientId,
  attributes,
  pageDispatch,
  onPress,
  onAction,
  blockData,
  data = {},
}) => {
  // console.log(appmaker.applyFilters);
  const ReviewsComp = appmaker.applyFilters(
    'appmaker-product-grid-reviews',
    () => null,
  );
  const {
    priceSale = '0',
    wishList,
    specialPrice,
    groceryMode = appSettings.getOptionAsBoolean('grocery_mode'),
    easyAddCart = false,
    numColumns,
    attribute,
    appmakerAction,
    size = '', // sm for small
    brandName,
    new_product,
    show_last_few_remaining,
    last_few_remaining_text,
    bestSeller,
    brandColor,
    titleNumberOfLines,
    numberOfItemsToShowInScroller,
    __appmakerCustomStyles = {},
  } = attributes;
  const numberOfLines = parseFloat(titleNumberOfLines);
  //let imageRatio = thumbnail_meta.height / thumbnail_meta.width;
  const customHeight = gridViewListing ? 186 : 180;

  // const [currentQuantity, setCurrentQuantity] = useState(quatity);
  const { onSaved, addwish, setAddwish } = useProductHook({
    attributes,
    onAction,
    blockData,
    pageDispatch,
  });

  const {
    gridViewListing,
    average_rating,
    productType,
    imageRatio: ratio,
    salePrice,
    regularPrice,
    onSale,
    salePercentage,
    outOfStock,
    // wishList,
    imageUrl: uri,
    title,
    addToCart,
    count,
    updateCart,
    isAddToCartLoading,
    openProduct,
  } = useProductListItem({ attributes, onAction, blockData, pageDispatch });
  const { toggleWishList, isSaved } = useProductWishList({
    onAction,
    blockData,
  });
  let imageRatio = ratio;
  imageRatio = appmaker.applyFilters('product-grid-image-ratio', imageRatio);

  const in_stock = !outOfStock;
  const ratingBadgeColor = () => {
    let rating = average_rating;
    switch (true) {
      case rating >= '3.5':
        return 'success';
      case rating >= '2.5':
        return 'warining';
      default:
        return 'dark';
    }
  };
  const { color, spacing, font, dimensions } = useApThemeState();
  const styles = allStyles({ color, spacing, imageRatio, customHeight });
  const containerStyle = [styles.container];
  const imageContainerStyle = [styles.imageContainer];

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  let attributeView = null;
  if (attribute) {
    attributeView = attribute.map((value) => {
      return (
        <Layout style={styles.attributesListing}>
          <Badge
            text={value.label}
            status="demiDark"
            style={styles.attributeBadgeStyle}
          />
        </Layout>
      );
    });
  }
  if (size === 'sm') {
    imageContainerStyle.push({
      width: 100,
      height: imageRatio ? imageRatio * 80 : 100,
    });
  }

  if (gridViewListing === true) {
    containerStyle.push({ marginLeft: 0 });
    imageContainerStyle.push({ width: '100%' });
  }
  if (__appmakerCustomStyles) {
    containerStyle.push(__appmakerCustomStyles?.container);
    imageContainerStyle.push(__appmakerCustomStyles?.image_container);
  }
  //bookmarksList[data.id] != undefined
  // console.log(show_last_few_remaining);
  const customScrollerItemWidth = numberOfItemsToShowInScroller
    ? dimensions.fullWidth / numberOfItemsToShowInScroller
    : 196;
  return (
    <AppTouchable
      onPress={openProduct}
      style={
        gridViewListing
          ? {
              width: `${100 / numColumns}%`,
              padding: spacing.nano,
            }
          : {
              width: isTab ? 300 : customScrollerItemWidth,
              marginLeft: spacing.base,
            }
      }
      clientId={clientId}>
      <Layout style={[styles.container, __appmakerCustomStyles?.container]}>
        <Layout style={imageContainerStyle}>
          <AppImage
            uri={uri}
            style={[styles.productImage, __appmakerCustomStyles?.product_image]}
          />
          <Layout style={styles.topLeftBlocks}>
            {average_rating && average_rating > 0 && (
              <Badge
                style={styles.saleBadgeStyle}
                text={average_rating}
                status={ratingBadgeColor()}
                iconName="star"
                customStyles={__appmakerCustomStyles?.rating_badge}
              />
            )}
            {ReviewsComp && <ReviewsComp attributes={attributes} />}
            {bestSeller ? (
              <Badge
                style={styles.saleBadgeStyle}
                status="demiDark"
                text={bestSeller.value}
                customStyles={__appmakerCustomStyles?.bestseller_badge}
              />
            ) : null}
          </Layout>
        </Layout>
        <Layout style={styles.contentContainer}>
          <Layout
            style={[
              styles.overlayContainer,
              __appmakerCustomStyles?.overlay_container,
            ]}>
            {wishList == true ? (
              <AppTouchable
                onPress={() => {
                  toggleWishList && toggleWishList();
                }}
                style={[
                  styles.wishListIcon,
                  styles.shadow,
                  __appmakerCustomStyles?.wish_list_icon_container,
                ]}>
                <Icon
                  name={!isSaved ? 'hearto' : 'heart'}
                  color={!isSaved ? color.demiDark : color.danger}
                  size={font.size.md}
                  style={__appmakerCustomStyles?.wish_list_icon}
                />
              </AppTouchable>
            ) : null}
            {groceryMode && in_stock == true ? (
              <AppTouchable
                onPress={() => addToCart({ appmakerAction: false })}
                style={[
                  styles.addToCart,
                  styles.shadow,
                  __appmakerCustomStyles?.add_icon_container,
                ]}>
                {isAddToCartLoading ? (
                  <LoadingIndicator />
                ) : (
                  <Icon
                    name="plus"
                    size={font.size.md}
                    color={color.demiDark}
                    style={__appmakerCustomStyles?.add_icon}
                  />
                )}
              </AppTouchable>
            ) : null}
          </Layout>
          <Layout style={styles.productTitle}>
            {new_product ? (
              <AppmakerText
                category="highlighter2"
                fontColor={brandColor?.secondary}
                style={__appmakerCustomStyles?.new_product_text}>
                {new_product.value}
              </AppmakerText>
            ) : null}
            {show_last_few_remaining &&
            (show_last_few_remaining === 'true' ||
              show_last_few_remaining === true) ? (
              <AppmakerText
                category="highlighter2"
                fontColor={brandColor?.secondary}
                style={__appmakerCustomStyles?.last_few_remaining_text}>
                {last_few_remaining_text}
              </AppmakerText>
            ) : null}
            {in_stock == true ? null : (
              <AppmakerText
                category="highlighter2"
                fontColor={brandColor?.secondary}
                style={__appmakerCustomStyles?.out_of_stock_text}>
                Out of Stock
              </AppmakerText>
            )}
            {isNaN(numberOfLines) == false ? (
              <AppmakerText
                style={__appmakerCustomStyles?.product_title}
                numberOfLines={titleNumberOfLines}>
                {title}
              </AppmakerText>
            ) : (
              <AppmakerText style={__appmakerCustomStyles?.product_title}>
                {title}
              </AppmakerText>
            )}
          </Layout>
          <Layout
            style={[
              styles.priceDataContainer,
              __appmakerCustomStyles?.price_data_container,
            ]}>
            <AppmakerText category="bodyParagraphBold">
              <AppmakerText style={__appmakerCustomStyles?.sale_price}>
                {salePrice}
              </AppmakerText>
            </AppmakerText>
            {regularPrice !== ' ' ? (
              <AppmakerText
                category="highlighter2"
                fontColor={
                  __appmakerCustomStyles?.offer_price?.color
                    ? __appmakerCustomStyles?.offer_price?.color
                    : brandColor?.primary
                }
                status="demiDark"
                hideText={!onSale && !priceSale}
                style={styles.offerPrice}>
                {regularPrice}
              </AppmakerText>
            ) : null}
            {onSale && (
              <Badge
                style={styles.saleBadgeStyle}
                status="success"
                text={salePercentage}
                customStyles={__appmakerCustomStyles?.sale_badge}
              />
            )}
          </Layout>
          {specialPrice ? (
            <SpecialBadge specialPrice={specialPrice} styles={styles} />
          ) : null}
        </Layout>
      </Layout>
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color, imageRatio }) =>
  StyleSheet.create({
    container: {
      flex: 1,
      marginBottom: spacing.base,
      position: 'relative',
    },
    imageContainer: {
      backgroundColor: color.white,
      borderRadius: spacing.small,
      aspectRatio: imageRatio ? 1 / imageRatio : 1,
      position: 'relative',
    },
    productImage: {
      width: '100%',
      height: '100%',
      borderRadius: spacing.small,
      overflow: 'hidden',
    },
    overlayContainer: {
      position: 'absolute',
      zIndex: 10,
      flexDirection: 'row',
      top: -42,
      right: spacing.base,
    },
    addToCart: {
      backgroundColor: color.white,
      padding: 8,
      borderRadius: spacing.lg,
      marginLeft: 10,
    },
    wishListIcon: {
      backgroundColor: color.white,
      padding: 8,
      borderRadius: spacing.lg,
    },
    shadow: {
      shadowColor: color.demiDark,
      shadowOffset: {
        width: 0,
        height: 8,
      },
      shadowOpacity: 0.3,
      shadowRadius: spacing.small,
      elevation: spacing.small,
    },
    contentContainer: {
      marginTop: spacing.lg,
      flex: 1,
      position: 'relative',
    },
    productTitle: {
      flex: 1,
      marginBottom: spacing.nano,
    },
    priceDataContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-start',
      flexWrap: 'wrap',
    },
    offerPrice: {
      textDecorationLine: 'line-through',
      marginHorizontal: spacing.nano,
    },
    saleBadgeStyle: {
      marginRight: spacing.nano,
    },
    stockOutBadgeStyle: {},
    topLeftBlocks: {
      flexDirection: 'row',
      position: 'absolute',
      flexWrap: 'wrap',
      left: 4,
      top: 4,
    },
    chumbakInsider: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#B6DBC9',
      paddingVertical: 2,
      paddingLeft: 6,
      marginTop: 4,
    },
    insiderImage: {
      width: 60,
      height: 20,
      resizeMode: 'contain',
    },
  });

export default ProductGridTwo;
