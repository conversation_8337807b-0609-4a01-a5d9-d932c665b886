import { appmaker } from '@appmaker-xyz/core';

import Judgeme from './judgeme/index';
import CmCommerce from './cm-commerce';
import LooxReviews from './loox-reviews/index';
import MetaFields from './meta-fields/index';
import SocialLogin from './social-login';
import Growave from './growave';
import Multipass from './multi-pass';
// import ReturnPrime from './return-prime';
import OrderEase from './order-ease';
import searchtap from './searchtap';
import SezzleIn from './sezzle-in/index';
import AdvacnedStyles from './advanced-styles';
import InstaFeed from './instafeed/index';
import OrdersifyProductAlerts from './ordersify-product-alerts/index';
import VoilaCouponDiscount from './voila-coupon-discount/index';
import AppForceUpdate from './app-force-update/index';
import ShopifyPlugin from './shopify/index';
import AppOnlyCoupon from './app-only-coupons/index';
import CurrencyChooser from './appmaker-currency-switcher/index';
import Xeno from './xeno/index';
import Loyaltylion from './loyaltylion/index';

// activate()

appmaker.registerPlugin(Loyaltylion);
appmaker.registerPlugin(Xeno);
appmaker.registerPlugin(AppOnlyCoupon);
appmaker.registerPlugin(Judgeme);
appmaker.registerPlugin(CmCommerce);
appmaker.registerPlugin(LooxReviews);
appmaker.registerPlugin(MetaFields);
appmaker.registerPlugin(SocialLogin);
appmaker.registerPlugin(Growave);
appmaker.registerPlugin(Multipass);
// appmaker.registerPlugin(ReturnPrime);
appmaker.registerPlugin(OrderEase);
// appmaker.registerPlugin(searchtap);
appmaker.registerPlugin(SezzleIn);
appmaker.registerPlugin(AdvacnedStyles);
appmaker.registerPlugin(InstaFeed);
appmaker.registerPlugin(OrdersifyProductAlerts);
appmaker.registerPlugin(VoilaCouponDiscount);
appmaker.registerPlugin(AppForceUpdate);
appmaker.registerPlugin(ShopifyPlugin);
appmaker.registerPlugin(CurrencyChooser);

// LooxReviews.activate();
