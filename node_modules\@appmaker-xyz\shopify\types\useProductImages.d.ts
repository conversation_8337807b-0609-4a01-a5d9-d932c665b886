import { RefObject } from 'react';

declare module '@appmaker-xyz/shopify' {
  export interface UseProductImagesProps {
    attributes: {
      uri?: string;
      customDotColor?: string;
      appmakerAction?: any;
    };
    onAction: (action: { action: string; params: any }) => void;
    blockData: Product;
    isMedia?: boolean;
    selectedVariant?: Variant;
  }

  export interface UseProductImagesConfig {
    customImageSize?: any;
  }

  export interface UseProductImagesReturn {
    openVideo: (params: { videoIndex?: number }) => void;
    isMedia: boolean;
    media: Array<any>;
    swiperRef: RefObject<any>;
    onAction: (action: { action: string; params: any }) => void;
    index: number;
    imageList: Array<Image>;
    appmakerAction: any;
    customDotColor?: string;
    imageRatio: number;
    shareProduct: () => Pr;
    currentImageIndex: number;
    setImageIndex: (index: number) => void;
    openImage: (params: { imageIndex?: number }) => void;
  }
  
  export function useProductImages(props: UseProductImagesProps, config?: UseProductImagesConfig): UseProductImagesReturn;
}