{"version": 3, "names": ["_parseError", "require", "kExpression", "kMaybeArrowParameterDeclaration", "kMaybeAsyncArrowParameterDeclaration", "kParameterDeclaration", "ExpressionScope", "constructor", "type", "canBeArrowParameterDeclaration", "isCertainlyParameterDeclaration", "ArrowHeadParsingScope", "declarationErrors", "Map", "recordDeclarationError", "ParsingErrorClass", "at", "index", "set", "clearDeclarationError", "delete", "iterateErrors", "iterator", "for<PERSON>ach", "ExpressionScopeHandler", "parser", "stack", "enter", "scope", "push", "exit", "pop", "recordParameterInitializerError", "toParseError", "node", "origin", "loc", "start", "i", "length", "raise", "recordArrowParameterBindingError", "error", "recordAsyncArrowParametersError", "Errors", "AwaitBindingIdentifier", "validateAsPattern", "currentScope", "exports", "default", "newParameterDeclarationScope", "newArrowHeadScope", "newAsyncArrowScope", "newExpressionScope"], "sources": ["../../src/util/expression-scope.ts"], "sourcesContent": ["import { Errors, type ParseErrorConstructor } from \"../parse-error\";\nimport type { Position } from \"./location\";\nimport type { Node } from \"../types\";\nimport type Tokenizer from \"../tokenizer\";\n\n/**\n * @module util/expression-scope\n\nExpressionScope is used to track declaration errors in these ambiguous patterns:\n\n- CoverParenthesizedExpressionAndArrowParameterList\n  e.g. we don't know if `({ x })` is an parenthesized expression or an\n  arrow function parameters until we see an `=>` after `)`.\n\n- CoverCallExpressionAndAsyncArrowHead\n  e.g. we don't know if `async({ x })` is a call expression or an async arrow\n  function parameters until we see an `=>` after `)`\n\nThe following declaration errors (@see parser-errors/standard) will be recorded in\nsome expression scopes and thrown later when we know what the ambiguous pattern is\n\n- AwaitBindingIdentifier\n- AwaitExpressionFormalParameter\n- YieldInParameter\n- InvalidParenthesizedAssignment when parenthesized is an identifier\n\nThere are four different expression scope\n- Expression\n  A general scope that represents program / function body / static block. No errors\n  will be recorded nor thrown in this scope.\n\n- MaybeArrowParameterDeclaration\n  A scope that represents ambiguous arrow head e.g. `(x)`. Errors will be recorded\n  alongside parent scopes and thrown when `ExpressionScopeHandler#validateAsPattern`\n  is called.\n\n- MaybeAsyncArrowParameterDeclaration\n  A scope that represents ambiguous async arrow head e.g. `async(x)`. Errors will\n  be recorded alongside parent scopes and thrown when\n  `ExpressionScopeHandler#validateAsPattern` is called.\n\n- ParameterDeclaration\n  A scope that represents unambiguous function parameters `function(x)`. Errors\n  recorded in this scope will be thrown immediately. No errors will be recorded in\n  this scope.\n\n// @see {@link https://docs.google.com/document/d/1FAvEp9EUK-G8kHfDIEo_385Hs2SUBCYbJ5H-NnLvq8M|V8 Expression Scope design docs}\n */\n\nconst kExpression = 0,\n  kMaybeArrowParameterDeclaration = 1,\n  kMaybeAsyncArrowParameterDeclaration = 2,\n  kParameterDeclaration = 3;\n\ntype ExpressionScopeType = 0 | 1 | 2 | 3;\n\nclass ExpressionScope {\n  type: ExpressionScopeType;\n\n  constructor(type: ExpressionScopeType = kExpression) {\n    this.type = type;\n  }\n\n  canBeArrowParameterDeclaration(): this is ArrowHeadParsingScope {\n    return (\n      this.type === kMaybeAsyncArrowParameterDeclaration ||\n      this.type === kMaybeArrowParameterDeclaration\n    );\n  }\n\n  isCertainlyParameterDeclaration() {\n    return this.type === kParameterDeclaration;\n  }\n}\n\ntype ArrowHeadParsingParameterInitializerError =\n  | typeof Errors.AwaitExpressionFormalParameter\n  | typeof Errors.YieldInParameter;\ntype ArrowHeadParsingDeclarationError =\n  | ArrowHeadParsingParameterInitializerError\n  | typeof Errors.InvalidParenthesizedAssignment\n  | typeof Errors.AwaitBindingIdentifier;\n\nclass ArrowHeadParsingScope extends ExpressionScope {\n  declarationErrors: Map<number, [ParseErrorConstructor<{}>, Position]> =\n    new Map();\n  constructor(type: 1 | 2) {\n    super(type);\n  }\n  recordDeclarationError(\n    ParsingErrorClass: ParseErrorConstructor<{}>,\n    {\n      at,\n    }: {\n      at: Position;\n    },\n  ) {\n    const index = at.index;\n\n    this.declarationErrors.set(index, [ParsingErrorClass, at]);\n  }\n  clearDeclarationError(index: number) {\n    this.declarationErrors.delete(index);\n  }\n  iterateErrors(\n    iterator: (a: [ArrowHeadParsingDeclarationError, Position]) => void,\n  ) {\n    this.declarationErrors.forEach(iterator);\n  }\n}\n\nexport default class ExpressionScopeHandler {\n  parser: Tokenizer;\n  stack: Array<ExpressionScope> = [new ExpressionScope()];\n\n  constructor(parser: Tokenizer) {\n    this.parser = parser;\n  }\n  enter(scope: ExpressionScope) {\n    this.stack.push(scope);\n  }\n\n  exit() {\n    this.stack.pop();\n  }\n\n  /**\n   * Record likely parameter initializer errors\n   *\n   * When current scope is a ParameterDeclaration, the error will be thrown immediately,\n   * otherwise it will be recorded to any ancestry MaybeArrowParameterDeclaration and\n   * MaybeAsyncArrowParameterDeclaration scope until an Expression scope is seen.\n   */\n  recordParameterInitializerError(\n    toParseError: ArrowHeadParsingParameterInitializerError,\n    {\n      at: node,\n    }: {\n      at: Node;\n    },\n  ): void {\n    const origin = { at: node.loc.start };\n    const { stack } = this;\n    let i = stack.length - 1;\n    let scope: ExpressionScope = stack[i];\n    while (!scope.isCertainlyParameterDeclaration()) {\n      if (scope.canBeArrowParameterDeclaration()) {\n        scope.recordDeclarationError(toParseError, origin);\n      } else {\n        /*:: invariant(scope.type == kExpression) */\n        // Type-Expression is the boundary where initializer error can populate to\n        return;\n      }\n      scope = stack[--i];\n    }\n    this.parser.raise(toParseError, origin);\n  }\n\n  /**\n   * Record errors that must be thrown if the current pattern ends up being an arrow\n   * function parameter. This is used to record parenthesized identifiers, and to record\n   * \"a as T\" and \"<T> a\" type assertions when parsing typescript.\n   *\n   * A parenthesized identifier (or type assertion) in LHS can be ambiguous because the assignment\n   * can be transformed to an assignable later, but not vice versa:\n   * For example, in `([(a) = []] = []) => {}`, we think `(a) = []` is an LHS in `[(a) = []]`,\n   * an LHS within `[(a) = []] = []`. However the LHS chain is then transformed by toAssignable,\n   * and we should throw assignment `(a)`, which is only valid in LHS. Hence we record the\n   * location of parenthesized `(a)` to current scope if it is one of MaybeArrowParameterDeclaration\n   * and MaybeAsyncArrowParameterDeclaration\n   *\n   * Unlike `recordParameterInitializerError`, we don't record to ancestry scope because we\n   * validate arrow head parsing scope before exit, and then the LHS will be unambiguous:\n   * For example, in `( x = ( [(a) = []] = [] ) ) => {}`, we should not record `(a)` in `( x = ... ) =>`\n   * arrow scope because when we finish parsing `( [(a) = []] = [] )`, it is an unambiguous assignment\n   * expression and can not be cast to pattern\n   */\n  recordArrowParameterBindingError(\n    error: ParseErrorConstructor<{}>,\n    {\n      at: node,\n    }: {\n      at: Node;\n    },\n  ): void {\n    const { stack } = this;\n    const scope: ExpressionScope = stack[stack.length - 1];\n    const origin = { at: node.loc.start };\n    if (scope.isCertainlyParameterDeclaration()) {\n      this.parser.raise(error, origin);\n    } else if (scope.canBeArrowParameterDeclaration()) {\n      scope.recordDeclarationError(error, origin);\n    } else {\n      return;\n    }\n  }\n\n  /**\n   * Record likely async arrow parameter errors\n   *\n   * Errors will be recorded to any ancestry MaybeAsyncArrowParameterDeclaration\n   * scope until an Expression scope is seen.\n   */\n  recordAsyncArrowParametersError({ at }: { at: Position }): void {\n    const { stack } = this;\n    let i = stack.length - 1;\n    let scope: ExpressionScope = stack[i];\n    while (scope.canBeArrowParameterDeclaration()) {\n      if (scope.type === kMaybeAsyncArrowParameterDeclaration) {\n        scope.recordDeclarationError(Errors.AwaitBindingIdentifier, { at });\n      }\n      scope = stack[--i];\n    }\n  }\n\n  validateAsPattern(): void {\n    const { stack } = this;\n    const currentScope = stack[stack.length - 1];\n    if (!currentScope.canBeArrowParameterDeclaration()) return;\n    currentScope.iterateErrors(([toParseError, loc]) => {\n      this.parser.raise(toParseError, { at: loc });\n      // iterate from parent scope\n      let i = stack.length - 2;\n      let scope = stack[i];\n      while (scope.canBeArrowParameterDeclaration()) {\n        scope.clearDeclarationError(loc.index);\n        scope = stack[--i];\n      }\n    });\n  }\n}\n\nexport function newParameterDeclarationScope() {\n  return new ExpressionScope(kParameterDeclaration);\n}\n\nexport function newArrowHeadScope() {\n  return new ArrowHeadParsingScope(kMaybeArrowParameterDeclaration);\n}\n\nexport function newAsyncArrowScope() {\n  return new ArrowHeadParsingScope(kMaybeAsyncArrowParameterDeclaration);\n}\n\nexport function newExpressionScope() {\n  return new ExpressionScope();\n}\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,WAAA,GAAAC,OAAA;AAiDA,MAAMC,WAAW,GAAG,CAAC;EACnBC,+BAA+B,GAAG,CAAC;EACnCC,oCAAoC,GAAG,CAAC;EACxCC,qBAAqB,GAAG,CAAC;AAI3B,MAAMC,eAAe,CAAC;EAGpBC,WAAWA,CAACC,IAAyB,GAAGN,WAAW,EAAE;IAAA,KAFrDM,IAAI;IAGF,IAAI,CAACA,IAAI,GAAGA,IAAI;EAClB;EAEAC,8BAA8BA,CAAA,EAAkC;IAC9D,OACE,IAAI,CAACD,IAAI,KAAKJ,oCAAoC,IAClD,IAAI,CAACI,IAAI,KAAKL,+BAA+B;EAEjD;EAEAO,+BAA+BA,CAAA,EAAG;IAChC,OAAO,IAAI,CAACF,IAAI,KAAKH,qBAAqB;EAC5C;AACF;AAUA,MAAMM,qBAAqB,SAASL,eAAe,CAAC;EAGlDC,WAAWA,CAACC,IAAW,EAAE;IACvB,KAAK,CAACA,IAAI,CAAC;IAAC,KAHdI,iBAAiB,GACf,IAAIC,GAAG,CAAC,CAAC;EAGX;EACAC,sBAAsBA,CACpBC,iBAA4C,EAC5C;IACEC;EAGF,CAAC,EACD;IACA,MAAMC,KAAK,GAAGD,EAAE,CAACC,KAAK;IAEtB,IAAI,CAACL,iBAAiB,CAACM,GAAG,CAACD,KAAK,EAAE,CAACF,iBAAiB,EAAEC,EAAE,CAAC,CAAC;EAC5D;EACAG,qBAAqBA,CAACF,KAAa,EAAE;IACnC,IAAI,CAACL,iBAAiB,CAACQ,MAAM,CAACH,KAAK,CAAC;EACtC;EACAI,aAAaA,CACXC,QAAmE,EACnE;IACA,IAAI,CAACV,iBAAiB,CAACW,OAAO,CAACD,QAAQ,CAAC;EAC1C;AACF;AAEe,MAAME,sBAAsB,CAAC;EAI1CjB,WAAWA,CAACkB,MAAiB,EAAE;IAAA,KAH/BA,MAAM;IAAA,KACNC,KAAK,GAA2B,CAAC,IAAIpB,eAAe,CAAC,CAAC,CAAC;IAGrD,IAAI,CAACmB,MAAM,GAAGA,MAAM;EACtB;EACAE,KAAKA,CAACC,KAAsB,EAAE;IAC5B,IAAI,CAACF,KAAK,CAACG,IAAI,CAACD,KAAK,CAAC;EACxB;EAEAE,IAAIA,CAAA,EAAG;IACL,IAAI,CAACJ,KAAK,CAACK,GAAG,CAAC,CAAC;EAClB;EASAC,+BAA+BA,CAC7BC,YAAuD,EACvD;IACEjB,EAAE,EAAEkB;EAGN,CAAC,EACK;IACN,MAAMC,MAAM,GAAG;MAAEnB,EAAE,EAAEkB,IAAI,CAACE,GAAG,CAACC;IAAM,CAAC;IACrC,MAAM;MAAEX;IAAM,CAAC,GAAG,IAAI;IACtB,IAAIY,CAAC,GAAGZ,KAAK,CAACa,MAAM,GAAG,CAAC;IACxB,IAAIX,KAAsB,GAAGF,KAAK,CAACY,CAAC,CAAC;IACrC,OAAO,CAACV,KAAK,CAAClB,+BAA+B,CAAC,CAAC,EAAE;MAC/C,IAAIkB,KAAK,CAACnB,8BAA8B,CAAC,CAAC,EAAE;QAC1CmB,KAAK,CAACd,sBAAsB,CAACmB,YAAY,EAAEE,MAAM,CAAC;MACpD,CAAC,MAAM;QAGL;MACF;MACAP,KAAK,GAAGF,KAAK,CAAC,EAAEY,CAAC,CAAC;IACpB;IACA,IAAI,CAACb,MAAM,CAACe,KAAK,CAACP,YAAY,EAAEE,MAAM,CAAC;EACzC;EAqBAM,gCAAgCA,CAC9BC,KAAgC,EAChC;IACE1B,EAAE,EAAEkB;EAGN,CAAC,EACK;IACN,MAAM;MAAER;IAAM,CAAC,GAAG,IAAI;IACtB,MAAME,KAAsB,GAAGF,KAAK,CAACA,KAAK,CAACa,MAAM,GAAG,CAAC,CAAC;IACtD,MAAMJ,MAAM,GAAG;MAAEnB,EAAE,EAAEkB,IAAI,CAACE,GAAG,CAACC;IAAM,CAAC;IACrC,IAAIT,KAAK,CAAClB,+BAA+B,CAAC,CAAC,EAAE;MAC3C,IAAI,CAACe,MAAM,CAACe,KAAK,CAACE,KAAK,EAAEP,MAAM,CAAC;IAClC,CAAC,MAAM,IAAIP,KAAK,CAACnB,8BAA8B,CAAC,CAAC,EAAE;MACjDmB,KAAK,CAACd,sBAAsB,CAAC4B,KAAK,EAAEP,MAAM,CAAC;IAC7C,CAAC,MAAM;MACL;IACF;EACF;EAQAQ,+BAA+BA,CAAC;IAAE3B;EAAqB,CAAC,EAAQ;IAC9D,MAAM;MAAEU;IAAM,CAAC,GAAG,IAAI;IACtB,IAAIY,CAAC,GAAGZ,KAAK,CAACa,MAAM,GAAG,CAAC;IACxB,IAAIX,KAAsB,GAAGF,KAAK,CAACY,CAAC,CAAC;IACrC,OAAOV,KAAK,CAACnB,8BAA8B,CAAC,CAAC,EAAE;MAC7C,IAAImB,KAAK,CAACpB,IAAI,KAAKJ,oCAAoC,EAAE;QACvDwB,KAAK,CAACd,sBAAsB,CAAC8B,kBAAM,CAACC,sBAAsB,EAAE;UAAE7B;QAAG,CAAC,CAAC;MACrE;MACAY,KAAK,GAAGF,KAAK,CAAC,EAAEY,CAAC,CAAC;IACpB;EACF;EAEAQ,iBAAiBA,CAAA,EAAS;IACxB,MAAM;MAAEpB;IAAM,CAAC,GAAG,IAAI;IACtB,MAAMqB,YAAY,GAAGrB,KAAK,CAACA,KAAK,CAACa,MAAM,GAAG,CAAC,CAAC;IAC5C,IAAI,CAACQ,YAAY,CAACtC,8BAA8B,CAAC,CAAC,EAAE;IACpDsC,YAAY,CAAC1B,aAAa,CAAC,CAAC,CAACY,YAAY,EAAEG,GAAG,CAAC,KAAK;MAClD,IAAI,CAACX,MAAM,CAACe,KAAK,CAACP,YAAY,EAAE;QAAEjB,EAAE,EAAEoB;MAAI,CAAC,CAAC;MAE5C,IAAIE,CAAC,GAAGZ,KAAK,CAACa,MAAM,GAAG,CAAC;MACxB,IAAIX,KAAK,GAAGF,KAAK,CAACY,CAAC,CAAC;MACpB,OAAOV,KAAK,CAACnB,8BAA8B,CAAC,CAAC,EAAE;QAC7CmB,KAAK,CAACT,qBAAqB,CAACiB,GAAG,CAACnB,KAAK,CAAC;QACtCW,KAAK,GAAGF,KAAK,CAAC,EAAEY,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;EACJ;AACF;AAACU,OAAA,CAAAC,OAAA,GAAAzB,sBAAA;AAEM,SAAS0B,4BAA4BA,CAAA,EAAG;EAC7C,OAAO,IAAI5C,eAAe,CAACD,qBAAqB,CAAC;AACnD;AAEO,SAAS8C,iBAAiBA,CAAA,EAAG;EAClC,OAAO,IAAIxC,qBAAqB,CAACR,+BAA+B,CAAC;AACnE;AAEO,SAASiD,kBAAkBA,CAAA,EAAG;EACnC,OAAO,IAAIzC,qBAAqB,CAACP,oCAAoC,CAAC;AACxE;AAEO,SAASiD,kBAAkBA,CAAA,EAAG;EACnC,OAAO,IAAI/C,eAAe,CAAC,CAAC;AAC9B"}