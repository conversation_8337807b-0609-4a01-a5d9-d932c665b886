import type { CodegenConfig } from '@graphql-codegen/cli'
import * as fs from 'fs';
import * as path from 'path';

// Check for extended GraphQL paths from the merged-operations.json file
const tempDir = path.join(__dirname, '.temp-graphql');
const mergedOperationsPath = path.join(tempDir, 'merged-operations.json');
let extendedPaths: string[] = [];

if (fs.existsSync(mergedOperationsPath)) {
  try {
    const mergedData = JSON.parse(fs.readFileSync(mergedOperationsPath, 'utf8'));
    if (mergedData?.config?.paths && Array.isArray(mergedData.config.paths)) {
      extendedPaths = mergedData.config.paths;
      console.log(`Found ${extendedPaths.length} extended paths from merged-operations.json`);
    }
  } catch (error) {
    console.error(`Error reading config from ${mergedOperationsPath}:`, error);
  }
}

const config: CodegenConfig = {
    schema: './schema/2025-01/storefront.schema.json',
    // Include base and extended GraphQL files
    documents: [
        './.temp-graphql/**/*.gql',
        ...extendedPaths // Include any extended paths
    ],
    hooks: {
        afterStart: ['node ./scripts/merge-graphql-operations.js'],
    },
    generates: {
        'datasource/api-react-query.ts': {
            plugins: ['typescript', 'typescript-operations', 'typescript-react-query'],
            config: {
                rawRequest: true,
                fetcher: './fetcher#shopifyGqlRequestFetcher',
                exposeQueryKeys: true,
                exposeMutationKeys: true,
                addInfiniteQuery: true,
                exposeDocument: true,
            },
        },
    }
}
export default config