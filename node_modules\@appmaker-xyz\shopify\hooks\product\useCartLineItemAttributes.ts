import { usePageState } from '@appmaker-xyz/core';

type Attributes = {
  [key: string]: string;
};
type SetAttributes = (attributes: Attributes) => void;

const useCartLineItemAttributes = () => {
  const lineItemAttributes = usePageState(
    (state: any) => state._lineItemAttributes || {},
  );
  const setPageState = usePageState((state: any) => state.setPageState);

  const setAttributes: SetAttributes = (attributes) => {
    setPageState({
      _lineItemAttributes: {
        ...lineItemAttributes,
        ...attributes,
      },
    });
  };

  return {
    setAttributes,
    lineItemAttributes,
  };
};

export { useCartLineItemAttributes };
