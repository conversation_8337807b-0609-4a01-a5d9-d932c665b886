import { useEffect } from 'react';

const FormHiddenBlock = ({ attributes, coreDispatch, clientId }) => {
  const { name, value } = attributes;

  function onChangeInput(valueText) {
    coreDispatch({
      type: 'SET_VALUE',
      name,
      value: valueText,
      parent: '_formData',
    });
  }

  useEffect(() => {
    onChangeInput(value);
  }, [value]);

  return null;
};
export default FormHiddenBlock;
