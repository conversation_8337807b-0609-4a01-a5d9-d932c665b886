import { appSettings } from '@appmaker-xyz/core';
import { isCartApiEnabled } from './helper/helper';
const ICONS = {
  WISHLIST: {
    name: 'appmaker/toolbar-icon',
    clientId: 'toolbar-icon-wishlist',
    attributes: {
      __display:
        !appSettings.getOptionAsBoolean('hide_wishlist_on_topbar') &&
        !appSettings.getOptionAsBoolean('hide_wishlist'),
      iconName: 'heart',
      activeColor: appSettings.getOption('primary_button_color'),
      activeTextColor: appSettings.getOption('primary_button_text_color'),
      itemCount:
        '<% try{echo(appStorageState.saved_items_count)}catch(e){echo(0)}%>',
      iconColor: appSettings.getOption('toolbar_text_color'),
      appmakerAction: { action: 'OPEN_WISHLIST' },
    },
  },
  SEARCH: {
    name: 'appmaker/toolbar-icon',
    clientId: 'toolbar-icon-search',
    attributes: {
      __display: !appSettings.getOptionAsBoolean('hide_search'),
      iconName: 'search',
      iconColor: appSettings.getOption('toolbar_text_color'),
      appmakerAction: { action: 'OPEN_SEARCH' },
    },
  },
  LOGIN_PAGE: {
    name: 'appmaker/toolbar-icon',
    clientId: 'toolbar-icon-login',

    attributes: {
      __visibility: (state) => !state.user.isLoggedIn,
      iconName: 'user',
      iconColor: appSettings.getOption('toolbar_text_color'),
      appmakerAction: { action: 'OPEN_LOGIN_PAGE' },
    },
  },
  LOGOUT: {
    name: 'appmaker/toolbar-icon',
    clientId: 'toolbar-icon-log-out',
    attributes: {
      __visibility: (state) => state.user.isLoggedIn,
      iconName: 'log-out',
      iconColor: appSettings.getOption('toolbar_text_color'),
      appmakerAction: {
        action: 'LOGOUT',
        params: { goBack: false },
        // params: { type: 'LOGGED_OUT' },
      },
    },
  },
  MY_ACCOUNT: {
    name: 'appmaker/toolbar-icon',
    clientId: 'toolbar-icon-my-account',
    attributes: {
      __visibility: (state) => state.user.isLoggedIn,
      iconName: 'user',
      iconColor: appSettings.getOption('toolbar_text_color'),
      appmakerAction: { action: 'OPEN_MY_ACCOUNT' },
    },
  },
  CART: {
    name: 'appmaker/toolbar-icon',
    clientId: 'toolbar-icon-cart',
    attributes: {
      __display: !appSettings.getOptionAsBoolean('hide_cart'),
      iconName: 'shopping-cart',
      iconColor: appSettings.getOption('toolbar_text_color'),
      appmakerAction: { action: 'OPEN_CART' },
      itemCount: isCartApiEnabled()
        ? '{{getCartTotalItemsCount(appStorageState.shopifyCart)}}'
        : '{{getCartTotalItemsCount(appStorageState.checkout)}}',
      activeColor: appSettings.getOption('primary_button_color'),
      activeTextColor: appSettings.getOption('primary_button_text_color'),
    },
    dependencies: {
      appStorageState: ['checkout', 'shopifyCart'],
    },
  },
};
export default ICONS;
