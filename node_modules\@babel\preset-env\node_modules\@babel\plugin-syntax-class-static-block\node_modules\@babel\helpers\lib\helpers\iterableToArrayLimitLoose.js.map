{"version": 3, "names": ["_iterableToArrayLimitLoose", "arr", "i", "_i", "Symbol", "iterator", "_arr", "_s", "call", "length", "next", "done", "push", "value"], "sources": ["../../src/helpers/iterableToArrayLimitLoose.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _iterableToArrayLimitLoose(arr, i) {\n  var _i =\n    arr &&\n    ((typeof Symbol !== \"undefined\" && arr[Symbol.iterator]) ||\n      arr[\"@@iterator\"]);\n  if (_i == null) return;\n\n  var _arr = [];\n  var _s;\n  for (_i = _i.call(arr); arr.length < i && !(_s = _i.next()).done; ) {\n    _arr.push(_s.value);\n  }\n  return _arr;\n}\n"], "mappings": ";;;;;;AAEe,SAASA,0BAA0B,CAACC,GAAG,EAAEC,CAAC,EAAE;EACzD,IAAIC,EAAE,GACJF,GAAG,KACD,OAAOG,MAAM,KAAK,WAAW,IAAIH,GAAG,CAACG,MAAM,CAACC,QAAQ,CAAC,IACrDJ,GAAG,CAAC,YAAY,CAAC,CAAC;EACtB,IAAIE,EAAE,IAAI,IAAI,EAAE;EAEhB,IAAIG,IAAI,GAAG,EAAE;EACb,IAAIC,EAAE;EACN,KAAKJ,EAAE,GAAGA,EAAE,CAACK,IAAI,CAACP,GAAG,CAAC,EAAEA,GAAG,CAACQ,MAAM,GAAGP,CAAC,IAAI,CAAC,CAACK,EAAE,GAAGJ,EAAE,CAACO,IAAI,EAAE,EAAEC,IAAI,GAAI;IAClEL,IAAI,CAACM,IAAI,CAACL,EAAE,CAACM,KAAK,CAAC;EACrB;EACA,OAAOP,IAAI;AACb"}