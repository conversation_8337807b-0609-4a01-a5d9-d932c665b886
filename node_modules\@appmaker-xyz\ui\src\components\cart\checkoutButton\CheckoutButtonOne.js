import React, { useState } from 'react';
import { View, I18nManager, Pressable, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { testProps } from '@appmaker-xyz/core';
import { useCart } from '@appmaker-xyz/shopify';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';
import { ThemeText } from '../../atoms';

const CheckoutButtonOne = ({
  clientId,
  show,
  totalPrice,
  subtotalAmount,
  showSubTotalAmount = false,
  onPress,
  viewCartText = 'View Cart',
  __appmakerCustomStyles: customStyles = {},
}) => {
  const {
    totalQuantity,
    cartTotalPayableWithCurrency,
    isCheckoutButtonLoading,
  } = useCart();
  const [loading, setLoading] = useState(false);
  const { styles, theme } = useStyles(stylesheet);

  const ChevronRight = (props) => (
    <Icon name={`chevron-${I18nManager.isRTL ? 'left' : 'right'}`} {...props} />
  );
  const containerStyles = [styles.checkoutButtonContainer];
  if (show === true) {
    containerStyles.push({ bottom: 0 });
  }
  if (customStyles.container) {
    containerStyles.push(customStyles?.container);
  }
  async function _onPress() {
    setLoading(true);
    await onPress();
    setLoading(false);
  }
  return (
    <View style={containerStyles} {...testProps(`${clientId}-container`)}>
      <Pressable
        testId={'checkout-button'}
        disabled={loading || isCheckoutButtonLoading}
        style={styles.checkoutButton}
        onPress={_onPress}
        clientId={clientId}>
        <View>
          <ThemeText
            testId={'checkout-button-items-count'}
            size={'sm'}
            color={
              customStyles?.itemCountText
                ? null
                : theme.colors.primaryButtonText
            }
            style={customStyles?.itemCountText}>
            {totalQuantity}{' '}Items
          </ThemeText>
          <View style={styles.priceDetails}>
            <ThemeText
              testId={'checkout-button-total-amount'}
              fontFamily="bold"
              color={
                customStyles?.itemCountText
                  ? null
                  : theme.colors.primaryButtonText
              }
              style={customStyles?.itemCountText}>
              {cartTotalPayableWithCurrency}
            </ThemeText>
            {showSubTotalAmount == true && totalPrice != subtotalAmount ? (
              <ThemeText
                size="sm"
                color={
                  customStyles?.itemCountText
                    ? null
                    : theme.colors.primaryButtonText
                }
                style={
                  ([styles.subtotalAmountText],
                  {
                    ...customStyles?.itemCountText,
                    ...styles.subtotalAmountText,
                  })
                }>
                {subtotalAmount}
              </ThemeText>
            ) : null}
          </View>
        </View>
        {loading || isCheckoutButtonLoading ? (
          <ActivityIndicator color={theme.colors.primaryButtonText} />
        ) : (
          <ThemeText
            size="md"
            fontFamily="medium"
            color={
              customStyles?.ctaText ? null : theme.colors.primaryButtonText
            }
            clientId={`${clientId}-btn`}
            style={customStyles?.ctaText}>
            {viewCartText}
            <ChevronRight style={customStyles?.ctaText_icon} size={16} />
          </ThemeText>
        )}
      </Pressable>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  checkoutButtonContainer: {
    width: '100%',
    backgroundColor: theme.colors.background,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderTopWidth: 1,
    borderTopColor: theme.colors.separator,
    justifyContent: 'flex-end',
  },
  checkoutButton: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primaryButtonBackground,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: theme.borderRadii.lg,
  },
  priceDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subtotalAmountText: {
    marginLeft: 10,
    textDecorationLine: 'line-through',
    textDecorationStyle: 'solid',
  },
  fontColor: {},
}));

export default CheckoutButtonOne;
