import React from 'react';
import { StyleSheet, Platform, Dimensions, I18nManager } from 'react-native';
import { Layout, ThemeText, AppTouchable } from '@appmaker-xyz/ui';
import { useUser } from '@appmaker-xyz/shopify';
import { getExtensionAsBoolean, getExtensionConfig } from '@appmaker-xyz/core';
import { UserProfile } from '../../../assets/svg';
import Icon from 'react-native-vector-icons/Feather';
import { AppmakerRemoteImage } from '@appmaker-xyz/react-native';

const TextProfile = ({ firstName, lastName }) => {
  const firstLetter = firstName?.charAt(0);
  const lastLetter = lastName?.charAt(0);
  return (
    <Layout style={styles.textProfile}>
      <ThemeText size="xl" fontFamily="medium" color="#94A3B8">
        {firstLetter}
      </ThemeText>
      <ThemeText size="xl" fontFamily="medium" color="#94A3B8">
        {lastLetter}
      </ThemeText>
    </Layout>
  );
};

const DrawerHeader = ({ onAction, ...props }) => {
  const { isLoggedin, user } = useUser(props);
  const { textColor = '#ffffff', bgImg = false } = props.attributes;

  const addMarginTop = getExtensionAsBoolean?.(
    'advanced-styles',
    'drawer_header_margin',
    false,
  );

  const textColorFinal = getExtensionConfig?.(
    'advanced-styles',
    'drawer_header_text_color',
    textColor,
  );

  const firstName = user?.firstName;
  const lastName = user?.lastName;

  return (
    <AppTouchable
      style={[
        styles.wholeContainer,
        addMarginTop ? styles.headerMarginTop : null,
      ]}
      onPress={() => {
        onAction({ action: 'CLOSE_DRAWER' });
        isLoggedin
          ? onAction({ action: 'OPEN_MY_ACCOUNT' })
          : onAction({ action: 'OPEN_LOGIN_PAGE' });
      }}>
      <AppmakerRemoteImage
        name="DRAWER_HEADER"
        style={styles.bgImage}
        background={true}>
        <Layout style={styles.container}>
          {isLoggedin && (firstName || lastName) ? (
            <TextProfile firstName={firstName} lastName={lastName} />
          ) : (
            <UserProfile style={styles.userIcon} width={70} height={70} />
          )}
          {isLoggedin ? (
            firstName || lastName ? (
              <ThemeText
                color={textColorFinal}
                fontFamily="medium"
                size="lg"
                style={styles.text}>
                {firstName} {lastName}
              </ThemeText>
            ) : (
              <ThemeText
                color={textColorFinal}
                fontFamily="medium"
                size="lg"
                style={styles.text}>
                Welcome
              </ThemeText>
            )
          ) : null}
          {isLoggedin ? (
            <Layout style={styles.row}>
              <ThemeText color={textColorFinal} size="sm" fontFamily="medium">
                Account
              </ThemeText>
              <Icon
                name={I18nManager.isRTL ? 'chevron-left' : 'chevron-right'}
                size={12}
                color={textColorFinal}
              />
            </Layout>
          ) : (
            <ThemeText
              color={textColorFinal}
              size="md"
              fontFamily="medium"
              style={styles.text}>
              Login/Register
            </ThemeText>
          )}
        </Layout>
      </AppmakerRemoteImage>
    </AppTouchable>
  );
};

const { height, width } = Dimensions.get('window');
const iosMargin = height / width > 1.8 ? 108 : 72;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
  },
  bgImage: {
    flex: 1,
    backgroundColor: '#212121',
  },
  text: {
    textAlign: 'center',
    marginTop: 8,
  },
  textProfile: {
    width: 70,
    height: 70,
    borderRadius: 50,
    overflow: 'hidden',
    backgroundColor: '#E2E8F0',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerMarginTop: {
    marginTop: Platform.OS === 'ios' ? iosMargin : 56,
  },
});

export default DrawerHeader;
