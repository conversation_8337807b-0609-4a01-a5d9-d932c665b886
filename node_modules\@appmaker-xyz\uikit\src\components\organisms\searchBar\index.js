import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  ActivityIndicator,
  I18nManager,
} from 'react-native';
import { useApThemeState } from '../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { debounce } from 'lodash';
// Used translation
import { useTranslation } from 'react-i18next';

const SearchBar = ({
  defaultText,
  onPress,
  onChange,
  searchLoading,
  setFocus = true,
  topBarView,
  onBack,
  debounceInterval = 500,
  debounceOnChange,
  label,
  wholeContainerStyle,
}) => {
  const { t } = useTranslation();
  const { color, spacing, font, fonts } = useApThemeState();
  const styles = allStyles({ color, spacing, font, fonts });
  const [value, setValue] = useState(defaultText);
  // const [searching, setSearching] = useState(false);
  const containerStyles = [styles.container];

  if (wholeContainerStyle) {
    containerStyles.push(wholeContainerStyle);
  }

  const debounceCallback = (text) => {
    // console.log(text, 'db');
    debounceOnChange(text);
  };
  const [debouncedCallApi] = useState(() =>
    debounce(debounceCallback, debounceInterval),
  );
  const onChangeText = (text) => {
    setValue(text);
    // setSearching(true);
    onChange && onChange(text);
    debouncedCallApi(text);
  };

  // if (topBarView) {
  //   containerStyles.push({
  //     flexDirection: 'row',
  //     padding: spacing.small,
  //     shadowColor: color.dark,
  //     shadowOffset: {
  //       width: 0,
  //       height: 2,
  //     },
  //     shadowOpacity: 0.23,
  //     shadowRadius: 2.62,
  //     elevation: 10,
  //     marginBottom: 4,
  //   });
  // }

  return (
    <View style={[containerStyles]}>
      {/* {topBarView && (
        <Icon
          name={`arrow-${I18nManager.isRTL ? 'right' : 'left'}`}
          style={styles.backIcon}
          onPress={onBack}
        />
      )} */}
      <View style={styles.searchbar}>
        {searchLoading ? (
          <ActivityIndicator
            size={font.size.lg}
            style={styles.icon}
            color={color.dark}
          />
        ) : (
          <Icon name="search" style={styles.icon} />
        )}
        <TextInput
          style={styles.inputArea}
          placeholder={t(label)}
          autoFocus={setFocus}
          onChangeText={onChangeText}
          returnKeyType={'search'}
          onSubmitEditing={() => onPress(value)}
          value={value}
        />
        {value !== '' && (
          <Icon name="x" style={styles.icon} onPress={() => onChangeText('')} />
        )}
      </View>
    </View>
  );
};

const allStyles = ({ spacing, color, font, fonts }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      padding: spacing.base,
      alignItems: 'center',
      height: 70,
    },
    searchbar: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: color.light,
      height: 44,
      borderRadius: spacing.nano,
      paddingHorizontal: spacing.small,
      flex: 1,
    },
    inputArea: {
      flex: 1,
      textAlign: `${I18nManager.isRTL ? 'right' : 'left'}`,
      paddingHorizontal: spacing.small,
      ...fonts.bodyParagraph,
      color: color.dark,
      fontWeight: 'normal',
    },
    icon: {
      fontSize: font.size.lg,
      color: color.dark,
      padding: spacing.nano,
    },
    backIcon: {
      fontSize: 20,
      color: color.dark,
      padding: spacing.small,
      marginRight: spacing.nano,
    },
  });

export default SearchBar;