import { color, spacing } from '../styles';
import { colors as configColors } from '@appmaker-xyz/app-config/newConfig';

const page = {
  title: 'Change Language',
  attributes: {
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: '#FFFFFF',
    },
    contentContainerStyle: {
      flex: 1,
      paddingHorizontal: 6,
      paddingTop: 8,
    },
  },
  blocks: [
    {
      clientId: 'my-account-menu',
      name: 'appmaker/LanguageChooser',
      attributes: {
        containerStyle: {
          flex: 1,
        },
        appmakerAction: {
          params: { language: 'en' },
          action: 'SET_LANGUAGE',
        },
        items: '{{blockItem.languages}}',
        selectedItem: '{{blockItem.language}}',
        // dataSource: {
        //   repeatable: 'Yes',
        //   attributes: {
        //     params: '{{blockData}}',
        //   },
        //   source: '{{blockData}}',
        //   repeatItem: 'DataVariable',
        // },
      },
    },
  ],
};
export default page;
