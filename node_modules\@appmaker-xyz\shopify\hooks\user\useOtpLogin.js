import { useReducer } from 'react';
import { appmaker } from '@appmaker-xyz/core';

const initialState = {
  currentStep: 'send', // 'send' or 'verify'

  phone: null,
  sendStatus: 'idle', // 'loading', 'success', 'error'
  sendErrorMessage: null,
  otpResponse: null,

  otpCode: null,
  verifyStatus: 'idle', // 'loading', 'success', 'error'
  verifyErrorMessage: null,
  verifyResponse: null,

  resendStatus: 'idle', // 'loading', 'success', 'error'
  resendErrorMessage: null,
  canResend: false,

  failedAttemptCount: 0,
  resendCount: 0,
};
const reducer = (state = initialState, action) => {
  switch (action.type) {
    case 'RESET':
      return {
        ...state,
        currentStep: 'send',
        // sendStatus: 'loading',
        phone: action.phone,
        sendErrorMessage: null,
        verifyErrorMessage: null,
        resendCount: 0,
        failedAttemptCount: 0,
      };
    case 'SEND_CODE':
      return {
        ...state,
        currentStep: 'verify',
        sendStatus: 'loading',
        phone: action.phone,
        sendErrorMessage: null,
      };
    case 'SEND_CODE_SUCCESS':
      return {
        ...state,
        sendStatus: 'success',
        otpResponse: action.payload,
        canResend: true,
      };
    case 'SEND_CODE_ERROR':
      return {
        ...state,
        sendStatus: 'error',
        sendErrorMessage: action.error,
      };
    case 'VERIFY_CODE':
      return {
        ...state,
        verifyStatus: 'loading',
        verifyErrorMessage: null,
        otpCode: action.otpCode,
      };
    case 'VERIFY_CODE_SUCCESS':
      return {
        ...state,
        currentStep: action.payload?.nextStep || 'done',
        verifyStatus: 'success',
        verifyResponse: action.payload,
        failedAttemptCount: 0,
      };
    case 'VERIFY_CODE_ERROR':
      return {
        ...state,
        verifyStatus: 'error',
        verifyErrorMessage: action.error,
        failedAttemptCount: state.failedAttemptCount + 1,
        otpCode: '',
      };
    case 'RESEND_CODE':
      return {
        ...state,
        resendStatus: 'loading',
        resendErrorMessage: null,
      };
    case 'RESEND_CODE_SUCCESS':
      return {
        ...state,
        resendStatus: 'success',
        resendCount: state.resendCount + 1,
        otpCode: '',
        otpResponse: action.payload,
      };
    case 'RESEND_CODE_ERROR':
      return {
        ...state,
        resendStatus: 'error',
        resendErrorMessage: action.error,
      };
    default:
      return state;
  }
};
export function useOtpLogin({
  handleAction,
  redirectAction,
  ignoreLoginRedirect = false,
} = {}) {
  const [state, dispatch] = useReducer(reducer, initialState);
  const otpDigitCount = appmaker.applyFilters('appmaker-otp-digit-count', 4);
  const phoneDigitCount = appmaker.applyFilters(
    'appmaker-phone-digit-count',
    10,
  );
  const countryCodes = appmaker.applyFilters(
    'appmaker-otp-login-country-codes',
    null,
  );
  const autoSubmitPhone = appmaker.applyFilters(
    'appmaker-otp-login-auto-submit-phone',
    true,
  );
  async function sendCode(phone, textMobile) {
    if (phone) {
      dispatch({ type: 'SEND_CODE', phone });
      try {
        const { data, error } = await appmaker.applyFilters(
          'appmaker-send-phone-otp',
          {
            phone,
          },
        );
        if (error) {
          return dispatch({ type: 'SEND_CODE_ERROR', error });
        } else {
          dispatch({ type: 'SEND_CODE_SUCCESS', payload: data });
        }
      } catch (error) {
        dispatch({ type: 'SEND_CODE_ERROR', error });
      }
    } else {
      handleAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'Please enter valid mobile number',
        },
      });
    }
  }
  async function verifyCode(otpCode) {
    dispatch({ type: 'VERIFY_CODE', otpCode });
    try {
      const response = await appmaker.applyFilters(
        'appmaker-verify-phone-otp',
        {
          phone: state?.phone,
          otpResponse: state?.otpResponse,
          otpCode,
          redirectAction,
        },
      );
      if (response?.error) {
        const errorMessage =
          typeof response?.error === 'string'
            ? response?.error
            : typeof response?.error?.message === 'string'
            ? response?.error?.message
            : 'Unable to verify otp!';
        handleAction({
          action: 'SHOW_MESSAGE',
          params: { title: errorMessage },
        });
        dispatch({ type: 'VERIFY_CODE_ERROR', error: response.error });
      } else {
        // multipassToken
        if (response?.multipassToken) {
          const multipassToken = response?.multipassToken;
          await handleAction(
            {
              action: 'LOGIN_USER_MULTIPASS',
              params: {
                multipassToken: multipassToken,
                method: 'otp',
                redirectAction,
                ignoreLoginRedirect,
              },
            },
            // deps,
          );
        } else if (response?.nextStep === 'custom_action') {
          response?.customAction && (await handleAction(response.customAction));
        }
        dispatch({ type: 'VERIFY_CODE_SUCCESS', payload: response });
        // Sleep for .5 sec to let the action finish before reset is called. {appmaker-react-native-app/issues/3139}
        await new Promise((resolve) => setTimeout(resolve, 500));
        reset();
      }
    } catch (error) {
      dispatch({ type: 'VERIFY_CODE_ERROR', error });
    }
  }
  async function resendCode(phone) {
    dispatch({ type: 'RESEND_CODE' });
    try {
      // sendCode(number);
      const { data, error } = await appmaker.applyFilters(
        'appmaker-re-send-phone-otp',
        {
          phone,
        },
      );
      if (error) {
        return dispatch({ type: 'RESEND_CODE_ERROR', error });
      }
      dispatch({ type: 'RESEND_CODE_SUCCESS', payload: data });
    } catch (error) {
      console.log('error', error);
      dispatch({ type: 'RESEND_CODE_ERROR', error });
    }
  }

  async function reset() {
    dispatch({ type: 'RESET' });
  }
  return {
    ...state,
    sendCode,
    verifyCode,
    resendCode,
    reset,
    otpDigitCount,
    phoneDigitCount,
    countryCodes,
    autoSubmitPhone,
  };
}
