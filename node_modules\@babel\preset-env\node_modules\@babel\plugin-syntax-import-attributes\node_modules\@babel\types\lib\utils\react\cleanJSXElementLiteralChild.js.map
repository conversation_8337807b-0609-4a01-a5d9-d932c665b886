{"version": 3, "names": ["_generated", "require", "_", "cleanJSXElementLiteralChild", "child", "args", "lines", "value", "split", "lastNonEmptyLine", "i", "length", "match", "str", "line", "isFirstLine", "isLastLine", "isLastNonEmptyLine", "trimmedLine", "replace", "push", "inherits", "stringLiteral"], "sources": ["../../../src/utils/react/cleanJSXElementLiteralChild.ts"], "sourcesContent": ["import { stringLiteral } from \"../../builders/generated\";\nimport type * as t from \"../..\";\nimport { inherits } from \"../..\";\n\nexport default function cleanJSXElementLiteralChild(\n  child: t.JSXText,\n  args: Array<t.Node>,\n) {\n  const lines = child.value.split(/\\r\\n|\\n|\\r/);\n\n  let lastNonEmptyLine = 0;\n\n  for (let i = 0; i < lines.length; i++) {\n    if (lines[i].match(/[^ \\t]/)) {\n      lastNonEmptyLine = i;\n    }\n  }\n\n  let str = \"\";\n\n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i];\n\n    const isFirstLine = i === 0;\n    const isLastLine = i === lines.length - 1;\n    const isLastNonEmptyLine = i === lastNonEmptyLine;\n\n    // replace rendered whitespace tabs with spaces\n    let trimmedLine = line.replace(/\\t/g, \" \");\n\n    // trim whitespace touching a newline\n    if (!isFirstLine) {\n      trimmedLine = trimmedLine.replace(/^[ ]+/, \"\");\n    }\n\n    // trim whitespace touching an endline\n    if (!isLastLine) {\n      trimmedLine = trimmedLine.replace(/[ ]+$/, \"\");\n    }\n\n    if (trimmedLine) {\n      if (!isLastNonEmptyLine) {\n        trimmedLine += \" \";\n      }\n\n      str += trimmedLine;\n    }\n  }\n\n  if (str) args.push(inherits(stringLiteral(str), child));\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,CAAA,GAAAD,OAAA;AAEe,SAASE,2BAA2BA,CACjDC,KAAgB,EAChBC,IAAmB,EACnB;EACA,MAAMC,KAAK,GAAGF,KAAK,CAACG,KAAK,CAACC,KAAK,CAAC,YAAY,CAAC;EAE7C,IAAIC,gBAAgB,GAAG,CAAC;EAExB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIJ,KAAK,CAACI,CAAC,CAAC,CAACE,KAAK,CAAC,QAAQ,CAAC,EAAE;MAC5BH,gBAAgB,GAAGC,CAAC;IACtB;EACF;EAEA,IAAIG,GAAG,GAAG,EAAE;EAEZ,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAMI,IAAI,GAAGR,KAAK,CAACI,CAAC,CAAC;IAErB,MAAMK,WAAW,GAAGL,CAAC,KAAK,CAAC;IAC3B,MAAMM,UAAU,GAAGN,CAAC,KAAKJ,KAAK,CAACK,MAAM,GAAG,CAAC;IACzC,MAAMM,kBAAkB,GAAGP,CAAC,KAAKD,gBAAgB;IAGjD,IAAIS,WAAW,GAAGJ,IAAI,CAACK,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IAG1C,IAAI,CAACJ,WAAW,EAAE;MAChBG,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IAChD;IAGA,IAAI,CAACH,UAAU,EAAE;MACfE,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IAChD;IAEA,IAAID,WAAW,EAAE;MACf,IAAI,CAACD,kBAAkB,EAAE;QACvBC,WAAW,IAAI,GAAG;MACpB;MAEAL,GAAG,IAAIK,WAAW;IACpB;EACF;EAEA,IAAIL,GAAG,EAAER,IAAI,CAACe,IAAI,CAAC,IAAAC,UAAQ,EAAC,IAAAC,wBAAa,EAACT,GAAG,CAAC,EAAET,KAAK,CAAC,CAAC;AACzD"}