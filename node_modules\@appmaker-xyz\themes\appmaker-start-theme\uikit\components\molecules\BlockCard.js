import React from 'react';
import Icon from 'react-native-vector-icons/Feather';
import Layout from '../atoms/Layout';
import ThemeText from '../atoms/ThemeText';

const BlockCard = ({
  color = '#fff',
  textColor = '#000',
  containerStyle,
  textStyle,
  attributes,
  children,
}) => {
  const { title } = attributes;
  const finalContainerStyles = {
    backgroundColor: color,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
    ...containerStyle,
  };
  const finalTextStyles = {
    color: textColor,
    ...textStyle,
  };
  return (
    <Layout style={finalContainerStyles}>
      <ThemeText style={finalTextStyles} size={'lg'} fontFamily={'medium'}>
        {title}
      </ThemeText>
      <Icon name="chevron-right" size={18} />
      {children}
    </Layout>
  );
};

export default BlockCard;
