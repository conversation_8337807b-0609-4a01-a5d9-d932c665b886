{"version": 3, "names": ["_index", "require", "_removeTypeDuplicates", "_index2", "createTSUnionType", "typeAnnotations", "types", "map", "type", "isTSTypeAnnotation", "typeAnnotation", "flattened", "removeTypeDuplicates", "length", "tsUnionType"], "sources": ["../../../src/builders/typescript/createTSUnionType.ts"], "sourcesContent": ["import { tsUnionType } from \"../generated/index.ts\";\nimport removeTypeDuplicates from \"../../modifications/typescript/removeTypeDuplicates.ts\";\nimport { isTSTypeAnnotation } from \"../../validators/generated/index.ts\";\nimport type * as t from \"../../index.ts\";\n\n/**\n * Takes an array of `types` and flattens them, removing duplicates and\n * returns a `UnionTypeAnnotation` node containing them.\n */\nexport default function createTSUnionType(\n  typeAnnotations: Array<t.TSTypeAnnotation | t.TSType>,\n): t.TSType {\n  const types = typeAnnotations.map(type => {\n    return isTSTypeAnnotation(type) ? type.typeAnnotation : type;\n  });\n  const flattened = removeTypeDuplicates(types);\n\n  if (flattened.length === 1) {\n    return flattened[0];\n  } else {\n    return tsUnionType(flattened);\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AAOe,SAASG,iBAAiBA,CACvCC,eAAqD,EAC3C;EACV,MAAMC,KAAK,GAAGD,eAAe,CAACE,GAAG,CAACC,IAAI,IAAI;IACxC,OAAO,IAAAC,0BAAkB,EAACD,IAAI,CAAC,GAAGA,IAAI,CAACE,cAAc,GAAGF,IAAI;EAC9D,CAAC,CAAC;EACF,MAAMG,SAAS,GAAG,IAAAC,6BAAoB,EAACN,KAAK,CAAC;EAE7C,IAAIK,SAAS,CAACE,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAOF,SAAS,CAAC,CAAC,CAAC;EACrB,CAAC,MAAM;IACL,OAAO,IAAAG,kBAAW,EAACH,SAAS,CAAC;EAC/B;AACF"}