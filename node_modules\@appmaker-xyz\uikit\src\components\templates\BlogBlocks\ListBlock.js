import React from 'react';
import { StyleSheet } from 'react-native';
import { spacing } from '../../../styles';
import { BlockCard } from '../../../components';
import PostCard from './components/PostCard';

const CardItem = ({ attributes, onPress }) => {
  const { data, type, reverse } = attributes;
  return (
    <PostCard
      attributes={{
        type: type,
        titleMeta: data.titleMeta,
        title: data.title,
        featureImg: data.featureImg,
        coverLetter: data.coverLetter,
        excerpt: data.excerpt,
        authorName: data.authorName,
        timeStamp: data.timeStamp,
        reverse: reverse,
      }}
      onPress={onPress}
    />
  );
};

const ListBlock = ({ attributes }) => {
  const { data, type, title, accessButton, reverse } = attributes;
  return (
    <BlockCard
      attributes={{
        title: title,
        accessButton: accessButton,
        childContainerStyle: styles.verticalContainer,
      }}>
      {data.map((item) => (
        <CardItem attributes={{ data: item, type: type, reverse: reverse }} />
      ))}
    </BlockCard>
  );
};

const styles = StyleSheet.create({
  verticalContainer: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.base,
  },
});

export default ListBlock;
