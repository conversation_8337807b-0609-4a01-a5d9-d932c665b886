import { appPluginStore<PERSON>pi } from '@appmaker-xyz/core';
import { shopifyIdHelper } from '@appmaker-xyz/shopify';
import { getStoreDomain } from '../../helper/shop';
import { syncCartById } from '../cartApi';
import { cartHelpers } from '../../helper/cartHelper';
export async function saveCartToLocal(response, { coreDispatch }) {
  coreDispatch({
    type: 'SET_APP_VAR_LOCAL',
    name: 'checkout',
    value: response,
  });
}

export async function syncCartFromId(cartId) {
  try {
    const cart = await syncCartById(cartId);
    if(cart) {
      cartHelpers.saveCart(cart);
      return cart;
    }
  } catch (error) {
    console.log('Cart Synced error' + error);
  }
}

export const generateCartPermalink = ({ lines: lineItems }) => {
  let variantIds = [];
  if (!lineItems?.length || lineItems?.length === 0) return;
  variantIds = lineItems?.map(
    (lineItem) =>
      `${shopifyIdHelper(lineItem?.node?.variant?.id, true)}:${
        lineItem?.node?.quantity
      }`,
  );
  if (variantIds?.length > 0) {
    const cartPermaLink = `${getStoreDomain()}/cart/${variantIds.join?.(
      ',',
    )}?storefront=true`;
    return cartPermaLink;
  }
  return;
};

export async function manageCart({
  cartProducts,
  // response,
  dataSource,
  appId,
  coreDispatch,
  params,
  runDataSource,
  selectedVariant,
}) {
  if (cartProducts?.lines?.edges?.length > 0) {
    let newLineItems = [];
    cartProducts.lines.edges.map(({ node }) => {
      if (
        (params.quantity === 0 && node.id != params.product.id) ||
        params.quantity != 0
      ) {
        newLineItems.push({
          // id: node.id,
          quantity: node.quantity,
          variantId: node.variant?.id,
        });
      }
    });
    if (params.quantity > 0) {
      newLineItems.push({
        quantity: params.quantity,
        variantId: selectedVariant.id,
      });
    }
    let updateCheckoutMethod = {
      methodName: 'checkoutCreate',
      params: {
        checkoutId: null,
        lineItems: newLineItems,
      },
    };
    const [response] = await runDataSource(
      {
        dataSource,
        appId,
      },
      updateCheckoutMethod,
    );
    let checkout = response.data?.data?.checkoutCreate?.checkout;
    return checkout;
  }
}

export function isNativeCheckout() {
  try {
    const enable_native_checkout =
      appPluginStoreApi().getState().plugins?.shopify?.settings
        ?.enable_native_checkout;
    if (
      enable_native_checkout === true ||
      enable_native_checkout === 1 ||
      enable_native_checkout === '1'
    ) {
      return true;
    }
  } catch (error) {
    console.log(error, 'error');
  }
  return false;
}

export function isNativeShippingMethodChooser() {
  try {
    const enable_native_checkout =
      appPluginStoreApi().getState().plugins?.shopify?.settings
        ?.enable_shipping_method_native;
    if (enable_native_checkout) {
      return true;
    }
  } catch (error) {
    console.log(error, 'error');
  }
  return false;
}

export function isGuestCheckoutAllowed() {
  try {
    const enable_guest_checkout =
      appPluginStoreApi().getState().plugins?.shopify?.settings
        ?.enable_guest_checkout;
    if (enable_guest_checkout === true) {
      return true;
    }
  } catch (error) {
    console.log(error, 'error');
  }
  return false;
}
/*
{
  lineItemsToAdd = [],
  lineItemsToRemove = [],
  lineItemsToUpdate = [],
}
*/
async function setShippingMethod({
  checkoutId,
  shippingRateHandle,
  runDataSource,
}) {
  const dataSource = {
    attributes: {},
    source: 'shopify',
  };
  const [response] = await runDataSource(
    { dataSource },
    {
      methodName: 'checkoutShippingLineUpdate',
      params: {
        shippingRateHandle,
        checkoutId,
      },
    },
  );
  return response;
}
async function getShippingMethods({
  checkoutId,
  shippingRateHandle,
  runDataSource,
}) {
  const dataSource = {
    attributes: {},
    source: 'shopify',
  };
  const [response] = await runDataSource(
    { dataSource },
    {
      methodName: 'availableShippingRates',
      params: checkoutId,
    },
  );
  return response.data.data.node.availableShippingRates.shippingRates;
}

export async function autoSelectShippingMethod(checkout, dependencies) {
  const { runDataSource } = dependencies;
  try {
    const auto_select_shipping_method =
      appPluginStoreApi().getState().plugins?.shopify?.settings
        ?.auto_select_shipping_method;
    if (auto_select_shipping_method !== true) {
      return false;
    }
    const shippingMethods = await getShippingMethods({
      checkoutId: checkout.id,
      runDataSource,
    });
    if (shippingMethods?.length === 1) {
      const [shippingMethod] = shippingMethods;
      const setShipping = await setShippingMethod({
        checkoutId: checkout.id,
        shippingRateHandle: shippingMethod.handle,
        runDataSource,
      });
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.log(error, 'error');
  }
  return false;
}
