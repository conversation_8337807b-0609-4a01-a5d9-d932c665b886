import React, { useState } from 'react';
import { View, Pressable, ActivityIndicator, I18nManager } from 'react-native';
import { useDiscount } from '@appmaker-xyz/shopify';
import { PercentageIcon } from './assets/svg';
import Icon from 'react-native-vector-icons/Feather';
import CouponSuccess from './CouponSuccess';
import { testProps } from '@appmaker-xyz/core';
import { usePageState } from '@appmaker-xyz/core';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';
import { ThemeText as Text } from '@appmaker-xyz/ui';

const ApplyCoupon = (props) => {
  const {
    onRemoveCoupon,
    couponTitle,
    couponTitleDisplay,
    hasCouponApplied,
    couponMessage,
    isCouponRemovingLoading,
    canShowSuccessMessage,
    dissmissSuccessMessage,
  } = useDiscount(props);
  const setPageState = usePageState((state) => state.setPageState);
  const applyCouponModalVisible = usePageState(
    (state) => state.applyCouponModalVisible,
  );
  const { styles, theme } = useStyles(stylesheet);

  const finalCodeDisplay =
    couponTitleDisplay.trim().length > 20
      ? couponTitleDisplay.trim().substring(0, 20) + '...'
      : couponTitleDisplay.trim();

  return (
    <>
      {hasCouponApplied ? (
        <View style={styles.container}>
          <PercentageIcon height={42} width={42} color={theme.colors.accent} />
          <View style={styles.textContainer}>
            <Text {...testProps('coupon-applied-text')} style={styles.textBold}>
              {`${finalCodeDisplay} Applied`}
            </Text>
            {couponMessage ? (
              <Text style={styles.textRegular}>{couponMessage}</Text>
            ) : null}
          </View>
          <Pressable
            {...testProps('coupon-remove-button')}
            onPress={onRemoveCoupon}>
            {isCouponRemovingLoading ? (
              <ActivityIndicator size={'small'} color={theme.colors.error} />
            ) : (
              <Text style={styles.textRemove}>Remove</Text>
            )}
          </Pressable>
        </View>
      ) : (
        <Pressable
          style={styles.container}
          onPress={() =>
            setPageState({ applyCouponModalVisible: !applyCouponModalVisible })
          }
          {...testProps('apply-coupon-button')}>
          <PercentageIcon height={36} width={36} color={theme.colors.accent} />
          <View style={styles.textContainer}>
            <Text style={styles.textBold}>Apply Coupon</Text>
            {/* <Text style={styles.textRegular}>
              You can save upto ₹499 on this order
            </Text> */}
          </View>
          <Icon
            name={I18nManager.isRTL ? 'chevron-left' : 'chevron-right'}
            size={24}
            color={theme.colors.text}
          />
        </Pressable>
      )}
      <CouponSuccess
        showModel={canShowSuccessMessage}
        couponTitle={couponTitle}
        couponTitleDisplay={couponTitleDisplay}
        closeModal={dissmissSuccessMessage}
      />
    </>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    padding: 12,
    marginHorizontal: 12,
    marginVertical: 6,
    borderRadius: 16,
  },
  textContainer: {
    flexGrow: 1,
    flexShrink: 1,
    marginHorizontal: 10,
  },
  textBold: {
    fontSize: 16,
    fontFamily: theme.fontFamily.bold,
    color: theme.colors.text,
  },
  textRegular: {
    fontSize: 14,
    fontFamily: theme.fontFamily.regular,
    color: '#30A479',
  },
  textRemove: {
    color: '#E71919',
    fontSize: 14,
    fontFamily: theme.fontFamily.regular,
  },
}));

export default ApplyCoupon;
