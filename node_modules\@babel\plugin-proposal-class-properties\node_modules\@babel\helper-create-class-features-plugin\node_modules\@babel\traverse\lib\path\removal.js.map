{"version": 3, "names": ["_removal<PERSON>ooks", "require", "_cache", "_index", "remove", "_this$opts", "_assertUnremoved", "resync", "opts", "noScope", "_removeFromScope", "_callRemovalHooks", "_mark<PERSON><PERSON>oved", "shareCommentsWithSiblings", "_remove", "bindings", "getBindingIdentifiers", "Object", "keys", "for<PERSON>ach", "name", "scope", "removeBinding", "fn", "hooks", "parentPath", "Array", "isArray", "container", "splice", "key", "updateSiblingKeys", "_replaceWith", "_traverseFlags", "SHOULD_SKIP", "REMOVED", "parent", "pathCache", "get", "delete", "node", "removed", "buildCodeFrameError"], "sources": ["../../src/path/removal.ts"], "sourcesContent": ["// This file contains methods responsible for removing a node.\n\nimport { hooks } from \"./lib/removal-hooks\";\nimport { path as pathCache } from \"../cache\";\nimport type NodePath from \"./index\";\nimport { REMOVED, SHOULD_SKIP } from \"./index\";\n\nexport function remove(this: NodePath) {\n  this._assertUnremoved();\n\n  this.resync();\n  if (!this.opts?.noScope) {\n    this._removeFromScope();\n  }\n\n  if (this._callRemovalHooks()) {\n    this._markRemoved();\n    return;\n  }\n\n  this.shareCommentsWithSiblings();\n  this._remove();\n  this._markRemoved();\n}\n\nexport function _removeFromScope(this: NodePath) {\n  const bindings = this.getBindingIdentifiers();\n  Object.keys(bindings).forEach(name => this.scope.removeBinding(name));\n}\n\nexport function _callRemovalHooks(this: NodePath) {\n  for (const fn of hooks) {\n    if (fn(this, this.parentPath)) return true;\n  }\n}\n\nexport function _remove(this: NodePath) {\n  if (Array.isArray(this.container)) {\n    this.container.splice(this.key as number, 1);\n    this.updateSiblingKeys(this.key as number, -1);\n  } else {\n    this._replaceWith(null);\n  }\n}\n\nexport function _markRemoved(this: NodePath) {\n  // this.shouldSkip = true; this.removed = true;\n  this._traverseFlags |= SHOULD_SKIP | REMOVED;\n  if (this.parent) pathCache.get(this.parent).delete(this.node);\n  this.node = null;\n}\n\nexport function _assertUnremoved(this: NodePath) {\n  if (this.removed) {\n    throw this.buildCodeFrameError(\n      \"NodePath has been removed so is read-only.\",\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAEA,IAAAA,aAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAEA,IAAAE,MAAA,GAAAF,OAAA;AAEO,SAASG,MAAMA,CAAA,EAAiB;EAAA,IAAAC,UAAA;EACrC,IAAI,CAACC,gBAAgB,EAAE;EAEvB,IAAI,CAACC,MAAM,EAAE;EACb,IAAI,GAAAF,UAAA,GAAC,IAAI,CAACG,IAAI,aAATH,UAAA,CAAWI,OAAO,GAAE;IACvB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA,IAAI,IAAI,CAACC,iBAAiB,EAAE,EAAE;IAC5B,IAAI,CAACC,YAAY,EAAE;IACnB;EACF;EAEA,IAAI,CAACC,yBAAyB,EAAE;EAChC,IAAI,CAACC,OAAO,EAAE;EACd,IAAI,CAACF,YAAY,EAAE;AACrB;AAEO,SAASF,gBAAgBA,CAAA,EAAiB;EAC/C,MAAMK,QAAQ,GAAG,IAAI,CAACC,qBAAqB,EAAE;EAC7CC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,OAAO,CAACC,IAAI,IAAI,IAAI,CAACC,KAAK,CAACC,aAAa,CAACF,IAAI,CAAC,CAAC;AACvE;AAEO,SAAST,iBAAiBA,CAAA,EAAiB;EAChD,KAAK,MAAMY,EAAE,IAAIC,mBAAK,EAAE;IACtB,IAAID,EAAE,CAAC,IAAI,EAAE,IAAI,CAACE,UAAU,CAAC,EAAE,OAAO,IAAI;EAC5C;AACF;AAEO,SAASX,OAAOA,CAAA,EAAiB;EACtC,IAAIY,KAAK,CAACC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,EAAE;IACjC,IAAI,CAACA,SAAS,CAACC,MAAM,CAAC,IAAI,CAACC,GAAG,EAAY,CAAC,CAAC;IAC5C,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACD,GAAG,EAAY,CAAC,CAAC,CAAC;EAChD,CAAC,MAAM;IACL,IAAI,CAACE,YAAY,CAAC,IAAI,CAAC;EACzB;AACF;AAEO,SAASpB,YAAYA,CAAA,EAAiB;EAE3C,IAAI,CAACqB,cAAc,IAAIC,kBAAW,GAAGC,cAAO;EAC5C,IAAI,IAAI,CAACC,MAAM,EAAEC,WAAS,CAACC,GAAG,CAAC,IAAI,CAACF,MAAM,CAAC,CAACG,MAAM,CAAC,IAAI,CAACC,IAAI,CAAC;EAC7D,IAAI,CAACA,IAAI,GAAG,IAAI;AAClB;AAEO,SAASlC,gBAAgBA,CAAA,EAAiB;EAC/C,IAAI,IAAI,CAACmC,OAAO,EAAE;IAChB,MAAM,IAAI,CAACC,mBAAmB,CAC5B,4CAA4C,CAC7C;EACH;AACF"}