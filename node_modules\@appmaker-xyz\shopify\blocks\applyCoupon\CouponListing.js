import React from 'react';
import {
  FlatList,
  Pressable,
  View,
  TextInput,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import { ThemeText as Text } from '@appmaker-xyz/ui';
import Icon from 'react-native-vector-icons/Feather';
import Modal from 'react-native-modal';
import CouponBlock from './CouponBlock';
import { testProps, usePageState } from '@appmaker-xyz/core';
import { useDiscount } from '@appmaker-xyz/shopify';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';
import { useTranslation } from 'react-i18next';

function EmptyState() {
  const { styles, theme } = useStyles(stylesheet);
  return (
    <View style={styles.noCoupon}>
      <Icon name="tag" size={50} color={`${theme.colors.text}66`} />
      <Text style={styles.noCouponText}>
        No coupons available at the moment
      </Text>
    </View>
  );
}

const CouponListing = (props) => {
  const [inputCoupon, onChangeInputCoupon] = React.useState();
  const {
    couponsList,
    onApplyCoupon,
    isCouponApplyingLoading,
    cartSubTotalAmount,
    cartTotalQuantity,
  } = useDiscount(props);
  const { styles, theme } = useStyles(stylesheet);
  const { t } = useTranslation();

  const applyCouponModalVisible = usePageState(
    (state) => state.applyCouponModalVisible,
  );
  const setPageState = usePageState((state) => state.setPageState);

  async function applyCoupon() {
    try {
      // trim input coupon
      await onApplyCoupon(inputCoupon, {
        referrer: {
          name: 'coupon-list-page',
          type: 'text-input',
          title: 'Apply',
        },
      });
    } catch (error) {
      console.log('coupon input error', error);
    }
  }
  const renderCoupon = ({ item, index }) => {
    return (
      <CouponBlock
        testId={`coupon-item-${index}`}
        item={item}
        onApply={onApplyCoupon}
        cartSubTotalAmount={cartSubTotalAmount}
        cartTotalQuantity={cartTotalQuantity}
      />
    );
  };
  const onCancelCoupon = () => {
    setPageState({ applyCouponModalVisible: false });
  };
  return (
    <View style={styles.mainContainer}>
      <Modal
        isVisible={applyCouponModalVisible}
        style={styles.modal}
        onDismiss={onCancelCoupon}
        onRequestClose={onCancelCoupon}>
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.modalContainer}>
            <View style={styles.header}>
              <View style={styles.topBar}>
                <Pressable
                  {...testProps('close-coupon')}
                  style={styles.back}
                  onPress={onCancelCoupon}>
                  <Icon name="x" size={20} color={theme.colors.text} />
                </Pressable>
                <Text style={styles.applyCouponText}>APPLY COUPON</Text>
              </View>
              <View style={styles.textInput}>
                <TextInput
                  {...testProps('coupon-input')}
                  style={styles.input}
                  placeholder={t('Enter Coupon Code')}
                  value={inputCoupon}
                  onChangeText={onChangeInputCoupon}
                  placeholderTextColor={theme.colors.placeholder}
                />
                <Pressable
                  onPress={applyCoupon}
                  {...testProps('submit-coupon')}>
                  {isCouponApplyingLoading ? (
                    <ActivityIndicator
                      size={'small'}
                      color={theme.colors.text}
                    />
                  ) : (
                    <Text
                      style={[
                        styles.applyText,
                        {
                          color: inputCoupon
                            ? theme.colors.text
                            : `${theme.colors.text}66`,
                        },
                      ]}>
                      APPLY
                    </Text>
                  )}
                </Pressable>
              </View>
            </View>
            <Text style={styles.textTopTitle}>Coupons</Text>
            <FlatList
              data={couponsList}
              ListEmptyComponent={EmptyState}
              renderItem={renderCoupon}
              contentContainerStyle={styles.containerStyle}
              keyExtractor={(item, index) => index.toString()}
            />
          </View>
        </SafeAreaView>
      </Modal>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  // main View
  mainContainer: {
    flex: 1,
    margin: 0,
    padding: 0,
    // backgroundColor: 'red',
  },
  modal: {
    margin: 0,
    backgroundColor: theme.colors.pageBackground,
    // maxHeight: 500,
  },
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalContainer: {
    backgroundColor: theme.colors.pageBackground,
    flex: 1,
    margin: 0,
    padding: 0,
    width: '100%',
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  back: {
    padding: 6,
    backgroundColor: theme.colors.pageBackground,
    borderRadius: 18,
    marginRight: 12,
  },
  header: {
    backgroundColor: theme.colors.background,
    paddingHorizontal: 12,
    paddingVertical: 16,
    marginBottom: 12,
  },
  textInput: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#A8ADB0',
    paddingRight: 16,
    borderRadius: 14,
  },
  input: {
    paddingHorizontal: 16,
    height: 48,
    flexGrow: 1,
    color: theme.colors.text,
  },

  // container style
  containerStyle: {
    paddingHorizontal: 12,
    paddingVertical: 12,
  },

  // text style
  textTopTitle: {
    color: theme.colors.text,
    fontSize: 16,
    paddingHorizontal: 12,
    paddingBottom: 6,
    fontFamily: theme.fontFamily.bold,
  },
  applyCouponText: {
    fontFamily: theme.fontFamily.bold,

    color: theme.colors.text,
  },
  applyText: {
    fontFamily: theme.fontFamily.bold,
  },
  noCoupon: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 24,
    borderRadius: 16,
    borderColor: `${theme.colors.text}66`,
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  noCouponText: {
    textAlign: 'center',
    fontFamily: theme.fontFamily.regular,
    marginTop: 12,
    color: `${theme.colors.text}66`,
  },
}));

export default CouponListing;
