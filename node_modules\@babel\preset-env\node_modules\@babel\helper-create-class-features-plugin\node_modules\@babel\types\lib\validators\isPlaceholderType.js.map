{"version": 3, "names": ["_definitions", "require", "isPlaceholderType", "placeholderType", "targetType", "aliases", "PLACEHOLDERS_ALIAS", "alias"], "sources": ["../../src/validators/isPlaceholderType.ts"], "sourcesContent": ["import { PLACEHOLDERS_ALIAS } from \"../definitions\";\n\n/**\n * Test if a `placeholderType` is a `targetType` or if `targetType` is an alias of `placeholderType`.\n */\nexport default function isPlaceholderType(\n  placeholderType: string,\n  targetType: string,\n): boolean {\n  if (placeholderType === targetType) return true;\n\n  const aliases: Array<string> | undefined =\n    PLACEHOLDERS_ALIAS[placeholderType];\n  if (aliases) {\n    for (const alias of aliases) {\n      if (targetType === alias) return true;\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAKe,SAASC,iBAAiBA,CACvCC,eAAuB,EACvBC,UAAkB,EACT;EACT,IAAID,eAAe,KAAKC,UAAU,EAAE,OAAO,IAAI;EAE/C,MAAMC,OAAkC,GACtCC,+BAAkB,CAACH,eAAe,CAAC;EACrC,IAAIE,OAAO,EAAE;IACX,KAAK,MAAME,KAAK,IAAIF,OAAO,EAAE;MAC3B,IAAID,UAAU,KAAKG,KAAK,EAAE,OAAO,IAAI;IACvC;EACF;EAEA,OAAO,KAAK;AACd"}