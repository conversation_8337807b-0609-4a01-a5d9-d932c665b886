import { useEffect, useMemo } from 'react';
import { Product, ProductOption, ProductVariant } from './../../datasource/api-client';
import { useImmer } from 'use-immer'
type ShopifyProductVariantArgs = {
    product: Product,
    useDefaultVariant?: boolean
}
type ShopifyProductVariantData = {
    options: ProductOption[],
    selectedVariant: ProductVariant | undefined,
}
type ShopifyProductVariantActions = {
    setOption: (name: string, value: string) => void
    getUnselectedOptions: () => ProductOption[]
}
export type SelectedOptions = { [key: string]: string }
type ShopifyProductVariantState = {
    selectedOptions: SelectedOptions
}
type ShopifyProductVariant = ShopifyProductVariantData & ShopifyProductVariantActions & ShopifyProductVariantState;
function getSelectedVariant(product: Product, selectedOptions: { [key: string]: string }): ProductVariant | undefined {
    const selectedVariant = product?.variants?.edges.find((variant) => {
        return variant?.node?.selectedOptions.every((selectedOption) => {
            return selectedOptions[selectedOption.name] === selectedOption.value;
        });
    });
    return selectedVariant;
}
function getInitialSelectedOptions(product: Product): { [key: string]: string } {
    const selectedOptions: SelectedOptions = product?.options?.reduce((selectedOptions: SelectedOptions, option) => {
        selectedOptions[option.name] = option.values[0];
        return selectedOptions;
    }, {});
    return selectedOptions;
}
function getInitialSelectedSingleOptions(product: Product): { [key: string]: string } {
    const selectedOptions: SelectedOptions = product?.options?.reduce((selectedOptions: SelectedOptions, option) => {
        if (option.values.length === 1) selectedOptions[option.name] = option.values[0];
        return selectedOptions;
    }, {});
    return selectedOptions;
}
function useSelectOptions() {
    const [state, setState] = useImmer<ShopifyProductVariantState>({
        selectedOptions: {}
    });
    const setOption = (name: string, value: string) => {
        setState(draft => {
            draft.selectedOptions[name] = value;
        });
    }
    const setOptions = (options: SelectedOptions) => {
        setState(draft => {
            draft.selectedOptions = options;
        });
    }
    const isAnyOptionSelected = () => {
        return Object.keys(state.selectedOptions).length > 0;
    }
    return {
        selectedOptions: state.selectedOptions,
        setOption,
        setOptions,
        isAnyOptionSelected
    }

}
function getNonSelectedOptions(productOptions: ProductOption[], selectedOptions: SelectedOptions): ProductOption[] {
    return productOptions.filter(option => selectedOptions[option.name] === undefined);
}

export function useShopifyProductVariants(args: ShopifyProductVariantArgs): ShopifyProductVariant {
    const { product, useDefaultVariant } = args;
    const { selectedOptions, setOption, setOptions, isAnyOptionSelected } = useSelectOptions();
    const options = product.options;
    useEffect(() => {
        console.log('useShopifyProductVariants');

        if (isAnyOptionSelected()) return;

        let initialSelectedOptions;

        if (useDefaultVariant) {
            initialSelectedOptions = getInitialSelectedOptions(product);
        } else {
            // You can set the initialSelectedOptions to an empty object or however you want it to be if useDefaultVariant is false.
            initialSelectedOptions = getInitialSelectedSingleOptions(product);
        }

        setOptions(initialSelectedOptions);

    }, [product, useDefaultVariant]);
    const selectedVariant = useMemo(() => getSelectedVariant(product, selectedOptions), [product, selectedOptions]);
    const getUnselectedOptions = () => {
        console.log(JSON.stringify(selectedOptions, null, 2));

        return getNonSelectedOptions(options, selectedOptions);
    }
    return {
        options,
        setOption,
        selectedOptions: selectedOptions,
        selectedVariant,
        getUnselectedOptions,
    };
}
