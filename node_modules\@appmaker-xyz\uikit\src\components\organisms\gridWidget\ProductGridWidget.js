import React, { useState, useEffect } from 'react';
import { StyleSheet, ActivityIndicator } from 'react-native';
import { appSettings } from '@appmaker-xyz/core';
import { useProductListItem, useProductWishList } from '@appmaker-xyz/shopify';

import {
  Layout,
  AppmakerText,
  Button,
  AppTouchable,
  Badge,
  Stepper,
  AppImage,
} from '../../../components';
import { useApThemeState } from '../../../theme/ThemeContext';
import Icon from '@appmaker-xyz/uikit/Icons/AntDesign';
import { appmaker } from '@appmaker-xyz/core';
import { useProductHook } from './useProductHook';
const PlusIcon = (props) => <Icon name="plus" color="#fff" {...props} />;
const LoadingIndicator = () => <ActivityIndicator size="small" color="#fff" />;
const ProductGridWidget = ({
  clientId,
  attributes,
  pageDispatch,
  onPress,
  onAction,
  blockData,
  data = {},
}) => {
  // console.log(appmaker.applyFilters);

  const ReviewsComp = appmaker.applyFilters(
    'appmaker-product-grid-reviews',
    () => null,
  );
  const {
    priceSale = '0',
    wishList,
    groceryMode = appSettings.getOptionAsBoolean('grocery_mode'),
    easyAddCart = false,
    numColumns,
    attribute,
    appmakerAction,
    size = '', // sm for small
    brandName,
    titleNumberOfLines,
    __appmakerCustomStyles = {},
    addtocartButton = false,
  } = attributes;
  const numberOfLines = parseFloat(titleNumberOfLines);
  //let imageRatio = thumbnail_meta.height / thumbnail_meta.width;
  const customHeight = gridViewListing ? 186 : 180;

  // const [currentQuantity, setCurrentQuantity] = useState(quatity);
  const { toggleWishList, isSaved } = useProductWishList({
    onAction,
    blockData,
  });
  const {
    gridViewListing,
    average_rating,
    productType,
    imageRatio: ratio,
    salePrice,
    regularPrice,
    onSale,
    salePercentage,
    outOfStock,
    // wishList,
    imageUrl: uri,
    title,
    addToCart,
    count,
    updateCart,
    isAddToCartLoading,
    openProduct,
  } = useProductListItem({ attributes, onAction, blockData, pageDispatch });
  let imageRatio = ratio;
  imageRatio = appmaker.applyFilters('product-grid-image-ratio', imageRatio);

  const in_stock = !outOfStock;
  // console.log('Hook title - ', count, titleTwo);
  const ratingBadgeColor = () => {
    let rating = average_rating;
    switch (true) {
      case rating >= '3.5':
        return 'success';
      case rating >= '2.5':
        return 'warining';
      default:
        return 'dark';
    }
  };
  const { color, spacing, font } = useApThemeState();
  const styles = allStyles({
    color,
    spacing,
    imageRatio,
    customHeight,
    __appmakerCustomStyles,
  });
  const containerStyle = [styles.container];
  const imageContainerStyle = [styles.imageContainer];
  const contentContainerStyle = [styles.contentContainer];

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }

  let attributeView = null;
  if (attribute) {
    attributeView = attribute.map((value) => {
      return (
        <Layout style={styles.attributesListing}>
          <Badge
            text={value.label}
            status="demiDark"
            style={styles.attributeBadgeStyle}
            customStyles={__appmakerCustomStyles?.attributes_badge}
          />
        </Layout>
      );
    });
  }
  if (size === 'sm') {
    imageContainerStyle.push({
      width: 100,
      height: imageRatio ? imageRatio * 80 : 100,
    });
    contentContainerStyle.push({ width: 100 });
  }

  if (gridViewListing === true) {
    containerStyle.push({ marginLeft: 0 });
    imageContainerStyle.push({ width: '100%' });
    contentContainerStyle.push({ width: '100%' });
  }
  if (__appmakerCustomStyles) {
    containerStyle.push(__appmakerCustomStyles?.container);
    imageContainerStyle.push(__appmakerCustomStyles?.image_container);
    contentContainerStyle.push(__appmakerCustomStyles?.content_container);
  }

  //bookmarksList[data.id] != undefined
  return (
    <AppTouchable
      onPress={openProduct}
      style={
        gridViewListing
          ? {
              width: `${100 / numColumns}%`,
              padding: spacing.nano,
            }
          : null
      }
      clientId={clientId}>
      <Layout style={containerStyle}>
        <Layout style={imageContainerStyle}>
          <AppImage
            uri={uri}
            style={[
              styles.scrollerImage,
              __appmakerCustomStyles?.product_image,
            ]}
          />
          <Layout
            style={[
              styles.topLeftBlocks,
              __appmakerCustomStyles?.top_left_block_container,
            ]}>
            {onSale && salePercentage ? (
              <Badge
                style={styles.saleBadgeStyle}
                status="success"
                text={`${salePercentage} OFF`}
                customStyles={__appmakerCustomStyles?.sale_badge}
              />
            ) : null}
            {average_rating && average_rating > 0 && (
              <Badge
                style={styles.saleBadgeStyle}
                text={average_rating}
                status={ratingBadgeColor()}
                iconName="star"
                customStyles={__appmakerCustomStyles?.rating_badge}
              />
            )}
            {ReviewsComp && <ReviewsComp attributes={attributes} />}
          </Layout>
          {in_stock == true ? null : (
            <Badge
              style={styles.stockOutBadgeStyle}
              status="danger"
              text="Out of Stock"
              full={true}
              customStyles={__appmakerCustomStyles?.stock_out_badge}
            />
          )}

          {wishList === true && (
            <Icon
              name={isSaved === false ? 'hearto' : 'heart'}
              color={isSaved === false ? color.grey : color.danger}
              size={font.size.lg}
              style={[
                styles.wishListIcon,
                __appmakerCustomStyles?.wish_list_icon,
              ]}
              onPress={() => {
                toggleWishList && toggleWishList();
              }}
            />
          )}
          {easyAddCart && in_stock == true && (
            <Layout
              style={[
                styles.addIcon,
                __appmakerCustomStyles?.add_icon_container,
              ]}>
              <Button
                onPress={() => addToCart({ appmakerAction: false })}
                accessoryLeft={isAddToCartLoading ? LoadingIndicator : PlusIcon}
                small
                wholeContainerStyle={__appmakerCustomStyles?.button}
              />
            </Layout>
          )}
        </Layout>
        <Layout style={contentContainerStyle}>
          {brandName ? (
            <AppmakerText
              category="smallButtonText"
              style={[
                styles.aboveTitleText,
                __appmakerCustomStyles?.brand_name,
              ]}>
              {brandName}
            </AppmakerText>
          ) : null}
          {isNaN(numberOfLines) == false ? (
            <AppmakerText
              clientId={`${clientId}-product-name`}
              category="pageSubHeading"
              style={__appmakerCustomStyles?.product_title}
              numberOfLines={numberOfLines}>
              {title}
            </AppmakerText>
          ) : (
            <AppmakerText
              clientId={`${clientId}-product-name`}
              category="pageSubHeading"
              style={__appmakerCustomStyles?.product_title}>
              {title}
            </AppmakerText>
          )}
          <Layout
            style={[
              styles.priceDataContainer,
              __appmakerCustomStyles?.price_data_container,
            ]}>
            <AppmakerText
              category="bodyParagraphBold"
              status="dark"
              style={__appmakerCustomStyles?.sale_price}>
              {salePrice}
            </AppmakerText>
            <AppmakerText
              category="highlighter2"
              status="success"
              hideText={!onSale && !priceSale}
              style={styles.offerPrice}
              fontColor={__appmakerCustomStyles?.offer_price?.color}>
              {regularPrice}
            </AppmakerText>
          </Layout>
          {attribute && (
            <Layout
              style={[
                styles.attributesContainer,
                __appmakerCustomStyles?.attributes_container,
              ]}>
              {attributeView}
            </Layout>
          )}
          {groceryMode && (
            <Layout
              style={[
                styles.scrollerStepper,
                __appmakerCustomStyles?.stepper_container,
              ]}>
              {in_stock == true ? (
                <Stepper
                  count={count}
                  customizable={
                    data.type === 'variable' || productType === 'variable'
                  }
                  //addToCart
                  productType={productType}
                  addToCart={
                    productType === 'variable' ? openProduct : addToCart
                  }
                  updateCart={updateCart}
                  displayCount={count}
                  buttonText={'+ ADD'}
                  buttonLoadingText={'Loading'}
                  adding={isAddToCartLoading}
                  // onChange={onQuantityChange}
                  __appmakerCustomStyles={__appmakerCustomStyles?.stepper}
                />
              ) : wishList === true ? (
                <Button
                  small
                  outline
                  status={isSaved ? 'light' : 'grey'}
                  onPress={() => {
                    toggleWishList && toggleWishList();
                  }}
                  wholeContainerStyle={
                    __appmakerCustomStyles?.stepper_initial_button
                  }>
                  {!isSaved ? 'Add to Wishlist' : 'Added to Wishlist'}
                </Button>
              ) : null}
            </Layout>
          )}
        </Layout>
        {!groceryMode && addtocartButton && (
          <AppTouchable
            onPress={productType === 'variable' ? openProduct : addToCart}
            style={[styles.addCartButton, { backgroundColor: '#F9C109' }]}>
            {isAddToCartLoading ? (
              <ActivityIndicator size="small" color="#212121" />
            ) : (
              <AppmakerText>ADD TO CART</AppmakerText>
            )}
          </AppTouchable>
        )}
      </Layout>
    </AppTouchable>
  );
};

const allStyles = ({
  spacing,
  color,
  imageRatio,
  customHeight,
  __appmakerCustomStyles,
}) =>
  StyleSheet.create({
    // mainContainer: ,
    container: {
      borderWidth: 1,
      flex: 1,
      borderColor: color.light,
      marginLeft: spacing.base, //0 for grid
      borderRadius: spacing.mini,
      backgroundColor: color.white,
    },
    imageContainer: {
      backgroundColor: color.white,
      borderRadius: spacing.mini,
      overflow: 'hidden',
      position: 'relative',
      width: 180, //100% for grid
      height: imageRatio ? imageRatio * customHeight : 170,
    },
    scrollerImage: {
      width: '100%',
      height: '100%',
      // borderRadius: spacing.nano,
      resizeMode: appmaker.applyFilters(
        'product-grid-image-resize-mode',
        appSettings.getOptionAsBoolean('product_list_type')
          ? 'contain'
          : 'cover',
      ),
    },
    wishListIcon: {
      position: 'absolute',
      right: 0,
      top: 0,
      padding: spacing.nano,
      opacity: 0.8,
    },
    saleBadgeStyle: {
      marginRight: spacing.nano,
      marginTop: spacing.nano,
      opacity: 0.8,
    },
    stockOutBadgeStyle: {
      position: 'absolute',
      left: spacing.nano,
      right: spacing.nano,
      bottom: spacing.nano,
      opacity: 0.8,
      textAlign: 'center',
    },
    topLeftBlocks: {
      flexDirection: 'row',
      position: 'absolute',
      flexWrap: 'wrap',
      left: 4,
      top: 0,
    },
    contentContainer: {
      padding: spacing.small,
      width: 170,
      flex: 1,
    },
    aboveTitleText: {
      marginBottom: spacing.nano,
    },
    productTitle: {
      flex: 1,
      flexWrap: 'wrap',
      // ...__appmakerCustomStyles?.product_title,
    },
    priceDataContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
    },
    offerPrice: {
      textDecorationLine: 'line-through',
      marginLeft: spacing.nano,
    },
    addIcon: {
      position: 'absolute',
      right: spacing.base,
      bottom: spacing.base,
      width: spacing.xl,
      height: spacing.xl,
      opacity: 0.8,
    },
    scrollerStepper: {
      marginTop: spacing.nano,
      paddingTop: spacing.mini,
      borderTopColor: color.light,
      borderTopWidth: 1,
    },
    attributesContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    attributesListing: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginTop: spacing.nano,
    },
    attributeBadgeStyle: {
      marginRight: spacing.nano,
      marginBottom: spacing.nano,
    },
    addCartButton: {
      paddingVertical: 6,
      marginHorizontal: 6,
      marginBottom: 6,
      borderRadius: 4,
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

export default ProductGridWidget;
