import { StyleSheet, Dimensions } from "react-native";

const windowWidth = Dimensions.get("window").width;

const styles = StyleSheet.create({
  image: {
    height: windowWidth * 0.54
  },
  title: {
    fontSize: 24,
    fontFamily: "Raleway",
    fontWeight: "bold",
    color: "black"
  },
  authorpress: {
    marginTop: 25,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between"
  },
  authortext: {
    fontSize: 9,
    fontFamily: "Raleway-Bold",
    fontWeight: "bold",
    color: "black"
  },
  categorytext: {
    fontSize: 10,
    marginTop: 4,
    fontFamily: "Raleway",
    color: "black"
  },
  authorimage: {
    height: 42,
    width: 42,
    borderRadius: 21
  }
});

export default styles;
