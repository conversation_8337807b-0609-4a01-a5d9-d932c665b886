/**
 * Created by saleeh on 27/08/16.
 */
export default class VariationSelect {
  constructor(product) {
    this.product = product;

    this.variationValues = {};
    this.variationValuesDisplay = {};
    this.variationProduct = false;
    this.allSelected = false;
  }

  setProduct(product) {
    this.product = product;
  }

  checkAllSelected() {
    var allSelected = true;
    this.product.attributes.map((attribute) => {
      allSelected =
        allSelected &&
        this.variationValues.hasOwnProperty(attribute.id) &&
        this.variationValues[attribute.id] != '';
    });
    return allSelected;
  }

  getVariation(attrId, selectedOption) {
    var keepGoing = true;
    var product = this.product;
    this.variationValuesDisplay[attrId] = selectedOption;
    this.variationValues[attrId] = selectedOption;
    if (this.checkAllSelected()) {
      product.variations.map((variation) => {
        if (!keepGoing) {
          return;
        }
        var isVariation = true;
        this.allSelected = false;
        variation.attributes.map((variationAttribute, key) => {
          let variationKey = key;
          isVariation =
            isVariation &&
            this.variationValues.hasOwnProperty(variationAttribute.id) &&
            this.variationValues[variationAttribute.id] != '' &&
            (variationAttribute.option == '' ||
              variationAttribute.option ==
                this.variationValues[variationAttribute.id] ||
              variationAttribute.option ==
                this.variationValuesDisplay[variationAttribute.id]);
          if (
            variationAttribute.option ==
            this.variationValuesDisplay[variationAttribute.id]
          ) {
            this.variationValues[
              variationAttribute.id
            ] = this.variationValuesDisplay[variationAttribute.id];
          }
        });
        if (isVariation) {
          this.variationProduct = variation;
          keepGoing = false;
        } else {
          this.variationProduct = false;
        }
      });
      return this.variationProduct;
    } else {
      return false;
    }
  }
  getVariationImageId = (attrId, selectedOption) => {
    const imageProduct = this.getVariationForImage(attrId, selectedOption);
    if (imageProduct) {
      if (imageProduct.image.length > 0) {
        var index = this.product.images.indexOf(imageProduct.image[0]);
        return index;
        // if (index > -1 && index != this.sliderIndex && this._carouselRef) {
        //   this._carouselRef.scrollBy(index - this.sliderIndex);
        //   this.sliderIndex = this.sliderIndex + (index - this.sliderIndex);
        // }
      }
    }
    return 0;
  };

  /** Function to get image variation,  */
  getVariationForImage(attrId, selectedOption) {
    var keepGoing = true;
    var product = this.product;
    this.variationValuesDisplay[attrId] = selectedOption;
    this.variationValues[attrId] = selectedOption;
    if (true) {
      product.variations.map((variation) => {
        if (!keepGoing) {
          return;
        }
        var isVariation = true;
        this.allSelected = false;
        variation.attributes.map((variationAttribute, key) => {
          let variationKey = key;
          //console.log('true or false - ', this.variationValues.hasOwnProperty(variationAttribute.id) &&
          //this.variationValues[variationAttribute.id] != '', variationAttribute);
          isVariation =
            isVariation &&
            // this.variationValues.hasOwnProperty(variationAttribute.id) &&
            this.variationValues[variationAttribute.id] != '' &&
            (variationAttribute.option == '' ||
              variationAttribute.option ==
                this.variationValues[variationAttribute.id] ||
              variationAttribute.option ==
                this.variationValuesDisplay[variationAttribute.id]);
          if (
            variationAttribute.option ==
            this.variationValuesDisplay[variationAttribute.id]
          ) {
            this.variationValues[
              variationAttribute.id
            ] = this.variationValuesDisplay[variationAttribute.id];
          }
        });
        if (isVariation) {
          this.variationProduct = variation;
          keepGoing = false;
        } else {
          this.variationProduct = false;
        }
      });
      return this.variationProduct;
    } else {
      return false;
    }
  }

  getForRequest() {
    var attributeValues = {};
    Object.keys(this.variationValues).map((key) => {
      attributeValues[`attribute_${key}`] = this.variationValues[key];
    });
    attributeValues.variation_id = this.variationProduct
      ? this.variationProduct.id
      : null;
    return attributeValues;
  }
}
