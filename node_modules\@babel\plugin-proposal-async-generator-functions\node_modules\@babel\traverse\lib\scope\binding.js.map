{"version": 3, "names": ["Binding", "constructor", "identifier", "scope", "path", "kind", "constantViolations", "constant", "referencePaths", "referenced", "references", "isDeclaredInLoop", "Error", "reassign", "clearValue", "deoptValue", "hasDeoptedValue", "setValue", "value", "hasValue", "indexOf", "push", "reference", "dereference", "exports", "default", "parentPath", "key", "isFunctionParent", "<PERSON><PERSON><PERSON><PERSON>", "isForXStatement", "isForStatement"], "sources": ["../../src/scope/binding.ts"], "sourcesContent": ["import type NodePath from \"../path\";\nimport type * as t from \"@babel/types\";\nimport type Scope from \"./index\";\n\nexport type BindingKind =\n  | \"var\" /* var declarator */\n  | \"let\" /* let declarator, class declaration id, catch clause parameters */\n  | \"const\" /* const/using declarator */\n  | \"module\" /* import specifiers */\n  | \"hoisted\" /* function declaration id */\n  | \"param\" /* function declaration parameters */\n  | \"local\" /* function expression id, class expression id */\n  | \"unknown\"; /* export specifiers */\n/**\n * This class is responsible for a binding inside of a scope.\n *\n * It tracks the following:\n *\n *  * Node path.\n *  * Amount of times referenced by other nodes.\n *  * Paths to nodes that reassign or modify this binding.\n *  * The kind of binding. (Is it a parameter, declaration etc)\n */\n\nexport default class Binding {\n  identifier: t.Identifier;\n  scope: Scope;\n  path: NodePath;\n  kind: BindingKind;\n\n  constructor({\n    identifier,\n    scope,\n    path,\n    kind,\n  }: {\n    identifier: t.Identifier;\n    scope: Scope;\n    path: NodePath;\n    kind: BindingKind;\n  }) {\n    this.identifier = identifier;\n    this.scope = scope;\n    this.path = path;\n    this.kind = kind;\n\n    if (\n      (kind === \"var\" || kind === \"hoisted\") &&\n      // https://github.com/rollup/rollup/issues/4654\n      // Rollup removes the path argument from this call. Add an\n      // unreachable IIFE (that rollup doesn't know is unreachable)\n      // with side effects, to prevent it from messing up with arguments.\n      // You can reproduce this with\n      //   BABEL_8_BREAKING=true make prepublish-build\n      isDeclaredInLoop(\n        path ||\n          (() => {\n            throw new Error(\"Internal Babel error: unreachable \");\n          })(),\n      )\n    ) {\n      this.reassign(path);\n    }\n\n    this.clearValue();\n  }\n\n  constantViolations: Array<NodePath> = [];\n  constant: boolean = true;\n\n  referencePaths: Array<NodePath> = [];\n  referenced: boolean = false;\n  references: number = 0;\n\n  declare hasDeoptedValue: boolean;\n  declare hasValue: boolean;\n  declare value: any;\n\n  deoptValue() {\n    this.clearValue();\n    this.hasDeoptedValue = true;\n  }\n\n  setValue(value: any) {\n    if (this.hasDeoptedValue) return;\n    this.hasValue = true;\n    this.value = value;\n  }\n\n  clearValue() {\n    this.hasDeoptedValue = false;\n    this.hasValue = false;\n    this.value = null;\n  }\n\n  /**\n   * Register a constant violation with the provided `path`.\n   */\n\n  reassign(path: NodePath) {\n    this.constant = false;\n    if (this.constantViolations.indexOf(path) !== -1) {\n      return;\n    }\n    this.constantViolations.push(path);\n  }\n\n  /**\n   * Increment the amount of references to this binding.\n   */\n\n  reference(path: NodePath) {\n    if (this.referencePaths.indexOf(path) !== -1) {\n      return;\n    }\n    this.referenced = true;\n    this.references++;\n    this.referencePaths.push(path);\n  }\n\n  /**\n   * Decrement the amount of references to this binding.\n   */\n\n  dereference() {\n    this.references--;\n    this.referenced = !!this.references;\n  }\n}\n\nfunction isDeclaredInLoop(path: NodePath) {\n  for (\n    let { parentPath, key } = path;\n    parentPath;\n    { parentPath, key } = parentPath\n  ) {\n    if (parentPath.isFunctionParent()) return false;\n    if (\n      parentPath.isWhile() ||\n      parentPath.isForXStatement() ||\n      (parentPath.isForStatement() && key === \"body\")\n    ) {\n      return true;\n    }\n  }\n  return false;\n}\n"], "mappings": ";;;;;;AAwBe,MAAMA,OAAO,CAAC;EAM3BC,WAAWA,CAAC;IACVC,UAAU;IACVC,KAAK;IACLC,IAAI;IACJC;EAMF,CAAC,EAAE;IAAA,KAfHH,UAAU;IAAA,KACVC,KAAK;IAAA,KACLC,IAAI;IAAA,KACJC,IAAI;IAAA,KAuCJC,kBAAkB,GAAoB,EAAE;IAAA,KACxCC,QAAQ,GAAY,IAAI;IAAA,KAExBC,cAAc,GAAoB,EAAE;IAAA,KACpCC,UAAU,GAAY,KAAK;IAAA,KAC3BC,UAAU,GAAW,CAAC;IA/BpB,IAAI,CAACR,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAEhB,IACE,CAACA,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,SAAS,KAOrCM,gBAAgB,CACdP,IAAI,IACF,CAAC,MAAM;MACL,MAAM,IAAIQ,KAAK,CAAC,oCAAoC,CAAC;IACvD,CAAC,GAAG,CACP,EACD;MACA,IAAI,CAACC,QAAQ,CAACT,IAAI,CAAC;IACrB;IAEA,IAAI,CAACU,UAAU,EAAE;EACnB;EAaAC,UAAUA,CAAA,EAAG;IACX,IAAI,CAACD,UAAU,EAAE;IACjB,IAAI,CAACE,eAAe,GAAG,IAAI;EAC7B;EAEAC,QAAQA,CAACC,KAAU,EAAE;IACnB,IAAI,IAAI,CAACF,eAAe,EAAE;IAC1B,IAAI,CAACG,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACD,KAAK,GAAGA,KAAK;EACpB;EAEAJ,UAAUA,CAAA,EAAG;IACX,IAAI,CAACE,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACG,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACD,KAAK,GAAG,IAAI;EACnB;EAMAL,QAAQA,CAACT,IAAc,EAAE;IACvB,IAAI,CAACG,QAAQ,GAAG,KAAK;IACrB,IAAI,IAAI,CAACD,kBAAkB,CAACc,OAAO,CAAChB,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;MAChD;IACF;IACA,IAAI,CAACE,kBAAkB,CAACe,IAAI,CAACjB,IAAI,CAAC;EACpC;EAMAkB,SAASA,CAAClB,IAAc,EAAE;IACxB,IAAI,IAAI,CAACI,cAAc,CAACY,OAAO,CAAChB,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;MAC5C;IACF;IACA,IAAI,CAACK,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACF,cAAc,CAACa,IAAI,CAACjB,IAAI,CAAC;EAChC;EAMAmB,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACb,UAAU,EAAE;IACjB,IAAI,CAACD,UAAU,GAAG,CAAC,CAAC,IAAI,CAACC,UAAU;EACrC;AACF;AAACc,OAAA,CAAAC,OAAA,GAAAzB,OAAA;AAED,SAASW,gBAAgBA,CAACP,IAAc,EAAE;EACxC,KACE,IAAI;IAAEsB,UAAU;IAAEC;EAAI,CAAC,GAAGvB,IAAI,EAC9BsB,UAAU,GACV;IAAEA,UAAU;IAAEC;EAAI,CAAC,GAAGD,UAAU,GAChC;IACA,IAAIA,UAAU,CAACE,gBAAgB,EAAE,EAAE,OAAO,KAAK;IAC/C,IACEF,UAAU,CAACG,OAAO,EAAE,IACpBH,UAAU,CAACI,eAAe,EAAE,IAC3BJ,UAAU,CAACK,cAAc,EAAE,IAAIJ,GAAG,KAAK,MAAO,EAC/C;MACA,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd"}