import React, { useRef, useEffect, useState } from 'react';
import { TextInput, StyleSheet, Keyboard } from 'react-native';
import { ThemeText, Layout } from '../../atoms';

const CustomOtpBar = ({ isOtpResent, showError, onFinalText, lengthInput }) => {
  const textInput = useRef();
  const [internalValue, setInternalValue] = useState('');
  const [inputKey, setInputKey] = useState(0); // Key to force re-render

  useEffect(() => {
    const focusInput = () => {
      if (textInput.current) {
        textInput.current.focus();
      }
    };

    setTimeout(focusInput, 100); // Delay to ensure focus after component mounts

    return () => {
      Keyboard.dismiss(); // Dismiss keyboard when component unmounts
    };
  }, [inputKey]); // Re-run effect when inputKey changes

  const onChangeText = (val) => {
    setInternalValue(val);
    onFinalText(val);
  };

  useEffect(() => {
    if (isOtpResent) {
      setInternalValue('');
      setInputKey((prevKey) => prevKey + 1); // Update key to re-render input
    }
  }, [isOtpResent]);

  return (
    <Layout>
      <TextInput
        key={inputKey} // Key to force re-render
        ref={textInput}
        onChangeText={onChangeText}
        style={styles.hiddenInput}
        value={internalValue}
        maxLength={lengthInput}
        placeholder={'0'}
        placeholderTextColor={'#f1f2f5'}
        secureTextEntry={true}
        caretHidden={true}
        returnKeyType={'done'}
        keyboardType={'numeric'}
      />
      <Layout style={styles.containerInput}>
        {Array(lengthInput)
          .fill()
          .map((item, index) => (
            <Layout
              key={index}
              style={[
                styles.otpCell,
                showError ? styles.errorCellOtp : styles.cellViewOtp,
              ]}>
              <ThemeText
                allowFontScaling={false}
                style={styles.cellTextOtp}
                onPress={() => {
                  textInput.current.focus();
                }}>
                {internalValue?.length > index ? internalValue[index] : ''}
              </ThemeText>
            </Layout>
          ))}
      </Layout>
    </Layout>
  );
};

const styles = StyleSheet.create({
  hiddenInput: {
    position: 'absolute',
    opacity: 0,
    width: 1,
    height: 1,
    width: '100%',  
    height: '100%',
    zIndex: 1,
  },
  containerInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  otpCell: {
    width: 38,
    height: 38,
    margin: 5,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    backgroundColor: '#fff',
    borderRadius: 6,
  },
  errorCellOtp: {
    borderColor: '#EF4444',
  },
  cellViewOtp: {
    borderColor: '#64748B',
  },
  cellTextOtp: {
    margin:5,
    fontSize: 15,
    textAlign: 'center',
    color: '#212121',
  },
});

export default CustomOtpBar;
