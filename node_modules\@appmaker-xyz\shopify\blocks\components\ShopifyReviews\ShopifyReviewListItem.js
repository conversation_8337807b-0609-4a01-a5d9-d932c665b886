import React from 'react';
import { View, StyleSheet, Image, ScrollView, Pressable } from 'react-native';
import Icon from 'react-native-vector-icons/AntDesign';
import { useReviewListItem } from '../../../hooks/useReviewListItem';
import { ThemeText } from '@appmaker-xyz/ui';

function randomPastelColor() {
  const r = (Math.round(Math.random() * 127) + 127).toString(16);
  const g = (Math.round(Math.random() * 127) + 127).toString(16);
  const b = (Math.round(Math.random() * 127) + 127).toString(16);
  return '#' + r + g + b;
}

const ReviewListItem = (props) => {
  const { body, title, reviewer, rating, imageUrls, created_at, verified } =
    useReviewListItem(props);

  const ImagesView = () => {
    const [showModal, setShowModal] = React.useState(false);
    return imageUrls?.length > 0 ? (
      <ScrollView horizontal={true} style={styles.imagesContainer}>
        {imageUrls.map((url) => (
          <Pressable onPress={() => setShowModal(!showModal)}>
            <Image source={{ uri: url }} style={styles.image} />
          </Pressable>
        ))}
      </ScrollView>
    ) : null;
  };

  const VerifiedBadge = () => {
    return verified !== 'nothing' ? (
      <ThemeText category="highlighter2" style={styles.verifiedText}>
        Verified
      </ThemeText>
    ) : null;
  };

  return (
    <View style={styles.container}>
      <View style={styles.titleContainer}>
        <View style={styles.reviewerInitial}>
          <ThemeText>{reviewer?.name?.[0].toUpperCase()}</ThemeText>
        </View>
        <View>
          <View style={styles.starsContainer}>
            {[1, 2, 3, 4, 5].map((item, index) => {
              return (
                <Icon
                  key={`review-star-${index}`}
                  name={index < rating ? 'star' : 'staro'}
                  size={12}
                  color={index < rating ? '#FBBF24' : '#FBBF24'}
                />
              );
            })}
            <ThemeText size="xs" style={styles.dateText}>
              {created_at}
            </ThemeText>
          </View>
          <View style={styles.reviewerContainer}>
            <VerifiedBadge />
            <ThemeText size="sm">{reviewer.name}</ThemeText>
          </View>
        </View>
      </View>
      <View style={styles.contentContainer}>
        {title ? <ThemeText fontFamily="bold">{title}</ThemeText> : null}
        {body ? <ThemeText style={styles.bodyText}>{body}</ThemeText> : null}
        <ImagesView />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
    marginBottom: 1,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  reviewerInitial: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: randomPastelColor(),
  },
  starsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  dateText: {
    marginLeft: 8,
  },
  reviewerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  verifiedText: {
    color: '#FFFFFF',
    backgroundColor: '#000',
    paddingHorizontal: 4,
    marginRight: 4,
  },
  contentContainer: {
    marginTop: 16,
  },
  bodyText: {
    marginTop: 4,
  },
  imagesContainer: {
    marginTop: 16,
  },
  image: {
    width: 100,
    height: 100,
    marginRight: 6,
  },
});

export default ReviewListItem;
