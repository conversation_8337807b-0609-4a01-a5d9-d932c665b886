import AsyncStorage from '@react-native-community/async-storage';
// import notifee from '@notifee/react-native';

export class DeleteScheduledPush {
  async exec(params, context) {
    const { localPushId } = params;
    const currentId = await AsyncStorage.getItem(localPushId);
    if (currentId !== null) {
      // await notifee.cancelNotification(currentId);
    }
    return { removed: currentId !== null };
  }
}
