import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useApThemeState } from '../../../../theme/ThemeContext';
import { AppTouchable, Layout, LayoutIcon } from '../../..';
import NewSwiper from './NewSwiper';
import Icon from '@appmaker-xyz/uikit/Icons/AntDesign';
import { emitEvent, analytics } from '@appmaker-xyz/core';
import {
  useProductWishList,
  ProductBadge,
  useProductDetail,
} from '@appmaker-xyz/shopify';

const ProductImage = (props) => {
  const { attributes, onAction, blockData } = props;
  const {
    noTopBar,
    indexImage,
    title,
    uri,
    displayWishlist,
    customDotColor,
    centerPagination,
    __appmakerCustomStyles = {},
  } = attributes;
  const { isSaved, toggleWishList } = useProductWishList({
    onAction,
    blockData,
  });
  // const imageRatio = thumbnail_meta?.height / thumbnail_meta?.width;
  let imageRatio = 1;
  let { imageList, index } = attributes;
  try {
    imageRatio =
      imageList?.node?.images?.edges[0]?.node?.height /
      imageList?.node?.images?.edges[0]?.node?.width;
  } catch (error) {
    console.log(error);
  }
  const { color, spacing, font } = useApThemeState();
  const styles = allStyles({ color, spacing, imageRatio });
  // const [saved, setSaved] = useState(savedItemIds[data.id] === true);
  if (imageList) {
    if (imageList?.node?.images?.edges) {
      imageList = imageList?.node?.images?.edges.map((item) => {
        return {
          uri: item.node.src,
        };
      });
    } else if (imageList?.images) {
      imageList = imageList?.images.map((item) => {
        return {
          uri: item,
        };
      });
    }
    if (indexImage) {
      imageList?.map &&
        imageList?.map((x, key) => {
          if (x.uri === indexImage) {
            index = key + 1;
          }
        });
    }
  }
  const { product } = useProductDetail(props);
  return (
    <AppTouchable
      style={[
        styles.productImageContainer,
        __appmakerCustomStyles?.product_image?.container,
      ]}>
      <NewSwiper
        onAction={onAction}
        attributes={{
          showLoadingIndicator: true,
          index,
          imageList: imageList || [],
          autoplay: false,
          appmakerAction: attributes.appmakerAction,
          customDotColor:
            __appmakerCustomStyles?.product_image?.active_dot_color ||
            customDotColor,
          centerPagination: centerPagination == 1 ? true : false,
        }}
      />
      <TopLeftSlot>
        <ProductBadge product={product} slot="top-left" type="product-detail" />
      </TopLeftSlot>
      <TopRightSlot>
        <ProductBadge
          product={product}
          slot="top-right"
          type="product-detail"
        />
      </TopRightSlot>
      <BottomLeft>
        <ProductBadge
          product={product}
          slot="bottom-left"
          type="product-detail"
        />
      </BottomLeft>
      <BottomRightSlot>
        <ProductBadge
          product={product}
          slot="bottom-right"
          type="product-detail"
        />
      </BottomRightSlot>
      {noTopBar && (
        <Layout style={styles.overlayTop}>
          <LayoutIcon
            onAction={onAction}
            attributes={{
              iconName: 'arrow-left',
              overlay: true,
              appmakerAction: { action: 'GO_BACK' },
            }}
          />
          <LayoutIcon
            onAction={onAction}
            attributes={{
              iconName: 'shopping-cart',
              overlay: true,
              appmakerAction: { action: 'OPEN_CART' },
            }}
          />
        </Layout>
      )}
      <Layout
        style={[
          styles.overlayRight,
          __appmakerCustomStyles?.product_image?.overlay_container,
        ]}>
        {displayWishlist && (
          <Icon
            name={!isSaved ? 'hearto' : 'heart'}
            color={!isSaved ? color.demiDark : color.danger}
            size={font.size.lg}
            style={[
              styles.overlayIcon,
              __appmakerCustomStyles?.product_image?.overlay_icon,
            ]}
            onPress={() => {
              toggleWishList && toggleWishList();
            }}
          />
        )}
        <Icon
          name="sharealt"
          size={font.size.lg}
          style={[
            styles.overlayIcon,
            __appmakerCustomStyles?.product_image?.overlay_icon,
          ]}
          onPress={() => {
            emitEvent('shareProduct', { product_name: blockData?.node?.title });
            analytics.track('shareProduct', {
              product_name: blockData?.node?.title,
            });
            onAction({
              action: 'SHARE_ITEM',
              params: { message: title, url: uri },
            });
            // console.log();
          }}
        />
      </Layout>
    </AppTouchable>
  );
};
function TopLeftSlot({ children }) {
  return <View style={styles.topLeftContainer}>{children}</View>;
}

function TopRightSlot({ children }) {
  return <View style={styles.topRightContainer}>{children}</View>;
}
function BottomLeft({ children }) {
  return <View style={styles.bottomLeftContainer}>{children}</View>;
}
function BottomRightSlot({ children }) {
  return <View style={styles.bottomRightContainer}>{children}</View>;
}

const allStyles = ({ spacing, color, imageRatio }) =>
  StyleSheet.create({
    productImageContainer: {
      aspectRatio: imageRatio ? 1 / imageRatio : null,
      height: imageRatio ? null : 450,
      width: '100%',
      position: 'relative',
    },
    overlayRight: {
      position: 'absolute',
      bottom: spacing.base,
      right: spacing.base,
    },
    overlayTop: {
      position: 'absolute',
      flexDirection: 'row',
      width: '100%',
      justifyContent: 'space-between',
      padding: spacing.base,
      left: -5,
    },
    overlayIcon: {
      padding: spacing.base,
      backgroundColor: color.white,
      borderRadius: spacing.lg,
      overflow: 'hidden',
      marginVertical: spacing.nano / 2,
    },
  });
const styles = StyleSheet.create({
  topLeftContainer: {
    position: 'absolute',
    top: 4,
    left: 4,
  },
  topRightContainer: {
    position: 'absolute',
    top: 4,
    right: 4,
  },
  bottomLeftContainer: {
    position: 'absolute',
    bottom: 4,
    left: 4,
  },
  bottomRightContainer: {
    position: 'absolute',
    bottom: 4,
    right: 4,
  },
});
export default ProductImage;
