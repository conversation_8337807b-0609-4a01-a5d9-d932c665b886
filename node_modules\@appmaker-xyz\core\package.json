{"name": "@appmaker-xyz/core", "version": "0.4.36-expo-v2-build-test-25-04-e5e5c0b.0", "description": "", "main": "build-output/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rollup --config rollup.config.js && cp -r src/appSettings/files/images build-output/images", "watch": "rollup --config rollup.config.js --watch", "prepublishOnly": "npm run build"}, "keywords": [], "author": "", "license": "UNLICENSED", "files": ["build-output"], "dependencies": {"@wordpress/hooks": "^3.1.1", "abortcontroller-polyfill": "^1.7.3", "awesome-json2json": "^0.6.0", "base-64": "^0.1.0", "contrast": "^1.0.1", "dayjs": "^1.11.2", "ejs": "^3.1.6", "ejs-browser": "^3.2.2", "immer": "^9.0.15", "lodash": "^4.17.15", "react-native-base64": "^0.2.1", "react-native-simple-store": "^2.0.2", "rollup-plugin-local-resolve": "^1.0.7", "simpler-state": "^1.0.3", "url-pattern": "^1.0.3", "use-context-selector": "^1.3.7", "zod": "^3.20.6", "zustand": "^3.5.12"}, "devDependencies": {"@lopatnov/rollup-plugin-uglify": "^2.1.5", "@rollup/plugin-typescript": "^11.1.2", "rollup": "^2.79.0", "rollup-plugin-local-resolve": "^1.0.7", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-uglify": "^6.0.4", "terser": "^5.27.0"}, "peerDependencies": {"@react-native-firebase/analytics": "*", "@react-native-firebase/crashlytics": "*", "@react-native-firebase/perf": "*", "@tanstack/react-query": "*", "axios": "*", "react": "*", "react-native": "*"}, "repository": "https://github.com/Appmaker-xyz/starter-app", "publishConfig": {"registry": "https://flash.appmaker.xyz", "directory": "build-output"}}