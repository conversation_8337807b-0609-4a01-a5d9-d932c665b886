{"version": 3, "names": ["_assert", "data", "require", "_fs", "_interopRequireWildcard", "_process", "_url", "_path", "_module", "_v", "_util", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "isWindows", "process", "platform", "own$1", "classRegExp", "kTypes", "Set", "codes", "formatList", "array", "type", "length", "join", "slice", "messages", "Map", "nodeInternalPrefix", "userStackTraceLimit", "ERR_INVALID_ARG_TYPE", "createError", "name", "expected", "actual", "assert", "Array", "isArray", "message", "endsWith", "includes", "types", "instances", "other", "value", "push", "toLowerCase", "exec", "pos", "indexOf", "determineSpecificType", "TypeError", "ERR_INVALID_MODULE_SPECIFIER", "request", "reason", "base", "undefined", "ERR_INVALID_PACKAGE_CONFIG", "path", "Error", "ERR_INVALID_PACKAGE_TARGET", "pkgPath", "target", "isImport", "rel<PERSON><PERSON><PERSON>", "startsWith", "JSON", "stringify", "ERR_MODULE_NOT_FOUND", "ERR_NETWORK_IMPORT_DISALLOWED", "ERR_PACKAGE_IMPORT_NOT_DEFINED", "specifier", "packagePath", "ERR_PACKAGE_PATH_NOT_EXPORTED", "subpath", "ERR_UNSUPPORTED_DIR_IMPORT", "ERR_UNKNOWN_FILE_EXTENSION", "ext", "ERR_INVALID_ARG_VALUE", "inspected", "inspect", "ERR_UNSUPPORTED_ESM_URL_SCHEME", "url", "supported", "protocol", "sym", "def", "makeNodeErrorWithCode", "Base", "NodeError", "args", "limit", "stackTraceLimit", "isErrorStackTraceLimitWritable", "error", "getMessage", "defineProperties", "enumerable", "writable", "configurable", "toString", "captureLargerStackTrace", "code", "v8", "startupSnapshot", "isBuildingSnapshot", "_unused", "isExtensible", "hideStackFrames", "fn", "hidden", "stackTraceLimitIsWritable", "Number", "POSITIVE_INFINITY", "captureStackTrace", "self", "Reflect", "apply", "regex", "<PERSON><PERSON><PERSON><PERSON>", "unshift", "format", "String", "constructor", "depth", "colors", "reader", "read", "packageJsonReader", "jsonPath", "string", "fs", "readFileSync", "toNamespacedPath", "dirname", "exception", "ERR_INVALID_PACKAGE_CONFIG$1", "packageJsonCache", "getPackageConfig", "existing", "source", "packageConfig", "p<PERSON><PERSON><PERSON><PERSON>", "exists", "main", "exports", "imports", "packageJson", "parse", "fileURLToPath", "getPackageScopeConfig", "resolved", "packageJsonUrl", "URL", "packageJsonPath", "pathname", "lastPackageJsonUrl", "getPackageType", "extensionFormatMap", "__proto__", "mimeToFormat", "mime", "test", "protocolHandlers", "getDataProtocolModuleFormat", "getFileProtocolModuleFormat", "getHttpProtocolModuleFormat", "node:", "parsed", "extname", "index", "codePointAt", "_context", "ignoreErrors", "filepath", "defaultGetFormatWithoutErrors", "context", "DEFAULT_CONDITIONS", "freeze", "DEFAULT_CONDITIONS_SET", "getDefaultConditions", "getDefaultConditionsSet", "getConditionsSet", "conditions", "RegExpPrototypeSymbolReplace", "RegExp", "Symbol", "replace", "experimentalNetworkImports", "own", "invalidSegmentRegEx", "deprecatedInvalidSegmentRegEx", "invalidPackageNameRegEx", "patternRegEx", "encodedSepRegEx", "emittedPackageWarnings", "doubleSlashRegEx", "emitInvalidSegmentDeprecation", "match", "internal", "<PERSON><PERSON><PERSON><PERSON>", "double", "emitWarning", "emitLegacyIndexDeprecation", "parentURL", "href", "basePath", "tryStatSync", "statSync", "_unused2", "Stats", "fileExists", "stats", "throwIfNoEntry", "isFile", "legacyMainResolve", "guess", "tries", "i", "finalizeResolution", "preserveSymlinks", "filePath", "isDirectory", "real", "realpathSync", "search", "hash", "pathToFileURL", "sep", "importNotDefined", "exportsNotFound", "throwInvalidSubpath", "invalidPackageTarget", "resolvePackageTargetString", "pattern", "isPathMap", "isURL", "_unused3", "exportTarget", "packageResolve", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isArrayIndex", "keyNumber", "resolvePackageTarget", "packageSubpath", "targetList", "lastException", "targetItem", "resolveResult", "keys", "getOwnPropertyNames", "<PERSON><PERSON><PERSON><PERSON>", "isConditionalExportsMainSugar", "isConditionalSugar", "j", "curIsConditionalSugar", "emitTrailingSlashPatternDeprecation", "pjsonUrl", "add", "packageExportsResolve", "bestMatch", "bestMatchSubpath", "patternIndex", "patternTrailer", "patternKeyCompare", "lastIndexOf", "a", "b", "aPatternIndex", "bPatternIndex", "baseLengthA", "baseLengthB", "packageImportsResolve", "parsePackageName", "separatorIndex", "validPackageName", "isScoped", "packageName", "builtinModules", "last<PERSON><PERSON>", "stat", "isRelativeSpecifier", "shouldBeTreatedAsRelativeOrAbsolutePath", "moduleResolve", "isRemote", "_unused4", "checkIfDisallowedImport", "parsedParentURL", "parentProtocol", "parsedProtocol", "Boolean", "throwIfInvalidParentURL", "throwIfUnsupportedURLProtocol", "throwIfUnsupportedURLScheme", "concat", "defaultResolve", "_unused5", "_unused6", "maybeReturn", "resolve", "parent"], "sources": ["../../src/vendor/import-meta-resolve.js"], "sourcesContent": ["\n/****************************************************************************\\\n *                         NOTE FROM BABEL AUTHORS                          *\n * This file is inlined from https://github.com/wooorm/import-meta-resolve, *\n * because we need to compile it to CommonJS.                               *\n\\****************************************************************************/\n\n/*\n(The MIT License)\n\nCopyright (c) 2021 Titus Wormer <mailto:<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n---\n\nThis is a derivative work based on:\n<https://github.com/nodejs/node>.\nWhich is licensed:\n\n\"\"\"\nCopyright Node.js contributors. All rights reserved.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n\"\"\"\n\nThis license applies to parts of Node.js originating from the\nhttps://github.com/joyent/node repository:\n\n\"\"\"\nCopyright Joyent, Inc. and other Node contributors. All rights reserved.\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n\"\"\"\n*/\n\nimport assert from 'assert';\nimport fs, { realpathSync, statSync, Stats } from 'fs';\nimport process from 'process';\nimport { fileURLToPath, URL, pathToFileURL } from 'url';\nimport path from 'path';\nimport { builtinModules } from 'module';\nimport v8 from 'v8';\nimport { format, inspect } from 'util';\n\n/**\n * @typedef ErrnoExceptionFields\n * @property {number | undefined} [errnode]\n * @property {string | undefined} [code]\n * @property {string | undefined} [path]\n * @property {string | undefined} [syscall]\n * @property {string | undefined} [url]\n *\n * @typedef {Error & ErrnoExceptionFields} ErrnoException\n */\n\n\nconst isWindows = process.platform === 'win32';\n\nconst own$1 = {}.hasOwnProperty;\n\nconst classRegExp = /^([A-Z][a-z\\d]*)+$/;\n// Sorted by a rough estimate on most frequently used entries.\nconst kTypes = new Set([\n  'string',\n  'function',\n  'number',\n  'object',\n  // Accept 'Function' and 'Object' as alternative to the lower cased version.\n  'Function',\n  'Object',\n  'boolean',\n  'bigint',\n  'symbol'\n]);\n\nconst codes = {};\n\n/**\n * Create a list string in the form like 'A and B' or 'A, B, ..., and Z'.\n * We cannot use Intl.ListFormat because it's not available in\n * --without-intl builds.\n *\n * @param {Array<string>} array\n *   An array of strings.\n * @param {string} [type]\n *   The list type to be inserted before the last element.\n * @returns {string}\n */\nfunction formatList(array, type = 'and') {\n  return array.length < 3\n    ? array.join(` ${type} `)\n    : `${array.slice(0, -1).join(', ')}, ${type} ${array[array.length - 1]}`\n}\n\n/** @type {Map<string, MessageFunction | string>} */\nconst messages = new Map();\nconst nodeInternalPrefix = '__node_internal_';\n/** @type {number} */\nlet userStackTraceLimit;\n\ncodes.ERR_INVALID_ARG_TYPE = createError(\n  'ERR_INVALID_ARG_TYPE',\n  /**\n   * @param {string} name\n   * @param {Array<string> | string} expected\n   * @param {unknown} actual\n   */\n  (name, expected, actual) => {\n    assert(typeof name === 'string', \"'name' must be a string\");\n    if (!Array.isArray(expected)) {\n      expected = [expected];\n    }\n\n    let message = 'The ';\n    if (name.endsWith(' argument')) {\n      // For cases like 'first argument'\n      message += `${name} `;\n    } else {\n      const type = name.includes('.') ? 'property' : 'argument';\n      message += `\"${name}\" ${type} `;\n    }\n\n    message += 'must be ';\n\n    /** @type {Array<string>} */\n    const types = [];\n    /** @type {Array<string>} */\n    const instances = [];\n    /** @type {Array<string>} */\n    const other = [];\n\n    for (const value of expected) {\n      assert(\n        typeof value === 'string',\n        'All expected entries have to be of type string'\n      );\n\n      if (kTypes.has(value)) {\n        types.push(value.toLowerCase());\n      } else if (classRegExp.exec(value) === null) {\n        assert(\n          value !== 'object',\n          'The value \"object\" should be written as \"Object\"'\n        );\n        other.push(value);\n      } else {\n        instances.push(value);\n      }\n    }\n\n    // Special handle `object` in case other instances are allowed to outline\n    // the differences between each other.\n    if (instances.length > 0) {\n      const pos = types.indexOf('object');\n      if (pos !== -1) {\n        types.slice(pos, 1);\n        instances.push('Object');\n      }\n    }\n\n    if (types.length > 0) {\n      message += `${types.length > 1 ? 'one of type' : 'of type'} ${formatList(\n        types,\n        'or'\n      )}`;\n      if (instances.length > 0 || other.length > 0) message += ' or ';\n    }\n\n    if (instances.length > 0) {\n      message += `an instance of ${formatList(instances, 'or')}`;\n      if (other.length > 0) message += ' or ';\n    }\n\n    if (other.length > 0) {\n      if (other.length > 1) {\n        message += `one of ${formatList(other, 'or')}`;\n      } else {\n        if (other[0].toLowerCase() !== other[0]) message += 'an ';\n        message += `${other[0]}`;\n      }\n    }\n\n    message += `. Received ${determineSpecificType(actual)}`;\n\n    return message\n  },\n  TypeError\n);\n\ncodes.ERR_INVALID_MODULE_SPECIFIER = createError(\n  'ERR_INVALID_MODULE_SPECIFIER',\n  /**\n   * @param {string} request\n   * @param {string} reason\n   * @param {string} [base]\n   */\n  (request, reason, base = undefined) => {\n    return `Invalid module \"${request}\" ${reason}${\n      base ? ` imported from ${base}` : ''\n    }`\n  },\n  TypeError\n);\n\ncodes.ERR_INVALID_PACKAGE_CONFIG = createError(\n  'ERR_INVALID_PACKAGE_CONFIG',\n  /**\n   * @param {string} path\n   * @param {string} [base]\n   * @param {string} [message]\n   */\n  (path, base, message) => {\n    return `Invalid package config ${path}${\n      base ? ` while importing ${base}` : ''\n    }${message ? `. ${message}` : ''}`\n  },\n  Error\n);\n\ncodes.ERR_INVALID_PACKAGE_TARGET = createError(\n  'ERR_INVALID_PACKAGE_TARGET',\n  /**\n   * @param {string} pkgPath\n   * @param {string} key\n   * @param {unknown} target\n   * @param {boolean} [isImport=false]\n   * @param {string} [base]\n   */\n  (pkgPath, key, target, isImport = false, base = undefined) => {\n    const relError =\n      typeof target === 'string' &&\n      !isImport &&\n      target.length > 0 &&\n      !target.startsWith('./');\n    if (key === '.') {\n      assert(isImport === false);\n      return (\n        `Invalid \"exports\" main target ${JSON.stringify(target)} defined ` +\n        `in the package config ${pkgPath}package.json${\n          base ? ` imported from ${base}` : ''\n        }${relError ? '; targets must start with \"./\"' : ''}`\n      )\n    }\n\n    return `Invalid \"${\n      isImport ? 'imports' : 'exports'\n    }\" target ${JSON.stringify(\n      target\n    )} defined for '${key}' in the package config ${pkgPath}package.json${\n      base ? ` imported from ${base}` : ''\n    }${relError ? '; targets must start with \"./\"' : ''}`\n  },\n  Error\n);\n\ncodes.ERR_MODULE_NOT_FOUND = createError(\n  'ERR_MODULE_NOT_FOUND',\n  /**\n   * @param {string} path\n   * @param {string} base\n   * @param {string} [type]\n   */\n  (path, base, type = 'package') => {\n    return `Cannot find ${type} '${path}' imported from ${base}`\n  },\n  Error\n);\n\ncodes.ERR_NETWORK_IMPORT_DISALLOWED = createError(\n  'ERR_NETWORK_IMPORT_DISALLOWED',\n  \"import of '%s' by %s is not supported: %s\",\n  Error\n);\n\ncodes.ERR_PACKAGE_IMPORT_NOT_DEFINED = createError(\n  'ERR_PACKAGE_IMPORT_NOT_DEFINED',\n  /**\n   * @param {string} specifier\n   * @param {string} packagePath\n   * @param {string} base\n   */\n  (specifier, packagePath, base) => {\n    return `Package import specifier \"${specifier}\" is not defined${\n      packagePath ? ` in package ${packagePath}package.json` : ''\n    } imported from ${base}`\n  },\n  TypeError\n);\n\ncodes.ERR_PACKAGE_PATH_NOT_EXPORTED = createError(\n  'ERR_PACKAGE_PATH_NOT_EXPORTED',\n  /**\n   * @param {string} pkgPath\n   * @param {string} subpath\n   * @param {string} [base]\n   */\n  (pkgPath, subpath, base = undefined) => {\n    if (subpath === '.')\n      return `No \"exports\" main defined in ${pkgPath}package.json${\n        base ? ` imported from ${base}` : ''\n      }`\n    return `Package subpath '${subpath}' is not defined by \"exports\" in ${pkgPath}package.json${\n      base ? ` imported from ${base}` : ''\n    }`\n  },\n  Error\n);\n\ncodes.ERR_UNSUPPORTED_DIR_IMPORT = createError(\n  'ERR_UNSUPPORTED_DIR_IMPORT',\n  \"Directory import '%s' is not supported \" +\n    'resolving ES modules imported from %s',\n  Error\n);\n\ncodes.ERR_UNKNOWN_FILE_EXTENSION = createError(\n  'ERR_UNKNOWN_FILE_EXTENSION',\n  /**\n   * @param {string} ext\n   * @param {string} path\n   */\n  (ext, path) => {\n    return `Unknown file extension \"${ext}\" for ${path}`\n  },\n  TypeError\n);\n\ncodes.ERR_INVALID_ARG_VALUE = createError(\n  'ERR_INVALID_ARG_VALUE',\n  /**\n   * @param {string} name\n   * @param {unknown} value\n   * @param {string} [reason='is invalid']\n   */\n  (name, value, reason = 'is invalid') => {\n    let inspected = inspect(value);\n\n    if (inspected.length > 128) {\n      inspected = `${inspected.slice(0, 128)}...`;\n    }\n\n    const type = name.includes('.') ? 'property' : 'argument';\n\n    return `The ${type} '${name}' ${reason}. Received ${inspected}`\n  },\n  TypeError\n  // Note: extra classes have been shaken out.\n  // , RangeError\n);\n\ncodes.ERR_UNSUPPORTED_ESM_URL_SCHEME = createError(\n  'ERR_UNSUPPORTED_ESM_URL_SCHEME',\n  /**\n   * @param {URL} url\n   * @param {Array<string>} supported\n   */\n  (url, supported) => {\n    let message = `Only URLs with a scheme in: ${formatList(\n      supported\n    )} are supported by the default ESM loader`;\n\n    if (isWindows && url.protocol.length === 2) {\n      message += '. On Windows, absolute paths must be valid file:// URLs';\n    }\n\n    message += `. Received protocol '${url.protocol}'`;\n    return message\n  },\n  Error\n);\n\n/**\n * Utility function for registering the error codes. Only used here. Exported\n * *only* to allow for testing.\n * @param {string} sym\n * @param {MessageFunction | string} value\n * @param {ErrorConstructor} def\n * @returns {new (...args: Array<any>) => Error}\n */\nfunction createError(sym, value, def) {\n  // Special case for SystemError that formats the error message differently\n  // The SystemErrors only have SystemError as their base classes.\n  messages.set(sym, value);\n\n  return makeNodeErrorWithCode(def, sym)\n}\n\n/**\n * @param {ErrorConstructor} Base\n * @param {string} key\n * @returns {ErrorConstructor}\n */\nfunction makeNodeErrorWithCode(Base, key) {\n  // @ts-expect-error It’s a Node error.\n  return NodeError\n  /**\n   * @param {Array<unknown>} args\n   */\n  function NodeError(...args) {\n    const limit = Error.stackTraceLimit;\n    if (isErrorStackTraceLimitWritable()) Error.stackTraceLimit = 0;\n    const error = new Base();\n    // Reset the limit and setting the name property.\n    if (isErrorStackTraceLimitWritable()) Error.stackTraceLimit = limit;\n    const message = getMessage(key, args, error);\n    Object.defineProperties(error, {\n      // Note: no need to implement `kIsNodeError` symbol, would be hard,\n      // probably.\n      message: {\n        value: message,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      },\n      toString: {\n        /** @this {Error} */\n        value() {\n          return `${this.name} [${key}]: ${this.message}`\n        },\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n\n    captureLargerStackTrace(error);\n    // @ts-expect-error It’s a Node error.\n    error.code = key;\n    return error\n  }\n}\n\n/**\n * @returns {boolean}\n */\nfunction isErrorStackTraceLimitWritable() {\n  // Do no touch Error.stackTraceLimit as V8 would attempt to install\n  // it again during deserialization.\n  try {\n    // @ts-expect-error: not in types?\n    if (v8.startupSnapshot.isBuildingSnapshot()) {\n      return false\n    }\n  } catch {}\n\n  const desc = Object.getOwnPropertyDescriptor(Error, 'stackTraceLimit');\n  if (desc === undefined) {\n    return Object.isExtensible(Error)\n  }\n\n  return own$1.call(desc, 'writable') && desc.writable !== undefined\n    ? desc.writable\n    : desc.set !== undefined\n}\n\n/**\n * This function removes unnecessary frames from Node.js core errors.\n * @template {(...args: unknown[]) => unknown} T\n * @param {T} fn\n * @returns {T}\n */\nfunction hideStackFrames(fn) {\n  // We rename the functions that will be hidden to cut off the stacktrace\n  // at the outermost one\n  const hidden = nodeInternalPrefix + fn.name;\n  Object.defineProperty(fn, 'name', {value: hidden});\n  return fn\n}\n\nconst captureLargerStackTrace = hideStackFrames(\n  /**\n   * @param {Error} error\n   * @returns {Error}\n   */\n  // @ts-expect-error: fine\n  function (error) {\n    const stackTraceLimitIsWritable = isErrorStackTraceLimitWritable();\n    if (stackTraceLimitIsWritable) {\n      userStackTraceLimit = Error.stackTraceLimit;\n      Error.stackTraceLimit = Number.POSITIVE_INFINITY;\n    }\n\n    Error.captureStackTrace(error);\n\n    // Reset the limit\n    if (stackTraceLimitIsWritable) Error.stackTraceLimit = userStackTraceLimit;\n\n    return error\n  }\n);\n\n/**\n * @param {string} key\n * @param {Array<unknown>} args\n * @param {Error} self\n * @returns {string}\n */\nfunction getMessage(key, args, self) {\n  const message = messages.get(key);\n  assert(message !== undefined, 'expected `message` to be found');\n\n  if (typeof message === 'function') {\n    assert(\n      message.length <= args.length, // Default options do not count.\n      `Code: ${key}; The provided arguments length (${args.length}) does not ` +\n        `match the required ones (${message.length}).`\n    );\n    return Reflect.apply(message, self, args)\n  }\n\n  const regex = /%[dfijoOs]/g;\n  let expectedLength = 0;\n  while (regex.exec(message) !== null) expectedLength++;\n  assert(\n    expectedLength === args.length,\n    `Code: ${key}; The provided arguments length (${args.length}) does not ` +\n      `match the required ones (${expectedLength}).`\n  );\n  if (args.length === 0) return message\n\n  args.unshift(message);\n  return Reflect.apply(format, null, args)\n}\n\n/**\n * Determine the specific type of a value for type-mismatch errors.\n * @param {unknown} value\n * @returns {string}\n */\nfunction determineSpecificType(value) {\n  if (value === null || value === undefined) {\n    return String(value)\n  }\n\n  if (typeof value === 'function' && value.name) {\n    return `function ${value.name}`\n  }\n\n  if (typeof value === 'object') {\n    if (value.constructor && value.constructor.name) {\n      return `an instance of ${value.constructor.name}`\n    }\n\n    return `${inspect(value, {depth: -1})}`\n  }\n\n  let inspected = inspect(value, {colors: false});\n\n  if (inspected.length > 28) {\n    inspected = `${inspected.slice(0, 25)}...`;\n  }\n\n  return `type ${typeof value} (${inspected})`\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/3e74590/lib/internal/modules/package_json_reader.js>\n// Last checked on: Apr 24, 2023.\n// Removed the native dependency.\n// Also: no need to cache, we do that in resolve already.\n\n\nconst reader = {read};\nvar packageJsonReader = reader;\n\n/**\n * @param {string} jsonPath\n * @returns {{string: string | undefined}}\n */\nfunction read(jsonPath) {\n  try {\n    const string = fs.readFileSync(\n      path.toNamespacedPath(path.join(path.dirname(jsonPath), 'package.json')),\n      'utf8'\n    );\n    return {string}\n  } catch (error) {\n    const exception = /** @type {ErrnoException} */ (error);\n\n    if (exception.code === 'ENOENT') {\n      return {string: undefined}\n      // Throw all other errors.\n      /* c8 ignore next 4 */\n    }\n\n    throw exception\n  }\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/3e74590/lib/internal/modules/esm/package_config.js>\n// Last checked on: Apr 24, 2023.\n\n\nconst {ERR_INVALID_PACKAGE_CONFIG: ERR_INVALID_PACKAGE_CONFIG$1} = codes;\n\n/** @type {Map<string, PackageConfig>} */\nconst packageJsonCache = new Map();\n\n/**\n * @param {string} path\n * @param {URL | string} specifier Note: `specifier` is actually optional, not base.\n * @param {URL} [base]\n * @returns {PackageConfig}\n */\nfunction getPackageConfig(path, specifier, base) {\n  const existing = packageJsonCache.get(path);\n  if (existing !== undefined) {\n    return existing\n  }\n\n  const source = packageJsonReader.read(path).string;\n\n  if (source === undefined) {\n    /** @type {PackageConfig} */\n    const packageConfig = {\n      pjsonPath: path,\n      exists: false,\n      main: undefined,\n      name: undefined,\n      type: 'none',\n      exports: undefined,\n      imports: undefined\n    };\n    packageJsonCache.set(path, packageConfig);\n    return packageConfig\n  }\n\n  /** @type {Record<string, unknown>} */\n  let packageJson;\n  try {\n    packageJson = JSON.parse(source);\n  } catch (error) {\n    const exception = /** @type {ErrnoException} */ (error);\n\n    throw new ERR_INVALID_PACKAGE_CONFIG$1(\n      path,\n      (base ? `\"${specifier}\" from ` : '') + fileURLToPath(base || specifier),\n      exception.message\n    )\n  }\n\n  const {exports, imports, main, name, type} = packageJson;\n\n  /** @type {PackageConfig} */\n  const packageConfig = {\n    pjsonPath: path,\n    exists: true,\n    main: typeof main === 'string' ? main : undefined,\n    name: typeof name === 'string' ? name : undefined,\n    type: type === 'module' || type === 'commonjs' ? type : 'none',\n    // @ts-expect-error Assume `Record<string, unknown>`.\n    exports,\n    // @ts-expect-error Assume `Record<string, unknown>`.\n    imports: imports && typeof imports === 'object' ? imports : undefined\n  };\n  packageJsonCache.set(path, packageConfig);\n  return packageConfig\n}\n\n/**\n * @param {URL} resolved\n * @returns {PackageConfig}\n */\nfunction getPackageScopeConfig(resolved) {\n  let packageJsonUrl = new URL('package.json', resolved);\n\n  while (true) {\n    const packageJsonPath = packageJsonUrl.pathname;\n\n    if (packageJsonPath.endsWith('node_modules/package.json')) break\n\n    const packageConfig = getPackageConfig(\n      fileURLToPath(packageJsonUrl),\n      resolved\n    );\n    if (packageConfig.exists) return packageConfig\n\n    const lastPackageJsonUrl = packageJsonUrl;\n    packageJsonUrl = new URL('../package.json', packageJsonUrl);\n\n    // Terminates at root where ../package.json equals ../../package.json\n    // (can't just check \"/package.json\" for Windows support).\n    if (packageJsonUrl.pathname === lastPackageJsonUrl.pathname) break\n  }\n\n  const packageJsonPath = fileURLToPath(packageJsonUrl);\n  /** @type {PackageConfig} */\n  const packageConfig = {\n    pjsonPath: packageJsonPath,\n    exists: false,\n    main: undefined,\n    name: undefined,\n    type: 'none',\n    exports: undefined,\n    imports: undefined\n  };\n  packageJsonCache.set(packageJsonPath, packageConfig);\n  return packageConfig\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/3e74590/lib/internal/modules/esm/resolve.js>\n// Last checked on: Apr 24, 2023.\n//\n// This file solves a circular dependency.\n// In Node.js, `getPackageType` is in `resolve.js`.\n// `resolve.js` imports `get-format.js`, which needs `getPackageType`.\n// We split that up so that bundlers don’t fail.\n\n\n/**\n * @param {URL} url\n * @returns {PackageType}\n */\nfunction getPackageType(url) {\n  const packageConfig = getPackageScopeConfig(url);\n  return packageConfig.type\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/3e74590/lib/internal/modules/esm/get_format.js>\n// Last checked on: Apr 24, 2023.\n\n\nconst {ERR_UNKNOWN_FILE_EXTENSION} = codes;\n\nconst hasOwnProperty = {}.hasOwnProperty;\n\n/** @type {Record<string, string>} */\nconst extensionFormatMap = {\n  // @ts-expect-error: hush.\n  __proto__: null,\n  '.cjs': 'commonjs',\n  '.js': 'module',\n  '.json': 'json',\n  '.mjs': 'module'\n};\n\n/**\n * @param {string | null} mime\n * @returns {string | null}\n */\nfunction mimeToFormat(mime) {\n  if (\n    mime &&\n    /\\s*(text|application)\\/javascript\\s*(;\\s*charset=utf-?8\\s*)?/i.test(mime)\n  )\n    return 'module'\n  if (mime === 'application/json') return 'json'\n  return null\n}\n\n/**\n * @callback ProtocolHandler\n * @param {URL} parsed\n * @param {{parentURL: string}} context\n * @param {boolean} ignoreErrors\n * @returns {string | null | void}\n */\n\n/**\n * @type {Record<string, ProtocolHandler>}\n */\nconst protocolHandlers = {\n  // @ts-expect-error: hush.\n  __proto__: null,\n  'data:': getDataProtocolModuleFormat,\n  'file:': getFileProtocolModuleFormat,\n  'http:': getHttpProtocolModuleFormat,\n  'https:': getHttpProtocolModuleFormat,\n  'node:'() {\n    return 'builtin'\n  }\n};\n\n/**\n * @param {URL} parsed\n */\nfunction getDataProtocolModuleFormat(parsed) {\n  const {1: mime} = /^([^/]+\\/[^;,]+)[^,]*?(;base64)?,/.exec(\n    parsed.pathname\n  ) || [null, null, null];\n  return mimeToFormat(mime)\n}\n\n/**\n * Returns the file extension from a URL.\n *\n * Should give similar result to\n * `require('node:path').extname(require('node:url').fileURLToPath(url))`\n * when used with a `file:` URL.\n *\n * @param {URL} url\n * @returns {string}\n */\nfunction extname(url) {\n  const pathname = url.pathname;\n  let index = pathname.length;\n\n  while (index--) {\n    const code = pathname.codePointAt(index);\n\n    if (code === 47 /* `/` */) {\n      return ''\n    }\n\n    if (code === 46 /* `.` */) {\n      return pathname.codePointAt(index - 1) === 47 /* `/` */\n        ? ''\n        : pathname.slice(index)\n    }\n  }\n\n  return ''\n}\n\n/**\n * @type {ProtocolHandler}\n */\nfunction getFileProtocolModuleFormat(url, _context, ignoreErrors) {\n  const ext = extname(url);\n\n  if (ext === '.js') {\n    return getPackageType(url) === 'module' ? 'module' : 'commonjs'\n  }\n\n  const format = extensionFormatMap[ext];\n  if (format) return format\n\n  // Explicit undefined return indicates load hook should rerun format check\n  if (ignoreErrors) {\n    return undefined\n  }\n\n  const filepath = fileURLToPath(url);\n  throw new ERR_UNKNOWN_FILE_EXTENSION(ext, filepath)\n}\n\nfunction getHttpProtocolModuleFormat() {\n  // To do: HTTPS imports.\n}\n\n/**\n * @param {URL} url\n * @param {{parentURL: string}} context\n * @returns {string | null}\n */\nfunction defaultGetFormatWithoutErrors(url, context) {\n  if (!hasOwnProperty.call(protocolHandlers, url.protocol)) {\n    return null\n  }\n\n  return protocolHandlers[url.protocol](url, context, true) || null\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/3e74590/lib/internal/modules/esm/utils.js>\n// Last checked on: Apr 24, 2023.\n\n\nconst {ERR_INVALID_ARG_VALUE} = codes;\n\n// In Node itself these values are populated from CLI arguments, before any\n// user code runs.\n// Here we just define the defaults.\nconst DEFAULT_CONDITIONS = Object.freeze(['node', 'import']);\nconst DEFAULT_CONDITIONS_SET = new Set(DEFAULT_CONDITIONS);\n\nfunction getDefaultConditions() {\n  return DEFAULT_CONDITIONS\n}\n\nfunction getDefaultConditionsSet() {\n  return DEFAULT_CONDITIONS_SET\n}\n\n/**\n * @param {Array<string>} [conditions]\n * @returns {Set<string>}\n */\nfunction getConditionsSet(conditions) {\n  if (conditions !== undefined && conditions !== getDefaultConditions()) {\n    if (!Array.isArray(conditions)) {\n      throw new ERR_INVALID_ARG_VALUE(\n        'conditions',\n        conditions,\n        'expected an array'\n      )\n    }\n\n    return new Set(conditions)\n  }\n\n  return getDefaultConditionsSet()\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/3e74590/lib/internal/modules/esm/resolve.js>\n// Last checked on: Apr 24, 2023.\n\n\nconst RegExpPrototypeSymbolReplace = RegExp.prototype[Symbol.replace];\n\n// To do: potentially enable?\nconst experimentalNetworkImports = false;\n\nconst {\n  ERR_NETWORK_IMPORT_DISALLOWED,\n  ERR_INVALID_MODULE_SPECIFIER,\n  ERR_INVALID_PACKAGE_CONFIG,\n  ERR_INVALID_PACKAGE_TARGET,\n  ERR_MODULE_NOT_FOUND,\n  ERR_PACKAGE_IMPORT_NOT_DEFINED,\n  ERR_PACKAGE_PATH_NOT_EXPORTED,\n  ERR_UNSUPPORTED_DIR_IMPORT,\n  ERR_UNSUPPORTED_ESM_URL_SCHEME\n} = codes;\n\nconst own = {}.hasOwnProperty;\n\nconst invalidSegmentRegEx =\n  /(^|\\\\|\\/)((\\.|%2e)(\\.|%2e)?|(n|%6e|%4e)(o|%6f|%4f)(d|%64|%44)(e|%65|%45)(_|%5f)(m|%6d|%4d)(o|%6f|%4f)(d|%64|%44)(u|%75|%55)(l|%6c|%4c)(e|%65|%45)(s|%73|%53))?(\\\\|\\/|$)/i;\nconst deprecatedInvalidSegmentRegEx =\n  /(^|\\\\|\\/)((\\.|%2e)(\\.|%2e)?|(n|%6e|%4e)(o|%6f|%4f)(d|%64|%44)(e|%65|%45)(_|%5f)(m|%6d|%4d)(o|%6f|%4f)(d|%64|%44)(u|%75|%55)(l|%6c|%4c)(e|%65|%45)(s|%73|%53))(\\\\|\\/|$)/i;\nconst invalidPackageNameRegEx = /^\\.|%|\\\\/;\nconst patternRegEx = /\\*/g;\nconst encodedSepRegEx = /%2f|%5c/i;\n/** @type {Set<string>} */\nconst emittedPackageWarnings = new Set();\n\nconst doubleSlashRegEx = /[/\\\\]{2}/;\n\n/**\n *\n * @param {string} target\n * @param {string} request\n * @param {string} match\n * @param {URL} packageJsonUrl\n * @param {boolean} internal\n * @param {URL} base\n * @param {boolean} isTarget\n */\nfunction emitInvalidSegmentDeprecation(\n  target,\n  request,\n  match,\n  packageJsonUrl,\n  internal,\n  base,\n  isTarget\n) {\n  const pjsonPath = fileURLToPath(packageJsonUrl);\n  const double = doubleSlashRegEx.exec(isTarget ? target : request) !== null;\n  process.emitWarning(\n    `Use of deprecated ${\n      double ? 'double slash' : 'leading or trailing slash matching'\n    } resolving \"${target}\" for module ` +\n      `request \"${request}\" ${\n        request === match ? '' : `matched to \"${match}\" `\n      }in the \"${\n        internal ? 'imports' : 'exports'\n      }\" field module resolution of the package at ${pjsonPath}${\n        base ? ` imported from ${fileURLToPath(base)}` : ''\n      }.`,\n    'DeprecationWarning',\n    'DEP0166'\n  );\n}\n\n/**\n * @param {URL} url\n * @param {URL} packageJsonUrl\n * @param {URL} base\n * @param {unknown} [main]\n * @returns {void}\n */\nfunction emitLegacyIndexDeprecation(url, packageJsonUrl, base, main) {\n  const format = defaultGetFormatWithoutErrors(url, {parentURL: base.href});\n  if (format !== 'module') return\n  const path = fileURLToPath(url.href);\n  const pkgPath = fileURLToPath(new URL('.', packageJsonUrl));\n  const basePath = fileURLToPath(base);\n  if (main)\n    process.emitWarning(\n      `Package ${pkgPath} has a \"main\" field set to ${JSON.stringify(main)}, ` +\n        `excluding the full filename and extension to the resolved file at \"${path.slice(\n          pkgPath.length\n        )}\", imported from ${basePath}.\\n Automatic extension resolution of the \"main\" field is` +\n        'deprecated for ES modules.',\n      'DeprecationWarning',\n      'DEP0151'\n    );\n  else\n    process.emitWarning(\n      `No \"main\" or \"exports\" field defined in the package.json for ${pkgPath} resolving the main entry point \"${path.slice(\n        pkgPath.length\n      )}\", imported from ${basePath}.\\nDefault \"index\" lookups for the main are deprecated for ES modules.`,\n      'DeprecationWarning',\n      'DEP0151'\n    );\n}\n\n/**\n * @param {string} path\n * @returns {Stats}\n */\nfunction tryStatSync(path) {\n  // Note: from Node 15 onwards we can use `throwIfNoEntry: false` instead.\n  try {\n    return statSync(path)\n  } catch {\n    return new Stats()\n  }\n}\n\n/**\n * Legacy CommonJS main resolution:\n * 1. let M = pkg_url + (json main field)\n * 2. TRY(M, M.js, M.json, M.node)\n * 3. TRY(M/index.js, M/index.json, M/index.node)\n * 4. TRY(pkg_url/index.js, pkg_url/index.json, pkg_url/index.node)\n * 5. NOT_FOUND\n *\n * @param {URL} url\n * @returns {boolean}\n */\nfunction fileExists(url) {\n  const stats = statSync(url, {throwIfNoEntry: false});\n  const isFile = stats ? stats.isFile() : undefined;\n  return isFile === null || isFile === undefined ? false : isFile\n}\n\n/**\n * @param {URL} packageJsonUrl\n * @param {PackageConfig} packageConfig\n * @param {URL} base\n * @returns {URL}\n */\nfunction legacyMainResolve(packageJsonUrl, packageConfig, base) {\n  /** @type {URL | undefined} */\n  let guess;\n  if (packageConfig.main !== undefined) {\n    guess = new URL(packageConfig.main, packageJsonUrl);\n    // Note: fs check redundances will be handled by Descriptor cache here.\n    if (fileExists(guess)) return guess\n\n    const tries = [\n      `./${packageConfig.main}.js`,\n      `./${packageConfig.main}.json`,\n      `./${packageConfig.main}.node`,\n      `./${packageConfig.main}/index.js`,\n      `./${packageConfig.main}/index.json`,\n      `./${packageConfig.main}/index.node`\n    ];\n    let i = -1;\n\n    while (++i < tries.length) {\n      guess = new URL(tries[i], packageJsonUrl);\n      if (fileExists(guess)) break\n      guess = undefined;\n    }\n\n    if (guess) {\n      emitLegacyIndexDeprecation(\n        guess,\n        packageJsonUrl,\n        base,\n        packageConfig.main\n      );\n      return guess\n    }\n    // Fallthrough.\n  }\n\n  const tries = ['./index.js', './index.json', './index.node'];\n  let i = -1;\n\n  while (++i < tries.length) {\n    guess = new URL(tries[i], packageJsonUrl);\n    if (fileExists(guess)) break\n    guess = undefined;\n  }\n\n  if (guess) {\n    emitLegacyIndexDeprecation(guess, packageJsonUrl, base, packageConfig.main);\n    return guess\n  }\n\n  // Not found.\n  throw new ERR_MODULE_NOT_FOUND(\n    fileURLToPath(new URL('.', packageJsonUrl)),\n    fileURLToPath(base)\n  )\n}\n\n/**\n * @param {URL} resolved\n * @param {URL} base\n * @param {boolean} [preserveSymlinks]\n * @returns {URL}\n */\nfunction finalizeResolution(resolved, base, preserveSymlinks) {\n  if (encodedSepRegEx.exec(resolved.pathname) !== null)\n    throw new ERR_INVALID_MODULE_SPECIFIER(\n      resolved.pathname,\n      'must not include encoded \"/\" or \"\\\\\" characters',\n      fileURLToPath(base)\n    )\n\n  const filePath = fileURLToPath(resolved);\n\n  const stats = tryStatSync(\n    filePath.endsWith('/') ? filePath.slice(-1) : filePath\n  );\n\n  if (stats.isDirectory()) {\n    const error = new ERR_UNSUPPORTED_DIR_IMPORT(filePath, fileURLToPath(base));\n    // @ts-expect-error Add this for `import.meta.resolve`.\n    error.url = String(resolved);\n    throw error\n  }\n\n  if (!stats.isFile()) {\n    throw new ERR_MODULE_NOT_FOUND(\n      filePath || resolved.pathname,\n      base && fileURLToPath(base),\n      'module'\n    )\n  }\n\n  if (!preserveSymlinks) {\n    const real = realpathSync(filePath);\n    const {search, hash} = resolved;\n    resolved = pathToFileURL(real + (filePath.endsWith(path.sep) ? '/' : ''));\n    resolved.search = search;\n    resolved.hash = hash;\n  }\n\n  return resolved\n}\n\n/**\n * @param {string} specifier\n * @param {URL | undefined} packageJsonUrl\n * @param {URL} base\n * @returns {Error}\n */\nfunction importNotDefined(specifier, packageJsonUrl, base) {\n  return new ERR_PACKAGE_IMPORT_NOT_DEFINED(\n    specifier,\n    packageJsonUrl && fileURLToPath(new URL('.', packageJsonUrl)),\n    fileURLToPath(base)\n  )\n}\n\n/**\n * @param {string} subpath\n * @param {URL} packageJsonUrl\n * @param {URL} base\n * @returns {Error}\n */\nfunction exportsNotFound(subpath, packageJsonUrl, base) {\n  return new ERR_PACKAGE_PATH_NOT_EXPORTED(\n    fileURLToPath(new URL('.', packageJsonUrl)),\n    subpath,\n    base && fileURLToPath(base)\n  )\n}\n\n/**\n * @param {string} request\n * @param {string} match\n * @param {URL} packageJsonUrl\n * @param {boolean} internal\n * @param {URL} [base]\n * @returns {never}\n */\nfunction throwInvalidSubpath(request, match, packageJsonUrl, internal, base) {\n  const reason = `request is not a valid match in pattern \"${match}\" for the \"${\n    internal ? 'imports' : 'exports'\n  }\" resolution of ${fileURLToPath(packageJsonUrl)}`;\n  throw new ERR_INVALID_MODULE_SPECIFIER(\n    request,\n    reason,\n    base && fileURLToPath(base)\n  )\n}\n\n/**\n * @param {string} subpath\n * @param {unknown} target\n * @param {URL} packageJsonUrl\n * @param {boolean} internal\n * @param {URL} [base]\n * @returns {Error}\n */\nfunction invalidPackageTarget(subpath, target, packageJsonUrl, internal, base) {\n  target =\n    typeof target === 'object' && target !== null\n      ? JSON.stringify(target, null, '')\n      : `${target}`;\n\n  return new ERR_INVALID_PACKAGE_TARGET(\n    fileURLToPath(new URL('.', packageJsonUrl)),\n    subpath,\n    target,\n    internal,\n    base && fileURLToPath(base)\n  )\n}\n\n/**\n * @param {string} target\n * @param {string} subpath\n * @param {string} match\n * @param {URL} packageJsonUrl\n * @param {URL} base\n * @param {boolean} pattern\n * @param {boolean} internal\n * @param {boolean} isPathMap\n * @param {Set<string> | undefined} conditions\n * @returns {URL}\n */\nfunction resolvePackageTargetString(\n  target,\n  subpath,\n  match,\n  packageJsonUrl,\n  base,\n  pattern,\n  internal,\n  isPathMap,\n  conditions\n) {\n  if (subpath !== '' && !pattern && target[target.length - 1] !== '/')\n    throw invalidPackageTarget(match, target, packageJsonUrl, internal, base)\n\n  if (!target.startsWith('./')) {\n    if (internal && !target.startsWith('../') && !target.startsWith('/')) {\n      let isURL = false;\n\n      try {\n        new URL(target);\n        isURL = true;\n      } catch {\n        // Continue regardless of error.\n      }\n\n      if (!isURL) {\n        const exportTarget = pattern\n          ? RegExpPrototypeSymbolReplace.call(\n              patternRegEx,\n              target,\n              () => subpath\n            )\n          : target + subpath;\n\n        return packageResolve(exportTarget, packageJsonUrl, conditions)\n      }\n    }\n\n    throw invalidPackageTarget(match, target, packageJsonUrl, internal, base)\n  }\n\n  if (invalidSegmentRegEx.exec(target.slice(2)) !== null) {\n    if (deprecatedInvalidSegmentRegEx.exec(target.slice(2)) === null) {\n      if (!isPathMap) {\n        const request = pattern\n          ? match.replace('*', () => subpath)\n          : match + subpath;\n        const resolvedTarget = pattern\n          ? RegExpPrototypeSymbolReplace.call(\n              patternRegEx,\n              target,\n              () => subpath\n            )\n          : target;\n        emitInvalidSegmentDeprecation(\n          resolvedTarget,\n          request,\n          match,\n          packageJsonUrl,\n          internal,\n          base,\n          true\n        );\n      }\n    } else {\n      throw invalidPackageTarget(match, target, packageJsonUrl, internal, base)\n    }\n  }\n\n  const resolved = new URL(target, packageJsonUrl);\n  const resolvedPath = resolved.pathname;\n  const packagePath = new URL('.', packageJsonUrl).pathname;\n\n  if (!resolvedPath.startsWith(packagePath))\n    throw invalidPackageTarget(match, target, packageJsonUrl, internal, base)\n\n  if (subpath === '') return resolved\n\n  if (invalidSegmentRegEx.exec(subpath) !== null) {\n    const request = pattern\n      ? match.replace('*', () => subpath)\n      : match + subpath;\n    if (deprecatedInvalidSegmentRegEx.exec(subpath) === null) {\n      if (!isPathMap) {\n        const resolvedTarget = pattern\n          ? RegExpPrototypeSymbolReplace.call(\n              patternRegEx,\n              target,\n              () => subpath\n            )\n          : target;\n        emitInvalidSegmentDeprecation(\n          resolvedTarget,\n          request,\n          match,\n          packageJsonUrl,\n          internal,\n          base,\n          false\n        );\n      }\n    } else {\n      throwInvalidSubpath(request, match, packageJsonUrl, internal, base);\n    }\n  }\n\n  if (pattern) {\n    return new URL(\n      RegExpPrototypeSymbolReplace.call(\n        patternRegEx,\n        resolved.href,\n        () => subpath\n      )\n    )\n  }\n\n  return new URL(subpath, resolved)\n}\n\n/**\n * @param {string} key\n * @returns {boolean}\n */\nfunction isArrayIndex(key) {\n  const keyNumber = Number(key);\n  if (`${keyNumber}` !== key) return false\n  return keyNumber >= 0 && keyNumber < 0xff_ff_ff_ff\n}\n\n/**\n * @param {URL} packageJsonUrl\n * @param {unknown} target\n * @param {string} subpath\n * @param {string} packageSubpath\n * @param {URL} base\n * @param {boolean} pattern\n * @param {boolean} internal\n * @param {boolean} isPathMap\n * @param {Set<string> | undefined} conditions\n * @returns {URL | null}\n */\nfunction resolvePackageTarget(\n  packageJsonUrl,\n  target,\n  subpath,\n  packageSubpath,\n  base,\n  pattern,\n  internal,\n  isPathMap,\n  conditions\n) {\n  if (typeof target === 'string') {\n    return resolvePackageTargetString(\n      target,\n      subpath,\n      packageSubpath,\n      packageJsonUrl,\n      base,\n      pattern,\n      internal,\n      isPathMap,\n      conditions\n    )\n  }\n\n  if (Array.isArray(target)) {\n    /** @type {Array<unknown>} */\n    const targetList = target;\n    if (targetList.length === 0) return null\n\n    /** @type {ErrnoException | null | undefined} */\n    let lastException;\n    let i = -1;\n\n    while (++i < targetList.length) {\n      const targetItem = targetList[i];\n      /** @type {URL | null} */\n      let resolveResult;\n      try {\n        resolveResult = resolvePackageTarget(\n          packageJsonUrl,\n          targetItem,\n          subpath,\n          packageSubpath,\n          base,\n          pattern,\n          internal,\n          isPathMap,\n          conditions\n        );\n      } catch (error) {\n        const exception = /** @type {ErrnoException} */ (error);\n        lastException = exception;\n        if (exception.code === 'ERR_INVALID_PACKAGE_TARGET') continue\n        throw error\n      }\n\n      if (resolveResult === undefined) continue\n\n      if (resolveResult === null) {\n        lastException = null;\n        continue\n      }\n\n      return resolveResult\n    }\n\n    if (lastException === undefined || lastException === null) {\n      return null\n    }\n\n    throw lastException\n  }\n\n  if (typeof target === 'object' && target !== null) {\n    const keys = Object.getOwnPropertyNames(target);\n    let i = -1;\n\n    while (++i < keys.length) {\n      const key = keys[i];\n      if (isArrayIndex(key)) {\n        throw new ERR_INVALID_PACKAGE_CONFIG(\n          fileURLToPath(packageJsonUrl),\n          base,\n          '\"exports\" cannot contain numeric property keys.'\n        )\n      }\n    }\n\n    i = -1;\n\n    while (++i < keys.length) {\n      const key = keys[i];\n      if (key === 'default' || (conditions && conditions.has(key))) {\n        // @ts-expect-error: indexable.\n        const conditionalTarget = /** @type {unknown} */ (target[key]);\n        const resolveResult = resolvePackageTarget(\n          packageJsonUrl,\n          conditionalTarget,\n          subpath,\n          packageSubpath,\n          base,\n          pattern,\n          internal,\n          isPathMap,\n          conditions\n        );\n        if (resolveResult === undefined) continue\n        return resolveResult\n      }\n    }\n\n    return null\n  }\n\n  if (target === null) {\n    return null\n  }\n\n  throw invalidPackageTarget(\n    packageSubpath,\n    target,\n    packageJsonUrl,\n    internal,\n    base\n  )\n}\n\n/**\n * @param {unknown} exports\n * @param {URL} packageJsonUrl\n * @param {URL} base\n * @returns {boolean}\n */\nfunction isConditionalExportsMainSugar(exports, packageJsonUrl, base) {\n  if (typeof exports === 'string' || Array.isArray(exports)) return true\n  if (typeof exports !== 'object' || exports === null) return false\n\n  const keys = Object.getOwnPropertyNames(exports);\n  let isConditionalSugar = false;\n  let i = 0;\n  let j = -1;\n  while (++j < keys.length) {\n    const key = keys[j];\n    const curIsConditionalSugar = key === '' || key[0] !== '.';\n    if (i++ === 0) {\n      isConditionalSugar = curIsConditionalSugar;\n    } else if (isConditionalSugar !== curIsConditionalSugar) {\n      throw new ERR_INVALID_PACKAGE_CONFIG(\n        fileURLToPath(packageJsonUrl),\n        base,\n        '\"exports\" cannot contain some keys starting with \\'.\\' and some not.' +\n          ' The exports object must either be an object of package subpath keys' +\n          ' or an object of main entry condition name keys only.'\n      )\n    }\n  }\n\n  return isConditionalSugar\n}\n\n/**\n * @param {string} match\n * @param {URL} pjsonUrl\n * @param {URL} base\n */\nfunction emitTrailingSlashPatternDeprecation(match, pjsonUrl, base) {\n  const pjsonPath = fileURLToPath(pjsonUrl);\n  if (emittedPackageWarnings.has(pjsonPath + '|' + match)) return\n  emittedPackageWarnings.add(pjsonPath + '|' + match);\n  process.emitWarning(\n    `Use of deprecated trailing slash pattern mapping \"${match}\" in the ` +\n      `\"exports\" field module resolution of the package at ${pjsonPath}${\n        base ? ` imported from ${fileURLToPath(base)}` : ''\n      }. Mapping specifiers ending in \"/\" is no longer supported.`,\n    'DeprecationWarning',\n    'DEP0155'\n  );\n}\n\n/**\n * @param {URL} packageJsonUrl\n * @param {string} packageSubpath\n * @param {Record<string, unknown>} packageConfig\n * @param {URL} base\n * @param {Set<string> | undefined} conditions\n * @returns {URL}\n */\nfunction packageExportsResolve(\n  packageJsonUrl,\n  packageSubpath,\n  packageConfig,\n  base,\n  conditions\n) {\n  let exports = packageConfig.exports;\n\n  if (isConditionalExportsMainSugar(exports, packageJsonUrl, base)) {\n    exports = {'.': exports};\n  }\n\n  if (\n    own.call(exports, packageSubpath) &&\n    !packageSubpath.includes('*') &&\n    !packageSubpath.endsWith('/')\n  ) {\n    // @ts-expect-error: indexable.\n    const target = exports[packageSubpath];\n    const resolveResult = resolvePackageTarget(\n      packageJsonUrl,\n      target,\n      '',\n      packageSubpath,\n      base,\n      false,\n      false,\n      false,\n      conditions\n    );\n    if (resolveResult === null || resolveResult === undefined) {\n      throw exportsNotFound(packageSubpath, packageJsonUrl, base)\n    }\n\n    return resolveResult\n  }\n\n  let bestMatch = '';\n  let bestMatchSubpath = '';\n  const keys = Object.getOwnPropertyNames(exports);\n  let i = -1;\n\n  while (++i < keys.length) {\n    const key = keys[i];\n    const patternIndex = key.indexOf('*');\n\n    if (\n      patternIndex !== -1 &&\n      packageSubpath.startsWith(key.slice(0, patternIndex))\n    ) {\n      // When this reaches EOL, this can throw at the top of the whole function:\n      //\n      // if (StringPrototypeEndsWith(packageSubpath, '/'))\n      //   throwInvalidSubpath(packageSubpath)\n      //\n      // To match \"imports\" and the spec.\n      if (packageSubpath.endsWith('/')) {\n        emitTrailingSlashPatternDeprecation(\n          packageSubpath,\n          packageJsonUrl,\n          base\n        );\n      }\n\n      const patternTrailer = key.slice(patternIndex + 1);\n\n      if (\n        packageSubpath.length >= key.length &&\n        packageSubpath.endsWith(patternTrailer) &&\n        patternKeyCompare(bestMatch, key) === 1 &&\n        key.lastIndexOf('*') === patternIndex\n      ) {\n        bestMatch = key;\n        bestMatchSubpath = packageSubpath.slice(\n          patternIndex,\n          packageSubpath.length - patternTrailer.length\n        );\n      }\n    }\n  }\n\n  if (bestMatch) {\n    // @ts-expect-error: indexable.\n    const target = /** @type {unknown} */ (exports[bestMatch]);\n    const resolveResult = resolvePackageTarget(\n      packageJsonUrl,\n      target,\n      bestMatchSubpath,\n      bestMatch,\n      base,\n      true,\n      false,\n      packageSubpath.endsWith('/'),\n      conditions\n    );\n\n    if (resolveResult === null || resolveResult === undefined) {\n      throw exportsNotFound(packageSubpath, packageJsonUrl, base)\n    }\n\n    return resolveResult\n  }\n\n  throw exportsNotFound(packageSubpath, packageJsonUrl, base)\n}\n\n/**\n * @param {string} a\n * @param {string} b\n */\nfunction patternKeyCompare(a, b) {\n  const aPatternIndex = a.indexOf('*');\n  const bPatternIndex = b.indexOf('*');\n  const baseLengthA = aPatternIndex === -1 ? a.length : aPatternIndex + 1;\n  const baseLengthB = bPatternIndex === -1 ? b.length : bPatternIndex + 1;\n  if (baseLengthA > baseLengthB) return -1\n  if (baseLengthB > baseLengthA) return 1\n  if (aPatternIndex === -1) return 1\n  if (bPatternIndex === -1) return -1\n  if (a.length > b.length) return -1\n  if (b.length > a.length) return 1\n  return 0\n}\n\n/**\n * @param {string} name\n * @param {URL} base\n * @param {Set<string>} [conditions]\n * @returns {URL}\n */\nfunction packageImportsResolve(name, base, conditions) {\n  if (name === '#' || name.startsWith('#/') || name.endsWith('/')) {\n    const reason = 'is not a valid internal imports specifier name';\n    throw new ERR_INVALID_MODULE_SPECIFIER(name, reason, fileURLToPath(base))\n  }\n\n  /** @type {URL | undefined} */\n  let packageJsonUrl;\n\n  const packageConfig = getPackageScopeConfig(base);\n\n  if (packageConfig.exists) {\n    packageJsonUrl = pathToFileURL(packageConfig.pjsonPath);\n    const imports = packageConfig.imports;\n    if (imports) {\n      if (own.call(imports, name) && !name.includes('*')) {\n        const resolveResult = resolvePackageTarget(\n          packageJsonUrl,\n          imports[name],\n          '',\n          name,\n          base,\n          false,\n          true,\n          false,\n          conditions\n        );\n        if (resolveResult !== null && resolveResult !== undefined) {\n          return resolveResult\n        }\n      } else {\n        let bestMatch = '';\n        let bestMatchSubpath = '';\n        const keys = Object.getOwnPropertyNames(imports);\n        let i = -1;\n\n        while (++i < keys.length) {\n          const key = keys[i];\n          const patternIndex = key.indexOf('*');\n\n          if (patternIndex !== -1 && name.startsWith(key.slice(0, -1))) {\n            const patternTrailer = key.slice(patternIndex + 1);\n            if (\n              name.length >= key.length &&\n              name.endsWith(patternTrailer) &&\n              patternKeyCompare(bestMatch, key) === 1 &&\n              key.lastIndexOf('*') === patternIndex\n            ) {\n              bestMatch = key;\n              bestMatchSubpath = name.slice(\n                patternIndex,\n                name.length - patternTrailer.length\n              );\n            }\n          }\n        }\n\n        if (bestMatch) {\n          const target = imports[bestMatch];\n          const resolveResult = resolvePackageTarget(\n            packageJsonUrl,\n            target,\n            bestMatchSubpath,\n            bestMatch,\n            base,\n            true,\n            true,\n            false,\n            conditions\n          );\n\n          if (resolveResult !== null && resolveResult !== undefined) {\n            return resolveResult\n          }\n        }\n      }\n    }\n  }\n\n  throw importNotDefined(name, packageJsonUrl, base)\n}\n\n// Note: In Node.js, `getPackageType` is here.\n// To prevent a circular dependency, we move it to\n// `resolve-get-package-type.js`.\n\n/**\n * @param {string} specifier\n * @param {URL} base\n */\nfunction parsePackageName(specifier, base) {\n  let separatorIndex = specifier.indexOf('/');\n  let validPackageName = true;\n  let isScoped = false;\n  if (specifier[0] === '@') {\n    isScoped = true;\n    if (separatorIndex === -1 || specifier.length === 0) {\n      validPackageName = false;\n    } else {\n      separatorIndex = specifier.indexOf('/', separatorIndex + 1);\n    }\n  }\n\n  const packageName =\n    separatorIndex === -1 ? specifier : specifier.slice(0, separatorIndex);\n\n  // Package name cannot have leading . and cannot have percent-encoding or\n  // \\\\ separators.\n  if (invalidPackageNameRegEx.exec(packageName) !== null) {\n    validPackageName = false;\n  }\n\n  if (!validPackageName) {\n    throw new ERR_INVALID_MODULE_SPECIFIER(\n      specifier,\n      'is not a valid package name',\n      fileURLToPath(base)\n    )\n  }\n\n  const packageSubpath =\n    '.' + (separatorIndex === -1 ? '' : specifier.slice(separatorIndex));\n\n  return {packageName, packageSubpath, isScoped}\n}\n\n/**\n * @param {string} specifier\n * @param {URL} base\n * @param {Set<string> | undefined} conditions\n * @returns {URL}\n */\nfunction packageResolve(specifier, base, conditions) {\n  if (builtinModules.includes(specifier)) {\n    return new URL('node:' + specifier)\n  }\n\n  const {packageName, packageSubpath, isScoped} = parsePackageName(\n    specifier,\n    base\n  );\n\n  // ResolveSelf\n  const packageConfig = getPackageScopeConfig(base);\n\n  // Can’t test.\n  /* c8 ignore next 16 */\n  if (packageConfig.exists) {\n    const packageJsonUrl = pathToFileURL(packageConfig.pjsonPath);\n    if (\n      packageConfig.name === packageName &&\n      packageConfig.exports !== undefined &&\n      packageConfig.exports !== null\n    ) {\n      return packageExportsResolve(\n        packageJsonUrl,\n        packageSubpath,\n        packageConfig,\n        base,\n        conditions\n      )\n    }\n  }\n\n  let packageJsonUrl = new URL(\n    './node_modules/' + packageName + '/package.json',\n    base\n  );\n  let packageJsonPath = fileURLToPath(packageJsonUrl);\n  /** @type {string} */\n  let lastPath;\n  do {\n    const stat = tryStatSync(packageJsonPath.slice(0, -13));\n    if (!stat.isDirectory()) {\n      lastPath = packageJsonPath;\n      packageJsonUrl = new URL(\n        (isScoped ? '../../../../node_modules/' : '../../../node_modules/') +\n          packageName +\n          '/package.json',\n        packageJsonUrl\n      );\n      packageJsonPath = fileURLToPath(packageJsonUrl);\n      continue\n    }\n\n    // Package match.\n    const packageConfig = getPackageConfig(packageJsonPath, specifier, base);\n    if (packageConfig.exports !== undefined && packageConfig.exports !== null) {\n      return packageExportsResolve(\n        packageJsonUrl,\n        packageSubpath,\n        packageConfig,\n        base,\n        conditions\n      )\n    }\n\n    if (packageSubpath === '.') {\n      return legacyMainResolve(packageJsonUrl, packageConfig, base)\n    }\n\n    return new URL(packageSubpath, packageJsonUrl)\n    // Cross-platform root check.\n  } while (packageJsonPath.length !== lastPath.length)\n\n  throw new ERR_MODULE_NOT_FOUND(packageName, fileURLToPath(base))\n}\n\n/**\n * @param {string} specifier\n * @returns {boolean}\n */\nfunction isRelativeSpecifier(specifier) {\n  if (specifier[0] === '.') {\n    if (specifier.length === 1 || specifier[1] === '/') return true\n    if (\n      specifier[1] === '.' &&\n      (specifier.length === 2 || specifier[2] === '/')\n    ) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * @param {string} specifier\n * @returns {boolean}\n */\nfunction shouldBeTreatedAsRelativeOrAbsolutePath(specifier) {\n  if (specifier === '') return false\n  if (specifier[0] === '/') return true\n  return isRelativeSpecifier(specifier)\n}\n\n/**\n * The “Resolver Algorithm Specification” as detailed in the Node docs (which is\n * sync and slightly lower-level than `resolve`).\n *\n * @param {string} specifier\n *   `/example.js`, `./example.js`, `../example.js`, `some-package`, `fs`, etc.\n * @param {URL} base\n *   Full URL (to a file) that `specifier` is resolved relative from.\n * @param {Set<string>} [conditions]\n *   Conditions.\n * @param {boolean} [preserveSymlinks]\n *   Keep symlinks instead of resolving them.\n * @returns {URL}\n *   A URL object to the found thing.\n */\nfunction moduleResolve(specifier, base, conditions, preserveSymlinks) {\n  const protocol = base.protocol;\n  const isRemote = protocol === 'http:' || protocol === 'https:';\n  // Order swapped from spec for minor perf gain.\n  // Ok since relative URLs cannot parse as URLs.\n  /** @type {URL | undefined} */\n  let resolved;\n\n  if (shouldBeTreatedAsRelativeOrAbsolutePath(specifier)) {\n    resolved = new URL(specifier, base);\n  } else if (!isRemote && specifier[0] === '#') {\n    resolved = packageImportsResolve(specifier, base, conditions);\n  } else {\n    try {\n      resolved = new URL(specifier);\n    } catch {\n      if (!isRemote) {\n        resolved = packageResolve(specifier, base, conditions);\n      }\n    }\n  }\n\n  assert(resolved !== undefined, 'expected to be defined');\n\n  if (resolved.protocol !== 'file:') {\n    return resolved\n  }\n\n  return finalizeResolution(resolved, base, preserveSymlinks)\n}\n\n/**\n * @param {string} specifier\n * @param {URL | undefined} parsed\n * @param {URL | undefined} parsedParentURL\n */\nfunction checkIfDisallowedImport(specifier, parsed, parsedParentURL) {\n  if (parsedParentURL) {\n    // Avoid accessing the `protocol` property due to the lazy getters.\n    const parentProtocol = parsedParentURL.protocol;\n\n    if (parentProtocol === 'http:' || parentProtocol === 'https:') {\n      if (shouldBeTreatedAsRelativeOrAbsolutePath(specifier)) {\n        // Avoid accessing the `protocol` property due to the lazy getters.\n        const parsedProtocol = parsed?.protocol;\n\n        // `data:` and `blob:` disallowed due to allowing file: access via\n        // indirection\n        if (\n          parsedProtocol &&\n          parsedProtocol !== 'https:' &&\n          parsedProtocol !== 'http:'\n        ) {\n          throw new ERR_NETWORK_IMPORT_DISALLOWED(\n            specifier,\n            parsedParentURL,\n            'remote imports cannot import from a local location.'\n          )\n        }\n\n        return {url: parsed?.href || ''}\n      }\n\n      if (builtinModules.includes(specifier)) {\n        throw new ERR_NETWORK_IMPORT_DISALLOWED(\n          specifier,\n          parsedParentURL,\n          'remote imports cannot import from a local location.'\n        )\n      }\n\n      throw new ERR_NETWORK_IMPORT_DISALLOWED(\n        specifier,\n        parsedParentURL,\n        'only relative and absolute specifiers are supported.'\n      )\n    }\n  }\n}\n\n// Note: this is from:\n// <https://github.com/nodejs/node/blob/3e74590/lib/internal/url.js#L687>\n/**\n * Checks if a value has the shape of a WHATWG URL object.\n *\n * Using a symbol or instanceof would not be able to recognize URL objects\n * coming from other implementations (e.g. in Electron), so instead we are\n * checking some well known properties for a lack of a better test.\n *\n * We use `href` and `protocol` as they are the only properties that are\n * easy to retrieve and calculate due to the lazy nature of the getters.\n *\n * @template {unknown} Value\n * @param {Value} self\n * @returns {Value is URL}\n */\nfunction isURL(self) {\n  return Boolean(\n    self &&\n      typeof self === 'object' &&\n      'href' in self &&\n      typeof self.href === 'string' &&\n      'protocol' in self &&\n      typeof self.protocol === 'string' &&\n      self.href &&\n      self.protocol\n  )\n}\n\n/**\n * Validate user-input in `context` supplied by a custom loader.\n *\n * @param {unknown} parentURL\n * @returns {asserts parentURL is URL | string | undefined}\n */\nfunction throwIfInvalidParentURL(parentURL) {\n  if (parentURL === undefined) {\n    return // Main entry point, so no parent\n  }\n\n  if (typeof parentURL !== 'string' && !isURL(parentURL)) {\n    throw new codes.ERR_INVALID_ARG_TYPE(\n      'parentURL',\n      ['string', 'URL'],\n      parentURL\n    )\n  }\n}\n\n/**\n * @param {URL} url\n */\nfunction throwIfUnsupportedURLProtocol(url) {\n  // Avoid accessing the `protocol` property due to the lazy getters.\n  const protocol = url.protocol;\n\n  if (protocol !== 'file:' && protocol !== 'data:' && protocol !== 'node:') {\n    throw new ERR_UNSUPPORTED_ESM_URL_SCHEME(url)\n  }\n}\n\n/**\n * @param {URL | undefined} parsed\n * @param {boolean} experimentalNetworkImports\n */\nfunction throwIfUnsupportedURLScheme(parsed, experimentalNetworkImports) {\n  // Avoid accessing the `protocol` property due to the lazy getters.\n  const protocol = parsed?.protocol;\n\n  if (\n    protocol &&\n    protocol !== 'file:' &&\n    protocol !== 'data:' &&\n    (!experimentalNetworkImports ||\n      (protocol !== 'https:' && protocol !== 'http:'))\n  ) {\n    throw new ERR_UNSUPPORTED_ESM_URL_SCHEME(\n      parsed,\n      ['file', 'data'].concat(\n        experimentalNetworkImports ? ['https', 'http'] : []\n      )\n    )\n  }\n}\n\n/**\n * @param {string} specifier\n * @param {{parentURL?: string, conditions?: Array<string>}} context\n * @returns {{url: string, format?: string | null}}\n */\nfunction defaultResolve(specifier, context = {}) {\n  const {parentURL} = context;\n  assert(parentURL !== undefined, 'expected `parentURL` to be defined');\n  throwIfInvalidParentURL(parentURL);\n\n  /** @type {URL | undefined} */\n  let parsedParentURL;\n  if (parentURL) {\n    try {\n      parsedParentURL = new URL(parentURL);\n    } catch {\n      // Ignore exception\n    }\n  }\n\n  /** @type {URL | undefined} */\n  let parsed;\n  try {\n    parsed = shouldBeTreatedAsRelativeOrAbsolutePath(specifier)\n      ? new URL(specifier, parsedParentURL)\n      : new URL(specifier);\n\n    // Avoid accessing the `protocol` property due to the lazy getters.\n    const protocol = parsed.protocol;\n\n    if (\n      protocol === 'data:' ||\n      (experimentalNetworkImports &&\n        (protocol === 'https:' || protocol === 'http:'))\n    ) {\n      return {url: parsed.href, format: null}\n    }\n  } catch {\n    // Ignore exception\n  }\n\n  // There are multiple deep branches that can either throw or return; instead\n  // of duplicating that deeply nested logic for the possible returns, DRY and\n  // check for a return. This seems the least gnarly.\n  const maybeReturn = checkIfDisallowedImport(\n    specifier,\n    parsed,\n    parsedParentURL\n  );\n\n  if (maybeReturn) return maybeReturn\n\n  // This must come after checkIfDisallowedImport\n  if (parsed && parsed.protocol === 'node:') return {url: specifier}\n\n  throwIfUnsupportedURLScheme(parsed, experimentalNetworkImports);\n\n  const conditions = getConditionsSet(context.conditions);\n\n  const url = moduleResolve(specifier, new URL(parentURL), conditions, false);\n\n  throwIfUnsupportedURLProtocol(url);\n\n  return {\n    // Do NOT cast `url` to a string: that will work even when there are real\n    // problems, silencing them\n    url: url.href,\n    format: defaultGetFormatWithoutErrors(url, {parentURL})\n  }\n}\n\n/**\n * @typedef {import('./lib/errors.js').ErrnoException} ErrnoException\n */\n\n\n/**\n * Match `import.meta.resolve` except that `parent` is required (you can pass\n * `import.meta.url`).\n *\n * @param {string} specifier\n *   The module specifier to resolve relative to parent\n *   (`/example.js`, `./example.js`, `../example.js`, `some-package`, `fs`,\n *   etc).\n * @param {string} parent\n *   The absolute parent module URL to resolve from.\n *   You must pass `import.meta.url` or something else.\n * @returns {string}\n *   Returns a string to a full `file:`, `data:`, or `node:` URL\n *   to the found thing.\n */\nfunction resolve(specifier, parent) {\n  if (!parent) {\n    throw new Error(\n      'Please pass `parent`: `import-meta-resolve` cannot ponyfill that'\n    )\n  }\n\n  try {\n    return defaultResolve(specifier, {parentURL: parent}).url\n  } catch (error) {\n    const exception = /** @type {ErrnoException} */ (error);\n\n    if (\n      exception.code === 'ERR_UNSUPPORTED_DIR_IMPORT' &&\n      typeof exception.url === 'string'\n    ) {\n      return exception.url\n    }\n\n    throw error\n  }\n}\n\nexport { moduleResolve, resolve };\n"], "mappings": ";;;;;;;AAoFA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,IAAA;EAAA,MAAAF,IAAA,GAAAG,uBAAA,CAAAF,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,SAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,QAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,KAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,IAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,MAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,KAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,GAAA;EAAA,MAAAR,IAAA,GAAAC,OAAA;EAAAO,EAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,MAAA;EAAA,MAAAT,IAAA,GAAAC,OAAA;EAAAQ,KAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuC,SAAAU,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAR,wBAAAY,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAcvC,MAAMW,SAAS,GAAGC,SAAMA,CAAC,CAACC,QAAQ,KAAK,OAAO;AAE9C,MAAMC,KAAK,GAAG,CAAC,CAAC,CAACP,cAAc;AAE/B,MAAMQ,WAAW,GAAG,oBAAoB;AAExC,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAAC,CACrB,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,QAAQ,EAER,UAAU,EACV,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,CACT,CAAC;AAEF,MAAMC,KAAK,GAAG,CAAC,CAAC;AAahB,SAASC,UAAUA,CAACC,KAAK,EAAEC,IAAI,GAAG,KAAK,EAAE;EACvC,OAAOD,KAAK,CAACE,MAAM,GAAG,CAAC,GACnBF,KAAK,CAACG,IAAI,CAAE,IAAGF,IAAK,GAAE,CAAC,GACtB,GAAED,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACD,IAAI,CAAC,IAAI,CAAE,KAAIF,IAAK,IAAGD,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAE,EAAC;AAC5E;AAGA,MAAMG,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC1B,MAAMC,kBAAkB,GAAG,kBAAkB;AAE7C,IAAIC,mBAAmB;AAEvBV,KAAK,CAACW,oBAAoB,GAAGC,WAAW,CACtC,sBAAsB,EAMtB,CAACC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EAC1BC,QAAKA,CAAC,CAAC,OAAOH,IAAI,KAAK,QAAQ,EAAE,yBAAyB,CAAC;EAC3D,IAAI,CAACI,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,EAAE;IAC5BA,QAAQ,GAAG,CAACA,QAAQ,CAAC;EACvB;EAEA,IAAIK,OAAO,GAAG,MAAM;EACpB,IAAIN,IAAI,CAACO,QAAQ,CAAC,WAAW,CAAC,EAAE;IAE9BD,OAAO,IAAK,GAAEN,IAAK,GAAE;EACvB,CAAC,MAAM;IACL,MAAMV,IAAI,GAAGU,IAAI,CAACQ,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,UAAU;IACzDF,OAAO,IAAK,IAAGN,IAAK,KAAIV,IAAK,GAAE;EACjC;EAEAgB,OAAO,IAAI,UAAU;EAGrB,MAAMG,KAAK,GAAG,EAAE;EAEhB,MAAMC,SAAS,GAAG,EAAE;EAEpB,MAAMC,KAAK,GAAG,EAAE;EAEhB,KAAK,MAAMC,KAAK,IAAIX,QAAQ,EAAE;IAC5BE,QAAKA,CAAC,CACJ,OAAOS,KAAK,KAAK,QAAQ,EACzB,gDACF,CAAC;IAED,IAAI3B,MAAM,CAAClB,GAAG,CAAC6C,KAAK,CAAC,EAAE;MACrBH,KAAK,CAACI,IAAI,CAACD,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC;IACjC,CAAC,MAAM,IAAI9B,WAAW,CAAC+B,IAAI,CAACH,KAAK,CAAC,KAAK,IAAI,EAAE;MAC3CT,QAAKA,CAAC,CACJS,KAAK,KAAK,QAAQ,EAClB,kDACF,CAAC;MACDD,KAAK,CAACE,IAAI,CAACD,KAAK,CAAC;IACnB,CAAC,MAAM;MACLF,SAAS,CAACG,IAAI,CAACD,KAAK,CAAC;IACvB;EACF;EAIA,IAAIF,SAAS,CAACnB,MAAM,GAAG,CAAC,EAAE;IACxB,MAAMyB,GAAG,GAAGP,KAAK,CAACQ,OAAO,CAAC,QAAQ,CAAC;IACnC,IAAID,GAAG,KAAK,CAAC,CAAC,EAAE;MACdP,KAAK,CAAChB,KAAK,CAACuB,GAAG,EAAE,CAAC,CAAC;MACnBN,SAAS,CAACG,IAAI,CAAC,QAAQ,CAAC;IAC1B;EACF;EAEA,IAAIJ,KAAK,CAAClB,MAAM,GAAG,CAAC,EAAE;IACpBe,OAAO,IAAK,GAAEG,KAAK,CAAClB,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG,SAAU,IAAGH,UAAU,CACtEqB,KAAK,EACL,IACF,CAAE,EAAC;IACH,IAAIC,SAAS,CAACnB,MAAM,GAAG,CAAC,IAAIoB,KAAK,CAACpB,MAAM,GAAG,CAAC,EAAEe,OAAO,IAAI,MAAM;EACjE;EAEA,IAAII,SAAS,CAACnB,MAAM,GAAG,CAAC,EAAE;IACxBe,OAAO,IAAK,kBAAiBlB,UAAU,CAACsB,SAAS,EAAE,IAAI,CAAE,EAAC;IAC1D,IAAIC,KAAK,CAACpB,MAAM,GAAG,CAAC,EAAEe,OAAO,IAAI,MAAM;EACzC;EAEA,IAAIK,KAAK,CAACpB,MAAM,GAAG,CAAC,EAAE;IACpB,IAAIoB,KAAK,CAACpB,MAAM,GAAG,CAAC,EAAE;MACpBe,OAAO,IAAK,UAASlB,UAAU,CAACuB,KAAK,EAAE,IAAI,CAAE,EAAC;IAChD,CAAC,MAAM;MACL,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,KAAKH,KAAK,CAAC,CAAC,CAAC,EAAEL,OAAO,IAAI,KAAK;MACzDA,OAAO,IAAK,GAAEK,KAAK,CAAC,CAAC,CAAE,EAAC;IAC1B;EACF;EAEAL,OAAO,IAAK,cAAaY,qBAAqB,CAAChB,MAAM,CAAE,EAAC;EAExD,OAAOI,OAAO;AAChB,CAAC,EACDa,SACF,CAAC;AAEDhC,KAAK,CAACiC,4BAA4B,GAAGrB,WAAW,CAC9C,8BAA8B,EAM9B,CAACsB,OAAO,EAAEC,MAAM,EAAEC,IAAI,GAAGC,SAAS,KAAK;EACrC,OAAQ,mBAAkBH,OAAQ,KAAIC,MAAO,GAC3CC,IAAI,GAAI,kBAAiBA,IAAK,EAAC,GAAG,EACnC,EAAC;AACJ,CAAC,EACDJ,SACF,CAAC;AAEDhC,KAAK,CAACsC,0BAA0B,GAAG1B,WAAW,CAC5C,4BAA4B,EAM5B,CAAC2B,IAAI,EAAEH,IAAI,EAAEjB,OAAO,KAAK;EACvB,OAAQ,0BAAyBoB,IAAK,GACpCH,IAAI,GAAI,oBAAmBA,IAAK,EAAC,GAAG,EACrC,GAAEjB,OAAO,GAAI,KAAIA,OAAQ,EAAC,GAAG,EAAG,EAAC;AACpC,CAAC,EACDqB,KACF,CAAC;AAEDxC,KAAK,CAACyC,0BAA0B,GAAG7B,WAAW,CAC5C,4BAA4B,EAQ5B,CAAC8B,OAAO,EAAEvD,GAAG,EAAEwD,MAAM,EAAEC,QAAQ,GAAG,KAAK,EAAER,IAAI,GAAGC,SAAS,KAAK;EAC5D,MAAMQ,QAAQ,GACZ,OAAOF,MAAM,KAAK,QAAQ,IAC1B,CAACC,QAAQ,IACTD,MAAM,CAACvC,MAAM,GAAG,CAAC,IACjB,CAACuC,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;EAC1B,IAAI3D,GAAG,KAAK,GAAG,EAAE;IACf6B,QAAKA,CAAC,CAAC4B,QAAQ,KAAK,KAAK,CAAC;IAC1B,OACG,iCAAgCG,IAAI,CAACC,SAAS,CAACL,MAAM,CAAE,WAAU,GACjE,yBAAwBD,OAAQ,eAC/BN,IAAI,GAAI,kBAAiBA,IAAK,EAAC,GAAG,EACnC,GAAES,QAAQ,GAAG,gCAAgC,GAAG,EAAG,EAAC;EAEzD;EAEA,OAAQ,YACND,QAAQ,GAAG,SAAS,GAAG,SACxB,YAAWG,IAAI,CAACC,SAAS,CACxBL,MACF,CAAE,iBAAgBxD,GAAI,2BAA0BuD,OAAQ,eACtDN,IAAI,GAAI,kBAAiBA,IAAK,EAAC,GAAG,EACnC,GAAES,QAAQ,GAAG,gCAAgC,GAAG,EAAG,EAAC;AACvD,CAAC,EACDL,KACF,CAAC;AAEDxC,KAAK,CAACiD,oBAAoB,GAAGrC,WAAW,CACtC,sBAAsB,EAMtB,CAAC2B,IAAI,EAAEH,IAAI,EAAEjC,IAAI,GAAG,SAAS,KAAK;EAChC,OAAQ,eAAcA,IAAK,KAAIoC,IAAK,mBAAkBH,IAAK,EAAC;AAC9D,CAAC,EACDI,KACF,CAAC;AAEDxC,KAAK,CAACkD,6BAA6B,GAAGtC,WAAW,CAC/C,+BAA+B,EAC/B,2CAA2C,EAC3C4B,KACF,CAAC;AAEDxC,KAAK,CAACmD,8BAA8B,GAAGvC,WAAW,CAChD,gCAAgC,EAMhC,CAACwC,SAAS,EAAEC,WAAW,EAAEjB,IAAI,KAAK;EAChC,OAAQ,6BAA4BgB,SAAU,mBAC5CC,WAAW,GAAI,eAAcA,WAAY,cAAa,GAAG,EAC1D,kBAAiBjB,IAAK,EAAC;AAC1B,CAAC,EACDJ,SACF,CAAC;AAEDhC,KAAK,CAACsD,6BAA6B,GAAG1C,WAAW,CAC/C,+BAA+B,EAM/B,CAAC8B,OAAO,EAAEa,OAAO,EAAEnB,IAAI,GAAGC,SAAS,KAAK;EACtC,IAAIkB,OAAO,KAAK,GAAG,EACjB,OAAQ,gCAA+Bb,OAAQ,eAC7CN,IAAI,GAAI,kBAAiBA,IAAK,EAAC,GAAG,EACnC,EAAC;EACJ,OAAQ,oBAAmBmB,OAAQ,oCAAmCb,OAAQ,eAC5EN,IAAI,GAAI,kBAAiBA,IAAK,EAAC,GAAG,EACnC,EAAC;AACJ,CAAC,EACDI,KACF,CAAC;AAEDxC,KAAK,CAACwD,0BAA0B,GAAG5C,WAAW,CAC5C,4BAA4B,EAC5B,yCAAyC,GACvC,uCAAuC,EACzC4B,KACF,CAAC;AAEDxC,KAAK,CAACyD,0BAA0B,GAAG7C,WAAW,CAC5C,4BAA4B,EAK5B,CAAC8C,GAAG,EAAEnB,IAAI,KAAK;EACb,OAAQ,2BAA0BmB,GAAI,SAAQnB,IAAK,EAAC;AACtD,CAAC,EACDP,SACF,CAAC;AAEDhC,KAAK,CAAC2D,qBAAqB,GAAG/C,WAAW,CACvC,uBAAuB,EAMvB,CAACC,IAAI,EAAEY,KAAK,EAAEU,MAAM,GAAG,YAAY,KAAK;EACtC,IAAIyB,SAAS,GAAG,IAAAC,eAAO,EAACpC,KAAK,CAAC;EAE9B,IAAImC,SAAS,CAACxD,MAAM,GAAG,GAAG,EAAE;IAC1BwD,SAAS,GAAI,GAAEA,SAAS,CAACtD,KAAK,CAAC,CAAC,EAAE,GAAG,CAAE,KAAI;EAC7C;EAEA,MAAMH,IAAI,GAAGU,IAAI,CAACQ,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,UAAU;EAEzD,OAAQ,OAAMlB,IAAK,KAAIU,IAAK,KAAIsB,MAAO,cAAayB,SAAU,EAAC;AACjE,CAAC,EACD5B,SAGF,CAAC;AAEDhC,KAAK,CAAC8D,8BAA8B,GAAGlD,WAAW,CAChD,gCAAgC,EAKhC,CAACmD,GAAG,EAAEC,SAAS,KAAK;EAClB,IAAI7C,OAAO,GAAI,+BAA8BlB,UAAU,CACrD+D,SACF,CAAE,0CAAyC;EAE3C,IAAIvE,SAAS,IAAIsE,GAAG,CAACE,QAAQ,CAAC7D,MAAM,KAAK,CAAC,EAAE;IAC1Ce,OAAO,IAAI,yDAAyD;EACtE;EAEAA,OAAO,IAAK,wBAAuB4C,GAAG,CAACE,QAAS,GAAE;EAClD,OAAO9C,OAAO;AAChB,CAAC,EACDqB,KACF,CAAC;AAUD,SAAS5B,WAAWA,CAACsD,GAAG,EAAEzC,KAAK,EAAE0C,GAAG,EAAE;EAGpC5D,QAAQ,CAACf,GAAG,CAAC0E,GAAG,EAAEzC,KAAK,CAAC;EAExB,OAAO2C,qBAAqB,CAACD,GAAG,EAAED,GAAG,CAAC;AACxC;AAOA,SAASE,qBAAqBA,CAACC,IAAI,EAAElF,GAAG,EAAE;EAExC,OAAOmF,SAAS;EAIhB,SAASA,SAASA,CAAC,GAAGC,IAAI,EAAE;IAC1B,MAAMC,KAAK,GAAGhC,KAAK,CAACiC,eAAe;IACnC,IAAIC,8BAA8B,CAAC,CAAC,EAAElC,KAAK,CAACiC,eAAe,GAAG,CAAC;IAC/D,MAAME,KAAK,GAAG,IAAIN,IAAI,CAAC,CAAC;IAExB,IAAIK,8BAA8B,CAAC,CAAC,EAAElC,KAAK,CAACiC,eAAe,GAAGD,KAAK;IACnE,MAAMrD,OAAO,GAAGyD,UAAU,CAACzF,GAAG,EAAEoF,IAAI,EAAEI,KAAK,CAAC;IAC5C3F,MAAM,CAAC6F,gBAAgB,CAACF,KAAK,EAAE;MAG7BxD,OAAO,EAAE;QACPM,KAAK,EAAEN,OAAO;QACd2D,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB,CAAC;MACDC,QAAQ,EAAE;QAERxD,KAAKA,CAAA,EAAG;UACN,OAAQ,GAAE,IAAI,CAACZ,IAAK,KAAI1B,GAAI,MAAK,IAAI,CAACgC,OAAQ,EAAC;QACjD,CAAC;QACD2D,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;IAEFE,uBAAuB,CAACP,KAAK,CAAC;IAE9BA,KAAK,CAACQ,IAAI,GAAGhG,GAAG;IAChB,OAAOwF,KAAK;EACd;AACF;AAKA,SAASD,8BAA8BA,CAAA,EAAG;EAGxC,IAAI;IAEF,IAAIU,GAACA,CAAC,CAACC,eAAe,CAACC,kBAAkB,CAAC,CAAC,EAAE;MAC3C,OAAO,KAAK;IACd;EACF,CAAC,CAAC,OAAAC,OAAA,EAAM,CAAC;EAET,MAAMhG,IAAI,GAAGP,MAAM,CAACE,wBAAwB,CAACsD,KAAK,EAAE,iBAAiB,CAAC;EACtE,IAAIjD,IAAI,KAAK8C,SAAS,EAAE;IACtB,OAAOrD,MAAM,CAACwG,YAAY,CAAChD,KAAK,CAAC;EACnC;EAEA,OAAO5C,KAAK,CAACN,IAAI,CAACC,IAAI,EAAE,UAAU,CAAC,IAAIA,IAAI,CAACwF,QAAQ,KAAK1C,SAAS,GAC9D9C,IAAI,CAACwF,QAAQ,GACbxF,IAAI,CAACC,GAAG,KAAK6C,SAAS;AAC5B;AAQA,SAASoD,eAAeA,CAACC,EAAE,EAAE;EAG3B,MAAMC,MAAM,GAAGlF,kBAAkB,GAAGiF,EAAE,CAAC7E,IAAI;EAC3C7B,MAAM,CAACC,cAAc,CAACyG,EAAE,EAAE,MAAM,EAAE;IAACjE,KAAK,EAAEkE;EAAM,CAAC,CAAC;EAClD,OAAOD,EAAE;AACX;AAEA,MAAMR,uBAAuB,GAAGO,eAAe,CAM7C,UAAUd,KAAK,EAAE;EACf,MAAMiB,yBAAyB,GAAGlB,8BAA8B,CAAC,CAAC;EAClE,IAAIkB,yBAAyB,EAAE;IAC7BlF,mBAAmB,GAAG8B,KAAK,CAACiC,eAAe;IAC3CjC,KAAK,CAACiC,eAAe,GAAGoB,MAAM,CAACC,iBAAiB;EAClD;EAEAtD,KAAK,CAACuD,iBAAiB,CAACpB,KAAK,CAAC;EAG9B,IAAIiB,yBAAyB,EAAEpD,KAAK,CAACiC,eAAe,GAAG/D,mBAAmB;EAE1E,OAAOiE,KAAK;AACd,CACF,CAAC;AAQD,SAASC,UAAUA,CAACzF,GAAG,EAAEoF,IAAI,EAAEyB,IAAI,EAAE;EACnC,MAAM7E,OAAO,GAAGZ,QAAQ,CAAC1B,GAAG,CAACM,GAAG,CAAC;EACjC6B,QAAKA,CAAC,CAACG,OAAO,KAAKkB,SAAS,EAAE,gCAAgC,CAAC;EAE/D,IAAI,OAAOlB,OAAO,KAAK,UAAU,EAAE;IACjCH,QAAKA,CAAC,CACJG,OAAO,CAACf,MAAM,IAAImE,IAAI,CAACnE,MAAM,EAC5B,SAAQjB,GAAI,oCAAmCoF,IAAI,CAACnE,MAAO,aAAY,GACrE,4BAA2Be,OAAO,CAACf,MAAO,IAC/C,CAAC;IACD,OAAO6F,OAAO,CAACC,KAAK,CAAC/E,OAAO,EAAE6E,IAAI,EAAEzB,IAAI,CAAC;EAC3C;EAEA,MAAM4B,KAAK,GAAG,aAAa;EAC3B,IAAIC,cAAc,GAAG,CAAC;EACtB,OAAOD,KAAK,CAACvE,IAAI,CAACT,OAAO,CAAC,KAAK,IAAI,EAAEiF,cAAc,EAAE;EACrDpF,QAAKA,CAAC,CACJoF,cAAc,KAAK7B,IAAI,CAACnE,MAAM,EAC7B,SAAQjB,GAAI,oCAAmCoF,IAAI,CAACnE,MAAO,aAAY,GACrE,4BAA2BgG,cAAe,IAC/C,CAAC;EACD,IAAI7B,IAAI,CAACnE,MAAM,KAAK,CAAC,EAAE,OAAOe,OAAO;EAErCoD,IAAI,CAAC8B,OAAO,CAAClF,OAAO,CAAC;EACrB,OAAO8E,OAAO,CAACC,KAAK,CAACI,cAAM,EAAE,IAAI,EAAE/B,IAAI,CAAC;AAC1C;AAOA,SAASxC,qBAAqBA,CAACN,KAAK,EAAE;EACpC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKY,SAAS,EAAE;IACzC,OAAOkE,MAAM,CAAC9E,KAAK,CAAC;EACtB;EAEA,IAAI,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,CAACZ,IAAI,EAAE;IAC7C,OAAQ,YAAWY,KAAK,CAACZ,IAAK,EAAC;EACjC;EAEA,IAAI,OAAOY,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAIA,KAAK,CAAC+E,WAAW,IAAI/E,KAAK,CAAC+E,WAAW,CAAC3F,IAAI,EAAE;MAC/C,OAAQ,kBAAiBY,KAAK,CAAC+E,WAAW,CAAC3F,IAAK,EAAC;IACnD;IAEA,OAAQ,GAAE,IAAAgD,eAAO,EAACpC,KAAK,EAAE;MAACgF,KAAK,EAAE,CAAC;IAAC,CAAC,CAAE,EAAC;EACzC;EAEA,IAAI7C,SAAS,GAAG,IAAAC,eAAO,EAACpC,KAAK,EAAE;IAACiF,MAAM,EAAE;EAAK,CAAC,CAAC;EAE/C,IAAI9C,SAAS,CAACxD,MAAM,GAAG,EAAE,EAAE;IACzBwD,SAAS,GAAI,GAAEA,SAAS,CAACtD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAE,KAAI;EAC5C;EAEA,OAAQ,QAAO,OAAOmB,KAAM,KAAImC,SAAU,GAAE;AAC9C;AASA,MAAM+C,MAAM,GAAG;EAACC;AAAI,CAAC;AACrB,IAAIC,iBAAiB,GAAGF,MAAM;AAM9B,SAASC,IAAIA,CAACE,QAAQ,EAAE;EACtB,IAAI;IACF,MAAMC,MAAM,GAAGC,aAAE,CAACC,YAAY,CAC5B1E,MAAGA,CAAC,CAAC2E,gBAAgB,CAAC3E,MAAGA,CAAC,CAAClC,IAAI,CAACkC,MAAGA,CAAC,CAAC4E,OAAO,CAACL,QAAQ,CAAC,EAAE,cAAc,CAAC,CAAC,EACxE,MACF,CAAC;IACD,OAAO;MAACC;IAAM,CAAC;EACjB,CAAC,CAAC,OAAOpC,KAAK,EAAE;IACd,MAAMyC,SAAS,GAAkCzC,KAAM;IAEvD,IAAIyC,SAAS,CAACjC,IAAI,KAAK,QAAQ,EAAE;MAC/B,OAAO;QAAC4B,MAAM,EAAE1E;MAAS,CAAC;IAG5B;IAEA,MAAM+E,SAAS;EACjB;AACF;AAOA,MAAM;EAAC9E,0BAA0B,EAAE+E;AAA4B,CAAC,GAAGrH,KAAK;AAGxE,MAAMsH,gBAAgB,GAAG,IAAI9G,GAAG,CAAC,CAAC;AAQlC,SAAS+G,gBAAgBA,CAAChF,IAAI,EAAEa,SAAS,EAAEhB,IAAI,EAAE;EAC/C,MAAMoF,QAAQ,GAAGF,gBAAgB,CAACzI,GAAG,CAAC0D,IAAI,CAAC;EAC3C,IAAIiF,QAAQ,KAAKnF,SAAS,EAAE;IAC1B,OAAOmF,QAAQ;EACjB;EAEA,MAAMC,MAAM,GAAGZ,iBAAiB,CAACD,IAAI,CAACrE,IAAI,CAAC,CAACwE,MAAM;EAElD,IAAIU,MAAM,KAAKpF,SAAS,EAAE;IAExB,MAAMqF,aAAa,GAAG;MACpBC,SAAS,EAAEpF,IAAI;MACfqF,MAAM,EAAE,KAAK;MACbC,IAAI,EAAExF,SAAS;MACfxB,IAAI,EAAEwB,SAAS;MACflC,IAAI,EAAE,MAAM;MACZ2H,OAAO,EAAEzF,SAAS;MAClB0F,OAAO,EAAE1F;IACX,CAAC;IACDiF,gBAAgB,CAAC9H,GAAG,CAAC+C,IAAI,EAAEmF,aAAa,CAAC;IACzC,OAAOA,aAAa;EACtB;EAGA,IAAIM,WAAW;EACf,IAAI;IACFA,WAAW,GAAGjF,IAAI,CAACkF,KAAK,CAACR,MAAM,CAAC;EAClC,CAAC,CAAC,OAAO9C,KAAK,EAAE;IACd,MAAMyC,SAAS,GAAkCzC,KAAM;IAEvD,MAAM,IAAI0C,4BAA4B,CACpC9E,IAAI,EACJ,CAACH,IAAI,GAAI,IAAGgB,SAAU,SAAQ,GAAG,EAAE,IAAI,IAAA8E,oBAAa,EAAC9F,IAAI,IAAIgB,SAAS,CAAC,EACvEgE,SAAS,CAACjG,OACZ,CAAC;EACH;EAEA,MAAM;IAAC2G,OAAO;IAAEC,OAAO;IAAEF,IAAI;IAAEhH,IAAI;IAAEV;EAAI,CAAC,GAAG6H,WAAW;EAGxD,MAAMN,aAAa,GAAG;IACpBC,SAAS,EAAEpF,IAAI;IACfqF,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,OAAOA,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGxF,SAAS;IACjDxB,IAAI,EAAE,OAAOA,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGwB,SAAS;IACjDlC,IAAI,EAAEA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAG,MAAM;IAE9D2H,OAAO;IAEPC,OAAO,EAAEA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAG1F;EAC9D,CAAC;EACDiF,gBAAgB,CAAC9H,GAAG,CAAC+C,IAAI,EAAEmF,aAAa,CAAC;EACzC,OAAOA,aAAa;AACtB;AAMA,SAASS,qBAAqBA,CAACC,QAAQ,EAAE;EACvC,IAAIC,cAAc,GAAG,KAAIC,UAAG,EAAC,cAAc,EAAEF,QAAQ,CAAC;EAEtD,OAAO,IAAI,EAAE;IACX,MAAMG,eAAe,GAAGF,cAAc,CAACG,QAAQ;IAE/C,IAAID,eAAe,CAACnH,QAAQ,CAAC,2BAA2B,CAAC,EAAE;IAE3D,MAAMsG,aAAa,GAAGH,gBAAgB,CACpC,IAAAW,oBAAa,EAACG,cAAc,CAAC,EAC7BD,QACF,CAAC;IACD,IAAIV,aAAa,CAACE,MAAM,EAAE,OAAOF,aAAa;IAE9C,MAAMe,kBAAkB,GAAGJ,cAAc;IACzCA,cAAc,GAAG,KAAIC,UAAG,EAAC,iBAAiB,EAAED,cAAc,CAAC;IAI3D,IAAIA,cAAc,CAACG,QAAQ,KAAKC,kBAAkB,CAACD,QAAQ,EAAE;EAC/D;EAEA,MAAMD,eAAe,GAAG,IAAAL,oBAAa,EAACG,cAAc,CAAC;EAErD,MAAMX,aAAa,GAAG;IACpBC,SAAS,EAAEY,eAAe;IAC1BX,MAAM,EAAE,KAAK;IACbC,IAAI,EAAExF,SAAS;IACfxB,IAAI,EAAEwB,SAAS;IACflC,IAAI,EAAE,MAAM;IACZ2H,OAAO,EAAEzF,SAAS;IAClB0F,OAAO,EAAE1F;EACX,CAAC;EACDiF,gBAAgB,CAAC9H,GAAG,CAAC+I,eAAe,EAAEb,aAAa,CAAC;EACpD,OAAOA,aAAa;AACtB;AAgBA,SAASgB,cAAcA,CAAC3E,GAAG,EAAE;EAC3B,MAAM2D,aAAa,GAAGS,qBAAqB,CAACpE,GAAG,CAAC;EAChD,OAAO2D,aAAa,CAACvH,IAAI;AAC3B;AAOA,MAAM;EAACsD;AAA0B,CAAC,GAAGzD,KAAK;AAE1C,MAAMX,cAAc,GAAG,CAAC,CAAC,CAACA,cAAc;AAGxC,MAAMsJ,kBAAkB,GAAG;EAEzBC,SAAS,EAAE,IAAI;EACf,MAAM,EAAE,UAAU;EAClB,KAAK,EAAE,QAAQ;EACf,OAAO,EAAE,MAAM;EACf,MAAM,EAAE;AACV,CAAC;AAMD,SAASC,YAAYA,CAACC,IAAI,EAAE;EAC1B,IACEA,IAAI,IACJ,+DAA+D,CAACC,IAAI,CAACD,IAAI,CAAC,EAE1E,OAAO,QAAQ;EACjB,IAAIA,IAAI,KAAK,kBAAkB,EAAE,OAAO,MAAM;EAC9C,OAAO,IAAI;AACb;AAaA,MAAME,gBAAgB,GAAG;EAEvBJ,SAAS,EAAE,IAAI;EACf,OAAO,EAAEK,2BAA2B;EACpC,OAAO,EAAEC,2BAA2B;EACpC,OAAO,EAAEC,2BAA2B;EACpC,QAAQ,EAAEA,2BAA2B;EACrC,OAAOC,CAAA,EAAG;IACR,OAAO,SAAS;EAClB;AACF,CAAC;AAKD,SAASH,2BAA2BA,CAACI,MAAM,EAAE;EAC3C,MAAM;IAAC,CAAC,EAAEP;EAAI,CAAC,GAAG,mCAAmC,CAAClH,IAAI,CACxDyH,MAAM,CAACb,QACT,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACvB,OAAOK,YAAY,CAACC,IAAI,CAAC;AAC3B;AAYA,SAASQ,OAAOA,CAACvF,GAAG,EAAE;EACpB,MAAMyE,QAAQ,GAAGzE,GAAG,CAACyE,QAAQ;EAC7B,IAAIe,KAAK,GAAGf,QAAQ,CAACpI,MAAM;EAE3B,OAAOmJ,KAAK,EAAE,EAAE;IACd,MAAMpE,IAAI,GAAGqD,QAAQ,CAACgB,WAAW,CAACD,KAAK,CAAC;IAExC,IAAIpE,IAAI,KAAK,EAAE,EAAY;MACzB,OAAO,EAAE;IACX;IAEA,IAAIA,IAAI,KAAK,EAAE,EAAY;MACzB,OAAOqD,QAAQ,CAACgB,WAAW,CAACD,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,GACzC,EAAE,GACFf,QAAQ,CAAClI,KAAK,CAACiJ,KAAK,CAAC;IAC3B;EACF;EAEA,OAAO,EAAE;AACX;AAKA,SAASL,2BAA2BA,CAACnF,GAAG,EAAE0F,QAAQ,EAAEC,YAAY,EAAE;EAChE,MAAMhG,GAAG,GAAG4F,OAAO,CAACvF,GAAG,CAAC;EAExB,IAAIL,GAAG,KAAK,KAAK,EAAE;IACjB,OAAOgF,cAAc,CAAC3E,GAAG,CAAC,KAAK,QAAQ,GAAG,QAAQ,GAAG,UAAU;EACjE;EAEA,MAAMuC,MAAM,GAAGqC,kBAAkB,CAACjF,GAAG,CAAC;EACtC,IAAI4C,MAAM,EAAE,OAAOA,MAAM;EAGzB,IAAIoD,YAAY,EAAE;IAChB,OAAOrH,SAAS;EAClB;EAEA,MAAMsH,QAAQ,GAAG,IAAAzB,oBAAa,EAACnE,GAAG,CAAC;EACnC,MAAM,IAAIN,0BAA0B,CAACC,GAAG,EAAEiG,QAAQ,CAAC;AACrD;AAEA,SAASR,2BAA2BA,CAAA,EAAG,CAEvC;AAOA,SAASS,6BAA6BA,CAAC7F,GAAG,EAAE8F,OAAO,EAAE;EACnD,IAAI,CAACxK,cAAc,CAACC,IAAI,CAAC0J,gBAAgB,EAAEjF,GAAG,CAACE,QAAQ,CAAC,EAAE;IACxD,OAAO,IAAI;EACb;EAEA,OAAO+E,gBAAgB,CAACjF,GAAG,CAACE,QAAQ,CAAC,CAACF,GAAG,EAAE8F,OAAO,EAAE,IAAI,CAAC,IAAI,IAAI;AACnE;AAOA,MAAM;EAAClG;AAAqB,CAAC,GAAG3D,KAAK;AAKrC,MAAM8J,kBAAkB,GAAG9K,MAAM,CAAC+K,MAAM,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC5D,MAAMC,sBAAsB,GAAG,IAAIjK,GAAG,CAAC+J,kBAAkB,CAAC;AAE1D,SAASG,oBAAoBA,CAAA,EAAG;EAC9B,OAAOH,kBAAkB;AAC3B;AAEA,SAASI,uBAAuBA,CAAA,EAAG;EACjC,OAAOF,sBAAsB;AAC/B;AAMA,SAASG,gBAAgBA,CAACC,UAAU,EAAE;EACpC,IAAIA,UAAU,KAAK/H,SAAS,IAAI+H,UAAU,KAAKH,oBAAoB,CAAC,CAAC,EAAE;IACrE,IAAI,CAAChJ,KAAK,CAACC,OAAO,CAACkJ,UAAU,CAAC,EAAE;MAC9B,MAAM,IAAIzG,qBAAqB,CAC7B,YAAY,EACZyG,UAAU,EACV,mBACF,CAAC;IACH;IAEA,OAAO,IAAIrK,GAAG,CAACqK,UAAU,CAAC;EAC5B;EAEA,OAAOF,uBAAuB,CAAC,CAAC;AAClC;AAOA,MAAMG,4BAA4B,GAAGC,MAAM,CAAClL,SAAS,CAACmL,MAAM,CAACC,OAAO,CAAC;AAGrE,MAAMC,0BAA0B,GAAG,KAAK;AAExC,MAAM;EACJvH,6BAA6B;EAC7BjB,4BAA4B;EAC5BK,0BAA0B;EAC1BG,0BAA0B;EAC1BQ,oBAAoB;EACpBE,8BAA8B;EAC9BG,6BAA6B;EAC7BE,0BAA0B;EAC1BM;AACF,CAAC,GAAG9D,KAAK;AAET,MAAM0K,GAAG,GAAG,CAAC,CAAC,CAACrL,cAAc;AAE7B,MAAMsL,mBAAmB,GACvB,0KAA0K;AAC5K,MAAMC,6BAA6B,GACjC,yKAAyK;AAC3K,MAAMC,uBAAuB,GAAG,UAAU;AAC1C,MAAMC,YAAY,GAAG,KAAK;AAC1B,MAAMC,eAAe,GAAG,UAAU;AAElC,MAAMC,sBAAsB,GAAG,IAAIjL,GAAG,CAAC,CAAC;AAExC,MAAMkL,gBAAgB,GAAG,UAAU;AAYnC,SAASC,6BAA6BA,CACpCvI,MAAM,EACNT,OAAO,EACPiJ,KAAK,EACL9C,cAAc,EACd+C,QAAQ,EACRhJ,IAAI,EACJiJ,QAAQ,EACR;EACA,MAAM1D,SAAS,GAAG,IAAAO,oBAAa,EAACG,cAAc,CAAC;EAC/C,MAAMiD,MAAM,GAAGL,gBAAgB,CAACrJ,IAAI,CAACyJ,QAAQ,GAAG1I,MAAM,GAAGT,OAAO,CAAC,KAAK,IAAI;EAC1ExC,SAAMA,CAAC,CAAC6L,WAAW,CAChB,qBACCD,MAAM,GAAG,cAAc,GAAG,oCAC3B,eAAc3I,MAAO,eAAc,GACjC,YAAWT,OAAQ,KAClBA,OAAO,KAAKiJ,KAAK,GAAG,EAAE,GAAI,eAAcA,KAAM,IAC/C,WACCC,QAAQ,GAAG,SAAS,GAAG,SACxB,+CAA8CzD,SAAU,GACvDvF,IAAI,GAAI,kBAAiB,IAAA8F,oBAAa,EAAC9F,IAAI,CAAE,EAAC,GAAG,EAClD,GAAE,EACL,oBAAoB,EACpB,SACF,CAAC;AACH;AASA,SAASoJ,0BAA0BA,CAACzH,GAAG,EAAEsE,cAAc,EAAEjG,IAAI,EAAEyF,IAAI,EAAE;EACnE,MAAMvB,MAAM,GAAGsD,6BAA6B,CAAC7F,GAAG,EAAE;IAAC0H,SAAS,EAAErJ,IAAI,CAACsJ;EAAI,CAAC,CAAC;EACzE,IAAIpF,MAAM,KAAK,QAAQ,EAAE;EACzB,MAAM/D,IAAI,GAAG,IAAA2F,oBAAa,EAACnE,GAAG,CAAC2H,IAAI,CAAC;EACpC,MAAMhJ,OAAO,GAAG,IAAAwF,oBAAa,EAAC,KAAII,UAAG,EAAC,GAAG,EAAED,cAAc,CAAC,CAAC;EAC3D,MAAMsD,QAAQ,GAAG,IAAAzD,oBAAa,EAAC9F,IAAI,CAAC;EACpC,IAAIyF,IAAI,EACNnI,SAAMA,CAAC,CAAC6L,WAAW,CAChB,WAAU7I,OAAQ,8BAA6BK,IAAI,CAACC,SAAS,CAAC6E,IAAI,CAAE,IAAG,GACrE,sEAAqEtF,IAAI,CAACjC,KAAK,CAC9EoC,OAAO,CAACtC,MACV,CAAE,oBAAmBuL,QAAS,2DAA0D,GACxF,4BAA4B,EAC9B,oBAAoB,EACpB,SACF,CAAC,CAAC,KAEFjM,SAAMA,CAAC,CAAC6L,WAAW,CAChB,gEAA+D7I,OAAQ,oCAAmCH,IAAI,CAACjC,KAAK,CACnHoC,OAAO,CAACtC,MACV,CAAE,oBAAmBuL,QAAS,wEAAuE,EACrG,oBAAoB,EACpB,SACF,CAAC;AACL;AAMA,SAASC,WAAWA,CAACrJ,IAAI,EAAE;EAEzB,IAAI;IACF,OAAO,IAAAsJ,cAAQ,EAACtJ,IAAI,CAAC;EACvB,CAAC,CAAC,OAAAuJ,QAAA,EAAM;IACN,OAAO,KAAIC,WAAK,EAAC,CAAC;EACpB;AACF;AAaA,SAASC,UAAUA,CAACjI,GAAG,EAAE;EACvB,MAAMkI,KAAK,GAAG,IAAAJ,cAAQ,EAAC9H,GAAG,EAAE;IAACmI,cAAc,EAAE;EAAK,CAAC,CAAC;EACpD,MAAMC,MAAM,GAAGF,KAAK,GAAGA,KAAK,CAACE,MAAM,CAAC,CAAC,GAAG9J,SAAS;EACjD,OAAO8J,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK9J,SAAS,GAAG,KAAK,GAAG8J,MAAM;AACjE;AAQA,SAASC,iBAAiBA,CAAC/D,cAAc,EAAEX,aAAa,EAAEtF,IAAI,EAAE;EAE9D,IAAIiK,KAAK;EACT,IAAI3E,aAAa,CAACG,IAAI,KAAKxF,SAAS,EAAE;IACpCgK,KAAK,GAAG,KAAI/D,UAAG,EAACZ,aAAa,CAACG,IAAI,EAAEQ,cAAc,CAAC;IAEnD,IAAI2D,UAAU,CAACK,KAAK,CAAC,EAAE,OAAOA,KAAK;IAEnC,MAAMC,KAAK,GAAG,CACX,KAAI5E,aAAa,CAACG,IAAK,KAAI,EAC3B,KAAIH,aAAa,CAACG,IAAK,OAAM,EAC7B,KAAIH,aAAa,CAACG,IAAK,OAAM,EAC7B,KAAIH,aAAa,CAACG,IAAK,WAAU,EACjC,KAAIH,aAAa,CAACG,IAAK,aAAY,EACnC,KAAIH,aAAa,CAACG,IAAK,aAAY,CACrC;IACD,IAAI0E,CAAC,GAAG,CAAC,CAAC;IAEV,OAAO,EAAEA,CAAC,GAAGD,KAAK,CAAClM,MAAM,EAAE;MACzBiM,KAAK,GAAG,KAAI/D,UAAG,EAACgE,KAAK,CAACC,CAAC,CAAC,EAAElE,cAAc,CAAC;MACzC,IAAI2D,UAAU,CAACK,KAAK,CAAC,EAAE;MACvBA,KAAK,GAAGhK,SAAS;IACnB;IAEA,IAAIgK,KAAK,EAAE;MACTb,0BAA0B,CACxBa,KAAK,EACLhE,cAAc,EACdjG,IAAI,EACJsF,aAAa,CAACG,IAChB,CAAC;MACD,OAAOwE,KAAK;IACd;EAEF;EAEA,MAAMC,KAAK,GAAG,CAAC,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC;EAC5D,IAAIC,CAAC,GAAG,CAAC,CAAC;EAEV,OAAO,EAAEA,CAAC,GAAGD,KAAK,CAAClM,MAAM,EAAE;IACzBiM,KAAK,GAAG,KAAI/D,UAAG,EAACgE,KAAK,CAACC,CAAC,CAAC,EAAElE,cAAc,CAAC;IACzC,IAAI2D,UAAU,CAACK,KAAK,CAAC,EAAE;IACvBA,KAAK,GAAGhK,SAAS;EACnB;EAEA,IAAIgK,KAAK,EAAE;IACTb,0BAA0B,CAACa,KAAK,EAAEhE,cAAc,EAAEjG,IAAI,EAAEsF,aAAa,CAACG,IAAI,CAAC;IAC3E,OAAOwE,KAAK;EACd;EAGA,MAAM,IAAIpJ,oBAAoB,CAC5B,IAAAiF,oBAAa,EAAC,KAAII,UAAG,EAAC,GAAG,EAAED,cAAc,CAAC,CAAC,EAC3C,IAAAH,oBAAa,EAAC9F,IAAI,CACpB,CAAC;AACH;AAQA,SAASoK,kBAAkBA,CAACpE,QAAQ,EAAEhG,IAAI,EAAEqK,gBAAgB,EAAE;EAC5D,IAAI1B,eAAe,CAACnJ,IAAI,CAACwG,QAAQ,CAACI,QAAQ,CAAC,KAAK,IAAI,EAClD,MAAM,IAAIvG,4BAA4B,CACpCmG,QAAQ,CAACI,QAAQ,EACjB,iDAAiD,EACjD,IAAAN,oBAAa,EAAC9F,IAAI,CACpB,CAAC;EAEH,MAAMsK,QAAQ,GAAG,IAAAxE,oBAAa,EAACE,QAAQ,CAAC;EAExC,MAAM6D,KAAK,GAAGL,WAAW,CACvBc,QAAQ,CAACtL,QAAQ,CAAC,GAAG,CAAC,GAAGsL,QAAQ,CAACpM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGoM,QAChD,CAAC;EAED,IAAIT,KAAK,CAACU,WAAW,CAAC,CAAC,EAAE;IACvB,MAAMhI,KAAK,GAAG,IAAInB,0BAA0B,CAACkJ,QAAQ,EAAE,IAAAxE,oBAAa,EAAC9F,IAAI,CAAC,CAAC;IAE3EuC,KAAK,CAACZ,GAAG,GAAGwC,MAAM,CAAC6B,QAAQ,CAAC;IAC5B,MAAMzD,KAAK;EACb;EAEA,IAAI,CAACsH,KAAK,CAACE,MAAM,CAAC,CAAC,EAAE;IACnB,MAAM,IAAIlJ,oBAAoB,CAC5ByJ,QAAQ,IAAItE,QAAQ,CAACI,QAAQ,EAC7BpG,IAAI,IAAI,IAAA8F,oBAAa,EAAC9F,IAAI,CAAC,EAC3B,QACF,CAAC;EACH;EAEA,IAAI,CAACqK,gBAAgB,EAAE;IACrB,MAAMG,IAAI,GAAG,IAAAC,kBAAY,EAACH,QAAQ,CAAC;IACnC,MAAM;MAACI,MAAM;MAAEC;IAAI,CAAC,GAAG3E,QAAQ;IAC/BA,QAAQ,GAAG,IAAA4E,oBAAa,EAACJ,IAAI,IAAIF,QAAQ,CAACtL,QAAQ,CAACmB,MAAGA,CAAC,CAAC0K,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;IACzE7E,QAAQ,CAAC0E,MAAM,GAAGA,MAAM;IACxB1E,QAAQ,CAAC2E,IAAI,GAAGA,IAAI;EACtB;EAEA,OAAO3E,QAAQ;AACjB;AAQA,SAAS8E,gBAAgBA,CAAC9J,SAAS,EAAEiF,cAAc,EAAEjG,IAAI,EAAE;EACzD,OAAO,IAAIe,8BAA8B,CACvCC,SAAS,EACTiF,cAAc,IAAI,IAAAH,oBAAa,EAAC,KAAII,UAAG,EAAC,GAAG,EAAED,cAAc,CAAC,CAAC,EAC7D,IAAAH,oBAAa,EAAC9F,IAAI,CACpB,CAAC;AACH;AAQA,SAAS+K,eAAeA,CAAC5J,OAAO,EAAE8E,cAAc,EAAEjG,IAAI,EAAE;EACtD,OAAO,IAAIkB,6BAA6B,CACtC,IAAA4E,oBAAa,EAAC,KAAII,UAAG,EAAC,GAAG,EAAED,cAAc,CAAC,CAAC,EAC3C9E,OAAO,EACPnB,IAAI,IAAI,IAAA8F,oBAAa,EAAC9F,IAAI,CAC5B,CAAC;AACH;AAUA,SAASgL,mBAAmBA,CAAClL,OAAO,EAAEiJ,KAAK,EAAE9C,cAAc,EAAE+C,QAAQ,EAAEhJ,IAAI,EAAE;EAC3E,MAAMD,MAAM,GAAI,4CAA2CgJ,KAAM,cAC/DC,QAAQ,GAAG,SAAS,GAAG,SACxB,mBAAkB,IAAAlD,oBAAa,EAACG,cAAc,CAAE,EAAC;EAClD,MAAM,IAAIpG,4BAA4B,CACpCC,OAAO,EACPC,MAAM,EACNC,IAAI,IAAI,IAAA8F,oBAAa,EAAC9F,IAAI,CAC5B,CAAC;AACH;AAUA,SAASiL,oBAAoBA,CAAC9J,OAAO,EAAEZ,MAAM,EAAE0F,cAAc,EAAE+C,QAAQ,EAAEhJ,IAAI,EAAE;EAC7EO,MAAM,GACJ,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,GACzCI,IAAI,CAACC,SAAS,CAACL,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,GAC/B,GAAEA,MAAO,EAAC;EAEjB,OAAO,IAAIF,0BAA0B,CACnC,IAAAyF,oBAAa,EAAC,KAAII,UAAG,EAAC,GAAG,EAAED,cAAc,CAAC,CAAC,EAC3C9E,OAAO,EACPZ,MAAM,EACNyI,QAAQ,EACRhJ,IAAI,IAAI,IAAA8F,oBAAa,EAAC9F,IAAI,CAC5B,CAAC;AACH;AAcA,SAASkL,0BAA0BA,CACjC3K,MAAM,EACNY,OAAO,EACP4H,KAAK,EACL9C,cAAc,EACdjG,IAAI,EACJmL,OAAO,EACPnC,QAAQ,EACRoC,SAAS,EACTpD,UAAU,EACV;EACA,IAAI7G,OAAO,KAAK,EAAE,IAAI,CAACgK,OAAO,IAAI5K,MAAM,CAACA,MAAM,CAACvC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EACjE,MAAMiN,oBAAoB,CAAClC,KAAK,EAAExI,MAAM,EAAE0F,cAAc,EAAE+C,QAAQ,EAAEhJ,IAAI,CAAC;EAE3E,IAAI,CAACO,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC,EAAE;IAC5B,IAAIsI,QAAQ,IAAI,CAACzI,MAAM,CAACG,UAAU,CAAC,KAAK,CAAC,IAAI,CAACH,MAAM,CAACG,UAAU,CAAC,GAAG,CAAC,EAAE;MACpE,IAAI2K,KAAK,GAAG,KAAK;MAEjB,IAAI;QACF,KAAInF,UAAG,EAAC3F,MAAM,CAAC;QACf8K,KAAK,GAAG,IAAI;MACd,CAAC,CAAC,OAAAC,QAAA,EAAM,CAER;MAEA,IAAI,CAACD,KAAK,EAAE;QACV,MAAME,YAAY,GAAGJ,OAAO,GACxBlD,4BAA4B,CAAC/K,IAAI,CAC/BwL,YAAY,EACZnI,MAAM,EACN,MAAMY,OACR,CAAC,GACDZ,MAAM,GAAGY,OAAO;QAEpB,OAAOqK,cAAc,CAACD,YAAY,EAAEtF,cAAc,EAAE+B,UAAU,CAAC;MACjE;IACF;IAEA,MAAMiD,oBAAoB,CAAClC,KAAK,EAAExI,MAAM,EAAE0F,cAAc,EAAE+C,QAAQ,EAAEhJ,IAAI,CAAC;EAC3E;EAEA,IAAIuI,mBAAmB,CAAC/I,IAAI,CAACe,MAAM,CAACrC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;IACtD,IAAIsK,6BAA6B,CAAChJ,IAAI,CAACe,MAAM,CAACrC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;MAChE,IAAI,CAACkN,SAAS,EAAE;QACd,MAAMtL,OAAO,GAAGqL,OAAO,GACnBpC,KAAK,CAACX,OAAO,CAAC,GAAG,EAAE,MAAMjH,OAAO,CAAC,GACjC4H,KAAK,GAAG5H,OAAO;QACnB,MAAMsK,cAAc,GAAGN,OAAO,GAC1BlD,4BAA4B,CAAC/K,IAAI,CAC/BwL,YAAY,EACZnI,MAAM,EACN,MAAMY,OACR,CAAC,GACDZ,MAAM;QACVuI,6BAA6B,CAC3B2C,cAAc,EACd3L,OAAO,EACPiJ,KAAK,EACL9C,cAAc,EACd+C,QAAQ,EACRhJ,IAAI,EACJ,IACF,CAAC;MACH;IACF,CAAC,MAAM;MACL,MAAMiL,oBAAoB,CAAClC,KAAK,EAAExI,MAAM,EAAE0F,cAAc,EAAE+C,QAAQ,EAAEhJ,IAAI,CAAC;IAC3E;EACF;EAEA,MAAMgG,QAAQ,GAAG,KAAIE,UAAG,EAAC3F,MAAM,EAAE0F,cAAc,CAAC;EAChD,MAAMyF,YAAY,GAAG1F,QAAQ,CAACI,QAAQ;EACtC,MAAMnF,WAAW,GAAG,KAAIiF,UAAG,EAAC,GAAG,EAAED,cAAc,CAAC,CAACG,QAAQ;EAEzD,IAAI,CAACsF,YAAY,CAAChL,UAAU,CAACO,WAAW,CAAC,EACvC,MAAMgK,oBAAoB,CAAClC,KAAK,EAAExI,MAAM,EAAE0F,cAAc,EAAE+C,QAAQ,EAAEhJ,IAAI,CAAC;EAE3E,IAAImB,OAAO,KAAK,EAAE,EAAE,OAAO6E,QAAQ;EAEnC,IAAIuC,mBAAmB,CAAC/I,IAAI,CAAC2B,OAAO,CAAC,KAAK,IAAI,EAAE;IAC9C,MAAMrB,OAAO,GAAGqL,OAAO,GACnBpC,KAAK,CAACX,OAAO,CAAC,GAAG,EAAE,MAAMjH,OAAO,CAAC,GACjC4H,KAAK,GAAG5H,OAAO;IACnB,IAAIqH,6BAA6B,CAAChJ,IAAI,CAAC2B,OAAO,CAAC,KAAK,IAAI,EAAE;MACxD,IAAI,CAACiK,SAAS,EAAE;QACd,MAAMK,cAAc,GAAGN,OAAO,GAC1BlD,4BAA4B,CAAC/K,IAAI,CAC/BwL,YAAY,EACZnI,MAAM,EACN,MAAMY,OACR,CAAC,GACDZ,MAAM;QACVuI,6BAA6B,CAC3B2C,cAAc,EACd3L,OAAO,EACPiJ,KAAK,EACL9C,cAAc,EACd+C,QAAQ,EACRhJ,IAAI,EACJ,KACF,CAAC;MACH;IACF,CAAC,MAAM;MACLgL,mBAAmB,CAAClL,OAAO,EAAEiJ,KAAK,EAAE9C,cAAc,EAAE+C,QAAQ,EAAEhJ,IAAI,CAAC;IACrE;EACF;EAEA,IAAImL,OAAO,EAAE;IACX,OAAO,KAAIjF,UAAG,EACZ+B,4BAA4B,CAAC/K,IAAI,CAC/BwL,YAAY,EACZ1C,QAAQ,CAACsD,IAAI,EACb,MAAMnI,OACR,CACF,CAAC;EACH;EAEA,OAAO,KAAI+E,UAAG,EAAC/E,OAAO,EAAE6E,QAAQ,CAAC;AACnC;AAMA,SAAS2F,YAAYA,CAAC5O,GAAG,EAAE;EACzB,MAAM6O,SAAS,GAAGnI,MAAM,CAAC1G,GAAG,CAAC;EAC7B,IAAK,GAAE6O,SAAU,EAAC,KAAK7O,GAAG,EAAE,OAAO,KAAK;EACxC,OAAO6O,SAAS,IAAI,CAAC,IAAIA,SAAS,GAAG,UAAa;AACpD;AAcA,SAASC,oBAAoBA,CAC3B5F,cAAc,EACd1F,MAAM,EACNY,OAAO,EACP2K,cAAc,EACd9L,IAAI,EACJmL,OAAO,EACPnC,QAAQ,EACRoC,SAAS,EACTpD,UAAU,EACV;EACA,IAAI,OAAOzH,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAO2K,0BAA0B,CAC/B3K,MAAM,EACNY,OAAO,EACP2K,cAAc,EACd7F,cAAc,EACdjG,IAAI,EACJmL,OAAO,EACPnC,QAAQ,EACRoC,SAAS,EACTpD,UACF,CAAC;EACH;EAEA,IAAInJ,KAAK,CAACC,OAAO,CAACyB,MAAM,CAAC,EAAE;IAEzB,MAAMwL,UAAU,GAAGxL,MAAM;IACzB,IAAIwL,UAAU,CAAC/N,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAGxC,IAAIgO,aAAa;IACjB,IAAI7B,CAAC,GAAG,CAAC,CAAC;IAEV,OAAO,EAAEA,CAAC,GAAG4B,UAAU,CAAC/N,MAAM,EAAE;MAC9B,MAAMiO,UAAU,GAAGF,UAAU,CAAC5B,CAAC,CAAC;MAEhC,IAAI+B,aAAa;MACjB,IAAI;QACFA,aAAa,GAAGL,oBAAoB,CAClC5F,cAAc,EACdgG,UAAU,EACV9K,OAAO,EACP2K,cAAc,EACd9L,IAAI,EACJmL,OAAO,EACPnC,QAAQ,EACRoC,SAAS,EACTpD,UACF,CAAC;MACH,CAAC,CAAC,OAAOzF,KAAK,EAAE;QACd,MAAMyC,SAAS,GAAkCzC,KAAM;QACvDyJ,aAAa,GAAGhH,SAAS;QACzB,IAAIA,SAAS,CAACjC,IAAI,KAAK,4BAA4B,EAAE;QACrD,MAAMR,KAAK;MACb;MAEA,IAAI2J,aAAa,KAAKjM,SAAS,EAAE;MAEjC,IAAIiM,aAAa,KAAK,IAAI,EAAE;QAC1BF,aAAa,GAAG,IAAI;QACpB;MACF;MAEA,OAAOE,aAAa;IACtB;IAEA,IAAIF,aAAa,KAAK/L,SAAS,IAAI+L,aAAa,KAAK,IAAI,EAAE;MACzD,OAAO,IAAI;IACb;IAEA,MAAMA,aAAa;EACrB;EAEA,IAAI,OAAOzL,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;IACjD,MAAM4L,IAAI,GAAGvP,MAAM,CAACwP,mBAAmB,CAAC7L,MAAM,CAAC;IAC/C,IAAI4J,CAAC,GAAG,CAAC,CAAC;IAEV,OAAO,EAAEA,CAAC,GAAGgC,IAAI,CAACnO,MAAM,EAAE;MACxB,MAAMjB,GAAG,GAAGoP,IAAI,CAAChC,CAAC,CAAC;MACnB,IAAIwB,YAAY,CAAC5O,GAAG,CAAC,EAAE;QACrB,MAAM,IAAImD,0BAA0B,CAClC,IAAA4F,oBAAa,EAACG,cAAc,CAAC,EAC7BjG,IAAI,EACJ,iDACF,CAAC;MACH;IACF;IAEAmK,CAAC,GAAG,CAAC,CAAC;IAEN,OAAO,EAAEA,CAAC,GAAGgC,IAAI,CAACnO,MAAM,EAAE;MACxB,MAAMjB,GAAG,GAAGoP,IAAI,CAAChC,CAAC,CAAC;MACnB,IAAIpN,GAAG,KAAK,SAAS,IAAKiL,UAAU,IAAIA,UAAU,CAACxL,GAAG,CAACO,GAAG,CAAE,EAAE;QAE5D,MAAMsP,iBAAiB,GAA2B9L,MAAM,CAACxD,GAAG,CAAE;QAC9D,MAAMmP,aAAa,GAAGL,oBAAoB,CACxC5F,cAAc,EACdoG,iBAAiB,EACjBlL,OAAO,EACP2K,cAAc,EACd9L,IAAI,EACJmL,OAAO,EACPnC,QAAQ,EACRoC,SAAS,EACTpD,UACF,CAAC;QACD,IAAIkE,aAAa,KAAKjM,SAAS,EAAE;QACjC,OAAOiM,aAAa;MACtB;IACF;IAEA,OAAO,IAAI;EACb;EAEA,IAAI3L,MAAM,KAAK,IAAI,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,MAAM0K,oBAAoB,CACxBa,cAAc,EACdvL,MAAM,EACN0F,cAAc,EACd+C,QAAQ,EACRhJ,IACF,CAAC;AACH;AAQA,SAASsM,6BAA6BA,CAAC5G,OAAO,EAAEO,cAAc,EAAEjG,IAAI,EAAE;EACpE,IAAI,OAAO0F,OAAO,KAAK,QAAQ,IAAI7G,KAAK,CAACC,OAAO,CAAC4G,OAAO,CAAC,EAAE,OAAO,IAAI;EACtE,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE,OAAO,KAAK;EAEjE,MAAMyG,IAAI,GAAGvP,MAAM,CAACwP,mBAAmB,CAAC1G,OAAO,CAAC;EAChD,IAAI6G,kBAAkB,GAAG,KAAK;EAC9B,IAAIpC,CAAC,GAAG,CAAC;EACT,IAAIqC,CAAC,GAAG,CAAC,CAAC;EACV,OAAO,EAAEA,CAAC,GAAGL,IAAI,CAACnO,MAAM,EAAE;IACxB,MAAMjB,GAAG,GAAGoP,IAAI,CAACK,CAAC,CAAC;IACnB,MAAMC,qBAAqB,GAAG1P,GAAG,KAAK,EAAE,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG;IAC1D,IAAIoN,CAAC,EAAE,KAAK,CAAC,EAAE;MACboC,kBAAkB,GAAGE,qBAAqB;IAC5C,CAAC,MAAM,IAAIF,kBAAkB,KAAKE,qBAAqB,EAAE;MACvD,MAAM,IAAIvM,0BAA0B,CAClC,IAAA4F,oBAAa,EAACG,cAAc,CAAC,EAC7BjG,IAAI,EACJ,sEAAsE,GACpE,sEAAsE,GACtE,uDACJ,CAAC;IACH;EACF;EAEA,OAAOuM,kBAAkB;AAC3B;AAOA,SAASG,mCAAmCA,CAAC3D,KAAK,EAAE4D,QAAQ,EAAE3M,IAAI,EAAE;EAClE,MAAMuF,SAAS,GAAG,IAAAO,oBAAa,EAAC6G,QAAQ,CAAC;EACzC,IAAI/D,sBAAsB,CAACpM,GAAG,CAAC+I,SAAS,GAAG,GAAG,GAAGwD,KAAK,CAAC,EAAE;EACzDH,sBAAsB,CAACgE,GAAG,CAACrH,SAAS,GAAG,GAAG,GAAGwD,KAAK,CAAC;EACnDzL,SAAMA,CAAC,CAAC6L,WAAW,CAChB,qDAAoDJ,KAAM,WAAU,GAClE,uDAAsDxD,SAAU,GAC/DvF,IAAI,GAAI,kBAAiB,IAAA8F,oBAAa,EAAC9F,IAAI,CAAE,EAAC,GAAG,EAClD,4DAA2D,EAC9D,oBAAoB,EACpB,SACF,CAAC;AACH;AAUA,SAAS6M,qBAAqBA,CAC5B5G,cAAc,EACd6F,cAAc,EACdxG,aAAa,EACbtF,IAAI,EACJgI,UAAU,EACV;EACA,IAAItC,OAAO,GAAGJ,aAAa,CAACI,OAAO;EAEnC,IAAI4G,6BAA6B,CAAC5G,OAAO,EAAEO,cAAc,EAAEjG,IAAI,CAAC,EAAE;IAChE0F,OAAO,GAAG;MAAC,GAAG,EAAEA;IAAO,CAAC;EAC1B;EAEA,IACE4C,GAAG,CAACpL,IAAI,CAACwI,OAAO,EAAEoG,cAAc,CAAC,IACjC,CAACA,cAAc,CAAC7M,QAAQ,CAAC,GAAG,CAAC,IAC7B,CAAC6M,cAAc,CAAC9M,QAAQ,CAAC,GAAG,CAAC,EAC7B;IAEA,MAAMuB,MAAM,GAAGmF,OAAO,CAACoG,cAAc,CAAC;IACtC,MAAMI,aAAa,GAAGL,oBAAoB,CACxC5F,cAAc,EACd1F,MAAM,EACN,EAAE,EACFuL,cAAc,EACd9L,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACLgI,UACF,CAAC;IACD,IAAIkE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKjM,SAAS,EAAE;MACzD,MAAM8K,eAAe,CAACe,cAAc,EAAE7F,cAAc,EAAEjG,IAAI,CAAC;IAC7D;IAEA,OAAOkM,aAAa;EACtB;EAEA,IAAIY,SAAS,GAAG,EAAE;EAClB,IAAIC,gBAAgB,GAAG,EAAE;EACzB,MAAMZ,IAAI,GAAGvP,MAAM,CAACwP,mBAAmB,CAAC1G,OAAO,CAAC;EAChD,IAAIyE,CAAC,GAAG,CAAC,CAAC;EAEV,OAAO,EAAEA,CAAC,GAAGgC,IAAI,CAACnO,MAAM,EAAE;IACxB,MAAMjB,GAAG,GAAGoP,IAAI,CAAChC,CAAC,CAAC;IACnB,MAAM6C,YAAY,GAAGjQ,GAAG,CAAC2C,OAAO,CAAC,GAAG,CAAC;IAErC,IACEsN,YAAY,KAAK,CAAC,CAAC,IACnBlB,cAAc,CAACpL,UAAU,CAAC3D,GAAG,CAACmB,KAAK,CAAC,CAAC,EAAE8O,YAAY,CAAC,CAAC,EACrD;MAOA,IAAIlB,cAAc,CAAC9M,QAAQ,CAAC,GAAG,CAAC,EAAE;QAChC0N,mCAAmC,CACjCZ,cAAc,EACd7F,cAAc,EACdjG,IACF,CAAC;MACH;MAEA,MAAMiN,cAAc,GAAGlQ,GAAG,CAACmB,KAAK,CAAC8O,YAAY,GAAG,CAAC,CAAC;MAElD,IACElB,cAAc,CAAC9N,MAAM,IAAIjB,GAAG,CAACiB,MAAM,IACnC8N,cAAc,CAAC9M,QAAQ,CAACiO,cAAc,CAAC,IACvCC,iBAAiB,CAACJ,SAAS,EAAE/P,GAAG,CAAC,KAAK,CAAC,IACvCA,GAAG,CAACoQ,WAAW,CAAC,GAAG,CAAC,KAAKH,YAAY,EACrC;QACAF,SAAS,GAAG/P,GAAG;QACfgQ,gBAAgB,GAAGjB,cAAc,CAAC5N,KAAK,CACrC8O,YAAY,EACZlB,cAAc,CAAC9N,MAAM,GAAGiP,cAAc,CAACjP,MACzC,CAAC;MACH;IACF;EACF;EAEA,IAAI8O,SAAS,EAAE;IAEb,MAAMvM,MAAM,GAA2BmF,OAAO,CAACoH,SAAS,CAAE;IAC1D,MAAMZ,aAAa,GAAGL,oBAAoB,CACxC5F,cAAc,EACd1F,MAAM,EACNwM,gBAAgB,EAChBD,SAAS,EACT9M,IAAI,EACJ,IAAI,EACJ,KAAK,EACL8L,cAAc,CAAC9M,QAAQ,CAAC,GAAG,CAAC,EAC5BgJ,UACF,CAAC;IAED,IAAIkE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKjM,SAAS,EAAE;MACzD,MAAM8K,eAAe,CAACe,cAAc,EAAE7F,cAAc,EAAEjG,IAAI,CAAC;IAC7D;IAEA,OAAOkM,aAAa;EACtB;EAEA,MAAMnB,eAAe,CAACe,cAAc,EAAE7F,cAAc,EAAEjG,IAAI,CAAC;AAC7D;AAMA,SAASkN,iBAAiBA,CAACE,CAAC,EAAEC,CAAC,EAAE;EAC/B,MAAMC,aAAa,GAAGF,CAAC,CAAC1N,OAAO,CAAC,GAAG,CAAC;EACpC,MAAM6N,aAAa,GAAGF,CAAC,CAAC3N,OAAO,CAAC,GAAG,CAAC;EACpC,MAAM8N,WAAW,GAAGF,aAAa,KAAK,CAAC,CAAC,GAAGF,CAAC,CAACpP,MAAM,GAAGsP,aAAa,GAAG,CAAC;EACvE,MAAMG,WAAW,GAAGF,aAAa,KAAK,CAAC,CAAC,GAAGF,CAAC,CAACrP,MAAM,GAAGuP,aAAa,GAAG,CAAC;EACvE,IAAIC,WAAW,GAAGC,WAAW,EAAE,OAAO,CAAC,CAAC;EACxC,IAAIA,WAAW,GAAGD,WAAW,EAAE,OAAO,CAAC;EACvC,IAAIF,aAAa,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC;EAClC,IAAIC,aAAa,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EACnC,IAAIH,CAAC,CAACpP,MAAM,GAAGqP,CAAC,CAACrP,MAAM,EAAE,OAAO,CAAC,CAAC;EAClC,IAAIqP,CAAC,CAACrP,MAAM,GAAGoP,CAAC,CAACpP,MAAM,EAAE,OAAO,CAAC;EACjC,OAAO,CAAC;AACV;AAQA,SAAS0P,qBAAqBA,CAACjP,IAAI,EAAEuB,IAAI,EAAEgI,UAAU,EAAE;EACrD,IAAIvJ,IAAI,KAAK,GAAG,IAAIA,IAAI,CAACiC,UAAU,CAAC,IAAI,CAAC,IAAIjC,IAAI,CAACO,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC/D,MAAMe,MAAM,GAAG,gDAAgD;IAC/D,MAAM,IAAIF,4BAA4B,CAACpB,IAAI,EAAEsB,MAAM,EAAE,IAAA+F,oBAAa,EAAC9F,IAAI,CAAC,CAAC;EAC3E;EAGA,IAAIiG,cAAc;EAElB,MAAMX,aAAa,GAAGS,qBAAqB,CAAC/F,IAAI,CAAC;EAEjD,IAAIsF,aAAa,CAACE,MAAM,EAAE;IACxBS,cAAc,GAAG,IAAA2E,oBAAa,EAACtF,aAAa,CAACC,SAAS,CAAC;IACvD,MAAMI,OAAO,GAAGL,aAAa,CAACK,OAAO;IACrC,IAAIA,OAAO,EAAE;MACX,IAAI2C,GAAG,CAACpL,IAAI,CAACyI,OAAO,EAAElH,IAAI,CAAC,IAAI,CAACA,IAAI,CAACQ,QAAQ,CAAC,GAAG,CAAC,EAAE;QAClD,MAAMiN,aAAa,GAAGL,oBAAoB,CACxC5F,cAAc,EACdN,OAAO,CAAClH,IAAI,CAAC,EACb,EAAE,EACFA,IAAI,EACJuB,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,KAAK,EACLgI,UACF,CAAC;QACD,IAAIkE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKjM,SAAS,EAAE;UACzD,OAAOiM,aAAa;QACtB;MACF,CAAC,MAAM;QACL,IAAIY,SAAS,GAAG,EAAE;QAClB,IAAIC,gBAAgB,GAAG,EAAE;QACzB,MAAMZ,IAAI,GAAGvP,MAAM,CAACwP,mBAAmB,CAACzG,OAAO,CAAC;QAChD,IAAIwE,CAAC,GAAG,CAAC,CAAC;QAEV,OAAO,EAAEA,CAAC,GAAGgC,IAAI,CAACnO,MAAM,EAAE;UACxB,MAAMjB,GAAG,GAAGoP,IAAI,CAAChC,CAAC,CAAC;UACnB,MAAM6C,YAAY,GAAGjQ,GAAG,CAAC2C,OAAO,CAAC,GAAG,CAAC;UAErC,IAAIsN,YAAY,KAAK,CAAC,CAAC,IAAIvO,IAAI,CAACiC,UAAU,CAAC3D,GAAG,CAACmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5D,MAAM+O,cAAc,GAAGlQ,GAAG,CAACmB,KAAK,CAAC8O,YAAY,GAAG,CAAC,CAAC;YAClD,IACEvO,IAAI,CAACT,MAAM,IAAIjB,GAAG,CAACiB,MAAM,IACzBS,IAAI,CAACO,QAAQ,CAACiO,cAAc,CAAC,IAC7BC,iBAAiB,CAACJ,SAAS,EAAE/P,GAAG,CAAC,KAAK,CAAC,IACvCA,GAAG,CAACoQ,WAAW,CAAC,GAAG,CAAC,KAAKH,YAAY,EACrC;cACAF,SAAS,GAAG/P,GAAG;cACfgQ,gBAAgB,GAAGtO,IAAI,CAACP,KAAK,CAC3B8O,YAAY,EACZvO,IAAI,CAACT,MAAM,GAAGiP,cAAc,CAACjP,MAC/B,CAAC;YACH;UACF;QACF;QAEA,IAAI8O,SAAS,EAAE;UACb,MAAMvM,MAAM,GAAGoF,OAAO,CAACmH,SAAS,CAAC;UACjC,MAAMZ,aAAa,GAAGL,oBAAoB,CACxC5F,cAAc,EACd1F,MAAM,EACNwM,gBAAgB,EAChBD,SAAS,EACT9M,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACLgI,UACF,CAAC;UAED,IAAIkE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKjM,SAAS,EAAE;YACzD,OAAOiM,aAAa;UACtB;QACF;MACF;IACF;EACF;EAEA,MAAMpB,gBAAgB,CAACrM,IAAI,EAAEwH,cAAc,EAAEjG,IAAI,CAAC;AACpD;AAUA,SAAS2N,gBAAgBA,CAAC3M,SAAS,EAAEhB,IAAI,EAAE;EACzC,IAAI4N,cAAc,GAAG5M,SAAS,CAACtB,OAAO,CAAC,GAAG,CAAC;EAC3C,IAAImO,gBAAgB,GAAG,IAAI;EAC3B,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAI9M,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACxB8M,QAAQ,GAAG,IAAI;IACf,IAAIF,cAAc,KAAK,CAAC,CAAC,IAAI5M,SAAS,CAAChD,MAAM,KAAK,CAAC,EAAE;MACnD6P,gBAAgB,GAAG,KAAK;IAC1B,CAAC,MAAM;MACLD,cAAc,GAAG5M,SAAS,CAACtB,OAAO,CAAC,GAAG,EAAEkO,cAAc,GAAG,CAAC,CAAC;IAC7D;EACF;EAEA,MAAMG,WAAW,GACfH,cAAc,KAAK,CAAC,CAAC,GAAG5M,SAAS,GAAGA,SAAS,CAAC9C,KAAK,CAAC,CAAC,EAAE0P,cAAc,CAAC;EAIxE,IAAInF,uBAAuB,CAACjJ,IAAI,CAACuO,WAAW,CAAC,KAAK,IAAI,EAAE;IACtDF,gBAAgB,GAAG,KAAK;EAC1B;EAEA,IAAI,CAACA,gBAAgB,EAAE;IACrB,MAAM,IAAIhO,4BAA4B,CACpCmB,SAAS,EACT,6BAA6B,EAC7B,IAAA8E,oBAAa,EAAC9F,IAAI,CACpB,CAAC;EACH;EAEA,MAAM8L,cAAc,GAClB,GAAG,IAAI8B,cAAc,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG5M,SAAS,CAAC9C,KAAK,CAAC0P,cAAc,CAAC,CAAC;EAEtE,OAAO;IAACG,WAAW;IAAEjC,cAAc;IAAEgC;EAAQ,CAAC;AAChD;AAQA,SAAStC,cAAcA,CAACxK,SAAS,EAAEhB,IAAI,EAAEgI,UAAU,EAAE;EACnD,IAAIgG,wBAAc,CAAC/O,QAAQ,CAAC+B,SAAS,CAAC,EAAE;IACtC,OAAO,KAAIkF,UAAG,EAAC,OAAO,GAAGlF,SAAS,CAAC;EACrC;EAEA,MAAM;IAAC+M,WAAW;IAAEjC,cAAc;IAAEgC;EAAQ,CAAC,GAAGH,gBAAgB,CAC9D3M,SAAS,EACThB,IACF,CAAC;EAGD,MAAMsF,aAAa,GAAGS,qBAAqB,CAAC/F,IAAI,CAAC;EAIjD,IAAIsF,aAAa,CAACE,MAAM,EAAE;IACxB,MAAMS,cAAc,GAAG,IAAA2E,oBAAa,EAACtF,aAAa,CAACC,SAAS,CAAC;IAC7D,IACED,aAAa,CAAC7G,IAAI,KAAKsP,WAAW,IAClCzI,aAAa,CAACI,OAAO,KAAKzF,SAAS,IACnCqF,aAAa,CAACI,OAAO,KAAK,IAAI,EAC9B;MACA,OAAOmH,qBAAqB,CAC1B5G,cAAc,EACd6F,cAAc,EACdxG,aAAa,EACbtF,IAAI,EACJgI,UACF,CAAC;IACH;EACF;EAEA,IAAI/B,cAAc,GAAG,KAAIC,UAAG,EAC1B,iBAAiB,GAAG6H,WAAW,GAAG,eAAe,EACjD/N,IACF,CAAC;EACD,IAAImG,eAAe,GAAG,IAAAL,oBAAa,EAACG,cAAc,CAAC;EAEnD,IAAIgI,QAAQ;EACZ,GAAG;IACD,MAAMC,IAAI,GAAG1E,WAAW,CAACrD,eAAe,CAACjI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvD,IAAI,CAACgQ,IAAI,CAAC3D,WAAW,CAAC,CAAC,EAAE;MACvB0D,QAAQ,GAAG9H,eAAe;MAC1BF,cAAc,GAAG,KAAIC,UAAG,EACtB,CAAC4H,QAAQ,GAAG,2BAA2B,GAAG,wBAAwB,IAChEC,WAAW,GACX,eAAe,EACjB9H,cACF,CAAC;MACDE,eAAe,GAAG,IAAAL,oBAAa,EAACG,cAAc,CAAC;MAC/C;IACF;IAGA,MAAMX,aAAa,GAAGH,gBAAgB,CAACgB,eAAe,EAAEnF,SAAS,EAAEhB,IAAI,CAAC;IACxE,IAAIsF,aAAa,CAACI,OAAO,KAAKzF,SAAS,IAAIqF,aAAa,CAACI,OAAO,KAAK,IAAI,EAAE;MACzE,OAAOmH,qBAAqB,CAC1B5G,cAAc,EACd6F,cAAc,EACdxG,aAAa,EACbtF,IAAI,EACJgI,UACF,CAAC;IACH;IAEA,IAAI8D,cAAc,KAAK,GAAG,EAAE;MAC1B,OAAO9B,iBAAiB,CAAC/D,cAAc,EAAEX,aAAa,EAAEtF,IAAI,CAAC;IAC/D;IAEA,OAAO,KAAIkG,UAAG,EAAC4F,cAAc,EAAE7F,cAAc,CAAC;EAEhD,CAAC,QAAQE,eAAe,CAACnI,MAAM,KAAKiQ,QAAQ,CAACjQ,MAAM;EAEnD,MAAM,IAAI6C,oBAAoB,CAACkN,WAAW,EAAE,IAAAjI,oBAAa,EAAC9F,IAAI,CAAC,CAAC;AAClE;AAMA,SAASmO,mBAAmBA,CAACnN,SAAS,EAAE;EACtC,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACxB,IAAIA,SAAS,CAAChD,MAAM,KAAK,CAAC,IAAIgD,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,OAAO,IAAI;IAC/D,IACEA,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,KACnBA,SAAS,CAAChD,MAAM,KAAK,CAAC,IAAIgD,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAChD;MACA,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAMA,SAASoN,uCAAuCA,CAACpN,SAAS,EAAE;EAC1D,IAAIA,SAAS,KAAK,EAAE,EAAE,OAAO,KAAK;EAClC,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,OAAO,IAAI;EACrC,OAAOmN,mBAAmB,CAACnN,SAAS,CAAC;AACvC;AAiBA,SAASqN,aAAaA,CAACrN,SAAS,EAAEhB,IAAI,EAAEgI,UAAU,EAAEqC,gBAAgB,EAAE;EACpE,MAAMxI,QAAQ,GAAG7B,IAAI,CAAC6B,QAAQ;EAC9B,MAAMyM,QAAQ,GAAGzM,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,QAAQ;EAI9D,IAAImE,QAAQ;EAEZ,IAAIoI,uCAAuC,CAACpN,SAAS,CAAC,EAAE;IACtDgF,QAAQ,GAAG,KAAIE,UAAG,EAAClF,SAAS,EAAEhB,IAAI,CAAC;EACrC,CAAC,MAAM,IAAI,CAACsO,QAAQ,IAAItN,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC5CgF,QAAQ,GAAG0H,qBAAqB,CAAC1M,SAAS,EAAEhB,IAAI,EAAEgI,UAAU,CAAC;EAC/D,CAAC,MAAM;IACL,IAAI;MACFhC,QAAQ,GAAG,KAAIE,UAAG,EAAClF,SAAS,CAAC;IAC/B,CAAC,CAAC,OAAAuN,QAAA,EAAM;MACN,IAAI,CAACD,QAAQ,EAAE;QACbtI,QAAQ,GAAGwF,cAAc,CAACxK,SAAS,EAAEhB,IAAI,EAAEgI,UAAU,CAAC;MACxD;IACF;EACF;EAEApJ,QAAKA,CAAC,CAACoH,QAAQ,KAAK/F,SAAS,EAAE,wBAAwB,CAAC;EAExD,IAAI+F,QAAQ,CAACnE,QAAQ,KAAK,OAAO,EAAE;IACjC,OAAOmE,QAAQ;EACjB;EAEA,OAAOoE,kBAAkB,CAACpE,QAAQ,EAAEhG,IAAI,EAAEqK,gBAAgB,CAAC;AAC7D;AAOA,SAASmE,uBAAuBA,CAACxN,SAAS,EAAEiG,MAAM,EAAEwH,eAAe,EAAE;EACnE,IAAIA,eAAe,EAAE;IAEnB,MAAMC,cAAc,GAAGD,eAAe,CAAC5M,QAAQ;IAE/C,IAAI6M,cAAc,KAAK,OAAO,IAAIA,cAAc,KAAK,QAAQ,EAAE;MAC7D,IAAIN,uCAAuC,CAACpN,SAAS,CAAC,EAAE;QAEtD,MAAM2N,cAAc,GAAG1H,MAAM,oBAANA,MAAM,CAAEpF,QAAQ;QAIvC,IACE8M,cAAc,IACdA,cAAc,KAAK,QAAQ,IAC3BA,cAAc,KAAK,OAAO,EAC1B;UACA,MAAM,IAAI7N,6BAA6B,CACrCE,SAAS,EACTyN,eAAe,EACf,qDACF,CAAC;QACH;QAEA,OAAO;UAAC9M,GAAG,EAAE,CAAAsF,MAAM,oBAANA,MAAM,CAAEqC,IAAI,KAAI;QAAE,CAAC;MAClC;MAEA,IAAI0E,wBAAc,CAAC/O,QAAQ,CAAC+B,SAAS,CAAC,EAAE;QACtC,MAAM,IAAIF,6BAA6B,CACrCE,SAAS,EACTyN,eAAe,EACf,qDACF,CAAC;MACH;MAEA,MAAM,IAAI3N,6BAA6B,CACrCE,SAAS,EACTyN,eAAe,EACf,sDACF,CAAC;IACH;EACF;AACF;AAkBA,SAASpD,KAAKA,CAACzH,IAAI,EAAE;EACnB,OAAOgL,OAAO,CACZhL,IAAI,IACF,OAAOA,IAAI,KAAK,QAAQ,IACxB,MAAM,IAAIA,IAAI,IACd,OAAOA,IAAI,CAAC0F,IAAI,KAAK,QAAQ,IAC7B,UAAU,IAAI1F,IAAI,IAClB,OAAOA,IAAI,CAAC/B,QAAQ,KAAK,QAAQ,IACjC+B,IAAI,CAAC0F,IAAI,IACT1F,IAAI,CAAC/B,QACT,CAAC;AACH;AAQA,SAASgN,uBAAuBA,CAACxF,SAAS,EAAE;EAC1C,IAAIA,SAAS,KAAKpJ,SAAS,EAAE;IAC3B;EACF;EAEA,IAAI,OAAOoJ,SAAS,KAAK,QAAQ,IAAI,CAACgC,KAAK,CAAChC,SAAS,CAAC,EAAE;IACtD,MAAM,IAAIzL,KAAK,CAACW,oBAAoB,CAClC,WAAW,EACX,CAAC,QAAQ,EAAE,KAAK,CAAC,EACjB8K,SACF,CAAC;EACH;AACF;AAKA,SAASyF,6BAA6BA,CAACnN,GAAG,EAAE;EAE1C,MAAME,QAAQ,GAAGF,GAAG,CAACE,QAAQ;EAE7B,IAAIA,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,OAAO,EAAE;IACxE,MAAM,IAAIH,8BAA8B,CAACC,GAAG,CAAC;EAC/C;AACF;AAMA,SAASoN,2BAA2BA,CAAC9H,MAAM,EAAEoB,0BAA0B,EAAE;EAEvE,MAAMxG,QAAQ,GAAGoF,MAAM,oBAANA,MAAM,CAAEpF,QAAQ;EAEjC,IACEA,QAAQ,IACRA,QAAQ,KAAK,OAAO,IACpBA,QAAQ,KAAK,OAAO,KACnB,CAACwG,0BAA0B,IACzBxG,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,OAAQ,CAAC,EAClD;IACA,MAAM,IAAIH,8BAA8B,CACtCuF,MAAM,EACN,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC+H,MAAM,CACrB3G,0BAA0B,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,EACnD,CACF,CAAC;EACH;AACF;AAOA,SAAS4G,cAAcA,CAACjO,SAAS,EAAEyG,OAAO,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAM;IAAC4B;EAAS,CAAC,GAAG5B,OAAO;EAC3B7I,QAAKA,CAAC,CAACyK,SAAS,KAAKpJ,SAAS,EAAE,oCAAoC,CAAC;EACrE4O,uBAAuB,CAACxF,SAAS,CAAC;EAGlC,IAAIoF,eAAe;EACnB,IAAIpF,SAAS,EAAE;IACb,IAAI;MACFoF,eAAe,GAAG,KAAIvI,UAAG,EAACmD,SAAS,CAAC;IACtC,CAAC,CAAC,OAAA6F,QAAA,EAAM,CAER;EACF;EAGA,IAAIjI,MAAM;EACV,IAAI;IACFA,MAAM,GAAGmH,uCAAuC,CAACpN,SAAS,CAAC,GACvD,KAAIkF,UAAG,EAAClF,SAAS,EAAEyN,eAAe,CAAC,GACnC,KAAIvI,UAAG,EAAClF,SAAS,CAAC;IAGtB,MAAMa,QAAQ,GAAGoF,MAAM,CAACpF,QAAQ;IAEhC,IACEA,QAAQ,KAAK,OAAO,IACnBwG,0BAA0B,KACxBxG,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,OAAO,CAAE,EAClD;MACA,OAAO;QAACF,GAAG,EAAEsF,MAAM,CAACqC,IAAI;QAAEpF,MAAM,EAAE;MAAI,CAAC;IACzC;EACF,CAAC,CAAC,OAAAiL,QAAA,EAAM,CAER;EAKA,MAAMC,WAAW,GAAGZ,uBAAuB,CACzCxN,SAAS,EACTiG,MAAM,EACNwH,eACF,CAAC;EAED,IAAIW,WAAW,EAAE,OAAOA,WAAW;EAGnC,IAAInI,MAAM,IAAIA,MAAM,CAACpF,QAAQ,KAAK,OAAO,EAAE,OAAO;IAACF,GAAG,EAAEX;EAAS,CAAC;EAElE+N,2BAA2B,CAAC9H,MAAM,EAAEoB,0BAA0B,CAAC;EAE/D,MAAML,UAAU,GAAGD,gBAAgB,CAACN,OAAO,CAACO,UAAU,CAAC;EAEvD,MAAMrG,GAAG,GAAG0M,aAAa,CAACrN,SAAS,EAAE,KAAIkF,UAAG,EAACmD,SAAS,CAAC,EAAErB,UAAU,EAAE,KAAK,CAAC;EAE3E8G,6BAA6B,CAACnN,GAAG,CAAC;EAElC,OAAO;IAGLA,GAAG,EAAEA,GAAG,CAAC2H,IAAI;IACbpF,MAAM,EAAEsD,6BAA6B,CAAC7F,GAAG,EAAE;MAAC0H;IAAS,CAAC;EACxD,CAAC;AACH;AAsBA,SAASgG,OAAOA,CAACrO,SAAS,EAAEsO,MAAM,EAAE;EAClC,IAAI,CAACA,MAAM,EAAE;IACX,MAAM,IAAIlP,KAAK,CACb,kEACF,CAAC;EACH;EAEA,IAAI;IACF,OAAO6O,cAAc,CAACjO,SAAS,EAAE;MAACqI,SAAS,EAAEiG;IAAM,CAAC,CAAC,CAAC3N,GAAG;EAC3D,CAAC,CAAC,OAAOY,KAAK,EAAE;IACd,MAAMyC,SAAS,GAAkCzC,KAAM;IAEvD,IACEyC,SAAS,CAACjC,IAAI,KAAK,4BAA4B,IAC/C,OAAOiC,SAAS,CAACrD,GAAG,KAAK,QAAQ,EACjC;MACA,OAAOqD,SAAS,CAACrD,GAAG;IACtB;IAEA,MAAMY,KAAK;EACb;AACF;AAAC"}