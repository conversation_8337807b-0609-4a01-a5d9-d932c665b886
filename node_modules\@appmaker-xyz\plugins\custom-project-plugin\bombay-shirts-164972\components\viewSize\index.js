import React, { useState, useEffect } from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ThemeText, UiKitButton } from '@appmaker-xyz/ui';
import Icon from 'react-native-vector-icons/Feather';
import {
  usePluginStore,
  appStorageApi,
  usePageState,
} from '@appmaker-xyz/core';
import ModalWebview from '../ModalWebview';
import { getProductProperties } from '../../helper/index';
import axios from 'axios';
import { shopifyIdHelper } from '@appmaker-xyz/shopify';

export async function isFitsmartSaved(blockItem, setpageState) {
  const { vendor } = blockItem;
  try {
    const { id, email } = appStorageApi().getState()?.user;
    const customerId = shopifyIdHelper(id, true);
    var config = {
      method: 'get',
      url: `https://new-admin.bombayshirts.com/sizing/v1/profile?customerId=${customerId}&email=${email}`,
      headers: {
        authority: 'new-admin.bombayshirts.com',
        accept: 'application/json, text/javascript, */*; q=0.01',
        'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8,ml;q=0.7',
        'content-type': 'application/json',
        origin: 'https://www.bombayshirts.com',
        referer: 'https://www.bombayshirts.com/',
        requesttoken: '38e128dec8bf42b885786f5f4f98e01c',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent':
          'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
      },
    };
    const request = await axios(config).then(function (response) {
      if (response?.data) {
        if (Object.keys(response.data).length > 0) {
          const currentFits =
            Object.keys(response.data.fitProfiles).length > 0
              ? response.data.fitProfiles
              : response.data.guestProfiles;
          if (vendor === 'Bombay Shirt Company') {
            if (currentFits.some((item) => item.brand === 'BOM')) {
              setpageState((state) => {
                state.isSizeSaved = true;
              });
              return { displayButton: true, fitSaved: true };
            }
          } else if (vendor === 'Korra') {
            if (currentFits.some((item) => item.brand === 'KOR')) {
              setpageState((state) => {
                state.isSizeSaved = true;
              });
              return { displayButton: true, fitSaved: true };
            }
          } else {
            return { displayButton: false, fitSaved: false };
          }
        }
      }
    });
    return request;
  } catch (error) {
    return { displayButton: false, fitSaved: false };
  }
}
function formatDataForProduct(jsonData) {
  const formated = {};
  Object.keys(jsonData).forEach((key) => {
    if (jsonData[key] === '' || !key.match(/properties/)) {
    } else {
      const newKey = key.replace('properties[', '').replace(']', '');
      formated[newKey] = jsonData[key];
    }
  });
  return formated;
}
function getBookingUrl({ onlineStoreUrl, handle, vendor }) {
  // picarioview#modal appview
  const productUrl =
    onlineStoreUrl ||
    `https://bombayshirt-india.myshopify.com/products/${handle}`;
  const preview_theme_id = onlineStoreUrl ? '131692888227' : '131723231402';
  let accessTokenParams;
  //  ;
  try {
    const { id, email, accessToken } = appStorageApi().getState()?.user;
    const customerId = shopifyIdHelper(id, true);
    // if
    accessTokenParams = `&accessToken=${accessToken}&customerId=${customerId}&email=${email}`;
  } catch (error) {}
  let finalURL = `${productUrl}/?preview_theme_id=${preview_theme_id}&view=appview${accessTokenParams}`;
  if (vendor === 'Korra') {
    finalURL = finalURL + '&view=korra-appview';
  }
  return finalURL;
}
const TimeSlotBooking = ({
  attributes,
  onPress,
  onAction,
  blockData,
  pageState,
}) => {
  const [timeSlot, setTimeSlot] = useState('');
  const [sizeSaved, setSizeSaved] = useState(true);
  const [updatedOn, setUpdatedOn] = useState(false);
  const [visible, setVisibility] = useState(false);
  const open = () => setVisibility(true);
  const close = () => setVisibility(false);
  const { appmakerAction } = attributes;
  const blockItem = attributes?.blockItem?.node;
  const sizeModalOpen = usePageState((state) => state.sizeModalOpen);
  useEffect(() => {
    setVisibility(sizeModalOpen);
  }, [sizeModalOpen]);
  useEffect(() => {
    isFitsmartSaved(blockItem, setpageState)
      .then((result) => {
        setSizeSaved(result?.fitSaved);
        // setSizeSaved(result.fitSaved);
      })
      .done();
  }, [sizeSaved]);

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  const setpageState = usePageState((state) => state.setState);
  const onMessage = (event) => {
    const { data } = event.nativeEvent;

    const jsonData = JSON.parse(data);
    setVisibility(false);
    const productAttributes = getProductProperties(jsonData);

    // }
    setVisibility(false);
    // const productAttributes = formatDataForProduct(jsonData);
    setpageState((state) => {
      state.sizeModalOpen = false;
      if (!state.productAttributes) {
        state.productAttributes = {};
      }
      state.productAttributes = {
        ...state.productAttributes,
        ...productAttributes,
      };
    });
  };
  const { productId, variantId } = attributes;
  const shopId = usePluginStore((state) => 'sss');
  let normalProductId = '',
    normalVariantId = '';
  try {
    normalProductId = shopifyIdHelper(productId, true);
    normalVariantId = shopifyIdHelper(variantId, true);
  } catch (error) {
    console.log(error);
  }
  return (
    <Layout style={styles.container}>
      <Layout style={styles.barContainer}>
        <ThemeText fontFamily="bold" style={styles.title}>
          {sizeSaved
            ? 'Your size is saved with us.'
            : 'Create your size in just 30 seconds with our fitsmart technology.'}
        </ThemeText>
        <Layout style={styles.barContainer}>
          {timeSlot ? (
            <ThemeText fontFamily="bold" style={styles.timeText}>
              {timeSlot}
            </ThemeText>
          ) : (
            <UiKitButton
              onPress={open}
              small
              status="dark"
              outline
              wholeContainerStyle={styles.button}>
              {sizeSaved ? 'View / Edit' : 'Get Size'}
            </UiKitButton>
          )}
          {timeSlot ? (
            <Icon
              name="x"
              size={12}
              onPress={() => setTimeSlot('')}
              style={styles.icon}
            />
          ) : null}
        </Layout>
      </Layout>
      <ModalWebview
        visible={visible}
        close={close}
        uri={getBookingUrl({
          onlineStoreUrl: blockData.node.onlineStoreUrl,
          handle: blockData.node.handle,
          vendor: blockData.node.vendor,
          shopId,
          productId: normalProductId,
          variantId: normalVariantId,
        })}
        onMessage={onMessage}
      />
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    padding: 8,
    flex: 1,
    marginBottom: 4,
  },
  barContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#E9EDF1',
    paddingVertical: 6,
    paddingStart: 8,
    paddingEnd: 4,
    borderRadius: 8,
  },
  title: {
    flex: 1,
    lineHeight: 20,
  },
  view: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  icon: {
    marginLeft: 4,
    padding: 6,
  },
  timeText: {
    backgroundColor: '#E9EDF1',
    padding: 4,
    borderRadius: 4,
  },
  button: { borderRadius: 100 },
});

export default TimeSlotBooking;
