import { useInfiniteDataSource } from "@appmaker-xyz/core";
import { AxiosResponse } from "axios";
import { MetaObjectsParams } from "../../souces/shopify/metaobject";

export function useMetaObjectListQuery(params: MetaObjectsParams) {
    const dataSource = {
      source: 'shopify',
      methodName: 'metaObjects',
      dataExtractor: (response: AxiosResponse) => response?.data?.data?.metaobjects?.nodes,
      params: params,
    };
    const [queryResponse, dataSourceResult] = useInfiniteDataSource({
      dataSource,
      enabled: true,
      filterParams: {},
    });
    return {
      items: dataSourceResult,
      ...queryResponse,
    }
  }