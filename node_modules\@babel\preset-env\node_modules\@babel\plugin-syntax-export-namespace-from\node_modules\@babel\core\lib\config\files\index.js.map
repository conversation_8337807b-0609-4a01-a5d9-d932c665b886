{"version": 3, "names": ["_package", "require", "_configuration", "plugins", "_gensync", "data", "resolvePlugin", "gens<PERSON>", "sync", "exports", "resolvePreset"], "sources": ["../../../src/config/files/index.ts"], "sourcesContent": ["type indexBrowserType = typeof import(\"./index-browser\");\ntype indexType = typeof import(\"./index\");\n\n// Kind of gross, but essentially asserting that the exports of this module are the same as the\n// exports of index-browser, since this file may be replaced at bundle time with index-browser.\n({} as any as indexBrowserType as indexType);\n\nexport { findPackageData } from \"./package\";\n\nexport {\n  findConfigUpwards,\n  findRelativeConfig,\n  findRootConfig,\n  loadConfig,\n  resolveShowConfigPath,\n  ROOT_CONFIG_FILENAMES,\n} from \"./configuration\";\nexport type {\n  ConfigFile,\n  IgnoreFile,\n  RelativeConfig,\n  FilePackageData,\n} from \"./types\";\nexport { loadPlugin, loadPreset } from \"./plugins\";\n\nimport gensync from \"gensync\";\nimport * as plugins from \"./plugins\";\n\nexport const resolvePlugin = gensync(plugins.resolvePlugin).sync;\nexport const resolvePreset = gensync(plugins.resolvePreset).sync;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,cAAA,GAAAD,OAAA;AAcA,IAAAE,OAAA,GAAAF,OAAA;AAEA,SAAAG,SAAA;EAAA,MAAAC,IAAA,GAAAJ,OAAA;EAAAG,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AApBA,CAAC,CAAC,CAAC;AAuBI,MAAMC,aAAa,GAAGC,UAAO,CAACJ,OAAO,CAACG,aAAa,CAAC,CAACE,IAAI;AAACC,OAAA,CAAAH,aAAA,GAAAA,aAAA;AAC1D,MAAMI,aAAa,GAAGH,UAAO,CAACJ,OAAO,CAACO,aAAa,CAAC,CAACF,IAAI;AAACC,OAAA,CAAAC,aAAA,GAAAA,aAAA;AAAA"}