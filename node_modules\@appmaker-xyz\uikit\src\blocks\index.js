import React, { useState } from 'react';
import { View, Text, ActivityIndicator, Dimensions } from 'react-native';
import {
  ActionButtons,
  ProductImage,
  CounterBlock,
  ProductInfo,
  ProductVariationBlock,
  ProductReview,
  ExpandableTextBlock,
  ZoomableSwiper,
  ShopifyReview,
  ExtraPaymentInfo,
  CustomizeButtons,
} from '@appmaker-xyz/uikit/src/components/pages/productDetail/components';
import {
  Layout,
  AppmakerText,
  ProductGridBlock,
  ProductListBlock,
  SortFilterBlock,
  AnySwiper,
  WooCommerceCart,
  NoticeBanner,
  TextNotice,
  Card,
  Input,
  AppImage,
  TouchableImage,
  AppTouchable,
  Button,
  PostCard,
  PostHeader,
  SearchBar,
  InputBlock,
  NotificationWidget,
  LayoutIcon,
  RadioSelect,
  AddressChooser,
  AddressDisplay,
  SocialLinkBar,
  FloatingButton,
  Onboarding,
  BasicDetails,
  FloatingLabelInput,
  AttrButton,
  PageHead,
  FeatureCard,
  Spacer,
  Divider,
  TextList,
  FormCheckboxInputBlock,
  DateTimePicker,
  RadioSelectInput,
  CartFreeGift,
} from '@appmaker-xyz/uikit';
import { AppmakerRemoteImage } from '@appmaker-xyz/react-native';
import { addFilter } from '@appmaker-xyz/core';
import WebView from './components/WebView';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { styles } from '@appmaker-xyz/uikit';
import Menu from './components/Menu';
import Tabs from './components/Tabs';
import PinCheck from '@appmaker-xyz/uikit/src/components/pages/productDetail/components/blocks/PinCheck';
import SkeletonPlaceholderBlocks from './components/SkeletonPlaceholderBlocks';
import SocialLoginHelper from './components/SocialLoginHelper';
import { BottomSheet } from '@appmaker-xyz/uikit/src/components/organisms/index';
import FormInputBlock from '@appmaker-xyz/uikit/src/components/molecules/formFields/FormInputBlock';
import FormHiddenBlock from '@appmaker-xyz/uikit/src/components/molecules/formFields/FormHiddenBlock';
import FormRadioBlock from '@appmaker-xyz/uikit/src/components/molecules/formFields/FormRadioBlock';
import { ColumContainer, ColumItem } from './components/Column';
import {
  BadgeBlock,
  PhoneInputBlock,
} from '@appmaker-xyz/uikit/src/components/molecules/index';
import TabBlock from './components/TabBlock';
import InAppPage from './components/InAppPage';
import AutoHeightWebViewBlock from './components/WebView/AutoHeightWebView';
import NativeJSONBlock from './components/NativeJSONBlock/index';
import './v2Blocks';
import ToolbarIcon from './components/ToolbarIcon';
import { usePageState } from '@appmaker-xyz/core';
import { appSettings } from '@appmaker-xyz/core';
import { isEmpty, trim } from 'lodash';
import { analytics } from '@appmaker-xyz/core';

const { color, spacing, font, fonts } = styles;

addFilter('appmaker-blocks', 'product-image', (blocks) => {
  // blocks['appmaker/tabs'] = {
  //   View: Tabs,
  // };
  // blocks['appmaker/badge'] = {
  //   View: BadgeBlock,
  // };
  // blocks['appmaker/native-json-block'] = {
  //   View: NativeJSONBlock,
  // };
  // blocks['appmaker/SocialLogin'] = { View: SocialLoginHelper };

  // blocks['appmaker/json'] = {
  //   View: ({ attributes }) => (
  //     <Text>{JSON.stringify(attributes, null, 2)}</Text>
  //   ),
  // };
  // // blocks['appmaker/order-detail-header'] = { View: OrderDetailHeader };
  // // blocks['appmaker/toolbar-icon'] = { View: ToolbarIcon };
  // blocks['appmaker/icon'] = {
  //   View: ({ attributes }) => {
  //     return (
  //       <Icon
  //         name={attributes.name}
  //         size={attributes.size}
  //         color={attributes.color}
  //         style={attributes.style}
  //       />
  //     );
  //   },
  // };

  // blocks['appmaker/checkbox-input'] = {
  //   View: FormCheckboxInputBlock,
  // };
  // blocks['appmaker/form-date-time-picker'] = {
  //   View: DateTimePicker,
  // };
  // blocks['appmaker/input-radio'] = {
  //   View: RadioSelectInput,
  // };

  // blocks['appmaker/form-radio-item'] = {
  //   View: FormRadioBlock,
  // };
  // blocks['core/group'] = {
  //   View: ({ attributes, BlocksView, innerBlocks, ...props }) => {
  //     return (
  //       <BlocksView
  //         inAppPage={{ attributes, blocks: innerBlocks }}
  //         {...props}
  //         blockData={props.data}
  //       />
  //     );
  //   },
  // };
  // blocks['appmaker/order-detail-loading'] = {
  //   View: ({ attributes }) => {
  //     return !attributes.value ? <ActivityIndicator size="large" /> : null;
  //   },
  // };
  // blocks['appmaker/appTouchable'] = {
  //   View: ({ attributes, BlocksView, innerBlocks, onPress }) => (
  //     <AppTouchable onPress={onPress} style={attributes.style}>
  //       <BlocksView template={innerBlocks} />
  //     </AppTouchable>
  //   ),
  // };

  // blocks['appmaker/InitialInAppPage'] = {
  //   View: ({ attributes, onAction }) => {
  //     onAction({
  //       action: 'SAVE_DATA',
  //       params: { key: '@first_open', value: '1' },
  //     });
  //     onAction(attributes.nextAction);
  //     return (
  //       <View>
  //         <AppmakerText>Initial inApp Page</AppmakerText>
  //         <ActivityIndicator />
  //       </View>
  //     );
  //   },
  // };
  // blocks['appmaker/TouchableImage'] = {
  //   View: TouchableImage,
  // };

  // blocks['appmaker/ZoomableImage'] = {
  //   View: ZoomableSwiper,
  // };
  // // blocks['appmaker/languageButton'] = {
  // //   View: ({ attributes, onAction, clientId }) => {
  // //     console.log('language button ', attributes);
  // //     return (
  // //       <Button
  // //       clientId={clientId}
  // //       small={attributes.small}
  // //       baseSize={attributes.baseSize}
  // //       disabled={!!!attributes.selectedLan}
  // //       wholeContainerStyle={attributes.wholeContainerStyle}
  // //       onPress={() => {
  // //         attributes.appmakerAction &&
  // //           onAction &&
  // //           onAction({...attributes.appmakerAction, params: {language: attributes.selectedLan }});
  // //       }}
  // //       link={attributes.link}
  // //       outline={attributes.outline}
  // //       accessoryRight={() => (
  // //         <Icon
  // //           name={attributes.iconName}
  // //           size={attributes.iconSize ? attributes.iconSize : font.size.lg}
  // //           color={attributes.iconColor ? attributes.iconColor : color.white}
  // //         />
  // //       )}
  // //       status={attributes.status}>
  // //       {attributes.content}
  // //     </Button>
  // //     );
  // //   },
  // // };

  // // blocks['appmaker/CurrencySwitcher'] = {
  // //   View: ({ attributes, ...props }) => {
  // //     const { dataSource, appmakerAction } = attributes;
  // //     const {items} = dataSource?.attributes;
  // //     const [selectedCurrency, setSelectedCurrency] = useState(undefined);
  // //     const renderItem = ({item, index}) => {
  // //       const actionParams = {
  // //         values: {
  // //           selectedCurrency: item.code
  // //         }
  // //       }
  // //       const actionCallback =() => {
  // //         setSelectedCurrency(item.code);
  // //       }
  // //       return (<ActionBar {...props}  actionCallback={actionCallback} attributes={{...attributes, title: item.name,
  // //         appmakerAction: ({...appmakerAction,  params: actionParams}), rightIcon: (selectedCurrency == item.code) ? 'check-circle': 'circle',
  // //         featureImg: `https://transferwise.com/public-resources/assets/flags/rectangle/${item.code.toLowerCase()}.png`
  // //     }}/>)
  // //     }

  // //     return (
  // //       <View>
  // //         <FlatList
  // //           data={items}
  // //           renderItem={renderItem}
  // //           keyExtractor={item => item.id}
  // //         />
  // //       </View>
  // //     );
  // //   },
  // // };
  // // blocks['appmaker/currencyButton'] = {
  // //   View: ({ attributes, onAction, clientId }) => {
  // //     return (
  // //       <Button
  // //       clientId={clientId}
  // //       small={attributes.small}
  // //       baseSize={attributes.baseSize}
  // //       disabled={!!!attributes.selectedCurrency}

  // //       wholeContainerStyle={attributes.wholeContainerStyle}
  // //       onPress={() => {
  // //         attributes.appmakerAction &&
  // //           onAction &&
  // //           onAction({...attributes.appmakerAction, params: {currency: attributes.selectedCurrency }});
  // //       }}
  // //       link={attributes.link}
  // //       outline={attributes.outline}
  // //       accessoryRight={() => (
  // //         <Icon
  // //           name={attributes.iconName}
  // //           size={attributes.iconSize ? attributes.iconSize : font.size.lg}
  // //           color={attributes.iconColor ? attributes.iconColor : color.white}
  // //         />
  // //       )}
  // //       status={attributes.status}>
  // //       {attributes.content}
  // //     </Button>
  // //     );
  // //   },
  // // };

  // blocks['appmaker/ImageSwiper'] = { View: AnySwiper };
  // blocks['appmaker/notice-banner'] = { View: NoticeBanner };
  // blocks['appmaker/text-notice'] = { View: TextNotice };

  // blocks['appmaker/download-card'] = {
  //   View: (props) => {
  //     const { data, onAction } = props;
  //     // TODO UI need to do
  //     return (
  //       <View>
  //         <Button
  //           title={'data.download_name'}
  //           onPress={() =>
  //             onAction({
  //               action: 'OPEN_URL',
  //               params: { url: data.download_url },
  //             })
  //           }>
  //           <Text>{data.download_name}</Text>
  //         </Button>
  //       </View>
  //     );
  //   },
  // };
  // blocks['appmaker/sort-filter-block'] = {
  //   View: SortFilterBlock,
  // };
  // blocks['appmaker/blockView'] = {
  //   View: ({
  //     attributes,
  //     pageDispatch,
  //     BlocksView,
  //     pageState,
  //     data,
  //     onAction,
  //   }) => {
  //     return <BlocksView template={attributes.blocks} onAction={onAction} />;
  //   },
  // };
  // blocks['appmaker/product-extra-blocks'] = {
  //   View: ({
  //     attributes,
  //     pageDispatch,
  //     BlocksView,
  //     pageState,
  //     data,
  //     onAction,
  //   }) => {
  //     return (
  //       <BlocksView template={attributes.extra_blocks} onAction={onAction} />
  //     );
  //   },
  // };
  // blocks['appmaker/product-variations-blocks'] = {
  //   View: ({
  //     attributes,
  //     pageDispatch,
  //     BlocksView,
  //     pageState,
  //     data,
  //     onAction,
  //   }) => {
  //     return (
  //       <BlocksView
  //         template={attributes.variations_blocks}
  //         onAction={onAction}
  //       />
  //     );
  //   },
  // };
  // blocks['appmaker/custom-app-list-listener'] = {
  //   View: ({ attributes, pageDispatch, pageState, data }) => {
  //     addFilter('appmaker-actions', 'add-SET_FILTER', (actions) => {
  //       actions.SET_FILTER = (action) => {
  //         pageDispatch({
  //           type: 'set_values',
  //           values: { answer: action.dataSource.attributes },
  //         });
  //       };
  //       return actions;
  //     });
  //     // console.log(pageState?.state);
  //     // console.log(data);
  //     // if(pageState?)
  //     return <Text>Hello : {JSON.stringify(pageState, null, 2)}</Text>;
  //   },
  // };
  // blocks['appmaker/pageState-listener'] = {
  //   View: ({ attributes, pageDispatch, pageState, data }) => {
  //     // console.log(pageState);
  //     // console.log(data);
  //     // if(pageState?)
  //     return null;
  //   },
  // };
  // // blocks['appmaker/checkout-button'] = {
  // //   View: ({ onAction, attributes, pageState, clientId, t }) => {
  // //     const { appmakerAction } = attributes;
  // //     return (
  // //       <CheckoutButton
  // //         clientId={clientId}
  // //         wholeContainerStyle={attributes.wholeContainerStyle}
  // //         fontColor={attributes.fontColor}
  // //         onPress={() => onAction({ ...appmakerAction, t, onAction })}
  // //         {...attributes}
  // //       />
  // //     );
  // //   },
  // // };
  // blocks['appmaker/product-grid-item'] = {
  //   View: ProductGridBlock,
  // };
  // blocks['appmaker/product-list-item'] = {
  //   View: ProductListBlock,
  // };
  // blocks['appmaker/notification'] = {
  //   View: NotificationWidget,
  // };
  // blocks['appmaker/product-list-item-old'] = {
  //   View: ({ attributes, clientId, data, onAction, ...props }) => {
  //     // return ListItem;
  //     const item = data;
  //     const staticTexts = {
  //       outOfStockText: 'Out of stock',
  //     };
  //     let defaultItemProps = {
  //       clientId,
  //       uri: item.thumbnail,
  //       title: item.name,
  //       salePrice: item.on_sale && item.regular_price_display,
  //       regularPrice: item.price_display,
  //       groceryMode: appSettings.getOptionAsBoolean('grocery_mode'),
  //       saved: item.saved,
  //       staticTexts: staticTexts,
  //       onSaved: (status) => {
  //         // status ? saveBookMark(toProduct(item)) : deleteBookMark(toProduct(item));
  //       },
  //       quatity: 1,
  //       outOfStock: item.in_stock ? false : staticTexts.outOfStockText,
  //       attribute: item.labels,
  //       quantityLoading: false,
  //       saleBadge: item.sale_percentage || '',
  //       onPress: () => {
  //         onAction(attributes.appmakerAction);
  //       },
  //       onQuantityChange: async (quantity) => {},
  //     };
  //     return <ProductListBlock {...defaultItemProps} />;
  //     return (
  //       <View>
  //         <Text>{JSON.stringify(attributes, null, 2)}</Text>
  //       </View>
  //     );
  //   },
  // };
  // blocks['appmaker/product-info'] = {
  //   View: ProductInfo,
  // };
  // blocks['appmaker/expandable-text-block'] = {
  //   View: ExpandableTextBlock,
  // };
  // blocks['appmaker/product-review'] = {
  //   View: ProductReview,
  // };
  // blocks['appmaker/full_loading_indicator'] = {
  //   View: ({ attributes }) => {
  //     return !attributes.loaded ? <Layout loading={true} /> : null;
  //   },
  // };
  // blocks['appmaker/product-variation'] = {
  //   View: ProductVariationBlock,
  // };

  // blocks['appmaker/product-image'] = {
  //   View: ProductImage,
  // };
  // blocks['appmaker/post-header'] = {
  //   View: PostHeader,
  // };
  // blocks['appmaker/post-item'] = {
  //   View: PostCard,
  // };
  // blocks['appmaker/radio'] = {
  //   View: RadioSelect,
  // };
  // blocks['appmaker/payment-details'] = {
  //   View: ExtraPaymentInfo,
  // };
  // blocks['appmaker/bottom-sheet'] = {
  //   View: BottomSheet,
  // };

  // blocks['appmaker/customize-buttons'] = {
  //   View: CustomizeButtons,
  // };

  return blocks;
});
