import React from 'react';
import {StyleSheet} from 'react-native';
import {Layout, AppmakerText, Button} from '../../../../components';
import {useApThemeState} from '../../../../theme/ThemeContext';
import {SocialLogin} from './index';

const ToSignIn = (props) => {
  const {loginAction, createAccountAction, showSocialLogin = true} = props;
  const {color, spacing} = useApThemeState();
  const styles = allStyles({color, spacing});
  return (
    <Layout style={styles.container}>
      <AppmakerText category="bodyParagraph">
        Sign In to your account to get personalised features
      </AppmakerText>
      <Layout style={styles.row}>
        <Layout style={styles.column}>
          <Button status="dark" outline onPress={createAccountAction}>
            Create Account
          </Button>
        </Layout>
        <Layout style={styles.column}>
          <Button status="dark" onPress={loginAction}>
            Login
          </Button>
        </Layout>
      </Layout>
      {showSocialLogin && (
        <Layout style={styles.socialLogins}>
          <SocialLogin google={true} apple={true} facebook={true} />
        </Layout>
      )}
    </Layout>
  );
};

const allStyles = ({spacing, color}) =>
  StyleSheet.create({
    container: {
      alignItems: 'center',
      backgroundColor: color.white,
    },
    row: {
      flexDirection: 'row',
      width: '100%',
      marginTop: spacing.md,
    },
    column: {
      width: '50%',
      paddingHorizontal: spacing.nano,
    },
    socialLogins: {
      alignItems: 'center',
      justifyContent: 'space-between',
    },
  });

export default ToSignIn;
