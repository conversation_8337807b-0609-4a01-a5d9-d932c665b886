import React from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '../../../../theme/ThemeContext';
import { Layout, Button, AppmakerText, AppImage } from '../../../../components';

const SocialLogin = ({
  google,
  googleOnPress,
  facebook,
  facebookOnPress,
  apple,
  appleOnPress,
  loading = false,
  __appmakerCustomStyles = {},
}) => {
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });
  return (
    <Layout style={styles.socialLogin}>
      <Layout style={styles.text}>
        {/* <AppmakerText category="h1SubHeading" status="grey">
          OR
        </AppmakerText> */}
        {/* commented because two OR's coming if individual blocks */}
      </Layout>
      <Layout style={styles.socialIconContainer}>
        {google && (
          <Button
            status="light"
            loading={loading}
            outline
            style={styles.buttonInner}
            onPress={googleOnPress}
            baseSize={true}
            __appmakerCustomStyles={__appmakerCustomStyles?.googleLoginButton}>
            <AppImage
              source={require('./socialIcons/google-icon.png')}
              style={styles.socialIcon}
            />
            {google && !apple && !facebook && (
              <AppmakerText
                category="actionTitle"
                style={styles.googleLoginText}>
                Sign in with Google
              </AppmakerText>
            )}
          </Button>
        )}
        {apple && (
          <Button
            status="light"
            outline
            loading={loading}
            style={styles.buttonInner}
            onPress={appleOnPress}
            baseSize={true}
            __appmakerCustomStyles={__appmakerCustomStyles?.appleLoginButton}>
            <AppImage
              source={require('./socialIcons/apple-icon.png')}
              style={styles.socialIcon}
            />
            {!google && apple && !facebook && (
              <AppmakerText
                category="actionTitle"
                style={styles.googleLoginText}>
                Sign in with Apple
              </AppmakerText>
            )}
          </Button>
        )}
        {facebook && (
          <Button
            status="light"
            outline
            loading={loading}
            style={styles.buttonInner}
            onPress={facebookOnPress}
            baseSize={true}
            __appmakerCustomStyles={
              __appmakerCustomStyles?.facebookLoginButton
            }>
            <AppImage
              source={require('./socialIcons/facebook-icon.png')}
              style={styles.socialIcon}
            />
            {!google && !apple && facebook && (
              <AppmakerText
                category="actionTitle"
                style={styles.googleLoginText}>
                Login with Facebook
              </AppmakerText>
            )}
          </Button>
        )}
      </Layout>
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    socialLogin: {
      alignItems: 'center',
    },
    text: {
      marginVertical: spacing.base,
    },
    socialIconContainer: {
      flexDirection: 'row',
      width: 200,
      justifyContent: 'space-around',
    },
    socialIcon: {
      width: 20,
      height: 20,
    },
    buttonInner: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    googleLoginText: {
      marginLeft: spacing.mini,
    },
  });

export default SocialLogin;
