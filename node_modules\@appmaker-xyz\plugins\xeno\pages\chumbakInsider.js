import { color, spacing } from '../../../shopify/pages/styles';

const ChumbakInsider = {
  type: 'normal',
  title: 'Chumbak insider',
  attributes: {
    showLoadingTillData: true,
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    // {
    //   name: 'appmaker/chumbak-insider-button',
    //   attributes: {
    //     type: 'cart_button',
    //   },
    // },
    {
      name: 'appmaker/chumbak-insider',
      attributes: {
        data: '{{blockData}}',
        // type: 'cart_popup',
      },
    },
    {
      name: 'appmaker/chumbak-insider-footer',
      attributes: {
        data: '{{blockData}}',
        // type: 'cart_popup',
      },
    },
    // {
    //   name: 'appmaker/appImage',
    //   attributes: {
    //     uri: 'https://cdn.shopify.com/s/files/1/0601/1093/0098/files/popup_logo-removebg-preview_1024x1024.png',
    //     style: {
    //       width: 120,
    //       height: 80,
    //       resizeMode: 'contain',
    //       marginBottom: spacing.xl,
    //       alignSelf: 'center',
    //     },
    //   },
    // },
    // {
    //   name: 'appmaker/layout',
    //   attributes: {
    //     style: {
    //       flexDirection: 'row',
    //       alignItems: 'center',
    //       backgroundColor: color.white,
    //       alignSelf: 'flex-start',
    //       paddingHorizontal: spacing.mini,
    //       marginBottom: spacing.base,
    //       marginLeft: spacing.base,
    //     },
    //   },
    //   innerBlocks: [
    //     {
    //       name: 'appmaker/appImage',
    //       attributes: {
    //         uri: 'https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png',
    //         style: {
    //           width: 70,
    //           height: 30,
    //           resizeMode: 'contain',
    //         },
    //       },
    //     },
    //     {
    //       name: 'appmaker/text',
    //       attributes: {
    //         content: 'BENEFITS',
    //       },
    //     },
    //   ],
    // },
    // {
    //   name: 'appmaker/layout',
    //   attributes: {
    //     style: {
    //       paddingHorizontal: spacing.base,
    //     },
    //   },
    //   innerBlocks: [
    //     {
    //       name: 'appmaker/text',
    //       attributes: {
    //         content: 'Free Priority Shipping',
    //         category: 'bodyParagraphBold',
    //       },
    //     },
    //     {
    //       name: 'appmaker/text',
    //       attributes: {
    //         content:
    //           'Enjoy priority shipping, free of any additional cost on every order',
    //         category: 'bodySubText',
    //         style: {
    //           marginBottom: spacing.base,
    //         },
    //       },
    //     },
    //     {
    //       name: 'appmaker/text',
    //       attributes: {
    //         content: 'Additional Discounts',
    //         category: 'bodyParagraphBold',
    //       },
    //     },
    //     {
    //       name: 'appmaker/text',
    //       attributes: {
    //         content:
    //           'Enjoy a guaranteed 10% discount on all future purchases (over & above; no conditions)',
    //         category: 'bodySubText',
    //         style: {
    //           marginBottom: spacing.base,
    //         },
    //       },
    //     },
    //     {
    //       name: 'appmaker/text',
    //       attributes: {
    //         content: 'Exclusive Access',
    //         category: 'bodyParagraphBold',
    //       },
    //     },
    //     {
    //       name: 'appmaker/text',
    //       attributes: {
    //         content:
    //           'Get special access to preview all upcoming sales and events.',
    //         category: 'bodySubText',
    //         style: {
    //           marginBottom: spacing.base,
    //         },
    //       },
    //     },
    //     {
    //       name: 'appmaker/text',
    //       attributes: {
    //         content: 'Gifts & More',
    //         category: 'bodyParagraphBold',
    //       },
    //     },
    //     {
    //       name: 'appmaker/text',
    //       attributes: {
    //         content: 'Get your Chumbak free gift on every 3rd purchase.',
    //         category: 'bodySubText',
    //         style: {
    //           marginBottom: spacing.base,
    //         },
    //       },
    //     },
    //   ],
    // },
    // {
    //   name: 'appmaker/layout',
    //   attributes: {
    //     style: {
    //       backgroundColor: color.white,
    //       paddingHorizontal: spacing.base,
    //       flex: 1,
    //       justifyContent: 'center',
    //     },
    //   },
    //   innerBlocks: [
    //     {
    //       name: 'appmaker/layout',
    //       attributes: {
    //         containerStyle: {
    //           flexDirection: 'row',
    //           backgroundColor: 'red',
    //         },
    //         style: {
    //           flexDirection: 'row',
    //           alignItems: 'center',
    //           backgroundColor: '#B6DCC9',
    //           alignSelf: 'flex-start',
    //           paddingHorizontal: spacing.mini,
    //           marginBottom: spacing.base,
    //         },
    //       },
    //       innerBlocks: [
    //         {
    //           name: 'appmaker/text',
    //           attributes: {
    //             content: 'BECOME AN',
    //           },
    //         },
    //         {
    //           name: 'appmaker/appImage',
    //           attributes: {
    //             uri: 'https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png',
    //             style: {
    //               width: 70,
    //               height: 30,
    //               resizeMode: 'contain',
    //             },
    //           },
    //         },
    //         {
    //           name: 'appmaker/text',
    //           attributes: {
    //             content: 'MEMBER NOW',
    //           },
    //         },
    //       ],
    //     },
    //   ],
    // },
  ],
  dataSource: {
    source: 'shopify',
    responseType: 'replace',
    attributes: {
      mapping: {
        items: 'data.data.products.edges',
      },
      methodName: 'searchProduct',
      params: 'product_type:membership',
    },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
    dependencies: {
      pageState: ['searchKey'],
    },
  },
};

export default ChumbakInsider;
