{"version": 3, "names": ["_types", "require", "_identifier", "_node", "_scopeflags", "_parseError", "getOwn", "object", "key", "Object", "hasOwnProperty", "call", "unwrapParenthesizedExpression", "node", "type", "expression", "ParseBindingListFlags", "ALLOW_EMPTY", "IS_FUNCTION_PARAMS", "IS_CONSTRUCTOR_PARAMS", "exports", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NodeUtils", "toAssignable", "isLHS", "_node$extra", "_node$extra3", "parenthesized", "undefined", "extra", "expressionScope", "recordArrowParameterBindingError", "Errors", "InvalidParenthesizedAssignment", "at", "raise", "i", "length", "properties", "last", "_node$extra2", "prop", "isLast", "toAssignableObjectExpressionProp", "trailingCommaLoc", "RestTrailingComma", "value", "isPrivateName", "classScope", "usePrivateName", "getPrivateNameSV", "loc", "start", "Error", "toAssignableList", "elements", "operator", "MissingEqInAssignment", "left", "end", "kind", "PatternHasAccessor", "PatternHasMethod", "arg", "argument", "checkToRestConversion", "exprList", "elt", "isAssignable", "isBinding", "every", "element", "toReferencedList", "isParenthesizedExpr", "toReferencedListDeep", "expr", "parseSpread", "refExpressionErrors", "startNode", "next", "parseMaybeAssignAllowIn", "finishNode", "parseRestBinding", "parseBindingAtom", "state", "parseBindingList", "parseObjectLike", "parseIdentifier", "close", "closeCharCode", "flags", "allowEmpty", "elts", "first", "eat", "expect", "match", "push", "parseAssignableListItemTypes", "checkCommaAfterRest", "decorators", "hasPlugin", "UnsupportedParameterDecorator", "startLoc", "parseDecorator", "parseAssignableListItem", "parseBindingRestProperty", "parseBindingProperty", "expectPlugin", "parsePrivateName", "parsePropertyName", "method", "parseObjPropValue", "parseMaybeDefault", "param", "_startLoc", "_left", "startNodeAt", "right", "isValidLVal", "isUnparenthesizedInAssign", "binding", "AssignmentPattern", "RestElement", "ObjectProperty", "ParenthesizedExpression", "ArrayPattern", "ObjectPattern", "checkLVal", "in", "ancestor", "BIND_NONE", "checkClashes", "strictModeChanged", "hasParenthesizedAncestor", "_expression$extra", "isObjectMethod", "InvalidPropertyBindingPattern", "checkIdentifier", "name", "has", "ParamDupe", "add", "validity", "ParseErrorClass", "InvalidLhs", "InvalidLhsBinding", "isParenthesizedExpression", "Array", "isArray", "nextAncestor", "child", "concat", "bindingType", "strict", "isStrictBindReservedWord", "inModule", "isStrictBindOnlyReservedWord", "StrictEvalArguments", "referenceName", "StrictEvalArgumentsBinding", "bindingName", "BIND_FLAGS_NO_LET_IN_LEXICAL", "LetInLexicalBinding", "declareNameFromIdentifier", "identifier", "scope", "declareName", "allowPattern", "InvalidRestAssignmentPattern", "lookaheadCharCode", "ElementAfterRest", "default"], "sources": ["../../src/parser/lval.ts"], "sourcesContent": ["import * as charCodes from \"charcodes\";\nimport { tt, type TokenType } from \"../tokenizer/types\";\nimport type {\n  AssignmentPattern,\n  TSParameterProperty,\n  Decorator,\n  Expression,\n  Identifier,\n  Node,\n  Pattern,\n  RestElement,\n  SpreadElement,\n  ObjectOrClassMember,\n  ClassMember,\n  ObjectMember,\n  TsNamedTypeElementBase,\n  PrivateName,\n  ObjectExpression,\n  ObjectPattern,\n  ArrayExpression,\n  ArrayPattern,\n} from \"../types\";\nimport type { Pos, Position } from \"../util/location\";\nimport {\n  isStrictBindOnlyReservedWord,\n  isStrictBindReservedWord,\n} from \"../util/identifier\";\nimport { NodeUtils, type Undone } from \"./node\";\nimport {\n  type BindingTypes,\n  BIND_NONE,\n  BIND_FLAGS_NO_LET_IN_LEXICAL,\n} from \"../util/scopeflags\";\nimport type { ExpressionErrors } from \"./util\";\nimport { Errors, type LValAncestor } from \"../parse-error\";\nimport type Parser from \"./index\";\n\nconst getOwn = <T extends {}>(object: T, key: keyof T) =>\n  Object.hasOwnProperty.call(object, key) && object[key];\n\nconst unwrapParenthesizedExpression = (node: Node): Node => {\n  return node.type === \"ParenthesizedExpression\"\n    ? unwrapParenthesizedExpression(node.expression)\n    : node;\n};\n\nexport const enum ParseBindingListFlags {\n  ALLOW_EMPTY = 1 << 0,\n  IS_FUNCTION_PARAMS = 1 << 1,\n  IS_CONSTRUCTOR_PARAMS = 1 << 2,\n}\n\nexport default abstract class LValParser extends NodeUtils {\n  // Forward-declaration: defined in expression.js\n  abstract parseIdentifier(liberal?: boolean): Identifier;\n  abstract parseMaybeAssign(\n    refExpressionErrors?: ExpressionErrors | null,\n    afterLeftParse?: Function,\n    refNeedsArrowPos?: Pos | null,\n  ): Expression;\n\n  abstract parseMaybeAssignAllowIn(\n    refExpressionErrors?: ExpressionErrors | null,\n    afterLeftParse?: Function,\n    refNeedsArrowPos?: Pos | null,\n  ): Expression;\n\n  abstract parseObjectLike<T extends ObjectPattern | ObjectExpression>(\n    close: TokenType,\n    isPattern: boolean,\n    isRecord?: boolean,\n    refExpressionErrors?: ExpressionErrors,\n  ): T;\n  abstract parseObjPropValue(\n    prop: any,\n    startLoc: Position | null,\n    isGenerator: boolean,\n    isAsync: boolean,\n    isPattern: boolean,\n    isAccessor: boolean,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): void;\n  abstract parsePropertyName(\n    prop: ObjectOrClassMember | ClassMember | TsNamedTypeElementBase,\n  ): Expression | Identifier;\n  abstract parsePrivateName(): PrivateName;\n  // Forward-declaration: defined in statement.js\n  abstract parseDecorator(): Decorator;\n\n  /**\n   * Convert existing expression atom to assignable pattern\n   * if possible. Also checks invalid destructuring targets:\n   *\n   * - Parenthesized Destructuring patterns\n   * - RestElement is not the last element\n   * - Missing `=` in assignment pattern\n   *\n   * NOTE: There is a corresponding \"isAssignable\" method.\n   * When this one is updated, please check if also that one needs to be updated.\n   *\n   * @param node The expression atom\n   * @param isLHS Whether we are parsing a LeftHandSideExpression.\n   *              If isLHS is `true`, the following cases are allowed: `[(a)] = [0]`, `[(a.b)] = [0]`\n   *              If isLHS is `false`, we are in an arrow function parameters list.\n   */\n  toAssignable(node: Node, isLHS: boolean = false): void {\n    let parenthesized = undefined;\n    if (node.type === \"ParenthesizedExpression\" || node.extra?.parenthesized) {\n      parenthesized = unwrapParenthesizedExpression(node);\n      if (isLHS) {\n        // an LHS can be reinterpreted to a binding pattern but not vice versa.\n        // therefore a parenthesized identifier is ambiguous until we are sure it is an assignment expression\n        // i.e. `([(a) = []] = []) => {}`\n        // see also `recordArrowParameterBindingError` signature in packages/babel-parser/src/util/expression-scope.js\n        if (parenthesized.type === \"Identifier\") {\n          this.expressionScope.recordArrowParameterBindingError(\n            Errors.InvalidParenthesizedAssignment,\n            { at: node },\n          );\n        } else if (parenthesized.type !== \"MemberExpression\") {\n          // A parenthesized member expression can be in LHS but not in pattern.\n          // If the LHS is later interpreted as a pattern, `checkLVal` will throw for member expression binding\n          // i.e. `([(a.b) = []] = []) => {}`\n          this.raise(Errors.InvalidParenthesizedAssignment, { at: node });\n        }\n      } else {\n        this.raise(Errors.InvalidParenthesizedAssignment, { at: node });\n      }\n    }\n\n    switch (node.type) {\n      case \"Identifier\":\n      case \"ObjectPattern\":\n      case \"ArrayPattern\":\n      case \"AssignmentPattern\":\n      case \"RestElement\":\n        break;\n\n      case \"ObjectExpression\":\n        node.type = \"ObjectPattern\";\n        for (\n          let i = 0, length = node.properties.length, last = length - 1;\n          i < length;\n          i++\n        ) {\n          const prop = node.properties[i];\n          const isLast = i === last;\n          this.toAssignableObjectExpressionProp(prop, isLast, isLHS);\n\n          if (\n            isLast &&\n            prop.type === \"RestElement\" &&\n            node.extra?.trailingCommaLoc\n          ) {\n            this.raise(Errors.RestTrailingComma, {\n              at: node.extra.trailingCommaLoc,\n            });\n          }\n        }\n        break;\n\n      case \"ObjectProperty\": {\n        const { key, value } = node;\n        if (this.isPrivateName(key)) {\n          this.classScope.usePrivateName(\n            this.getPrivateNameSV(key),\n            key.loc.start,\n          );\n        }\n        this.toAssignable(value, isLHS);\n        break;\n      }\n\n      case \"SpreadElement\": {\n        throw new Error(\n          \"Internal @babel/parser error (this is a bug, please report it).\" +\n            \" SpreadElement should be converted by .toAssignable's caller.\",\n        );\n      }\n\n      case \"ArrayExpression\":\n        node.type = \"ArrayPattern\";\n        this.toAssignableList(\n          node.elements,\n          node.extra?.trailingCommaLoc,\n          isLHS,\n        );\n        break;\n\n      case \"AssignmentExpression\":\n        if (node.operator !== \"=\") {\n          this.raise(Errors.MissingEqInAssignment, { at: node.left.loc.end });\n        }\n\n        node.type = \"AssignmentPattern\";\n        delete node.operator;\n        this.toAssignable(node.left, isLHS);\n        break;\n\n      case \"ParenthesizedExpression\":\n        /*::invariant (parenthesized !== undefined) */\n        this.toAssignable(parenthesized, isLHS);\n        break;\n\n      default:\n      // We don't know how to deal with this node. It will\n      // be reported by a later call to checkLVal\n    }\n  }\n\n  toAssignableObjectExpressionProp(\n    prop: Node,\n    isLast: boolean,\n    isLHS: boolean,\n  ) {\n    if (prop.type === \"ObjectMethod\") {\n      this.raise(\n        prop.kind === \"get\" || prop.kind === \"set\"\n          ? Errors.PatternHasAccessor\n          : Errors.PatternHasMethod,\n        { at: prop.key },\n      );\n    } else if (prop.type === \"SpreadElement\") {\n      prop.type = \"RestElement\";\n      const arg = prop.argument;\n      this.checkToRestConversion(arg, /* allowPattern */ false);\n      this.toAssignable(arg, isLHS);\n\n      if (!isLast) {\n        this.raise(Errors.RestTrailingComma, { at: prop });\n      }\n    } else {\n      this.toAssignable(prop, isLHS);\n    }\n  }\n\n  // Convert list of expression atoms to binding list.\n\n  toAssignableList(\n    exprList: Expression[],\n    trailingCommaLoc: Position | undefined | null,\n    isLHS: boolean,\n  ): void {\n    const end = exprList.length - 1;\n\n    for (let i = 0; i <= end; i++) {\n      const elt = exprList[i];\n      if (!elt) continue;\n\n      if (elt.type === \"SpreadElement\") {\n        elt.type = \"RestElement\";\n        const arg = elt.argument;\n        this.checkToRestConversion(arg, /* allowPattern */ true);\n        this.toAssignable(arg, isLHS);\n      } else {\n        this.toAssignable(elt, isLHS);\n      }\n\n      if (elt.type === \"RestElement\") {\n        if (i < end) {\n          this.raise(Errors.RestTrailingComma, { at: elt });\n        } else if (trailingCommaLoc) {\n          this.raise(Errors.RestTrailingComma, { at: trailingCommaLoc });\n        }\n      }\n    }\n  }\n\n  isAssignable(node: Node, isBinding?: boolean): boolean {\n    switch (node.type) {\n      case \"Identifier\":\n      case \"ObjectPattern\":\n      case \"ArrayPattern\":\n      case \"AssignmentPattern\":\n      case \"RestElement\":\n        return true;\n\n      case \"ObjectExpression\": {\n        const last = node.properties.length - 1;\n        return (node.properties as ObjectExpression[\"properties\"]).every(\n          (prop, i) => {\n            return (\n              prop.type !== \"ObjectMethod\" &&\n              (i === last || prop.type !== \"SpreadElement\") &&\n              this.isAssignable(prop)\n            );\n          },\n        );\n      }\n\n      case \"ObjectProperty\":\n        return this.isAssignable(node.value);\n\n      case \"SpreadElement\":\n        return this.isAssignable(node.argument);\n\n      case \"ArrayExpression\":\n        return (node as ArrayExpression).elements.every(\n          element => element === null || this.isAssignable(element),\n        );\n\n      case \"AssignmentExpression\":\n        return node.operator === \"=\";\n\n      case \"ParenthesizedExpression\":\n        return this.isAssignable(node.expression);\n\n      case \"MemberExpression\":\n      case \"OptionalMemberExpression\":\n        return !isBinding;\n\n      default:\n        return false;\n    }\n  }\n\n  // Convert list of expression atoms to a list of\n\n  toReferencedList(\n    exprList: ReadonlyArray<Expression | undefined | null>,\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    isParenthesizedExpr?: boolean,\n  ): ReadonlyArray<Expression | undefined | null> {\n    return exprList;\n  }\n\n  toReferencedListDeep(\n    exprList: ReadonlyArray<Expression | undefined | null>,\n    isParenthesizedExpr?: boolean,\n  ): void {\n    this.toReferencedList(exprList, isParenthesizedExpr);\n\n    for (const expr of exprList) {\n      if (expr?.type === \"ArrayExpression\") {\n        this.toReferencedListDeep(expr.elements);\n      }\n    }\n  }\n\n  // Parses spread element.\n\n  parseSpread(\n    this: Parser,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): SpreadElement {\n    const node = this.startNode<SpreadElement>();\n    this.next();\n    node.argument = this.parseMaybeAssignAllowIn(\n      refExpressionErrors,\n      undefined,\n    );\n    return this.finishNode(node, \"SpreadElement\");\n  }\n\n  // https://tc39.es/ecma262/#prod-BindingRestProperty\n  // https://tc39.es/ecma262/#prod-BindingRestElement\n  parseRestBinding(this: Parser): RestElement {\n    const node = this.startNode<RestElement>();\n    this.next(); // eat `...`\n    node.argument = this.parseBindingAtom();\n    return this.finishNode(node, \"RestElement\");\n  }\n\n  // Parses lvalue (assignable) atom.\n  parseBindingAtom(this: Parser): Pattern {\n    // https://tc39.es/ecma262/#prod-BindingPattern\n    switch (this.state.type) {\n      case tt.bracketL: {\n        const node = this.startNode<ArrayPattern>();\n        this.next();\n        // @ts-expect-error: Fixme: TSParameterProperty can not be assigned to node.elements\n        node.elements = this.parseBindingList(\n          tt.bracketR,\n          charCodes.rightSquareBracket,\n          ParseBindingListFlags.ALLOW_EMPTY,\n        );\n        return this.finishNode(node, \"ArrayPattern\");\n      }\n\n      case tt.braceL:\n        return this.parseObjectLike(tt.braceR, true);\n    }\n\n    // https://tc39.es/ecma262/#prod-BindingIdentifier\n    return this.parseIdentifier();\n  }\n\n  // https://tc39.es/ecma262/#prod-BindingElementList\n  parseBindingList(\n    this: Parser,\n    close: TokenType,\n    closeCharCode: (typeof charCodes)[keyof typeof charCodes],\n    flags: ParseBindingListFlags,\n  ): Array<Pattern | TSParameterProperty> {\n    const allowEmpty = flags & ParseBindingListFlags.ALLOW_EMPTY;\n\n    const elts: Array<Pattern | TSParameterProperty> = [];\n    let first = true;\n    while (!this.eat(close)) {\n      if (first) {\n        first = false;\n      } else {\n        this.expect(tt.comma);\n      }\n      if (allowEmpty && this.match(tt.comma)) {\n        elts.push(null);\n      } else if (this.eat(close)) {\n        break;\n      } else if (this.match(tt.ellipsis)) {\n        elts.push(\n          this.parseAssignableListItemTypes(this.parseRestBinding(), flags),\n        );\n        if (!this.checkCommaAfterRest(closeCharCode)) {\n          this.expect(close);\n          break;\n        }\n      } else {\n        const decorators = [];\n        if (this.match(tt.at) && this.hasPlugin(\"decorators\")) {\n          this.raise(Errors.UnsupportedParameterDecorator, {\n            at: this.state.startLoc,\n          });\n        }\n        // invariant: hasPlugin(\"decorators-legacy\")\n        while (this.match(tt.at)) {\n          decorators.push(this.parseDecorator());\n        }\n        elts.push(this.parseAssignableListItem(flags, decorators));\n      }\n    }\n    return elts;\n  }\n\n  // https://tc39.es/ecma262/#prod-BindingRestProperty\n  parseBindingRestProperty(\n    this: Parser,\n    prop: Undone<RestElement>,\n  ): RestElement {\n    this.next(); // eat '...'\n    // Don't use parseRestBinding() as we only allow Identifier here.\n    prop.argument = this.parseIdentifier();\n    this.checkCommaAfterRest(charCodes.rightCurlyBrace);\n    return this.finishNode(prop, \"RestElement\");\n  }\n\n  // https://tc39.es/ecma262/#prod-BindingProperty\n  parseBindingProperty(this: Parser): ObjectMember | RestElement {\n    const prop = this.startNode<ObjectMember | RestElement>();\n    const { type, startLoc } = this.state;\n    if (type === tt.ellipsis) {\n      return this.parseBindingRestProperty(prop as Undone<RestElement>);\n    } else if (type === tt.privateName) {\n      this.expectPlugin(\"destructuringPrivate\", startLoc);\n      this.classScope.usePrivateName(this.state.value, startLoc);\n      (prop as Undone<ObjectMember>).key = this.parsePrivateName();\n    } else {\n      this.parsePropertyName(prop as Undone<ObjectMember>);\n    }\n    (prop as Undone<ObjectMember>).method = false;\n    return this.parseObjPropValue(\n      prop as Undone<ObjectMember>,\n      startLoc,\n      false /* isGenerator */,\n      false /* isAsync */,\n      true /* isPattern */,\n      false /* isAccessor */,\n    );\n  }\n\n  parseAssignableListItem(\n    this: Parser,\n    flags: ParseBindingListFlags,\n    decorators: Decorator[],\n  ): Pattern | TSParameterProperty {\n    const left = this.parseMaybeDefault();\n    this.parseAssignableListItemTypes(left, flags);\n    const elt = this.parseMaybeDefault(left.loc.start, left);\n    if (decorators.length) {\n      left.decorators = decorators;\n    }\n    return elt;\n  }\n\n  // Used by flow/typescript plugin to add type annotations to binding elements\n  parseAssignableListItemTypes(\n    param: Pattern,\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    flags: ParseBindingListFlags,\n  ): Pattern {\n    return param;\n  }\n\n  // Parses assignment pattern around given atom if possible.\n  // https://tc39.es/ecma262/#prod-BindingElement\n  parseMaybeDefault(\n    this: Parser,\n    startLoc?: Position | null,\n    left?: Pattern | null,\n  ): Pattern {\n    startLoc ??= this.state.startLoc;\n    left = left ?? this.parseBindingAtom();\n    if (!this.eat(tt.eq)) return left;\n\n    const node = this.startNodeAt<AssignmentPattern>(startLoc);\n    node.left = left;\n    node.right = this.parseMaybeAssignAllowIn();\n    return this.finishNode(node, \"AssignmentPattern\");\n  }\n  /**\n   * Return information use in determining whether a Node of a given type is an LVal,\n   * possibly given certain additional context information.\n   *\n   * Subclasser notes: This method has kind of a lot of mixed, but related,\n   * responsibilities. If we can definitively determine with the information\n   * provided that this either *is* or *isn't* a valid `LVal`, then the return\n   * value is easy: just return `true` or `false`. However, if it is a valid\n   * LVal *ancestor*, and thus its descendants must be subsequently visited to\n   * continue the \"investigation\", then this method should return the relevant\n   * child key as a `string`. In some special cases, you additionally want to\n   * convey that this node should be treated as if it were parenthesized. In\n   * that case, a tuple of [key: string, parenthesized: boolean] is returned.\n   * The `string`-only return option is actually just a shorthand for:\n   * `[key: string, parenthesized: false]`.\n   *\n   * @param type A Node `type` string\n   * @param isUnparenthesizedInAssign\n   *        Whether the node in question is unparenthesized and its parent\n   *        is either an assignment pattern or an assignment expression.\n   * @param binding\n   *        The binding operation that is being considered for this potential\n   *        LVal.\n   * @returns `true` or `false` if we can immediately determine whether the node\n   *          type in question can be treated as an `LVal`.\n   *          A `string` key to traverse if we must check this child.\n   *          A `[string, boolean]` tuple if we need to check this child and\n   *          treat is as parenthesized.\n   */\n  isValidLVal(\n    type: string,\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    isUnparenthesizedInAssign: boolean,\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    binding: BindingTypes,\n  ): string | boolean {\n    return getOwn(\n      {\n        AssignmentPattern: \"left\",\n        RestElement: \"argument\",\n        ObjectProperty: \"value\",\n        ParenthesizedExpression: \"expression\",\n        ArrayPattern: \"elements\",\n        ObjectPattern: \"properties\",\n      },\n      // @ts-expect-error refine string to enum\n      type,\n    );\n  }\n\n  /**\n   * Verify that a target expression is an lval (something that can be assigned to).\n   *\n   * @param expression The expression in question to check.\n   * @param options A set of options described below.\n   * @param options.in\n   *        The relevant ancestor to provide context information for the error\n   *        if the check fails.\n   * @param options.binding\n   *        The desired binding type. If the given expression is an identifier\n   *        and `binding` is not `BIND_NONE`, `checkLVal` will register binding\n   *        to the parser scope See also `src/util/scopeflags.js`\n   * @param options.checkClashes\n   *        An optional string set to check if an identifier name is included.\n   *        `checkLVal` will add checked identifier name to `checkClashes` It is\n   *        used in tracking duplicates in function parameter lists. If it is\n   *        false, `checkLVal` will skip duplicate checks\n   * @param options.strictModeChanged\n   *        Whether an identifier has been parsed in a sloppy context but should\n   *        be reinterpreted as strict-mode. e.g. `(arguments) => { \"use strict \"}`\n   * @param options.hasParenthesizedAncestor\n   *        This is only used internally during recursive calls, and you should\n   *        not have to set it yourself.\n   */\n\n  checkLVal(\n    expression: Expression | ObjectMember | RestElement,\n    {\n      in: ancestor,\n      binding = BIND_NONE,\n      checkClashes = false,\n      strictModeChanged = false,\n      hasParenthesizedAncestor = false,\n    }: {\n      in: LValAncestor;\n      binding?: BindingTypes;\n      checkClashes?: Set<string> | false;\n      strictModeChanged?: boolean;\n      hasParenthesizedAncestor?: boolean;\n    },\n  ): void {\n    const type = expression.type;\n\n    // If we find here an ObjectMethod, it's because this was originally\n    // an ObjectExpression which has then been converted.\n    // toAssignable already reported this error with a nicer message.\n    if (this.isObjectMethod(expression)) return;\n\n    if (type === \"MemberExpression\") {\n      if (binding !== BIND_NONE) {\n        this.raise(Errors.InvalidPropertyBindingPattern, { at: expression });\n      }\n      return;\n    }\n\n    if (type === \"Identifier\") {\n      this.checkIdentifier(\n        expression as Identifier,\n        binding,\n        strictModeChanged,\n      );\n\n      const { name } = expression as Identifier;\n\n      if (checkClashes) {\n        if (checkClashes.has(name)) {\n          this.raise(Errors.ParamDupe, { at: expression });\n        } else {\n          checkClashes.add(name);\n        }\n      }\n\n      return;\n    }\n\n    const validity = this.isValidLVal(\n      type,\n      !(hasParenthesizedAncestor || expression.extra?.parenthesized) &&\n        ancestor.type === \"AssignmentExpression\",\n      binding,\n    );\n\n    if (validity === true) return;\n    if (validity === false) {\n      const ParseErrorClass =\n        binding === BIND_NONE ? Errors.InvalidLhs : Errors.InvalidLhsBinding;\n\n      this.raise(ParseErrorClass, { at: expression, ancestor });\n      return;\n    }\n\n    const [key, isParenthesizedExpression] = Array.isArray(validity)\n      ? validity\n      : [validity, type === \"ParenthesizedExpression\"];\n    const nextAncestor =\n      type === \"ArrayPattern\" ||\n      type === \"ObjectPattern\" ||\n      type === \"ParenthesizedExpression\"\n        ? ({ type } as const)\n        : ancestor;\n\n    // @ts-expect-error key may not index expression.\n    for (const child of [].concat(expression[key])) {\n      if (child) {\n        this.checkLVal(child, {\n          in: nextAncestor,\n          binding,\n          checkClashes,\n          strictModeChanged,\n          hasParenthesizedAncestor: isParenthesizedExpression,\n        });\n      }\n    }\n  }\n\n  checkIdentifier(\n    at: Identifier,\n    bindingType: BindingTypes,\n    strictModeChanged: boolean = false,\n  ) {\n    if (\n      this.state.strict &&\n      (strictModeChanged\n        ? isStrictBindReservedWord(at.name, this.inModule)\n        : isStrictBindOnlyReservedWord(at.name))\n    ) {\n      if (bindingType === BIND_NONE) {\n        this.raise(Errors.StrictEvalArguments, { at, referenceName: at.name });\n      } else {\n        this.raise(Errors.StrictEvalArgumentsBinding, {\n          at,\n          bindingName: at.name,\n        });\n      }\n    }\n\n    if (bindingType & BIND_FLAGS_NO_LET_IN_LEXICAL && at.name === \"let\") {\n      this.raise(Errors.LetInLexicalBinding, { at });\n    }\n\n    if (!(bindingType & BIND_NONE)) {\n      this.declareNameFromIdentifier(at, bindingType);\n    }\n  }\n\n  declareNameFromIdentifier(identifier: Identifier, binding: BindingTypes) {\n    this.scope.declareName(identifier.name, binding, identifier.loc.start);\n  }\n\n  checkToRestConversion(node: Node, allowPattern: boolean): void {\n    switch (node.type) {\n      case \"ParenthesizedExpression\":\n        this.checkToRestConversion(node.expression, allowPattern);\n        break;\n      case \"Identifier\":\n      case \"MemberExpression\":\n        break;\n      case \"ArrayExpression\":\n      case \"ObjectExpression\":\n        if (allowPattern) break;\n      /* falls through */\n      default:\n        this.raise(Errors.InvalidRestAssignmentPattern, { at: node });\n    }\n  }\n\n  checkCommaAfterRest(\n    close: (typeof charCodes)[keyof typeof charCodes],\n  ): boolean {\n    if (!this.match(tt.comma)) {\n      return false;\n    }\n\n    this.raise(\n      this.lookaheadCharCode() === close\n        ? Errors.RestTrailingComma\n        : Errors.ElementAfterRest,\n      { at: this.state.startLoc },\n    );\n\n    return true;\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AAsBA,IAAAC,WAAA,GAAAD,OAAA;AAIA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;AAMA,IAAAI,WAAA,GAAAJ,OAAA;AAGA,MAAMK,MAAM,GAAGA,CAAeC,MAAS,EAAEC,GAAY,KACnDC,MAAM,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,IAAID,MAAM,CAACC,GAAG,CAAC;AAExD,MAAMI,6BAA6B,GAAIC,IAAU,IAAW;EAC1D,OAAOA,IAAI,CAACC,IAAI,KAAK,yBAAyB,GAC1CF,6BAA6B,CAACC,IAAI,CAACE,UAAU,CAAC,GAC9CF,IAAI;AACV,CAAC;AAAC,IAEgBG,qBAAqB;EAAAC,WAAA;EAAAC,kBAAA;EAAAC,qBAAA;AAAA;AAAAC,OAAA,CAAAJ,qBAAA,GAAAA,qBAAA;AAMxB,MAAeK,UAAU,SAASC,eAAS,CAAC;EAqDzDC,YAAYA,CAACV,IAAU,EAAEW,KAAc,GAAG,KAAK,EAAQ;IAAA,IAAAC,WAAA,EAAAC,YAAA;IACrD,IAAIC,aAAa,GAAGC,SAAS;IAC7B,IAAIf,IAAI,CAACC,IAAI,KAAK,yBAAyB,KAAAW,WAAA,GAAIZ,IAAI,CAACgB,KAAK,aAAVJ,WAAA,CAAYE,aAAa,EAAE;MACxEA,aAAa,GAAGf,6BAA6B,CAACC,IAAI,CAAC;MACnD,IAAIW,KAAK,EAAE;QAKT,IAAIG,aAAa,CAACb,IAAI,KAAK,YAAY,EAAE;UACvC,IAAI,CAACgB,eAAe,CAACC,gCAAgC,CACnDC,kBAAM,CAACC,8BAA8B,EACrC;YAAEC,EAAE,EAAErB;UAAK,CACb,CAAC;QACH,CAAC,MAAM,IAAIc,aAAa,CAACb,IAAI,KAAK,kBAAkB,EAAE;UAIpD,IAAI,CAACqB,KAAK,CAACH,kBAAM,CAACC,8BAA8B,EAAE;YAAEC,EAAE,EAAErB;UAAK,CAAC,CAAC;QACjE;MACF,CAAC,MAAM;QACL,IAAI,CAACsB,KAAK,CAACH,kBAAM,CAACC,8BAA8B,EAAE;UAAEC,EAAE,EAAErB;QAAK,CAAC,CAAC;MACjE;IACF;IAEA,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,YAAY;MACjB,KAAK,eAAe;MACpB,KAAK,cAAc;MACnB,KAAK,mBAAmB;MACxB,KAAK,aAAa;QAChB;MAEF,KAAK,kBAAkB;QACrBD,IAAI,CAACC,IAAI,GAAG,eAAe;QAC3B,KACE,IAAIsB,CAAC,GAAG,CAAC,EAAEC,MAAM,GAAGxB,IAAI,CAACyB,UAAU,CAACD,MAAM,EAAEE,IAAI,GAAGF,MAAM,GAAG,CAAC,EAC7DD,CAAC,GAAGC,MAAM,EACVD,CAAC,EAAE,EACH;UAAA,IAAAI,YAAA;UACA,MAAMC,IAAI,GAAG5B,IAAI,CAACyB,UAAU,CAACF,CAAC,CAAC;UAC/B,MAAMM,MAAM,GAAGN,CAAC,KAAKG,IAAI;UACzB,IAAI,CAACI,gCAAgC,CAACF,IAAI,EAAEC,MAAM,EAAElB,KAAK,CAAC;UAE1D,IACEkB,MAAM,IACND,IAAI,CAAC3B,IAAI,KAAK,aAAa,KAAA0B,YAAA,GAC3B3B,IAAI,CAACgB,KAAK,aAAVW,YAAA,CAAYI,gBAAgB,EAC5B;YACA,IAAI,CAACT,KAAK,CAACH,kBAAM,CAACa,iBAAiB,EAAE;cACnCX,EAAE,EAAErB,IAAI,CAACgB,KAAK,CAACe;YACjB,CAAC,CAAC;UACJ;QACF;QACA;MAEF,KAAK,gBAAgB;QAAE;UACrB,MAAM;YAAEpC,GAAG;YAAEsC;UAAM,CAAC,GAAGjC,IAAI;UAC3B,IAAI,IAAI,CAACkC,aAAa,CAACvC,GAAG,CAAC,EAAE;YAC3B,IAAI,CAACwC,UAAU,CAACC,cAAc,CAC5B,IAAI,CAACC,gBAAgB,CAAC1C,GAAG,CAAC,EAC1BA,GAAG,CAAC2C,GAAG,CAACC,KACV,CAAC;UACH;UACA,IAAI,CAAC7B,YAAY,CAACuB,KAAK,EAAEtB,KAAK,CAAC;UAC/B;QACF;MAEA,KAAK,eAAe;QAAE;UACpB,MAAM,IAAI6B,KAAK,CACb,iEAAiE,GAC/D,+DACJ,CAAC;QACH;MAEA,KAAK,iBAAiB;QACpBxC,IAAI,CAACC,IAAI,GAAG,cAAc;QAC1B,IAAI,CAACwC,gBAAgB,CACnBzC,IAAI,CAAC0C,QAAQ,GAAA7B,YAAA,GACbb,IAAI,CAACgB,KAAK,qBAAVH,YAAA,CAAYkB,gBAAgB,EAC5BpB,KACF,CAAC;QACD;MAEF,KAAK,sBAAsB;QACzB,IAAIX,IAAI,CAAC2C,QAAQ,KAAK,GAAG,EAAE;UACzB,IAAI,CAACrB,KAAK,CAACH,kBAAM,CAACyB,qBAAqB,EAAE;YAAEvB,EAAE,EAAErB,IAAI,CAAC6C,IAAI,CAACP,GAAG,CAACQ;UAAI,CAAC,CAAC;QACrE;QAEA9C,IAAI,CAACC,IAAI,GAAG,mBAAmB;QAC/B,OAAOD,IAAI,CAAC2C,QAAQ;QACpB,IAAI,CAACjC,YAAY,CAACV,IAAI,CAAC6C,IAAI,EAAElC,KAAK,CAAC;QACnC;MAEF,KAAK,yBAAyB;QAE5B,IAAI,CAACD,YAAY,CAACI,aAAa,EAAEH,KAAK,CAAC;QACvC;MAEF;IAGF;EACF;EAEAmB,gCAAgCA,CAC9BF,IAAU,EACVC,MAAe,EACflB,KAAc,EACd;IACA,IAAIiB,IAAI,CAAC3B,IAAI,KAAK,cAAc,EAAE;MAChC,IAAI,CAACqB,KAAK,CACRM,IAAI,CAACmB,IAAI,KAAK,KAAK,IAAInB,IAAI,CAACmB,IAAI,KAAK,KAAK,GACtC5B,kBAAM,CAAC6B,kBAAkB,GACzB7B,kBAAM,CAAC8B,gBAAgB,EAC3B;QAAE5B,EAAE,EAAEO,IAAI,CAACjC;MAAI,CACjB,CAAC;IACH,CAAC,MAAM,IAAIiC,IAAI,CAAC3B,IAAI,KAAK,eAAe,EAAE;MACxC2B,IAAI,CAAC3B,IAAI,GAAG,aAAa;MACzB,MAAMiD,GAAG,GAAGtB,IAAI,CAACuB,QAAQ;MACzB,IAAI,CAACC,qBAAqB,CAACF,GAAG,EAAqB,KAAK,CAAC;MACzD,IAAI,CAACxC,YAAY,CAACwC,GAAG,EAAEvC,KAAK,CAAC;MAE7B,IAAI,CAACkB,MAAM,EAAE;QACX,IAAI,CAACP,KAAK,CAACH,kBAAM,CAACa,iBAAiB,EAAE;UAAEX,EAAE,EAAEO;QAAK,CAAC,CAAC;MACpD;IACF,CAAC,MAAM;MACL,IAAI,CAAClB,YAAY,CAACkB,IAAI,EAAEjB,KAAK,CAAC;IAChC;EACF;EAIA8B,gBAAgBA,CACdY,QAAsB,EACtBtB,gBAA6C,EAC7CpB,KAAc,EACR;IACN,MAAMmC,GAAG,GAAGO,QAAQ,CAAC7B,MAAM,GAAG,CAAC;IAE/B,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIuB,GAAG,EAAEvB,CAAC,EAAE,EAAE;MAC7B,MAAM+B,GAAG,GAAGD,QAAQ,CAAC9B,CAAC,CAAC;MACvB,IAAI,CAAC+B,GAAG,EAAE;MAEV,IAAIA,GAAG,CAACrD,IAAI,KAAK,eAAe,EAAE;QAChCqD,GAAG,CAACrD,IAAI,GAAG,aAAa;QACxB,MAAMiD,GAAG,GAAGI,GAAG,CAACH,QAAQ;QACxB,IAAI,CAACC,qBAAqB,CAACF,GAAG,EAAqB,IAAI,CAAC;QACxD,IAAI,CAACxC,YAAY,CAACwC,GAAG,EAAEvC,KAAK,CAAC;MAC/B,CAAC,MAAM;QACL,IAAI,CAACD,YAAY,CAAC4C,GAAG,EAAE3C,KAAK,CAAC;MAC/B;MAEA,IAAI2C,GAAG,CAACrD,IAAI,KAAK,aAAa,EAAE;QAC9B,IAAIsB,CAAC,GAAGuB,GAAG,EAAE;UACX,IAAI,CAACxB,KAAK,CAACH,kBAAM,CAACa,iBAAiB,EAAE;YAAEX,EAAE,EAAEiC;UAAI,CAAC,CAAC;QACnD,CAAC,MAAM,IAAIvB,gBAAgB,EAAE;UAC3B,IAAI,CAACT,KAAK,CAACH,kBAAM,CAACa,iBAAiB,EAAE;YAAEX,EAAE,EAAEU;UAAiB,CAAC,CAAC;QAChE;MACF;IACF;EACF;EAEAwB,YAAYA,CAACvD,IAAU,EAAEwD,SAAmB,EAAW;IACrD,QAAQxD,IAAI,CAACC,IAAI;MACf,KAAK,YAAY;MACjB,KAAK,eAAe;MACpB,KAAK,cAAc;MACnB,KAAK,mBAAmB;MACxB,KAAK,aAAa;QAChB,OAAO,IAAI;MAEb,KAAK,kBAAkB;QAAE;UACvB,MAAMyB,IAAI,GAAG1B,IAAI,CAACyB,UAAU,CAACD,MAAM,GAAG,CAAC;UACvC,OAAQxB,IAAI,CAACyB,UAAU,CAAoCgC,KAAK,CAC9D,CAAC7B,IAAI,EAAEL,CAAC,KAAK;YACX,OACEK,IAAI,CAAC3B,IAAI,KAAK,cAAc,KAC3BsB,CAAC,KAAKG,IAAI,IAAIE,IAAI,CAAC3B,IAAI,KAAK,eAAe,CAAC,IAC7C,IAAI,CAACsD,YAAY,CAAC3B,IAAI,CAAC;UAE3B,CACF,CAAC;QACH;MAEA,KAAK,gBAAgB;QACnB,OAAO,IAAI,CAAC2B,YAAY,CAACvD,IAAI,CAACiC,KAAK,CAAC;MAEtC,KAAK,eAAe;QAClB,OAAO,IAAI,CAACsB,YAAY,CAACvD,IAAI,CAACmD,QAAQ,CAAC;MAEzC,KAAK,iBAAiB;QACpB,OAAQnD,IAAI,CAAqB0C,QAAQ,CAACe,KAAK,CAC7CC,OAAO,IAAIA,OAAO,KAAK,IAAI,IAAI,IAAI,CAACH,YAAY,CAACG,OAAO,CAC1D,CAAC;MAEH,KAAK,sBAAsB;QACzB,OAAO1D,IAAI,CAAC2C,QAAQ,KAAK,GAAG;MAE9B,KAAK,yBAAyB;QAC5B,OAAO,IAAI,CAACY,YAAY,CAACvD,IAAI,CAACE,UAAU,CAAC;MAE3C,KAAK,kBAAkB;MACvB,KAAK,0BAA0B;QAC7B,OAAO,CAACsD,SAAS;MAEnB;QACE,OAAO,KAAK;IAChB;EACF;EAIAG,gBAAgBA,CACdN,QAAsD,EAEtDO,mBAA6B,EACiB;IAC9C,OAAOP,QAAQ;EACjB;EAEAQ,oBAAoBA,CAClBR,QAAsD,EACtDO,mBAA6B,EACvB;IACN,IAAI,CAACD,gBAAgB,CAACN,QAAQ,EAAEO,mBAAmB,CAAC;IAEpD,KAAK,MAAME,IAAI,IAAIT,QAAQ,EAAE;MAC3B,IAAI,CAAAS,IAAI,oBAAJA,IAAI,CAAE7D,IAAI,MAAK,iBAAiB,EAAE;QACpC,IAAI,CAAC4D,oBAAoB,CAACC,IAAI,CAACpB,QAAQ,CAAC;MAC1C;IACF;EACF;EAIAqB,WAAWA,CAETC,mBAA6C,EAC9B;IACf,MAAMhE,IAAI,GAAG,IAAI,CAACiE,SAAS,CAAgB,CAAC;IAC5C,IAAI,CAACC,IAAI,CAAC,CAAC;IACXlE,IAAI,CAACmD,QAAQ,GAAG,IAAI,CAACgB,uBAAuB,CAC1CH,mBAAmB,EACnBjD,SACF,CAAC;IACD,OAAO,IAAI,CAACqD,UAAU,CAACpE,IAAI,EAAE,eAAe,CAAC;EAC/C;EAIAqE,gBAAgBA,CAAA,EAA4B;IAC1C,MAAMrE,IAAI,GAAG,IAAI,CAACiE,SAAS,CAAc,CAAC;IAC1C,IAAI,CAACC,IAAI,CAAC,CAAC;IACXlE,IAAI,CAACmD,QAAQ,GAAG,IAAI,CAACmB,gBAAgB,CAAC,CAAC;IACvC,OAAO,IAAI,CAACF,UAAU,CAACpE,IAAI,EAAE,aAAa,CAAC;EAC7C;EAGAsE,gBAAgBA,CAAA,EAAwB;IAEtC,QAAQ,IAAI,CAACC,KAAK,CAACtE,IAAI;MACrB;QAAkB;UAChB,MAAMD,IAAI,GAAG,IAAI,CAACiE,SAAS,CAAe,CAAC;UAC3C,IAAI,CAACC,IAAI,CAAC,CAAC;UAEXlE,IAAI,CAAC0C,QAAQ,GAAG,IAAI,CAAC8B,gBAAgB,QAGnCrE,qBAAqB,CAACC,WACxB,CAAC;UACD,OAAO,IAAI,CAACgE,UAAU,CAACpE,IAAI,EAAE,cAAc,CAAC;QAC9C;MAEA;QACE,OAAO,IAAI,CAACyE,eAAe,IAAY,IAAI,CAAC;IAChD;IAGA,OAAO,IAAI,CAACC,eAAe,CAAC,CAAC;EAC/B;EAGAF,gBAAgBA,CAEdG,KAAgB,EAChBC,aAAyD,EACzDC,KAA4B,EACU;IACtC,MAAMC,UAAU,GAAGD,KAAK,GAAG1E,qBAAqB,CAACC,WAAW;IAE5D,MAAM2E,IAA0C,GAAG,EAAE;IACrD,IAAIC,KAAK,GAAG,IAAI;IAChB,OAAO,CAAC,IAAI,CAACC,GAAG,CAACN,KAAK,CAAC,EAAE;MACvB,IAAIK,KAAK,EAAE;QACTA,KAAK,GAAG,KAAK;MACf,CAAC,MAAM;QACL,IAAI,CAACE,MAAM,GAAS,CAAC;MACvB;MACA,IAAIJ,UAAU,IAAI,IAAI,CAACK,KAAK,GAAS,CAAC,EAAE;QACtCJ,IAAI,CAACK,IAAI,CAAC,IAAI,CAAC;MACjB,CAAC,MAAM,IAAI,IAAI,CAACH,GAAG,CAACN,KAAK,CAAC,EAAE;QAC1B;MACF,CAAC,MAAM,IAAI,IAAI,CAACQ,KAAK,GAAY,CAAC,EAAE;QAClCJ,IAAI,CAACK,IAAI,CACP,IAAI,CAACC,4BAA4B,CAAC,IAAI,CAAChB,gBAAgB,CAAC,CAAC,EAAEQ,KAAK,CAClE,CAAC;QACD,IAAI,CAAC,IAAI,CAACS,mBAAmB,CAACV,aAAa,CAAC,EAAE;UAC5C,IAAI,CAACM,MAAM,CAACP,KAAK,CAAC;UAClB;QACF;MACF,CAAC,MAAM;QACL,MAAMY,UAAU,GAAG,EAAE;QACrB,IAAI,IAAI,CAACJ,KAAK,GAAM,CAAC,IAAI,IAAI,CAACK,SAAS,CAAC,YAAY,CAAC,EAAE;UACrD,IAAI,CAAClE,KAAK,CAACH,kBAAM,CAACsE,6BAA6B,EAAE;YAC/CpE,EAAE,EAAE,IAAI,CAACkD,KAAK,CAACmB;UACjB,CAAC,CAAC;QACJ;QAEA,OAAO,IAAI,CAACP,KAAK,GAAM,CAAC,EAAE;UACxBI,UAAU,CAACH,IAAI,CAAC,IAAI,CAACO,cAAc,CAAC,CAAC,CAAC;QACxC;QACAZ,IAAI,CAACK,IAAI,CAAC,IAAI,CAACQ,uBAAuB,CAACf,KAAK,EAAEU,UAAU,CAAC,CAAC;MAC5D;IACF;IACA,OAAOR,IAAI;EACb;EAGAc,wBAAwBA,CAEtBjE,IAAyB,EACZ;IACb,IAAI,CAACsC,IAAI,CAAC,CAAC;IAEXtC,IAAI,CAACuB,QAAQ,GAAG,IAAI,CAACuB,eAAe,CAAC,CAAC;IACtC,IAAI,CAACY,mBAAmB,IAA0B,CAAC;IACnD,OAAO,IAAI,CAAClB,UAAU,CAACxC,IAAI,EAAE,aAAa,CAAC;EAC7C;EAGAkE,oBAAoBA,CAAA,EAA2C;IAC7D,MAAMlE,IAAI,GAAG,IAAI,CAACqC,SAAS,CAA6B,CAAC;IACzD,MAAM;MAAEhE,IAAI;MAAEyF;IAAS,CAAC,GAAG,IAAI,CAACnB,KAAK;IACrC,IAAItE,IAAI,OAAgB,EAAE;MACxB,OAAO,IAAI,CAAC4F,wBAAwB,CAACjE,IAA2B,CAAC;IACnE,CAAC,MAAM,IAAI3B,IAAI,QAAmB,EAAE;MAClC,IAAI,CAAC8F,YAAY,CAAC,sBAAsB,EAAEL,QAAQ,CAAC;MACnD,IAAI,CAACvD,UAAU,CAACC,cAAc,CAAC,IAAI,CAACmC,KAAK,CAACtC,KAAK,EAAEyD,QAAQ,CAAC;MACzD9D,IAAI,CAA0BjC,GAAG,GAAG,IAAI,CAACqG,gBAAgB,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL,IAAI,CAACC,iBAAiB,CAACrE,IAA4B,CAAC;IACtD;IACCA,IAAI,CAA0BsE,MAAM,GAAG,KAAK;IAC7C,OAAO,IAAI,CAACC,iBAAiB,CAC3BvE,IAAI,EACJ8D,QAAQ,EACR,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KACF,CAAC;EACH;EAEAE,uBAAuBA,CAErBf,KAA4B,EAC5BU,UAAuB,EACQ;IAC/B,MAAM1C,IAAI,GAAG,IAAI,CAACuD,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACf,4BAA4B,CAACxC,IAAI,EAAEgC,KAAK,CAAC;IAC9C,MAAMvB,GAAG,GAAG,IAAI,CAAC8C,iBAAiB,CAACvD,IAAI,CAACP,GAAG,CAACC,KAAK,EAAEM,IAAI,CAAC;IACxD,IAAI0C,UAAU,CAAC/D,MAAM,EAAE;MACrBqB,IAAI,CAAC0C,UAAU,GAAGA,UAAU;IAC9B;IACA,OAAOjC,GAAG;EACZ;EAGA+B,4BAA4BA,CAC1BgB,KAAc,EAEdxB,KAA4B,EACnB;IACT,OAAOwB,KAAK;EACd;EAIAD,iBAAiBA,CAEfV,QAA0B,EAC1B7C,IAAqB,EACZ;IAAA,IAAAyD,SAAA,EAAAC,KAAA;IACT,CAAAD,SAAA,GAAAZ,QAAQ,YAAAY,SAAA,GAARZ,QAAQ,GAAK,IAAI,CAACnB,KAAK,CAACmB,QAAQ;IAChC7C,IAAI,IAAA0D,KAAA,GAAG1D,IAAI,YAAA0D,KAAA,GAAI,IAAI,CAACjC,gBAAgB,CAAC,CAAC;IACtC,IAAI,CAAC,IAAI,CAACW,GAAG,GAAM,CAAC,EAAE,OAAOpC,IAAI;IAEjC,MAAM7C,IAAI,GAAG,IAAI,CAACwG,WAAW,CAAoBd,QAAQ,CAAC;IAC1D1F,IAAI,CAAC6C,IAAI,GAAGA,IAAI;IAChB7C,IAAI,CAACyG,KAAK,GAAG,IAAI,CAACtC,uBAAuB,CAAC,CAAC;IAC3C,OAAO,IAAI,CAACC,UAAU,CAACpE,IAAI,EAAE,mBAAmB,CAAC;EACnD;EA8BA0G,WAAWA,CACTzG,IAAY,EAEZ0G,yBAAkC,EAElCC,OAAqB,EACH;IAClB,OAAOnH,MAAM,CACX;MACEoH,iBAAiB,EAAE,MAAM;MACzBC,WAAW,EAAE,UAAU;MACvBC,cAAc,EAAE,OAAO;MACvBC,uBAAuB,EAAE,YAAY;MACrCC,YAAY,EAAE,UAAU;MACxBC,aAAa,EAAE;IACjB,CAAC,EAEDjH,IACF,CAAC;EACH;EA2BAkH,SAASA,CACPjH,UAAmD,EACnD;IACEkH,EAAE,EAAEC,QAAQ;IACZT,OAAO,GAAGU,qBAAS;IACnBC,YAAY,GAAG,KAAK;IACpBC,iBAAiB,GAAG,KAAK;IACzBC,wBAAwB,GAAG;EAO7B,CAAC,EACK;IAAA,IAAAC,iBAAA;IACN,MAAMzH,IAAI,GAAGC,UAAU,CAACD,IAAI;IAK5B,IAAI,IAAI,CAAC0H,cAAc,CAACzH,UAAU,CAAC,EAAE;IAErC,IAAID,IAAI,KAAK,kBAAkB,EAAE;MAC/B,IAAI2G,OAAO,KAAKU,qBAAS,EAAE;QACzB,IAAI,CAAChG,KAAK,CAACH,kBAAM,CAACyG,6BAA6B,EAAE;UAAEvG,EAAE,EAAEnB;QAAW,CAAC,CAAC;MACtE;MACA;IACF;IAEA,IAAID,IAAI,KAAK,YAAY,EAAE;MACzB,IAAI,CAAC4H,eAAe,CAClB3H,UAAU,EACV0G,OAAO,EACPY,iBACF,CAAC;MAED,MAAM;QAAEM;MAAK,CAAC,GAAG5H,UAAwB;MAEzC,IAAIqH,YAAY,EAAE;QAChB,IAAIA,YAAY,CAACQ,GAAG,CAACD,IAAI,CAAC,EAAE;UAC1B,IAAI,CAACxG,KAAK,CAACH,kBAAM,CAAC6G,SAAS,EAAE;YAAE3G,EAAE,EAAEnB;UAAW,CAAC,CAAC;QAClD,CAAC,MAAM;UACLqH,YAAY,CAACU,GAAG,CAACH,IAAI,CAAC;QACxB;MACF;MAEA;IACF;IAEA,MAAMI,QAAQ,GAAG,IAAI,CAACxB,WAAW,CAC/BzG,IAAI,EACJ,EAAEwH,wBAAwB,KAAAC,iBAAA,GAAIxH,UAAU,CAACc,KAAK,aAAhB0G,iBAAA,CAAkB5G,aAAa,CAAC,IAC5DuG,QAAQ,CAACpH,IAAI,KAAK,sBAAsB,EAC1C2G,OACF,CAAC;IAED,IAAIsB,QAAQ,KAAK,IAAI,EAAE;IACvB,IAAIA,QAAQ,KAAK,KAAK,EAAE;MACtB,MAAMC,eAAe,GACnBvB,OAAO,KAAKU,qBAAS,GAAGnG,kBAAM,CAACiH,UAAU,GAAGjH,kBAAM,CAACkH,iBAAiB;MAEtE,IAAI,CAAC/G,KAAK,CAAC6G,eAAe,EAAE;QAAE9G,EAAE,EAAEnB,UAAU;QAAEmH;MAAS,CAAC,CAAC;MACzD;IACF;IAEA,MAAM,CAAC1H,GAAG,EAAE2I,yBAAyB,CAAC,GAAGC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAAC,GAC5DA,QAAQ,GACR,CAACA,QAAQ,EAAEjI,IAAI,KAAK,yBAAyB,CAAC;IAClD,MAAMwI,YAAY,GAChBxI,IAAI,KAAK,cAAc,IACvBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,yBAAyB,GAC7B;MAAEA;IAAK,CAAC,GACToH,QAAQ;IAGd,KAAK,MAAMqB,KAAK,IAAI,EAAE,CAACC,MAAM,CAACzI,UAAU,CAACP,GAAG,CAAC,CAAC,EAAE;MAC9C,IAAI+I,KAAK,EAAE;QACT,IAAI,CAACvB,SAAS,CAACuB,KAAK,EAAE;UACpBtB,EAAE,EAAEqB,YAAY;UAChB7B,OAAO;UACPW,YAAY;UACZC,iBAAiB;UACjBC,wBAAwB,EAAEa;QAC5B,CAAC,CAAC;MACJ;IACF;EACF;EAEAT,eAAeA,CACbxG,EAAc,EACduH,WAAyB,EACzBpB,iBAA0B,GAAG,KAAK,EAClC;IACA,IACE,IAAI,CAACjD,KAAK,CAACsE,MAAM,KAChBrB,iBAAiB,GACd,IAAAsB,oCAAwB,EAACzH,EAAE,CAACyG,IAAI,EAAE,IAAI,CAACiB,QAAQ,CAAC,GAChD,IAAAC,wCAA4B,EAAC3H,EAAE,CAACyG,IAAI,CAAC,CAAC,EAC1C;MACA,IAAIc,WAAW,KAAKtB,qBAAS,EAAE;QAC7B,IAAI,CAAChG,KAAK,CAACH,kBAAM,CAAC8H,mBAAmB,EAAE;UAAE5H,EAAE;UAAE6H,aAAa,EAAE7H,EAAE,CAACyG;QAAK,CAAC,CAAC;MACxE,CAAC,MAAM;QACL,IAAI,CAACxG,KAAK,CAACH,kBAAM,CAACgI,0BAA0B,EAAE;UAC5C9H,EAAE;UACF+H,WAAW,EAAE/H,EAAE,CAACyG;QAClB,CAAC,CAAC;MACJ;IACF;IAEA,IAAIc,WAAW,GAAGS,wCAA4B,IAAIhI,EAAE,CAACyG,IAAI,KAAK,KAAK,EAAE;MACnE,IAAI,CAACxG,KAAK,CAACH,kBAAM,CAACmI,mBAAmB,EAAE;QAAEjI;MAAG,CAAC,CAAC;IAChD;IAEA,IAAI,EAAEuH,WAAW,GAAGtB,qBAAS,CAAC,EAAE;MAC9B,IAAI,CAACiC,yBAAyB,CAAClI,EAAE,EAAEuH,WAAW,CAAC;IACjD;EACF;EAEAW,yBAAyBA,CAACC,UAAsB,EAAE5C,OAAqB,EAAE;IACvE,IAAI,CAAC6C,KAAK,CAACC,WAAW,CAACF,UAAU,CAAC1B,IAAI,EAAElB,OAAO,EAAE4C,UAAU,CAAClH,GAAG,CAACC,KAAK,CAAC;EACxE;EAEAa,qBAAqBA,CAACpD,IAAU,EAAE2J,YAAqB,EAAQ;IAC7D,QAAQ3J,IAAI,CAACC,IAAI;MACf,KAAK,yBAAyB;QAC5B,IAAI,CAACmD,qBAAqB,CAACpD,IAAI,CAACE,UAAU,EAAEyJ,YAAY,CAAC;QACzD;MACF,KAAK,YAAY;MACjB,KAAK,kBAAkB;QACrB;MACF,KAAK,iBAAiB;MACtB,KAAK,kBAAkB;QACrB,IAAIA,YAAY,EAAE;MAEpB;QACE,IAAI,CAACrI,KAAK,CAACH,kBAAM,CAACyI,4BAA4B,EAAE;UAAEvI,EAAE,EAAErB;QAAK,CAAC,CAAC;IACjE;EACF;EAEAsF,mBAAmBA,CACjBX,KAAiD,EACxC;IACT,IAAI,CAAC,IAAI,CAACQ,KAAK,GAAS,CAAC,EAAE;MACzB,OAAO,KAAK;IACd;IAEA,IAAI,CAAC7D,KAAK,CACR,IAAI,CAACuI,iBAAiB,CAAC,CAAC,KAAKlF,KAAK,GAC9BxD,kBAAM,CAACa,iBAAiB,GACxBb,kBAAM,CAAC2I,gBAAgB,EAC3B;MAAEzI,EAAE,EAAE,IAAI,CAACkD,KAAK,CAACmB;IAAS,CAC5B,CAAC;IAED,OAAO,IAAI;EACb;AACF;AAACnF,OAAA,CAAAwJ,OAAA,GAAAvJ,UAAA"}