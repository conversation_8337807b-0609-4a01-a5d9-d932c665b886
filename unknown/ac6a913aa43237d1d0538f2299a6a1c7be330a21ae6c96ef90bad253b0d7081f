{"version": 3, "names": ["_options", "require", "_statement", "_scope", "<PERSON><PERSON><PERSON>", "Statement<PERSON><PERSON>er", "constructor", "options", "input", "getOptions", "initializeScopes", "plugins", "pluginsMap", "filename", "sourceFilename", "getScopeHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parse", "enterInitialScopes", "file", "startNode", "program", "nextToken", "errors", "parseTopLevel", "state", "exports", "default", "pluginMap", "Map", "plugin", "name", "Array", "isArray", "has", "set"], "sources": ["../../src/parser/index.ts"], "sourcesContent": ["import type { Options } from \"../options\";\nimport type * as N from \"../types\";\nimport type { PluginList } from \"../plugin-utils\";\nimport { getOptions } from \"../options\";\nimport StatementParser from \"./statement\";\nimport ScopeHandler from \"../util/scope\";\n\nexport type PluginsMap = Map<\n  string,\n  {\n    [x: string]: any;\n  }\n>;\n\nexport default class Parser extends StatementParser {\n  // Forward-declaration so typescript plugin can override jsx plugin\n  // todo(flow->ts) - this probably can be removed\n  // abstract jsxParseOpeningElementAfterName(\n  //   node: N.JSXOpeningElement,\n  // ): N.JSXOpeningElement;\n\n  constructor(options: Options | undefined | null, input: string) {\n    options = getOptions(options);\n    super(options, input);\n\n    this.options = options;\n    this.initializeScopes();\n    this.plugins = pluginsMap(this.options.plugins);\n    this.filename = options.sourceFilename;\n  }\n\n  // This can be overwritten, for example, by the TypeScript plugin.\n  getScopeHandler(): {\n    new (...args: any): <PERSON><PERSON><PERSON>and<PERSON>;\n  } {\n    return ScopeHandler;\n  }\n\n  parse(): N.File {\n    this.enterInitialScopes();\n    const file = this.startNode() as N.File;\n    const program = this.startNode() as N.Program;\n    this.nextToken();\n    file.errors = null;\n    this.parseTopLevel(file, program);\n    file.errors = this.state.errors;\n    return file;\n  }\n}\n\nfunction pluginsMap(plugins: PluginList): PluginsMap {\n  const pluginMap: PluginsMap = new Map();\n  for (const plugin of plugins) {\n    const [name, options] = Array.isArray(plugin) ? plugin : [plugin, {}];\n    if (!pluginMap.has(name)) pluginMap.set(name, options || {});\n  }\n  return pluginMap;\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AASe,MAAMG,MAAM,SAASC,kBAAe,CAAC;EAOlDC,WAAWA,CAACC,OAAmC,EAAEC,KAAa,EAAE;IAC9DD,OAAO,GAAG,IAAAE,mBAAU,EAACF,OAAO,CAAC;IAC7B,KAAK,CAACA,OAAO,EAAEC,KAAK,CAAC;IAErB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,OAAO,GAAGC,UAAU,CAAC,IAAI,CAACL,OAAO,CAACI,OAAO,CAAC;IAC/C,IAAI,CAACE,QAAQ,GAAGN,OAAO,CAACO,cAAc;EACxC;EAGAC,eAAeA,CAAA,EAEb;IACA,OAAOC,cAAY;EACrB;EAEAC,KAAKA,CAAA,EAAW;IACd,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,MAAMC,IAAI,GAAG,IAAI,CAACC,SAAS,CAAC,CAAW;IACvC,MAAMC,OAAO,GAAG,IAAI,CAACD,SAAS,CAAC,CAAc;IAC7C,IAAI,CAACE,SAAS,CAAC,CAAC;IAChBH,IAAI,CAACI,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,aAAa,CAACL,IAAI,EAAEE,OAAO,CAAC;IACjCF,IAAI,CAACI,MAAM,GAAG,IAAI,CAACE,KAAK,CAACF,MAAM;IAC/B,OAAOJ,IAAI;EACb;AACF;AAACO,OAAA,CAAAC,OAAA,GAAAvB,MAAA;AAED,SAASQ,UAAUA,CAACD,OAAmB,EAAc;EACnD,MAAMiB,SAAqB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACvC,KAAK,MAAMC,MAAM,IAAInB,OAAO,EAAE;IAC5B,MAAM,CAACoB,IAAI,EAAExB,OAAO,CAAC,GAAGyB,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,EAAE,CAAC,CAAC,CAAC;IACrE,IAAI,CAACF,SAAS,CAACM,GAAG,CAACH,IAAI,CAAC,EAAEH,SAAS,CAACO,GAAG,CAACJ,IAAI,EAAExB,OAAO,IAAI,CAAC,CAAC,CAAC;EAC9D;EACA,OAAOqB,SAAS;AAClB"}