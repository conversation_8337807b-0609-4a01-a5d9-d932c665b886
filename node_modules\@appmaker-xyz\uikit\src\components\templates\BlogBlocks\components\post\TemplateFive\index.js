/**
 * Post list Template View for inAppView
 */
import { AppmakerText } from '@appmaker-xyz/uikit/src/components';
import PropTypes from 'prop-types';
import React from 'react';
import { View, Image, Text, TouchableOpacity } from 'react-native';
// import AppTouchable from '../../../common/AppTouchable';
import styles from './styles';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { color } from '@appmaker-xyz/uikit/src/styles/index';
// import { shadeColor } from '../../../common/Utils';

// const userImage = require('../../../../image/user-grey.png');

/**
 * Template Five for Post list item
 * @param { title, data, onClick } props.
 */
const TemplateFive = ({ attributes, data, onAction }) => {
  const item = data;
  // Check if response has orientation if not set default to left
  const titleStyle = attributes.styles ? attributes.styles.titleStyle : {};
  const contentStyle = attributes.styles ? attributes.styles.contentStyle : {};
  const firstLetter = data.title.charAt(0);
  const handleAction = () => onAction(attributes.appmakerAction);
  // TODO add conditions for no image
  return (
    <TouchableOpacity onPress={handleAction}>
      <View style={styles.rootContainer}>
        {item.image && (
          <Image style={styles.image} source={{ uri: item.image }} />
        )}
        {!item.image && (
          <View style={styles.imageDummy}>
            <AppmakerText category="pageHeading" style={styles.firstLetter}>
              {firstLetter}
            </AppmakerText>
          </View>
        )}
        <View style={styles.container}>
          <View style={styles.textContainer}>
            {item.category && (
              <AppmakerText style={styles.categoryText}>
                {item.category}
              </AppmakerText>
            )}
            <AppmakerText
              category="h1Heading"
              style={[styles.title, titleStyle]}
              numberOfLines={2}>
              {attributes.title}
            </AppmakerText>
            <AppmakerText
              category="bodyParagraph"
              style={[styles.description, contentStyle]}
              numberOfLines={2}>
              {item.content}
            </AppmakerText>

            {item.author && (
              <View style={styles.authorContainer}>
                <Icon
                  name="user"
                  style={styles.userIcon}
                  size={12}
                  color={color.demiDark}
                />
                <AppmakerText category="highlighter2" style={styles.author}>
                  {item.author}
                </AppmakerText>
              </View>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};
TemplateFive.propTypes = {
  data: PropTypes.any.isRequired,
  title: PropTypes.string,
  onClick: PropTypes.func,
};

TemplateFive.defaultProps = {};

export default TemplateFive;
