import React, { useState, useEffect } from 'react';
import { ScrollView, FlatList } from 'react-native';
import { useApThemeState } from '../../../../../theme/ThemeContext';
import {
  AppTouchable,
  Layout,
  Button,
  AppmakerText
} from '../../../../../components';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import Modal from 'react-native-modal';
import { allStyles } from './ProductVariations';

const ListBlocks = ({ attributes, onChange }) => {
  const { key, variation, selectedItem } = attributes;
  const { spacing, color } = useApThemeState();
  const styles = allStyles({ spacing, color });
  const [selected, setSelected] = useState(selectedItem === key ? true : false);
  useEffect(() => {
    if (attributes.selectedItem != attributes.key && selected) {
      setSelected(false);
    }
  }, [selectedItem]);
  return (
    <AppTouchable
      key={key}
      style={[
        styles.variationItem,
        selected && styles.variationItemSelected,
        styles.listVariationItem,
      ]}
      onPress={() => {
        if (!selected) {
          setSelected(!selected);
          onChange(attributes);
        }
      }}>
      <AppmakerText
        category="bodyParagraph"
        status={selected ? 'primary' : 'dark'}>
        {variation}
      </AppmakerText>
    </AppTouchable>
  );
};
export const SelectBlock = ({ attributes, onChange }) => {
  const { key, variation, selectedItem } = attributes;
  const { spacing, color, font } = useApThemeState();
  const styles = allStyles({ spacing, color });
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState('Select a varient');

  let toggle = () => setVisible(!visible);

  const renderItem = ({ item }) => (
    <ListBlocks
      onChange={(res) => {
        toggle();
        onChange(res);
        setTitle(res.variation);
      }}
      attributes={{
        variation: item.variationName,
        key: item.id,
        selectedItem,
      }} />
  );
  return (
    <AppTouchable key={key} style={styles.selectBlock} onPress={toggle}>
      <AppmakerText>{title}</AppmakerText>
      <Icon name="chevron-down" size={font.size.semi} color={color.dark} />
      <Modal
        isVisible={visible}
        onSwipeComplete={toggle}
        onBackButtonPress={toggle}
        backdropTransitionOutTiming={0}
        onBackdropPress={toggle}
        style={styles.modal}>
        <Layout style={styles.modalBody}>
          <Layout style={styles.modalHeader}>
            <Layout>
              <AppmakerText category="actionTitle" status="dark">
                Select a varient
              </AppmakerText>
            </Layout>
            <Button
              onPress={toggle}
              status="white"
              accessoryLeft={() => <Icon name="x" size={font.size.lg} />}
              small />
          </Layout>
          <ScrollView style={styles.modalContent}>
            <FlatList
              showsVerticalScrollIndicator={true}
              showsHorizontalScrollIndicator={false}
              data={variation}
              renderItem={renderItem}
              keyExtractor={(item) => item.id} />
            <Layout style={{ marginBottom: spacing.base }} />
          </ScrollView>
        </Layout>
      </Modal>
    </AppTouchable>
  );
};
